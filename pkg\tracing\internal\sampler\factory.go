/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package sampler

import (
	"fmt"

	"go.opentelemetry.io/otel/sdk/trace"
)

// SamplerConfig 采样器配置（从主包复制，避免循环依赖）
type SamplerConfig struct {
	Type        string  `mapstructure:"type"`
	Param       float64 `mapstructure:"param"`
	ParentBased bool    `mapstructure:"parent_based"`
}

// New 根据配置创建对应的 Sampler
func New(cfg SamplerConfig) (trace.Sampler, error) {
	switch cfg.Type {
	case "always_on":
		return trace.AlwaysSample(), nil
	case "always_off":
		return trace.NeverSample(), nil
	case "trace_id_ratio":
		return createTraceIDRatioSampler(cfg)
	case "parent_based_trace_id_ratio":
		return createParentBasedTraceIDRatioSampler(cfg)
	default:
		return nil, fmt.Errorf("unsupported sampler type: %s", cfg.Type)
	}
}

// createTraceIDRatioSampler 创建基于 TraceID 比例的采样器
func createTraceIDRatioSampler(cfg SamplerConfig) (trace.Sampler, error) {
	if cfg.Param < 0.0 || cfg.Param > 1.0 {
		return nil, fmt.Errorf("trace_id_ratio sampler param must be between 0.0 and 1.0, got: %f", cfg.Param)
	}
	return trace.TraceIDRatioBased(cfg.Param), nil
}

// createParentBasedTraceIDRatioSampler 创建基于父 span 的 TraceID 比例采样器
func createParentBasedTraceIDRatioSampler(cfg SamplerConfig) (trace.Sampler, error) {
	if cfg.Param < 0.0 || cfg.Param > 1.0 {
		return nil, fmt.Errorf("parent_based_trace_id_ratio sampler param must be between 0.0 and 1.0, got: %f", cfg.Param)
	}

	// 基础采样器使用 TraceIDRatioBased
	baseSampler := trace.TraceIDRatioBased(cfg.Param)

	// 创建基于父 span 的采样器
	// 如果有父 span 且父 span 被采样，则采样当前 span
	// 如果没有父 span，使用基础采样器的决策
	return trace.ParentBased(baseSampler), nil
}

// ValidateConfig 验证采样器配置
func ValidateConfig(cfg SamplerConfig) error {
	switch cfg.Type {
	case "always_on", "always_off":
		// 这些类型不需要参数验证
		return nil
	case "trace_id_ratio", "parent_based_trace_id_ratio":
		if cfg.Param < 0.0 || cfg.Param > 1.0 {
			return fmt.Errorf("sampler param must be between 0.0 and 1.0, got: %f", cfg.Param)
		}
		return nil
	default:
		return fmt.Errorf("unsupported sampler type: %s", cfg.Type)
	}
}

// GetSamplingRate 获取采样器的采样率（用于监控和调试）
func GetSamplingRate(cfg SamplerConfig) float64 {
	switch cfg.Type {
	case "always_on":
		return 1.0
	case "always_off":
		return 0.0
	case "trace_id_ratio", "parent_based_trace_id_ratio":
		return cfg.Param
	default:
		return 0.0
	}
}

// GetDescription 获取采样器的描述信息
func GetDescription(cfg SamplerConfig) string {
	switch cfg.Type {
	case "always_on":
		return "Always sample all traces"
	case "always_off":
		return "Never sample any traces"
	case "trace_id_ratio":
		return fmt.Sprintf("Sample %.1f%% of traces based on TraceID ratio", cfg.Param*100)
	case "parent_based_trace_id_ratio":
		return fmt.Sprintf("Sample %.1f%% of root traces, follow parent decision for child spans", cfg.Param*100)
	default:
		return fmt.Sprintf("Unknown sampler type: %s", cfg.Type)
	}
}
