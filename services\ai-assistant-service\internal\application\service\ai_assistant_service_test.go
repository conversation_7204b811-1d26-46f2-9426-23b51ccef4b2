/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package service

import (
	"context"
	"strings"
	"testing"
	"time"

	"cina.club/services/ai-assistant-service/internal/application/port"
	"cina.club/services/ai-assistant-service/internal/domain/model"
	"cina.club/services/ai-assistant-service/internal/domain/session"
)

// MockSessionStore implements SessionStore for testing
type MockSessionStore struct {
	sessions map[string]*model.DialogState
}

func NewMockSessionStore() *MockSessionStore {
	return &MockSessionStore{
		sessions: make(map[string]*model.DialogState),
	}
}

func (m *MockSessionStore) Get(ctx context.Context, sessionID string) (*model.DialogState, error) {
	if ds, exists := m.sessions[sessionID]; exists {
		return ds, nil
	}
	return nil, session.ErrSessionNotFound
}

func (m *MockSessionStore) Set(ctx context.Context, sessionID string, state *model.DialogState, ttl time.Duration) error {
	m.sessions[sessionID] = state
	return nil
}

func (m *MockSessionStore) Delete(ctx context.Context, sessionID string) error {
	delete(m.sessions, sessionID)
	return nil
}

func (m *MockSessionStore) Exists(ctx context.Context, sessionID string) (bool, error) {
	_, exists := m.sessions[sessionID]
	return exists, nil
}

func (m *MockSessionStore) ExtendTTL(ctx context.Context, sessionID string, ttl time.Duration) error {
	return nil
}

// MockTool implements Tool for testing
type MockTool struct {
	name     string
	category port.ToolCategory
	result   *port.ToolResult
	err      error
}

func (m *MockTool) Name() string                   { return m.name }
func (m *MockTool) Description() string            { return "Mock tool" }
func (m *MockTool) Category() port.ToolCategory    { return m.category }
func (m *MockTool) RequiresAuth() bool             { return false }
func (m *MockTool) IsAsync() bool                  { return false }
func (m *MockTool) InputSchema() *port.JSONSchema  { return nil }
func (m *MockTool) OutputSchema() *port.JSONSchema { return nil }
func (m *MockTool) Execute(ctx context.Context, inputs map[string]interface{}) (*port.ToolResult, error) {
	return m.result, m.err
}

// MockToolkit implements Toolkit for testing
type MockToolkit struct {
	tools map[string]port.Tool
}

func NewMockToolkit() *MockToolkit {
	return &MockToolkit{
		tools: make(map[string]port.Tool),
	}
}

func (m *MockToolkit) RegisterTool(tool port.Tool) error {
	m.tools[tool.Name()] = tool
	return nil
}

func (m *MockToolkit) GetTool(name string) (port.Tool, error) {
	if tool, exists := m.tools[name]; exists {
		return tool, nil
	}
	return nil, port.ErrToolNotFound
}

func (m *MockToolkit) GetAllTools() map[string]port.Tool {
	return m.tools
}

func (m *MockToolkit) GetToolsByCategory(category port.ToolCategory) []port.Tool {
	var result []port.Tool
	for _, tool := range m.tools {
		if tool.Category() == category {
			result = append(result, tool)
		}
	}
	return result
}

func (m *MockToolkit) GetToolSchemas() []*port.ToolSchema {
	var schemas []*port.ToolSchema
	for _, tool := range m.tools {
		schemas = append(schemas, &port.ToolSchema{
			Name:        tool.Name(),
			Description: tool.Description(),
			Category:    string(tool.Category()),
		})
	}
	return schemas
}

func (m *MockToolkit) ValidateToolCall(toolName string, inputs map[string]interface{}) error {
	_, err := m.GetTool(toolName)
	return err
}

func (m *MockToolkit) ExecuteTool(ctx context.Context, toolName string, inputs map[string]interface{}) (*port.ToolResult, error) {
	tool, err := m.GetTool(toolName)
	if err != nil {
		return nil, err
	}
	return tool.Execute(ctx, inputs)
}

func TestNewAIAssistantService(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	service := NewAIAssistantService(sessionManager, toolkit)

	if service == nil {
		t.Fatal("NewAIAssistantService returned nil")
	}

	if service.sessionManager != sessionManager {
		t.Error("SessionManager not set correctly")
	}

	if service.toolkit != toolkit {
		t.Error("Toolkit not set correctly")
	}

	if service.planner == nil {
		t.Error("Planner should be initialized")
	}
}

func TestAIAssistantService_ProcessMessage_WithoutWorkflow(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	service := NewAIAssistantService(sessionManager, toolkit)

	req := &ProcessMessageRequest{
		SessionID: "test-session",
		UserID:    "test-user",
		Message:   "Hello world",
	}

	resp, err := service.ProcessMessage(context.Background(), req)
	if err != nil {
		t.Fatalf("ProcessMessage failed: %v", err)
	}

	if resp == nil {
		t.Fatal("Response is nil")
	}

	if resp.SessionID == "" {
		t.Error("SessionID should not be empty")
	}

	if resp.Content == "" {
		t.Error("Content should not be empty")
	}

	if resp.MessageType != "text" {
		t.Errorf("Expected MessageType 'text', got '%s'", resp.MessageType)
	}
}

func TestAIAssistantService_ProcessMessage_WithWorkflow(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	// Register a mock tool
	mockTool := &MockTool{
		name:     "search_services",
		category: port.ToolCategorySearch,
		result: port.NewToolResult(map[string]interface{}{
			"content": "Found 3 services",
		}),
	}
	toolkit.RegisterTool(mockTool)

	service := NewAIAssistantService(sessionManager, toolkit)

	req := &ProcessMessageRequest{
		SessionID: "test-session",
		UserID:    "test-user",
		Message:   "search for photography services",
	}

	resp, err := service.ProcessMessage(context.Background(), req)
	if err != nil {
		t.Fatalf("ProcessMessage failed: %v", err)
	}

	if resp == nil {
		t.Fatal("Response is nil")
	}

	if resp.Content == "" {
		t.Error("Content should not be empty")
	}
}

func TestAIAssistantService_GetSessionHistory(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	service := NewAIAssistantService(sessionManager, toolkit)

	// First create a session with some messages
	req := &ProcessMessageRequest{
		SessionID: "test-session",
		UserID:    "test-user",
		Message:   "Hello",
	}

	_, err := service.ProcessMessage(context.Background(), req)
	if err != nil {
		t.Fatalf("ProcessMessage failed: %v", err)
	}

	// Get session history
	history, err := service.GetSessionHistory(context.Background(), "test-session", 10)
	if err != nil {
		t.Fatalf("GetSessionHistory failed: %v", err)
	}

	if history == nil {
		t.Fatal("History is nil")
	}

	if history.SessionID != "test-session" {
		t.Errorf("Expected SessionID 'test-session', got '%s'", history.SessionID)
	}

	if len(history.Messages) == 0 {
		t.Error("Expected at least one message in history")
	}
}

func TestAIAssistantService_ClearSession(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	service := NewAIAssistantService(sessionManager, toolkit)

	// First create a session
	req := &ProcessMessageRequest{
		SessionID: "test-session",
		UserID:    "test-user",
		Message:   "Hello",
	}

	_, err := service.ProcessMessage(context.Background(), req)
	if err != nil {
		t.Fatalf("ProcessMessage failed: %v", err)
	}

	// Clear the session
	err = service.ClearSession(context.Background(), "test-session")
	if err != nil {
		t.Fatalf("ClearSession failed: %v", err)
	}

	// Verify session is cleared
	exists, err := sessionStore.Exists(context.Background(), "test-session")
	if err != nil {
		t.Fatalf("Failed to check session existence: %v", err)
	}

	if exists {
		t.Error("Session should be cleared")
	}
}

func TestAIAssistantService_GetAvailableTools(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	// Register some tools
	tool1 := &MockTool{name: "tool1", category: port.ToolCategorySearch}
	tool2 := &MockTool{name: "tool2", category: port.ToolCategorySchedule}
	toolkit.RegisterTool(tool1)
	toolkit.RegisterTool(tool2)

	service := NewAIAssistantService(sessionManager, toolkit)

	tools, err := service.GetAvailableTools(context.Background())
	if err != nil {
		t.Fatalf("GetAvailableTools failed: %v", err)
	}

	if len(tools) != 2 {
		t.Errorf("Expected 2 tools, got %d", len(tools))
	}
}

func TestAIAssistantService_executeWorkflow(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	// Register a mock tool
	mockTool := &MockTool{
		name:     "test-tool",
		category: port.ToolCategoryUtility,
		result: port.NewToolResult(map[string]interface{}{
			"result": "success",
		}),
	}
	toolkit.RegisterTool(mockTool)

	service := NewAIAssistantService(sessionManager, toolkit)

	// Create a workflow plan
	plan := model.NewWorkflowPlan("test-plan", "Test workflow")
	step := model.WorkflowStep{
		ID:     "step-1",
		Name:   "Test Step",
		ToolID: "test-tool",
		Inputs: map[string]interface{}{
			"input": "test",
		},
	}
	plan.AddStep(step)

	dialogState := model.NewDialogState("test-user")

	result, err := service.executeWorkflow(context.Background(), dialogState, plan)
	if err != nil {
		t.Fatalf("executeWorkflow failed: %v", err)
	}

	if result == nil {
		t.Fatal("Result is nil")
	}

	if !result.Success {
		t.Error("Workflow execution should be successful")
	}

	if len(result.Results) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result.Results))
	}
}

func TestAIAssistantService_executeStep(t *testing.T) {
	sessionStore := NewMockSessionStore()
	sessionManager := session.NewSessionManager(sessionStore)
	toolkit := NewMockToolkit()

	// Register a mock tool
	mockTool := &MockTool{
		name:     "test-tool",
		category: port.ToolCategoryUtility,
		result: port.NewToolResult(map[string]interface{}{
			"result": "success",
		}),
	}
	toolkit.RegisterTool(mockTool)

	service := NewAIAssistantService(sessionManager, toolkit)

	step := model.WorkflowStep{
		ID:     "step-1",
		Name:   "Test Step",
		ToolID: "test-tool",
		Inputs: map[string]interface{}{
			"input": "test",
		},
	}

	result, err := service.executeStep(context.Background(), step, make(map[string]interface{}))
	if err != nil {
		t.Fatalf("executeStep failed: %v", err)
	}

	if result == nil {
		t.Fatal("Result is nil")
	}

	if !result.Success {
		t.Error("Step execution should be successful")
	}
}

func TestAIAssistantService_checkDependencies(t *testing.T) {
	service := &AIAssistantService{}

	// Test with no dependencies
	step := model.WorkflowStep{
		Dependencies: []string{},
	}
	results := map[string]interface{}{}

	if !service.checkDependencies(step, results) {
		t.Error("Step with no dependencies should pass")
	}

	// Test with satisfied dependencies
	step = model.WorkflowStep{
		Dependencies: []string{"dep1", "dep2"},
	}
	results = map[string]interface{}{
		"dep1": "result1",
		"dep2": "result2",
	}

	if !service.checkDependencies(step, results) {
		t.Error("Step with satisfied dependencies should pass")
	}

	// Test with unsatisfied dependencies
	step = model.WorkflowStep{
		Dependencies: []string{"dep1", "dep2", "dep3"},
	}
	results = map[string]interface{}{
		"dep1": "result1",
		"dep2": "result2",
	}

	if service.checkDependencies(step, results) {
		t.Error("Step with unsatisfied dependencies should fail")
	}
}

func TestAIAssistantService_generateResponse(t *testing.T) {
	service := &AIAssistantService{}
	dialogState := model.NewDialogState("test-user")

	// Test with nil execution result
	response := service.generateResponse(context.Background(), dialogState, nil)
	if response == nil {
		t.Fatal("Response is nil")
	}

	if response.MessageType != "text" {
		t.Errorf("Expected MessageType 'text', got '%s'", response.MessageType)
	}

	if len(response.Suggestions) == 0 {
		t.Error("Should have suggestions for nil execution result")
	}

	// Test with failed execution result
	failedResult := &ExecutionResult{
		Success: false,
		Errors:  []string{"test error"},
	}

	response = service.generateResponse(context.Background(), dialogState, failedResult)
	if response == nil {
		t.Fatal("Response is nil")
	}

	if !strings.Contains(response.Content, "Sorry") {
		t.Error("Failed execution should contain apology")
	}

	// Test with successful execution result
	successResult := &ExecutionResult{
		Success: true,
		Results: map[string]interface{}{
			"step1": &port.ToolResult{
				Success: true,
				Data: map[string]interface{}{
					"content": "Test result",
				},
			},
		},
		Duration: time.Second,
	}

	response = service.generateResponse(context.Background(), dialogState, successResult)
	if response == nil {
		t.Fatal("Response is nil")
	}

	if response.Metadata["execution_time"] == nil {
		t.Error("Should have execution_time metadata")
	}

	if response.Metadata["steps_executed"] == nil {
		t.Error("Should have steps_executed metadata")
	}
}
