好的，遵照您的指示，我们来生成一份为 `community-forum-service` (社区论坛服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **富文本与内容生态**: 详细定义对富文本编辑器（如基于TipTap/ProseMirror）的支持，并明确与`file-storage-service`和`short-video-service`的集成，以支持图片和视频嵌入。
2.  **高级互动与排序**: 引入更丰富的互动（如收藏、分享）和更智能的排序算法（如热度算法），而不仅仅是按时间或投票。
3.  **版主与权限系统**: 细化版主(Moderator)权限体系，支持版块级别的精细化管理。
4.  **草稿与版本历史**: 增加对帖子草稿和编辑历史的支持，提升内容创作体验。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和扩展性指标，特别是针对高并发读写场景。

这份文档将描绘一个功能丰富、体验优秀、治理完善且能承载大规模用户互动的现代化社区论坛。

---

### CINA.CLUB - community-forum-service 需求规格说明书

**版本: 2.0 (生产级定义，支持富文本与高级互动)**  
**发布日期: 2025-06-21**  
**最后修订日期: 2025-06-21**  
**文档负责人:** [社区产品经理/技术负责人名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台内构建一个充满活力的用户社区，`community-forum-service` 旨在提供一个功能丰富、体验现代化的在线论坛。它允许用户围绕不同的话题创建内容丰富的帖子、进行深度讨论、分享经验和观点，从而增强用户归属感，促进用户间的知识交流和情感连接，并为平台沉淀有价值的用户生成内容 (UGC)。

#### 1.2. 服务范围
本服务 **负责**:
*   **版块/分类管理**: 创建和管理论坛的层级化版块。
*   **帖子/主题(Thread)管理**:
    *   支持**富文本**内容的发布、编辑、草稿保存和版本历史追溯。
    *   支持图片、视频等媒体内容的嵌入。
    *   管理帖子的生命周期（置顶、加精、锁定、删除）。
*   **回复/评论(Post)管理**: 支持嵌套回复、富文本内容和`@mentions`。
*   **高级互动功能**: 处理对帖子和回复的顶/踩、收藏、分享计数。
*   **标签(Tag)管理**: 支持用户为帖子打标签，并进行标签的聚合与发现。
*   **内容发现与排序**: 提供按最新、最热、精华等多种排序算法的帖子流。
*   **与平台生态的深度集成**: 包括内容审核、游戏化、通知、搜索等。

本服务 **不负责**:
*   结构化的问答 (由 `community-qa-service` 负责)。
*   即时通讯/私信 (由 `chat-service` 体系负责)。
*   文件二进制数据的存储 (由 `file-storage-service`, `short-video-service` 负责)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 用户通过客户端与论坛交互。
*   **`search-indexer-service`**: (消费本服务事件) 索引论坛内容以支持全局搜索。
*   **`content-moderation-service`**: (被本服务调用) 审核所有UGC。
*   **`gamification-service`**: (被本服务调用或消费事件) 根据用户行为（发帖、获赞）授予积分/徽章。
*   **`notification-dispatch-service`**: (被本服务调用) 发送新回复、@提及等通知。
*   **CINA.CLUB管理后台**: 管理员/版主管理版块、帖子和用户。

#### 1.4. 定义与缩略语
*   **Thread**: 帖子/主题，论坛讨论的起点。
*   **Post**: 回复/评论，对Thread或其他Post的回应。
*   **Rich Text Content**: 富文本内容，通常以JSON格式（如TipTap/ProseMirror的JSON输出）存储，可被渲染为复杂的HTML。
*   **Hotness Score**: 热度分，一个综合了浏览、回复、投票和时间的动态评分。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`community-forum-service` 是平台**用户互动和长篇内容沉淀的主要场所**。它通过提供开放式讨论空间，与强调解决具体问题的Q&A服务互补，共同构成CINA.CLUB的社区核心。它是一个典型的UGC密集型服务，对内容管理、审核、发现和用户激励有很高的要求。

#### 2.2. 主要功能概述
*   支持富文本和嵌入式媒体的内容创作体验。
*   基于热度算法的内容发现机制。
*   精细化的版主权限管理体系。
*   完善的草稿与编辑历史功能。
*   与平台生态的深度事件驱动集成。

### 3. 核心流程图

#### 3.1. 用户发布带图片的帖子流程
```mermaid
sequenceDiagram
    participant Client
    participant FileStorageService as FSS
    participant ForumService as FS
    participant MQ
    participant ContentModeration as CMS
    participant SearchIndexer as SI

    Client->>FSS: 1. Request image upload URL
    FSS-->>Client: (Presigned URL)
    Client->>ObjectStorage: 2. Upload image
    ObjectStorage-->>Client: (Image Key/URL)

    Client->>FS: 3. POST /threads (title, content_json_with_image_url, tags)
    FS->>FS: 4. Validate request, save thread as DRAFT or PENDING_MODERATION
    FS-->>Client: 202 Accepted (threadId)

    FS->>MQ: 5. Publish ThreadSubmittedEvent
    
    MQ-->>CMS: 6a. Consume event, start content moderation
    MQ-->>SI: 6b. Consume event, start indexing process (initially hidden)

    CMS-->>MQ: 7. Publish ModerationResultEvent (APPROVED)
    
    MQ-->>FS: 8. Consume moderation result
    FS->>DB: 9. Update thread status to PUBLISHED
    FS->>MQ: 10. Publish ThreadPublishedEvent (for search indexer to make public)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 内容创作与管理
*   **FR4.1.1 (富文本支持)**:
    *   帖子和回复的内容必须支持富文本，后端以结构化JSON格式（如ProseMirror/TipTap JSON）存储。
    *   后端提供API，能将此JSON安全地渲染为HTML（在服务器端完成，防止XSS）。
*   **FR4.1.2 (媒体嵌入)**: 用户在编辑器中必须能上传图片或选择已发布的短视频，并将其嵌入到内容中。本服务只存储媒体的引用ID或URL。
*   **FR4.1.3 (草稿)**: 用户在创建帖子时，系统必须能自动或手动地将其保存为草稿。草稿只有作者本人可见。
*   **FR4.1.4 (编辑历史)**: 对已发布的帖子或回复进行编辑时，系统必须保留历史版本。提供API供用户（或版主）查看和比较历史版本。

#### 4.2. 版块与权限
*   **FR4.2.1 (版块管理)**: 管理员能创建、编辑、排序和设置层级化的版块。
*   **FR4.2.2 (版主权限)**:
    *   管理员可以将用户任命为特定版块的**版主(Moderator)**。
    *   版主在其负责的版块内，拥有对帖子的置顶、加精、锁定、移动、删除等管理权限。

#### 4.3. 互动与发现
*   **FR4.3.1 (高级互动)**: 除了顶/踩，用户还可以**收藏(Bookmark)**和**分享(Share)**帖子。系统需要记录这些互动计数。
*   **FR4.3.2 (热度算法)**:
    *   帖子的默认排序（“热门”）应基于一个**热度分(Hotness Score)**。
    *   热度分算法是一个函数，输入包括：投票数、回复数、浏览量、分享数、以及**发布时间（时间衰减）**。例如，`Hotness = (P-1) / (T+2)^G` (Hacker News算法变体)。
    *   热度分应由后台任务定期计算并更新。

#### 4.4. 订阅与通知
*   **FR4.4.1 (订阅)**: 用户可以订阅感兴趣的帖子、版块或标签。
*   **FR4.4.2 (通知触发)**:
    *   当订阅的帖子有新回复时，通知订阅者。
    *   当用户的帖子/回复被回复或`@mention`时，通知该用户。
    *   所有通知通过调用`notification-dispatch-service`实现。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/community/forum`
*   **认证**: User JWT, Admin/Mod JWT。
*   **核心端点**:
    *   **Threads**:
        *   `GET /threads?category_id=...&tag=...&sort_by=hot|newest|top`: 列表与搜索。
        *   `POST /threads`: 创建新帖子。Request: `CreateThreadRequest { title, contentJson, tags, categoryId, status: "DRAFT"|"PUBLISHED" }`。
        *   `GET /threads/{threadId}`: 获取帖子详情（包含回复列表）。
        *   `PUT /threads/{threadId}`: 更新帖子。
        *   `GET /threads/{threadId}/history`: 获取编辑历史。
    *   **Posts**:
        *   `POST /threads/{threadId}/posts`: 创建回复。
    *   **Interactions**:
        *   `POST /threads/{id}/vote`, `POST /posts/{id}/vote`
        *   `POST /threads/{id}/bookmark`, `DELETE /threads/{id}/bookmark`
    *   **Admin/Mod Actions**:
        *   `POST /admin/threads/{id}/pin`: 置顶。
        *   `POST /admin/threads/{id}/lock`: 锁定。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`forum_categories`**: `id`, `name`, `slug`, `parent_id`, `display_order`, `denormalized_stats (JSONB)`.
*   **`forum_threads`**:
    *   `id (PK)`, `category_id (FK, INDEX)`, `author_user_id (FK, INDEX)`, `title`, `slug`
    *   `content_json (JSONB)`: 存储富文本结构化数据。
    *   `content_html (TEXT)`: 存储服务端渲染后的安全HTML。
    *   `view_count`, `reply_count`, `vote_score`, `bookmark_count`, `share_count`
    *   `hotness_score (DOUBLE PRECISION, INDEX)`
    *   `is_pinned`, `is_locked`, `is_essence`
    *   `status (VARCHAR, INDEX)`: `DRAFT`, `PENDING_MODERATION`, `PUBLISHED`, `DELETED`.
    *   `created_at`, `updated_at`, `last_reply_at (INDEX)`.
*   **`forum_thread_versions`**: `id`, `thread_id`, `editor_user_id`, `content_json`, `edit_reason`, `created_at`.
*   **`forum_posts`**: 类似`forum_threads`，但增加`parent_post_id (FK)`以支持嵌套。
*   **`forum_moderators`**: `(user_id, category_id)` (PK), `granted_by`, `granted_at`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**:
    *   帖子列表和详情页P99加载延迟 < 250ms。
    *   发帖/回帖/投票等写操作P99响应时间 < 150ms。
*   **热度计算**: 后台任务计算热度分不应影响在线API性能。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **数据一致性**: 互动计数等冗余数据的更新可以通过事务或最终一致性保证，但必须有对账和修复机制。

#### 7.3. 可扩展性需求
*   服务可水平扩展。
*   数据库可通过读写分离和缓存优化。对于海量帖子，`forum_threads`和`forum_posts`表需要考虑基于`category_id`或`created_at`进行分区。

#### 7.4. 安全性需求
*   **XSS防护**: 所有UGC内容在存储和渲染时必须经过严格的清理和转义。富文本JSON在服务器端渲染为HTML是关键。
*   **内容审核**: 所有UGC必须提交到`content-moderation-service`。
*   **权限控制**: 严格的权限检查，确保用户、版主、管理员只能执行其被授权的操作。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。利用其强大的事务能力、JSONB字段和全文检索功能。
*   **富文本处理**:
    *   前端: 使用TipTap, ProseMirror, Slate.js等现代富文本编辑器。
    *   后端: 使用Go的HTML清理库（如`bluemonday`）对编辑器生成的HTML或服务器端渲染的HTML进行严格的XSS过滤。
*   **异步处理**: 内容审核、通知、积分、热度分计算等所有非核心阻塞操作，都应通过后台任务或消息队列进行异步处理。
*   **数据冗余 (Denormalization)**: 在`forum_categories`和`forum_threads`中冗余存储统计数据（如回复数、最后回复信息）对于提升列表页查询性能至关重要。

---
这份版本2.0的SRS文档为`community-forum-service`构建了一个功能强大、体验现代、治理完善的社区平台。它通过支持富文本和高级互动，并与平台生态深度集成，旨在为CINA.CLUB用户提供一个富有吸引力和归属感的交流空间。