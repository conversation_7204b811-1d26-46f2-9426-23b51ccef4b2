好的，遵照您的指示。我将为您生成一份专门针对 **Monorepo `pkg/` 目录** 的、极致细化的、生产级**架构设计文档**。

这份文档将作为所有**后端开发人员**的“标准工具库”和“最佳实践指南”。它定义了所有后端微服务共享的、与业务无关的通用基础设施能力，旨在确保平台后端技术栈的统一、高效和可靠。

---
### CINA.CLUB - 共享后端库 (`pkg/`) 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [平台总架构师/首席工程师]

## 1. 概述

`pkg/` 目录是CINA.CLUB后端Monorepo中的**后端专用共享代码库**。它包含了一系列被所有或多数微服务共同依赖的、与具体业务逻辑解耦的通用功能模块。`pkg/`的存在是为了解决微服务架构中的横切关注点(Cross-Cutting Concerns)问题，其核心目的在于：
*   **消除重复**: 避免在每个微服务中重复编写如数据库连接、日志配置、认证中间件等基础设施代码。
*   **强制统一**: 确保所有服务在基础能力（如错误处理、可观测性）上遵循同一套标准和最佳实践。
*   **提升开发效率**: 为业务开发者提供一个稳定、易用、功能强大的“工具箱”，使其能专注于实现业务逻辑。
*   **集中维护**: 对基础能力的升级、bug修复或性能优化，只需在`pkg/`中一处完成，即可惠及整个后端平台。

### 1.1 与 `core/` 的核心区别

`pkg/` 和 `core/` 都是共享库，但它们的范围和职责有本质区别：

| 特性         | `core/` (Universal Core)                               | `pkg/` (Backend Shared Packages)                             |
|--------------|--------------------------------------------------------|--------------------------------------------------------------|
| **目标用户** | **所有端** (后端, iOS, Android, Web)                     | **仅后端微服务**                                             |
| **依赖**     | **零依赖** (除标准库和极少数核心库)                      | 可以依赖 `core/` 和其他 `pkg/` 包                            |
| **内容**     | 平台无关的纯逻辑、API契约、通用模型 (如E2EE算法)         | 与后端技术栈强相关的封装 (如gRPC中间件、数据库客户端工厂)      |
| **变更影响** | **极高**，影响整个平台所有组件                           | **高**，影响所有后端服务                                     |

**一句话总结：`core/`是平台的“DNA”，定义了“是什么”；`pkg/`是后端的“工具箱”，定义了“怎么做”。**

---

## 2. 架构图与目录结构

### 2.1 依赖关系图

```mermaid
graph TD
    subgraph "services/*"
        A[所有微服务]
    end

    subgraph "pkg/*"
        B[pkg/middleware]
        C[pkg/auth]
        D[pkg/database]
        E[pkg/messaging]
        F[pkg/logger]
        G[pkg/tracing]
        H[pkg/config]
        I[pkg/errors]
        J[pkg/workflow]
        K[pkg/utils]
    end
    
    subgraph "core/*"
        L[core/api]
        M[core/models]
    end

    A --> B & C & D & E & F & G & H & I & J & K
    
    B --> F & G & I
    C --> I & F & L
    D --> H & F & G
    E --> H & F & G & L
    J --> I
    
    F & I & H --> K
    
    K -- "无自定义依赖" --> GoStdLib([Go Standard Library])
```

### 2.2 最终目录结构 (`pkg/`)

```
pkg/
├── auth/         # 认证与授权中间件
├── config/       # 配置加载与解析
├── database/     # 数据库客户端工厂
├── errors/       # 标准化错误处理
├── logger/       # 结构化日志记录器
├── messaging/    # 消息队列(Kafka)客户端封装
├── middleware/   # 通用gRPC中间件 (日志, 追踪, 恢复等)
├── tracing/      # 分布式追踪(OpenTelemetry)初始化
├── workflow/     # 通用工作流引擎核心
├── utils/        # 零依赖通用工具函数
│   ├── conv/
│   ├── crypto/
│   ├── rand/
│   └── ...
└── go.mod        # (可选) 如果pkg作为一个大的独立模块
```

---

## 3. 各包职责深度解析 (功能与实现要点)

### 3.1 `pkg/config` - 配置加载器
*   **职责**: 从文件和环境变量中加载配置，并解析到强类型的Go `struct`中。
*   **核心实现**:
    *   **底层库**: `spf13/viper`。
    *   **加载流程**: `Defaults -> File -> Env Vars -> Flags`。
    *   **验证**: 成功解析后，**必须**使用`go-playground/validator`对`struct`进行验证。
*   **核心价值**: 统一所有服务的配置加载方式，支持不同环境的灵活覆盖。

### 3.2 `pkg/logger` - 结构化日志记录器
*   **职责**: 提供一个全平台统一的、上下文感知的结构化日志记录器。
*   **核心实现**:
    *   **底层库**: Go 1.21+ 标准库 **`log/slog`**。
    *   **输出格式**: 生产环境强制为**JSON**。
    *   **上下文感知**: 提供`FromContext(ctx)`函数，自动从`context.Context`中提取`trace_id`等信息，并作为日志的固定字段。
*   **核心价值**: 保证所有日志格式统一、可查询，并与分布式追踪无缝集成。

### 3.3 `pkg/errors` - 标准化错误处理
*   **职责**: 定义平台的错误码体系和标准的错误`struct`。
*   **核心实现**:
    *   **`AppError` struct**: 包含`Code` (枚举), `Message` (对内), `Cause` (原始错误), `Metadata`。
    *   **gRPC集成**: 提供`ToGRPCStatus`和`FromGRPCError`函数，实现`AppError`与gRPC `status`的无损转换，利用`details`字段传递结构化错误信息。
*   **核心价值**: 规范化错误传递，使跨服务错误排查和面向客户端的错误呈现变得简单、一致。

### 3.4 `pkg/tracing` - 分布式追踪初始化器
*   **职责**: 封装OpenTelemetry SDK的初始化和配置。
*   **核心实现**:
    *   **`Init(cfg)`函数**: 根据配置，创建并注册全局的`TracerProvider`。
    *   **动态Exporter**: 支持根据配置选择Jaeger, OTLP-gRPC/HTTP等不同的导出器。
    *   **动态Sampler**: 支持根据配置选择不同的采样策略。
*   **核心价值**: 一行代码即可为任何服务启用符合平台标准的分布式追踪能力。

### 3.5 `pkg/middleware` - 通用gRPC拦截器
*   **职责**: 提供一系列可组合的、与业务无关的gRPC服务器拦截器。
*   **核心实现**:
    *   **Recovery**: 捕获panic，转换为`Internal`错误。
    *   **Tracing**: 创建和传播Trace Span。
    *   **Logging**: 记录每个请求的详细信息，并为请求上下文注入带追踪信息的Logger。
    *   **Metrics**: 收集标准的RED (Rate, Errors, Duration) Prometheus指标。
    *   **Validation**: 自动调用请求消息上的`Validate()`方法。
    *   **Chaining**: 使用`go-grpc-middleware/v2`来链接多个拦截器。
*   **核心价值**: 将所有请求的通用横切逻辑集中实现，保持gRPC handler的干净。

### 3.6 `pkg/auth` - 认证授权中间件
*   **职责**: 提供用于S2S和Admin用户认证授权的gRPC拦截器。
*   **核心实现**:
    *   **JWKS自动刷新客户端**: 用于验证最终用户的Access Token。
    *   **S2S Token验证器**: 用于验证服务间的JWT。
    *   **RBAC拦截器**: 根据Token中的`roles`和预加载的权限策略进行授权检查。
*   **核心价值**: 统一平台的API安全策略，将复杂的认证逻辑从业务服务中剥离。

### 3.7 `pkg/database` - 数据库客户端工厂
*   **职责**: 提供创建带可观测性的、高性能的数据库客户端的工厂函数。
*   **核心实现**:
    *   为**PostgreSQL (`pgx`)**, **MongoDB**, **Redis**提供`New...`工厂函数。
    *   **可观测性内建**: 所有创建的客户端都**必须**自动集成OpenTelemetry的检测插件和日志钩子。
*   **核心价值**: 简化数据库连接的创建，并强制所有数据库操作都可被追踪和监控。

### 3.8 `pkg/messaging` - 消息队列客户端
*   **职责**: 封装与Kafka的交互，提供可靠的、类型安全的生产者和消费者。
*   **核心实现**:
    *   **`Producer`**: 异步、批量发送，自动注入Trace Context。
    *   **`ConsumerGroup`**: 管理消费者组生命周期，提供“至少一次”消费语义，并内置重试和DLQ逻辑。
    *   **序列化**: 强制使用`core/api`中定义的Protobuf进行消息体的序列化/反序列化。
*   **核心价值**: 极大地简化了事件驱动编程，开发者只需关注消息处理的业务逻辑。

### 3.9 `pkg/workflow` - 通用工作流引擎
*   **职责**: 提供一个轻量级、无状态、基于DAG的进程内工作流执行引擎。
*   **核心实现**:
    *   **`Executor`**: 解析工作流图并按拓扑顺序执行。
    *   **`NodeExecutor`接口**: 定义了动作节点的标准接口，由上层服务（如`ai-assistant-service`）实现和注册。
*   **核心价值**: 为需要流程编排的服务提供了一个可复用的、与业务逻辑解耦的核心引擎。

### 3.10 `pkg/utils` - 零依赖通用工具
*   **职责**: 提供完全通用的、不依赖任何其他`pkg/`或`core/`包的辅助函数。
*   **核心实现**:
    *   划分为`conv`, `crypto`, `rand`, `slice`, `str`, `timeutil`等子包。
    *   **零外部依赖**（除Go标准库）。
*   **核心价值**: 平台最稳定、最基础的工具集。

## 4. 总结

`pkg/`目录是CINA.CLUB后端工程卓越性的体现。它通过将所有通用基础设施逻辑沉淀为一系列高质量、标准化的共享库，实现了以下目标：

1.  **开发标准化**: 所有服务都使用相同的“积木”来构建其基础能力。
2.  **效率最大化**: 开发者无需关心底层细节，可以像调用标准库一样使用平台提供的强大能力。
3.  **质量内建**: 性能、安全、可观测性等非功能性需求，在`pkg/`层级就已经被系统性地解决。
4.  **维护集中化**: 对基础库的任何改进，都能立即赋能整个后端平台。

这份架构设计确保了`pkg/`库能够作为所有后端微服务的坚实地基，有力地支撑上层业务的快速、稳健发展。