/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package logger

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// Context key types to avoid collisions
type auditContextKey string

const (
	auditTraceIDKey auditContextKey = "trace_id"
)

// AuditLogger implements the audit logging interface with Kafka producer support
type AuditLogger struct {
	logger   *logrus.Logger
	producer KafkaProducer
	topic    string
	enabled  bool
	buffer   chan *model.AuditLogEntry
	wg       sync.WaitGroup
	ctx      context.Context
	cancel   context.CancelFunc
}

// KafkaProducer interface for message production
type KafkaProducer interface {
	SendMessage(topic string, key string, value []byte) error
	Close() error
}

// MockKafkaProducer provides a mock implementation for development/testing
type MockKafkaProducer struct {
	logger *logrus.Logger
}

// NewMockKafkaProducer creates a new mock Kafka producer
func NewMockKafkaProducer(logger *logrus.Logger) KafkaProducer {
	return &MockKafkaProducer{
		logger: logger,
	}
}

// SendMessage simulates sending a message to Kafka
func (m *MockKafkaProducer) SendMessage(topic string, key string, value []byte) error {
	m.logger.WithFields(logrus.Fields{
		"topic": topic,
		"key":   key,
		"size":  len(value),
	}).Debug("Mock Kafka message sent")
	return nil
}

// Close simulates closing the Kafka producer
func (m *MockKafkaProducer) Close() error {
	m.logger.Debug("Mock Kafka producer closed")
	return nil
}

// NewAuditLogger creates a new audit logger instance
func NewAuditLogger(logger *logrus.Logger, producer KafkaProducer, topic string, enabled bool) port.AuditLogger {
	ctx, cancel := context.WithCancel(context.Background())

	auditLogger := &AuditLogger{
		logger:   logger,
		producer: producer,
		topic:    topic,
		enabled:  enabled,
		buffer:   make(chan *model.AuditLogEntry, 1000), // Buffer up to 1000 entries
		ctx:      ctx,
		cancel:   cancel,
	}

	// Start background worker for async processing
	auditLogger.wg.Add(1)
	go auditLogger.processAuditLogs()

	return auditLogger
}

// LogEntry logs an audit entry asynchronously
func (a *AuditLogger) LogEntry(ctx context.Context, entry *model.AuditLogEntry) error {
	if !a.enabled {
		return nil
	}

	// Validate the audit log entry
	if err := entry.Validate(); err != nil {
		a.logger.WithError(err).Error("Invalid audit log entry")
		return fmt.Errorf("invalid audit log entry: %w", err)
	}

	// Extract trace ID from context if available
	if traceID := extractTraceID(ctx); traceID != "" {
		entry.SetTraceID(traceID)
	}

	// Try to send to buffer (non-blocking)
	select {
	case a.buffer <- entry:
		return nil
	default:
		// Buffer is full, log error and try to send synchronously
		a.logger.Error("Audit log buffer is full, sending synchronously")
		return a.sendAuditLog(entry)
	}
}

// LogAction is a convenience method for logging simple actions
func (a *AuditLogger) LogAction(ctx context.Context, actorID, actorEmail, actorIP, action, resourceType, resourceID string) error {
	entry := model.NewAuditLogEntry(actorID, actorEmail, actorIP)
	entry.SetResource(resourceType, resourceID, action)
	return a.LogEntry(ctx, entry)
}

// LogHTTPRequest logs an HTTP request as an audit entry
func (a *AuditLogger) LogHTTPRequest(ctx context.Context, actorID, actorEmail, actorIP, method, path string, body map[string]interface{}, statusCode int, responseTime int64, success bool) error {
	entry := model.NewAuditLogEntry(actorID, actorEmail, actorIP)
	entry.SetRequest(method, path, body)
	entry.SetResponse(statusCode, "", success)
	entry.SetResponseTime(responseTime)
	entry.SetResource("http_request", path, method)
	return a.LogEntry(ctx, entry)
}

// LogBatch logs multiple audit entries in batch
func (a *AuditLogger) LogBatch(ctx context.Context, entries []*model.AuditLogEntry) error {
	for _, entry := range entries {
		if err := a.LogEntry(ctx, entry); err != nil {
			return err
		}
	}
	return nil
}

// GetLogs retrieves audit logs based on filter (mock implementation)
func (a *AuditLogger) GetLogs(ctx context.Context, filter model.AuditLogFilter) ([]*model.AuditLogEntry, error) {
	// This would typically query from a database or search system
	// For now, return empty slice as this is primarily a write-only audit logger
	a.logger.WithFields(logrus.Fields{
		"actor_id":      filter.ActorID,
		"resource_type": filter.ResourceType,
		"action":        filter.Action,
	}).Debug("GetLogs called (mock implementation)")

	return []*model.AuditLogEntry{}, nil
}

// GetLogCount gets total audit log count (mock implementation)
func (a *AuditLogger) GetLogCount(ctx context.Context, filter model.AuditLogFilter) (int64, error) {
	// This would typically count from a database or search system
	a.logger.Debug("GetLogCount called (mock implementation)")
	return 0, nil
}

// Close gracefully shuts down the audit logger
func (a *AuditLogger) Close() error {
	a.logger.Info("Shutting down audit logger...")

	// Cancel context to stop background worker
	a.cancel()

	// Wait for background worker to finish
	a.wg.Wait()

	// Close the producer
	return a.producer.Close()
}

// processAuditLogs processes audit log entries in the background
func (a *AuditLogger) processAuditLogs() {
	defer a.wg.Done()

	ticker := time.NewTicker(100 * time.Millisecond) // Process every 100ms
	defer ticker.Stop()

	var batch []*model.AuditLogEntry
	const maxBatchSize = 10

	for {
		select {
		case <-a.ctx.Done():
			// Process remaining entries in buffer
			a.processBatch(batch)
			a.drainBuffer()
			return

		case entry := <-a.buffer:
			batch = append(batch, entry)
			if len(batch) >= maxBatchSize {
				a.processBatch(batch)
				batch = batch[:0] // Reset batch
			}

		case <-ticker.C:
			if len(batch) > 0 {
				a.processBatch(batch)
				batch = batch[:0] // Reset batch
			}
		}
	}
}

// processBatch processes a batch of audit log entries
func (a *AuditLogger) processBatch(batch []*model.AuditLogEntry) {
	for _, entry := range batch {
		if err := a.sendAuditLog(entry); err != nil {
			a.logger.WithError(err).WithField("entry_id", entry.ID).Error("Failed to send audit log")
		}
	}
}

// drainBuffer drains any remaining entries in the buffer
func (a *AuditLogger) drainBuffer() {
	for {
		select {
		case entry := <-a.buffer:
			if err := a.sendAuditLog(entry); err != nil {
				a.logger.WithError(err).WithField("entry_id", entry.ID).Error("Failed to send audit log during shutdown")
			}
		default:
			return
		}
	}
}

// sendAuditLog sends a single audit log entry to Kafka
func (a *AuditLogger) sendAuditLog(entry *model.AuditLogEntry) error {
	// Convert to JSON
	data, err := entry.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to marshal audit log entry: %w", err)
	}

	// Send to Kafka using actor ID as the key for partitioning
	key := entry.ActorID
	if err := a.producer.SendMessage(a.topic, key, data); err != nil {
		return fmt.Errorf("failed to send audit log to Kafka: %w", err)
	}

	// Log locally for debugging
	a.logger.WithFields(logrus.Fields{
		"audit_id":      entry.ID,
		"actor_id":      entry.ActorID,
		"action":        entry.Action,
		"resource_type": entry.ResourceType,
		"resource_id":   entry.ResourceID,
		"success":       entry.Success,
		"severity":      entry.GetSeverity(),
	}).Debug("Audit log entry sent")

	return nil
}

// extractTraceID extracts the trace ID from the context
func extractTraceID(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	// This would typically extract from OpenTelemetry or similar
	if traceID, ok := ctx.Value(auditTraceIDKey).(string); ok {
		return traceID
	}
	return ""
}

// AuditLoggerConfig holds configuration for the audit logger
type AuditLoggerConfig struct {
	Enabled    bool   `json:"enabled"`
	Topic      string `json:"topic"`
	BufferSize int    `json:"buffer_size"`
	BatchSize  int    `json:"batch_size"`
}

// DefaultAuditLoggerConfig returns default configuration
func DefaultAuditLoggerConfig() AuditLoggerConfig {
	return AuditLoggerConfig{
		Enabled:    true,
		Topic:      "admin-audit-logs",
		BufferSize: 1000,
		BatchSize:  10,
	}
}
