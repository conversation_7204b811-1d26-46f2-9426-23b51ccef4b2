/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

/**
 * CINA.CLUB shape configuration.
 * Defines consistent corner radius and shapes across the app.
 */

val CinaClubShapes = Shapes(
    // Extra small shapes - for small components like chips, badges
    extraSmall = RoundedCornerShape(4.dp),
    
    // Small shapes - for buttons, input fields
    small = RoundedCornerShape(8.dp),
    
    // Medium shapes - for cards, dialogs
    medium = RoundedCornerShape(12.dp),
    
    // Large shapes - for bottom sheets, large cards
    large = RoundedCornerShape(16.dp),
    
    // Extra large shapes - for full-screen modals
    extraLarge = RoundedCornerShape(24.dp)
)

/**
 * Additional custom shapes for specific use cases.
 */
object CinaClubCustomShapes {
    // Chat bubble shapes
    val ChatBubbleLeft = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 4.dp,
        bottomEnd = 16.dp
    )
    
    val ChatBubbleRight = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 16.dp,
        bottomEnd = 4.dp
    )
    
    // Avatar shapes
    val AvatarSmall = RoundedCornerShape(20.dp)  // 40dp diameter
    val AvatarMedium = RoundedCornerShape(24.dp) // 48dp diameter
    val AvatarLarge = RoundedCornerShape(32.dp)  // 64dp diameter
    
    // Button shapes
    val ButtonRounded = RoundedCornerShape(24.dp)
    val ButtonSquare = RoundedCornerShape(8.dp)
    
    // Input field shapes
    val InputField = RoundedCornerShape(8.dp)
    val SearchField = RoundedCornerShape(20.dp)
    
    // Card shapes
    val CardElevated = RoundedCornerShape(12.dp)
    val CardFlat = RoundedCornerShape(8.dp)
    
    // Modal shapes
    val BottomSheet = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 0.dp,
        bottomEnd = 0.dp
    )
    
    val Dialog = RoundedCornerShape(16.dp)
} 