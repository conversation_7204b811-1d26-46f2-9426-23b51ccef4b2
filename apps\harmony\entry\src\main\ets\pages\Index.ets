/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { DependencyInjector } from '../common/di/DependencyInjector';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { HomeTabContent } from '../components/tabs/HomeTabContent';
import { ChatTabContent } from '../components/tabs/ChatTabContent';
import { PKBTabContent } from '../components/tabs/PKBTabContent';
import { ProfileTabContent } from '../components/tabs/ProfileTabContent';

/**
 * CINA.CLUB主页面
 * 
 * 实现底部Tab导航，包含：
 * - 首页：推荐内容、快讯、服务市场
 * - 聊天：实时通信、AI助手
 * - PKB：个人知识库
 * - 我的：个人中心、设置
 */
@Entry
@Component
struct Index {
  @State private currentTabIndex: number = 0;
  @State private isLoading: boolean = true;
  @State private isUserLoggedIn: boolean = false;
  
  private static readonly TAG = 'IndexPage';
  
  // 底部导航配置
  private readonly tabBarData: TabBarItem[] = [
    {
      icon: $r('app.media.ic_home'),
      selectedIcon: $r('app.media.ic_home_selected'),
      text: '首页',
      index: 0
    },
    {
      icon: $r('app.media.ic_chat'),
      selectedIcon: $r('app.media.ic_chat_selected'),
      text: '聊天',
      index: 1
    },
    {
      icon: $r('app.media.ic_pkb'),
      selectedIcon: $r('app.media.ic_pkb_selected'),
      text: 'PKB',
      index: 2
    },
    {
      icon: $r('app.media.ic_profile'),
      selectedIcon: $r('app.media.ic_profile_selected'),
      text: '我的',
      index: 3
    }
  ];

  aboutToAppear(): void {
    hilog.info(0x0000, Index.TAG, 'Index page about to appear');
    this.initializePage();
  }

  aboutToDisappear(): void {
    hilog.info(0x0000, Index.TAG, 'Index page about to disappear');
  }

  build() {
    Column() {
      if (this.isLoading) {
        this.LoadingView()
      } else {
        this.MainContent()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.background_primary'))
  }

  /**
   * 加载视图
   */
  @Builder
  LoadingView() {
    Column() {
      Image($r('app.media.logo'))
        .width(120)
        .height(120)
        .margin({ bottom: 20 })
      
      Text('CINA.CLUB')
        .fontSize(24)
        .fontColor($r('app.color.text_primary'))
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 10 })
      
      Text('智能生活，无限可能')
        .fontSize(14)
        .fontColor($r('app.color.text_secondary'))
        .margin({ bottom: 40 })
      
      LoadingProgress()
        .width(40)
        .height(40)
        .color($r('app.color.primary'))
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 主要内容
   */
  @Builder
  MainContent() {
    Column() {
      // 内容区域
      Tabs({ 
        barPosition: BarPosition.End,
        index: this.currentTabIndex 
      }) {
        TabContent() {
          HomeTabContent()
        }
        .tabBar(this.TabBarBuilder(this.tabBarData[0]))

        TabContent() {
          if (this.isUserLoggedIn) {
            ChatTabContent()
          } else {
            this.LoginPromptView('聊天功能需要登录')
          }
        }
        .tabBar(this.TabBarBuilder(this.tabBarData[1]))

        TabContent() {
          if (this.isUserLoggedIn) {
            PKBTabContent()
          } else {
            this.LoginPromptView('个人知识库需要登录')
          }
        }
        .tabBar(this.TabBarBuilder(this.tabBarData[2]))

        TabContent() {
          ProfileTabContent()
        }
        .tabBar(this.TabBarBuilder(this.tabBarData[3]))
      }
      .onChange((index: number) => {
        this.currentTabIndex = index;
        hilog.info(0x0000, Index.TAG, `Tab changed to index: ${index}`);
      })
      .barMode(BarMode.Fixed)
      .barHeight(80)
      .width('100%')
      .height('100%')
    }
  }

  /**
   * 登录提示视图
   */
  @Builder
  LoginPromptView(message: string) {
    Column() {
      Image($r('app.media.ic_login_prompt'))
        .width(80)
        .height(80)
        .margin({ bottom: 20 })
      
      Text(message)
        .fontSize(16)
        .fontColor($r('app.color.text_secondary'))
        .margin({ bottom: 30 })
      
      Button('立即登录')
        .width(200)
        .height(44)
        .fontSize(16)
        .fontColor($r('app.color.white'))
        .backgroundColor($r('app.color.primary'))
        .borderRadius(22)
        .onClick(() => {
          this.navigateToLogin();
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 底部标签构建器
   */
  @Builder
  TabBarBuilder(item: TabBarItem) {
    Column() {
      Image(this.currentTabIndex === item.index ? item.selectedIcon : item.icon)
        .width(24)
        .height(24)
        .margin({ bottom: 4 })
      
      Text(item.text)
        .fontSize(10)
        .fontColor(this.currentTabIndex === item.index ? 
          $r('app.color.primary') : 
          $r('app.color.text_tertiary'))
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 初始化页面
   */
  private async initializePage(): Promise<void> {
    try {
      // 检查用户登录状态
      const userManager = DependencyInjector.getInstance().getUserManager();
      this.isUserLoggedIn = await userManager.checkLoginStatus();
      
      // 初始化完成
      this.isLoading = false;
      
      hilog.info(0x0000, Index.TAG, 'Page initialized successfully');
    } catch (error) {
      hilog.error(0x0000, Index.TAG, `Failed to initialize page: ${error.message}`);
      this.isLoading = false;
    }
  }

  /**
   * 导航到登录页面
   */
  private navigateToLogin(): void {
    router.pushUrl({
      url: 'pages/auth/LoginPage'
    }).catch((error) => {
      hilog.error(0x0000, Index.TAG, `Failed to navigate to login: ${error.message}`);
    });
  }
}

/**
 * 底部导航栏项目接口
 */
interface TabBarItem {
  icon: Resource;
  selectedIcon: Resource;
  text: string;
  index: number;
} 