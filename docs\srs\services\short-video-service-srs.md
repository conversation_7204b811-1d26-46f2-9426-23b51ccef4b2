﻿好的，遵照您的指示，我们来生成一份为 `short-video-service` (短视频服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **上传与转码工作流**: 详细定义客户端分块上传、断点续传的支持，以及服务端基于事件驱动的、可配置的、多清晰度的转码流水线。
2.  **智能内容处理**: 引入AI能力，在转码工作流中自动进行视频封面智能选取、内容标签提取和语音识别（ASR）生成字幕。
3.  **分发与CDN策略**: 细化视频的分发策略，包括CDN集成、防盗链（Signed URLs）和基于用户网络状况的自适应比特率（ABR）流媒体。
4.  **数据模型与API**: 引入更丰富的视频元数据（如拍摄设备、地理位置），并提供更具体的API请求/响应体示例和事件契约。
5.  **互动与推荐支持**: 增加对评论、弹幕（未来）等互动数据的聚合，并为推荐系统提供所需的数据信号。
6.  **强化非功能性需求**: 补充更具体、可量化的性能指标（如首帧加载时间TTFF、卡顿率），以及对成本控制的考量。

这份文档将描绘一个功能强大、性能卓越、体验流畅且能应对海量视频内容的现代化短视频平台后端。

---

### CINA.CLUB - short-video-service 需求规格说明书

**版本: 2.0 (生产级定义，集成智能处理与高级分发)**  
**发布日期: 2025-06-25**  
**最后修订日期: 2025-06-25**  
**文档负责人:** [多媒体/内容平台团队负责人名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了丰富CINA.CLUB平台的内容形态，增强用户表达和信息传递的生动性，`short-video-service` 旨在提供一个**端到端、高效、可靠且智能**的短视频解决方案。用户可以通过此服务上传、管理和分享短视频，用于生活记录、服务展示、案例说明等多种场景。本服务的核心是构建一个能处理海量视频上传、执行复杂转码工作流、并通过CDN进行全球加速分发的高性能媒体处理与管理后端。

#### 1.2. 服务范围
本服务 **负责**:
*   **视频上传**:
    *   协调客户端进行**分块上传**和**断点续传**。
    *   通过预签名URL机制保证上传安全。
*   **视频处理与转码工作流**:
    *   编排异步的、事件驱动的视频处理流水线。
    *   **转码**: 生成多种分辨率（如1080p, 720p, 480p）的**自适应比特率(ABR)**流媒体（HLS/DASH）。
    *   **智能内容处理**: 自动截取高质量封面、生成预览雪碧图、提取音频并进行语音识别(ASR)生成字幕。
*   **视频元数据管理**: CRUD视频的丰富元数据（标题、描述、标签、分类、拍摄地点等）。
*   **视频分发与播放**:
    *   提供**CDN加速**的、多清晰度自适应的视频播放地址。
    *   通过**防盗链**机制（如Signed URLs）保护私有或付费内容。
*   **互动数据聚合**: 聚合视频的观看数、点赞数、评论数、分享数等互动数据。
*   **内容审核集成**: 将视频内容（关键帧、音频转录文本）提交给`content-moderation-service`。

本服务 **不负责**:
*   **直播流媒体**。
*   **客户端视频编辑** (剪辑、滤镜等在客户端完成)。
*   **复杂的评论/弹幕系统** (由社区服务负责，本服务只记录计数和关联)。
*   **文件二进制数据的持久化存储** (由 `file-storage-service` 负责)。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`short-video-service` 是平台**富媒体内容的核心引擎**。它为各种业务场景提供了基础的视频能力，极大地提升了信息密度和用户参与感。它是一个典型的I/O密集型和计算密集型（转码部分）的服务，其架构设计必须以**异步化、事件驱动、可伸缩性**为核心原则，并重点关注**用户播放体验**和**成本控制**。

#### 2.2. 主要功能概述
*   支持分块上传和断点续传的安全上传流程。
*   集成了AI能力的、异步的视频处理工作流。
*   基于HLS/DASH和CDN的、带防盗链的高性能分发。
*   全面的元数据管理和互动数据聚合。

### 3. 核心流程图

#### 3.1. 视频分块上传与异步处理流程
```mermaid
sequenceDiagram
    participant Client
    participant ShortVideoService as SVS
    participant FileStorageService as FSS
    participant MQ
    participant TranscodingWorker

    Client->>SVS: 1. POST /uploads/initiate (fileName, fileSize)
    SVS->>FSS: 2. Initiate Multipart Upload
    FSS-->>SVS: (uploadId)
    SVS->>DB: 3. Create Video record (status: UPLOADING, uploadId)
    SVS-->>Client: 4. (fileKey, uploadId)

    loop For each chunk
        Client->>SVS: 5. GET /uploads/{uploadId}/part-url?partNumber=...
        SVS->>FSS: 6. Generate Presigned URL for one part
        FSS-->>SVS: (presignedUrl)
        SVS-->>Client: (presignedUrl)
        Client->>ObjectStorage: 7. Upload chunk
        ObjectStorage-->>Client: (ETag)
    end
    
    Client->>SVS: 8. POST /uploads/{uploadId}/complete (parts: [{partNumber, ETag}])
    SVS->>FSS: 9. Complete Multipart Upload
    SVS->>DB: 10. Update Video status to PROCESSING
    SVS->>MQ: 11. Publish VideoProcessingJobRequest event
    SVS-->>Client: 202 Accepted
    
    MQ-->>TranscodingWorker: 12. Consume event... (start transcoding, ASR, etc.)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 视频上传
*   **FR4.1.1 (分块上传)**: 系统必须支持大文件的分块上传和断点续传，以提高在不稳定网络下的上传成功率。
    *   **流程**: `initiate` -> 循环`get_part_url`并上传 -> `complete`。
*   **FR4.1.2 (上传前校验)**: 在`initiate`阶段，系统应对文件类型、大小、时长进行预校验。

#### 4.2. 智能处理与转码工作流 (异步)
*   **FR4.2.1 (工作流触发)**: 在`complete`上传后，系统必须向MQ发布一个`VideoProcessingJobRequest`事件。
*   **FR4.2.2 (转码)**: 后台Worker消费此事件，使用FFmpeg或云转码服务，将原始视频转码为多种分辨率的HLS/DASH流媒体格式。
*   **FR4.2.3 (智能封面)**: Worker必须能**分析视频内容**（如使用场景识别、人脸检测模型），自动选取**多个高质量的候选封面帧**，并默认选择最优的一个。用户后续可以更换。
*   **FR4.2.4 (ASR字幕)**: Worker必须提取音频轨道，并调用`ai-assistant-service`或专门的语音转文字服务，生成**带时间戳的字幕文件(VTT/SRT)**。
*   **FR4.2.5 (结果上报)**: Worker处理完成后，将所有产物上传到对象存储，并发布一个`VideoProcessingCompletedEvent`（包含所有产物的key和元数据）。

#### 4.3. 视频分发与播放
*   **FR4.3.1 (播放信息API)**: `GET /videos/{id}`接口必须返回一个包含所有播放所需信息的`PlayInfo`对象。
*   **FR4.3.2 (ABR流)**: `PlayInfo`中必须包含一个主播放列表的URL (HLS .m3u8 / DASH .mpd)，该URL应指向**CDN**。
*   **FR4.3.3 (防盗链)**: 对于私有或付费视频，返回的CDN URL必须是**签名的、有时效性的** (CDN Signed URLs)。本服务负责生成签名。

#### 4.4. 互动与推荐支持
*   **FR4.4.1 (互动计数)**: 本服务应聚合存储视频的`likes_count`, `comments_count`, `shares_count`, `views_count`等。这些计数的更新主要通过消费来自其他服务的事件实现。
*   **FR4.4.2 (数据信号)**: 在`VideoPublishedEvent`中，应包含足够丰富的数据信号（如AI提取的标签、分类、时长、作者信息），供推荐系统和搜索系统使用。

#### 4.5. 内容审核与隐私控制
*   **FR4.5.1 (异步审核)**: 在视频处理过程中，系统必须将关键帧、转录文本、用户输入的元数据等打包提交给`content-moderation-service`进行审核。
*   **FR4.5.2 (审核与发布)**: 视频在通过审核前，状态为`PENDING_MODERATION`，对外不可见。审核通过后，状态自动变为`PUBLISHED`。
*   **FR4.5.3 (隐私设置)**: 用户必须能设置其视频的隐私级别：`PUBLIC`, `UNLISTED`, `FRIENDS_ONLY`, `PRIVATE`。API在返回视频信息时必须严格遵守此设置。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/videos`
*   **核心端点**:
    *   **Upload**: `POST /uploads/initiate`, `GET /uploads/{id}/part-url`, `POST /uploads/{id}/complete`
    *   **Playback**: `GET /videos/{id}`
    *   **Discovery**: `GET /`, `GET /feed`
    *   **Management**: `PUT /my/{id}`, `DELETE /my/{id}`

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`videos`**: `id`, `uploader_user_id`, `title`, `description`, `tags (TEXT[])`, `category_id`, `status` (INDEX), `privacy` (INDEX), `duration_seconds`, `resolution (JSONB)`, `cover_image_url`, `interaction_counts (JSONB)`.
*   **`video_transcoding_jobs`**: `id`, `video_id`, `status`, `pipeline_name`, `error_log`.
*   **`video_artifacts`**: `id`, `video_id`, `artifact_type` (`HLS_MANIFEST`, `DASH_MANIFEST`, `THUMBNAIL`, `SUBTITLE_VTT`), `storage_key`, `metadata (JSONB)`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **上传**: 支持大文件稳定上传，分块上传的`part-url`请求P99 < 50ms。
*   **播放体验**:
    *   **首帧加载时间(TTFF)**: P95 < 2秒。
    *   **卡顿率(Rebuffering Rate)**: < 1%。
*   **API延迟**: 元数据API P99 < 150ms。
*   **转码效率**: 1分钟的1080p视频，转码任务应在1-2分钟内完成。

#### 7.2. 可靠性与可用性需求
*   **API可用性**: > 99.95%。
*   **转码工作流可靠性**: 必须有重试和失败处理机制，确保上传的视频最终都能被处理或标记为失败。

#### 7.3. 可扩展性需求
*   API服务可水平扩展。
*   **视频转码Worker集群**必须是可根据任务队列长度**自动伸缩**的。

#### 7.4. 安全性需求
*   **内容安全**: 所有视频内容必须经过审核。
*   **访问控制**: 严格遵守`privacy`设置。私有视频的CDN URL必须是签名的、有时效性的。
*   **上传安全**: 预签名URL机制防止未授权上传。

#### 7.5. 成本控制
*   **存储**: 实施生命周期策略，将冷门视频转移到低成本存储。
*   **转码**: 按需启动转码实例，并选择性价比最高的转码配置。
*   **CDN**: 优化缓存策略，监控带宽成本。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **视频处理**: **FFmpeg** 是核心工具。转码Worker应将其封装在容器中。强烈建议使用成熟的云转码服务 (如AWS MediaConvert, 阿里云MTS) 来替代自建Worker，以获得更高的稳定性和弹性。
*   **异步工作流**: 必须使用**事件驱动架构**（MQ + 独立的Worker）来解耦上传、转码、审核等流程。
*   **上传协议**: 除了预签名URL，可以评估集成**Tus协议**，以获得更健壮的断点续传支持。

---
这份版本2.0的SRS文档为`short-video-service`构建了一个现代化、智能化、体验优秀的富媒体处理与分发平台。它覆盖了从上传到分发的整个复杂流程，并强调了性能、可扩展性、用户体验和成本控制的重要性，能够为CINA.CLUB平台提供强大的视频能力。