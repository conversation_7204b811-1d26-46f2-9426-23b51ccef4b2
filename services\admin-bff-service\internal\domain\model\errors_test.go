/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package model

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Test session error constants
func TestSessionErrors(t *testing.T) {
	assert.Equal(t, "session not found", ErrSessionNotFound.Error())
	assert.Equal(t, "session has expired", ErrSessionExpired.Error())
	assert.Equal(t, "session is invalid", ErrSessionInvalid.Error())
	assert.Equal(t, "session already exists", ErrSessionAlreadyExists.Error())
	assert.Equal(t, "failed to create session", ErrSessionCreateFailed.Error())
	assert.Equal(t, "failed to update session", ErrSessionUpdateFailed.Error())
	assert.Equal(t, "failed to delete session", ErrSessionDeleteFailed.Error())
}

// Test employee error constants
func TestEmployeeErrors(t *testing.T) {
	assert.Equal(t, "employee not found", ErrEmployeeNotFound.Error())
	assert.Equal(t, "employee is inactive", ErrEmployeeInactive.Error())
	assert.Equal(t, "employee is unauthorized", ErrEmployeeUnauthorized.Error())
	assert.Equal(t, "invalid employee ID", ErrInvalidEmployeeID.Error())
	assert.Equal(t, "invalid email address", ErrInvalidEmail.Error())
}

// Test authentication error constants
func TestAuthenticationErrors(t *testing.T) {
	assert.Equal(t, "authentication failed", ErrAuthenticationFailed.Error())
	assert.Equal(t, "invalid credentials", ErrInvalidCredentials.Error())
	assert.Equal(t, "account is locked", ErrAccountLocked.Error())
	assert.Equal(t, "account is suspended", ErrAccountSuspended.Error())
	assert.Equal(t, "insufficient roles for operation", ErrInsufficientRoles.Error())
}

// Test audit log error constants
func TestAuditLogErrors(t *testing.T) {
	assert.Equal(t, "invalid audit log entry", ErrAuditLogInvalidEntry.Error())
	assert.Equal(t, "failed to store audit log", ErrAuditLogStoreFailed.Error())
}

// Test validation error constants
func TestValidationErrors(t *testing.T) {
	assert.Equal(t, "invalid user ID", ErrInvalidUserID.Error())
	assert.Equal(t, "invalid order ID", ErrInvalidOrderID.Error())
	assert.Equal(t, "invalid content ID", ErrInvalidContentID.Error())
	assert.Equal(t, "invalid filter parameters", ErrInvalidFilter.Error())
	assert.Equal(t, "invalid time range", ErrInvalidTimeRange.Error())
	assert.Equal(t, "invalid pagination parameters", ErrInvalidPagination.Error())
	assert.Equal(t, "missing required parameter", ErrMissingParameter.Error())
}

// Test business logic error constants
func TestBusinessLogicErrors(t *testing.T) {
	assert.Equal(t, "user is already suspended", ErrUserAlreadySuspended.Error())
	assert.Equal(t, "user is not suspended", ErrUserNotSuspended.Error())
	assert.Equal(t, "content is already approved", ErrContentAlreadyApproved.Error())
	assert.Equal(t, "content is already rejected", ErrContentAlreadyRejected.Error())
	assert.Equal(t, "order cannot be cancelled", ErrOrderCannotBeCancelled.Error())
	assert.Equal(t, "order cannot be refunded", ErrOrderCannotBeRefunded.Error())
}

// Test external service error constants
func TestExternalServiceErrors(t *testing.T) {
	assert.Equal(t, "user service is unavailable", ErrUserServiceUnavailable.Error())
	assert.Equal(t, "billing service is unavailable", ErrBillingServiceUnavailable.Error())
	assert.Equal(t, "social service is unavailable", ErrSocialServiceUnavailable.Error())
	assert.Equal(t, "content service is unavailable", ErrContentServiceUnavailable.Error())
	assert.Equal(t, "analytics service is unavailable", ErrAnalyticsServiceUnavailable.Error())
	assert.Equal(t, "notification service is unavailable", ErrNotificationServiceUnavailable.Error())
}

// Test system error constants
func TestSystemErrors(t *testing.T) {
	assert.Equal(t, "internal server error", ErrInternalServerError.Error())
	assert.Equal(t, "service is unavailable", ErrServiceUnavailable.Error())
	assert.Equal(t, "rate limit exceeded", ErrRateLimitExceeded.Error())
	assert.Equal(t, "request timeout", ErrRequestTimeout.Error())
	assert.Equal(t, "database error", ErrDatabaseError.Error())
	assert.Equal(t, "cache error", ErrCacheError.Error())
}

// Test error code constants
func TestErrorCodeConstants(t *testing.T) {
	// Validation error codes
	assert.Equal(t, "INVALID_INPUT", CodeInvalidInput)
	assert.Equal(t, "MISSING_FIELD", CodeMissingField)
	assert.Equal(t, "INVALID_FORMAT", CodeInvalidFormat)
	assert.Equal(t, "VALUE_OUT_OF_RANGE", CodeValueOutOfRange)

	// Authentication error codes
	assert.Equal(t, "UNAUTHORIZED", CodeUnauthorized)
	assert.Equal(t, "FORBIDDEN", CodeForbidden)
	assert.Equal(t, "SESSION_EXPIRED", CodeSessionExpired)
	assert.Equal(t, "INVALID_SESSION", CodeInvalidSession)

	// Business logic error codes
	assert.Equal(t, "RESOURCE_NOT_FOUND", CodeResourceNotFound)
	assert.Equal(t, "RESOURCE_CONFLICT", CodeResourceConflict)
	assert.Equal(t, "OPERATION_NOT_ALLOWED", CodeOperationNotAllowed)
	assert.Equal(t, "BUSINESS_RULE_VIOLATION", CodeBusinessRuleViolation)

	// System error codes
	assert.Equal(t, "INTERNAL_ERROR", CodeInternalError)
	assert.Equal(t, "SERVICE_UNAVAILABLE", CodeServiceUnavailable)
	assert.Equal(t, "TIMEOUT", CodeTimeout)
	assert.Equal(t, "RATE_LIMITED", CodeRateLimited)
}

// Test ValidationError type
func TestValidationError(t *testing.T) {
	t.Run("constructor", func(t *testing.T) {
		err := NewValidationError("email", "Invalid email format", CodeInvalidFormat)

		assert.Equal(t, "email", err.Field)
		assert.Equal(t, "Invalid email format", err.Message)
		assert.Equal(t, CodeInvalidFormat, err.Code)
	})

	t.Run("error method", func(t *testing.T) {
		err := ValidationError{
			Field:   "username",
			Message: "Username is required",
			Code:    CodeMissingField,
		}

		assert.Equal(t, "Username is required", err.Error())
	})

	t.Run("empty values", func(t *testing.T) {
		err := NewValidationError("", "", "")
		assert.Equal(t, "", err.Field)
		assert.Equal(t, "", err.Message)
		assert.Equal(t, "", err.Code)
		assert.Equal(t, "", err.Error())
	})
}

// Test BusinessError type
func TestBusinessError(t *testing.T) {
	t.Run("constructor", func(t *testing.T) {
		err := NewBusinessError(CodeResourceConflict, "User already exists", "Email is already registered")

		assert.Equal(t, CodeResourceConflict, err.Code)
		assert.Equal(t, "User already exists", err.Message)
		assert.Equal(t, "Email is already registered", err.Details)
	})

	t.Run("error method", func(t *testing.T) {
		err := BusinessError{
			Code:    CodeOperationNotAllowed,
			Message: "Operation not permitted",
			Details: "User lacks required permissions",
		}

		assert.Equal(t, "Operation not permitted", err.Error())
	})

	t.Run("without details", func(t *testing.T) {
		err := NewBusinessError(CodeResourceNotFound, "Resource not found", "")
		assert.Equal(t, CodeResourceNotFound, err.Code)
		assert.Equal(t, "Resource not found", err.Message)
		assert.Equal(t, "", err.Details)
	})
}

// Test ServiceError type
func TestServiceError(t *testing.T) {
	t.Run("constructor", func(t *testing.T) {
		err := NewServiceError("user-service", CodeServiceUnavailable, "User service is down", true)

		assert.Equal(t, "user-service", err.Service)
		assert.Equal(t, CodeServiceUnavailable, err.Code)
		assert.Equal(t, "User service is down", err.Message)
		assert.True(t, err.Retry)
	})

	t.Run("error method", func(t *testing.T) {
		err := ServiceError{
			Service: "billing-service",
			Code:    CodeTimeout,
			Message: "Request timed out",
			Retry:   false,
		}

		assert.Equal(t, "Request timed out", err.Error())
	})

	t.Run("non-retryable error", func(t *testing.T) {
		err := NewServiceError("analytics-service", CodeInternalError, "Internal error", false)
		assert.Equal(t, "analytics-service", err.Service)
		assert.Equal(t, CodeInternalError, err.Code)
		assert.Equal(t, "Internal error", err.Message)
		assert.False(t, err.Retry)
	})
}

// Test IsRetryableError function
func TestIsRetryableError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "retryable service error pointer",
			err:      NewServiceError("test-service", CodeServiceUnavailable, "Service down", true),
			expected: true,
		},
		{
			name: "retryable service error value",
			err: ServiceError{
				Service: "test-service",
				Code:    CodeTimeout,
				Message: "Timeout",
				Retry:   true,
			},
			expected: true,
		},
		{
			name:     "non-retryable service error pointer",
			err:      NewServiceError("test-service", CodeInternalError, "Internal error", false),
			expected: false,
		},
		{
			name: "non-retryable service error value",
			err: ServiceError{
				Service: "test-service",
				Code:    CodeInternalError,
				Message: "Internal error",
				Retry:   false,
			},
			expected: false,
		},
		{
			name:     "retryable system error - service unavailable",
			err:      ErrServiceUnavailable,
			expected: true,
		},
		{
			name:     "retryable system error - timeout",
			err:      ErrRequestTimeout,
			expected: true,
		},
		{
			name:     "retryable system error - database",
			err:      ErrDatabaseError,
			expected: true,
		},
		{
			name:     "retryable system error - cache",
			err:      ErrCacheError,
			expected: true,
		},
		{
			name:     "non-retryable error - validation",
			err:      NewValidationError("field", "message", "code"),
			expected: false,
		},
		{
			name:     "non-retryable error - business",
			err:      NewBusinessError("code", "message", "details"),
			expected: false,
		},
		{
			name:     "non-retryable error - generic",
			err:      errors.New("generic error"),
			expected: false,
		},
		{
			name:     "non-retryable error - session",
			err:      ErrSessionExpired,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsRetryableError(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test GetErrorCode function
func TestGetErrorCode(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
	}{
		{
			name:     "validation error pointer",
			err:      NewValidationError("field", "message", CodeInvalidInput),
			expected: CodeInvalidInput,
		},
		{
			name: "validation error value",
			err: ValidationError{
				Field:   "field",
				Message: "message",
				Code:    CodeMissingField,
			},
			expected: CodeMissingField,
		},
		{
			name:     "business error pointer",
			err:      NewBusinessError(CodeResourceConflict, "message", "details"),
			expected: CodeResourceConflict,
		},
		{
			name: "business error value",
			err: BusinessError{
				Code:    CodeOperationNotAllowed,
				Message: "message",
				Details: "details",
			},
			expected: CodeOperationNotAllowed,
		},
		{
			name:     "service error pointer",
			err:      NewServiceError("service", CodeServiceUnavailable, "message", true),
			expected: CodeServiceUnavailable,
		},
		{
			name: "service error value",
			err: ServiceError{
				Service: "service",
				Code:    CodeTimeout,
				Message: "message",
				Retry:   false,
			},
			expected: CodeTimeout,
		},
		{
			name:     "generic error",
			err:      errors.New("generic error"),
			expected: CodeInternalError,
		},
		{
			name:     "system error",
			err:      ErrServiceUnavailable,
			expected: CodeInternalError,
		},
		{
			name:     "nil error",
			err:      nil,
			expected: CodeInternalError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetErrorCode(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test error interfaces and type assertions
func TestErrorInterfaces(t *testing.T) {
	t.Run("ValidationError implements error interface", func(t *testing.T) {
		var err error = NewValidationError("field", "message", "code")
		assert.NotNil(t, err)
		assert.Equal(t, "message", err.Error())
	})

	t.Run("BusinessError implements error interface", func(t *testing.T) {
		var err error = NewBusinessError("code", "message", "details")
		assert.NotNil(t, err)
		assert.Equal(t, "message", err.Error())
	})

	t.Run("ServiceError implements error interface", func(t *testing.T) {
		var err error = NewServiceError("service", "code", "message", true)
		assert.NotNil(t, err)
		assert.Equal(t, "message", err.Error())
	})
}

// Test error equality
func TestErrorEquality(t *testing.T) {
	t.Run("same validation errors are equal", func(t *testing.T) {
		err1 := NewValidationError("field", "message", "code")
		err2 := NewValidationError("field", "message", "code")

		assert.Equal(t, err1.Field, err2.Field)
		assert.Equal(t, err1.Message, err2.Message)
		assert.Equal(t, err1.Code, err2.Code)
	})

	t.Run("different validation errors are not equal", func(t *testing.T) {
		err1 := NewValidationError("field1", "message", "code")
		err2 := NewValidationError("field2", "message", "code")

		assert.NotEqual(t, err1.Field, err2.Field)
	})

	t.Run("error constants are equal", func(t *testing.T) {
		assert.Equal(t, ErrSessionNotFound, ErrSessionNotFound)
		assert.NotEqual(t, ErrSessionNotFound, ErrSessionExpired)
	})
}

// Test edge cases
func TestErrorEdgeCases(t *testing.T) {
	t.Run("empty error messages", func(t *testing.T) {
		validationErr := NewValidationError("", "", "")
		businessErr := NewBusinessError("", "", "")
		serviceErr := NewServiceError("", "", "", false)

		assert.Equal(t, "", validationErr.Error())
		assert.Equal(t, "", businessErr.Error())
		assert.Equal(t, "", serviceErr.Error())
	})

	t.Run("nil pointer handling in helper functions", func(t *testing.T) {
		// Test with nil pointer (should not panic)
		assert.False(t, IsRetryableError(nil))
		assert.Equal(t, CodeInternalError, GetErrorCode(nil))
	})

	t.Run("large error messages", func(t *testing.T) {
		largeMessage := string(make([]byte, 10000))
		err := NewValidationError("field", largeMessage, "code")
		assert.Equal(t, largeMessage, err.Error())
	})
}

// Benchmark tests
func BenchmarkNewValidationError(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		NewValidationError("field", "message", "code")
	}
}

func BenchmarkNewBusinessError(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		NewBusinessError("code", "message", "details")
	}
}

func BenchmarkNewServiceError(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		NewServiceError("service", "code", "message", true)
	}
}

func BenchmarkIsRetryableError(b *testing.B) {
	err := NewServiceError("service", "code", "message", true)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		IsRetryableError(err)
	}
}

func BenchmarkGetErrorCode(b *testing.B) {
	err := NewValidationError("field", "message", "code")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetErrorCode(err)
	}
}
