好的，遵照您的指示。我将为您生成一份专门针对 **`pkg/logger`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`pkg/logger` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到代码实现的层面，详细阐述**如何基于`log/slog`构建一个上下文感知的、高性能的结构化日志系统，如何与`pkg/tracing`和`pkg/errors`无缝集成，以及如何为所有后端服务提供统一、规范的日志记录体验**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `pkg/logger` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `pkg/logger-srs.md` (v1.0)
**核心架构**: `log/slog` 封装 + 上下文注入与传播

## 1. 概述

`pkg/logger` 是CINA.CLUB后端微服务生态中，用于**规范化日志记录**的基础核心库。它提供了一个统一的接口，确保所有服务产生的日志都具有**一致的结构、丰富的上下文和高性能**。其架构设计的核心目标是：
1.  **结构化 (Structured)**: 所有日志**必须**以JSON格式输出，以便被日志聚合系统（如Loki, Elasticsearch）高效地解析和索引。
2.  **上下文感知 (Context-Aware)**: 日志**必须**自动包含来自请求上下文的关键追踪信息（`trace_id`, `span_id`, `user_id`），实现日志与分布式追踪的联动。
3.  **高性能**: 日志记录操作本身不应成为服务的性能瓶颈，特别是在高并发场景下。
4.  **易用性**: 为开发者提供简洁的、类型安全的API，鼓励记录带有丰富键值对的日志，而不是拼接字符串。
5.  **与错误处理集成**: 能够智能地处理`pkg/errors.AppError`，自动记录其结构化信息和堆栈跟踪。

本架构设计通过**封装Go 1.21+标准库`log/slog`**，并构建一套**基于`context.Context`的日志记录器传播机制**，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (上下文日志记录流程)

```mermaid
graph TD
    subgraph "请求入口 (gRPC Middleware)"
        A[Logging Interceptor]
    end

    subgraph "pkg/logger"
        style "pkg/logger" fill:#e0f7fa
        B[Global Logger Instance]
        C[Contextual Logger Creation]
        D[Context Utils<br/>(FromContext, NewContextWithLogger)]
        E[Shortcut Functions<br/>(Info, Error, ...)]
    end
    
    subgraph "业务逻辑代码 (Service/Repo Layer)"
        F[some_service.go]
    end
    
    subgraph "Go Standard Library & Other pkgs"
        G[context.Context]
        H[log/slog]
        I[pkg/tracing]
        J[pkg/errors]
    end

    subgraph "输出"
        K[stdout (JSON format)]
    end

    %% Initialization Flow
    subgraph "服务启动时"
        Init("main.go") -- "1. Creates" --> B
        B -- "is a" --> H
    end
    
    %% Request Flow
    A -- "2. On new request" --> A
    A -- "a. Extracts traceID from" --> I
    A -- "b. Creates contextual logger" --> C
    C -- "c. Clones global logger and adds attrs" --> B
    C -- "d. Injects new logger into" --> G
    A -- "3. Calls next handler with new context" --> F
    
    F -- "4. Calls logger.Info(ctx, ...)" --> E
    E -- "a. Gets logger from context" --> D
    D -- "b. Retrieves logger from" --> G
    E -- "c. Calls slog.Info(...)" --> H
    
    H -- "5. Writes structured log to" --> K
```

### 2.2 最终目录结构 (`pkg/logger/`)

```
pkg/logger/
├── logger.go           # ✨ 主入口, 定义New, FromContext和快捷函数 ✨
├── handler.go          # ✨ 自定义的slog.Handler, 用于错误处理和格式化 ✨
├── config.go           # 定义LoggerConfig struct
└── logger_test.go      # 单元测试
```

---

## 3. 各层职责深度解析

### 3.1 `config.go` - 日志配置

*   **`LoggerConfig` struct**: 定义所有可配置的参数。
    ```go
    type Config struct {
        Level     slog.Level `mapstructure:"level" default:"info"`
        Format    string     `mapstructure:"format" default:"json"` // "json" or "text"
        AddSource bool       `mapstructure:"add_source" default:"false"`
    }
    ```
    **设计决策**: `Level`直接使用`slog.Level`类型，Viper可以通过实现`TextUnmarshaler`接口来支持从字符串（如"debug"）解析。

### 3.2 `handler.go` - 自定义`slog.Handler`

这是实现与`pkg/errors`和`pkg/tracing`深度集成的关键。我们将创建一个包装了标准`slog.JSONHandler`的自定义Handler。

*   **`CinaLogHandler` struct**:
    ```go
    type CinaLogHandler struct {
        slog.Handler // 嵌入一个底层的handler
    }
    ```
*   **`Handle(ctx context.Context, r slog.Record)` method**:
    *   **这是核心的拦截和增强方法**。
    *   **步骤1: 提取上下文属性**:
        *   从`ctx`中提取`trace_id`, `span_id` (通过`pkg/tracing`的工具函数)。
        *   从`ctx`中提取`user_id` (通过`pkg/auth`的工具函数)。
        *   将这些属性添加到`slog.Record`中。
    *   **步骤2: 智能错误处理**:
        *   遍历`r.Attrs`，查找`key`为`"error"`的属性。
        *   如果找到，并且其`value`是一个`error`类型：
            a. **使用`errors.As`**检查它是否为一个`*app_errors.AppError`。
            b. 如果是，则将其`Code`, `Metadata`等结构化信息，作为新的`slog.Attr`添加到记录中。
            c. **使用`errors.As`**检查它是否实现了包含`StackTrace()`方法的接口（来自`github.com/pkg/errors`）。
            d. 如果是，则将其堆栈跟踪格式化后，作为一个新的`stack_trace`属性添加。
    *   **步骤3: 调用底层Handler**: `return h.Handler.Handle(ctx, r)`，让底层的`JSONHandler`完成最终的格式化和输出。

### 3.3 `logger.go` - 主API实现

*   **全局变量**:
    *   `var globalLogger *slog.Logger`: 在包级别维护一个默认的、在服务启动时初始化的Logger实例。
*   **`New(cfg Config, serviceName, serviceVersion string)` function**:
    1.  **创建HandlerOptions**: `opts := &slog.HandlerOptions{ AddSource: cfg.AddSource, Level: cfg.Level }`。
    2.  **创建自定义Handler**: `handler := NewCinaLogHandler(slog.NewJSONHandler(os.Stdout, opts))`。
    3.  **创建Logger**: `logger := slog.New(handler)`。
    4.  **添加服务固定属性**: `globalLogger = logger.With("service_name", serviceName, "service_version", serviceVersion)`。
    5.  返回`globalLogger`。
*   **`FromContext(ctx context.Context)` function**:
    *   从`ctx`中尝试获取Logger。
    *   如果获取不到（即`ctx`中没有注入Logger），**必须返回`globalLogger`**，而不是`nil`。这是一种**防御性设计**，确保任何地方调用日志记录都不会因为`ctx`不正确而panic。
*   **快捷函数 (`Info`, `Error`, ...)**:
    *   `func Info(ctx context.Context, msg string, args ...interface{})`
    *   **实现**: `FromContext(ctx).Info(msg, args...)`。
    *   **`Error(ctx, err, msg, args ...interface{})`**:
        *   这是一个特殊的快捷函数。
        *   它会调用`FromContext(ctx).Error(msg, append(args, "error", err)...)`。
        *   将`error`作为一个**结构化的键值对**传入，而不是混在`msg`中，这样`CinaLogHandler`才能拦截并处理它。

---

## 4. 使用示例与最佳实践

### 4.1 初始化 (在`main.go`)
```go
// 1. 加载配置
var cfg AppConfig
// ...

// 2. 初始化Logger
// 这是在整个服务生命周期中唯一一次调用New
appLogger := logger.New(cfg.Logger, "my-service", "v1.0")

// 3. 将全局Logger设置为slog的默认Logger（可选，但推荐）
slog.SetDefault(appLogger)

// ...后续所有地方都可以直接使用slog.Info等标准库函数，它们会自动使用我们配置的Handler
```

### 4.2 中间件注入 (在`pkg/middleware/logging.go`)
```go
func LoggingInterceptor(baseLogger *slog.Logger) grpc.UnaryServerInterceptor {
    return func(ctx context.Context, req interface{}, ...) (interface{}, error) {
        // ...
        traceID := ... // 从ctx中获取
        
        // ✨ 创建带请求上下文的Logger ✨
        ctxLogger := baseLogger.With("trace_id", traceID)
        
        // ✨ 将新Logger注入到新的上下文中 ✨
        newCtx := logger.NewContextWithLogger(ctx, ctxLogger)
        
        // ...调用handler(newCtx, req)...
        
        // 使用快捷函数记录请求结束日志
        logger.Info(newCtx, "request completed", "duration_ms", ...)
        // ...
    }
}
```

### 4.3 业务代码使用
```go
// 在任何函数中，只要它接收ctx作为第一个参数
func (s *someService) DoWork(ctx context.Context) error {
    // 无需关心logger从何而来，直接使用快捷函数
    logger.Info(ctx, "work started")
    
    err := s.repo.Find()
    if err != nil {
        // Error函数会自动处理err的结构化信息
        logger.Error(ctx, err, "failed to find data")
        return app_errors.Wrap(err, ...)
    }
    return nil
}
```

---

## 5. 总结

本架构设计通过以下关键点来构建一个生产级的`pkg/logger`：
1.  **拥抱标准库**: 基于Go 1.21+强大的`log/slog`标准库进行封装，而不是引入新的第三方日志API。这保证了长期的兼容性和社区支持。
2.  **自定义Handler实现增强**: 通过创建一个包装了`slog.JSONHandler`的自定义`Handler`，以一种**非侵入式**的方式，实现了对错误和追踪上下文的**自动化、结构化处理**。
3.  **上下文传播是核心**: 强制推行一种通过`context.Context`来传递Logger实例的模式。这使得在任何业务代码深处，都能轻易地访问到带有完整请求链路信息的Logger，而无需手动传递。
4.  **简洁的API设计**: 通过提供`Info(ctx, ...)`等一系列快捷函数，极大地简化了开发者的使用，降低了心智负担，并引导他们遵循最佳实践（如不拼接字符串，将错误作为字段传递）。

这种架构确保了`pkg/logger`能够为CINA.CLUB平台的所有后端服务，提供一个**高性能、信息丰富、且与可观测性生态（Tracing, Errors）深度集成**的统一日志记录解决方案。