/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package handlers

import (
	"context"
	"encoding/json"
	"fmt"

	"cina.club/services/activity-feed-service/internal/domain"
)

// NotificationHandler handles system notification events
type NotificationHandler struct {
	feedRepo        domain.FeedRepository
	unreadCountRepo domain.UnreadCountRepository
	aggregator      *domain.Aggregator
	logger          domain.Logger
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(
	feedRepo domain.FeedRepository,
	unreadCountRepo domain.UnreadCountRepository,
	aggregator *domain.Aggregator,
	logger domain.Logger,
) *NotificationHandler {
	return &NotificationHandler{
		feedRepo:        feedRepo,
		unreadCountRepo: unreadCountRepo,
		aggregator:      aggregator,
		logger:          logger,
	}
}

// Handle processes notification events (system announcements, alerts, status changes)
func (h *NotificationHandler) Handle(ctx context.Context, event domain.Event) error {
	eventType := event.GetEventType()

	h.logger.Debug(ctx, "Processing notification event",
		"event_type", eventType,
		"event_id", event.GetEventID(),
		"user_id", event.GetUserID())

	// Parse event payload
	var eventData map[string]interface{}
	if err := json.Unmarshal(event.GetPayload(), &eventData); err != nil {
		h.logger.Error(ctx, "Failed to parse event payload",
			"event_type", eventType,
			"error", err)
		return fmt.Errorf("failed to parse event payload: %w", err)
	}

	// Create activity feed item based on event type
	item, err := h.createNotificationFromEvent(eventType, eventData)
	if err != nil {
		h.logger.Error(ctx, "Failed to create notification from event",
			"event_type", eventType,
			"error", err)
		return fmt.Errorf("failed to create notification: %w", err)
	}

	if item == nil {
		h.logger.Debug(ctx, "No notification created for event", "event_type", eventType)
		return nil
	}

	// Set event metadata
	item.EventID = event.GetEventID()
	item.EventType = eventType

	// Most notifications are not aggregated, but we still process through aggregator
	result, err := h.aggregator.AggregateOrNew(ctx, item)
	if err != nil {
		h.logger.Error(ctx, "Aggregation processing failed",
			"event_type", eventType,
			"item_id", item.ID,
			"error", err)
		return fmt.Errorf("aggregation processing failed: %w", err)
	}

	// Handle result (usually create action for notifications)
	switch result.Action {
	case domain.AggregationActionCreate:
		if err := h.feedRepo.CreateFeedItem(ctx, result.Item); err != nil {
			h.logger.Error(ctx, "Failed to create notification item",
				"item_id", result.Item.ID,
				"error", err)
			return fmt.Errorf("failed to create notification item: %w", err)
		}

		// Increment unread count
		if err := h.unreadCountRepo.IncrementUnreadCount(ctx, result.Item.UserID, result.Item.FeedType); err != nil {
			h.logger.Warn(ctx, "Failed to increment unread count",
				"user_id", result.Item.UserID,
				"feed_type", result.Item.FeedType,
				"error", err)
		}

		h.logger.Info(ctx, "Created notification feed item",
			"item_id", result.Item.ID,
			"activity_type", result.Item.ActivityType,
			"user_id", result.Item.UserID)

	case domain.AggregationActionUpdate:
		// Handle update case (rare for notifications)
		h.logger.Debug(ctx, "Notification aggregation update not implemented",
			"event_type", eventType)
	}

	return nil
}

// createNotificationFromEvent creates a notification feed item based on event type and data
func (h *NotificationHandler) createNotificationFromEvent(eventType string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	userID, _ := eventData["user_id"].(string)
	if userID == "" {
		return nil, fmt.Errorf("missing user ID in notification event")
	}

	switch eventType {
	case "system.announcement":
		return h.createSystemAnnouncementItem(userID, eventData)
	case "account.security.alert":
		return h.createSecurityAlertItem(userID, eventData)
	case "task.status.changed":
		return h.createTaskStatusChangedItem(userID, eventData)
	case "service.booked":
		return h.createServiceBookedItem(userID, eventData)
	case "payment.received":
		return h.createPaymentReceivedItem(userID, eventData)
	case "payment.failed":
		return h.createPaymentFailedItem(userID, eventData)
	case "subscription.expired":
		return h.createSubscriptionExpiredItem(userID, eventData)
	case "verification.completed":
		return h.createVerificationCompletedItem(userID, eventData)
	default:
		h.logger.Warn(context.Background(), "Unknown notification event type", "event_type", eventType)
		return nil, nil
	}
}

// createSystemAnnouncementItem creates a system announcement notification
func (h *NotificationHandler) createSystemAnnouncementItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	title, _ := eventData["title"].(string)
	message, _ := eventData["message"].(string)
	iconURL, _ := eventData["icon_url"].(string)
	deepLinkURL, _ := eventData["deep_link_url"].(string)

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypeSystemAnnouncement)
	item.SetDisplay(domain.DisplayData{
		Title:   title,
		Message: message,
		IconURL: iconURL,
	})

	if deepLinkURL != "" {
		item.DeepLinkURL = deepLinkURL
	}

	return item, nil
}

// createSecurityAlertItem creates a security alert notification
func (h *NotificationHandler) createSecurityAlertItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	alertType, _ := eventData["alert_type"].(string)
	message, _ := eventData["message"].(string)
	ipAddress, _ := eventData["ip_address"].(string)
	location, _ := eventData["location"].(string)

	var title string
	switch alertType {
	case "suspicious_login":
		title = "Suspicious login detected"
	case "password_changed":
		title = "Password changed"
	case "new_device_login":
		title = "New device login"
	default:
		title = "Security alert"
	}

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypeAccountSecurity)
	item.SetDisplay(domain.DisplayData{
		Title:   title,
		Message: message,
		IconURL: "/icons/security.png",
		Metadata: map[string]string{
			"alert_type": alertType,
			"ip_address": ipAddress,
			"location":   location,
		},
	})
	item.DeepLinkURL = "cinaclub://security/alerts"

	return item, nil
}

// createTaskStatusChangedItem creates a task status change notification
func (h *NotificationHandler) createTaskStatusChangedItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	taskID, _ := eventData["task_id"].(string)
	taskTitle, _ := eventData["task_title"].(string)
	oldStatus, _ := eventData["old_status"].(string)
	newStatus, _ := eventData["new_status"].(string)
	changedBy, _ := eventData["changed_by"].(string)

	var title string
	switch newStatus {
	case "accepted":
		title = fmt.Sprintf("Your task \"%s\" was accepted", taskTitle)
	case "completed":
		title = fmt.Sprintf("Your task \"%s\" was completed", taskTitle)
	case "cancelled":
		title = fmt.Sprintf("Your task \"%s\" was cancelled", taskTitle)
	default:
		title = fmt.Sprintf("Task \"%s\" status updated", taskTitle)
	}

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypeTaskStatusChanged)
	item.SetTarget(domain.Target{
		ID:   taskID,
		Type: "task",
		Name: taskTitle,
		URL:  fmt.Sprintf("/tasks/%s", taskID),
	})
	item.SetDisplay(domain.DisplayData{
		Title:   title,
		Message: fmt.Sprintf("Status changed from %s to %s by %s", oldStatus, newStatus, changedBy),
		IconURL: "/icons/task.png",
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://task/%s", taskID)

	return item, nil
}

// createServiceBookedItem creates a service booking notification
func (h *NotificationHandler) createServiceBookedItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	serviceID, _ := eventData["service_id"].(string)
	serviceName, _ := eventData["service_name"].(string)
	bookedBy, _ := eventData["booked_by"].(string)
	bookingTime, _ := eventData["booking_time"].(string)

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypeServiceBooked)
	item.SetTarget(domain.Target{
		ID:   serviceID,
		Type: "service",
		Name: serviceName,
		URL:  fmt.Sprintf("/services/%s", serviceID),
	})
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("Your service \"%s\" was booked", serviceName),
		Message: fmt.Sprintf("Booked by %s for %s", bookedBy, bookingTime),
		IconURL: "/icons/booking.png",
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://service/%s/bookings", serviceID)

	return item, nil
}

// createPaymentReceivedItem creates a payment received notification
func (h *NotificationHandler) createPaymentReceivedItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	amount, _ := eventData["amount"].(float64)
	currency, _ := eventData["currency"].(string)
	fromUser, _ := eventData["from_user"].(string)
	transactionID, _ := eventData["transaction_id"].(string)
	serviceID, _ := eventData["service_id"].(string)
	serviceName, _ := eventData["service_name"].(string)

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypePaymentReceived)
	if serviceID != "" {
		item.SetTarget(domain.Target{
			ID:   serviceID,
			Type: "service",
			Name: serviceName,
			URL:  fmt.Sprintf("/services/%s", serviceID),
		})
	}
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("Payment received: %.2f %s", amount, currency),
		Message: fmt.Sprintf("Payment from %s for %s", fromUser, serviceName),
		IconURL: "/icons/payment.png",
		Metadata: map[string]string{
			"transaction_id": transactionID,
			"amount":         fmt.Sprintf("%.2f", amount),
			"currency":       currency,
		},
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://payments/transaction/%s", transactionID)

	return item, nil
}

// createPaymentFailedItem creates a payment failed notification
func (h *NotificationHandler) createPaymentFailedItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	amount, _ := eventData["amount"].(float64)
	currency, _ := eventData["currency"].(string)
	reason, _ := eventData["reason"].(string)
	transactionID, _ := eventData["transaction_id"].(string)

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypePaymentReceived)
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("Payment failed: %.2f %s", amount, currency),
		Message: fmt.Sprintf("Payment failed: %s", reason),
		IconURL: "/icons/payment-failed.png",
		Metadata: map[string]string{
			"transaction_id": transactionID,
			"reason":         reason,
		},
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://payments/retry/%s", transactionID)

	return item, nil
}

// createSubscriptionExpiredItem creates a subscription expired notification
func (h *NotificationHandler) createSubscriptionExpiredItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	subscriptionType, _ := eventData["subscription_type"].(string)
	expiredAt, _ := eventData["expired_at"].(string)

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypeSystemAnnouncement)
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("Your %s subscription has expired", subscriptionType),
		Message: fmt.Sprintf("Expired on %s. Renew now to continue enjoying premium features.", expiredAt),
		IconURL: "/icons/subscription.png",
	})
	item.DeepLinkURL = "cinaclub://subscription/renew"

	return item, nil
}

// createVerificationCompletedItem creates a verification completed notification
func (h *NotificationHandler) createVerificationCompletedItem(userID string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	verificationType, _ := eventData["verification_type"].(string)
	status, _ := eventData["status"].(string)

	var title, message, iconURL string
	if status == "approved" {
		title = fmt.Sprintf("Your %s verification was approved", verificationType)
		message = "You can now access additional features and services"
		iconURL = "/icons/verified.png"
	} else {
		title = fmt.Sprintf("Your %s verification was rejected", verificationType)
		message = "Please review the requirements and try again"
		iconURL = "/icons/verification-failed.png"
	}

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypeSystemAnnouncement)
	item.SetDisplay(domain.DisplayData{
		Title:   title,
		Message: message,
		IconURL: iconURL,
		Metadata: map[string]string{
			"verification_type": verificationType,
			"status":            status,
		},
	})
	item.DeepLinkURL = "cinaclub://profile/verification"

	return item, nil
}
