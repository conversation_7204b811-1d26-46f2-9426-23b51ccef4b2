好的，遵照您的指示。我将根据我们刚才讨论的、整合了Zapier理念的宏大构想，以及版本3.0的SRS，为您生成一份专门针对 **`routines-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将详细阐述其作为**平台自动化中心**的技术实现细节，包括如何消费多源触发器、如何与`pkg/workflow`和`connector-service`协同工作，以及如何确保其执行的安全性和可靠性，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `routines-service` (工作流自动化服务) 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `routines-service-srs.md` (v3.0)
**核心架构**: 多源触发器引擎 + `pkg/workflow` 编排 + `connector-service` 动作执行

## 1. 概述

`routines-service` 作为平台的自动化大脑，其架构设计的核心是构建一个**高吞吐、高可靠、可扩展的触发-编排-执行管道**。它必须能有效地处理来自多个来源的触发信号，并将其转化为对一个具体工作流的、权限安全的执行。

**核心技术挑战**:
1.  **多源触发器管理**: 如何统一处理来自Kafka事件、Cron定时、外部Webhook和API轮询这四种截然不同的触发源。
2.  **工作流实例化与执行**: 如何将用户定义的JSON图，高效、安全地交给`pkg/workflow`引擎执行。
3.  **安全上下文传递**: 如何为每一次工作流执行，创建并传递一个代表用户的、临时的、安全的执行凭证。
4.  **状态与日志**: 如何在无状态的服务实例中，为用户提供有状态的、可追溯的执行历史。

本架构通过将服务拆分为**触发器层**、**编排层**和**执行协调层**来应对这些挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (内部)

```mermaid
graph TD
    subgraph "Trigger Sources"
        A[Kafka Events]
        B[Cron Scheduler]
        C[Polling Scheduler]
        D[Webhook Endpoint]
    end
    
    subgraph "Routines Service"
        subgraph "Trigger Layer (adapter/trigger)"
            T1[Kafka Consumer]
            T2[Cron Job Runner]
            T3[Polling Job Runner]
            T4[Webhook HTTP Handler]
        end

        subgraph "Orchestration Layer (application/orchestrator)"
            O[Orchestrator Service]
        end

        subgraph "Execution Coordination Layer"
            P[pkg/workflow Executor]
            Q[Action Executor (via Connector Service Client)]
        end
        
        subgraph "Data Layer"
            DB[PostgreSQL (Definitions & Logs)]
        end
    end

    A & B & C & D --> T1 & T2 & T3 & T4
    T1 & T2 & T3 & T4 -- "1. Fire Trigger(payload)" --> O
    O -- "2. Find matching Routines" --> DB
    O -- "3. For each, call RunWorkflow" --> O
    
    subgraph "Single Workflow Execution"
        O -- "4. Get User Credentials" --> UserCoreServiceClient
        O -- "5. Build Workflow Graph" --> P
        P -- "6. Execute Node" --> Q
        Q -- "7. Call Connector Service" --> ConnectorServiceClient
    end
    
    O -- "8. Log Execution" --> DB
```

### 2.2 最终目录结构 (`services/routines-service/`)

```
routines-service/
├── cmd/
│   ├── server/
│   │   └── main.go             # API服务器和事件消费者的入口
│   └── poller/
│       └── main.go             # ✨ 轮询触发器的独立Worker入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   └── handler.go      # gRPC Handler，调用application/service
│   │   ├── trigger/            # ✨ 触发器实现层 ✨
│   │   │   ├── event_consumer.go # Kafka消费者
│   │   │   ├── cron_scheduler.go # Cron调度器
│   │   │   ├── polling_scheduler.go # Polling调度器
│   │   │   └── webhook_handler.go # Webhook HTTP Handler
│   │   ├── repository/
│   │   │   ├── model.go
│   │   │   └── routine_repo.go
│   │   └── client/
│   │       ├── connector_service_client.go
│   │       └── user_core_service_client.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       ├── orchestrator.go     # ✨ 核心编排器 ✨
│   │       ├── query_service.go
│   │       └── routine_service.go  # 处理CRUD
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── factory/
│           └── execution_log_factory.go
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `adapter/trigger/` - 触发器层 (The Ears)

这一层负责监听所有外部信号，并将其**标准化**为一个内部的`Trigger`事件，然后传递给应用层的`Orchestrator`。

*   **`event_consumer.go`**:
    *   使用`pkg/messaging`启动一个Kafka消费者组。
    *   当收到消息时，将其`eventType`和`payload`封装成一个`EventTrigger`对象，调用`orchestrator.HandleTrigger(...)`。
*   **`cron_scheduler.go`**:
    *   使用`gocron`或类似库。
    *   服务启动时，从数据库加载所有时间驱动的、启用的Routines。
    *   为每个Routine创建一个定时任务。
    *   当任务触发时，构造一个`TimeTrigger`对象（payload为空），调用`orchestrator.HandleTrigger(...)`。
*   **`polling_scheduler.go`**:
    *   **独立部署**: 这是一个独立的Worker进程（`cmd/poller/main.go`），可以独立扩展。
    *   它也使用`gocron`，但执行的是轮询任务。
    *   当任务触发时，它调用`connector-service`中对应的**轮询触发器执行器**（如`google_calendar.check_new_event`）。
    *   `connector-service`返回新检测到的项目后，`polling_scheduler`为每个新项目构造一个`PollingTrigger`对象，并**通过Kafka**将这些触发事件发送出去，交由`event_consumer`统一处理。
*   **`webhook_handler.go`**:
    *   一个简单的HTTP Handler。
    *   解析URL中的`uniqueTriggerId`，从数据库中找到对应的Routine。
    *   将HTTP请求体作为`payload`，构造一个`WebhookTrigger`对象，调用`orchestrator.HandleTrigger(...)`。

### 3.2 `application/service/orchestrator.go` - 编排层 (The Brain)

这是整个服务的核心，它驱动了从触发到执行完成的整个生命周期。

*   **`Orchestrator` struct**: 依赖注入了`RoutineRepository`, `UserCoreClient`, `ConnectorClient`等。
*   **`HandleTrigger(ctx, trigger)` method**:
    1.  根据`trigger.Type`和`trigger.Source`（如事件类型、Webhook ID），调用`repo.FindRoutinesByTrigger()`从数据库中查找所有匹配的、已启用的`RoutineDefinition`。
    2.  对于每个匹配到的Routine，启动一个新的goroutine来异步执行`runWorkflow(routine, trigger.Payload)`。
*   **`runWorkflow(routine, initialPayload)` method**:
    1.  **安全上下文准备**:
        *   调用`userCoreClient.GetScopedToken(routine.UserID, "workflow_execution")`，获取一个代表该用户的、临时的、权限受限的S2S令牌。这个令牌将用于后续所有动作的执行。
        *   创建一个新的`context.Context`，其中包含这个令牌和新的`trace_id`。
    2.  **工作流初始化**:
        *   创建一个`pkg/workflow.Executor`实例。
        *   创建一个`ExecutionState`，并将`initialPayload`作为`trigger`节点的输出存入。
    3.  **动作节点注册**: **这是与`connector-service`解耦的关键**。
        *   `pkg/workflow`的执行器需要知道每个动作节点（如`chat.send_message`）如何执行。我们不直接在这里实现它。
        *   我们创建一个通用的`ActionNodeExecutor`结构体，它实现了`pkg/workflow.NodeExecutor`接口。
        *   这个通用的执行器在它的`Execute`方法中，会调用`connectorClient.ExecuteAction(ctx, nodeType, inputs)`。
        *   在初始化`workflowExecutor`时，我们为工作流定义中所有出现过的动作类型，都注册**同一个`ActionNodeExecutor`实例**。
        ```go
        // 伪代码
        actionExecutor := NewActionNodeExecutor(connectorClient)
        for _, node := range routine.WorkflowGraph.Nodes {
            if isActionType(node.Type) {
                workflowExecutor.RegisterNode(node.Type, actionExecutor)
            }
        }
        ```
    4.  **执行**: 调用`workflowExecutor.Run(...)`。
    5.  **日志记录**: 无论成功或失败，将完整的`ExecutionState`（包含每一步的输入输出）和最终状态记录到`routine_execution_logs`表中。

### 3.3 与`connector-service`的协同

`routines-service`与`connector-service`之间是一种清晰的**调用与被调用**关系，实现了关注点分离。

*   **`routines-service` 关心**:
    *   **何时**触发一个工作流 (When)。
    *   **哪个**工作流被触发 (Which)。
    *   工作流的**执行顺序和数据流** (How)。
*   **`connector-service` 关心**:
    *   一个动作**具体做了什么** (What)。
    *   如何与**第三方或内部服务**的API进行交互。
    *   如何处理特定动作的**认证和参数转换**。

这种解耦使得在`connector-service`中增删一个动作，对`routines-service`的核心逻辑完全透明。

## 4. 数据库与数据模型

*   **`routine_definitions` Table**:
    *   `workflow_graph` (JSONB): 存储与`pkg/workflow.Workflow`结构兼容的JSON对象。
    *   `trigger_config` (JSONB): 存储触发器的配置，如`{ "type": "event", "source": "community.qa.AnswerAccepted" }` 或 `{ "type": "cron", "spec": "0 9 * * *" }`。
*   **`routine_execution_logs` Table**:
    *   `execution_trace` (JSONB): 存储一个数组，数组中每个元素都代表一个节点的执行记录 `{ "node_id", "status", "inputs", "outputs", "error" }`。这对于用户调试其Routine至关重要。
    *   **分区**: 此表会非常巨大，**必须**按`start_time`进行月度或周度分区。

## 5. 总结

本架构设计通过以下关键点，构建了一个生产级的`routines-service`：
1.  **分层触发器**: 通过`adapter/trigger`层，将不同来源的触发信号标准化，送入统一的编排层。
2.  **独立工作单元**: 将资源密集的轮询任务部署为独立的Worker，保证在线API性能。
3.  **职责清晰的协同**: 与`connector-service`明确分工，`routines-service`负责“编排”，`connector-service`负责“执行”，实现了高度解耦和可扩展性。
4.  **安全第一**: 通过临时的、范围受限的令牌来模拟用户权限，确保了工作流执行的安全性。
5.  **可观测性与调试**: 详尽的、结构化的执行日志为用户和开发者提供了强大的调试能力。

这种架构使得`routines-service`能够作为一个健壮、灵活、安全的平台自动化中心，随着`connector-service`能力的不断丰富，其价值和想象空间将无限扩大。