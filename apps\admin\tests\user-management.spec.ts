/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:45:00
 * Modified: 2025-01-23 18:45:00
 */

import { test, expect } from '@playwright/test';

test.describe('User Management (CRUD Flow)', () => {
  const newUser = {
    email: `testuser-${Date.now()}@example.com`,
    firstName: 'Test',
    lastName: 'User',
    username: `testuser-${Date.now()}`,
  };

  test.beforeEach(async ({ page }) => {
    // Log in before each test in this describe block
    await page.goto('/login');
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password');
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('should allow creating, updating, and deleting a user', async ({ page }) => {
    // **1. Navigate to User Management**
    await page.goto('/users');
    await expect(page.getByRole('heading', { name: '用户管理' })).toBeVisible();

    // **2. Create a new user**
    await page.getByRole('button', { name: '新建用户' }).click();
    await expect(page).toHaveURL(/.*\/users\/create/);

    await page.getByLabel('First Name').fill(newUser.firstName);
    await page.getByLabel('Last Name').fill(newUser.lastName);
    await page.getByLabel('Username').fill(newUser.username);
    await page.getByLabel('Email').fill(newUser.email);
    
    await page.getByRole('button', { name: 'Create User' }).click();
    await expect(page.locator('.ant-message-success')).toBeVisible();

    // **3. Verify user appears in the list**
    await page.goto('/users');
    await expect(page.getByText(newUser.email)).toBeVisible();

    // **4. Edit the user's role**
    const userRow = page.locator(`tr:has-text("${newUser.email}")`);
    await userRow.getByRole('button', { name: '编辑' }).click();
    
    await expect(page).toHaveURL(/.*\/edit/);
    // Let's assume there is a role selector
    await page.getByLabel('Role').selectOption('ADMIN');
    await page.getByRole('button', { name: 'Save Changes' }).click();
    await expect(page.locator('.ant-message-success')).toBeVisible();

    // **5. Verify the role change**
    await page.goto('/users');
    const updatedUserRow = page.locator(`tr:has-text("${newUser.email}")`);
    await expect(updatedUserRow.getByText('ADMIN')).toBeVisible();

    // **6. Delete the user**
    await updatedUserRow.getByRole('button', { name: '删除' }).click();
    // Confirm the deletion in the modal
    await page.getByRole('button', { name: '确认删除' }).click();
    await expect(page.locator('.ant-message-success')).toBeVisible();

    // **7. Verify the user is removed**
    await page.goto('/users');
    await expect(page.getByText(newUser.email)).not.toBeVisible();
  });
}); 