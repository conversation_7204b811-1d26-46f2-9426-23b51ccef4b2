好的，遵照您的指示。我将为您生成一份**最终的、集大成的、包含了所有新增服务和架构决策的生产级** `platform-overall-srs.md` 文档。

这份文档将是我们所有讨论的最终沉淀，整合了所有微服务的核心职责、最新的多原生平台前端架构、Go-Centric技术选型、以及极致细化的Monorepo目录结构。它将作为整个CINA.CLUB平台**唯一的、最高级别的权威技术蓝图**。

---
### CINA.CLUB - 平台总体 需求规格说明书

**版本: 9.0 (全功能生产级最终蓝图)**  
**发布日期: 2025-06-27**  
**文档负责人:** [平台总架构师/CTO]  
**审批人:** [CINA.CLUB 核心决策层]

#### 修订历史
| 版本 | 日期       | 修订人     | 修订描述                                                                 |
|------|------------|------------|--------------------------------------------------------------------------|
| 7.1  | 2025-06-27 | Cina.Club  | 整合所有附件，形成v7.0最终版。                                           |
| 8.0  | 2025-06-27 | Cina.Club  | **最终版。新增直播与快讯系统，并将其所有相关服务和架构设计融入平台总体蓝图。** |
| 9.0  | 2025-07-09 | Cina.Club  | 集成Web3钱包功能域** |

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [平台级功能需求域](#3-平台级功能需求域)
4.  [平台级非功能性需求](#4-平台级非功能性需求)
5.  [技术架构与原则 (Go-Centric 全栈)](#5-技术架构与原则-go-centric-全栈)
6.  [附录A: 核心业务流程图](#6-附录a-核心业务流程图)
7.  [附录B: 微服务清单与核心职责](#7-附录b-微服务清单与核心职责)
8.  [附录C: 统一Monorepo代码库架构 (极致细化)](#8-附录c-统一monorepo代码库架构-极致细化)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB旨在通过构建一个集成了先进AI技术、多元化生活服务、高效协作工具和沉浸式数字体验的综合平台，革新用户的数字生活方式。本需求规格说明书 (SRS) 定义了驱动CINA.CLUB平台的**整个后端、多原生平台前端及核心共享逻辑**的技术架构、功能范围和非功能性要求。其目的在于提供一个**单一、权威的蓝图**，指导平台所有技术团队的开发、协作和运维工作，确保最终产品的一致性、高质量和可扩展性。

#### 1.2. 平台范围与边界
本SRS定义的平台负责处理和管理CINA.CLUB的所有核心业务逻辑、数据存储、用户身份、AI能力、交易流程、实时通信、内容管理、**各原生平台的前端渲染与客户端逻辑**以及与第三方服务的集成。它是一个**全栈技术规约**。

*   **范围之内 (In-Scope)**: 用户身份与成长体系、AI助手与自动化、内容与知识生态、市场与交易流程、实时通信与通知、**直播与快讯**、虚拟形象与元宇宙、数据分析与治理、以及实现以上功能的**全栈（后端微服务、前端原生应用、共享核心Go库）**代码库。

*   **范围之外 (Out-of-Scope)**: 底层物理基础设施的管理（依赖云提供商）、需要物理世界操作的离线服务履约（但平台管理其信息流）。

#### 1.3. 目标用户与系统涉众
*   **最终用户**: CINA.CLUB的普通消费者、服务需求方、服务提供方、社区参与者。
*   **内部服务消费者**: 平台内的各个微服务之间相互调用。
*   **平台管理员与运营团队**: 通过统一的后台管理系统与后端服务交互。
*   **开发与运维团队**: 本文档的主要读者和执行者。

---

### 2. 总体描述

#### 2.1. 平台愿景与核心价值主张
*   **愿景**: 成为全球领先的、以人为本的、智能驱动的数字生活与协作生态系统。
*   **核心价值**: 赋能个体、连接价值、智能驱动、信任安全、开放共创。

#### 2.2. 总体架构原则
*   **Go-Centric 全栈协同**: 后端全面采用Go；**前端原生应用（Kotlin, SwiftUI, C#, JS）共享由Go编写的核心逻辑库**，以实现极致的性能、安全和代码复用。
*   **API优先设计 (API-First Design)**: 所有通信都通过良好定义的gRPC/Protobuf契约暴露。
*   **微服务架构 (Backend)**: 后端由一系列高内聚、低耦合的独立微服务构成。
*   **多原生平台架构 (Frontend)**: 为每个主流生态（Android, Apple, Web, Windows）提供**最佳原生UI框架**，以追求极致的用户体验。
*   **异步与事件驱动**: 广泛使用Kafka进行服务解耦和异步处理。
*   **隐私与安全内建**: 端到端加密(E2EE)和应用层加密(ALE)是核心安全策略。
*   **可观测性优先**: 从一开始就设计好日志、监控和追踪。
*   **Monorepo统一管理**: 所有代码库在单一代码库中进行版本控制和管理。

#### 2.3. 约束与假设
*   平台将部署在主流公有云提供商。
*   数据隐私和安全合规性是平台的最高优先级。
*   开发团队具备所选技术栈（**以Go为核心，辅以各原生平台语言**）的开发和运维能力。

---

### 3. 平台级功能需求域

平台功能被划分为多个高内聚的业务领域，由**附录B**中的微服务清单负责实现。

#### 3.1. 用户与身份域
*   **核心功能**: 统一认证、账户与资料管理、基于活跃度的等级系统、会员体系、社交图谱、家庭族谱、RBAC权限控制、KYC/官方认证。
*   **关键服务**: `user-core-service`, `social-service`, `family-tree-service`。

#### 3.2. AI与个性化域
*   **核心功能**: Agentic工作流AI助手、E2EE个人知识库(PKB)与个人记忆(PM)、用户自定义自动化规则(Routines)、端侧AI模型管理与分发。
*   **关键服务**: `ai-assistant-service`, `routines-service`, `personal-kb-service`, `memory-service`, `embedding-service`, `model-management-service`。

#### 3.3. 内容与知识域
*   **核心功能**: 高质量付费共享知识库(CKB)、社区论坛与问答、短视频上传与处理、**7x24实时快讯**、统一内容审核。
*   **关键服务**: `shared-kb-service`, `community-forum-service`, `community-qa-service`, `short-video-service`, `content-moderation-service`, `fast-news-service`, `news-crawler-service`。

#### 3.4. 市场与交易域
*   **核心功能**: 标准化服务市场、多维度评价与申诉、订阅与计费、多网关法币支付、双式记账虚拟货币账本。
*   **关键服务**: `service-offering-service`, `billing-service`, `payment-service`, `cina-coin-ledger-service`, `review-service`。

#### 3.5. 实时通信与通知域
*   **核心功能**: 持久化群聊与1v1聊天、**实时视频直播与互动**、多渠道通知分发、聚合活动流。
*   **关键服务**: `chat-api-service`, `chat-websocket-server`, `live-api-service`, `live-gateway-service`, `live-im-service`, `notification-dispatch-service`, `activity-feed-service`。

#### 3.6. 虚拟形象与元宇宙域
*   **核心功能**: 3D虚拟分身(Avatar)与个性(Persona)管理、虚拟资产系统、多人实时3D场景交互。
*   **关键服务**: `digital-twin-service`, `metaverse-engine-service`。

#### 3.7. 平台基础设施域
*   **核心功能**: API网关、**统一后台管理BFF**、文件存储与图片处理、密钥管理(ALE/E2EE)、统一搜索、地理位置服务、日历同步等。
*   **关键服务**: `api-gateway`, `admin-bff-service`, `file-storage-service`, `key-management-proxy-service`, `search-service`, `search-indexer-service`, `location-service`, `calendar-sync-service`等。

#### **3.8. Web3钱包与去中心化身份域 (新增)**

*   **核心功能**: 为每个用户提供一个**非托管的、多链的加密货币钱包**，作为其在去中心化世界的入口和身份象征。
*   **SR3.8.1 (钱包核心)**: 支持通过BIP-39助记词创建和导入钱包；在设备端安全地存储和管理私钥；支持多钱包管理。
*   **SR3.8.2 (资产管理)**: 支持多链（EVM及非EVM）原生币、Token (ERC-20等)和NFT (ERC-721/1155)的余额获取与展示。
*   **SR3.8.3 (交易处理)**: 支持构建、本地签名和广播标准转账交易；提供Gas费估算。
*   **SR3.8.4 (DApp生态)**: 提供内置的、符合EIP-1193标准的Web3浏览器；支持WalletConnect协议，与外部DApp交互。
*   **SR3.8.5 (增值服务)**: 提供内置的DEX聚合器Swap功能和Staking入口。
*   **关键服务**:
    *   **`wallet-meta-service` (新增)**
    *   **`blockchain-gateway-service` (新增)**
    *   **`market-data-service` (新增)**
    *   `user-core-service` (扩展)
    *   `notification-dispatch-service` (复用)
---

### 4. 平台级非功能性需求

#### 4.1. 性能与吞吐量
*   **API延迟 (P95)**: 核心交互API < 300ms; 认证API < 100ms。
*   **实时通信延迟 (P99)**: 聊天消息 < 150ms; **直播互动 < 200ms**。
*   **直播媒体流延迟**: HTTP-FLV/HLS: 2-8s; WebRTC: < 1s。
*   **前端性能**: 原生应用启动 < 2s; 页面切换流畅; Web应用 LCP < 2.5s。

#### 4.2. 可靠性与可用性
*   **核心服务可用性 (SLO)**: >= 99.95% (包括`user-core`, `payment`, `chat`, `live`体系)。
*   **数据持久性**: RPO <= 5分钟, RTO <= 1小时。

#### 4.3. 可扩展性
*   所有服务和后台Worker均可独立水平扩展。架构需支持百万级DAU和**十万级并发直播观众**。

#### 4.4. 安全性 (最高优先级)
*   严格遵循数据加密、认证授权、合规性要求。**直播内容需进行实时/准实时审核**。

#### 4.5. 可观测性
*   日志、指标、追踪三位一体，覆盖所有组件。

---

### 5. 技术架构与原则 (Go-Centric 全栈)

#### 5.1. 后端技术栈 (`/services`, `/pkg`)
*   **语言**: Go 1.22+
*   **通信**: gRPC (S2S), REST (via grpc-gateway)
*   **数据库**: PostgreSQL, MongoDB, Redis, Elasticsearch, VectorDB
*   **消息队列**: Kafka
*   **异步任务**: Asynq

#### 5.2. 前端技术栈 (`/apps`)
*   **Android/HarmonyOS**: Kotlin + Jetpack Compose
*   **Apple (iOS, macOS, ...)**: Swift + SwiftUI
*   **Web**: Svelte/SvelteKit + TypeScript
*   **Windows**: C# + WinUI 3
*   **核心逻辑**: **所有平台都通过各自的原生绑定技术，调用由Go编写、编译的共享核心库 (`/core`)**。

#### 5.3. 共享核心技术栈 (`/core`)
*   **语言**: Go 1.22+
*   **编译目标**:
    *   **后端**: 直接Go import。
    *   **Android**: Go Mobile (生成 `.aar`)。
    *   **Apple**: Go Mobile (生成 `.xcframework`)。
    *   **Web**: WebAssembly (生成 `.wasm`)。
    *   **Windows**: C-shared library (生成 `.dll`)。
*   **核心能力**: 使用Go实现E2EE加密、数据同步协议、本地AI推理绑定等逻辑。

#### 5.4. Monorepo与自动化
*   **版本控制**: Git
*   **Monorepo工具**: Turborepo (前端), Go Workspaces (后端), PNPM (JS包管理)。
*   **CI/CD**: GitHub Actions，使用高度参数化的脚本 (`/scripts`)。
*   **基础设施**: Terraform (IaC), Kubernetes (容器编排), Docker (容器化)。

---

### 6. 附录A: 核心业务流程图

#### 6.1. 用户预订付费服务流程 (Saga)
```mermaid
sequenceDiagram
    participant Client
    participant ServiceOffering as service-offering
    participant Billing as billing-service
    participant Payment as payment-service
    participant Schedule as schedule-service
    participant Notification as notification-dispatch

    Client->>ServiceOffering: 1. CreateBooking (offeringId, timeSlot)
    ServiceOffering->>ServiceOffering: 2. Create Order (status: PENDING)
    
    note over ServiceOffering: Start Payment Saga
    ServiceOffering->>Billing: 3. CreateInvoice for Order
    Billing-->>ServiceOffering: (invoiceId)
    
    ServiceOffering->>Payment: 4. InitiatePayment for Invoice
    Payment-->>ServiceOffering: (paymentParams for Client)
    ServiceOffering-->>Client: (paymentParams)
    
    note over Client: User completes payment on frontend
    
    Payment-->>ServiceOffering: 5. [Event/Callback] PaymentSucceeded
    ServiceOffering->>ServiceOffering: 6. Update Order status to CONFIRMED
    ServiceOffering->>Schedule: 7. Block Time Slot
    ServiceOffering->>Notification: 8. Notify both parties
```

#### 6.2. AI助手使用PKB回答问题流程 (E2EE)
```mermaid
sequenceDiagram
    participant Client
    participant AIAssistant as ai-assistant-service
    participant PKB as personal-kb-service
    participant KMSProxy as key-management-proxy
    participant Embedding as embedding-service
    participant VectorDB

    Client->>AIAssistant: "根据我的项目笔记，总结一下A项目的风险"
    AIAssistant->>Embedding: 1. Get embedding for query
    Embedding-->>AIAssistant: (Query Vector)

    AIAssistant->>PKB: 2. Search PKB with query vector
    PKB->>VectorDB: 3. Vector search, get item IDs
    PKB->>PKB: 4. For each item ID, get encrypted content
    PKB->>KMSProxy: 5. Request decryption for each content
    KMSProxy-->>PKB: (Decrypted content)

    note over PKB: In-memory filtering and ranking
    PKB-->>AIAssistant: (Top N relevant decrypted notes)

    AIAssistant->>AIAssistant: 6. Augment prompt with notes
    AIAssistant->>LLM: 7. Generate summary
    LLM-->>AIAssistant: (Summary text)
    AIAssistant-->>Client: (Final answer)
```

#### 6.3. 主播开播与观众加入流程
```mermaid
sequenceDiagram
    participant Pusher as "主播App"
    participant Player as "观众App"
    participant LiveAPI as "live-api-service"
    participant LiveGateway as "live-gateway-service"
    participant LiveIM as "live-im-service"
    participant MediaServer as "SRS集群"
    participant CDN

    Pusher->>LiveAPI: 1. StartLive
    LiveAPI->>LiveGateway: 2. RequestPushURL
    LiveGateway-->>LiveAPI: (推流URL)
    LiveAPI-->>Pusher: (推流URL)
    Pusher-->>MediaServer: 3. **推流 (RTMP/SRT)**
    
    Player->>LiveAPI: 4. JoinRoom
    LiveAPI->>LiveGateway: 5. RequestPlayURL
    LiveGateway-->>LiveAPI: (拉流URL - CDN)
    LiveAPI-->>Player: (拉流URL, IM地址)
    Player-->>CDN: 6. **拉流 (HLS/FLV)**
    CDN-->>MediaServer: (回源)
    
    Player-->>LiveIM: 7. **连接WebSocket, 加入IM房间**
    Player->>LiveIM: 8. 发送弹幕
    LiveIM-->>Player: 9. 接收广播的弹幕
    LiveIM-->>Pusher: 9. 接收广播的弹幕
```

---

### 7. 附录B: 微服务清单与核心职责

| 领域 (Domain)         | 微服务名称                      | 核心职责                                                               |
| --------------------- | ------------------------------- | ---------------------------------------------------------------------- |
| **网关与入口**        | `api-gateway`                   | 路由, 认证, 限流, 聚合                                                 |
| **用户与身份**        | `user-core-service`             | 认证, 账户, 资料, **等级**, **会员**, RBAC, KYC                        |
|                       | `social-service`                | **关注/好友**, 粉丝, 拉黑                                              |
|                       | `family-tree-service`           | 家庭, 族谱, 亲缘关系                                                   |
| **AI与个性化**        | `ai-assistant-service`          | 对话编排, LLM代理, 工作流引擎                                          |
|                       | `embedding-service`             | **多模态**文本/图片向量化                                              |
|                       | `memory-service`                | **E2EE个人记忆(PM)**, RAG支持, 记忆生命周期                            |
|                       | `personal-kb-service`           | **E2EE个人知识库(PKB)**, 混合搜索                                      |
|                       | `routines-service`              | **用户自定义自动化规则**, 多源触发器                                   |
|                       | `model-management-service`      | **端侧AI模型**生命周期管理与分发                                       |
| **内容与知识**        | `shared-kb-service`             | 共享/商业知识库(CKB), **策展工作流**, 付费访问                        |
|                       | `community-forum-service`       | 社区论坛, **富文本**, 热度算法                                         |
|                       | `community-qa-service`          | 社区问答, **悬赏机制**, 声望系统                                       |
|                       | `short-video-service`           | 短视频上传, **智能转码**, CDN分发                                      |
|                       | `fast-news-service`             | **(新增)** 7x24快讯处理、去重、增强、发布                             |
|                       | `news-crawler-service`          | **(新增)** 多源新闻采集与标准化                                        |
| **市场与交易**        | `service-offering-service`      | 标准化服务市场, **Saga订单流程**, 高级可用性                           |
|                       | `review-service`                | **多维度评价**, AI摘要, **申诉仲裁**                                   |
| **实时通信与通知**    | `chat-api-service`              | 聊天历史, **高级群聊**管理                                             |
|                       | `chat-websocket-server`         | 实时WebSocket连接, **多设备同步**                                      |
|                       | `notification-dispatch-service` | **多渠道**通知分发, **防打扰策略**, 模板引擎                         |
|                       | `activity-feed-service`         | 应用内**多Feed流**/通知历史, **智能聚合**                            |
|                       | `live-api-service`              | **(新增)** 直播业务逻辑与状态机管理                                    |
|                       | `live-gateway-service`          | **(新增)** 媒体网关, 推拉流鉴权与地址分发                              |
|                       | `live-im-service`               | **(新增)** 直播间互动消息(弹幕/礼物)广播                               |
| **虚拟形象与元宇宙**  | `digital-twin-service`          | 虚拟分身(Avatar), **资产与库存**, Persona                                |
|                       | `metaverse-engine-service`      | 3D虚拟世界, **服务器权威**, 实时同步                                   |
| **平台治理与基础设施** | `cina-coin-ledger-service`      | 灵境币账本, **双式记账法**                                             |
|                       | `billing-service`               | 计费与订阅, **高级定价模型**                                           |
|                       | `payment-service`               | 法币支付网关, **多网关路由**, 退款争议                                 |
|                       | `gamification-service`          | **多维激励体系** (XP/徽章/积分), **规则引擎**                          |
|                       | `content-moderation-service`    | UGC内容审核, **多模态**, **工作流引擎**                                |
|                       | `location-service`              | 地理编码, POI, **地理围栏**, **多提供商**                              |
|                       | `file-storage-service`          | 文件存储代理, **图片处理**, **多后端**                                 |
|                       | `key-management-proxy-service`  | **信封加密**, 用户数据加密密钥(DEK)管理                                |
|                       | `search-service`                | **混合搜索**聚合查询, **个性化排序**                                   |
|                       | `search-indexer-service`        | 搜索索引器, 事件驱动同步                                               |
|                       | `analytics-service`             | (内部) 数据分析与报告, **数据治理**                                    |
|                       | `cloud-sync-service`            | (E2EE方案) 加密数据块同步, **版本向量**                                |
|                       | `calendar-sync-service`         | 外部日历同步, **双向同步**                                             |
|                       | `admin-bff-service`             | **(新增)** 统一后台管理BFF (Backend for Frontend)                      |
| **Web3钱包与身份**    | `wallet-meta-service`           | **(新增)** 管理钱包的非敏感元数据（名称、标签、自定义Token列表）。   | `../../srs/services/wallet-meta-service-srs.md` |
|                       | `blockchain-gateway-service`    | **(新增)** 统一的区块链交互网关，封装RPC调用（查余额/交易/广播）。   | `../../srs/services/blockchain-gateway-service-srs.md`|
|                       | `market-data-service`           | **(新增)** 提供加密货币实时价格、市值等市场数据。                      | `../../srs/services/market-data-service-srs.md`|

**对现有服务的职责扩展**:
*   **`user-core-service`**: **扩展**其职责，增加对用户Web3身份（钱包地址）的关联管理，但不管理钱包本身。
*   **`notification-service`**: **复用**其能力，用于发送交易状态相关的推送通知。


---

### 8. 附录C: 统一Monorepo代码库架构 (极致细化)

*(在原有清单基础上，新增服务和应用的目录占位)*

```
cina.club-monorepo/
├── .config/
│   ├── config.dev.yaml.template
│   ├── config.staging.yaml.template
│   └── config.prod.yaml.template
├── .github/
│   ├── ISSUE_TEMPLATE/
│   │   ├── bug_report.md
│   │   └── feature_request.md
│   └── workflows/
│       ├── ci-backend.yml
│       ├── ci-frontend.yml
│       ├── release.yml
│       ├── deploy.yml
│       └── generate.yml
├── .vscode/
│   ├── extensions.json
│   ├── launch.json
│   └── settings.json
├── apps/
│   ├── android/
│   │   ├── app/
│   │   │   ├── build.gradle.kts
│   │   │   └── src/main/java/com/cinaclub/
│   │   │       ├── CinaClubApplication.java
│   │   │       ├── MainActivity.java
│   │   │       └── nativemodules/
│   │   │           ├── AICoreModule.kt
│   │   │           ├── CoreGoBridgePackage.kt
│   │   │           ├── CryptoModule.kt
│   │   │           └── DataSyncModule.kt
│   │   ├── build-logic/
│   │   ├── core/
│   │   │   ├── common/
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── go-bridge/
│   │   ├── feature_auth/
│   │   ├── feature_chat/
│   │   ├── feature_pkb/
│   │   └── libs/
│   │       └── core-go.aar
│   ├── apple/
│   │   ├── CinaClub.xcodeproj/
│   │   ├── Frameworks/
│   │   │   └── CoreGo.xcframework
│   │   ├── Packages/
│   │   │   ├── AppCore/
│   │   │   ├── DataLayer/
│   │   │   ├── DesignSystem/
│   │   │   ├── Feature/
│   │   │   └── GoBridge/
│   │   └── Targets/
│   │       ├── iOS_iPadOS/
│   │       ├── macOS/
│   │       ├── visionOS/
│   │       └── watchOS/
│   ├── web/
│   │   ├── src/
│   │   │   ├── lib/
│   │   │   │   └── client/
│   │   │   │       └── wasm_loader.ts
│   │   │   └── routes/
│   │   └── static/
│   │       └── core.wasm
│   ├── windows/
│   │    ├── CinaClub.sln
│   │    ├── CinaClub.App/
│   │    ├── CinaClub.Core/
│   │    ├── CinaClub.Infrastructure/
│   │    │   └── GoBridge/
│   │    └── libs/
│   │        └── x64/
│   │            ├── core_go.dll
│   │            └── core_go.h
│   └── admin-frontend/         # <-- 新增
├── core/
│   ├── api/
│   │   ├── buf.gen.yaml
│   │   ├── buf.lock
│   │   ├── buf.yaml
│   │   └── proto/
│   │       └── v1/
│   │           ├── activity_feed.proto
│   │           ├── ai_assistant.proto
│   │           ├── analytics.proto
│   │           ├── billing.proto
│   │           ├── calendar_sync.proto
│   │           ├── chat.proto
│   │           ├── cina_coin_ledger.proto
│   │           ├── cloud_sync.proto
│   │           ├── common.proto
│   │           ├── community_forum.proto
│   │           ├── community_qa.proto
│   │           ├── content_moderation.proto
│   │           ├── digital_twin.proto
│   │           ├── embedding.proto
│   │           ├── errors.proto
│   │           ├── family_tree.proto
│   │           ├── file_storage.proto
│   │           ├── gamification.proto
│   │           ├── key_management_proxy.proto
│   │           ├── location.proto
│   │           ├── memory.proto
│   │           ├── metaverse_engine.proto
│   │           ├── model_management.proto
│   │           ├── notification_dispatch.proto
│   │           ├── payment.proto
│   │           ├── personal_kb.proto
│   │           ├── review.proto
│   │           ├── routines.proto
│   │           ├── schedule.proto
│   │           ├── search.proto
│   │           ├── service_offering.proto
│   │           ├── short_video.proto
│   │           ├── social.proto
│   │           ├── live.proto          # <-- 新增
│   │           ├── live_gateway.proto  # <-- 新增
│   │           ├── live_im.proto       # <-- 新增
│   │           └── news.proto          # <-- 新增
│   │           └── user_core.proto
│   ├── aic/
│   │   ├── cgo_bindings.go
│   │   ├── engine.go
│   │   ├── engine_llama_cpp.go
│   │   └── exports_mobile.go
│   ├── crypto/
│   │   ├── e2ee.go
│   │   ├── exports_mobile.go
│   │   └── exports_wasm.go
│   ├── datasync/
│   │   ├── chunker.go
│   │   ├── engine.go
│   │   ├── version_vector.go
│   │   ├── exports_mobile.go
│   │   └── exports_wasm.go
│   ├── models/
│   │   ├── asset.go
│   │   ├── content.go
│   │   ├── service.go
│   │   ├── social.go
│   │   └── user.go
│   └── go.mod
├── docs/
│   ├── architecture/
│   │   ├── L1_Principles_and_Vision/
│   │   │   ├── 01_architectural_principles.md
│   │   │   └── 02_technology_radar.md
│   │   ├── L2_System_Landscape/
│   │   │   ├── 01_system_context_and_domains.md
│   │   │   ├── 02_service_communication_patterns.md
│   │   │   └── 03_platform_data_flow.md
│   │   ├── L3_Core_Capabilities_Deep_Dive/
│   │   │   ├── 01_authentication_and_authorization.md
│   │   │   ├── 02_observability_stack.md
│   │   │   ├── 03_data_management_and_persistence.md
│   │   │   ├── 04_asynchronous_processing_and_events.md
│   │   │   ├── 05_security_and_privacy_model.md
│   │   │   └── 06_ai_and_workflow_engine.md
│   │   └── L4_Service_Design_Patterns/
│   │       ├── 01_clean_architecture_for_services.md
│   │       ├── 02_repository_and_unit_of_work_pattern.md
│   │       └── 03_error_handling_best_practices.md
│   ├── adr/
│   │   └── 0001-example-adr.md
│   ├── diagrams/
│   │   └── system_context_diagram.md
│   └── srs/
│       ├── platform-overall-srs.md
│       ├── packages/
│       │   ├── pkg-auth-srs.md
│       │   ├── pkg-config-srs.md
│       │   ├── pkg-database-srs.md
│       │   ├── pkg-errors-srs.md
│       │   ├── pkg-logger-srs.md
│       │   ├── pkg-messaging-srs.md
│       │   ├── pkg-middleware-srs.md
│       │   ├── pkg-tracing-srs.md
│       │   └── pkg-workflow-srs.md
│       └── services/
│           ├── activity-feed-service-srs.md
│           ├── ai-assistant-service-srs.md
│           ├── analytics-service-srs.md
│           ├── billing-service-srs.md
│           ├── calendar-sync-service-srs.md
│           ├── chat-api-service-srs.md
│           ├── chat-websocket-server-srs.md
│           ├── cina-coin-ledger-service-srs.md
│           ├── cloud-sync-service-srs.md
│           ├── community-forum-service-srs.md
│           ├── community-qa-service-srs.md
│           ├── content-moderation-service-srs.md
│           ├── digital-twin-service-srs.md
│           ├── embedding-service-srs.md
│           ├── family-tree-service-srs.md
│           ├── file-storage-service-srs.md
│           ├── gamification-service-srs.md
│           ├── key-management-proxy-service-srs.md
│           ├── location-service-srs.md
│           ├── memory-service-srs.md
│           ├── metaverse-engine-service-srs.md
│           ├── model-management-service-srs.md
│           ├── notification-dispatch-service-srs.md
│           ├── payment-service-srs.md
│           ├── personal-kb-service-srs.md
│           ├── review-service-srs.md
│           ├── routines-service-srs.md
│           ├── schedule-service-srs.md
│           ├── search-indexer-service-srs.md
│           ├── search-service-srs.md
│           ├── service-offering-service-srs.md
│           ├── short-video-service-srs.md
│           ├── social-service-srs.md
│           └── user-core-service-srs.md
├── infra/
│   ├── docker/
│   │   ├── base/
│   │   │   ├── Dockerfile.go
│   │   │   └── Dockerfile.python
│   │   ├── dev/
│   │   │   └── docker-compose.yml
│   │   └── services/
│   │       ├── activity-feed.Dockerfile
│   │       ├── ai-assistant.Dockerfile
│   │       ├── analytics.Dockerfile
│   │       ├── billing.Dockerfile
│   │       ├── calendar-sync.Dockerfile
│   │       ├── chat-api.Dockerfile
│   │       ├── chat-websocket-server-srs.md
│   │       ├── cina-coin-ledger.Dockerfile
│   │       ├── cloud-sync.Dockerfile
│   │       ├── community-forum.Dockerfile
│   │       ├── community-qa.Dockerfile
│   │       ├── content-moderation.Dockerfile
│   │       ├── digital-twin.Dockerfile
│   │       ├── embedding.Dockerfile
│   │       ├── family-tree.Dockerfile
│   │       ├── file-storage.Dockerfile
│   │       ├── gamification.Dockerfile
│   │       ├── key-management-proxy.Dockerfile
│   │       ├── location.Dockerfile
│   │       ├── memory.Dockerfile
│   │       ├── metaverse-engine.Dockerfile
│   │       ├── model-management.Dockerfile
│   │       ├── notification-dispatch.Dockerfile
│   │       ├── payment.Dockerfile
│   │       ├── personal-kb.Dockerfile
│   │       ├── review.Dockerfile
│   │       ├── routines.Dockerfile
│   │       ├── schedule.Dockerfile
│   │       ├── search-indexer.Dockerfile
│   │       ├── search.Dockerfile
│   │       ├── service-offering.Dockerfile
│   │       ├── short-video.Dockerfile
│   │       ├── social.Dockerfile
│   │       └── user-core.Dockerfile
│   ├── kubernetes/
│   │   ├── base/
│   │   ├── overlays/
│   │   └── system/
│   └── terraform/
│       ├── environments/
│       └── modules/
├── pkg/
│   ├── auth/
│   │   ├── context.go
│   │   ├── interceptor/
│   │   └── jwks/
│   ├── config/
│   │   └── config.go
│   ├── database/
│   │   ├── postgres.go
│   │   └── redis.go
│   ├── errors/
│   │   └── errors.go
│   ├── logger/
│   │   └── logger.go
│   ├── messaging/
│   │   ├── consumer.go
│   │   └── producer.go
│   ├── middleware/
│   │   ├── logging.go
│   │   ├── metrics.go
│   │   ├── recovery.go
│   │   └── tracing.go
│   ├── tracing/
│   │   └── tracing.go
│   ├── workflow/
│   │   ├── engine.go
│   │   └── interface.go
│   └── utils/
│       ├── conv/
│       ├── crypto/
│       ├── rand/
│       ├── slice/
│       ├── str/
│       └── timeutil/
├── scripts/
│   ├── ci/
│   │   ├── lint.sh
│   │   └── test.sh
│   ├── db/
│   │   ├── migrate.sh
│   │   └── seed.sh
│   ├── gen/
│   │   ├── core.sh
│   │   └── proto.sh
│   ├── lib/
│   │   └── helpers.sh
│   └── setup/
│       └── dev-env.sh
├── services/
│   ├── activity-feed-service/
│   ├── ai-assistant-service/
│   ├── analytics-service/
│   ├── api-gateway/
│   ├── billing-service/
│   ├── calendar-sync-service/
│   ├── chat-api-service/
│   ├── chat-websocket-server/
│   ├── cina-coin-ledger-service/
│   ├── cloud-sync-service/
│   ├── community-forum-service/
│   ├── community-qa-service/
│   ├── content-moderation-service/
│   ├── digital-twin-service/
│   ├── embedding-service/
│   ├── family-tree-service/
│   ├── file-storage-service/
│   ├── gamification-service/
│   ├── key-management-proxy-service/
│   ├── location-service/
│   ├── memory-service/
│   ├── metaverse-engine-service/
│   ├── model-management-service/
│   ├── notification-dispatch-service/
│   ├── payment-service/
│   ├── personal-kb-service/
│   ├── review-service/
│   ├── routines-service/
│   ├── schedule-service/
│   ├── search-indexer-service/
│   ├── search-service/
│   ├── service-offering-service/
│   ├── short-video-service/
│   ├── social-service/
│   ├── admin-bff-service/      # <-- 新增
│   ├── fast-news-service/      # <-- 新增
│   ├── live-api-service/       # <-- 新增
│   ├── live-gateway-service/   # <-- 新增
│   ├── live-im-service/        # <-- 新增
│   ├── news-crawler-service/   # <-- 新增 (Python)
│   └── user-core-service/
├── tools/
│   ├── codegen/
│   │   └── main.go
│   ├── config-validator/
│   │   └── main.go
│   └── license-scanner/
│       └── main.go
│
├── .dockerignore
├── .editorconfig
├── .gitignore
├── go.mod
├── go.work
├── package.json
├── pnpm-workspace.yaml
├── README.md
└── turbo.json


---
### **附录D (新增): Web3钱包域 架构设计 (ARCH)**

本附录详细描述了为实现Web3钱包功能而新增的微服务架构。

#### **D.1 架构目标**

*   **安全隔离**: 将与区块链直接交互的逻辑、需要高频更新的市场数据逻辑，与平台现有业务（如社交、内容）完全隔离。
*   **抽象与统一**: `blockchain-gateway-service`必须将多条异构链的RPC调用，抽象为统一的、简洁的内部API。
*   **性能与成本**: `market-data-service`和`blockchain-gateway-service`必须有强大的缓存机制，以降低对第三方API和RPC节点的请求频率和成本。

#### **D.2 新增微服务详细设计**

##### **D.2.1 `wallet-meta-service`**

*   **定位**: 用户的“钱包通讯录”。它**绝对不**触碰任何私钥或助记词。
*   **核心职责**:
    *   管理用户为其钱包地址设置的**名称或标签** (e.g., "My Savings Wallet")。
    *   管理用户**手动添加的代币合约地址**列表。
    *   管理**地址簿**（常用联系人地址及其备注）。
*   **数据模型 (PostgreSQL)**:
    *   `user_wallets`: `id`, `user_id`, `wallet_address`, `chain_type`, `name`。
    *   `wallet_custom_tokens`: `wallet_id`, `token_contract_address`, `chain_type`。
    *   `wallet_address_book`: `user_id`, `address`, `name`, `chain_type`。
*   **交互**:
    *   被客户端调用，用于个性化设置。
    *   被`blockchain-gateway-service`调用，以获取某个用户地址需要查询的代币列表。

##### **D.2.2 `blockchain-gateway-service`**

*   **定位**: 平台的“区块链翻译官”和“RPC代理”。
*   **核心职责**:
    *   **抽象层**: 提供统一的gRPC接口，如`GetBalance`, `GetTransactions`, `EstimateGas`, `BroadcastTransaction`。请求中包含`chain`参数（如`"bsc"`, `"polygon"`）。
    *   **多链适配**: 内部为每条支持的链实现一个`Provider`适配器。每个适配器封装了与该链RPC节点交互的逻辑（使用`go-ethereum`, `@solana/web3.js`的Go封装等）。
    *   **RPC节点管理**: 管理与多个RPC节点（自建或Infura/Alchemy）的连接池，并支持对节点的健康检查和故障切换。
    *   **缓存**: 在Redis中大量缓存不常变化的数据，如**交易历史**和**余额**（缓存TTL较短，如15-30秒）。
*   **API示例 (gRPC)**:
    ```protobuf
    rpc GetBalance(GetBalanceRequest { chain: string, address: string, token_contracts: repeated string }) returns (GetBalanceResponse);
    rpc BroadcastTransaction(BroadcastRequest { chain: string, signed_tx_hex: string }) returns (BroadcastResponse { tx_hash: string });
    ```
*   **事件发布**: 广播交易成功后，发布一个`TransactionBroadcasted`事件到Kafka，其中包含`tx_hash`, `chain`, `address`等信息，供`notification-service`进行后续的链上状态跟踪。

##### **D.2.3 `market-data-service`**

*   **定位**: 平台的“加密货币行情中心”。
*   **核心职责**:
    *   **数据拉取**: 后台任务**定期**（如每分钟）从CoinGecko、CoinMarketCap等多个数据源拉取主流加密货币的价格、市值、24小时涨跌幅等数据。
    *   **数据清洗与聚合**: 对来自多数据源的数据进行清洗、去重和聚合，形成一个统一的内部价格源。
    *   **高速查询**: 提供一个极低延迟的API，供客户端获取一个或多个代币的实时价格。
*   **数据存储**:
    *   **Redis**: **强制使用**。所有价格数据都存储在Redis中，以实现毫秒级查询。`Key: "price:bitcoin"`, `Value: "{usd: 60000, cny: 420000, ...}"`。
    *   **PostgreSQL (可选)**: 用于存储历史价格数据，以供未来图表分析。
*   **API示例 (gRPC)**:
    ```protobuf
    rpc GetPrices(GetPricesRequest { token_ids: repeated string }) returns (GetPricesResponse { prices: map<string, PriceInfo> });
    ```

#### **D.3 对前端 `core/` 包的影响**

为了支持钱包功能，前端的`core/`共享Go包需要进行扩展。

*   **`core/crypto`**:
    *   **必须**包含符合**BIP-39, BIP-32, BIP-44**标准的、完整的密钥派生逻辑。
    *   **必须**包含针对不同链（如ETH的`keccak256`，非EVM链的其他算法）的**签名算法**实现。
    *   这是客户端安全的核心，其代码必须经过最严格的审计。
*   **`core/aic` 和 `core/datasync`**: 与钱包功能无直接关系，保持不变。

#### **D.4 整体数据流示例：用户查看BSC钱包首页**

1.  **Client App** 向 **`api-gateway`** 发起并行的多个请求。
2.  `GET /users/me`: 请求路由到 **`user-core-service`**，获取用户信息。
3.  `GET /wallets/me?chain=bsc`: 请求路由到 **`wallet-meta-service`**，获取钱包名称和用户添加的自定义代币列表。
4.  `GET /prices?ids=bnb,usdt,busd,...`: 请求路由到 **`market-data-service`**，从Redis中获取BNB和主流稳定币的价格。
5.  **Client App** 拿到自定义代币列表后，向 **`blockchain-gateway-service`** 发起`GetBalance`请求，参数包含用户地址和所有需要查询余额的代币合约地址。
6.  **`blockchain-gateway-service`**:
    *   检查Redis缓存中是否有该地址的余额，如果有且未过期，直接返回。
    *   如果未命中，通过BSC的RPC节点查询原生币和所有代币的余额。
    *   将结果存入Redis缓存，并返回给客户端。
7.  **Client App** 收到所有数据后，在UI层进行聚合：显示钱包名称、总资产（根据余额和价格计算）、并分别列出每个代币的数量和价值。

---

通过以上设计，我们将钱包功能作为一个高内聚的功能域，无缝地整合进了CINA.CLUB的Monorepo架构中。它既复用了平台已有的用户和通知能力，又通过新增的专用微服务，有效地隔离了区块链交互的复杂性和风险，确保了整个平台的稳定和可扩展性。
**文档结束**

这份版本9.0的平台总体SRS，通过引入**直播**和**实时快讯**这两大功能域，进一步完善了CINA.CLUB平台作为综合性数字生活生态系统的能力。架构上，新增的服务严格遵循了**职责分离、事件驱动、解耦协同**的核心原则，能够无缝地融入现有的Monorepo和技术体系中。至此，这份文档构成了一份覆盖极广、细节深入、且具备前瞻性的全栈生产级架构蓝图。