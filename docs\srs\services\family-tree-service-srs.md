
### CINA.CLUB - family-tree-service 需求规格说明书

**版本: 1.0**  
**发布日期: 2025-06-16**  
**最后修订日期: 2025-06-16**  
**文档负责人:** [Cina.Club]  
**审批人:** [CTO/Cina.Club]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了深化CINA.CLUB平台的社交连接，超越传统的“关注/粉丝”模型，`family-tree-service` 旨在构建一个以家庭为核心的、结构化的族谱社交网络。本服务允许用户邀请家人，建立并可视化家庭成员间的真实关系（如父子、夫妻），从而增强用户的情感归属感，沉淀具有传承价值的家庭数据，并为平台探索更深层次的家庭单位社交与服务模式提供基础。

#### 1.2. 服务范围
本服务 **负责**:
*   **家庭成员管理**: 注册用户到族谱系统中，并冗余其核心信息。
*   **关系邀请与确认**: 管理用户间建立家庭关系的邀请流程。
*   **关系图谱构建**: 存储和管理用户之间已确认的、有向的家庭关系（如父子、配偶）。
*   **族谱图生成**: 提供API，能根据用户ID，动态生成其所在的、可视化的族谱图数据结构。
*   **关系查询**: 提供API查询任意两个用户之间的亲缘关系路径。
*   **隐私控制**: 管理用户对其族谱信息的可见性设置。
*   **事件发布**: 当关系发生重要变化时，发布领域事件。

本服务 **不负责**:
*   用户身份认证或核心资料管理 (由 `user-core-service` 负责)。
*   通用的、非家庭关系的社交图谱（关注/粉丝） (由 `social-service` 负责)。
*   即时通讯 (由 `chat-service` 体系负责)。
*   文件存储（如家庭相册的图片） (由 `file-storage-service` 负责，本服务只存引用)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用**: 通过API Gateway调用本服务，执行邀请、接受邀请、查询族谱图等操作。
*   **`user-core-service`**: 本服务消费其发布的 `UserProfileUpdatedEvent` 和 `UserAccountHardDeletedEvent`。
*   **`notification-dispatch-service`**: 本服务调用它发送邀请和关系确认的通知。
*   **`search-service`**: （可选）消费本服务发布的事件，以支持通过家庭关系进行搜索。
*   **CINA.CLUB平台管理员**: 通过管理后台查看和（在极端情况下）管理族谱关系。

#### 1.4. 定义与缩略语
*   **族谱 (Family Tree)**: 以用户为节点，以家庭关系为边的图结构。
*   **节点 (Node)**: 代表一个家庭成员，与一个CINA.CLUB用户关联。
*   **边 (Edge)**: 代表两个成员之间的有向关系，如`PARENT_OF`。
*   **关系类型 (Relationship Type)**: 如 `PARENT_OF`, `CHILD_OF`, `SPOUSE_OF`。
*   **邀请 (Invitation)**: 一个用户向另一个用户发起的、建立特定家庭关系的请求。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`family-tree-service` 是平台的“家庭关系中心”，一个高度专业化的图谱服务。它独立于 `social-service`，专注于处理结构化、强验证的家庭纽带。其数据为平台提供了独特的社交维度，可用于家庭群组推荐、纪念日提醒、家庭共享等多种创新功能。

#### 2.2. 主要功能概述
*   基于邀请-确认机制的、安全的关系建立流程。
*   高效的图遍历算法，用于动态生成族谱。
*   精细化的隐私控制。
*   通过事件驱动与平台其他服务解耦。

#### 2.3. 用户特征
本服务的用户是希望在CINA.CLUB中记录和维系家庭关系的注册用户。

#### 2.4. 约束与假设
*   假设存在可靠的 `user-core-service`, `notification-dispatch-service`。
*   族谱关系的建立必须基于双方同意。
*   数据模型的准确性和防止逻辑悖论（如A是B的父亲，B又是A的父亲）是核心挑战。

### 3. 核心流程图

#### 3.1. 建立“父子”关系邀请流程
```mermaid
sequenceDiagram
    participant Alice (Client)
    participant Bob (Client)
    participant API Gateway
    participant FamilyTreeService as FTS
    participant NotificationService
    participant DB

    Alice->>API Gateway: POST /invitations (targetUsername: "bob_username", relationship: "PARENT_OF")
    API Gateway->>FTS: (forward request, with Alice's userId)
    FTS->>DB: 1. Create Invitation record (source: Alice, target: Bob, status: PENDING)
    FTS->>NotificationService: 2. Request to send notification to Bob
    NotificationService-->>Bob: "Alice invites you to be their parent"

    Note over Bob: Bob reviews and accepts the invitation
    
    Bob->>API Gateway: POST /invitations/{inviteId}/accept
    API Gateway->>FTS: (forward request, with Bob's userId)
    FTS->>DB: 3. Start Transaction
    FTS->>DB: 4. Validate invitation and user permissions
    FTS->>DB: 5. Create Relationship record (source: Bob, target: Alice, type: 'PARENT_OF')
    FTS->>DB: 6. Update Invitation status to 'ACCEPTED'
    FTS->>DB: 7. Commit Transaction
    FTS->>FTS: 8. Publish FamilyRelationshipCreatedEvent
    FTS-->>API Gateway: 200 OK
    API Gateway-->>Bob: 200 OK
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 成员与关系管理
*   **FR4.1.1 (成员注册)**: 当用户首次发起或接受邀请时，系统必须在`family_members`表中为其创建一个节点记录，并通过API从`user-core-service`拉取其`username`, `displayName`等冗余信息。
*   **FR4.1.2 (关系邀请)**:
    *   系统必须提供API，允许用户A向用户B（通过`username`指定）发起建立特定关系的邀请。
    *   邀请关系类型必须是明确的，如“邀请B成为我的父亲/母亲/配偶”。
    *   系统必须防止向自己、或向已有直接关系的用户发送重复邀请。
*   **FR4.1.3 (邀请处理)**:
    *   被邀请者可以接受或拒绝邀请。
    *   接受邀请后，系统必须原子性地在`family_relationships`表中创建一条或多条（如配偶关系）对应的关系记录，并更新邀请状态。
*   **FR4.1.4 (关系解除)**:
    *   用户可以单方面解除一段关系（如离婚、或更正错误关系）。
    *   解除关系后，系统必须删除对应的关系记录，并发布`FamilyRelationshipDeletedEvent`。

#### 4.2. 族谱图谱查询
*   **FR4.2.1 (全图谱生成)**: 系统必须提供API，能以一个用户ID为中心，通过图遍历算法，生成其向上（父母、祖父母）、向下（子女、孙子女）、平级（配偶、兄弟姐妹）的完整或指定深度的族谱图数据。
*   **FR4.2.2 (关系路径查询)**: 系统必须提供API，查询任意两个用户之间的最短亲缘关系路径（如 A -> B 的关系是：A的母亲的兄弟）。

#### 4.3. 逻辑校验与约束
*   **FR4.3.1 (防悖论)**: 在创建关系时，系统必须执行图的**环路检测**，防止出现逻辑上不可能的关系（如A是B的后代，又想成为B的祖先）。
*   **FR4.3.2 (生物学/社会学约束)**:
    *   一个成员最多只能有两个`PARENT_OF`关系指向他/她。
    *   在建立`PARENT_OF`关系时，可以校验年龄差（需`user-core-service`提供出生日期）。
    *   对`SPOUSE_OF`关系，可以根据平台政策限制其唯一性（如一夫一妻）。

#### 4.4. 隐私控制
*   **FR4.4.1 (设置)**: 系统必须提供API，允许用户设置其族谱的可见性级别。
    *   `PRIVATE`: 仅自己可见。
    *   `FAMILY_MEMBERS_ONLY`: 仅已确认的家庭成员可见。
    *   `PUBLIC`: （不推荐，但可选）所有人可见。
*   **FR4.4.2 (强制执行)**: 所有族谱图谱和关系查询API，在返回数据前，都必须严格执行被查询用户的隐私设置。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/family-tree`
*   **认证**: 用户JWT。
*   **核心端点**:
    *   **Invitations**:
        *   `POST /invitations`: 发起关系邀请。Request: `{ "target_username": "...", "relationship_to_target": "CHILD" | "SPOUSE" }`
        *   `GET /invitations/me?status=PENDING`: 获取我收到的待处理邀请。
        *   `POST /invitations/{inviteId}/accept`: 接受邀请。
        *   `POST /invitations/{inviteId}/reject`: 拒绝邀请。
    *   **Relationships**:
        *   `DELETE /relationships/{relationshipId}`: 解除关系。
    *   **Tree/Graph**:
        *   `GET /users/{userId}/graph?depth=3`: 获取指定用户的族谱图数据。
        *   `GET /users/path?from_user_id={userIdA}&to_user_id={userIdB}`: 查询两个用户间的关系路径。
    *   **Privacy**:
        *   `GET /me/privacy-settings`: 获取我的隐私设置。
        *   `PUT /me/privacy-settings`: 更新我的隐私设置。Request: `{ "visibility": "FAMILY_MEMBERS_ONLY" }`

#### 5.2. 消息队列事件契约
*   **出站 (发布)**:
    *   `FamilyRelationshipCreatedEvent { eventId, users: [userIdA, userIdB], relationshipType }`
    *   `FamilyRelationshipDeletedEvent { eventId, users: [userIdA, userIdB], relationshipType }`
*   **入站 (消费)**:
    *   `UserProfileUpdatedEvent { userId, changedFields: {...} }`
    *   `UserAccountHardDeletedEvent { userId }`

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (Go Structs for GORM/sqlx)
```go
// FamilyMember is a node in the family graph, a proxy for a user.
type FamilyMember struct {
    UserID       uuid.UUID `gorm:"primary_key;type:uuid"`
    Username     string    `gorm:"type:varchar(20);not null;index"`
    DisplayName  string    `gorm:"type:varchar(50)"`
    AvatarURL    string    `gorm:"type:varchar(512)"`
    // other denormalized fields...
    UpdatedAt    time.Time
}

// FamilyRelationship is a directed edge between two members.
type FamilyRelationship struct {
    ID                int64     `gorm:"primary_key;auto_increment"`
    SourceUserID      uuid.UUID `gorm:"not null;index:idx_relationship_source"`
    TargetUserID      uuid.UUID `gorm:"not null;index:idx_relationship_target"`
    RelationshipType  string    `gorm:"type:varchar(50);not null"` // e.g., "PARENT_OF", "SPOUSE_OF"
    Status            string    `gorm:"type:varchar(20);not null;default:'ACTIVE'"`
    CreatedAt         time.Time
}

// RelationshipInvitation tracks the process of forming a relationship.
type RelationshipInvitation struct {
    ID                uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
    SourceUserID      uuid.UUID `gorm:"not null;index"`
    TargetUserID      uuid.UUID `gorm:"not null;index"`
    RelationshipType  string    `gorm:"type:varchar(50);not null"`
    Status            string    `gorm:"type:varchar(20);not null;index"` // "PENDING", "ACCEPTED", "REJECTED", "EXPIRED"
    ExpiresAt         time.Time
    CreatedAt         time.Time
}
```

#### 6.2. 数据持久化与存储
*   **数据库**: PostgreSQL。其强大的关系模型和递归查询能力（`WITH RECURSIVE`）非常适合处理树状/图状数据。
*   **备选方案 (超大规模)**: Neo4j 或其他原生图数据库，可以更自然、更高效地处理复杂的图遍历和路径查找。初期使用PostgreSQL完全足够。
*   **索引**: 对所有`user_id`外键、状态字段、邀请表中的`target_user_id`和`status`建立复合索引。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: 邀请、接受/拒绝等写操作 P99延迟应 `< 200ms`。
*   **图谱查询延迟**: 对于中等规模的家庭（如50人以内），查询P99延迟应 `< 500ms`。对于大规模图谱，需要优化查询或采用缓存。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **数据准确性**: 关系建立和删除必须是事务性的。逻辑校验必须健壮，防止数据污染。

#### 7.3. 可扩展性需求
*   服务应为无状态，可水平扩展。
*   数据库是主要扩展点。可通过读写分离、缓存、以及最终迁移到分布式图数据库来扩展。

#### 7.4. 安全性需求
*   **隐私第一**: 严格执行用户隐私设置，是本服务的最高安全要求。
*   **API安全**: 所有API必须经过严格的用户认证和授权。
*   **防滥用**: 对邀请操作进行速率限制。

#### 7.5. 可维护性需求
*   清晰的领域模型，将图算法、关系逻辑、API处理分层。
*   全面的单元测试和集成测试，特别是对图遍历和逻辑约束的测试。

#### 7.6. 可观测性需求
*   **日志**: 记录所有关系创建/删除、邀请生命周期、API请求、错误。
*   **指标**: API性能、图谱查询平均耗时、数据库查询性能、邀请接受/拒绝率。
*   **追踪**: 分布式追踪覆盖从API请求到数据库操作和事件发布的完整链路。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: 强烈推荐从 **PostgreSQL** 开始。它的`WITH RECURSIVE`查询对于构建树状结构非常强大。
*   **图算法库**: 使用或自研Go的图数据结构和算法库，用于在内存中进行环路检测和路径查找。
*   **事件驱动**: 通过消费`user-core-service`的事件来保持成员信息的最终一致性。

---

这份生产级的SRS文档为`family-tree-service`的开发提供了全面指导。它不仅定义了功能，更强调了在处理敏感家庭关系数据时，对**安全、隐私和逻辑严谨性**的极高要求。