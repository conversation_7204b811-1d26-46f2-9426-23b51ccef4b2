// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 16:30:00
// Modified: 2025-01-23 16:30:00

package api

import (
	"context"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// VersioningManager manages API versions and compatibility
type VersioningManager struct {
	versions            map[string]*APIVersionInfo
	compatibilityMatrix map[string][]string
	migrationPaths      map[string]*MigrationPath
	deprecationPolicies map[string]*DeprecationPolicy
	defaultVersion      string
	supportedVersions   []string
}

// APIVersionInfo contains detailed information about an API version
type APIVersionInfo struct {
	Version         string                 `json:"version"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	Status          VersionStatus          `json:"status"`
	ReleaseDate     time.Time              `json:"release_date"`
	DeprecationDate *time.Time             `json:"deprecation_date,omitempty"`
	SunsetDate      *time.Time             `json:"sunset_date,omitempty"`
	Compatibility   CompatibilityInfo      `json:"compatibility"`
	Features        []VersionFeature       `json:"features"`
	BreakingChanges []BreakingChange       `json:"breaking_changes"`
	Migration       *MigrationInfo         `json:"migration,omitempty"`
	Documentation   DocumentationLinks     `json:"documentation"`
	Metrics         *VersionMetrics        `json:"metrics,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// VersionStatus represents the status of an API version
type VersionStatus string

const (
	VersionStatusDevelopment VersionStatus = "development"
	VersionStatusBeta        VersionStatus = "beta"
	VersionStatusStable      VersionStatus = "stable"
	VersionStatusDeprecated  VersionStatus = "deprecated"
	VersionStatusSunset      VersionStatus = "sunset"
)

// CompatibilityInfo describes version compatibility
type CompatibilityInfo struct {
	BackwardCompatible  []string `json:"backward_compatible"`
	ForwardCompatible   []string `json:"forward_compatible"`
	BreakingChanges     []string `json:"breaking_changes"`
	RequiredMigration   bool     `json:"required_migration"`
	AutoMigration       bool     `json:"auto_migration"`
	MigrationComplexity string   `json:"migration_complexity"` // "low", "medium", "high"
	CompatibilityScore  float64  `json:"compatibility_score"`
}

// VersionFeature describes a feature in a version
type VersionFeature struct {
	Name         string  `json:"name"`
	Description  string  `json:"description"`
	Type         string  `json:"type"`   // "new", "enhanced", "deprecated", "removed"
	Impact       string  `json:"impact"` // "low", "medium", "high"
	AddedIn      string  `json:"added_in"`
	DeprecatedIn *string `json:"deprecated_in,omitempty"`
	RemovedIn    *string `json:"removed_in,omitempty"`
}

// BreakingChange describes a breaking change
type BreakingChange struct {
	ID                string                 `json:"id"`
	Title             string                 `json:"title"`
	Description       string                 `json:"description"`
	Type              string                 `json:"type"`     // "field_removed", "type_changed", "endpoint_removed", etc.
	Severity          string                 `json:"severity"` // "low", "medium", "high", "critical"
	AffectedEndpoints []string               `json:"affected_endpoints"`
	MitigationSteps   []string               `json:"mitigation_steps"`
	AutoMigratable    bool                   `json:"auto_migratable"`
	Workarounds       []string               `json:"workarounds"`
	Timeline          ChangeTimeline         `json:"timeline"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// ChangeTimeline describes the timeline for a change
type ChangeTimeline struct {
	AnnouncementDate time.Time     `json:"announcement_date"`
	DeprecationDate  time.Time     `json:"deprecation_date"`
	RemovalDate      time.Time     `json:"removal_date"`
	GracePeriod      time.Duration `json:"grace_period"`
}

// MigrationPath describes how to migrate between versions
type MigrationPath struct {
	FromVersion   string            `json:"from_version"`
	ToVersion     string            `json:"to_version"`
	Strategy      MigrationStrategy `json:"strategy"`
	Steps         []MigrationStep   `json:"steps"`
	EstimatedTime time.Duration     `json:"estimated_time"`
	Complexity    string            `json:"complexity"`
	Prerequisites []string          `json:"prerequisites"`
	Rollback      *RollbackPlan     `json:"rollback,omitempty"`
	Testing       *TestingPlan      `json:"testing,omitempty"`
	Documentation string            `json:"documentation"`
}

// MigrationStrategy defines the migration approach
type MigrationStrategy string

const (
	MigrationStrategyAutomatic     MigrationStrategy = "automatic"
	MigrationStrategyManual        MigrationStrategy = "manual"
	MigrationStrategyGradual       MigrationStrategy = "gradual"
	MigrationStrategyBigBang       MigrationStrategy = "big_bang"
	MigrationStrategyCanaryRelease MigrationStrategy = "canary_release"
)

// MigrationStep describes a single migration step
type MigrationStep struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Type         string                 `json:"type"` // "data", "schema", "code", "config"
	Order        int                    `json:"order"`
	Required     bool                   `json:"required"`
	Automated    bool                   `json:"automated"`
	Command      string                 `json:"command,omitempty"`
	Script       string                 `json:"script,omitempty"`
	Validation   string                 `json:"validation,omitempty"`
	Rollback     string                 `json:"rollback,omitempty"`
	Duration     time.Duration          `json:"duration"`
	Dependencies []string               `json:"dependencies"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// RollbackPlan describes how to rollback a migration
type RollbackPlan struct {
	Supported     bool                   `json:"supported"`
	Strategy      string                 `json:"strategy"`
	Steps         []MigrationStep        `json:"steps"`
	TimeLimit     time.Duration          `json:"time_limit"`
	DataLoss      bool                   `json:"data_loss"`
	Prerequisites []string               `json:"prerequisites"`
	Validation    string                 `json:"validation"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// TestingPlan describes the testing approach for migration
type TestingPlan struct {
	TestSuites  []string               `json:"test_suites"`
	TestData    string                 `json:"test_data"`
	Environment string                 `json:"environment"`
	Criteria    []string               `json:"criteria"`
	Duration    time.Duration          `json:"duration"`
	Automated   bool                   `json:"automated"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// DeprecationPolicy defines how versions are deprecated
type DeprecationPolicy struct {
	Version              string                 `json:"version"`
	MinSupportTime       time.Duration          `json:"min_support_time"`
	WarningPeriod        time.Duration          `json:"warning_period"`
	GracePeriod          time.Duration          `json:"grace_period"`
	NotificationChannels []string               `json:"notification_channels"`
	DeprecationReasons   []string               `json:"deprecation_reasons"`
	MigrationSupport     bool                   `json:"migration_support"`
	EmergencyOverride    bool                   `json:"emergency_override"`
	Metadata             map[string]interface{} `json:"metadata"`
}

// MigrationInfo provides migration guidance
type MigrationInfo struct {
	FromVersions    []string   `json:"from_versions"`
	MigrationGuide  string     `json:"migration_guide"`
	AutoMigration   bool       `json:"auto_migration"`
	ToolsAvailable  []string   `json:"tools_available"`
	EstimatedEffort string     `json:"estimated_effort"`
	SupportLevel    string     `json:"support_level"`
	Deadline        *time.Time `json:"deadline,omitempty"`
}

// DocumentationLinks provides links to version documentation
type DocumentationLinks struct {
	APIReference     string   `json:"api_reference"`
	MigrationGuide   string   `json:"migration_guide"`
	ChangeLog        string   `json:"change_log"`
	Examples         string   `json:"examples"`
	SDKDocumentation string   `json:"sdk_documentation"`
	TutorialLinks    []string `json:"tutorial_links"`
}

// VersionMetrics tracks version usage and performance
type VersionMetrics struct {
	RequestCount    int64            `json:"request_count"`
	ActiveClients   int64            `json:"active_clients"`
	ErrorRate       float64          `json:"error_rate"`
	AvgResponseTime time.Duration    `json:"avg_response_time"`
	LastUsed        time.Time        `json:"last_used"`
	TrendDirection  string           `json:"trend_direction"` // "increasing", "stable", "decreasing"
	UsageByEndpoint map[string]int64 `json:"usage_by_endpoint"`
	ClientVersions  map[string]int64 `json:"client_versions"`
}

// VersionRequest represents a version request from a client
type VersionRequest struct {
	RequestedVersion string             `json:"requested_version"`
	ClientInfo       *ClientVersionInfo `json:"client_info"`
	AcceptedVersions []string           `json:"accepted_versions"`
	Preferences      VersionPreferences `json:"preferences"`
}

// ClientVersionInfo contains client version information
type ClientVersionInfo struct {
	ClientID      string `json:"client_id"`
	ClientVersion string `json:"client_version"`
	SDKVersion    string `json:"sdk_version,omitempty"`
	Platform      string `json:"platform"`
	UserAgent     string `json:"user_agent"`
}

// VersionPreferences defines client version preferences
type VersionPreferences struct {
	PreferStable     bool     `json:"prefer_stable"`
	AllowBeta        bool     `json:"allow_beta"`
	MaxVersion       string   `json:"max_version,omitempty"`
	MinVersion       string   `json:"min_version,omitempty"`
	ExcludeVersions  []string `json:"exclude_versions"`
	RequiredFeatures []string `json:"required_features"`
}

// VersionNegotiationResult represents the result of version negotiation
type VersionNegotiationResult struct {
	SelectedVersion   string                 `json:"selected_version"`
	Reason            string                 `json:"reason"`
	Warnings          []string               `json:"warnings,omitempty"`
	MigrationAdvice   *MigrationAdvice       `json:"migration_advice,omitempty"`
	DeprecationNotice *DeprecationNotice     `json:"deprecation_notice,omitempty"`
	SupportedFeatures []string               `json:"supported_features"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// MigrationAdvice provides migration recommendations
type MigrationAdvice struct {
	RecommendedVersion string     `json:"recommended_version"`
	MigrationPath      string     `json:"migration_path"`
	EstimatedEffort    string     `json:"estimated_effort"`
	Benefits           []string   `json:"benefits"`
	Deadline           *time.Time `json:"deadline,omitempty"`
	Resources          []string   `json:"resources"`
}

// DeprecationNotice provides deprecation information
type DeprecationNotice struct {
	DeprecatedVersion  string     `json:"deprecated_version"`
	DeprecationDate    time.Time  `json:"deprecation_date"`
	SunsetDate         *time.Time `json:"sunset_date,omitempty"`
	Reason             string     `json:"reason"`
	ReplacementVersion string     `json:"replacement_version"`
	MigrationRequired  bool       `json:"migration_required"`
	SupportLevel       string     `json:"support_level"`
}

// NewVersioningManager creates a new versioning manager
func NewVersioningManager() *VersioningManager {
	return &VersioningManager{
		versions:            make(map[string]*APIVersionInfo),
		compatibilityMatrix: make(map[string][]string),
		migrationPaths:      make(map[string]*MigrationPath),
		deprecationPolicies: make(map[string]*DeprecationPolicy),
		supportedVersions:   []string{},
	}
}

// RegisterVersion registers a new API version
func (vm *VersioningManager) RegisterVersion(version *APIVersionInfo) error {
	if version.Version == "" {
		return fmt.Errorf("version string cannot be empty")
	}

	vm.versions[version.Version] = version

	// Update supported versions list
	vm.updateSupportedVersions()

	return nil
}

// RegisterMigrationPath registers a migration path between versions
func (vm *VersioningManager) RegisterMigrationPath(path *MigrationPath) error {
	key := fmt.Sprintf("%s->%s", path.FromVersion, path.ToVersion)
	vm.migrationPaths[key] = path
	return nil
}

// RegisterDeprecationPolicy registers a deprecation policy for a version
func (vm *VersioningManager) RegisterDeprecationPolicy(policy *DeprecationPolicy) error {
	vm.deprecationPolicies[policy.Version] = policy
	return nil
}

// SetDefaultVersion sets the default version
func (vm *VersioningManager) SetDefaultVersion(version string) error {
	if _, exists := vm.versions[version]; !exists {
		return fmt.Errorf("version %s not found", version)
	}

	vm.defaultVersion = version
	return nil
}

// NegotiateVersion negotiates the best version based on client request
func (vm *VersioningManager) NegotiateVersion(request VersionRequest) *VersionNegotiationResult {
	result := &VersionNegotiationResult{
		Metadata: make(map[string]interface{}),
	}

	// If specific version requested, try to use it
	if request.RequestedVersion != "" {
		if vm.isVersionSupported(request.RequestedVersion) {
			versionInfo := vm.versions[request.RequestedVersion]

			result.SelectedVersion = request.RequestedVersion
			result.Reason = "requested_version_available"

			// Check for deprecation
			if versionInfo.Status == VersionStatusDeprecated {
				result.DeprecationNotice = vm.createDeprecationNotice(versionInfo)
				result.Warnings = append(result.Warnings, "Requested version is deprecated")
			}

			// Provide migration advice if newer stable version available
			if newerVersion := vm.findNewerStableVersion(request.RequestedVersion); newerVersion != "" {
				result.MigrationAdvice = vm.createMigrationAdvice(request.RequestedVersion, newerVersion)
			}

			return result
		} else {
			result.Warnings = append(result.Warnings, fmt.Sprintf("Requested version %s is not supported", request.RequestedVersion))
		}
	}

	// Try to find best compatible version from accepted versions
	if len(request.AcceptedVersions) > 0 {
		for _, version := range request.AcceptedVersions {
			if vm.isVersionSupported(version) {
				if vm.meetsPreferences(version, request.Preferences) {
					result.SelectedVersion = version
					result.Reason = "best_compatible_version"
					return result
				}
			}
		}
	}

	// Find best version based on preferences
	bestVersion := vm.findBestVersion(request.Preferences)
	if bestVersion != "" {
		result.SelectedVersion = bestVersion
		result.Reason = "best_available_version"
		return result
	}

	// Fall back to default version
	result.SelectedVersion = vm.defaultVersion
	result.Reason = "default_version_fallback"

	return result
}

// GetVersionInfo returns information about a specific version
func (vm *VersioningManager) GetVersionInfo(version string) (*APIVersionInfo, error) {
	info, exists := vm.versions[version]
	if !exists {
		return nil, fmt.Errorf("version %s not found", version)
	}

	return info, nil
}

// GetSupportedVersions returns all supported versions
func (vm *VersioningManager) GetSupportedVersions() []string {
	return vm.supportedVersions
}

// GetMigrationPath returns the migration path between two versions
func (vm *VersioningManager) GetMigrationPath(fromVersion, toVersion string) (*MigrationPath, error) {
	key := fmt.Sprintf("%s->%s", fromVersion, toVersion)
	path, exists := vm.migrationPaths[key]
	if !exists {
		return nil, fmt.Errorf("migration path from %s to %s not found", fromVersion, toVersion)
	}

	return path, nil
}

// CheckCompatibility checks if two versions are compatible
func (vm *VersioningManager) CheckCompatibility(version1, version2 string) (bool, *CompatibilityInfo) {
	info1, exists1 := vm.versions[version1]
	info2, exists2 := vm.versions[version2]

	if !exists1 || !exists2 {
		return false, nil
	}

	// Check backward compatibility
	for _, compatible := range info2.Compatibility.BackwardCompatible {
		if compatible == version1 {
			return true, &info2.Compatibility
		}
	}

	// Check forward compatibility
	for _, compatible := range info1.Compatibility.ForwardCompatible {
		if compatible == version2 {
			return true, &info1.Compatibility
		}
	}

	return false, nil
}

// DeprecateVersion marks a version as deprecated
func (vm *VersioningManager) DeprecateVersion(version string, reason string) error {
	info, exists := vm.versions[version]
	if !exists {
		return fmt.Errorf("version %s not found", version)
	}

	now := time.Now()
	info.Status = VersionStatusDeprecated
	info.DeprecationDate = &now

	// Set sunset date based on deprecation policy
	if policy, exists := vm.deprecationPolicies[version]; exists {
		sunsetDate := now.Add(policy.GracePeriod)
		info.SunsetDate = &sunsetDate
	}

	vm.updateSupportedVersions()

	return nil
}

// SunsetVersion marks a version as sunset (no longer supported)
func (vm *VersioningManager) SunsetVersion(version string) error {
	info, exists := vm.versions[version]
	if !exists {
		return fmt.Errorf("version %s not found", version)
	}

	now := time.Now()
	info.Status = VersionStatusSunset
	info.SunsetDate = &now

	vm.updateSupportedVersions()

	return nil
}

// GetVersionFromRequest extracts version from HTTP request
func (vm *VersioningManager) GetVersionFromRequest(r *http.Request) string {
	// Check Accept header
	if acceptHeader := r.Header.Get("Accept"); acceptHeader != "" {
		if version := vm.parseVersionFromAcceptHeader(acceptHeader); version != "" {
			return version
		}
	}

	// Check custom version header
	if version := r.Header.Get("X-API-Version"); version != "" {
		return version
	}

	// Check query parameter
	if version := r.URL.Query().Get("version"); version != "" {
		return version
	}

	// Check URL path
	if version := vm.parseVersionFromPath(r.URL.Path); version != "" {
		return version
	}

	return vm.defaultVersion
}

// Helper methods
func (vm *VersioningManager) updateSupportedVersions() {
	var supported []string

	for version, info := range vm.versions {
		if info.Status == VersionStatusStable ||
			info.Status == VersionStatusBeta ||
			info.Status == VersionStatusDeprecated {
			supported = append(supported, version)
		}
	}

	// Sort versions in descending order
	sort.Slice(supported, func(i, j int) bool {
		return vm.compareVersions(supported[i], supported[j]) > 0
	})

	vm.supportedVersions = supported
}

func (vm *VersioningManager) isVersionSupported(version string) bool {
	info, exists := vm.versions[version]
	if !exists {
		return false
	}

	return info.Status != VersionStatusSunset
}

func (vm *VersioningManager) meetsPreferences(version string, prefs VersionPreferences) bool {
	info, exists := vm.versions[version]
	if !exists {
		return false
	}

	// Check stability preference
	if prefs.PreferStable && info.Status != VersionStatusStable {
		return false
	}

	// Check beta allowance
	if !prefs.AllowBeta && info.Status == VersionStatusBeta {
		return false
	}

	// Check version constraints
	if prefs.MaxVersion != "" && vm.compareVersions(version, prefs.MaxVersion) > 0 {
		return false
	}

	if prefs.MinVersion != "" && vm.compareVersions(version, prefs.MinVersion) < 0 {
		return false
	}

	// Check exclusions
	for _, excluded := range prefs.ExcludeVersions {
		if version == excluded {
			return false
		}
	}

	// Check required features
	for _, requiredFeature := range prefs.RequiredFeatures {
		if !vm.hasFeature(version, requiredFeature) {
			return false
		}
	}

	return true
}

func (vm *VersioningManager) findBestVersion(prefs VersionPreferences) string {
	var candidates []string

	for _, version := range vm.supportedVersions {
		if vm.meetsPreferences(version, prefs) {
			candidates = append(candidates, version)
		}
	}

	if len(candidates) == 0 {
		return ""
	}

	// Return the highest version that meets preferences
	sort.Slice(candidates, func(i, j int) bool {
		return vm.compareVersions(candidates[i], candidates[j]) > 0
	})

	return candidates[0]
}

func (vm *VersioningManager) findNewerStableVersion(currentVersion string) string {
	for _, version := range vm.supportedVersions {
		info := vm.versions[version]
		if info.Status == VersionStatusStable &&
			vm.compareVersions(version, currentVersion) > 0 {
			return version
		}
	}

	return ""
}

func (vm *VersioningManager) compareVersions(v1, v2 string) int {
	// Simple semantic version comparison
	// In practice, you'd use a proper semver library
	parts1 := strings.Split(strings.TrimPrefix(v1, "v"), ".")
	parts2 := strings.Split(strings.TrimPrefix(v2, "v"), ".")

	maxLen := len(parts1)
	if len(parts2) > maxLen {
		maxLen = len(parts2)
	}

	for i := 0; i < maxLen; i++ {
		var n1, n2 int

		if i < len(parts1) {
			n1, _ = strconv.Atoi(parts1[i])
		}

		if i < len(parts2) {
			n2, _ = strconv.Atoi(parts2[i])
		}

		if n1 < n2 {
			return -1
		} else if n1 > n2 {
			return 1
		}
	}

	return 0
}

func (vm *VersioningManager) hasFeature(version, feature string) bool {
	info, exists := vm.versions[version]
	if !exists {
		return false
	}

	for _, f := range info.Features {
		if f.Name == feature && f.Type != "removed" {
			return true
		}
	}

	return false
}

func (vm *VersioningManager) parseVersionFromAcceptHeader(acceptHeader string) string {
	// Parse Accept header for version information
	// Example: application/vnd.api+json;version=2
	parts := strings.Split(acceptHeader, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "version=") {
			return strings.TrimPrefix(part, "version=")
		}
	}

	return ""
}

func (vm *VersioningManager) parseVersionFromPath(path string) string {
	// Parse version from URL path
	// Example: /api/v2/users -> v2
	parts := strings.Split(path, "/")
	for _, part := range parts {
		if strings.HasPrefix(part, "v") && len(part) > 1 {
			if _, err := strconv.Atoi(part[1:]); err == nil {
				return part
			}
		}
	}

	return ""
}

func (vm *VersioningManager) createDeprecationNotice(info *APIVersionInfo) *DeprecationNotice {
	notice := &DeprecationNotice{
		DeprecatedVersion: info.Version,
		SupportLevel:      "limited",
	}

	if info.DeprecationDate != nil {
		notice.DeprecationDate = *info.DeprecationDate
	}

	if info.SunsetDate != nil {
		notice.SunsetDate = info.SunsetDate
	}

	// Find replacement version
	for _, version := range vm.supportedVersions {
		versionInfo := vm.versions[version]
		if versionInfo.Status == VersionStatusStable &&
			vm.compareVersions(version, info.Version) > 0 {
			notice.ReplacementVersion = version
			break
		}
	}

	return notice
}

func (vm *VersioningManager) createMigrationAdvice(fromVersion, toVersion string) *MigrationAdvice {
	advice := &MigrationAdvice{
		RecommendedVersion: toVersion,
		EstimatedEffort:    "medium",
		Benefits:           []string{"improved performance", "new features", "better security"},
	}

	// Check if migration path exists
	key := fmt.Sprintf("%s->%s", fromVersion, toVersion)
	if path, exists := vm.migrationPaths[key]; exists {
		advice.MigrationPath = key
		advice.EstimatedEffort = path.Complexity
		advice.Resources = []string{path.Documentation}
	}

	return advice
}

// VersionMiddleware provides HTTP middleware for version handling
func (vm *VersioningManager) VersionMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			version := vm.GetVersionFromRequest(r)

			// Validate version
			if !vm.isVersionSupported(version) {
				http.Error(w, fmt.Sprintf("API version %s is not supported", version), http.StatusBadRequest)
				return
			}

			// Add version to response headers
			w.Header().Set("X-API-Version", version)

			// Add deprecation warnings if needed
			if info, exists := vm.versions[version]; exists && info.Status == VersionStatusDeprecated {
				w.Header().Set("Warning", fmt.Sprintf("299 - \"API version %s is deprecated\"", version))

				if info.SunsetDate != nil {
					w.Header().Set("Sunset", info.SunsetDate.Format(time.RFC1123))
				}
			}

			// Add version to request context
			ctx := r.Context()
			ctx = context.WithValue(ctx, "api_version", version)
			r = r.WithContext(ctx)

			next.ServeHTTP(w, r)
		})
	}
}

// GetVersionStatus returns a summary of all version statuses
func (vm *VersioningManager) GetVersionStatus() map[string]interface{} {
	status := make(map[string]interface{})

	versionSummary := make(map[string]int)
	for _, info := range vm.versions {
		versionSummary[string(info.Status)]++
	}

	status["version_summary"] = versionSummary
	status["supported_versions"] = vm.supportedVersions
	status["default_version"] = vm.defaultVersion
	status["total_versions"] = len(vm.versions)

	return status
}

// Global versioning manager instance
var DefaultVersioningManager = NewVersioningManager()
