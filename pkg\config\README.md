# pkg/config

A standardized configuration loading and validation system for CINA.CLUB backend services.

## Overview

The `pkg/config` package provides a robust, flexible, and type-safe configuration management system for all CINA.CLUB microservices. It implements a layered configuration strategy with support for files, environment variables, and defaults, backed by [<PERSON>](https://github.com/spf13/viper) and [go-playground/validator](https://github.com/go-playground/validator) for reliable configuration management.

## Features

- **Layered Configuration**: Supports configuration from multiple sources with clear precedence
- **Type Safety**: Strong typing through Go structs with compile-time validation
- **Environment Variable Overrides**: Automatic binding of environment variables with `CINA_` prefix
- **Validation**: Built-in and custom validation rules using struct tags
- **Fail-Fast**: Early validation during service startup to catch configuration errors
- **Default Values**: Support for default values through struct tags
- **Custom Validation**: Extensible validation system for domain-specific rules

## Configuration Precedence

Configuration values are resolved in the following order (highest to lowest priority):

1. **Environment Variables** (highest priority)
2. **Configuration Files** (YAML format)
3. **Default Values** (from struct tags)

## Quick Start

### 1. Define Your Configuration Structure

```go
type AppConfig struct {
    Server   ServerConfig   `mapstructure:"server" validate:"required"`
    Database DatabaseConfig `mapstructure:"database" validate:"required"`
    Logger   LoggerConfig   `mapstructure:"logger"`
}

type ServerConfig struct {
    Port int    `mapstructure:"port" validate:"gte=1024,lte=65535" default:"8080"`
    Host string `mapstructure:"host" default:"localhost"`
}

type DatabaseConfig struct {
    DSN      string `mapstructure:"dsn" validate:"required,dsn"`
    MaxConns int    `mapstructure:"max_conns" validate:"gte=1" default:"10"`
}

type LoggerConfig struct {
    Level string `mapstructure:"level" validate:"loglevel" default:"info"`
}
```

### 2. Create Configuration File (config.yaml)

```yaml
server:
  port: 8080
  host: "localhost"

database:
  dsn: "postgres://user:pass@localhost:5432/mydb"
  max_conns: 25

logger:
  level: "info"
```

### 3. Load Configuration

```go
package main

import (
    "log"
    "pkg/config"
)

func main() {
    var cfg AppConfig
    
    // Load configuration from file with environment variable overrides
    if err := config.LoadConfig("./config.yaml", &cfg); err != nil {
        log.Fatalf("Failed to load configuration: %v", err)
    }
    
    // Use your configuration
    fmt.Printf("Starting server on %s:%d\n", cfg.Server.Host, cfg.Server.Port)
}
```

### 4. Override with Environment Variables

```bash
# Environment variables automatically override file values
export CINA_SERVER_PORT=9090
export CINA_DATABASE_MAX_CONNS=50
export CINA_LOGGER_LEVEL=debug
```

## API Reference

### Core Functions

#### `LoadConfig(configPath string, configStruct interface{}) error`

Loads configuration from file and environment variables into the provided struct.

```go
var cfg MyConfig
err := config.LoadConfig("./config.yaml", &cfg)
if err != nil {
    log.Fatalf("Config load failed: %v", err)
}
```

#### `MustLoadConfig(configPath string, configStruct interface{})`

Like `LoadConfig` but panics on failure. Useful for services that cannot continue without valid configuration.

```go
var cfg MyConfig
config.MustLoadConfig("./config.yaml", &cfg)
```

### Advanced Usage

#### `NewLoader() *Loader`

Creates a new configuration loader for advanced usage patterns.

```go
loader := config.NewLoader()
var cfg MyConfig
err := loader.Load("./config.yaml", &cfg)
```

#### `LoadWithInfo(configPath string, configStruct interface{}) (*ConfigInfo, error)`

Loads configuration and returns detailed information about the loading process.

```go
loader := config.NewLoader()
var cfg MyConfig
info, err := loader.LoadWithInfo("./config.yaml", &cfg)
if err != nil {
    log.Fatalf("Config load failed: %v", err)
}

fmt.Printf("Config file: %s\n", info.ConfigFile)
fmt.Printf("Environment variables found: %v\n", info.EnvVarsFound)
```

### Validation

#### `ValidateStruct(s interface{}) error`

Validates a struct using the configured validator.

```go
err := config.ValidateStruct(myConfigStruct)
if err != nil {
    log.Printf("Validation failed: %v", err)
}
```

#### `AddCustomValidation(tag string, fn validator.Func) error`

Registers a custom validation rule.

```go
err := config.AddCustomValidation("apikey", func(fl validator.FieldLevel) bool {
    value := fl.Field().String()
    return len(value) >= 32 // API key must be at least 32 characters
})
```

## Environment Variable Mapping

Environment variables are automatically mapped to configuration fields using the following rules:

- **Prefix**: All environment variables must start with `CINA_`
- **Nesting**: Nested structs are separated by underscores (`_`)
- **Case**: All letters must be uppercase

### Examples

| Go Struct Field | YAML Key | Environment Variable |
|----------------|----------|---------------------|
| `Server.Port` | `server.port` | `CINA_SERVER_PORT` |
| `Database.Postgres.DSN` | `database.postgres.dsn` | `CINA_DATABASE_POSTGRES_DSN` |
| `Logger.Level` | `logger.level` | `CINA_LOGGER_LEVEL` |

## Struct Tags

### mapstructure

Maps Go struct fields to configuration keys:

```go
type Config struct {
    Port int `mapstructure:"port"`
}
```

### validate

Defines validation rules:

```go
type Config struct {
    Port int `mapstructure:"port" validate:"required,gte=1024,lte=65535"`
    Mode string `mapstructure:"mode" validate:"oneof=dev staging prod"`
}
```

### default

Sets default values (applied if no value is provided):

```go
type Config struct {
    Port int    `mapstructure:"port" default:"8080"`
    Host string `mapstructure:"host" default:"localhost"`
}
```

## Built-in Validation Rules

The package includes several custom validation rules for common CINA.CLUB patterns:

### `dsn`
Validates database connection strings:
```go
DSN string `validate:"required,dsn"`
```

### `servicemode`
Validates service running modes:
```go
Mode string `validate:"servicemode"` // development, staging, production
```

### `loglevel`
Validates log levels:
```go
Level string `validate:"loglevel"` // trace, debug, info, warn, error, fatal
```

### `cinaurl`
Validates CINA service URLs:
```go
URL string `validate:"cinaurl"`
```

## Error Handling

The package provides detailed error messages for configuration issues:

```go
err := config.LoadConfig("./config.yaml", &cfg)
if err != nil {
    // Error messages include:
    // - Which file failed to load
    // - Which validation rule failed
    // - Which field has invalid value
    log.Printf("Configuration error: %v", err)
}
```

Example error output:
```
configuration validation failed:
  - field 'Port' must be greater than or equal to 1024 (got: 80)
  - field 'Mode' must be one of: development, staging, production (got: invalid)
  - field 'DSN' must be a valid database connection string
```

## Best Practices

### 1. Service Configuration Structure

Organize your configuration into logical groups:

```go
type ServiceConfig struct {
    Service  ServiceInfo    `mapstructure:"service" validate:"required"`
    Server   ServerConfig   `mapstructure:"server" validate:"required"`
    Database DatabaseConfig `mapstructure:"database" validate:"required"`
    External ExternalConfig `mapstructure:"external"`
    Features FeatureConfig  `mapstructure:"features"`
}
```

### 2. Use Validation Tags

Always validate critical configuration:

```go
type ServerConfig struct {
    Port int    `mapstructure:"port" validate:"required,gte=1024,lte=65535"`
    Host string `mapstructure:"host" validate:"required"`
    Mode string `mapstructure:"mode" validate:"required,servicemode"`
}
```

### 3. Provide Sensible Defaults

Use default tags for non-critical settings:

```go
type LoggerConfig struct {
    Level  string `mapstructure:"level" validate:"loglevel" default:"info"`
    Format string `mapstructure:"format" validate:"oneof=json text" default:"json"`
}
```

### 4. Environment-Specific Files

Use different configuration files for different environments:

```
configs/
├── config.dev.yaml
├── config.staging.yaml
└── config.prod.yaml
```

### 5. Secure Sensitive Data

Never commit sensitive data to configuration files. Use environment variables:

```yaml
# config.yaml - safe to commit
database:
  host: "localhost"
  port: 5432
  name: "mydb"
  # DSN with credentials should come from environment variable
```

```bash
# Environment variable with sensitive data
export CINA_DATABASE_DSN="postgres://user:password@localhost:5432/mydb"
```

## Testing

Test your configuration loading:

```go
func TestConfigLoading(t *testing.T) {
    // Create test config file
    configContent := `
server:
  port: 8080
database:
  dsn: "postgres://test@localhost/testdb"
`
    
    tmpFile := createTempConfig(t, configContent)
    defer os.Remove(tmpFile)
    
    var cfg MyConfig
    err := config.LoadConfig(tmpFile, &cfg)
    assert.NoError(t, err)
    assert.Equal(t, 8080, cfg.Server.Port)
}
```

## Integration Examples

### Basic Service

```go
// cmd/server/main.go
package main

import (
    "flag"
    "log"
    "pkg/config"
)

func main() {
    configPath := flag.String("config", "./config.yaml", "path to config file")
    flag.Parse()
    
    var cfg ServiceConfig
    config.MustLoadConfig(*configPath, &cfg)
    
    // Initialize components with config
    server := NewServer(cfg.Server)
    db := NewDatabase(cfg.Database)
    
    // Start service
    log.Printf("Starting %s v%s", cfg.Service.Name, cfg.Service.Version)
    server.Start()
}
```

### With Graceful Shutdown

```go
func main() {
    var cfg ServiceConfig
    config.MustLoadConfig("./config.yaml", &cfg)
    
    server := NewServer(cfg.Server)
    
    // Handle shutdown signals
    ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt)
    defer cancel()
    
    go func() {
        if err := server.Start(); err != nil {
            log.Printf("Server error: %v", err)
            cancel()
        }
    }()
    
    <-ctx.Done()
    log.Println("Shutting down...")
    server.Shutdown(context.Background())
}
```

## Performance

The configuration loading is optimized for startup time:

- **Target**: Configuration loading completes within 100ms
- **Memory**: Minimal allocations during loading process
- **Validation**: Fast struct validation using pre-compiled rules

## Security Considerations

1. **Never commit secrets** to configuration files
2. **Use environment variables** for sensitive data
3. **Validate all inputs** using struct tags
4. **Fail fast** on invalid configuration
5. **Log configuration errors** but don't expose sensitive values

## Troubleshooting

### Common Issues

#### Configuration Not Loading
```bash
# Check file permissions
ls -la config.yaml

# Check file format
yamllint config.yaml
```

#### Environment Variables Not Working
```bash
# Verify environment variable names
env | grep CINA_

# Check variable casing (must be uppercase)
export CINA_SERVER_PORT=8080  # ✓ Correct
export cina_server_port=8080  # ✗ Wrong
```

#### Validation Errors
```go
// Enable detailed validation output
err := config.LoadConfig("./config.yaml", &cfg)
if err != nil {
    fmt.Printf("Full error: %+v\n", err)
}
```

## Contributing

When adding new validation rules or features:

1. Add comprehensive tests
2. Update documentation with examples
3. Follow existing code patterns
4. Ensure backward compatibility

## License

Copyright (c) 2025 Cina.Club, All rights reserved. 