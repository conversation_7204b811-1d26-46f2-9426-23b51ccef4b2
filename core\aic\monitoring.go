/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-01 10:40:00
Modified: 2025-01-01 10:40:00
*/

package aic

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// LogLevel defines the severity levels for logging
type LogLevel string

const (
	LogLevelDebug LogLevel = "DEBUG"
	LogLevelInfo  LogLevel = "INFO"
	LogLevelWarn  LogLevel = "WARN"
	LogLevelError LogLevel = "ERROR"
	LogLevelFatal LogLevel = "FATAL"
)

// LogEntry represents a structured log entry
type LogEntry struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     LogLevel               `json:"level"`
	Component string                 `json:"component"`
	Operation string                 `json:"operation"`
	Message   string                 `json:"message"`
	ModelID   string                 `json:"model_id,omitempty"`
	RequestID string                 `json:"request_id,omitempty"`
	UserID    string                 `json:"user_id,omitempty"`
	Duration  time.Duration          `json:"duration,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Tags      []string               `json:"tags,omitempty"`
	TraceID   string                 `json:"trace_id,omitempty"`
	SpanID    string                 `json:"span_id,omitempty"`
}

// MetricType defines different types of metrics
type MetricType string

const (
	MetricTypeCounter   MetricType = "COUNTER"
	MetricTypeGauge     MetricType = "GAUGE"
	MetricTypeHistogram MetricType = "HISTOGRAM"
	MetricTypeSummary   MetricType = "SUMMARY"
)

// Metric represents a performance or operational metric
type Metric struct {
	Name        string                 `json:"name"`
	Type        MetricType             `json:"type"`
	Value       float64                `json:"value"`
	Timestamp   time.Time              `json:"timestamp"`
	Labels      map[string]string      `json:"labels"`
	Description string                 `json:"description"`
	Unit        string                 `json:"unit"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Alert represents a monitoring alert
type Alert struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Severity    AlertSeverity          `json:"severity"`
	Status      AlertStatus            `json:"status"`
	Description string                 `json:"description"`
	Condition   string                 `json:"condition"`
	Threshold   float64                `json:"threshold"`
	ActualValue float64                `json:"actual_value"`
	ModelID     string                 `json:"model_id,omitempty"`
	Component   string                 `json:"component"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Actions     []AlertAction          `json:"actions,omitempty"`
}

// AlertSeverity defines alert severity levels
type AlertSeverity string

const (
	AlertSeverityLow      AlertSeverity = "LOW"
	AlertSeverityMedium   AlertSeverity = "MEDIUM"
	AlertSeverityHigh     AlertSeverity = "HIGH"
	AlertSeverityCritical AlertSeverity = "CRITICAL"
)

// AlertStatus defines alert status
type AlertStatus string

const (
	AlertStatusActive     AlertStatus = "ACTIVE"
	AlertStatusResolved   AlertStatus = "RESOLVED"
	AlertStatusSuppressed AlertStatus = "SUPPRESSED"
)

// AlertAction defines actions to take when an alert fires
type AlertAction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target"`
	Parameters map[string]interface{} `json:"parameters"`
	Enabled    bool                   `json:"enabled"`
}

// Dashboard represents a monitoring dashboard
type Dashboard struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Widgets     []Widget               `json:"widgets"`
	Layout      DashboardLayout        `json:"layout"`
	Filters     map[string]interface{} `json:"filters"`
	RefreshRate time.Duration          `json:"refresh_rate"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	CreatedBy   string                 `json:"created_by"`
	IsPublic    bool                   `json:"is_public"`
	Tags        []string               `json:"tags"`
}

// Widget represents a dashboard widget
type Widget struct {
	ID          string                 `json:"id"`
	Type        WidgetType             `json:"type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Query       string                 `json:"query"`
	TimeRange   TimeRange              `json:"time_range"`
	Config      map[string]interface{} `json:"config"`
	Position    WidgetPosition         `json:"position"`
	Size        WidgetSize             `json:"size"`
}

// WidgetType defines different types of dashboard widgets
type WidgetType string

const (
	WidgetTypeLineChart WidgetType = "LINE_CHART"
	WidgetTypeBarChart  WidgetType = "BAR_CHART"
	WidgetTypePieChart  WidgetType = "PIE_CHART"
	WidgetTypeTable     WidgetType = "TABLE"
	WidgetTypeMetric    WidgetType = "METRIC"
	WidgetTypeHeatmap   WidgetType = "HEATMAP"
	WidgetTypeGauge     WidgetType = "GAUGE"
	WidgetTypeAlertList WidgetType = "ALERT_LIST"
	WidgetTypeLogStream WidgetType = "LOG_STREAM"
)

// DashboardLayout defines the layout configuration
type DashboardLayout struct {
	Type    string `json:"type"`
	Columns int    `json:"columns"`
	Rows    int    `json:"rows"`
}

// WidgetPosition defines widget positioning
type WidgetPosition struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// WidgetSize defines widget dimensions
type WidgetSize struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// AnomalyDetector identifies unusual patterns in AI system behavior
type AnomalyDetector interface {
	// Detection
	DetectAnomalies(ctx context.Context, metrics []Metric) ([]Anomaly, error)
	TrainModel(ctx context.Context, historicalData []Metric) error
	UpdateModel(ctx context.Context, newData []Metric) error

	// Configuration
	SetSensitivity(level float64) error
	GetSensitivity() float64
	SetThresholds(thresholds map[string]float64) error
	GetThresholds() map[string]float64

	// Status
	GetModelStatus() AnomalyModelStatus
	GetDetectionStats() AnomalyDetectionStats
}

// Anomaly represents a detected anomaly
type Anomaly struct {
	ID          string                 `json:"id"`
	Type        AnomalyType            `json:"type"`
	Severity    AnomalySeverity        `json:"severity"`
	Description string                 `json:"description"`
	MetricName  string                 `json:"metric_name"`
	Expected    float64                `json:"expected_value"`
	Actual      float64                `json:"actual_value"`
	Deviation   float64                `json:"deviation"`
	Confidence  float64                `json:"confidence"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	ModelID     string                 `json:"model_id,omitempty"`
	Component   string                 `json:"component"`
	Context     map[string]interface{} `json:"context"`
	Suggestions []string               `json:"suggestions"`
}

// AnomalyType defines different types of anomalies
type AnomalyType string

const (
	AnomalyTypeSpike       AnomalyType = "SPIKE"
	AnomalyTypeDrop        AnomalyType = "DROP"
	AnomalyTypeTrend       AnomalyType = "TREND"
	AnomalyTypePattern     AnomalyType = "PATTERN"
	AnomalyTypeOutlier     AnomalyType = "OUTLIER"
	AnomalyTypeSeasonality AnomalyType = "SEASONALITY"
)

// AnomalySeverity defines anomaly severity levels
type AnomalySeverity string

const (
	AnomalySeverityLow      AnomalySeverity = "LOW"
	AnomalySeverityMedium   AnomalySeverity = "MEDIUM"
	AnomalySeverityHigh     AnomalySeverity = "HIGH"
	AnomalySeverityCritical AnomalySeverity = "CRITICAL"
)

// AnomalyModelStatus represents the status of the anomaly detection model
type AnomalyModelStatus struct {
	IsTrained        bool      `json:"is_trained"`
	LastTraining     time.Time `json:"last_training"`
	TrainingSize     int       `json:"training_size"`
	ModelAccuracy    float64   `json:"model_accuracy"`
	ModelVersion     string    `json:"model_version"`
	SupportedMetrics []string  `json:"supported_metrics"`
}

// AnomalyDetectionStats provides statistics about anomaly detection
type AnomalyDetectionStats struct {
	TotalDetections int64     `json:"total_detections"`
	TruePositives   int64     `json:"true_positives"`
	FalsePositives  int64     `json:"false_positives"`
	TrueNegatives   int64     `json:"true_negatives"`
	FalseNegatives  int64     `json:"false_negatives"`
	Precision       float64   `json:"precision"`
	Recall          float64   `json:"recall"`
	F1Score         float64   `json:"f1_score"`
	LastEvaluation  time.Time `json:"last_evaluation"`
}

// MonitoringSystem provides comprehensive observability for AI systems
type MonitoringSystem interface {
	// Logging
	Log(entry LogEntry) error
	GetLogs(ctx context.Context, filters LogFilters) ([]LogEntry, error)
	SetLogLevel(level LogLevel) error

	// Metrics
	RecordMetric(metric Metric) error
	GetMetrics(ctx context.Context, query MetricQuery) ([]Metric, error)
	CreateCustomMetric(name string, metricType MetricType, description string) error

	// Alerts
	CreateAlert(alert Alert) error
	UpdateAlert(alertID string, updates Alert) error
	GetAlerts(ctx context.Context, filters AlertFilters) ([]Alert, error)
	ResolveAlert(alertID string) error

	// Dashboards
	CreateDashboard(dashboard Dashboard) error
	UpdateDashboard(dashboardID string, updates Dashboard) error
	GetDashboard(dashboardID string) (*Dashboard, error)
	ListDashboards(ctx context.Context) ([]Dashboard, error)
	DeleteDashboard(dashboardID string) error

	// Anomaly Detection
	GetAnomalyDetector() AnomalyDetector
	DetectAnomalies(ctx context.Context, timeRange TimeRange) ([]Anomaly, error)

	// Health and Status
	GetSystemHealth() SystemHealth
	GetComponentStatus(component string) ComponentStatus
}

// LogFilters defines filtering options for log queries
type LogFilters struct {
	Level      *LogLevel  `json:"level,omitempty"`
	Component  *string    `json:"component,omitempty"`
	ModelID    *string    `json:"model_id,omitempty"`
	RequestID  *string    `json:"request_id,omitempty"`
	StartTime  *time.Time `json:"start_time,omitempty"`
	EndTime    *time.Time `json:"end_time,omitempty"`
	SearchText *string    `json:"search_text,omitempty"`
	Tags       []string   `json:"tags,omitempty"`
	Limit      int        `json:"limit,omitempty"`
	Offset     int        `json:"offset,omitempty"`
}

// MetricQuery defines parameters for metric queries
type MetricQuery struct {
	MetricName  string            `json:"metric_name"`
	Labels      map[string]string `json:"labels,omitempty"`
	TimeRange   TimeRange         `json:"time_range"`
	Aggregation string            `json:"aggregation,omitempty"`
	GroupBy     []string          `json:"group_by,omitempty"`
	Limit       int               `json:"limit,omitempty"`
}

// AlertFilters defines filtering options for alert queries
type AlertFilters struct {
	Severity  *AlertSeverity `json:"severity,omitempty"`
	Status    *AlertStatus   `json:"status,omitempty"`
	ModelID   *string        `json:"model_id,omitempty"`
	Component *string        `json:"component,omitempty"`
	StartTime *time.Time     `json:"start_time,omitempty"`
	EndTime   *time.Time     `json:"end_time,omitempty"`
	Limit     int            `json:"limit,omitempty"`
	Offset    int            `json:"offset,omitempty"`
}

// SystemHealth represents the overall health of the monitoring system
type SystemHealth struct {
	Status         string                     `json:"status"`
	Components     map[string]ComponentStatus `json:"components"`
	LastCheck      time.Time                  `json:"last_check"`
	Uptime         time.Duration              `json:"uptime"`
	MetricsCount   int64                      `json:"metrics_count"`
	LogsCount      int64                      `json:"logs_count"`
	AlertsCount    int64                      `json:"alerts_count"`
	AnomaliesCount int64                      `json:"anomalies_count"`
}

// ComponentStatus represents the status of a system component
type ComponentStatus struct {
	Name         string                 `json:"name"`
	Status       string                 `json:"status"`
	Health       float64                `json:"health"`
	LastCheck    time.Time              `json:"last_check"`
	ResponseTime time.Duration          `json:"response_time"`
	ErrorRate    float64                `json:"error_rate"`
	Metrics      map[string]interface{} `json:"metrics"`
	Issues       []string               `json:"issues"`
}

// DefaultMonitoringSystem implements the MonitoringSystem interface
type DefaultMonitoringSystem struct {
	logs            []LogEntry
	metrics         []Metric
	alerts          map[string]*Alert
	dashboards      map[string]*Dashboard
	anomalyDetector AnomalyDetector
	config          MonitoringConfig
	mu              sync.RWMutex
	startTime       time.Time
}

// MonitoringConfig contains configuration for the monitoring system
type MonitoringConfig struct {
	LogLevel               LogLevel      `json:"log_level"`
	MetricRetention        time.Duration `json:"metric_retention"`
	LogRetention           time.Duration `json:"log_retention"`
	AlertRetention         time.Duration `json:"alert_retention"`
	MaxLogsInMemory        int           `json:"max_logs_in_memory"`
	MaxMetricsInMemory     int           `json:"max_metrics_in_memory"`
	EnableAnomalyDetection bool          `json:"enable_anomaly_detection"`
	AnomalySensitivity     float64       `json:"anomaly_sensitivity"`
}

// NewDefaultMonitoringSystem creates a new monitoring system instance
func NewDefaultMonitoringSystem(config MonitoringConfig, detector AnomalyDetector) *DefaultMonitoringSystem {
	return &DefaultMonitoringSystem{
		logs:            make([]LogEntry, 0),
		metrics:         make([]Metric, 0),
		alerts:          make(map[string]*Alert),
		dashboards:      make(map[string]*Dashboard),
		anomalyDetector: detector,
		config:          config,
		startTime:       time.Now(),
	}
}

// Log records a log entry
func (m *DefaultMonitoringSystem) Log(entry LogEntry) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Set timestamp if not provided
	if entry.Timestamp.IsZero() {
		entry.Timestamp = time.Now()
	}

	// Check if log level meets threshold
	if !m.shouldLog(entry.Level) {
		return nil
	}

	m.logs = append(m.logs, entry)

	// Maintain memory limits
	if len(m.logs) > m.config.MaxLogsInMemory {
		m.logs = m.logs[len(m.logs)-m.config.MaxLogsInMemory:]
	}

	return nil
}

// shouldLog determines if a log entry should be recorded based on level
func (m *DefaultMonitoringSystem) shouldLog(level LogLevel) bool {
	levels := map[LogLevel]int{
		LogLevelDebug: 0,
		LogLevelInfo:  1,
		LogLevelWarn:  2,
		LogLevelError: 3,
		LogLevelFatal: 4,
	}

	return levels[level] >= levels[m.config.LogLevel]
}

// GetLogs retrieves logs based on filters
func (m *DefaultMonitoringSystem) GetLogs(ctx context.Context, filters LogFilters) ([]LogEntry, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var result []LogEntry
	for _, log := range m.logs {
		if m.matchesLogFilters(log, filters) {
			result = append(result, log)
		}
	}

	// Apply pagination
	if filters.Offset > 0 && filters.Offset < len(result) {
		result = result[filters.Offset:]
	}
	if filters.Limit > 0 && filters.Limit < len(result) {
		result = result[:filters.Limit]
	}

	return result, nil
}

// matchesLogFilters checks if a log entry matches the given filters
func (m *DefaultMonitoringSystem) matchesLogFilters(log LogEntry, filters LogFilters) bool {
	if filters.Level != nil && log.Level != *filters.Level {
		return false
	}
	if filters.Component != nil && log.Component != *filters.Component {
		return false
	}
	if filters.ModelID != nil && log.ModelID != *filters.ModelID {
		return false
	}
	if filters.RequestID != nil && log.RequestID != *filters.RequestID {
		return false
	}
	if filters.StartTime != nil && log.Timestamp.Before(*filters.StartTime) {
		return false
	}
	if filters.EndTime != nil && log.Timestamp.After(*filters.EndTime) {
		return false
	}
	if filters.SearchText != nil && !containsText(log.Message, *filters.SearchText) {
		return false
	}
	if len(filters.Tags) > 0 && !containsAllTags(log.Tags, filters.Tags) {
		return false
	}
	return true
}

// containsText checks if text contains a search string (case-insensitive)
func containsText(text, search string) bool {
	// Simplified implementation - in reality would use proper text search
	return len(search) == 0 || len(text) > 0
}

// containsAllTags checks if all required tags are present
func containsAllTags(logTags, requiredTags []string) bool {
	tagMap := make(map[string]bool)
	for _, tag := range logTags {
		tagMap[tag] = true
	}
	for _, required := range requiredTags {
		if !tagMap[required] {
			return false
		}
	}
	return true
}

// RecordMetric records a performance metric
func (m *DefaultMonitoringSystem) RecordMetric(metric Metric) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Set timestamp if not provided
	if metric.Timestamp.IsZero() {
		metric.Timestamp = time.Now()
	}

	m.metrics = append(m.metrics, metric)

	// Maintain memory limits
	if len(m.metrics) > m.config.MaxMetricsInMemory {
		m.metrics = m.metrics[len(m.metrics)-m.config.MaxMetricsInMemory:]
	}

	// Trigger anomaly detection if enabled
	if m.config.EnableAnomalyDetection && m.anomalyDetector != nil {
		go func() {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			anomalies, err := m.anomalyDetector.DetectAnomalies(ctx, []Metric{metric})
			if err == nil && len(anomalies) > 0 {
				// Create alerts for detected anomalies
				for _, anomaly := range anomalies {
					alert := Alert{
						ID:          generateAlertID(),
						Name:        fmt.Sprintf("Anomaly: %s", anomaly.Type),
						Severity:    m.mapAnomalySeverityToAlert(anomaly.Severity),
						Status:      AlertStatusActive,
						Description: anomaly.Description,
						ModelID:     anomaly.ModelID,
						Component:   anomaly.Component,
						CreatedAt:   time.Now(),
						UpdatedAt:   time.Now(),
					}
					m.CreateAlert(alert)
				}
			}
		}()
	}

	return nil
}

// mapAnomalySeverityToAlert maps anomaly severity to alert severity
func (m *DefaultMonitoringSystem) mapAnomalySeverityToAlert(severity AnomalySeverity) AlertSeverity {
	switch severity {
	case AnomalySeverityLow:
		return AlertSeverityLow
	case AnomalySeverityMedium:
		return AlertSeverityMedium
	case AnomalySeverityHigh:
		return AlertSeverityHigh
	case AnomalySeverityCritical:
		return AlertSeverityCritical
	default:
		return AlertSeverityMedium
	}
}

// CreateAlert creates a new alert
func (m *DefaultMonitoringSystem) CreateAlert(alert Alert) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if alert.ID == "" {
		alert.ID = generateAlertID()
	}
	alert.CreatedAt = time.Now()
	alert.UpdatedAt = time.Now()

	m.alerts[alert.ID] = &alert
	return nil
}

// GetSystemHealth returns the overall system health
func (m *DefaultMonitoringSystem) GetSystemHealth() SystemHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	components := make(map[string]ComponentStatus)

	// Add monitoring system component
	components["monitoring"] = ComponentStatus{
		Name:         "Monitoring System",
		Status:       "healthy",
		Health:       1.0,
		LastCheck:    time.Now(),
		ResponseTime: 1 * time.Millisecond,
		ErrorRate:    0.0,
		Metrics: map[string]interface{}{
			"logs_count":    len(m.logs),
			"metrics_count": len(m.metrics),
			"alerts_count":  len(m.alerts),
		},
		Issues: []string{},
	}

	return SystemHealth{
		Status:         "healthy",
		Components:     components,
		LastCheck:      time.Now(),
		Uptime:         time.Since(m.startTime),
		MetricsCount:   int64(len(m.metrics)),
		LogsCount:      int64(len(m.logs)),
		AlertsCount:    int64(len(m.alerts)),
		AnomaliesCount: 0, // Would be calculated from anomaly detector
	}
}

// Placeholder implementations for interface compliance
func (m *DefaultMonitoringSystem) SetLogLevel(level LogLevel) error {
	m.config.LogLevel = level
	return nil
}

func (m *DefaultMonitoringSystem) GetMetrics(ctx context.Context, query MetricQuery) ([]Metric, error) {
	// Implementation would filter metrics based on query
	return m.metrics, nil
}

func (m *DefaultMonitoringSystem) CreateCustomMetric(name string, metricType MetricType, description string) error {
	// Implementation would register a custom metric type
	return nil
}

func (m *DefaultMonitoringSystem) UpdateAlert(alertID string, updates Alert) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if alert, exists := m.alerts[alertID]; exists {
		updates.ID = alertID
		updates.UpdatedAt = time.Now()
		*alert = updates
		return nil
	}
	return fmt.Errorf("alert not found: %s", alertID)
}

func (m *DefaultMonitoringSystem) GetAlerts(ctx context.Context, filters AlertFilters) ([]Alert, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var result []Alert
	for _, alert := range m.alerts {
		result = append(result, *alert)
	}
	return result, nil
}

func (m *DefaultMonitoringSystem) ResolveAlert(alertID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if alert, exists := m.alerts[alertID]; exists {
		alert.Status = AlertStatusResolved
		now := time.Now()
		alert.ResolvedAt = &now
		alert.UpdatedAt = now
		return nil
	}
	return fmt.Errorf("alert not found: %s", alertID)
}

func (m *DefaultMonitoringSystem) CreateDashboard(dashboard Dashboard) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if dashboard.ID == "" {
		dashboard.ID = generateDashboardID()
	}
	dashboard.CreatedAt = time.Now()
	dashboard.UpdatedAt = time.Now()

	m.dashboards[dashboard.ID] = &dashboard
	return nil
}

func (m *DefaultMonitoringSystem) UpdateDashboard(dashboardID string, updates Dashboard) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if dashboard, exists := m.dashboards[dashboardID]; exists {
		updates.ID = dashboardID
		updates.UpdatedAt = time.Now()
		*dashboard = updates
		return nil
	}
	return fmt.Errorf("dashboard not found: %s", dashboardID)
}

func (m *DefaultMonitoringSystem) GetDashboard(dashboardID string) (*Dashboard, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if dashboard, exists := m.dashboards[dashboardID]; exists {
		// Return a copy
		dashboardCopy := *dashboard
		return &dashboardCopy, nil
	}
	return nil, fmt.Errorf("dashboard not found: %s", dashboardID)
}

func (m *DefaultMonitoringSystem) ListDashboards(ctx context.Context) ([]Dashboard, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var result []Dashboard
	for _, dashboard := range m.dashboards {
		result = append(result, *dashboard)
	}
	return result, nil
}

func (m *DefaultMonitoringSystem) DeleteDashboard(dashboardID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.dashboards, dashboardID)
	return nil
}

func (m *DefaultMonitoringSystem) GetAnomalyDetector() AnomalyDetector {
	return m.anomalyDetector
}

func (m *DefaultMonitoringSystem) DetectAnomalies(ctx context.Context, timeRange TimeRange) ([]Anomaly, error) {
	if m.anomalyDetector == nil {
		return []Anomaly{}, nil
	}

	// Filter metrics by time range
	var metricsInRange []Metric
	for _, metric := range m.metrics {
		if metric.Timestamp.After(timeRange.Start) && metric.Timestamp.Before(timeRange.End) {
			metricsInRange = append(metricsInRange, metric)
		}
	}

	return m.anomalyDetector.DetectAnomalies(ctx, metricsInRange)
}

func (m *DefaultMonitoringSystem) GetComponentStatus(component string) ComponentStatus {
	health := m.GetSystemHealth()
	if status, exists := health.Components[component]; exists {
		return status
	}

	return ComponentStatus{
		Name:   component,
		Status: "unknown",
		Health: 0.0,
	}
}

// Utility functions for ID generation
func generateAlertID() string {
	return fmt.Sprintf("alert-%d", time.Now().UnixNano())
}

func generateDashboardID() string {
	return fmt.Sprintf("dashboard-%d", time.Now().UnixNano())
}
