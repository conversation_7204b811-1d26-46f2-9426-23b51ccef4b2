好的，遵照您的指示，我们来生成一份为 `service-offering-service` (标准化服务市场服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **服务与产品的解耦**: 引入`Service Product`的概念，允许多个提供者提供同一种标准化的服务产品，便于平台管理和用户比较。
2.  **高级定价与套餐**: 详细定义更灵活的定价模型，包括套餐（Packages）、附加服务（Add-ons）和按时/按次计费。
3.  **高级可用性管理**: 引入更复杂的可用性逻辑，支持多地点、多员工、以及与`schedule-service`的双向同步。
4.  **健壮的预订与订单状态机**: 细化预订（Booking）和订单（Order）的状态机，覆盖预授权、履约、争议、结算等完整流程。
5.  **与平台生态的深度集成**: 明确与`billing`, `payment`, `review`, `location`, `chat`等服务的详细交互协议和Saga事务流程。
6.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义和事件契约，并对数据模型进行优化。
7.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、交易流程严谨、高度可扩展，且能作为整个平台服务交易生态核心的 marketplace。

---

### CINA.CLUB - service-offering-service 需求规格说明书

**版本: 2.0 (生产级定义，集成高级产品与订单模型)**  
**发布日期: 2025-06-24**  
**最后修订日期: 2025-06-24**  
**文档负责人:** [服务市场产品/技术负责人名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台的核心是连接人与服务。`service-offering-service` 旨在构建一个**繁荣、可信、高效的标准化服务交易市场**。它允许具备特定技能的个人或商家（服务提供者）发布其服务，也让有需求的用户能够方便地发现、比较、预订和评价这些服务。本服务是平台内C2C和B2C服务交易的基础设施，其设计的健壮性、灵活性和可靠性直接决定了平台交易生态的成败。

#### 1.2. 服务范围
本服务 **负责**:
*   **服务产品目录管理**:
    *   管理平台标准化的**服务产品(Service Product)**目录，如“1小时摄影”、“钢琴初级课”。
    *   管理服务分类、属性模板和标签。
*   **服务项目(Offering)管理**:
    *   服务提供者基于`Service Product`，创建自己可售卖的`Offering`，并进行个性化定价和描述。
*   **高级定价与套餐**: 支持固定价、按时/按次计费、以及包含多个项目的套餐(Packages)和可选的附加服务(Add-ons)。
*   **高级可用性(Availability)管理**:
    *   提供者定义和管理其可服务的时间，支持多员工、多地点。
    *   与`schedule-service`进行双向同步，确保可用性时段的准确性。
*   **预订与订单(Booking & Order)管理**:
    *   处理用户的预订请求，并管理从预订到履约、结算、关闭的**完整订单状态机**。
    *   作为**Saga分布式事务的协调者**，编排与`billing`, `payment`, `notification`, `chat`等服务的交互。

本服务 **不负责**:
*   **即时代购/跑腿类任务** (由专门的`errand-task-service`或类似服务负责)。
*   **核心支付网关交互** (由 `payment-service` 负责)。
*   **核心计费与订阅逻辑** (由 `billing-service` 负责，但本服务会调用它)。
*   **通用的社区功能**。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用**: 服务需求方（浏览、预订）、服务提供方（发布、管理）。
*   **`search-indexer-service`**: (消费本服务事件) 索引服务项目。
*   **`billing-service`, `payment-service`**: (被本服务调用) 处理订单支付。
*   **`review-service`**: (被本服务调用) 在订单完成后触发评价流程。
*   **`notification-dispatch-service`, `chat-service`**: (被本服务调用) 发送订单相关通知和创建沟通渠道。
*   **`schedule-service`**: (双向交互) 同步可用性与已预订时段。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`service-offering-service` 是CINA.CLUB平台**交易生态的核心**。它定义了“服务”这一核心业务对象的属性和生命周期，并作为**交易流程的中央编排者**，负责协调从服务发布、发现、预订、支付到最终完成和评价的整个复杂业务流程。它是连接供需双方并产生商业价值的关键枢纽。

#### 2.2. 主要功能概述
*   “服务产品-服务项目”分离的、可扩展的目录体系。
*   支持套餐和附加服务的高级定价模型。
*   与日程服务双向同步的、精确的可用性管理。
*   健壮的、支持Saga模式的订单与预订流程状态机。

### 3. 核心流程图

#### 3.1. 用户预订服务并完成支付的Saga流程
```mermaid
sequenceDiagram
    participant Client
    participant ServiceOffering as SO
    participant BillingService as BS
    participant PaymentService as PS
    
    Client->>SO: 1. POST /bookings (offeringId, timeSlot, addOns)
    SO->>DB: 2. Create Order record (status: PENDING_PAYMENT)
    
    Note over SO: **Start Payment Saga**
    
    SO->>BS: 3. **[Execute]** Request to create invoice for the order
    BS-->>SO: (invoiceId)
    SO->>DB: 4. Update Order with invoiceId
    
    SO->>PS: 5. **[Execute]** Initiate payment for the invoice
    PS-->>SO: (paymentParams for client)
    SO-->>Client: 6. (paymentParams)
    
    Note over Client: User completes payment on frontend
    
    PS-->>SO: 7. **[Callback/Event]** Payment Succeeded
    SO->>DB: 8. Update Order status to CONFIRMED
    SO->>ScheduleService: 9. Block the booked time slot
    SO->>Notification/Chat: 10. Notify both parties
    
    alt Payment Fails or Times Out
        PS-->>SO: **[Callback/Event]** Payment Failed
        Note over SO: **Start Compensation Saga**
        SO->>DB: 1. Update Order status to FAILED
        SO->>BS: 2. **[Compensate]** Cancel the invoice
        SO-->>Client: (Notify payment failure)
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 服务产品与项目管理
*   **FR4.1.1 (产品目录)**: 管理员必须能创建和管理标准化的`ServiceProduct`，定义其通用属性、分类和适用的评价维度。
*   **FR4.1.2 (服务项目)**: 服务提供者基于一个`ServiceProduct`创建自己的`ServiceOffering`，可以覆盖价格、添加个性化描述和图片。
*   **FR4.1.3 (套餐与附加服务)**: `ServiceOffering`的定价模型必须支持：
    *   **Packages**: 创建不同等级的套餐（如“基础-标准-高级”），每个套餐包含不同的服务内容和价格。
    *   **Add-ons**: 提供可选的附加服务，用户在预订时可以勾选，并计入总价。

#### 4.2. 高级可用性管理
*   **FR4.2.1 (规则定义)**: 提供者能定义复杂的、基于规则的可用性（每周重复、特定日期、节假日除外）。
*   **FR4.2.2 (双向同步)**:
    *   **写操作**: 当在本服务中创建一个已确认的预订时，必须调用`schedule-service`的API，在提供者的个人日历中创建一个对应的“忙碌”事件。
    *   **读操作**: 在查询可用性时，不仅要考虑本服务中的预订，还必须调用`schedule-service`的`free-busy`接口，获取提供者个人日历中的忙碌时段，并将两者合并，得到最终的可用时间槽。

#### 4.3. 预订与订单状态机
*   **FR4.3.1 (订单(Order)作为核心)**: 每次预订都将创建一个`Order`实体，它代表了整个交易的生命周期。`Booking`是`Order`的一部分，代表了具体的服务时段。
*   **FR4.3.2 (状态机)**: `Order`必须有明确的状态机，覆盖从创建到完成的全过程：`PENDING_PAYMENT`, `CONFIRMED`, `IN_PROGRESS`, `COMPLETED`, `CANCELED`, `DISPUTED`, `REFUNDED`。
*   **FR4.3.3 (履约管理)**: 提供者和客户必须能更新订单状态（如“开始服务”、“确认完成”）。客户的“确认完成”是触发最终结算和评价流程的关键。
*   **FR4.3.4 (Saga协调)**: 所有涉及跨服务调用的流程（如支付、取消、退款），都必须在本服务内作为Saga事务进行编排，确保最终一致性。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部/客户端gRPC API接口
*   **Package**: `hina.vip.marketplace.v1`
*   **认证**: User JWT, S2S Auth, Admin Role。
*   **核心RPC**:
    ```protobuf
    service ServiceOfferingService {
      // Discovery
      rpc SearchOfferings(SearchOfferingsRequest) returns (SearchOfferingsResponse);
      rpc GetOfferingDetails(GetOfferingDetailsRequest) returns (Offering);
      rpc GetAvailability(GetAvailabilityRequest) returns (AvailabilityResponse);
      
      // Booking & Order
      rpc CreateBooking(CreateBookingRequest) returns (Order);
      rpc GetOrder(GetOrderRequest) returns (Order);
      rpc ConfirmOrder(ConfirmOrderRequest) returns (Order); // Provider confirms
      rpc CancelOrder(CancelOrderRequest) returns (Order);
      rpc FulfillOrder(FulfillOrderRequest) returns (Order); // Provider marks as completed
      rpc ConfirmCompletion(ConfirmCompletionRequest) returns (Order); // Customer confirms completion
      
      // Provider Management
      rpc CreateOffering(CreateOfferingRequest) returns (Offering);
      rpc UpdateOffering(UpdateOfferingRequest) returns (Offering);
    }
    ```

#### 5.2. 消息队列事件契约
*   **出站 (发布)**:
    *   `ServiceOfferingPublishedEvent` (for `search-indexer-service`).
    *   `OrderConfirmedEvent` (for `notification-service`, `chat-service`).
    *   `OrderCompletionConfirmedEvent` (for `review-service`, `gamification-service`, `analytics-service`).
*   **入站 (消费)**: `PaymentSucceededEvent`, `PaymentFailedEvent` (from `payment-service`).

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL + PostGIS)
*   **`service_products`**: `id`, `name`, `category_id`, `attribute_template (JSONB)`.
*   **`service_offerings`**: `id`, `product_id`, `provider_user_id`, `title`, `description`, `pricing_details (JSONB)`, `status`.
*   **`orders`**: `id`, `customer_user_id`, `provider_user_id`, `offering_id`, `status (INDEX)`, `total_amount`, `payment_status`, `booking_start_time`, `booking_end_time`.
*   **`order_line_items`**: `id`, `order_id`, `description`, `price`, `quantity` (e.g., for add-ons).
*   **`availability_rules`**: `id`, `provider_user_id`, `rrule_string`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **服务搜索API**: P99延迟 < 300ms (依赖搜索引擎)。
*   **可用性查询API**: P99延迟 < 400ms (因涉及对`schedule-service`的调用)。
*   **预订创建API**: P99延迟 < 500ms (因涉及Saga编排)。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **数据一致性**: 必须通过Saga和补偿事务，保证在任何故障情况下，订单和支付状态的最终一致性。
*   **并发控制**: 对可用性槽位的预订操作，必须使用**分布式锁**或数据库悲观锁，防止双重预订。

#### 7.3. 可扩展性需求
*   服务可水平扩展。数据库可通过读写分离和分区进行扩展。

#### 7.4. 安全性需求
*   严格的权限控制。
*   防止订单流程中的价格篡改等逻辑漏洞。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL + PostGIS (如果涉及地理位置)。
*   **分布式事务**: **必须**采用成熟的Saga编排模式。可以自研一个简单的Saga管理器，或使用`Temporal`/`Cadence`等专业的Workflow-as-Code工具来管理复杂的订单流程。
*   **可用性计算**: 这是核心技术难点之一，需要健壮的逻辑来合并数据库中的预订和从`schedule-service`获取的忙碌时段。

---
这份版本2.0的SRS文档为`service-offering-service`构建了一个功能强大、流程严谨、高度可扩展的服务交易市场核心。它通过引入产品化、高级定价和Saga事务等概念，能够为CINA.CLUB平台复杂多变的商业场景提供坚实、可靠的支撑。