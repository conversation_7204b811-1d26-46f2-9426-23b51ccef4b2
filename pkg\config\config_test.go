/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:30:00
Modified: 2025-01-21 10:30:00
*/

package config

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/go-playground/validator/v10"
)

// TestConfig represents a test configuration structure
type TestConfig struct {
	Server   ServerConfig   `mapstructure:"server" validate:"required"`
	Database DatabaseConfig `mapstructure:"database" validate:"required"`
	Logger   LoggerConfig   `mapstructure:"logger"`
}

type ServerConfig struct {
	Port int    `mapstructure:"port" validate:"required,gte=1024,lte=65535" default:"8080"`
	Mode string `mapstructure:"mode" validate:"required,servicemode" default:"development"`
	Host string `mapstructure:"host" default:"localhost"`
}

type DatabaseConfig struct {
	Postgres PostgresConfig `mapstructure:"postgres" validate:"required"`
	Redis    RedisConfig    `mapstructure:"redis"`
}

type PostgresConfig struct {
	DSN         string `mapstructure:"dsn" validate:"required,dsn"`
	MaxConns    int    `mapstructure:"max_conns" validate:"gte=1" default:"10"`
	MaxIdleTime string `mapstructure:"max_idle_time" default:"30m"`
}

type RedisConfig struct {
	Addr     string `mapstructure:"addr" validate:"required" default:"localhost:6379"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db" validate:"gte=0" default:"0"`
}

type LoggerConfig struct {
	Level  string `mapstructure:"level" validate:"loglevel" default:"info"`
	Format string `mapstructure:"format" validate:"oneof=json text" default:"json"`
}

func TestLoadConfig_WithValidFile(t *testing.T) {
	// Create a temporary config file
	configContent := `
server:
  port: 9090
  mode: production
  host: 0.0.0.0

database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/testdb"
    max_conns: 20
  redis:
    addr: "localhost:6379"
    db: 1

logger:
  level: debug
  format: json
`

	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Load configuration
	var cfg TestConfig
	err = LoadConfig(configPath, &cfg)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify configuration values
	if cfg.Server.Port != 9090 {
		t.Errorf("Expected server port 9090, got %d", cfg.Server.Port)
	}
	if cfg.Server.Mode != "production" {
		t.Errorf("Expected server mode 'production', got %s", cfg.Server.Mode)
	}
	if cfg.Database.Postgres.MaxConns != 20 {
		t.Errorf("Expected postgres max_conns 20, got %d", cfg.Database.Postgres.MaxConns)
	}
}

func TestLoadConfig_WithDefaults(t *testing.T) {
	// Create a minimal config file
	configContent := `
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/testdb"
`

	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Load configuration
	var cfg TestConfig
	err = LoadConfig(configPath, &cfg)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify default values were applied
	if cfg.Server.Port != 8080 {
		t.Errorf("Expected default server port 8080, got %d", cfg.Server.Port)
	}
	if cfg.Server.Mode != "development" {
		t.Errorf("Expected default server mode 'development', got %s", cfg.Server.Mode)
	}
	if cfg.Database.Postgres.MaxConns != 10 {
		t.Errorf("Expected default postgres max_conns 10, got %d", cfg.Database.Postgres.MaxConns)
	}
}

func TestLoadConfig_WithEnvironmentVariables(t *testing.T) {
	// Set environment variables
	os.Setenv("CINA_SERVER_PORT", "3000")
	os.Setenv("CINA_SERVER_MODE", "staging")
	os.Setenv("CINA_DATABASE_POSTGRES_MAX_CONNS", "50")
	defer func() {
		os.Unsetenv("CINA_SERVER_PORT")
		os.Unsetenv("CINA_SERVER_MODE")
		os.Unsetenv("CINA_DATABASE_POSTGRES_MAX_CONNS")
	}()

	// Create a minimal config file
	configContent := `
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/testdb"
`

	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Load configuration
	var cfg TestConfig
	err = LoadConfig(configPath, &cfg)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify environment variables override file values
	if cfg.Server.Port != 3000 {
		t.Errorf("Expected server port 3000 from env var, got %d", cfg.Server.Port)
	}
	if cfg.Server.Mode != "staging" {
		t.Errorf("Expected server mode 'staging' from env var, got %s", cfg.Server.Mode)
	}
	if cfg.Database.Postgres.MaxConns != 50 {
		t.Errorf("Expected postgres max_conns 50 from env var, got %d", cfg.Database.Postgres.MaxConns)
	}
}

func TestLoadConfig_ValidationFailure(t *testing.T) {
	// Create a config with invalid values
	configContent := `
server:
  port: 80  # Invalid - below 1024
  mode: invalid_mode  # Invalid mode
  
database:
  postgres:
    dsn: "invalid-dsn"  # Invalid DSN format
    max_conns: -1  # Invalid - negative
`

	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Load configuration - should fail validation
	var cfg TestConfig
	err = LoadConfig(configPath, &cfg)
	if err == nil {
		t.Fatal("Expected validation to fail, but it succeeded")
	}

	// Verify error message contains validation details
	errorMessage := err.Error()
	if !contains(errorMessage, "validation failed") {
		t.Errorf("Expected validation error message, got: %s", errorMessage)
	}
}

func TestLoadConfig_MissingFile(t *testing.T) {
	// Try to load a non-existent file
	var cfg TestConfig
	err := LoadConfig("/non/existent/config.yaml", &cfg)

	// Should not fail if file doesn't exist (environment variables might provide config)
	// But validation should still fail because required fields are missing
	if err == nil {
		t.Fatal("Expected error due to missing required configuration")
	}
}

func TestLoadConfig_InvalidStruct(t *testing.T) {
	// Try to load into a non-pointer
	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte("test: value"), 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	var cfg TestConfig // Not a pointer
	err = LoadConfig(configPath, cfg)

	if err == nil {
		t.Fatal("Expected error when passing non-pointer struct")
	}
	if !contains(err.Error(), "must be a pointer to a struct") {
		t.Errorf("Expected pointer error message, got: %s", err.Error())
	}
}

func TestMustLoadConfig_Success(t *testing.T) {
	// Create a valid config file
	configContent := `
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/testdb"
`

	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// This should not panic
	var cfg TestConfig
	MustLoadConfig(configPath, &cfg)

	// Verify it loaded correctly
	if cfg.Database.Postgres.DSN == "" {
		t.Error("Expected DSN to be loaded")
	}
}

func TestCustomValidation(t *testing.T) {
	// Register a custom validation
	err := AddCustomValidation("custom", func(fl validator.FieldLevel) bool {
		return fl.Field().String() == "valid"
	})
	if err != nil {
		t.Fatalf("Failed to register custom validation: %v", err)
	}

	// Test struct with custom validation
	type CustomConfig struct {
		CustomField string `validate:"custom"`
	}

	// Test valid value
	validConfig := CustomConfig{CustomField: "valid"}
	err = ValidateStruct(validConfig)
	if err != nil {
		t.Errorf("Expected validation to pass for valid value, got: %v", err)
	}

	// Test invalid value
	invalidConfig := CustomConfig{CustomField: "invalid"}
	err = ValidateStruct(invalidConfig)
	if err == nil {
		t.Error("Expected validation to fail for invalid value")
	}
}

func TestLoadWithInfo(t *testing.T) {
	// Set an environment variable
	os.Setenv("CINA_SERVER_PORT", "9999")
	defer os.Unsetenv("CINA_SERVER_PORT")

	// Create a config file
	configContent := `
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/testdb"
`

	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Load with info
	loader := NewLoader()
	var cfg TestConfig
	info, err := loader.LoadWithInfo(configPath, &cfg)
	if err != nil {
		t.Fatalf("Failed to load config with info: %v", err)
	}

	// Verify info
	if info.ConfigFile != configPath {
		t.Errorf("Expected config file %s, got %s", configPath, info.ConfigFile)
	}

	// Check if environment variable was found
	found := false
	for _, envVar := range info.EnvVarsFound {
		if contains(envVar, "CINA_SERVER_PORT") {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected CINA_SERVER_PORT to be found in environment variables")
	}
}

// Benchmark the configuration loading performance
func BenchmarkLoadConfig(b *testing.B) {
	// Create a test config file
	configContent := `
server:
  port: 8080
  mode: development

database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/testdb"
    max_conns: 10
  redis:
    addr: "localhost:6379"
    db: 0

logger:
  level: info
  format: json
`

	tmpDir := b.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		b.Fatalf("Failed to write test config file: %v", err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		var cfg TestConfig
		err := LoadConfig(configPath, &cfg)
		if err != nil {
			b.Fatalf("Failed to load config: %v", err)
		}
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(substr) <= len(s) && s[len(s)-len(substr):] == substr ||
		len(substr) <= len(s) && s[:len(substr)] == substr ||
		len(substr) < len(s) && findSubstring(s, substr)
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
