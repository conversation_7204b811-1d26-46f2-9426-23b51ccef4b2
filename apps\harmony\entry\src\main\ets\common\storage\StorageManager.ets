/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { hilog } from '@kit.PerformanceAnalysisKit';
import { preferences } from '@kit.ArkData';
import { security } from '@kit.SecurityKit';
import { common } from '@kit.AbilityKit';
import { DatabaseManager } from '../database/DatabaseManager';

/**
 * 存储管理器
 * 
 * 提供统一的存储接口，包括普通存储、安全存储和数据库存储
 */
export class StorageManager {
  private static readonly TAG = 'StorageManager';
  
  private context: common.UIAbilityContext;
  private databaseManager: DatabaseManager;
  private preferences: preferences.Preferences | null = null;

  constructor(context: common.UIAbilityContext, databaseManager: DatabaseManager) {
    this.context = context;
    this.databaseManager = databaseManager;
  }

  /**
   * 初始化存储管理器
   */
  async initialize(): Promise<void> {
    try {
      this.preferences = await preferences.getPreferences(this.context, 'app_storage');
      hilog.info(0x0000, StorageManager.TAG, 'StorageManager initialized');
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Initialize failed: ${error.message}`);
      throw error;
    }
  }

  // ===================== 普通存储 =====================

  /**
   * 存储字符串
   */
  async setString(key: string, value: string): Promise<void> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      await this.preferences!.put(key, value);
      await this.preferences!.flush();
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Set string failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取字符串
   */
  async getString(key: string, defaultValue?: string): Promise<string | null> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      const value = await this.preferences!.get(key, defaultValue || null);
      return value as string | null;
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Get string failed: ${error.message}`);
      return null;
    }
  }

  /**
   * 存储数字
   */
  async setNumber(key: string, value: number): Promise<void> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      await this.preferences!.put(key, value);
      await this.preferences!.flush();
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Set number failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取数字
   */
  async getNumber(key: string, defaultValue?: number): Promise<number | null> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      const value = await this.preferences!.get(key, defaultValue || null);
      return value as number | null;
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Get number failed: ${error.message}`);
      return null;
    }
  }

  /**
   * 存储布尔值
   */
  async setBoolean(key: string, value: boolean): Promise<void> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      await this.preferences!.put(key, value);
      await this.preferences!.flush();
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Set boolean failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取布尔值
   */
  async getBoolean(key: string, defaultValue?: boolean): Promise<boolean | null> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      const value = await this.preferences!.get(key, defaultValue || null);
      return value as boolean | null;
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Get boolean failed: ${error.message}`);
      return null;
    }
  }

  // ===================== 安全存储 =====================

  /**
   * 安全存储字符串（使用HarmonyOS Keystore）
   */
  async setSecureString(key: string, value: string): Promise<void> {
    try {
      // 注意：这里简化了Keystore的使用，实际实现需要更复杂的密钥管理
      // 在实际项目中，应该使用HarmonyOS的安全存储API
      await this.setString(`secure_${key}`, this.simpleEncrypt(value));
      hilog.debug(0x0000, StorageManager.TAG, `Secure string stored: ${key}`);
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Set secure string failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取安全存储的字符串
   */
  async getSecureString(key: string): Promise<string | null> {
    try {
      const encrypted = await this.getString(`secure_${key}`);
      if (!encrypted) {
        return null;
      }
      
      return this.simpleDecrypt(encrypted);
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Get secure string failed: ${error.message}`);
      return null;
    }
  }

  // ===================== 通用方法 =====================

  /**
   * 删除存储项
   */
  async remove(key: string): Promise<void> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      await this.preferences!.delete(key);
      await this.preferences!.flush();
      
      // 同时删除安全存储版本
      await this.preferences!.delete(`secure_${key}`);
      await this.preferences!.flush();
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Remove failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查是否存在
   */
  async has(key: string): Promise<boolean> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      return await this.preferences!.has(key);
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Has check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 清理所有数据
   */
  async clear(): Promise<void> {
    if (!this.preferences) {
      await this.initialize();
    }
    
    try {
      await this.preferences!.clear();
      await this.preferences!.flush();
      hilog.info(0x0000, StorageManager.TAG, 'All storage data cleared');
    } catch (error) {
      hilog.error(0x0000, StorageManager.TAG, `Clear failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.preferences = null;
    hilog.info(0x0000, StorageManager.TAG, 'StorageManager cleaned up');
  }

  // ===================== 私有方法 =====================

  /**
   * 简单加密（仅作示例，实际应使用更安全的方法）
   */
  private simpleEncrypt(text: string): string {
    // 这里应该使用真正的加密算法
    // 在实际项目中，应该调用CoreGoBridge的加密功能
    return Buffer.from(text, 'utf8').toString('base64');
  }

  /**
   * 简单解密（仅作示例，实际应使用更安全的方法）
   */
  private simpleDecrypt(encrypted: string): string {
    // 这里应该使用真正的解密算法
    // 在实际项目中，应该调用CoreGoBridge的解密功能
    return Buffer.from(encrypted, 'base64').toString('utf8');
  }
} 