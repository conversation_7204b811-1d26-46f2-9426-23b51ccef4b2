/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package logger

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"time"

	"cina.club/pkg/errors"
)

// CinaLogHandler 是一个自定义的slog.Handler，提供增强的日志功能
type CinaLogHandler struct {
	handler     slog.Handler
	serviceName string
	serviceVer  string
}

// NewCinaLogHandler 创建一个新的CinaLogHandler
func NewCinaLogHandler(cfg *Config) *CinaLogHandler {
	opts := &slog.HandlerOptions{
		Level:     cfg.ToSlogLevel(),
		AddSource: cfg.AddSource,
	}

	var baseHandler slog.Handler
	if cfg.IsJSONFormat() {
		baseHandler = slog.NewJSONHandler(os.Stdout, opts)
	} else {
		baseHandler = slog.NewTextHandler(os.<PERSON>, opts)
	}

	return &CinaLogHandler{
		handler:     baseHandler,
		serviceName: cfg.ServiceName,
		serviceVer:  cfg.ServiceVersion,
	}
}

// Enabled 实现slog.Handler接口
func (h *CinaLogHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

// Handle 实现slog.Handler接口的核心方法
func (h *CinaLogHandler) Handle(ctx context.Context, r slog.Record) error {
	// 1. 添加服务固定字段
	r.AddAttrs(
		slog.Group("service",
			slog.String("name", h.serviceName),
			slog.String("version", h.serviceVer),
		),
	)

	// 2. 从上下文中提取追踪信息
	if traceID := extractTraceID(ctx); traceID != "" {
		r.AddAttrs(slog.Group("trace", slog.String("trace_id", traceID)))
	}

	if userID := extractUserID(ctx); userID != "" {
		r.AddAttrs(slog.String("user_id", userID))
	}

	// 3. 智能错误处理
	h.enhanceErrorInfo(&r)

	// 4. 添加时间戳
	if r.Time.IsZero() {
		r.Time = time.Now()
	}

	// 5. 调用底层handler
	return h.handler.Handle(ctx, r)
}

// WithAttrs 实现slog.Handler接口
func (h *CinaLogHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &CinaLogHandler{
		handler:     h.handler.WithAttrs(attrs),
		serviceName: h.serviceName,
		serviceVer:  h.serviceVer,
	}
}

// WithGroup 实现slog.Handler接口
func (h *CinaLogHandler) WithGroup(name string) slog.Handler {
	return &CinaLogHandler{
		handler:     h.handler.WithGroup(name),
		serviceName: h.serviceName,
		serviceVer:  h.serviceVer,
	}
}

// enhanceErrorInfo 增强错误信息
func (h *CinaLogHandler) enhanceErrorInfo(r *slog.Record) {
	r.Attrs(func(a slog.Attr) bool {
		if a.Key == "error" && a.Value.Kind() == slog.KindAny {
			if err, ok := a.Value.Any().(error); ok {
				if appErr := h.extractAppError(err); appErr != nil {
					r.AddAttrs(
						slog.String("error_code", string(appErr.Code)),
						slog.String("error_message", appErr.Message),
					)

					if len(appErr.Metadata) > 0 {
						metadataAttrs := make([]any, 0, len(appErr.Metadata)*2)
						for k, v := range appErr.Metadata {
							metadataAttrs = append(metadataAttrs, k, v)
						}
						r.AddAttrs(slog.Group("error_metadata", metadataAttrs...))
					}

					if stack := errors.GetStack(err); stack != nil {
						r.AddAttrs(slog.String("stack_trace", fmt.Sprintf("%+v", stack)))
					}
				}
			}
		}
		return true
	})
}

// extractAppError 尝试从error中提取AppError
func (h *CinaLogHandler) extractAppError(err error) *errors.AppError {
	if appErr, ok := err.(*errors.AppError); ok {
		return appErr
	}
	return nil
}

// extractTraceID 从上下文中提取trace_id
func extractTraceID(ctx context.Context) string {
	if traceID, ok := ctx.Value("trace_id").(string); ok {
		return traceID
	}
	return ""
}

// extractUserID 从上下文中提取user_id
func extractUserID(ctx context.Context) string {
	if userID, ok := ctx.Value("user_id").(string); ok {
		return userID
	}
	return ""
}
