好的，遵照您的指示，我们来生成一份为 `key-management-proxy-service` (密钥管理代理服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **安全模型深化**: 明确定义了“信封加密(Envelope Encryption)”模型，并详细阐述了MEK、DEK和WrappedDEK之间的关系和生命周期。
2.  **API设计优化**: 引入了“加解密代理”模式作为更安全的API选项，与直接提供DEK的模式并存，以适应不同安全等级和性能要求的场景。
3.  **密钥轮换与数据重加密**: 详细定义了用户DEK轮换和数据重加密的协调流程，这是长期数据安全的关键。
4.  **灾难恢复与密钥备份**: 增加对密钥备份和恢复流程的考量，确保在极端情况下用户数据不会永久丢失。
5.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义，并对数据模型进行优化。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性、特别是安全审计和合规性指标。

这份文档将描绘一个符合零信任架构、满足最高安全合规要求，且能作为整个平台隐私保护基石的密钥管理服务。

---

### CINA.CLUB - key-management-proxy-service 需求规格说明书

**版本: 2.0 (生产级定义，强化安全模型与灾备)**  
**发布日期: 2025-06-22**  
**最后修订日期: 2025-06-22**  
**文档负责人:** [首席安全官 (CSO) / 安全架构师]  
**审批人:** [CTO / CEO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心安全模型与流程](#3-核心安全模型与流程)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台致力于为用户提供对个人数据（如个人知识库PKB、个人记忆PM）的绝对隐私保护。`key-management-proxy-service` (KMSProxy) 的目的在于实现**应用层加密 (Application-Level Encryption, ALE)**，通过集中管理每个用户的独立**数据加密密钥 (Data Encryption Key, DEK)**，确保即使在云端数据库被物理访问的极端情况下，用户的核心私密数据内容也无法被解密。本服务是平台“**服务器端零知识存储**”承诺的技术基石和安全核心。

#### 1.2. 服务范围
本服务 **负责**:
*   **信封加密(Envelope Encryption)的实现**:
    *   作为与外部硬件安全模块(HSM)或云密钥管理服务(Cloud KMS)交互的**唯一安全代理**。
    *   使用KMS中的**主加密密钥(Master Encryption Key, MEK)**，安全地**包装(wrapping)**和**解包(unwrapping)**用户DEK。
*   **用户DEK生命周期管理**:
    *   为每个用户安全地生成、存储（包装后）、轮换和销毁DEK。
*   **安全的密钥供应/加解密代理**:
    *   提供一个严格受控的内部API，供授权微服务在验证了有效的用户会话后，请求对数据进行加解密操作。
    *   （在特定、受限场景下）提供获取明文DEK的接口。
*   **密钥轮换与数据重加密协调**: 管理DEK的版本和轮换流程，并为上层服务的数据重加密提供支持。
*   **安全审计**: 记录所有密钥操作的**详细、不可篡改的审计日志**。

本服务 **不负责**:
*   **管理MEK的生命周期**: MEK的创建、轮换、销毁由安全团队直接在云KMS/HSM中操作。
*   **用户身份认证**: 由`user-core-service`负责。本服务会调用它来验证用户会话的有效性。
*   **存储任何用户业务数据**: 它只存储包装后的密钥和密钥元数据。
*   **直接面向最终用户或API Gateway暴露任何API**。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部需要加解密私密数据的微服务 (唯一调用方)**:
    *   `personal-kb-service`: 请求对PKB内容进行加解密。
    *   `memory-service`: 请求对PM内容进行加解密。
*   **CINA.CLUB安全与合规团队**: 通过审计日志监控密钥使用情况。

#### 1.4. 定义与缩略语
*   **KMS**: Key Management Service (e.g., AWS KMS, Google Cloud KMS, HashiCorp Vault)。
*   **MEK**: Master Encryption Key，主加密密钥，存储在KMS中，用于加密其他密钥。
*   **DEK**: Data Encryption Key，数据加密密钥，每个用户一个，用于加密该用户的实际数据。
*   **WrappedDEK**: 被MEK加密（包装）后的DEK，是本服务存储的形式。
*   **Plaintext DEK**: 解密后的DEK明文，极度敏感，只能在内存中短时间存在。
*   **ALE**: Application-Level Encryption。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`key-management-proxy-service` 是CINA.CLUB安全架构的“**心脏**”和“**数字保险库**”。它位于所有需要处理用户加密数据的服务与底层KMS之间，形成一个**集中的、可审计的、策略驱动的密钥访问控制点**。它将复杂的KMS交互和密钥生命周期管理对业务服务透明化，同时强制执行最严格的安全策略，是整个平台零知识隐私保护方案中**最关键、最敏感**的一环。

#### 2.2. 主要功能概述
*   基于信封加密的安全DEK生命周期管理。
*   基于多重验证的、以“加解密代理”为首选的密钥服务API。
*   与云KMS的解耦和安全代理。
*   全面的、防篡改的安全审计日志。
*   支持密钥轮换和灾难恢复。

### 3. 核心安全模型与流程

#### 3.1. 信封加密 (Envelope Encryption) 流程
```mermaid
graph TD
    subgraph Cloud KMS / HSM (Highest Security)
        MEK("Master Encryption Key (MEK)")
    end

    subgraph KMSProxy Service
        subgraph In-Memory (Ephemeral)
            P_DEK("Plaintext DEK")
        end
        subgraph PostgreSQL DB
            W_DEK("Wrapped DEK")
        end
        API(gRPC API)
    end
    
    subgraph Business Service (e.g., PKB Service)
        Data("User's Plaintext Data")
        EncData("User's Encrypted Data")
    end

    style MEK fill:#ff4f4f,stroke:#333,stroke-width:4px
    style P_DEK fill:#f9f,stroke:#333,stroke-width:2px

    P_DEK -- "1. Wrap with MEK" --> MEK
    MEK -- "2. Returns WrappedDEK" --> W_DEK
    W_DEK -- "3. Store" --> DB
    
    Data -- "6. Encrypt with Plaintext DEK" --> P_DEK
    P_DEK -- "7. Returns Encrypted Data" --> EncData

    subgraph "Decryption Flow"
        API_Req(API Request for Data) --> BS_Decrypt(Business Service)
        BS_Decrypt -- "4. Request Decryption" --> API
        API -- "Get WrappedDEK from DB" --> DB
        API -- "5. Unwrap with MEK" --> MEK
        MEK -- "Returns Plaintext DEK" --> P_DEK_Decrypt(Plaintext DEK)
        BS_Decrypt -- "Sends Encrypted Data" --> P_DEK_Decrypt
        P_DEK_Decrypt -- "Decrypts" --> BS_Decrypt
        BS_Decrypt -- "Returns Plaintext Data" --> API_Req
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 用户DEK生命周期管理
*   **FR4.1.1 (生成与包装)**: 系统必须提供内部API，为指定用户生成一个新的、密码学安全的DEK（AES-256），并立即调用KMS使用当前的MEK对其进行包装。
*   **FR4.1.2 (存储)**: 系统必须将`WrappedDEK`、`dekVersion`、`mekIdUsed`等元数据安全地持久化到其数据库中，与`userId`关联。
*   **FR4.1.3 (销毁)**: 系统必须提供机制安全地销毁用户的DEK（通过删除DB中的`WrappedDEK`记录）。此操作是**不可逆的**，将导致该用户所有加密数据无法解密。必须有严格的、多重审批的流程。

#### 4.2. 安全的加解密代理服务 (首选API模式)
*   **FR4.2.1 (加解密API)**: 系统必须提供`EncryptData`和`DecryptData`的gRPC接口。这是**推荐**的、最安全的交互模式。
    *   `EncryptData`接收`userId`、会话凭证和**明文数据**，内部完成获取DEK、加密、返回**密文**的全过程。
    *   `DecryptData`接收`userId`、会话凭证和**密文数据**，内部完成获取DEK、解密、返回**明文**的全过程。
*   **FR4.2.2 (强制多重验证)**: 所有对加解密API的调用，都必须执行以下所有检查：
    *   **S2S认证**: 验证调用方服务的真实性和合法性。
    *   **用户会话验证**: 调用`user-core-service`的内省接口验证用户会话是否有效，且属于目标`userId`。
    *   **授权策略检查 (ACL)**: 验证调用方服务是否有权为该用户执行该操作。

#### 4.3. DEK明文供应 (备用API模式)
*   **FR4.3.1 (获取DEK API)**: **仅在性能要求极高、无法接受加解密代理模式网络开销的场景下**，系统可提供一个`RequestDecryptedUserDek`接口。
*   **FR4.3.2 (风险与控制)**: 此接口的调用权限必须被严格限制到极少数服务。返回的明文DEK必须设置一个极短的生命周期，并由调用方服务负责在内存中安全处理和立即销毁。**所有对此接口的调用都必须触发高优先级安全告警。**

#### 4.4. 密钥轮换与数据重加密
*   **FR4.4.1 (用户DEK轮换)**:
    *   提供API供用户触发为其生成新版本的DEK。
    *   新生成的DEK（如`v2`）被标记为`ACTIVE`，旧的DEK（`v1`）被标记为`LEGACY`。
    *   `LEGACY`密钥只能用于解密，不能用于加密新数据。
*   **FR4.4.2 (数据重加密协调)**:
    *   当`personal-kb-service`等服务发现一个用`LEGACY`密钥加密的数据时，它应在解密后，使用`ACTIVE`的新密钥重新加密，并更新数据。
    *   这是一个由上层业务服务驱动的、渐进式的重加密过程。

#### 4.5. 灾难恢复
*   **FR4.5.1 (密钥备份)**: `WrappedDEK`作为数据库的一部分进行常规备份。
*   **FR4.5.2 (MEK可恢复性)**: 整个系统的可恢复性依赖于底层云KMS对MEK的保护和可用性。安全团队必须确保MEK本身是可用的，且访问策略在灾难恢复场景下是可行的。

#### 4.6. 审计与日志
*   **FR4.6.1 (不可篡改日志)**: 所有对密钥的操作（生成、包装、解包、轮换、销毁）都必须被记录到专门的、防篡改的安全审计日志中（如写入到Write-Once-Read-Many (WORM)存储）。
*   **FR4.6.2 (日志内容)**: 审计日志必须包含：时间戳、操作类型、目标`userId`、请求方服务ID、请求方IP、操作状态（成功/失败）、以及相关的请求ID和追踪ID。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC API接口 (S2S)
*   **Package**: `hina.vip.kms_proxy.v1`
*   **认证**: mTLS + 服务级JWT。
*   **核心RPC**:
    ```protobuf
    service KeyManagementProxyService {
      // 首选、最安全的接口
      rpc EncryptData(EncryptDataRequest) returns (EncryptDataResponse);
      rpc DecryptData(DecryptDataRequest) returns (DecryptDataResponse);
      
      // 受限的、高风险的备用接口
      rpc RequestDecryptedUserDek(RequestDecryptedUserDekRequest) returns (RequestDecryptedUserDekResponse);
      
      // 管理接口
      rpc ProvisionUserDek(ProvisionUserDekRequest) returns (google.protobuf.Empty);
      rpc RotateUserDek(RotateUserDekRequest) returns (google.protobuf.Empty);
    }

    message EncryptDataRequest {
      AuthContext auth_context = 1;
      bytes plaintext_data = 2;
      int32 dek_version_to_use = 3; // 0 for active version
    }
    message EncryptDataResponse {
      bytes encrypted_data_blob = 1; // 包含密文、IV、AuthTag等
      int32 dek_version_used = 2;
    }
    // ... 其他请求/响应定义
    ```

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`user_wrapped_deks`**:
    *   `user_id (UUID)`
    *   `dek_version (INT)`
    *   `wrapped_dek (BYTEA)`: 加密后的DEK。
    *   `master_key_id (VARCHAR)`: 使用的MEK的标识符。
    *   `status (VARCHAR)`: `ACTIVE`, `LEGACY`, `COMPROMISED`.
    *   PRIMARY KEY (`user_id`, `dek_version`)
*   **`key_access_audit_logs`**: (或发送到专门的日志系统)
    *   `id`, `timestamp`, `trace_id`, `action`, `status`, `target_user_id`, `requesting_service_id`, `source_ip`, `error_details`, `metadata_jsonb`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: 加解密代理API的P99延迟应 `< 50ms`（主要取决于KMS的响应速度）。
*   **吞吐量**: 能够处理所有依赖加解密操作的服务发来的请求总和。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.99%。本服务的故障将导致所有依赖E2EE的功能完全不可用。必须高可用部署（多可用区）。
*   **KMS依赖**: 对外部KMS的调用失败必须有容错和重试机制，并触发最高优先级告警。

#### 7.3. 可扩展性需求
*   服务本身无状态，易于水平扩展。数据库负载通常不大，但需要高可用。

#### 7.4. 安全性需求 (最高优先级)
*   **零信任架构**: 不信任任何网络内部或外部的调用，所有请求都必须经过严格认证和授权。
*   **防内部威胁**: 最小化可访问此服务的人员和系统。所有操作必须留下不可篡改的审计日志。
*   **代码安全**: 必须经过最严格的代码审查和静态/动态安全测试。使用的加密库必须是经过验证的、无已知漏洞的安全版本。
*   **物理安全**: 依赖云KMS/HSM提供的硬件层面的保护。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **底层KMS**: 必须使用云提供商的KMS（AWS KMS, Google Cloud KMS）或自建的HashiCorp Vault。**绝不能在代码或普通数据库中自行实现MEK的管理**。
*   **服务间通信**: **必须使用gRPC + mTLS**。
*   **配置管理**: 所有敏感配置（如KMS的MEK ID、S2S认证凭证）必须从安全的Secrets Manager（如Vault, AWS Secrets Manager）注入，而非环境变量文件。

---
这份版本2.0的SRS文档为`key-management-proxy-service`构建了一个满足生产级、金融级安全要求的隐私保护基石。其核心在于通过**信封加密代理**和**严格的访问控制**，将密钥管理的复杂性和风险集中到一个高度受控和可审计的服务中，从而赋能其他服务实现强大的应用层加密。