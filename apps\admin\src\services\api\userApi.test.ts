/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:25:00
 * Modified: 2025-01-23 18:25:00
 */

import { describe, it, expect, beforeAll, afterEach, afterAll } from 'vitest';
import { UserApiService } from './userApi';
import { server } from '@/mocks/server';
import { http, HttpResponse } from 'msw';
import { User, UserRole, UserStatus, Permission } from '@/types/user';

// Establish API mocking before all tests.
beforeAll(() => server.listen());
// Reset any request handlers that we may add during the tests,
// so they don't affect other tests.
afterEach(() => server.resetHandlers());
// Clean up after the tests are finished.
afterAll(() => server.close());

const API_BASE_URL = '/api/v1/users';

const mockUsers: User[] = [
    {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      avatar: '',
      status: UserStatus.ACTIVE,
      roles: [UserRole.ADMIN],
      permissions: [Permission.USER_VIEW],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
      emailVerified: true,
      phoneVerified: false,
      twoFactorEnabled: false,
    },
  ];

describe('UserApiService', () => {
  describe('getUsers', () => {
    it('should return a list of users on success', async () => {
      server.use(
        http.get(API_BASE_URL, () => {
          return HttpResponse.json({ data: mockUsers, total: mockUsers.length });
        })
      );

      const response = await UserApiService.getUsers({});
      expect(response.data).toEqual(mockUsers);
      expect(response.total).toBe(mockUsers.length);
    });

    it('should throw an error on failure', async () => {
        server.use(
            http.get(API_BASE_URL, () => {
              return new HttpResponse(null, { status: 500 });
            })
          );

      await expect(UserApiService.getUsers({})).rejects.toThrow();
    });
  });

  describe('createUser', () => {
    it('should return the new user on success', async () => {
      const newUserRequest = { email: '<EMAIL>', username: 'newuser' };
      const expectedUser = { ...newUserRequest, id: 'new-id' };
      
      server.use(
        http.post(API_BASE_URL, async () => {
          return HttpResponse.json({ data: expectedUser });
        })
      );

      const response = await UserApiService.createUser(newUserRequest as any);
      expect(response).toEqual(expectedUser);
    });

    it('should throw an error on failure', async () => {
        server.use(
            http.post(API_BASE_URL, () => {
              return new HttpResponse(null, { status: 500 });
            })
          );
      await expect(UserApiService.createUser({} as any)).rejects.toThrow();
    });
  });
}); 