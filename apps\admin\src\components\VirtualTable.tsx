/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 17:15:00
 * Modified: 2025-01-23 17:15:00
 */

import React, { useState, useEffect, useRef } from 'react';
import { Table } from 'antd';
import { VariableSizeGrid as Grid } from 'react-window';
import { Resizable } from 'react-resizable';
import { TableProps } from 'antd/es/table';

// Resizable antd table header
const ResizableTitle = (props: React.HTMLAttributes<any> & { onResize: () => void; width: number }) => {
  const { onResize, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

interface VirtualTableProps<T> extends TableProps<T> {
  columns: any[];
  scroll: { y: number; x: number | string };
}

// Virtualized antd table component
export const VirtualTable = <T extends {}>(props: VirtualTableProps<T>) => {
  const { columns, scroll } = props;
  const [tableWidth, setTableWidth] = useState(0);

  const widthColumnCount = columns.filter(({ width }) => !width).length;
  const mergedColumns = columns.map((column) => {
    if (column.width) {
      return column;
    }
    return {
      ...column,
      width: Math.floor(tableWidth / widthColumnCount),
    };
  });

  const gridRef = useRef<Grid>(null);
  const [connectObject] = useState<any>(() => {
    const obj = {};
    Object.defineProperty(obj, 'scrollLeft', {
      get: () => null,
      set: (scrollLeft: number) => {
        if (gridRef.current) {
          gridRef.current.scrollTo({ scrollLeft });
        }
      },
    });
    return obj;
  });

  const resetVirtualGrid = () => {
    gridRef.current?.resetAfterIndices({
      columnIndex: 0,
      shouldForceUpdate: true,
    });
  };

  useEffect(resetVirtualGrid, [tableWidth]);

  const renderVirtualList: React.FC<any> = (rawData, { scrollbarSize }) => {
    resetVirtualGrid();
    const totalHeight = rawData.length * 54;

    return (
      <Grid
        ref={gridRef}
        className="virtual-grid"
        columnCount={mergedColumns.length}
        columnWidth={(index: number) => {
          const { width } = mergedColumns[index];
          return totalHeight > scroll.y && index === mergedColumns.length - 1
            ? (width as number) - scrollbarSize - 1
            : (width as number);
        }}
        height={scroll.y as number}
        rowCount={rawData.length}
        rowHeight={() => 54}
        width={tableWidth}
      >
        {({ columnIndex, rowIndex, style }: { columnIndex: number; rowIndex: number; style: React.CSSProperties }) => {
          const record = rawData[rowIndex] as T;
          const column = mergedColumns[columnIndex];
          const cellContent = (record as any)[column.dataIndex] || (column.render ? column.render((record as any)[column.dataIndex], record, rowIndex) : null);
          
          return (
            <div
              className={`virtual-table-cell ${columnIndex === mergedColumns.length - 1 ? 'virtual-table-cell-last' : ''}`}
              style={{ ...style, display: 'flex', alignItems: 'center', padding: '0 8px' }}
            >
              {cellContent}
            </div>
          )
        }}
      </Grid>
    );
  };

  return (
    <Table
      {...props}
      className="virtual-table"
      columns={mergedColumns}
      pagination={false}
      components={{
        header: {
          cell: ResizableTitle,
        },
        body: renderVirtualList,
      }}
    />
  );
}; 