﻿好的，遵照您的指示，我们来生成一份为 `shared-kb-service` (共享/商业知识库服务 - CKB) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **内容模型与多模态**: 引入更丰富的、结构化的内容模型，明确支持富文本、图片、视频、代码块等多种内容块，并与`file-storage-service`等深度集成。
2.  **策展与工作流**: 详细定义内容的策展工作流，包括草稿、审核、发布、版本控制和回滚，为专业内容生产提供支持。
3.  **商业化与访问控制**: 细化与`billing-service`的集成，支持基于订阅、按次付费、以及（未来）基于Token消耗的精细化访问控制。
4.  **AI集成与增强**: 明确AI助手如何查询、引用CKB内容，并引入AI能力，自动为知识条目生成摘要、翻译和关联标签。
5.  **分析与反馈**: 增加对内容消费数据的分析，以及用户对知识条目反馈（“有用/无用”）的收集。
6.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义和事件契约，并对数据模型进行优化。
7.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、内容结构化、商业模式灵活，且能作为平台权威知识与价值高地的知识库系统。

---

### CINA.CLUB - shared-kb-service 需求规格说明书

**版本: 2.0 (生产级定义，集成策展工作流与高级商业化)**  
**发布日期: 2025-06-25**  
**最后修订日期: 2025-06-25**  
**文档负责人:** [内容平台/知识管理团队负责人名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台不仅连接人与服务，也致力于成为一个知识分享和价值创造的中心。`shared-kb-service` 的目的在于构建一个**高质量、可信赖、结构化的共享知识库 (CKB)**，内容由平台官方、认证的领域专家或商业合作伙伴提供。这些知识旨在赋能用户解决更专业、更深度的问题，并为AI助手提供**权威的、可引用的信息源**，同时为平台探索可持续的内容商业化模式。

#### 1.2. 服务范围
本服务 **负责**:
*   **知识条目(CKB Item)管理**:
    *   支持**多模态、结构化内容**（富文本、代码、视频、图片）的创建和管理。
    *   实现专业的**内容策展工作流**，包括草稿、审核、发布、版本化和回滚。
*   **分类与标签管理**: 管理CKB内容的层级分类和标签体系。
*   **商业化与访问控制**:
    *   与`billing-service`深度集成，实现基于**订阅等级**、**按次付费**和**按Token消耗**的精细化访问控制。
    *   为AI引用分成提供数据支持。
*   **AI增强**:
    *   （异步）调用`ai-assistant-service`为知识条目**自动生成摘要、翻译和推荐标签**。
*   **搜索与发现**:
    *   为CKB内容生成嵌入向量和全文索引，并发布事件供`search-indexer-service`消费。
*   **用户反馈与分析**: 收集用户对知识条目的“有用/无用”反馈，并记录内容的消费数据。

本服务 **不负责**:
*   **用户个人知识库(PKB)** (由 `personal-kb-service` 负责)。
*   **核心的计费和订阅逻辑** (由 `billing-service` 负责)。
*   **文件二进制数据的存储** (由 `file-storage-service` 负责)。
*   **处理搜索查询** (由 `search-service` 负责)。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`shared-kb-service` 是CINA.CLUB平台的**内容价值高地**和**权威知识资产库**。它为平台提供了权威、深度、结构化的信息，不仅提升了用户体验和AI助手的智能水平，也构成了平台内容商业化和知识付费的基础。它是一个内容管理、访问控制、智能检索和商业化策略高度集成的复杂系统。

#### 2.2. 主要功能概述
*   支持多模态内容的、专业的策展与版本控制工作流。
*   与计费系统深度集成的、灵活的多模式访问控制。
*   AI驱动的内容摘要、翻译和标签增强。
*   为混合搜索提供高质量的、可索引的数据。
*   用户反馈闭环与消费数据分析。

### 3. 核心流程图

#### 3.1. 用户访问付费知识条目的流程
```mermaid
sequenceDiagram
    participant Client
    participant SharedKBService as CKB
    participant BillingService as BS
    participant PaymentService as PS

    Client->>CKB: 1. GET /items/{itemId} (requesting full content)
    CKB->>DB: 2. Get item's metadata (access_tier: "PREMIUM")
    
    CKB->>BS: 3. **[Access Check]** POST /internal/access-check (userId, required_tier: "PREMIUM")
    
    alt User has valid subscription
        BS-->>CKB: 4a. { granted: true }
        CKB->>DB: 5a. Get full content for the item
        CKB-->>Client: 6a. 200 OK (Return full content)
    else User needs to pay
        BS-->>CKB: 4b. { granted: false, reason: "PAYMENT_REQUIRED", price: 10.0 }
        CKB-->>Client: 4c. **402 Payment Required** { purchase_info: {...} }
        
        Note over Client: User confirms one-time purchase
        
        Client->>BS: 5b. Initiate payment for the item
        BS->>PS: 6b. (Handle payment flow...)
        PS-->>BS: (Payment Succeeded)
        
        Note over Client: Client retries the original request
        
        Client->>CKB: 7b. GET /items/{itemId}
        CKB->>BS: 8b. **[Access Check]**
        BS-->>CKB: 9b. { granted: true } (BS now knows user has paid)
        CKB-->>Client: 10b. 200 OK (Return full content)
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 结构化内容与策展
*   **FR4.1.1 (块编辑器模型)**: 内容主体必须以**块(Block)**的数组形式存储（JSONB），类似于Notion或WordPress Gutenberg。
    *   **支持的Block类型**: `PARAGRAPH`, `HEADING`, `IMAGE`, `VIDEO_EMBED`, `CODE_BLOCK`, `QUOTE`.
*   **FR4.1.2 (策展工作流)**: 条目必须有清晰的生命周期状态：`DRAFT`, `PENDING_REVIEW`, `PUBLISHED`, `NEEDS_UPDATE`, `ARCHIVED`。只有具有`CURATOR`或`ADMIN`角色的用户才能将条目状态从`PENDING_REVIEW`变更为`PUBLISHED`。
*   **FR4.1.3 (版本控制)**: 对已发布的CKB条目的所有重要修改都必须创建一个新的版本。系统必须记录版本历史，并支持比较和一键回滚。

#### 4.2. 商业化与访问控制
*   **FR4.2.1 (访问策略)**: 每个CKB条目或分类必须能关联一个灵活的访问策略：
    *   `FREE`: 所有人可读。
    *   `PREMIUM_SUBSCRIBER_ONLY`: 需要用户拥有有效的、特定等级的会员订阅。
    *   `ONE_TIME_PURCHASE`: 需要用户为该条目单独付费。
    *   `TOKEN_BASED`: （高级）访问内容会消耗用户的AI Token（或一种专门的“阅读Token”）。
*   **FR4.2.2 (访问检查)**: 在用户请求访问一个CKB条目的完整内容前，本服务**必须**调用`billing-service`的`access-check`接口进行权限校验。
*   **FR4.2.3 (用量上报)**: 对于`TOKEN_BASED`的条目，在用户成功访问后，必须调用`billing-service`的`usage/record`接口，上报本次内容的消耗量。

#### 4.3. AI集成与增强
*   **FR4.3.1 (AI摘要与标签)**: 在条目创建或更新后，系统必须异步触发一个任务，调用`ai-assistant-service`为内容生成摘要、提取关键词，并推荐相关标签。
*   **FR4.3.2 (AI翻译)**: （可选）对于发布的条目，可以触发AI翻译任务，生成多语言版本。
*   **FR4.3.3 (AI知识源)**: `ai-assistant-service`在回答问题时，优先检索CKB。本服务的`internal/search`接口必须为AI优化，返回结构化的、便于引用的内容片段。

#### 4.4. 用户反馈与分析
*   **FR4.4.1 (反馈收集)**: 用户可以对每个CKB条目进行“有用”/“无用”的投票。
*   **FR4.4.2 (数据分析)**: 系统必须记录每个条目的详细消费数据，如总浏览量、独立访客数、付费转化率、平均阅读时长等，并将这些数据发送到`analytics-service`。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部/客户端gRPC API接口
*   **Package**: `hina.vip.knowledge.v1`
*   **认证**: Public GET接口可匿名（但返回受限内容）。需要完整内容或个性化结果时需用户JWT。Admin/Curator接口需相应角色JWT。
*   **核心RPC**:
    ```protobuf
    service SharedKBService {
      // Public/User Facing
      rpc GetItem(GetItemRequest) returns (GetItemResponse);
      rpc SearchItems(SearchItemsRequest) returns (SearchItemsResponse);
      rpc PostFeedback(PostFeedbackRequest) returns (google.protobuf.Empty);
      
      // Admin/Curator Facing
      rpc CreateItem(CreateItemRequest) returns (Item);
      rpc UpdateItem(UpdateItemRequest) returns (Item);
      rpc ListItemVersions(ListItemVersionsRequest) returns (ListItemVersionsResponse);
      rpc PublishItem(PublishItemRequest) returns (Item);
      
      // Internal S2S
      rpc InternalSearchForAI(InternalSearchRequest) returns (InternalSearchResponse);
    }
    
    message GetItemResponse {
      Item item = 1;
      bool full_content_accessible = 2;
      PurchaseInfo required_purchase_info = 3; // if not accessible
    }
    ```

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`ckb_items`**: `id`, `author_id`, `title`, `slug`, `status`, `access_policy (JSONB)`.
*   **`ckb_item_versions`**:
    *   `id (PK)`, `item_id (FK)`, `version_number (INT)`
    *   `content_blocks (JSONB)`: 存储块编辑器结构的数组。
    *   `ai_summary`, `ai_keywords`
    *   `editor_id`, `created_at`, `commit_message`.
*   **`ckb_categories`**, **`ckb_tags`**.
*   **`ckb_feedback`**: `(user_id, item_id)` (PK), `is_helpful (BOOLEAN)`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**:
    *   获取公共条目（摘要）P99 < 150ms。
    *   获取付费条目（含权限检查）P99 < 250ms。
*   **搜索延迟**: P95 < 400ms (依赖`search-service`)。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **访问控制**: 与`billing-service`的集成必须可靠，访问控制绝不能出错。

#### 7.3. 可扩展性需求
*   服务可水平扩展。数据库可通过读写分离和分区扩展。

#### 7.4. 安全性需求
*   **访问控制**: 严格执行基于`billing-service`结果的访问控制，是核心安全要求。
*   **内容安全**: 所有提交的内容都需经过审核。
*   **Admin API**: 严格的RBAC保护。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。其强大的事务能力和JSONB字段非常适合处理结构化内容和灵活的访问策略。
*   **搜索**: 必须与`search-service`和`search-indexer-service`深度集成。本服务负责发布内容变更事件。
*   **异步处理**: AI增强、翻译、索引更新等所有耗时操作，都应通过后台任务队列进行异步处理。

---
这份版本2.0的SRS文档为`shared-kb-service`构建了一个企业级、支持复杂商业模式的知识管理与分发平台。它通过将专业的内容策展工作流、灵活的访问控制和AI能力相结合，旨在将平台的知识资产价值最大化。