/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

/**
 * API响应的通用格式
 */
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
  timestamp: string
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * API错误响应
 */
export interface ApiError {
  success: false
  message: string
  code: number
  details?: Record<string, any>
  timestamp: string
}

/**
 * 排序参数
 */
export interface SortParam {
  field: string
  order: 'asc' | 'desc'
}

/**
 * 过滤参数
 */
export interface FilterParam {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'like' | 'between'
  value: any
}

/**
 * 查询参数
 */
export interface QueryParams extends PaginationParams {
  search?: string
  filters?: FilterParam[]
  sorts?: SortParam[]
}

/**
 * 批量操作请求
 */
export interface BatchRequest {
  ids: string[]
  action: string
  params?: Record<string, any>
}

/**
 * 批量操作响应
 */
export interface BatchResponse {
  total: number
  success: number
  failed: number
  errors: Array<{
    id: string
    error: string
  }>
}

/**
 * 导出请求参数
 */
export interface ExportParams {
  format: 'xlsx' | 'csv' | 'pdf'
  filters?: FilterParam[]
  fields?: string[]
  filename?: string
}

/**
 * 导入响应
 */
export interface ImportResponse {
  total: number
  success: number
  failed: number
  errors: Array<{
    row: number
    field: string
    error: string
  }>
}

/**
 * 统计数据
 */
export interface StatisticsData {
  key: string
  value: number
  label: string
  trend?: number
  percentage?: number
}

/**
 * 时间序列数据点
 */
export interface TimeSeriesPoint {
  timestamp: string
  value: number
  label?: string
}

/**
 * 图表数据
 */
export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'area'
  title: string
  data: TimeSeriesPoint[] | StatisticsData[]
  xAxis?: string
  yAxis?: string
} 