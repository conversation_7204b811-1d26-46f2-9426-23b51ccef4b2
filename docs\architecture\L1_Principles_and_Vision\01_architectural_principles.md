# CINA.CLUB - 核心架构原则

**文档状态**: 已采纳 (Accepted)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27

## 1. 引言

本文档定义了CINA.CLUB平台在技术架构层面必须遵守的一系列核心原则。这些原则是所有架构决策、技术选型和编码实践的最高指导方针。它们旨在确保平台在快速迭代的同时，保持技术体系的**一致性、可扩展性、可靠性、安全性和可维护性**。

任何偏离这些原则的技术决策，都必须通过创建一份架构决策记录（ADR）并获得架构委员会的批准。

---

## 2. 核心原则详解

### Principle #1: Go-Centric 全栈协同 (Go-Centric Stack Synergy)

*   **原则**: **Go语言是整个平台技术栈的核心和首选语言**。后端微服务**必须**使用Go编写。对于前端，最关键的、性能敏感的或安全要求高的核心逻辑（如E2EE加密、数据同步协议），**必须**也使用Go编写，并通过Go Mobile/WASM编译后供前端调用。
*   **理由**:
    *   **统一技术栈**: 降低团队成员跨前后端核心逻辑的认知负荷，便于知识共享和人员流动。
    *   **极致性能**: 将计算密集型任务从JS/TS转移到Go，充分利用Go的并发模型和执行效率，为用户提供最佳性能体验。
    *   **类型安全与可靠性**: 实现了从后端到前端核心逻辑的端到端强类型保障，减少了运行时错误。
    *   **代码复用**: 实现了“一次编写，多端运行”的终极目标，确保了加密等关键算法在所有平台上的行为完全一致。

### Principle #2: API契约驱动 (API Contract-Driven)

*   **原则**: **所有服务间通信和客户端-服务器通信的接口，都必须通过Protocol Buffers (Protobuf)进行定义**。`/core/api`目录是平台API契约的唯一事实来源。
*   **理由**:
    *   **强类型契约**: 保证了在编译时就能发现接口不匹配的问题。
    *   **语言无关性**: 支持为Go, TypeScript, Python等多种语言自动生成类型安全的代码。
    *   **向后兼容**: Protobuf的字段编号机制为API的平滑演进提供了坚实基础。
    *   **自文档化**: `.proto`文件本身就是清晰、易读的接口文档。

### Principle #3: 事件驱动与异步优先 (Event-Driven & Async-First)

*   **原则**: **微服务间的解耦优先采用异步的、事件驱动的模式**。Kafka是平台的中央事件总线。对于需要立即响应的请求-响应模式，才使用同步的gRPC调用。
*   **理由**:
    *   **高可用性与韧性**: 当一个服务不可用时，不会因为同步调用而导致整个请求链路失败。事件可以被暂存，待服务恢复后处理。
    *   **可扩展性**: 生产者和消费者可以独立扩展，以应对流量洪峰。
    *   **松耦合**: 服务之间无需知道彼此的网络位置或具体实现，只需关注它们产生和消费的事件即可。

### Principle #4: 模块化与微服务 (Modular & Microservices)

*   **原则**: **后端按业务领域划分为高内聚、低耦合的微服务。前端按功能和职责划分为可复用的模块化包**。Monorepo是实现这一原则的组织形式。
*   **理由**:
    *   **独立部署与演进**: 每个服务或模块都可以独立开发、测试、部署和扩展。
    *   **技术异构性**: (虽然我们主张Go-Centric) 在必要时，允许特定服务（如Python AI Worker）使用最适合其任务的技术栈。
    *   **团队自治**: 促进小型、专注的团队对特定业务领域拥有所有权。

### Principle #5: 共享核心，而非共享数据库 (Shared Core, Not Shared Database)

*   **原则**: **服务之间严禁共享数据库**。所有跨服务的代码复用，都必须通过共享库（`/core`, `/pkg`）的方式实现。
*   **理由**:
    *   **数据所有权**: 每个服务都是其自身数据的唯一所有者和管理者，这保证了数据的封装和一致性。
    *   **避免紧耦合**: 共享数据库是微服务架构中最常见的反模式，它会导致服务之间产生隐式的、难以管理的紧耦合。
    *   **可维护性**: 通过共享库复用代码，逻辑清晰，更新和测试都集中在一处。

### Principle #6: 隐私与安全内建 (Privacy & Security by Design)

*   **原则**: **安全和隐私不是事后添加的功能，而是架构设计的第一等公民**。用户的敏感数据默认采用端到端加密(E2EE)或应用层加密(ALE)。
*   **理由**:
    *   **建立用户信任**: 这是CINA.CLUB平台的核心价值主张。
    *   **降低数据泄露风险**: 即使在数据库被攻破的极端情况下，用户的核心隐私内容依然是安全的。
    *   **合规性**: 满足GDPR等全球数据保护法规的严格要求。

### Principle #7: 可观测性优先 (Observability First)

*   **原则**: **任何新功能或服务的开发，都必须将日志、指标和追踪作为其交付的一部分**。如果一个功能无法被有效观测，那么它就是未完成的。
*   **理由**:
    *   **快速故障排查**: 在复杂的分布式系统中，没有端到端的可观测性，定位问题将是一场噩梦。
    *   **性能优化**: 通过指标和追踪数据，可以精确找到系统瓶颈。
    *   **业务洞察**: 结构化日志是进行数据分析和获取业务洞察的基础。

### Principle #8: 基础设施即代码 (Infrastructure as Code - IaC)

*   **原则**: **所有基础设施资源（网络、服务器、数据库、K8s集群）和应用部署，都必须通过声明式的代码（Terraform, Kubernetes YAML）进行管理**。`/infra`目录是基础设施的唯一事实来源。
*   **理由**:
    *   **可重复性与一致性**: 能够一键创建出与生产环境完全一致的Staging或开发环境。
    *   **版本控制与审计**: 对基础设施的所有变更都通过代码审查进行，有完整的历史记录。
    *   **自动化**: IaC是实现CI/CD和自动化运维的先决条件。
    *   **灾难恢复**: 能够在数小时内在新区域重建整个平台。

---

## 3. 反模式 (Anti-Patterns)

以下是CINA.CLUB平台在开发过程中应极力避免的反模式：

*   **共享数据库 (Shared Database)**: 严禁不同微服务直接读写同一个数据库。
*   **同步一切 (Synchronous Everything)**: 滥用同步gRPC调用，形成脆弱、冗长的服务调用链。
*   **胖客户端/瘦服务端 (Fat Client / Anemic Server)**: 将过多的业务逻辑放在前端，后端只作为简单的CRUD接口。
*   **分布式单体 (Distributed Monolith)**: 微服务之间存在循环依赖或过度的同步调用，导致一个服务的变更或故障会连锁引发多个服务的问题。
*   **雪花服务器 (Snowflake Servers)**: 手动登录服务器进行配置修改，导致环境不一致和变更不可追溯。
*   **日志即字符串 (Logging as Strings)**: 记录非结构化的、难以解析的日志信息。
*   **在代码中硬编码配置**: 将地址、密钥等配置信息硬编码在代码中，而不是通过配置文件或环境变量注入。