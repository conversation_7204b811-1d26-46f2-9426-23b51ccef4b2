# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

terraform {
  required_version = ">= 1.5"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.20"
    }
  }

  backend "s3" {
    bucket         = "cina-club-terraform-state-prod"
    key            = "prod/terraform.tfstate"
    region         = "us-west-2"
    dynamodb_table = "cina-club-terraform-locks"
    encrypt        = true
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Environment = "prod"
      Project     = "cina-club"
      ManagedBy   = "terraform"
    }
  }
}

# VPC Module
module "vpc" {
  source = "../../modules/vpc"

  environment = "prod"
  vpc_cidr    = "10.2.0.0/16"
  
  public_subnet_cidrs  = ["10.2.1.0/24", "10.2.2.0/24", "10.2.3.0/24"]
  private_subnet_cidrs = ["10.2.11.0/24", "10.2.12.0/24", "10.2.13.0/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = true
  
  tags = {
    Name = "cina-club-prod-vpc"
  }
}

# EKS Cluster Module
module "eks" {
  source = "../../modules/eks"

  environment = "prod"
  vpc_id      = module.vpc.vpc_id
  subnet_ids  = module.vpc.private_subnet_ids

  cluster_name    = "cina-club-prod"
  cluster_version = "1.28"

  node_groups = {
    main = {
      instance_types = ["m5.xlarge"]
      min_size       = 5
      max_size       = 50
      desired_size   = 10
    }
    spot = {
      instance_types = ["m5.large", "m5.xlarge"]
      min_size       = 2
      max_size       = 20
      desired_size   = 5
      capacity_type  = "SPOT"
    }
  }

  tags = {
    Name = "cina-club-prod-eks"
  }
}

# RDS PostgreSQL Module - Main Database
module "rds_main" {
  source = "../../modules/rds"

  environment = "prod"
  vpc_id      = module.vpc.vpc_id
  subnet_ids  = module.vpc.private_subnet_ids

  identifier = "cina-club-prod-main"
  engine     = "postgres"
  version    = "15.4"
  
  instance_class    = "db.r6g.large"
  allocated_storage = 500
  storage_encrypted = true
  multi_az         = true

  database_name = "cinaclub_prod"
  username      = "cinaadmin"
  
  backup_retention_period = 30
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  tags = {
    Name = "cina-club-prod-main-db"
  }
}

# Redis Cache Module
module "redis" {
  source = "../../modules/redis"

  environment = "prod"
  vpc_id      = module.vpc.vpc_id
  subnet_ids  = module.vpc.private_subnet_ids

  cluster_id              = "cina-club-prod-cache"
  node_type              = "cache.r6g.large"
  num_cache_nodes        = 3
  parameter_group        = "default.redis7"
  replication_group_id   = "cina-club-prod-redis"
  
  tags = {
    Name = "cina-club-prod-redis"
  }
} 