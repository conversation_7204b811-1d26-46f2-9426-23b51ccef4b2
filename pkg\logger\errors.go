/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package logger

import "errors"

// 日志配置相关错误
var (
	ErrMissingServiceName    = errors.New("service name is required")
	ErrMissingServiceVersion = errors.New("service version is required")
	ErrInvalidLogLevel       = errors.New("invalid log level, must be one of: debug, info, warn, error")
	ErrInvalidLogFormat      = errors.New("invalid log format, must be one of: json, text")
	ErrLoggerNotFound        = errors.New("logger not found in context")
)
