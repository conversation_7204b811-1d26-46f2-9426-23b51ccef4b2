/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package slice

// Unique removes duplicate elements from a slice, preserving the order of first occurrence.
// Returns a new slice with unique elements.
//
// Example:
//
//	unique := slice.Unique([]int{1, 2, 2, 3, 1, 4})     // returns [1, 2, 3, 4]
//	unique = slice.Unique([]string{"a", "b", "a", "c"}) // returns ["a", "b", "c"]
func Unique[T comparable](s []T) []T {
	if len(s) == 0 {
		return nil
	}

	seen := make(map[T]struct{}, len(s))
	result := make([]T, 0, len(s))

	for _, item := range s {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}

	return result
}

// Difference returns elements that are in the first slice but not in the second slice.
// The order of elements from the first slice is preserved.
//
// Example:
//
//	diff := slice.Difference([]int{1, 2, 3, 4}, []int{2, 4, 5})     // returns [1, 3]
//	diff = slice.Difference([]string{"a", "b", "c"}, []string{"b"}) // returns ["a", "c"]
func Difference[T comparable](s1, s2 []T) []T {
	if len(s1) == 0 {
		return nil
	}

	// Create a set from the second slice for O(1) lookup
	set2 := make(map[T]struct{}, len(s2))
	for _, item := range s2 {
		set2[item] = struct{}{}
	}

	result := make([]T, 0, len(s1))
	for _, item := range s1 {
		if _, exists := set2[item]; !exists {
			result = append(result, item)
		}
	}

	return result
}

// Intersection returns elements that are present in both slices.
// The order of elements from the first slice is preserved.
// Duplicates are removed from the result.
//
// Example:
//
//	intersection := slice.Intersection([]int{1, 2, 3, 4}, []int{2, 4, 5})     // returns [2, 4]
//	intersection = slice.Intersection([]string{"a", "b", "c"}, []string{"b", "c", "d"}) // returns ["b", "c"]
func Intersection[T comparable](s1, s2 []T) []T {
	if len(s1) == 0 || len(s2) == 0 {
		return nil
	}

	// Create a set from the second slice for O(1) lookup
	set2 := make(map[T]struct{}, len(s2))
	for _, item := range s2 {
		set2[item] = struct{}{}
	}

	// Track seen items to avoid duplicates in result
	seen := make(map[T]struct{})
	result := make([]T, 0, min(len(s1), len(s2)))

	for _, item := range s1 {
		if _, inS2 := set2[item]; inS2 {
			if _, alreadySeen := seen[item]; !alreadySeen {
				seen[item] = struct{}{}
				result = append(result, item)
			}
		}
	}

	return result
}

// Union returns elements that are present in either slice.
// Duplicates are removed from the result.
// The order preserves elements from the first slice first, then new elements from the second slice.
//
// Example:
//
//	union := slice.Union([]int{1, 2, 3}, []int{3, 4, 5})     // returns [1, 2, 3, 4, 5]
//	union = slice.Union([]string{"a", "b"}, []string{"b", "c"}) // returns ["a", "b", "c"]
func Union[T comparable](s1, s2 []T) []T {
	if len(s1) == 0 {
		return Unique(s2)
	}
	if len(s2) == 0 {
		return Unique(s1)
	}

	seen := make(map[T]struct{}, len(s1)+len(s2))
	result := make([]T, 0, len(s1)+len(s2))

	// Add elements from first slice
	for _, item := range s1 {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}

	// Add elements from second slice that aren't already present
	for _, item := range s2 {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}

	return result
}

// SymmetricDifference returns elements that are in either slice but not in both.
// This is equivalent to (s1 - s2) ∪ (s2 - s1).
//
// Example:
//
//	symDiff := slice.SymmetricDifference([]int{1, 2, 3}, []int{3, 4, 5})     // returns [1, 2, 4, 5]
//	symDiff = slice.SymmetricDifference([]string{"a", "b"}, []string{"b", "c"}) // returns ["a", "c"]
func SymmetricDifference[T comparable](s1, s2 []T) []T {
	diff1 := Difference(s1, s2)
	diff2 := Difference(s2, s1)
	return append(diff1, diff2...)
}

// IsSubset checks if s1 is a subset of s2 (all elements of s1 are in s2).
//
// Example:
//
//	isSubset := slice.IsSubset([]int{1, 2}, []int{1, 2, 3, 4})     // returns true
//	isSubset = slice.IsSubset([]string{"a", "b"}, []string{"a", "c"}) // returns false
func IsSubset[T comparable](s1, s2 []T) bool {
	if len(s1) > len(s2) {
		return false
	}

	set2 := make(map[T]struct{}, len(s2))
	for _, item := range s2 {
		set2[item] = struct{}{}
	}

	for _, item := range s1 {
		if _, exists := set2[item]; !exists {
			return false
		}
	}

	return true
}

// IsSuperset checks if s1 is a superset of s2 (all elements of s2 are in s1).
//
// Example:
//
//	isSuperset := slice.IsSuperset([]int{1, 2, 3, 4}, []int{1, 2})     // returns true
//	isSuperset = slice.IsSuperset([]string{"a", "c"}, []string{"a", "b"}) // returns false
func IsSuperset[T comparable](s1, s2 []T) bool {
	return IsSubset(s2, s1)
}

// Equal checks if two slices contain the same elements in the same order.
//
// Example:
//
//	equal := slice.Equal([]int{1, 2, 3}, []int{1, 2, 3})     // returns true
//	equal = slice.Equal([]string{"a", "b"}, []string{"b", "a"}) // returns false
func Equal[T comparable](s1, s2 []T) bool {
	if len(s1) != len(s2) {
		return false
	}

	for i, item := range s1 {
		if item != s2[i] {
			return false
		}
	}

	return true
}

// EqualUnordered checks if two slices contain the same elements, regardless of order.
// This is equivalent to checking if the two slices represent the same set.
//
// Example:
//
//	equal := slice.EqualUnordered([]int{1, 2, 3}, []int{3, 1, 2})     // returns true
//	equal = slice.EqualUnordered([]string{"a", "b"}, []string{"a", "b", "c"}) // returns false
func EqualUnordered[T comparable](s1, s2 []T) bool {
	if len(s1) != len(s2) {
		return false
	}

	counts1 := make(map[T]int)
	counts2 := make(map[T]int)

	for _, item := range s1 {
		counts1[item]++
	}

	for _, item := range s2 {
		counts2[item]++
	}

	if len(counts1) != len(counts2) {
		return false
	}

	for item, count := range counts1 {
		if counts2[item] != count {
			return false
		}
	}

	return true
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
