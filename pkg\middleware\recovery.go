/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package middleware

import (
	"context"
	"fmt"
	"log/slog"
	"runtime/debug"

	"cina.club/pkg/errors"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// RecoveryInterceptor 创建一个用于捕获 panic 的 gRPC 拦截器
// 这是拦截器链中的第一个（最外层），用于防止任何下游的 panic 导致服务崩溃
func RecoveryInterceptor(logger *slog.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		panicked := true

		defer func() {
			if r := recover(); r != nil || panicked {
				// 1. 记录带有完整堆栈的 ERROR 日志
				stackTrace := debug.Stack()
				logger.ErrorContext(ctx, "gRPC request panicked",
					slog.String("method", info.FullMethod),
					slog.Any("panic", r),
					slog.String("stack", string(stackTrace)),
				)

				// 2. 将 panic 转换为标准的 gRPC Internal 错误
				if r != nil {
					appErr := errors.New(errors.Internal, fmt.Sprintf("panic occurred: %v", r))
					err = status.Error(codes.Internal, appErr.Error())
				} else {
					// 如果没有恢复到 panic 但 panicked 仍为 true，说明有其他异常情况
					appErr := errors.New(errors.Internal, "unexpected error occurred")
					err = status.Error(codes.Internal, appErr.Error())
				}
			}
		}()

		// 调用下游处理器
		resp, err = handler(ctx, req)
		panicked = false // 如果 handler 正常返回，则标记为非 panic

		return resp, err
	}
}

// RecoveryStreamInterceptor 创建一个用于捕获 panic 的 gRPC 流拦截器
func RecoveryStreamInterceptor(logger *slog.Logger) grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) (err error) {
		panicked := true

		defer func() {
			if r := recover(); r != nil || panicked {
				// 1. 记录带有完整堆栈的 ERROR 日志
				stackTrace := debug.Stack()
				logger.ErrorContext(ss.Context(), "gRPC stream panicked",
					slog.String("method", info.FullMethod),
					slog.Any("panic", r),
					slog.String("stack", string(stackTrace)),
				)

				// 2. 将 panic 转换为标准的 gRPC Internal 错误
				if r != nil {
					appErr := errors.New(errors.Internal, fmt.Sprintf("panic occurred: %v", r))
					err = status.Error(codes.Internal, appErr.Error())
				} else {
					appErr := errors.New(errors.Internal, "unexpected error occurred")
					err = status.Error(codes.Internal, appErr.Error())
				}
			}
		}()

		// 调用下游处理器
		err = handler(srv, ss)
		panicked = false // 如果 handler 正常返回，则标记为非 panic

		return err
	}
}

// RecoveryOptions 恢复拦截器的配置选项
type RecoveryOptions struct {
	// LogStackTrace 是否记录完整的堆栈跟踪（默认：true）
	LogStackTrace bool
	// CustomRecoveryHandler 自定义的恢复处理函数
	CustomRecoveryHandler func(ctx context.Context, r interface{}) error
}

// DefaultRecoveryOptions 返回默认的恢复选项
func DefaultRecoveryOptions() *RecoveryOptions {
	return &RecoveryOptions{
		LogStackTrace: true,
	}
}

// RecoveryInterceptorWithOptions 创建一个带有自定义选项的恢复拦截器
func RecoveryInterceptorWithOptions(logger *slog.Logger, opts *RecoveryOptions) grpc.UnaryServerInterceptor {
	if opts == nil {
		opts = DefaultRecoveryOptions()
	}

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		panicked := true

		defer func() {
			if r := recover(); r != nil || panicked {
				// 使用自定义恢复处理器（如果提供）
				if opts.CustomRecoveryHandler != nil {
					err = opts.CustomRecoveryHandler(ctx, r)
					return
				}

				// 记录日志
				logFields := []slog.Attr{
					slog.String("method", info.FullMethod),
					slog.Any("panic", r),
				}

				if opts.LogStackTrace && r != nil {
					logFields = append(logFields, slog.String("stack", string(debug.Stack())))
				}

				logger.LogAttrs(ctx, slog.LevelError, "gRPC request panicked", logFields...)

				// 转换为 gRPC 错误
				if r != nil {
					appErr := errors.New(errors.Internal, fmt.Sprintf("panic occurred: %v", r))
					err = status.Error(codes.Internal, appErr.Error())
				} else {
					appErr := errors.New(errors.Internal, "unexpected error occurred")
					err = status.Error(codes.Internal, appErr.Error())
				}
			}
		}()

		// 调用下游处理器
		resp, err = handler(ctx, req)
		panicked = false

		return resp, err
	}
}
