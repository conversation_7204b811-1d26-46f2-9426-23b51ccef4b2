# CINA.CLUB BOM字符修复脚本
# 修复go.mod文件中的BOM字符问题

Write-Host "Starting BOM character fix for go.mod files..." -ForegroundColor Green

$fixedCount = 0
$serviceDirectories = Get-ChildItem -Path "services" -Directory

Write-Host "Found $($serviceDirectories.Count) service directories" -ForegroundColor Cyan

foreach ($serviceDir in $serviceDirectories) {
    $serviceName = $serviceDir.Name
    $servicePath = $serviceDir.FullName
    $goModPath = Join-Path $servicePath "go.mod"
    
    Write-Host "Processing $serviceName..." -ForegroundColor Yellow
    
    if (Test-Path $goModPath) {
        try {
            # Read file as bytes to check for BOM
            $bytes = [System.IO.File]::ReadAllBytes($goModPath)
            
            # Check for UTF-8 BOM (EF BB BF)
            if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
                Write-Host "  Found BOM character, removing..." -ForegroundColor Red
                
                # Remove BOM and write clean file
                $cleanBytes = $bytes[3..($bytes.Length-1)]
                [System.IO.File]::WriteAllBytes($goModPath, $cleanBytes)
                
                $fixedCount++
                Write-Host "  FIXED: Removed BOM from $serviceName" -ForegroundColor Green
            } else {
                # Still check if file has content issues
                $content = Get-Content $goModPath -Raw -ErrorAction SilentlyContinue
                if ([string]::IsNullOrWhiteSpace($content)) {
                    Write-Host "  Recreating empty go.mod..." -ForegroundColor Yellow
                    
                    # Create new go.mod without BOM
                    $newContent = "module cina.club/services/$serviceName`n`n"
                    $newContent += "go 1.22`n`n"
                    $newContent += "replace (`n"
                    $newContent += "    cina.club/core => ../../core`n"
                    $newContent += "    cina.club/pkg => ../../pkg`n"
                    $newContent += ")`n"
                    
                    # Write without BOM using UTF8NoBOM
                    $utf8NoBom = New-Object System.Text.UTF8Encoding $false
                    [System.IO.File]::WriteAllText($goModPath, $newContent, $utf8NoBom)
                    
                    $fixedCount++
                    Write-Host "  FIXED: Recreated go.mod for $serviceName" -ForegroundColor Green
                } else {
                    Write-Host "  OK: No BOM found in $serviceName" -ForegroundColor Gray
                }
            }
        }
        catch {
            Write-Host "  ERROR: Failed to process $serviceName - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  Creating missing go.mod..." -ForegroundColor Yellow
        
        # Create new go.mod without BOM
        $newContent = "module cina.club/services/$serviceName`n`n"
        $newContent += "go 1.22`n`n"
        $newContent += "replace (`n"
        $newContent += "    cina.club/core => ../../core`n"
        $newContent += "    cina.club/pkg => ../../pkg`n"
        $newContent += ")`n"
        
        # Write without BOM using UTF8NoBOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($goModPath, $newContent, $utf8NoBom)
        
        $fixedCount++
        Write-Host "  CREATED: New go.mod for $serviceName" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "=== BOM FIX SUMMARY ===" -ForegroundColor Blue
Write-Host "Total Services: $($serviceDirectories.Count)" -ForegroundColor Cyan
Write-Host "Fixed/Created: $fixedCount" -ForegroundColor Green

Write-Host ""
Write-Host "Now running go mod tidy for all services..." -ForegroundColor Yellow

# Run go mod tidy for all services
foreach ($serviceDir in $serviceDirectories) {
    $serviceName = $serviceDir.Name
    $servicePath = $serviceDir.FullName
    
    Write-Host "Running go mod tidy for $serviceName..." -ForegroundColor Gray
    
    $currentLocation = Get-Location
    Set-Location $servicePath
    
    try {
        go mod tidy 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  Tidy OK: $serviceName" -ForegroundColor Green
        } else {
            Write-Host "  Tidy WARN: $serviceName" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "  Tidy ERROR: $serviceName" -ForegroundColor Red
    }
    finally {
        Set-Location $currentLocation
    }
}

Write-Host ""
Write-Host "BOM fix completed! Run batch test to verify improvements." -ForegroundColor Green
Write-Host "Command: .\scripts\simple-batch-test.ps1" -ForegroundColor Cyan 