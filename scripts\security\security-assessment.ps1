# Cina.Club Security Assessment Automation Script
# Version: 1.0.0

# Configuration
$AssessmentVersion = "1.0.0"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$LogDir = ".\security-logs"
$ReportDir = ".\security-reports"

# Ensure log and report directories exist
New-Item -ItemType Directory -Force -Path $LogDir, $ReportDir | Out-Null

# Color codes for output
$Green = "`e[32m"
$Yellow = "`e[33m"
$Red = "`e[31m"
$Reset = "`e[0m"

# Function to perform initial system scan
function Invoke-SystemScan {
    Write-Host "${Yellow}[*] Starting System Vulnerability Scan${Reset}"
    
    # System information
    Get-ComputerInfo | Out-File "$LogDir\system_info_$Timestamp.log"
    
    # Network configuration
    Get-NetIPConfiguration | Out-File "$LogDir\network_config_$Timestamp.log"
    
    # Open ports
    Get-NetTCPConnection | Where-Object {$_.State -eq 'Listen'} | Out-File "$LogDir\open_ports_$Timestamp.log"
    
    Write-Host "${Green}[✓] System Scan Complete${Reset}"
}

# Function to check cryptographic configurations
function Invoke-CryptoCheck {
    Write-Host "${Yellow}[*] Analyzing Cryptographic Configurations${Reset}"
    
    # TLS/SSL Protocol Check
    Get-TlsSettings | Out-File "$LogDir\tls_settings_$Timestamp.log"
    
    # Certificate Store Analysis
    Get-ChildItem Cert:\LocalMachine\My | Out-File "$LogDir\certificate_store_$Timestamp.log"
    
    Write-Host "${Green}[✓] Cryptographic Configuration Analysis Complete${Reset}"
}

# Function to perform dependency vulnerability check
function Invoke-DependencyScan {
    Write-Host "${Yellow}[*] Scanning Project Dependencies${Reset}"
    
    # Go module dependency check
    try {
        $GoDependencies = go list -m all | Where-Object { $_ -notmatch "indirect" }
        $GoDependencies | Out-File "$LogDir\go_dependencies_$Timestamp.log"
    }
    catch {
        Write-Host "${Red}[!] Go dependency scan failed${Reset}"
    }
    
    Write-Host "${Green}[✓] Dependency Vulnerability Scan Complete${Reset}"
}

# Function to generate comprehensive security report
function New-SecurityReport {
    Write-Host "${Yellow}[*] Generating Security Assessment Report${Reset}"
    
    # Combine log files
    $ReportPath = "$ReportDir\security_assessment_$Timestamp.md"
    
    # Create report with headers
    @"
# Cina.Club Security Assessment Report
## Assessment Version: $AssessmentVersion
## Date: $Timestamp

## System Information
$(Get-Content "$LogDir\system_info_$Timestamp.log")

## Network Configuration
$(Get-Content "$LogDir\network_config_$Timestamp.log")

## Open Ports
$(Get-Content "$LogDir\open_ports_$Timestamp.log")

## TLS/SSL Settings
$(Get-Content "$LogDir\tls_settings_$Timestamp.log")

## Certificate Store
$(Get-Content "$LogDir\certificate_store_$Timestamp.log")

## Go Dependencies
$(Get-Content "$LogDir\go_dependencies_$Timestamp.log")
"@ | Out-File $ReportPath
    
    Write-Host "${Green}[✓] Security Assessment Report Generated${Reset}"
}

# Main execution function
function Invoke-SecurityAssessment {
    Clear-Host
    
    Write-Host "${Green}Cina.Club Security Assessment Automation${Reset}"
    Write-Host "-------------------------------------------"
    
    # Perform security checks
    Invoke-SystemScan
    Invoke-CryptoCheck
    Invoke-DependencyScan
    New-SecurityReport
    
    Write-Host "`n${Green}[✓] Security Assessment Complete${Reset}"
    Write-Host "Report available at: $ReportDir\security_assessment_$Timestamp.md"
}

# Run the main function
Invoke-SecurityAssessment

# Exit script
exit 0 