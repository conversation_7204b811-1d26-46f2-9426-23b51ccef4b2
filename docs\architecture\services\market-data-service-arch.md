好的，遵照您的指示。我将根据我们刚才讨论的、版本1.0的`market-data-service` SRS，为您生成一份专门针对 **`market-data-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将详细阐述其作为**平台行情数据中台**的技术实现细节，包括**后台数据拉取工作流、多源聚合与降级逻辑、高速缓存策略、以及API服务**的设计，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `market-data-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-28
**参考SRS**: `market-data-service-srs.md` (v1.0)
**核心架构**: 后台聚合拉取 + Redis高速缓存服务

## 1. 概述

`market-data-service` 的架构核心是**读写分离**和**缓存优先**。写路径（数据拉取与更新）与读路径（API查询）被彻底解耦，并通过Redis作为数据交换中心。这种设计旨在最大限度地保证**查询API的极低延迟和高可用性**，即使在上游数据源出现故障时也能提供服务（虽然数据可能暂时不是最新的）。

**核心技术挑战**:
1.  **数据源适配与聚合**: 如何优雅地处理来自多个异构API的数据，并将其标准化。
2.  **后台任务的可靠性**: 如何确保数据拉取任务能够稳定、准时地运行，并具备故障恢复能力。
3.  **缓存架构**: 如何设计Redis的Key和数据结构，以支持高效的批量查询和数据更新。
4.  **服务分离**: 如何将长时间运行的数据拉取Worker与提供在线API的Server进行物理和逻辑上的分离。

本架构通过**独立的后台Worker进程**和**只读的API Server**来应对这些挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (读写分离)

```mermaid
graph TD
    subgraph "外部数据源"
        A[CoinGecko API]
        B[CoinMarketCap API]
    end

    subgraph "Market Data Service (Backend Workers)"
        C[CronJob Scheduler] --> D{Data Fetcher Worker}
        D --> E{Provider Adapters<br/>(CoinGecko, CMC)}
        E --> A & B
        D --> F[Data Aggregator]
        F --> G[Redis Cache Writer]
        G -- "MSET prices" --> H[Redis]
        F -- "(Optional) Write history" --> I[PostgreSQL]
    end

    subgraph "Market Data Service (API Server)"
        J[Client App] --> K[gRPC API Server]
        K --> L[Redis Cache Reader]
        L -- "MGET prices" --> H
    end

    style D fill:#f9f,stroke:#333,stroke-width:2px
    style K fill:#b3ffb3,stroke:#333,stroke-width:2px
```
*   **写路径 (紫色)**: 由CronJob触发的`Data Fetcher Worker`负责所有的数据拉取、处理和写入Redis。
*   **读路径 (绿色)**: `API Server`是无状态的，它**只从Redis读取数据**，提供极速响应。

### 2.2 最终目录结构 (`services/market-data-service/`)

```
market-data-service/
├── cmd/
│   ├── server/
│   │   └── main.go             # ✨ API Server的启动入口 ✨
│   └── worker/
│       └── main.go             # ✨ Data Fetcher Worker的启动入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   └── handler.go      # gRPC Handler, 调用application/query_service
│   │   ├── cache/
│   │   │   ├── redis_reader.go # 实现了CacheReader接口
│   │   │   └── redis_writer.go # 实现了CacheWriter接口
│   │   └── provider/           # ✨ 数据源适配器 ✨
│   │       ├── interface.go    # 定义MarketDataProvider接口
│   │       ├── coingecko/
│   │       └── coinmarketcap/
│   ├── application/
│   │   ├── port/
│   │   │   ├── cache.go
│   │   │   └── provider.go
│   │   ├── query_service.go    # (API Server用) 处理读请求
│   │   └── worker_service.go   # (Worker用) 编排数据拉取流程
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── aggregator.go       # 数据聚合与清洗逻辑
├── go.mod
├── Dockerfile.server           # API Server的Dockerfile
└── Dockerfile.worker           # Worker的Dockerfile
```

---

## 3. 各核心组件深度解析

### 3.1 后台Worker (`cmd/worker/`)

这是数据的心脏，一个**独立部署、可独立扩展**的组件。

*   **触发方式**: 由**Kubernetes CronJob**配置，按固定周期（如每分钟）启动一个`worker` Pod。
*   **`worker/main.go`**:
    1.  初始化依赖（配置、Logger、Redis客户端、Provider客户端等）。
    2.  实例化`application.WorkerService`。
    3.  调用`workerService.FetchAndCacheAllMarketData(ctx)`。
    4.  执行完毕后，Pod自动退出。
*   **`application/worker_service.go`**:
    *   **`FetchAndCacheAllMarketData` method**:
        1.  **获取目标列表**: 从配置或数据库中获取需要拉取价格的代币列表（如Top 250）。
        2.  **并发拉取**: **并发地**从所有配置的主数据源(`Provider`)拉取数据。
            *   **容错**: 使用`errgroup`或类似模式。如果主`Provider`（如CoinGecko）失败，则记录错误，并从备用`Provider`（如CMC）拉取。
        3.  **聚合数据**: 将所有成功拉取到的数据，传递给`domain.Aggregator`。
        4.  **写入缓存**: 将聚合后的标准数据，通过`adapter.cache.CacheWriter`批量写入Redis。
        5.  (可选) **写入历史DB**: 将价格快照写入PostgreSQL。
*   **`adapter/provider/`**:
    *   `interface.go`: 定义`MarketDataProvider`接口，包含`GetPrices`, `GetTokenMetadata`等方法。
    *   每个子目录（如`coingecko/`）都实现了这个接口，封装了对特定外部API的HTTP调用、参数构造和响应解析。

### 3.2 API服务器 (`cmd/server/`)

这是一个典型的**无状态、只读**的微服务。

*   **`server/main.go`**:
    1.  初始化依赖（配置、Logger、Redis客户端等）。
    2.  实例化`application.QueryService`。
    3.  实例化`adapter.grpc.Handler`，并将`QueryService`注入。
    4.  启动gRPC服务器。
*   **`application/query_service.go`**:
    *   实现了所有面向客户端的查询逻辑。
    *   **所有方法都只与`adapter.cache.CacheReader`交互**。
    *   **`GetPrices(ctx, tokenIDs, currencies)` method**:
        1.  构造一批Redis keys (`market:price:bitcoin`, `market:price:ethereum`, ...)。
        2.  调用`cacheReader.MGet(keys)`进行一次批量查询。
        3.  解析返回的JSON字符串，并组装成Protobuf响应。
        4.  对于在Redis中找不到的稀有币种，可以直接返回空值或一个默认值，而不是回源到外部API，以保证API的低延迟承诺。

### 3.3 领域层 (`domain/`)

*   `domain/aggregator.go`:
    *   `Aggregator`负责核心的数据融合逻辑。
    *   `AggregatePrices(providerResults ...[]ProviderPriceData)`:
        1.  **ID标准化**: 将`"BTC"`和`"bitcoin"`都映射到内部统一的`token_id`。
        2.  **价格融合**: 如果多个源都提供了同一个币的价格，可以采取`avg()`（平均值）、`median()`（中位数，更能抵抗异常值）或基于数据源权重的加权平均。
        3.  **数据清洗**: 校验数据是否在合理范围内（如价格不能为负数）。

### 3.4 缓存设计 (Redis)

*   **Key设计**:
    *   **命名空间**: 所有Key都以`market:`作为前缀。
    *   **结构**: `market:{data_type}:{token_id}:{params}`。
    *   **示例**:
        *   `market:price:bitcoin`
        *   `market:info:ethereum`
        *   `market:chart:1d:tether`
*   **数据结构**:
    *   **价格/元数据**: **JSON字符串**。
        *   **理由**: 方便一次性获取一个代币的所有相关数据（多种法币价格、市值等），并且易于扩展（增加新字段无需改变数据结构）。使用`MSET`/`MGET`进行批量操作。
    *   **K线图数据**: **Sorted Set**。
        *   **Key**: `market:chart:1d:bitcoin`。
        *   **Score**: `Unix timestamp`。
        *   **Member**: `OHLCV`数据，序列化为JSON或自定义的紧凑格式。
        *   **查询**: 使用`ZRANGEBYSCORE`可以高效地获取一个时间范围内的K线数据。

## 4. 总结

本架构设计通过以下关键点，构建了一个生产级的`market-data-service`：
1.  **彻底的读写分离**: 通过独立的`worker`和`server`部署，确保了数据拉取的复杂性和延迟**绝对不会**影响在线查询API的性能和可用性。
2.  **缓存即数据库**: API服务将Redis视为其主数据源，从而实现了**毫秒级的API响应**。这是提供流畅用户体验的关键。
3.  **高可用设计**: 通过**多数据源聚合**和**故障切换**逻辑，极大地提高了数据的可靠性，即使单个外部数据源出现问题，服务依然可用。
4.  **可扩展性**: `worker`和`server`都可以根据各自的负载（CPU密集型vs请求密集型）进行独立的水平扩展。

这种架构不仅满足了SRS中定义的所有功能和非功能性需求，而且在性能、可靠性和成本控制方面都达到了生产级的最佳实践，能够为CINA.CLUB的Web3功能提供坚如磐石的数据支持。