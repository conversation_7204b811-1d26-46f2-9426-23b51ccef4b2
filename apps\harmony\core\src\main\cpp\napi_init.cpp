/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

#include <napi/native_api.h>
#include <hilog/log.h>
#include "include/core_go.h"

constexpr unsigned int LOG_DOMAIN = 0x3900;
constexpr char LOG_TAG[] = "CinaClubNAPI";

// 声明各模块的注册函数
extern napi_value RegisterCryptoFunctions(napi_env env, napi_value exports);
extern napi_value RegisterAICFunctions(napi_env env, napi_value exports);
extern napi_value RegisterDataSyncFunctions(napi_env env, napi_value exports);

/**
 * 初始化Go核心库
 */
napi_value InitializeGoCore(napi_env env, napi_callback_info info) {
    int result = GoInitializeCore();
    
    napi_value napiResult;
    napi_create_int32(env, result, &napiResult);
    
    if (result == 0) {
        OH_LOG_INFO(LOG_DOMAIN, LOG_TAG, "Go core initialized successfully");
    } else {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to initialize Go core, error code: %{public}d", result);
    }
    
    return napiResult;
}

/**
 * 清理Go核心库
 */
napi_value CleanupGoCore(napi_env env, napi_callback_info info) {
    GoCleanupCore();
    OH_LOG_INFO(LOG_DOMAIN, LOG_TAG, "Go core cleaned up");
    
    napi_value result;
    napi_get_undefined(env, &result);
    return result;
}

/**
 * 获取Go核心库的最后错误
 */
napi_value GetLastError(napi_env env, napi_callback_info info) {
    GoString errorMsg = GoGetLastError();
    
    napi_value result;
    if (errorMsg.p && errorMsg.n > 0) {
        napi_create_string_utf8(env, errorMsg.p, errorMsg.n, &result);
        GoFreeString(errorMsg);
    } else {
        napi_get_null(env, &result);
    }
    
    return result;
}

/**
 * 获取模块版本信息
 */
napi_value GetModuleVersion(napi_env env, napi_callback_info info) {
    napi_value version;
    napi_create_string_utf8(env, "1.0.0", NAPI_AUTO_LENGTH, &version);
    return version;
}

/**
 * NAPI模块初始化函数
 */
static napi_value Init(napi_env env, napi_value exports) {
    OH_LOG_INFO(LOG_DOMAIN, LOG_TAG, "Initializing CINA.CLUB Go Bridge NAPI module");

    // 注册核心管理函数
    napi_property_descriptor coreDesc[] = {
        {"initializeGoCore", nullptr, InitializeGoCore, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"cleanupGoCore", nullptr, CleanupGoCore, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getLastError", nullptr, GetLastError, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getModuleVersion", nullptr, GetModuleVersion, nullptr, nullptr, nullptr, napi_default, nullptr}
    };
    
    napi_define_properties(env, exports, sizeof(coreDesc) / sizeof(coreDesc[0]), coreDesc);

    // 注册各功能模块的函数
    RegisterCryptoFunctions(env, exports);
    RegisterAICFunctions(env, exports);
    RegisterDataSyncFunctions(env, exports);

    OH_LOG_INFO(LOG_DOMAIN, LOG_TAG, "CINA.CLUB Go Bridge NAPI module initialized successfully");
    return exports;
}

/**
 * NAPI模块定义
 */
static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "goBridge",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

/**
 * 模块注册
 */
extern "C" __attribute__((constructor)) void RegisterGoBridgeModule(void) {
    napi_module_register(&demoModule);
} 