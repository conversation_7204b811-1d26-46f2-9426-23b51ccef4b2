# CINA.CLUB Platform - Elasticsearch Credentials Secret
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: v1
kind: Secret
metadata:
  name: elasticsearch-credentials
  namespace: logging
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: credentials
    app.kubernetes.io/part-of: logging-platform
type: Opaque
data:
  # Base64 encoded credentials
  # Default username: elastic
  # Default password: changeme123! (CHANGE IN PRODUCTION)
  username: ZWxhc3RpYw==  # elastic
  password: Y2hhbmdlbWUxMjMh  # changeme123!

---
# Network Policy for Fluentd
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: fluentd-network-policy
  namespace: logging
  labels:
    app.kubernetes.io/name: fluentd
    app.kubernetes.io/component: network-security
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: fluentd
      app.kubernetes.io/component: log-collector
  
  policyTypes:
    - Ingress
    - Egress
  
  # Ingress rules - allow health checks
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 24231  # Fluentd monitoring port
  
  # Egress rules - allow specific connections
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow connection to Elasticsearch
    - to:
        - namespaceSelector:
            matchLabels:
              name: logging
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: elasticsearch
      ports:
        - protocol: TCP
          port: 9200
    
    # Allow connection to Kubernetes API
    - to: []
      ports:
        - protocol: TCP
          port: 443

---
# Security Context Constraints for Fluentd
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluentd-secure
  namespace: logging
  labels:
    app.kubernetes.io/name: fluentd
    app.kubernetes.io/component: log-collector
  annotations:
    description: "Secure service account for Fluentd with minimal privileges"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluentd-secure
  labels:
    app.kubernetes.io/name: fluentd
    app.kubernetes.io/component: log-collector
rules:
  # Minimal required permissions for log collection
  - apiGroups: [""]
    resources: ["pods", "namespaces"]
    verbs: ["get", "list", "watch"]
  
  # Read-only access to nodes for metadata
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluentd-secure
  labels:
    app.kubernetes.io/name: fluentd
    app.kubernetes.io/component: log-collector
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluentd-secure
subjects:
  - kind: ServiceAccount
    name: fluentd-secure
    namespace: logging 