# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-20 HH:MM:SS
# Modified: 2025-06-20 HH:MM:SS

cmake_minimum_required(VERSION 3.4.1)
project(CinaClubGoBridge)

set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加头文件路径
include_directories(
    ${NATIVERENDER_ROOT_PATH}
    ${NATIVERENDER_ROOT_PATH}/include
)

# 添加Go库路径（由gomobile生成）
set(GO_LIB_PATH "${CMAKE_CURRENT_SOURCE_DIR}/libs")

# 添加源文件
add_library(
    goBridge 
    SHARED
    napi_init.cpp
    napi_crypto.cpp
    napi_aic.cpp
    napi_datasync.cpp
    utils/type_converter.cpp
    utils/async_work.cpp
)

# 链接Go静态库
if(${CMAKE_ANDROID_ARCH_ABI} STREQUAL "arm64-v8a")
    target_link_libraries(goBridge ${GO_LIB_PATH}/arm64-v8a/libcore.a)
elseif(${CMAKE_ANDROID_ARCH_ABI} STREQUAL "armeabi-v7a")
    target_link_libraries(goBridge ${GO_LIB_PATH}/armeabi-v7a/libcore.a)
endif()

# 查找并链接必需的库
find_library(
    hilog-lib
    hilog_ndk.z
)

find_library(
    libnapi
    ace_napi.z
)

find_library(
    libuv
    uv
)

# 链接系统库
target_link_libraries(
    goBridge
    ${hilog-lib}
    ${libnapi}
    ${libuv}
    libace_napi.z.so
) 