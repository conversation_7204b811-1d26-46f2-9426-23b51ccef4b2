# CINA.CLUB Platform - Admin BFF Service Base Kustomization
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Metadata
metadata:
  name: admin-bff-service-base
  annotations:
    description: "Base configuration for Admin BFF Service Kong Ingress"
    service: "admin-bff-service"
    service-type: "admin-backend-for-frontend"
    security-classification: "restricted"

# Resources to include
resources:
  - ingress.yaml

# Namespace for all resources
namespace: admin

# Common labels applied to all resources
commonLabels:
  app: admin-bff-service
  component: api-gateway
  service: admin-bff
  tier: admin
  platform: cina-club
  service-type: admin-bff
  security-level: maximum

# Common annotations applied to all resources
commonAnnotations:
  platform: "cina-club"
  service-owner: "<EMAIL>"
  api-version: "v1"
  managed-by: "kong-ingress-controller"
  service-category: "admin-backend-services"
  security-classification: "restricted"
  compliance: "admin-access-controls" 