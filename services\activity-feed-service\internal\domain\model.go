/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package domain

import (
	"time"

	"github.com/google/uuid"
)

// FeedType represents the type of activity feed
type FeedType string

const (
	FeedTypeNotifications FeedType = "notifications" // System notifications, account security, task/service status changes
	FeedTypeInteractions  FeedType = "interactions"  // Social interactions: likes, comments, mentions
	FeedTypeFollowing     FeedType = "following"     // Updates from followed users
)

// ActivityType represents the type of activity
type ActivityType string

const (
	// Notification activities
	ActivityTypeSystemAnnouncement ActivityType = "system_announcement"
	ActivityTypeAccountSecurity    ActivityType = "account_security"
	ActivityTypeTaskStatusChanged  ActivityType = "task_status_changed"
	ActivityTypeServiceBooked      ActivityType = "service_booked"
	ActivityTypePaymentReceived    ActivityType = "payment_received"

	// Interaction activities
	ActivityTypePostLiked     ActivityType = "post_liked"
	ActivityTypePostCommented ActivityType = "post_commented"
	ActivityTypeUserMentioned ActivityType = "user_mentioned"
	ActivityTypeReplyReceived ActivityType = "reply_received"
	ActivityTypeFollowed      ActivityType = "followed"

	// Following activities
	ActivityTypeNewContentPublished ActivityType = "new_content_published"
	ActivityTypeUserStatusUpdated   ActivityType = "user_status_updated"
)

// Actor represents a user who performed an action
type Actor struct {
	ID       string `json:"id" bson:"id"`
	Name     string `json:"name" bson:"name"`
	Avatar   string `json:"avatar,omitempty" bson:"avatar,omitempty"`
	Username string `json:"username,omitempty" bson:"username,omitempty"`
}

// Target represents the target object of an activity
type Target struct {
	ID   string `json:"id" bson:"id"`
	Type string `json:"type" bson:"type"` // "post", "comment", "task", "user", etc.
	Name string `json:"name" bson:"name"`
	URL  string `json:"url,omitempty" bson:"url,omitempty"`
}

// DisplayData contains pre-rendered display information for UI
type DisplayData struct {
	TitleTemplate string            `json:"title_template" bson:"title_template"`
	Title         string            `json:"title" bson:"title"`
	Message       string            `json:"message,omitempty" bson:"message,omitempty"`
	IconURL       string            `json:"icon_url,omitempty" bson:"icon_url,omitempty"`
	ImageURL      string            `json:"image_url,omitempty" bson:"image_url,omitempty"`
	Metadata      map[string]string `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// ActivityFeedItem represents a single activity item in the feed
type ActivityFeedItem struct {
	ID           string       `json:"id" bson:"_id"`
	UserID       string       `json:"user_id" bson:"user_id"`
	FeedType     FeedType     `json:"feed_type" bson:"feed_type"`
	ActivityType ActivityType `json:"activity_type" bson:"activity_type"`
	CreatedAt    time.Time    `json:"created_at" bson:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at" bson:"updated_at"`
	IsRead       bool         `json:"is_read" bson:"is_read"`

	// Aggregation fields
	IsAggregated      bool    `json:"is_aggregated" bson:"is_aggregated"`
	AggregatedActors  []Actor `json:"aggregated_actors,omitempty" bson:"aggregated_actors,omitempty"`
	AggregationCount  int     `json:"aggregation_count" bson:"aggregation_count"`
	AggregationKey    string  `json:"aggregation_key,omitempty" bson:"aggregation_key,omitempty"`
	AggregationWindow int     `json:"aggregation_window,omitempty" bson:"aggregation_window,omitempty"` // in minutes

	// Content fields
	Actors      []Actor      `json:"actors" bson:"actors"`
	Target      *Target      `json:"target,omitempty" bson:"target,omitempty"`
	Display     *DisplayData `json:"display" bson:"display"`
	DeepLinkURL string       `json:"deep_link_url,omitempty" bson:"deep_link_url,omitempty"`

	// Metadata
	EventID   string            `json:"event_id,omitempty" bson:"event_id,omitempty"`
	EventType string            `json:"event_type,omitempty" bson:"event_type,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// NewActivityFeedItem creates a new activity feed item
func NewActivityFeedItem(userID string, feedType FeedType, activityType ActivityType) *ActivityFeedItem {
	now := time.Now()
	return &ActivityFeedItem{
		ID:           uuid.New().String(),
		UserID:       userID,
		FeedType:     feedType,
		ActivityType: activityType,
		CreatedAt:    now,
		UpdatedAt:    now,
		IsRead:       false,
		Actors:       make([]Actor, 0),
		Display:      &DisplayData{},
		Metadata:     make(map[string]string),
	}
}

// AddActor adds an actor to the activity feed item
func (item *ActivityFeedItem) AddActor(actor Actor) {
	// Check if actor already exists
	for _, existingActor := range item.Actors {
		if existingActor.ID == actor.ID {
			return
		}
	}
	item.Actors = append(item.Actors, actor)
}

// SetTarget sets the target of the activity
func (item *ActivityFeedItem) SetTarget(target Target) {
	item.Target = &target
}

// SetDisplay sets the display data for the activity
func (item *ActivityFeedItem) SetDisplay(display DisplayData) {
	item.Display = &display
}

// MarkAsRead marks the item as read
func (item *ActivityFeedItem) MarkAsRead() {
	item.IsRead = true
	item.UpdatedAt = time.Now()
}

// GetPrimaryActor returns the first actor or nil if no actors
func (item *ActivityFeedItem) GetPrimaryActor() *Actor {
	if len(item.Actors) > 0 {
		return &item.Actors[0]
	}
	return nil
}

// GetActorCount returns the total number of actors
func (item *ActivityFeedItem) GetActorCount() int {
	if item.IsAggregated {
		return item.AggregationCount
	}
	return len(item.Actors)
}

// FeedSummary represents a summary of all feed types for a user
type FeedSummary struct {
	UserID         string                 `json:"user_id"`
	UnreadCounts   map[FeedType]int       `json:"unread_counts"`
	TotalUnread    int                    `json:"total_unread"`
	LastUpdatedAt  time.Time              `json:"last_updated_at"`
	LastActivityAt map[FeedType]time.Time `json:"last_activity_at,omitempty"`
	RecentItems    []*ActivityFeedItem    `json:"recent_items,omitempty"`
}

// NewFeedSummary creates a new feed summary
func NewFeedSummary(userID string) *FeedSummary {
	return &FeedSummary{
		UserID:         userID,
		UnreadCounts:   make(map[FeedType]int),
		TotalUnread:    0,
		LastUpdatedAt:  time.Now(),
		LastActivityAt: make(map[FeedType]time.Time),
		RecentItems:    make([]*ActivityFeedItem, 0),
	}
}

// UpdateUnreadCount updates the unread count for a specific feed type
func (summary *FeedSummary) UpdateUnreadCount(feedType FeedType, count int) {
	summary.UnreadCounts[feedType] = count
	summary.TotalUnread = 0
	for _, c := range summary.UnreadCounts {
		summary.TotalUnread += c
	}
	summary.LastUpdatedAt = time.Now()
}

// IncrementUnreadCount increments the unread count for a specific feed type
func (summary *FeedSummary) IncrementUnreadCount(feedType FeedType) {
	summary.UnreadCounts[feedType]++
	summary.TotalUnread++
	summary.LastUpdatedAt = time.Now()
}

// AggregationRule defines rules for aggregating similar activities
type AggregationRule struct {
	FeedType       FeedType      `json:"feed_type"`
	ActivityType   ActivityType  `json:"activity_type"`
	TimeWindow     time.Duration `json:"time_window"`     // Time window for aggregation
	MaxActors      int           `json:"max_actors"`      // Maximum number of actors to display
	TargetRequired bool          `json:"target_required"` // Whether target is required for aggregation
	Enabled        bool          `json:"enabled"`
}

// GetAggregationKey generates a unique key for aggregation
func (rule *AggregationRule) GetAggregationKey(item *ActivityFeedItem) string {
	key := string(rule.FeedType) + ":" + string(rule.ActivityType)
	if rule.TargetRequired && item.Target != nil {
		key += ":" + item.Target.Type + ":" + item.Target.ID
	}
	return key
}

// DefaultAggregationRules returns the default aggregation rules
func DefaultAggregationRules() map[string]*AggregationRule {
	return map[string]*AggregationRule{
		"interactions:post_liked": {
			FeedType:       FeedTypeInteractions,
			ActivityType:   ActivityTypePostLiked,
			TimeWindow:     5 * time.Minute,
			MaxActors:      5,
			TargetRequired: true,
			Enabled:        true,
		},
		"interactions:post_commented": {
			FeedType:       FeedTypeInteractions,
			ActivityType:   ActivityTypePostCommented,
			TimeWindow:     10 * time.Minute,
			MaxActors:      3,
			TargetRequired: true,
			Enabled:        true,
		},
		"interactions:followed": {
			FeedType:       FeedTypeInteractions,
			ActivityType:   ActivityTypeFollowed,
			TimeWindow:     30 * time.Minute,
			MaxActors:      10,
			TargetRequired: false,
			Enabled:        true,
		},
	}
}
