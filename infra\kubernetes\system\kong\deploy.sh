#!/bin/bash
# CINA.CLUB Platform - Kong Gateway Deployment Script
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NAMESPACE_KONG="kong-system"
ENVIRONMENT="${ENVIRONMENT:-dev}"
DRY_RUN="${DRY_RUN:-false}"
SKIP_TESTS="${SKIP_TESTS:-false}"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy Kong Gateway to Kubernetes according to CINA.CLUB architecture.

OPTIONS:
    -e, --environment ENV    Environment to deploy to (dev, staging, prod) [default: dev]
    -d, --dry-run           Perform a dry run without making changes
    -s, --skip-tests        Skip post-deployment tests
    -h, --help              Show this help message

EXAMPLES:
    # Deploy to development environment
    $0 --environment dev

    # Deploy to production with dry run
    $0 --environment prod --dry-run

    # Deploy and skip tests
    $0 --environment staging --skip-tests

ENVIRONMENT VARIABLES:
    ENVIRONMENT    Target environment (dev, staging, prod)
    DRY_RUN       Perform dry run (true/false)
    SKIP_TESTS    Skip tests (true/false)

EOF
}

# Function to parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN="true"
                shift
                ;;
            -s|--skip-tests)
                SKIP_TESTS="true"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Function to validate environment
validate_environment() {
    case $ENVIRONMENT in
        dev|staging|prod)
            log_info "Deploying to environment: $ENVIRONMENT"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT. Must be one of: dev, staging, prod"
            exit 1
            ;;
    esac
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl > /dev/null; then
        log_error "kubectl is required but not installed"
        exit 1
    fi
    
    # Check Kubernetes connectivity
    if ! kubectl cluster-info > /dev/null 2>&1; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if we have cluster admin permissions
    if ! kubectl auth can-i '*' '*' --all-namespaces > /dev/null 2>&1; then
        log_warning "You may not have cluster admin permissions. Some operations may fail."
    fi
    
    log_success "Prerequisites check passed"
}

# Function to create namespace
create_namespace() {
    log_info "Creating Kong namespace..."
    
    local cmd="kubectl apply -f namespace.yaml"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        cmd="$cmd --dry-run=client"
        log_info "DRY RUN: $cmd"
    fi
    
    if eval "$cmd"; then
        log_success "Kong namespace created/updated"
    else
        log_error "Failed to create Kong namespace"
        exit 1
    fi
}

# Function to install Kong CRDs
install_kong_crds() {
    log_info "Installing Kong Custom Resource Definitions..."
    
    # Kong CRDs URL (update version as needed)
    local kong_crds_url="https://raw.githubusercontent.com/Kong/kubernetes-ingress-controller/v2.12.0/config/crd/bases"
    
    local crds=(
        "configuration.konghq.com_kongplugins.yaml"
        "configuration.konghq.com_kongconsumers.yaml"
        "configuration.konghq.com_kongingresss.yaml"
        "configuration.konghq.com_kongservices.yaml"
        "configuration.konghq.com_kongupstreams.yaml"
        "configuration.konghq.com_kongconsumergroups.yaml"
        "configuration.konghq.com_kongclusterplugins.yaml"
    )
    
    for crd in "${crds[@]}"; do
        local cmd="kubectl apply -f $kong_crds_url/$crd"
        
        if [[ "$DRY_RUN" == "true" ]]; then
            cmd="$cmd --dry-run=client"
            log_info "DRY RUN: $cmd"
        else
            log_info "Installing CRD: $crd"
            if ! eval "$cmd"; then
                log_warning "Failed to install CRD: $crd (may already exist)"
            fi
        fi
    done
    
    log_success "Kong CRDs installation completed"
}

# Function to deploy Kong control plane
deploy_kong_control_plane() {
    log_info "Deploying Kong Ingress Controller (Control Plane)..."
    
    local cmd="kubectl apply -f control-plane/"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        cmd="$cmd --dry-run=client"
        log_info "DRY RUN: $cmd"
    fi
    
    if eval "$cmd"; then
        log_success "Kong Ingress Controller deployed"
    else
        log_error "Failed to deploy Kong Ingress Controller"
        exit 1
    fi
}

# Function to deploy Kong data plane
deploy_kong_data_plane() {
    log_info "Deploying Kong Proxy (Data Plane)..."
    
    local cmd="kubectl apply -f data-plane/"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        cmd="$cmd --dry-run=client"
        log_info "DRY RUN: $cmd"
    fi
    
    if eval "$cmd"; then
        log_success "Kong Proxy deployed"
    else
        log_error "Failed to deploy Kong Proxy"
        exit 1
    fi
}

# Function to deploy platform plugins
deploy_platform_plugins() {
    log_info "Deploying platform-level Kong plugins..."
    
    local plugin_dir="../../base/kong/plugins"
    
    if [[ ! -d "$plugin_dir" ]]; then
        log_warning "Platform plugins directory not found: $plugin_dir"
        return 0
    fi
    
    local cmd="kubectl apply -f $plugin_dir/"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        cmd="$cmd --dry-run=client"
        log_info "DRY RUN: $cmd"
    fi
    
    if eval "$cmd"; then
        log_success "Platform plugins deployed"
    else
        log_error "Failed to deploy platform plugins"
        exit 1
    fi
}

# Function to wait for deployment
wait_for_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Skipping deployment wait"
        return 0
    fi
    
    log_info "Waiting for Kong deployments to be ready..."
    
    # Wait for Kong Ingress Controller
    if kubectl wait --for=condition=available \
        --timeout=300s \
        deployment/kong-ingress-controller \
        -n "$NAMESPACE_KONG"; then
        log_success "Kong Ingress Controller is ready"
    else
        log_error "Kong Ingress Controller failed to become ready"
        exit 1
    fi
    
    # Wait for Kong Proxy
    if kubectl wait --for=condition=available \
        --timeout=300s \
        deployment/kong-proxy \
        -n "$NAMESPACE_KONG"; then
        log_success "Kong Proxy is ready"
    else
        log_error "Kong Proxy failed to become ready"
        exit 1
    fi
}

# Function to verify deployment
verify_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Skipping deployment verification"
        return 0
    fi
    
    log_info "Verifying Kong deployment..."
    
    # Check if all pods are running
    local kong_pods
    kong_pods=$(kubectl get pods -n "$NAMESPACE_KONG" --no-headers 2>/dev/null | wc -l)
    
    if [[ "$kong_pods" -gt 0 ]]; then
        log_success "Kong pods are running ($kong_pods pods)"
    else
        log_error "No Kong pods found"
        return 1
    fi
    
    # Check Kong proxy service
    if kubectl get service kong-proxy -n "$NAMESPACE_KONG" > /dev/null 2>&1; then
        log_success "Kong proxy service is available"
    else
        log_error "Kong proxy service not found"
        return 1
    fi
    
    # Check Kong plugins
    local plugins
    plugins=$(kubectl get kongplugins -n "$NAMESPACE_KONG" --no-headers 2>/dev/null | wc -l)
    
    if [[ "$plugins" -gt 0 ]]; then
        log_success "Kong plugins are configured ($plugins plugins)"
    else
        log_warning "No Kong plugins found"
    fi
    
    return 0
}

# Function to run tests
run_tests() {
    if [[ "$SKIP_TESTS" == "true" || "$DRY_RUN" == "true" ]]; then
        log_info "Skipping tests"
        return 0
    fi
    
    log_info "Running integration tests..."
    
    local test_script="tests/integration-test.sh"
    
    if [[ -f "$test_script" ]]; then
        if bash "$test_script"; then
            log_success "Integration tests passed"
        else
            log_error "Integration tests failed"
            return 1
        fi
    else
        log_warning "Test script not found: $test_script"
    fi
}

# Function to show deployment status
show_deployment_status() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Deployment status check skipped"
        return 0
    fi
    
    log_info "Kong Gateway Deployment Status:"
    echo "=================================="
    
    echo ""
    echo "Namespace: $NAMESPACE_KONG"
    kubectl get namespace "$NAMESPACE_KONG" 2>/dev/null || echo "Namespace not found"
    
    echo ""
    echo "Deployments:"
    kubectl get deployments -n "$NAMESPACE_KONG" 2>/dev/null || echo "No deployments found"
    
    echo ""
    echo "Services:"
    kubectl get services -n "$NAMESPACE_KONG" 2>/dev/null || echo "No services found"
    
    echo ""
    echo "Pods:"
    kubectl get pods -n "$NAMESPACE_KONG" 2>/dev/null || echo "No pods found"
    
    echo ""
    echo "Kong Plugins:"
    kubectl get kongplugins -n "$NAMESPACE_KONG" 2>/dev/null || echo "No Kong plugins found"
    
    echo ""
    echo "Ingress Classes:"
    kubectl get ingressclasses 2>/dev/null | grep -E "(NAME|kong)" || echo "Kong ingress class not found"
}

# Main deployment function
main() {
    log_info "Starting Kong Gateway Deployment for CINA.CLUB Platform"
    log_info "======================================================="
    log_info "Environment: $ENVIRONMENT"
    log_info "Dry Run: $DRY_RUN"
    log_info "Skip Tests: $SKIP_TESTS"
    echo ""
    
    # Change to script directory
    cd "$SCRIPT_DIR"
    
    # Execute deployment steps
    check_prerequisites
    validate_environment
    create_namespace
    install_kong_crds
    deploy_kong_control_plane
    deploy_kong_data_plane
    deploy_platform_plugins
    wait_for_deployment
    verify_deployment
    run_tests
    show_deployment_status
    
    log_info "======================================================="
    if [[ "$DRY_RUN" == "true" ]]; then
        log_success "Dry run completed successfully!"
        log_info "Run without --dry-run to perform actual deployment"
    else
        log_success "Kong Gateway deployment completed successfully!"
        log_info "Kong Gateway is ready to serve traffic"
        
        # Show next steps
        echo ""
        log_info "Next Steps:"
        echo "1. Configure your services to use Kong Ingress Controller"
        echo "2. Apply service-specific Ingress configurations"
        echo "3. Monitor Kong Gateway metrics and logs"
        echo "4. Set up external DNS for your domain"
        echo ""
        echo "Documentation: https://docs.cina.club/platform/api-gateway"
        echo "Support: <EMAIL>"
    fi
}

# Parse arguments and run main function
parse_arguments "$@"
main 