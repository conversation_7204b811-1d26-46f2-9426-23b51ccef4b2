# CINA.CLUB Platform - Observability Plugins (Platform Standards)
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Prometheus Metrics Plugin - Global Monitoring
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: prometheus-metrics
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: monitoring
    scope: global
  annotations:
    description: "Prometheus metrics collection for API Gateway monitoring"
    usage: "Applied globally to collect metrics from all services"
    owner: "<EMAIL>"
    konghq.com/global: "true"               # Apply globally

plugin: prometheus
config:
  # Metrics endpoint configuration
  per_consumer: true                        # Collect per-consumer metrics
  status_code_metrics: true                # Collect HTTP status code metrics
  latency_metrics: true                    # Collect latency metrics
  bandwidth_metrics: true                  # Collect bandwidth metrics
  upstream_health_metrics: true            # Collect upstream health metrics
  
  # Custom metric labels
  custom_labels:
    platform: "cina-club"
    environment: "{{ .Values.environment }}"
    version: "{{ .Values.version }}"
    region: "{{ .Values.region }}"

---
# OpenTelemetry Tracing Plugin - Distributed Tracing
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: opentelemetry-tracing
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: tracing
    scope: global
  annotations:
    description: "OpenTelemetry distributed tracing for request correlation"
    usage: "Applied globally to trace all requests through the system"
    konghq.com/global: "true"

plugin: opentelemetry
config:
  # OTLP endpoint configuration
  endpoint: "http://jaeger-collector.monitoring.svc.cluster.local:14268/api/traces"
  
  # Trace configuration
  traces_endpoint: "http://jaeger-collector.monitoring.svc.cluster.local:14268/api/traces"
  logs_endpoint: "http://jaeger-collector.monitoring.svc.cluster.local:14268/api/logs"
  
  # Sampling configuration
  sampling_rate: 0.1                       # Sample 10% of requests (adjust for production)
  
  # Resource attributes
  resource_attributes:
    service.name: "kong-gateway"
    service.version: "3.4.2"
    service.namespace: "kong-system"
    platform.name: "cina-club"
    platform.version: "1.0.0"
  
  # Headers to propagate
  propagation:
    extract:
      - "tracecontext"                     # W3C Trace Context
      - "baggage"                          # W3C Baggage
      - "b3"                               # B3 propagation (for compatibility)
      - "jaeger"                           # Jaeger propagation
    inject:
      - "tracecontext"
      - "baggage"
      - "b3"
      - "jaeger"
  
  # Trace headers
  header_type: "preserve"                  # Preserve existing trace headers
  
  # Custom tags
  tags:
    component: "api-gateway"
    layer: "infrastructure"

---
# Request ID Plugin - Request Correlation
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-id
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: correlation
    scope: global
  annotations:
    description: "Request ID generation and propagation for request correlation"
    konghq.com/global: "true"

plugin: correlation-id
config:
  # Header name for request ID
  header_name: "X-Request-ID"
  
  # Generator type
  generator: "uuid"                        # Generate UUID for each request
  
  # Echo the request ID back to client
  echo_downstream: true

---
# Request Transformer Plugin - Standard Headers
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-transformer-standard
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: headers
  annotations:
    description: "Standard request transformation for observability headers"

plugin: request-transformer
config:
  # Add standard headers to all requests
  add:
    headers:
      - "X-Gateway-Start-Time:$(date +%s%3N)"  # Add gateway start time
      - "X-API-Version:v1"                     # Add API version
      - "X-Platform:cina-club"                 # Add platform identifier
  
  # Append headers (don't overwrite if they exist)
  append:
    headers:
      - "X-Forwarded-Proto:$(scheme)"
      - "X-Real-IP:$(remote_addr)"
  
  # Remove sensitive headers
  remove:
    headers:
      - "X-Internal-Secret"
      - "X-Debug-Token"

---
# Response Transformer Plugin - Standard Response Headers
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: response-transformer-standard
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: headers
  annotations:
    description: "Standard response transformation for observability headers"

plugin: response-transformer
config:
  # Add standard response headers
  add:
    headers:
      - "X-Gateway-End-Time:$(date +%s%3N)"   # Add gateway end time
      - "X-Response-Time:$(response_time)"     # Add response time
      - "X-Served-By:kong-gateway"            # Add server identifier
      - "X-Content-Type-Options:nosniff"      # Security header
      - "X-Frame-Options:DENY"                # Security header
      - "Referrer-Policy:strict-origin-when-cross-origin"  # Security header
  
  # Remove internal headers from response
  remove:
    headers:
      - "X-Internal-Processing-Time"
      - "X-Upstream-Debug"
      - "Server"                             # Hide server version

---
# Logging Plugin - Structured Access Logs
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: structured-logging
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: logging
    scope: global
  annotations:
    description: "Structured logging for centralized log aggregation"
    konghq.com/global: "true"

plugin: file-log
config:
  # Log file path (will be collected by Fluentd)
  path: "/dev/stdout"
  
  # Custom log format (JSON)
  custom_fields_by_lua:
    # Custom fields to add to logs
    request_id: "kong.request.get_header('X-Request-ID')"
    trace_id: "kong.request.get_header('X-Trace-ID')"
    user_id: "kong.ctx.shared.authenticated_credential and kong.ctx.shared.authenticated_credential.consumer_id"
    api_version: "kong.request.get_header('X-API-Version')"
    platform: "'cina-club'"
    environment: "'{{ .Values.environment }}'"
    gateway_version: "'3.4.2'"

---
# HTTP Log Plugin - Centralized Logging
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: http-log-centralized
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: logging
  annotations:
    description: "HTTP logging to centralized log aggregation service"

plugin: http-log
config:
  # HTTP endpoint for log aggregation
  http_endpoint: "http://fluentd.logging.svc.cluster.local:8080/kong"
  
  # HTTP method for sending logs
  method: "POST"
  
  # Content type
  content_type: "application/json"
  
  # Timeout configuration
  timeout: 10000                           # 10 second timeout
  keepalive: 60000                         # 60 second keepalive
  
  # Retry configuration
  retry_count: 3
  queue_size: 1000
  flush_timeout: 2
  
  # Custom log format
  custom_fields_by_lua:
    timestamp: "os.date('!%Y-%m-%dT%H:%M:%S.000Z')"
    service_name: "kong.router.get_service() and kong.router.get_service().name"
    route_name: "kong.router.get_route() and kong.router.get_route().name"
    consumer_id: "kong.ctx.shared.authenticated_credential and kong.ctx.shared.authenticated_credential.consumer_id"
    request_size: "kong.request.get_size()"
    response_size: "kong.response.get_size()"
    request_id: "kong.request.get_header('X-Request-ID')"
    user_agent: "kong.request.get_header('User-Agent')"
    referer: "kong.request.get_header('Referer')"

---
# Datadog APM Plugin - Application Performance Monitoring
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: datadog-apm
  namespace: kong-system
  labels:
    app: kong-plugin
    component: observability
    tier: platform-standard
    category: apm
  annotations:
    description: "Datadog APM integration for application performance monitoring"
    usage: "Apply to services that need detailed APM monitoring"

plugin: datadog
config:
  # Datadog agent configuration
  host: "datadog-agent.monitoring.svc.cluster.local"
  port: 8125
  
  # Metric configuration
  metrics:
    - name: "kong.request.count"
      type: "counter"
      tags: ["service:$(service_name)", "route:$(route_name)"]
    - name: "kong.request.size"
      type: "histogram"
      tags: ["service:$(service_name)"]
    - name: "kong.response.size"
      type: "histogram"
      tags: ["service:$(service_name)"]
    - name: "kong.latency"
      type: "histogram"
      tags: ["service:$(service_name)", "status:$(status)"]
  
  # Custom tags
  tags:
    - "platform:cina-club"
    - "component:api-gateway"
    - "environment:{{ .Values.environment }}"
    - "version:{{ .Values.version }}"

---
# Request Size Limiting Plugin - DOS Protection
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-size-limit
  namespace: kong-system
  labels:
    app: kong-plugin
    component: security
    tier: platform-standard
    category: dos-protection
  annotations:
    description: "Request size limiting for DOS protection"

plugin: request-size-limiting
config:
  # Maximum allowed request size
  allowed_payload_size: 10                 # 10MB default limit
  size_unit: "megabytes"
  
  # Require content length header
  require_content_length: true

---
# Request Termination Plugin - Maintenance Mode
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: maintenance-mode
  namespace: kong-system
  labels:
    app: kong-plugin
    component: maintenance
    tier: platform-standard
    category: operational
  annotations:
    description: "Maintenance mode plugin for planned downtime"
    usage: "Apply when system is under maintenance"

plugin: request-termination
config:
  # Return maintenance response
  status_code: 503
  content_type: "application/json"
  body: |
    {
      "error": "service_unavailable",
      "message": "CINA.CLUB platform is currently under maintenance. Please try again later.",
      "status": 503,
      "timestamp": "$(date)",
      "maintenance_window": "2025-01-27T12:00:00Z - 2025-01-27T14:00:00Z",
      "contact": "<EMAIL>"
    } 