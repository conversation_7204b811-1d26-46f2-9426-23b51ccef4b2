//go:build mobile

// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package datasync

import (
	"encoding/json"
	"fmt"
)

// MobileSyncEngine provides a mobile-friendly interface to the sync engine
type MobileSyncEngine struct {
	engine *SyncEngine
}

// NewMobileSyncEngine creates a new mobile sync engine
func NewMobileSyncEngine(userID, deviceID string, encryptionKey []byte) *MobileSyncEngine {
	// Note: In a real implementation, you'd provide a concrete SyncClient
	// For now, we'll use nil to demonstrate the interface
	engine := NewSyncEngine(userID, deviceID, encryptionKey, nil)
	
	return &MobileSyncEngine{
		engine: engine,
	}
}

// ChunkDataMobile chunks data and returns the result as JSON
func ChunkDataMobile(data []byte) (string, error) {
	result := ChunkDataSimple(data)
	jsonData, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal chunk result: %w", err)
	}
	return string(jsonData), nil
}

// CompareChunksMobile compares two chunk results and returns the differences as JSON
func CompareChunksMobile(oldResultJSON, newResultJSON string) (string, error) {
	var oldResult, newResult *ChunkResult
	
	if oldResultJSON != "" {
		if err := json.Unmarshal([]byte(oldResultJSON), &oldResult); err != nil {
			return "", fmt.Errorf("failed to unmarshal old result: %w", err)
		}
	}
	
	if newResultJSON != "" {
		if err := json.Unmarshal([]byte(newResultJSON), &newResult); err != nil {
			return "", fmt.Errorf("failed to unmarshal new result: %w", err)
		}
	}
	
	diff := CompareChunks(oldResult, newResult)
	jsonData, err := json.Marshal(diff)
	if err != nil {
		return "", fmt.Errorf("failed to marshal chunk diff: %w", err)
	}
	
	return string(jsonData), nil
}

// ValidateChunksMobile validates chunks from JSON and returns any errors
func ValidateChunksMobile(chunksJSON string) (string, error) {
	var chunks []Chunk
	if err := json.Unmarshal([]byte(chunksJSON), &chunks); err != nil {
		return "", fmt.Errorf("failed to unmarshal chunks: %w", err)
	}
	
	if err := ValidateChunks(chunks); err != nil {
		return err.Error(), nil
	}
	
	return "", nil // No error
}

// ReassembleChunksMobile reassembles chunks from JSON
func ReassembleChunksMobile(chunksJSON string) ([]byte, error) {
	var chunks []Chunk
	if err := json.Unmarshal([]byte(chunksJSON), &chunks); err != nil {
		return nil, fmt.Errorf("failed to unmarshal chunks: %w", err)
	}
	
	return ReassembleChunks(chunks), nil
}

// NewVersionVectorMobile creates a new version vector and returns it as JSON
func NewVersionVectorMobile() (string, error) {
	vv := NewVersionVector()
	jsonData, err := json.Marshal(vv)
	if err != nil {
		return "", fmt.Errorf("failed to marshal version vector: %w", err)
	}
	return string(jsonData), nil
}

// CompareVersionVectorsMobile compares two version vectors and returns the result
func CompareVersionVectorsMobile(vv1JSON, vv2JSON string) (string, error) {
	var vv1, vv2 VersionVector
	
	if err := json.Unmarshal([]byte(vv1JSON), &vv1); err != nil {
		return "", fmt.Errorf("failed to unmarshal first version vector: %w", err)
	}
	
	if err := json.Unmarshal([]byte(vv2JSON), &vv2); err != nil {
		return "", fmt.Errorf("failed to unmarshal second version vector: %w", err)
	}
	
	result := vv1.Compare(vv2)
	return result.String(), nil
}

// MergeVersionVectorsMobile merges two version vectors and returns the result as JSON
func MergeVersionVectorsMobile(vv1JSON, vv2JSON string) (string, error) {
	var vv1, vv2 VersionVector
	
	if err := json.Unmarshal([]byte(vv1JSON), &vv1); err != nil {
		return "", fmt.Errorf("failed to unmarshal first version vector: %w", err)
	}
	
	if err := json.Unmarshal([]byte(vv2JSON), &vv2); err != nil {
		return "", fmt.Errorf("failed to unmarshal second version vector: %w", err)
	}
	
	result := vv1.Clone()
	result.Merge(vv2)
	
	jsonData, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal merged version vector: %w", err)
	}
	
	return string(jsonData), nil
}

// UpdateVersionVectorMobile updates a version vector and returns the result as JSON
func UpdateVersionVectorMobile(vvJSON, participant string, version int64) (string, error) {
	var vv VersionVector
	
	if err := json.Unmarshal([]byte(vvJSON), &vv); err != nil {
		return "", fmt.Errorf("failed to unmarshal version vector: %w", err)
	}
	
	vv.Update(participant, version)
	
	jsonData, err := json.Marshal(vv)
	if err != nil {
		return "", fmt.Errorf("failed to marshal updated version vector: %w", err)
	}
	
	return string(jsonData), nil
}

// IncrementVersionVectorMobile increments a version vector and returns the result as JSON
func IncrementVersionVectorMobile(vvJSON, participant string) (string, error) {
	var vv VersionVector
	
	if err := json.Unmarshal([]byte(vvJSON), &vv); err != nil {
		return "", fmt.Errorf("failed to unmarshal version vector: %w", err)
	}
	
	vv.Increment(participant)
	
	jsonData, err := json.Marshal(vv)
	if err != nil {
		return "", fmt.Errorf("failed to marshal incremented version vector: %w", err)
	}
	
	return string(jsonData), nil
} 