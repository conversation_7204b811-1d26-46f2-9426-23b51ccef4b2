好的，遵照您的指示。我将为您生成一份专门针对 **`service-offering-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`service-offering-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**、**Saga模式**和相关模式。它将特别关注**服务产品与项目的解耦、高级定价与可用性管理、作为Saga协调者的健壮订单流程，以及与平台多个核心服务的深度协同**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `service-offering-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `service-offering-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + Saga编排模式 + 策略模式(用于定价/可用性)

## 1. 概述

`service-offering-service` 是CINA.CLUB平台**交易生态的核心**。它负责管理从服务定义、发布到用户预订、履约的全过程。其核心挑战在于：
1.  **复杂的交易流程**: 一个成功的预订需要与`billing`, `payment`, `schedule`, `notification`等多个服务进行一系列的、有条件的、可能失败的交互。
2.  **分布式数据一致性**: 必须保证在任何故障情况下，订单状态、支付状态和日历占用状态的最终一致性。
3.  **灵活的业务模型**: 需要支持标准化的服务产品目录，同时允许服务提供者进行个性化的定价（套餐、附加服务）和可用性设置。
4.  **高性能的可用性查询**: 查询一个服务在某个时间段的可用时间槽，是一个需要合并多个数据源（自身预订、提供者个人日历）的复杂、高频操作。
5.  **作为流程编排者**: 本服务在很多场景下扮演**Saga协调者**的角色，需要健壮的状态机和补偿逻辑。

本架构设计通过采用**整洁架构**，并以**编排式Saga**作为处理分布式事务的核心模式，同时结合**策略模式**来处理灵活的定价和可用性逻辑，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (Saga协调的预订流程)

```mermaid
graph TD
    subgraph "客户端/用户"
        ClientApp
    end

    subgraph "ServiceOfferingService (Saga Coordinator)"
        style ServiceOfferingService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[OrderSagaManager<br/><em>application/saga</em>]
        C[ApplicationService<br/><em>application/service</em>]
        D[Domain Logic & Aggregates<br/>(Order, Offering)<br/><em>domain/aggregate</em>]
        E[Repository<br/><em>adapter/repository</em>]
        F[S2S gRPC Clients<br/><em>adapter/client</em>]
    end

    subgraph "参与者服务 (Saga Participants)"
        style "参与者服务 (Saga Participants)" fill:#f3e5f5
        S1[billing-service]
        S2[payment-service]
        S3[schedule-service]
        S4[notification-dispatch-service]
    end

    ClientApp -- "1. CreateBooking" --> A
    A -- "调用" --> C
    C -- "2. 创建Order, 启动Saga" --> B
    
    B -- "3. [Step 1] Execute: CreateInvoice" --> F
    F -- "gRPC Call" --> S1
    S1 -- "invoiceId" --> F --> B
    
    B -- "4. [Step 2] Execute: InitiatePayment" --> F
    F -- "gRPC Call" --> S2
    S2 -- "paymentParams" --> F --> B
    
    B -- "返回给客户端" --> C --> A --> ClientApp
    
    subgraph "异步回调"
        S2 -- "[Event] PaymentSucceeded" --> Kafka[(Kafka)]
        Kafka -- "消费事件" --> C
        C -- "5. 继续Saga: 标记支付成功" --> B
        B -- "6. [Step 3] Execute: BlockSchedule" --> F
        F -- "gRPC Call" --> S3
        B -- "7. [Step 4] Execute: SendNotification" --> F
        F -- "gRPC Call" --> S4
    end

    subgraph "失败补偿"
        S2 -- "[Event] PaymentFailed" --> Kafka
        Kafka -- "消费事件" --> C
        C -- "启动补偿流程" --> B
        B -- "[Compensate 1] CancelInvoice" --> F
        F -- "gRPC Call" --> S1
    end
```

### 2.2 最终目录结构 (`services/service-offering-service/`)

```
service-offering-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── billing_client.go
│   │   │   ├── payment_client.go
│   │   │   └── schedule_client.go
│   │   ├── event/
│   │   │   ├── producer.go
│   │   │   └── payment_consumer.go # 消费支付结果事件
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── service/
│   │   │   └── offering_service.go # 核心应用服务实现
│   │   └── saga/                 # ✨ Saga分布式事务实现 ✨
│   │       ├── interface.go
│   │       ├── create_order_saga.go
│   │       └── manager.go
│   └── domain/
│       ├── aggregate/
│       │   └── order_aggregate.go # 封装订单状态机
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── availability_service.go # ✨ 可用性计算服务 ✨
│           └── pricing_service.go      # ✨ 定价计算服务 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Marketplace Rules)

*   `domain/model/`: 使用`/core/models`中与服务、订单相关的`struct`。
*   **`domain/aggregate/order_aggregate.go`**:
    *   **`Order`聚合根**: 封装了`Order`实体及其行项目`OrderLineItems`和预订`Booking`。
    *   **状态机**: 包含`Confirm()`, `StartFulfillment()`, `Complete()`, `Cancel()`等方法，严格控制`Order.Status`的合法流转。例如，只有`CONFIRMED`状态的订单才能调用`StartFulfillment()`。
    *   每个状态变更方法都会生成一个对应的领域事件（如`OrderConfirmedEvent`）。
*   **`domain/service/pricing_service.go`**:
    *   **`PricingService`**: 一个无状态的领域服务，封装了复杂的定价计算逻辑。
    *   **`CalculateTotal(offering, selectedPackage, selectedAddOns)`**: 根据服务的定价模型（套餐、附加服务），计算出最终的订单总价和行项目列表。
*   **`domain/service/availability_service.go`**:
    *   **`AvailabilityService`**: 封装了可用性时段的计算逻辑。
    *   **`GetAvailableSlots(ctx, offering, providerUserID, start, end)`**:
        1.  **调用`schedule_client.GetFreeBusy(...)`**获取提供者个人日历的忙闲时段。
        2.  **调用`repository.GetBookedSlotsForProvider(...)`**获取在本服务中已被预订的时段。
        3.  从提供者定义的可用性规则（如“周一至周五9-5点”）中，**减去**上述两个忙碌时段集合。
        4.  将剩余的连续空闲时间，按服务的时长（如1小时）分割成可用的**时间槽(slots)**。
        5.  返回最终的时间槽列表。

### 3.2 `application/` - 应用层 (The Saga Coordinator)

*   **`application/service/offering_service.go`**:
    *   **`CreateBooking(ctx, ...)`**:
        1.  调用`domain.AvailabilityService`检查请求的时间槽是否可用。
        2.  调用`domain.PricingService`计算订单总价。
        3.  创建一个`Order`聚合根，初始状态为`PENDING_PAYMENT`。
        4.  **调用`saga.Manager.Execute(newCreateOrderSaga(...))`来启动Saga事务**。
    *   **`HandlePaymentResult(ctx, paymentResultEvent)` (由事件消费者调用)**:
        *   根据`business_order_id`找到对应的`Order`。
        *   调用`saga.Manager`来**继续或补偿**对应的Saga实例。
*   **`application/saga/`**: **这是分布式事务的核心实现**。
    *   **`interface.go`**: 定义`Saga`和`Step`接口。
        ```go
        type Step struct {
            Execute     func(ctx, sagaCtx) error
            Compensate  func(ctx, sagaCtx) error
        }
        type Saga interface {
            GetSteps() []Step
        }
        ```
    *   `create_order_saga.go`:
        *   **`CreateOrderSaga` struct**: 实现了`Saga`接口。
        *   **`GetSteps()`**: 返回一个`Step`数组，每个`Step`都定义了其执行操作和补偿操作。
            *   **Step 1**: Execute=`billing.CreateInvoice`, Compensate=`billing.CancelInvoice`.
            *   **Step 2**: Execute=`payment.InitiatePayment`, Compensate= (通常无需补偿，支付会自动过期).
            *   **Step 3**: Execute=`schedule.BlockSlot`, Compensate=`schedule.UnblockSlot`.
            *   ...
    *   `manager.go`:
        *   **`SagaManager`**: 负责执行Saga。它会按顺序执行每个Step的`Execute`函数。如果任何一步失败，它会按**相反的顺序**执行所有已成功步骤的`Compensate`函数。
        *   Saga的状态（当前执行到第几步）可以被持久化到`orders`表的一个`saga_state` JSONB字段中，以便在服务重启或异步回调时能恢复。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**: 使用**PostgreSQL**。
    *   `repo.go`:
        *   **`GetAvailableSlotsForProvider`**: 需要高效地查询`orders`表，获取指定提供者在某个时间段内所有已确认的预订。
        *   **并发控制**: 在创建预订的事务中，可以对提供者的可用性进行**悲观锁**，或使用数据库的**排他约束(Exclusion Constraint)**来防止同一时间段被双重预订。
*   **`adapter/client/`**: 封装对`billing`, `payment`, `schedule`等服务的gRPC调用。
*   **`adapter/event/`**:
    *   `producer.go`: 在Saga成功完成或订单状态变更时，发布`OrderConfirmedEvent`等领域事件。
    *   `payment_consumer.go`: 消费支付结果事件，并调用`application.service`来驱动Saga流程。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`service-offering-service`：
1.  **Saga模式作为核心**: 明确使用**编排式Saga**来管理复杂的、跨多个服务的预订和支付流程，通过执行/补偿逻辑，保证了分布式环境下的最终数据一致性。
2.  **领域服务封装复杂逻辑**: 将高度复杂且纯粹的业务计算（如定价、可用性时段计算）封装在无状态的**领域服务**中，使得核心逻辑清晰、可测试，并与应用流程解耦。
3.  **聚合根管理状态机**: 使用`Order`聚合根来严格控制订单的生命周期和状态转换，确保所有变更都符合预定义的业务规则。
4.  **清晰的外部协同**: 与`schedule-service`的双向交互（写预订、读忙闲）和与`billing/payment`服务的Saga交互，都有清晰的定义和实现路径。
5.  **为性能优化的读操作**: 可用性查询是一个复杂的读操作，架构明确了其需要合并多个数据源，并为未来的缓存优化留下了空间。

这种架构确保了`service-offering-service`能够作为一个**健壮、可靠、流程严谨**的交易中枢，为CINA.CLUB平台多样化的服务交易场景提供坚实的技术支撑。