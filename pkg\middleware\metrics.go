/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package middleware

import (
	"context"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// ServerMetrics 包含服务器端的 Prometheus 指标
type ServerMetrics struct {
	// 请求总数计数器
	requestsTotal *prometheus.CounterVec
	// 请求延迟直方图
	requestDuration *prometheus.HistogramVec
	// 活跃请求数量计数器
	requestsInFlight prometheus.Gauge
	// 请求大小直方图
	requestSize *prometheus.HistogramVec
	// 响应大小直方图
	responseSize *prometheus.HistogramVec
}

// NewServerMetrics 创建新的服务器指标实例
func NewServerMetrics() *ServerMetrics {
	return &ServerMetrics{
		requestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "grpc_server_handled_total",
				Help: "Total number of RPCs completed on the server, regardless of success or failure.",
			},
			[]string{"grpc_service", "grpc_method", "grpc_code"},
		),
		requestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_server_handling_seconds",
				Help:    "Histogram of response latency (seconds) of gRPC that had been application-level handled by the server.",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"grpc_service", "grpc_method"},
		),
		requestsInFlight: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "grpc_server_requests_in_flight",
				Help: "Number of gRPC requests currently being processed by the server.",
			},
		),
		requestSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_server_request_size_bytes",
				Help:    "Histogram of request size in bytes for gRPC requests.",
				Buckets: prometheus.ExponentialBuckets(1, 2, 20),
			},
			[]string{"grpc_service", "grpc_method"},
		),
		responseSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_server_response_size_bytes",
				Help:    "Histogram of response size in bytes for gRPC responses.",
				Buckets: prometheus.ExponentialBuckets(1, 2, 20),
			},
			[]string{"grpc_service", "grpc_method"},
		),
	}
}

// Register 将指标注册到 Prometheus
func (m *ServerMetrics) Register(reg prometheus.Registerer) error {
	collectors := []prometheus.Collector{
		m.requestsTotal,
		m.requestDuration,
		m.requestsInFlight,
		m.requestSize,
		m.responseSize,
	}

	for _, collector := range collectors {
		if err := reg.Register(collector); err != nil {
			return err
		}
	}
	return nil
}

// UnaryServerInterceptor 返回一个 gRPC 一元拦截器，用于收集指标
func (m *ServerMetrics) UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		startTime := time.Now()

		// 解析服务名和方法名
		service, method := parseFullMethodName(info.FullMethod)

		// 增加活跃请求计数
		m.requestsInFlight.Inc()
		defer m.requestsInFlight.Dec()

		// 记录请求大小（如果可以估算）
		if reqSize := estimateMessageSize(req); reqSize > 0 {
			m.requestSize.WithLabelValues(service, method).Observe(float64(reqSize))
		}

		// 调用实际的处理器
		resp, err := handler(ctx, req)

		// 计算延迟
		duration := time.Since(startTime)

		// 获取状态码
		statusCode := codes.OK
		if err != nil {
			statusCode = status.Code(err)
		}

		// 记录指标
		m.requestsTotal.WithLabelValues(service, method, statusCode.String()).Inc()
		m.requestDuration.WithLabelValues(service, method).Observe(duration.Seconds())

		// 记录响应大小（如果可以估算）
		if respSize := estimateMessageSize(resp); respSize > 0 {
			m.responseSize.WithLabelValues(service, method).Observe(float64(respSize))
		}

		return resp, err
	}
}

// StreamServerInterceptor 返回一个 gRPC 流拦截器，用于收集指标
func (m *ServerMetrics) StreamServerInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		startTime := time.Now()

		// 解析服务名和方法名
		service, method := parseFullMethodName(info.FullMethod)

		// 增加活跃请求计数
		m.requestsInFlight.Inc()
		defer m.requestsInFlight.Dec()

		// 调用实际的处理器
		err := handler(srv, ss)

		// 计算延迟
		duration := time.Since(startTime)

		// 获取状态码
		statusCode := codes.OK
		if err != nil {
			statusCode = status.Code(err)
		}

		// 记录指标
		m.requestsTotal.WithLabelValues(service, method, statusCode.String()).Inc()
		m.requestDuration.WithLabelValues(service, method).Observe(duration.Seconds())

		return err
	}
}

// parseFullMethodName 解析完整的方法名，提取服务名和方法名
func parseFullMethodName(fullMethodName string) (service, method string) {
	if len(fullMethodName) == 0 {
		return "unknown", "unknown"
	}

	// gRPC 方法名格式为 "/package.service/method"
	// 去掉开头的 '/'
	name := fullMethodName[1:]

	// 找到最后一个 '/' 的位置
	lastSlash := -1
	for i := len(name) - 1; i >= 0; i-- {
		if name[i] == '/' {
			lastSlash = i
			break
		}
	}

	if lastSlash == -1 {
		return "unknown", name
	}

	service = name[:lastSlash]
	method = name[lastSlash+1:]

	return service, method
}

// estimateMessageSize 估算消息大小（简单实现）
func estimateMessageSize(msg interface{}) int {
	if msg == nil {
		return 0
	}

	// 这是一个简化的实现
	// 在实际生产环境中，可能需要更精确的大小计算
	// 可以使用 protobuf 的 Size() 方法或序列化后的大小

	// 如果消息实现了 Size() 方法（protobuf 生成的代码通常会有）
	if sizer, ok := msg.(interface{ Size() int }); ok {
		return sizer.Size()
	}

	// 如果消息实现了 XXX_Size() 方法（较老的 protobuf 版本）
	if sizer, ok := msg.(interface{ XXX_Size() int }); ok {
		return sizer.XXX_Size()
	}

	// 默认返回 0，表示无法估算大小
	return 0
}

// MetricsInterceptor 创建一个简化的指标拦截器
func MetricsInterceptor() grpc.UnaryServerInterceptor {
	metrics := NewServerMetrics()
	// 注册到默认的 Prometheus 注册表
	metrics.Register(prometheus.DefaultRegisterer)
	return metrics.UnaryServerInterceptor()
}

// MetricsStreamInterceptor 创建一个简化的流指标拦截器
func MetricsStreamInterceptor() grpc.StreamServerInterceptor {
	metrics := NewServerMetrics()
	// 注册到默认的 Prometheus 注册表
	metrics.Register(prometheus.DefaultRegisterer)
	return metrics.StreamServerInterceptor()
}
