/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

package interceptor

import (
	"context"
	"errors"
)

// AuthenticatedUser represents a verified end-user from JWT validation
// This is a copy to avoid import cycle with the main auth package
type AuthenticatedUser struct {
	ID    string   `json:"id"`
	Roles []string `json:"roles"`
	Email string   `json:"email,omitempty"`
}

// ServiceIdentity represents a verified service identity from S2S JWT validation
// This is a copy to avoid import cycle with the main auth package
type ServiceIdentity struct {
	Name string `json:"name"` // e.g., "user-core-service"
}

// Private context keys to prevent external package conflicts
type contextKey string

const (
	userContextKey    contextKey = "auth_user"
	serviceContextKey contextKey = "auth_service"
)

var (
	ErrUserNotFound    = errors.New("authenticated user not found in context")
	ErrServiceNotFound = errors.New("service identity not found in context")
)

// NewContextWithUser injects authenticated user information into the context
func NewContextWithUser(ctx context.Context, user *AuthenticatedUser) context.Context {
	return context.WithValue(ctx, userContextKey, user)
}

// UserFromContext safely extracts authenticated user information from the context
func UserFromContext(ctx context.Context) (*AuthenticatedUser, error) {
	user, ok := ctx.Value(userContextKey).(*AuthenticatedUser)
	if !ok || user == nil {
		return nil, ErrUserNotFound
	}
	return user, nil
}

// NewContextWithService injects service identity information into the context
func NewContextWithService(ctx context.Context, service *ServiceIdentity) context.Context {
	return context.WithValue(ctx, serviceContextKey, service)
}

// ServiceFromContext safely extracts service identity information from the context
func ServiceFromContext(ctx context.Context) (*ServiceIdentity, error) {
	service, ok := ctx.Value(serviceContextKey).(*ServiceIdentity)
	if !ok || service == nil {
		return nil, ErrServiceNotFound
	}
	return service, nil
}
