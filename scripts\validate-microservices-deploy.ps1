# CINA.CLUB Platform - Microservices Deploy Configuration Validation
# Copyright (c) 2025 Cina.Club
# PowerShell script to validate all microservices deploy configurations

param(
    [Parameter(Mandatory=$false)]
    [switch]$Detailed = $false,
    [Parameter(Mandatory=$false)]
    [switch]$FixIssues = $false
)

# Expected structure for each microservice
$requiredStructure = @{
    "base" = @("ingress.yaml", "kustomization.yaml")
    "overlays" = @("dev", "staging", "prod")
    "overlayFiles" = @("kustomization.yaml", "patch-ingress-host.yaml")
}

# All microservices that should have deploy configurations
$expectedMicroservices = @(
    "user-core-service",
    "chat-api-service", 
    "ai-assistant-service",
    "admin-bff-service",
    "personal-kb-service",
    "live-api-service",
    "billing-service",
    "payment-service",
    "social-service",
    "search-service",
    "analytics-service",
    "file-storage-service",
    "notification-dispatch-service",
    "content-moderation-service",
    "short-video-service",
    "embedding-service",
    "gamification-service",
    "search-indexer-service",
    "activity-feed-service",
    "location-service",
    "memory-service",
    "chat-websocket-server",
    "live-gateway-service",
    "community-forum-service",
    "community-qa-service",
    "digital-twin-service",
    "fast-news-service",
    "family-tree-service",
    "calendar-sync-service",
    "cina-coin-ledger-service",
    "cloud-sync-service",
    "key-management-proxy-service",
    "live-im-service",
    "metaverse-engine-service",
    "model-management-service",
    "news-crawler-service",
    "review-service",
    "routines-service",
    "schedule-service",
    "service-offering-service",
    "shared-kb-service"
)

function Test-FileContent {
    param($FilePath, $RequiredPatterns)
    
    if (!(Test-Path $FilePath)) {
        return @{ Valid = $false; Issues = @("File does not exist") }
    }
    
    $content = Get-Content $FilePath -Raw
    $issues = @()
    
    foreach ($pattern in $RequiredPatterns) {
        if ($content -notmatch $pattern) {
            $issues += "Missing required pattern: $pattern"
        }
    }
    
    return @{ Valid = ($issues.Count -eq 0); Issues = $issues }
}

function Validate-BaseConfiguration {
    param($ServiceName, $BasePath)
    
    $results = @{
        Valid = $true
        Issues = @()
        Files = @{}
    }
    
    # Check if base directory exists
    if (!(Test-Path $BasePath)) {
        $results.Valid = $false
        $results.Issues += "Base directory does not exist"
        return $results
    }
    
    # Check ingress.yaml
    $ingressPath = "$BasePath/ingress.yaml"
    $ingressPatterns = @(
        "apiVersion: networking.k8s.io/v1",
        "kind: Ingress",
        "kubernetes.io/ingress.class.*kong",
        "Copyright \(c\) 2025 Cina.Club"
    )
    $ingressResult = Test-FileContent -FilePath $ingressPath -RequiredPatterns $ingressPatterns
    $results.Files["ingress.yaml"] = $ingressResult
    if (!$ingressResult.Valid) { $results.Valid = $false }
    
    # Check kustomization.yaml
    $kustomizationPath = "$BasePath/kustomization.yaml"
    $kustomizationPatterns = @(
        "apiVersion: kustomize.config.k8s.io/v1beta1",
        "kind: Kustomization",
        "resources:",
        "- ingress.yaml",
        "Copyright \(c\) 2025 Cina.Club"
    )
    $kustomizationResult = Test-FileContent -FilePath $kustomizationPath -RequiredPatterns $kustomizationPatterns
    $results.Files["kustomization.yaml"] = $kustomizationResult
    if (!$kustomizationResult.Valid) { $results.Valid = $false }
    
    return $results
}

function Validate-OverlayConfiguration {
    param($ServiceName, $OverlayPath, $Environment)
    
    $results = @{
        Valid = $true
        Issues = @()
        Files = @{}
    }
    
    # Check if overlay directory exists
    if (!(Test-Path $OverlayPath)) {
        $results.Valid = $false
        $results.Issues += "Overlay directory for $Environment does not exist"
        return $results
    }
    
    # Check kustomization.yaml
    $kustomizationPath = "$OverlayPath/kustomization.yaml"
    $kustomizationPatterns = @(
        "apiVersion: kustomize.config.k8s.io/v1beta1",
        "kind: Kustomization",
        "bases:",
        "../../base",
        "Copyright \(c\) 2025 Cina.Club"
    )
    $kustomizationResult = Test-FileContent -FilePath $kustomizationPath -RequiredPatterns $kustomizationPatterns
    $results.Files["kustomization.yaml"] = $kustomizationResult
    if (!$kustomizationResult.Valid) { $results.Valid = $false }
    
    # Check patch-ingress-host.yaml
    $patchPath = "$OverlayPath/patch-ingress-host.yaml"
    $patchPatterns = @(
        "apiVersion: networking.k8s.io/v1",
        "kind: Ingress",
        "Copyright \(c\) 2025 Cina.Club"
    )
    $patchResult = Test-FileContent -FilePath $patchPath -RequiredPatterns $patchPatterns
    $results.Files["patch-ingress-host.yaml"] = $patchResult
    if (!$patchResult.Valid) { $results.Valid = $false }
    
    return $results
}

# Main execution
Write-Host "🔍 CINA.CLUB Microservices Deploy Configuration Validation" -ForegroundColor Green
Write-Host "Copyright (c) 2025 Cina.Club" -ForegroundColor Yellow
Write-Host ""

$totalMicroservices = $expectedMicroservices.Count
$validMicroservices = 0
$issuesFound = 0
$validationResults = @{}

Write-Host "📊 Validating $totalMicroservices microservices..." -ForegroundColor Cyan
Write-Host ""

foreach ($serviceName in $expectedMicroservices) {
    $serviceNum = $expectedMicroservices.IndexOf($serviceName) + 1
    $progress = [math]::Round(($serviceNum / $totalMicroservices) * 100, 1)
    
    Write-Host "[$serviceNum/$totalMicroservices] ($progress%) Validating: $serviceName" -ForegroundColor Yellow
    
    $serviceResults = @{
        ServiceName = $serviceName
        BaseValid = $false
        OverlaysValid = @{}
        Issues = @()
    }
    
    $servicePath = "services/$serviceName"
    $deployPath = "$servicePath/deploy"
    
    # Check if deploy directory exists
    if (!(Test-Path $deployPath)) {
        $serviceResults.Issues += "Deploy directory does not exist"
        $issuesFound++
    } else {
        # Validate base configuration
        $basePath = "$deployPath/base"
        $baseResult = Validate-BaseConfiguration -ServiceName $serviceName -BasePath $basePath
        $serviceResults.BaseValid = $baseResult.Valid
        
        if (!$baseResult.Valid) {
            $serviceResults.Issues += "Base configuration issues: $($baseResult.Issues -join '; ')"
            $issuesFound++
        }
        
        # Validate overlay configurations
        $overlaysPath = "$deployPath/overlays"
        foreach ($env in $requiredStructure.overlays) {
            $overlayPath = "$overlaysPath/$env"
            $overlayResult = Validate-OverlayConfiguration -ServiceName $serviceName -OverlayPath $overlayPath -Environment $env
            $serviceResults.OverlaysValid[$env] = $overlayResult.Valid
            
            if (!$overlayResult.Valid) {
                $serviceResults.Issues += "Overlay $env issues: $($overlayResult.Issues -join '; ')"
                $issuesFound++
            }
        }
    }
    
    # Check if service is completely valid
    $isServiceValid = $serviceResults.BaseValid -and 
                     ($serviceResults.OverlaysValid["dev"] -eq $true) -and
                     ($serviceResults.OverlaysValid["staging"] -eq $true) -and
                     ($serviceResults.OverlaysValid["prod"] -eq $true)
    
    if ($isServiceValid) {
        $validMicroservices++
        Write-Host "  ✅ Valid" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Issues found" -ForegroundColor Red
        if ($Detailed) {
            foreach ($issue in $serviceResults.Issues) {
                Write-Host "    - $issue" -ForegroundColor Gray
            }
        }
    }
    
    $validationResults[$serviceName] = $serviceResults
}

Write-Host ""
Write-Host "📈 Validation Summary:" -ForegroundColor Cyan
Write-Host "  Total Microservices: $totalMicroservices" -ForegroundColor White
Write-Host "  Valid Configurations: $validMicroservices" -ForegroundColor Green
Write-Host "  Issues Found: $issuesFound" -ForegroundColor Red
Write-Host "  Success Rate: $([math]::Round(($validMicroservices / $totalMicroservices) * 100, 1))%" -ForegroundColor Yellow

# Generate detailed report
Write-Host ""
Write-Host "📋 Detailed Configuration Report:" -ForegroundColor Cyan

# Valid services
$validServices = $validationResults.Values | Where-Object { 
    $_.BaseValid -and $_.OverlaysValid["dev"] -and $_.OverlaysValid["staging"] -and $_.OverlaysValid["prod"] 
}

if ($validServices.Count -gt 0) {
    Write-Host ""
    Write-Host "✅ Valid Services ($($validServices.Count)):" -ForegroundColor Green
    foreach ($service in $validServices) {
        Write-Host "  - $($service.ServiceName)" -ForegroundColor White
    }
}

# Services with issues
$servicesWithIssues = $validationResults.Values | Where-Object { 
    !$_.BaseValid -or !$_.OverlaysValid["dev"] -or !$_.OverlaysValid["staging"] -or !$_.OverlaysValid["prod"] 
}

if ($servicesWithIssues.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ Services with Issues ($($servicesWithIssues.Count)):" -ForegroundColor Red
    foreach ($service in $servicesWithIssues) {
        Write-Host "  - $($service.ServiceName):" -ForegroundColor White
        foreach ($issue in $service.Issues) {
            Write-Host "    • $issue" -ForegroundColor Gray
        }
    }
}

# File structure summary
Write-Host ""
Write-Host "📁 File Structure Analysis:" -ForegroundColor Cyan
$totalExpectedFiles = $totalMicroservices * 8  # 2 base files + 6 overlay files (3 envs x 2 files each)
$actualFiles = 0

foreach ($serviceName in $expectedMicroservices) {
    $servicePath = "services/$serviceName/deploy"
    
    # Count base files
    if (Test-Path "$servicePath/base/ingress.yaml") { $actualFiles++ }
    if (Test-Path "$servicePath/base/kustomization.yaml") { $actualFiles++ }
    
    # Count overlay files
    foreach ($env in @("dev", "staging", "prod")) {
        if (Test-Path "$servicePath/overlays/$env/kustomization.yaml") { $actualFiles++ }
        if (Test-Path "$servicePath/overlays/$env/patch-ingress-host.yaml") { $actualFiles++ }
    }
}

Write-Host "  Expected Files: $totalExpectedFiles" -ForegroundColor White
Write-Host "  Actual Files: $actualFiles" -ForegroundColor White
Write-Host "  Completion Rate: $([math]::Round(($actualFiles / $totalExpectedFiles) * 100, 1))%" -ForegroundColor Yellow

# Next steps
Write-Host ""
Write-Host "🚀 Next Steps:" -ForegroundColor Cyan
if ($issuesFound -eq 0) {
    Write-Host "  🎉 All microservices deploy configurations are valid!" -ForegroundColor Green
    Write-Host "  ✅ Ready for production deployment" -ForegroundColor Green
    Write-Host ""
    Write-Host "  Deployment commands:" -ForegroundColor White
    Write-Host "    # Deploy all to dev:" -ForegroundColor Gray
    Write-Host "    find services -path '*/overlays/dev' | xargs -I {} kubectl apply -k {}" -ForegroundColor Gray
    Write-Host "    # Deploy specific service:" -ForegroundColor Gray
    Write-Host "    kubectl apply -k services/<service-name>/deploy/overlays/prod" -ForegroundColor Gray
} else {
    Write-Host "  1. Review and fix the issues listed above" -ForegroundColor White
    Write-Host "  2. Re-run validation: ./scripts/validate-microservices-deploy.ps1 -Detailed" -ForegroundColor White
    Write-Host "  3. Test configurations: kubectl apply --dry-run=client -k services/<service>/deploy/base" -ForegroundColor White
}

Write-Host ""
Write-Host "✨ Validation completed!" -ForegroundColor Green 