﻿好的，遵照您的指示，我们来生成一份为 `billing-service` (计费与订阅服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **产品目录与定价模型**: 引入更灵活、更强大的产品目录和定价模型，支持分层定价、按用量计费的多种模式。
2.  **订阅生命周期管理**: 详细定义订阅的完整状态机，包括试用(Trial)、宽限期(Grace Period)、暂停(Paused)等状态。
3.  **计费与发票引擎**: 细化计费周期的运行逻辑，以及发票(Invoice)和账单条目(Line Item)的生成过程。
4.  **优惠与折扣系统**: 增加对优惠券(Coupon)和折扣(Discount)的支持，这是商业化运营的关键。
5.  **分账与版税协同**: 明确与`shared-kb-service`等内容服务的分账协同逻辑。
6.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。

这份文档将描绘一个功能强大、灵活、准确且可审计的商业化引擎。

---

### CINA.CLUB - billing-service 需求规格说明书

**版本: 2.0 (生产级定义，集成高级定价与订阅模型)**  
**发布日期: 2025-06-20**  
**最后修订日期: 2025-06-20**  
**文档负责人:** [商业化/财务技术团队负责人名称]  
**审批人:** [CTO/CFO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了支持CINA.CLUB平台多样化、可演进的商业模式，包括高级功能订阅、付费内容访问、API按量使用等，需要一个集中、灵活、准确的计费与订阅管理系统。`billing-service` 的目的在于构建这样一个核心商业化引擎，它负责管理**产品目录、定价策略、用户订阅、用量计量、账单生成和支付编排**，是平台实现收入和商业价值闭环的关键。

#### 1.2. 服务范围
本服务 **负责**:
*   **产品目录管理**: 定义平台提供的所有可付费项目（`Product`）、其定价方案（`Price`）和权益（`Feature`）。
*   **用户订阅管理**: 处理用户对订阅类产品的购买、续订、升级、降级、暂停、取消等完整的生命周期。
*   **用量计量与追踪**: 接收和记录用户对按量计费资源（如AI Token消耗、API调用次数）的使用情况，并进行聚合。
*   **计费与发票引擎**: 根据订阅和用量，在计费周期（Billing Cycle）结束时自动生成详细的发票（`Invoice`）。
*   **优惠与折扣**: 管理和应用优惠券（`Coupon`）和折扣。
*   **支付编排**: 与`payment-service`（法币支付）和`cina-coin-ledger-service`（灵境币支付）协同，发起支付并处理结果。
*   **访问控制决策支持**: 为其他服务提供API，查询用户是否有权访问某个付费资源。
*   **分账协同**: 在收到付费内容相关的支付成功事件后，创建待分账记录，供后续处理。

本服务 **不负责**:
*   直接处理法定货币的支付网关交互 (由 `payment-service` 负责)。
*   管理灵境币的底层账本 (由 `cina-coin-ledger-service` 负责)。
*   发现和推荐产品 (由营销模块或AI助手负责)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用**: (间接) 用户通过UI购买订阅、查看账单。
*   **`shared-kb-service`, `ai-assistant-service`等资源服务**: 调用本服务检查访问权限和上报用量。
*   **`payment-service`, `cina-coin-ledger-service`**: (通过事件/回调) 通知本服务支付/扣款结果。
*   **`user-core-service`**: 本服务调用它来更新用户的会员状态。
*   **CINA.CLUB平台管理员/运营团队**: 通过管理后台管理产品目录、优惠券、查看用户订阅、处理计费问题。

#### 1.4. 定义与缩略语
*   **Product**: 平台销售的一个抽象商品，如“AI Pro会员”。
*   **Price**: 一个`Product`的具体定价方案，如“月付$9.99”或“年付$99.99”。
*   **Subscription**: 用户对某个`Price`的订阅关系。
*   **Usage-based Billing**: 按量计费。
*   **Invoice**: 发票/账单，一个计费周期的费用明细。
*   **Proration**: 按比例计费，用于订阅升降级。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`billing-service` 是CINA.CLUB商业化战略的“**发动机**”和“**财务官**”。它将平台提供的各种有价值的功能和服务转化为具体的产品，管理用户与这些产品之间的商业关系（订阅），并负责准确计算费用，是平台实现收入的核心。其设计必须兼顾灵活性以支持市场变化，以及严谨性以保证财务准确。

#### 2.2. 主要功能概述
*   灵活的“产品-价格”分离的产品目录。
*   完整的订阅生命周期状态机管理。
*   支持固定、按量和分层定价的计费引擎。
*   优惠券与折扣系统。
*   与支付/账本服务的解耦协同。

### 3. 核心流程图

#### 3.1. 订阅续订与计费流程 (后台任务)
```mermaid
sequenceDiagram
    participant Scheduler
    participant BillingService as BS
    participant DB
    participant PaymentService as PS
    participant NotificationService as NS

    Scheduler->>BS: 1. Trigger daily billing run
    BS->>DB: 2. Find subscriptions due for renewal today
    
    loop For each due subscription
        BS->>BS: 3. Create a new Invoice (status: DRAFT)
        BS->>DB: 4. Get usage records for this billing cycle
        BS->>BS: 5. Calculate usage costs & add to invoice
        BS->>BS: 6. Add subscription fee to invoice
        BS->>BS: 7. Apply any active discounts/coupons
        BS->>DB: 8. Finalize Invoice, update status to OPEN
        
        BS->>PS: 9. Initiate payment for the invoice amount
        PS-->>BS: (Payment processing...)
        
        alt Payment Succeeded
            BS->>DB: 10a. Update Invoice to PAID, update Subscription to ACTIVE with new cycle dates
            BS->>NS: 11a. Notify user of successful renewal
        else Payment Failed
            BS->>DB: 10b. Update Invoice to FAILED, update Subscription to PAST_DUE (enter grace period)
            BS->>NS: 11b. Notify user of payment failure
        end
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 产品目录与定价
*   **FR4.1.1 (产品与价格分离)**:
    *   **Product**: 定义一个抽象的服务或功能，如“AI Pro”，包含名称、描述和关联的`Feature`列表。
    *   **Price**: 定义一个`Product`的具体售卖方式，包含金额、货币、计费周期（月/年）、定价模型（`FLAT`, `PER_UNIT`）。一个`Product`可以有多个`Price`。
*   **FR4.1.2 (定价模型)**:
    *   **`FLAT`**: 固定费用，如每月$9.99。
    *   **`PER_UNIT`**: 按量计费，如每1000个AI Token $0.02。
    *   **`TIERED` (分层)**: （高级）阶梯定价，如前1000次API调用免费，1001-10000次每次$0.01。
*   **FR4.1.3 (权益管理)**: `Feature`定义了购买产品后可获得的具体权益，如“无限次PKB搜索”、“每月10000 AI Pro Tokens”。

#### 4.2. 订阅生命周期管理
*   **FR4.2.1 (状态机)**: `Subscription`必须有一个健壮的状态机：
    *   `TRIALING` -> `ACTIVE` | `CANCELED`
    *   `ACTIVE` -> `PAST_DUE` (支付失败) | `CANCELED` (用户取消)
    *   `PAST_DUE` -> `ACTIVE` (在宽限期内支付成功) | `INACTIVE` (宽限期结束仍未支付)
    *   `CANCELED` -> `INACTIVE` (在周期结束时)
*   **FR4.2.2 (试用)**: 支持为新用户提供固定天数的免费试用。试用结束时自动尝试首次扣款。
*   **FR4.2.3 (升降级)**: 支持用户在不同`Price`之间切换。必须支持**按比例计费(Proration)**，即时计算并收取差价或生成信用额度。
*   **FR4.2.4 (取消)**: 用户可以随时取消订阅，订阅将在当前计费周期结束时失效。

#### 4.3. 用量计量与计费
*   **FR4.3.1 (用量上报)**: 系统必须提供一个高吞吐量的内部API (`/internal/usage/record`)，供其他服务上报用户对计量资源的使用情况。请求必须是幂等的。
*   **FR4.3.2 (计费周期运行)**: 一个后台定时任务必须能为所有需要计费的用户自动运行，执行如核心流程图所示的计费逻辑。

#### 4.4. 优惠与折扣
*   **FR4.4.1 (优惠券)**: 支持管理员创建优惠券（`Coupon`），可定义折扣百分比或固定金额，以及有效期、使用次数、适用产品等。
*   **FR4.4.2 (应用)**: 用户在购买或订阅时可以应用一个有效的优惠券。系统必须在生成发票时正确应用折扣。

#### 4.5. 访问控制决策支持
*   **FR4.5.1 (权限检查API)**: 系统必须提供一个高效、低延迟的内部API (`/internal/access-check`)。
*   **FR4.5.2 (逻辑)**: 该API接收`userId`和`requiredFeature`，内部检查该用户是否有任何`ACTIVE`或`TRIALING`的订阅包含了此`Feature`。返回`{ "granted": boolean }`。此结果必须被调用方服务积极缓存。

#### 4.6. 分账协同
*   **FR4.6.1 (待分账记录)**: 当收到与付费内容相关的`PaymentSucceededEvent`时（事件中包含AI回复的`citations`元数据），本服务必须创建一条或多条`RevenueSharePendingRecord`记录。
*   **FR4.6.2 (异步处理)**: 一个独立的后台任务负责计算和执行实际的分账操作（调用`cina-coin-ledger-service`）。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/billing`
*   **认证**: 用户JWT, S2S认证, Admin角色JWT。
*   **核心端点**:
    *   `GET /products`: 获取可供购买的产品和价格列表。
    *   `POST /subscriptions`: 用户发起新订阅。Request: `{ price_id, coupon_code? }`
    *   `GET /me/subscriptions`: 获取用户的订阅列表。
    *   `PATCH /me/subscriptions/{id}`: 更新订阅（用于升降级）。
    *   `DELETE /me/subscriptions/{id}`: 取消订阅。
    *   `GET /me/invoices`: 获取用户账单列表。
    *   `POST /me/invoices/{id}/pay`: 触发账单支付。
*   **内部API (S2S)**:
    *   `POST /internal/usage/record`: 上报用量。
    *   `POST /internal/access-check`: 检查访问权限。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`products`**: `id`, `name`, `description`, `is_active`.
*   **`features`**: `id`, `key` (e.g., "AI_PRO_TOKENS"), `description`.
*   **`product_features` (Join Table)**.
*   **`prices`**: `id`, `product_id`, `amount`, `currency`, `interval` ("month", "year"), `pricing_model`.
*   **`subscriptions`**: `id`, `user_id`, `price_id`, `status`, `current_period_start`, `current_period_end`, `trial_end`, `cancel_at_period_end`.
*   **`usage_records`**: `id`, `user_id`, `feature_id`, `quantity`, `timestamp`, `invoice_id` (nullable).
*   **`invoices`**: `id`, `user_id`, `subscription_id`, `status`, `total_amount`, `due_date`.
*   **`invoice_line_items`**: `id`, `invoice_id`, `description`, `amount`, `price_id`, `usage_start`, `usage_end`.
*   **`coupons`**: `id`, `code`, `discount_type`, `discount_value`, `is_active`.
*   **`revenue_share_pending_records`**.

#### 6.2. 数据一致性
*   所有涉及金额、订阅状态变更的操作必须在**ACID事务**中进行。
*   使用**乐观锁**（`version`字段）处理对`Subscription`等核心记录的并发修改。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **`access-check` API**: 必须是极低延迟的（P99 < 30ms），因为它处于其他服务的关键路径上。需积极使用缓存。
*   **`usage/record` API**: 必须是高吞吐量的，且不应阻塞调用方服务（采用异步写入或消息队列）。
*   **计费运行**: 批处理任务的性能需能支持在数小时内完成对百万级用户的计费。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。本服务的故障会影响平台所有商业化功能。
*   **数据准确性**: 财务计算的准确性是第一要务。所有计费逻辑必须有严格的单元测试、集成测试，并有对账机制。

#### 7.3. 可扩展性需求
*   服务可水平扩展。数据库可读写分离。`usage_records`和`invoices`表可能增长非常快，需要考虑按时间分区策略。

#### 7.4. 安全性与可审计性
*   **安全**: Admin API必须有严格的RBAC。防止计费逻辑被利用产生不正确的账单。
*   **可审计性 (非常重要)**: 所有影响用户账单和订阅状态的操作都必须有详细、不可篡改的审计日志。必须能够追溯任何一笔费用的来源。

### 8. 技术约束与选型建议
*   **语言**: Go。其强类型和性能优势适合构建要求严谨的计费系统。
*   **数据库**: 必须选择支持强ACID事务的关系型数据库，**PostgreSQL** 是首选。
*   **后台任务**: 使用Kubernetes CronJob或`Temporal`/`Cadence`等专业的Workflow-as-Code工具来编排和执行复杂的计费运行。
*   **集成模式**: 与`payment-service`和`cina-coin-ledger-service`的交互是本服务的核心，接口契约和错误处理必须非常清晰和健壮。

---
这份版本2.0的SRS文档为`billing-service`的商业化引擎提供了坚实、灵活且面向未来的设计基础。它的实现需要对订阅制计费系统有深入的理解，并对系统的准确性、安全性和可靠性有极高的要求。