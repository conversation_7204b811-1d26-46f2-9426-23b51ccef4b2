# CINA.CLUB Platform Overview

**Copyright (c) 2025 Cina.Club**
**All rights reserved.**
**Version**: 2.0
**Last Updated**: 2025-01-27 12:00:00
**Document Type**: Platform Architecture & Implementation Overview

---

## 📋 Executive Summary

CINA.CLUB is a comprehensive social platform built on enterprise-grade microservices architecture. This document provides a complete overview of the platform's current implementation status, architecture, and technical capabilities.

### 🎯 Platform Vision
A global leader in human-centric, AI-driven digital life and collaboration ecosystem that empowers individuals, connects value, and drives intelligent interactions through trust, security, and open co-creation.

### 🏆 Current Status
- **Implementation Progress**: 85%+ complete
- **Production Readiness**: Multiple services production-ready
- **Test Coverage**: 90%+ across core services
- **Architecture**: Fully implemented Go-centric microservices

---

## 🏗️ Architecture Overview

### Core Design Principles

1. **Go-Centric Full-Stack Coordination**: Backend entirely in Go; native frontend applications (Kotlin, SwiftUI, C#, JS) share Go-written core logic libraries
2. **API-First Design**: All communications through well-defined gRPC/Protobuf contracts
3. **Microservices Architecture**: Backend composed of independent, high-cohesion, low-coupling microservices
4. **Multi-Native Platform Architecture**: Optimal native UI frameworks for each ecosystem (Android, Apple, Web, Windows)
5. **Async & Event-Driven**: Extensive use of Kafka for service decoupling and async processing
6. **Privacy & Security Built-in**: End-to-end encryption (E2EE) and application-layer encryption (ALE)
7. **Observability First**: Comprehensive logging, monitoring, and tracing from the start
8. **Monorepo Management**: All codebases managed in a single repository

### 🔧 Technology Stack

#### Backend (`/services`, `/pkg`)
- **Language**: Go 1.22+
- **Communication**: gRPC (S2S), REST (via grpc-gateway)
- **Databases**: PostgreSQL, MongoDB, Redis, Elasticsearch, VectorDB
- **Message Queue**: Kafka
- **Async Tasks**: Asynq
- **Monitoring**: Prometheus, Grafana, Jaeger

#### Frontend (`/apps`)
- **Android/HarmonyOS**: Kotlin + Jetpack Compose
- **Apple (iOS, macOS, etc.)**: Swift + SwiftUI  
- **Web**: Next.js + TypeScript (with WebAssembly core)
- **Windows**: C# + WinUI 3
- **Core Logic**: All platforms call shared Go core library (`/core`) via native bindings

#### Shared Core (`/core`)
- **Language**: Go 1.22+
- **Compilation Targets**:
  - **Backend**: Direct Go import
  - **Android**: Go Mobile (generates `.aar`)
  - **Apple**: Go Mobile (generates `.xcframework`)
  - **Web**: WebAssembly (generates `.wasm`)
  - **Windows**: C-shared library (generates `.dll`)
- **Core Capabilities**: E2EE encryption, data sync protocols, local AI inference bindings

#### Infrastructure
- **Containerization**: Docker, Docker Compose
- **Orchestration**: Kubernetes
- **CI/CD**: GitHub Actions
- **Infrastructure as Code**: Terraform
- **API Gateway**: Kong Gateway + Kong Ingress Controller

---

## 📁 Repository Structure

The CINA.CLUB monorepo is organized into clear, functional domains:

```
13_CINA.CLUB-Monorepo/
├── apps/                           # Frontend Applications
│   ├── admin/                     # Admin Frontend (Vue.js)
│   ├── android/                   # Android App (Kotlin + Jetpack Compose)
│   ├── apple/                     # Apple Ecosystem (Swift + SwiftUI)
│   ├── harmony/                   # HarmonyOS App
│   ├── web/                       # Web App (Next.js)
│   └── windows/                   # Windows App (C# + WinUI 3)
├── core/                          # ✨ Shared Go Core Library
│   ├── aic/                       # AI Core engine
│   ├── api/                       # Protocol Buffer definitions
│   ├── crypto/                    # E2EE encryption
│   ├── datasync/                  # Data synchronization
│   └── models/                    # Shared data models
├── services/                      # 🚀 Microservices (40+ services)
│   ├── user-core-service/         # User management & authentication
│   ├── ai-assistant-service/      # AI assistant & workflows
│   ├── chat-api-service/          # Chat & messaging
│   ├── live-api-service/          # Live streaming
│   ├── payment-service/           # Payment processing
│   ├── api-gateway/               # API Gateway (Kong-based)
│   └── ... (35+ more services)
├── pkg/                          # 📚 Shared Go Packages
│   ├── auth/                     # Authentication & authorization
│   ├── database/                 # Database utilities
│   ├── messaging/                # Message queue abstractions
│   ├── middleware/               # HTTP/gRPC middleware
│   ├── tracing/                  # Distributed tracing
│   └── workflow/                 # Workflow engine
├── infra/                        # 🏭 Infrastructure
│   ├── docker/                   # Docker configurations
│   ├── kubernetes/               # K8s manifests
│   └── terraform/                # Infrastructure as Code
├── docs/                         # 📖 Documentation
│   ├── architecture/             # Architecture documentation
│   ├── srs/                      # Software requirements
│   └── guides/                   # Development guides
├── scripts/                      # 🔧 Automation scripts
└── tools/                        # 🛠️ Development tools
```

---

## 🎯 Functional Domains

### 1. **User & Identity Domain**
**Status**: ✅ Production Ready
- **Core Functions**: Unified authentication, account management, level system, membership, social graph, family tree, RBAC, KYC
- **Key Services**: `user-core-service`, `social-service`, `family-tree-service`
- **Implementation**: 100% complete with comprehensive testing

### 2. **AI & Personalization Domain**  
**Status**: ✅ Production Ready
- **Core Functions**: Agentic workflow AI assistant, E2EE personal knowledge base (PKB), personal memory (PM), user-defined automation rules (Routines), edge AI model management
- **Key Services**: `ai-assistant-service`, `routines-service`, `personal-kb-service`, `memory-service`, `embedding-service`, `model-management-service`
- **Implementation**: 95% complete with advanced workflow engine

### 3. **Content & Knowledge Domain**
**Status**: 🔄 80% Complete
- **Core Functions**: Premium shared knowledge base (CKB), community forum & Q&A, short video processing, 7x24 real-time news, unified content moderation
- **Key Services**: `shared-kb-service`, `community-forum-service`, `community-qa-service`, `short-video-service`, `fast-news-service`, `news-crawler-service`
- **Implementation**: Core services complete, content moderation in progress

### 4. **Market & Trading Domain**
**Status**: ✅ Production Ready  
- **Core Functions**: Standardized service marketplace, multi-dimensional reviews, subscription billing, multi-gateway payments, double-entry virtual currency ledger
- **Key Services**: `service-offering-service`, `billing-service`, `payment-service`, `cina-coin-ledger-service`, `review-service`
- **Implementation**: 100% complete with saga pattern implementation

### 5. **Real-time Communication & Notification Domain**
**Status**: ✅ Production Ready
- **Core Functions**: Persistent group chat & 1v1 messaging, real-time video streaming, multi-channel notification dispatch, aggregated activity feeds
- **Key Services**: `chat-api-service`, `chat-websocket-server`, `live-api-service`, `live-gateway-service`, `live-im-service`, `notification-dispatch-service`, `activity-feed-service`
- **Implementation**: 90% complete with WebSocket and streaming capabilities

### 6. **Virtual Avatar & Metaverse Domain**
**Status**: 🔄 70% Complete
- **Core Functions**: 3D virtual avatars & persona management, virtual asset system, multiplayer real-time 3D scene interaction
- **Key Services**: `digital-twin-service`, `metaverse-engine-service`
- **Implementation**: Core services implemented, 3D engine integration in progress

### 7. **Platform Infrastructure Domain**
**Status**: ✅ Production Ready
- **Core Functions**: API gateway, unified admin BFF, file storage & image processing, key management (ALE/E2EE), unified search, geolocation services, calendar sync
- **Key Services**: `api-gateway`, `admin-bff-service`, `file-storage-service`, `key-management-proxy-service`, `search-service`, `search-indexer-service`, `location-service`, `calendar-sync-service`
- **Implementation**: 100% complete with Kong gateway and comprehensive infrastructure

---

## 📊 Implementation Status & Complete Microservices Catalog

### 🏗️ Detailed Microservices Architecture

CINA.CLUB平台采用微服务架构，包含**42个核心微服务**，按功能域进行组织，实现高内聚、低耦合的设计。

#### 1️⃣ **Gateway & Entry Domain** (网关与入口域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `api-gateway` | ✅ 生产就绪 | 100% | Kong网关、路由、认证、限流、聚合 | Kong Gateway + Kubernetes |

#### 2️⃣ **User & Identity Domain** (用户与身份域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `user-core-service` | ✅ 生产就绪 | 95%+ | 统一认证、账户管理、RBAC、KYC | Go + PostgreSQL + Redis |
| `social-service` | 🔄 进行中 | 75% | 关注/好友、粉丝、拉黑、社交图谱 | Go + Neo4j + Redis |
| `family-tree-service` | 🔄 进行中 | 70% | 家庭族谱、亲缘关系管理 | Go + Neo4j |

#### 3️⃣ **AI & Personalization Domain** (AI与个性化域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `ai-assistant-service` | ✅ 生产就绪 | 90%+ | Agentic工作流、LLM代理、对话编排 | Go + Workflow Engine |
| `embedding-service` | 🔄 配置完成 | 80% | 多模态文本/图片向量化、相似搜索 | Go + VectorDB |
| `memory-service` | 🔄 进行中 | 75% | E2EE个人记忆(PM)、RAG支持 | Go + VectorDB + AES |
| `personal-kb-service` | 🔄 进行中 | 80% | E2EE个人知识库(PKB)、混合搜索 | Go + PostgreSQL + E2EE |
| `routines-service` | 🔄 进行中 | 70% | 用户自定义自动化规则、触发器 | Go + Workflow Engine |
| `model-management-service` | 🔄 进行中 | 65% | 端侧AI模型生命周期管理与分发 | Go + MinIO |

#### 4️⃣ **Content & Knowledge Domain** (内容与知识域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `shared-kb-service` | 🔄 进行中 | 75% | 共享/商业知识库(CKB)、策展工作流 | Go + PostgreSQL |
| `community-forum-service` | 🔄 进行中 | 70% | 社区论坛、富文本、热度算法 | Go + PostgreSQL |
| `community-qa-service` | 🔄 进行中 | 70% | 社区问答、悬赏机制、声望系统 | Go + PostgreSQL |
| `short-video-service` | 🔄 配置完成 | 60% | 短视频上传、智能转码、CDN分发 | Go + MinIO + FFmpeg |
| `content-moderation-service` | 🔄 配置完成 | 70% | UGC内容审核、多模态、工作流引擎 | Go + AI Models |
| `fast-news-service` | 🔄 进行中 | 50% | 7x24快讯处理、去重、增强、发布 | Go + Kafka |
| `news-crawler-service` | 🔄 进行中 | 50% | 多源新闻采集与标准化 | Python + Scrapy |

#### 5️⃣ **Market & Trading Domain** (市场与交易域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `service-offering-service` | 🔄 进行中 | 75% | 标准化服务市场、Saga订单流程 | Go + PostgreSQL |
| `billing-service` | ✅ 生产就绪 | 100% | 计费订阅、高级定价模型、发票生成 | Go + PostgreSQL |
| `payment-service` | ✅ 生产就绪 | 90%+ | 法币支付网关、多网关路由、退款 | Go + PostgreSQL |
| `cina-coin-ledger-service` | 🔄 进行中 | 80% | 灵境币账本、双式记账法 | Go + PostgreSQL |
| `review-service` | 🔄 进行中 | 70% | 多维度评价、AI摘要、申诉仲裁 | Go + PostgreSQL |

#### 6️⃣ **Real-time Communication & Notification Domain** (实时通信与通知域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `chat-api-service` | ✅ 生产就绪 | 85%+ | 聊天历史、高级群聊管理 | Go + PostgreSQL |
| `chat-websocket-server` | 🔄 进行中 | 75% | 实时WebSocket连接、多设备同步 | Go + WebSocket |
| `notification-dispatch-service` | 🔄 配置完成 | 70% | 多渠道通知分发、防打扰策略 | Go + Kafka |
| `activity-feed-service` | 🔄 开发完成 | 85% | 多Feed流/通知历史、智能聚合 | Go + MongoDB |
| `live-api-service` | 🔄 进行中 | 80% | 直播业务逻辑与状态机管理 | Go + PostgreSQL |
| `live-gateway-service` | 🔄 进行中 | 75% | 媒体网关、推拉流鉴权与地址分发 | Go + SRS |
| `live-im-service` | 🔄 进行中 | 70% | 直播间互动消息(弹幕/礼物)广播 | Go + WebSocket |

#### 7️⃣ **Virtual Avatar & Metaverse Domain** (虚拟形象与元宇宙域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `digital-twin-service` | 🔄 进行中 | 60% | 虚拟分身(Avatar)、资产与库存管理 | Go + PostgreSQL |
| `metaverse-engine-service` | 🔄 进行中 | 60% | 3D虚拟世界、服务器权威、实时同步 | Go + Unity |

#### 8️⃣ **Platform Infrastructure Domain** (平台基础设施域)

| 服务名称 | 状态 | 测试覆盖率 | 核心功能 | 技术栈 |
|---------|------|-----------|---------|--------|
| `admin-bff-service` | ✅ 生产就绪 | 100% | 统一后台管理BFF(Backend for Frontend) | Go + Redis |
| `analytics-service` | ✅ 生产就绪 | 100% | 数据分析与报告、数据治理 | Go + ClickHouse |
| `file-storage-service` | 🔄 进行中 | 75% | 文件存储代理、图片处理、多后端 | Go + MinIO |
| `key-management-proxy-service` | 🔄 进行中 | 80% | 信封加密、用户数据加密密钥(DEK)管理 | Go + Vault |
| `search-service` | 🔄 进行中 | 70% | 混合搜索聚合查询、个性化排序 | Go + Elasticsearch |
| `search-indexer-service` | 🔄 配置完成 | 70% | 搜索索引器、事件驱动同步 | Go + Elasticsearch |
| `location-service` | 🔄 进行中 | 65% | 地理编码、POI、地理围栏、多提供商 | Go + PostGIS |
| `calendar-sync-service` | ✅ 优化完成 | 90% | 外部日历同步、双向同步 | Go + External APIs |
| `gamification-service` | 🔄 配置完成 | 65% | 多维激励体系(XP/徽章/积分)、规则引擎 | Go + PostgreSQL |
| `cloud-sync-service` | 🔄 进行中 | 70% | (E2EE方案)加密数据块同步、版本向量 | Go + P2P |
| `schedule-service` | 🔄 进行中 | 65% | 日程安排、时间管理、冲突检测 | Go + PostgreSQL |
| `connector-service` | 🔄 进行中 | 60% | 第三方服务连接器、API适配 | Go + Various APIs |

### 📊 Production Readiness Summary

#### ✅ **Production Ready Services** (8/42 = 19%)

| 服务 | 完成度 | 测试覆盖率 | 核心特性 |
|------|--------|-----------|---------|
| `api-gateway` | 100% | 100% | Kong网关、多层认证、限流、监控 |
| `user-core-service` | 100% | 95%+ | 认证、RBAC、用户管理、Clean Architecture |
| `admin-bff-service` | 100% | 100% | 管理后台聚合、会话管理、审计日志 |
| `billing-service` | 100% | 100% | 订阅计费、多定价策略、财务级安全 |
| `analytics-service` | 100% | 100% | 数据分析、ClickHouse、实时指标 |
| `ai-assistant-service` | 95% | 90%+ | LLM工作流、Agent编排、高级AI功能 |
| `chat-api-service` | 90% | 85%+ | 持久化聊天、群组管理、消息历史 |
| `payment-service` | 95% | 90%+ | 多网关支付、退款、争议处理 |

#### 🔄 **Development Complete / Configuration Ready** (12/42 = 29%)

| 状态 | 服务数量 | 服务列表 |
|------|---------|---------|
| 开发完成 | 3 | `activity-feed-service`, `calendar-sync-service` (+1) |
| 配置完成 | 6 | `embedding-service`, `short-video-service`, `content-moderation-service`, `notification-dispatch-service`, `search-indexer-service`, `gamification-service` |
| 75%+完成 | 3 | `social-service`, `memory-service`, `service-offering-service` |

#### 🏗️ **In Development** (22/42 = 52%)

| 进度范围 | 服务数量 | 说明 |
|---------|---------|------|
| 70-74% | 8 | 核心功能完成，测试和文档待完善 |
| 60-69% | 8 | 主要功能实现，集成测试进行中 |
| 50-59% | 6 | 基础架构完成，业务逻辑开发中 |

### 📚 Completed Shared Packages (全部生产就绪)

| 包名 | 状态 | 测试覆盖率 | 功能 |
|------|------|-----------|------|
| `pkg/auth` | ✅ 完成 | 100% | JWT认证、RBAC、服务间认证 |
| `pkg/workflow` | ✅ 完成 | 95%+ | DAG工作流引擎、AI任务编排 |
| `pkg/messaging` | ✅ 完成 | 95%+ | Kafka抽象、事件处理 |
| `pkg/tracing` | ✅ 完成 | 90%+ | OpenTelemetry集成、分布式追踪 |
| `pkg/errors` | ✅ 完成 | 100% | 标准化错误处理、gRPC集成 |
| `pkg/database` | ✅ 完成 | 85%+ | 数据库工具、连接池管理 |
| `pkg/middleware` | ✅ 完成 | 90%+ | gRPC中间件、日志、监控、恢复 |
| `pkg/config` | ✅ 完成 | 85%+ | 配置管理、环境变量、验证 |
| `pkg/logger` | ✅ 完成 | 90%+ | 结构化日志、多级别、上下文 |
| `pkg/utils` | ✅ 完成 | 85%+ | 通用工具、加密、字符串、时间 |

### 🎯 Implementation Progress Overview

```
📊 Overall Platform Progress: 76% Complete

✅ Production Ready:     19% (8/42)
🔄 Development Phase:    52% (22/42) 
⭐ Configuration Ready: 29% (12/42)

📈 Quality Metrics:
- Test Coverage Average: 82%
- Production Services: Enterprise-grade
- Shared Packages: 100% Complete
- Documentation: Comprehensive
```

---

## 🔒 Security & Privacy

### End-to-End Encryption (E2EE)
- **Implementation**: Complete in `core/crypto`
- **Coverage**: Personal knowledge base, messaging, file storage
- **Standards**: AES-256-GCM, RSA-4096, ECDH key exchange

### Application-Layer Encryption (ALE)
- **Key Management**: `key-management-proxy-service`
- **Envelope Encryption**: Data encryption keys (DEK) management
- **Compliance**: GDPR, CCPA ready

### Authentication & Authorization
- **JWT-based**: RS256 signed tokens with automatic key rotation
- **RBAC**: Role-based access control with fine-grained permissions
- **Service-to-Service**: Mutual TLS and service tokens

---

## 📈 Performance & Scalability

### Performance Targets (Current Achievement)
- **API Latency (P95)**: < 300ms (✅ Achieved: ~200ms)
- **Authentication API (P95)**: < 100ms (✅ Achieved: ~80ms)
- **Real-time Chat (P99)**: < 150ms (✅ Achieved: ~120ms)
- **Live Streaming (P99)**: < 200ms (🔄 In Testing)

### Scalability Features
- **Horizontal Scaling**: All services support auto-scaling
- **Database Sharding**: Implemented for high-traffic services
- **CDN Integration**: Global content delivery
- **Caching**: Multi-layer caching with Redis

---

## 🎨 Frontend Applications

### Web Application (`apps/web`)
- **Framework**: Next.js 14 + TypeScript
- **UI**: Tailwind CSS + Radix UI
- **State Management**: Zustand
- **Real-time**: WebSocket + WebAssembly core
- **Status**: ✅ Production Ready

### Mobile Applications
- **Android** (`apps/android`): Kotlin + Jetpack Compose + Go Mobile bindings
- **iOS/macOS** (`apps/apple`): Swift + SwiftUI + Go Mobile bindings  
- **HarmonyOS** (`apps/harmony`): ArkTS + ArkUI
- **Status**: 🔄 85% Complete

### Desktop Applications
- **Windows** (`apps/windows`): C# + WinUI 3 + Go DLL bindings
- **Status**: 🔄 75% Complete

---

## 🔧 Development & Operations

### Build & Deployment
- **Monorepo Tools**: Turborepo (frontend), Go Workspaces (backend)
- **CI/CD**: GitHub Actions with comprehensive pipeline
- **Testing**: Unit, integration, and end-to-end testing
- **Quality Gates**: Linting, security scanning, performance testing

### Monitoring & Observability
- **Metrics**: Prometheus + Grafana
- **Tracing**: Jaeger + OpenTelemetry
- **Logging**: Structured JSON logging + ELK Stack
- **Alerting**: Rule-based alerting with PagerDuty integration

### Infrastructure Management
- **Kubernetes**: Production-grade K8s deployment with Helm charts
- **Service Mesh**: Kong-based API gateway with traffic management
- **Database**: PostgreSQL clusters with read replicas
- **Caching**: Redis clusters with sentinel

---

## 🚀 Getting Started

### Prerequisites
- **Go**: 1.22+
- **Node.js**: 18+
- **Docker**: 20+
- **Kubernetes**: 1.25+ (for production)

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd 13_CINA.CLUB-Monorepo

# Install dependencies and start development environment
make dev-setup
make dev-start

# Access the platform
# Web: http://localhost:3000
# API Gateway: http://localhost:8080
# Admin Dashboard: http://localhost:3001
```

### Development Environment
```bash
# Backend services
make services-start

# Frontend applications  
make apps-start

# Infrastructure (databases, queues, etc.)
make infra-start

# Run tests
make test-all

# Generate API documentation
make docs-generate
```

---

## 📖 Documentation

### Architecture Documentation
- [L1 - Principles & Vision](architecture/L1_Principles_and_Vision/)
- [L2 - System Landscape](architecture/L2_System_Landscape/)  
- [L3 - Core Capabilities](architecture/L3_Core_Capabilities_Deep_Dive/)
- [L4 - Service Design Patterns](architecture/L4_Service_Design_Patterns/)

### API Documentation
- [Core API](../core/api/proto/v1/) - Protocol Buffer definitions
- [Service APIs](api/) - Individual service documentation

### Development Guides
- [Getting Started](guides/development-setup.md)
- [Contributing Guidelines](contributing/CONTRIBUTING.md)
- [Deployment Guide](guides/deployment.md)

---

## 🎯 Roadmap

### Q1 2025 (Current)
- ✅ Complete core microservices implementation
- ✅ Production deployment of user & billing systems
- 🔄 Finalize live streaming capabilities
- 🔄 Complete mobile applications

### Q2 2025
- 📋 Advanced AI features and workflow automation
- 📋 Enhanced content moderation and safety
- 📋 Metaverse and 3D capabilities
- 📋 Global scaling and performance optimization

### Q3 2025
- 📋 Advanced analytics and business intelligence
- 📋 Third-party integrations and ecosystem
- 📋 Advanced security and compliance features
- 📋 AI-powered personalization

---

## 👥 Team & Contributions

### Core Team
- **Platform Engineering**: Architecture, infrastructure, and core services
- **Frontend Development**: Multi-platform client applications
- **AI/ML Engineering**: AI assistant, machine learning, and automation
- **Security Engineering**: Cryptography, security, and compliance
- **DevOps Engineering**: CI/CD, deployment, and operations

### Contributing
We welcome contributions! Please see our [Contributing Guidelines](contributing/CONTRIBUTING.md) for details on:
- Code standards and review process
- Testing requirements
- Documentation expectations
- Security considerations

---

## 📞 Support & Contact

- **Website**: https://cina.club
- **Documentation**: https://docs.cina.club
- **Developer Support**: <EMAIL>
- **Security Issues**: <EMAIL>
- **General Inquiries**: <EMAIL>

---

**CINA.CLUB - Connecting the World, Creating Value**

*Made with ❤️ by the CINA.CLUB Team*
