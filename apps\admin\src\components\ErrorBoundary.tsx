/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 15:35:00
 * Modified: 2025-01-23 19:50:00
 */

import React from 'react';
import { Button, Result, Card, Typography, Space } from 'antd';
import { BugOutlined, RedoOutlined, SendOutlined } from '@ant-design/icons';
import { ErrorBoundary as ReactErrorBoundary, FallbackProps } from 'react-error-boundary';
import { logger } from '@/lib/logger';

const { Paragraph, Text } = Typography;

/**
 * The UI component to display when an error is caught.
 */
const ErrorFallback: React.FC<FallbackProps> = ({ error, resetErrorBoundary }) => {
  const errorId = `err-${Date.now()}`;

  const handleReport = () => {
    // In a real app, this could open a feedback modal
    // For now, we just log it with more context.
    logger.error(error, {
      errorId,
      message: 'User explicitly reported this error.',
      componentStack: (error as any).componentStack,
    });
    alert(`Thank you for your report! Error ID: ${errorId}`);
  };

  return (
    <Card style={{ margin: '20px' }}>
      <Result
        status="error"
        title="Something Went Wrong"
        subTitle="An unexpected error occurred. Please try the action again."
        extra={[
          <Button icon={<RedoOutlined />} key="reload" type="primary" onClick={resetErrorBoundary}>
            Try Again
          </Button>,
          <Button icon={<SendOutlined />} key="report" onClick={handleReport}>
            Report Feedback
          </Button>,
        ]}
      >
        <div style={{ background: '#fff0f0', border: '1px solid #ffccc7', padding: '16px', borderRadius: '4px', textAlign: 'left' }}>
          <Paragraph>
            <Text strong>Error Details:</Text>
          </Paragraph>
          <Paragraph>
            <Text code>{error.message}</Text>
          </Paragraph>
          {import.meta.env.DEV && (
            <details style={{ whiteSpace: 'pre-wrap', marginTop: '12px' }}>
              <summary>Component Stack</summary>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {(error as any).componentStack}
              </Text>
            </details>
          )}
          <Paragraph style={{ marginTop: '16px' }}>
            <Text type="secondary">Error ID: {errorId}</Text>
          </Paragraph>
        </div>
      </Result>
    </Card>
  );
};

/**
 * Logs the error to a reporting service like Sentry.
 */
const logErrorToService = (error: Error, info: React.ErrorInfo) => {
  logger.error(error, { componentStack: info.componentStack });
};

/**
 * A wrapper around the `react-error-boundary` component that pre-configures
 * it with our logging function and fallback UI.
 */
export const ErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ReactErrorBoundary
      onReset={() => {
        // Can add logic here to reset app state if needed
      }}
      FallbackComponent={ErrorFallback}
      onError={logErrorToService}
    >
      {children}
    </ReactErrorBoundary>
  );
}; 