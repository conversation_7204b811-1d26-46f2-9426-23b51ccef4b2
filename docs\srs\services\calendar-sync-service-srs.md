﻿好的，遵照您的指示，我们来生成一份为 `calendar-sync-service` (日历同步服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **双向同步逻辑深化**: 详细定义双向同步中的冲突检测、解决策略以及数据溯源，这是最复杂的部分。
2.  **Webhook与增量同步强化**: 将Webhook作为首选的实时同步触发机制，并细化`syncToken`/`deltaLink`的使用。
3.  **多日历与多账户支持**: 明确支持一个用户连接多个外部日历账户（如一个Google账户，一个Microsoft账户），并在每个账户下选择同步多个日历。
4.  **错误处理与重授权流程**: 详细定义处理外部API错误、凭证失效和引导用户重新授权的流程。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、健壮、且能应对复杂现实世界同步场景的日历集成服务。

---

### CINA.CLUB - calendar-sync-service 需求规格说明书

**版本: 2.0 (生产级定义，支持高级双向同步与多账户)**  
**发布日期: 2025-06-20**  
**最后修订日期: 2025-06-20**  
**文档负责人:** [平台集成团队负责人/架构师名称]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了让CINA.CLUB的日程管理功能无缝融入用户的现有工作流，`calendar-sync-service` 旨在提供一个强大、可靠的桥梁，实现CINA.CLUB日程与用户主流第三方日历服务（如Google Calendar, Microsoft 365 Calendar）之间的数据同步。这能帮助用户在一个地方查看和管理所有安排，避免重复录入和日程冲突，从而极大提升日程管理的整体效率和用户体验。

#### 1.2. 服务范围
本服务 **负责**:
*   **多外部日历账户连接管理**: 处理OAuth 2.0流程，安全地存储和刷新**多个**外部日历账户的访问凭证。
*   **双向同步引擎**: 实现单向（CINA.CLUB -> 外部，或 外部 -> CINA.CLUB）和**健壮的双向**的日程数据同步。
*   **数据映射与转换**: 在CINA.CLUB日程模型与各外部日历平台的事件模型之间进行精确转换，特别关注重复日程（RRULE）、参与者和时区。
*   **冲突检测与解决**: 识别并根据预设策略处理双向同步中的数据编辑冲突。
*   **实时与后台同步**:
    *   **实时 (Webhook)**: 接收外部日历平台的实时变更通知（Push Notifications），触发即时同步。
    *   **后台 (Polling)**: 定期轮询外部日历API，作为Webhook的补充和容错。
    *   **事件驱动**: 消费来自`schedule-service`的事件，即时将CINA.CLUB的日程变更同步出去。

本服务 **不负责**:
*   存储CINA.CLUB日程的主数据 (由`schedule-service`负责)。
*   提供日程管理的UI (由客户端App负责)。
*   用户核心身份认证 (由`user-core-service`负责)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用**: (间接) 用户通过UI发起连接外部日历、配置同步选项。
*   **`schedule-service`**: (主要协同方) 通过消息队列或API通知本服务有日程变更需要同步；本服务通过API调用`schedule-service`来更新CINA.CLUB的日程。
*   **`notification-dispatch-service`**: (被本服务调用) 发送同步状态或需要用户重新授权的通知。
*   **`key-management-proxy-service`**: (被本服务调用) 用于加密存储外部日历的高度敏感的`refresh_token`。

#### 1.4. 定义与缩略语
*   **Provider**: 外部日历服务提供商 (e.g., `GOOGLE`, `MICROSOFT_GRAPH`)。
*   **Connection**: 用户的一个CINA.CLUB账户与一个外部平台账户的授权连接。
*   **Sync Mapping**: 一个具体的同步配置，将CINA.CLUB的一个日历与外部的一个日历进行映射。
*   **RRULE**: iCalendar Recurrence Rule format。
*   **Sync Token / Delta Link**: 外部API提供的用于增量同步的令牌或链接。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`calendar-sync-service` 是一个专注于系统集成的**中间件服务**。它作为CINA.CLUB平台与外部主流日历生态系统之间的“**数据同步总线**”和“**协议翻译官**”，扩展了CINA.CLUB日程管理功能的边界，提升了其在用户工作和生活中的实用性和粘性。

#### 2.2. 主要功能概述
*   支持多账户、多日历的连接与同步配置。
*   以Webhook为主、Polling为辅的混合式实时同步触发机制。
*   健壮的、支持冲突解决的双向同步引擎。
*   精确的、支持复杂重复日程的数据映射。

### 3. 核心流程图

#### 3.1. 双向同步（外部日历变更）流程
```mermaid
sequenceDiagram
    participant ExternalCalendar as "External Calendar (e.g., Google)"
    participant CalendarSyncService as CSS
    participant DB as "PostgreSQL"
    participant ScheduleService as SS
    participant KMSProxy

    ExternalCalendar->>CSS: 1. **[Webhook]** Send Push Notification (resource changed)
    
    CSS->>ExternalCalendar: 2. Call API with syncToken to get incremental changes
    ExternalCalendar-->>CSS: (List of changed/deleted events)
    
    loop For each changed external event
        CSS->>DB: 3. Find corresponding cinaScheduleId from SyncMapping table
        
        alt Event is new
            CSS->>CSS: 4a. Map external event to CINA.CLUB schedule model
            CSS->>SS: 5a. Call schedule-service to create new schedule
            SS-->>CSS: (New cinaScheduleId)
            CSS->>DB: 6a. Create a new SyncMapping record
        
        else Event is an update
            CSS->>SS: 4b. Get current CINA schedule version
            alt No Conflict (external version > local version)
                CSS->>CSS: 5b. Map and apply changes
                CSS->>SS: 6b. Call schedule-service to update schedule
            else Conflict Detected
                CSS->>CSS: 5c. **[Resolve Conflict]** Apply strategy (e.g., external wins)
                CSS->>SS: 6c. Force update CINA schedule with resolved data
                CSS->>CSS: 7c. Log the conflict resolution
            end
        end
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 外部日历账户连接管理
*   **FR4.1.1 (多账户连接)**: 用户必须能连接**多个**不同或相同类型的外部日历账户（如一个个人Google账户，一个工作Google账户）。
*   **FR4.1.2 (OAuth流程)**: 系统必须为每个Provider正确处理OAuth 2.0授权码流程，并请求最小必要权限 (scopes)。
*   **FR4.1.3 (凭证安全存储)**: 系统必须使用`key-management-proxy-service`来加密存储每个连接的`refresh_token`。`access_token`可与过期时间一同存储。
*   **FR4.1.4 (日历选择与映射)**: 连接成功后，系统必须能获取该账户下的日历列表，并允许用户**为每个外部日历创建独立的同步映射(Sync Mapping)**，配置其同步方向。
*   **FR4.1.5 (凭证失效处理)**: 当使用`refresh_token`失败（如用户在外部撤销授权）时，系统必须将对应`Connection`的状态标记为`NEEDS_REAUTH`，并通知用户。

#### 4.2. 数据同步引擎
*   **FR4.2.1 (数据映射)**:
    *   必须能精确地在CINA.CLUB的Schedule模型与各平台的事件模型之间进行转换，正确处理核心字段、**重复日程(RRULE, EXDATE, Overrides)**、以及**参与者(Attendees)**及其回复状态。
*   **FR4.2.2 (ID关联)**: 系统必须在`sync_mappings`表中，为每个同步的日程/事件对，存储其`cina_schedule_id`和`external_event_id`，以及**`external_etag` (或类似的版本标识)**。这是更新和冲突检测的基础。

#### 4.3. 双向同步与冲突解决
*   **FR4.3.1 (冲突检测)**:
    *   **场景**: 在将本地变更推送到外部，或处理外部变更时，如果发现外部事件的`etag`与本地存储的`external_etag`不匹配，则判定为可能存在冲突。
    *   **逻辑**: 比较本地日程的`updated_at`和外部事件的`lastModified`时间戳。
*   **FR4.3.2 (解决策略)**: 系统必须支持可为每个Sync Mapping配置的冲突解决策略：
    *   `EXTERNAL_WINS` (默认): 以外部日历的版本为准，覆盖CINA.CLUB的修改。
    *   `LOCAL_WINS`: 以CINA.CLUB的版本为准。
    *   `DUPLICATE`: （安全选项）在CINA.CLUB中创建一个新的、冲突的日程，并通知用户手动合并。
*   **FR4.3.3 (冲突日志)**: 所有检测到的冲突和采用的解决方法都必须被详细记录。

#### 4.4. 增量同步与Webhooks
*   **FR4.4.1 (Webhook优先)**: 系统必须能提供Webhook端点，并在外部日历平台注册，以接收实时变更通知。这是实现低延迟同步的首选方式。
*   **FR4.4.2 (增量拉取)**: 在收到Webhook通知或执行定时轮询时，系统必须使用提供商支持的增量同步机制（`syncToken`/`deltaLink`）来获取变更，避免全量拉取。
*   **FR4.4.3 (定时轮询 - 补充)**: 系统必须有一个后台定时任务（如每小时），对所有连接执行一次增量拉取，以处理可能丢失的Webhook通知。

#### 4.5. 事件驱动同步 (CINA.CLUB -> 外部)
*   **FR4.5.1 (事件消费)**: 系统必须消费来自`schedule-service`的`ScheduleChangedEvent`。
*   **FR4.5.2 (即时推送)**: 收到事件后，根据`cina_schedule_id`找到所有相关的`SyncMapping`，并立即将变更推送到对应的外部日历。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 用户端RESTful API接口
*   **版本**: `/api/v1/calendar-sync`
*   **认证**: 用户JWT。
*   **核心端点**:
    *   `GET /connections/me`: 获取当前用户已连接的所有外部日历账户。
    *   `POST /connections/{providerType}/initiate`: 发起OAuth授权。
    *   `GET /connections/{providerType}/callback`: OAuth回调端点。
    *   `DELETE /connections/me/{connectionId}`: 断开并删除一个账户连接。
    *   `GET /connections/me/{connectionId}/calendars`: 获取某连接下的外部日历列表。
    *   `POST /mappings`: 创建一个新的同步映射。Request: `{ connection_id, external_calendar_id, cina_calendar_id, sync_direction, conflict_policy }`
    *   `GET /mappings/me`: 获取用户所有的同步映射配置。
    *   `PUT /mappings/me/{mappingId}`: 更新同步映射配置。

#### 5.2. 内部服务接口 (出站)
*   -> **外部日历提供商API** (Google, MS Graph, etc.)
*   -> **`schedule-service`** (gRPC/HTTP Client): CRUD日程。
*   -> **`key-management-proxy-service`** (gRPC/HTTP Client): 加解密`refresh_token`。
*   -> **`notification-dispatch-service`** (via MQ): 发送通知。

#### 5.3. 消息队列事件契约 (入站)
*   **Topic**: `hina_schedule_events`
*   **Event**: `ScheduleChangedEvent { scheduleId, userId, changeType, ... }`

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`user_calendar_connections`**: `id`, `user_id`, `provider_type`, `external_user_id`, `encrypted_refresh_token`, `status` (`ACTIVE`, `NEEDS_REAUTH`).
*   **`sync_mappings`**: `id`, `connection_id`, `external_calendar_id`, `cina_calendar_id` (可为`default`) , `sync_direction`, `conflict_policy`, `last_sync_token`, `last_sync_timestamp`.
*   **`synced_item_references`**:
    *   `mapping_id (FK)`
    *   `cina_schedule_id (PK)`
    *   `external_event_id (PK)`
    *   `external_etag (VARCHAR)`
    *   `last_synced_at (TIMESTAMPTZ)`
*   **`sync_job_logs`**: 记录每次同步作业的日志。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: 用户管理API的P99延迟 < 200ms。
*   **同步延迟**:
    *   **Webhook触发**: P99端到端延迟（从外部变更到CINA.CLUB更新）< 10秒。
    *   **事件触发 (CINA->外部)**: P99端到端延迟 < 5秒。
*   **吞吐量**: 能处理大量并发的同步任务，特别是对于定时轮询。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **数据不错、不漏、不重**: 这是同步服务的核心。必须有健壮的逻辑来处理各种边界情况和API错误。
*   **外部API容错**: 对外部日历API的调用必须有超时、重试（带指数退避）和熔断机制。

#### 7.3. 可扩展性需求
*   服务应可水平扩展，以处理大量用户的同步任务。需要分布式锁（基于Redis）来协调同一用户的任务，防止并发执行。

#### 7.4. 安全性需求
*   **OAuth凭证安全**: `refresh_token`是高度敏感的长期凭证，必须使用`KMSProxy`进行强加密存储。
*   **权限最小化**: 向外部日历提供商请求最小必要权限 (scopes)。
*   **Webhook安全**: 必须验证Webhook请求的来源和签名（如果提供商支持）。

### 8. 技术约束与选型建议
*   **语言**: Go。其强大的并发模型适合处理大量并行的同步任务。
*   **后台任务**: 大量使用后台任务和调度器（如`Kubernetes CronJob`或Go的`Asynq`）来执行轮询同步，而不是在API请求中同步执行。
*   **外部API适配器**: 必须通过`ProviderSyncStrategy`接口和工厂模式，清晰地隔离不同日历平台的实现细节。
*   **深入理解API**: 必须深入理解每个目标日历平台的API特性、限制和最佳实践（特别是增量同步、重复日程和错误码）。

---
这份版本2.0的SRS文档为`calendar-sync-service`构建了一个健壮、安全且可扩展的日历集成中心。鉴于其高度的复杂性和对外部系统的强依赖，建议采用迭代开发，从支持一个平台的一种同步方式开始，逐步完善，并投入大量的资源进行集成测试和边界条件测试。