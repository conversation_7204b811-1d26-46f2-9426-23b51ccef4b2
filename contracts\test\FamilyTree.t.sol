// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import {Deploy} from "../script/Deploy.s.sol";
import {FamilyTree} from "../src/FamilyTree.sol";

contract FamilyTreeTest is Test {
    
    FamilyTree public familyTree;
    address public owner = makeAddr("owner");
    address public alice = makeAddr("alice");
    address public bob = makeAddr("bob");
    address public carol = makeAddr("carol");

    function setUp() public {
        // Use the deployment script to deploy a new contract instance
        Deploy deployer = new Deploy();
        familyTree = deployer.run();
        
        // Initialize the contract with the owner
        vm.prank(owner);
        familyTree.initialize(owner);
        
        // Add initial members
        vm.prank(owner);
        familyTree.addMember(alice, "ar://alice_meta");
        vm.prank(owner);
        familyTree.addMember(bob, "ar://bob_meta");
        vm.prank(owner);
        familyTree.addMember(carol, "ar://carol_meta");
    }

    // Test successfully creating a relationship
    function test_CreateRelationship() public {
        bytes32 childType = keccak256("CHILD");
        bytes32 parentType = keccak256("PARENT");
        
        // Simulate Alice calling the contract to add Bob as her child
        vm.prank(alice);
        familyTree.createRelationship(bob, childType);
        
        // Assert that the RelationshipCreated event was emitted correctly
        vm.expectEmit(true, true, true, true);
        emit familyTree.RelationshipCreated(alice, bob, childType);

        // Verify the relationship and the reverse relationship
        address[] memory children = familyTree.getRelationships(alice, childType);
        address[] memory parents = familyTree.getRelationships(bob, parentType);

        assertEq(children.length, 1);
        assertEq(children[0], bob);
        assertEq(parents.length, 1);
        assertEq(parents[0], alice);
    }

    // Test that a member cannot be added twice
    function test_Fail_AddDuplicateMember() public {
        vm.prank(owner);
        // Expect this call to revert with the specified error message
        vm.expectRevert(bytes("FamilyTree: Member already exists"));
        familyTree.addMember(alice, "ar://new_meta");
    }

    // Test removing a relationship
    function test_RemoveRelationship() public {
        bytes32 childType = keccak256("CHILD");
        bytes32 parentType = keccak256("PARENT");

        // Alice adds Bob as a child
        vm.prank(alice);
        familyTree.createRelationship(bob, childType);

        // Alice removes Bob as a child
        vm.prank(alice);
        familyTree.removeRelationship(bob, childType);

        // Assert that the RelationshipRemoved event was emitted
        vm.expectEmit(true, true, true, true);
        emit familyTree.RelationshipRemoved(alice, bob, childType);

        // Verify that the relationships are gone
        address[] memory children = familyTree.getRelationships(alice, childType);
        address[] memory parents = familyTree.getRelationships(bob, parentType);

        assertEq(children.length, 0);
        assertEq(parents.length, 0);
    }


    // Fuzz test for creating relationships
    // Foundry will automatically generate a large number of random inputs
    function test_Fuzz_CreateRelationship(address _target, bytes32 _type) public {
        // Assume the target is a valid, existing member and not the caller
        vm.prank(owner);
        familyTree.addMember(_target, "ar://fuzz_meta");
        
        vm.assume(_target != address(0) && _target != alice);
        
        // Run the test
        vm.prank(alice);
        familyTree.createRelationship(_target, _type);
        
        // Check invariants: a relationship should exist
        address[] memory targets = familyTree.getRelationships(alice, _type);
        assertTrue(targets.length > 0);
    }
} 