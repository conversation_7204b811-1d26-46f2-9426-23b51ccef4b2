/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:10:00
 * Modified: 2025-01-23 18:10:00
 */

import { http, HttpResponse } from 'msw';
import { User, UserStatus, UserRole, Permission } from '@/types/user';

const mockUsers: User[] = [
  // ... mock user data
];

const API_BASE_URL = 'http://localhost:8080';

export const handlers = [
  // Mock for getting users
  http.get(`${API_BASE_URL}/api/users`, () => {
    return HttpResponse.json({
      success: true,
      data: mockUsers,
      total: mockUsers.length,
    });
  }),

  // Mock for a user fetch failing
  http.get(`${API_BASE_URL}/api/users/error`, () => {
    return new HttpResponse(null, {
        status: 500,
        statusText: 'Internal Server Error',
    });
  }),

  // Mock for creating a user
  http.post(`${API_BASE_URL}/api/users`, async ({ request }) => {
    const newUser = await request.json();
    return HttpResponse.json({
      success: true,
      data: { ...newUser, id: 'new-user-id' },
    }, { status: 201 });
  }),
]; 