/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package service

import (
	"context"
	"regexp"
	"strings"

	"cina.club/services/ai-assistant-service/internal/application/port"
	"cina.club/services/ai-assistant-service/internal/domain/model"
)

// WorkflowPlanner workflow planner
type WorkflowPlanner struct {
	toolkit port.Toolkit
}

// NewWorkflowPlanner creates a new workflow planner
func NewWorkflowPlanner(toolkit port.Toolkit) *WorkflowPlanner {
	return &WorkflowPlanner{
		toolkit: toolkit,
	}
}

// CreatePlan creates a workflow plan
func (p *WorkflowPlanner) CreatePlan(ctx context.Context, userMessage string, dialogState *model.DialogState) (*model.WorkflowPlan, error) {
	// Analyze user intent
	intent := p.analyzeIntent(userMessage)

	// Create workflow plan based on intent
	plan := p.createPlanForIntent(intent, userMessage, dialogState)

	return plan, nil
}

// IntentType intent type
type IntentType string

const (
	IntentSearch    IntentType = "search"    // Search services
	IntentSchedule  IntentType = "schedule"  // Create schedule
	IntentMemory    IntentType = "memory"    // Query memory
	IntentKnowledge IntentType = "knowledge" // Query knowledge base
	IntentUserInfo  IntentType = "user_info" // Get user information
	IntentGeneral   IntentType = "general"   // General conversation
	IntentUnknown   IntentType = "unknown"   // Unknown intent
)

// Intent user intent
type Intent struct {
	Type       IntentType             `json:"type"`
	Confidence float64                `json:"confidence"`
	Entities   map[string]interface{} `json:"entities"`
	Keywords   []string               `json:"keywords"`
}

// analyzeIntent analyzes user intent
func (p *WorkflowPlanner) analyzeIntent(message string) *Intent {
	message = strings.ToLower(strings.TrimSpace(message))

	// Search intent keywords
	searchPatterns := []string{
		"search", "find", "look for", "recommend", "are there any", "where can I find", "service", "provider",
		"搜索", "查找", "找", "寻找", "推荐", "有没有", "哪里有", "服务", "提供商",
	}

	// Schedule intent keywords
	schedulePatterns := []string{
		"book", "schedule", "appointment", "time", "meeting", "remind", "tomorrow", "next week", "month",
		"预约", "安排", "日程", "时间", "会议", "提醒", "明天", "下周", "月份",
	}

	// Memory intent keywords
	memoryPatterns := []string{
		"remember", "before", "last time", "history", "previously", "record", "preference", "like",
		"记住", "之前", "上次", "历史", "以前", "记录", "偏好", "喜欢",
	}

	// Knowledge base intent keywords
	knowledgePatterns := []string{
		"how", "how to", "what is", "explain", "describe", "help", "guide", "tutorial",
		"怎么", "如何", "什么是", "解释", "说明", "帮助", "指导", "教程",
	}

	// User info intent keywords
	userInfoPatterns := []string{
		"my", "personal", "profile", "information", "settings", "account", "order", "history",
		"我的", "个人", "资料", "信息", "设置", "账户", "订单", "历史",
	}

	// Check for various intents
	if p.matchPatterns(message, searchPatterns) {
		return &Intent{
			Type:       IntentSearch,
			Confidence: 0.8,
			Entities:   p.extractSearchEntities(message),
			Keywords:   p.extractKeywords(message, searchPatterns),
		}
	}

	if p.matchPatterns(message, schedulePatterns) {
		return &Intent{
			Type:       IntentSchedule,
			Confidence: 0.8,
			Entities:   p.extractScheduleEntities(message),
			Keywords:   p.extractKeywords(message, schedulePatterns),
		}
	}

	if p.matchPatterns(message, memoryPatterns) {
		return &Intent{
			Type:       IntentMemory,
			Confidence: 0.7,
			Entities:   p.extractMemoryEntities(message),
			Keywords:   p.extractKeywords(message, memoryPatterns),
		}
	}

	if p.matchPatterns(message, knowledgePatterns) {
		return &Intent{
			Type:       IntentKnowledge,
			Confidence: 0.7,
			Entities:   p.extractKnowledgeEntities(message),
			Keywords:   p.extractKeywords(message, knowledgePatterns),
		}
	}

	if p.matchPatterns(message, userInfoPatterns) {
		return &Intent{
			Type:       IntentUserInfo,
			Confidence: 0.8,
			Entities:   p.extractUserInfoEntities(message),
			Keywords:   p.extractKeywords(message, userInfoPatterns),
		}
	}

	// Default to general conversation
	return &Intent{
		Type:       IntentGeneral,
		Confidence: 0.3,
		Entities:   make(map[string]interface{}),
		Keywords:   []string{},
	}
}

// matchPatterns matches patterns
func (p *WorkflowPlanner) matchPatterns(message string, patterns []string) bool {
	for _, pattern := range patterns {
		if strings.Contains(message, pattern) {
			return true
		}
	}
	return false
}

// extractKeywords extracts keywords
func (p *WorkflowPlanner) extractKeywords(message string, patterns []string) []string {
	keywords := make([]string, 0)
	for _, pattern := range patterns {
		if strings.Contains(message, pattern) {
			keywords = append(keywords, pattern)
		}
	}
	return keywords
}

// extractSearchEntities extracts search entities
func (p *WorkflowPlanner) extractSearchEntities(message string) map[string]interface{} {
	entities := make(map[string]interface{})

	// Extract query words (simplified implementation)
	words := strings.Fields(message)
	queryWords := make([]string, 0)
	for _, word := range words {
		if len(word) > 1 && !p.isStopWord(word) {
			queryWords = append(queryWords, word)
		}
	}

	if len(queryWords) > 0 {
		entities["query"] = strings.Join(queryWords, " ")
	}

	// Extract location
	locationPatterns := []string{"beijing", "shanghai", "guangzhou", "shenzhen", "hangzhou", "chengdu", "xian", "wuhan",
		"北京", "上海", "广州", "深圳", "杭州", "成都", "西安", "武汉"}
	for _, location := range locationPatterns {
		if strings.Contains(message, location) {
			entities["location"] = location
			break
		}
	}

	return entities
}

// extractScheduleEntities extracts schedule entities
func (p *WorkflowPlanner) extractScheduleEntities(message string) map[string]interface{} {
	entities := make(map[string]interface{})

	// Extract time expressions (simplified implementation)
	timePatterns := map[string]string{
		"tomorrow":           "tomorrow",
		"day after tomorrow": "day_after_tomorrow",
		"next week":          "next_week",
		"next month":         "next_month",
		"today":              "today",
		"tonight":            "tonight",
		"tomorrow morning":   "tomorrow_morning",
		"明天":                 "tomorrow",
		"后天":                 "day_after_tomorrow",
		"下周":                 "next_week",
		"下个月":                "next_month",
		"今天":                 "today",
		"今晚":                 "tonight",
		"明早":                 "tomorrow_morning",
	}

	for pattern, value := range timePatterns {
		if strings.Contains(message, pattern) {
			entities["time_expression"] = value
			break
		}
	}

	// Extract time (HH:MM format)
	timeRegex := regexp.MustCompile(`(\d{1,2}):(\d{2})`)
	if matches := timeRegex.FindStringSubmatch(message); len(matches) > 0 {
		entities["time"] = matches[0]
	}

	return entities
}

// extractMemoryEntities extracts memory entities
func (p *WorkflowPlanner) extractMemoryEntities(message string) map[string]interface{} {
	entities := make(map[string]interface{})

	// Extract query keywords
	words := strings.Fields(message)
	queryWords := make([]string, 0)
	for _, word := range words {
		if len(word) > 1 && !p.isStopWord(word) {
			queryWords = append(queryWords, word)
		}
	}

	if len(queryWords) > 0 {
		entities["query"] = strings.Join(queryWords, " ")
	}

	return entities
}

// extractKnowledgeEntities extracts knowledge base entities
func (p *WorkflowPlanner) extractKnowledgeEntities(message string) map[string]interface{} {
	entities := make(map[string]interface{})

	// Extract query keywords
	words := strings.Fields(message)
	queryWords := make([]string, 0)
	for _, word := range words {
		if len(word) > 1 && !p.isStopWord(word) {
			queryWords = append(queryWords, word)
		}
	}

	if len(queryWords) > 0 {
		entities["query"] = strings.Join(queryWords, " ")
	}

	return entities
}

// extractUserInfoEntities extracts user information entities
func (p *WorkflowPlanner) extractUserInfoEntities(message string) map[string]interface{} {
	entities := make(map[string]interface{})

	// Check information type
	if strings.Contains(message, "order") || strings.Contains(message, "订单") {
		entities["include_order_history"] = true
	}

	if strings.Contains(message, "preference") || strings.Contains(message, "settings") ||
		strings.Contains(message, "偏好") || strings.Contains(message, "设置") {
		entities["include_preferences"] = true
	}

	return entities
}

// isStopWord checks if word is a stop word
func (p *WorkflowPlanner) isStopWord(word string) bool {
	stopWords := []string{
		"the", "is", "at", "which", "on", "and", "a", "to", "an", "as", "are", "was", "were", "been", "be", "have", "has", "had", "do", "does", "did", "will", "would", "should", "could", "can", "may", "might", "must", "shall", "should", "will", "would",
		"的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这", "那", "里", "就是", "还是", "把", "被", "从", "跟", "对", "于",
	}

	for _, stopWord := range stopWords {
		if word == stopWord {
			return true
		}
	}
	return false
}

// createPlanForIntent creates workflow plan based on intent
func (p *WorkflowPlanner) createPlanForIntent(intent *Intent, userMessage string, _ *model.DialogState) *model.WorkflowPlan {
	switch intent.Type {
	case IntentSearch:
		return p.createSearchPlan(intent, userMessage)
	case IntentSchedule:
		return p.createSchedulePlan(intent, userMessage)
	case IntentMemory:
		return p.createMemoryPlan(intent, userMessage)
	case IntentKnowledge:
		return p.createKnowledgePlan(intent, userMessage)
	case IntentUserInfo:
		return p.createUserInfoPlan(intent, userMessage)
	case IntentGeneral:
		return p.createGeneralPlan(intent, userMessage)
	default:
		return nil
	}
}

// createSearchPlan creates search plan
func (p *WorkflowPlanner) createSearchPlan(intent *Intent, userMessage string) *model.WorkflowPlan {
	plan := model.NewWorkflowPlan("search_services", "Search service providers")

	// Prepare search parameters
	inputs := make(map[string]interface{})
	if query, exists := intent.Entities["query"]; exists {
		inputs["query"] = query
	} else {
		inputs["query"] = userMessage
	}

	if location, exists := intent.Entities["location"]; exists {
		inputs["location"] = location
	}

	inputs["limit"] = 5

	// Add search step
	searchStep := model.WorkflowStep{
		Name:   "Search Services",
		ToolID: "search_services",
		Inputs: inputs,
	}

	plan.AddStep(searchStep)
	return plan
}

// createSchedulePlan creates schedule plan
func (p *WorkflowPlanner) createSchedulePlan(intent *Intent, userMessage string) *model.WorkflowPlan {
	plan := model.NewWorkflowPlan("create_schedule", "Create schedule arrangement")

	// Prepare schedule parameters
	inputs := make(map[string]interface{})
	inputs["title"] = userMessage // Simplified implementation, use user message as title

	if _, exists := intent.Entities["time_expression"]; exists {
		// Here should convert time expression to specific time
		inputs["start_time"] = "2024-01-25T10:00:00Z" // Simplified implementation
	}

	if time, exists := intent.Entities["time"]; exists {
		inputs["time"] = time
	}

	// Add create schedule step
	scheduleStep := model.WorkflowStep{
		Name:   "Create Schedule",
		ToolID: "create_schedule",
		Inputs: inputs,
	}

	plan.AddStep(scheduleStep)
	return plan
}

// createMemoryPlan creates memory query plan
func (p *WorkflowPlanner) createMemoryPlan(intent *Intent, userMessage string) *model.WorkflowPlan {
	plan := model.NewWorkflowPlan("query_memory", "Query user memory")

	// Prepare query parameters
	inputs := make(map[string]interface{})
	if query, exists := intent.Entities["query"]; exists {
		inputs["query"] = query
	} else {
		inputs["query"] = userMessage
	}

	inputs["limit"] = 5

	// Add query memory step
	memoryStep := model.WorkflowStep{
		Name:   "Query Memory",
		ToolID: "query_memory",
		Inputs: inputs,
	}

	plan.AddStep(memoryStep)
	return plan
}

// createKnowledgePlan creates knowledge base query plan
func (p *WorkflowPlanner) createKnowledgePlan(intent *Intent, userMessage string) *model.WorkflowPlan {
	plan := model.NewWorkflowPlan("query_knowledge", "Query knowledge base")

	// Prepare query parameters
	inputs := make(map[string]interface{})
	if query, exists := intent.Entities["query"]; exists {
		inputs["query"] = query
	} else {
		inputs["query"] = userMessage
	}

	inputs["limit"] = 3

	// Add query knowledge base step
	knowledgeStep := model.WorkflowStep{
		Name:   "Query Knowledge",
		ToolID: "query_knowledge",
		Inputs: inputs,
	}

	plan.AddStep(knowledgeStep)
	return plan
}

// createUserInfoPlan creates user information query plan
func (p *WorkflowPlanner) createUserInfoPlan(intent *Intent, _ string) *model.WorkflowPlan {
	plan := model.NewWorkflowPlan("get_user_info", "Get user information")

	// Prepare query parameters
	inputs := make(map[string]interface{})

	if includeOrderHistory, exists := intent.Entities["include_order_history"]; exists {
		inputs["include_order_history"] = includeOrderHistory
	}

	if includePreferences, exists := intent.Entities["include_preferences"]; exists {
		inputs["include_preferences"] = includePreferences
	}

	// Add get user information step
	userInfoStep := model.WorkflowStep{
		Name:   "Get User Info",
		ToolID: "get_user_info",
		Inputs: inputs,
	}

	plan.AddStep(userInfoStep)
	return plan
}

// createGeneralPlan creates general conversation plan
func (p *WorkflowPlanner) createGeneralPlan(_ *Intent, userMessage string) *model.WorkflowPlan {
	plan := model.NewWorkflowPlan("llm_conversation", "General conversation")

	// Prepare LLM call parameters
	inputs := make(map[string]interface{})
	inputs["prompt"] = userMessage
	inputs["model"] = "gpt-3.5-turbo"
	inputs["temperature"] = 0.7
	inputs["max_tokens"] = 500

	// Add LLM call step
	llmStep := model.WorkflowStep{
		Name:   "LLM Conversation",
		ToolID: "llm_call",
		Inputs: inputs,
	}

	plan.AddStep(llmStep)
	return plan
}
