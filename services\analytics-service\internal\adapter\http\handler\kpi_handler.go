// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-15 12:00:00
// Modified: 2025-01-15 12:00:00

package handler

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"cina.club/services/analytics-service/internal/domain/model"
)

// KPIServiceInterface defines the interface for KPI service
type KPIServiceInterface interface {
	GetDailySummary(ctx context.Context, date time.Time) (*model.KPIMetrics, error)
	GetPlatformOverview(ctx context.Context) (*model.PlatformOverview, error)
	GetGrowthMetrics(ctx context.Context, timeRange model.TimeRange) (*model.GrowthMetrics, error)
}

// KPIHandler KPI HTTP handler
type KPIHandler struct {
	kpiService KPIServiceInterface
}

// NewKPIHandler Create KPI handler
func NewKPIHandler(kpiService KPIServiceInterface) *KPIHandler {
	return &KPIHandler{
		kpiService: kpiService,
	}
}

// GetDailySummary Get daily KPI summary
func (h *KPIHandler) GetDailySummary(c *gin.Context) {
	dateStr := c.Query("date")
	if dateStr == "" {
		// Default to use yesterday's date
		dateStr = time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid date format",
			"message": "Date should be in YYYY-MM-DD format",
		})
		return
	}

	kpi, err := h.kpiService.GetDailySummary(c.Request.Context(), date)
	if err != nil {
		logrus.WithError(err).WithField("date", date).Error("Failed to get daily summary")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve daily summary",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    kpi,
	})
}

// GetPlatformOverview Get platform overview metrics
func (h *KPIHandler) GetPlatformOverview(c *gin.Context) {
	overview, err := h.kpiService.GetPlatformOverview(c.Request.Context())
	if err != nil {
		logrus.WithError(err).Error("Failed to get platform overview")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve platform overview",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    overview,
	})
}

// GetGrowthMetrics Get growth metrics
func (h *KPIHandler) GetGrowthMetrics(c *gin.Context) {
	// Parse time range parameters
	timeRange, err := parseTimeRange(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameters",
			"message": err.Error(),
		})
		return
	}

	metrics, err := h.kpiService.GetGrowthMetrics(c.Request.Context(), timeRange)
	if err != nil {
		logrus.WithError(err).WithField("time_range", timeRange).Error("Failed to get growth metrics")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve growth metrics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// parseTimeRange Parse time range parameters
func parseTimeRange(c *gin.Context) (model.TimeRange, error) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	period := c.DefaultQuery("period", "daily")

	// Default time range: last 30 days
	if startDateStr == "" || endDateStr == "" {
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		return model.TimeRange{
			StartDate: startDate,
			EndDate:   endDate,
			Period:    period,
		}, nil
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return model.TimeRange{}, err
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return model.TimeRange{}, err
	}

	return model.TimeRange{
		StartDate: startDate,
		EndDate:   endDate,
		Period:    period,
	}, nil
}
