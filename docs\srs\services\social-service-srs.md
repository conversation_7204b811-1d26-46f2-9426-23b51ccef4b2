好的，遵照您的指示，我们来生成一份为 `social-service` (社交关系服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **关系类型的扩展**: 除了关注/拉黑，明确增加“好友请求”模式，以支持需要双方同意才能建立的双向关系。
2.  **数据存储策略**: 详细探讨在超大规模用户场景下，从PostgreSQL演进到图数据库（Graph DB）或分布式KV存储的可能路径和考量。
3.  **反垃圾与推荐支持**: 增加对识别垃圾关注（Spam Following）和机器人网络的初步支持，并为推荐系统提供所需的数据信号（如二度人脉）。
4.  **性能优化**: 细化对高粉丝数用户（明星用户）的“粉丝列表”查询优化策略，如缓存、读写分离和数据预聚合。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例和事件契约，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和扩展性指标，特别是针对高并发写的场景。

这份文档将描绘一个功能强大、性能卓越、可扩展，且能应对复杂社交场景的社交图谱中心。

---

### CINA.CLUB - social-service 需求规格说明书

**版本: 2.0 (生产级定义，支持好友请求与大规模优化)**  
**发布日期: 2025-06-25**  
**最后修订日期: 2025-06-25**  
**文档负责人:** [社交/平台工程团队负责人名称]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台内构建一个充满活力的、互联的社区，`social-service` 旨在构建和管理用户之间的**社交图谱**。本服务负责处理用户间的关注、好友请求、拉黑等核心社交关系，为平台提供基础的社交连接能力，是实现个性化内容推荐、社交发现、社区互动和增强用户归属感不可或缺的基础设施。

#### 1.2. 服务范围
本服务 **负责**:
*   **关系管理**:
    *   处理单向的**关注/取关**操作。
    *   处理需要双方同意的**好友请求/接受/拒绝**流程。
    *   处理**拉黑/解除拉黑**、**静音/取消静音**等屏蔽关系。
*   **关系查询**:
    *   提供API查询用户的粉丝列表、关注列表、好友列表。
    *   提供API查询两个用户间的详细关系状态。
    *   （未来）提供API查询二度人脉等图谱遍历功能。
*   **数据聚合**: 实时、原子性地维护和提供用户的粉丝数、关注数、好友数。
*   **反垃圾与风控**: 初步识别和标记可疑的垃圾关注行为。
*   **事件发布**: 当关系发生变化时，发布领域事件。

本服务 **不负责**:
*   **家庭/族谱关系** (由 `family-tree-service` 负责)。
*   **聊天消息或实时通信** (由 `chat-service` 体系负责)。
*   **内容推荐算法或Feed流生成** (这些服务会 *消费* 本服务的数据)。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`social-service` 是平台的“**社交关系图谱中心**”。它维护了所有用户之间的连接关系（边），是构建一切社交功能的基础。其数据模式和读写特性与`user-core-service`截然不同，`social-service`具有**极高的写吞吐量**（关注/取关操作频繁）和**高扇出读**（查询粉丝列表）的特点，因此必须作为独立服务存在，并对其数据存储和查询进行深度优化。

#### 2.2. 主要功能概述
*   支持“关注”和“好友请求”两种关系模式。
*   原子性的、可靠的关系变更与计数更新。
*   针对“明星用户”场景优化的高效列表查询。
*   初步的反垃圾关注能力。
*   通过事件驱动与平台其他服务解耦。

### 3. 核心流程图

#### 3.1. 用户A发送好友请求给用户B
```mermaid
sequenceDiagram
    participant ClientA
    participant SocialService as SS
    participant NotificationService as NS
    participant DB

    ClientA->>SS: 1. POST /friend-requests (targetUserId: "user_b_id")
    SS->>DB: 2. Check if relationship already exists or is blocked
    DB-->>SS: (No blocking relationship)
    SS->>DB: 3. Create FriendRequest record (status: PENDING)
    SS-->>ClientA: 202 Accepted

    SS->>NS: 4. Request to send notification to User B
    NS-->>ClientB: "User A wants to be your friend"
    
    Note over ClientB: User B accepts the request
    
    ClientB->>SS: 5. POST /friend-requests/{reqId}/accept
    SS->>DB: 6. **Start Transaction**
    SS->>DB: 7. Validate request
    SS->>DB: 8a. Create Relationship (A -> B, type: FRIEND)
    SS->>DB: 8b. Create Relationship (B -> A, type: FRIEND)
    SS->>DB: 9a. Atomically increment friends_count for A & B
    SS->>DB: 9b. (If also following) Update followers/following counts
    SS->>DB: 10. Update FriendRequest status to ACCEPTED
    SS->>DB: 11. **Commit Transaction**

    SS->>MQ: 12. Publish FriendshipCreatedEvent
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 关系模型
*   **FR4.1.1 (双模式)**: 系统必须同时支持两种关系建立模式：
    *   **关注模式**: 用户可单向关注他人，无需对方同意。
    *   **好友模式**: 需一方发起好友请求，另一方同意后，建立双向好友关系。
*   **FR4.1.2 (关系类型)**: `relationships`表中应能区分`FOLLOWING`和`FRIEND`两种主要关系类型。`FRIEND`关系通常是双向的。

#### 4.2. 关系管理
*   **FR4.2.1 (好友请求)**:
    *   提供API发起、撤回、接受、拒绝好友请求。
    *   好友请求有过期时间。
*   **FR4.2.2 (拉黑)**: 拉黑操作具有最高优先级。用户A拉黑用户B后，系统必须：
    *   自动解除A和B之间任何已存在的关注或好友关系。
    *   阻止B向A发起任何新的关注或好友请求。
    *   在关系查询接口中明确返回“被拉黑”的状态。

#### 4.3. 查询性能优化
*   **FR4.3.1 (列表查询)**: 查询粉丝/关注/好友列表的API必须支持基于游标的分页（Cursor-based Pagination），以实现高效的无限滚动。
*   **FR4.3.2 (明星用户优化)**:
    *   对于粉丝数极多（如超过10万）的用户，其粉丝列表的查询不应直接扫描数据库。
    *   **策略**: 将这些用户的粉丝列表**缓存**在Redis的`Sorted Set`中，或预生成到专门的只读数据存储中。
*   **FR4.3.3 (二度人脉查询 - 高级)**: 提供API查询“好友的好友”，此操作应在图数据库或通过优化的SQL递归查询实现。

#### 4.4. 反垃圾与风控
*   **FR4.4.1 (行为监控)**: 系统应监控短时间内来自同一用户或IP的大量关注或好友请求行为，并进行速率限制。
*   **FR4.4.2 (机器人识别)**: （与AI/风控服务协同）可以分析用户的社交图谱特征（如出入度比、社群聚集度），识别可疑的机器人账户，并将其标记。被标记账户的关注行为在计算热度或推荐权重时可以被降权。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/social`
*   **核心端点**:
    *   **Follows**: `POST /follows` (targetUserId), `DELETE /follows/{targetUserId}`
    *   **Friends**: `POST /friend-requests`, `GET /friend-requests/me`, `POST /friend-requests/{reqId}/accept`, `DELETE /friends/{userId}`
    *   **Blocks**: `POST /blocks`, `DELETE /blocks/{targetUserId}`
    *   **Lists**: `GET /users/{userId}/followers`, `GET /users/{userId}/following`, `GET /users/{userId}/friends`
    *   **Status**: `GET /relationships/status?from_user_id=...&to_user_id=...`

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL - 适用于中大规模)
*   **`relationships`**: `(user_a_id, user_b_id)` (PK), `type` (`FOLLOWING`, `BLOCKED`, `MUTED`), `status`, `created_at`. (使用`CHECK(user_a_id < user_b_id)`约束)
*   **`friend_requests`**: `id`, `from_user_id`, `to_user_id`, `status`, `expires_at`.
*   **`user_social_stats`**: `user_id (PK)`, `followers_count`, `following_count`, `friends_count`. (原子更新)

#### 6.2. 数据存储演进路径 (应对超大规模)
*   **阶段1 (启动-百万用户)**: **PostgreSQL**。通过合理的索引和冗余计数，性能完全足够。
*   **阶段2 (千万用户)**:
    *   **数据库**: 考虑迁移到**分布式SQL数据库(TiDB, CockroachDB)**或**图数据库(Neo4j, Dgraph)**。
    *   **图数据库优势**: 对“二度人脉”、“共同好友”、“关系路径推荐”等复杂图查询有天然的性能优势。
*   **阶段3 (亿级用户)**: 采用类似Twitter的架构，粉丝列表存储在**分布式缓存/KV存储（如Redis Cluster, TiKV）**中，并通过**扇出(Fan-out)**模式进行更新。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **写延迟**: 关注/好友请求等写操作P99 < 100ms。
*   **读延迟**:
    *   普通用户列表查询 P99 < 150ms。
    *   明星用户列表查询 P99 < 300ms (可能来自缓存)。
*   **高并发写**: 系统必须能承受“热点事件”引发的突发性高并发写操作，如大量用户同时关注一个新晋网红。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **数据一致性**: 粉丝/关注计数与实际关系必须保持**强一致性**。所有更新必须在数据库事务内完成。

#### 7.3. 可扩展性需求
*   服务应为无状态，可水平扩展。
*   数据库层是扩展的核心，必须从设计上考虑**FR6.2**中的演进路径。

#### 7.4. 安全性需求
*   **API授权**: 严格的权限校验，用户只能操作自己的关系。
*   **防滥用**: 对所有写操作进行严格的速率限制。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: 启动时使用**PostgreSQL**。
*   **缓存**: **Redis** 对于缓存计数和热点列表至关重要。
*   **并发控制**: **必须**使用数据库的**悲观锁 (`SELECT ... FOR UPDATE`)** 或**乐观锁（version字段）**来保证`user_social_stats`计数的原子性更新。
*   **图谱分析**: 对于复杂的图谱分析需求（如社群发现），应通过ETL将关系数据导入到图数据库或Spark GraphX中进行离线计算。

---
这份版本2.0的SRS文档为`social-service`构建了一个功能全面、性能卓越、且具备长期扩展性的社交图谱中心。它通过支持多种关系模式和针对性的性能优化，能够为CINA.CLUB平台提供稳定、可靠的社交基础，并为未来的功能演进（如图谱推荐、社群发现）奠定了坚实的基础。