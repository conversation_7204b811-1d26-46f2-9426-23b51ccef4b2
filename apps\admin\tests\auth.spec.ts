/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:40:00
 * Modified: 2025-01-23 18:40:00
 */

import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test('should allow a user to log in successfully', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');

    // Expect a title "to contain" a substring.
    await expect(page).toHaveTitle(/Cina.Club Admin/);

    // Fill in the login form
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password'); // Use a mock password

    // Click the login button
    await page.getByRole('button', { name: 'Login' }).click();

    // Wait for navigation and check for a dashboard element
    await expect(page).toHaveURL(/.*dashboard/);
    await expect(page.getByText('Welcome back, Admin!')).toBeVisible();
  });

  test('should show an error message on failed login', async ({ page }) => {
    // Navigate to the login page
    await page.goto('/login');

    // Fill in the login form with incorrect credentials
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('wrong-password');

    // Click the login button
    await page.getByRole('button', { name: 'Login' }).click();

    // Check for an error message
    const errorMessage = page.locator('.ant-message-error');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toHaveText(/Invalid credentials/);
  });
}); 