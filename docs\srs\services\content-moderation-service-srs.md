﻿好的，遵照您的指示，我们来生成一份为 `content-moderation-service` (内容审核服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多模态审核**: 明确支持对文本、图片、视频、音频等多种模态内容的审核流程。
2.  **审核工作流引擎**: 引入可配置的、基于风险分级的审核工作流（Pipeline），实现“机审-人审-复审”的多级流程。
3.  **用户风险画像**: 建立用户历史违规档案，并将其作为审核策略的一部分，实现对高风险用户的重点监控。
4.  **规则引擎与策略中心**: 详细定义一个灵活的规则引擎，允许运营人员动态调整审核策略和敏感词库。
5.  **与人工审核平台的集成**: 明确与第三方或自建人工审核后台的API对接规范。
6.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
7.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、智能、灵活且能应对复杂内容安全挑战的平台“免疫系统”。

---

### CINA.CLUB - content-moderation-service 需求规格说明书

**版本: 2.0 (生产级定义，集成多模态审核与工作流引擎)**  
**发布日期: 2025-06-22**  
**最后修订日期: 2025-06-22**  
**文档负责人:** [信任与安全团队负责人/架构师名称]  
**审批人:** [CTO/法务负责人]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台承载着海量的、多模态的用户生成内容 (UGC)。为了维护一个健康、安全、合规的平台环境，保护用户免受不当信息侵害，并遵守各地法律法规，`content-moderation-service` 旨在构建一个集中、高效、智能的**内容治理中心**。它将结合多模态AI分析、可配置的规则引擎和分级人工审核流程，对平台所有UGC进行有效、精确的风险识别与处置。

#### 1.2. 服务范围
本服务 **负责**:
*   **统一审核任务接收**: 提供统一的API，接收来自平台所有业务服务的待审核内容（文本、图片、视频、音频等）。
*   **审核工作流编排**:
    *   对接收到的内容进行初步风险评估。
    *   根据风险等级和内容类型，将其路由到不同的、可配置的审核流水线（Pipeline）。
    *   编排“机审->人审->复审”的多级审核流程。
*   **自动化AI审核**: 集成和调用多种AI分析能力，包括：
    *   文本分析：涉政、色情、辱骂、垃圾广告、违禁品、PII泄露。
    *   图像/视频分析：色情、暴力、恐怖、令人不适的视觉内容。
    *   音频分析：语音转文本（ASR）后进行文本分析。
*   **人工审核支持**: 管理需要人工干预的审核任务队列，并与人工审核后台进行API对接。
*   **规则引擎与策略管理**: 维护和执行审核规则、敏感词库、黑白名单。
*   **决策与执行协调**: 根据最终审核决策，通过消息队列将处置指令通知回源业务服务，并协调对用户的处罚。
*   **用户风险画像**: 记录和管理用户的历史违规记录，并计算用户风险等级。

本服务 **不负责**:
*   训练核心的AI内容审核模型 (但会调用这些模型)。
*   直接修改或删除源业务数据 (本服务只发出指令，由源服务执行)。
*   提供面向最终用户的UI。
*   人工审核后台的UI开发。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部所有UGC服务 (主要)**: `community-forum-service`, `community-qa-service`, `chat-api-service`, `short-video-service`, `user-core-service`(审核头像/昵称)等。
*   **人工审核团队**: 通过审核后台与本服务交互。
*   **平台管理员/运营团队**: 配置审核策略，查看报告。
*   **`user-core-service`**: (被本服务调用) 执行对用户的处罚，如临时挂起。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`content-moderation-service` 是平台的“**内容安全防火墙**”和“**治理中枢**”。它提供横向的内容安全能力，确保所有UGC在发布前后都经过一致、标准的审核流程。本服务是平台信任与安全体系的关键基础设施，其审核的准确性和效率直接关系到用户体验和平台的法律风险。

#### 2.2. 主要功能概述
*   支持多模态内容的统一审核任务管理。
*   基于风险分级的、可配置的审核工作流引擎。
*   结合用户风险画像的动态审核策略。
*   AI机审与人工审核的无缝协同。
*   基于审核结果的闭环处置与反馈。

### 3. 核心流程图

#### 3.1. 一个带图片的帖子的审核流程
```mermaid
sequenceDiagram
    participant SourceService as "community-forum-service"
    participant ModerationService as CMS
    participant AI_Analyzers as "AI Analysis Engines"
    participant RuleEngine as "Rules Engine"
    participant HumanReviewPlatform as "Human Review Platform"

    SourceService->>CMS: 1. POST /tasks (content: {text, imageUrl}, user_id, ...)
    CMS->>DB: 2. Create ModerationTask (status: PENDING)
    
    CMS->>AI_Analyzers: 3. **[Async]** Submit text & image for analysis
    AI_Analyzers-->>CMS: 4. (AI Results: {text_labels, image_labels, scores})
    
    CMS->>RuleEngine: 5. Evaluate (AI_Results, User_Risk_Profile)
    RuleEngine-->>CMS: 6. (Decision: "PASS" | "REJECT" | "REVIEW")
    
    alt Decision is "PASS" or "REJECT"
        CMS->>DB: 7a. Update Task status (APPROVED/REJECTED)
        CMS->>MQ: 8a. Publish ModerationCompletedEvent to SourceService
    else Decision is "REVIEW"
        CMS->>HumanReviewPlatform: 7b. Push task to human review queue
        HumanReviewPlatform-->>Auditor: (Auditor works on task)
        Auditor-->>HumanReviewPlatform: (Submit decision)
        HumanReviewPlatform->>CMS: 8b. **[Webhook]** Send human review result
        CMS->>DB: 9b. Update Task status based on human decision
        CMS->>MQ: 10b. Publish ModerationCompletedEvent
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 审核任务与工作流管理
*   **FR4.1.1 (统一任务提交)**: 系统必须提供一个统一的、异步的内部API (`POST /tasks`)，供其他服务提交多模态的审核任务。
*   **FR4.1.2 (工作流引擎)**:
    *   系统必须支持管理员定义多个审核工作流（Pipeline）。
    *   每个工作流定义了在不同阶段（如机审、人审）使用的策略和规则集。
    *   系统能根据提交任务的`contentType`和`businessScenario`自动选择合适的工作流。

#### 4.2. 自动化审核 (机审)
*   **FR4.2.1 (多模态分析)**: 系统必须能编排对多种AI分析能力的调用：
    *   **文本**: 调用内部或第三方服务，分析涉政、色情、辱骂、广告、违禁品等。
    *   **图像**: 调用内部或第三方服务，分析色情、暴力、血腥、公众人物等。
    *   **视频**: 采用**抽帧**策略，将关键视频帧送交图像分析；采用**音频提取+ASR**策略，将音频转换为文本进行分析。
*   **FR4.2.2 (结果归一化)**: 系统必须将来自不同AI分析引擎的异构结果，转换为统一的、包含`{label, score, details}`的结构化风险标签。

#### 4.3. 规则引擎与策略中心
*   **FR4.3.1 (灵活的规则)**: 规则引擎必须支持基于多种输入的复杂逻辑判断：
    *   AI机审结果（如：`image.porn_score > 0.9`）。
    *   **用户风险画像**（如：`user.risk_level == 'HIGH'`）。
    *   文本内容（如：匹配敏感词库、正则表达式）。
    *   元数据（如：`source_service == 'chat-api-service'`）。
*   **FR4.3.2 (动态策略)**: 管理员必须能通过后台UI或API，动态地创建、修改、启用/禁用审核规则和敏感词库，无需重新部署服务。

#### 4.4. 人工审核协同
*   **FR4.4.1 (智能送审)**: 当机审结果的置信度低于某个阈值，或触发了“需要人工复核”的规则时，任务必须被自动送入人工审核队列。
*   **FR4.4.2 (队列管理)**: 系统必须支持多个人工审核队列（如涉政队、色情队），并能根据风险标签智能分派。
*   **FR4.4.3 (API对接)**: 系统必须提供拉取(pull)待审任务和接收(push)审核结果的API，与人工审核平台对接。API需支持任务的加锁(locking)和超时释放。

#### 4.5. 处置与闭环
*   **FR4.5.1 (决策引擎)**: 系统必须有一个决策引擎，根据机审和人审（如有）的最终结果，做出`PASS`, `REJECT`, `EDIT_AND_PASS`等最终处置决策。
*   **FR4.5.2 (执行协调)**:
    *   通过消息队列将审核结果和处置决策通知回**源业务服务**。
    *   如果决策涉及对用户的处罚（如禁言、封号），系统必须调用`user-core-service`的内部API来执行，并调用`notification-dispatch-service`来通知用户。

#### 4.6. 用户风险画像
*   **FR4.6.1 (违规记录)**: 系统必须为每个用户的违规行为创建并维护一个历史记录档案。
*   **FR4.6.2 (风险评分)**: 系统可以基于用户的违规历史（频率、严重性），计算一个动态的“用户风险评分”。此评分可作为规则引擎的重要输入，以对高风险用户采取更严格的审核策略（如内容先审后发）。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC/RESTful API接口 (S2S)
*   **版本**: `/api/v1/moderation`
*   **认证**: 严格的S2S认证。
*   **核心端点**:
    *   `POST /tasks`: 提交内容进行异步审核。
        *   Request: `SubmitModerationRequest { idempotency_key, content: {text?, image_urls?, video_url?}, user_info: {id, risk_level}, source_info: {service, entity_id}, callback_topic }`
    *   `GET /tasks/{taskId}`: 查询单个审核任务的详细状态和结果。

#### 5.2. 人工审核平台对接API
*   `GET /admin/queues/{queueName}/pull-tasks?count=10`: 人审平台拉取任务。
*   `POST /admin/tasks/{taskId}/lock`: 审核员锁定任务。
*   `POST /admin/tasks/submit-decision`: 审核员提交审核结果。

#### 5.3. 消息队列事件契约
*   **出站 (发布)**:
    *   `ModerationCompletedEvent { taskId, sourceService, sourceEntityId, finalDecision ("APPROVED"|"REJECTED"), rejectionLabels: [], reviewedBy: "MACHINE"|"HUMAN" }`.
    *   `UserActionRequiredEvent { userId, actionType ("WARN", "MUTE", "SUSPEND"), reason, relatedTaskId }`.

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`moderation_tasks`**: `id`, `source_service`, `source_entity_id`, `user_id`, `content_snapshot (JSONB)`, `status` (INDEX), `risk_level`, `machine_review_result (JSONB)`, `human_review_result (JSONB)`, `workflow_id`, `created_at`.
*   **`moderation_rules`**: `id`, `name`, `conditions (JSONB)`, `actions (JSONB)`, `priority`, `is_active`.
*   **`user_violation_logs`**: `id`, `user_id (INDEX)`, `task_id`, `violation_type`, `decision`, `timestamp`.
*   **`sensitive_word_catalogs`**: `id`, `name`, `word_list (TEXT[])` (使用GIN索引)。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API响应**: 提交审核任务的API P99延迟应 `< 50ms`。
*   **自动化审核处理时间**:
    *   纯文本: P95 < 2秒。
    *   图片: P95 < 5秒。
    *   视频（1分钟内）: P95 < 2分钟。
*   **吞吐量**: 能够处理平台所有UGC产生的高并发审核请求。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **容错**: 对外部AI服务的调用失败应有重试和降级机制（例如，如果图像审核失败，可以先基于文本审核结果做出初步判断，并标记送人审）。

#### 7.3. 可扩展性需求
*   API服务层和事件消费层均可水平扩展。
*   AI分析和规则引擎处理逻辑应能通过后台Worker/消费者集群进行水平扩展。

#### 7.4. 安全性需求
*   **凭证安全**: 严格保护所有第三方AI服务的API Key。
*   **数据隐私**: 审核员对用户隐私数据的访问应受到严格的监控和审计。访问权限应遵循最小必要原则。

### 8. 技术约束与选型建议
*   **语言**: Go。其高并发模型适合处理大量的异步审核任务和API调用。
*   **AI分析能力**: 优先集成成熟的第三方云服务（如阿里云内容安全、腾讯云T-Sec、Google Cloud Vision/Video/Natural Language API），以快速获得高质量的AI分析能力。
*   **规则引擎**: 初期可使用简单的代码逻辑实现。如果规则变得极其复杂，可考虑引入专用的Go规则引擎库（如`hyperjumptech/grule`）。
*   **异步工作流**: 视频抽帧、ASR等耗时操作必须通过异步任务队列（如`Asynq`）和独立的Worker来执行。

---
这份版本2.0的SRS文档为`content-moderation-service`构建了一个现代化、智能化、流程化的内容治理平台。通过将AI能力、动态规则和人工审核流程有机结合，它能为CINA.CLUB的健康、合规运营提供有力保障。