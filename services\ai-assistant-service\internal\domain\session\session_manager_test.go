/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package session

import (
	"context"
	"testing"
	"time"

	"cina.club/services/ai-assistant-service/internal/domain/model"
)

func TestNewSessionManager(t *testing.T) {
	store := &MockSessionStore{}
	manager := NewSessionManager(store)

	if manager == nil {
		t.Fatal("NewSessionManager() returned nil")
	}

	if manager.store != store {
		t.Error("Store not set correctly")
	}
}

func TestSessionManager_GetOrCreate_ExistingSession(t *testing.T) {
	store := &MockSessionStore{}
	manager := NewSessionManager(store)

	// Mock existing session
	existingState := model.NewDialogState("user123")
	store.sessions["session123"] = existingState

	ctx := context.Background()
	state, err := manager.GetOrCreate(ctx, "session123", "user123")

	if err != nil {
		t.Fatalf("Failed to get existing session: %v", err)
	}

	if state != existingState {
		t.Error("Got wrong session state")
	}
}

func TestSessionManager_GetOrCreate_NewSession(t *testing.T) {
	store := &MockSessionStore{}
	manager := NewSessionManager(store)

	ctx := context.Background()
	state, err := manager.GetOrCreate(ctx, "new_session", "user456")

	if err != nil {
		t.Fatalf("Failed to create new session: %v", err)
	}

	if state == nil {
		t.Fatal("Created session state is nil")
	}

	if state.UserID != "user456" {
		t.Errorf("Expected UserID 'user456', got '%s'", state.UserID)
	}

	if state.SessionID != "new_session" {
		t.Errorf("Expected SessionID 'new_session', got '%s'", state.SessionID)
	}
}

func TestSessionManager_Save(t *testing.T) {
	store := &MockSessionStore{}
	manager := NewSessionManager(store)

	state := model.NewDialogState("user789")
	ctx := context.Background()

	err := manager.Save(ctx, state)
	if err != nil {
		t.Fatalf("Failed to save session: %v", err)
	}

	// Verify it was saved to store
	if !store.setSessions[state.SessionID] {
		t.Error("Session was not saved to store")
	}
}

func TestSessionManager_Delete(t *testing.T) {
	store := &MockSessionStore{}
	manager := NewSessionManager(store)

	// Set up session
	sessionID := "session_to_delete"
	store.sessions[sessionID] = model.NewDialogState("user123")

	ctx := context.Background()
	err := manager.Delete(ctx, sessionID)

	if err != nil {
		t.Fatalf("Failed to delete session: %v", err)
	}

	// Verify it was deleted
	if !store.deletedSessions[sessionID] {
		t.Error("Session was not deleted from store")
	}
}

func TestSessionManager_GetMessageHistory(t *testing.T) {
	store := &MockSessionStore{}
	manager := NewSessionManager(store)

	// Create session with message history
	state := model.NewDialogState("user123")
	state.AddTextMessage(model.MessageRoleUser, "Hello")
	state.AddTextMessage(model.MessageRoleAssistant, "Hi there!")
	state.AddTextMessage(model.MessageRoleUser, "How are you?")

	store.sessions["session123"] = state

	ctx := context.Background()
	messages, err := manager.GetMessageHistory(ctx, "session123", 2)

	if err != nil {
		t.Fatalf("Failed to get message history: %v", err)
	}

	if len(messages) != 2 {
		t.Errorf("Expected 2 messages, got %d", len(messages))
	}

	// Should get the last 2 messages
	if messages[0].Content[0].Text != "Hi there!" {
		t.Error("Wrong first message")
	}

	if messages[1].Content[0].Text != "How are you?" {
		t.Error("Wrong second message")
	}
}

func TestSessionManager_GetMessageHistory_NonExistentSession(t *testing.T) {
	store := &MockSessionStore{}
	manager := NewSessionManager(store)

	ctx := context.Background()
	_, err := manager.GetMessageHistory(ctx, "non_existent", 5)

	if err == nil {
		t.Error("Expected error for non-existent session")
	}
}

func TestSessionManager_SessionExpiry(t *testing.T) {
	store := &MockSessionStore{}

	// Set up session
	state := model.NewDialogState("user123")
	originalExpiry := state.ExpiresAt
	store.sessions["session123"] = state

	ctx := context.Background()

	// Test extending session via ExtendTTL
	err := store.ExtendTTL(ctx, "session123", 2*time.Hour)
	if err != nil {
		t.Fatalf("Failed to extend session TTL: %v", err)
	}

	// Check if expiry was extended
	if !state.ExpiresAt.After(originalExpiry) {
		t.Error("Session expiry was not extended")
	}
}

// MockSessionStore for testing
type MockSessionStore struct {
	sessions        map[string]*model.DialogState
	setSessions     map[string]bool
	deletedSessions map[string]bool
}

func (m *MockSessionStore) Get(ctx context.Context, sessionID string) (*model.DialogState, error) {
	if m.sessions == nil {
		m.sessions = make(map[string]*model.DialogState)
	}

	state, exists := m.sessions[sessionID]
	if !exists {
		return nil, ErrSessionNotFound
	}

	return state, nil
}

func (m *MockSessionStore) Set(ctx context.Context, sessionID string, state *model.DialogState, ttl time.Duration) error {
	if m.sessions == nil {
		m.sessions = make(map[string]*model.DialogState)
	}
	if m.setSessions == nil {
		m.setSessions = make(map[string]bool)
	}

	m.sessions[sessionID] = state
	m.setSessions[sessionID] = true
	return nil
}

func (m *MockSessionStore) Delete(ctx context.Context, sessionID string) error {
	if m.deletedSessions == nil {
		m.deletedSessions = make(map[string]bool)
	}

	if m.sessions != nil {
		delete(m.sessions, sessionID)
	}
	m.deletedSessions[sessionID] = true
	return nil
}

func (m *MockSessionStore) Exists(ctx context.Context, sessionID string) (bool, error) {
	if m.sessions == nil {
		return false, nil
	}

	_, exists := m.sessions[sessionID]
	return exists, nil
}

func (m *MockSessionStore) ExtendTTL(ctx context.Context, sessionID string, ttl time.Duration) error {
	if m.sessions == nil {
		return ErrSessionNotFound
	}

	state, exists := m.sessions[sessionID]
	if !exists {
		return ErrSessionNotFound
	}

	state.ExtendExpiry(ttl)
	return nil
}
