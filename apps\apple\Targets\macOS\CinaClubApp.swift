/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import SwiftUI
import AppCore
import DataLayer
import DesignSystem
import FeatureAuth

@main
struct CinaClubApp: App {
    @StateObject private var appCore = AppCore.shared
    @StateObject private var appCoordinator = AppCoordinator()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appCore)
                .environmentObject(appCoordinator)
                .task {
                    await initializeApp()
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowToolbarStyle(.unified)
        
        // Menu bar extra for quick access
        MenuBarExtra("CINA.CLUB", systemImage: "brain.head.profile") {
            MenuBarView()
                .environmentObject(appCore)
        }
        .menuBarExtraStyle(.window)
        
        // Settings window
        Settings {
            SettingsView()
                .environmentObject(appCore)
        }
    }
    
    private func initializeApp() async {
        do {
            try await appCore.initialize()
        } catch {
            print("Failed to initialize app: \(error)")
        }
    }
}

struct ContentView: View {
    @EnvironmentObject private var appCore: AppCore
    @EnvironmentObject private var appCoordinator: AppCoordinator
    
    var body: some View {
        Group {
            if appCore.isAuthenticated {
                MainView()
            } else {
                AuthView()
            }
        }
        .frame(minWidth: 900, minHeight: 600)
        .animation(.easeInOut(duration: 0.3), value: appCore.isAuthenticated)
    }
}

struct MainView: View {
    @EnvironmentObject private var appCoordinator: AppCoordinator
    
    var body: some View {
        NavigationSplitView {
            // Sidebar
            SidebarView()
        } detail: {
            // Detail view
            detailView()
        }
        .navigationSplitViewStyle(.balanced)
    }
    
    @ViewBuilder
    private func detailView() -> some View {
        switch appCoordinator.currentTab {
        case .home:
            HomeView()
        case .chat:
            ChatView()
        case .pkb:
            PKBView()
        case .marketplace:
            MarketplaceView()
        case .profile:
            ProfileView()
        }
    }
}

struct SidebarView: View {
    @EnvironmentObject private var appCoordinator: AppCoordinator
    
    var body: some View {
        List(AppTab.allCases, id: \.self, selection: $appCoordinator.currentTab) { tab in
            Label(tab.displayName, systemImage: tab.iconName)
                .tag(tab)
        }
        .listStyle(.sidebar)
        .navigationTitle("CINA.CLUB")
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button(action: {
                    // TODO: Add new item action
                }) {
                    Image(systemName: "plus")
                }
            }
        }
    }
}

struct MenuBarView: View {
    @EnvironmentObject private var appCore: AppCore
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            // User info
            if let user = appCore.currentUser {
                HStack {
                    AvatarView(
                        imageURL: user.avatarURL,
                        size: 24,
                        fallbackText: String(user.displayName.prefix(1))
                    )
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(user.displayName)
                            .font(.cinaCallout)
                            .foregroundColor(.cinaLabel)
                        
                        Text(user.membershipType.displayName)
                            .font(.cinaCaption1)
                            .foregroundColor(.cinaSecondaryLabel)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, Spacing.sm)
                .padding(.vertical, Spacing.xs)
                
                Divider()
            }
            
            // Quick actions
            VStack(alignment: .leading, spacing: 0) {
                MenuBarActionButton(
                    icon: "message",
                    title: "New Chat",
                    action: { /* TODO */ }
                )
                
                MenuBarActionButton(
                    icon: "plus.circle",
                    title: "Add to PKB",
                    action: { /* TODO */ }
                )
                
                MenuBarActionButton(
                    icon: "bell",
                    title: "Notifications",
                    action: { /* TODO */ }
                )
            }
            
            Divider()
            
            // Settings and quit
            VStack(alignment: .leading, spacing: 0) {
                MenuBarActionButton(
                    icon: "gear",
                    title: "Settings",
                    action: { /* TODO */ }
                )
                
                MenuBarActionButton(
                    icon: "power",
                    title: "Quit",
                    action: { NSApplication.shared.terminate(nil) }
                )
            }
        }
        .frame(width: 200)
        .padding(.vertical, Spacing.xs)
    }
}

struct MenuBarActionButton: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.cinaCallout)
                    .foregroundColor(.cinaSecondaryLabel)
                    .frame(width: 16)
                
                Text(title)
                    .font(.cinaCallout)
                    .foregroundColor(.cinaLabel)
                
                Spacer()
            }
            .padding(.horizontal, Spacing.sm)
            .padding(.vertical, Spacing.xs)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { isHovered in
            // TODO: Add hover effect
        }
    }
}

struct SettingsView: View {
    @EnvironmentObject private var appCore: AppCore
    
    var body: some View {
        TabView {
            GeneralSettingsView()
                .tabItem {
                    Label("General", systemImage: "gear")
                }
            
            PrivacySettingsView()
                .tabItem {
                    Label("Privacy", systemImage: "lock")
                }
            
            NotificationSettingsView()
                .tabItem {
                    Label("Notifications", systemImage: "bell")
                }
        }
        .frame(width: 500, height: 400)
    }
}

struct GeneralSettingsView: View {
    var body: some View {
        Form {
            Section("Appearance") {
                // TODO: Add appearance settings
                Text("Appearance settings coming soon")
                    .foregroundColor(.cinaSecondaryLabel)
            }
            
            Section("Behavior") {
                // TODO: Add behavior settings
                Text("Behavior settings coming soon")
                    .foregroundColor(.cinaSecondaryLabel)
            }
        }
        .formStyle(.grouped)
        .padding()
    }
}

struct PrivacySettingsView: View {
    var body: some View {
        Form {
            Section("Data Encryption") {
                // TODO: Add encryption settings
                Text("Encryption settings coming soon")
                    .foregroundColor(.cinaSecondaryLabel)
            }
            
            Section("Sync") {
                // TODO: Add sync settings
                Text("Sync settings coming soon")
                    .foregroundColor(.cinaSecondaryLabel)
            }
        }
        .formStyle(.grouped)
        .padding()
    }
}

struct NotificationSettingsView: View {
    var body: some View {
        Form {
            Section("Notifications") {
                // TODO: Add notification settings
                Text("Notification settings coming soon")
                    .foregroundColor(.cinaSecondaryLabel)
            }
        }
        .formStyle(.grouped)
        .padding()
    }
}

// MARK: - Placeholder Views (reuse from iOS)

struct HomeView: View {
    var body: some View {
        EmptyStateView(
            image: "house",
            title: "Welcome Home",
            message: "Your personal dashboard is coming soon!"
        )
        .navigationTitle("Home")
    }
}

struct ChatView: View {
    var body: some View {
        EmptyStateView(
            image: "message",
            title: "No Conversations",
            message: "Start chatting with your AI assistant or friends!",
            actionTitle: "Start Chat"
        ) {
            // TODO: Implement chat creation
        }
        .navigationTitle("Chat")
    }
}

struct PKBView: View {
    var body: some View {
        EmptyStateView(
            image: "brain",
            title: "Your Knowledge Base",
            message: "Create your first knowledge item to get started!",
            actionTitle: "Add Item"
        ) {
            // TODO: Implement PKB item creation
        }
        .navigationTitle("Knowledge")
    }
}

struct MarketplaceView: View {
    var body: some View {
        EmptyStateView(
            image: "storefront",
            title: "Service Marketplace",
            message: "Discover and book amazing services from our community!",
            actionTitle: "Browse Services"
        ) {
            // TODO: Implement marketplace browsing
        }
        .navigationTitle("Marketplace")
    }
}

struct ProfileView: View {
    @EnvironmentObject private var appCore: AppCore
    
    var body: some View {
        ScrollView {
            VStack(spacing: Spacing.lg) {
                // User profile header
                VStack(spacing: Spacing.md) {
                    AvatarView(
                        imageURL: appCore.currentUser?.avatarURL,
                        size: 80,
                        fallbackText: String(appCore.currentUser?.displayName.prefix(1) ?? "?")
                    )
                    
                    Text(appCore.currentUser?.displayName ?? "User")
                        .font(.cinaTitle2)
                        .foregroundColor(.cinaLabel)
                    
                    Text(appCore.currentUser?.email ?? "")
                        .font(.cinaCallout)
                        .foregroundColor(.cinaSecondaryLabel)
                    
                    TagView(
                        text: appCore.currentUser?.membershipType.displayName ?? "Basic",
                        color: .cinaPrimary
                    )
                }
                .cardStyle()
                
                // Sign out button
                SecondaryButton(title: "Sign Out") {
                    Task {
                        await appCore.signOut()
                    }
                }
                .frame(maxWidth: 200)
            }
            .padding()
        }
        .navigationTitle("Profile")
    }
}

#Preview {
    ContentView()
        .environmentObject(AppCore.shared)
        .environmentObject(AppCoordinator())
} 