/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"

	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// RedisSessionStore Redis session storage implementation
type RedisSessionStore struct {
	client redis.Cmdable
	logger *logrus.Logger
	prefix string
	ttl    time.Duration
}

// NewRedisSessionStore creates a new Redis session storage
func NewRedisSessionStore(client redis.Cmdable, logger *logrus.Logger) port.SessionStore {
	return &RedisSessionStore{
		client: client,
		logger: logger,
		prefix: "admin_session:",
		ttl:    24 * time.Hour, // Default 24 hours expiration
	}
}

// CreateSession creates a new session
func (r *RedisSessionStore) CreateSession(ctx context.Context, session *model.AdminSession) error {
	key := r.session<PERSON>ey(session.ID)

	data, err := json.Marshal(session)
	if err != nil {
		r.logger.WithError(err).Error("Failed to marshal admin session")
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	err = r.client.Set(ctx, key, data, r.ttl).Err()
	if err != nil {
		r.logger.WithError(err).WithField("session_id", session.ID).Error("Failed to create admin session")
		return fmt.Errorf("failed to create session: %w", err)
	}

	// Add to employee sessions set
	err = r.addToEmployeeSessions(ctx, session.EmployeeID, session.ID)
	if err != nil {
		r.logger.WithError(err).Error("Failed to add session to employee sessions")
		// Don't return error as main operation succeeded
	}

	r.logger.WithFields(logrus.Fields{
		"session_id":  session.ID,
		"employee_id": session.EmployeeID,
		"expires_at":  session.ExpiresAt,
	}).Info("Admin session created")

	return nil
}

// GetSession gets session by session ID
func (r *RedisSessionStore) GetSession(ctx context.Context, sessionID string) (*model.AdminSession, error) {
	key := r.sessionKey(sessionID)

	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("session not found")
		}
		r.logger.WithError(err).WithField("session_id", sessionID).Error("Failed to get admin session")
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	var session model.AdminSession
	err = json.Unmarshal([]byte(data), &session)
	if err != nil {
		r.logger.WithError(err).Error("Failed to unmarshal admin session")
		return nil, fmt.Errorf("failed to unmarshal session: %w", err)
	}

	// Check if session is expired
	if !session.IsValid() {
		// Asynchronously delete expired session
		go func() {
			ctx := context.Background()
			if err := r.DeleteSession(ctx, sessionID); err != nil {
				r.logger.WithError(err).Error("Failed to delete expired session")
			}
		}()
		return nil, fmt.Errorf("session expired")
	}

	return &session, nil
}

// UpdateSession updates session information
func (r *RedisSessionStore) UpdateSession(ctx context.Context, session *model.AdminSession) error {
	key := r.sessionKey(session.ID)

	data, err := json.Marshal(session)
	if err != nil {
		r.logger.WithError(err).Error("Failed to marshal admin session")
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	// Check if session exists
	exists, err := r.client.Exists(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("failed to check session existence: %w", err)
	}
	if exists == 0 {
		return fmt.Errorf("session not found")
	}

	// Calculate remaining TTL
	ttl, err := r.client.TTL(ctx, key).Result()
	if err != nil {
		r.logger.WithError(err).Warn("Failed to get session TTL, using default")
		ttl = r.ttl
	}

	err = r.client.Set(ctx, key, data, ttl).Err()
	if err != nil {
		r.logger.WithError(err).WithField("session_id", session.ID).Error("Failed to update admin session")
		return fmt.Errorf("failed to update session: %w", err)
	}

	r.logger.WithField("session_id", session.ID).Debug("Admin session updated")
	return nil
}

// DeleteSession deletes a session
func (r *RedisSessionStore) DeleteSession(ctx context.Context, sessionID string) error {
	key := r.sessionKey(sessionID)

	// First get session info (for removing from employee sessions set)
	session, err := r.GetSession(ctx, sessionID)
	if err != nil {
		// If session doesn't exist, still try to delete the key
		r.client.Del(ctx, key)
		return nil
	}

	err = r.client.Del(ctx, key).Err()
	if err != nil {
		r.logger.WithError(err).WithField("session_id", sessionID).Error("Failed to delete admin session")
		return fmt.Errorf("failed to delete session: %w", err)
	}

	// Remove from employee sessions set
	err = r.removeFromEmployeeSessions(ctx, session.EmployeeID, sessionID)
	if err != nil {
		r.logger.WithError(err).Error("Failed to remove session from employee sessions")
		// Don't return error as main operation succeeded
	}

	r.logger.WithField("session_id", sessionID).Info("Admin session deleted")
	return nil
}

// RefreshSession refreshes session expiration time
func (r *RedisSessionStore) RefreshSession(ctx context.Context, sessionID string, duration time.Duration) error {
	key := r.sessionKey(sessionID)

	err := r.client.Expire(ctx, key, duration).Err()
	if err != nil {
		r.logger.WithError(err).WithField("session_id", sessionID).Error("Failed to refresh admin session")
		return fmt.Errorf("failed to refresh session: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"duration":   duration,
	}).Debug("Admin session refreshed")

	return nil
}

// GetActiveSessionsByEmployeeID gets all active sessions for an employee
func (r *RedisSessionStore) GetActiveSessionsByEmployeeID(ctx context.Context, employeeID string) ([]*model.AdminSession, error) {
	setKey := r.employeeSessionsKey(employeeID)

	sessionIDs, err := r.client.SMembers(ctx, setKey).Result()
	if err != nil {
		if err == redis.Nil {
			return []*model.AdminSession{}, nil
		}
		return nil, fmt.Errorf("failed to get employee sessions: %w", err)
	}

	var sessions []*model.AdminSession
	for _, sessionID := range sessionIDs {
		session, err := r.GetSession(ctx, sessionID)
		if err != nil {
			// Session doesn't exist or expired, remove from set
			r.client.SRem(ctx, setKey, sessionID)
			continue
		}
		sessions = append(sessions, session)
	}

	return sessions, nil
}

// DeleteAllSessionsByEmployeeID deletes all sessions for an employee
func (r *RedisSessionStore) DeleteAllSessionsByEmployeeID(ctx context.Context, employeeID string) error {
	sessions, err := r.GetActiveSessionsByEmployeeID(ctx, employeeID)
	if err != nil {
		return fmt.Errorf("failed to get employee sessions: %w", err)
	}

	var deletedCount int
	for _, session := range sessions {
		if err := r.DeleteSession(ctx, session.ID); err != nil {
			r.logger.WithError(err).WithField("session_id", session.ID).Error("Failed to delete employee session")
		} else {
			deletedCount++
		}
	}

	// Clean up employee sessions set
	setKey := r.employeeSessionsKey(employeeID)
	r.client.Del(ctx, setKey)

	r.logger.WithFields(logrus.Fields{
		"employee_id":    employeeID,
		"deleted_count":  deletedCount,
		"total_sessions": len(sessions),
	}).Info("Deleted employee sessions")

	return nil
}

// GetSessionCount gets total number of active sessions
func (r *RedisSessionStore) GetSessionCount(ctx context.Context) (int64, error) {
	pattern := r.prefix + "*"

	// Use SCAN command to iterate through all session keys
	iter := r.client.Scan(ctx, 0, pattern, 0).Iterator()
	count := int64(0)

	for iter.Next(ctx) {
		count++
	}

	if err := iter.Err(); err != nil {
		return 0, fmt.Errorf("failed to count sessions: %w", err)
	}

	return count, nil
}

// CleanupExpiredSessions cleans up expired sessions
func (r *RedisSessionStore) CleanupExpiredSessions(ctx context.Context) (int64, error) {
	pattern := r.prefix + "*"

	// Use SCAN command to iterate through all session keys
	iter := r.client.Scan(ctx, 0, pattern, 0).Iterator()
	cleanedCount := int64(0)

	for iter.Next(ctx) {
		key := iter.Val()
		sessionID := r.extractSessionID(key)

		// Try to get session, if expired it will be automatically deleted
		_, err := r.GetSession(ctx, sessionID)
		if err != nil {
			cleanedCount++
		}
	}

	if err := iter.Err(); err != nil {
		return cleanedCount, fmt.Errorf("failed to cleanup expired sessions: %w", err)
	}

	r.logger.WithField("cleaned_count", cleanedCount).Info("Cleaned up expired admin sessions")
	return cleanedCount, nil
}

// Helper methods

// sessionKey generates session key
func (r *RedisSessionStore) sessionKey(sessionID string) string {
	return r.prefix + sessionID
}

// employeeSessionsKey generates employee sessions set key
func (r *RedisSessionStore) employeeSessionsKey(employeeID string) string {
	return fmt.Sprintf("admin_employee_sessions:%s", employeeID)
}

// extractSessionID extracts session ID from key
func (r *RedisSessionStore) extractSessionID(key string) string {
	if len(key) > len(r.prefix) {
		return key[len(r.prefix):]
	}
	return ""
}

// addToEmployeeSessions adds session to employee sessions set
func (r *RedisSessionStore) addToEmployeeSessions(ctx context.Context, employeeID, sessionID string) error {
	setKey := r.employeeSessionsKey(employeeID)
	return r.client.SAdd(ctx, setKey, sessionID).Err()
}

// removeFromEmployeeSessions removes session from employee sessions set
func (r *RedisSessionStore) removeFromEmployeeSessions(ctx context.Context, employeeID, sessionID string) error {
	setKey := r.employeeSessionsKey(employeeID)
	return r.client.SRem(ctx, setKey, sessionID).Err()
}
