# CINA.CLUB Platform - Analytics Service Base Kustomization
# Copyright (c) 2025 Cina.Club

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: analytics-service-base
  annotations:
    description: "Base configuration for Analytics Service Kong Ingress"
    service: "analytics-service"
    service-type: "analytics-processing"

resources:
  - ingress.yaml

namespace: analytics

commonLabels:
  app: analytics-service
  component: api-gateway
  service: analytics
  tier: application
  platform: cina-club
  service-type: analytics-processing

commonAnnotations:
  platform: "cina-club"
  service-owner: "<EMAIL>"
  api-version: "v1"
  managed-by: "kong-ingress-controller" 