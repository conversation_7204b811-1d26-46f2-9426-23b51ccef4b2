好的，遵照您的指示。我将为您生成一份专门针对 **`gamification-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`gamification-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**灵活的规则引擎、多维度的激励体系（XP、徽章、积分）、高性能的排行榜计算，以及与平台生态的事件驱动集成**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `gamification-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `gamification-service-srs.md` (v2.0)
**核心架构**: 事件驱动 + 规则引擎 + 策略模式(用于奖励)

## 1. 概述

`gamification-service` 是CINA.CLUB平台的用户**激励层**和**行为引导引擎**。其核心挑战在于：
1.  **规则的灵活性与可扩展性**: 需要一个能应对多变运营需求的规则引擎，允许非技术人员（运营团队）通过配置来创建和修改激励规则。
2.  **高吞吐事件处理**: 需要能消费来自平台所有服务的海量用户行为事件，并进行实时评估。
3.  **多维激励体系管理**: 需要同时管理并精确计算用户的经验值(XP)、积分(Points)和徽章(Badges)的获取与消耗。
4.  **高性能排行榜**: 排行榜的计算，特别是周期性排行榜，可能会非常耗费资源，需要高效的实现方案。
5.  **原子性与一致性**: 奖励的发放（特别是涉及积分和XP的）必须是原子操作，防止并发问题。
6.  **防作弊与滥用**: 必须有机制来防止用户通过恶意行为刷取奖励。

本架构设计通过采用**事件驱动**的架构，内置一个**可配置的规则引擎**，并利用**Redis**来处理高性能的排行榜和速率限制，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (事件驱动的奖励流程)

```mermaid
graph TD
    subgraph "平台事件总线 (Kafka)"
        A[Platform Events<br/>(e.g., TaskCompletedEvent)]
    end

    subgraph "GamificationService"
        B[Kafka Consumer<br/><em>adapter/event</em>]
        C[Application Service<br/><em>application/service</em>]
        D[RuleEngine<br/><em>domain/service</em>]
        E[RewardStrategyFactory<br/><em>domain/strategy</em>]
        F{Reward Strategies<br/>(AwardXP, GrantBadge, ...)}
        G[Repository<br/><em>adapter/repository</em>]
        H[Cache (Redis)<br/><em>adapter/cache</em>]
        I[API Layer (gRPC)<br/><em>adapter/transport</em>]
    end

    subgraph "下游服务"
        J[Client App]
        K[cina-coin-ledger-service]
        L[digital-twin-service]
        M[notification-dispatch-service]
    end

    A -- "1. 用户行为事件" --> B
    B -- "调用" --> C
    C -- "2. Evaluate with RuleEngine" --> D
    D -- "Loads rules from DB" --> G
    D -- "3. Returns matched actions" --> C
    
    C -- "4. Get Reward Strategy" --> E
    E -- "Returns" --> F
    
    C -- "5. Execute Strategy" --> F
    F -- "6a. Update DB" --> G
    F -- "6b. Update Cache" --> H
    F -- "6c. Call External Svc" --> K & L & M

    J -- "7. 查询用户档案/排行榜" --> I
    I -- "查询" --> C
    C -- "优先查缓存" --> H
    H -- "未命中则查DB" --> G
```

### 2.2 最终目录结构 (`services/gamification-service/`)

```
gamification-service/
├── cmd/server/
│   └── main.go                 # API服务和事件消费者的启动入口
├── cmd/worker/
│   └── main.go                 # ✨ 排行榜结算等后台任务的独立入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_cache.go  # 排行榜、速率限制
│   │   ├── client/
│   │   │   └── ... (下游服务客户端)
│   │   ├── event/
│   │   │   └── consumer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── gamification_service.go # 核心应用服务实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       ├── service/
│       │   └── rule_engine.go      # ✨ 规则引擎核心 ✨
│       └── strategy/               # ✨ 奖励策略模式实现 ✨
│           ├── interface.go
│           ├── award_xp_strategy.go
│           ├── grant_badge_strategy.go
│           └── factory.go
├── config/
│   └── rules/
│       └── default_rules.json  # (可选) 默认或示例规则的配置文件
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Rules of Fun)

*   `domain/model/`: 使用`/core/models`中与游戏化相关的`struct`，如`GamificationProfile`, `Badge`, `Leaderboard`。
*   **`domain/service/rule_engine.go`**: **这是本服务决策的核心**。
    *   **`RuleEngine` struct**: 注入`Repository`以加载规则。
    *   **`Evaluate(ctx, event)` method**:
        1.  从事件中提取`eventType`和`userID`。
        2.  调用`repository.GetActiveRulesByEventType()`从数据库或缓存中加载所有相关的、启用的规则。
        3.  **循环处理每条规则**:
            a. **速率限制检查**: 调用`Cache`接口检查该用户针对此规则的操作是否在速率限制内。
            b. **条件评估**: 使用一个表达式求值库（如`govaluate`），解析规则的`conditions` JSONB字段，并用`event`的payload进行求值。
            c. 如果所有条件都满足，将该规则的`actions`添加到一个待执行列表中。
        4.  返回所有匹配的动作列表。
*   **`domain/strategy/`**: **策略模式的实现，用于执行不同的奖励动作**。
    *   `interface.go`: 定义`RewardStrategy`接口。
        ```go
        type RewardContext struct { /* User, Event, ... */ }
        type RewardStrategy interface {
            Execute(ctx context.Context, context RewardContext, params map[string]interface{}) error
        }
        ```
    *   **`award_xp_strategy.go`, `grant_badge_strategy.go`**: 分别实现`RewardStrategy`接口。例如，`AwardXPStrategy`会调用仓储，原子性地增加用户的`contribution_xp`，并检查是否升级。
    *   `factory.go`: `RewardStrategyFactory`根据动作类型（如"AWARD_XP"）返回一个具体的策略实例。

### 3.2 `application/` - 应用层 (The Event Processor)

*   `application/port/`: 定义`Repository`, `Cache`, `GamificationService`等接口。
*   **`application/service/gamification_service.go`**: 实现`GamificationService`接口，是所有业务流程的编排者。
    *   **`ProcessEvent(ctx, event)` (由事件消费者调用)**:
        1.  调用`domain.RuleEngine.Evaluate(ctx, event)`获取待执行的动作列表。
        2.  **循环处理每个动作**:
            a. 使用`strategy.Factory`获取对应的`RewardStrategy`实例。
            b. 构造一个`RewardContext`。
            c. 调用`strategy.Execute(...)`来执行奖励逻辑。
            d. **错误处理**: 如果某个奖励执行失败，应记录日志，但通常不应阻塞其他奖励的执行。可以发送一个失败事件到专门的队列供后续处理。
    *   **`GetLeaderboard(ctx, leaderboardID)`**:
        1.  **优先从`Cache` (Redis Sorted Set) 中获取排行榜**。
        2.  如果缓存不存在或已过期，则触发一个后台任务来重建排行榜，同时可以返回一个旧的缓存或提示“正在计算中”。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库选型**: **PostgreSQL**。其强事务性对于保证XP和积分的原子性更新至关重要。
    *   `repo.go`: 实现`port.Repository`接口。
    *   **原子更新**: 对`user_gamification_profiles`表的`xp`和`points`字段的更新，**必须**使用`UPDATE ... SET points_balance = points_balance + ? WHERE ...`这样的原子操作，并结合**乐观锁（version字段）**。
*   **`adapter/cache/`**: **Redis实现，用于高性能场景**。
    *   `redis_cache.go`:
        *   **排行榜**: 使用**Redis Sorted Set**。`Key: leaderboard:{id}:{period}`, `Score: points/xp`, `Member: userID`。
        *   **速率限制**: 使用Redis的`INCR`和`EXPIRE`命令实现滑动窗口或固定窗口的速率限制器。
*   **`adapter/event/`**:
    *   `consumer.go`: 封装`pkg/messaging`的`ConsumerGroup`，负责从Kafka消费平台所有业务事件，并将其传递给`application.GamificationService.ProcessEvent`。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现`gamification-service.proto`中定义的查询接口。

### 3.4 `cmd/worker/` - 后台任务

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   **排行榜结算器 (Leaderboard Finalizer)**:
        *   以**Kubernetes CronJob**的形式定期运行（如每日/每周/每月的零点）。
        *   **工作**:
            a. 从Redis中获取上一个周期的最终排行榜排名。
            b. 将最终排名持久化到PostgreSQL的`leaderboard_archives`表中。
            c. 根据排行榜的奖励配置，为Top N用户触发奖励发放流程（调用`application.service`的相关方法）。
            d. 清理旧的Redis排行榜Key。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`gamification-service`：
1.  **事件驱动核心**: 整个服务围绕消费平台事件来构建，实现了与业务源的完全解耦。
2.  **规则引擎与策略模式分离**:
    *   **规则引擎(`RuleEngine`)** 负责“**决定做什么**”（匹配规则，返回动作列表）。
    *   **策略模式(`RewardStrategy`)** 负责“**如何做**”（执行具体的奖励逻辑）。
    *   这种分离使得运营可以灵活配置“决定”部分，而开发可以独立扩展“如何做”部分。
3.  **高性能数据处理**: 充分利用**Redis**来处理对性能要求极高的排行榜计算和速率限制，而将需要强事务性的数据（用户档案）保留在**PostgreSQL**中。
4.  **原子性与防作弊**: 在应用层和仓储层明确了使用数据库原子操作和乐观锁来保证数据一致性，并在领域层内置了防作弊的检查点。
5.  **后台任务分离**: 将资源消耗大的、定期的排行榜结算任务移至独立的Worker进程，保证了在线API和事件处理的性能。

这种架构确保了`gamification-service`能够以一种**灵活、可靠、高性能且防滥用**的方式，深度驱动整个CINA.CLUB平台的用户行为，是实现用户增长和社区活跃度的核心技术引擎。