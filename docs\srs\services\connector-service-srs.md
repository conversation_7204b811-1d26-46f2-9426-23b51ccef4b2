好的，遵照您的指示。我将根据我们刚才讨论的、整合了“外部连接器”和OAuth 2.0流程的宏大构想，为您**重新生成一份全新的、生产级的`connector-service`需求规格说明书**。

这份**版本 2.0**的SRS，将把`connector-service`从一个简单的内部适配器，**战略升级**为一个功能对标Zapier/Make核心连接能力的、支持内外部应用的**平台级集成与连接中枢**。

---
### CINA.CLUB - connector-service (连接器服务) 需求规格说明书

**版本: 2.0 (生产级定义，集成外部应用连接)**  
**发布日期: 2025-06-28**  
**文档负责人:** [平台集成团队负责人/架构师]  
**审批人:** [CTO/产品总监]

#### 修订历史
| 版本 | 日期       | 修订人     | 修订描述                                                     |
|------|------------|------------|--------------------------------------------------------------|
| 1.0  | (N/A)      | -          | 初始构想，作为内部服务的动作执行器。                         |
| 2.0  | 2025-06-28 | Cina.Club  | **战略升级为内外兼修的连接器平台，增加对外部应用OAuth 2.0连接、凭证管理和轮询触发器的支持。** |

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心架构与流程](#3-核心架构与流程)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了将CINA.CLUB打造成一个连接和自动化用户数字生活的“超级应用”，平台需要具备与广泛的内部服务和**第三方外部应用**（如Google Drive, Slack, Notion）无缝集成的能力。`connector-service` 的目的在于构建这样一个**集中化、可扩展、极度安全的连接器即服务(Connector-as-a-Service)平台**。它负责将所有内部和外部应用的能力，标准化为可被自动化工作流消费的**触发器(Triggers)**和**动作(Actions)**，并管理用户与这些应用的**安全连接和授权**。

#### 1.2. 服务范围
本服务 **负责**:
*   **连接器目录管理**: 维护一个全平台的“连接器(Connector)”目录，每个连接器代表一个内部服务或一个外部应用。
*   **能力Schema注册**: 为每个连接器的触发器和动作，提供其输入/输出的**JSON Schema**。
*   **外部应用连接管理 (核心)**:
    *   处理连接外部应用的完整**OAuth 2.0授权码流程**。
    *   **安全地存储和自动刷新**用户的外部应用访问凭证（Access/Refresh Tokens），**必须**使用`key-management-proxy-service`进行加密。
*   **统一动作执行网关**:
    *   提供一个统一的`ExecuteAction` gRPC接口，供`routines-service`调用。
    *   透明地处理内部服务调用和外部API调用的认证和参数适配。
*   **轮询触发器实现**: 为需要轮询的触发器（如“检查Google Drive中是否有新文件”）提供有状态的执行逻辑。

本服务 **不负责**:
*   **工作流的定义、编排与执行**: 由`routines-service`负责。
*   **最终用户的UI界面**。

#### 1.3. 目标用户/调用方
*   **`routines-service` (主要)**: 调用本服务执行动作和轮询触发器。
*   **CINA.CLUB客户端应用**:
    *   调用本服务获取可用的连接器、触发器和动作目录，以构建工作流编辑器UI。
    *   调用本服务发起和处理连接外部应用的OAuth流程。
*   **`key-management-proxy-service`**: (被本服务调用) 加密/解密外部应用的`refresh_token`。
*   **CINA.CLUB平台管理员**: 通过后台管理和审核新的连接器。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`connector-service` 是CINA.CLUB平台自动化生态的“**万能适配器**”和“**联邦认证中心**”。它位于`routines-service`（大脑）和所有能力提供方之间，扮演着**能力目录、认证代理、和API适配器**的三重角色。它将平台所有分散、异构的能力“标准化”和“API化”，是实现平台从一个封闭生态走向开放互联的关键。

#### 2.2. 主要功能概述
*   可扩展的、插件式的连接器框架，同时支持内部和外部应用。
*   安全、可靠的、面向多应用的OAuth 2.0流程管理和凭证生命周期管理。
*   统一的、权限安全的动作执行网关，对上层透明。
*   有状态的轮询触发器执行能力。

---

### 3. 核心架构与流程

#### 3.1 用户连接外部应用 (OAuth 2.0) 流程

```mermaid
sequenceDiagram
    participant Client
    participant ConnectorService as CS
    participant ExternalApp as "e.g., Google"
    participant KMSProxy

    Client->>CS: 1. POST /connections/initiate (connector_id: "google_drive")
    CS->>ExternalApp: 2. Generate Auth URL with client_id, scope, redirect_uri, state
    CS-->>Client: 3. (auth_url)

    Note over Client: User is redirected to Google's consent screen and authorizes.
    
    ExternalApp-->>CS: 4. [Redirect] User is sent to /connections/callback?code=...&state=...
    
    CS->>ExternalApp: 5. Exchange authorization_code for tokens
    ExternalApp-->>CS: 6. (access_token, refresh_token, expires_in)
    
    CS->>KMSProxy: 7. **Encrypt** the refresh_token
    KMSProxy-->>CS: (encrypted_refresh_token)
    
    CS->>DB: 8. Store the new connection record
    CS-->>Client: 9. [Redirect] Send user back to a "success" page in the app.
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 连接器框架与目录服务
*   **FR4.1.1 (插件式框架)**: 系统必须提供一个**`Connector`接口**，所有内部和外部连接器都必须实现此接口。这使得添加新连接器成为一个标准化的、低耦合的过程。
*   **FR4.1.2 (能力Schema)**:
    *   每个触发器和动作的实现，都必须提供其输入/输出字段的**JSON Schema**。
    *   Schema中应包含字段的`type`, `title`, `description`, `required`等信息，以及用于动态下拉菜单的`source`（如调用另一个API获取选项列表）。
*   **FR4.1.3 (目录API)**: `GET /catalog`接口必须能返回一个结构化的JSON，包含所有已注册的连接器及其支持的触发器、动作和对应的Schema，供前端动态渲染UI。

#### 4.2. 外部应用连接管理 (核心)
*   **FR4.2.1 (OAuth 2.0流程)**: 必须完整、安全地实现**FR3.1**中定义的OAuth 2.0授权码流程，并能处理错误和用户取消授权的情况。
*   **FR4.2.2 (凭证安全存储)**:
    *   **`refresh_token`** **必须**在持久化前，通过调用`key-management-proxy-service`进行加密。
    *   平台的**`client_id`和`client_secret`**也必须从安全的Secrets Manager中获取。
*   **FR4.2.3 (令牌自动刷新)**: 在执行一个外部动作时，系统必须实现**透明的令牌刷新机制**。如果检测到`access_token`过期，应自动在后台使用`refresh_token`获取新令牌，并更新数据库，然后继续执行原动作。此过程对`routines-service`无感知。
*   **FR4.2.4 (连接失效处理)**: 如果`refresh_token`本身失效（如用户在外部应用中撤销了授权），系统必须将对应的`user_connections`记录标记为`NEEDS_REAUTH`，并通知用户需要重新授权。

#### 4.3. 统一动作/触发器执行
*   **FR4.3.1 (动作执行接口)**: 必须提供一个统一的`ExecuteAction` gRPC接口。
    *   **输入**: `action_name` (e.g., `google_drive.upload_file`), `inputs` (map), `user_auth_context` (包含用户的S2S令牌)。
    *   **流程**: 内部根据`action_name`动态分派到对应的连接器和动作实现，并准备好执行所需的认证凭证（内部S2S令牌或外部OAuth令牌）。
*   **FR4.3.2 (轮询触发器执行接口)**: 必须提供一个统一的`ExecutePollingTrigger` gRPC接口。
    *   **输入**: `trigger_name`, `user_connection_id`, `last_state`。
    *   **输出**: `new_items` (触发的新事件列表), `updated_state`。
    *   **状态管理**: 轮询逻辑**本身是无状态的**，它接收上次的状态，并返回新的状态。状态的持久化由调用方（`routines-service`）负责。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端RESTful API接口
*   **版本**: `/api/v1/connectors`
*   **核心端点**:
    *   `GET /catalog`: 获取连接器、触发器和动作的完整目录。
    *   `POST /connections/initiate`: 为指定连接器发起OAuth授权流程。
    *   `GET /connections/callback`: 接收OAuth回调。
    *   `GET /connections/me`: 获取用户的所有有效连接。
    *   `DELETE /connections/me/{connectionId}`: 删除一个连接。

#### 5.2. 内部gRPC API接口 (S2S)
*   **Package**: `hina.v1.connector`
*   **核心RPC**:
    *   `rpc ExecuteAction(ExecuteActionRequest) returns (ExecuteActionResponse)`
    *   `rpc ExecutePollingTrigger(ExecutePollingTriggerRequest) returns (ExecutePollingTriggerResponse)`

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`connectors` (可选, 用于动态管理)**: `id (PK)`, `name`, `auth_type`, `oauth_config (JSONB)`, `encrypted_credentials (JSONB)`。
*   **`user_connections`**: `id (PK)`, `user_id (INDEX)`, `connector_id (FK)`, `status` (`ACTIVE`, `NEEDS_REAUTH`), `access_token`, `encrypted_refresh_token (BYTEA)`, `expires_at`, `scopes (TEXT[])`.
*   **`polling_trigger_states`**: (此表已移至`routines-service`，本服务是无状态的)

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   目录API P99 < 150ms (可被重度缓存)。
    *   动作执行API的开销（不含对下游API的调用时间）P99 < 50ms。
*   **NFR7.2 (可靠性)**:
    *   OAuth流程和令牌刷新机制必须极度健壮。
    *   对所有下游API的调用都必须有合理的超时、重试和熔断机制。
*   **NFR7.3 (可扩展性)**:
    *   服务应为无状态，可水平扩展。
    *   连接器框架的设计应确保添加新连接器不会影响现有连接器的性能。
*   **NFR7.4 (安全性 - 最高优先级)**:
    *   **凭证安全**: **FR4.2.2**中定义的凭证加密存储是核心安全要求。
    *   **OAuth安全**: 严格使用`state`参数防止CSRF，严格校验`redirect_uri`。
    *   **权限边界**: 严格隔离每个用户的连接和凭证。

---

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **适配器/插件模式**: 这是本服务架构的核心，必须清晰地定义`Connector`接口和注册机制。
*   **OAuth库**: **强制**使用`golang.org/x/oauth2`标准库来处理OAuth 2.0流程。
*   **加密**: **强制**使用`key-management-proxy-service`来处理所有敏感凭证的加密。
*   **配置**: 每个外部连接器的配置（如`client_id`, `client_secret`）应从安全的配置源加载，而不是硬编码。

---
这份版本2.0的SRS为`connector-service`构建了一个功能强大、安全可靠、高度可扩展的集成与连接平台。它不仅是平台内部自动化的基础，更是CINA.CLUB走向开放生态、连接外部世界的关键桥梁。