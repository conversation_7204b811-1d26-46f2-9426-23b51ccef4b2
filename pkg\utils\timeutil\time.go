/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package timeutil provides time utilities that complement the standard time package.
package timeutil

import (
	"fmt"
	"time"
)

// Common time format constants
const (
	// ISO8601 is the ISO 8601 date-time format
	ISO8601 = "2006-01-02T15:04:05Z07:00"

	// RFC3339Nano is RFC 3339 with nanoseconds
	RFC3339Nano = "2006-01-02T15:04:05.999999999Z07:00"

	// DateOnly is the date-only format (YYYY-MM-DD)
	DateOnly = "2006-01-02"

	// TimeOnly is the time-only format (HH:MM:SS)
	TimeOnly = "15:04:05"

	// DateTime is a common date-time format
	DateTime = "2006-01-02 15:04:05"

	// DateTimeWithZone is date-time with timezone
	DateTimeWithZone = "2006-01-02 15:04:05 MST"

	// Compact is a compact format without separators
	Compact = "20060102150405"

	// CompactDate is a compact date format
	CompactDate = "20060102"

	// CompactTime is a compact time format
	CompactTime = "150405"

	// Human readable formats
	HumanDate     = "January 2, 2006"
	HumanDateTime = "January 2, 2006 at 3:04 PM"
	HumanTime     = "3:04 PM"
)

// NowMillis returns the current Unix timestamp in milliseconds.
//
// Example:
//
//	timestamp := timeutil.NowMillis()  // returns current time in milliseconds
func NowMillis() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

// NowSeconds returns the current Unix timestamp in seconds.
//
// Example:
//
//	timestamp := timeutil.NowSeconds()  // returns current time in seconds
func NowSeconds() int64 {
	return time.Now().Unix()
}

// NowMicros returns the current Unix timestamp in microseconds.
//
// Example:
//
//	timestamp := timeutil.NowMicros()  // returns current time in microseconds
func NowMicros() int64 {
	return time.Now().UnixNano() / int64(time.Microsecond)
}

// NowNanos returns the current Unix timestamp in nanoseconds.
//
// Example:
//
//	timestamp := timeutil.NowNanos()  // returns current time in nanoseconds
func NowNanos() int64 {
	return time.Now().UnixNano()
}

// FromMillis converts a Unix timestamp in milliseconds to a Time.
//
// Example:
//
//	t := timeutil.FromMillis(1640995200000)  // converts milliseconds to Time
func FromMillis(millis int64) time.Time {
	return time.Unix(millis/1000, (millis%1000)*int64(time.Millisecond))
}

// FromSeconds converts a Unix timestamp in seconds to a Time.
//
// Example:
//
//	t := timeutil.FromSeconds(1640995200)  // converts seconds to Time
func FromSeconds(seconds int64) time.Time {
	return time.Unix(seconds, 0)
}

// FromMicros converts a Unix timestamp in microseconds to a Time.
//
// Example:
//
//	t := timeutil.FromMicros(1640995200000000)  // converts microseconds to Time
func FromMicros(micros int64) time.Time {
	return time.Unix(micros/1000000, (micros%1000000)*int64(time.Microsecond))
}

// StartOfDay returns the start of the day (00:00:00) for the given time.
//
// Example:
//
//	start := timeutil.StartOfDay(time.Now())  // returns today at 00:00:00
func StartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// EndOfDay returns the end of the day (23:59:59.999999999) for the given time.
//
// Example:
//
//	end := timeutil.EndOfDay(time.Now())  // returns today at 23:59:59.999999999
func EndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}

// StartOfWeek returns the start of the week (Monday 00:00:00) for the given time.
//
// Example:
//
//	start := timeutil.StartOfWeek(time.Now())  // returns Monday of this week at 00:00:00
func StartOfWeek(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 { // Sunday
		weekday = 7
	}
	days := weekday - 1 // Days since Monday
	monday := t.AddDate(0, 0, -days)
	return StartOfDay(monday)
}

// EndOfWeek returns the end of the week (Sunday 23:59:59.999999999) for the given time.
//
// Example:
//
//	end := timeutil.EndOfWeek(time.Now())  // returns Sunday of this week at 23:59:59.999999999
func EndOfWeek(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 { // Sunday
		return EndOfDay(t)
	}
	daysUntilSunday := 7 - weekday
	sunday := t.AddDate(0, 0, daysUntilSunday)
	return EndOfDay(sunday)
}

// StartOfMonth returns the start of the month (1st day 00:00:00) for the given time.
//
// Example:
//
//	start := timeutil.StartOfMonth(time.Now())  // returns 1st of this month at 00:00:00
func StartOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// EndOfMonth returns the end of the month (last day 23:59:59.999999999) for the given time.
//
// Example:
//
//	end := timeutil.EndOfMonth(time.Now())  // returns last day of this month at 23:59:59.999999999
func EndOfMonth(t time.Time) time.Time {
	nextMonth := t.AddDate(0, 1, 0)
	firstOfNextMonth := StartOfMonth(nextMonth)
	lastOfThisMonth := firstOfNextMonth.Add(-time.Nanosecond)
	return lastOfThisMonth
}

// StartOfYear returns the start of the year (January 1st 00:00:00) for the given time.
//
// Example:
//
//	start := timeutil.StartOfYear(time.Now())  // returns January 1st of this year at 00:00:00
func StartOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), time.January, 1, 0, 0, 0, 0, t.Location())
}

// EndOfYear returns the end of the year (December 31st 23:59:59.999999999) for the given time.
//
// Example:
//
//	end := timeutil.EndOfYear(time.Now())  // returns December 31st of this year at 23:59:59.999999999
func EndOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), time.December, 31, 23, 59, 59, 999999999, t.Location())
}

// Age calculates the age in years from the given birth date to now.
//
// Example:
//
//	birthDate := time.Date(1990, 5, 15, 0, 0, 0, 0, time.UTC)
//	age := timeutil.Age(birthDate)  // returns age in years
func Age(birthDate time.Time) int {
	return AgeAt(birthDate, time.Now())
}

// AgeAt calculates the age in years from the birth date to the specified date.
//
// Example:
//
//	birthDate := time.Date(1990, 5, 15, 0, 0, 0, 0, time.UTC)
//	atDate := time.Date(2020, 5, 15, 0, 0, 0, 0, time.UTC)
//	age := timeutil.AgeAt(birthDate, atDate)  // returns 30
func AgeAt(birthDate, atDate time.Time) int {
	age := atDate.Year() - birthDate.Year()

	// Adjust if birthday hasn't occurred this year yet
	if atDate.YearDay() < birthDate.YearDay() {
		age--
	}

	return age
}

// DaysBetween returns the number of days between two dates.
// The result is positive if end is after start, negative if before.
//
// Example:
//
//	start := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
//	end := time.Date(2023, 1, 10, 0, 0, 0, 0, time.UTC)
//	days := timeutil.DaysBetween(start, end)  // returns 9
func DaysBetween(start, end time.Time) int {
	duration := end.Sub(start)
	return int(duration.Hours() / 24)
}

// IsWeekend checks if the given time falls on a weekend (Saturday or Sunday).
//
// Example:
//
//	isWeekend := timeutil.IsWeekend(time.Now())
func IsWeekend(t time.Time) bool {
	weekday := t.Weekday()
	return weekday == time.Saturday || weekday == time.Sunday
}

// IsWeekday checks if the given time falls on a weekday (Monday to Friday).
//
// Example:
//
//	isWeekday := timeutil.IsWeekday(time.Now())
func IsWeekday(t time.Time) bool {
	return !IsWeekend(t)
}

// IsLeapYear checks if the given year is a leap year.
//
// Example:
//
//	isLeap := timeutil.IsLeapYear(2024)  // returns true
//	isLeap = timeutil.IsLeapYear(2023)   // returns false
func IsLeapYear(year int) bool {
	return year%4 == 0 && (year%100 != 0 || year%400 == 0)
}

// DaysInMonth returns the number of days in the specified month and year.
//
// Example:
//
//	days := timeutil.DaysInMonth(2024, time.February)  // returns 29 (leap year)
//	days = timeutil.DaysInMonth(2023, time.February)   // returns 28
func DaysInMonth(year int, month time.Month) int {
	// Get the first day of the next month, then subtract one day
	firstOfNextMonth := time.Date(year, month+1, 1, 0, 0, 0, 0, time.UTC)
	lastOfThisMonth := firstOfNextMonth.AddDate(0, 0, -1)
	return lastOfThisMonth.Day()
}

// FormatDuration formats a duration in a human-readable way.
//
// Example:
//
//	formatted := timeutil.FormatDuration(2*time.Hour + 30*time.Minute)  // returns "2h30m"
//	formatted = timeutil.FormatDuration(90*time.Second)                 // returns "1m30s"
func FormatDuration(d time.Duration) string {
	if d == 0 {
		return "0s"
	}

	var parts []string

	// Handle negative durations
	negative := d < 0
	if negative {
		d = -d
	}

	// Extract days, hours, minutes, seconds
	days := int(d.Hours()) / 24
	hours := int(d.Hours()) % 24
	minutes := int(d.Minutes()) % 60
	seconds := int(d.Seconds()) % 60

	if days > 0 {
		parts = append(parts, fmt.Sprintf("%dd", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%dh", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%dm", minutes))
	}
	if seconds > 0 || len(parts) == 0 {
		parts = append(parts, fmt.Sprintf("%ds", seconds))
	}

	result := ""
	for _, part := range parts {
		result += part
	}

	if negative {
		result = "-" + result
	}

	return result
}

// ParseAnyFormat attempts to parse a date string using multiple common formats.
// Returns the parsed time and the format that was successfully used.
//
// Example:
//
//	t, format, err := timeutil.ParseAnyFormat("2023-01-15")
//	// t is the parsed time, format is "2006-01-02", err is nil
func ParseAnyFormat(dateStr string) (time.Time, string, error) {
	formats := []string{
		time.RFC3339,
		time.RFC3339Nano,
		ISO8601,
		DateTime,
		DateOnly,
		TimeOnly,
		"2006-01-02T15:04:05",
		"2006/01/02 15:04:05",
		"2006/01/02",
		"01/02/2006",
		"01/02/2006 15:04:05",
		"02/01/2006",
		"02/01/2006 15:04:05",
		"Jan 2, 2006",
		"January 2, 2006",
		"Jan 2, 2006 15:04:05",
		"January 2, 2006 15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t, format, nil
		}
	}

	return time.Time{}, "", fmt.Errorf("unable to parse date string: %s", dateStr)
}
