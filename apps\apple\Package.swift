// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import PackageDescription

let package = Package(
    name: "CinaClubApple",
    platforms: [
        .iOS(.v17),
        .macOS(.v14),
        .watchOS(.v10),
        .visionOS(.v1)
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(name: "AppCore", targets: ["AppCore"]),
        .library(name: "DataLayer", targets: ["DataLayer"]),
        .library(name: "DesignSystem", targets: ["DesignSystem"]),
        .library(name: "GoBridge", targets: ["GoBridge"]),
        .library(name: "FeatureAuth", targets: ["FeatureAuth"]),
        .library(name: "FeatureChat", targets: ["FeatureChat"]),
        .library(name: "FeaturePKB", targets: ["FeaturePKB"]),
        .library(name: "FeatureLive", targets: ["FeatureLive"]),
        .library(name: "FeatureMarketplace", targets: ["FeatureMarketplace"])
    ],
    dependencies: [
        // Dependencies declare other packages that this package depends on.
        .package(url: "https://github.com/grpc/grpc-swift.git", from: "1.23.0"),
        .package(url: "https://github.com/apple/swift-log.git", from: "1.0.0"),
        .package(url: "https://github.com/apple/swift-crypto.git", from: "3.0.0"),
        .package(url: "https://github.com/pointfreeco/swift-composable-architecture.git", from: "1.7.0")
    ],
    targets: [
        // Core business logic layer
        .target(
            name: "AppCore",
            dependencies: [
                "GoBridge",
                .product(name: "Logging", package: "swift-log"),
                .product(name: "ComposableArchitecture", package: "swift-composable-architecture")
            ]
        ),
        
        // Data layer for repositories and network
        .target(
            name: "DataLayer",
            dependencies: [
                "AppCore",
                "GoBridge",
                .product(name: "GRPC", package: "grpc-swift"),
                .product(name: "Logging", package: "swift-log")
            ]
        ),
        
        // Design system and UI components
        .target(
            name: "DesignSystem",
            dependencies: []
        ),
        
        // Go bridge for core logic
        .target(
            name: "GoBridge",
            dependencies: [
                .product(name: "Crypto", package: "swift-crypto")
            ]
        ),
        
        // Feature modules
        .target(
            name: "FeatureAuth",
            dependencies: [
                "AppCore",
                "DataLayer",
                "DesignSystem"
            ]
        ),
        
        .target(
            name: "FeatureChat",
            dependencies: [
                "AppCore",
                "DataLayer", 
                "DesignSystem"
            ]
        ),
        
        .target(
            name: "FeaturePKB",
            dependencies: [
                "AppCore",
                "DataLayer",
                "DesignSystem"
            ]
        ),
        
        .target(
            name: "FeatureLive",
            dependencies: [
                "AppCore",
                "DataLayer",
                "DesignSystem"
            ]
        ),
        
        .target(
            name: "FeatureMarketplace",
            dependencies: [
                "AppCore",
                "DataLayer",
                "DesignSystem"
            ]
        ),
        
        // Test targets
        .testTarget(
            name: "AppCoreTests",
            dependencies: ["AppCore"]
        ),
        .testTarget(
            name: "DataLayerTests",
            dependencies: ["DataLayer"]
        ),
        .testTarget(
            name: "GoBridgeTests",
            dependencies: ["GoBridge"]
        )
    ]
) 