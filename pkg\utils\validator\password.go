/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package validator

import (
	"strings"
	"unicode"
)

// PasswordStrength represents the strength level of a password
type PasswordStrength int

const (
	// PasswordVeryWeak indicates a very weak password
	PasswordVeryWeak PasswordStrength = iota
	// PasswordWeak indicates a weak password
	PasswordWeak
	// PasswordFair indicates a fair password
	PasswordFair
	// PasswordGood indicates a good password
	PasswordGood
	// PasswordStrong indicates a strong password
	PasswordStrong
)

// String returns the string representation of password strength
func (ps PasswordStrength) String() string {
	switch ps {
	case PasswordVeryWeak:
		return "Very Weak"
	case PasswordWeak:
		return "Weak"
	case PasswordFair:
		return "Fair"
	case PasswordGood:
		return "Good"
	case PasswordStrong:
		return "Strong"
	default:
		return "Unknown"
	}
}

// PasswordPolicy defines the requirements for password validation
type PasswordPolicy struct {
	MinLength      int  // Minimum password length
	MaxLength      int  // Maximum password length (0 means no limit)
	RequireUpper   bool // Require at least one uppercase letter
	RequireLower   bool // Require at least one lowercase letter
	RequireDigit   bool // Require at least one digit
	RequireSpecial bool // Require at least one special character
	MinUpper       int  // Minimum number of uppercase letters
	MinLower       int  // Minimum number of lowercase letters
	MinDigits      int  // Minimum number of digits
	MinSpecial     int  // Minimum number of special characters
}

// DefaultPasswordPolicy returns a commonly used password policy
func DefaultPasswordPolicy() PasswordPolicy {
	return PasswordPolicy{
		MinLength:      8,
		MaxLength:      128,
		RequireUpper:   true,
		RequireLower:   true,
		RequireDigit:   true,
		RequireSpecial: true,
		MinUpper:       1,
		MinLower:       1,
		MinDigits:      1,
		MinSpecial:     1,
	}
}

// StrictPasswordPolicy returns a strict password policy for high-security applications
func StrictPasswordPolicy() PasswordPolicy {
	return PasswordPolicy{
		MinLength:      12,
		MaxLength:      128,
		RequireUpper:   true,
		RequireLower:   true,
		RequireDigit:   true,
		RequireSpecial: true,
		MinUpper:       2,
		MinLower:       2,
		MinDigits:      2,
		MinSpecial:     2,
	}
}

// RelaxedPasswordPolicy returns a relaxed password policy for user-friendly applications
func RelaxedPasswordPolicy() PasswordPolicy {
	return PasswordPolicy{
		MinLength:      6,
		MaxLength:      0, // No max length limit
		RequireUpper:   false,
		RequireLower:   false,
		RequireDigit:   false,
		RequireSpecial: false,
		MinUpper:       0,
		MinLower:       0,
		MinDigits:      0,
		MinSpecial:     0,
	}
}

// ValidatePassword validates a password against the given policy.
// Returns true if the password meets all requirements, false otherwise.
//
// Example:
//
//	policy := validator.DefaultPasswordPolicy()
//	isValid := validator.ValidatePassword("MyPassword123!", policy)  // returns true
//	isValid = validator.ValidatePassword("weak", policy)             // returns false
func ValidatePassword(password string, policy PasswordPolicy) bool {
	errors := CheckPassword(password, policy)
	return len(errors) == 0
}

// CheckPassword checks a password against the given policy and returns a list of violations.
// Returns an empty slice if the password meets all requirements.
//
// Example:
//
//	policy := validator.DefaultPasswordPolicy()
//	errors := validator.CheckPassword("weak", policy)
//	// errors might contain: ["Password must be at least 8 characters long", "Password must contain at least 1 uppercase letter", ...]
func CheckPassword(password string, policy PasswordPolicy) []string {
	var errors []string

	// Check length requirements
	if len(password) < policy.MinLength {
		errors = append(errors, "Password must be at least "+string(rune(policy.MinLength+'0'))+" characters long")
	}

	if policy.MaxLength > 0 && len(password) > policy.MaxLength {
		errors = append(errors, "Password must be at most "+string(rune(policy.MaxLength+'0'))+" characters long")
	}

	// Count character types
	var upperCount, lowerCount, digitCount, specialCount int

	for _, r := range password {
		switch {
		case unicode.IsUpper(r):
			upperCount++
		case unicode.IsLower(r):
			lowerCount++
		case unicode.IsDigit(r):
			digitCount++
		case isSpecialChar(r):
			specialCount++
		}
	}

	// Check character type requirements
	if policy.RequireUpper && upperCount == 0 {
		errors = append(errors, "Password must contain at least 1 uppercase letter")
	}

	if policy.RequireLower && lowerCount == 0 {
		errors = append(errors, "Password must contain at least 1 lowercase letter")
	}

	if policy.RequireDigit && digitCount == 0 {
		errors = append(errors, "Password must contain at least 1 digit")
	}

	if policy.RequireSpecial && specialCount == 0 {
		errors = append(errors, "Password must contain at least 1 special character")
	}

	// Check minimum counts
	if upperCount < policy.MinUpper {
		errors = append(errors, "Password must contain at least "+string(rune(policy.MinUpper+'0'))+" uppercase letters")
	}

	if lowerCount < policy.MinLower {
		errors = append(errors, "Password must contain at least "+string(rune(policy.MinLower+'0'))+" lowercase letters")
	}

	if digitCount < policy.MinDigits {
		errors = append(errors, "Password must contain at least "+string(rune(policy.MinDigits+'0'))+" digits")
	}

	if specialCount < policy.MinSpecial {
		errors = append(errors, "Password must contain at least "+string(rune(policy.MinSpecial+'0'))+" special characters")
	}

	return errors
}

// CheckPasswordStrength evaluates the strength of a password and returns a strength level.
// This provides a more nuanced evaluation than simple policy validation.
//
// Example:
//
//	strength := validator.CheckPasswordStrength("MyPassword123!")  // returns PasswordStrong
//	strength = validator.CheckPasswordStrength("weak")             // returns PasswordVeryWeak
func CheckPasswordStrength(password string) PasswordStrength {
	score := 0

	// Length scoring
	length := len(password)
	switch {
	case length >= 12:
		score += 3
	case length >= 8:
		score += 2
	case length >= 6:
		score += 1
	}

	// Character diversity scoring
	var hasUpper, hasLower, hasDigit, hasSpecial bool
	var upperCount, lowerCount, digitCount, specialCount int

	for _, r := range password {
		switch {
		case unicode.IsUpper(r):
			hasUpper = true
			upperCount++
		case unicode.IsLower(r):
			hasLower = true
			lowerCount++
		case unicode.IsDigit(r):
			hasDigit = true
			digitCount++
		case isSpecialChar(r):
			hasSpecial = true
			specialCount++
		}
	}

	// Base character type scoring
	if hasUpper {
		score += 1
	}
	if hasLower {
		score += 1
	}
	if hasDigit {
		score += 1
	}
	if hasSpecial {
		score += 2 // Special characters get extra weight
	}

	// Bonus for multiple characters of each type
	if upperCount >= 2 {
		score += 1
	}
	if lowerCount >= 2 {
		score += 1
	}
	if digitCount >= 2 {
		score += 1
	}
	if specialCount >= 2 {
		score += 1
	}

	// Check for common patterns and penalize
	if hasCommonPatterns(password) {
		score -= 2
	}

	// Check if it's a common weak password
	if isCommonPassword(password) {
		score -= 3
	}

	// Convert score to strength level
	switch {
	case score >= 10:
		return PasswordStrong
	case score >= 7:
		return PasswordGood
	case score >= 5:
		return PasswordFair
	case score >= 3:
		return PasswordWeak
	default:
		return PasswordVeryWeak
	}
}

// IsPasswordSecure is a convenient function that checks if a password is secure
// using the default password policy.
//
// Example:
//
//	isSecure := validator.IsPasswordSecure("MyPassword123!")  // returns true
//	isSecure = validator.IsPasswordSecure("weak")            // returns false
func IsPasswordSecure(password string) bool {
	return ValidatePassword(password, DefaultPasswordPolicy())
}

// IsPasswordStrong checks if a password has at least "Good" strength level.
//
// Example:
//
//	isStrong := validator.IsPasswordStrong("MyPassword123!")  // returns true
//	isStrong = validator.IsPasswordStrong("simple123")       // returns false
func IsPasswordStrong(password string) bool {
	strength := CheckPasswordStrength(password)
	return strength >= PasswordGood
}

// isSpecialChar checks if a rune is a special character
func isSpecialChar(r rune) bool {
	// Common special characters for passwords
	specialChars := "!@#$%^&*()_+-=[]{}|;:,.<>?"
	return strings.ContainsRune(specialChars, r)
}

// hasCommonPatterns checks for common weak patterns in passwords
func hasCommonPatterns(password string) bool {
	lower := strings.ToLower(password)

	// Check for sequential characters
	sequences := []string{
		"123456", "654321", "abcdef", "fedcba", "qwerty", "asdfgh",
		"zxcvbn", "098765", "567890", "ghijkl", "mnopqr", "stuvwx",
	}

	for _, seq := range sequences {
		if strings.Contains(lower, seq) {
			return true
		}
	}

	// Check for repeated characters (more than 2 in a row)
	for i := 0; i < len(password)-2; i++ {
		if password[i] == password[i+1] && password[i+1] == password[i+2] {
			return true
		}
	}

	return false
}

// isCommonPassword checks if the password is in a list of commonly used passwords
func isCommonPassword(password string) bool {
	// List of common weak passwords
	commonPasswords := []string{
		"password", "123456", "password123", "admin", "qwerty",
		"letmein", "welcome", "monkey", "1234567890", "abc123",
		"111111", "123123", "password1", "1234", "12345",
		"dragon", "master", "123456789", "login", "pass",
		"football", "iloveyou", "admin123", "welcome123", "sunshine",
		"princess", "azerty", "trustno1", "000000",
	}

	lower := strings.ToLower(password)
	for _, common := range commonPasswords {
		if lower == common {
			return true
		}
	}

	return false
}
