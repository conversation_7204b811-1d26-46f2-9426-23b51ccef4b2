/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package model

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewAuditLogEntry(t *testing.T) {
	actorID := "actor123"
	actorEmail := "<EMAIL>"
	actorIP := "***********"

	entry := NewAuditLogEntry(actorID, actorEmail, actorIP)

	assert.NotEmpty(t, entry.ID)
	assert.NotZero(t, entry.Timestamp)
	assert.Equal(t, "admin-bff-service", entry.ServiceName)
	assert.Equal(t, actorID, entry.ActorID)
	assert.Equal(t, actorEmail, entry.ActorEmail)
	assert.Equal(t, actorIP, entry.ActorIP)
	assert.True(t, entry.Success)
	assert.NotNil(t, entry.Metadata)
	assert.NotNil(t, entry.Tags)
	assert.Len(t, entry.Tags, 0)
	assert.True(t, time.Since(entry.Timestamp) < time.Second)
}

func TestAuditLogEntry_SetTraceID(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	traceID := "trace-123"

	entry.SetTraceID(traceID)

	assert.Equal(t, traceID, entry.TraceID)
}

func TestAuditLogEntry_SetActorRoles(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	roles := []string{"admin", "user_manager"}

	entry.SetActorRoles(roles)

	assert.Equal(t, roles, entry.ActorRoles)
}

func TestAuditLogEntry_SetUserAgent(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"

	entry.SetUserAgent(userAgent)

	assert.Equal(t, userAgent, entry.UserAgent)
}

func TestAuditLogEntry_SetResource(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	resourceType := ResourceUser
	resourceID := "user123"
	action := ActionUserCreate

	entry.SetResource(resourceType, resourceID, action)

	assert.Equal(t, resourceType, entry.ResourceType)
	assert.Equal(t, resourceID, entry.ResourceID)
	assert.Equal(t, action, entry.Action)
}

func TestAuditLogEntry_SetRequest(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	method := "POST"
	path := "/api/users"
	body := map[string]interface{}{
		"email": "<EMAIL>",
		"name":  "Test User",
	}

	entry.SetRequest(method, path, body)

	assert.Equal(t, method, entry.RequestMethod)
	assert.Equal(t, path, entry.RequestPath)
	assert.Equal(t, body, entry.RequestBody)
}

func TestAuditLogEntry_SetResponse(t *testing.T) {
	tests := []struct {
		name     string
		status   int
		message  string
		success  bool
		expected bool
	}{
		{"successful response with message", 200, "User created", true, true},
		{"successful response without message", 201, "", true, true},
		{"failed response with message", 400, "Bad request", false, false},
		{"failed response without message", 500, "", false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entry := NewAuditLogEntry("actor", "email", "ip")

			entry.SetResponse(tt.status, tt.message, tt.success)

			assert.Equal(t, tt.status, entry.ResponseStatus)
			assert.Equal(t, tt.expected, entry.Success)

			if tt.message != "" {
				assert.Equal(t, tt.message, entry.Metadata["response_message"])
			} else {
				assert.NotContains(t, entry.Metadata, "response_message")
			}
		})
	}
}

func TestAuditLogEntry_SetResponseTime(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	responseTime := int64(150)

	entry.SetResponseTime(responseTime)

	assert.Equal(t, responseTime, entry.ResponseTime)
}

func TestAuditLogEntry_AddMetadata(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")

	// Test adding to existing metadata
	entry.AddMetadata("key1", "value1")
	entry.AddMetadata("key2", 123)
	entry.AddMetadata("key3", true)

	assert.Equal(t, "value1", entry.Metadata["key1"])
	assert.Equal(t, 123, entry.Metadata["key2"])
	assert.Equal(t, true, entry.Metadata["key3"])

	// Test with nil metadata
	entry.Metadata = nil
	entry.AddMetadata("new_key", "new_value")
	assert.NotNil(t, entry.Metadata)
	assert.Equal(t, "new_value", entry.Metadata["new_key"])
}

func TestAuditLogEntry_AddTag(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")

	entry.AddTag("security")
	entry.AddTag("admin_action")

	assert.Equal(t, []string{"security", "admin_action"}, entry.Tags)
}

func TestAuditLogEntry_ToJSON(t *testing.T) {
	entry := NewAuditLogEntry("actor123", "<EMAIL>", "***********")
	entry.SetResource(ResourceUser, "user123", ActionUserCreate)
	entry.SetResponse(201, "User created", true)
	entry.AddMetadata("test_key", "test_value")
	entry.AddTag("test_tag")

	jsonData, err := entry.ToJSON()

	require.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	// Verify it's valid JSON by unmarshaling
	var unmarshaled AuditLogEntry
	err = json.Unmarshal(jsonData, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, entry.ActorID, unmarshaled.ActorID)
	assert.Equal(t, entry.Action, unmarshaled.Action)
	assert.Equal(t, entry.ResourceType, unmarshaled.ResourceType)
}

func TestAuditLogEntry_Validate(t *testing.T) {
	tests := []struct {
		name        string
		entry       *AuditLogEntry
		expectError bool
		expectedErr error
	}{
		{
			name:        "valid entry",
			entry:       &AuditLogEntry{ActorID: "actor123", Action: "login"},
			expectError: false,
		},
		{
			name:        "missing actor ID",
			entry:       &AuditLogEntry{ActorID: "", Action: "login"},
			expectError: true,
			expectedErr: ErrAuditLogMissingActor,
		},
		{
			name:        "missing action",
			entry:       &AuditLogEntry{ActorID: "actor123", Action: ""},
			expectError: true,
			expectedErr: ErrAuditLogMissingAction,
		},
		{
			name:        "missing both",
			entry:       &AuditLogEntry{ActorID: "", Action: ""},
			expectError: true,
			expectedErr: ErrAuditLogMissingActor, // First error encountered
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.entry.Validate()

			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAuditLogEntry_GetSeverity(t *testing.T) {
	tests := []struct {
		name     string
		action   string
		success  bool
		expected string
	}{
		{"failed action returns ERROR", ActionLogin, false, "ERROR"},
		{"high severity action - suspend", ActionUserSuspend, true, "HIGH"},
		{"high severity action - delete", ActionUserDelete, true, "HIGH"},
		{"high severity action - reject", ActionContentReject, true, "HIGH"},
		{"medium severity action - create", ActionUserCreate, true, "MEDIUM"},
		{"medium severity action - restore", ActionUserRestore, true, "MEDIUM"},
		{"medium severity action - approve", ActionContentApprove, true, "MEDIUM"},
		{"low severity action - login", ActionLogin, true, "LOW"},
		{"low severity action - logout", ActionLogout, true, "LOW"},
		{"unknown action defaults to medium", "unknown_action", true, "MEDIUM"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entry := &AuditLogEntry{
				Action:  tt.action,
				Success: tt.success,
			}

			severity := entry.GetSeverity()

			assert.Equal(t, tt.expected, severity)
		})
	}
}

func TestResourceConstants(t *testing.T) {
	assert.Equal(t, "user", ResourceUser)
	assert.Equal(t, "content", ResourceContent)
	assert.Equal(t, "order", ResourceOrder)
	assert.Equal(t, "session", ResourceSession)
	assert.Equal(t, "system", ResourceSystem)
}

func TestActionConstants(t *testing.T) {
	assert.Equal(t, "login", ActionLogin)
	assert.Equal(t, "logout", ActionLogout)
	assert.Equal(t, "user_create", ActionUserCreate)
	assert.Equal(t, "user_update", ActionUserUpdate)
	assert.Equal(t, "user_suspend", ActionUserSuspend)
	assert.Equal(t, "user_restore", ActionUserRestore)
	assert.Equal(t, "user_delete", ActionUserDelete)
	assert.Equal(t, "content_approve", ActionContentApprove)
	assert.Equal(t, "content_reject", ActionContentReject)
	assert.Equal(t, "order_cancel", ActionOrderCancel)
	assert.Equal(t, "order_refund", ActionOrderRefund)
}

func TestDefaultConstants(t *testing.T) {
	assert.Equal(t, 50, DefaultAuditLogPageSize)
	assert.Equal(t, 1000, MaxAuditLogPageSize)
	assert.Equal(t, "timestamp", DefaultAuditLogSortBy)
}

func TestErrorConstants(t *testing.T) {
	assert.Equal(t, "audit log entry missing actor ID", ErrAuditLogMissingActor.Error())
	assert.Equal(t, "audit log entry missing action", ErrAuditLogMissingAction.Error())
}

func TestAuditLogFilter_Struct(t *testing.T) {
	startTime := time.Now().Add(-24 * time.Hour)
	endTime := time.Now()
	success := true

	filter := AuditLogFilter{
		ActorID:      "actor123",
		ActorEmail:   "<EMAIL>",
		Action:       ActionLogin,
		ResourceType: ResourceUser,
		ResourceID:   "user123",
		Success:      &success,
		StartTime:    &startTime,
		EndTime:      &endTime,
		Page:         1,
		PageSize:     50,
		SortBy:       "timestamp",
		SortOrder:    "desc",
	}

	assert.Equal(t, "actor123", filter.ActorID)
	assert.Equal(t, "<EMAIL>", filter.ActorEmail)
	assert.Equal(t, ActionLogin, filter.Action)
	assert.Equal(t, ResourceUser, filter.ResourceType)
	assert.Equal(t, "user123", filter.ResourceID)
	assert.Equal(t, true, *filter.Success)
	assert.Equal(t, startTime, *filter.StartTime)
	assert.Equal(t, endTime, *filter.EndTime)
	assert.Equal(t, 1, filter.Page)
	assert.Equal(t, 50, filter.PageSize)
	assert.Equal(t, "timestamp", filter.SortBy)
	assert.Equal(t, "desc", filter.SortOrder)
}

func TestAuditLogEntry_FullWorkflow(t *testing.T) {
	// Test a complete audit log entry creation workflow
	entry := NewAuditLogEntry("admin123", "<EMAIL>", "*************")

	// Set additional fields
	entry.SetTraceID("trace-456")
	entry.SetActorRoles([]string{"admin", "user_manager"})
	entry.SetUserAgent("AdminPanel/1.0")
	entry.SetResource(ResourceUser, "user789", ActionUserSuspend)
	entry.SetRequest("POST", "/api/users/789/suspend", map[string]interface{}{
		"reason": "Policy violation",
	})
	entry.SetResponse(200, "User suspended successfully", true)
	entry.SetResponseTime(250)
	entry.AddMetadata("suspension_duration", "30d")
	entry.AddTag("user_management")
	entry.AddTag("policy_enforcement")

	// Validate the entry
	err := entry.Validate()
	assert.NoError(t, err)

	// Check all fields
	assert.NotEmpty(t, entry.ID)
	assert.Equal(t, "admin123", entry.ActorID)
	assert.Equal(t, "<EMAIL>", entry.ActorEmail)
	assert.Equal(t, "*************", entry.ActorIP)
	assert.Equal(t, "trace-456", entry.TraceID)
	assert.Equal(t, []string{"admin", "user_manager"}, entry.ActorRoles)
	assert.Equal(t, "AdminPanel/1.0", entry.UserAgent)
	assert.Equal(t, ResourceUser, entry.ResourceType)
	assert.Equal(t, "user789", entry.ResourceID)
	assert.Equal(t, ActionUserSuspend, entry.Action)
	assert.Equal(t, "POST", entry.RequestMethod)
	assert.Equal(t, "/api/users/789/suspend", entry.RequestPath)
	assert.Equal(t, "Policy violation", entry.RequestBody["reason"])
	assert.Equal(t, 200, entry.ResponseStatus)
	assert.Equal(t, int64(250), entry.ResponseTime)
	assert.True(t, entry.Success)
	assert.Equal(t, "User suspended successfully", entry.Metadata["response_message"])
	assert.Equal(t, "30d", entry.Metadata["suspension_duration"])
	assert.Equal(t, []string{"user_management", "policy_enforcement"}, entry.Tags)

	// Check severity
	assert.Equal(t, "HIGH", entry.GetSeverity())

	// Test JSON serialization
	jsonData, err := entry.ToJSON()
	assert.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	// Verify JSON can be unmarshaled
	var restored AuditLogEntry
	err = json.Unmarshal(jsonData, &restored)
	assert.NoError(t, err)
	assert.Equal(t, entry.ID, restored.ID)
	assert.Equal(t, entry.ActorID, restored.ActorID)
	assert.Equal(t, entry.Action, restored.Action)
}

func TestAuditLogEntry_EdgeCases(t *testing.T) {
	t.Run("nil metadata handling", func(t *testing.T) {
		entry := &AuditLogEntry{}
		entry.AddMetadata("test", "value")
		assert.NotNil(t, entry.Metadata)
		assert.Equal(t, "value", entry.Metadata["test"])
	})

	t.Run("empty values", func(t *testing.T) {
		entry := NewAuditLogEntry("", "", "")
		assert.Equal(t, "", entry.ActorID)
		assert.Equal(t, "", entry.ActorEmail)
		assert.Equal(t, "", entry.ActorIP)

		err := entry.Validate()
		assert.Error(t, err)
		assert.Equal(t, ErrAuditLogMissingActor, err)
	})

	t.Run("large metadata values", func(t *testing.T) {
		entry := NewAuditLogEntry("actor", "email", "ip")
		largeData := make(map[string]interface{})
		for i := 0; i < 1000; i++ {
			largeData[string(rune(i))] = i
		}
		entry.AddMetadata("large_data", largeData)

		jsonData, err := entry.ToJSON()
		assert.NoError(t, err)
		assert.NotEmpty(t, jsonData)
	})
}

func TestAuditLogEntry_ConcurrentAccess(t *testing.T) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	done := make(chan bool, 100)

	// Test concurrent metadata addition
	for i := 0; i < 100; i++ {
		go func(i int) {
			defer func() { done <- true }()
			entry.AddMetadata(string(rune(i)), i)
			entry.AddTag(string(rune(i)))
		}(i)
	}

	// Wait for all goroutines
	for i := 0; i < 100; i++ {
		<-done
	}

	// Verify no data corruption
	assert.GreaterOrEqual(t, len(entry.Metadata), 1)
	assert.GreaterOrEqual(t, len(entry.Tags), 1)
}

// Benchmark tests
func BenchmarkNewAuditLogEntry(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		NewAuditLogEntry("actor", "email", "ip")
	}
}

func BenchmarkAuditLogEntry_ToJSON(b *testing.B) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	entry.SetResource(ResourceUser, "user123", ActionUserCreate)
	entry.AddMetadata("test", "value")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		entry.ToJSON()
	}
}

func BenchmarkAuditLogEntry_Validate(b *testing.B) {
	entry := NewAuditLogEntry("actor", "email", "ip")
	entry.SetResource(ResourceUser, "user123", ActionUserCreate)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		entry.Validate()
	}
}
