好的，遵照您的指示。我将为您生成一份专门针对 **`cloud-sync-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`cloud-sync-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**同步协议的服务器端实现、与`core/datasync`的对应关系、版本向量冲突检测、数据块去重，以及作为“可信盲人”的安全模型**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `cloud-sync-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `cloud-sync-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + CRDT思想 (版本向量) + 代理存储

## 1. 概述

`cloud-sync-service` 是CINA.CLUB平台隐私保护架构的“**安全保险库**”和“**同步协调器**”。它为端到端加密(E2EE)数据提供云端备份和跨设备同步的能力。其核心挑战在于：
1.  **服务器端零知识 (Server-Side Zero-Knowledge)**: 服务本身**绝不能**访问或解密用户数据内容，它只处理元数据和不透明的加密数据块。
2.  **并发控制与冲突检测**: 必须能精确地处理来自多个设备对同一数据条目的并发修改，并可靠地检测出冲突。
3.  **存储与带宽效率**: 对于大文件，必须通过数据分块和去重来节省存储成本和传输带宽。
4.  **协议的健壮性**: 客户端与服务器之间的同步协议必须健壮，能处理各种网络异常和乱序操作。
5.  **元数据一致性**: 对元数据（如版本向量、Chunk列表）的更新必须是原子性的。

本架构设计通过采用**整洁架构**，并在领域层实现基于**版本向量(Version Vector)**的并发控制逻辑，同时将实际文件存储代理给`file-storage-service`，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (同步流程协调)

```mermaid
graph TD
    subgraph "客户端 (Client App)"
        A[core/datasync Library]
    end

    subgraph "CloudSyncService (This Service)"
        B[API Layer (gRPC/REST)<br/><em>adapter/transport</em>]
        C[SyncEngine<br/><em>application/service</em>]
        D[ConcurrencyControlService<br/><em>domain/service</em>]
        E[ChunkService<br/><em>domain/service</em>]
        F[Repository<br/><em>adapter/repository</em>]
    end

    subgraph "下游服务"
        G[file-storage-service]
        H[notification-dispatch-service]
    end

    A -- "1. POST /push (metadata & checksums)" --> B
    B -- "调用" --> C
    
    C -- "2. Use for concurrency check" --> D
    D -- "Compares VersionVectors" --> C
    C -- "3. Check for existing chunks" --> E
    
    E -- "4. Request upload URLs for new chunks" --> G
    G -- "Returns Presigned URLs" --> E
    E -- "Returns URLs to client" --> A
    
    A -- "5. Uploads chunks to Object Storage" --> G

    A -- "6. POST /finalize" --> B
    B -- "调用" --> C
    C -- "7. Atomically update metadata" --> F
    F -- "Persists to PostgreSQL" --> DB[(PostgreSQL)]

    C -- "8. Trigger notification" --> H
```

### 2.2 最终目录结构 (`services/cloud-sync-service/`)

```
cloud-sync-service/
├── cmd/server/
│   └── main.go                 # API服务启动入口
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   └── file_storage_client.go
│   │   ├── grpc/
│   │   │   └── handler.go      # gRPC Handler实现
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型
│   │       └── repo.go         # 实现了所有仓储接口的struct
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── sync_engine.go  # ✨ 同步引擎核心, 业务流程编排 ✨
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/            # ✨ 核心领域逻辑服务 ✨
│           ├── concurrency_control_service.go # 版本向量比较与冲突检测
│           └── chunk_service.go # 数据块去重与管理
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Sync Protocol Rules)

这是封装同步协议核心、无状态规则的地方。

*   `domain/model/`: 使用`/core/models`中与同步相关的`struct`，如`SyncItem`, `Chunk`, `VersionVector`。
*   **`domain/service/concurrency_control_service.go`**:
    *   **`ConcurrencyControlService`**: 一个无状态的领域服务，封装了版本向量的比较逻辑。
    *   **`CheckConflict(serverVector, clientVector) (ComparisonResult, error)`**:
        *   这个纯函数是并发控制的核心。
        *   它接收服务端存储的权威版本向量和客户端提交的版本向量。
        *   **实现逻辑 (与`core/datasync`中的逻辑一一对应)**:
            *   如果`clientVector`是`serverVector`的直接后代（即只更新了自己设备的版本号），返回`NoConflict_FastForward`。
            *   如果`clientVector`是`serverVector`的祖先或相等，说明客户端数据已过时，返回`NoConflict_Stale`。
            *   如果两者互不为祖先，说明发生了并发修改，返回`Conflict`。
*   **`domain/service/chunk_service.go`**:
    *   **`ChunkService`**: 封装数据块去重和URL请求逻辑。
    *   **`IdentifyMissingChunks(repo, checksums)`**:
        *   接收一个checksum列表。
        *   调用仓储接口，批量查询哪些checksum在`chunks`表中已存在。
        *   返回一个缺失的checksum列表。
    *   **`RequestUploadURLs(fileStorageClient, missingChunks)`**:
        *   为每个缺失的chunk，调用`file-storage-service`客户端请求一个预签名上传URL。

### 3.2 `application/` - 应用层 (The Sync Engine)

*   `application/port/`: 定义`Repository`, `SyncEngineService`等接口。
*   **`application/service/sync_engine.go`**: **这是所有API请求的业务流程编排者**。
    *   `SyncEngine` struct: 注入`Repository`, `ConcurrencyControlService`, `ChunkService`, `fileStorageClient`等依赖。
    *   **`HandlePushRequest(ctx, pushRequest)`**:
        1.  从`pushRequest`中提取所有`SyncItem`的变更。
        2.  **循环处理每个item**:
            a. 调用`repo.GetItemWithLock(ctx, itemID)`，**使用悲观锁 (`FOR UPDATE`)** 获取服务端的`SyncItem`元数据，防止并发`Push`。
            b. 调用`concurrencyService.CheckConflict()`进行版本向量比较。
            c. **如果冲突**: 立即为该item构建一个冲突响应，并继续处理下一个item。
            d. **如果不冲突**: 调用`chunkService.IdentifyMissingChunks()`找出需要上传的块。
        3.  汇总所有需要上传的块，调用`chunkService.RequestUploadURLs()`获取URL。
        4.  将结果（成功或冲突信息、需要上传的URL列表）返回给gRPC handler。
    *   **`HandleFinalizeRequest(ctx, finalizeRequest)`**:
        1.  **开启数据库事务（工作单元）**。
        2.  **循环处理每个item**:
            a. 再次调用`repo.GetItemWithLock()`锁定记录，并进行**最终的并发检查**。
            b. 调用`repo.UpdateItemMetadata()`，原子性地更新该item的`version_vector`和其关联的`item_chunks`列表。
            c. 调用`repo.IncrementChunkRefCounts()`，为所有新引用的chunk增加引用计数。
        3.  **提交事务**。
        4.  事务成功后，**异步地**调用`notification-dispatch-service`，向用户的其他设备发送静默推送。
    *   **`HandlePullRequest(ctx, pullRequest)`**:
        1.  调用`repo.GetChangesSince(...)`，根据客户端提供的版本向量快照，找出所有需要同步给客户端的变更（新建、更新、删除）。
        2.  对于需要下载的变更，批量获取其所有chunk的下载URL（通过`file-storage-service`）。
        3.  将完整的变更列表和下载URL返回。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库选型**: **PostgreSQL**。其强大的事务和JSONB类型非常适合存储元数据。
    *   `repo.go`: 实现`port.Repository`接口。
        *   **`GetItemWithLock`**: 必须使用`SELECT ... FOR UPDATE`来实现悲观锁，确保在`push`到`finalize`的窗口期内，对同一条目的处理是串行的。
        *   **`UpdateItemMetadata`**: 必须在一个数据库事务中，同时更新`sync_items`表和`item_chunks`关联表。
*   **`adapter/client/`**:
    *   `file_storage_client.go`: 封装对`file-storage-service`的gRPC调用。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现`cloud_sync_service.proto`中定义的gRPC服务，将请求转发给`application.SyncEngine`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`cloud-sync-service`：
1.  **领域逻辑与流程编排分离**:
    *   **`domain/service`**: 封装了无状态、可复用的、纯粹的协议规则（如版本向量比较）。
    *   **`application/service`**: 作为`SyncEngine`，负责编排复杂的、有状态的、涉及多步骤I/O的业务流程。
2.  **严格的并发控制**: 在`Push`和`Finalize`流程中，明确使用数据库的**悲观锁**来保证元数据更新的原子性和一致性，这是保证同步协议正确性的关键。
3.  **职责清晰的依赖**: 本服务作为“可信的盲人”，严格遵守其边界，将文件存储的复杂性完全委托给`file-storage-service`，自己只专注于元数据和同步协调。
4.  **客户端协议对应**: 本服务的设计与`core/datasync`的设计是**镜像关系**，共同构成了一个完整的、端到端的同步解决方案。

这种架构确保了`cloud-sync-service`在保证用户数据端到端加密和服务器零知识的前提下，能够提供一个**高效、可靠、且能正确处理并发**的数据同步与备份后端。