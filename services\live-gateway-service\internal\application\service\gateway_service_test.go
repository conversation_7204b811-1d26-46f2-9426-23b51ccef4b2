package service

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

// Mock implementations
type mockCacheRepository struct {
	mock.Mock
}

func (m *mockCacheRepository) StoreStreamMapping(ctx context.Context, mapping *model.StreamMapping) error {
	args := m.Called(ctx, mapping)
	return args.Error(0)
}

func (m *mockCacheRepository) GetStreamMapping(ctx context.Context, streamKey string) (*model.StreamMapping, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamMapping), args.Error(1)
}

func (m *mockCacheRepository) DeleteStreamMapping(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) StoreNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNodeLoad(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) DeleteNodeLoad(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) ListNodeLoads(ctx context.Context) (map[string]*model.NodeLoad, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *mockCacheRepository) StoreStreamStats(ctx context.Context, streamKey string, stats *model.StreamStats) error {
	args := m.Called(ctx, streamKey, stats)
	return args.Error(0)
}

func (m *mockCacheRepository) GetStreamStats(ctx context.Context, streamKey string) (*model.StreamStats, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamStats), args.Error(1)
}

func (m *mockCacheRepository) DeleteStreamStats(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// Node operations
func (m *mockCacheRepository) StoreNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNode(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) DeleteNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error {
	args := m.Called(ctx, nodeID, status)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error {
	args := m.Called(ctx, nodeID, stats)
	return args.Error(0)
}

func (m *mockCacheRepository) Transaction(ctx context.Context, keys []string, fn func(tx port.Transaction) error) error {
	args := m.Called(ctx, keys, fn)
	return args.Error(0)
}

type mockMediaServerAdapter struct {
	mock.Mock
}

func (m *mockMediaServerAdapter) GeneratePushURL(ctx context.Context, req *port.PushURLRequest) (*model.PushURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PushURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GeneratePlayURLs(ctx context.Context, req *port.RequestPlayURLsRequest) ([]*model.PlayURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.PlayURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GetStreamInfo(ctx context.Context, streamKey string) (*port.StreamInfo, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.StreamInfo), args.Error(1)
}

func (m *mockMediaServerAdapter) KickStream(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) GetServerStats(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockMediaServerAdapter) GetType() model.MediaServerType {
	args := m.Called()
	return args.Get(0).(model.MediaServerType)
}

func (m *mockMediaServerAdapter) HealthCheck(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) ParseWebhookRequest(req *port.WebhookParseRequest) (*port.WebhookRequest, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.WebhookRequest), args.Error(1)
}

func (m *mockMediaServerAdapter) RespondWebhookFailure(req *port.WebhookErrorRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) RespondWebhookSuccess(req *port.WebhookResponseRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) ValidateWebhookRequest(req *port.WebhookValidateRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

type mockLoadBalancer struct {
	mock.Mock
}

func (m *mockLoadBalancer) RegisterNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) UpdateNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) GetNodeByID(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) GetAvailableNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) SelectNode(ctx context.Context, eligibleNodes []*model.MediaNode) (*model.MediaNode, error) {
	args := m.Called(ctx, eligibleNodes)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) SelectNodeByRequest(ctx context.Context, req *port.NodeSelectionRequest) (*model.MediaNode, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) UpdateNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *mockLoadBalancer) AddNode(ctx context.Context, node *model.MediaServerNode) error {
	// Convert MediaServerNode to MediaNode
	mediaNode := node.ToMediaNode()
	args := m.Called(ctx, mediaNode)
	return args.Error(0)
}

func (m *mockLoadBalancer) RemoveNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

type mockLiveAPIClient struct {
	mock.Mock
}

func (m *mockLiveAPIClient) CheckPushAuth(ctx context.Context, req *port.AuthRequest) (*port.AuthResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.AuthResponse), args.Error(1)
}

func (m *mockLiveAPIClient) NotifyStreamPublished(ctx context.Context, event *port.StreamPublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyStreamUnpublished(ctx context.Context, event *port.StreamUnpublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyRecordingCompleted(ctx context.Context, event *port.RecordingCompletedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyStreamKicked(ctx context.Context, event *port.StreamKickedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) SubmitForModeration(ctx context.Context, event *port.ModerationSubmissionEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type mockEventPublisher struct {
	mock.Mock
}

func (m *mockEventPublisher) PublishEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishAlertEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type mockEventStore struct {
	mock.Mock
}

func (m *mockEventStore) QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error) {
	args := m.Called(ctx, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Event), args.Error(1)
}

func (m *mockEventStore) StoreEvent(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventStore) StoreBatch(ctx context.Context, events []*model.Event) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

// createTestService creates a new GatewayService with all mocks for testing.
func createTestService(config *GatewayConfig) (*GatewayService, *mockCacheRepository, *mockMediaServerAdapter, *mockLoadBalancer, *mockLiveAPIClient, *mockEventPublisher, *mockEventStore) {
	cache := new(mockCacheRepository)
	mediaAdapter := new(mockMediaServerAdapter)
	loadBalancer := new(mockLoadBalancer)
	liveAPIClient := new(mockLiveAPIClient)
	eventPublisher := new(mockEventPublisher)
	eventStore := new(mockEventStore)
	logger := logrus.New()

	service := NewGatewayService(
		cache,
		mediaAdapter,
		loadBalancer,
		liveAPIClient,
		eventPublisher,
		eventStore,
		config,
		logger,
	)

	return service.(*GatewayService), cache, mediaAdapter, loadBalancer, liveAPIClient, eventPublisher, eventStore
}

func TestNewGatewayService(t *testing.T) {
	config := &GatewayConfig{
		DefaultTTL:           time.Hour,
		MaxConcurrentStreams: 1000,
		LoadBalanceStrategy:  "round_robin",
		EnableMetrics:        true,
		WebhookTimeout:       time.Second * 5,
		AuthTimeout:          time.Second * 3,
	}

	service, _, _, _, _, _, _ := createTestService(config)
	assert.NotNil(t, service)
}

func TestGatewayService_RequestPushURL(t *testing.T) {
	service, cache, mediaAdapter, loadBalancer, _, _, _ := createTestService(nil)
	ctx := context.Background()
	roomID := uuid.New()
	userID := uuid.New()

	// Setup test data
	node := &model.MediaNode{
		ID:         "test-node",
		Address:    "test-host",
		Status:     "active",
		ServerType: model.MediaServerTypeSRS,
	}

	pushURL := &model.PushURL{
		URLString: "rtmp://test-host/live/test-stream",
		StreamKey: "test-stream",
		TTL:       time.Hour,
	}

	// Setup expectations
	loadBalancer.On("SelectNodeByRequest", mock.Anything, mock.AnythingOfType("*port.NodeSelectionRequest")).Return(node, nil)
	cache.On("StoreStreamMapping", mock.Anything, mock.Anything).Return(nil)
	mediaAdapter.On("GeneratePushURL", mock.Anything, mock.Anything).Return(pushURL, nil)

	// Test successful case
	req := &port.CreateStreamRequest{
		RoomID:    roomID,
		UserID:    userID,
		Protocol:  model.StreamProtocolRTMP,
		Quality:   model.StreamQualityHigh,
		ClientIP:  "127.0.0.1",
		UserAgent: "test-agent",
	}

	result, err := service.RequestPushURL(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, pushURL.URLString, result.URLString)
	assert.Equal(t, pushURL.StreamKey, result.StreamKey)
	assert.Equal(t, pushURL.TTL, result.TTL)

	// Test error cases
	loadBalancer.On("SelectNodeByRequest", mock.Anything, mock.AnythingOfType("*port.NodeSelectionRequest")).Return(nil, errors.New("node selection failed"))
	_, err = service.RequestPushURL(ctx, req)
	assert.Error(t, err)
}

func TestGatewayService_RequestPushURL_ValidationErrors(t *testing.T) {
	service, _, _, _, _, _, _ := createTestService(nil)
	ctx := context.Background()

	tests := []struct {
		name    string
		req     *port.CreateStreamRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "Empty room ID",
			req: &port.CreateStreamRequest{
				UserID:    uuid.New(),
				Protocol:  model.StreamProtocolRTMP,
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "room_id is required",
		},
		{
			name: "Empty user ID",
			req: &port.CreateStreamRequest{
				RoomID:    uuid.New(),
				Protocol:  model.StreamProtocolRTMP,
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "user_id is required",
		},
		{
			name: "Empty protocol",
			req: &port.CreateStreamRequest{
				RoomID:    uuid.New(),
				UserID:    uuid.New(),
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "protocol is required",
		},
		{
			name: "Invalid protocol",
			req: &port.CreateStreamRequest{
				RoomID:    uuid.New(),
				UserID:    uuid.New(),
				Protocol:  "invalid",
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "unsupported protocol",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.RequestPushURL(ctx, tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGatewayService_RequestPlayURLs(t *testing.T) {
	service, cache, mediaAdapter, _, _, _, _ := createTestService(nil)
	ctx := context.Background()
	roomID := uuid.New()
	userID := uuid.New()

	// Setup test data
	mapping := &model.StreamMapping{
		StreamKey:  "test-stream",
		RoomID:     roomID,
		UserID:     userID,
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}

	playURLs := []*model.PlayURL{
		{
			URLString: "http://test-host/live/test-stream.m3u8",
			Protocol:  model.PlayProtocolHLS,
			StreamKey: "test-stream",
			TTL:       time.Hour,
		},
	}

	// Setup expectations
	cache.On("GetStreamMapping", mock.Anything, "test-stream").Return(mapping, nil)
	mediaAdapter.On("GeneratePlayURLs", mock.Anything, mock.Anything).Return(playURLs, nil)

	// Test successful case
	req := &port.RequestPlayURLsRequest{
		StreamKey: "test-stream",
		Protocols: []model.PlayProtocol{model.PlayProtocolHLS},
		ClientIP:  "127.0.0.1",
		UserAgent: "test-agent",
	}

	result, err := service.RequestPlayURLs(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Len(t, result, 1)
	assert.Equal(t, playURLs[0].URLString, result[0].URLString)
}

func TestGatewayService_RequestPlayURLs_ValidationErrors(t *testing.T) {
	service, _, _, _, _, _, _ := createTestService(nil)
	ctx := context.Background()

	tests := []struct {
		name    string
		req     *port.RequestPlayURLsRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "Empty stream key",
			req: &port.RequestPlayURLsRequest{
				Protocols: []model.PlayProtocol{model.PlayProtocolHLS},
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "stream_key is required",
		},
		{
			name: "Empty protocols",
			req: &port.RequestPlayURLsRequest{
				StreamKey: "test-stream",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "at least one protocol is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.RequestPlayURLs(ctx, tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGatewayService_HandleWebhook(t *testing.T) {
	service, cache, _, _, liveAPIClient, _, _ := createTestService(nil)
	ctx := context.Background()
	roomID := uuid.New()
	streamKey := "test-stream"

	// Setup test data
	mapping := &model.StreamMapping{
		StreamKey:  streamKey,
		RoomID:     roomID,
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}

	authResp := &port.AuthResponse{
		Allowed: true,
	}

	// Setup expectations for publish event
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(mapping, nil)
	liveAPIClient.On("CheckPushAuth", mock.Anything, mock.Anything).Return(authResp, nil)
	liveAPIClient.On("NotifyStreamPublished", mock.Anything, mock.Anything).Return(nil)

	// Test publish event
	publishReq := &port.WebhookRequest{
		EventType:  model.WebhookEventTypePublish,
		StreamKey:  streamKey,
		AuthToken:  "test-token",
		ClientIP:   "127.0.0.1",
		UserAgent:  "test-agent",
		ServerNode: "test-node",
		Protocol:   model.StreamProtocolRTMP,
		Timestamp:  time.Now(),
	}

	resp, err := service.HandleWebhook(ctx, publishReq)
	require.NoError(t, err)
	require.NotNil(t, resp)
	assert.True(t, resp.Success)

	// Setup expectations for unpublish event
	cache.On("DeleteStreamMapping", mock.Anything, streamKey).Return(nil)
	liveAPIClient.On("NotifyStreamUnpublished", mock.Anything, mock.Anything).Return(nil)

	// Test unpublish event
	unpublishReq := &port.WebhookRequest{
		EventType:  model.WebhookEventTypeUnpublish,
		StreamKey:  streamKey,
		ServerNode: "test-node",
		Timestamp:  time.Now(),
	}

	resp, err = service.HandleWebhook(ctx, unpublishReq)
	require.NoError(t, err)
	require.NotNil(t, resp)
	assert.True(t, resp.Success)
}

func TestGatewayService_HandleWebhook_AuthFailure(t *testing.T) {
	service, cache, _, _, liveAPIClient, _, _ := createTestService(nil)
	ctx := context.Background()
	roomID := uuid.New()
	streamKey := "test-stream"

	// Setup test data
	mapping := &model.StreamMapping{
		StreamKey:  streamKey,
		RoomID:     roomID,
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}

	// Test case 1: Auth service returns error
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(mapping, nil)
	liveAPIClient.On("CheckPushAuth", mock.Anything, mock.Anything).Return(nil, errors.New("auth service error"))

	publishReq := &port.WebhookRequest{
		EventType:  model.WebhookEventTypePublish,
		StreamKey:  streamKey,
		AuthToken:  "test-token",
		ClientIP:   "127.0.0.1",
		UserAgent:  "test-agent",
		ServerNode: "test-node",
		Protocol:   model.StreamProtocolRTMP,
		Timestamp:  time.Now(),
	}

	resp, err := service.HandleWebhook(ctx, publishReq)
	require.NoError(t, err)
	require.NotNil(t, resp)
	assert.False(t, resp.Success)
	assert.Equal(t, "Authentication failed", resp.Message)

	// Test case 2: Auth service denies access
	authResp := &port.AuthResponse{
		Allowed: false,
		Reason:  "invalid token",
	}
	liveAPIClient.On("CheckPushAuth", mock.Anything, mock.Anything).Return(authResp, nil)

	resp, err = service.HandleWebhook(ctx, publishReq)
	require.NoError(t, err)
	require.NotNil(t, resp)
	assert.False(t, resp.Success)
	assert.Equal(t, "invalid token", resp.Message)

	// Test case 3: Auth fallback enabled
	config := &GatewayConfig{
		AuthTimeout:              time.Second * 3,
		AllowAuthFallbackOnError: true,
	}
	service, _, _, _, _, _, _ = createTestService(config)

	liveAPIClient.On("CheckPushAuth", mock.Anything, mock.Anything).Return(nil, errors.New("auth service error"))
	liveAPIClient.On("NotifyStreamPublished", mock.Anything, mock.Anything).Return(nil)

	resp, err = service.HandleWebhook(ctx, publishReq)
	require.NoError(t, err)
	require.NotNil(t, resp)
	assert.True(t, resp.Success)
}

func TestGatewayService_HandleWebhook_ExpiredStream(t *testing.T) {
	service, cache, mediaAdapter, _, liveAPIClient, _, _ := createTestService(nil)
	ctx := context.Background()
	streamKey := "test-stream"

	// Setup test data
	mapping := &model.StreamMapping{
		StreamKey:  streamKey,
		RoomID:     uuid.New(),
		UserID:     uuid.New(),
		ServerNode: "test-node",
		CreatedAt:  time.Now().Add(-2 * time.Hour),
		ExpiresAt:  time.Now().Add(-1 * time.Hour),
	}

	// Setup expectations
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(mapping, nil)
	mediaAdapter.On("ParseWebhookRequest", mock.Anything).Return(&port.WebhookRequest{
		StreamKey:  streamKey,
		EventType:  model.WebhookEventTypePublish,
		Timestamp:  time.Now(),
		AuthToken:  "test-token",
		ClientIP:   "127.0.0.1",
		UserAgent:  "test-agent",
		ServerNode: "test-node",
	}, nil)
	liveAPIClient.On("CheckPushAuth", mock.Anything, mock.Anything).Return(nil, nil)

	// Test expired stream
	req := &port.WebhookRequest{
		StreamKey:  streamKey,
		EventType:  model.WebhookEventTypePublish,
		Timestamp:  time.Now(),
		AuthToken:  "test-token",
		ClientIP:   "127.0.0.1",
		UserAgent:  "test-agent",
		ServerNode: "test-node",
	}

	resp, err := service.HandleWebhook(ctx, req)
	require.NoError(t, err)
	assert.False(t, resp.Success)
	assert.Contains(t, resp.Message, "stream has expired")
}

func TestGatewayService_HandleWebhook_UnknownEvent(t *testing.T) {
	service, cache, mediaAdapter, _, liveAPIClient, _, _ := createTestService(nil)
	ctx := context.Background()
	streamKey := "test-stream"

	// Setup test data
	mapping := &model.StreamMapping{
		StreamKey:  streamKey,
		RoomID:     uuid.New(),
		UserID:     uuid.New(),
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}

	// Setup expectations
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(mapping, nil)
	mediaAdapter.On("ParseWebhookRequest", mock.Anything).Return(&port.WebhookRequest{
		StreamKey:  streamKey,
		EventType:  "unknown",
		Timestamp:  time.Now(),
		AuthToken:  "test-token",
		ClientIP:   "127.0.0.1",
		UserAgent:  "test-agent",
		ServerNode: "test-node",
	}, nil)
	liveAPIClient.On("CheckPushAuth", mock.Anything, mock.Anything).Return(nil, nil)

	// Test unknown event
	req := &port.WebhookRequest{
		StreamKey:  streamKey,
		EventType:  "unknown",
		Timestamp:  time.Now(),
		AuthToken:  "test-token",
		ClientIP:   "127.0.0.1",
		UserAgent:  "test-agent",
		ServerNode: "test-node",
	}

	resp, err := service.HandleWebhook(ctx, req)
	require.NoError(t, err)
	assert.False(t, resp.Success)
	assert.Contains(t, resp.Message, "unknown event type")
}

func TestGatewayService_HandleWebhook_ValidationErrors(t *testing.T) {
	service, _, mediaAdapter, _, _, _, _ := createTestService(nil)
	ctx := context.Background()

	// Setup expectations
	mediaAdapter.On("ParseWebhookRequest", mock.Anything).Return(nil, errors.New("invalid request"))

	// Test cases
	tests := []struct {
		name    string
		req     *port.WebhookRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "Empty stream key",
			req: &port.WebhookRequest{
				EventType:  model.WebhookEventTypePublish,
				Timestamp:  time.Now(),
				AuthToken:  "test-token",
				ClientIP:   "127.0.0.1",
				UserAgent:  "test-agent",
				ServerNode: "test-node",
			},
			wantErr: true,
			errMsg:  "stream_key is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.HandleWebhook(ctx, tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGatewayService_KickStream(t *testing.T) {
	service, cache, mediaAdapter, _, liveAPIClient, _, _ := createTestService(nil)
	ctx := context.Background()
	roomID := uuid.New()
	streamKey := "test-stream"

	// Setup test data
	mapping := &model.StreamMapping{
		StreamKey:  streamKey,
		RoomID:     roomID,
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}

	// Setup expectations
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(mapping, nil)
	mediaAdapter.On("KickStream", mock.Anything, streamKey).Return(nil)
	liveAPIClient.On("NotifyStreamKicked", mock.Anything, mock.Anything).Return(nil)

	// Test successful case
	req := &port.KickStreamRequest{
		StreamKey: streamKey,
		Reason:    "test reason",
	}

	err := service.KickStream(ctx, req)
	assert.NoError(t, err)

	// Test error cases
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(nil, errors.New("mapping not found"))
	err = service.KickStream(ctx, req)
	assert.Error(t, err)
}

func TestGatewayService_KickStream_ValidationErrors(t *testing.T) {
	service, cache, mediaAdapter, _, _, _, _ := createTestService(nil)
	ctx := context.Background()

	// Setup expectations
	cache.On("GetStreamMapping", mock.Anything, mock.Anything).Return(nil, nil)
	mediaAdapter.On("KickStream", mock.Anything, mock.Anything).Return(nil)

	tests := []struct {
		name    string
		req     *port.KickStreamRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "Empty stream key",
			req: &port.KickStreamRequest{
				Reason: "test reason",
			},
			wantErr: true,
			errMsg:  "stream_key is required",
		},
		{
			name: "Empty reason",
			req: &port.KickStreamRequest{
				StreamKey: "test-stream",
			},
			wantErr: true,
			errMsg:  "reason is required",
		},
		{
			name: "Stream not found",
			req: &port.KickStreamRequest{
				StreamKey: "non-existent",
				Reason:    "test reason",
			},
			wantErr: true,
			errMsg:  "stream not found",
		},
		{
			name: "Media adapter error",
			req: &port.KickStreamRequest{
				StreamKey: "test-stream",
				Reason:    "test reason",
			},
			wantErr: true,
			errMsg:  "failed to kick stream via media adapter",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.KickStream(ctx, tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGatewayService_GetStreamInfo(t *testing.T) {
	service, cache, mediaAdapter, _, _, _, _ := createTestService(nil)
	ctx := context.Background()
	roomID := uuid.New()
	streamKey := "test-stream"

	// Setup test data
	mapping := &model.StreamMapping{
		StreamKey:  streamKey,
		RoomID:     roomID,
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}

	streamInfo := &port.StreamInfo{
		StreamKey: streamKey,
		Status:    model.StreamStatusLive,
		Stats: &model.StreamStats{
			VideoBitrate:  5000000,
			VideoFPS:      30,
			VideoFrames:   1000,
			AudioBitrate:  128000,
			AudioFrames:   1000,
			AudioLevel:    0.8,
			BytesSent:     1000000,
			BytesReceived: 1000000,
			PacketsLost:   0,
			RTT:           10,
			Jitter:        1,
		},
		StartTime:    &time.Time{},
		LastSeenTime: &time.Time{},
	}

	// Setup expectations
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(mapping, nil)
	mediaAdapter.On("GetStreamInfo", mock.Anything, streamKey).Return(streamInfo, nil)

	// Test successful case
	info, err := service.GetStreamInfo(ctx, streamKey)
	require.NoError(t, err)
	require.NotNil(t, info)
	assert.Equal(t, streamKey, info.StreamKey)
	assert.Equal(t, model.StreamStatusLive, info.Status)

	// Test error cases
	cache.On("GetStreamMapping", mock.Anything, streamKey).Return(nil, errors.New("mapping not found"))
	_, err = service.GetStreamInfo(ctx, streamKey)
	assert.Error(t, err)
}

func TestGatewayService_GetStreamInfo_ValidationErrors(t *testing.T) {
	service, _, _, _, _, _, _ := createTestService(nil)
	ctx := context.Background()

	tests := []struct {
		name      string
		streamKey string
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "Empty stream key",
			streamKey: "",
			wantErr:   true,
			errMsg:    "stream key is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.GetStreamInfo(ctx, tt.streamKey)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGatewayService_GetServerStats_Error(t *testing.T) {
	service, _, mediaAdapter, _, _, _, _ := createTestService(nil)
	ctx := context.Background()

	// Setup expectations
	mediaAdapter.On("GetServerStats", mock.Anything).Return(nil, errors.New("adapter error"))

	// Test error case
	_, err := service.GetServerStats(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get server stats")
}

func TestGatewayService_GetServerStats_ValidationErrors(t *testing.T) {
	service, _, _, _, _, _, _ := createTestService(nil)
	ctx := context.Background()

	// Test error case
	_, err := service.GetServerStats(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get server stats")
}
