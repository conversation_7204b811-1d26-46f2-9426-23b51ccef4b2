# CINA.CLUB Go Module 修复脚本
# 修复常见的go.mod文件问题：空文件、格式错误、注释问题等

Write-Host "🔧 开始修复 go.mod 文件问题..." -ForegroundColor Green

$fixedCount = 0
$processedCount = 0

# 获取所有服务目录
$serviceDirectories = Get-ChildItem -Path "services" -Directory

Write-Host "📂 发现 $($serviceDirectories.Count) 个服务目录" -ForegroundColor Cyan

# 标准go.mod模板函数
function New-StandardGoMod {
    param(
        [string]$ServiceName
    )
    
    $goModContent = @"
module cina.club/services/$ServiceName

go 1.22

replace (
    cina.club/core => ../../core
    cina.club/pkg => ../../pkg
    cina.club/services/$ServiceName => ./
)

require (
    github.com/gin-gonic/gin v1.10.0
    github.com/go-redis/redis/v8 v8.11.5
    github.com/google/uuid v1.6.0
    google.golang.org/grpc v1.65.0
    google.golang.org/protobuf v1.34.2
    gopkg.in/yaml.v3 v3.0.1
)
"@
    
    return $goModContent
}

# 清理go.mod文件函数
function Repair-GoMod {
    param(
        [string]$ServicePath,
        [string]$ServiceName
    )
    
    $goModPath = Join-Path $ServicePath "go.mod"
    $fixed = $false
    
    if (Test-Path $goModPath) {
        $content = Get-Content $goModPath -Raw -ErrorAction SilentlyContinue
        
        # 检查是否是空文件
        if ([string]::IsNullOrWhiteSpace($content)) {
            Write-Host "  🔧 空文件，创建标准go.mod" -ForegroundColor Yellow
            $newContent = New-StandardGoMod -ServiceName $ServiceName
            $newContent | Out-File -FilePath $goModPath -Encoding UTF8
            $fixed = $true
        }
        # 检查是否有注释在module声明之前
        elseif ($content -match "^/\*.*?\*/\s*module" -or $content -match "^//.*\n.*module") {
            Write-Host "  🔧 修复注释位置问题" -ForegroundColor Yellow
            
            # 提取module声明和之后的内容
            $lines = $content -split "`n"
            $moduleLineIndex = -1
            
            for ($i = 0; $i -lt $lines.Count; $i++) {
                if ($lines[$i] -match "^module\s+") {
                    $moduleLineIndex = $i
                    break
                }
            }
            
            if ($moduleLineIndex -gt 0) {
                # 重新组织内容：module声明开始
                $cleanContent = $lines[$moduleLineIndex..($lines.Count-1)] -join "`n"
                $cleanContent | Out-File -FilePath $goModPath -Encoding UTF8
                $fixed = $true
            }
        }
        # 检查module声明是否正确
        elseif ($content -notmatch "module cina\.club/services/$ServiceName") {
            Write-Host "  🔧 修复module声明" -ForegroundColor Yellow
            
            # 替换错误的module声明
            $correctedContent = $content -replace "module\s+[^\s]+", "module cina.club/services/$ServiceName"
            $correctedContent | Out-File -FilePath $goModPath -Encoding UTF8
            $fixed = $true
        }
    } else {
        Write-Host "  🔧 创建缺失的go.mod文件" -ForegroundColor Yellow
        $newContent = New-StandardGoMod -ServiceName $ServiceName
        $newContent | Out-File -FilePath $goModPath -Encoding UTF8
        $fixed = $true
    }
    
    return $fixed
}

# 处理每个服务目录
foreach ($serviceDir in $serviceDirectories) {
    $serviceName = $serviceDir.Name
    $servicePath = $serviceDir.FullName
    
    Write-Host "📦 处理服务: $serviceName" -ForegroundColor Yellow
    $processedCount++
    
    try {
        if (Repair-GoMod -ServicePath $servicePath -ServiceName $serviceName) {
            $fixedCount++
            Write-Host "  ✅ 已修复" -ForegroundColor Green
        } else {
            Write-Host "  ℹ️ 无需修复" -ForegroundColor Gray
        }
        
        # 运行go mod tidy来清理依赖
        $currentLocation = Get-Location
        Set-Location $servicePath
        
        Write-Host "  🧹 运行 go mod tidy..." -ForegroundColor Cyan
        $tidyOutput = go mod tidy 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ go mod tidy 成功" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️ go mod tidy 有警告" -ForegroundColor Yellow
        }
        
        Set-Location $currentLocation
    }
    catch {
        Write-Host "  ❌ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 200
}

# 输出修复总结
Write-Host ""
Write-Host "🎉 Go.mod 修复完成!" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Blue
Write-Host "📊 修复统计:" -ForegroundColor Cyan
Write-Host "  总处理: $processedCount 个服务" -ForegroundColor White
Write-Host "  已修复: $fixedCount 个服务" -ForegroundColor Green
Write-Host "  修复率: $([math]::Round(($fixedCount / $processedCount) * 100, 1))%" -ForegroundColor Cyan

Write-Host ""
Write-Host "🚀 建议下一步:" -ForegroundColor Cyan
Write-Host "1. 运行批量编译测试验证修复效果" -ForegroundColor White
Write-Host "2. .\scripts\simple-batch-test.ps1" -ForegroundColor Yellow
Write-Host "3. 检查并修复剩余的编译错误" -ForegroundColor White 