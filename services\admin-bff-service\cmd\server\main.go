/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"

	"cina.club/services/admin-bff-service/internal/adapter/cache"
	"cina.club/services/admin-bff-service/internal/adapter/client"
	"cina.club/services/admin-bff-service/internal/adapter/logger"
	httptransport "cina.club/services/admin-bff-service/internal/adapter/transport/http"
	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/application/service"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

func main() {
	// Initialize logger
	appLogger := setupLogger()

	// Load configuration
	config := loadConfig()

	appLogger.Info("Starting Admin BFF Service...")

	// Initialize Redis client
	redisClient, err := initRedis(config, appLogger)
	if err != nil {
		appLogger.Fatalf("Failed to initialize Redis: %v", err)
	}
	defer redisClient.Close()

	// Initialize session store
	sessionStore := cache.NewRedisSessionStore(redisClient, appLogger)

	// Initialize audit logger
	kafkaProducer := logger.NewMockKafkaProducer(appLogger) // Use Mock, replace with real Kafka Producer in production
	auditLogger := logger.NewAuditLogger(appLogger, kafkaProducer, "admin-audit-logs", true)

	// Initialize gRPC clients
	clientFactory, err := initClientFactory(config, appLogger)
	if err != nil {
		appLogger.Fatalf("Failed to initialize client factory: %v", err)
	}
	defer clientFactory.Close()

	// Initialize BFF service
	bffService := service.NewBFFService(
		sessionStore,
		auditLogger,
		clientFactory.GetClients(),
		appLogger,
	)

	// Initialize HTTP server
	server := setupHTTPServer(bffService, auditLogger, appLogger, config)

	// Start server
	go func() {
		appLogger.Infof("Admin BFF Service listening on port %s", config.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			appLogger.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info("Shutting down Admin BFF Service...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		appLogger.Fatalf("Server forced to shutdown: %v", err)
	}

	appLogger.Info("Admin BFF Service stopped gracefully")
}

// Config application configuration
type Config struct {
	Port        string
	Environment string
	LogLevel    string
	Redis       RedisConfig
	Services    map[string]ServiceConfig
}

// RedisConfig Redis configuration
type RedisConfig struct {
	Address  string
	Password string
	DB       int
}

// ServiceConfig service configuration
type ServiceConfig struct {
	Address        string
	ConnectTimeout time.Duration
	RequestTimeout time.Duration
}

// setupLogger sets up the logger
func setupLogger() *logrus.Logger {
	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(getEnv("LOG_LEVEL", "info"))
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// Set log format
	if getEnv("LOG_FORMAT", "json") == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	return logger
}

// loadConfig loads application configuration
func loadConfig() Config {
	return Config{
		Port:        getEnv("PORT", "8080"),
		Environment: getEnv("ENVIRONMENT", "development"),
		LogLevel:    getEnv("LOG_LEVEL", "info"),
		Redis: RedisConfig{
			Address:  getEnv("REDIS_ADDRESS", "localhost:6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvInt("REDIS_DB", 0),
		},
		Services: map[string]ServiceConfig{
			"user-core-service": {
				Address:        getEnv("USER_CORE_SERVICE_ADDRESS", "user-core-service:50051"),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
			"billing-service": {
				Address:        getEnv("BILLING_SERVICE_ADDRESS", "billing-service:50051"),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
			"social-service": {
				Address:        getEnv("SOCIAL_SERVICE_ADDRESS", "social-service:50051"),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
			"content-moderation-service": {
				Address:        getEnv("CONTENT_MODERATION_SERVICE_ADDRESS", "content-moderation-service:50051"),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
			"service-offering-service": {
				Address:        getEnv("SERVICE_OFFERING_SERVICE_ADDRESS", "service-offering-service:50051"),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
			"analytics-service": {
				Address:        getEnv("ANALYTICS_SERVICE_ADDRESS", "analytics-service:50051"),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
			"notification-dispatch-service": {
				Address:        getEnv("NOTIFICATION_DISPATCH_SERVICE_ADDRESS", "notification-dispatch-service:50051"),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			},
		},
	}
}

// initRedis initializes Redis client
func initRedis(config Config, logger *logrus.Logger) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.Redis.Address,
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	logger.Info("Redis connection established")
	return rdb, nil
}

// initClientFactory initializes client factory
func initClientFactory(config Config, logger *logrus.Logger) (*client.ClientFactory, error) {
	clientConfig := client.ClientConfig{
		Services: make(map[string]client.ServiceConfig),
	}

	// Convert configuration format
	for serviceName, serviceConfig := range config.Services {
		clientConfig.Services[serviceName] = client.ServiceConfig{
			Address:        serviceConfig.Address,
			ConnectTimeout: serviceConfig.ConnectTimeout,
			RequestTimeout: serviceConfig.RequestTimeout,
		}
	}

	return client.NewClientFactory(logger, clientConfig)
}

// setupHTTPServer sets up HTTP server
func setupHTTPServer(bffService port.BFFService, auditLogger port.AuditLogger, logger *logrus.Logger, config Config) *http.Server {
	r := chi.NewRouter()

	// Create middleware
	middlewares := httptransport.NewMiddleware(bffService, auditLogger, logger)

	// Create handler
	handler := httptransport.NewHandler(bffService, logger)

	// Basic middleware
	r.Use(middlewares.RequestID)
	r.Use(middlewares.Logging)
	r.Use(middlewares.Recovery)
	r.Use(middlewares.CORS)
	r.Use(middlewares.RateLimit)

	// Health check endpoint (no authentication required)
	r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"healthy","timestamp":"` + time.Now().Format(time.RFC3339) + `"}`))
	})

	// API routes (authentication required)
	r.Route("/api/v1/admin", func(r chi.Router) {
		// Authentication middleware
		r.Use(middlewares.SessionAuth)
		r.Use(middlewares.AuditLog)

		// Authentication related routes
		r.Route("/auth", func(r chi.Router) {
			r.Get("/me", handler.GetCurrentUser)
			r.Post("/logout", handler.Logout)
		})

		// User management routes
		r.Route("/users", func(r chi.Router) {
			r.Use(middlewares.RequireRole(string(model.RoleUserManager)))
			r.Get("/", handler.GetUsers)
			r.Get("/{userID}/profile", handler.GetUserProfile)
			r.Post("/{userID}/suspend", handler.SuspendUser)
			r.Post("/{userID}/restore", handler.RestoreUser)
		})

		// Content management routes
		r.Route("/content", func(r chi.Router) {
			r.Use(middlewares.RequireRole(string(model.RoleContentModerator)))
			r.Get("/moderation-queue", handler.GetModerationQueue)
			r.Post("/tasks/{taskID}/approve", handler.ApproveContent)
			r.Post("/tasks/{taskID}/reject", handler.RejectContent)
		})

		// Order management routes
		r.Route("/orders", func(r chi.Router) {
			r.Use(middlewares.RequireRole(string(model.RoleBillingManager)))
			r.Get("/", handler.GetOrders)
			r.Get("/{orderID}", handler.GetOrderDetails)
			r.Post("/{orderID}/cancel", handler.CancelOrder)
			r.Post("/{orderID}/refund", handler.RefundOrder)
		})

		// Analytics routes
		r.Route("/analytics", func(r chi.Router) {
			r.Use(middlewares.RequireRole(string(model.RoleAnalyst)))
			r.Get("/dashboard", handler.GetDashboardSummary)
			r.Get("/users", handler.GetUserAnalytics)
			r.Get("/content", handler.GetContentAnalytics)
			r.Get("/revenue", handler.GetRevenueAnalytics)
		})

		// System management routes
		r.Route("/system", func(r chi.Router) {
			r.Use(middlewares.RequireRole(string(model.RoleSystemAdmin)))
			r.Get("/health", handler.GetSystemHealth)
			r.Get("/audit-logs", handler.GetAuditLogs)
		})
	})

	return &http.Server{
		Addr:         ":" + config.Port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
}

// Helper functions

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
