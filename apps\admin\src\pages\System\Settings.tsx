/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:00:00
 * Modified: 2025-01-23 16:00:00
 */

import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Switch,
  Button,
  Space,
  Typography,
  Divider,
  Alert,
  Select,
  InputNumber,
  Upload,
  Progress,
  Tag,
  Modal,
  List,
  Tooltip,
  Badge,
  Row,
  Col,
  Statistic,
  Table,
} from 'antd';
import {
  SettingOutlined,
  FlagOutlined,
  ToolOutlined,
  CloudUploadOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SaveOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  BugOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  SecurityScanOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

interface SystemConfig {
  id: string;
  key: string;
  value: string | number | boolean;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  description: string;
  isSecret: boolean;
  updatedAt: string;
  updatedBy: string;
}

interface FeatureFlag {
  id: string;
  name: string;
  key: string;
  enabled: boolean;
  description: string;
  rolloutPercentage: number;
  targetUsers: string[];
  environment: 'development' | 'staging' | 'production' | 'all';
  createdAt: string;
  updatedAt: string;
}

interface BackupInfo {
  id: string;
  filename: string;
  size: number;
  createdAt: string;
  type: 'manual' | 'scheduled';
  status: 'completed' | 'in_progress' | 'failed';
  description?: string;
}

const SystemSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [form] = Form.useForm();
  const [featureFlagForm] = Form.useForm();
  const [maintenanceForm] = Form.useForm();
  const [isMaintenanceModalVisible, setIsMaintenanceModalVisible] = useState(false);
  const [isFeatureFlagModalVisible, setIsFeatureFlagModalVisible] = useState(false);
  const [selectedFeatureFlag, setSelectedFeatureFlag] = useState<FeatureFlag | null>(null);
  const [backupProgress, setBackupProgress] = useState(0);
  const [isBackupInProgress, setIsBackupInProgress] = useState(false);

  const queryClient = useQueryClient();

  // Mock data - replace with actual API calls
  const { data: systemConfigs = [], isLoading: configsLoading } = useQuery({
    queryKey: ['system', 'configs'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return [
        {
          id: '1',
          key: 'app.name',
          value: 'CINA.CLUB Admin',
          type: 'string' as const,
          category: 'General',
          description: 'Application name displayed in the UI',
          isSecret: false,
          updatedAt: '2025-01-23T10:00:00Z',
          updatedBy: '<EMAIL>'
        },
        {
          id: '2',
          key: 'app.version',
          value: '1.0.0',
          type: 'string' as const,
          category: 'General',
          description: 'Current application version',
          isSecret: false,
          updatedAt: '2025-01-23T10:00:00Z',
          updatedBy: 'system'
        },
        {
          id: '3',
          key: 'auth.session_timeout',
          value: 3600,
          type: 'number' as const,
          category: 'Security',
          description: 'Session timeout in seconds',
          isSecret: false,
          updatedAt: '2025-01-23T10:00:00Z',
          updatedBy: '<EMAIL>'
        },
        {
          id: '4',
          key: 'api.rate_limit',
          value: 1000,
          type: 'number' as const,
          category: 'API',
          description: 'API rate limit per hour',
          isSecret: false,
          updatedAt: '2025-01-23T10:00:00Z',
          updatedBy: '<EMAIL>'
        }
      ] as SystemConfig[];
    }
  });

  const { data: featureFlags = [], isLoading: flagsLoading } = useQuery({
    queryKey: ['system', 'feature-flags'],
    queryFn: async () => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return [
        {
          id: '1',
          name: 'Advanced Analytics',
          key: 'advanced_analytics',
          enabled: true,
          description: 'Enable advanced analytics dashboard with AI insights',
          rolloutPercentage: 100,
          targetUsers: [],
          environment: 'all' as const,
          createdAt: '2025-01-20T10:00:00Z',
          updatedAt: '2025-01-23T10:00:00Z'
        },
        {
          id: '2',
          name: 'Real-time Notifications',
          key: 'realtime_notifications',
          enabled: false,
          description: 'Enable real-time push notifications',
          rolloutPercentage: 25,
          targetUsers: ['<EMAIL>'],
          environment: 'development' as const,
          createdAt: '2025-01-22T10:00:00Z',
          updatedAt: '2025-01-23T10:00:00Z'
        },
        {
          id: '3',
          name: 'AI Content Moderation',
          key: 'ai_content_moderation',
          enabled: true,
          description: 'Use AI for automatic content moderation',
          rolloutPercentage: 80,
          targetUsers: [],
          environment: 'production' as const,
          createdAt: '2025-01-21T10:00:00Z',
          updatedAt: '2025-01-23T10:00:00Z'
        }
      ] as FeatureFlag[];
    }
  });

  const { data: backups = [], isLoading: backupsLoading } = useQuery({
    queryKey: ['system', 'backups'],
    queryFn: async () => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return [
        {
          id: '1',
          filename: 'system_backup_2025-01-23_10-00.tar.gz',
          size: 1024 * 1024 * 150, // 150MB
          createdAt: '2025-01-23T10:00:00Z',
          type: 'scheduled' as const,
          status: 'completed' as const,
          description: 'Daily scheduled backup'
        },
        {
          id: '2',
          filename: 'system_backup_2025-01-22_15-30.tar.gz',
          size: 1024 * 1024 * 148,
          createdAt: '2025-01-22T15:30:00Z',
          type: 'manual' as const,
          status: 'completed' as const,
          description: 'Pre-deployment backup'
        }
      ] as BackupInfo[];
    }
  });

  const { data: maintenanceStatus } = useQuery({
    queryKey: ['system', 'maintenance'],
    queryFn: async () => {
      await new Promise(resolve => setTimeout(resolve, 500));
      return {
        enabled: false,
        message: '',
        scheduledStart: null,
        scheduledEnd: null,
        allowedUsers: ['<EMAIL>']
      };
    }
  });

  // Mutations
  const updateConfigMutation = useMutation({
    mutationFn: async (config: Partial<SystemConfig>) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return config;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system', 'configs'] });
    }
  });

  const toggleFeatureFlagMutation = useMutation({
    mutationFn: async ({ id, enabled }: { id: string; enabled: boolean }) => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return { id, enabled };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system', 'feature-flags'] });
    }
  });

  const createBackupMutation = useMutation({
    mutationFn: async (description?: string) => {
      setIsBackupInProgress(true);
      setBackupProgress(0);
      
      // Simulate backup progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        setBackupProgress(i);
      }
      
      setIsBackupInProgress(false);
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system', 'backups'] });
    }
  });

  const toggleMaintenanceMutation = useMutation({
    mutationFn: async (data: any) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system', 'maintenance'] });
      setIsMaintenanceModalVisible(false);
    }
  });

  const handleConfigUpdate = (config: SystemConfig) => {
    updateConfigMutation.mutate(config);
  };

  const handleFeatureFlagToggle = (flag: FeatureFlag) => {
    toggleFeatureFlagMutation.mutate({
      id: flag.id,
      enabled: !flag.enabled
    });
  };

  const handleCreateBackup = () => {
    Modal.confirm({
      title: 'Create System Backup',
      content: 'This will create a complete backup of the system. This process may take several minutes.',
      onOk: () => {
        createBackupMutation.mutate('Manual backup created from admin panel');
      }
    });
  };

  const handleMaintenanceMode = () => {
    setIsMaintenanceModalVisible(true);
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const configColumns = [
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
      render: (key: string, record: SystemConfig) => (
        <Space>
          <Text code>{key}</Text>
          {record.isSecret && <Tag color="red">Secret</Tag>}
        </Space>
      )
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      render: (value: any, record: SystemConfig) => {
        if (record.isSecret) {
          return <Text type="secondary">••••••••</Text>;
        }
        if (record.type === 'boolean') {
          return <Tag color={value ? 'green' : 'red'}>{value ? 'True' : 'False'}</Tag>;
        }
        return <Text>{String(value)}</Text>;
      }
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => <Tag>{category}</Tag>
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Last Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: SystemConfig) => (
        <Button 
          type="link" 
          size="small"
          onClick={() => handleConfigUpdate(record)}
        >
          Edit
        </Button>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <SettingOutlined /> System Settings
        </Title>
        <Paragraph type="secondary">
          Manage system configuration, feature flags, maintenance mode, and backups.
        </Paragraph>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              General Configuration
            </span>
          }
          key="general"
        >
          <Card>
            <Title level={4}>System Configuration</Title>
            <Paragraph type="secondary">
              Manage application settings and configuration values.
            </Paragraph>
            
            <Table
              columns={configColumns}
              dataSource={systemConfigs}
              loading={configsLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <FlagOutlined />
              Feature Flags
            </span>
          }
          key="features"
        >
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                  <div>
                    <Title level={4}>Feature Flags</Title>
                    <Paragraph type="secondary">
                      Control feature rollouts and experimental functionality.
                    </Paragraph>
                  </div>
                  <Button 
                    type="primary" 
                    icon={<ExperimentOutlined />}
                    onClick={() => {
                      setSelectedFeatureFlag(null);
                      setIsFeatureFlagModalVisible(true);
                    }}
                  >
                    New Feature Flag
                  </Button>
                </div>

                <List
                  loading={flagsLoading}
                  dataSource={featureFlags}
                  renderItem={(flag) => (
                    <List.Item
                      actions={[
                        <Switch
                          checked={flag.enabled}
                          onChange={() => handleFeatureFlagToggle(flag)}
                          loading={toggleFeatureFlagMutation.isPending}
                        />,
                        <Button 
                          type="link" 
                          onClick={() => {
                            setSelectedFeatureFlag(flag);
                            setIsFeatureFlagModalVisible(true);
                          }}
                        >
                          Edit
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        title={
                          <Space>
                            <Text strong>{flag.name}</Text>
                            <Tag color={flag.enabled ? 'green' : 'red'}>
                              {flag.enabled ? 'Enabled' : 'Disabled'}
                            </Tag>
                            <Tag>{flag.environment}</Tag>
                            {flag.rolloutPercentage < 100 && (
                              <Tag color="orange">{flag.rolloutPercentage}% rollout</Tag>
                            )}
                          </Space>
                        }
                        description={
                          <div>
                            <Paragraph type="secondary">{flag.description}</Paragraph>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              Key: <Text code>{flag.key}</Text> | 
                              Updated: {new Date(flag.updatedAt).toLocaleString()}
                            </Text>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane
          tab={
            <span>
              <ToolOutlined />
              Maintenance
            </span>
          }
          key="maintenance"
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card>
                <Statistic
                  title="Maintenance Mode"
                  value={maintenanceStatus?.enabled ? "Active" : "Inactive"}
                  prefix={maintenanceStatus?.enabled ? <WarningOutlined /> : <CheckCircleOutlined />}
                  valueStyle={{ color: maintenanceStatus?.enabled ? '#f5222d' : '#52c41a' }}
                />
                <div style={{ marginTop: '16px' }}>
                  <Button 
                    type={maintenanceStatus?.enabled ? "default" : "primary"}
                    danger={maintenanceStatus?.enabled}
                    onClick={handleMaintenanceMode}
                    icon={<ToolOutlined />}
                  >
                    {maintenanceStatus?.enabled ? 'Disable' : 'Enable'} Maintenance Mode
                  </Button>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card>
                <Statistic
                  title="System Status"
                  value="Operational"
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
                <div style={{ marginTop: '16px' }}>
                  <Space>
                    <Button icon={<ReloadOutlined />}>Restart Services</Button>
                    <Button icon={<BugOutlined />}>Health Check</Button>
                  </Space>
                </div>
              </Card>
            </Col>
          </Row>

          {maintenanceStatus?.enabled && (
            <Card style={{ marginTop: '16px' }}>
              <Alert
                message="Maintenance Mode Active"
                description={maintenanceStatus.message || "System is currently under maintenance."}
                type="warning"
                showIcon
              />
            </Card>
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <DatabaseOutlined />
              Backup & Restore
            </span>
          }
          key="backup"
        >
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                  <div>
                    <Title level={4}>System Backups</Title>
                    <Paragraph type="secondary">
                      Create and manage system backups for data protection.
                    </Paragraph>
                  </div>
                  <Space>
                    <Button 
                      type="primary" 
                      icon={<CloudUploadOutlined />}
                      onClick={handleCreateBackup}
                      loading={createBackupMutation.isPending}
                    >
                      Create Backup
                    </Button>
                    <Upload>
                      <Button icon={<DownloadOutlined />}>
                        Restore Backup
                      </Button>
                    </Upload>
                  </Space>
                </div>

                {isBackupInProgress && (
                  <Card style={{ marginBottom: '16px' }}>
                    <Title level={5}>Creating Backup...</Title>
                    <Progress percent={backupProgress} status="active" />
                  </Card>
                )}

                <List
                  loading={backupsLoading}
                  dataSource={backups}
                  renderItem={(backup) => (
                    <List.Item
                      actions={[
                        <Button type="link" icon={<DownloadOutlined />}>
                          Download
                        </Button>,
                        <Button type="link" icon={<ReloadOutlined />}>
                          Restore
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        title={
                          <Space>
                            <Text strong>{backup.filename}</Text>
                            <Badge 
                              status={backup.status === 'completed' ? 'success' : 'processing'} 
                              text={backup.status}
                            />
                            <Tag color={backup.type === 'manual' ? 'blue' : 'green'}>
                              {backup.type}
                            </Tag>
                          </Space>
                        }
                        description={
                          <div>
                            <Text type="secondary">
                              Size: {formatFileSize(backup.size)} | 
                              Created: {new Date(backup.createdAt).toLocaleString()}
                            </Text>
                            {backup.description && (
                              <div>
                                <Text type="secondary">{backup.description}</Text>
                              </div>
                            )}
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* Maintenance Mode Modal */}
      <Modal
        title="Maintenance Mode"
        open={isMaintenanceModalVisible}
        onCancel={() => setIsMaintenanceModalVisible(false)}
        footer={null}
      >
        <Form
          form={maintenanceForm}
          layout="vertical"
          onFinish={(values) => toggleMaintenanceMutation.mutate(values)}
        >
          <Alert
            message="Warning"
            description="Enabling maintenance mode will prevent users from accessing the system."
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <Form.Item
            name="message"
            label="Maintenance Message"
            rules={[{ required: true, message: 'Please enter a maintenance message' }]}
          >
            <TextArea 
              placeholder="System is currently under maintenance. Please try again later."
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="allowedUsers"
            label="Allowed Users (Email addresses)"
          >
            <Select
              mode="tags"
              placeholder="Enter email addresses of users who can access during maintenance"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={toggleMaintenanceMutation.isPending}
              >
                {maintenanceStatus?.enabled ? 'Disable' : 'Enable'} Maintenance Mode
              </Button>
              <Button onClick={() => setIsMaintenanceModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SystemSettings; 