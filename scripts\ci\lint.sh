#!/usr/bin/env bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB 代码检查脚本
# 运行所有平台的代码规范检查

set -euo pipefail

# 导入共享函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# shellcheck source=../lib/helpers.sh
source "$SCRIPT_DIR/../lib/helpers.sh"

# 脚本配置
readonly SCRIPT_NAME="code-linter"

# 默认设置
DEFAULT_TARGET="all"
LINT_TIMEOUT="${LINT_TIMEOUT:-300}"

# 显示使用说明
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Run code style and quality checks for all supported languages.

Options:
    --fast                 Run only fast checks
    --fix                  Auto-fix issues where possible
    -v, --verbose          Enable verbose output
    -h, --help             Show this help message

Examples:
    $0                     # Run all linting checks
    $0 --fast              # Run fast checks only
    $0 --fix               # Auto-fix issues

Environment Variables:
    DEBUG=1                Enable debug output
    LINT_TIMEOUT          Timeout for linters (default: 300s)
EOF
}

# 解析参数
parse_args() {
    local fast=false
    local fix=false
    local verbose=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --fast)
                fast=true
                shift
                ;;
            --fix)
                fix=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    FAST="$fast"
    FIX="$fix"
    
    if [[ "$verbose" == "true" ]]; then
        export DEBUG=1
    fi
}

# 检查 Go 代码
lint_go() {
    step "Linting Go code"
    
    if ! command -v golangci-lint &> /dev/null; then
        warn "golangci-lint not found, skipping Go linting"
        return 0
    fi
    
    local project_root
    project_root="$(get_project_root)"
    
    local cmd="golangci-lint run ./..."
    
    if [[ "$FIX" == "true" ]]; then
        cmd="$cmd --fix"
    fi
    
    if [[ "$FAST" == "true" ]]; then
        cmd="$cmd --fast"
    fi
    
    if ! run_in_dir "$project_root" "$cmd"; then
        error "Go linting failed"
        return 1
    fi
    
    success "Go linting passed"
}

# 检查 Shell 脚本
lint_shell() {
    step "Linting shell scripts"
    
    if ! command -v shellcheck &> /dev/null; then
        warn "shellcheck not found, skipping shell linting"
        return 0
    fi
    
    local project_root
    project_root="$(get_project_root)"
    
    local scripts
    scripts=$(find "$project_root/scripts" -name "*.sh" 2>/dev/null || echo "")
    
    if [[ -z "$scripts" ]]; then
        warn "No shell scripts found"
        return 0
    fi
    
    local failed=false
    echo "$scripts" | while read -r script; do
        if [[ -f "$script" ]]; then
            debug "Checking: $script"
            if ! shellcheck "$script"; then
                failed=true
            fi
        fi
    done
    
    if [[ "$failed" == "true" ]]; then
        error "Shell linting failed"
        return 1
    fi
    
    success "Shell linting passed"
}

# 检查 Protobuf
lint_protobuf() {
    step "Linting Protobuf files"
    
    if ! command -v buf &> /dev/null; then
        warn "buf not found, skipping protobuf linting"
        return 0
    fi
    
    local project_root
    project_root="$(get_project_root)"
    local proto_dir="$project_root/core/api/proto/v1"
    
    if [[ ! -d "$proto_dir" ]]; then
        warn "Proto directory not found, skipping protobuf linting"
        return 0
    fi
    
    if ! run_in_dir "$project_root" "buf lint $proto_dir"; then
        error "Protobuf linting failed"
        return 1
    fi
    
    success "Protobuf linting passed"
}

# 检查 TypeScript (如果存在)
lint_typescript() {
    step "Linting TypeScript code"
    
    local project_root
    project_root="$(get_project_root)"
    local web_dir="$project_root/apps/web"
    
    if [[ ! -d "$web_dir" ]] || [[ ! -f "$web_dir/package.json" ]]; then
        warn "Web project not found, skipping TypeScript linting"
        return 0
    fi
    
    if ! run_in_dir "$web_dir" "npm run lint" 2>/dev/null; then
        warn "TypeScript linting failed or not configured"
        return 0
    fi
    
    success "TypeScript linting passed"
}

# 主函数
main() {
    init_script "$SCRIPT_NAME"
    
    parse_args "$@"
    
    local failed=false
    
    # 运行各种检查
    if ! lint_go; then
        failed=true
    fi
    
    if ! lint_shell; then
        failed=true
    fi
    
    if ! lint_protobuf; then
        failed=true
    fi
    
    if ! lint_typescript; then
        failed=true
    fi
    
    if [[ "$failed" == "true" ]]; then
        error "Some linting checks failed"
        exit 1
    fi
    
    finish_script "$SCRIPT_NAME"
}

# 只有在直接执行时才运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 