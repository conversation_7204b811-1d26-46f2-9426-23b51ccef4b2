#!/bin/bash
# CINA.CLUB Platform - Kong Gateway Integration Tests
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NAMESPACE_KONG="kong-system"
NAMESPACE_USER="user-identity"
TEST_TIMEOUT=300
KONG_PROXY_SERVICE="kong-proxy"
KONG_CONTROLLER_SERVICE="kong-ingress-controller"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test result tracking
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_info "Running test: $test_name"
    
    if eval "$test_command"; then
        log_success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "✗ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to wait for deployment
wait_for_deployment() {
    local namespace="$1"
    local deployment="$2"
    local timeout="$3"
    
    log_info "Waiting for deployment $deployment in namespace $namespace to be ready..."
    
    if kubectl wait --for=condition=available \
        --timeout="${timeout}s" \
        deployment/"$deployment" \
        -n "$namespace" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to check pod health
check_pod_health() {
    local namespace="$1"
    local label_selector="$2"
    
    local pods
    pods=$(kubectl get pods -n "$namespace" -l "$label_selector" -o jsonpath='{.items[*].metadata.name}')
    
    if [[ -z "$pods" ]]; then
        log_error "No pods found with label selector: $label_selector"
        return 1
    fi
    
    for pod in $pods; do
        local status
        status=$(kubectl get pod "$pod" -n "$namespace" -o jsonpath='{.status.phase}')
        
        if [[ "$status" != "Running" ]]; then
            log_error "Pod $pod is not running (status: $status)"
            return 1
        fi
        
        # Check readiness
        local ready
        ready=$(kubectl get pod "$pod" -n "$namespace" -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}')
        
        if [[ "$ready" != "True" ]]; then
            log_error "Pod $pod is not ready"
            return 1
        fi
    done
    
    return 0
}

# Function to test HTTP endpoint
test_http_endpoint() {
    local url="$1"
    local expected_status="$2"
    local description="$3"
    
    local status_code
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    
    if [[ "$status_code" == "$expected_status" ]]; then
        log_success "$description (Status: $status_code)"
        return 0
    else
        log_error "$description (Expected: $expected_status, Got: $status_code)"
        return 1
    fi
}

# Function to get Kong proxy URL
get_kong_proxy_url() {
    local service_type
    service_type=$(kubectl get service "$KONG_PROXY_SERVICE" -n "$NAMESPACE_KONG" -o jsonpath='{.spec.type}')
    
    if [[ "$service_type" == "LoadBalancer" ]]; then
        local external_ip
        external_ip=$(kubectl get service "$KONG_PROXY_SERVICE" -n "$NAMESPACE_KONG" -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        
        if [[ -n "$external_ip" && "$external_ip" != "null" ]]; then
            echo "http://$external_ip"
            return 0
        fi
    fi
    
    # Fallback to port-forward or NodePort
    echo "http://localhost:8000"
}

# Test functions
test_kong_system_deployment() {
    log_info "Testing Kong system deployment..."
    
    # Check Kong proxy deployment
    wait_for_deployment "$NAMESPACE_KONG" "kong-proxy" "$TEST_TIMEOUT" && \
    check_pod_health "$NAMESPACE_KONG" "app=kong-proxy,component=data-plane" && \
    log_success "Kong proxy deployment is healthy"
}

test_kong_controller_deployment() {
    log_info "Testing Kong Ingress Controller deployment..."
    
    # Check Kong controller deployment
    wait_for_deployment "$NAMESPACE_KONG" "kong-ingress-controller" "$TEST_TIMEOUT" && \
    check_pod_health "$NAMESPACE_KONG" "app=kong-ingress-controller,component=control-plane" && \
    log_success "Kong Ingress Controller deployment is healthy"
}

test_kong_services() {
    log_info "Testing Kong services..."
    
    # Check Kong proxy service
    kubectl get service "$KONG_PROXY_SERVICE" -n "$NAMESPACE_KONG" > /dev/null && \
    kubectl get service "$KONG_CONTROLLER_SERVICE" -n "$NAMESPACE_KONG" > /dev/null && \
    log_success "Kong services are available"
}

test_kong_plugins() {
    log_info "Testing Kong plugins installation..."
    
    # Check if Kong plugins are available
    local plugins
    plugins=$(kubectl get kongplugins -n "$NAMESPACE_KONG" --no-headers 2>/dev/null | wc -l)
    
    if [[ "$plugins" -gt 0 ]]; then
        log_success "Kong plugins are installed ($plugins plugins found)"
        return 0
    else
        log_error "No Kong plugins found"
        return 1
    fi
}

test_ingress_configuration() {
    log_info "Testing Ingress configurations..."
    
    # Check if user-core ingress exists
    kubectl get ingress user-core-ingress -n "$NAMESPACE_USER" > /dev/null 2>&1 && \
    kubectl get ingress user-core-public-ingress -n "$NAMESPACE_USER" > /dev/null 2>&1 && \
    kubectl get ingress user-core-admin-ingress -n "$NAMESPACE_USER" > /dev/null 2>&1 && \
    log_success "Ingress configurations are present"
}

test_kong_health_endpoint() {
    log_info "Testing Kong health endpoint..."
    
    # Port-forward to test Kong admin API health
    kubectl port-forward -n "$NAMESPACE_KONG" service/kong-proxy-internal 8001:8001 > /dev/null 2>&1 &
    local pf_pid=$!
    
    sleep 5
    
    if curl -s http://localhost:8001/status > /dev/null 2>&1; then
        log_success "Kong health endpoint is responding"
        kill $pf_pid
        return 0
    else
        log_error "Kong health endpoint is not responding"
        kill $pf_pid
        return 1
    fi
}

test_kong_metrics_endpoint() {
    log_info "Testing Kong metrics endpoint..."
    
    # Port-forward to test Kong metrics
    kubectl port-forward -n "$NAMESPACE_KONG" service/kong-proxy-internal 8100:8100 > /dev/null 2>&1 &
    local pf_pid=$!
    
    sleep 5
    
    if curl -s http://localhost:8100/metrics | grep -q "kong_"; then
        log_success "Kong metrics endpoint is responding with Prometheus metrics"
        kill $pf_pid
        return 0
    else
        log_error "Kong metrics endpoint is not responding properly"
        kill $pf_pid
        return 1
    fi
}

test_api_gateway_routing() {
    log_info "Testing API Gateway routing..."
    
    local kong_url
    kong_url=$(get_kong_proxy_url)
    
    # Test health endpoint (should be publicly accessible)
    if kubectl get ingress user-core-public-ingress -n "$NAMESPACE_USER" > /dev/null 2>&1; then
        # Port-forward for testing
        kubectl port-forward -n "$NAMESPACE_KONG" service/kong-proxy 8000:8000 > /dev/null 2>&1 &
        local pf_pid=$!
        
        sleep 5
        
        # Test public health endpoint
        if curl -s -H "Host: api.cina.club" http://localhost:8000/api/v1/health > /dev/null 2>&1; then
            log_success "API Gateway routing is working"
            kill $pf_pid
            return 0
        else
            log_warning "API Gateway routing test failed (backend service may not be running)"
            kill $pf_pid
            return 1
        fi
    else
        log_warning "User core ingress not found, skipping routing test"
        return 0
    fi
}

test_rate_limiting_configuration() {
    log_info "Testing rate limiting configuration..."
    
    # Check if rate limiting plugins exist
    local rate_limit_plugins
    rate_limit_plugins=$(kubectl get kongplugins -n "$NAMESPACE_KONG" --no-headers 2>/dev/null | grep -c "rate-limit" || echo "0")
    
    if [[ "$rate_limit_plugins" -gt 0 ]]; then
        log_success "Rate limiting plugins are configured ($rate_limit_plugins found)"
        return 0
    else
        log_error "No rate limiting plugins found"
        return 1
    fi
}

test_jwt_authentication_configuration() {
    log_info "Testing JWT authentication configuration..."
    
    # Check if JWT plugins exist
    local jwt_plugins
    jwt_plugins=$(kubectl get kongplugins -n "$NAMESPACE_KONG" --no-headers 2>/dev/null | grep -c "jwt-validator" || echo "0")
    
    if [[ "$jwt_plugins" -gt 0 ]]; then
        log_success "JWT authentication plugins are configured ($jwt_plugins found)"
        return 0
    else
        log_error "No JWT authentication plugins found"
        return 1
    fi
}

test_cors_configuration() {
    log_info "Testing CORS configuration..."
    
    # Check if CORS plugins exist
    local cors_plugins
    cors_plugins=$(kubectl get kongplugins -n "$NAMESPACE_KONG" --no-headers 2>/dev/null | grep -c "cors" || echo "0")
    
    if [[ "$cors_plugins" -gt 0 ]]; then
        log_success "CORS plugins are configured ($cors_plugins found)"
        return 0
    else
        log_error "No CORS plugins found"
        return 1
    fi
}

test_observability_configuration() {
    log_info "Testing observability configuration..."
    
    # Check if observability plugins exist
    local observability_plugins
    observability_plugins=$(kubectl get kongplugins -n "$NAMESPACE_KONG" --no-headers 2>/dev/null | grep -E "(prometheus|opentelemetry|request-id)" | wc -l || echo "0")
    
    if [[ "$observability_plugins" -gt 0 ]]; then
        log_success "Observability plugins are configured ($observability_plugins found)"
        return 0
    else
        log_error "No observability plugins found"
        return 1
    fi
}

# Main test execution
main() {
    log_info "Starting Kong Gateway Integration Tests for CINA.CLUB Platform"
    log_info "============================================================"
    
    # Pre-flight checks
    if ! command -v kubectl > /dev/null; then
        log_error "kubectl is required but not installed"
        exit 1
    fi
    
    if ! command -v curl > /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    # Check if namespaces exist
    if ! kubectl get namespace "$NAMESPACE_KONG" > /dev/null 2>&1; then
        log_error "Kong namespace ($NAMESPACE_KONG) does not exist"
        exit 1
    fi
    
    # Run tests
    run_test "Kong System Deployment" "test_kong_system_deployment"
    run_test "Kong Controller Deployment" "test_kong_controller_deployment"
    run_test "Kong Services" "test_kong_services"
    run_test "Kong Plugins Installation" "test_kong_plugins"
    run_test "Kong Health Endpoint" "test_kong_health_endpoint"
    run_test "Kong Metrics Endpoint" "test_kong_metrics_endpoint"
    run_test "Ingress Configuration" "test_ingress_configuration"
    run_test "API Gateway Routing" "test_api_gateway_routing"
    run_test "Rate Limiting Configuration" "test_rate_limiting_configuration"
    run_test "JWT Authentication Configuration" "test_jwt_authentication_configuration"
    run_test "CORS Configuration" "test_cors_configuration"
    run_test "Observability Configuration" "test_observability_configuration"
    
    # Test summary
    log_info "============================================================"
    log_info "Test Results Summary:"
    log_info "Total Tests: $TESTS_TOTAL"
    log_success "Passed: $TESTS_PASSED"
    log_error "Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "All tests passed! Kong Gateway is ready for production."
        exit 0
    else
        log_error "Some tests failed. Please review the configuration."
        exit 1
    fi
}

# Run main function
main "$@" 