# CINA.CLUB 全面Import路径修复脚本

Write-Host "Starting comprehensive import path fixes..." -ForegroundColor Green

$fixedFiles = 0
$services = Get-ChildItem -Path "services" -Directory

foreach ($service in $services) {
    $serviceName = $service.Name
    Write-Host "Processing: $serviceName" -ForegroundColor Yellow
    
    $goFiles = Get-ChildItem -Path $service.FullName -Filter "*.go" -Recurse
    
    foreach ($goFile in $goFiles) {
        try {
            $content = Get-Content $goFile.FullName -Raw
            if ([string]::IsNullOrWhiteSpace($content)) { continue }
            
            $original = $content
            
            # Fix 1: service-name/pkg/ -> cina.club/pkg/
            $content = $content -replace "$serviceName/pkg/", "cina.club/pkg/"
            
            # Fix 2: service-name/core/ -> cina.club/core/
            $content = $content -replace "$serviceName/core/", "cina.club/core/"
            
            # Fix 3: service-name/internal/ -> cina.club/services/service-name/internal/
            $content = $content -replace "$serviceName/internal/", "cina.club/services/$serviceName/internal/"
            
            # Fix 4: service-name/worker/ -> cina.club/services/service-name/worker/
            $content = $content -replace "$serviceName/worker/", "cina.club/services/$serviceName/worker/"
            
            # Fix 5: service-name/cmd/ -> cina.club/services/service-name/cmd/
            $content = $content -replace "$serviceName/cmd/", "cina.club/services/$serviceName/cmd/"
            
            # Fix 6: Common hardcoded service paths
            $content = $content -replace "short-video-service/internal/", "cina.club/services/short-video-service/internal/"
            $content = $content -replace "short-video-service/worker/", "cina.club/services/short-video-service/worker/"
            $content = $content -replace "social-service/internal/", "cina.club/services/social-service/internal/"
            
            if ($content -ne $original) {
                $utf8 = New-Object System.Text.UTF8Encoding $false
                [System.IO.File]::WriteAllText($goFile.FullName, $content, $utf8)
                $fixedFiles++
                Write-Host "  Fixed: $($goFile.Name)" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "  Error: $($goFile.Name)" -ForegroundColor Red
        }
    }
    
    # Update go.mod for each service
    $goModPath = Join-Path $service.FullName "go.mod"
    if (Test-Path $goModPath) {
        try {
            $goModContent = Get-Content $goModPath -Raw
            
            # Add cina.club/pkg and cina.club/core to require section if not present
            if ($goModContent -notmatch "cina\.club/pkg" -and $goModContent -match "require \(") {
                $goModContent = $goModContent -replace "(require \()", "`$1`n    cina.club/pkg v0.0.0`n    cina.club/core v0.0.0"
                
                $utf8 = New-Object System.Text.UTF8Encoding $false
                [System.IO.File]::WriteAllText($goModPath, $goModContent, $utf8)
                
                Write-Host "  Updated go.mod dependencies" -ForegroundColor Cyan
            }
        }
        catch {
            Write-Host "  Error updating go.mod: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "Fixed $fixedFiles files across all services" -ForegroundColor Green

# Run go mod tidy for all services
Write-Host "Running go mod tidy for all services..." -ForegroundColor Yellow

foreach ($service in $services) {
    $currentLocation = Get-Location
    Set-Location $service.FullName
    
    Write-Host "  Tidying: $($service.Name)" -ForegroundColor Gray
    go mod tidy 2>$null
    
    Set-Location $currentLocation
}

Write-Host ""
Write-Host "Comprehensive fix completed!" -ForegroundColor Green
Write-Host "Run: .\scripts\simple-batch-test.ps1 to test results" -ForegroundColor Yellow 