# CINA.CLUB Platform - Production Environment Overlay
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: cina-club-system-production
  annotations:
    description: "Production environment configuration for CINA.CLUB platform"

# Base configuration
resources:
  - ../../base

# Environment-specific labels
commonLabels:
  environment: production
  criticality: high
  tier: production

# Environment-specific annotations
commonAnnotations:
  deployment.environment: production
  monitoring.enabled: "true"
  backup.enabled: "true"

# Production-specific patches
patchesStrategicMerge:
  - patches/production-resources.yaml
  - patches/production-security.yaml
  - patches/production-monitoring.yaml

# Production replicas
replicas:
  - name: kong-proxy
    count: 5  # Higher replica count for production
  - name: kong-ingress-controller
    count: 2  # HA for control plane
  - name: prometheus
    count: 3  # HA for monitoring

# Production images with specific tags
images:
  - name: kong/kubernetes-ingress-controller
    newTag: "2.12.0"
  - name: kong
    newTag: "3.4.2-alpine"
  - name: prom/prometheus
    newTag: "v2.47.2"
  - name: grafana/grafana
    newTag: "10.2.0"

# Production secrets
secretGenerator:
  - name: production-secrets
    literals:
      - environment=production
      - elasticsearch.password=production-secret-password
      - grafana.admin.password=production-admin-password
      - alert.webhook.url=https://hooks.slack.com/services/PROD/WEBHOOK/URL
    type: Opaque

# Production config maps
configMapGenerator:
  - name: production-config
    literals:
      - environment=production
      - log.level=warn
      - metrics.retention=90d
      - backup.schedule=0 2 * * *
      - domain=api.cina.club
      - tls.enabled=true

# Resource transformations for production
transformers:
  - name-reference.yaml
  - security-transformer.yaml 