/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage

/**
 * Chat list screen showing conversations and system messages.
 * Follows WeChat message list design patterns.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatListScreen(
    onChatClick: (ChatItem) -> Unit,
    onSearchClick: () -> Unit,
    onAddClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val chatItems = remember {
        listOf(
            // System messages
            ChatItem(
                id = "announcement",
                title = "审批",
                lastMessage = "\"法大大电子合同\"应用在企业添加了一个审批模...",
                timestamp = "2024/10/18",
                isSystemMessage = true,
                icon = Icons.Default.Assignment,
                iconColor = Color(0xFFFF9800)
            ),
            ChatItem(
                id = "checkin",
                title = "打卡",
                lastMessage = "管理员修改了打卡规则，当前规则类型不支持...",
                timestamp = "2024/9/25",
                isSystemMessage = true,
                icon = Icons.Default.LocationOn,
                iconColor = Color(0xFFFF9800)
            ),
            ChatItem(
                id = "report",
                title = "汇报",
                lastMessage = "汇报循写提醒",
                timestamp = "2022/10/10",
                isSystemMessage = true,
                icon = Icons.Default.Description,
                iconColor = Color(0xFFFF9800)
            ),
            ChatItem(
                id = "announcement2",
                title = "公告",
                lastMessage = "",
                timestamp = "",
                isSystemMessage = true,
                icon = Icons.Default.Campaign,
                iconColor = Color(0xFFFF9800)
            ),
            
            // Group chats with WeChat-style styling
            ChatItem(
                id = "group1",
                title = "阳光🍀王菲🌀银行贷款@微信",
                lastMessage = "【语音】",
                timestamp = "昨天",
                avatarUrl = "https://via.placeholder.com/40x40",
                hasWeChatIcon = true
            ),
            ChatItem(
                id = "industry_news",
                title = "行业资讯",
                lastMessage = "同事们在看《龙雨溪万蒸H3！性能炸裂！》",
                timestamp = "昨天",
                avatarUrl = "https://via.placeholder.com/40x40"
            ),
            ChatItem(
                id = "service_assistant",
                title = "企业微信服务商助手",
                lastMessage = "你已成为企业微信服务商，ID:未来得销售资...",
                timestamp = "星期一",
                avatarUrl = "https://via.placeholder.com/40x40"
            ),
            ChatItem(
                id = "abc_group",
                title = "ABC乐线绸-小颖 13479890512@微信",
                lastMessage = "哈喽 前门没想微信咨询看您未回复 可能忙...",
                timestamp = "星期一",
                avatarUrl = "https://via.placeholder.com/40x40",
                hasWeChatIcon = true
            ),
            ChatItem(
                id = "wechat_team",
                title = "企业微信团队",
                lastMessage = "企业微信管理后台登录通知",
                timestamp = "星期一",
                avatarUrl = "https://via.placeholder.com/40x40"
            )
        )
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "消息",
                    fontWeight = FontWeight.Medium
                )
            },
            actions = {
                IconButton(onClick = onSearchClick) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "Search"
                    )
                }
                
                IconButton(onClick = onAddClick) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF4A90E2)
            )
        )
        
        // Filter tabs
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            FilterChip(
                onClick = { /* Filter all */ },
                label = { Text("全部") },
                selected = true
            )
            
            FilterChip(
                onClick = { /* Filter schedule */ },
                label = { Text("日程") },
                selected = false
            )
            
            FilterChip(
                onClick = { /* Filter meetings */ },
                label = { Text("会议") },
                selected = false
            )
        }
        
        // Chat list
        LazyColumn(
            modifier = Modifier.fillMaxSize()
        ) {
            items(chatItems) { chatItem ->
                ChatListItem(
                    chatItem = chatItem,
                    onClick = { onChatClick(chatItem) }
                )
                
                Divider(
                    modifier = Modifier.padding(start = 72.dp),
                    color = Color(0xFFE0E0E0)
                )
            }
        }
    }
}

@Composable
private fun ChatListItem(
    chatItem: ChatItem,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(Color.White)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Avatar or icon
        if (chatItem.isSystemMessage) {
            Surface(
                modifier = Modifier.size(40.dp),
                color = chatItem.iconColor,
                shape = CircleShape
            ) {
                Icon(
                    imageVector = chatItem.icon!!,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.padding(8.dp)
                )
            }
        } else {
            AsyncImage(
                model = chatItem.avatarUrl,
                contentDescription = "Avatar",
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(Color.Gray)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // Chat content
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = chatItem.title,
                    color = Color(0xFF333333),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                if (chatItem.hasWeChatIcon) {
                    Icon(
                        imageVector = Icons.Default.Chat,
                        contentDescription = "WeChat",
                        tint = Color(0xFF07C160),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = chatItem.lastMessage,
                    color = Color(0xFF888888),
                    fontSize = 14.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                if (chatItem.timestamp.isNotEmpty()) {
                    Text(
                        text = chatItem.timestamp,
                        color = Color(0xFFBBBBBB),
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}

// Data class for chat items
data class ChatItem(
    val id: String,
    val title: String,
    val lastMessage: String,
    val timestamp: String,
    val avatarUrl: String? = null,
    val isSystemMessage: Boolean = false,
    val icon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    val iconColor: Color = Color.Gray,
    val hasWeChatIcon: Boolean = false,
    val unreadCount: Int = 0
) 