/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { persist } from 'zustand/middleware'

import type { AuthState, AuthUser, Permission, UserRole } from '@/types/auth'
import { User } from '@/types/user'
import { useAnalytics } from '@/hooks/useAnalytics'
// Re-export types for easier access
export type { AuthUser, Permission, UserRole } from '@/types/auth'

interface AuthStore extends AuthState {
  // Actions
  login: (token: string, refreshToken: string, user: AuthUser) => void
  logout: () => void
  updateUser: (user: Partial<AuthUser>) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void

  // Selectors/Computed
  hasPermission: (permission: Permission | Permission[]) => boolean
  hasRole: (role: UserRole | UserRole[]) => boolean
  hasAnyPermission: (permissions: Permission[]) => boolean
  hasAllPermissions: (permissions: Permission[]) => boolean
}

export const useAuthStore = create<AuthStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      loading: false,
      error: null,

      // Actions
      login: (token: string, refreshToken: string, user: AuthUser) => {
        const { identifyUser } = useAnalytics()
        set((state) => {
          state.isAuthenticated = true
          state.token = token
          state.refreshToken = refreshToken
          state.user = user
          state.error = null
          state.loading = false
        })
        identifyUser(user)
      },

      logout: () => {
        const { resetUser } = useAnalytics()
        set((state) => {
          state.isAuthenticated = false
          state.token = null
          state.refreshToken = null
          state.user = null
          state.error = null
          state.loading = false
        })
        resetUser()
      },

      updateUser: (userData: Partial<AuthUser>) => {
        set((state) => {
          if (state.user) {
            Object.assign(state.user, userData)
          }
        })
      },

      setLoading: (loading: boolean) => {
        set((state) => {
          state.loading = loading
        })
      },

      setError: (error: string | null) => {
        set((state) => {
          state.error = error
          state.loading = false
        })
      },

      clearError: () => {
        set((state) => {
          state.error = null
        })
      },

      // Permission checking functions
      hasPermission: (permission: Permission | Permission[]) => {
        const { user } = get()
        if (!user) return false

        const permissions = Array.isArray(permission) ? permission : [permission]
        return permissions.some((perm) => user.permissions.includes(perm))
      },

      hasRole: (role: UserRole | UserRole[]) => {
        const { user } = get()
        if (!user) return false

        const roles = Array.isArray(role) ? role : [role]
        return roles.some((r) => user.roles.includes(r))
      },

      hasAnyPermission: (permissions: Permission[]) => {
        const { user } = get()
        if (!user) return false

        return permissions.some((perm) => user.permissions.includes(perm))
      },

      hasAllPermissions: (permissions: Permission[]) => {
        const { user } = get()
        if (!user) return false

        return permissions.every((perm) => user.permissions.includes(perm))
      },
    })),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        token: state.token,
        refreshToken: state.refreshToken,
        user: state.user,
      }),
    }
  )
)

// 权限检查Hook
export const usePermission = () => {
  const hasPermission = useAuthStore((state) => state.hasPermission)
  const hasRole = useAuthStore((state) => state.hasRole)
  const hasAnyPermission = useAuthStore((state) => state.hasAnyPermission)
  const hasAllPermissions = useAuthStore((state) => state.hasAllPermissions)

  return {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
  }
} 