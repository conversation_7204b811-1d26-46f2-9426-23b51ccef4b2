# CINA.CLUB Platform - Batch Create Remaining Microservices Deploy Configurations
# Copyright (c) 2025 Cina.Club
# PowerShell script to create deploy configurations for all remaining microservices

param(
    [Parameter(Mandatory=$false)]
    [string]$Mode = "create"  # create, update, or verify
)

# Microservices configuration matrix
$microservices = @(
    @{
        Name = "chat-websocket-server"
        Namespace = "chat"
        Template = "realtime"
        Timeout = "30000"
        Plugins = "jwt-validator-user-service, rate-limit-premium, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/chat/ws", "/api/v1/chat/websocket", "/api/v1/chat/realtime")
    },
    @{
        Name = "live-gateway-service"
        Namespace = "live"
        Template = "realtime"
        Timeout = "15000"
        Plugins = "jwt-validator-user-service, rate-limit-premium, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/live/gateway", "/api/v1/live/proxy", "/api/v1/live/stream-gateway")
    },
    @{
        Name = "community-forum-service"
        Namespace = "community"
        Template = "social"
        Timeout = "60000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/community/forums", "/api/v1/community/posts", "/api/v1/community/topics")
    },
    @{
        Name = "community-qa-service"
        Namespace = "community"
        Template = "social"
        Timeout = "45000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/community/qa", "/api/v1/community/questions", "/api/v1/community/answers")
    },
    @{
        Name = "digital-twin-service"
        Namespace = "digital-twin"
        Template = "ai"
        Timeout = "180000"
        Plugins = "jwt-validator-user-service, rate-limit-user, response-rate-limit, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/digital-twin/create", "/api/v1/digital-twin/sync", "/api/v1/digital-twin/simulate")
    },
    @{
        Name = "fast-news-service"
        Namespace = "news"
        Template = "analytics"
        Timeout = "30000"
        Plugins = "jwt-validator-optional, rate-limit-ip, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/news/fast", "/api/v1/news/breaking", "/api/v1/news/realtime")
    },
    @{
        Name = "family-tree-service"
        Namespace = "family"
        Template = "user"
        Timeout = "45000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/family/tree", "/api/v1/family/members", "/api/v1/family/relationships")
    },
    @{
        Name = "calendar-sync-service"
        Namespace = "calendar"
        Template = "user"
        Timeout = "60000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/calendar/sync", "/api/v1/calendar/events", "/api/v1/calendar/integration")
    },
    @{
        Name = "cina-coin-ledger-service"
        Namespace = "blockchain"
        Template = "financial"
        Timeout = "45000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-restrictive, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/cina-coin/ledger", "/api/v1/cina-coin/transactions", "/api/v1/cina-coin/balance")
    },
    @{
        Name = "cloud-sync-service"
        Namespace = "cloud"
        Template = "file"
        Timeout = "300000"
        Plugins = "jwt-validator-user-service, rate-limit-premium, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/cloud/sync", "/api/v1/cloud/backup", "/api/v1/cloud/restore")
    },
    @{
        Name = "key-management-proxy-service"
        Namespace = "security"
        Template = "admin"
        Timeout = "30000"
        Plugins = "jwt-validator-admin, rate-limit-admin, cors-restrictive, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/keys/manage", "/api/v1/keys/rotate", "/api/v1/keys/vault")
    },
    @{
        Name = "live-im-service"
        Namespace = "live"
        Template = "realtime"
        Timeout = "30000"
        Plugins = "jwt-validator-user-service, rate-limit-premium, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/live/im", "/api/v1/live/chat", "/api/v1/live/messages")
    },
    @{
        Name = "metaverse-engine-service"
        Namespace = "metaverse"
        Template = "ai"
        Timeout = "300000"
        Plugins = "jwt-validator-user-service, rate-limit-user, response-rate-limit, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/metaverse/engine", "/api/v1/metaverse/render", "/api/v1/metaverse/physics")
    },
    @{
        Name = "model-management-service"
        Namespace = "models"
        Template = "ai"
        Timeout = "180000"
        Plugins = "jwt-validator-user-service, rate-limit-user, response-rate-limit, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/models/manage", "/api/v1/models/deploy", "/api/v1/models/inference")
    },
    @{
        Name = "news-crawler-service"
        Namespace = "news"
        Template = "analytics"
        Timeout = "120000"
        Plugins = "rate-limit-api-key, cors-restrictive, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/news/crawl", "/api/v1/news/extract", "/api/v1/news/parse")
    },
    @{
        Name = "review-service"
        Namespace = "review"
        Template = "social"
        Timeout = "45000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/reviews/create", "/api/v1/reviews/manage", "/api/v1/reviews/moderate")
    },
    @{
        Name = "routines-service"
        Namespace = "routines"
        Template = "user"
        Timeout = "30000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/routines/manage", "/api/v1/routines/execute", "/api/v1/routines/schedule")
    },
    @{
        Name = "schedule-service"
        Namespace = "schedule"
        Template = "user"
        Timeout = "30000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/schedule/manage", "/api/v1/schedule/events", "/api/v1/schedule/calendar")
    },
    @{
        Name = "service-offering-service"
        Namespace = "offerings"
        Template = "social"
        Timeout = "45000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/offerings/create", "/api/v1/offerings/manage", "/api/v1/offerings/discover")
    },
    @{
        Name = "shared-kb-service"
        Namespace = "knowledge"
        Template = "file"
        Timeout = "180000"
        Plugins = "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
        Paths = @("/api/v1/shared-kb/manage", "/api/v1/shared-kb/collaborate", "/api/v1/shared-kb/public")
    }
)

function Create-IngressConfig {
    param($service)
    
    $ingressContent = @"
# CINA.CLUB Platform - $($service.Name) Ingress Configuration
# Copyright (c) 2025 Cina.Club

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: $($service.Name.Replace('-service', ''))-ingress
  namespace: $($service.Namespace)
  labels:
    app: $($service.Name)
    component: ingress
    tier: application
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    konghq.com/plugins: "$($service.Plugins)"
    konghq.com/protocol: "grpc"
    konghq.com/read-timeout: "$($service.Timeout)"
    konghq.com/write-timeout: "$($service.Timeout)"
    konghq.com/connect-timeout: "3000"
    description: "$($service.Name) for CINA.CLUB platform"

spec:
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  rules:
    - host: "api.cina.club"
      http:
        paths:
"@

    foreach ($path in $service.Paths) {
        $ingressContent += @"

          - path: $path
            pathType: Prefix
            backend:
              service:
                name: $($service.Name)
                port:
                  number: 8080
"@
    }

    return $ingressContent
}

function Create-KustomizationConfig {
    param($service)
    
    return @"
# CINA.CLUB Platform - $($service.Name) Base Kustomization
# Copyright (c) 2025 Cina.Club

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: $($service.Name)-base
  annotations:
    description: "Base configuration for $($service.Name) Kong Ingress"
    service: "$($service.Name)"
    service-type: "$($service.Template)"

resources:
  - ingress.yaml

namespace: $($service.Namespace)

commonLabels:
  app: $($service.Name)
  component: api-gateway
  service: $($service.Name.Replace('-service', ''))
  tier: application
  platform: cina-club
  service-type: $($service.Template)

commonAnnotations:
  platform: "cina-club"
  service-owner: "$($service.Template)-<EMAIL>"
  api-version: "v1"
  managed-by: "kong-ingress-controller"
"@
}

# Main execution
Write-Host "🚀 CINA.CLUB Microservices Deploy Configuration Batch Creation" -ForegroundColor Green
Write-Host "Copyright (c) 2025 Cina.Club" -ForegroundColor Yellow
Write-Host ""

$totalServices = $microservices.Count
$completed = 0

foreach ($service in $microservices) {
    $completed++
    $progress = [math]::Round(($completed / $totalServices) * 100, 1)
    
    Write-Host "[$completed/$totalServices] ($progress%) Processing: $($service.Name)" -ForegroundColor Cyan
    
    # Create directories
    $baseDir = "services/$($service.Name)/deploy/base"
    if (!(Test-Path $baseDir)) {
        New-Item -ItemType Directory -Path $baseDir -Force | Out-Null
    }
    
    # Create ingress.yaml
    $ingressPath = "$baseDir/ingress.yaml"
    $ingressContent = Create-IngressConfig -service $service
    Set-Content -Path $ingressPath -Value $ingressContent -Encoding UTF8
    
    # Create kustomization.yaml
    $kustomizationPath = "$baseDir/kustomization.yaml"
    $kustomizationContent = Create-KustomizationConfig -service $service
    Set-Content -Path $kustomizationPath -Value $kustomizationContent -Encoding UTF8
    
    Write-Host "  ✅ Created: $ingressPath" -ForegroundColor Green
    Write-Host "  ✅ Created: $kustomizationPath" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Batch creation completed!" -ForegroundColor Green
Write-Host "✅ Created deploy configurations for $totalServices microservices" -ForegroundColor Green
Write-Host "📊 Total files created: $($totalServices * 2)" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Review generated configurations" -ForegroundColor White
Write-Host "2. Create overlays for dev/staging/prod environments" -ForegroundColor White
Write-Host "3. Validate with: kubectl apply --dry-run=client -k services/<service>/deploy/base" -ForegroundColor White
Write-Host "4. Deploy to cluster: kubectl apply -k services/<service>/deploy/base" -ForegroundColor White 