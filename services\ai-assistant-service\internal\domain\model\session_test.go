/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package model

import (
	"fmt"
	"testing"
	"time"
)

func TestNewDialogState(t *testing.T) {
	userID := "test-user-123"
	ds := NewDialogState(userID)

	if ds == nil {
		t.Fatal("NewDialogState returned nil")
	}

	if ds.UserID != userID {
		t.Errorf("Expected UserID %s, got %s", userID, ds.UserID)
	}

	if ds.SessionID == "" {
		t.Error("SessionID should not be empty")
	}

	if len(ds.MessageHistory) != 0 {
		t.Error("MessageHistory should be empty initially")
	}

	if ds.Context == nil {
		t.Error("Context should be initialized")
	}

	if ds.CreatedAt.IsZero() {
		t.Error("CreatedAt should be set")
	}

	if ds.UpdatedAt.IsZero() {
		t.<PERSON>rror("UpdatedAt should be set")
	}

	if ds.ExpiresAt.IsZero() {
		t.<PERSON>rror("ExpiresAt should be set")
	}

	// Check if expires at is 24 hours from creation
	expectedExpiry := ds.CreatedAt.Add(24 * time.Hour)
	if ds.ExpiresAt.Unix() != expectedExpiry.Unix() {
		t.Errorf("Expected ExpiresAt to be 24 hours from creation")
	}
}

func TestDialogState_AddMessage(t *testing.T) {
	ds := NewDialogState("test-user")
	originalUpdateTime := ds.UpdatedAt

	// Add a message
	content := []ContentItem{
		{
			Type: ContentTypeText,
			Text: "Hello world",
		},
	}

	// Sleep to ensure time difference
	time.Sleep(time.Millisecond)
	ds.AddMessage(MessageRoleUser, content)

	if len(ds.MessageHistory) != 1 {
		t.Errorf("Expected 1 message, got %d", len(ds.MessageHistory))
	}

	msg := ds.MessageHistory[0]
	if msg.Role != MessageRoleUser {
		t.Errorf("Expected role %s, got %s", MessageRoleUser, msg.Role)
	}

	if len(msg.Content) != 1 {
		t.Errorf("Expected 1 content item, got %d", len(msg.Content))
	}

	if msg.Content[0].Text != "Hello world" {
		t.Errorf("Expected text 'Hello world', got '%s'", msg.Content[0].Text)
	}

	if msg.ID == "" {
		t.Error("Message ID should not be empty")
	}

	if msg.Timestamp.IsZero() {
		t.Error("Message timestamp should be set")
	}

	if ds.UpdatedAt.Equal(originalUpdateTime) {
		t.Error("UpdatedAt should be updated after adding message")
	}
}

func TestDialogState_AddTextMessage(t *testing.T) {
	ds := NewDialogState("test-user")

	ds.AddTextMessage(MessageRoleAssistant, "Hello, how can I help you?")

	if len(ds.MessageHistory) != 1 {
		t.Errorf("Expected 1 message, got %d", len(ds.MessageHistory))
	}

	msg := ds.MessageHistory[0]
	if msg.Role != MessageRoleAssistant {
		t.Errorf("Expected role %s, got %s", MessageRoleAssistant, msg.Role)
	}

	if len(msg.Content) != 1 {
		t.Errorf("Expected 1 content item, got %d", len(msg.Content))
	}

	if msg.Content[0].Type != ContentTypeText {
		t.Errorf("Expected content type %s, got %s", ContentTypeText, msg.Content[0].Type)
	}

	if msg.Content[0].Text != "Hello, how can I help you?" {
		t.Errorf("Expected text 'Hello, how can I help you?', got '%s'", msg.Content[0].Text)
	}
}

func TestDialogState_GetLastMessages(t *testing.T) {
	ds := NewDialogState("test-user")

	// Test with empty history
	messages := ds.GetLastMessages(5)
	if len(messages) != 0 {
		t.Errorf("Expected 0 messages, got %d", len(messages))
	}

	// Test with n <= 0
	messages = ds.GetLastMessages(0)
	if len(messages) != 0 {
		t.Errorf("Expected 0 messages with n=0, got %d", len(messages))
	}

	messages = ds.GetLastMessages(-1)
	if len(messages) != 0 {
		t.Errorf("Expected 0 messages with n=-1, got %d", len(messages))
	}

	// Add some messages
	for i := 0; i < 10; i++ {
		ds.AddTextMessage(MessageRoleUser, fmt.Sprintf("Message %d", i))
	}

	// Test getting last 5 messages
	messages = ds.GetLastMessages(5)
	if len(messages) != 5 {
		t.Errorf("Expected 5 messages, got %d", len(messages))
	}

	// Should get messages 5-9
	for i, msg := range messages {
		expectedText := fmt.Sprintf("Message %d", i+5)
		if msg.Content[0].Text != expectedText {
			t.Errorf("Expected text '%s', got '%s'", expectedText, msg.Content[0].Text)
		}
	}

	// Test getting more messages than available
	messages = ds.GetLastMessages(20)
	if len(messages) != 10 {
		t.Errorf("Expected 10 messages, got %d", len(messages))
	}
}

func TestDialogState_SetExecutionState(t *testing.T) {
	ds := NewDialogState("test-user")
	originalUpdateTime := ds.UpdatedAt

	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	// Sleep to ensure time difference
	time.Sleep(time.Millisecond)
	ds.SetExecutionState(execState)

	if ds.ExecutionState == nil {
		t.Error("ExecutionState should not be nil")
	}

	if ds.ExecutionState.PlanID != plan.ID {
		t.Errorf("Expected PlanID %s, got %s", plan.ID, ds.ExecutionState.PlanID)
	}

	if ds.UpdatedAt.Equal(originalUpdateTime) {
		t.Error("UpdatedAt should be updated after setting execution state")
	}
}

func TestDialogState_UpdateExecutionStep(t *testing.T) {
	ds := NewDialogState("test-user")

	// Test with nil execution state
	ds.UpdateExecutionStep(0, "result")
	// Should not panic

	// Set up execution state
	plan := NewWorkflowPlan("test", "test plan")
	step := WorkflowStep{
		ID:     "step-1",
		Name:   "Test Step",
		ToolID: "test-tool",
	}
	plan.AddStep(step)

	execState := NewExecutionState(plan)
	ds.SetExecutionState(execState)

	originalUpdateTime := ds.UpdatedAt

	// Sleep to ensure time difference
	time.Sleep(time.Millisecond)
	ds.UpdateExecutionStep(0, "test result")

	if ds.ExecutionState.CurrentStep != 0 {
		t.Errorf("Expected CurrentStep 0, got %d", ds.ExecutionState.CurrentStep)
	}

	if ds.ExecutionState.StepResults["step-1"] != "test result" {
		t.Errorf("Expected step result 'test result', got %v", ds.ExecutionState.StepResults["step-1"])
	}

	if ds.UpdatedAt.Equal(originalUpdateTime) {
		t.Error("UpdatedAt should be updated after updating execution step")
	}

	// Test with step index out of range
	ds.UpdateExecutionStep(10, "out of range result")
	// Should not crash, and should not add to StepResults
}

func TestDialogState_IsExpired(t *testing.T) {
	ds := NewDialogState("test-user")

	// Should not be expired initially
	if ds.IsExpired() {
		t.Error("Dialog state should not be expired initially")
	}

	// Set expiry to past
	ds.ExpiresAt = time.Now().Add(-1 * time.Hour)
	if !ds.IsExpired() {
		t.Error("Dialog state should be expired")
	}
}

func TestDialogState_ExtendExpiry(t *testing.T) {
	ds := NewDialogState("test-user")
	originalExpiry := ds.ExpiresAt
	originalUpdateTime := ds.UpdatedAt

	// Sleep to ensure time difference
	time.Sleep(time.Millisecond)
	ds.ExtendExpiry(2 * time.Hour)

	if ds.ExpiresAt.Equal(originalExpiry) {
		t.Error("ExpiresAt should be updated")
	}

	if ds.UpdatedAt.Equal(originalUpdateTime) {
		t.Error("UpdatedAt should be updated after extending expiry")
	}

	// Check if the expiry is approximately 2 hours from now
	expectedExpiry := time.Now().Add(2 * time.Hour)
	timeDiff := ds.ExpiresAt.Sub(expectedExpiry)
	if timeDiff > time.Second || timeDiff < -time.Second {
		t.Errorf("Expected expiry to be around 2 hours from now, got %v", ds.ExpiresAt)
	}
}

func TestDialogState_ToJSON_FromJSON(t *testing.T) {
	ds := NewDialogState("test-user")
	ds.AddTextMessage(MessageRoleUser, "Hello")
	ds.AddTextMessage(MessageRoleAssistant, "Hi there!")

	// Serialize to JSON
	jsonData, err := ds.ToJSON()
	if err != nil {
		t.Fatalf("Failed to serialize to JSON: %v", err)
	}

	// Deserialize from JSON
	newDS := &DialogState{}
	err = newDS.FromJSON(jsonData)
	if err != nil {
		t.Fatalf("Failed to deserialize from JSON: %v", err)
	}

	// Compare
	if newDS.SessionID != ds.SessionID {
		t.Errorf("SessionID mismatch: expected %s, got %s", ds.SessionID, newDS.SessionID)
	}

	if newDS.UserID != ds.UserID {
		t.Errorf("UserID mismatch: expected %s, got %s", ds.UserID, newDS.UserID)
	}

	if len(newDS.MessageHistory) != len(ds.MessageHistory) {
		t.Errorf("MessageHistory length mismatch: expected %d, got %d", len(ds.MessageHistory), len(newDS.MessageHistory))
	}
}

func TestNewWorkflowPlan(t *testing.T) {
	name := "test-plan"
	description := "Test workflow plan"
	plan := NewWorkflowPlan(name, description)

	if plan == nil {
		t.Fatal("NewWorkflowPlan returned nil")
	}

	if plan.Name != name {
		t.Errorf("Expected name %s, got %s", name, plan.Name)
	}

	if plan.Description != description {
		t.Errorf("Expected description %s, got %s", description, plan.Description)
	}

	if plan.ID == "" {
		t.Error("Plan ID should not be empty")
	}

	if len(plan.Steps) != 0 {
		t.Error("Steps should be empty initially")
	}

	if plan.Variables == nil {
		t.Error("Variables should be initialized")
	}

	if plan.CreatedAt.IsZero() {
		t.Error("CreatedAt should be set")
	}
}

func TestWorkflowPlan_AddStep(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")

	step := WorkflowStep{
		Name:   "Test Step",
		ToolID: "test-tool",
	}

	plan.AddStep(step)

	if len(plan.Steps) != 1 {
		t.Errorf("Expected 1 step, got %d", len(plan.Steps))
	}

	addedStep := plan.Steps[0]
	if addedStep.Name != "Test Step" {
		t.Errorf("Expected step name 'Test Step', got '%s'", addedStep.Name)
	}

	if addedStep.ID == "" {
		t.Error("Step ID should be auto-generated")
	}

	// Test adding step with existing ID
	stepWithID := WorkflowStep{
		ID:     "custom-id",
		Name:   "Step with ID",
		ToolID: "test-tool",
	}

	plan.AddStep(stepWithID)

	if len(plan.Steps) != 2 {
		t.Errorf("Expected 2 steps, got %d", len(plan.Steps))
	}

	if plan.Steps[1].ID != "custom-id" {
		t.Errorf("Expected step ID 'custom-id', got '%s'", plan.Steps[1].ID)
	}
}

func TestWorkflowPlan_GetStep(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")

	step := WorkflowStep{
		ID:     "test-step-id",
		Name:   "Test Step",
		ToolID: "test-tool",
	}

	plan.AddStep(step)

	// Test finding existing step
	foundStep := plan.GetStep("test-step-id")
	if foundStep == nil {
		t.Error("Expected to find step, got nil")
	}

	if foundStep.Name != "Test Step" {
		t.Errorf("Expected step name 'Test Step', got '%s'", foundStep.Name)
	}

	// Test finding non-existing step
	notFoundStep := plan.GetStep("non-existing-id")
	if notFoundStep != nil {
		t.Error("Expected nil for non-existing step")
	}
}

func TestNewExecutionState(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	if execState == nil {
		t.Fatal("NewExecutionState returned nil")
	}

	if execState.PlanID != plan.ID {
		t.Errorf("Expected PlanID %s, got %s", plan.ID, execState.PlanID)
	}

	if execState.Plan != plan {
		t.Error("Plan reference should be set")
	}

	if execState.CurrentStep != 0 {
		t.Errorf("Expected CurrentStep 0, got %d", execState.CurrentStep)
	}

	if execState.Status != ExecutionStatusPending {
		t.Errorf("Expected status %s, got %s", ExecutionStatusPending, execState.Status)
	}

	if execState.StepResults == nil {
		t.Error("StepResults should be initialized")
	}

	if execState.IntermediateResults == nil {
		t.Error("IntermediateResults should be initialized")
	}
}

func TestExecutionState_MarkAsRunning(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	execState.MarkAsRunning()

	if execState.Status != ExecutionStatusRunning {
		t.Errorf("Expected status %s, got %s", ExecutionStatusRunning, execState.Status)
	}
}

func TestExecutionState_MarkAsPaused(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	execState.MarkAsPaused()

	if execState.Status != ExecutionStatusPaused {
		t.Errorf("Expected status %s, got %s", ExecutionStatusPaused, execState.Status)
	}

	if execState.PausedAt == nil {
		t.Error("PausedAt should be set")
	}
}

func TestExecutionState_MarkAsCompleted(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	execState.MarkAsCompleted()

	if execState.Status != ExecutionStatusCompleted {
		t.Errorf("Expected status %s, got %s", ExecutionStatusCompleted, execState.Status)
	}

	if execState.CompletedAt == nil {
		t.Error("CompletedAt should be set")
	}
}

func TestExecutionState_MarkAsFailed(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	testErr := fmt.Errorf("test error")
	execState.MarkAsFailed(testErr)

	if execState.Status != ExecutionStatusFailed {
		t.Errorf("Expected status %s, got %s", ExecutionStatusFailed, execState.Status)
	}

	if execState.Error != "test error" {
		t.Errorf("Expected error 'test error', got '%s'", execState.Error)
	}

	// Test with nil error
	execState2 := NewExecutionState(plan)
	execState2.MarkAsFailed(nil)

	if execState2.Status != ExecutionStatusFailed {
		t.Errorf("Expected status %s, got %s", ExecutionStatusFailed, execState2.Status)
	}

	if execState2.Error != "" {
		t.Errorf("Expected empty error string, got '%s'", execState2.Error)
	}
}

func TestExecutionState_IsCompleted(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	// Test pending state
	if execState.IsCompleted() {
		t.Error("Pending execution should not be completed")
	}

	// Test running state
	execState.MarkAsRunning()
	if execState.IsCompleted() {
		t.Error("Running execution should not be completed")
	}

	// Test paused state
	execState.MarkAsPaused()
	if execState.IsCompleted() {
		t.Error("Paused execution should not be completed")
	}

	// Test completed state
	execState.MarkAsCompleted()
	if !execState.IsCompleted() {
		t.Error("Completed execution should be completed")
	}

	// Test failed state
	execState.MarkAsFailed(fmt.Errorf("test error"))
	if !execState.IsCompleted() {
		t.Error("Failed execution should be completed")
	}
}

func TestExecutionState_CanResume(t *testing.T) {
	plan := NewWorkflowPlan("test", "test plan")
	execState := NewExecutionState(plan)

	// Test pending state
	if execState.CanResume() {
		t.Error("Pending execution should not be resumable")
	}

	// Test running state
	execState.MarkAsRunning()
	if execState.CanResume() {
		t.Error("Running execution should not be resumable")
	}

	// Test paused state
	execState.MarkAsPaused()
	if !execState.CanResume() {
		t.Error("Paused execution should be resumable")
	}

	// Test completed state
	execState.MarkAsCompleted()
	if execState.CanResume() {
		t.Error("Completed execution should not be resumable")
	}

	// Test failed state
	execState.MarkAsFailed(fmt.Errorf("test error"))
	if execState.CanResume() {
		t.Error("Failed execution should not be resumable")
	}
}
