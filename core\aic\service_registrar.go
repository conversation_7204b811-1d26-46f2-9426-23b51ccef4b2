package aic

import (
	"fmt"
	"reflect"

	"cina.club/core/di"
)

// ServiceRegistrar implements the AICoreServiceRegistrar interface
type ServiceRegistrar struct {
	aiEngine *Engine
	models   []AIModel
}

// NewServiceRegistrar creates a new AI Core service registrar
func NewServiceRegistrar(aiEngine *Engine, models []AIModel) *ServiceRegistrar {
	return &ServiceRegistrar{
		aiEngine: aiEngine,
		models:   models,
	}
}

// Register adds AI Core services to the dependency injection container
func (r *ServiceRegistrar) Register(container *di.Container) error {
	// Register AI Engine
	if err := container.Register(r.aiEngine); err != nil {
		return fmt.Errorf("failed to register AI engine: %w", err)
	}

	// Register AI Models
	for _, model := range r.models {
		if err := container.Register(model); err != nil {
			return fmt.Errorf("failed to register AI model %v: %w", reflect.TypeOf(model), err)
		}
	}

	return nil
}

// InitializeAIModels prepares and validates AI models
func (r *ServiceRegistrar) InitializeAIModels() error {
	for _, model := range r.models {
		// Use GetModelInfo() to get the model name for logging
		modelInfo := model.GetModelInfo()
		if err := model.Initialize(); err != nil {
			return fmt.Errorf("failed to initialize AI model %s: %w", modelInfo.Name, err)
		}
	}
	return nil
}

// Example usage
func ExampleAIServiceRegistration() {
	// Create AI Engine
	aiEngine := NewEngine()

	// Create AI Models
	models := []AIModel{
		NewBaseAIModel(ModelConfig{
			ID:   "text-gen-1",
			Name: "TextGenerationModel",
		}),
		NewBaseAIModel(ModelConfig{
			ID:   "img-rec-1",
			Name: "ImageRecognitionModel",
		}),
	}

	// Create Service Registrar
	registrar := NewServiceRegistrar(aiEngine, models)

	// Register services
	err := registrar.Register(di.GlobalContainer)
	if err != nil {
		panic(err)
	}

	// Initialize models
	err = registrar.InitializeAIModels()
	if err != nil {
		panic(err)
	}
}
