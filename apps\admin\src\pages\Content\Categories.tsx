/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useRef } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Switch, 
  Tag, 
  Space, 
  message, 
  Typography,
  Tooltip,
  Popconfirm,
  Badge,
  ColorPicker
} from 'antd'
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-table'
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  FolderOpenOutlined,
  FolderOutlined,
  SortAscendingOutlined
} from '@ant-design/icons'

import { ContentCategory, ContentCategoryStatus } from '@/types/content'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { Text, Title } = Typography
const { Option } = Select
const { TextArea } = Input

// Mock categories data with hierarchical structure
const mockCategories: ContentCategory[] = [
  {
    id: '1',
    name: '生活方式',
    slug: 'lifestyle',
    description: '关于生活方式、健康、家庭等内容',
    color: '#52c41a',
    icon: '🏠',
    parentId: null,
    level: 0,
    status: ContentCategoryStatus.ACTIVE,
    contentCount: 3820,
    order: 1,
    isFeature: true,
    createdAt: '2025-01-20T10:00:00Z',
    updatedAt: '2025-01-20T10:00:00Z',
  },
  {
    id: '2',
    name: '健康',
    slug: 'health',
    description: '健康养生、医疗保健相关内容',
    color: '#1890ff',
    icon: '⚕️',
    parentId: '1',
    level: 1,
    status: ContentCategoryStatus.ACTIVE,
    contentCount: 1240,
    order: 1,
    isFeature: false,
    createdAt: '2025-01-20T10:00:00Z',
    updatedAt: '2025-01-20T10:00:00Z',
  },
  {
    id: '3',
    name: '科技',
    slug: 'technology',
    description: '科技产品、数码设备、技术资讯',
    color: '#722ed1',
    icon: '💻',
    parentId: null,
    level: 0,
    status: ContentCategoryStatus.ACTIVE,
    contentCount: 2940,
    order: 2,
    isFeature: true,
    createdAt: '2025-01-20T10:00:00Z',
    updatedAt: '2025-01-20T10:00:00Z',
  },
  {
    id: '4',
    name: '美食',
    slug: 'food',
    description: '美食制作、餐厅推荐、饮食文化',
    color: '#f5222d',
    icon: '🍳',
    parentId: null,
    level: 0,
    status: ContentCategoryStatus.ACTIVE,
    contentCount: 2150,
    order: 3,
    isFeature: true,
    createdAt: '2025-01-20T10:00:00Z',
    updatedAt: '2025-01-20T10:00:00Z',
  }
]

/**
 * Content Categories Management Component
 */
const Categories: React.FC = () => {
  const actionRef = useRef<ActionType>()
  const { hasPermission } = usePermission()
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ContentCategory | null>(null)
  const [form] = Form.useForm()

  // Permission check
  const canManageCategories = hasPermission(Permission.CONTENT_MANAGE)

  // Fetch categories data
  const fetchCategories = async (params: any) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        data: mockCategories,
        success: true,
        total: mockCategories.length,
      }
    } catch (error) {
      message.error('获取分类列表失败')
      return {
        data: [],
        success: false,
        total: 0,
      }
    }
  }

  // Get status color
  const getStatusColor = (status: ContentCategoryStatus) => {
    switch (status) {
      case ContentCategoryStatus.ACTIVE: return 'green'
      case ContentCategoryStatus.INACTIVE: return 'red'
      case ContentCategoryStatus.ARCHIVED: return 'gray'
      default: return 'default'
    }
  }

  // Get status text
  const getStatusText = (status: ContentCategoryStatus) => {
    switch (status) {
      case ContentCategoryStatus.ACTIVE: return '启用'
      case ContentCategoryStatus.INACTIVE: return '禁用'
      case ContentCategoryStatus.ARCHIVED: return '归档'
      default: return '未知'
    }
  }

  // Open add/edit modal
  const handleEdit = (category?: ContentCategory) => {
    setEditingCategory(category || null)
    setModalVisible(true)
    
    if (category) {
      form.setFieldsValue({
        name: category.name,
        slug: category.slug,
        description: category.description,
        parentId: category.parentId,
        color: category.color,
        icon: category.icon,
        status: category.status,
        isFeature: category.isFeature,
        order: category.order,
      })
    } else {
      form.resetFields()
    }
  }

  // Submit form
  const handleSubmit = async (values: any) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (editingCategory) {
        message.success('分类更新成功')
      } else {
        message.success('分类创建成功')
      }
      
      setModalVisible(false)
      actionRef.current?.reload()
      form.resetFields()
    } catch (error) {
      message.error('操作失败')
    }
  }

  // Delete category
  const handleDelete = async (id: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      message.success('分类删除成功')
      actionRef.current?.reload()
    } catch (error) {
      message.error('删除失败')
    }
  }

  // Toggle status
  const handleToggleStatus = async (id: string, status: ContentCategoryStatus) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      const newStatus = status === ContentCategoryStatus.ACTIVE 
        ? ContentCategoryStatus.INACTIVE 
        : ContentCategoryStatus.ACTIVE
      message.success(`分类已${getStatusText(newStatus)}`)
      actionRef.current?.reload()
    } catch (error) {
      message.error('状态切换失败')
    }
  }

  // Table columns configuration
  const columns: ProColumns<ContentCategory>[] = [
    {
      title: '分类信息',
      key: 'info',
      width: 250,
      fixed: 'left',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ 
            marginRight: '12px', 
            fontSize: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '32px',
            height: '32px',
            backgroundColor: record.color,
            borderRadius: '6px',
            color: '#fff'
          }}>
            {record.icon}
          </div>
          <div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Text strong>{record.name}</Text>
              {record.isFeature && (
                <Badge 
                  count="推荐" 
                  style={{ 
                    backgroundColor: '#f50',
                    marginLeft: '8px',
                    fontSize: '10px'
                  }} 
                />
              )}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.slug}
            </div>
            <div style={{ fontSize: '12px', color: '#999', marginTop: '2px' }}>
              {record.description}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: number) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ marginBottom: '4px' }}>
            {level === 0 ? <FolderOutlined /> : <FolderOpenOutlined />}
          </div>
          <Text style={{ fontSize: '12px' }}>
            L{level}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ContentCategoryStatus, record) => (
        <div style={{ textAlign: 'center' }}>
          <Tag color={getStatusColor(status)}>
            {getStatusText(status)}
          </Tag>
          {canManageCategories && (
            <div style={{ marginTop: '4px' }}>
              <Switch
                size="small"
                checked={status === ContentCategoryStatus.ACTIVE}
                onChange={() => handleToggleStatus(record.id, status)}
              />
            </div>
          )}
        </div>
      ),
    },
    {
      title: '内容数量',
      dataIndex: 'contentCount',
      key: 'contentCount',
      width: 100,
      sorter: true,
      render: (count: number) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
            {count.toLocaleString()}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            篇内容
          </div>
        </div>
      ),
    },
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      width: 80,
      sorter: true,
      render: (order: number) => (
        <div style={{ textAlign: 'center' }}>
          <SortAscendingOutlined style={{ marginRight: '4px' }} />
          {order}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      valueType: 'dateTime',
      sorter: true,
      render: (_, record) => (
        <Text style={{ fontSize: '12px' }}>
          {new Date(record.createdAt).toLocaleString()}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 160,
      render: (_, record) => (
        <Space>
          {canManageCategories && (
            <>
              <Tooltip title="编辑">
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(record)}
                />
              </Tooltip>
              <Tooltip title="删除">
                <Popconfirm
                  title="确定要删除这个分类吗？"
                  onConfirm={() => handleDelete(record.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Popconfirm>
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ]

  if (!canManageCategories) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限管理内容分类</p>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <ProTable<ContentCategory>
        headerTitle="内容分类管理"
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={fetchCategories}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleEdit()}
          >
            新建分类
          </Button>,
        ]}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 1000 }}
      />

      {/* Add/Edit Category Modal */}
      <Modal
        title={editingCategory ? '编辑分类' : '新建分类'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form form={form} onFinish={handleSubmit} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="分类名称"
                name="name"
                rules={[{ required: true, message: '请输入分类名称' }]}
              >
                <Input placeholder="请输入分类名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="分类标识"
                name="slug"
                rules={[{ required: true, message: '请输入分类标识' }]}
              >
                <Input placeholder="请输入分类标识（英文）" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="分类描述"
            name="description"
          >
            <TextArea rows={3} placeholder="请输入分类描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="上级分类"
                name="parentId"
              >
                <Select placeholder="请选择上级分类" allowClear>
                  {mockCategories
                    .filter(cat => cat.level === 0 && cat.id !== editingCategory?.id)
                    .map(cat => (
                      <Option key={cat.id} value={cat.id}>
                        {cat.name}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="分类颜色"
                name="color"
                rules={[{ required: true, message: '请选择分类颜色' }]}
              >
                <Input placeholder="#52c41a" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="分类图标"
                name="icon"
                rules={[{ required: true, message: '请输入分类图标' }]}
              >
                <Input placeholder="请输入 Emoji 图标" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="状态"
                name="status"
                initialValue={ContentCategoryStatus.ACTIVE}
              >
                <Select>
                  <Option value={ContentCategoryStatus.ACTIVE}>启用</Option>
                  <Option value={ContentCategoryStatus.INACTIVE}>禁用</Option>
                  <Option value={ContentCategoryStatus.ARCHIVED}>归档</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="排序值"
                name="order"
                initialValue={1}
              >
                <Input type="number" placeholder="排序值（数字越小越靠前）" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="是否推荐"
                name="isFeature"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default Categories 