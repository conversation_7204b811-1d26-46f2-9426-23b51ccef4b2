# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

terraform {
  required_version = ">= 1.5"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.20"
    }
  }

  backend "s3" {
    bucket         = "cina-club-terraform-state-staging"
    key            = "staging/terraform.tfstate"
    region         = "us-west-2"
    dynamodb_table = "cina-club-terraform-locks"
    encrypt        = true
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Environment = "staging"
      Project     = "cina-club"
      ManagedBy   = "terraform"
    }
  }
}

# VPC Module
module "vpc" {
  source = "../../modules/vpc"

  environment = "staging"
  vpc_cidr    = "10.1.0.0/16"
  
  public_subnet_cidrs  = ["10.1.1.0/24", "10.1.2.0/24", "10.1.3.0/24"]
  private_subnet_cidrs = ["10.1.11.0/24", "10.1.12.0/24", "10.1.13.0/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = false
  
  tags = {
    Name = "cina-club-staging-vpc"
  }
}

# EKS Cluster Module
module "eks" {
  source = "../../modules/eks"

  environment = "staging"
  vpc_id      = module.vpc.vpc_id
  subnet_ids  = module.vpc.private_subnet_ids

  cluster_name    = "cina-club-staging"
  cluster_version = "1.28"

  node_groups = {
    main = {
      instance_types = ["t3.large"]
      min_size       = 3
      max_size       = 15
      desired_size   = 5
    }
  }

  tags = {
    Name = "cina-club-staging-eks"
  }
}

# RDS PostgreSQL Module
module "rds_main" {
  source = "../../modules/rds"

  environment = "staging"
  vpc_id      = module.vpc.vpc_id
  subnet_ids  = module.vpc.private_subnet_ids

  identifier = "cina-club-staging-main"
  engine     = "postgres"
  version    = "15.4"
  
  instance_class    = "db.t3.small"
  allocated_storage = 100
  storage_encrypted = true

  database_name = "cinaclub_staging"
  username      = "cinaadmin"
  
  backup_retention_period = 14
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  tags = {
    Name = "cina-club-staging-main-db"
  }
}

# Redis Cache Module
module "redis" {
  source = "../../modules/redis"

  environment = "staging"
  vpc_id      = module.vpc.vpc_id
  subnet_ids  = module.vpc.private_subnet_ids

  cluster_id         = "cina-club-staging-cache"
  node_type         = "cache.t3.small"
  num_cache_nodes   = 2
  parameter_group   = "default.redis7"
  
  tags = {
    Name = "cina-club-staging-redis"
  }
} 