# 新开发者入职指南

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

欢迎加入CINA.CLUB开发团队！本文档将帮助您快速了解项目架构、开发流程和团队文化。

## 🎯 第一天：项目概览

### 1. 项目介绍

**CINA.CLUB** 是一个Go-Centric的全栈智能生活平台，具有以下特点：

- **核心理念**: "Shared Core, Native Shell" - 共享核心逻辑，原生应用外壳
- **架构模式**: 微服务 + Go核心库 + 多平台客户端
- **技术栈**: Go后端 + React/TypeScript前端 + 原生移动应用
- **安全特性**: 端到端加密 + 零知识架构

### 2. 产品功能

| 模块 | 功能描述 | 技术实现 |
|------|----------|----------|
| **用户中心** | 认证、授权、个人资料管理 | Go + JWT + 生物识别 |
| **智能聊天** | AI助手、实时通信 | WebSocket + 本地AI模型 |
| **个人知识库** | 端到端加密笔记、智能搜索 | AES-256 + 向量搜索 |
| **服务市场** | 第三方服务集成、支付 | 微服务架构 |
| **数据同步** | 跨设备数据同步、冲突解决 | CRDT算法 |

### 3. 团队结构

```mermaid
graph TB
    CTO[技术总监]
    CTO --> BackendLead[后端负责人]
    CTO --> FrontendLead[前端负责人]
    CTO --> MobileLead[移动端负责人]
    CTO --> DevOpsLead[运维负责人]
    
    BackendLead --> GoDevs[Go开发工程师]
    FrontendLead --> WebDevs[Web开发工程师]
    MobileLead --> iOSDevs[iOS开发工程师]
    MobileLead --> AndroidDevs[Android开发工程师]
    MobileLead --> HarmonyDevs[HarmonyOS开发工程师]
    DevOpsLead --> SREDevs[SRE工程师]
```

## 📚 第一周：技术学习

### Day 1-2: 架构理解

**必读文档：**
- [系统架构文档](../architecture/cina.club-monorepo-arch.md)
- [API设计文档](../srs/core/core-api-srs.md)
- [安全架构文档](../architecture/L3_Core_Capabilities_Deep_Dive/security-architecture.md)

**实践任务：**
```bash
# 1. 环境搭建
git clone https://github.com/cina-club/monorepo.git
cd monorepo
make setup-dev

# 2. 运行项目
make dev

# 3. 访问服务
curl http://localhost:8080/health
```

### Day 3-4: 代码熟悉

**代码结构概览：**
```
monorepo/
├── core/           # Go核心库 (加密、AI、数据同步)
├── services/       # 微服务集合
├── pkg/           # 共享包
├── apps/          # 客户端应用
├── infra/         # 基础设施代码
├── scripts/       # 自动化脚本
└── docs/          # 项目文档
```

**重点模块：**
1. **core/crypto**: 端到端加密实现
2. **core/aic**: AI推理引擎
3. **services/user-core-service**: 用户核心服务
4. **apps/admin**: 管理后台
5. **pkg/auth**: 认证授权包

### Day 5: 开发流程

**Git工作流：**
```bash
# 1. 创建功能分支
git checkout -b feature/your-name/feature-description

# 2. 开发并提交
git add .
git commit -m "feat(scope): description"

# 3. 推送并创建PR
git push origin feature/your-name/feature-description
```

**代码审查流程：**
1. 自检：运行 `make lint` 和 `make test`
2. 创建PR：使用PR模板
3. 等待审查：至少需要2个approve
4. 合并：使用squash merge

## 🛠️ 第二周：实战项目

### 入门任务

选择一个适合您技能的入门任务：

**后端开发者：**
```go
// 任务：为用户服务添加用户偏好设置API
// 文件：services/user-core-service/internal/handlers/preferences.go

func (h *UserHandler) UpdatePreferences(c *gin.Context) {
    // TODO: 实现用户偏好设置更新逻辑
    // 1. 验证请求参数
    // 2. 更新数据库
    // 3. 返回响应
}
```

**前端开发者：**
```typescript
// 任务：实现用户偏好设置页面
// 文件：apps/admin/src/pages/settings/Preferences.tsx

interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
  notifications: boolean;
}

export const PreferencesPage: React.FC = () => {
  // TODO: 实现偏好设置页面
  // 1. 获取当前设置
  // 2. 提供编辑界面
  // 3. 保存更改
};
```

**移动端开发者：**
```kotlin
// 任务：实现HarmonyOS偏好设置页面
// 文件：apps/harmony/entry/src/main/ets/pages/settings/PreferencesPage.ets

@Entry
@Component
struct PreferencesPage {
  // TODO: 实现偏好设置页面
  // 1. 数据绑定
  // 2. UI交互
  // 3. 数据持久化
}
```

### 导师制度

**导师分配：**
- 每位新员工分配一位技术导师
- 导师负责技术指导和代码审查
- 定期一对一会议（每周2次）

**导师职责：**
1. 技术指导和答疑
2. 代码审查和建议
3. 职业发展规划
4. 团队文化传承

## 🏢 团队文化

### 核心价值观

1. **技术卓越**: 追求代码质量和技术创新
2. **用户第一**: 以用户体验为中心
3. **团队合作**: 知识共享和互相帮助
4. **持续学习**: 拥抱新技术和最佳实践

### 工作方式

**敏捷开发：**
- 2周一个Sprint
- 每日站会（10分钟）
- Sprint计划会议
- 回顾和总结会议

**沟通协作：**
- **Slack**: 日常沟通
- **Jira**: 任务管理
- **Confluence**: 知识分享
- **GitHub**: 代码协作

### 学习成长

**技术分享：**
- 每周技术分享会
- 月度技术论坛
- 季度技术大会

**培训资源：**
- 在线课程报销
- 技术图书购买
- 技术会议参加

## 📋 检查清单

### 第一天完成项

- [ ] 完成入职手续
- [ ] 获得开发环境访问权限
- [ ] 设置开发工具和账号
- [ ] 阅读项目概览文档
- [ ] 与团队成员见面

### 第一周完成项

- [ ] 完成开发环境搭建
- [ ] 阅读核心技术文档
- [ ] 熟悉代码结构
- [ ] 完成第一个PR
- [ ] 参加团队会议

### 第二周完成项

- [ ] 完成入门任务
- [ ] 通过代码审查
- [ ] 参与技术分享
- [ ] 与导师建立联系
- [ ] 制定个人学习计划

### 第一个月完成项

- [ ] 独立完成完整功能
- [ ] 熟悉部署流程
- [ ] 参与架构讨论
- [ ] 提出改进建议
- [ ] 帮助新同事入职

## 📞 联系方式

### 技术支持

- **技术总监**: <EMAIL>
- **后端负责人**: <EMAIL>
- **前端负责人**: <EMAIL>
- **移动端负责人**: <EMAIL>
- **运维负责人**: <EMAIL>

### 行政支持

- **HR部门**: <EMAIL>
- **IT支持**: <EMAIL>
- **法务事务**: <EMAIL>

### 紧急联系

- **24小时技术支持**: +86-400-123-4567
- **安全事件报告**: <EMAIL>

## 📖 推荐学习资源

### 技术书籍

**Go语言：**
- 《Go语言实战》
- 《Go语言高级编程》
- 《Effective Go》

**系统设计：**
- 《设计数据密集型应用》
- 《微服务架构设计模式》
- 《高性能MySQL》

**前端开发：**
- 《React技术揭秘》
- 《TypeScript编程》
- 《现代前端技术解析》

### 在线课程

- **Coursera**: 计算机科学课程
- **edX**: MIT和哈佛公开课
- **极客时间**: 中文技术课程
- **YouTube**: 技术频道订阅

### 技术社区

- **GitHub**: 开源项目贡献
- **Stack Overflow**: 技术问答
- **掘金**: 中文技术社区
- **Medium**: 技术博客阅读

## 🎉 欢迎加入

恭喜您加入CINA.CLUB大家庭！我们相信您将在这里：

- 🚀 参与构建下一代智能生活平台
- 💡 学习最前沿的技术和架构
- 🤝 与优秀的同事一起成长
- 🌟 实现个人技术价值

如果您有任何问题或建议，随时联系我们。期待与您一起创造未来！

---

**Welcome to CINA.CLUB! Let's build the future together!** 🎊 