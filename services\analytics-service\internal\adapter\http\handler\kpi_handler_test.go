// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-15 12:00:00
// Modified: 2025-01-15 12:00:00

package handler

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/analytics-service/internal/domain/model"
)

// MockKPIService is a simple mock for testing
type MockKPIService struct {
	GetDailySummaryFunc     func(ctx context.Context, date time.Time) (*model.KPIMetrics, error)
	GetPlatformOverviewFunc func(ctx context.Context) (*model.PlatformOverview, error)
	GetGrowthMetricsFunc    func(ctx context.Context, timeRange model.TimeRange) (*model.GrowthMetrics, error)
}

func (m *MockKPIService) GetDailySummary(ctx context.Context, date time.Time) (*model.KPIMetrics, error) {
	if m.GetDailySummaryFunc != nil {
		return m.GetDailySummaryFunc(ctx, date)
	}
	return nil, nil
}

func (m *MockKPIService) GetPlatformOverview(ctx context.Context) (*model.PlatformOverview, error) {
	if m.GetPlatformOverviewFunc != nil {
		return m.GetPlatformOverviewFunc(ctx)
	}
	return nil, nil
}

func (m *MockKPIService) GetGrowthMetrics(ctx context.Context, timeRange model.TimeRange) (*model.GrowthMetrics, error) {
	if m.GetGrowthMetricsFunc != nil {
		return m.GetGrowthMetricsFunc(ctx, timeRange)
	}
	return nil, nil
}

func TestKPIHandler_GetDailySummary(t *testing.T) {
	gin.SetMode(gin.TestMode)

	expectedKPI := &model.KPIMetrics{
		Date:               time.Date(2025, 1, 14, 0, 0, 0, 0, time.UTC),
		DailyActiveUsers:   1000,
		MonthlyActiveUsers: 15000,
		NewUsers:           50,
		TotalUsers:         100000,
		Revenue:            decimal.NewFromFloat(10000.50),
		Orders:             200,
		ConversionRate:     0.15,
		RetentionRate:      0.85,
	}

	t.Run("Success with date parameter", func(t *testing.T) {
		mockService := &MockKPIService{
			GetDailySummaryFunc: func(ctx context.Context, date time.Time) (*model.KPIMetrics, error) {
				assert.Equal(t, time.Date(2025, 1, 14, 0, 0, 0, 0, time.UTC), date)
				return expectedKPI, nil
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/daily-summary", handler.GetDailySummary)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/daily-summary?date=2025-01-14", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.True(t, response["success"].(bool))
		assert.NotNil(t, response["data"])
	})

	t.Run("Success with default date", func(t *testing.T) {
		mockService := &MockKPIService{
			GetDailySummaryFunc: func(ctx context.Context, date time.Time) (*model.KPIMetrics, error) {
				// Should be yesterday's date
				yesterday := time.Now().AddDate(0, 0, -1)
				assert.Equal(t, yesterday.Format("2006-01-02"), date.Format("2006-01-02"))
				return expectedKPI, nil
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/daily-summary", handler.GetDailySummary)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/daily-summary", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Error with invalid date format", func(t *testing.T) {
		mockService := &MockKPIService{}
		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/daily-summary", handler.GetDailySummary)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/daily-summary?date=invalid-date", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "Invalid date format", response["error"])
	})

	t.Run("Error from service", func(t *testing.T) {
		mockService := &MockKPIService{
			GetDailySummaryFunc: func(ctx context.Context, date time.Time) (*model.KPIMetrics, error) {
				return nil, errors.New("database error")
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/daily-summary", handler.GetDailySummary)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/daily-summary?date=2025-01-14", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "Internal server error", response["error"])
	})
}

func TestKPIHandler_GetPlatformOverview(t *testing.T) {
	gin.SetMode(gin.TestMode)

	expectedOverview := &model.PlatformOverview{
		TotalUsers:         100000,
		TotalServices:      5000,
		TotalProviders:     1000,
		TotalRevenue:       decimal.NewFromFloat(1000000),
		AverageOrderValue:  decimal.NewFromFloat(150.75),
		PlatformGrowthRate: 0.25,
	}

	t.Run("Success", func(t *testing.T) {
		mockService := &MockKPIService{
			GetPlatformOverviewFunc: func(ctx context.Context) (*model.PlatformOverview, error) {
				return expectedOverview, nil
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/platform-overview", handler.GetPlatformOverview)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/platform-overview", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.True(t, response["success"].(bool))
		assert.NotNil(t, response["data"])
	})

	t.Run("Error from service", func(t *testing.T) {
		mockService := &MockKPIService{
			GetPlatformOverviewFunc: func(ctx context.Context) (*model.PlatformOverview, error) {
				return nil, errors.New("database error")
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/platform-overview", handler.GetPlatformOverview)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/platform-overview", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "Internal server error", response["error"])
	})
}

func TestKPIHandler_GetGrowthMetrics(t *testing.T) {
	gin.SetMode(gin.TestMode)

	expectedMetrics := &model.GrowthMetrics{
		TimeRange: model.TimeRange{
			StartDate: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			EndDate:   time.Date(2025, 1, 31, 0, 0, 0, 0, time.UTC),
			Period:    "daily",
		},
		UserGrowth: model.GrowthRate{
			Current:    decimal.NewFromFloat(1000),
			Previous:   decimal.NewFromFloat(900),
			GrowthRate: 0.11,
			Trend:      "up",
		},
	}

	t.Run("Success with parameters", func(t *testing.T) {
		mockService := &MockKPIService{
			GetGrowthMetricsFunc: func(ctx context.Context, timeRange model.TimeRange) (*model.GrowthMetrics, error) {
				assert.Equal(t, "2025-01-01", timeRange.StartDate.Format("2006-01-02"))
				assert.Equal(t, "2025-01-31", timeRange.EndDate.Format("2006-01-02"))
				assert.Equal(t, "daily", timeRange.Period)
				return expectedMetrics, nil
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/growth-metrics", handler.GetGrowthMetrics)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/growth-metrics?start_date=2025-01-01&end_date=2025-01-31&period=daily", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.True(t, response["success"].(bool))
		assert.NotNil(t, response["data"])
	})

	t.Run("Success with default parameters", func(t *testing.T) {
		mockService := &MockKPIService{
			GetGrowthMetricsFunc: func(ctx context.Context, timeRange model.TimeRange) (*model.GrowthMetrics, error) {
				// Should use last 30 days
				assert.Equal(t, "daily", timeRange.Period)
				return expectedMetrics, nil
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/growth-metrics", handler.GetGrowthMetrics)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/growth-metrics", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Error with invalid date format", func(t *testing.T) {
		mockService := &MockKPIService{}
		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/growth-metrics", handler.GetGrowthMetrics)

		// Test with both parameters to ensure proper validation
		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/growth-metrics?start_date=invalid-date&end_date=2025-01-31", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Error from service", func(t *testing.T) {
		mockService := &MockKPIService{
			GetGrowthMetricsFunc: func(ctx context.Context, timeRange model.TimeRange) (*model.GrowthMetrics, error) {
				return nil, errors.New("database error")
			},
		}

		handler := NewKPIHandler(mockService)
		router := gin.New()
		router.GET("/api/v1/analytics/kpis/growth-metrics", handler.GetGrowthMetrics)

		req, _ := http.NewRequest("GET", "/api/v1/analytics/kpis/growth-metrics?start_date=2025-01-01&end_date=2025-01-31", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestNewKPIHandler(t *testing.T) {
	mockService := &MockKPIService{}
	handler := NewKPIHandler(mockService)

	assert.NotNil(t, handler)
	assert.Equal(t, mockService, handler.kpiService)
}
