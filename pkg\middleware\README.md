# CINA.CLUB Middleware Package

CINA.CLUB 微服务架构的标准 gRPC 中间件包，提供统一的请求处理流程，包括 panic 恢复、分布式追踪、日志记录、指标收集和请求验证等功能。

## 🚀 特性

- **🛡️ Panic 恢复**: 自动捕获并处理 panic，防止服务崩溃
- **🔍 分布式追踪**: 基于 OpenTelemetry 的标准化追踪支持
- **📝 结构化日志**: 自动记录请求日志并注入上下文信息
- **📊 Prometheus 指标**: 收集 RED 指标（请求率、错误率、延迟）
- **✅ 请求验证**: 自动验证 Protobuf 消息
- **🔧 可配置**: 灵活的配置选项和预设
- **⚡ 高性能**: 优化的性能，最小化开销
- **🧪 测试友好**: 完整的测试覆盖和 Mock 支持

## 📦 安装

```bash
go get pkg/middleware
```

## 🏃 快速开始

### 最简单的使用方式

```go
package main

import (
    "log/slog"
    "os"
    
    "google.golang.org/grpc"
    "pkg/middleware"
)

func main() {
    logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
    
    // 一行代码获取标准拦截器链
    unaryInterceptors, streamInterceptors := middleware.QuickStart("my-service", logger)
    
    server := grpc.NewServer(
        grpc.ChainUnaryInterceptor(unaryInterceptors...),
        grpc.ChainStreamInterceptor(streamInterceptors...),
    )
    
    // 注册你的服务...
    // server.Serve(listener)
}
```

### 使用流式构建器

```go
package main

import (
    "log/slog"
    "os"
    
    "github.com/prometheus/client_golang/prometheus"
    "google.golang.org/grpc"
    "pkg/middleware"
)

func main() {
    logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
    
    // 使用流式 API 构建拦截器链
    chain := middleware.NewSimpleChain("my-service", logger).
        WithRecovery(true).
        WithTracing(true).
        WithLogging(true).
        WithMetrics(true).
        WithValidation(true).
        WithMetricsRegisterer(prometheus.DefaultRegisterer)
    
    server := grpc.NewServer(
        grpc.ChainUnaryInterceptor(chain.BuildUnary()...),
        grpc.ChainStreamInterceptor(chain.BuildStream()...),
    )
    
    // 注册你的服务...
}
```

### 高级配置

```go
package main

import (
    "log/slog"
    "os"
    
    "google.golang.org/grpc"
    "pkg/middleware"
)

func main() {
    logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
    
    // 自定义配置
    config := middleware.DefaultChainConfig("my-service", logger)
    
    // 自定义 Recovery 选项
    config.RecoveryOptions = &middleware.RecoveryOptions{
        LogStackTrace: true,
        CustomRecoveryHandler: func(ctx context.Context, r interface{}) error {
            // 自定义 panic 处理逻辑
            return status.Error(codes.Internal, "service temporarily unavailable")
        },
    }
    
    // 自定义 Logging 选项
    config.LoggingOptions = &middleware.LoggingOptions{
        EnableRequestLogging:  true,
        EnableResponseLogging: false,
        ExcludeMethods: []string{
            "/grpc.health.v1.Health/Check",
        },
        IncludeHeaders: []string{
            "user-agent",
            "x-request-id",
        },
    }
    
    // 自定义 Validation 选项
    config.ValidationOptions = &middleware.ValidationOptions{
        SkipValidation: false,
        ExcludeMethods: []string{
            "/grpc.health.v1.Health/Check",
        },
    }
    
    unaryInterceptors := middleware.BuildUnaryChain(config)
    streamInterceptors := middleware.BuildStreamChain(config)
    
    server := grpc.NewServer(
        grpc.ChainUnaryInterceptor(unaryInterceptors...),
        grpc.ChainStreamInterceptor(streamInterceptors...),
    )
}
```

## 🛠️ 中间件详细说明

### 1. Recovery 中间件

自动捕获 panic 并转换为 gRPC 错误，防止服务进程崩溃。

```go
// 基本使用
interceptor := middleware.RecoveryInterceptor(logger)

// 带选项使用
opts := &middleware.RecoveryOptions{
    LogStackTrace: true,
    CustomRecoveryHandler: func(ctx context.Context, r interface{}) error {
        // 自定义处理逻辑
        return status.Error(codes.Internal, "panic occurred")
    },
}
interceptor := middleware.RecoveryInterceptorWithOptions(logger, opts)
```

**特性**:
- 自动捕获所有 panic
- 记录完整的堆栈跟踪
- 转换为标准 gRPC Internal 错误
- 支持自定义恢复处理逻辑

### 2. Tracing 中间件

基于 OpenTelemetry 的分布式追踪支持。

```go
// 基本使用
interceptor := middleware.TracingInterceptor("my-service")

// 带选项使用
opts := &middleware.TracingOptions{
    ServiceName:    "my-service",
    TracerProvider: otel.GetTracerProvider(),
    Propagators:    otel.GetTextMapPropagator(),
    ExcludeMethods: []string{
        "/grpc.health.v1.Health/Check",
    },
}
interceptor := middleware.TracingInterceptorWithOptions(opts)
```

**特性**:
- 自动提取和传播 W3C Trace Context
- 创建或延续 trace span
- 添加标准的 gRPC span 属性
- 支持方法过滤

**工具函数**:
```go
// 获取当前 trace ID
traceID := middleware.GetTraceID(ctx)

// 获取当前 span ID
spanID := middleware.GetSpanID(ctx)

// 添加 span 属性
middleware.AddSpanAttribute(ctx, "user_id", "123")

// 创建子 span
childCtx, span := middleware.StartSpan(ctx, "database-query")
defer span.End()
```

### 3. Logging 中间件

结构化日志记录，自动注入追踪信息。

```go
// 基本使用
interceptor := middleware.LoggingInterceptor(logger)

// 带选项使用
opts := &middleware.LoggingOptions{
    EnableRequestLogging:  true,
    EnableResponseLogging: false,
    LogLevel:              slog.LevelInfo,
    ExcludeMethods: []string{
        "/grpc.health.v1.Health/Check",
    },
    IncludeHeaders: []string{
        "user-agent",
        "x-request-id",
        "x-forwarded-for",
    },
}
interceptor := middleware.LoggingInterceptorWithOptions(logger, opts)
```

**特性**:
- 自动记录请求开始和完成日志
- 注入 trace_id 和 span_id
- 基于错误级别调整日志级别
- 支持自定义包含的请求头
- 将上下文 logger 注入到请求上下文中

**在业务代码中使用上下文 logger**:
```go
func (s *MyService) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.GetUserResponse, error) {
    // 从上下文获取带有追踪信息的 logger
    logger := logger.FromContext(ctx)
    
    logger.InfoContext(ctx, "getting user", slog.String("user_id", req.UserId))
    
    // ... 业务逻辑
    
    return response, nil
}
```

### 4. Metrics 中间件

Prometheus 指标收集。

```go
// 基本使用
metrics := middleware.NewServerMetrics()
metrics.Register(prometheus.DefaultRegisterer)
interceptor := metrics.UnaryServerInterceptor()

// 简化使用
interceptor := middleware.MetricsInterceptor()
```

**收集的指标**:
- `grpc_server_handled_total`: 请求总数（按服务、方法、状态码分组）
- `grpc_server_handling_seconds`: 请求延迟分布
- `grpc_server_requests_in_flight`: 当前活跃请求数
- `grpc_server_request_size_bytes`: 请求大小分布
- `grpc_server_response_size_bytes`: 响应大小分布

### 5. Validation 中间件

自动验证 Protobuf 消息。

```go
// 基本使用
interceptor := middleware.ValidationInterceptor()

// 带选项使用
opts := &middleware.ValidationOptions{
    SkipValidation: false,
    ExcludeMethods: []string{
        "/grpc.health.v1.Health/Check",
    },
    CustomValidator: func(req interface{}) error {
        // 自定义验证逻辑
        return nil
    },
}
interceptor := middleware.ValidationInterceptorWithOptions(opts)
```

**特性**:
- 自动检测实现 `Validator` 接口的消息
- 支持详细验证结果
- 转换验证错误为 InvalidArgument gRPC 错误
- 支持自定义验证逻辑

**在 Protobuf 中启用验证**:
```protobuf
syntax = "proto3";

import "validate/validate.proto";

message CreateUserRequest {
  string name = 1 [(validate.rules).string.min_len = 1];
  string email = 2 [(validate.rules).string.pattern = "^[^@]+@[^@]+\\.[^@]+$"];
  int32 age = 3 [(validate.rules).int32.gte = 0];
}
```

## 🔧 预设配置

### 生产环境

```go
config := middleware.Presets.ProductionChain("my-service", logger)
// 启用所有功能：Recovery + Tracing + Logging + Metrics + Validation
```

### 开发环境

```go
config := middleware.Presets.DevelopmentChain("my-service", logger)
// 启用所有功能，但排除健康检查的日志记录
```

### 测试环境

```go
config := middleware.Presets.TestingChain("my-service", logger)
// 最小功能：Recovery + Logging + Validation（不包括 Metrics 和 Tracing）
```

### 最小配置

```go
config := middleware.Presets.MinimalChain("my-service", logger)
// 只有 Recovery 和 Validation
```

## 📋 拦截器执行顺序

中间件按照以下"洋葱模型"顺序执行：

```
入站请求 → Recovery → Tracing → Logging → Metrics → Validation → 业务逻辑
出站响应 ← Recovery ← Tracing ← Logging ← Metrics ← Validation ← 业务逻辑
```

这个顺序确保了：
1. **Recovery** 在最外层，捕获所有可能的 panic
2. **Tracing** 创建 span，供内层中间件使用
3. **Logging** 可以访问 trace 信息
4. **Metrics** 记录包含追踪信息的指标
5. **Validation** 在最内层，确保业务逻辑收到有效数据

## 🧪 测试支持

### Mock 请求消息

```go
type TestRequest struct {
    Message   string
    ShouldErr bool
}

func (r *TestRequest) Validate() error {
    if r.ShouldErr {
        return errors.New("validation failed")
    }
    return nil
}
```

### 测试拦截器

```go
func TestMyInterceptor(t *testing.T) {
    logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
    interceptor := middleware.RecoveryInterceptor(logger)
    
    ctx := context.Background()
    req := &TestRequest{Message: "test"}
    info := &grpc.UnaryServerInfo{FullMethod: "/test.Service/Method"}
    
    handler := func(ctx context.Context, req interface{}) (interface{}, error) {
        return &TestResponse{Message: "success"}, nil
    }
    
    resp, err := interceptor(ctx, req, info, handler)
    
    assert.NoError(t, err)
    assert.NotNil(t, resp)
}
```

## 🚀 性能优化

### 基准测试结果

```
BenchmarkRecoveryInterceptor-8     1000000    1000 ns/op
BenchmarkValidationInterceptor-8   2000000     500 ns/op
BenchmarkFullChain-8               500000     3000 ns/op
```

### 优化建议

1. **排除健康检查**: 在生产环境中排除健康检查方法的日志记录
2. **合理配置指标**: 根据需要启用/禁用特定指标
3. **验证优化**: 对于频繁调用的方法，考虑禁用验证
4. **日志级别**: 在生产环境中使用适当的日志级别

```go
// 生产环境优化配置
config := middleware.DefaultChainConfig("my-service", logger)

// 排除健康检查的日志和指标
config.LoggingOptions.ExcludeMethods = []string{
    "/grpc.health.v1.Health/Check",
    "/grpc.health.v1.Health/Watch",
}

config.TracingOptions.ExcludeMethods = []string{
    "/grpc.health.v1.Health/Check",
    "/grpc.health.v1.Health/Watch",
}

// 禁用响应日志以提高性能
config.LoggingOptions.EnableResponseLogging = false
```

## 🔍 故障排除

### 常见问题

1. **依赖冲突**: 确保使用兼容的 gRPC 和 OpenTelemetry 版本
2. **指标重复注册**: 避免多次注册相同的 Prometheus 指标
3. **验证失败**: 检查 Protobuf 消息是否正确实现了 `Validate()` 方法
4. **追踪丢失**: 确保正确配置了 OpenTelemetry TracerProvider

### 调试建议

```go
// 启用详细日志
logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
    Level: slog.LevelDebug,
}))

// 测试单个拦截器
interceptor := middleware.ValidationInterceptor()
// 测试你的请求...
```

## 📚 示例

查看 `middleware_test.go` 文件获取更多使用示例和测试用例。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

Copyright (c) 2025 Cina.Club. All rights reserved. 