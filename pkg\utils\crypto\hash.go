/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package crypto provides common cryptographic functions like hashing and password handling.
// This package is for general-purpose cryptography, not for E2EE or ALE which are handled elsewhere.
package crypto

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/hex"
)

// MD5 computes the MD5 hash of the input data and returns it as a hex string.
// Note: MD5 is cryptographically broken and should not be used for security purposes.
// It's included here for compatibility with legacy systems.
//
// Example:
//
//	hash := crypto.MD5([]byte("hello world"))
//	// Returns: "5d41402abc4b2a76b9719d911017c592"
func MD5(data []byte) string {
	h := md5.Sum(data)
	return hex.EncodeToString(h[:])
}

// MD5String is a convenience function that computes the MD5 hash of a string.
//
// Example:
//
//	hash := crypto.MD5String("hello world")
//	// Returns: "5d41402abc4b2a76b9719d911017c592"
func MD5String(s string) string {
	return MD5([]byte(s))
}

// SHA1 computes the SHA1 hash of the input data and returns it as a hex string.
// Note: SHA1 is cryptographically weak and should be avoided for new applications.
//
// Example:
//
//	hash := crypto.SHA1([]byte("hello world"))
//	// Returns: "2aae6c35c94fcfb415dbe95f408b9ce91ee846ed"
func SHA1(data []byte) string {
	h := sha1.Sum(data)
	return hex.EncodeToString(h[:])
}

// SHA1String is a convenience function that computes the SHA1 hash of a string.
func SHA1String(s string) string {
	return SHA1([]byte(s))
}

// SHA256 computes the SHA256 hash of the input data and returns it as a hex string.
// This is the recommended hash function for most applications.
//
// Example:
//
//	hash := crypto.SHA256([]byte("hello world"))
//	// Returns: "b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9"
func SHA256(data []byte) string {
	h := sha256.Sum256(data)
	return hex.EncodeToString(h[:])
}

// SHA256String is a convenience function that computes the SHA256 hash of a string.
func SHA256String(s string) string {
	return SHA256([]byte(s))
}

// SHA512 computes the SHA512 hash of the input data and returns it as a hex string.
//
// Example:
//
//	hash := crypto.SHA512([]byte("hello world"))
func SHA512(data []byte) string {
	h := sha512.Sum512(data)
	return hex.EncodeToString(h[:])
}

// SHA512String is a convenience function that computes the SHA512 hash of a string.
func SHA512String(s string) string {
	return SHA512([]byte(s))
}

// HMACSHA256 computes the HMAC-SHA256 of the given data using the provided key.
// Returns the result as a hex string.
//
// Example:
//
//	mac := crypto.HMACSHA256([]byte("message"), []byte("secret-key"))
func HMACSHA256(data, key []byte) string {
	h := hmac.New(sha256.New, key)
	h.Write(data)
	return hex.EncodeToString(h.Sum(nil))
}

// HMACSHA256String is a convenience function for HMAC-SHA256 with string inputs.
func HMACSHA256String(message, key string) string {
	return HMACSHA256([]byte(message), []byte(key))
}

// HMACSHA512 computes the HMAC-SHA512 of the given data using the provided key.
// Returns the result as a hex string.
func HMACSHA512(data, key []byte) string {
	h := hmac.New(sha512.New, key)
	h.Write(data)
	return hex.EncodeToString(h.Sum(nil))
}

// HMACSHA512String is a convenience function for HMAC-SHA512 with string inputs.
func HMACSHA512String(message, key string) string {
	return HMACSHA512([]byte(message), []byte(key))
}

// VerifyHMACSHA256 verifies that the given MAC is correct for the data and key.
// Uses constant-time comparison to prevent timing attacks.
//
// Example:
//
//	isValid := crypto.VerifyHMACSHA256([]byte("message"), []byte("secret-key"), expectedMAC)
func VerifyHMACSHA256(data, key []byte, expectedMAC string) bool {
	computedMAC := HMACSHA256(data, key)
	return hmac.Equal([]byte(computedMAC), []byte(expectedMAC))
}

// VerifyHMACSHA256String is a convenience function for verifying HMAC-SHA256 with string inputs.
func VerifyHMACSHA256String(message, key, expectedMAC string) bool {
	return VerifyHMACSHA256([]byte(message), []byte(key), expectedMAC)
}
