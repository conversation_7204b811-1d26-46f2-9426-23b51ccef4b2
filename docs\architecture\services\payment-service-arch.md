好的，遵照您的指示。我将为您生成一份专门针对 **`payment-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`payment-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**多支付网关的适配与路由、健壮的订单与退款状态机、与`billing-service`的Saga事务协同，以及作为平台金融交易核心的极致安全、合规与可靠性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `payment-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `payment-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 策略模式(Strategy Pattern) + 状态机模式(State Machine)

## 1. 概述

`payment-service` 是CINA.CLUB平台的**法定货币资金流动网关**，是所有法币交易的**唯一、权威处理者**。其核心挑战在于：
1.  **极高的安全性与合规性**: 必须严格遵守PCI DSS标准，绝不直接处理或存储敏感的持卡人数据。所有与外部支付网关的交互都必须是安全的。
2.  **多网关适配与抽象**: 需要将Stripe, PayPal, 支付宝, 微信支付等功能、API、回调机制都完全不同的支付网关，抽象成一套统一的内部接口。
3.  **交易的原子性与一致性**: 支付、退款等操作必须是原子性的，并需要与上游业务（如`billing-service`）保持最终一致性。
4.  **健壮的状态管理**: 支付订单的生命周期复杂（预授权、捕获、退款、争议），必须通过一个严谨的状态机来管理，确保状态转换的正确性。
5.  **可靠的回调处理**: 必须能可靠、幂等地处理来自支付网关的异步Webhook通知。

本架构设计通过采用**整洁架构**，并结合**策略/适配器模式**来封装不同支付网关的逻辑，以及**状态机模式**来管理订单生命周期，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (支付与回调流程)

```mermaid
graph TD
    subgraph "上游业务服务"
        style "上游业务服务" fill:#eee
        BillingService[billing-service]
    end

    subgraph "PaymentService"
        style PaymentService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[Webhook Endpoint<br/><em>adapter/http</em>]
        C[PaymentService<br/><em>application/service</em>]
        D[PaymentOrderAggregate<br/><em>domain/aggregate</em>]
        E{GatewayStrategyFactory<br/><em>domain/strategy</em>}
        F{Gateway Strategies<br/>(StripeStrategy, PaypalStrategy, ...)}
        G[Provider Adapters<br/>(StripeAdapter, ...)<br/><em>adapter/provider</em>]
        H[Repository<br/><em>adapter/repository</em>]
        I[Kafka Producer<br/><em>adapter/event</em>]
    end

    subgraph "外部支付网关"
        style "外部支付网关" fill:#f3e5f5
        Stripe[Stripe API]
        Paypal[PayPal API]
    end
    
    BillingService -- "1. InitiatePayment" --> A
    A -- "调用" --> C
    C -- "2. Select Gateway" --> E
    E -- "Returns StripeStrategy" --> F
    
    C -- "3. Use Strategy to create payment intent" --> F
    F -- "4. Use Adapter to call API" --> G
    G -- "Calls" --> Stripe
    Stripe -- "Returns client_secret" --> G --> F --> C
    
    C -- "5. Create PaymentOrder (PENDING)" --> D & H
    C -- "6. Return client_secret" --> A --> BillingService
    
    Stripe -- "7. [Webhook] Payment Succeeded" --> B
    B -- "8. Validate & call" --> C
    C -- "9. Load Aggregate & update status" --> D & H
    
    C -- "10. Publish PaymentSucceededEvent" --> I
    I --> Kafka[(Kafka)]
    Kafka --> BillingService
```

### 2.2 最终目录结构 (`services/payment-service/`)

```
payment-service/
├── cmd/server/
│   └── main.go                 # API服务和Webhook服务器启动入口
├── internal/
│   ├── adapter/
│   │   ├── event/
│   │   │   └── producer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   ├── http/
│   │   │   └── webhook_handler.go # ✨ Webhook处理器, 带签名验证 ✨
│   │   ├── provider/           # ✨ 支付网关的适配器实现 ✨
│   │   │   ├── interface.go
│   │   │   ├── stripe/
│   │   │   │   └── stripe_adapter.go
│   │   │   └── paypal/
│   │   │       └── paypal_adapter.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── payment_service.go # 核心应用服务实现
│   └── domain/
│       ├── aggregate/
│       │   └── payment_order_aggregate.go # ✨ 封装支付订单状态机 ✨
│       ├── model/
│       │   └── alias.go
│       └── strategy/
│           ├── interface.go      # 定义GatewayStrategy接口
│           ├── stripe_strategy.go
│           ├── paypal_strategy.go
│           └── factory.go      # GatewayStrategyFactory
├── config/
│   └── payment_gateways.yaml # ✨ 支付网关配置与路由规则 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/payment_gateways.yaml` - 路由的“单一事实来源”

```yaml
gateways:
  - id: "stripe-card-global"
    type: "stripe"
    # credentials loaded from vault
  - id: "alipay-cn"
    type: "alipay"

routing_rules:
  # 对于来自中国大陆、使用CNY的支付请求，优先使用支付宝
  - when:
      country_code: "CN"
      currency: "CNY"
    use: "alipay-cn"
    fallback: "stripe-card-global"
  # 默认规则
  - default:
      use: "stripe-card-global"
```

### 3.2 `domain/` - 领域层 (The Financial Transaction Rules)

*   `domain/model/`: 使用`/core/models`中与支付、订单相关的`struct`。
*   **`domain/strategy/`**: **策略模式的实现，用于适配不同支付网关**。
    *   `interface.go`: 定义`GatewayStrategy`接口。
        ```go
        type GatewayStrategy interface {
            InitiatePayment(...) (*PaymentIntent, error)
            CapturePayment(...) error
            InitiateRefund(...) error
            // ...
        }
        ```
    *   `stripe_strategy.go`, `alipay_strategy.go`: 分别实现`GatewayStrategy`接口，它们内部会调用`adapter/provider`中的具体适配器。策略层负责处理一些通用的逻辑，如参数组合。
    *   `factory.go`: `GatewayStrategyFactory`在启动时加载路由规则，并提供`SelectStrategy(context)`方法来根据请求上下文（地区、货币）返回一个具体的策略实例。
*   **`domain/aggregate/payment_order_aggregate.go`**: **这是管理订单生命周期的核心**。
    *   **`PaymentOrder`聚合根**: 封装了`PaymentOrder`实体及其所有的`PaymentTransaction`。
    *   **状态机方法**: **所有状态变更都必须通过聚合根的方法进行**。
        *   `Authorize()`: 将状态从`PENDING`变为`AUTHORIZED`。
        *   `HandlePaymentSuccess()`: 将状态变为`SUCCEEDED`。
        *   `HandlePaymentFailure()`: 将状态变为`FAILED`。
        *   `Refund(amount)`: 检查当前是否可退款，创建`RefundTransaction`，并更新状态为`PARTIALLY_REFUNDED`或`REFUNDED`。
        *   每个方法内部都包含严格的前置条件检查，如“只有在`AUTHORIZED`状态下才能`Capture`”。

### 3.3 `application/` - 应用层 (The Payment Orchestrator)

*   **`application/service/payment_service.go`**: 实现`PaymentService`接口，是所有业务流程的编排者。
    *   **`InitiatePayment(ctx, request)`**:
        1.  调用`gatewayStrategyFactory.SelectStrategy()`选择支付网关策略。
        2.  调用`strategy.InitiatePayment()`与外部网关交互，获取`client_secret`等信息。
        3.  创建一个`PaymentOrder`聚合根，初始状态为`PENDING`或`AWAITING_USER_ACTION`。
        4.  调用`repository.CreatePaymentOrder()`持久化。
        5.  返回支付所需参数给上游服务。
    *   **`HandleWebhook(ctx, gatewayType, payload)`**:
        1.  **这是一个幂等操作**。首先根据`payload`中的外部交易ID，检查`payment_transactions`表中是否已处理过该事件。
        2.  使用`gatewayStrategyFactory`获取对应的策略，并调用策略的`ParseWebhook(payload)`方法，将异构的webhook内容解析为统一的内部领域事件（如`PaymentSucceeded`, `RefundFailed`）。
        3.  根据解析出的事件，加载对应的`PaymentOrder`聚合根。
        4.  **调用聚合根的状态机方法**（如`order.HandlePaymentSuccess()`）来更新状态。
        5.  持久化变更后的聚合根。
        6.  **发布平台内部的领域事件**（如`PaymentSucceededEvent`）到Kafka，通知上游服务。

### 3.4 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/provider/`**: **适配器模式，封装SDK调用**。
    *   `interface.go`: 定义`StripeClient`, `AlipayClient`等接口。
    *   `stripe_adapter.go`: 封装所有对Stripe Go SDK的直接调用。负责处理API Key认证、HTTP请求和具体错误码的转换。
*   **`adapter/repository/`**:
    *   使用**PostgreSQL**。所有金融操作必须在**ACID事务**中进行。
    *   实现`port.Repository`接口。
*   **`adapter/http/webhook_handler.go`**:
    *   **安全第一**: **必须**实现对Webhook请求的**签名验证**。每个支付网关的验证逻辑都不同。
    *   **只做最少的事**: 验证签名后，立即将合法的`payload`推送到一个内部的**Asynq任务队列**进行异步处理，并马上返回`200 OK`给支付网关。这防止了因处理逻辑耗时而导致网关重试。
*   **`adapter/event/`**:
    *   `producer.go`: 封装`pkg/messaging`，用于向Kafka发布`PaymentSucceededEvent`等内部领域事件。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`payment-service`：
1.  **双重抽象层**:
    *   **策略层(`domain/strategy`)**: 负责“业务层”的适配，如决定用哪个网关。
    *   **适配器层(`adapter/provider`)**: 负责“技术层”的适配，如封装具体的SDK调用。
    *   这种分离使得系统极度灵活，添加新网关只需新增一个适配器和一个策略即可。
2.  **状态机模式**: 将复杂的订单生命周期封装在`PaymentOrder`聚合根中，通过其方法来驱动状态的流转，确保了所有状态变更都符合预定义的业务规则，避免了状态不一致。
3.  **可靠的异步处理**: 通过Webhook + 任务队列(Asynq)的模式，确保了对外部异步通知的可靠、幂等处理，同时保证了API的快速响应。
4.  **安全合规**: 严格遵循PCI DSS，不触碰敏感数据。将所有与外部的交互都隔离在适配层，并对Webhook进行签名验证，保证了系统的安全性。
5.  **Saga参与者**: 作为Saga事务的一个可靠参与者，通过gRPC API接收指令，并通过向Kafka发布事件来通知上游业务流程的下一步。

这种架构确保了`payment-service`能够以一种**安全、可靠、合规且高度可扩展**的方式，处理CINA.CLUB平台所有的法币交易，是平台商业化成功的关键金融基础设施。