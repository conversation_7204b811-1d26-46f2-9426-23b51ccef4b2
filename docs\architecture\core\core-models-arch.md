好的，遵照您的指示。我将为您生成一份专门针对 **`core/models`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`core/models` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到**领域驱动设计(DDD)的理念、贫血模型的选择、与Protobuf消息的界限，以及如何设计一套稳定、清晰、高度可复用的核心业务实体**，为所有开发团队提供一份清晰、可执行的“通用业务语言”定义。

---
### CINA.CLUB - `core/models` 内部架构设计

**文档状态**: 设计完成 (Designed) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `core/models-srs.md` (v1.0)
**核心架构**: 领域驱动设计(DDD)的贫血领域模型

## 1. 概述

`core/models` 是CINA.CLUB Monorepo中**业务概念的“单一事实来源”(SSoT)**。它不是一个功能库，而是一个**定义库**，其唯一的职责是用**纯Go `struct`**来精确地、无歧义地描述平台的核心业务实体和值对象。其架构设计的核心目标是：
1.  **统一业务语言 (Ubiquitous Language)**: 为所有开发者（后端、前端核心逻辑）提供一套共同的、关于核心业务概念的词汇表。当谈论`User`时，所有人脑海中都是同一个`core/models.User`结构。
2.  **绝对的纯粹性与稳定性**: 模型**必须**与任何技术实现（数据库、API框架、序列化格式）完全解耦。它们是平台最稳定的代码，变更成本极高。
3.  **清晰的边界**: 明确区分领域模型(`core/models`)、持久化模型(`services/.../repository/model.go`)和API模型(`core/api/.../*.proto`)。
4.  **可组合性**: 模型应设计为可被其他模型组合引用，形成一个连贯的业务领域网络。

本架构设计通过采用**贫血领域模型 (Anemic Domain Model)** 的方法，将数据结构与业务行为逻辑分离，来构建一个清晰、稳定、易于理解的核心模型层。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (模型在系统中的流转与转换)

```mermaid
graph TD
    subgraph "core/api (Communication Layer)"
        A[Protobuf Messages<br/>(e.g., user_v1.User)]
    end
    
    subgraph "services/* (Business Logic Layer)"
        B[gRPC Handler]
        C[Application Service]
        D[Repository Implementation]
    end

    subgraph "core/models (Domain Model Layer)"
        style "core/models (Domain Model Layer)" fill:#e0f7fa
        E[Domain Model Structs<br/>(e.g., models.User)]
    end

    subgraph "services/*/adapter/repository (Persistence Layer)"
        style "services/*/adapter/repository (Persistence Layer)" fill:#f3e5f5
        F[Persistence Model Structs<br/>(e.g., repo.UserDB)]
    end
    
    subgraph "Database"
        G[(PostgreSQL/MongoDB)]
    end

    A -- "1. 转换为" --> E
    B -- "调用" --> C
    
    C -- "2. 操作" --> E
    
    C -- "3. 调用仓储接口, 传递" --> D
    E -- " " --> D
    
    D -- "4. 转换为" --> F
    F -- "5. 写入" --> G
    
    G -- "6. 读取" --> F
    F -- "7. 转换为" --> E
    D -- "返回" --> C
```
**数据流转核心**: **`core/models`是业务逻辑层的“内部货币”**。所有外部数据（来自API的Protobuf，来自DB的持久化模型）在进入业务逻辑层之前，都**必须**被转换为`core/models`中的领域模型。

### 2.2 最终目录结构 (`core/models/`)

```
core/models/
├── user.go         # 用户、认证、成长、会员、权限
├── social.go       # 社交图谱 (关注、好友、拉黑)
├── family.go       # 族谱
├── content.go      # 通用内容原语 (帖子, 问答, 视频, CKB/PKB)
├── service.go      # 服务市场与交易 (产品, 项目, 订单, 预订)
├── payment.go      # 计费与支付 (发票, 订阅, 支付订单)
├── asset.go        # 通用资产模型 (Avatar资产, 游戏化物品)
├── time.go         # 时间与日程 (日历, 事件, 重复规则)
├── geo.go          # 地理位置 (点, 多边形)
└── model.go        # (可选) 定义通用的基础接口, 如`Entity`
```
**设计决策**: 按**业务领域**将高度相关的模型组织在同一个文件中。这使得开发者在处理某个领域的业务时，可以方便地找到所有相关的核心数据结构。

---

## 3. 各模型文件职责与实现细节

### 3.1 `user.go` - 用户域模型
*   **职责**: 定义与用户身份、状态和成长相关的一切。
*   **核心`struct`**:
    ```go
    // User 是用户聚合根的根实体
    type User struct {
        ID          uuid.UUID
        Username    string
        Status      UserStatus // 使用枚举类型
        // ...
    }
    
    // UserProfile 是一个值对象, 描述用户的可变资料
    type UserProfile struct { ... }

    // UserStatus 是一个枚举类型
    type UserStatus string
    const (
        UserStatusActive          UserStatus = "ACTIVE"
        UserStatusSuspended       UserStatus = "SUSPENDED"
        UserStatusPendingDeletion UserStatus = "PENDING_DELETION"
    )
    ```

### 3.2 `content.go` - 内容域模型
*   **职责**: 定义所有UGC和PGC的通用结构。
*   **核心`struct`**:
    ```go
    // Block 是内容的基本单元, 类似于Notion的Block
    type Block struct {
        ID      string
        Type    BlockType // PARAGRAPH, IMAGE, ...
        Content interface{} // 使用interface{}来容纳不同类型块的数据
    }

    // RichContent 代表一个由多个Block组成的富文本文档
    type RichContent struct {
        Blocks []Block
    }
    ```
    **设计决策**: 采用**块编辑器(Block Editor)**的数据模型，提供了极高的灵活性和可扩展性，便于前端渲染和后端处理。

### 3.3 `payment.go` - 支付域模型
*   **职责**: 定义与财务、计费、订阅相关的核心概念。
*   **核心`struct`**:
    ```go
    // Money 是一个值对象, 避免了浮点数精度问题
    type Money struct {
        Amount   int64  // 使用最小货币单位, e.g., 分
        Currency string // e.g., "CNY", "USD"
    }

    type Subscription struct {
        ID        uuid.UUID
        UserID    uuid.UUID
        Status    SubscriptionStatus
        ExpiresAt time.Time
        // ...
    }
    ```
    **设计决策**: 对金额等需要精确计算的业务概念，使用专门的**值对象(Value Object)**（如`Money`）来封装，而不是直接使用基本类型。

### 3.4 模型方法的规范
*   `core/models`中的`struct`可以包含方法，但这些方法**必须是纯粹的、无副作用的查询或计算**。
*   **允许的示例**:
    ```go
    func (u *User) IsActive() bool {
        return u.Status == UserStatusActive
    }
    func (m *Money) Add(other Money) (Money, error) {
        if m.Currency != other.Currency {
            return Money{}, errors.New("currency mismatch")
        }
        return Money{Amount: m.Amount + other.Amount, ...}, nil
    }
    ```
*   **禁止的示例**:
    ```go
    // 👎 错误: 包含业务逻辑和状态变更
    func (s *Subscription) Cancel(repo UserRepository) error { ... }

    // 👎 错误: 包含I/O操作
    func (u *User) SaveToDB(db *sql.DB) error { ... }
    ```

---

## 4. 总结与开发规范

本架构设计通过以下关键点来构建一个生产级的`core/models`：
1.  **贫血模型**: 严格将数据（在`core/models`）与行为（在`services/*/domain/aggregate`或`service`）分离。这使得核心模型极其稳定，而复杂的业务逻辑可以在各自的服务中独立演进。
2.  **与持久化和API解耦**: **强制规定**`core/models`的`struct`不能包含任何技术细节的标签。这保证了核心领域模型的纯粹性，是实现“依赖倒置”和“整洁架构”的根本。
3.  **按领域组织**: 清晰的文件组织结构，使得开发者可以快速地理解和找到特定业务领域的核心概念。
4.  **使用值对象和枚举**: 对于有业务含义的非实体数据（如`Money`, `UserStatus`），使用自定义的类型或枚举，增强了代码的类型安全性和可读性。

**开发规范**:
*   **第一原则**: 在向`core/models`添加或修改任何模型之前，开发者必须自问：“这个概念是否是平台级的、跨多个服务共享的？它是否足够纯粹，不包含任何技术实现细节？”
*   **PR审查**: 对`core/models`的任何修改，都必须被视为一次**重大架构变更**，需要经过**架构师团队和所有受影响服务的主要负责人**共同审查和批准。
*   **文档**: 每个`struct`和`field`都必须有清晰的GoDoc注释，解释其业务含义。

通过这套严谨的设计和规范，`core/models`将成为CINA.CLUB平台坚不可摧的业务基石，为所有上层应用的构建提供了一个稳定、一致、易于理解的“通用语言”。