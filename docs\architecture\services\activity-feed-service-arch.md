好的，遵照您的指示。我将为您生成一份专门针对 **`activity-feed-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将基于您提供的`activity-feed-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**事件驱动、高吞吐量写入、时间线数据模型、智能聚合逻辑以及与实时推送的协同**等核心挑战，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `activity-feed-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `activity-feed-service-srs.md` (v2.0)
**核心架构**: 事件驱动 + 时间线数据模型

## 1. 概述

`activity-feed-service` 负责构建和管理用户的多维活动流（通知、互动、关注）。其核心挑战在于：
1.  **高并发写入**: 需要能消费来自整个平台所有微服务的海量领域事件。
2.  **时间线数据建模**: 数据以用户ID和时间为核心进行组织，读写模式非常特殊（写多、按用户分页读）。
3.  **智能聚合与降噪**: 需要高效地实现“N人赞了你”这类聚合逻辑，提升信息质量。
4.  **实时性**: 需与`chat-websocket-server`协同，实现未读数的实时更新（小红点）。
5.  **数据存储选型**: 关系型数据库难以应对这种时间线模型的写入压力和数据量。

本架构设计旨在解决上述挑战，构建一个高性能、高可用、用户体验优秀的信息聚合与历史追溯中心。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (事件驱动的管道)

```mermaid
graph TD
    subgraph "平台事件总线 (Kafka)"
        A[Platform Events<br/>(e.g., ForumPostLikedEvent)]
    end
    
    subgraph "ActivityFeedService"
        B[Kafka Consumer<br/>(adapter/event)]
        C[Event Dispatcher<br/>(application/dispatcher)]
        D{Feed Handlers<br/>(application/handlers)}
        E[Aggregation Logic<br/>(domain/aggregator)]
        F[NoSQL Database<br/>(adapter/repository)]
        G[Redis Cache<br/>(adapter/cache)]
        H[API Server (gRPC)<br/>(adapter/grpc)]
    end

    subgraph "下游服务"
        I[chat-websocket-server]
        J[Client App]
    end

    A --> B
    B --> C
    C -- "Route by eventType" --> D
    D -- "Generate ActivityItem" --> E
    E -- "Use" --> G
    E -- "Write/Update" --> F
    
    H -- "Query feeds" --> F
    H -- "Query unread counts" --> G

    F -- "Write-through" --> G
    E -- "Publish UnreadCountChanged" --> Kafka
    Kafka --> I -- "Push real-time update" --> J
```
### 2.2 最终目录结构 (`services/activity-feed-service/`)

```
activity-feed-service/
├── cmd/server/
│   └── main.go                 # 服务启动入口 (启动API服务器和事件消费者)
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   ├── server.go
│   │   │   └── handler.go      # gRPC Handler, 调用application/query_service
│   │   ├── event/
│   │   │   └── consumer.go     # Kafka消费者, 调用application/dispatcher
│   │   ├── cache/
│   │   │   └── redis_cache.go  # 实现了Cache接口 (未读数, 聚合追踪)
│   │   └── repository/
│   │       ├── model.go        # NoSQL数据库的实体模型
│   │       └── nosql_repo.go   # 实现了Repository接口 (Cassandra/MongoDB)
│   ├── application/
│   │   ├── port/
│   │   │   ├── cache.go
│   │   │   └── repository.go
│   │   ├── dispatcher.go       # 事件分发器, 核心路由逻辑
│   │   ├── query_service.go    # 查询服务, 处理读请求
│   │   └── handlers/           # ✨ 每个事件类型的具体处理器 ✨
│   │       ├── interface.go    # 定义FeedHandler接口
│   │       ├── interaction_handler.go # 处理点赞、评论等
│   │       ├── notification_handler.go # 处理系统通知等
│   │       └── factory.go      # 根据事件类型创建对应的Handler
│   └── domain/
│       ├── model/              # 核心领域模型 (ActivityFeedItem, UnreadCount)
│       └── aggregator.go       # ✨ 智能聚合核心逻辑 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Core Rules)

*   `domain/model/`: 定义`ActivityFeedItem`这个核心领域对象，它是一个标准化的、与源事件无关的活动条目结构。包含`FeedType`, `ActivityType`, `Actors`, `Target`等字段。
*   **`domain/aggregator.go`**: **这是本服务智能降噪的核心**。
    *   **`Aggregator` struct**: 这是一个无状态的逻辑处理器。
    *   **`AggregateOrNew(item ActivityFeedItem)` method**:
        1.  接收一个新生成的`ActivityFeedItem`。
        2.  从`item`的元数据中，构造一个用于聚合的**唯一键 (Aggregation Key)**。例如，对于点赞，key可能是`like:{target_type}:{target_id}`。
        3.  调用`Cache`接口，检查在时间窗口内（如5分钟）是否存在一个正在进行中的、针对此key的聚合条目ID。
        4.  **如果存在**:
            *   返回“更新”指令，包含已存在的条目ID和需要追加的新参与者信息。
        5.  **如果不存在**:
            *   返回“创建”指令，包含这个新的`ActivityFeedItem`。
            *   同时，调用`Cache`接口，将这个新条目的ID与Aggregation Key关联起来，并设置一个时间窗口的TTL。

### 3.2 `application/` - 应用层 (The Pipeline)

这是事件处理的流水线。

*   **`application/dispatcher.go`**:
    *   `Dispatcher`是事件消费的入口。
    *   它接收来自`adapter/event`的原始Kafka消息。
    *   **核心职责**:
        1.  解析事件，获取`eventType`。
        2.  使用`handlers.Factory`，根据`eventType`获取一个具体的`FeedHandler`。
        3.  调用该handler的`Handle`方法。
*   **`application/handlers/`**: **事件处理逻辑的核心**。
    *   `interface.go`: 定义`FeedHandler`接口，包含`Handle(ctx, eventPayload) error`方法。
    *   `interaction_handler.go`: 实现`FeedHandler`接口。
        *   **`Handle`方法**:
            1.  将`ForumPostLikedEvent`这类具体的事件`payload`，转换为一个或多个标准化的`domain.ActivityFeedItem`。例如，一个点赞事件会为被点赞的用户生成一个`ActivityFeedItem`。
            2.  调用`domain.Aggregator`的`AggregateOrNew`方法，获取“创建”或“更新”指令。
            3.  根据指令，调用`Repository`接口来持久化数据。
            4.  调用`Cache`接口，原子性地增加对应Feed流的未读数。
            5.  调用事件生产者，发布`FeedUnreadCountChangedEvent`。
*   **`application/query_service.go`**:
    *   处理所有读请求（来自gRPC handler）。
    *   `GetFeeds(userID, feedType, page)`: 调用`Repository`接口分页查询。
    *   `GetUnreadSummary(userID)`: 调用`Cache`接口获取所有Feed的未读数。

### 3.3 `adapter/` - 适配层 (The Bridge to Infrastructure)

*   **`adapter/repository/`**: **数据持久化**。
    *   **数据库选型**: **必须**是 **NoSQL数据库**。
        *   **Cassandra/ScyllaDB (首选)**: 性能极高，为时间序列写入优化。`Partition Key`为`user_id`, `Clustering Key`为`created_at` (DESC)，可以实现极高效的分页查询。
        *   **MongoDB**: 也是一个很好的选择，使用`user_id`作为分片键。
    *   `nosql_repo.go`: 实现`Repository`接口，将领域模型转换为数据库实体并执行CQL/MQL查询。
*   **`adapter/cache/`**: **Redis实现**。
    *   `redis_cache.go`:
        *   **未读数**: 使用**Redis Hash**，`Key: unread_counts:{userId}`, `Field: interactions`, `Value: 23`。使用`HINCRBY`实现原子增减。
        *   **聚合追踪**: 使用**Redis String**或**Sorted Set**。`Key: aggregation_tracker:{aggregation_key}`, `Value: activity_item_id`, `TTL: 5m`。
*   **`adapter/event/`**:
    *   `consumer.go`: 封装`pkg/messaging`的`ConsumerGroup`，负责从Kafka消费平台事件，并将其传递给`application.Dispatcher`。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现`activity_feed_service.proto`中定义的gRPC服务，将请求转发给`application.QueryService`。

## 4. 数据模型与存储

### 4.1 数据库 (Cassandra/ScyllaDB)
*   **`activity_feed_items` Table**:
    *   `user_id` (uuid, **Partition Key**)
    *   `created_at` (timestamp, **Clustering Key**, DESC)
    *   `item_id` (uuid, Primary Key Component)
    *   `feed_type` (text)
    *   `activity_type` (text)
    *   `is_read` (boolean)
    *   `actors` (list<frozen<map<text, text>>>)  // 存储参与者列表
    *   `target` (frozen<map<text, text>>)       // 存储目标对象信息
    *   `display_data` (text) // 预先渲染好的显示文本

### 4.2 缓存 (Redis)
*   **Unread Counts**: `HSET unread_counts:user_123 interactions 24`
*   **Aggregation Tracker**: `SETEX aggregation_tracker:like:post_abc 300 item_xyz`

## 5. 总结

本架构设计通过以下关键点来满足`activity-feed-service`的生产级需求：
1.  **事件驱动管道**: 采用`Consumer -> Dispatcher -> Handler`的清晰管道模式处理海量事件。
2.  **领域逻辑集中**: 将复杂的聚合逻辑封装在无状态的`domain.Aggregator`中，使其易于测试和复用。
3.  **读写分离的存储策略**:
    *   **写密集**: 使用NoSQL数据库（Cassandra）来承载高并发的写入和时间线数据存储。
    *   **实时读**: 使用Redis来处理对性能要求极高的未读数查询和聚合追踪。
4.  **职责明确**: `Handler`负责“翻译”事件，`Aggregator`负责“降噪”，`Repository`和`Cache`负责“存储”，分工明确。

这种架构确保了`activity-feed-service`能够可靠、高效地处理平台级的事件流，并在保证信息全面性的同时，通过智能聚合为用户提供信噪比极高的、实时的活动与通知体验。