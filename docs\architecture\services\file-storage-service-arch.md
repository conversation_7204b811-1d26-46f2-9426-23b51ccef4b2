好的，遵照您的指示。我将为您生成一份专门针对 **`file-storage-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`file-storage-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**多存储后端的抽象与策略路由、高性能动态图片处理、CDN集成与防盗链，以及作为平台统一文件管理基石的安全性与可靠性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `file-storage-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `file-storage-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 代理模式(Proxy) + 策略模式(Strategy)

## 1. 概述

`file-storage-service` 是CINA.CLUB平台的**统一文件网关**和**动态图片处理引擎**。它作为一个**代理服务**，将上层业务与底层的具体对象存储实现完全解耦。其核心挑战在于：
1.  **多后端适配与抽象**: 需要支持多种对象存储后端（AWS S3, MinIO等），并对上层服务提供统一的接口。
2.  **高性能图片处理**: 需要能实时地、高并发地处理图片缩放、裁剪、格式转换等操作，且不能成为系统瓶颈。
3.  **安全的文件分发**: 需要为私有文件提供安全的、有时效性的访问方式，并集成CDN进行全球加速。
4.  **成本与生命周期管理**: 需要通过存储策略和生命周期规则，来优化存储成本。
5.  **异步安全扫描**: 文件上传后，需要与`content-moderation-service`协同，进行异步的安全扫描和内容审核。

本架构设计通过采用**整洁架构**，并结合**策略模式**来选择存储后端，同时将**图片处理**和**文件扫描**等计算密集型/耗时操作异步化，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (上传与图片处理流程)

```mermaid
graph TD
    subgraph "调用方服务"
        style "调用方服务" fill:#eee
        Requester[e.g., user-core-service]
    end

    subgraph "FileStorageService"
        style FileStorageService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[FileService<br/><em>application/service</em>]
        C{StoragePolicyRouter<br/><em>domain/service</em>}
        D{StorageProviderStrategy<br/><em>(interface)</em>}
        E[Provider Implementations<br/>(S3Adapter, MinioAdapter)<br/><em>adapter/provider</em>]
        F[Repository<br/><em>adapter/repository</em>]
        G[ImageProcessor<br/><em>application/image</em>]
        H[Kafka Producer<br/><em>adapter/event</em>]
    end

    subgraph "下游依赖"
        style "下游依赖" fill:#f3e5f5
        S3[AWS S3]
        MinIO[MinIO]
        CDN[Content Delivery Network]
        ModerationSvc[content-moderation-service]
    end

    Requester -- "1. RequestUploadURL" --> A
    A -- "调用" --> B
    B -- "2. Select Storage Policy" --> C
    C -- "Returns Provider & Bucket" --> B
    B -- "3. Get Provider Impl" --> E
    E -- "4. Generate Presigned URL" --> S3
    B -- "5. Create Metadata (PENDING)" --> F
    
    B -- "6. Return URL to Requester" --> A
    
    subgraph "异步流程"
        B -- "On Finalize: Publish FileUploadedEvent" --> H
        H --> Kafka[(Kafka)]
        Kafka -- "Trigger scan" --> ModerationSvc
    end

    subgraph "图片处理流程"
        Client -- "GET /images/{fileKey}?w=200" --> CDN
        CDN -- "Cache Miss, Forward to" --> A
        A -- "调用" --> G
        G -- "1. Get Metadata" --> F
        F -- "Gets storage info" --> G
        G -- "2. Get Provider Impl" --> E
        E -- "3. Download Original from S3" --> S3
        G -- "4. Process Image (In-Memory)" --> G
        G -- "5. Return Processed Image" --> A
        A -- "Response to" --> CDN
    end
```

### 2.2 最终目录结构 (`services/file-storage-service/`)

```
file-storage-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # ✨ 异步安全扫描/清理任务的独立入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── event/
│   │   │   └── producer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   ├── http/
│   │   │   └── image_handler.go # ✨ 面向CDN的动态图片处理HTTP端点 ✨
│   │   ├── provider/           # ✨ 对象存储提供商的适配器实现 ✨
│   │   │   ├── interface.go
│   │   │   ├── s3_adapter.go
│   │   │   └── minio_adapter.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── service/
│   │   │   └── file_service.go # 核心应用服务实现
│   │   └── image/
│   │       └── processor.go    # ✨ 图片处理核心逻辑 ✨
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── storage_policy_router.go # ✨ 存储策略路由服务 ✨
├── config/
│   └── storage_policies.yaml   # ✨ 存储策略配置文件 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/storage_policies.yaml` - 存储策略的“单一事实来源”

这是实现灵活存储管理的关键。
```yaml
backends:
  - id: "s3-standard-cn"
    provider: "s3"
    region: "cn-north-1"
    bucket: "cina-club-prod-cn"
    # credentials loaded from vault
  - id: "s3-hot-global"
    provider: "s3"
    region: "us-east-1"
    bucket: "cina-club-hot-global"
  - id: "minio-internal"
    provider: "minio"
    endpoint: "minio.internal:9000"
    bucket: "internal-files"

policies:
  - context_type: "USER_AVATAR"
    backend_id: "s3-hot-global"
    default_acl: "PUBLIC_READ"
    path_prefix: "avatars/"
  - context_type: "CHAT_IMAGE"
    backend_id: "s3-standard-cn"
    default_acl: "PRIVATE"
    path_prefix: "chat/images/"
  - context_type: "E2EE_CHUNK" # for cloud-sync-service
    backend_id: "s3-standard-cn"
    default_acl: "PRIVATE"
    path_prefix: "e2ee_chunks/"
  - default:
      backend_id: "s3-standard-cn"
      default_acl: "PRIVATE"
      path_prefix: "others/"
```

### 3.2 `domain/` - 领域层 (The Storage Rules)

*   `domain/model/`: 使用`/core/models`中与文件相关的`struct`，如`FileMetadata`。
*   **`domain/service/storage_policy_router.go`**:
    *   **`StoragePolicyRouter`**: 一个无状态的领域服务，在启动时加载并解析`storage_policies.yaml`。
    *   **`SelectPolicy(contextType string)`**:
        *   根据传入的`contextType`，从已加载的策略中查找并返回匹配的`Policy`对象（包含`backend_id`, `default_acl`等）。
        *   如果没有找到匹配的，则返回`default`策略。

### 3.3 `application/` - 应用层 (The Use Cases)

*   **`application/service/file_service.go`**: 实现核心业务流程。
    *   **`RequestUploadURL(ctx, contextType, ...)`**:
        1.  调用`storagePolicyRouter.SelectPolicy()`获取存储策略。
        2.  使用`provider.Factory`（未在图中画出，但存在）根据策略中的`backend_id`获取对应的`StorageProvider`适配器实例。
        3.  在`repo.CreateFileMetadata()`中创建一个状态为`PENDING`的元数据记录。
        4.  调用`provider.GeneratePresignedUploadURL()`生成上传URL。
        5.  返回`fileKey`和`presignedUrl`。
    *   **`FinalizeUpload(ctx, fileKey, ...)`**:
        1.  更新元数据状态为`COMPLETED`。
        2.  **异步地**将`FileUploadedEvent`发布到Kafka，以触发后续的安全扫描和内容审核。
*   **`application/image/processor.go`**: **图片处理引擎**。
    *   **`ImageProcessor`**: 注入了`Repository`和`StorageProvider`工厂。
    *   **`ProcessAndServe(ctx, fileKey, params)`**:
        1.  从`Repository`获取文件的元数据（包括它存储在哪个`backend`）。
        2.  获取对应的`StorageProvider`实例。
        3.  调用`provider.DownloadObject()`从S3/MinIO下载原始图片数据到**内存**。
        4.  **使用`imaging`等Go库，在内存中执行缩放、裁剪、格式转换等操作**。
        5.  将处理后的图片二进制流直接写入HTTP响应中，并设置正确的`Content-Type`和`Cache-Control`头。

### 3.4 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/provider/`**: **策略模式+适配器模式的实现，用于适配不同对象存储**。
    *   `interface.go`: 定义`StorageProvider`接口，包含`GeneratePresignedUploadURL`, `GeneratePresignedDownloadURL`, `DownloadObject`等方法。
    *   `s3_adapter.go`, `minio_adapter.go`: 分别实现`StorageProvider`接口，封装对AWS S3 SDK和MinIO Go SDK的调用。
*   **`adapter/http/image_handler.go`**:
    *   一个**公开的HTTP Handler**，作为CDN的回源地址。
    *   路径如`/images/{fileKey}`。
    *   **安全**: 为了防止DDoS攻击或滥用，此端点**必须**受保护。推荐使用**基于HMAC的URL签名**。即CDN在回源时，需要按照约定算法，用一个共享密钥对URL（包含所有查询参数）进行签名，并将签名附加到请求中。本Handler会首先验证签名，通过后才执行图片处理。
*   **`adapter/repository/`**:
    *   使用**PostgreSQL**存储文件元数据。
*   **`adapter/event/`**:
    *   `producer.go`: 在`FinalizeUpload`成功后，发布`FileUploadedEvent`。

### 3.5 `cmd/worker/` - 后台任务

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   **生命周期管理器**:
        *   以**Kubernetes CronJob**的形式每日运行。
        *   查询并硬删除那些`status`为`DELETED`且已超过宽限期（如30天）的文件元数据及其在对象存储中的实体。
    *   **安全扫描结果消费者 (可选)**:
        *   消费来自`content-moderation-service`的`FileScanCompletedEvent`。
        *   根据扫描结果，更新文件元数据的`status`（如改为`QUARANTINED`）。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`file-storage-service`：
1.  **代理与解耦**: 作为一个代理服务，它完美地将上层业务与底层具体的、可能变化的对象存储技术隔离开。
2.  **策略驱动**: 通过外部化的配置文件，实现了存储策略的灵活管理，便于成本优化和合规性调整。
3.  **高性能图片处理**: 将图片处理实现为一个安全的、面向CDN的、无状态的HTTP服务，可以轻松地独立扩展以应对高并发请求。
4.  **异步工作流**: 通过事件驱动，将耗时的、非核心路径的操作（如安全扫描）异步化，保证了核心上传流程的低延迟和高成功率。
5.  **安全为本**: 从上传到分发，通过预签名URL、CDN签名、访问控制策略等多种机制，确保了文件资产的安全。

这种架构使得`file-storage-service`能够为CINA.CLUB平台的所有富媒体功能提供一个**安全、可靠、经济高效且高性能**的统一文件管理基石。