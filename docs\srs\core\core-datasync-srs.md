好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`core/datasync`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`core/datasync`的设计哲学、同步协议、接口定义和实现细节，作为所有前端应用与后端`cloud-sync-service`进行E2EE数据同步的权威实现蓝图。

---
### CINA.CLUB - 共享核心数据同步库 (`core/datasync`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师/首席工程师]  
**审批人:** [CTO, 各技术团队负责人]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心同步协议与模型](#3-核心同步协议与模型)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [跨平台导出与使用](#6-跨平台导出与使用)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB平台通过`cloud-sync-service`为用户的端到端加密(E2EE)数据（如PKB, PM）提供跨设备同步和云端备份。为了确保所有客户端（iOS, Android, Web）都以**完全相同、安全、高效**的方式与后端进行同步，需要一个统一的客户端同步协议实现。`core/datasync` 包的目的就是提供这个**标准化的、平台无关的、封装了复杂同步逻辑的核心库**。它将版本控制、冲突检测、数据分块、加密协调等复杂操作封装起来，为上层应用提供简单、可靠的数据同步能力。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   实现**版本向量(Version Vector)**的创建、比较和合并逻辑。
    *   实现**内容定义分块(Content-Defined Chunking, CDC)**算法，用于将数据分割成块。
    *   实现与`cloud-sync-service` API交互的客户端逻辑，包括`push`, `pull`, `finalize`等。
    *   协调调用`core/crypto`对数据块进行加密。
    *   实现冲突检测逻辑，并能将冲突信息报告给上层。
*   **范围之外 (Out-of-Scope)**:
    *   **解决冲突的业务逻辑**: 本包只负责**检测**冲突并提供冲突双方的数据版本。具体的合并策略（如三方合并UI）由上层应用逻辑负责。
    *   **网络请求的执行**: 本包会构建API请求，但实际的HTTP/gRPC调用由`core/api`生成的客户端执行。
    *   **本地数据的持久化**: 本包不直接与本地数据库交互，它接收数据、处理后返回给上层，由上层决定如何存储。
    *   **用户界面(UI)**。

#### 1.3. 目标用户
*   **CINA.CLUB 前端应用** (通过Go Mobile/WASM)，是本包的唯一消费者。
*   **后端服务**可能会使用本包的部分纯逻辑函数，如CDC或版本向量操作。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`core/datasync` 是位于`core/`目录下的一个核心逻辑库。它依赖于`core/crypto`（用于加密）和`core/api`（用于API客户端），并被所有前端应用（通过编译产物）所依赖。它扮演着前端**同步引擎**的角色。

#### 2.2. 设计原则
*   **协议实现者**: 本包的核心职责是精确、无误地实现与`cloud-sync-service` SRS中定义的同步协议。
*   **无状态**: 引擎的核心函数应设计为无状态的。所有必要的状态（如本地数据库句柄、API客户端实例）都应通过依赖注入传入。
*   **原子性与鲁棒性**: 同步流程中的每一步（如`push`）都应被视为一个原子操作。任何步骤的失败都应导致操作回滚或进入一个明确的失败状态，而不留下不一致的数据。
*   **可测试性**: 必须能够独立于真实的网络和UI，对同步协议的每一步进行单元测试和集成测试。
*   **性能优化**: 数据分块和去重是核心性能优化点，其实现必须高效。

---

### 3. 核心同步协议与模型

本包必须实现`cloud-sync-service-srs.md`中定义的高级同步协议。

#### 3.1. 版本向量 (`VersionVector`)
*   **数据结构**: `map[string]int64` (deviceId -> version)。
*   **核心逻辑**:
    *   `Compare(other VersionVector)`: 比较两个版本向量，返回`Equal`, `Ancestor`, `Descendant`, `Conflict`四种状态之一。
    *   `Increment(deviceId string)`: 将指定设备的版本号加一。
    *   `Merge(other VersionVector)`: 将两个版本向量合并，取每个设备ID的较大版本号。

#### 3.2. 内容定义分块 (CDC)
*   **算法**: **必须**使用 **FastCDC** 算法。
    *   **理由**: 性能高，且分块结果稳定，即使在文件开头或中间插入/删除少量数据，也只有少数块会受影响，能最大化去重效果。
*   **输出**: `ChunkData`函数接收`[]byte`，返回一个`[]Chunk`切片，每个`Chunk`包含`Data`和`Checksum` (SHA-256)。

#### 3.3. 同步流程 (`SyncEngine`)
`SyncEngine`是本包的核心结构体，封装了完整的同步流程。
1.  **Push Flow**:
    *   上层应用调用`engine.Push(item)`。
    *   引擎内部执行：加密数据 -> CDC分块 -> 调用`cloud-sync-service`的`/push` API并提交checksums -> 根据响应上传缺失的块 -> 调用`/finalize` API。
    *   如果`/push`返回冲突，则`engine.Push`返回一个特定的`ConflictError`，其中包含冲突的版本信息。
2.  **Pull Flow**:
    *   上层应用调用`engine.Pull(knownVersions)`。
    *   引擎内部执行：调用`/pull` API -> 解析返回的变更列表 -> 为需要下载的块获取下载URL -> 下载并解密数据 -> 将所有变更返回给上层。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 版本向量操作
*   **FR4.1.1 (创建)**: 提供`NewVersionVector()`函数。
*   **FR4.1.2 (比较)**: 提供`Compare(v1, v2)`函数，实现**FR3.1**中定义的逻辑。
*   **FR4.1.3 (操作)**: 提供`Increment`和`Merge`方法。

#### 4.2. 数据分块与哈希
*   **FR4.2.1 (分块)**: 提供`ChunkData(data []byte, avgSize uint)`函数，使用FastCDC算法进行分块。
*   **FR4.2.2 (校验和)**: **必须**使用**SHA-256**计算每个数据块的校验和。

#### 4.3. 同步引擎 (`SyncEngine`)
*   **FR4.3.1 (初始化)**: 提供`NewSyncEngine(apiClient, cryptoProvider, ...)`函数，通过依赖注入接收API客户端和加密提供者。
*   **FR4.3.2 (`Push`操作)**:
    *   提供`Push(ctx, itemData, currentVersion)`方法。
    *   必须完整实现**FR3.3**中定义的Push Flow。
    *   必须能处理API错误、上传失败等情况，并返回可操作的错误。
*   **FR4.3.3 (`Pull`操作)**:
    *   提供`Pull(ctx, knownVersionMap)`方法。
    *   必须完整实现**FR3.3**中定义的Pull Flow。
    *   返回一个结构化的变更集（`Created`, `Updated`, `Deleted`列表）。
*   **FR4.3.4 (冲突处理)**: `Push`操作在检测到`409 Conflict`响应时，必须解析出服务端的版本信息，并返回一个包含这些信息的、可被类型断言的`ConflictError`。

---

### 5. 接口定义 (API Specification)

```go
// core/datasync/version_vector.go

type VersionVector map[string]int64
type ComparisonResult int // ANCESTOR, DESCENDANT, EQUAL, CONFLICT

func (vv VersionVector) Compare(other VersionVector) ComparisonResult
// ...

// core/datasync/chunker.go

type Chunk struct {
    Checksum string // SHA-256 hex string
    Data     []byte
}
func ChunkData(data []byte, avgChunkSize uint) ([]Chunk, error)

// core/datasync/engine.go

// SyncEngine 是客户端同步逻辑的核心。
type SyncEngine struct {
    // 依赖注入的字段
    apiClient    cloudsync.V1.Client
    crypto       CryptoProvider // 来自core/crypto的接口
    // ...
}

type PushableItem struct {
    ID      string
    Data    []byte
    Version VersionVector
}

type PullResult struct {
    Created []PulledItem
    Updated []PulledItem
    Deleted []string // item IDs
}

// Push 将本地变更推送到云端。
// 可能返回一个 *ConflictError。
func (e *SyncEngine) Push(ctx context.Context, item PushableItem) (newVersion VersionVector, err error)

// Pull 从云端拉取自上次同步以来的所有变更。
func (e *SyncEngine) Pull(ctx context.Context, knownVersions map[string]VersionVector) (*PullResult, error)
```

---

### 6. 跨平台导出与使用

*   **FR6.1 (Go Mobile/WASM导出)**:
    *   必须创建`exports_*.go`文件，将`SyncEngine`的**高层方法**（`Push`, `Pull`）导出。
    *   由于Go Mobile/WASM不支持直接传递复杂的`struct`和`map`，输入和输出必须序列化为**JSON字符串**或Protobuf二进制。
    *   例如，导出的`Push`函数签名可能是`func Push(itemJSON string) (newVersionJSON string, errorJSON string)`。

*   **FR6.2 (前端使用)**:
    *   前端的`data-sync` TypeScript模块负责调用编译后的Go函数。
    *   它将JS对象序列化为JSON，调用Go函数，然后再将返回的JSON字符串解析为JS对象。
    *   它负责管理同步的触发时机（如应用启动、收到静默推送、定时轮询）和管理离线队列。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   **CDC分块**: 性能必须足够高，在移动设备上处理一个几MB的文件应在秒级内完成。
    *   **内存使用**: 必须能处理大文件，而不会将整个文件一次性读入内存。应使用流式处理。
*   **NFR7.2 (可靠性)**:
    *   同步协议的实现必须100%准确。
    *   错误处理必须健壮，能从网络中断等情况中恢复。
*   **NFR7.3 (可测试性)**:
    *   `SyncEngine`的测试必须能够mock `apiClient`和`cryptoProvider`，以独立测试协议逻辑。
    *   必须编写端到端的集成测试，模拟客户端与一个mock的`cloud-sync-service`之间的完整同步流程。

---

### 8. 技术约束与开发规范

*   **TC8.1 (核心依赖)**:
    *   **CDC算法**: 强制使用`github.com/fastcdc/fastcdc`的Go实现。
    *   **加密**: 依赖`core/crypto`包。
    *   **API客户端**: 依赖`core/api`生成的`cloud-sync-service`客户端。
*   **TC8.2 (开发规范)**:
    *   **协议一致性**: 本包的实现必须与`cloud-sync-service`的SRS严格保持一致。任何一方的协议变更，都必须同步修改另一方。
    *   **错误处理**: 返回的`error`必须是结构化的，便于上层应用根据错误类型（如`ConflictError`, `NetworkError`）采取不同策略。

---
这份SRS为`core/datasync`库的设计和实现提供了坚实、全面的指导。通过将复杂的、对正确性要求极高的同步协议封装在这个核心库中，CINA.CLUB平台可以确保所有客户端在数据同步和隐私保护方面行为一致，为用户提供无缝、可靠的跨设备E2EE体验。