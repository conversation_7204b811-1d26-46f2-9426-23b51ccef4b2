# CINA.CLUB Platform - AI Assistant Service Base Kustomization
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Metadata
metadata:
  name: ai-assistant-service-base
  annotations:
    description: "Base configuration for AI Assistant Service Kong Ingress"
    service: "ai-assistant-service"
    service-type: "ai-powered-assistant"

# Resources to include
resources:
  - ingress.yaml

# Namespace for all resources
namespace: ai-assistant

# Common labels applied to all resources
commonLabels:
  app: ai-assistant-service
  component: api-gateway
  service: ai-assistant
  tier: application
  platform: cina-club
  service-type: ai-assistant

# Common annotations applied to all resources
commonAnnotations:
  platform: "cina-club"
  service-owner: "<EMAIL>"
  api-version: "v1"
  managed-by: "kong-ingress-controller"
  service-category: "ai-powered-services"
  resource-intensity: "high" 