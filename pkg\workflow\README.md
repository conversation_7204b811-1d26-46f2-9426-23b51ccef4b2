# pkg/workflow - Workflow Execution Engine

## Overview

`pkg/workflow` is a lightweight, stateless, DAG-based workflow execution engine designed for the CINA.CLUB platform. It provides a flexible and extensible framework for orchestrating complex, multi-step tasks with conditional logic and data flow management.

## Key Features

- **DAG-based Execution**: Uses directed acyclic graphs to model workflow dependencies
- **Stateless Design**: Pure functional execution model for easy testing and reasoning
- **Template Expression Support**: Dynamic data flow between nodes using Go templates
- **Pluggable Architecture**: Easy registration of custom node types
- **Built-in Retry Logic**: Configurable retry mechanisms with exponential backoff
- **Conditional Branching**: Support for complex conditional workflow logic
- **Resume Capability**: Can resume workflows from any execution state
- **Thread-Safe**: Concurrent execution support with proper synchronization

## Architecture

The workflow engine consists of several key components:

### Core Components

1. **Workflow**: Defines the complete workflow structure (nodes + edges)
2. **Node**: Individual execution units with configurable inputs/outputs
3. **Edge**: Defines dependencies and data flow between nodes
4. **Executor**: Main execution engine that orchestrates workflow execution
5. **Graph**: DAG representation with topological sorting and cycle detection
6. **ExecutionState**: Maintains execution progress and node results

### Built-in Node Types

- `start`: Entry point for workflows
- `end`: Exit point for workflows  
- `condition`: Conditional logic evaluation
- `merge`: Combines outputs from multiple branches
- `delay`: Introduces execution delays
- `transform`: Data transformation and mapping

## Quick Start

### 1. Create an Executor

```go
import "github.com/cina-club/monorepo/pkg/workflow"

executor := workflow.NewExecutor()
```

### 2. Register Custom Node Types

```go
// Define a custom node executor
type GreetingNode struct{}

func (n *GreetingNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
    name := inputs["name"].(string)
    return map[string]interface{}{
        "greeting": fmt.Sprintf("Hello, %s!", name),
    }, nil
}

// Register the node type
executor.RegisterNode("greeting", &GreetingNode{})
```

### 3. Define a Workflow

```go
workflow := &workflow.Workflow{
    ID:   "simple-greeting",
    Name: "Simple Greeting Workflow",
    Nodes: []workflow.Node{
        {
            ID:   "greet",
            Type: "greeting",
            Inputs: map[string]interface{}{
                "name": "World",
            },
        },
    },
    Edges: []workflow.Edge{},
}
```

### 4. Execute the Workflow

```go
ctx := context.Background()
finalState, err := executor.Execute(ctx, workflow, nil)
if err != nil {
    log.Fatalf("Workflow execution failed: %v", err)
}

result := finalState.GetNodeResult("greet")
fmt.Printf("Result: %s\n", result.Outputs["greeting"])
```

## Advanced Usage

### Template Expressions

Use template expressions to create dynamic data flow between nodes:

```go
workflow := &workflow.Workflow{
    Nodes: []workflow.Node{
        {
            ID:   "fetch_user",
            Type: "api_call",
            Inputs: map[string]interface{}{
                "url": "/users/123",
            },
        },
        {
            ID:   "send_notification",
            Type: "notification",
            Inputs: map[string]interface{}{
                "user_id": "{{ .nodes.fetch_user.outputs.user.id }}",
                "message": "Hello {{ .nodes.fetch_user.outputs.user.name }}!",
            },
        },
    },
    Edges: []workflow.Edge{
        {FromNode: "fetch_user", ToNode: "send_notification"},
    },
}
```

### Conditional Workflows

Create conditional branches using edge conditions:

```go
workflow := &workflow.Workflow{
    Nodes: []workflow.Node{
        {
            ID:   "check_weather",
            Type: "weather_api",
        },
        {
            ID:   "umbrella_reminder",
            Type: "notification",
        },
        {
            ID:   "sunshine_reminder",
            Type: "notification",
        },
    },
    Edges: []workflow.Edge{
        {
            FromNode: "check_weather",
            ToNode:   "umbrella_reminder",
            Condition: &workflow.EdgeCondition{
                Type:  "equals",
                Field: "condition",
                Value: "rain",
            },
        },
        {
            FromNode: "check_weather",
            ToNode:   "sunshine_reminder",
            Condition: &workflow.EdgeCondition{
                Type:  "equals",
                Field: "condition",
                Value: "sunny",
            },
        },
    },
}
```

### Retry Configuration

Configure retry behavior for unreliable operations:

```go
node := workflow.Node{
    ID:   "api_call",
    Type: "http_request",
    Retry: &workflow.RetryConfig{
        MaxAttempts:       3,
        BackoffSeconds:    2,
        BackoffMultiplier: 2.0,
        RetryableErrors:   []string{"timeout", "connection_error"},
    },
    Timeout: 30, // seconds
}
```

### Execution Listeners

Monitor workflow execution with listeners:

```go
type LoggingListener struct{}

func (l *LoggingListener) OnNodeStart(ctx context.Context, node *workflow.Node, inputs map[string]interface{}) {
    log.Printf("Starting node %s with inputs: %v", node.ID, inputs)
}

func (l *LoggingListener) OnNodeComplete(ctx context.Context, node *workflow.Node, result *workflow.NodeResult) {
    log.Printf("Node %s completed in %dms", node.ID, result.Duration)
}

// Implement other listener methods...

executor.RegisterListener(&LoggingListener{})
```

## JSON Workflow Definition

Workflows can be defined in JSON for dynamic loading:

```json
{
  "id": "user-onboarding",
  "name": "User Onboarding Workflow",
  "nodes": [
    {
      "id": "create_account",
      "type": "user_service",
      "inputs": {
        "email": "{{ .variables.email }}",
        "name": "{{ .variables.name }}"
      }
    },
    {
      "id": "send_welcome_email",
      "type": "email_service",
      "inputs": {
        "to": "{{ .nodes.create_account.outputs.email }}",
        "template": "welcome"
      }
    }
  ],
  "edges": [
    {
      "fromNode": "create_account",
      "toNode": "send_welcome_email"
    }
  ]
}
```

Load from JSON:

```go
workflowJSON := []byte(`{"id": "example", ...}`)
workflow, err := workflow.FromJSON(workflowJSON)
if err != nil {
    log.Fatal(err)
}
```

## Best Practices

### 1. Node Design

- **Stateless**: Nodes should be stateless and deterministic
- **Error Handling**: Return descriptive errors for debugging
- **Input Validation**: Validate inputs at the start of execution
- **Idempotent**: Design nodes to be safely retryable

```go
func (n *MyNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
    // Validate inputs
    userID, ok := inputs["user_id"].(string)
    if !ok || userID == "" {
        return nil, fmt.Errorf("user_id is required and must be a string")
    }
    
    // Respect context cancellation
    select {
    case <-ctx.Done():
        return nil, ctx.Err()
    default:
    }
    
    // Perform work...
    
    return outputs, nil
}
```

### 2. Workflow Design

- **Keep It Simple**: Start with simple workflows and add complexity gradually
- **Clear Naming**: Use descriptive IDs and names for nodes
- **Error Paths**: Design explicit error handling paths
- **Documentation**: Add descriptions to workflows and nodes

### 3. Performance Considerations

- **Minimize State**: Keep execution state as small as possible
- **Async Operations**: Use context cancellation for long-running operations
- **Resource Cleanup**: Ensure proper cleanup in node executors

## Integration with CINA.CLUB Services

### AI Assistant Service

```go
// Register AI-specific nodes
executor.RegisterNode("llm_prompt", &LLMPromptNode{client: openaiClient})
executor.RegisterNode("vector_search", &VectorSearchNode{db: vectorDB})
executor.RegisterNode("context_builder", &ContextBuilderNode{})
```

### Routines Service

```go
// Register routine-specific nodes
executor.RegisterNode("schedule_check", &ScheduleCheckNode{})
executor.RegisterNode("send_notification", &NotificationNode{client: notificationClient})
executor.RegisterNode("update_calendar", &CalendarNode{client: calendarClient})
```

## Error Handling

The workflow engine provides comprehensive error handling:

- **Node Failures**: Individual node failures can stop or continue execution
- **Timeout Handling**: Configurable timeouts at node and workflow levels
- **Retry Logic**: Automatic retry with configurable backoff strategies
- **Error Propagation**: Clear error messages with execution context

## Testing

Test workflows using mock node executors:

```go
func TestMyWorkflow(t *testing.T) {
    executor := workflow.NewExecutor()
    
    // Register mock nodes
    executor.RegisterNode("mock_api", &MockNodeExecutor{
        ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
            return map[string]interface{}{"result": "success"}, nil
        },
    })
    
    // Test workflow execution
    finalState, err := executor.Execute(context.Background(), testWorkflow, nil)
    assert.NoError(t, err)
    assert.Equal(t, workflow.ExecutionStatusCompleted, finalState.Status)
}
```

## Dependencies

This package has minimal dependencies and uses only Go standard libraries:

- `context` - For cancellation and timeouts
- `encoding/json` - For workflow serialization
- `text/template` - For expression evaluation
- `sync` - For thread safety
- `time` - For timing and delays

## License

Copyright (c) 2025 Cina.Club. All rights reserved. 