/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package validator provides common validation functions for data format checking.
package validator

import (
	"net"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"unicode"
)

var (
	// Email regex pattern based on RFC 5322 specification
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9.!#$%&'*+/=?^_` + "`" + `{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$`)

	// Phone number regex patterns for different formats
	phoneRegexes = []*regexp.Regexp{
		regexp.MustCompile(`^\+?[1-9]\d{1,14}$`),               // E.164 format
		regexp.MustCompile(`^\+?[1-9]\d{0,3}[-.\s]?\d{1,14}$`), // International with separators
		regexp.MustCompile(`^\(\d{3}\)\s?\d{3}[-.\s]?\d{4}$`),  // US format: (*************
		regexp.MustCompile(`^\d{3}[-.\s]?\d{3}[-.\s]?\d{4}$`),  // US format: ************
		regexp.MustCompile(`^\d{10}$`),                         // 10 digits
		regexp.MustCompile(`^\d{11}$`),                         // 11 digits (with country code)
	}

	// URL regex for basic URL validation
	urlRegex = regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)

	// IPv4 regex
	ipv4Regex = regexp.MustCompile(`^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)

	// Credit card regex patterns
	creditCardRegexes = map[string]*regexp.Regexp{
		"visa":       regexp.MustCompile(`^4[0-9]{12}(?:[0-9]{3})?$`),
		"mastercard": regexp.MustCompile(`^5[1-5][0-9]{14}$`),
		"amex":       regexp.MustCompile(`^3[47][0-9]{13}$`),
		"discover":   regexp.MustCompile(`^6(?:011|5[0-9]{2})[0-9]{12}$`),
	}

	// MAC address regex
	macAddressRegex = regexp.MustCompile(`^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$`)
)

// IsEmail validates if a string is a valid email address.
// Uses RFC 5322 compliant regex pattern.
//
// Example:
//
//	validator.IsEmail("<EMAIL>")     // returns true
//	validator.IsEmail("invalid.email")       // returns false
func IsEmail(email string) bool {
	if len(email) < 3 || len(email) > 254 {
		return false
	}
	return emailRegex.MatchString(email)
}

// IsPhoneNumber validates if a string is a valid phone number.
// Supports multiple international formats.
//
// Example:
//
//	validator.IsPhoneNumber("+1234567890")    // returns true
//	validator.IsPhoneNumber("(*************") // returns true
//	validator.IsPhoneNumber("invalid")        // returns false
func IsPhoneNumber(phone string) bool {
	// Remove common separators for basic validation
	cleaned := strings.ReplaceAll(phone, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "-", "")
	cleaned = strings.ReplaceAll(cleaned, "(", "")
	cleaned = strings.ReplaceAll(cleaned, ")", "")
	cleaned = strings.ReplaceAll(cleaned, ".", "")

	if len(cleaned) < 7 || len(cleaned) > 15 {
		return false
	}

	// Check against multiple phone number patterns
	for _, regex := range phoneRegexes {
		if regex.MatchString(phone) {
			return true
		}
	}

	return false
}

// IsURL validates if a string is a valid URL.
// Supports HTTP and HTTPS protocols.
//
// Example:
//
//	validator.IsURL("https://example.com")    // returns true
//	validator.IsURL("http://example.com")     // returns true
//	validator.IsURL("invalid-url")            // returns false
func IsURL(urlStr string) bool {
	if len(urlStr) == 0 {
		return false
	}

	// Use Go's url.Parse for more comprehensive validation
	u, err := url.Parse(urlStr)
	if err != nil {
		return false
	}

	// Check if scheme is http or https and host is present
	return (u.Scheme == "http" || u.Scheme == "https") && u.Host != ""
}

// IsIPv4 validates if a string is a valid IPv4 address.
//
// Example:
//
//	validator.IsIPv4("***********")    // returns true
//	validator.IsIPv4("256.1.1.1")      // returns false
func IsIPv4(ip string) bool {
	return net.ParseIP(ip) != nil && ipv4Regex.MatchString(ip)
}

// IsIPv6 validates if a string is a valid IPv6 address.
//
// Example:
//
//	validator.IsIPv6("2001:0db8:85a3:0000:0000:8a2e:0370:7334")    // returns true
//	validator.IsIPv6("***********")                                // returns false
func IsIPv6(ip string) bool {
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && parsedIP.To4() == nil
}

// IsIP validates if a string is a valid IP address (IPv4 or IPv6).
//
// Example:
//
//	validator.IsIP("***********")                                   // returns true
//	validator.IsIP("2001:0db8:85a3:0000:0000:8a2e:0370:7334")     // returns true
func IsIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

// IsMACAddress validates if a string is a valid MAC address.
//
// Example:
//
//	validator.IsMACAddress("00:1A:2B:3C:4D:5E")    // returns true
//	validator.IsMACAddress("00-1A-2B-3C-4D-5E")    // returns true
//	validator.IsMACAddress("invalid")               // returns false
func IsMACAddress(mac string) bool {
	return macAddressRegex.MatchString(mac)
}

// IsCreditCard validates if a string is a valid credit card number.
// Supports Visa, MasterCard, American Express, and Discover.
//
// Example:
//
//	validator.IsCreditCard("****************")    // returns true (Visa test number)
//	validator.IsCreditCard("invalid")             // returns false
func IsCreditCard(number string) bool {
	// Remove spaces and dashes
	cleaned := strings.ReplaceAll(number, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "-", "")

	// Check if it's all digits
	if !IsNumeric(cleaned) {
		return false
	}

	// Check against known credit card patterns
	for _, regex := range creditCardRegexes {
		if regex.MatchString(cleaned) {
			return luhnCheck(cleaned)
		}
	}

	return false
}

// IsNumeric validates if a string contains only numeric characters.
//
// Example:
//
//	validator.IsNumeric("12345")      // returns true
//	validator.IsNumeric("123.45")     // returns false
//	validator.IsNumeric("abc123")     // returns false
func IsNumeric(str string) bool {
	if len(str) == 0 {
		return false
	}

	for _, r := range str {
		if !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

// IsAlpha validates if a string contains only alphabetic characters.
//
// Example:
//
//	validator.IsAlpha("abcDEF")       // returns true
//	validator.IsAlpha("abc123")       // returns false
func IsAlpha(str string) bool {
	if len(str) == 0 {
		return false
	}

	for _, r := range str {
		if !unicode.IsLetter(r) {
			return false
		}
	}
	return true
}

// IsAlphanumeric validates if a string contains only alphanumeric characters.
//
// Example:
//
//	validator.IsAlphanumeric("abc123")    // returns true
//	validator.IsAlphanumeric("abc-123")   // returns false
func IsAlphanumeric(str string) bool {
	if len(str) == 0 {
		return false
	}

	for _, r := range str {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

// IsFloat validates if a string is a valid floating-point number.
//
// Example:
//
//	validator.IsFloat("123.45")       // returns true
//	validator.IsFloat("-123.45")      // returns true
//	validator.IsFloat("abc")          // returns false
func IsFloat(str string) bool {
	_, err := strconv.ParseFloat(str, 64)
	return err == nil
}

// IsInt validates if a string is a valid integer.
//
// Example:
//
//	validator.IsInt("123")            // returns true
//	validator.IsInt("-123")           // returns true
//	validator.IsInt("123.45")         // returns false
func IsInt(str string) bool {
	_, err := strconv.Atoi(str)
	return err == nil
}

// IsPositiveInt validates if a string is a valid positive integer.
//
// Example:
//
//	validator.IsPositiveInt("123")    // returns true
//	validator.IsPositiveInt("-123")   // returns false
//	validator.IsPositiveInt("0")      // returns false
func IsPositiveInt(str string) bool {
	num, err := strconv.Atoi(str)
	return err == nil && num > 0
}

// IsNonNegativeInt validates if a string is a valid non-negative integer (>= 0).
//
// Example:
//
//	validator.IsNonNegativeInt("123")    // returns true
//	validator.IsNonNegativeInt("0")      // returns true
//	validator.IsNonNegativeInt("-123")   // returns false
func IsNonNegativeInt(str string) bool {
	num, err := strconv.Atoi(str)
	return err == nil && num >= 0
}

// IsHexColor validates if a string is a valid hexadecimal color code.
//
// Example:
//
//	validator.IsHexColor("#FF0000")      // returns true
//	validator.IsHexColor("#ff0000")      // returns true
//	validator.IsHexColor("FF0000")       // returns true
//	validator.IsHexColor("#FFF")         // returns true
//	validator.IsHexColor("invalid")      // returns false
func IsHexColor(color string) bool {
	// Remove # if present
	if strings.HasPrefix(color, "#") {
		color = color[1:]
	}

	// Check length (3 or 6 characters)
	if len(color) != 3 && len(color) != 6 {
		return false
	}

	// Check if all characters are hex digits
	for _, r := range color {
		if !((r >= '0' && r <= '9') || (r >= 'A' && r <= 'F') || (r >= 'a' && r <= 'f')) {
			return false
		}
	}

	return true
}

// IsUUID validates if a string is a valid UUID (version 1-5).
//
// Example:
//
//	validator.IsUUID("550e8400-e29b-41d4-a716-************")    // returns true
//	validator.IsUUID("invalid")                                 // returns false
func IsUUID(uuid string) bool {
	uuidRegex := regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$`)
	return uuidRegex.MatchString(strings.ToLower(uuid))
}

// IsBase64 validates if a string is valid base64 encoding.
//
// Example:
//
//	validator.IsBase64("SGVsbG8gV29ybGQ=")    // returns true
//	validator.IsBase64("invalid")             // returns false
func IsBase64(str string) bool {
	base64Regex := regexp.MustCompile(`^[A-Za-z0-9+/]*={0,2}$`)
	return base64Regex.MatchString(str) && len(str)%4 == 0
}

// luhnCheck performs the Luhn algorithm check for credit card validation
func luhnCheck(number string) bool {
	sum := 0
	alternate := false

	// Process digits from right to left
	for i := len(number) - 1; i >= 0; i-- {
		digit := int(number[i] - '0')

		if alternate {
			digit *= 2
			if digit > 9 {
				digit = digit%10 + digit/10
			}
		}

		sum += digit
		alternate = !alternate
	}

	return sum%10 == 0
}
