好的，遵照您的指示。我将为您生成一份专门针对 **`social-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将基于您提供的`social-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**高性能读写、大规模数据存储的演进路径，以及原子性计数更新**等社交服务的核心挑战，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `social-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `social-service-srs.md` (v2.0)
**核心架构**: 整洁架构 (Clean Architecture)，为大规模读写优化

## 1. 概述

`social-service` 负责管理CINA.CLUB平台中的通用社交图谱（关注、好友、拉黑）。其核心挑战与`family-tree-service`不同，主要在于：
1.  **极高的写吞吐量**: 关注/取关、拉黑等操作非常频繁，对数据库写入性能要求极高。
2.  **高扇出读 (High Fan-out Read)**: 查询“明星用户”（拥有数百万粉丝）的粉丝列表是一个巨大的挑战，被称为“粉丝列表问题”。
3.  **数据一致性**: 粉丝数、关注数等统计数据必须与实际关系保持强一致性。
4.  **可扩展性**: 数据模型必须能够从初期的关系型数据库，平滑演进到能支撑亿级用户的分布式存储。

本架构设计旨在解决上述挑战，构建一个高性能、高可用、可平滑演进的社交图谱中心。

---

## 2. 架构图与目录结构

### 2.1 整洁架构分层图 (与`family-tree-service`类似，但实现细节不同)

```mermaid
graph TD
    subgraph "外部世界 (Frameworks & Drivers)"
        A[gRPC Server<br/>(adapter/grpc)]
        B[PostgreSQL/TiDB<br/>(adapter/repository/sql)]
        B2[Redis Cache<br/>(adapter/repository/cache)]
        C[Kafka Consumer<br/>(adapter/event)]
    end
    
    subgraph "应用层 (Application)"
        E[SocialService<br/>(application/service)]
    end
    
    subgraph "领域层 (Domain)"
        F[Relationship, SocialStats<br/>(domain/model)]
        G[Repository & Service Interfaces<br/>(application/port)]
    end
    
    A -- "调用" --> E
    E -- "使用" --> G
    B -- "实现" --> G
    B2 -- "实现" --> G
    C -- "调用" --> E
    E -- "操作" --> F
```

### 2.2 最终目录结构 (`services/social-service/`)

```
social-service/
├── cmd/server/
│   └── main.go                 # 服务启动入口
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   ├── server.go
│   │   │   └── handler.go      # gRPC Handler实现
│   │   ├── event/
│   │   │   └── user_consumer.go # (可选)消费用户删除等事件
│   │   └── repository/
│   │       ├── cache/          # ✨ 缓存层实现 ✨
│   │       │   └── redis_repo.go
│   │       ├── model.go        # 数据库实体模型
│   │       └── sql/            # ✨ SQL数据库实现 ✨
│   │           ├── repo.go     # 实现了所有仓储接口的struct
│   │           └── relation_repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── cache.go        # 定义缓存接口
│   │   │   ├── repository.go   # 定义数据库仓储接口
│   │   │   └── service.go      # 定义SocialService接口
│   │   └── service/
│   │       └── service.go      # SocialService接口的实现
│   └── domain/
│       ├── model/              # 核心领域模型 (使用/core/models)
│       │   └── alias.go
│       └── spec/               # ✨ 业务规则与规格 ✨
│           └── anti_spam_spec.go
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Rules)

*   `domain/model/`: 使用`/core/models`中定义的`Relationship`和`SocialStats`。
*   **`domain/spec/`**: **这是本服务风控逻辑的体现**。
    *   `anti_spam_spec.go`: 定义“反垃圾规格”。例如，`IsSpamFollow(ctx, actor, target)`函数，它内部可以包含一系列规则（如：actor的账号注册时间、粉丝/关注比、操作频率等），用于判断一次关注行为是否可疑。这个规格不执行操作，只返回一个布尔值或一个风险评分。

### 3.2 `application/` - 应用层 (The Orchestrator)

*   **`application/port/`**: 定义了清晰的职责边界。
    *   `repository.go`: 定义与**主持久化存储（DB）**交互的接口，如`CreateRelation`, `DeleteRelation`, `IncrementStats`。
    *   `cache.go`: 定义与**缓存层（Redis）**交互的接口，如`GetFollowerList`, `AddToFollowerList`, `GetStats`。
    *   `service.go`: 定义所有业务用例，如`Follow`, `Unfollow`, `AcceptFriendRequest`。
*   **`application/service/`**: 实现业务流程编排，**这是读写优化的核心**。

    **示例: `Follow` 业务流程**
    ```go
    func (s *service) Follow(ctx context.Context, actorID, targetID uuid.UUID) error {
        // 1. 业务规则校验
        // 1a. 不能关注自己
        // 1b. 检查是否已被对方拉黑
        // 1c. ✨ 调用反垃圾规格进行检查 ✨
        if s.antiSpamSpec.IsSpamFollow(...) {
            // 可以记录日志、触发告警，或直接返回错误
            return app_errors.New(app_errors.PermissionDenied, "spam detected")
        }

        // 2. 开启工作单元（事务）
        tx, err := s.db.Begin(ctx)
        defer tx.Rollback(ctx)

        // 3. ✨ 执行数据库写操作 (Write-to-DB) ✨
        // 3a. 在relationships表中创建关系
        err = s.repo.CreateRelation(ctx, tx, actorID, targetID, "FOLLOWING")
        // 3b. 原子性地更新统计计数
        err = s.repo.IncrementFollowingCount(ctx, tx, actorID, 1)
        err = s.repo.IncrementFollowerCount(ctx, tx, targetID, 1)

        // 4. 提交事务
        if err := tx.Commit(ctx); err != nil {
            return app_errors.Wrap(err, ...)
        }

        // 5. ✨ 异步更新缓存 (Cache Invalidation/Update) ✨
        // 在事务成功后，发布一个事件或直接调用（取决于延迟要求）
        go s.cache.AddToFollowerList(context.Background(), targetID, actorID)
        
        // 6. 发布领域事件
        s.eventProducer.Publish(ctx, ...)

        return nil
    }
    ```

### 3.3 `adapter/` - 适配层 (The Implementations)

*   **`adapter/repository/sql/`**: **主存储实现**。
    *   **数据库选型演进**:
        *   **阶段1 (PostgreSQL)**: `relation_repo.go`使用标准SQL。`Increment...Count`方法使用`UPDATE user_social_stats SET followers_count = followers_count + 1 WHERE user_id = ...`并配合**行级锁 (`FOR UPDATE`)**来保证原子性。
        *   **阶段2 (TiDB/CockroachDB)**: 当单机PostgreSQL成为瓶颈时，可以平滑迁移到分布式SQL数据库。由于它们兼容PostgreSQL协议，`repo`层的代码改动会很小。
    *   **数据模型**:
        *   `relationships`表的设计：为了避免热点，`user_a_id`和`user_b_id`可以约定小的ID总在前面，然后对`(user_a_id, user_b_id)`创建主键。同时，为了快速查询某个人的关注列表，需要在`user_a_id`上单独创建索引；为了查询粉丝列表，需要在`user_b_id`上单独创建索引。
*   **`adapter/repository/cache/`**: **缓存层与读优化实现**。
    *   **职责**: 解决“粉丝列表问题”和加速计数查询。
    *   `redis_repo.go`:
        *   **粉丝/关注列表缓存**:
            *   对于普通用户，不缓存列表。
            *   对于粉丝数超过阈值（如10000）的**明星用户**，其粉丝列表被存储在Redis的**Sorted Set**中，`score`为关注时间戳，`member`为粉丝ID。
            *   `GetFollowerList`方法会先检查用户是否为明星用户，如果是，则从Redis分页拉取；否则，从SQL数据库拉取。
        *   **计数缓存**: `user_social_stats`的计数值可以在Redis中缓存，采用Cache-Aside模式。

*   **`adapter/grpc/`**:
    *   `handler.go`实现。`ListFollowers`的实现会调用`application.service`的`GetFollowers`方法，该方法内部已经封装了对普通用户和明星用户的不同处理逻辑。

---

## 4. 大规模数据存储演进路径 (详细)

这是本服务架构设计的核心考量。

#### 阶段1: PostgreSQL (启动 -> ~10M 用户)
*   **模型**: `relationships` (user_a, user_b), `user_social_stats`。
*   **优点**: 简单、可靠、事务性强。
*   **瓶颈**:
    *   `relationships`表会变得巨大，写入和索引更新成为瓶颈。
    *   `user_social_stats`表的更新会产生大量行锁竞争，特别是在有明星用户时。

#### 阶段2: PostgreSQL + Redis (当前设计)
*   **优化**:
    *   引入Redis缓存明星用户的粉丝列表（只读）。
    *   写操作依然是“**先写DB，再更新/失效缓存**”。
*   **优点**: 缓解了读压力。
*   **瓶颈**: 写瓶颈依然存在。

#### 阶段3: 迁移到分布式数据库 (千万级 -> 亿级用户)
*   **选项A (平滑): TiDB/CockroachDB**:
    *   将PostgreSQL替换为分布式SQL数据库。
    *   几乎不需要修改应用层代码。
    *   水平扩展了写入能力。
*   **选项B (重构): 图数据库 (Neo4j/Dgraph)**:
    *   将`relationships`表迁移到图数据库。
    *   **优点**: 对“共同关注”、“二度人脉”等复杂图查询有无与伦比的性能。
    *   **缺点**: 需要重写`repository`层，引入新的技术栈和运维复杂性。

#### 阶段4: 最终架构 (亿级+用户，参考Twitter/Facebook)
*   **模型**:
    *   **关系存储**: 不再使用数据库，而是使用**分布式KV存储（如TiKV, Redis Cluster）**。每个用户的关注/粉丝列表都是一个独立的Key。
    *   **写操作 (Fan-out on write)**: 用户A发帖时，会有一个“扇出”服务，将帖子ID写入**所有粉丝**的“收件箱(Timeline)”中。这是一个写密集型操作。
    *   **读操作**: 读取自己的Timeline时，只需从KV存储中读取一个Key，速度极快。
*   **说明**: 这是终极架构，非常复杂，目前阶段只需在设计上**预留演进可能性**即可。我们当前的**DB+Cache**架构是走向这个方向的坚实一步。

---

## 5. 总结

本架构设计通过以下关键点来满足`social-service`的生产级需求：
1.  **分层架构**: 使用整洁架构，清晰分离业务逻辑、数据访问和API接口。
2.  **读写分离策略**:
    *   **写**: 始终写入主数据库（SQL），保证数据一致性和可靠性。
    *   **读**: 对高频、高扇出的读（粉丝列表），引入缓存层（Redis）进行优化。
3.  **原子性保证**: 通过数据库事务和行级锁，确保统计计数的绝对准确。
4.  **可演进性**: 明确了从PostgreSQL到分布式存储的演进路径，确保架构能支撑未来的业务增长。
5.  **风控集成**: 在领域层引入“规格(Specification)”模式，将反垃圾等风控逻辑与核心业务流程解耦。