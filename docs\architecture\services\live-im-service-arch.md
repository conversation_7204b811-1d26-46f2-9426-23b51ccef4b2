好的，遵照您的指示。我将为您生成一份专门针对 **`live-im-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`live-im-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将特别关注**如何实现一个专为大规模“扇出(Fan-out)”广播优化的实时服务器、与`chat-websocket-server`的设计异同、高性能的Redis Pub/Sub集成，以及与计费服务的实时同步交互**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `live-im-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `live-im-service-srs.md` (v1.0)
**核心架构**: 有状态的实时广播网关 + Redis Pub/Sub驱动

## 1. 概述

`live-im-service` 是CINA.CLUB直播生态的“**互动消息总线**”。它是一个**有状态的、为大规模广播场景深度优化的**实时服务。其架构设计必须围绕以下核心挑战展开：
1.  **超高并发连接**: 需要承载比普通聊天多几个数量级的并发连接（一个热门直播间可能有数万甚至数十万观众）。
2.  **极低延迟广播**: 弹幕和礼物的消息必须在200ms内触达房间内的所有用户，以营造“实时”的氛围。
3.  **消息风暴处理**: 需要有效处理瞬间产生的大量消息（如集体点赞、弹幕刷屏），并防止系统过载。
4.  **无状态扩展**: 服务器实例本身应该是无状态的（或软状态），以便进行无缝的水平扩展。
5.  **与`chat-websocket-server`的区别**: 必须明确其设计侧重点与通用聊天服务的不同：
    *   **`live-im-service`**: 优化**广播（一对多）**性能，**不保证**消息持久化，允许一定的消息丢失（如普通点赞），状态主要在Redis中。
    *   **`chat-websocket-server`**: 优化**小群组/单聊**，**保证**消息持久化，不允许消息丢失，状态由`chat-api-service`的DB保证。

本架构设计通过采用**Go的并发模型**，结合一个**优化的、基于Redis Pub/Sub的全局广播模型**，来构建一个高性能、可扩展的直播互动引擎。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (单个实例的内部结构与多实例协同)

```mermaid
graph TD
    subgraph "Client Connections"
        C1 & C2 & C3 --- "WebSocket" --> WSS
    end
    
    subgraph "LiveIMService Node"
        style LiveIMService fill:#e0f7fa
        WSS[WebSocket Server<br/>(adapter/transport)]
        Hub[Hub (The Local Post Office)<br/>(application/service)]
        subgraph "Client Goroutines"
            CR1(Client 1 Reader/Writer)
            CR2(Client 2 Reader/Writer)
            CR3(Client 3 Reader/Writer)
        end
        Broadcaster[RedisBroadcaster<br/>(adapter/redis)]
    end

    subgraph "分布式后端 (Shared State & Bus)"
        style "分布式后端 (Shared State & Bus)" fill:#f3e5f5
        RedisPubSub[Redis (Pub/Sub)]
        RedisState[Redis (State Store)]
    end

    WSS -- "New Connection" --> Hub
    Hub -- "Manages" --> CR1 & CR2 & CR3

    %% Message Flow
    CR1 -- "1. Send Barrage" --> Hub
    Hub -- "2. Auth/RateLimit (using RedisState)" --> RedisState
    Hub -- "3. Publish to Global Bus" --> Broadcaster
    Broadcaster -- "PUBLISH 'live_im_room:R1'" --> RedisPubSub
    
    RedisPubSub -- "4. Fan-out to ALL nodes" --> Broadcaster
    Broadcaster -- "5. Push to Local Hub" --> Hub
    Hub -- "6. Dispatch to subscribed clients" --> CR1 & CR2
```
*（注意：图中省略了与此节点上的Client C3在同一房间，但连接在其他节点上的Client D, E...）*

### 2.2 最终目录结构 (`services/live-im-service/`)

```
live-im-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_rate_limiter.go # 速率限制器实现
│   │   ├── client/
│   │   │   ├── live_api_client.go
│   │   │   └── ledger_client.go
│   │   ├── redis/
│   │   │   ├── broadcaster.go      # ✨ Redis Pub/Sub广播器实现 ✨
│   │   │   └── room_subscription_store.go # 房间订阅关系存储
│   │   └── transport/
│   │       └── websocket/
│   │           └── handler.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── broadcaster.go
│   │   │   └── room_store.go
│   │   └── service/
│   │       ├── hub.go              # ✨ Hub: 本地连接与房间的管理器 ✨
│   │       └── message_processor.go # ✨ 消息处理与业务协同 ✨
│   └── domain/
│       ├── model/
│       │   ├── client.go
│       │   ├── room.go
│       │   └── message.go        # 定义各种互动消息类型
│       └── protocol/
│           └── protobuf.go       # Protobuf序列化/反序列化
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Real-time Primitives)

*   `domain/model/`:
    *   **`client.go`**: 与`chat-websocket-server`的设计类似，封装了单个WebSocket连接、一个`send chan []byte`发送队列，以及`readPump`/`writePump`两个核心goroutine。
    *   **`room.go`**: 定义`Room`结构体，**关键区别在于**：它只包含一个`roomID`和`hub`的引用，而**不直接包含`clients` map**。客户端与房间的订阅关系完全由Redis管理。
    *   **`message.go`**: 定义`BarrageMsg`, `GiftMsg`, `LikeMsg`等各种互动消息的`struct`。

### 3.2 `application/` - 应用层 (The Hub & Processor)

*   **`application/service/hub.go`**: **这是单个服务器实例的“本地邮局”**。
    *   **`Hub` struct**:
        *   `clients map[*model.Client]bool`: 本实例上所有客户端的集合。
        *   `rooms map[string]map[*model.Client]bool`: **本地房间-客户端订阅缓存**。`map[roomID] -> map[clientPtr] -> {}`。当从Redis Pub/Sub收到一条消息时，Hub用这个map来快速找到所有需要接收该消息的本地客户端。
        *   `processor *MessageProcessor`: 消息处理器。
    *   **`Run()` method (Hub的核心事件循环)**:
        *   处理客户端的注册(`register`)和注销(`unregister`)。
        *   **注册时**:
            1.  将客户端加入`clients` map。
            2.  调用`roomStore.Subscribe(userID, roomID)`，将用户订阅关系写入Redis。
            3.  将客户端加入本地的`rooms`订阅缓存。
            4.  广播`USER_ENTER`消息。
        *   接收来自`Client.readPump`的消息，并将其**转发给`MessageProcessor`**。
        *   接收来自`RedisBroadcaster`的全局广播消息，并将其分发给本地`rooms`缓存中的所有客户端。
*   **`application/service/message_processor.go`**: **这是处理所有上行消息和业务协同的核心**。
    *   **`MessageProcessor` struct**: 注入所有需要的客户端（`ledgerClient`, `liveAPIClient`）和`Broadcaster`。
    *   **`Process(client, message)` method**:
        *   使用`switch`语句根据消息类型进行处理。
        *   **处理弹幕 (`BarrageMsg`)**:
            a. 调用`cache.RateLimiter.Allow()`进行速率限制检查。
            b. 调用`broadcaster.Broadcast(roomID, message)`。
        *   **处理点赞 (`LikeMsg`)**:
            a. **进行本地聚合**。例如，使用一个`map[roomID]atomic.Int32`来累加一秒内的点赞数。
            b. 使用一个`time.Ticker`每秒触发一次，将聚合后的点赞总数`LikeBurstMsg`广播出去。
        *   **处理礼物 (`GiftMsg`)**:
            a. **同步调用`ledgerClient.Debit(...)`**。这是一个阻塞操作。
            b. 如果扣款成功，才调用`broadcaster.Broadcast(roomID, giftMessage)`。
            c. 如果失败，向发送方客户端发送一条错误消息。

### 3.3 `adapter/` - 适配层 (The Bridge to Infrastructure)

*   **`adapter/redis/`**: **这是实现分布式广播和状态管理的核心**。
    *   **`broadcaster.go`**:
        *   **`Broadcast(roomID, message)`**: 将消息序列化后，`PUBLISH`到Redis的`live_im_room:{roomId}`频道。
        *   **启动时**: `NewBroadcaster`会创建一个Redis Pub/Sub客户端，`SUBSCRIBE`到一个**模式频道(Pattern Channel)**，如`live_im_room:*`。在一个独立的goroutine中监听所有房间的消息，并将收到的消息推送到`Hub`的内部channel。
    *   `room_subscription_store.go`: 实现`port.RoomStore`接口，使用Redis的`SET` (`SADD`, `SREM`, `SISMEMBER`) 来管理`room_members:{roomId}`。
*   **`adapter/cache/redis_rate_limiter.go`**:
    *   实现一个基于Redis的**滑动窗口**或**令牌桶**速率限制算法。
*   **`adapter/transport/websocket/`**:
    *   `handler.go`: 升级HTTP连接，创建`domain.Client`实例，并将其注册到`Hub`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`live-im-service`：
1.  **为广播优化的设计**:
    *   与`chat-websocket-server`不同，本服务**不进行消息持久化**，极大地降低了处理延迟。
    *   **点赞聚合**: 通过在应用层进行时间窗口聚合，有效防止了点赞引发的消息风暴。
2.  **分布式状态与通信**:
    *   **Redis Pub/Sub**作为全局消息总线，是实现跨实例、低延迟广播的核心技术。
    *   **Redis Set**作为全局订阅关系存储，使得任何一个服务实例都能知道一个房间有哪些成员。
3.  **职责清晰的组件**:
    *   **`Hub`**: 只负责管理**本地连接**和消息的**本地分发**。
    *   **`MessageProcessor`**: 负责处理**所有上行业务逻辑**，如鉴权、速率限制、计费协同。
    *   **`RedisBroadcaster`**: 负责**与全局消息总线的交互**。
4.  **实时金融交易**: 对于礼物这类需要强一致性的操作，明确采用了**同步调用**金融服务（`ledger-service`）的模式，保证了“先扣款，后广播”的原子性。

这种架构确保了`live-im-service`能够以**极高的性能、极低的延迟和强大的水平扩展能力**，来应对超大规模直播间的实时互动挑战，为CINA.CLUB平台营造出最具吸引力的实时社交氛围。