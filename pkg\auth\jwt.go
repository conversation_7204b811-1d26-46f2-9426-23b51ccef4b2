// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package auth

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
)

var (
	ErrInvalidToken     = errors.New("invalid token")
	ErrTokenExpired     = errors.New("token expired")
	ErrTokenNotFound    = errors.New("token not found")
	ErrInvalidClaims    = errors.New("invalid token claims")
	ErrInvalidSignature = errors.New("invalid token signature")
)

// JWTManager JWT管理器
type JWTManager struct {
	secretKey     string
	tokenDuration time.Duration
	logger        *zap.Logger
}

// UserClaims 用户JWT声明
type UserClaims struct {
	UserID    string   `json:"user_id"`
	Username  string   `json:"username"`
	Email     string   `json:"email"`
	Roles     []string `json:"roles"`
	Level     int      `json:"level"`
	IsActive  bool     `json:"is_active"`
	TokenType string   `json:"token_type"` // "access" or "refresh"
	jwt.RegisteredClaims
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(secretKey string, tokenDuration time.Duration, logger *zap.Logger) *JWTManager {
	return &JWTManager{
		secretKey:     secretKey,
		tokenDuration: tokenDuration,
		logger:        logger,
	}
}

// GenerateToken 生成访问令牌
func (manager *JWTManager) GenerateToken(userID, username, email string, roles []string, level int, isActive bool) (string, error) {
	claims := UserClaims{
		UserID:    userID,
		Username:  username,
		Email:     email,
		Roles:     roles,
		Level:     level,
		IsActive:  isActive,
		TokenType: "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(manager.tokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "cina.club",
			Subject:   userID,
			ID:        generateTokenID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(manager.secretKey))
	if err != nil {
		manager.logger.Error("failed to generate token",
			zap.String("user_id", userID),
			zap.Error(err))
		return "", err
	}

	manager.logger.Info("token generated successfully",
		zap.String("user_id", userID),
		zap.String("username", username),
		zap.String("token_id", claims.ID))

	return tokenString, nil
}

// GenerateRefreshToken 生成刷新令牌
func (manager *JWTManager) GenerateRefreshToken(userID, username string) (string, error) {
	claims := UserClaims{
		UserID:    userID,
		Username:  username,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(7 * 24 * time.Hour)), // 7天
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "cina.club",
			Subject:   userID,
			ID:        generateTokenID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(manager.secretKey))
	if err != nil {
		manager.logger.Error("failed to generate refresh token",
			zap.String("user_id", userID),
			zap.Error(err))
		return "", err
	}

	return tokenString, nil
}

// VerifyToken 验证令牌
func (manager *JWTManager) VerifyToken(tokenString string) (*UserClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &UserClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ErrInvalidSignature
		}
		return []byte(manager.secretKey), nil
	})

	if err != nil {
		manager.logger.Warn("token verification failed", zap.Error(err))

		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrTokenExpired
		}
		return nil, ErrInvalidToken
	}

	claims, ok := token.Claims.(*UserClaims)
	if !ok || !token.Valid {
		return nil, ErrInvalidClaims
	}

	// 检查用户是否激活（仅对访问令牌）
	if claims.TokenType == "access" && !claims.IsActive {
		return nil, errors.New("user account is inactive")
	}

	manager.logger.Debug("token verified successfully",
		zap.String("user_id", claims.UserID),
		zap.String("username", claims.Username),
		zap.String("token_type", claims.TokenType))

	return claims, nil
}

// RefreshAccessToken 刷新访问令牌
func (manager *JWTManager) RefreshAccessToken(refreshTokenString string) (string, error) {
	claims, err := manager.VerifyToken(refreshTokenString)
	if err != nil {
		return "", err
	}

	if claims.TokenType != "refresh" {
		return "", errors.New("invalid token type for refresh")
	}

	// 这里通常需要查询数据库获取最新的用户信息
	// 为了简化，我们使用现有的claims信息
	return manager.GenerateToken(
		claims.UserID,
		claims.Username,
		claims.Email,
		claims.Roles,
		claims.Level,
		true, // 假设用户仍然活跃
	)
}

// ExtractTokenFromHeader 从HTTP头中提取令牌
func ExtractTokenFromHeader(authHeader string) (string, error) {
	if authHeader == "" {
		return "", ErrTokenNotFound
	}

	const bearerPrefix = "Bearer "
	if len(authHeader) < len(bearerPrefix) || authHeader[:len(bearerPrefix)] != bearerPrefix {
		return "", ErrInvalidToken
	}

	return authHeader[len(bearerPrefix):], nil
}

// generateTokenID 生成令牌ID
func generateTokenID() string {
	// 这里可以使用UUID或其他唯一标识符生成方法
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// HasRole 检查用户是否具有特定角色
func (claims *UserClaims) HasRole(role string) bool {
	for _, r := range claims.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasAnyRole 检查用户是否具有任一角色
func (claims *UserClaims) HasAnyRole(roles []string) bool {
	for _, role := range roles {
		if claims.HasRole(role) {
			return true
		}
	}
	return false
}

// IsAdmin 检查用户是否为管理员
func (claims *UserClaims) IsAdmin() bool {
	return claims.HasRole("admin") || claims.HasRole("super_admin")
}

// CanAccess 检查用户是否可以访问资源
func (claims *UserClaims) CanAccess(requiredRoles []string, minLevel int) bool {
	// 检查角色权限
	if len(requiredRoles) > 0 && !claims.HasAnyRole(requiredRoles) {
		return false
	}

	// 检查等级权限
	if claims.Level < minLevel {
		return false
	}

	// 检查账户状态
	if !claims.IsActive {
		return false
	}

	return true
}
