apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-dashboard-deployment
  labels:
    app: admin-dashboard
spec:
  replicas: 2 # Start with 2 replicas for high availability
  selector:
    matchLabels:
      app: admin-dashboard
  template:
    metadata:
      labels:
        app: admin-dashboard
    spec:
      containers:
      - name: admin-dashboard
        # Replace with your actual image registry and tag
        image: your-registry/cina-club-admin:latest 
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /index.html
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /index.html
            port: 80
          initialDelaySeconds: 15
          periodSeconds: 20

---

apiVersion: v1
kind: Service
metadata:
  name: admin-dashboard-service
spec:
  selector:
    app: admin-dashboard
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer # Or ClusterIP if you are using an Ingress 