/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

package interceptor

import (
	"context"
	"errors"
	"fmt"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"cina.club/pkg/auth/rbac"
)

var (
	ErrPermissionDenied     = errors.New("permission denied")
	ErrNoPermissionMapping  = errors.New("no permission mapping found for method")
	ErrUserNotAuthenticated = errors.New("user not authenticated")
)

// RBACInterceptor provides role-based access control
type RBACInterceptor struct {
	engine         *rbac.Engine
	rpcPermissions map[string]string // map[fullMethodName] -> required permission
	defaultPolicy  RBACDefaultPolicy
}

// RBACDefaultPolicy defines how to handle methods without explicit permission mappings
type RBACDefaultPolicy int

const (
	// DenyByDefault denies access to methods without explicit permission mappings
	DenyByDefault RBACDefaultPolicy = iota
	// AllowByDefault allows access to methods without explicit permission mappings
	AllowByDefault
)

// NewRBACInterceptor creates a new RBAC interceptor
func NewRBACInterceptor(engine *rbac.Engine, rpcPermissions map[string]string) *RBACInterceptor {
	return &RBACInterceptor{
		engine:         engine,
		rpcPermissions: rpcPermissions,
		defaultPolicy:  DenyByDefault,
	}
}

// NewRBACInterceptorWithDefaultPolicy creates a new RBAC interceptor with a custom default policy
func NewRBACInterceptorWithDefaultPolicy(engine *rbac.Engine, rpcPermissions map[string]string, defaultPolicy RBACDefaultPolicy) *RBACInterceptor {
	return &RBACInterceptor{
		engine:         engine,
		rpcPermissions: rpcPermissions,
		defaultPolicy:  defaultPolicy,
	}
}

// UnaryInterceptor returns a unary server interceptor for RBAC
func (i *RBACInterceptor) UnaryInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		if err := i.checkPermission(ctx, info.FullMethod); err != nil {
			return nil, status.Errorf(codes.PermissionDenied, "access denied: %v", err)
		}

		return handler(ctx, req)
	}
}

// StreamInterceptor returns a stream server interceptor for RBAC
func (i *RBACInterceptor) StreamInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		if err := i.checkPermission(ss.Context(), info.FullMethod); err != nil {
			return status.Errorf(codes.PermissionDenied, "access denied: %v", err)
		}

		return handler(srv, ss)
	}
}

// checkPermission checks if the current user has permission to access the method
func (i *RBACInterceptor) checkPermission(ctx context.Context, fullMethodName string) error {
	// Extract user from context
	user, err := UserFromContext(ctx)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrUserNotAuthenticated, err)
	}

	// Find required permission for this method
	requiredPermission, exists := i.rpcPermissions[fullMethodName]
	if !exists {
		// No explicit permission mapping found
		switch i.defaultPolicy {
		case AllowByDefault:
			// Allow access when no permission mapping exists
			return nil
		case DenyByDefault:
			// Deny access when no permission mapping exists
			return fmt.Errorf("%w: %s", ErrNoPermissionMapping, fullMethodName)
		default:
			return fmt.Errorf("%w: %s", ErrNoPermissionMapping, fullMethodName)
		}
	}

	// Check if user has the required permission
	if !i.engine.Can(user.Roles, requiredPermission) {
		return fmt.Errorf("%w: user with roles %v lacks permission '%s' for method %s",
			ErrPermissionDenied, user.Roles, requiredPermission, fullMethodName)
	}

	return nil
}

// AddPermissionMapping adds a new permission mapping at runtime
func (i *RBACInterceptor) AddPermissionMapping(fullMethodName, permission string) {
	i.rpcPermissions[fullMethodName] = permission
}

// RemovePermissionMapping removes a permission mapping at runtime
func (i *RBACInterceptor) RemovePermissionMapping(fullMethodName string) {
	delete(i.rpcPermissions, fullMethodName)
}

// GetPermissionMapping returns the permission mapping for a method
func (i *RBACInterceptor) GetPermissionMapping(fullMethodName string) (string, bool) {
	permission, exists := i.rpcPermissions[fullMethodName]
	return permission, exists
}

// GetAllPermissionMappings returns all permission mappings
func (i *RBACInterceptor) GetAllPermissionMappings() map[string]string {
	// Return a copy to prevent external modification
	result := make(map[string]string, len(i.rpcPermissions))
	for method, permission := range i.rpcPermissions {
		result[method] = permission
	}
	return result
}
