#!/bin/bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB 模块路径批量修正脚本
# 将所有服务的模块路径统一为 cina.club/* 格式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录
get_project_root() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    echo "$(dirname "$script_dir")"
}

# 修正单个服务的模块路径
fix_service_module_path() {
    local service_dir="$1"
    local service_name="$(basename "$service_dir")"
    local go_mod_file="$service_dir/go.mod"
    
    if [[ ! -f "$go_mod_file" ]]; then
        log_warning "No go.mod found in $service_name, skipping..."
        return 0
    fi
    
    log_info "Fixing module path for $service_name..."
    
    # 读取当前模块路径
    local current_module=$(head -n 10 "$go_mod_file" | grep -E "^module " | head -n 1 | cut -d' ' -f2)
    local new_module="cina.club/services/$service_name"
    
    if [[ "$current_module" == "$new_module" ]]; then
        log_info "$service_name already has correct module path"
        return 0
    fi
    
    # 创建备份
    cp "$go_mod_file" "$go_mod_file.backup"
    
    # 修正模块路径
    sed -i.tmp "s|^module .*|module $new_module|" "$go_mod_file"
    
    # 修正依赖引用
    sed -i.tmp 's|github\.com/cina-club/monorepo/core|cina.club/core|g' "$go_mod_file"
    sed -i.tmp 's|github\.com/cina-club/monorepo/pkg|cina.club/pkg|g' "$go_mod_file"
    sed -i.tmp 's|github\.com/cina-club/pkg/|cina.club/pkg/|g' "$go_mod_file"
    sed -i.tmp 's|github\.com/CINA-CLUB/Monorepo/|cina.club/|g' "$go_mod_file"
    sed -i.tmp 's|github\.com/cina-club/cina-club-monorepo/|cina.club/|g' "$go_mod_file"
    sed -i.tmp 's|github\.com/cina-club/monorepo/services/|cina.club/services/|g' "$go_mod_file"
    
    # 确保有本地依赖替换
    if ! grep -q "replace cina.club/core" "$go_mod_file"; then
        echo "" >> "$go_mod_file"
        echo "replace cina.club/core => ../../core" >> "$go_mod_file"
    fi
    
    if ! grep -q "replace cina.club/pkg" "$go_mod_file"; then
        echo "replace cina.club/pkg => ../../pkg" >> "$go_mod_file"
    fi
    
    # 清理临时文件
    rm -f "$go_mod_file.tmp"
    
    log_success "Fixed module path for $service_name: $current_module -> $new_module"
}

# 修正所有服务
fix_all_services() {
    local project_root="$(get_project_root)"
    local services_dir="$project_root/services"
    
    if [[ ! -d "$services_dir" ]]; then
        log_error "Services directory not found: $services_dir"
        exit 1
    fi
    
    log_info "Starting module path fixes for all services..."
    
    local fixed_count=0
    local total_count=0
    
    for service_dir in "$services_dir"/*; do
        if [[ -d "$service_dir" ]]; then
            total_count=$((total_count + 1))
            if fix_service_module_path "$service_dir"; then
                fixed_count=$((fixed_count + 1))
            fi
        fi
    done
    
    log_success "Module path fixes completed: $fixed_count/$total_count services processed"
}

# 验证修正结果
verify_fixes() {
    local project_root="$(get_project_root)"
    local services_dir="$project_root/services"
    
    log_info "Verifying module path fixes..."
    
    local errors=0
    
    for service_dir in "$services_dir"/*; do
        if [[ -d "$service_dir" ]]; then
            local service_name="$(basename "$service_dir")"
            local go_mod_file="$service_dir/go.mod"
            
            if [[ -f "$go_mod_file" ]]; then
                local module_path=$(head -n 10 "$go_mod_file" | grep -E "^module " | head -n 1 | cut -d' ' -f2)
                local expected_path="cina.club/services/$service_name"
                
                if [[ "$module_path" != "$expected_path" ]]; then
                    log_error "$service_name has incorrect module path: $module_path (expected: $expected_path)"
                    errors=$((errors + 1))
                else
                    log_success "$service_name has correct module path: $module_path"
                fi
            fi
        fi
    done
    
    if [[ $errors -eq 0 ]]; then
        log_success "All services have correct module paths!"
    else
        log_error "Found $errors services with incorrect module paths"
        return 1
    fi
}

# 恢复备份
restore_backups() {
    local project_root="$(get_project_root)"
    local services_dir="$project_root/services"
    
    log_info "Restoring backups..."
    
    for service_dir in "$services_dir"/*; do
        if [[ -d "$service_dir" ]]; then
            local backup_file="$service_dir/go.mod.backup"
            local original_file="$service_dir/go.mod"
            
            if [[ -f "$backup_file" ]]; then
                mv "$backup_file" "$original_file"
                log_info "Restored backup for $(basename "$service_dir")"
            fi
        fi
    done
    
    log_success "All backups restored"
}

# 清理备份文件
cleanup_backups() {
    local project_root="$(get_project_root)"
    local services_dir="$project_root/services"
    
    log_info "Cleaning up backup files..."
    
    for service_dir in "$services_dir"/*; do
        if [[ -d "$service_dir" ]]; then
            local backup_file="$service_dir/go.mod.backup"
            
            if [[ -f "$backup_file" ]]; then
                rm "$backup_file"
                log_info "Removed backup for $(basename "$service_dir")"
            fi
        fi
    done
    
    log_success "All backup files cleaned up"
}

# 显示帮助信息
show_help() {
    cat << EOF
CINA.CLUB 模块路径批量修正脚本

用法:
    $0 [选项]

选项:
    fix         修正所有服务的模块路径
    verify      验证修正结果
    restore     恢复所有备份文件
    cleanup     清理备份文件
    help        显示此帮助信息

示例:
    $0 fix              # 修正所有服务的模块路径
    $0 verify           # 验证修正结果
    $0 restore          # 恢复备份（如果修正有问题）
    $0 cleanup          # 清理备份文件（确认修正无误后）

注意事项:
    - 脚本会自动创建备份文件 (go.mod.backup)
    - 如果修正有问题，可以使用 restore 命令恢复
    - 确认修正无误后，使用 cleanup 命令清理备份文件
EOF
}

# 主函数
main() {
    case "${1:-}" in
        "fix")
            fix_all_services
            ;;
        "verify")
            verify_fixes
            ;;
        "restore")
            restore_backups
            ;;
        "cleanup")
            cleanup_backups
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        "")
            log_info "CINA.CLUB 模块路径批量修正脚本"
            log_info "使用 '$0 help' 查看帮助信息"
            log_info "使用 '$0 fix' 开始修正"
            ;;
        *)
            log_error "未知选项: $1"
            log_info "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 