/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package toolkit

import (
	"context"
	"fmt"

	"cina.club/services/ai-assistant-service/internal/application/port"
)

// SearchServicesTool represents a tool for searching services
type SearchServicesTool struct {
	name        string
	description string
}

// NewSearchServicesTool creates a new service search tool
func NewSearchServicesTool() port.Tool {
	return &SearchServicesTool{
		name:        "search_services",
		description: "Search service providers on the platform, supporting filtering by keywords, location, category, price range, etc.",
	}
}

// Name returns the tool name
func (t *SearchServicesTool) Name() string {
	return t.name
}

// Description returns the tool description
func (t *SearchServicesTool) Description() string {
	return t.description
}

// Category returns the tool category
func (t *SearchServicesTool) Category() port.ToolCategory {
	return port.ToolCategorySearch
}

// RequiresAuth returns whether authentication is required
func (t *SearchServicesTool) RequiresAuth() bool {
	return false // Search doesn't require authentication
}

// IsAsync returns whether this is an async tool
func (t *SearchServicesTool) IsAsync() bool {
	return false
}

// InputSchema returns the input parameter schema
func (t *SearchServicesTool) InputSchema() *port.JSONSchema {
	schema := port.NewObjectSchema(
		"Search services parameters",
		map[string]*port.JSONSchema{},
		[]string{"query"},
	)

	// Add properties
	schema.AddProperty("query", port.NewStringSchema("Search keywords", true))
	schema.AddProperty("location", port.NewStringSchema("Location (city or region)", false))
	schema.AddProperty("category", port.NewStringSchema("Service category", false))

	minPrice := float64(0)
	maxPrice := float64(100000)
	schema.AddProperty("min_price", port.NewIntegerSchema("Minimum price", &minPrice, &maxPrice))
	schema.AddProperty("max_price", port.NewIntegerSchema("Maximum price", &minPrice, &maxPrice))

	limitMin := float64(1)
	limitMax := float64(50)
	schema.AddProperty("limit", port.NewIntegerSchema("Limit number of results returned", &limitMin, &limitMax))

	return schema
}

// OutputSchema returns the output result schema
func (t *SearchServicesTool) OutputSchema() *port.JSONSchema {
	serviceSchema := port.NewObjectSchema(
		"Service information",
		map[string]*port.JSONSchema{},
		[]string{"id", "name", "provider_name"},
	)

	serviceSchema.AddProperty("id", port.NewStringSchema("Service ID", true))
	serviceSchema.AddProperty("name", port.NewStringSchema("Service name", true))
	serviceSchema.AddProperty("description", port.NewStringSchema("Service description", false))
	serviceSchema.AddProperty("provider_name", port.NewStringSchema("Service provider name", true))
	serviceSchema.AddProperty("provider_id", port.NewStringSchema("Service provider ID", false))
	serviceSchema.AddProperty("category", port.NewStringSchema("Service category", false))
	serviceSchema.AddProperty("location", port.NewStringSchema("Service location", false))
	serviceSchema.AddProperty("price", port.NewIntegerSchema("Price", nil, nil))
	serviceSchema.AddProperty("rating", port.NewNumberSchema("Rating", nil, nil))
	serviceSchema.AddProperty("review_count", port.NewIntegerSchema("Review count", nil, nil))

	resultSchema := port.NewObjectSchema(
		"Search result",
		map[string]*port.JSONSchema{},
		[]string{"services", "total_count"},
	)

	resultSchema.AddProperty("services", port.NewArraySchema("Service list", serviceSchema))
	resultSchema.AddProperty("total_count", port.NewIntegerSchema("Total count", nil, nil))
	resultSchema.AddProperty("has_more", port.NewBooleanSchema("Whether there are more results"))

	return resultSchema
}

// Execute executes the tool logic
func (t *SearchServicesTool) Execute(ctx context.Context, inputs map[string]interface{}) (*port.ToolResult, error) {
	// Extract parameters
	query, ok := inputs["query"].(string)
	if !ok || query == "" {
		return port.NewToolError("query is required and must be a string"), nil
	}

	location := ""
	if l, ok := inputs["location"].(string); ok {
		location = l
	}

	category := ""
	if c, ok := inputs["category"].(string); ok {
		category = c
	}

	var minPrice, maxPrice *float64
	if mp, ok := inputs["min_price"].(float64); ok {
		minPrice = &mp
	}
	if mp, ok := inputs["max_price"].(float64); ok {
		maxPrice = &mp
	}

	limit := 10
	if l, ok := inputs["limit"].(float64); ok {
		limit = int(l)
	}

	// TODO: Actual search logic
	// This should call the search-service gRPC interface
	result := t.searchServices(ctx, query, location, category, minPrice, maxPrice, limit)

	return port.NewToolResult(map[string]interface{}{
		"services":    result.Services,
		"total_count": result.TotalCount,
		"has_more":    result.HasMore,
	}), nil
}

// SearchResult represents the search result
type SearchResult struct {
	Services   []ServiceInfo `json:"services"`
	TotalCount int           `json:"total_count"`
	HasMore    bool          `json:"has_more"`
}

// ServiceInfo represents service information
type ServiceInfo struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Description  string  `json:"description"`
	ProviderName string  `json:"provider_name"`
	ProviderID   string  `json:"provider_id"`
	Category     string  `json:"category"`
	Location     string  `json:"location"`
	Price        int     `json:"price"`
	Rating       float64 `json:"rating"`
	ReviewCount  int     `json:"review_count"`
}

// searchServices searches for services (simplified implementation)
func (t *SearchServicesTool) searchServices(_ context.Context, query, location, category string, minPrice, maxPrice *float64, limit int) *SearchResult {
	// This is a simplified implementation, should call search-service gRPC interface in practice

	services := []ServiceInfo{
		{
			ID:           "service-001",
			Name:         fmt.Sprintf("%s Professional Service", query),
			Description:  fmt.Sprintf("Providing high-quality %s services", query),
			ProviderName: "Professional Service Provider A",
			ProviderID:   "provider-001",
			Category:     category,
			Location:     location,
			Price:        500,
			Rating:       4.8,
			ReviewCount:  128,
		},
		{
			ID:           "service-002",
			Name:         fmt.Sprintf("%s Premium Service", query),
			Description:  fmt.Sprintf("Experienced %s team", query),
			ProviderName: "Premium Service Provider B",
			ProviderID:   "provider-002",
			Category:     category,
			Location:     location,
			Price:        800,
			Rating:       4.9,
			ReviewCount:  256,
		},
		{
			ID:           "service-003",
			Name:         fmt.Sprintf("%s Elite Service", query),
			Description:  fmt.Sprintf("One-on-one %s customized service", query),
			ProviderName: "Elite Service Provider C",
			ProviderID:   "provider-003",
			Category:     category,
			Location:     location,
			Price:        1200,
			Rating:       4.7,
			ReviewCount:  89,
		},
	}

	// Apply price filtering
	filteredServices := []ServiceInfo{}
	for _, service := range services {
		if minPrice != nil && float64(service.Price) < *minPrice {
			continue
		}
		if maxPrice != nil && float64(service.Price) > *maxPrice {
			continue
		}
		filteredServices = append(filteredServices, service)
	}

	// Apply limit
	if len(filteredServices) > limit {
		filteredServices = filteredServices[:limit]
	}

	return &SearchResult{
		Services:   filteredServices,
		TotalCount: len(filteredServices),
		HasMore:    false,
	}
}
