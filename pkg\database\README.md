# pkg/database

A standardized database client factory with built-in observability for CINA.CLUB backend services.

## Overview

The `pkg/database` package provides production-ready database client factories for PostgreSQL, MongoDB, and Redis with automatic integration of OpenTelemetry tracing, structured logging, and metrics collection. It implements the factory pattern to create database clients with standardized observability, health checking, and configuration management.

## Features

- **Multiple Database Support**: PostgreSQL (pgx), MongoDB (mongo-driver), Redis (go-redis)
- **Built-in Observability**: Automatic OpenTelemetry tracing and structured logging for all operations
- **Health Checking**: Automatic connection health verification during client creation
- **Configuration-Driven**: Uses `pkg/config` for standardized configuration management
- **Connection Pooling**: Optimized connection pool configurations for each database type
- **Security**: Automatic sanitization of sensitive data in logs and traces
- **Production-Ready**: Implements best practices for connection management and error handling

## Quick Start

### 1. PostgreSQL

```go
import (
    "context"
    "log/slog"
    
    "pkg/database"
    "go.opentelemetry.io/otel/trace"
)

// Configure PostgreSQL
cfg := database.PostgresConfig{
    DSN:             "postgres://user:pass@localhost:5432/mydb",
    MaxConns:        25,
    MinConns:        5,
    MaxConnLifetime: time.Hour,
    MaxConnIdleTime: 30 * time.Minute,
}

// Create connection pool with observability
pool, err := database.NewPostgresPool(ctx, cfg, logger, tracer)
if err != nil {
    log.Fatalf("Failed to create postgres pool: %v", err)
}
defer pool.Close()

// Use the pool for database operations
var userCount int
err = pool.QueryRow(ctx, "SELECT COUNT(*) FROM users").Scan(&userCount)
```

### 2. Redis

```go
// Configure Redis
cfg := database.RedisConfig{
    Addr:     "localhost:6379",
    Password: "",
    DB:       0,
    PoolSize: 10,
}

// Create Redis client with observability
client, err := database.NewRedisClient(ctx, cfg, logger, tracer)
if err != nil {
    log.Fatalf("Failed to create redis client: %v", err)
}
defer client.Close()

// Use the client for operations
err = client.Set(ctx, "key", "value", time.Hour).Err()
```

### 3. MongoDB

```go
// Configure MongoDB
cfg := database.MongoConfig{
    URI:      "mongodb://localhost:27017",
    Database: "myapp",
    MaxPoolSize: 100,
    ReadPreference: "primary",
}

// Create MongoDB client with observability
client, err := database.NewMongoClient(ctx, cfg, logger, tracer)
if err != nil {
    log.Fatalf("Failed to create mongo client: %v", err)
}
defer client.Disconnect(ctx)

// Use the client for operations
collection := client.Database("myapp").Collection("users")
```

## Configuration

### PostgreSQL Configuration

```go
type PostgresConfig struct {
    DSN             string        `mapstructure:"dsn" validate:"required,dsn"`
    MaxConns        int32         `mapstructure:"max_conns" validate:"gte=1" default:"25"`
    MinConns        int32         `mapstructure:"min_conns" validate:"gte=0" default:"5"`
    MaxConnLifetime time.Duration `mapstructure:"max_conn_lifetime" default:"1h"`
    MaxConnIdleTime time.Duration `mapstructure:"max_conn_idle_time" default:"30m"`
    HealthCheckPeriod time.Duration `mapstructure:"health_check_period" default:"1m"`
    SSLMode         string        `mapstructure:"ssl_mode" validate:"oneof=disable allow prefer require verify-ca verify-full" default:"prefer"`
}
```

### Redis Configuration

```go
type RedisConfig struct {
    Addr            string        `mapstructure:"addr" validate:"required" default:"localhost:6379"`
    Password        string        `mapstructure:"password"`
    DB              int           `mapstructure:"db" validate:"gte=0" default:"0"`
    PoolSize        int           `mapstructure:"pool_size" validate:"gte=1" default:"10"`
    DialTimeout     time.Duration `mapstructure:"dial_timeout" default:"5s"`
    ReadTimeout     time.Duration `mapstructure:"read_timeout" default:"3s"`
    WriteTimeout    time.Duration `mapstructure:"write_timeout" default:"3s"`
    MaxRetries      int           `mapstructure:"max_retries" validate:"gte=0" default:"3"`
    TLSEnabled      bool          `mapstructure:"tls_enabled" default:"false"`
}
```

### MongoDB Configuration

```go
type MongoConfig struct {
    URI                    string        `mapstructure:"uri" validate:"required"`
    Database               string        `mapstructure:"database" validate:"required"`
    MaxPoolSize            uint64        `mapstructure:"max_pool_size" validate:"gte=1" default:"100"`
    ConnectTimeout         time.Duration `mapstructure:"connect_timeout" default:"10s"`
    ServerSelectionTimeout time.Duration `mapstructure:"server_selection_timeout" default:"30s"`
    ReadPreference         string        `mapstructure:"read_preference" validate:"oneof=primary primaryPreferred secondary secondaryPreferred nearest" default:"primary"`
    WriteConcern           string        `mapstructure:"write_concern" default:"majority"`
    TLSEnabled             bool          `mapstructure:"tls_enabled" default:"false"`
}
```

## Observability Features

### Automatic Tracing

All database operations are automatically traced with OpenTelemetry:

- **PostgreSQL**: Query execution, connection events, transaction boundaries
- **Redis**: Individual commands, pipelines, connection events
- **MongoDB**: Command execution, collection operations

### Structured Logging

All operations generate structured logs with:

- Operation timing and performance metrics
- Error details and stack traces
- Connection pool statistics
- Sanitized query information (no sensitive data)

### Health Monitoring

Each client factory includes automatic health checking:

- Connection verification during startup
- Basic operation testing (ping, simple query)
- Graceful failure handling with detailed error messages

## Service Integration

### Typical Service Usage

```go
// services/user-service/cmd/server/main.go
package main

import (
    "context"
    "log/slog"
    
    "pkg/config"
    "pkg/database"
    "pkg/logger"
    "pkg/tracing"
)

type ServiceConfig struct {
    Database struct {
        Postgres database.PostgresConfig `mapstructure:"postgres"`
        Redis    database.RedisConfig    `mapstructure:"redis"`
    } `mapstructure:"database"`
}

func main() {
    // Load configuration
    var cfg ServiceConfig
    config.MustLoadConfig("./config.yaml", &cfg)
    
    // Initialize observability
    logger := logger.NewLogger(cfg.Logger, "user-service", "v1.0.0")
    tracer := tracing.InitTracer("user-service")
    
    // Create database clients
    pgPool, err := database.NewPostgresPool(ctx, cfg.Database.Postgres, logger, tracer)
    if err != nil {
        logger.Error("Failed to create postgres pool", "error", err)
        os.Exit(1)
    }
    defer pgPool.Close()
    
    redisClient, err := database.NewRedisClient(ctx, cfg.Database.Redis, logger, tracer)
    if err != nil {
        logger.Error("Failed to create redis client", "error", err)
        os.Exit(1)
    }
    defer redisClient.Close()
    
    // Initialize repositories with database clients
    userRepo := repository.NewUserRepository(pgPool)
    cacheRepo := cache.NewUserCache(redisClient)
    
    // Start service...
}
```

### Configuration File Example

```yaml
# config.yaml
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/userdb"
    max_conns: 25
    min_conns: 5
    ssl_mode: "prefer"
  
  redis:
    addr: "localhost:6379"
    db: 0
    pool_size: 10
    tls_enabled: false
```

## Advanced Usage

### Wrapper Types

The package provides wrapper types for enhanced functionality:

```go
// PostgreSQL wrapper with additional utilities
wrapper, err := database.NewPostgresPoolWithWrapper(ctx, cfg, logger, tracer)
defer wrapper.Close()

// Get connection pool statistics
stats := wrapper.Stats()
wrapper.LogStats() // Log current statistics

// Execute within transaction
err = wrapper.WithTransaction(ctx, func(tx pgx.Tx) error {
    // Your transactional operations here
    return nil
})
```

### Redis Pipeline Support

```go
// Redis wrapper with pipeline support
wrapper, err := database.NewRedisClientWithWrapper(ctx, cfg, logger, tracer)
defer wrapper.Close()

// Execute pipeline
cmds, err := wrapper.WithPipeline(ctx, func(pipe redis.Pipeliner) error {
    pipe.Set(ctx, "key1", "value1", 0)
    pipe.Set(ctx, "key2", "value2", 0)
    return nil
})
```

### MongoDB Transactions

```go
// MongoDB wrapper with transaction support
wrapper, err := database.NewMongoClientWithWrapper(ctx, cfg, logger, tracer)
defer wrapper.Close(ctx)

// Execute within transaction
result, err := wrapper.WithTransaction(ctx, func(sc mongo.SessionContext) (interface{}, error) {
    // Your transactional operations here
    return nil, nil
})
```

## Performance Considerations

### Connection Pool Tuning

- **PostgreSQL**: Default 25 max connections, 5 min connections
- **Redis**: Default 10 connections per pool
- **MongoDB**: Default 100 max connections

### Observability Overhead

- Tracing overhead: < 5% performance impact
- Logging: Asynchronous structured logging with minimal impact
- Health checks: Performed only during client creation

## Security Features

- **DSN Sanitization**: Connection strings are sanitized in logs and traces
- **Query Parameter Protection**: SQL parameters are not logged to prevent data leaks
- **Sensitive Command Detection**: Redis AUTH and similar commands are automatically redacted
- **TLS Support**: Configurable TLS/SSL for all database types

## Error Handling

The package provides comprehensive error handling:

- **Connection Errors**: Clear error messages with connection details
- **Configuration Errors**: Validation errors with specific field information
- **Health Check Failures**: Detailed failure reasons and troubleshooting hints
- **Operation Errors**: Contextual error information with timing data

## Best Practices

1. **Always use context**: Pass context to all database operations
2. **Handle connection lifecycle**: Properly close clients in defer statements
3. **Monitor pool statistics**: Use wrapper types to monitor connection health
4. **Configure timeouts**: Set appropriate timeouts for your use case
5. **Use transactions wisely**: Leverage transaction helpers for data consistency
6. **Monitor observability data**: Use tracing and logging for performance optimization

## Dependencies

- `github.com/jackc/pgx/v5` - PostgreSQL driver
- `github.com/redis/go-redis/v9` - Redis client
- `go.mongodb.org/mongo-driver` - MongoDB driver
- `go.opentelemetry.io/otel` - OpenTelemetry tracing
- `pkg/config` - Configuration management
- Standard library: `log/slog`, `context`, `time`

## License

Copyright (c) 2025 Cina.Club, All rights reserved. 