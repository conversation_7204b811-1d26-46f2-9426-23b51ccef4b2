/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System;
using System.Runtime.InteropServices;

namespace CinaClub.Infrastructure.GoBridge;

/// <summary>
/// Go核心库的P/Invoke方法定义
/// 所有与Go DLL交互的原生方法都在这里定义
/// </summary>
internal static class NativeMethods
{
    private const string DllName = "core_go.dll";

    #region 加密相关函数

    /// <summary>
    /// 从密码派生主密钥
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <returns>加密结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern CryptoResult DeriveKeyFromPassword(GoString password, GoSlice salt);

    /// <summary>
    /// 对称加密
    /// </summary>
    /// <param name="key">密钥</param>
    /// <param name="plaintext">明文</param>
    /// <returns>加密结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern CryptoResult EncryptSymmetric(GoSlice key, GoSlice plaintext);

    /// <summary>
    /// 对称解密
    /// </summary>
    /// <param name="key">密钥</param>
    /// <param name="ciphertext">密文</param>
    /// <returns>解密结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern CryptoResult DecryptSymmetric(GoSlice key, GoSlice ciphertext);

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    /// <param name="keySize">密钥长度</param>
    /// <returns>密钥数据</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern CryptoResult GenerateRandomKey(int keySize);

    #endregion

    #region AI相关函数

    /// <summary>
    /// 初始化AI引擎
    /// </summary>
    /// <param name="modelPath">模型路径</param>
    /// <returns>初始化结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern GoError InitAIEngine(GoString modelPath);

    /// <summary>
    /// 创建AI会话
    /// </summary>
    /// <returns>会话句柄</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern IntPtr CreateAISession();

    /// <summary>
    /// 销毁AI会话
    /// </summary>
    /// <param name="session">会话句柄</param>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern void DestroyAISession(IntPtr session);

    /// <summary>
    /// 同步预测
    /// </summary>
    /// <param name="session">会话句柄</param>
    /// <param name="prompt">提示文本</param>
    /// <returns>预测结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern AIResult PredictSync(IntPtr session, GoString prompt);

    /// <summary>
    /// 开始流式预测
    /// </summary>
    /// <param name="session">会话句柄</param>
    /// <param name="prompt">提示文本</param>
    /// <param name="callback">回调函数</param>
    /// <returns>错误状态</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern GoError PredictStream(IntPtr session, GoString prompt, TokenCallback callback);

    /// <summary>
    /// 停止当前预测
    /// </summary>
    /// <param name="session">会话句柄</param>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern void StopPrediction(IntPtr session);

    #endregion

    #region 数据同步相关函数

    /// <summary>
    /// 初始化数据同步引擎
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>初始化结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern GoError InitDataSync(GoString deviceId);

    /// <summary>
    /// 同步数据到云端
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="callback">进度回调</param>
    /// <returns>同步结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern GoError SyncToCloud(GoSlice data, GoString dataType, ProgressCallback callback);

    /// <summary>
    /// 从云端拉取数据
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <param name="callback">进度回调</param>
    /// <returns>数据结果</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern CryptoResult PullFromCloud(GoString dataType, ProgressCallback callback);

    #endregion

    #region 内存管理

    /// <summary>
    /// 释放Go分配的内存
    /// </summary>
    /// <param name="ptr">内存指针</param>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern void FreeGoMemory(IntPtr ptr);

    /// <summary>
    /// 释放Go Slice
    /// </summary>
    /// <param name="slice">Slice结构体</param>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern void FreeGoSlice(GoSlice slice);

    /// <summary>
    /// 释放Go String
    /// </summary>
    /// <param name="str">String结构体</param>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern void FreeGoString(GoString str);

    #endregion

    #region 工具函数

    /// <summary>
    /// 获取版本信息
    /// </summary>
    /// <returns>版本字符串</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern GoString GetVersion();

    /// <summary>
    /// 检查库是否可用
    /// </summary>
    /// <returns>可用性状态</returns>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern bool IsLibraryReady();

    /// <summary>
    /// 设置日志级别
    /// </summary>
    /// <param name="level">日志级别</param>
    [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
    internal static extern void SetLogLevel(int level);

    #endregion
} 