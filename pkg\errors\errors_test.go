/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package errors

import (
	"errors"
	"fmt"
	"strings"
	"testing"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestErrorCode(t *testing.T) {
	tests := []struct {
		name     string
		code     ErrorCode
		grpcCode codes.Code
		valid    bool
	}{
		{"OK", OK, codes.OK, true},
		{"InvalidArgument", InvalidArgument, codes.InvalidArgument, true},
		{"NotFound", NotFound, codes.NotFound, true},
		{"Internal", Internal, codes.Internal, true},
		{"Invalid", ErrorCode("Invalid"), codes.Internal, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test ToGRPCCode
			grpcCode := tt.code.ToGRPCCode()
			if grpcCode != tt.grpcCode {
				t.Errorf("ToGRPCCode() = %v, want %v", grpcCode, tt.grpcCode)
			}

			// Test String
			if tt.code.String() != string(tt.code) {
				t.Errorf("String() = %v, want %v", tt.code.String(), string(tt.code))
			}

			// Test IsValid
			if tt.code.IsValid() != tt.valid {
				t.Errorf("IsValid() = %v, want %v", tt.code.IsValid(), tt.valid)
			}
		})
	}
}

func TestFromGRPCCode(t *testing.T) {
	tests := []struct {
		grpcCode  codes.Code
		errorCode ErrorCode
	}{
		{codes.OK, OK},
		{codes.InvalidArgument, InvalidArgument},
		{codes.NotFound, NotFound},
		{codes.Internal, Internal},
		{codes.Code(999), Internal}, // Invalid code should map to Internal
	}

	for _, tt := range tests {
		t.Run(tt.grpcCode.String(), func(t *testing.T) {
			errorCode := FromGRPCCode(tt.grpcCode)
			if errorCode != tt.errorCode {
				t.Errorf("FromGRPCCode(%v) = %v, want %v", tt.grpcCode, errorCode, tt.errorCode)
			}
		})
	}
}

func TestAppError_New(t *testing.T) {
	err := New(InvalidArgument, "test message")

	if err.Code != InvalidArgument {
		t.Errorf("Code = %v, want %v", err.Code, InvalidArgument)
	}

	if err.Message != "test message" {
		t.Errorf("Message = %v, want %v", err.Message, "test message")
	}

	if err.Cause != nil {
		t.Errorf("Cause = %v, want nil", err.Cause)
	}

	if err.Metadata == nil {
		t.Error("Metadata should be initialized")
	}

	if err.stack == nil {
		t.Error("Stack should be captured")
	}
}

func TestAppError_Newf(t *testing.T) {
	err := Newf(InvalidArgument, "test %s %d", "message", 123)

	if err.Message != "test message 123" {
		t.Errorf("Message = %v, want %v", err.Message, "test message 123")
	}
}

func TestAppError_Wrap(t *testing.T) {
	originalErr := errors.New("original error")
	err := Wrap(originalErr, InvalidArgument, "wrapped message")

	if err.Code != InvalidArgument {
		t.Errorf("Code = %v, want %v", err.Code, InvalidArgument)
	}

	if err.Message != "wrapped message" {
		t.Errorf("Message = %v, want %v", err.Message, "wrapped message")
	}

	if err.Cause != originalErr {
		t.Errorf("Cause = %v, want %v", err.Cause, originalErr)
	}
}

func TestAppError_Wrapf(t *testing.T) {
	originalErr := errors.New("original error")
	err := Wrapf(originalErr, InvalidArgument, "wrapped %s", "message")

	if err.Message != "wrapped message" {
		t.Errorf("Message = %v, want %v", err.Message, "wrapped message")
	}

	if err.Cause != originalErr {
		t.Errorf("Cause = %v, want %v", err.Cause, originalErr)
	}
}

func TestAppError_Error(t *testing.T) {
	originalErr := errors.New("original error")
	err := Wrap(originalErr, InvalidArgument, "test message")

	errorString := err.Error()
	if !strings.Contains(errorString, "InvalidArgument") {
		t.Errorf("Error() should contain error code")
	}
	if !strings.Contains(errorString, "test message") {
		t.Errorf("Error() should contain message")
	}
	if !strings.Contains(errorString, "original error") {
		t.Errorf("Error() should contain cause")
	}
}

func TestAppError_Unwrap(t *testing.T) {
	originalErr := errors.New("original error")
	err := Wrap(originalErr, InvalidArgument, "test message")

	unwrapped := err.Unwrap()
	if unwrapped != originalErr {
		t.Errorf("Unwrap() = %v, want %v", unwrapped, originalErr)
	}
}

func TestAppError_Format(t *testing.T) {
	err := New(InvalidArgument, "test message")

	// Test simple format
	simple := fmt.Sprintf("%s", err)
	if !strings.Contains(simple, "InvalidArgument") {
		t.Errorf("Simple format should contain error code")
	}

	// Test detailed format
	detailed := fmt.Sprintf("%+v", err)
	if !strings.Contains(detailed, "Error Code: InvalidArgument") {
		t.Errorf("Detailed format should contain error code label")
	}
	if !strings.Contains(detailed, "Message: test message") {
		t.Errorf("Detailed format should contain message label")
	}
}

func TestWithMeta(t *testing.T) {
	err := New(InvalidArgument, "test message")
	errWithMeta := WithMeta(err, "field", "email")

	appErr, ok := errWithMeta.(*AppError)
	if !ok {
		t.Fatal("WithMeta should return AppError")
	}

	if appErr.Metadata["field"] != "email" {
		t.Errorf("Metadata[field] = %v, want %v", appErr.Metadata["field"], "email")
	}
}

func TestWithMeta_NonAppError(t *testing.T) {
	originalErr := errors.New("original error")
	errWithMeta := WithMeta(originalErr, "field", "email")

	appErr, ok := errWithMeta.(*AppError)
	if !ok {
		t.Fatal("WithMeta should wrap non-AppError as AppError")
	}

	if appErr.Code != Internal {
		t.Errorf("Code = %v, want %v", appErr.Code, Internal)
	}

	if appErr.Metadata["field"] != "email" {
		t.Errorf("Metadata[field] = %v, want %v", appErr.Metadata["field"], "email")
	}
}

func TestWithMetadata(t *testing.T) {
	err := New(InvalidArgument, "test message")
	metadata := map[string]string{
		"field1": "value1",
		"field2": "value2",
	}
	errWithMeta := WithMetadata(err, metadata)

	appErr, ok := errWithMeta.(*AppError)
	if !ok {
		t.Fatal("WithMetadata should return AppError")
	}

	for k, v := range metadata {
		if appErr.Metadata[k] != v {
			t.Errorf("Metadata[%s] = %v, want %v", k, appErr.Metadata[k], v)
		}
	}
}

func TestIsCode(t *testing.T) {
	err := New(InvalidArgument, "test message")

	if !IsCode(err, InvalidArgument) {
		t.Error("IsCode should return true for matching code")
	}

	if IsCode(err, NotFound) {
		t.Error("IsCode should return false for non-matching code")
	}

	if IsCode(nil, InvalidArgument) {
		t.Error("IsCode should return false for nil error")
	}

	// Test with wrapped error
	wrappedErr := Wrap(err, NotFound, "wrapped")
	if !IsCode(wrappedErr, NotFound) {
		t.Error("IsCode should return true for wrapped error code")
	}
}

func TestGetCode(t *testing.T) {
	err := New(InvalidArgument, "test message")

	if GetCode(err) != InvalidArgument {
		t.Errorf("GetCode() = %v, want %v", GetCode(err), InvalidArgument)
	}

	if GetCode(nil) != OK {
		t.Errorf("GetCode(nil) = %v, want %v", GetCode(nil), OK)
	}

	// Test with non-AppError
	originalErr := errors.New("original error")
	if GetCode(originalErr) != Internal {
		t.Errorf("GetCode(non-AppError) = %v, want %v", GetCode(originalErr), Internal)
	}
}

func TestGetMetadata(t *testing.T) {
	err := New(InvalidArgument, "test message")
	err = WithMeta(err, "field", "email").(*AppError)
	
	metadata := GetMetadata(err)
	if metadata["field"] != "email" {
		t.Errorf("GetMetadata()[field] = %v, want %v", metadata["field"], "email")
	}

	// Test mutation protection
	metadata["field"] = "modified"
	originalMetadata := GetMetadata(err)
	if originalMetadata["field"] != "email" {
		t.Error("GetMetadata should return a copy to prevent mutation")
	}

	// Test with nil error
	if GetMetadata(nil) != nil {
		t.Error("GetMetadata(nil) should return nil")
	}

	// Test with non-AppError
	originalErr := errors.New("original error")
	metadata = GetMetadata(originalErr)
	if metadata == nil {
		t.Error("GetMetadata(non-AppError) should return empty map")
	}
}

func TestGetMessage(t *testing.T) {
	err := New(InvalidArgument, "test message")

	if GetMessage(err) != "test message" {
		t.Errorf("GetMessage() = %v, want %v", GetMessage(err), "test message")
	}

	if GetMessage(nil) != "" {
		t.Errorf("GetMessage(nil) = %v, want empty string", GetMessage(nil))
	}

	// Test with non-AppError
	originalErr := errors.New("original error")
	if GetMessage(originalErr) != "original error" {
		t.Errorf("GetMessage(non-AppError) = %v, want %v", GetMessage(originalErr), "original error")
	}
}

func TestGetCause(t *testing.T) {
	originalErr := errors.New("original error")
	err := Wrap(originalErr, InvalidArgument, "wrapped message")

	if GetCause(err) != originalErr {
		t.Errorf("GetCause() = %v, want %v", GetCause(err), originalErr)
	}

	if GetCause(nil) != nil {
		t.Error("GetCause(nil) should return nil")
	}

	// Test with error without cause
	simpleErr := New(InvalidArgument, "simple error")
	if GetCause(simpleErr) != nil {
		t.Error("GetCause() should return nil for error without cause")
	}
}

func TestToGRPCStatus(t *testing.T) {
	err := New(InvalidArgument, "test message")
	err = WithMeta(err, "field", "email").(*AppError)
	
	st := ToGRPCStatus(err)

	if st.Code() != codes.InvalidArgument {
		t.Errorf("gRPC code = %v, want %v", st.Code(), codes.InvalidArgument)
	}

	if st.Message() != "test message" {
		t.Errorf("gRPC message = %v, want %v", st.Message(), "test message")
	}

	// Test with nil error
	nilSt := ToGRPCStatus(nil)
	if nilSt.Code() != codes.OK {
		t.Errorf("ToGRPCStatus(nil) code = %v, want %v", nilSt.Code(), codes.OK)
	}

	// Test with non-AppError
	originalErr := errors.New("original error")
	originalSt := ToGRPCStatus(originalErr)
	if originalSt.Code() != codes.Internal {
		t.Errorf("ToGRPCStatus(non-AppError) code = %v, want %v", originalSt.Code(), codes.Internal)
	}
}

func TestFromGRPCError(t *testing.T) {
	// Create a gRPC error
	st := status.New(codes.InvalidArgument, "test message")
	grpcErr := st.Err()

	appErr := FromGRPCError(grpcErr)

	if appErr.Code != InvalidArgument {
		t.Errorf("Code = %v, want %v", appErr.Code, InvalidArgument)
	}

	if appErr.Message != "test message" {
		t.Errorf("Message = %v, want %v", appErr.Message, "test message")
	}

	// Test with nil error
	if FromGRPCError(nil) != nil {
		t.Error("FromGRPCError(nil) should return nil")
	}

	// Test with non-gRPC error
	originalErr := errors.New("original error")
	appErr = FromGRPCError(originalErr)
	if appErr.Code != Internal {
		t.Errorf("FromGRPCError(non-gRPC) code = %v, want %v", appErr.Code, Internal)
	}
}

func TestToGRPCError(t *testing.T) {
	err := New(InvalidArgument, "test message")
	grpcErr := ToGRPCError(err)

	st, ok := status.FromError(grpcErr)
	if !ok {
		t.Fatal("ToGRPCError should return gRPC error")
	}

	if st.Code() != codes.InvalidArgument {
		t.Errorf("gRPC code = %v, want %v", st.Code(), codes.InvalidArgument)
	}

	// Test with nil error
	if ToGRPCError(nil) != nil {
		t.Error("ToGRPCError(nil) should return nil")
	}
}

func TestIsGRPCError(t *testing.T) {
	st := status.New(codes.InvalidArgument, "test message")
	grpcErr := st.Err()

	if !IsGRPCError(grpcErr) {
		t.Error("IsGRPCError should return true for gRPC error")
	}

	originalErr := errors.New("original error")
	if IsGRPCError(originalErr) {
		t.Error("IsGRPCError should return false for non-gRPC error")
	}

	if IsGRPCError(nil) {
		t.Error("IsGRPCError should return false for nil error")
	}
}

func TestGetGRPCCode(t *testing.T) {
	err := New(InvalidArgument, "test message")

	if GetGRPCCode(err) != codes.InvalidArgument {
		t.Errorf("GetGRPCCode() = %v, want %v", GetGRPCCode(err), codes.InvalidArgument)
	}

	if GetGRPCCode(nil) != codes.OK {
		t.Errorf("GetGRPCCode(nil) = %v, want %v", GetGRPCCode(nil), codes.OK)
	}

	// Test with gRPC error
	st := status.New(codes.NotFound, "not found")
	grpcErr := st.Err()
	if GetGRPCCode(grpcErr) != codes.NotFound {
		t.Errorf("GetGRPCCode(gRPC error) = %v, want %v", GetGRPCCode(grpcErr), codes.NotFound)
	}

	// Test with non-AppError, non-gRPC error
	originalErr := errors.New("original error")
	if GetGRPCCode(originalErr) != codes.Internal {
		t.Errorf("GetGRPCCode(other error) = %v, want %v", GetGRPCCode(originalErr), codes.Internal)
	}
}

func TestRoundTripGRPCConversion(t *testing.T) {
	// Test complete round trip: AppError -> gRPC -> AppError
	original := New(InvalidArgument, "test message")
	original = WithMeta(original, "field", "email").(*AppError)

	// Convert to gRPC and back
	grpcErr := ToGRPCError(original)
	recovered := FromGRPCError(grpcErr)

	if recovered.Code != original.Code {
		t.Errorf("Round trip code = %v, want %v", recovered.Code, original.Code)
	}

	if recovered.Message != original.Message {
		t.Errorf("Round trip message = %v, want %v", recovered.Message, original.Message)
	}

	// Note: metadata may not survive the round trip in this simplified implementation
	// In a full implementation with proper protobuf support, metadata should be preserved
}

// Benchmark tests
func BenchmarkNew(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = New(InvalidArgument, "test message")
	}
}

func BenchmarkWrap(b *testing.B) {
	originalErr := errors.New("original error")
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = Wrap(originalErr, InvalidArgument, "test message")
	}
}

func BenchmarkToGRPCStatus(b *testing.B) {
	err := New(InvalidArgument, "test message")
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = ToGRPCStatus(err)
	}
}

func BenchmarkFromGRPCError(b *testing.B) {
	st := status.New(codes.InvalidArgument, "test message")
	grpcErr := st.Err()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = FromGRPCError(grpcErr)
	}
}
