好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/database`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`pkg/database`的职责、接口、集成策略和最佳实践，作为所有后端服务统一数据访问层的权威设计蓝图。

---
### CINA.CLUB - 共享后端库 `pkg/database` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师/DBA负责人]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计与集成策略](#3-核心设计与集成策略)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [使用示例与最佳实践](#6-使用示例与最佳实践)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的微服务架构中，每个服务都需要与一种或多种数据存储（PostgreSQL, MongoDB, Redis等）进行交互。为了避免在每个服务中重复编写数据库连接、配置、监控和错误处理的模板代码，`pkg/database` 包应运而生。其目的在于提供一套**标准化的、可观测的、高性能的数据库客户端工厂**，封装了数据库连接的最佳实践，使业务开发人员能够以统一、简单、可靠的方式获取和使用数据库连接。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   提供用于创建**PostgreSQL**连接池的工厂函数。
    *   提供用于创建**MongoDB**客户端的工厂函数。
    *   提供用于创建**Redis**客户端的工厂函数。
    *   自动将**可观测性**（Tracing, Metrics, Logging）集成到所有创建的数据库客户端中。
    *   （可选）提供对GORM的封装，如果项目统一使用GORM作为ORM。
*   **范围之外 (Out-of-Scope)**:
    *   **数据库Schema和迁移**: 由各个服务自行管理（使用`golang-migrate`等工具）。
    *   **具体的CRUD业务逻辑**: 这是各个服务`repository`层的职责。
    *   **数据库的部署和管理**: 由`infra/`和DevOps/SRE团队负责。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/database` 是位于`pkg/`目录下的一个核心基础设施库。它被设计为一个纯功能的Go包，所有需要持久化或缓存数据的微服务，在启动时都会调用`pkg/database`提供的函数来初始化其数据库客户端。

#### 2.2. 设计原则
*   **工厂模式 (Factory Pattern)**: 提供简单、统一的`New...`函数来创建不同类型的数据库客户端。
*   **可观测性内建 (Observability by Default)**: 任何通过本包创建的客户端，都必须自动集成`pkg/tracing`和`pkg/logger`，无需业务开发者额外配置。所有数据库查询都应自动生成Trace Span和详细日志。
*   **配置驱动**: 客户端的所有参数（DSN, 连接池大小, 超时）都必须通过`pkg/config`加载的结构化配置来提供。
*   **性能导向**: 默认配置应遵循各数据库客户端官方推荐的最佳性能实践（如使用`pgxpool`而非`database/sql`）。
*   **资源安全**: 确保连接池能被优雅地创建和关闭，防止连接泄漏。

---

### 3. 核心设计与集成策略

#### 3.1. 可观测性集成
这是`pkg/database`的核心价值所在。所有工厂函数在创建客户端时，必须自动注入相应的**拦截器(Interceptor)**或**钩子(Hook)**。

*   **PostgreSQL (`pgx`)**: 使用`pgxpool`的`AfterConnect`和`Acquire`钩子，或者包装`pgx`的查询接口，来注入追踪和日志逻辑。
*   **MongoDB (`mongo-go-driver`)**: 使用`go.mongodb.org/mongo-driver/event`中的`CommandMonitor`来监控所有数据库命令，并生成Trace Span和日志。
*   **Redis (`go-redis`)**: 使用`redis.Client.AddHook()`方法来添加一个自定义钩子，该钩子在每个命令执行前后记录追踪和日志。

#### 3.2. 依赖关系
`pkg/database` 明确依赖于Monorepo中的其他`pkg`：
*   `pkg/config`: 用于接收数据库配置。
*   `pkg/logger`: 用于记录数据库操作日志。
*   `pkg/tracing`: 用于为数据库操作创建Trace Span。
*   `pkg/errors`: 用于将数据库驱动的特定错误，转换为标准的`AppError`。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. PostgreSQL 客户端工厂
*   **FR4.1.1 (基于pgxpool)**: 必须提供`NewPostgresPool(cfg PostgresConfig)`函数，使用`jackc/pgx/v5/pgxpool`创建连接池。
*   **FR4.1.2 (配置化)**: 函数必须接收一个`PostgresConfig`结构体，包含`DSN`, `MaxConns`, `MinConns`, `MaxConnIdleTime`, `MaxConnLifetime`等所有可配置参数。
*   **FR4.1.3 (可观测性)**: 创建的`pgxpool.Pool`必须自动集成OpenTelemetry追踪，使得每个SQL查询都成为一个独立的Trace Span。查询日志（包括慢查询）必须被记录。
*   **FR4.1.4 (健康检查)**: 工厂函数在返回连接池之前，必须执行一次`Ping`操作，以确保数据库连接是健康的。

#### 4.2. MongoDB 客户端工厂
*   **FR4.2.1 (基于官方驱动)**: 必须提供`NewMongoClient(ctx context.Context, cfg MongoConfig)`函数，使用`go.mongodb.org/mongo-driver/mongo`创建客户端。
*   **FR4.2.2 (配置化)**: 函数必须接收一个`MongoConfig`结构体，包含`URI`, `MaxPoolSize`, `MinPoolSize`, `MaxConnIdleTime`等参数。
*   **FR4.2.3 (可观测性)**: 创建的`mongo.Client`必须通过`event.CommandMonitor`集成OpenTelemetry追踪。
*   **FR4.2.4 (健康检查)**: 工厂函数在返回客户端之前，必须执行一次`Ping`操作。

#### 4.3. Redis 客户端工厂
*   **FR4.3.1 (基于go-redis)**: 必须提供`NewRedisClient(cfg RedisConfig)`函数，使用`github.com/redis/go-redis/v9`创建客户端。
*   **FR4.3.2 (配置化)**: 函数必须接收一个`RedisConfig`结构体，包含`Addr`, `Password`, `DB`, `PoolSize`等参数。
*   **FR4.3.3 (可观测性)**: 创建的`redis.Client`必须通过`AddHook`集成OpenTelemetry追踪。
*   **FR4.3.4 (健康检查)**: 工厂函数在返回客户端之前，必须执行一次`Ping`操作。

#### 4.4. GORM 封装 (可选)
*   **FR4.4.1 (GORM实例创建)**: 如果项目决定使用GORM，必须提供`NewGORMClient(cfg GORMConfig)`函数。
*   **FR4.4.2 (底层连接)**: 该函数内部应调用**FR4.1.1**来创建底层的`pgx`连接，然后将其传递给GORM进行初始化。
*   **FR4.4.3 (GORM插件)**: 必须为GORM实例自动注册OpenTelemetry的GORM插件，以实现追踪。

---

### 5. 接口定义 (API Specification)

```go
// pkg/database/postgres.go

type PostgresConfig struct {
    DSN             string        `mapstructure:"dsn" validate:"required"`
    MaxConns        int32         `mapstructure:"max_conns" validate:"gte=1"`
    MinConns        int32         `mapstructure:"min_conns" validate:"gte=0"`
    MaxConnLifetime time.Duration `mapstructure:"max_conn_lifetime"`
    MaxConnIdleTime time.Duration `mapstructure:"max_conn_idle_time"`
}

// NewPostgresPool 创建一个新的、带可观测性的PostgreSQL连接池。
func NewPostgresPool(ctx context.Context, cfg PostgresConfig, logger *slog.Logger, tracer trace.Tracer) (*pgxpool.Pool, error) { ... }


// pkg/database/redis.go

type RedisConfig struct {
    Addr     string `mapstructure:"addr" validate:"required"`
    Password string `mapstructure:"password"`
    DB       int    `mapstructure:"db" validate:"gte=0"`
    PoolSize int    `mapstructure:"pool_size"`
}

// NewRedisClient 创建一个新的、带可观测性的Redis客户端。
func NewRedisClient(cfg RedisConfig, logger *slog.Logger, tracer trace.Tracer) (*redis.Client, error) { ... }


// pkg/database/mongo.go
// ... 类似定义
```

---

### 6. 使用示例与最佳实践

#### 6.1. 在微服务中初始化
在每个服务的`cmd/server/main.go`或依赖注入容器中：
```go
// ...
// 1. 加载配置
var cfg MyServiceConfig
config.Load("./config.yaml", &cfg)

// 2. 初始化可观测性组件
logger := logger.NewLogger(cfg.Logger)
tracerProvider := tracing.InitTracerProvider(...)
tracer := tracerProvider.Tracer("my-service")

// 3. 使用pkg/database创建数据库客户端
pgPool, err := database.NewPostgresPool(ctx, cfg.Database.Postgres, logger, tracer)
if err != nil {
    logger.Error("failed to connect to postgres", "error", err)
    os.Exit(1)
}
defer pgPool.Close()

redisClient, err := database.NewRedisClient(cfg.Database.Redis, logger, tracer)
if err != nil {
    // ...
}

// 4. 将客户端注入到repository层
userRepo := repository.NewUserRepository(pgPool)
userCache := cache.NewUserCache(redisClient)

// 5. 启动服务
// ...
```

#### 6.2. 优雅关闭
每个`New...`函数都应返回一个可以被`defer`调用的`Close()`方法，或者在服务接收到关闭信号时，统一调用所有数据库客户端的关闭方法，以确保连接被优雅释放。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**: 本包的开销必须极小。可观测性钩子的性能影响应在可接受范围内（目标：<5%的额外开销）。
*   **NFR7.2 (可靠性)**:
    *   连接失败时，必须返回清晰、可追溯的错误。
    *   连接池配置必须健壮，能处理网络抖动和数据库重启等情况。
*   **NFR7.3 (可测试性)**: 提供mock数据库客户端或使用`pgxmock`, `gomock`等工具，使得业务服务的`repository`层可以被轻松地进行单元测试，而无需真实的数据库连接。
*   **NFR7.4 (安全性)**:
    *   数据库连接字符串(DSN)中包含的密码是敏感信息，绝不能被打印到日志中。
    *   强制要求使用TLS/SSL连接生产环境的数据库。配置中应有`sslmode`选项。

---

### 8. 技术约束与开发规范

*   **TC8.1 (依赖库)**:
    *   **PostgreSQL**: `jackc/pgx/v5`
    *   **MongoDB**: `go.mongodb.org/mongo-driver`
    *   **Redis**: `github.com/redis/go-redis/v9`
    *   **Tracing**: `go.opentelemetry.io/contrib/instrumentation/...` 下的官方或社区认证的数据库集成库。
*   **TC8.2 (开发规范)**:
    *   禁止在`pkg/database`中添加任何业务查询逻辑。
    *   所有对数据库配置的修改，都应通过`pkg/config`进行，而不是直接在`pkg/database`中硬编码。
    *   必须为每种支持的数据库提供清晰的文档，说明如何配置和使用其工厂函数。

---
这份SRS为`pkg/database`库的设计和实现提供了坚实的基础。通过将数据库连接的复杂性、配置和可观测性集成封装在这个包中，CINA.CLUB平台可以极大地简化和标准化所有后端微服务的数据访问层，从而提高开发效率和系统的整体可靠性。