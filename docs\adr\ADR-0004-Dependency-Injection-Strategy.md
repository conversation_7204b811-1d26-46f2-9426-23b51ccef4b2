# ADR-0004: Dependency Injection Strategy for Core Module

## Status
- **Proposed** ✍️
- **Accepted** ❌
- **Superseded** ❌

## Context
The Cina.Club project requires a robust, flexible, and type-safe mechanism for managing dependencies across its core module and various services. Existing dependency management was tightly coupled and lacked a standardized approach to service initialization and management.

## Problem Statement
- Existing code had hard-coded dependencies
- Lack of centralized service management
- Difficulty in testing individual components
- No clear strategy for service lifecycle management
- Limited ability to swap implementations dynamically

## Decision
Implement a custom Dependency Injection (DI) container with the following key characteristics:
- Type-safe service registration
- Thread-safe operations
- Support for global and local service containers
- Flexible service registration interfaces
- Explicit service initialization mechanisms

### Key Design Principles
1. **Type Safety**: Ensure compile-time type checking for registered services
2. **Loose Coupling**: Minimize direct dependencies between modules
3. **Extensibility**: Allow easy addition of new service types
4. **Concurrency Support**: Enable thread-safe service management
5. **Minimal Performance Overhead**: Use reflection efficiently

## Implementation Details

### Container Capabilities
- `Register(service interface{})`: Add services to the container
- `Resolve(serviceType reflect.Type)`: Retrieve services by type
- `MustResolve(serviceType reflect.Type)`: Retrieve services with panic on failure
- `Clear()`: Remove all registered services

### Service Registrar Interfaces
Specialized interfaces for different module types:
- `APIServiceRegistrar`
- `ModelServiceRegistrar`
- `AICoreServiceRegistrar`
- `DataSyncServiceRegistrar`
- `CryptoServiceRegistrar`

### Example Usage
```go
// Create a container
container := di.NewContainer()

// Register services
aiEngine := aic.NewEngine()
models := []aic.AIModel{
    &aic.BaseAIModel{name: "TextGenerationModel"},
}
registrar := aic.NewServiceRegistrar(aiEngine, models)
registrar.Register(container)
```

## Consequences

### Positive Outcomes
✅ Improved modularity
✅ Enhanced testability
✅ Dynamic service management
✅ Clear separation of concerns
✅ Simplified service initialization

### Potential Challenges
❗ Slight performance overhead due to reflection
❗ Learning curve for developers unfamiliar with DI
❗ Potential complexity in large-scale applications

## Alternatives Considered
1. **Third-Party DI Containers**
   - Pros: Mature, feature-rich
   - Cons: Additional dependencies, less control
2. **Manual Dependency Passing**
   - Pros: Simple implementation
   - Cons: Tight coupling, difficult to manage

## Recommendations for Future Iterations
- Implement service lifecycle management
- Add lazy loading of services
- Create more advanced dependency resolution strategies
- Develop comprehensive test coverage

## References
- Core Module DI Implementation: `core/di/container.go`
- AI Core Service Registrar: `core/aic/service_registrar.go`
- Project Checklist: `apps/admin/CHECKLIST.md`

## Approval
- **Proposed By**: Core Architecture Team
- **Date Proposed**: [Current Date]
- **Stakeholder Approval**: Pending

---

*This ADR captures the rationale behind our Dependency Injection strategy, providing a clear record of architectural decisions for the Cina.Club core module.* 