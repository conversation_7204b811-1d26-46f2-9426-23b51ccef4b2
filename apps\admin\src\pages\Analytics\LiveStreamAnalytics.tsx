/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:50:00
 * Modified: 2025-01-23 16:50:00
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Typography,
  Space,
  Button,
  Table,
  Tag,
  Progress,
  List,
  Avatar,
  Tabs,
  Alert,
  Tooltip,
  Badge,
  Timeline,
} from 'antd';
import {
  PlayCircleOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  TrendingUpOutlined,
  BarChartOutlined,
  LineChartOutlined,
  DownloadOutlined,
  HeartOutlined,
  ShareAltOutlined,
  CommentOutlined,
  VideoCameraOutlined,
  SignalOutlined,
  DashboardOutlined,
  UserOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { Line, Column, Pie, Area } from '@ant-design/charts';
import { useQuery } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

interface StreamMetrics {
  totalStreams: number;
  totalViewers: number;
  totalWatchTime: number;
  averageViewDuration: number;
  concurrentViewers: number;
  peakViewers: number;
  streamGrowth: number;
  viewerGrowth: number;
  engagementRate: number;
  chatMessages: number;
}

interface StreamData {
  id: string;
  title: string;
  streamer: {
    name: string;
    avatar?: string;
    followers: number;
  };
  status: 'live' | 'scheduled' | 'ended';
  viewers: number;
  peakViewers: number;
  duration: number;
  category: string;
  startTime: string;
  endTime?: string;
  quality: string;
  chatMessages: number;
  likes: number;
  shares: number;
}

interface ViewerAnalytics {
  timestamp: string;
  viewers: number;
  chatActivity: number;
  engagement: number;
}

interface GeographicData {
  country: string;
  viewers: number;
  percentage: number;
  avgWatchTime: number;
}

interface StreamPerformance {
  streamId: string;
  title: string;
  metrics: {
    bitrate: number;
    fps: number;
    resolution: string;
    latency: number;
    bufferRatio: number;
    quality: 'excellent' | 'good' | 'fair' | 'poor';
  };
  issues: Array<{
    type: 'buffering' | 'quality_drop' | 'connection_loss';
    timestamp: string;
    duration: number;
    severity: 'low' | 'medium' | 'high';
  }>;
}

const LiveStreamAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('24h');
  const [dateRange, setDateRange] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data queries
  const { data: streamMetrics } = useQuery({
    queryKey: ['stream-metrics', timeRange],
    queryFn: async (): Promise<StreamMetrics> => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        totalStreams: 1247,
        totalViewers: 89456,
        totalWatchTime: 456789,
        averageViewDuration: 23.5,
        concurrentViewers: 2345,
        peakViewers: 5678,
        streamGrowth: 18.7,
        viewerGrowth: 25.4,
        engagementRate: 67.8,
        chatMessages: 234567
      };
    }
  });

  const { data: liveStreams } = useQuery({
    queryKey: ['live-streams'],
    queryFn: async (): Promise<StreamData[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return [
        {
          id: '1',
          title: 'Tech Talk: Building Scalable Systems',
          streamer: {
            name: 'TechGuru',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=TechGuru',
            followers: 12345
          },
          status: 'live',
          viewers: 2345,
          peakViewers: 3456,
          duration: 120,
          category: 'Technology',
          startTime: '2025-01-23T15:00:00Z',
          quality: '1080p',
          chatMessages: 5678,
          likes: 234,
          shares: 45
        },
        {
          id: '2',
          title: 'Gaming Session: Latest RPG Adventure',
          streamer: {
            name: 'GameMaster',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=GameMaster',
            followers: 8901
          },
          status: 'live',
          viewers: 1876,
          peakViewers: 2234,
          duration: 180,
          category: 'Gaming',
          startTime: '2025-01-23T14:30:00Z',
          quality: '720p',
          chatMessages: 3456,
          likes: 189,
          shares: 23
        }
      ];
    }
  });

  const { data: viewerAnalytics } = useQuery({
    queryKey: ['viewer-analytics', timeRange],
    queryFn: async (): Promise<ViewerAnalytics[]> => {
      await new Promise(resolve => setTimeout(resolve, 700));
      const data: ViewerAnalytics[] = [];
      const now = new Date();
      
      for (let i = 23; i >= 0; i--) {
        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
        data.push({
          timestamp: timestamp.toISOString(),
          viewers: Math.floor(Math.random() * 3000) + 1000,
          chatActivity: Math.floor(Math.random() * 500) + 100,
          engagement: Math.floor(Math.random() * 30) + 50
        });
      }
      return data;
    }
  });

  const { data: geographicData } = useQuery({
    queryKey: ['geographic-data'],
    queryFn: async (): Promise<GeographicData[]> => {
      await new Promise(resolve => setTimeout(resolve, 500));
      return [
        { country: 'United States', viewers: 25678, percentage: 28.7, avgWatchTime: 32.5 },
        { country: 'United Kingdom', viewers: 15432, percentage: 17.3, avgWatchTime: 28.9 },
        { country: 'Germany', viewers: 12345, percentage: 13.8, avgWatchTime: 25.6 },
        { country: 'Canada', viewers: 9876, percentage: 11.1, avgWatchTime: 30.2 },
        { country: 'Australia', viewers: 7654, percentage: 8.6, avgWatchTime: 26.8 },
        { country: 'France', viewers: 6543, percentage: 7.3, avgWatchTime: 24.1 },
        { country: 'Others', viewers: 11923, percentage: 13.2, avgWatchTime: 22.7 }
      ];
    }
  });

  const { data: streamPerformance } = useQuery({
    queryKey: ['stream-performance'],
    queryFn: async (): Promise<StreamPerformance[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return [
        {
          streamId: '1',
          title: 'Tech Talk: Building Scalable Systems',
          metrics: {
            bitrate: 4500,
            fps: 60,
            resolution: '1080p',
            latency: 2.3,
            bufferRatio: 0.02,
            quality: 'excellent'
          },
          issues: [
            {
              type: 'quality_drop',
              timestamp: '2025-01-23T15:30:00Z',
              duration: 30,
              severity: 'low'
            }
          ]
        }
      ];
    }
  });

  // Chart configurations
  const viewerTrendConfig = {
    data: viewerAnalytics?.map(item => ({
      time: new Date(item.timestamp).getHours() + ':00',
      viewers: item.viewers
    })) || [],
    xField: 'time',
    yField: 'viewers',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 3,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Viewers',
        value: datum.viewers.toLocaleString()
      })
    }
  };

  const engagementConfig = {
    data: viewerAnalytics?.map(item => ({
      time: new Date(item.timestamp).getHours() + ':00',
      engagement: item.engagement,
      chat: item.chatActivity
    })) || [],
    xField: 'time',
    yField: 'engagement',
    seriesField: 'type',
    smooth: true,
    color: ['#52c41a', '#faad14']
  };

  const geographicConfig = {
    data: geographicData || [],
    angleField: 'viewers',
    colorField: 'country',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: {percentage}%'
    },
    interactions: [{ type: 'element-active' }]
  };

  const streamColumns: ColumnsType<StreamData> = [
    {
      title: 'Stream',
      key: 'stream',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.streamer.avatar} size="small" style={{ marginRight: '8px' }} />
          <div>
            <Text strong>{record.title}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              by {record.streamer.name}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge 
          status={status === 'live' ? 'processing' : status === 'scheduled' ? 'warning' : 'default'}
          text={status.toUpperCase()}
        />
      )
    },
    {
      title: 'Current Viewers',
      dataIndex: 'viewers',
      key: 'viewers',
      render: (viewers: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {viewers.toLocaleString()}
        </Text>
      ),
      sorter: (a, b) => a.viewers - b.viewers
    },
    {
      title: 'Peak Viewers',
      dataIndex: 'peakViewers',
      key: 'peakViewers',
      render: (peak: number) => peak.toLocaleString(),
      sorter: (a, b) => a.peakViewers - b.peakViewers
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => {
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        return `${hours}h ${minutes}m`;
      }
    },
    {
      title: 'Quality',
      dataIndex: 'quality',
      key: 'quality',
      render: (quality: string) => <Tag color="blue">{quality}</Tag>
    },
    {
      title: 'Engagement',
      key: 'engagement',
      render: (_, record) => (
        <Space>
          <Tooltip title="Chat Messages">
            <span><CommentOutlined /> {record.chatMessages}</span>
          </Tooltip>
          <Tooltip title="Likes">
            <span><HeartOutlined /> {record.likes}</span>
          </Tooltip>
          <Tooltip title="Shares">
            <span><ShareAltOutlined /> {record.shares}</span>
          </Tooltip>
        </Space>
      )
    }
  ];

  const performanceColumns: ColumnsType<StreamPerformance> = [
    {
      title: 'Stream',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: 'Quality',
      key: 'quality',
      render: (_, record) => (
        <Tag color={
          record.metrics.quality === 'excellent' ? 'green' :
          record.metrics.quality === 'good' ? 'blue' :
          record.metrics.quality === 'fair' ? 'orange' : 'red'
        }>
          {record.metrics.quality.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Bitrate',
      key: 'bitrate',
      render: (_, record) => `${record.metrics.bitrate} kbps`
    },
    {
      title: 'FPS',
      key: 'fps',
      render: (_, record) => `${record.metrics.fps} fps`
    },
    {
      title: 'Resolution',
      key: 'resolution',
      render: (_, record) => record.metrics.resolution
    },
    {
      title: 'Latency',
      key: 'latency',
      render: (_, record) => `${record.metrics.latency}s`
    },
    {
      title: 'Buffer Ratio',
      key: 'bufferRatio',
      render: (_, record) => (
        <Progress 
          percent={record.metrics.bufferRatio * 100}
          size="small"
          strokeColor={record.metrics.bufferRatio < 0.05 ? '#52c41a' : '#f5222d'}
        />
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2}>
              <VideoCameraOutlined /> Live Stream Analytics
            </Title>
            <Paragraph type="secondary">
              Monitor live streams, viewer engagement, and streaming performance.
            </Paragraph>
          </div>
          <Space>
            <Select
              value={timeRange}
              onChange={setTimeRange}
              style={{ width: 120 }}
            >
              <Option value="1h">Last Hour</Option>
              <Option value="24h">Last 24h</Option>
              <Option value="7d">Last 7 days</Option>
              <Option value="30d">Last 30 days</Option>
            </Select>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
            />
            <Button icon={<DownloadOutlined />}>
              Export Report
            </Button>
          </Space>
        </div>
      </div>

      {/* Key Metrics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Live Viewers"
              value={streamMetrics?.concurrentViewers || 0}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                Peak: {streamMetrics?.peakViewers.toLocaleString() || 0}
              </Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Watch Time"
              value={streamMetrics?.totalWatchTime || 0}
              suffix="hrs"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                Avg: {streamMetrics?.averageViewDuration || 0}min per viewer
              </Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Streams"
              value={liveStreams?.filter(s => s.status === 'live').length || 0}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                <TrendingUpOutlined style={{ color: '#52c41a' }} /> 
                {streamMetrics?.streamGrowth || 0}% growth
              </Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Engagement Rate"
              value={streamMetrics?.engagementRate || 0}
              suffix="%"
              precision={1}
              prefix={<HeartOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                {streamMetrics?.chatMessages.toLocaleString() || 0} chat messages
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Real-time Alert */}
      <Alert
        message="Live Stream Status"
        description={
          <div>
            <Text strong>{liveStreams?.filter(s => s.status === 'live').length || 0}</Text> streams currently live with{' '}
            <Text strong>{streamMetrics?.concurrentViewers.toLocaleString() || 0}</Text> total viewers
          </div>
        }
        type="success"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* Analytics Tabs */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Live Streams" key="overview">
          <Card title="Currently Live Streams">
            <Table
              columns={streamColumns}
              dataSource={liveStreams?.filter(s => s.status === 'live') || []}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </TabPane>

        <TabPane tab="Viewer Trends" key="trends">
          <Row gutter={16}>
            <Col span={16}>
              <Card title="Viewer Count Over Time" extra={<LineChartOutlined />}>
                <Line {...viewerTrendConfig} height={300} />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Geographic Distribution" extra={<GlobalOutlined />}>
                <Pie {...geographicConfig} height={300} />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Performance" key="performance">
          <Card title="Stream Quality & Performance">
            <Table
              columns={performanceColumns}
              dataSource={streamPerformance || []}
              rowKey="streamId"
              pagination={false}
            />
            
            <div style={{ marginTop: '24px' }}>
              <Title level={5}>Stream Issues Timeline</Title>
              <Timeline>
                {streamPerformance?.[0]?.issues.map((issue, index) => (
                  <Timeline.Item 
                    key={index}
                    color={issue.severity === 'high' ? 'red' : issue.severity === 'medium' ? 'orange' : 'blue'}
                  >
                    <div>
                      <Text strong>{issue.type.replace('_', ' ').toUpperCase()}</Text>
                      <br />
                      <Text type="secondary">
                        {new Date(issue.timestamp).toLocaleTimeString()} - Duration: {issue.duration}s
                      </Text>
                      <br />
                      <Tag color={issue.severity === 'high' ? 'red' : issue.severity === 'medium' ? 'orange' : 'blue'}>
                        {issue.severity.toUpperCase()}
                      </Tag>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          </Card>
        </TabPane>

        <TabPane tab="Geographic Insights" key="geographic">
          <Row gutter={16}>
            <Col span={16}>
              <Card title="Viewers by Country">
                <Table
                  dataSource={geographicData || []}
                  columns={[
                    {
                      title: 'Country',
                      dataIndex: 'country',
                      key: 'country'
                    },
                    {
                      title: 'Viewers',
                      dataIndex: 'viewers',
                      key: 'viewers',
                      render: (viewers: number) => viewers.toLocaleString(),
                      sorter: (a, b) => a.viewers - b.viewers
                    },
                    {
                      title: 'Percentage',
                      dataIndex: 'percentage',
                      key: 'percentage',
                      render: (percentage: number) => `${percentage}%`
                    },
                    {
                      title: 'Avg Watch Time',
                      dataIndex: 'avgWatchTime',
                      key: 'avgWatchTime',
                      render: (time: number) => `${time}min`
                    }
                  ]}
                  pagination={false}
                  rowKey="country"
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Top Performing Regions">
                <List
                  dataSource={geographicData?.slice(0, 5) || []}
                  renderItem={(item, index) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <div style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '6px',
                            backgroundColor: '#1890ff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <Text style={{ color: 'white', fontWeight: 'bold' }}>
                              #{index + 1}
                            </Text>
                          </div>
                        }
                        title={item.country}
                        description={
                          <div>
                            <Text>{item.viewers.toLocaleString()} viewers</Text>
                            <br />
                            <Text type="secondary">{item.avgWatchTime}min avg watch time</Text>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default LiveStreamAnalytics; 