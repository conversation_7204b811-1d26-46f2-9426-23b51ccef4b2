// This file is auto-generated by `openapi-typescript-codegen`.
// Do not edit this file manually.

import { ApiClient } from './ApiClient';
import { CancelablePromise } from './core/CancelablePromise';
import { OpenAPI } from './core/OpenAPI';
import type { User, UserListResponse, CreateUserRequest } from './models';
import type { request as __request } from './core/request';

export class DefaultService {

    /**
     * Get Users
     * @param page 
     * @param pageSize 
     * @returns UserListResponse A list of users.
     * @throws ApiError
     */
    public static getUsers(
        page?: number,
        pageSize?: number,
    ): CancelablePromise<UserListResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users',
            query: {
                'page': page,
                'pageSize': pageSize,
            },
        });
    }

    /**
     * Create User
     * @param requestBody 
     * @returns User User created successfully.
     * @throws ApiError
     */
    public static createUser(
        requestBody: CreateUserRequest,
    ): CancelablePromise<User> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/users',
            body: requestBody,
            mediaType: 'application/json',
        });
    }

    /**
     * Get User By ID
     * @param id 
     * @returns User A single user.
     * @throws ApiError
     */
    public static getUserById(
        id: string,
    ): CancelablePromise<User> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/{id}',
            path: {
                'id': id,
            },
        });
    }

    /**
     * Delete User
     * @param id 
     * @returns any User deleted successfully.
     * @throws ApiError
     */
    public static deleteUser(
        id: string,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/{id}',
            path: {
                'id': id,
            },
        });
    }

} 