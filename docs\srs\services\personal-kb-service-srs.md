﻿好的，遵照您的指示，我们来生成一份为 `personal-kb-service` (PKB，个人知识库服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **端到端加密 (E2EE) vs. 应用层加密 (ALE)**: 明确区分两种安全模型，并详细阐述本服务在云端ALE模型下的具体职责，这是生产级安全设计的核心。
2.  **内容处理工作流**: 细化从URL剪辑和文档上传后的异步内容提取与索引流程。
3.  **高级检索功能**: 增加对混合搜索（Hybrid Search）、分面搜索和结果重排（Re-ranking）的支持。
4.  **版本控制与共享**: 引入对知识条目的版本历史和安全共享的支持。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、安全和可靠性指标。

这份文档将描绘一个功能强大、极度注重隐私、且具备先进检索能力的个人知识管理系统。

---

### CINA.CLUB - personal-kb-service 需求规格说明书

**版本: 2.0 (生产级定义，强化安全模型与高级检索)**  
**发布日期: 2025-06-19**  
**最后修订日期: 2025-06-19**  
**文档负责人:** [AI平台/个性化团队负责人名称]  
**审批人:** [CTO/首席隐私官 (CPO)]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了使CINA.CLUB的AI助手能提供真正深度个性化的服务，平台需要一个**绝对安全、由用户完全控制、且可被智能检索**的私有知识存储库。`personal-kb-service` (PKB) 的目的就是提供这样一个个人知识管理的核心服务。用户可以主动记录笔记、剪藏网页、存储文档，AI助手也可以在用户授权下，将重要的信息片段存入此库，并在未来用于提供更精准的上下文和建议。本服务的核心是在云端以**服务器端零知识**的原则，安全地存储和检索用户的私密数据。

#### 1.2. 服务范围
本服务 **负责**:
*   **个人知识条目 (PKB Item) 的安全存储**: 接收来自AI助手或用户创建的知识条目，并进行**应用层加密 (Application-Level Encryption, ALE)** 后持久化。
*   **PKB条目的CRUD操作**: 提供API供授权的调用方创建、读取（解密后）、更新（重新加密）、删除知识条目。
*   **内容导入与处理**: 支持从URL异步剪辑网页内容，或接收上传的文档（通过`file-storage-service`）并异步提取文本。
*   **智能检索**:
    *   为PKB内容生成文本嵌入向量（通过`embedding-service`）。
    *   提供**混合搜索**能力，结合语义搜索（向量相似性）和关键词搜索（全文检索）。
    *   支持基于元数据的分面搜索和过滤。
*   **版本控制**: （可选）为知识条目保留历史版本，支持查看和恢复。
*   **安全共享**: （未来）支持用户将特定的PKB条目安全地共享给其他用户。
*   **与加密密钥管理的集成**: 与`key-management-proxy-service`紧密协作，安全地获取和使用用户数据加密密钥 (DEK)。

本服务 **不负责**:
*   管理用户主加密密钥 (Master Password) 或DEK明文 (由客户端和`key-management-proxy-service`负责)。
*   共享/商业知识库 (CKB) 的管理 (由`shared-kb-service`负责)。
*   AI自动推断的、非结构化的个人记忆 (PM) (由`memory-service`负责)。
*   提供面向最终用户的完整UI。

#### 1.3. 目标用户/调用方
*   **`ai-assistant-service` (主要)**: 创建新知识条目，查询PKB以获取上下文。
*   **CINA.CLUB客户端应用**: (通过API Gateway) 用户通过专门的UI界面直接管理自己的PKB。
*   **`key-management-proxy-service`**: (被本服务调用) 获取用户DEK以进行加解密。
*   **`embedding-service`**: (被本服务调用) 为PKB内容生成嵌入向量。
*   **`search-indexer-service`**: (可选) 如果关键词搜索由中心化ES/OS实现，本服务发布事件给它。

#### 1.4. 定义与缩略语
*   **PKB**: Personal Knowledge Base (个人知识库)。
*   **ALE (Application-Level Encryption)**: 应用层加密。本服务的核心安全模型，指数据在本服务内部，使用从KMSProxy获取的用户特定密钥进行加解密。服务器逻辑可以临时访问明文（用于嵌入和索引），但数据在数据库中是完全加密的。
*   **DEK**: Data Encryption Key (用户数据加密密钥)。
*   **KMSProxy**: `key-management-proxy-service`。
*   **VectorDB**: 向量数据库。
*   **Hybrid Search**: 混合搜索，结合了关键词搜索和向量语义搜索的结果。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`personal-kb-service` 是用户**个人数据主权**和平台深度个性化的核心体现。它是一个安全的、用户专属的数据保险箱，其内容构成了AI助手理解特定用户独特背景、知识和偏好的基础。与短暂的对话历史和AI自动推断的“记忆”不同，PKB更强调用户主动的、结构化的知识构建和组织。

#### 2.2. 主要功能概述
*   基于用户DEK的应用层加密存储，实现服务器端零知识。
*   支持多种知识条目类型（笔记、网页剪辑、文档）。
*   强大的混合搜索能力。
*   异步的内容导入和处理工作流。
*   用户完全的CRUD和管理权限。

### 3. 核心流程图

#### 3.1. 创建并索引一条加密的PKB笔记
```mermaid
sequenceDiagram
    participant Client
    participant PKBService
    participant KMSProxy
    participant EmbeddingService
    participant VectorDB
    participant DB as "PostgreSQL (Metadata)"

    Client->>PKBService: 1. POST /items (content: "明文笔记", tags: ["dev"])
    
    PKBService->>KMSProxy: 2. Request User DEK
    KMSProxy-->>PKBService: (User DEK)
    
    PKBService->>PKBService: 3. **[In-Memory]** Encrypt content with DEK
    
    PKBService->>EmbeddingService: 4. **[In-Memory]** Send plaintext content for embedding
    EmbeddingService-->>PKBService: (Embedding Vector)
    
    PKBService->>DB: 5. **Start Transaction**
    PKBService->>DB: 6. Store Encrypted Content & Metadata
    PKBService->>VectorDB: 7. Upsert Embedding Vector with metadata
    PKBService->>DB: 8. **Commit Transaction**
    
    PKBService-->>Client: 201 Created (PKB Item ID)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. PKB条目管理
*   **FR4.1.1 (CRUD)**: 系统必须提供完整的、安全的CRUD API，供授权方管理PKB条目。
*   **FR4.1.2 (类型支持)**: 至少支持`NOTE` (Markdown), `WEB_CLIP`, `DOCUMENT`类型。
*   **FR4.1.3 (版本控制 - 可选)**: 对PKB条目的每次修改都应创建一个`PKBItemVersion`记录，允许用户查看和恢复历史版本。

#### 4.2. 数据安全与加密 (核心)
*   **FR4.2.1 (ALE模型)**: 所有PKB条目的核心内容字段（如`title`, `content_text`）在持久化到数据库之前，必须使用该用户的DEK进行应用层加密 (AES-256-GCM)。
*   **FR4.2.2 (密钥交互)**: 本服务在执行任何加解密操作前，必须通过安全的S2S调用，向`key-management-proxy-service`请求并获取（解包后的）用户DEK。**明文DEK的生命周期必须被严格限制在单次请求的内存处理中，绝不能写入日志或持久化存储。**
*   **FR4.2.3 (元数据安全)**: 部分元数据（如`tags`, `folder_path`）如果需要用于明文搜索和过滤，可以不加密或使用确定性加密。此选择必须有明确的隐私影响评估。

#### 4.3. 智能检索
*   **FR4.3.1 (混合搜索)**: 系统必须提供统一的搜索API，支持混合搜索。
    *   **语义部分**: 将用户查询发送到`embedding-service`获取查询向量，在VectorDB中执行k-NN搜索。
    *   **关键词部分**: 对`title`和非加密元数据在PostgreSQL中使用全文检索(FTS)或确定性加密索引进行搜索。
    *   **结果融合**: 使用如Reciprocal Rank Fusion (RRF)等算法，将两路搜索结果进行融合和重排，返回最终列表。
*   **FR4.3.2 (分面搜索)**: 搜索结果必须能根据`tags`, `item_type`, `created_at`等元数据进行聚合，并返回分面信息供客户端筛选。

#### 4.4. 内容处理与导入 (异步)
*   **FR4.4.1 (Web剪辑)**: 提供API接收URL。系统将一个`WebClipJob`推入消息队列。后台Worker消费任务，使用网页抓取库（如`go-rod`）提取正文，并创建`WEB_CLIP`条目。
*   **FR4.4.2 (文档处理)**: 提供API接收`file-storage-service`的`fileKey`。系统将`DocumentProcessJob`推入消息队列。后台Worker消费任务，下载文件，使用`Tika`或`unidoc`等库提取文本，并创建`DOCUMENT`条目。

#### 4.5. 组织与共享
*   **FR4.5.1 (标签与文件夹)**: 用户可以为条目添加多个标签，或设置虚拟的`folderPath`来进行组织。
*   **FR4.5.2 (安全共享 - 未来)**:
    *   提供API允许用户A将一个PKB条目共享给用户B。
    *   **实现**: A的客户端用B的公钥加密该条目的DEK，然后将这个“加密后的DEK”和条目ID发送给B。B的客户端收到后，用自己的私钥解密得到条目DEK，然后就可以向本服务请求该条目的加密内容并自行解密。**本服务在此过程中只负责传递加密信封，依然无法访问明文**。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部服务RESTful API接口
*   **版本**: `/api/v1/pkb`
*   **认证**: 用户JWT必需，并需传递用于获取DEK的会话凭证。
*   **核心端点**:
    *   `POST /items`: 创建PKB条目。Request: `CreatePKBItemRequestDto` (含明文内容)。
    *   `GET /items?filter_by=...&sort_by=...&page=...`: 列出条目。
    *   `GET /items/{itemId}`: 获取单个条目详情 (返回解密内容)。
    *   `PUT /items/{itemId}`: 更新条目。
    *   `DELETE /items/{itemId}`: 删除。
    *   `POST /imports/from-url`: 异步从URL创建。Request: `{ "url": "...", "tags": [] }`.
    *   `POST /imports/from-document`: 异步从文档创建。Request: `{ "file_key": "...", "tags": [] }`.
    *   `POST /search`: **统一混合搜索接口**。Request: `PKBSearchRequestDto { query, semantic_weight, keyword_weight, filters }`.
    *   `GET /tags`, `GET /folders`: 获取组织结构。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`pkb_items`**:
    *   `item_id (PK, UUID)`
    *   `user_id (FK, INDEX)`
    *   `item_type (VARCHAR)`
    *   `encrypted_title (BYTEA)`
    *   `encrypted_content (BYTEA)`
    *   `encryption_metadata (JSONB)`: 存储IV, AuthTag等。
    *   `tags (TEXT[])`: (非加密，用于过滤) 使用GIN索引。
    *   `folder_path (VARCHAR, INDEX)`: (非加密，用于过滤)
    *   `status (VARCHAR)`: `ACTIVE`, `DELETED`, `PROCESSING`。
    *   `created_at`, `updated_at`
*   **`pkb_item_versions` (可选)**: 存储`pkb_items`的历史快照。

#### 6.2. 数据存储
*   **元数据与加密内容**: PostgreSQL。
*   **向量索引**: Pinecone, Weaviate, Milvus, or pgvector (如果PostgreSQL版本支持)。
*   **全文检索索引**: PostgreSQL自带的FTS，或同步到`search-indexer-service`的ES/OS。
*   **原始文件**: `file-storage-service` (对象存储)。
*   **任务队列**: Redis (如Asynq) 或 RabbitMQ/Kafka。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: CRUD操作 P99 < 300ms。混合搜索 P99 < 800ms。
*   **异步处理**: Web剪辑和文档处理应在分钟级完成。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **数据完整性**: 加解密、索引、元数据存储必须保持强一致性，任何一步失败都应回滚。
*   **容错**: 对`KMSProxy`, `EmbeddingService`, `VectorDB`的依赖必须有健壮的重试和熔断机制。

#### 7.3. 可扩展性需求
*   服务可水平扩展。数据库、向量库、任务队列都必须支持水平扩展。

#### 7.4. 安全性需求 (最高优先级)
*   **服务器零知识**: 严格执行ALE模型，服务器端逻辑在任何情况下都不能访问明文DEK，除非是在处理单个请求的短暂内存周期中。
*   **密钥管理安全**: 与`key-management-proxy-service`的所有交互必须通过最严格的S2S认证。
*   **权限控制**: 严格的`userId`隔离，防止任何数据交叉访问。
*   **审计**: 所有对用户PKB的访问（特别是导致解密的操作）都必须有详细、不可篡改的审计日志。

### 8. 技术约束与选型建议
*   **语言**: Go。其强类型和性能优势适合构建要求严谨、安全可靠的服务。
*   **加密库**: 使用Go标准库`crypto/aes`, `crypto/cipher`，并遵循密码学最佳实践。
*   **异步任务**: 使用如`Asynq` (基于Redis) 或更重的`Cadence`/`Temporal`来实现异步内容处理工作流。
*   **混合搜索**: 这是核心技术挑战之一，需要对RRF等融合算法有深入理解。

---
这份版本2.0的SRS文档为`personal-kb-service`构建了一个安全、智能、功能丰富的个人知识管理核心。它将用户数据隐私置于最高优先级，同时通过先进的AI技术和健壮的系统设计，赋能用户，使其个人信息沉淀能真正为其所用。