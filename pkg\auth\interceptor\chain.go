/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

package interceptor

import (
	"context"

	"google.golang.org/grpc"
)

// ChainBuilder provides a fluent interface for building interceptor chains
type ChainBuilder struct {
	unaryInterceptors  []grpc.UnaryServerInterceptor
	streamInterceptors []grpc.StreamServerInterceptor
}

// NewChainBuilder creates a new interceptor chain builder
func NewChainBuilder() *ChainBuilder {
	return &ChainBuilder{
		unaryInterceptors:  make([]grpc.UnaryServerInterceptor, 0),
		streamInterceptors: make([]grpc.StreamServerInterceptor, 0),
	}
}

// AddS2S adds S2S JWT validation to the chain
func (b *ChainBuilder) AddS2S(interceptor *S2SJWTInterceptor) *ChainBuilder {
	b.unaryInterceptors = append(b.unaryInterceptors, interceptor.UnaryInterceptor())
	b.streamInterceptors = append(b.streamInterceptors, interceptor.StreamInterceptor())
	return b
}

// AddUserJWT adds user JWT validation to the chain
func (b *ChainBuilder) AddUserJWT(interceptor *UserJWTInterceptor) *ChainBuilder {
	b.unaryInterceptors = append(b.unaryInterceptors, interceptor.UnaryInterceptor())
	b.streamInterceptors = append(b.streamInterceptors, interceptor.StreamInterceptor())
	return b
}

// AddRBAC adds RBAC authorization to the chain
func (b *ChainBuilder) AddRBAC(interceptor *RBACInterceptor) *ChainBuilder {
	b.unaryInterceptors = append(b.unaryInterceptors, interceptor.UnaryInterceptor())
	b.streamInterceptors = append(b.streamInterceptors, interceptor.StreamInterceptor())
	return b
}

// AddCustomUnary adds a custom unary interceptor to the chain
func (b *ChainBuilder) AddCustomUnary(interceptor grpc.UnaryServerInterceptor) *ChainBuilder {
	b.unaryInterceptors = append(b.unaryInterceptors, interceptor)
	return b
}

// AddCustomStream adds a custom stream interceptor to the chain
func (b *ChainBuilder) AddCustomStream(interceptor grpc.StreamServerInterceptor) *ChainBuilder {
	b.streamInterceptors = append(b.streamInterceptors, interceptor)
	return b
}

// BuildUnary builds and returns the unary interceptor chain
func (b *ChainBuilder) BuildUnary() grpc.UnaryServerInterceptor {
	if len(b.unaryInterceptors) == 0 {
		return nil
	}
	if len(b.unaryInterceptors) == 1 {
		return b.unaryInterceptors[0]
	}
	return chainUnaryInterceptors(b.unaryInterceptors...)
}

// BuildStream builds and returns the stream interceptor chain
func (b *ChainBuilder) BuildStream() grpc.StreamServerInterceptor {
	if len(b.streamInterceptors) == 0 {
		return nil
	}
	if len(b.streamInterceptors) == 1 {
		return b.streamInterceptors[0]
	}
	return chainStreamInterceptors(b.streamInterceptors...)
}

// BuildServerOptions builds and returns gRPC server options with the interceptor chains
func (b *ChainBuilder) BuildServerOptions() []grpc.ServerOption {
	var opts []grpc.ServerOption

	if unaryChain := b.BuildUnary(); unaryChain != nil {
		opts = append(opts, grpc.UnaryInterceptor(unaryChain))
	}

	if streamChain := b.BuildStream(); streamChain != nil {
		opts = append(opts, grpc.StreamInterceptor(streamChain))
	}

	return opts
}

// chainUnaryInterceptors creates a chain of unary interceptors
func chainUnaryInterceptors(interceptors ...grpc.UnaryServerInterceptor) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Build the chain by wrapping each interceptor
		chained := handler
		for i := len(interceptors) - 1; i >= 0; i-- {
			interceptor := interceptors[i]
			next := chained
			chained = func(currentCtx context.Context, currentReq interface{}) (interface{}, error) {
				return interceptor(currentCtx, currentReq, info,
					func(ctx context.Context, req interface{}) (interface{}, error) {
						return next(ctx, req)
					})
			}
		}
		return chained(ctx, req)
	}
}

// chainStreamInterceptors creates a chain of stream interceptors
func chainStreamInterceptors(interceptors ...grpc.StreamServerInterceptor) grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		// Build the chain by wrapping each interceptor
		chained := handler
		for i := len(interceptors) - 1; i >= 0; i-- {
			interceptor := interceptors[i]
			next := chained
			chained = func(currentSrv interface{}, currentSS grpc.ServerStream) error {
				return interceptor(currentSrv, currentSS, info,
					func(srv interface{}, ss grpc.ServerStream) error {
						return next(srv, ss)
					})
			}
		}
		return chained(srv, ss)
	}
}

// CommonChains provides pre-configured interceptor chains for common use cases
type CommonChains struct{}

// APIGateway returns a chain suitable for API gateway services (User JWT + RBAC)
func (CommonChains) APIGateway(userJWT *UserJWTInterceptor, rbac *RBACInterceptor) *ChainBuilder {
	return NewChainBuilder().
		AddUserJWT(userJWT).
		AddRBAC(rbac)
}

// InternalService returns a chain suitable for internal services (S2S JWT only)
func (CommonChains) InternalService(s2s *S2SJWTInterceptor) *ChainBuilder {
	return NewChainBuilder().
		AddS2S(s2s)
}

// MixedService returns a chain suitable for services that handle both user and service requests
func (CommonChains) MixedService(s2s *S2SJWTInterceptor, userJWT *UserJWTInterceptor, rbac *RBACInterceptor) *ChainBuilder {
	return NewChainBuilder().
		AddS2S(s2s).
		AddUserJWT(userJWT).
		AddRBAC(rbac)
}

// AdminService returns a chain suitable for admin services (User JWT + RBAC with strict permissions)
func (CommonChains) AdminService(userJWT *UserJWTInterceptor, rbac *RBACInterceptor) *ChainBuilder {
	return NewChainBuilder().
		AddUserJWT(userJWT).
		AddRBAC(rbac)
}
