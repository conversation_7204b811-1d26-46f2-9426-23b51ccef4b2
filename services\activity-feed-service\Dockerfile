# Activity Feed Service Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM golang:1.22-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o activity-feed-service \
    ./cmd/server/main.go

# Production stage
FROM scratch

# Copy timezone data
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# Copy CA certificates
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary
COPY --from=builder /app/activity-feed-service /activity-feed-service

# Copy configuration files
COPY --from=builder /app/configs/config.yaml /configs/config.yaml

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ["/activity-feed-service", "--health-check"] || exit 1

# Set user (non-root)
USER 65534:65534

# Run the application
ENTRYPOINT ["/activity-feed-service"]
CMD ["--config=/configs/config.yaml"] 