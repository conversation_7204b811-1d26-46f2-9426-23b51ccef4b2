/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.navigation

import androidx.compose.material3.windowsizeclass.WindowSizeClass
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.cinaclub.feature.auth.navigation.authNavigation
import com.cinaclub.feature.chat.navigation.chatNavigation
import com.cinaclub.feature.home.navigation.homeNavigation
import com.cinaclub.feature.home.navigation.HomeDestination
import com.cinaclub.feature.pkb.navigation.pkbNavigation
import com.cinaclub.feature.profile.navigation.profileNavigation
import com.cinaclub.feature.aiassistant.navigation.aiAssistantNavigation
import com.cinaclub.feature.live.navigation.liveNavigation
import com.cinaclub.feature.knowledge.navigation.knowledgeNavigation
import com.cinaclub.feature.social.navigation.socialNavigation
import com.cinaclub.feature.payment.navigation.paymentNavigation

/**
 * Main navigation controller for the CINA.CLUB application.
 * Manages navigation between different feature modules.
 */
@Composable
fun CinaClubNavigation(
    windowSizeClass: WindowSizeClass
) {
    val navController = rememberNavController()
    val isExpandedScreen = windowSizeClass.widthSizeClass == WindowWidthSizeClass.Expanded

    NavHost(
        navController = navController,
        startDestination = HomeDestination.route
    ) {
        // Home navigation
        homeNavigation(
            navController = navController,
            isExpandedScreen = isExpandedScreen
        )
        
        // Authentication navigation
        authNavigation(
            navController = navController
        )
        
        // Chat navigation
        chatNavigation(
            navController = navController,
            isExpandedScreen = isExpandedScreen
        )
        
        // Personal Knowledge Base navigation
        pkbNavigation(
            navController = navController,
            isExpandedScreen = isExpandedScreen
        )
        
        // Profile navigation
        profileNavigation(
            navController = navController
        )
        
        // AI Assistant navigation
        aiAssistantNavigation(
            navController = navController
        )
        
        // Live streaming navigation
        liveNavigation(
            navController = navController,
            isExpandedScreen = isExpandedScreen
        )
        
        // Knowledge base navigation
        knowledgeNavigation(
            navController = navController,
            isExpandedScreen = isExpandedScreen
        )
        
        // Social navigation
        socialNavigation(
            navController = navController,
            isExpandedScreen = isExpandedScreen
        )
        
        // Payment navigation
        paymentNavigation(
            navController = navController
        )
    }
} 