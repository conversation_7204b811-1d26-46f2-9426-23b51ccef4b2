/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 15:35:00
 * Modified: 2025-01-23 15:35:00
 */

import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, Space, Alert, Steps, Result } from 'antd';
import { MailOutlined, LockOutlined, CheckCircleOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { z } from 'zod';

const { Title, Text, Paragraph } = Typography;

// Validation schemas
const emailSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

const resetPasswordSchema = z.object({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type EmailFormData = z.infer<typeof emailSchema>;
type ResetFormData = z.infer<typeof resetPasswordSchema>;

enum ResetStep {
  REQUEST = 0,
  EMAIL_SENT = 1,
  RESET_PASSWORD = 2,
  SUCCESS = 3,
}

const PasswordReset: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  
  const [currentStep, setCurrentStep] = useState<ResetStep>(
    token ? ResetStep.RESET_PASSWORD : ResetStep.REQUEST
  );
  const [email, setEmail] = useState('');
  const [form] = Form.useForm();

  // Mock API calls - replace with actual API services
  const requestResetMutation = useMutation({
    mutationFn: async (data: EmailFormData) => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Password reset requested for:', data.email);
      return { success: true, message: 'Reset email sent' };
    },
    onSuccess: () => {
      setCurrentStep(ResetStep.EMAIL_SENT);
    },
  });

  const resetPasswordMutation = useMutation({
    mutationFn: async (data: ResetFormData & { token: string }) => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Password reset completed for token:', data.token);
      return { success: true, message: 'Password reset successfully' };
    },
    onSuccess: () => {
      setCurrentStep(ResetStep.SUCCESS);
    },
  });

  const handleEmailSubmit = async (values: EmailFormData) => {
    try {
      await emailSchema.parseAsync(values);
      setEmail(values.email);
      requestResetMutation.mutate(values);
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  const handlePasswordReset = async (values: ResetFormData) => {
    if (!token) return;
    
    try {
      await resetPasswordSchema.parseAsync(values);
      resetPasswordMutation.mutate({ ...values, token });
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  const handleResendEmail = () => {
    if (email) {
      requestResetMutation.mutate({ email });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case ResetStep.REQUEST:
        return (
          <Card style={{ width: '100%', maxWidth: 400 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <MailOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
                <Title level={3}>Reset Password</Title>
                <Paragraph type="secondary">
                  Enter your email address and we'll send you a link to reset your password.
                </Paragraph>
              </div>

              <Form
                form={form}
                onFinish={handleEmailSubmit}
                layout="vertical"
                requiredMark={false}
              >
                <Form.Item
                  name="email"
                  label="Email Address"
                  rules={[
                    { required: true, message: 'Please enter your email address' },
                    { type: 'email', message: 'Please enter a valid email address' },
                  ]}
                >
                  <Input
                    prefix={<MailOutlined />}
                    placeholder="Enter your email address"
                    size="large"
                  />
                </Form.Item>

                {requestResetMutation.error && (
                  <Alert
                    message="Error"
                    description={requestResetMutation.error.message || 'Failed to send reset email'}
                    type="error"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                )}

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    size="large"
                    loading={requestResetMutation.isPending}
                    block
                  >
                    Send Reset Link
                  </Button>
                </Form.Item>
              </Form>

              <div style={{ textAlign: 'center' }}>
                <Button type="link" onClick={handleBackToLogin} icon={<ArrowLeftOutlined />}>
                  Back to Login
                </Button>
              </div>
            </Space>
          </Card>
        );

      case ResetStep.EMAIL_SENT:
        return (
          <Card style={{ width: '100%', maxWidth: 400 }}>
            <Result
              status="success"
              title="Email Sent!"
              subTitle={
                <Space direction="vertical" size="small">
                  <Text>We've sent a password reset link to:</Text>
                  <Text strong>{email}</Text>
                  <Text type="secondary">
                    Please check your email and click the link to reset your password.
                    The link will expire in 24 hours.
                  </Text>
                </Space>
              }
              extra={[
                <Button key="resend" onClick={handleResendEmail} loading={requestResetMutation.isPending}>
                  Resend Email
                </Button>,
                <Button key="back" type="link" onClick={handleBackToLogin}>
                  Back to Login
                </Button>,
              ]}
            />
          </Card>
        );

      case ResetStep.RESET_PASSWORD:
        return (
          <Card style={{ width: '100%', maxWidth: 400 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <LockOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
                <Title level={3}>Set New Password</Title>
                <Paragraph type="secondary">
                  Please enter your new password below.
                </Paragraph>
              </div>

              <Form
                form={form}
                onFinish={handlePasswordReset}
                layout="vertical"
                requiredMark={false}
              >
                <Form.Item
                  name="password"
                  label="New Password"
                  rules={[
                    { required: true, message: 'Please enter your new password' },
                    { min: 8, message: 'Password must be at least 8 characters' },
                    {
                      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                      message: 'Password must contain uppercase, lowercase, number and special character',
                    },
                  ]}
                  hasFeedback
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="Enter new password"
                    size="large"
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  label="Confirm Password"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: 'Please confirm your password' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('Passwords do not match'));
                      },
                    }),
                  ]}
                  hasFeedback
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="Confirm new password"
                    size="large"
                  />
                </Form.Item>

                {resetPasswordMutation.error && (
                  <Alert
                    message="Error"
                    description={resetPasswordMutation.error.message || 'Failed to reset password'}
                    type="error"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                )}

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    size="large"
                    loading={resetPasswordMutation.isPending}
                    block
                  >
                    Reset Password
                  </Button>
                </Form.Item>
              </Form>

              <div style={{ textAlign: 'center' }}>
                <Button type="link" onClick={handleBackToLogin} icon={<ArrowLeftOutlined />}>
                  Back to Login
                </Button>
              </div>
            </Space>
          </Card>
        );

      case ResetStep.SUCCESS:
        return (
          <Card style={{ width: '100%', maxWidth: 400 }}>
            <Result
              status="success"
              icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              title="Password Reset Successful!"
              subTitle="Your password has been successfully reset. You can now log in with your new password."
              extra={
                <Button type="primary" onClick={handleBackToLogin}>
                  Go to Login
                </Button>
              }
            />
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
    }}>
      <div style={{ width: '100%', maxWidth: 500 }}>
        {/* Progress Steps */}
        {currentStep !== ResetStep.SUCCESS && (
          <Card style={{ marginBottom: 24, background: 'rgba(255, 255, 255, 0.95)' }}>
            <Steps
              current={currentStep}
              size="small"
              items={[
                {
                  title: 'Request',
                  description: 'Enter email',
                },
                {
                  title: 'Verify',
                  description: 'Check email',
                },
                {
                  title: 'Reset',
                  description: 'New password',
                },
              ]}
            />
          </Card>
        )}

        {/* Main Content */}
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {renderStepContent()}
        </div>

        {/* Help Text */}
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
            Need help? Contact{' '}
            <a 
              href="mailto:<EMAIL>" 
              style={{ color: 'rgba(255, 255, 255, 0.9)' }}
            >
              <EMAIL>
            </a>
          </Text>
        </div>
      </div>
    </div>
  );
};

export default PasswordReset; 