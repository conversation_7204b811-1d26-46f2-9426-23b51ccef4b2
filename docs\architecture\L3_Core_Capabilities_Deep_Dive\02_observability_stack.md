
### 文件2: `docs/architecture/L3_Core_Capabilities_Deep_Dive/02_observability_stack.md`

```markdown
# 深度解析: 可观测性技术栈 (Observability Stack)

**文档状态**: 已采纳 (Accepted)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**相关包**: `pkg/logger`, `pkg/tracing`, `pkg/middleware`

## 1. 概述

可观测性是维护和理解CINA.CLUB复杂分布式系统的关键。我们的可观测性策略基于三大支柱：**日志(Logging)、指标(Metrics)和追踪(Tracing)**。本文档定义了平台统一的可观测性技术栈和数据规范，旨在提供一个全面的、关联的系统视图。

---

## 2. 日志 (Logging)

*   **目的**: 记录离散的、带有丰富上下文的事件，用于**事后调试和审计**。
*   **技术栈**:
    *   **库**: Go标准库 **`log/slog`** (通过`pkg/logger`封装)。
    *   **格式**: **必须**是 **JSON** 格式。
    *   **收集**: **Fluentd** / **Fluent Bit**作为DaemonSet部署在Kubernetes节点上，负责收集所有容器的`stdout`/`stderr`。
    *   **存储与查询**: **Loki** 或 **Elasticsearch**。
*   **标准规范**:
    *   **结构化**: 所有日志都必须是结构化的键值对。严禁记录无格式的字符串。
    *   **标准字段**: 每条日志必须包含`ts`, `level`, `msg`, `service.name`, `trace.trace_id`, `trace.span_id`等标准字段。
    *   **上下文**: 日志必须包含与当前请求相关的上下文信息，如`user_id`。
*   **实现**: 通过`pkg/logger`和`pkg/middleware/logging`拦截器实现自动化。

---

## 3. 指标 (Metrics)

*   **目的**: 聚合的、可量化的数据，用于**实时监控、性能告警和容量规划**。
*   **技术栈**:
    *   **库**: **`prometheus/client_golang`**。
    *   **模型**: **Prometheus** 拉取模型(pull-based)。每个服务实例暴露一个`/metrics`端点。
    *   **存储与查询**: **Prometheus** / **Thanos** / **VictoriaMetrics**。
    *   **可视化与告警**: **Grafana** 和 **Alertmanager**。
*   **核心指标 (RED)**:
    *   **Rate**: 每秒请求数 (`grpc_server_handled_total` - counter)。
    *   **Errors**: 每秒失败请求数 (通过`grpc_code`标签过滤)。
    *   **Duration**: 请求延迟的分布情况 (`grpc_server_handling_seconds` - histogram)。
*   **实现**: 通过`pkg/middleware/metrics`拦截器自动收集所有gRPC服务的RED指标。业务代码也可以注册自定义的Prometheus指标。

---

## 4. 追踪 (Tracing)

*   **目的**: 捕获单个请求在分布式系统中的完整调用链，用于**性能瓶颈分析和根本原因定位**。
*   **技术栈**:
    *   **标准**: **OpenTelemetry** (OTel)。
    *   **库**: `go.opentelemetry.io/otel` (通过`pkg/tracing`封装)。
    *   **上下文传播**: **W3C Trace Context** (`traceparent`, `tracestate` headers)。
    *   **收集**: **OpenTelemetry Collector** 部署为Agent或Gateway。
    *   **存储与可视化**: **Jaeger** 或 **Grafana Tempo**。
*   **实现**:
    *   **初始化**: 每个服务在启动时调用`pkg/tracing.Init()`。
    *   **自动化检测**:
        *   gRPC: `pkg/middleware/tracing`拦截器自动创建和传播Span。
        *   数据库: `pkg/database`中的工厂函数自动为SQL查询创建子Span。
        *   消息队列: `pkg/messaging`的客户端自动在消息头中注入/提取Trace Context。
    *   **手动检测**: 开发者可以在复杂的业务逻辑中手动创建子Span，以获得更精细的追踪粒度。

---

## 5. 三者联动 (The Golden Triangle)

日志、指标和追踪并非孤立存在，而是相互关联的。
*   **Trace ID是关键**: **日志和追踪通过Trace ID进行关联**。在Grafana中，你可以从一个慢查询的Trace Span中，一键跳转到与该Span相关的所有服务的详细日志。
*   **指标触发追踪**: 当Prometheus的延迟告警触发时，SRE可以根据时间戳和服务名，去Jaeger中查找该时间段内最慢的请求Trace，从而定位问题。

```mermaid
graph TD
    A(Metrics - Prometheus/Grafana) -- "High latency alert!" --> B{Find problematic time/service};
    B -- "Query by time & service" --> C(Tracing - Jaeger/Tempo);
    C -- "Found slow trace, get TraceID" --> D{Analyze trace spans};
    D -- "Query by TraceID" --> E(Logging - Loki/Elasticsearch);
    E -- "Get detailed logs for the specific request" --> F(Root Cause Found!);
```
```
---