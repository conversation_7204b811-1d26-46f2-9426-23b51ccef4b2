# CINA.CLUB 基础设施即代码 (Infrastructure as Code)

<!--
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-18
Modified: 2025-06-18
-->

## 概述

本目录包含 CINA.CLUB 平台的完整基础设施配置，采用**基础设施即代码 (IaC)** 的方法，通过声明式配置实现环境的自动化创建、版本化管理和一键式复制。

## 架构设计

```
infra/
├── docker/                 # 容器化基础
│   ├── base/               # 基础镜像模板
│   ├── dev/                # 本地开发环境
│   └── services/           # 服务特定 Docker 配置
├── terraform/              # 云资源编排
│   ├── modules/            # 可复用模块
│   └── environments/       # 环境特定配置
└── kubernetes/             # 应用部署编排
    ├── base/               # 基础配置模板
    ├── overlays/           # 环境差异化配置
    └── system/             # 集群级系统组件
```

## 技术栈

- **容器化**: Docker
- **云资源编排**: Terraform >= 1.5
- **应用部署**: Kubernetes >= 1.25, Kustomize >= 5.0
- **容器编排**: Docker Compose (本地开发)
- **监控**: Prometheus + Grafana
- **网络**: Nginx Ingress Controller
- **追踪**: Jaeger

## 快速开始

### 本地开发环境

1. **启动完整的本地开发环境**:
   ```bash
   cd infra/docker/dev
   docker-compose up -d
   ```

2. **访问服务**:
   - Kafka UI: http://localhost:8080
   - Grafana: http://localhost:3000 (admin/admin123)
   - Prometheus: http://localhost:9090
   - Adminer: http://localhost:8081
   - MinIO Console: http://localhost:9001 (minioadmin/minioadmin123)

3. **停止环境**:
   ```bash
   docker-compose down -v  # -v 选项会删除数据卷
   ```

### Kubernetes 部署

1. **部署到开发环境**:
   ```bash
   kubectl apply -k infra/kubernetes/overlays/dev
   ```

2. **部署到生产环境**:
   ```bash
   kubectl apply -k infra/kubernetes/overlays/prod
   ```

3. **验证部署**:
   ```bash
   kubectl get pods -n cina-dev    # 开发环境
   kubectl get pods -n cina-prod   # 生产环境
   ```

## 目录详解

### Docker 配置 (`docker/`)

#### 基础镜像 (`docker/base/`)

- **`Dockerfile.go`**: Go 服务的多阶段构建镜像
  - 使用 `golang:1.22-alpine` 构建
  - 最终镜像基于 `distroless/static:nonroot`
  - 优化后镜像大小 < 20MB

- **`Dockerfile.python`**: Python 服务的多阶段构建镜像
  - 支持开发和生产模式
  - 内置热重载支持 (开发模式)
  - 非特权用户运行

#### 开发环境 (`docker/dev/`)

完整的本地开发环境，包含：

**基础设施服务**:
- PostgreSQL 15 (多数据库支持)
- Redis 7 (缓存和会话)
- Kafka + Zookeeper (消息队列)
- Elasticsearch 8 (搜索引擎)
- MinIO (S3 兼容存储)

**监控与可观测性**:
- Prometheus (监控)
- Grafana (仪表板)
- Jaeger (分布式追踪)

**开发工具**:
- Kafka UI (Kafka 管理界面)
- Adminer (数据库管理)
- MailHog (邮件测试)

### Terraform 配置 (`terraform/`)

#### 模块 (`terraform/modules/`)

**VPC 模块** (`modules/vpc/`):
- 完整的网络基础设施
- 公有、私有、数据库子网
- NAT Gateway、Internet Gateway
- VPC Flow Logs
- 安全组和网络 ACL

**EKS 模块** (`modules/eks/`):
- EKS 集群配置
- 托管节点组
- IRSA (IAM Roles for Service Accounts)
- 集群插件 (VPC CNI, CoreDNS, kube-proxy)
- AWS Load Balancer Controller

#### 环境配置 (`terraform/environments/`)

- **`dev/`**: 开发环境（单 AZ，较小实例）
- **`staging/`**: 预发布环境（多 AZ，生产镜像）
- **`prod/`**: 生产环境（高可用，性能优化）

### Kubernetes 配置 (`kubernetes/`)

#### 基础配置 (`base/`)

包含每个微服务的基础 Kubernetes 资源：

- **Deployment**: 应用部署配置
- **Service**: 网络服务发现
- **ConfigMap**: 应用配置
- **Secret**: 敏感信息管理
- **RBAC**: 权限控制
- **HPA**: 水平扩展
- **NetworkPolicy**: 网络安全策略

#### 环境覆盖 (`overlays/`)

**开发环境** (`overlays/dev/`):
- 单副本部署
- 较小资源限制
- 调试模式启用
- NodePort 服务类型

**生产环境** (`overlays/prod/`):
- 多副本高可用
- 严格资源限制
- 安全强化配置
- LoadBalancer 服务类型
- Pod 反亲和性
- Pod 中断预算

#### 系统组件 (`system/`)

**Ingress Nginx**:
- 网络入口控制器
- SSL/TLS 终端
- 速率限制
- 安全头配置

**Prometheus Stack**:
- Prometheus 监控
- AlertManager 告警
- Grafana 可视化
- 自定义告警规则

## 环境管理

### 环境策略

| 环境 | 用途 | 部署触发 | 特点 |
|------|------|----------|------|
| `dev` | 开发集成测试 | 合并到 `develop` 分支 | 单副本，调试模式 |
| `staging` | QA 和 UAT | 手动触发 | 生产镜像配置 |
| `prod` | 生产服务 | 审批后手动 | 高可用，性能优化 |

### 配置管理

1. **配置分层**:
   ```
   base/ (通用配置)
   ├── overlays/dev/ (开发环境差异)
   ├── overlays/staging/ (预发布环境差异)
   └── overlays/prod/ (生产环境差异)
   ```

2. **Secret 管理**:
   - 开发环境：明文配置 (仅用于开发)
   - 生产环境：外部 Secret 管理 (AWS Secrets Manager, Vault)

3. **镜像策略**:
   - 开发环境：`dev-latest`
   - 预发布：`staging-{git-sha}`
   - 生产环境：`v{semver}` (语义化版本)

## 安全最佳实践

### 网络安全

1. **网络策略**:
   - 默认拒绝所有入站流量
   - 明确允许必要的服务间通信
   - 限制出站流量到已知端口

2. **服务网格** (可选):
   - mTLS 服务间通信
   - 流量策略控制
   - 安全监控

### 容器安全

1. **镜像安全**:
   - 非特权用户运行
   - 只读根文件系统
   - 最小权限原则

2. **运行时安全**:
   - Security Context 配置
   - Pod Security Standards
   - 资源限制

### 访问控制

1. **RBAC**:
   - 最小权限原则
   - 服务账户隔离
   - 审计日志记录

2. **IRSA**:
   - AWS 服务权限精确控制
   - 临时凭证自动轮换

## 监控与可观测性

### 指标监控

1. **基础设施指标**:
   - 节点资源使用率
   - 网络流量统计
   - 存储 IOPS 和延迟

2. **应用指标**:
   - HTTP 请求率、延迟、错误率
   - 自定义业务指标
   - 数据库连接池状态

### 日志收集

1. **结构化日志**:
   - JSON 格式统一
   - 标准字段定义
   - 敏感信息脱敏

2. **日志聚合**:
   - Fluentd/Fluent Bit 收集
   - Elasticsearch 存储
   - Kibana 查询分析

### 分布式追踪

1. **OpenTelemetry**:
   - 统一追踪标准
   - 跨服务链路追踪
   - 性能瓶颈识别

## 故障排查

### 常见问题

1. **Pod 启动失败**:
   ```bash
   kubectl describe pod <pod-name> -n <namespace>
   kubectl logs <pod-name> -n <namespace>
   ```

2. **服务无法访问**:
   ```bash
   kubectl get svc -n <namespace>
   kubectl get endpoints -n <namespace>
   ```

3. **配置问题**:
   ```bash
   kubectl get configmap <configmap-name> -o yaml
   kubectl get secret <secret-name> -o yaml
   ```

### 调试工具

1. **临时调试容器**:
   ```bash
   kubectl run debug --image=busybox --rm -it -- sh
   ```

2. **端口转发**:
   ```bash
   kubectl port-forward svc/<service-name> 8080:80
   ```

## 成本优化

### 资源优化

1. **右级配置**:
   - 使用 VPA 推荐值
   - 定期审查资源使用
   - Spot 实例用于非关键工作负载

2. **自动伸缩**:
   - HPA 根据负载自动伸缩
   - Cluster Autoscaler 节点管理
   - 定时伸缩策略

### 存储优化

1. **存储类别**:
   - gp3 替代 gp2 (成本效益更好)
   - 冷存储归档策略
   - 备份数据生命周期管理

## CI/CD 集成

### 部署流水线

1. **构建阶段**:
   - Docker 镜像构建
   - 安全扫描 (Trivy, Snyk)
   - 镜像推送到 ECR

2. **部署阶段**:
   - Kustomize 配置渲染
   - Dry-run 验证
   - 滚动更新部署

3. **验证阶段**:
   - 健康检查
   - 烟雾测试
   - 回滚机制

## 备份与恢复

### 数据备份

1. **数据库备份**:
   - RDS 自动备份
   - 定期快照
   - 跨区域复制

2. **存储备份**:
   - EBS 快照
   - S3 版本控制
   - 生命周期策略

### 灾难恢复

1. **RTO/RPO 目标**:
   - RTO: 2 小时
   - RPO: 15 分钟

2. **恢复策略**:
   - 蓝绿部署
   - 数据库故障转移
   - 跨区域恢复

## 最佳实践

### 开发实践

1. **配置管理**:
   - 所有变更通过 PR
   - 强制代码审查
   - 自动化测试

2. **版本控制**:
   - 语义化版本
   - 标签管理
   - 变更日志

### 运维实践

1. **变更管理**:
   - 计划性维护窗口
   - 变更审批流程
   - 回滚计划

2. **监控告警**:
   - SLI/SLO 定义
   - 告警阈值调优
   - 值班机制

## 支持与联系

- **文档**: 详细文档请参考 `docs/architecture/`
- **问题反馈**: 请创建 GitHub Issue
- **紧急支持**: 联系 SRE 团队

---

## 许可证

Copyright (c) 2025 Cina.Club. All rights reserved. 