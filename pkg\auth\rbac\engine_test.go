/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 12:00:00
Modified: 2025-01-21 12:00:00
*/

package rbac

import (
	"testing"
)

func TestNewEngine(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)
	if engine == nil {
		t.<PERSON>("NewEngine returned nil")
	}

	// Test initial policy loading
	stats := engine.Stats()
	if stats["total_roles"] != 3 {
		t.<PERSON>("Expected 3 roles, got %v", stats["total_roles"])
	}
	if stats["total_permissions"] != 6 {
		t.<PERSON><PERSON><PERSON>("Expected 6 total permissions, got %v", stats["total_permissions"])
	}
}

func TestEngine_Can(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)

	tests := []struct {
		name       string
		roles      []string
		permission string
		expected   bool
	}{
		{
			name:       "admin has create permission",
			roles:      []string{"admin"},
			permission: "user.create",
			expected:   true,
		},
		{
			name:       "user has read permission",
			roles:      []string{"user"},
			permission: "user.read",
			expected:   true,
		},
		{
			name:       "guest has read permission",
			roles:      []string{"guest"},
			permission: "user.read",
			expected:   true,
		},
		{
			name:       "user does not have create permission",
			roles:      []string{"user"},
			permission: "user.create",
			expected:   false,
		},
		{
			name:       "guest does not have create permission",
			roles:      []string{"guest"},
			permission: "user.create",
			expected:   false,
		},
		{
			name:       "multiple roles - user and admin",
			roles:      []string{"user", "admin"},
			permission: "user.create",
			expected:   true,
		},
		{
			name:       "multiple roles - user and guest",
			roles:      []string{"user", "guest"},
			permission: "profile.update",
			expected:   true,
		},
		{
			name:       "unknown role",
			roles:      []string{"unknown"},
			permission: "user.read",
			expected:   false,
		},
		{
			name:       "empty roles",
			roles:      []string{},
			permission: "user.read",
			expected:   false,
		},
		{
			name:       "nil roles",
			roles:      nil,
			permission: "user.read",
			expected:   false,
		},
		{
			name:       "unknown permission",
			roles:      []string{"admin"},
			permission: "unknown.permission",
			expected:   false,
		},
		{
			name:       "empty permission",
			roles:      []string{"admin"},
			permission: "",
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := engine.Can(tt.roles, tt.permission)
			if result != tt.expected {
				t.Errorf("Can(%v, %s) = %v, expected %v",
					tt.roles, tt.permission, result, tt.expected)
			}
		})
	}
}

func TestEngine_GetRolePermissions(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)

	tests := []struct {
		name     string
		role     string
		expected []string
	}{
		{
			name:     "admin permissions",
			role:     "admin",
			expected: []string{"user.create", "user.delete", "user.read"},
		},
		{
			name:     "user permissions",
			role:     "user",
			expected: []string{"user.read", "profile.update"},
		},
		{
			name:     "guest permissions",
			role:     "guest",
			expected: []string{"user.read"},
		},
		{
			name:     "unknown role",
			role:     "unknown",
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := engine.GetRolePermissions(tt.role)
			if len(result) != len(tt.expected) {
				t.Errorf("GetRolePermissions(%s) returned %d permissions, expected %d",
					tt.role, len(result), len(tt.expected))
				return
			}

			// Convert to map for easier comparison
			resultMap := make(map[string]bool)
			for _, perm := range result {
				resultMap[perm] = true
			}

			for _, expectedPerm := range tt.expected {
				if !resultMap[expectedPerm] {
					t.Errorf("GetRolePermissions(%s) missing permission %s", tt.role, expectedPerm)
				}
			}
		})
	}
}

func TestEngine_HasRole(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)

	tests := []struct {
		name     string
		role     string
		expected bool
	}{
		{"admin role exists", "admin", true},
		{"user role exists", "user", true},
		{"guest role exists", "guest", true},
		{"unknown role does not exist", "unknown", false},
		{"empty role does not exist", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := engine.HasRole(tt.role)
			if result != tt.expected {
				t.Errorf("HasRole(%s) = %v, expected %v", tt.role, result, tt.expected)
			}
		})
	}
}

func TestEngine_GetAllRoles(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)
	roles := engine.GetAllRoles()

	expectedRoles := []string{"admin", "user", "guest"}
	if len(roles) != len(expectedRoles) {
		t.Errorf("GetAllRoles() returned %d roles, expected %d", len(roles), len(expectedRoles))
	}

	// Convert to map for easier comparison
	roleMap := make(map[string]bool)
	for _, role := range roles {
		roleMap[role] = true
	}

	for _, expectedRole := range expectedRoles {
		if !roleMap[expectedRole] {
			t.Errorf("GetAllRoles() missing role %s", expectedRole)
		}
	}
}

func TestEngine_UpdatePolicy(t *testing.T) {
	initialPolicy := map[string][]string{
		"admin": {"user.create", "user.delete"},
		"user":  {"user.read"},
	}

	engine := NewEngine(initialPolicy)

	// Test initial state
	if !engine.Can([]string{"admin"}, "user.create") {
		t.Error("Admin should have user.create permission initially")
	}
	if engine.Can([]string{"user"}, "profile.update") {
		t.Error("User should not have profile.update permission initially")
	}

	// Update policy
	newPolicy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine.UpdatePolicy(newPolicy)

	// Test updated state
	if !engine.Can([]string{"user"}, "profile.update") {
		t.Error("User should have profile.update permission after update")
	}
	if !engine.Can([]string{"guest"}, "user.read") {
		t.Error("Guest should have user.read permission after update")
	}
	if !engine.HasRole("guest") {
		t.Error("Guest role should exist after update")
	}

	// Test stats after update
	stats := engine.Stats()
	if stats["total_roles"] != 3 {
		t.Errorf("Expected 3 roles after update, got %v", stats["total_roles"])
	}
}

func TestEngine_AddRemoveRolePermissions(t *testing.T) {
	policy := map[string][]string{
		"user": {"user.read"},
	}

	engine := NewEngine(policy)

	// Add permissions
	engine.AddRolePermissions("user", []string{"profile.update", "user.create"})

	if !engine.Can([]string{"user"}, "profile.update") {
		t.Error("User should have profile.update permission after adding")
	}
	if !engine.Can([]string{"user"}, "user.create") {
		t.Error("User should have user.create permission after adding")
	}

	// Remove permissions
	engine.RemoveRolePermissions("user", []string{"user.create"})

	if engine.Can([]string{"user"}, "user.create") {
		t.Error("User should not have user.create permission after removing")
	}
	if !engine.Can([]string{"user"}, "profile.update") {
		t.Error("User should still have profile.update permission")
	}

	// Add role and permissions
	engine.AddRolePermissions("admin", []string{"user.delete", "service.manage"})
	if !engine.Can([]string{"admin"}, "user.delete") {
		t.Error("Admin should have user.delete permission after adding new role")
	}
}

func TestEngine_RemoveRole(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete"},
		"user":  {"user.read"},
	}

	engine := NewEngine(policy)

	// Verify role exists
	if !engine.HasRole("admin") {
		t.Error("Admin role should exist initially")
	}

	// Remove role
	engine.RemoveRole("admin")

	// Verify role is removed
	if engine.HasRole("admin") {
		t.Error("Admin role should not exist after removal")
	}
	if engine.Can([]string{"admin"}, "user.create") {
		t.Error("Admin should not have any permissions after role removal")
	}

	// User role should still exist
	if !engine.HasRole("user") {
		t.Error("User role should still exist")
	}
}

func TestEngine_Stats(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)
	stats := engine.Stats()

	if stats["total_roles"] != 3 {
		t.Errorf("Expected 3 roles, got %v", stats["total_roles"])
	}

	// Total assignments: 3 + 2 + 1 = 6
	if stats["total_permissions"] != 6 {
		t.Errorf("Expected 6 total permission assignments, got %v", stats["total_permissions"])
	}
}

func TestEngine_Validate(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete"},
		"user":  {"user.read"},
		"empty": {},
	}

	engine := NewEngine(policy)
	issues := engine.Validate()

	if len(issues) != 1 {
		t.Errorf("Expected 1 validation issue, got %d", len(issues))
	}

	expectedIssue := "role 'empty' has no permissions"
	if len(issues) > 0 && issues[0] != expectedIssue {
		t.Errorf("Expected issue '%s', got '%s'", expectedIssue, issues[0])
	}
}

func TestEngine_Concurrency(t *testing.T) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
	}

	engine := NewEngine(policy)

	// Test concurrent reads
	done := make(chan bool, 100)
	for i := 0; i < 100; i++ {
		go func() {
			defer func() { done <- true }()

			// Perform multiple read operations
			engine.Can([]string{"admin"}, "user.create")
			engine.GetRolePermissions("admin")
			engine.HasRole("admin")
			engine.GetAllRoles()
			engine.Stats()
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 100; i++ {
		<-done
	}

	// Test concurrent reads with writes
	for i := 0; i < 10; i++ {
		go func() {
			defer func() { done <- true }()

			newPolicy := map[string][]string{
				"admin": {"user.create", "user.delete", "user.read"},
				"user":  {"user.read", "profile.update"},
				"guest": {"user.read"},
			}
			engine.UpdatePolicy(newPolicy)
		}()
	}

	for i := 0; i < 10; i++ {
		go func() {
			defer func() { done <- true }()

			engine.Can([]string{"admin"}, "user.create")
			engine.Stats()
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 20; i++ {
		<-done
	}
}

func TestEngine_EmptyPolicy(t *testing.T) {
	engine := NewEngine(map[string][]string{})

	if engine.Can([]string{"admin"}, "user.create") {
		t.Error("Empty engine should not grant any permissions")
	}

	if engine.HasRole("admin") {
		t.Error("Empty engine should not have any roles")
	}

	roles := engine.GetAllRoles()
	if len(roles) != 0 {
		t.Errorf("Empty engine should return empty roles list, got %d", len(roles))
	}

	stats := engine.Stats()
	if stats["total_roles"] != 0 || stats["total_permissions"] != 0 {
		t.Error("Empty engine should have zero stats")
	}
}

// Benchmark tests
func BenchmarkEngine_Can(b *testing.B) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)
	roles := []string{"admin"}
	permission := "user.create"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.Can(roles, permission)
	}
}

func BenchmarkEngine_CanMultipleRoles(b *testing.B) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)
	roles := []string{"user", "guest", "admin"}
	permission := "user.create"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.Can(roles, permission)
	}
}

func BenchmarkEngine_GetRolePermissions(b *testing.B) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)
	role := "admin"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.GetRolePermissions(role)
	}
}

func BenchmarkEngine_UpdatePolicy(b *testing.B) {
	policy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
	}

	engine := NewEngine(policy)

	newPolicy := map[string][]string{
		"admin": {"user.create", "user.delete", "user.read", "service.manage"},
		"user":  {"user.read", "profile.update"},
		"guest": {"user.read"},
		"mod":   {"user.read", "user.update"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.UpdatePolicy(newPolicy)
	}
}
