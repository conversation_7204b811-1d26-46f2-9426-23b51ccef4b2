# CINA.CLUB 简化Go.mod修复脚本

Write-Host "Starting go.mod repair process..." -ForegroundColor Green

$fixedCount = 0
$serviceDirectories = Get-ChildItem -Path "services" -Directory

Write-Host "Found $($serviceDirectories.Count) service directories" -ForegroundColor Cyan

foreach ($serviceDir in $serviceDirectories) {
    $serviceName = $serviceDir.Name
    $servicePath = $serviceDir.FullName
    $goModPath = Join-Path $servicePath "go.mod"
    
    Write-Host "Processing $serviceName..." -ForegroundColor Yellow
    
    $needsFix = $false
    
    # Check if go.mod exists and is valid
    if (Test-Path $goModPath) {
        $content = Get-Content $goModPath -Raw -ErrorAction SilentlyContinue
        
        # Check if empty or starts with comments
        if ([string]::IsNullOrWhiteSpace($content) -or $content -match "^/\*" -or $content -match "^[\s]*$") {
            $needsFix = $true
            Write-Host "  Issue: Empty or invalid go.mod" -ForegroundColor Red
        }
        elseif ($content -notmatch "module cina\.club/services/$serviceName") {
            $needsFix = $true
            Write-Host "  Issue: Incorrect module declaration" -ForegroundColor Red
        }
    } else {
        $needsFix = $true
        Write-Host "  Issue: Missing go.mod file" -ForegroundColor Red
    }
    
    # Fix the go.mod file
    if ($needsFix) {
        Write-Host "  Fixing go.mod..." -ForegroundColor Cyan
        
        # Create standard go.mod content
        $goModContent = "module cina.club/services/$serviceName`n`n"
        $goModContent += "go 1.22`n`n"
        $goModContent += "replace (`n"
        $goModContent += "    cina.club/core => ../../core`n"
        $goModContent += "    cina.club/pkg => ../../pkg`n"
        $goModContent += "    cina.club/services/$serviceName => ./`n"
        $goModContent += ")`n`n"
        $goModContent += "require (`n"
        $goModContent += "    github.com/gin-gonic/gin v1.10.0`n"
        $goModContent += "    github.com/google/uuid v1.6.0`n"
        $goModContent += "    google.golang.org/grpc v1.65.0`n"
        $goModContent += ")`n"
        
        # Write the new go.mod
        $goModContent | Out-File -FilePath $goModPath -Encoding UTF8
        
        # Run go mod tidy
        $currentLocation = Get-Location
        Set-Location $servicePath
        
        Write-Host "  Running go mod tidy..." -ForegroundColor Gray
        go mod tidy 2>$null
        
        Set-Location $currentLocation
        
        $fixedCount++
        Write-Host "  FIXED: $serviceName" -ForegroundColor Green
    } else {
        Write-Host "  OK: $serviceName (no fix needed)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "=== GO.MOD REPAIR SUMMARY ===" -ForegroundColor Blue
Write-Host "Total Services: $($serviceDirectories.Count)" -ForegroundColor Cyan
Write-Host "Fixed: $fixedCount" -ForegroundColor Green
Write-Host "Success Rate: $([math]::Round((($serviceDirectories.Count - $fixedCount) / $serviceDirectories.Count) * 100, 1))% were already OK" -ForegroundColor Cyan

Write-Host ""
Write-Host "Next step: Run batch compilation test to verify fixes" -ForegroundColor Yellow
Write-Host "Command: .\scripts\simple-batch-test.ps1" -ForegroundColor Cyan 