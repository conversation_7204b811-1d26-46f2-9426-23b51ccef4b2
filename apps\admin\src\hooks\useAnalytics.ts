/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 19:25:00
 * Modified: 2025-01-23 20:00:00
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'
import { useCallback } from 'react'
import posthog from 'posthog-js'
import { useAuthStore } from '@/store/auth'
import type { User } from '@/types/user'

import { 
  AnalyticsOverview,
  UserAnalyticsData,
  CustomReport,
  ReportTemplate,
  ReportStatus,
  DashboardMetrics,
  ChartData 
} from '@/types/analytics'
import { apiClient } from '@/lib/api-client'

// Query keys for analytics
export const ANALYTICS_QUERY_KEYS = {
  // General analytics
  overview: 'analytics-overview',
  metrics: 'analytics-metrics',
  
  // User analytics
  userAnalytics: 'user-analytics',
  userGrowth: 'user-growth',
  userEngagement: 'user-engagement',
  userSegments: 'user-segments',
  userRetention: 'user-retention',
  
  // Content analytics
  contentAnalytics: 'content-analytics',
  contentPerformance: 'content-performance',
  trendingContent: 'trending-content',
  
  // Custom reports
  reports: 'custom-reports',
  reportById: (id: string) => ['custom-reports', id],
  reportTemplates: 'report-templates',
  reportExecution: 'report-execution',
  
  // Dashboard
  dashboardMetrics: 'dashboard-metrics',
  realtimeMetrics: 'realtime-metrics',
} as const

const isAnalyticsEnabled = import.meta.env.PROD

// General analytics hooks
export const useAnalytics = () => {
  const queryClient = useQueryClient()
  const { user } = useAuthStore.getState()

  // Get analytics overview
  const useAnalyticsOverview = (dateRange: string = '30days') => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.overview, dateRange],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/overview', {
          params: { dateRange }
        })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchInterval: 10 * 60 * 1000, // Refresh every 10 minutes
    })
  }

  // Get dashboard metrics
  const useDashboardMetrics = (dateRange: string = '7days') => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.dashboardMetrics, dateRange],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/dashboard', {
          params: { dateRange }
        })
        return response.data
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
      refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
    })
  }

  // Get real-time metrics
  const useRealtimeMetrics = () => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.realtimeMetrics],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/realtime')
        return response.data
      },
      staleTime: 30 * 1000, // 30 seconds
      refetchInterval: 60 * 1000, // Refresh every minute
    })
  }

  /**
   * Identifies the user to PostHog. This should be called on login.
   * It links all subsequent events to this user profile.
   * @param loggedInUser - The user object of the user who just logged in.
   */
  const identifyUser = useCallback((loggedInUser: User) => {
    if (isAnalyticsEnabled) {
      posthog.identify(loggedInUser.id, {
        email: loggedInUser.email,
        username: loggedInUser.username,
        name: `${loggedInUser.firstName} ${loggedInUser.lastName}`,
      })
    }
  }, [])

  /**
   * Tracks a custom event.
   * @param eventName - The name of the event to track (e.g., 'User List Exported').
   * @param properties - An optional object of key-value pairs to send with the event.
   */
  const trackEvent = useCallback(
    (eventName: string, properties?: Record<string, any>) => {
      if (isAnalyticsEnabled) {
        posthog.capture(eventName, {
          ...properties,
          // Add some default properties to all events for context
          user_role: user?.roles[0],
          user_id_internal: user?.id,
        })
      } else {
        // Log to console in development instead of sending to PostHog
        console.log(`[Analytics Disabled] Event: "${eventName}"`, properties)
      }
    },
    [user]
  )

  /**
   * Resets the PostHog user identification. This should be called on logout.
   */
  const resetUser = useCallback(() => {
    if (isAnalyticsEnabled) {
      posthog.reset()
    }
  }, [])

  return {
    useAnalyticsOverview,
    useDashboardMetrics,
    useRealtimeMetrics,
    identifyUser,
    trackEvent,
    resetUser,
  }
}

// User analytics hooks
export const useUserAnalytics = () => {
  // Get user analytics data
  const useUserAnalyticsData = (dateRange: string = '30days', viewType: string = 'overview') => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.userAnalytics, dateRange, viewType],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/users', {
          params: { dateRange, viewType }
        })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  // Get user growth metrics
  const useUserGrowth = (dateRange: string = '30days') => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.userGrowth, dateRange],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/users/growth', {
          params: { dateRange }
        })
        return response.data
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    })
  }

  // Get user engagement metrics
  const useUserEngagement = (dateRange: string = '30days') => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.userEngagement, dateRange],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/users/engagement', {
          params: { dateRange }
        })
        return response.data
      },
      staleTime: 15 * 60 * 1000, // 15 minutes
    })
  }

  // Get user segments
  const useUserSegments = () => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.userSegments],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/users/segments')
        return response.data
      },
      staleTime: 30 * 60 * 1000, // 30 minutes
    })
  }

  // Get user retention data
  const useUserRetention = (dateRange: string = '180days') => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.userRetention, dateRange],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/users/retention', {
          params: { dateRange }
        })
        return response.data
      },
      staleTime: 60 * 60 * 1000, // 1 hour - retention data changes slowly
    })
  }

  return {
    useUserAnalyticsData,
    useUserGrowth,
    useUserEngagement,
    useUserSegments,
    useUserRetention,
  }
}

// Content analytics hooks
export const useContentAnalytics = () => {
  // Get content analytics data
  const useContentAnalyticsData = (dateRange: string = '30days', contentType?: string) => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.contentAnalytics, dateRange, contentType],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/content', {
          params: { dateRange, contentType }
        })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  // Get content performance metrics
  const useContentPerformance = (dateRange: string = '7days', limit: number = 10) => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.contentPerformance, dateRange, limit],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/content/performance', {
          params: { dateRange, limit }
        })
        return response.data
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    })
  }

  // Get trending content
  const useTrendingContent = (limit: number = 10, timeframe: string = '24h') => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.trendingContent, limit, timeframe],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/content/trending', {
          params: { limit, timeframe }
        })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchInterval: 10 * 60 * 1000, // Refresh every 10 minutes
    })
  }

  return {
    useContentAnalyticsData,
    useContentPerformance,
    useTrendingContent,
  }
}

// Custom reports hooks
export const useCustomReports = () => {
  const queryClient = useQueryClient()

  // Get all custom reports
  const useReports = (params?: any) => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.reports, params],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/reports', { params })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  // Get report by ID
  const useReport = (reportId: string) => {
    return useQuery({
      queryKey: ANALYTICS_QUERY_KEYS.reportById(reportId),
      queryFn: async () => {
        const response = await apiClient.get(`/admin/analytics/reports/${reportId}`)
        return response.data
      },
      enabled: !!reportId,
    })
  }

  // Get report templates
  const useReportTemplates = () => {
    return useQuery({
      queryKey: [ANALYTICS_QUERY_KEYS.reportTemplates],
      queryFn: async () => {
        const response = await apiClient.get('/admin/analytics/reports/templates')
        return response.data
      },
      staleTime: 60 * 60 * 1000, // 1 hour - templates don't change often
    })
  }

  // Create report
  const useCreateReport = () => {
    return useMutation({
      mutationFn: async (reportData: Partial<CustomReport>) => {
        const response = await apiClient.post('/admin/analytics/reports', reportData)
        return response.data
      },
      onSuccess: () => {
        message.success('报告创建成功')
        queryClient.invalidateQueries({ queryKey: [ANALYTICS_QUERY_KEYS.reports] })
      },
      onError: (error) => {
        console.error('Create report failed:', error)
        message.error('报告创建失败')
      },
    })
  }

  // Update report
  const useUpdateReport = () => {
    return useMutation({
      mutationFn: async ({ 
        reportId, 
        reportData 
      }: {
        reportId: string
        reportData: Partial<CustomReport>
      }) => {
        const response = await apiClient.put(`/admin/analytics/reports/${reportId}`, reportData)
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success('报告更新成功')
        queryClient.invalidateQueries({ queryKey: [ANALYTICS_QUERY_KEYS.reports] })
        queryClient.invalidateQueries({ 
          queryKey: ANALYTICS_QUERY_KEYS.reportById(variables.reportId) 
        })
      },
      onError: (error) => {
        console.error('Update report failed:', error)
        message.error('报告更新失败')
      },
    })
  }

  // Delete report
  const useDeleteReport = () => {
    return useMutation({
      mutationFn: async (reportId: string) => {
        const response = await apiClient.delete(`/admin/analytics/reports/${reportId}`)
        return response.data
      },
      onSuccess: () => {
        message.success('报告删除成功')
        queryClient.invalidateQueries({ queryKey: [ANALYTICS_QUERY_KEYS.reports] })
      },
      onError: (error) => {
        console.error('Delete report failed:', error)
        message.error('报告删除失败')
      },
    })
  }

  // Execute report
  const useExecuteReport = () => {
    return useMutation({
      mutationFn: async ({ 
        reportId, 
        parameters 
      }: {
        reportId: string
        parameters?: any
      }) => {
        const response = await apiClient.post(`/admin/analytics/reports/${reportId}/execute`, {
          parameters
        })
        return response.data
      },
      onSuccess: () => {
        message.success('报告执行成功，结果将发送到您的邮箱')
        queryClient.invalidateQueries({ queryKey: [ANALYTICS_QUERY_KEYS.reportExecution] })
      },
      onError: (error) => {
        console.error('Execute report failed:', error)
        message.error('报告执行失败')
      },
    })
  }

  // Toggle report status
  const useToggleReportStatus = () => {
    return useMutation({
      mutationFn: async ({ 
        reportId, 
        status 
      }: {
        reportId: string
        status: ReportStatus
      }) => {
        const response = await apiClient.put(`/admin/analytics/reports/${reportId}/status`, {
          status
        })
        return response.data
      },
      onSuccess: () => {
        message.success('报告状态更新成功')
        queryClient.invalidateQueries({ queryKey: [ANALYTICS_QUERY_KEYS.reports] })
      },
      onError: (error) => {
        console.error('Toggle report status failed:', error)
        message.error('状态更新失败')
      },
    })
  }

  // Export report
  const useExportReport = () => {
    return useMutation({
      mutationFn: async ({ 
        reportId, 
        format, 
        parameters 
      }: {
        reportId: string
        format: 'excel' | 'pdf' | 'csv'
        parameters?: any
      }) => {
        const response = await apiClient.post(`/admin/analytics/reports/${reportId}/export`, {
          format,
          parameters,
        }, {
          responseType: 'blob'
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        // Create download link
        const url = window.URL.createObjectURL(new Blob([data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `report-${variables.reportId}-${Date.now()}.${variables.format}`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
        message.success('报告导出成功')
      },
      onError: (error) => {
        console.error('Export report failed:', error)
        message.error('报告导出失败')
      },
    })
  }

  return {
    useReports,
    useReport,
    useReportTemplates,
    useCreateReport,
    useUpdateReport,
    useDeleteReport,
    useExecuteReport,
    useToggleReportStatus,
    useExportReport,
  }
}

// Data export hooks
export const useDataExport = () => {
  // Export analytics data
  const useExportAnalytics = () => {
    return useMutation({
      mutationFn: async ({ 
        type, 
        dateRange, 
        format 
      }: {
        type: 'users' | 'content' | 'engagement'
        dateRange: string
        format: 'excel' | 'csv'
      }) => {
        const response = await apiClient.post('/admin/analytics/export', {
          type,
          dateRange,
          format,
        }, {
          responseType: 'blob'
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        // Create download link
        const url = window.URL.createObjectURL(new Blob([data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `analytics-${variables.type}-${Date.now()}.${variables.format}`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
        message.success('数据导出成功')
      },
      onError: (error) => {
        console.error('Export analytics failed:', error)
        message.error('数据导出失败')
      },
    })
  }

  return {
    useExportAnalytics,
  }
}

// Combine all analytics hooks
export const useAllAnalyticsHooks = () => {
  const general = useAnalytics()
  const userAnalytics = useUserAnalytics()
  const contentAnalytics = useContentAnalytics()
  const customReports = useCustomReports()
  const dataExport = useDataExport()

  return {
    ...general,
    ...userAnalytics,
    ...contentAnalytics,
    ...customReports,
    ...dataExport,
  }
}

// Export individual hook categories
export { useAnalytics, useUserAnalytics, useContentAnalytics, useCustomReports, useDataExport } 