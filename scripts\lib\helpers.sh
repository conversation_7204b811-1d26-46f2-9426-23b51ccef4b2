#!/usr/bin/env bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB 开发与运维脚本 - 共享函数库
# 提供所有脚本使用的通用辅助函数

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color

# 日志函数

# 打印信息消息
info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $*" >&2
}

# 打印警告消息
warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $*" >&2
}

# 打印错误消息
error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $*" >&2
}

# 打印成功消息
success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $*" >&2
}

# 打印调试消息（仅在 DEBUG=1 时显示）
debug() {
    if [[ "${DEBUG:-0}" == "1" ]]; then
        echo -e "${CYAN}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $*" >&2
    fi
}

# 打印步骤标题
step() {
    echo -e "\n${BLUE}==>${NC} ${WHITE}$*${NC}" >&2
}

# 命令检查函数

# 检查命令是否存在
check_command() {
    local cmd="$1"
    local install_hint="${2:-}"
    
    if ! command -v "$cmd" &> /dev/null; then
        error "Command '$cmd' not found"
        if [[ -n "$install_hint" ]]; then
            error "Please install it: $install_hint"
        fi
        return 1
    fi
    debug "Command '$cmd' found: $(command -v "$cmd")"
    return 0
}

# 检查必需的命令列表
check_required_commands() {
    local commands=("$@")
    local missing=()
    
    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing+=("$cmd")
        fi
    done
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        error "Missing required commands: ${missing[*]}"
        error "Please install the missing commands before continuing"
        return 1
    fi
    
    success "All required commands are available"
    return 0
}

# 文件和目录操作

# 确保目录存在
ensure_dir() {
    local dir="$1"
    if [[ ! -d "$dir" ]]; then
        debug "Creating directory: $dir"
        mkdir -p "$dir"
    fi
}

# 检查文件是否存在
check_file() {
    local file="$1"
    if [[ ! -f "$file" ]]; then
        error "File not found: $file"
        return 1
    fi
    return 0
}

# 检查目录是否存在
check_dir() {
    local dir="$1"
    if [[ ! -d "$dir" ]]; then
        error "Directory not found: $dir"
        return 1
    fi
    return 0
}

# 安全地移除文件或目录
safe_remove() {
    local path="$1"
    if [[ -e "$path" ]]; then
        debug "Removing: $path"
        rm -rf "$path"
    fi
}

# 项目路径函数

# 获取项目根目录（相对于脚本位置）
get_project_root() {
    local script_dir
    script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    echo "$(cd "$script_dir/../.." && pwd)"
}

# 获取相对于项目根目录的路径
get_project_path() {
    local relative_path="$1"
    local project_root
    project_root="$(get_project_root)"
    echo "$project_root/$relative_path"
}

# 进程和执行函数

# 运行命令并显示执行信息
run_cmd() {
    local cmd="$*"
    debug "Executing: $cmd"
    
    if [[ "${DRY_RUN:-0}" == "1" ]]; then
        info "[DRY RUN] Would execute: $cmd"
        return 0
    fi
    
    eval "$cmd"
}

# 运行命令，如果失败则退出
run_cmd_or_exit() {
    local cmd="$*"
    if ! run_cmd "$cmd"; then
        error "Command failed: $cmd"
        exit 1
    fi
}

# 在指定目录中运行命令
run_in_dir() {
    local dir="$1"
    shift
    local cmd="$*"
    
    check_dir "$dir"
    
    debug "Executing in $dir: $cmd"
    
    if [[ "${DRY_RUN:-0}" == "1" ]]; then
        info "[DRY RUN] Would execute in $dir: $cmd"
        return 0
    fi
    
    (cd "$dir" && eval "$cmd")
}

# 平台检测函数

# 检测操作系统
detect_os() {
    case "$(uname -s)" in
        Linux*)
            echo "linux"
            ;;
        Darwin*)
            echo "macos"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            echo "windows"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 检测架构
detect_arch() {
    case "$(uname -m)" in
        x86_64|amd64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        armv7l)
            echo "arm"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 检查是否在 CI 环境中
is_ci() {
    [[ "${CI:-false}" == "true" ]] || [[ -n "${GITHUB_ACTIONS:-}" ]] || [[ -n "${GITLAB_CI:-}" ]]
}

# Git 相关函数

# 获取当前 Git 分支
get_git_branch() {
    git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown"
}

# 获取当前 Git 提交哈希
get_git_commit() {
    git rev-parse --short HEAD 2>/dev/null || echo "unknown"
}

# 检查 Git 仓库状态是否干净
is_git_clean() {
    [[ -z "$(git status --porcelain 2>/dev/null)" ]]
}

# 环境变量处理

# 读取环境变量，如果不存在则使用默认值
get_env() {
    local var_name="$1"
    local default_value="${2:-}"
    echo "${!var_name:-$default_value}"
}

# 检查环境变量是否设置
check_env() {
    local var_name="$1"
    if [[ -z "${!var_name:-}" ]]; then
        error "Environment variable '$var_name' is not set"
        return 1
    fi
    return 0
}

# 版本比较函数

# 比较版本号 (semver)
version_compare() {
    local version1="$1"
    local operator="$2"
    local version2="$3"
    
    local v1_major v1_minor v1_patch
    local v2_major v2_minor v2_patch
    
    IFS='.' read -r v1_major v1_minor v1_patch <<< "$version1"
    IFS='.' read -r v2_major v2_minor v2_patch <<< "$version2"
    
    # 补齐缺失的版本号部分
    v1_major="${v1_major:-0}"
    v1_minor="${v1_minor:-0}"
    v1_patch="${v1_patch:-0}"
    v2_major="${v2_major:-0}"
    v2_minor="${v2_minor:-0}"
    v2_patch="${v2_patch:-0}"
    
    local v1_num=$((v1_major * 10000 + v1_minor * 100 + v1_patch))
    local v2_num=$((v2_major * 10000 + v2_minor * 100 + v2_patch))
    
    case "$operator" in
        "="|"==")
            [[ $v1_num -eq $v2_num ]]
            ;;
        "!=")
            [[ $v1_num -ne $v2_num ]]
            ;;
        "<")
            [[ $v1_num -lt $v2_num ]]
            ;;
        "<=")
            [[ $v1_num -le $v2_num ]]
            ;;
        ">")
            [[ $v1_num -gt $v2_num ]]
            ;;
        ">=")
            [[ $v1_num -ge $v2_num ]]
            ;;
        *)
            error "Invalid version comparison operator: $operator"
            return 1
            ;;
    esac
}

# 进度显示函数

# 显示进度条
show_progress() {
    local current="$1"
    local total="$2"
    local message="${3:-Processing}"
    
    local percentage=$((current * 100 / total))
    local filled=$((percentage / 2))
    local empty=$((50 - filled))
    
    printf "\r${message}: ["
    printf "%${filled}s" | tr ' ' '='
    printf "%${empty}s" | tr ' ' '-'
    printf "] %d%% (%d/%d)" "$percentage" "$current" "$total"
    
    if [[ $current -eq $total ]]; then
        printf "\n"
    fi
}

# 清理函数

# 注册清理函数（在脚本退出时执行）
cleanup_functions=()

register_cleanup() {
    local func="$1"
    cleanup_functions+=("$func")
    # 设置 trap 只在第一次调用时执行
    if [[ ${#cleanup_functions[@]} -eq 1 ]]; then
        trap 'run_cleanup_functions' EXIT
    fi
}

run_cleanup_functions() {
    for func in "${cleanup_functions[@]}"; do
        debug "Running cleanup function: $func"
        "$func" || true  # 继续执行其他清理函数，即使某个失败
    done
}

# 脚本初始化函数

# 初始化脚本环境
init_script() {
    local script_name="${1:-$(basename "$0")}"
    
    # 设置错误处理
    set -euo pipefail
    
    # 显示脚本开始信息
    step "Starting $script_name"
    debug "Script: $0"
    debug "Arguments: $*"
    debug "Working directory: $(pwd)"
    debug "Project root: $(get_project_root)"
    debug "OS: $(detect_os)"
    debug "Architecture: $(detect_arch)"
    debug "Git branch: $(get_git_branch)"
    debug "Git commit: $(get_git_commit)"
    
    # 在 CI 环境中显示额外信息
    if is_ci; then
        info "Running in CI environment"
        info "CI System: ${GITHUB_ACTIONS:+GitHub Actions}${GITLAB_CI:+GitLab CI}${CI_SYSTEM:-Unknown}"
    fi
}

# 脚本完成函数
finish_script() {
    local script_name="${1:-$(basename "$0")}"
    success "$script_name completed successfully"
} 