好的，遵照您的指示。我将为您生成一份专门针对 **`cina-coin-ledger-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`cina-coin-ledger-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**双式记账法的实现、数据库事务的原子性、高并发下的并发控制、以及作为平台金融核心的绝对安全与可审计性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `cina-coin-ledger-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `cina-coin-ledger-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 双式记账引擎 + 数据库事务与锁

## 1. 概述

`cina-coin-ledger-service` 是CINA.CLUB平台内部虚拟经济的“**中央银行**”和“**总账本**”。其核心挑战是金融系统固有的：
1.  **数据准确性与一致性**: 账本**绝不能**出错。每一笔交易都必须是原子性的，且整个系统必须时刻保持借贷平衡。
2.  **高并发写入**: 在促销、高频交易场景下，需要处理大量的并发记账请求。
3.  **安全性与防篡改**: 账本数据是平台的核心资产，必须防止任何未授权的访问和修改。
4.  **可审计性**: 任何一笔资金的流动，都必须有清晰、不可篡改的流水记录，以便进行对账和审计。
5.  **逻辑严谨性**: 封装双式记账的复杂规则，为上层业务服务提供简单、可靠的记账接口。

本架构设计通过采用**整洁架构**，并在领域层实现一个严谨的**双式记账引擎**，同时在仓储层利用数据库的**ACID事务和悲观锁**来保证数据一致性，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 整洁架构分层图

```mermaid
graph TD
    subgraph "外部世界 (Frameworks & Drivers)"
        A[gRPC Server<br/>(adapter/transport)]
        B[PostgreSQL<br/>(adapter/repository)]
        C[Admin Tools<br/>(CLI for Auditing)]
    end
    
    subgraph "应用层 (Application)"
        E[LedgerService<br/>(application/service)]
    end
    
    subgraph "领域层 (Domain)"
        F[Aggregates: Transaction<br/>(domain/aggregate)]
        G[Entities: Account, Ledger, Entry<br/>(domain/model)]
        H[Interfaces: Repositories<br/>(application/port)]
        I[Domain Services: DoubleEntryEngine<br/>(domain/service)]
    end
    
    A & C -- "调用" --> E
    E -- "使用" --> H
    B -- "实现" --> H
    E -- "调用" --> I
    I -- "操作" --> F & G
```

### 2.2 最终目录结构 (`services/cina-coin-ledger-service/`)

```
cina-coin-ledger-service/
├── cmd/server/
│   └── main.go                 # API服务启动入口
├── cmd/auditor/                # ✨ 独立的后台对账/调账CLI工具入口 ✨
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   └── handler.go      # gRPC Handler实现
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型
│   │       ├── repo.go         # 实现了所有仓储接口的struct
│   │       ├── ledger_repo.go  # Ledger仓储实现 (含锁)
│   │       └── transaction_repo.go # Transaction仓储实现
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── ledger_service.go # 核心应用服务实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── double_entry_engine.go # ✨ 双式记账核心逻辑 ✨
│           └── chart_of_accounts.go # 定义会计科目表
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Rules of Accounting)

这是本服务最严谨、最核心的部分，封装了会计学原则。

*   **`domain/model/`**: 使用`/core/models`中与账本相关的`struct`，如`Transaction`, `Entry`, `Account`, `Ledger`。
*   **`domain/service/chart_of_accounts.go`**:
    *   **会计科目表 (CoA)**: 以代码形式，**硬编码**平台核心的会计科目表。
    *   **`GetAccount(code)`**: 提供查询科目详情的函数。
    *   **`ValidatePair(debitCode, creditCode)`**: (可选) 定义合法的记账科目配对规则，防止不合逻辑的记账，如“从收入直接转给用户资产”。
*   **`domain/service/double_entry_engine.go`**: **这是双式记账的核心引擎**。
    *   **`DoubleEntryEngine` struct**: 一个无状态的领域服务。
    *   **`NewTransaction(idempotencyKey, type, entries)` method**:
        1.  接收一个交易请求。
        2.  **核心校验1: 借贷平衡**: 遍历所有分录(entries)，累加借方(debit)和贷方(credit)的总额，**必须**严格相等。不等则返回错误。
        3.  **核心校验2: 科目合法性**: 检查每个分录的`account_code`是否存在于会计科目表中。
        4.  **核心校验3: 账户类型与方向**: 检查记账方向是否符合科目类型。例如，不能对一个`ASSET`类型的账户进行贷记(credit)导致其余额为负（除非业务允许透支）。
        5.  如果所有校验通过，返回一个已验证的、准备好被持久化的`domain.Transaction`聚合根对象。

### 3.2 `application/` - 应用层 (The Transaction Orchestrator)

*   `application/port/`: 定义`Repository`和`LedgerService`接口。
*   **`application/service/ledger_service.go`**: 实现`LedgerService`接口，是所有业务流程的编排者。它负责**事务管理和并发控制**。

    **示例: `CreateTransaction` 核心流程**
    ```go
    func (s *ledgerService) CreateTransaction(ctx, req *CreateTxRequest) (*Transaction, error) {
        // 1. 幂等性检查 (可选，也可在handler层做)
        // ...

        // 2. ✨ 调用领域服务进行业务规则校验 ✨
        validatedTx, err := s.engine.NewTransaction(req.IdempotencyKey, req.Type, req.Entries)
        if err != nil {
            return nil, err // 返回InvalidArgument等业务错误
        }

        // 3. ✨ 开启数据库事务（工作单元）✨
        tx, err := s.db.Begin(ctx)
        if err != nil {
            return nil, app_errors.Wrap(err, app_errors.Internal, "failed to begin db transaction")
        }
        defer tx.Rollback(ctx)

        // 4. ✨ 持久化交易主体 ✨
        // 4a. 将validatedTx本身存入transactions表
        err = s.repo.CreateTransaction(ctx, tx, validatedTx)
        if err != nil {
            // 处理幂等键冲突等错误
            return nil, err
        }

        // 5. ✨ 更新账户余额 (核心并发控制点) ✨
        // 5a. 收集所有需要更新的账户(Ledger)ID
        ledgerIDs := collectLedgerIDs(validatedTx.Entries)
        
        // 5b. ✨ 使用悲观锁，一次性锁定所有相关账户行 ✨
        // 这是防止死锁的关键：总是按ID排序来锁定。
        ledgers, err := s.repo.GetLedgesForUpdate(ctx, tx, ledgerIDs)
        if err != nil {
            return nil, err
        }
        
        // 5c. 在内存中计算新余额并更新
        // ...
        
        // 5d. 将更新后的账户写回数据库
        err = s.repo.UpdateLedgers(ctx, tx, updatedLedgers)
        if err != nil {
            return nil, err
        }

        // 6. ✨ 提交数据库事务 ✨
        if err := tx.Commit(ctx); err != nil {
            return nil, app_errors.Wrap(err, app_errors.Internal, "failed to commit db transaction")
        }
        
        // 7. (可选) 发布领域事件
        // s.eventProducer.Publish(ctx, ...)

        return validatedTx, nil
    }
    ```

### 3.3 `adapter/` - 适配层 (The Persistence & Transport)

*   **`adapter/repository/`**: **数据持久化与并发控制的实现**。
    *   **数据库选型**: **必须**是支持**强ACID事务**和**行级锁**的关系型数据库，如 **PostgreSQL**。
    *   `ledger_repo.go`:
        *   **`GetLedgesForUpdate(ctx, tx, ids)`**:
            *   这是实现悲观锁的关键。
            *   SQL语句: `SELECT * FROM ledgers WHERE id IN (...) ORDER BY id ASC FOR UPDATE;`
            *   **`ORDER BY id ASC`** 是至关重要的，它确保了所有并发事务都以相同的顺序去获取锁，从而有效**避免了死锁**。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现`cina_coin_ledger_service.proto`中定义的gRPC服务。
    *   它的职责非常“薄”，主要是参数校验（使用`protoc-gen-validate`）、提取`X-Idempotency-Key`头，然后调用`application.LedgerService`的方法。

### 3.4 `cmd/auditor/` - 后台审计工具

*   **`main.go`**: 一个独立的、**只能由授权管理员在受控环境中运行**的CLI工具。
*   **职责**:
    1.  **对账 (Reconciliation)**:
        *   `auditor reconcile`:
            a. **全局平衡检查**: `SELECT SUM(CASE WHEN direction = 'DEBIT' THEN amount ELSE -amount END) FROM transaction_entries;` 结果必须为0。
            b. **账户快照检查**: 随机抽取或遍历所有账户，将其当前余额与从`transaction_entries`中重新计算出的余额进行比对。
    2.  **调账 (Manual Adjustment)**:
        *   `auditor adjust --reason "..." --debit "..." --credit "..." --amount "..."`:
            a. 创建一笔手动的、同样遵循借贷平衡原则的调账交易。
            b. 所有调账操作必须有极详细的日志和不可更改的记录。

## 4. 总结

本架构设计通过以下关键点来构建一个金融级的`cina-coin-ledger-service`：
1.  **领域驱动的记账引擎**: 将双式记账的会计学规则封装在`domain.service.DoubleEntryEngine`中，与技术实现解耦，保证了逻辑的严谨性和可测试性。
2.  **数据库作为一致性核心**: 充分利用PostgreSQL的**ACID事务**来保证每笔交易的原子性，这是最简单、最可靠的一致性保证机制。
3.  **悲观锁解决并发冲突**: 明确使用`SELECT ... FOR UPDATE`并配合ID排序，来解决高并发下更新同一账户余额时的数据竞争和死锁问题。这是高性能金融系统的标准实践。
4.  **清晰的分层**:
    *   `domain`层负责“会计规则”。
    *   `application`层负责“事务流程和并发控制”。
    *   `adapter`层负责“数据库交互和API暴露”。
5.  **可审计性设计**: 通过独立的审计工具和不可变的交易流水，确保了系统的完全可审计性。

这种架构确保了`cina-coin-ledger-service`在逻辑上是严谨的，在数据上是强一致的，在性能上是可扩展的，并且在安全上是可审计的，能够作为CINA.CLUB平台内部经济体系的绝对信任基石。