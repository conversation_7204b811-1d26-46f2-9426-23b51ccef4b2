好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/errors`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`pkg/errors`的设计哲学、错误模型、与gRPC的集成方式以及最佳实践，作为所有后端服务统一错误处理的权威设计蓝图。

---
### CINA.CLUB - 共享后端库 `pkg/errors` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师/首席工程师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心错误模型](#3-核心错误模型)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [使用示例与最佳实践](#6-使用示例与最佳实践)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在复杂的微服务架构中，一个请求可能跨越多个服务。如果每个服务都以不同的方式定义和返回错误，那么错误的传播、定位和处理将变得极其混乱和低效。`pkg/errors` 包的目的在于建立一套**统一、结构化、可追溯的错误处理规范和工具集**。它通过定义标准的错误类型和错误码，并提供与gRPC状态码的无缝转换，确保了错误信息在整个平台内部和对外的清晰、一致和有用。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   定义一个标准的、可扩展的错误码枚举。
    *   定义一个包含丰富上下文的自定义`AppError`结构体。
    *   提供用于创建、包装(wrapping)和检查`AppError`的工具函数。
    *   提供`AppError`与gRPC状态码之间的双向转换逻辑。
    *   提供将任意`error`转换为标准化`AppError`的工具函数。
*   **范围之外 (Out-of-Scope)**:
    *   **具体的业务错误信息**: 本包只定义错误的“类型”和“结构”，具体的错误消息由业务代码在创建错误时提供。
    *   **错误日志记录**: 由`pkg/logger`负责。本包的错误类型会提供方法以支持结构化日志。
    *   **Panic处理**: 由`pkg/middleware/recovery`中间件负责。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。
*   **API Gateway** 和 **前端团队**（间接），他们将消费由本包规范化的、通过gRPC Status返回的错误信息。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/errors` 是位于`pkg/`目录下的一个基础核心库。它几乎被所有其他的`pkg/`包和所有的`services/`包依赖。它的设计目标是轻量、无外部依赖（除了标准库和gRPC库），并且极度稳定。

#### 2.2. 设计原则
*   **标准化 (Standardization)**: 所有服务都必须使用同一种方式来表示和传递错误。
*   **信息丰富 (Informative)**: 错误对象不仅要告诉我们“发生了什么”，还要提供足够的信息来帮助调试，如错误码、堆栈跟踪、底层原因。
*   **对用户友好，对开发者也友好**: 错误信息需要能够被轻易地转换为对最终用户安全、友好的消息，同时为开发者保留详细的调试信息。
*   **gRPC原生集成 (gRPC-Native)**: 错误模型必须能与gRPC的错误模型（`status`包）完美契合，并利用其`details`字段来传递结构化错误信息。
*   **可追溯性 (Traceability)**: 支持Go 1.13+的错误包装（wrapping）特性，允许保留完整的错误链。

---

### 3. 核心错误模型

#### 3.1. 错误码 (`ErrorCode`)
`ErrorCode`是一个`string`类型的枚举，定义了平台通用的、与业务无关的错误类别。它与HTTP状态码和gRPC状态码有明确的映射关系。

| `ErrorCode`             | gRPC Status Code        | HTTP Status Code | 描述                                       |
|-------------------------|-------------------------|------------------|--------------------------------------------|
| `OK`                    | `OK`                    | 200              | 成功                                       |
| `InvalidArgument`       | `InvalidArgument`       | 400              | 请求参数无效（如格式错误、缺失）           |
| `Unauthenticated`       | `Unauthenticated`       | 401              | 请求需要认证，但未提供或认证无效           |
| `PermissionDenied`      | `PermissionDenied`      | 403              | 已认证，但无权执行该操作                   |
| `NotFound`              | `NotFound`              | 404              | 请求的资源不存在                           |
| `AlreadyExists`         | `AlreadyExists`         | 409              | 尝试创建的资源已存在                       |
| `ResourceExhausted`     | `ResourceExhausted`     | 429              | 资源配额用尽或达到速率限制                 |
| `Canceled`              | `Canceled`              | 499              | 请求被客户端取消                           |
| `Internal`              | `Internal`              | 500              | 服务器内部未知错误                         |
| `Unavailable`           | `Unavailable`           | 503              | 服务暂时不可用（通常是可重试的）           |
| `DeadlineExceeded`      | `DeadlineExceeded`      | 504              | 操作超时                                   |
| `FailedPrecondition`    | `FailedPrecondition`    | 400              | 系统状态不满足操作前提（如账户未激活）     |

#### 3.2. 错误详情 (`AppError` Protobuf Message)
为了能通过gRPC的`details`字段传递结构化的错误信息，我们需要定义一个Protobuf消息。

```protobuf
// core/api/proto/v1/errors.proto
syntax = "proto3";

package hina.v1;

// AppErrorDetail 提供了比标准gRPC错误更丰富的上下文信息。
// 它通过 gRPC 的 details 字段进行传递。
message AppErrorDetail {
  // 平台的统一错误码，如 "InvalidArgument", "NotFound"
  string error_code = 1;

  // (可选) 附加的元数据，为调试或客户端逻辑提供上下文
  map<string, string> metadata = 2;
}
```

#### 3.3. Go错误结构体 (`AppError`)
这是在Go代码中实际使用的错误类型。
```go
// pkg/errors/errors.go
type AppError struct {
    Code     ErrorCode
    Message  string // 对开发者友好的错误信息
    Cause    error  // 被包装的底层错误
    Metadata map[string]string // 附加元数据
    stack    *stack // 创建错误时的堆栈信息
}
```
*   `Code`: 来自**3.1**的`ErrorCode`。
*   `Message`: 详细的、面向开发者的错误描述。**此信息不应直接暴露给最终用户**。
*   `Cause`: 遵循`fmt.Errorf`的`%w`包装规范，保留原始错误链。
*   `Metadata`: 键值对，用于传递额外的上下文，如“哪个字段无效”。
*   `stack`: 自动捕获的堆栈跟踪，用于调试。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 错误创建与包装
*   **FR4.1.1 (创建新错误)**: 提供`New(code ErrorCode, msg string)`和`Newf(code ErrorCode, format string, args ...interface{})`函数，用于在错误链的根部创建一个新的`AppError`。
*   **FR4.1.2 (包装错误)**: 提供`Wrap(cause error, code ErrorCode, msg string)`和`Wrapf(cause error, code ErrorCode, format string, args ...interface{})`函数，用于将一个已有的`error`包装成一个`AppError`，并保留原始错误链。
*   **FR4.1.3 (添加元数据)**: 创建和包装函数都应支持附加元数据。`WithError(err error, key string, value string) error`。

#### 4.2. 错误检查
*   **FR4.2.1 (检查错误码)**: 提供`IsCode(err error, code ErrorCode)`函数，能检查错误链中是否存在指定`ErrorCode`的`AppError`。
*   **FR4.2.2 (获取错误码)**: 提供`GetCode(err error)`函数，返回错误链中最顶层的`AppError`的`ErrorCode`。如果错误链中没有`AppError`，则返回`Internal`。

#### 4.3. gRPC状态转换
*   **FR4.3.1 (错误到gRPC状态)**: 提供`ToGRPCStatus(err error)`函数。
    *   如果`err`是一个`AppError`，它会将其`Code`映射到对应的gRPC状态码，并将`Message`作为gRPC消息，同时将`AppError`本身（序列化为`AppErrorDetail` proto）附加到`details`中。
    *   如果`err`不是`AppError`，则统一视为`Internal`错误。
*   **FR4.3.2 (gRPC状态到错误)**: 提供`FromGRPCError(err error)`函数。
    *   它会检查gRPC错误的`details`中是否包含`AppErrorDetail`。
    *   如果包含，则将其恢复为一个`AppError`实例。
    *   如果不包含，则根据gRPC状态码创建一个对应的`AppError`。

---

### 5. 接口定义 (API Specification)

```go
// pkg/errors/errors.go

type ErrorCode string

// ... (常量定义)
const (
    InvalidArgument ErrorCode = "InvalidArgument"
    // ...
)

// AppError 定义
type AppError struct { ... }

// --- 创建函数 ---
func New(code ErrorCode, msg string) *AppError
func Newf(code ErrorCode, format string, args ...interface{}) *AppError
func Wrap(cause error, code ErrorCode, msg string) *AppError
func Wrapf(cause error, code ErrorCode, format string, args ...interface{}) *AppError

// --- 附加信息 ---
func WithMeta(err error, key string, value string) error

// --- 检查函数 ---
func IsCode(err error, code ErrorCode) bool
func GetCode(err error) ErrorCode

// --- gRPC转换函数 ---
func ToGRPCStatus(err error) *status.Status
func FromGRPCError(err error) *AppError
```

---

### 6. 使用示例与最佳实践

#### 6.1. 在Repository层
当数据库查询返回`pgx.ErrNoRows`时，应将其包装为业务上更有意义的`NotFound`错误。
```go
func (r *userRepo) GetByID(ctx context.Context, id string) (*User, error) {
    user, err := r.db.QueryRow(...)
    if err != nil {
        if errors.Is(err, pgx.ErrNoRows) {
            return nil, app_errors.Wrap(err, app_errors.NotFound, "user not found")
        }
        return nil, app_errors.Wrap(err, app_errors.Internal, "database error on GetByID")
    }
    return user, nil
}
```

#### 6.2. 在Service/Usecase层
业务逻辑验证失败时，创建`InvalidArgument`错误。
```go
func (s *userService) CreateUser(ctx context.Context, req *CreateUserRequest) error {
    if req.Email == "" {
        err := app_errors.New(app_errors.InvalidArgument, "email is required")
        return app_errors.WithMeta(err, "field", "email")
    }
    // ...
}
```

#### 6.3. 在gRPC Handler/Interceptor层
所有从Service层返回的`error`，在返回给gRPC框架前，都应通过`ToGRPCStatus`进行转换。这通常在一个统一的gRPC错误处理拦截器中完成。
```go
func ErrorInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
    resp, err := handler(ctx, req)
    if err != nil {
        // 将业务错误转换为gRPC状态
        return nil, app_errors.ToGRPCStatus(err).Err()
    }
    return resp, nil
}
```

#### 6.4. 在客户端（其他微服务）
当一个微服务调用另一个微服务并收到错误时，使用`FromGRPCError`来解析它。
```go
_, err := userClient.GetUser(ctx, &pb.GetUserRequest{Id: "123"})
if err != nil {
    appErr := app_errors.FromGRPCError(err)
    if app_errors.IsCode(appErr, app_errors.NotFound) {
        // 处理用户不存在的情况
    }
    // ...
}
```

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**: 错误创建和包装的性能开销必须极小。堆栈跟踪的捕获是最大的开销点，可以考虑只在开发/Staging环境或特定错误级别（如`Internal`）开启。
*   **NFR7.2 (可靠性)**: 库本身不能产生panic，必须是线程安全的。
*   **NFR7.3 (可测试性)**: 所有工具函数都必须有单元测试。

---

### 8. 技术约束与开发规范

*   **TC8.1 (依赖库)**:
    *   `google.golang.org/grpc/status`
    *   `google.golang.org/protobuf/proto`
    *   `github.com/pkg/errors` (用于堆栈跟踪的实现) 或 Go 1.13+ 的标准库`errors`。
*   **TC8.2 (开发规范)**:
    *   **强制使用**: 所有后端服务**必须**使用`pkg/errors`来创建和返回错误。
    *   **禁止裸错误**: 禁止直接返回`fmt.Errorf`或第三方库的原始错误给上层，必须进行包装。
    *   **消息分离**: `AppError.Message`用于开发者调试，**绝不**直接展示给最终用户。需要给用户看的消息应由API Gateway或前端根据`ErrorCode`和`Metadata`进行国际化(i18n)转换。

---
这份SRS为`pkg/errors`库的设计和实现提供了清晰、全面的指导。通过强制所有后端服务使用这个标准化的错误处理包，CINA.CLUB平台可以确保其错误处理流程的**一致性、可追溯性和健壮性**，极大地提升了系统的可维护性和开发者体验。