// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-15 12:00:00
// Modified: 2025-01-15 12:00:00

package middleware

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"

	"cina.club/pkg/logger"
)

// HTTPLoggingMiddleware creates HTTP request logging middleware for Gin
func HTTPLoggingMiddleware(appLogger logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// Process request
		c.Next()

		// Log request
		latency := time.Since(start)
		statusCode := c.Writer.Status()
		clientIP := c.ClientIP()

		appLogger.Info(context.Background(), "HTTP request processed",
			"method", method,
			"path", path,
			"status", statusCode,
			"latency", latency.String(),
			"client_ip", clientIP,
		)
	}
}

// CORSMiddleware handles Cross-Origin Resource Sharing
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// AuthMiddleware handles service-to-service authentication
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Simple authentication middleware
		// In a real implementation, this would validate JWT tokens or API keys

		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// For development, allow requests without auth
			// In production, this should return 401
			c.Next()
			return
		}

		// TODO: Implement proper S2S authentication
		// For now, just pass through
		c.Next()
	}
}
