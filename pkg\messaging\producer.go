/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/segmentio/kafka-go"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/protobuf/proto"
)

// Producer 生产者接口
type Producer interface {
	// Publish 发布消息到指定 topic
	Publish(ctx context.Context, topic string, key string, msg proto.Message) error
	// PublishWithHeaders 发布消息并携带自定义头
	PublishWithHeaders(ctx context.Context, topic string, key string, msg proto.Message, headers map[string]string) error
	// Close 关闭生产者
	Close() error
}

// KafkaProducer Kafka 生产者实现
type KafkaProducer struct {
	writer     *kafka.Writer
	serializer Serializer
	propagator *Propagator
	logger     *slog.Logger
	tracer     trace.Tracer
	config     KafkaProducerConfig
	closed     bool
	mu         sync.RWMutex
}

// NewProducer 创建新的 Kafka 生产者
func NewProducer(cfg KafkaProducerConfig, logger *slog.Logger, tracer trace.Tracer) (Producer, error) {
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	writer, err := NewKafkaWriter(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create writer: %w", err)
	}

	producer := &KafkaProducer{
		writer:     writer,
		serializer: NewProtobufSerializer(),
		propagator: NewPropagator(nil), // 使用默认的 W3C Trace Context
		logger:     logger,
		tracer:     tracer,
		config:     cfg,
		closed:     false,
	}

	// 设置异步写入的完成回调
	if cfg.WriterConfig.Async {
		writer.Completion = producer.onCompletion
	}

	return producer, nil
}

// Publish 发布消息到指定 topic
func (p *KafkaProducer) Publish(ctx context.Context, topic string, key string, msg proto.Message) error {
	return p.PublishWithHeaders(ctx, topic, key, msg, nil)
}

// PublishWithHeaders 发布消息并携带自定义头
func (p *KafkaProducer) PublishWithHeaders(ctx context.Context, topic string, key string, msg proto.Message, headers map[string]string) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.closed {
		return ErrProducerClosed
	}

	// 创建追踪 span
	ctx, span := p.tracer.Start(ctx, "kafka.publish")
	defer span.End()

	// 序列化消息
	payload, err := p.serializer.Marshal(msg)
	if err != nil {
		p.logger.ErrorContext(ctx, "Failed to serialize message",
			"error", err,
			"topic", topic,
			"key", key,
		)
		return fmt.Errorf("serialization failed: %w", err)
	}

	// 构建 Kafka 消息
	kafkaMsg := kafka.Message{
		Topic: topic,
		Key:   []byte(key),
		Value: payload,
		Time:  time.Now(),
	}

	// 添加标准头
	eventType := GetEventType(msg)
	eventID := generateEventID()
	AddStandardHeaders(&kafkaMsg.Headers, eventType, eventID, p.getSourceService())

	// 添加自定义头
	if headers != nil {
		for k, v := range headers {
			kafkaMsg.Headers = append(kafkaMsg.Headers, kafka.Header{
				Key:   k,
				Value: []byte(v),
			})
		}
	}

	// 注入追踪上下文
	p.propagator.Inject(ctx, &kafkaMsg.Headers)

	// 发送消息
	if err := p.writer.WriteMessages(ctx, kafkaMsg); err != nil {
		p.logger.ErrorContext(ctx, "Failed to write message",
			"error", err,
			"topic", topic,
			"key", key,
			"event_type", eventType,
			"event_id", eventID,
		)
		return fmt.Errorf("write failed: %w", err)
	}

	p.logger.DebugContext(ctx, "Message published successfully",
		"topic", topic,
		"key", key,
		"event_type", eventType,
		"event_id", eventID,
		"payload_size", len(payload),
	)

	return nil
}

// Close 关闭生产者
func (p *KafkaProducer) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil
	}

	p.closed = true

	if err := p.writer.Close(); err != nil {
		p.logger.Error("Failed to close writer", "error", err)
		return fmt.Errorf("failed to close writer: %w", err)
	}

	p.logger.Info("Producer closed successfully")
	return nil
}

// onCompletion 异步写入完成回调
func (p *KafkaProducer) onCompletion(messages []kafka.Message, err error) {
	if err != nil {
		p.logger.Error("Async write failed",
			"error", err,
			"message_count", len(messages),
		)
	} else {
		p.logger.Debug("Async write completed",
			"message_count", len(messages),
		)
	}
}

// getSourceService 获取源服务名称
func (p *KafkaProducer) getSourceService() string {
	// 可以从配置或环境变量中获取
	// 这里简化处理，实际应用中可以通过依赖注入传入
	return "unknown-service"
}

// generateEventID 生成事件ID
func generateEventID() string {
	// 简化实现，实际应用中可以使用 UUID
	return fmt.Sprintf("evt_%d", time.Now().UnixNano())
}

// MockProducer 模拟生产者，用于测试
type MockProducer struct {
	Messages []MockMessage
	Closed   bool
	Error    error
	mu       sync.Mutex
}

// MockMessage 模拟消息
type MockMessage struct {
	Topic   string
	Key     string
	Message proto.Message
	Headers map[string]string
}

// NewMockProducer 创建模拟生产者
func NewMockProducer() *MockProducer {
	return &MockProducer{
		Messages: make([]MockMessage, 0),
	}
}

// Publish 模拟发布消息
func (m *MockProducer) Publish(ctx context.Context, topic string, key string, msg proto.Message) error {
	return m.PublishWithHeaders(ctx, topic, key, msg, nil)
}

// PublishWithHeaders 模拟发布消息并携带自定义头
func (m *MockProducer) PublishWithHeaders(ctx context.Context, topic string, key string, msg proto.Message, headers map[string]string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.Closed {
		return ErrProducerClosed
	}

	if m.Error != nil {
		return m.Error
	}

	m.Messages = append(m.Messages, MockMessage{
		Topic:   topic,
		Key:     key,
		Message: msg,
		Headers: headers,
	})

	return nil
}

// Close 模拟关闭
func (m *MockProducer) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.Closed = true
	return nil
}

// GetMessages 获取已发送的消息
func (m *MockProducer) GetMessages() []MockMessage {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 返回副本避免并发问题
	messages := make([]MockMessage, len(m.Messages))
	copy(messages, m.Messages)
	return messages
}

// Reset 重置模拟生产者
func (m *MockProducer) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.Messages = make([]MockMessage, 0)
	m.Closed = false
	m.Error = nil
}
