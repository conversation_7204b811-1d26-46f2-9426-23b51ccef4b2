/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

package interceptor

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"cina.club/pkg/auth/s2s"
)

const (
	s2sTokenHeader = "x-s2s-token"
)

var (
	ErrMissingS2SToken = errors.New("missing S2S token")
	ErrInvalidS2SToken = errors.New("invalid S2S token")
)

// S2SJWTInterceptor provides S2S JWT validation
type S2SJWTInterceptor struct {
	verifier        *s2s.Verifier
	serviceName     string // Current service name (used as audience)
	requiredService string // If set, only allow tokens from this specific service
}

// NewS2SJWTInterceptor creates a new S2S JWT interceptor
func NewS2SJWTInterceptor(verifier *s2s.Verifier, serviceName string) *S2SJWTInterceptor {
	return &S2SJWTInterceptor{
		verifier:    verifier,
		serviceName: serviceName,
	}
}

// NewS2SJWTInterceptorWithRequiredService creates a new S2S JWT interceptor that only allows tokens from a specific service
func NewS2SJWTInterceptorWithRequiredService(verifier *s2s.Verifier, serviceName, requiredService string) *S2SJWTInterceptor {
	return &S2SJWTInterceptor{
		verifier:        verifier,
		serviceName:     serviceName,
		requiredService: requiredService,
	}
}

// UnaryInterceptor returns a unary server interceptor for S2S JWT validation
func (i *S2SJWTInterceptor) UnaryInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		newCtx, err := i.validateAndInjectService(ctx)
		if err != nil {
			return nil, status.Errorf(codes.Unauthenticated, "S2S authentication failed: %v", err)
		}

		return handler(newCtx, req)
	}
}

// StreamInterceptor returns a stream server interceptor for S2S JWT validation
func (i *S2SJWTInterceptor) StreamInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		newCtx, err := i.validateAndInjectService(ss.Context())
		if err != nil {
			return status.Errorf(codes.Unauthenticated, "S2S authentication failed: %v", err)
		}

		// Create a new server stream with the updated context
		wrapped := &wrappedServerStream{ServerStream: ss, ctx: newCtx}
		return handler(srv, wrapped)
	}
}

// validateAndInjectService validates the S2S JWT token and injects service info into context
func (i *S2SJWTInterceptor) validateAndInjectService(ctx context.Context) (context.Context, error) {
	// Extract token from metadata
	tokenString, err := i.extractS2SToken(ctx)
	if err != nil {
		return nil, err
	}

	// Validate S2S token
	claims, err := i.verifier.VerifyToken(ctx, tokenString, i.serviceName)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrInvalidS2SToken, err)
	}

	// Check if service is allowed (if restriction is configured)
	if i.requiredService != "" && claims.Service != i.requiredService {
		return nil, fmt.Errorf("service %s not allowed to access this endpoint", claims.Service)
	}

	// Create service identity from claims
	service := &ServiceIdentity{
		Name: claims.Service,
	}

	// Inject service into context
	return NewContextWithService(ctx, service), nil
}

// extractS2SToken extracts the S2S JWT token from gRPC metadata
func (i *S2SJWTInterceptor) extractS2SToken(ctx context.Context) (string, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "", ErrMissingS2SToken
	}

	tokens := md.Get(s2sTokenHeader)
	if len(tokens) == 0 {
		return "", ErrMissingS2SToken
	}

	return strings.TrimSpace(tokens[0]), nil
}
