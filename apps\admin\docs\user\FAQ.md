# Admin Dashboard - FAQ & Troubleshooting

This document provides answers to frequently asked questions and solutions for common issues.

## Frequently Asked Questions (FAQ)

**Q1: I can't log in. What should I do?**
-   **A:** First, double-check that you are using the correct email and password. If you have forgotten your password, use the "Forgot Your Password?" link on the login page to initiate a reset. If you are still unable to log in, contact a super-administrator to ensure your account is active and has the correct permissions.

**Q2: Why can't I see the "User Management" section in the sidebar?**
-   **A:** Access to different sections of the admin dashboard is based on user roles and permissions. If you cannot see a section, it means your current role does not have the required permissions to view it. Please contact a super-administrator to request access if you believe this is in error.

**Q3: How do I export a list of users?**
-   **A:** Navigate to the **User Management** page. Above the user table, you will find an **"导出数据" (Export Data)** button. Clicking this will download a CSV file of the currently filtered user list.

**Q4: The data on my dashboard seems out of date. How can I refresh it?**
-   **A:** Most data on the dashboard refreshes automatically. However, you can always perform a hard refresh of any page by using the **Reload** button in the table options or by refreshing your browser (Ctrl+R or Cmd+R). The application is designed to refetch data automatically when you refocus the window.

## Troubleshooting

**Issue 1: A user I created did not receive their invitation email.**
-   **Solution 1:** Ask the user to check their spam or junk mail folder.
-   **Solution 2:** Navigate to the user's profile page. There should be an option to **"Resend Invitation"**.
-   **Solution 3:** Verify that the email address was entered correctly. If not, you may need to delete the user and create a new one with the correct address.

**Issue 2: The virtualized table in "User Management" is behaving strangely or not scrolling properly.**
-   **Solution 1:** This is a complex component. The first step is always a hard refresh of the page (Ctrl+R or Cmd+R), which can resolve temporary rendering glitches.
-   **Solution 2:** Ensure your browser is up-to-date. The virtual scrolling relies on modern browser features.
-   **Solution 3:** If the issue persists, take a screenshot of the browser's developer console (F12) and report it to the development team, as this may indicate a code-level bug.

---
*This document is a template. Please expand it as new common questions and issues arise.* 