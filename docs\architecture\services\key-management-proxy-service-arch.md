好的，遵照您的指示。我将为您生成一份专门针对 **`key-management-proxy-service` (KMSProxy)** 的、极致细化的、生产级**内部架构设计文档**。

这份文档是整个平台**安全与隐私体系的基石**，将基于您提供的`key-management-proxy-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**信封加密模型的实现、与云KMS的安全交互、最小权限原则、防篡改的审计日志，以及作为平台安全核心的极致可靠性与安全性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `key-management-proxy-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `key-management-proxy-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 代理模式(Proxy) + 适配器模式(Adapter)

## 1. 概述

`key-management-proxy-service` (KMSProxy) 是CINA.CLUB安全架构的“**数字保险库**”和**密钥访问的唯一网关**。它实现了应用层加密(ALE)的核心逻辑。其核心挑战在于：
1.  **绝对安全**: 必须防止任何形式的密钥泄露，包括来自内部开发/运维人员的威胁。明文密钥在内存中的生命周期必须被控制到最短。
2.  **与云KMS的解耦**: 需要能适配不同的底层硬件安全模块(HSM)或云密钥管理服务（AWS KMS, Google Cloud KMS, Vault），而不影响上层业务。
3.  **严格的访问控制**: 对密钥的任何操作都必须经过多重、严格的认证和授权检查。
4.  **不可篡改的审计**: 所有密钥操作都必须留下详细、防篡改的审计日志，以满足合规性要求。
5.  **极高的可用性与低延迟**: 本服务的任何故障都将导致所有依赖加密的功能完全瘫痪。

本架构设计通过采用**整洁架构**，并结合**适配器模式**来封装对不同云KMS的调用，同时在应用层实现一个**严格的、多阶段的访问控制管道**，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (信封加解密代理流程)

```mermaid
graph TD
    subgraph "调用方服务"
        style "调用方服务" fill:#eee
        Requester[e.g., personal-kb-service]
    end

    subgraph "KeyManagementProxyService"
        style KeyManagementProxyService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[Application Service<br/><em>application/service</em>]
        C[AccessControlPipeline<br/><em>domain/service</em>]
        D{KMSProviderAdapter<br/><em>(interface)</em>}
        E[Provider Implementations<br/>(AWSKMSAdapter, GoogleKMSAdapter)<br/><em>adapter/provider</em>]
        F[Repository<br/><em>adapter/repository</em>]
        G[AuditLogger<br/><em>adapter/logger</em>]
    end

    subgraph "底层KMS/HSM"
        style "底层KMS/HSM" fill:#f3e5f5
        KMS[AWS KMS / Google Cloud KMS]
    end
    
    Requester -- "1. EncryptData(userId, plaintext)" --> A
    A -- "调用" --> B
    
    B -- "2. Start Access Control Pipeline" --> C
    C -- "2a. S2S Auth Check" --> C
    C -- "2b. User Session Check (calls user-core)" --> C
    C -- "2c. ACL Policy Check" --> C
    C -- "3. Access Granted" --> B
    
    B -- "4. Get WrappedDEK from DB" --> F
    B -- "5. Get KMS Provider" --> E
    E -- "6. Unwrap DEK using" --> KMS
    KMS -- "Returns Plaintext DEK" --> E
    
    B -- "7. [In-Memory] Encrypt plaintext" --> B
    
    B -- "8. Log operation" --> G
    
    B -- "9. Return ciphertext" --> A
    A --> Requester
```

### 2.2 最终目录结构 (`services/key-management-proxy-service/`)

```
key-management-proxy-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   └── user_core_client.go # 用于用户会话验证
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   ├── logger/
│   │   │   └── audit_logger.go # ✨ 实现了AuditLogger接口的防篡改日志记录器 ✨
│   │   ├── provider/           # ✨ KMS提供商的适配器实现 ✨
│   │   │   ├── interface.go    # 定义KMSProvider接口
│   │   │   ├── aws_kms_adapter.go
│   │   │   └── google_kms_adapter.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go         # 存储WrappedDEK
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── kms_proxy_service.go # 核心应用服务实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── access_control_pipeline.go # ✨ 访问控制管道服务 ✨
│           └── crypto_service.go # 本地DEK生成和数据加解密
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Security Primitives)

*   `domain/model/`: 定义`WrappedDEK`, `DEKVersion`等核心领域对象。
*   **`domain/service/crypto_service.go`**:
    *   **`CryptoService`**: 一个无状态的领域服务，封装了本地的、与KMS无关的密码学操作。
    *   **`GenerateDEK()`**: 使用`crypto/rand`生成一个新的、32字节的AES密钥。
    *   **`EncryptWithDEK(plaintext, dek)`**: 使用AES-256-GCM对数据进行加密。
    *   **`DecryptWithDEK(ciphertext, dek)`**: 解密数据。
*   **`domain/service/access_control_pipeline.go`**: **这是本服务安全性的核心保证**。
    *   **`AccessControlPipeline`**: 一个领域服务，通过链式调用执行多阶段验证。
    *   **`Validate(ctx, targetUserID, callingService)` method**:
        1.  **S2S认证检查**: 从`ctx`中提取调用方服务身份，验证其合法性（此部分可由`pkg/auth`的S2S拦截器完成）。
        2.  **用户会话验证**: 从`ctx`中提取用户JWT，并调用`user-core-service`的内省接口，**验证该token是否有效，且其`sub`（用户ID）是否与`targetUserID`匹配**。
        3.  **ACL策略检查**: 从一个**硬编码或安全配置**中，加载一个ACL策略表（`map[calling_service_name][]allowed_target_service`），检查调用方服务是否有权访问本服务。
        *   只有所有检查都通过，`Validate`方法才返回`nil`。任何失败都会返回一个明确的`PermissionDenied`错误。

### 3.2 `application/` - 应用层 (The Key Operations)

*   `application/port/`: 定义`Repository`, `KMSProxyService`等接口。
*   **`application/service/kms_proxy_service.go`**: 实现`KMSProxyService`接口，是所有gRPC请求的业务流程编排者。
    *   **`EncryptData(ctx, userID, plaintext)`**:
        1.  **调用`accessControlPipeline.Validate()`进行权限检查**。
        2.  从`repository.GetActiveDEK(userID)`获取用户的当前`WrappedDEK`和`masterKeyID`。
        3.  使用`provider.Factory`获取`masterKeyID`对应的`KMSProvider`适配器。
        4.  **调用`provider.Unwrap(wrappedDEK)`**，通过云KMS解包得到**明文DEK**。
        5.  **调用`domain.CryptoService.EncryptWithDEK(plaintext, plaintextDEK)`**对数据进行加密。
        6.  **在`defer`语句中或立即安全地将明文DEK从内存中清除（置零）**。
        7.  调用`auditLogger.Log(...)`记录本次操作。
        8.  返回加密后的数据。
    *   **`DecryptData(ctx, userID, ciphertext)`**: 流程类似，但最后一步是调用`DecryptWithDEK`。
    *   **`ProvisionUserDEK(ctx, userID)`**:
        1.  调用`domain.CryptoService.GenerateDEK()`生成新的明文DEK。
        2.  获取默认的`KMSProvider`。
        3.  **调用`provider.Wrap(plaintextDEK)`**得到`WrappedDEK`。
        4.  将`WrappedDEK`和版本信息存入数据库。
        5.  安全地清除明文DEK。

### 3.3 `adapter/` - 适配层 (The Infrastructure & I/O Bridge)

*   **`adapter/provider/`**: **适配器模式的实现，用于适配不同云KMS**。
    *   `interface.go`: 定义`KMSProvider`接口，包含`Wrap(plaintextKey)`和`Unwrap(ciphertextKey)`方法。
    *   `aws_kms_adapter.go`, `google_kms_adapter.go`: 分别实现`KMSProvider`接口，封装对AWS KMS SDK和Google Cloud KMS SDK的调用。这使得更换底层KMS提供商对上层应用完全透明。
*   **`adapter/repository/`**:
    *   使用**PostgreSQL**存储`user_wrapped_deks`表。
    *   数据库连接信息和密码**必须**通过安全的方式注入。
*   **`adapter/logger/audit_logger.go`**:
    *   **审计日志记录器**: 实现了`AuditLogger`接口。
    *   **特殊要求**: 它的日志目标**不能**是普通的`stdout`或文件。为了防篡改，它应将结构化的审计日志直接写入到一个**专用的、WORM (Write-Once, Read-Many)特性**的存储中，如AWS S3 Object Lock, Google Cloud Storage Bucket Lock，或专门的日志审计系统。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现gRPC接口，调用`application.KMSProxyService`的方法。在调用前，它会从gRPC元数据中提取所有需要的信息（如用户JWT），并注入到`ctx`中。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`key-management-proxy-service`：
1.  **分层防御**:
    *   **gRPC拦截器 (`pkg/auth`)** 提供第一层S2S认证。
    *   **`AccessControlPipeline`** 在应用层提供第二层、更精细的业务权限验证。
2.  **职责明确的领域服务**:
    *   `CryptoService`负责纯粹的、本地的密码学计算。
    *   `AccessControlPipeline`负责纯粹的权限校验。
    *   这使得最核心的安全逻辑内聚、简单且极易审计。
3.  **适配器模式隔离外部依赖**: 通过`KMSProvider`适配器，将对具体云KMS的依赖完全隔离，提高了系统的灵活性和可移植性。
4.  **无状态与高可用**: 服务本身无状态（状态在DB中），可以水平扩展。依赖的云KMS本身就是高可用的。
5.  **不可篡改的审计**: 设计了专门的`AuditLogger`，将审计日志写入WORM存储，满足最严格的合规要求。

这种架构确保了`key-management-proxy-service`在逻辑上是严谨的，在安全上是纵深防御的，在实现上是可维护和可扩展的，能够作为CINA.CLUB平台**隐私保护承诺的最终技术保障**。