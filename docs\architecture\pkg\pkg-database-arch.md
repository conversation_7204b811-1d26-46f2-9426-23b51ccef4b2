好的，遵照您的指示。我将为您生成一份专门针对 **`pkg/database`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`pkg/database` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到代码实现的层面，详细阐述**数据库客户端工厂的实现、如何无缝集成可观测性（Tracing, Logging, Metrics），以及如何为上层提供一个统一、可靠、高性能的数据访问基础**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `pkg/database` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `pkg/database-srs.md` (v1.0)
**核心架构**: 工厂模式(Factory Pattern) + 可观测性装饰器(Observability Decorator)

## 1. 概述

`pkg/database` 是CINA.CLUB后端微服务生态中，与所有数据存储进行交互的**统一入口和抽象层**。它不是一个ORM，而是一个**数据库客户端工厂和配置中心**。其架构设计的核心目标是：
1.  **标准化**: 提供统一的`New...Client()`函数来创建和配置所有类型的数据库连接，避免在每个服务中重复造轮子。
2.  **可观测性内建 (Observability by Default)**: 任何通过本包创建的数据库客户端，都**必须**自动集成分布式追踪、结构化日志和Prometheus指标，开发者无需手动处理。
3.  **高性能**: 默认使用和配置高性能的数据库驱动（如`pgx`），并遵循连接池的最佳实践。
4.  **可靠性**: 封装连接、重试和健康检查逻辑，确保返回给业务层的是一个健康、可用的客户端实例。
5.  **解耦**: 将业务代码与具体的数据库驱动库和可观测性库的实现细节解耦。

本架构设计通过采用**工厂模式**来创建客户端，并通过**装饰器模式（以钩子/拦截器的形式）**将可观测性逻辑无侵入地注入到客户端中。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (工厂与可观测性集成)

```mermaid
graph TD
    subgraph "Application Layer (in a Microservice)"
        A[main.go / Dependency Injector]
        B[Repository Layer<br/><em>(e.g., user_repo.go)</em>]
    end

    subgraph "pkg/database"
        style "pkg/database" fill:#e0f7fa
        C[PostgresFactory<br/><em>(postgres.go)</em>]
        D[RedisFactory<br/><em>(redis.go)</em>]
        E[MongoFactory<br/><em>(mongo.go)</em>]
        
        subgraph "Internal Observability Integrations"
            I_PG[pgx OTel/slog Integration]
            I_Redis[go-redis OTel/slog Hook]
            I_Mongo[mongo-go OTel/slog Monitor]
        end
    end
    
    subgraph "Other pkgs"
        P1[pkg/config]
        P2[pkg/logger]
        P3[pkg/tracing]
    end

    A -- "1. Calls NewPostgresPool(cfg, logger, tracer)" --> C
    
    C -- "a. Reads" --> P1
    C -- "b. Injects" --> P2 & P3
    C -- "c. Uses" --> I_PG
    
    C -- "2. Returns instrumented *pgxpool.Pool" --> A
    A -- "3. Injects pool into" --> B
    
    B -- "4. Executes query using the pool" --> B
    
    %% Instrumentation in action
    subgraph "On Query Execution"
        I_PG -- "Creates Trace Span" --> P3
        I_PG -- "Writes detailed log" --> P2
    end
    B -.-> I_PG
```

### 2.2 最终目录结构 (`pkg/database/`)

```
pkg/database/
├── postgres.go         # ✨ PostgreSQL (pgx) 客户端工厂 ✨
├── redis.go            # ✨ Redis (go-redis) 客户端工厂 ✨
├── mongo.go            # ✨ MongoDB (mongo-go) 客户端工厂 ✨
├── gorm.go             # (可选) GORM 封装
└── internal/
    └── instrument/
        ├── pgx.go      # pgx的追踪与日志集成逻辑
        ├── redis.go    # go-redis的钩子实现
        └── mongo.go    # mongo-go的命令监视器实现
```

---

## 3. 各层职责深度解析

### 3.1 `postgres.go` - PostgreSQL客户端工厂

*   **`PostgresConfig` struct**: 定义所有可配置的参数，与`pkg/config`的`mapstructure`标签对齐。
    ```go
    type PostgresConfig struct {
        DSN             string `mapstructure:"dsn" validate:"required"`
        MaxConns        int32  `mapstructure:"max_conns" default:"10"`
        // ...
    }
    ```
*   **`NewPostgresPool(ctx, cfg, logger, tracer)` function**:
    1.  **解析配置**: `poolConfig, err := pgxpool.ParseConfig(cfg.DSN)`。
    2.  **设置连接池参数**: `poolConfig.MaxConns = cfg.MaxConns`。
    3.  **✨ 注入可观测性 (核心) ✨**:
        *   创建一个`internal/instrument.PgxTracer`实例，它实现了`pgx.QueryTracer`等接口。
        *   `poolConfig.ConnConfig.Tracer = NewPgxTracer(tracer)`。
        *   设置`poolConfig.ConnConfig.Logger`为一个适配器，它将`pgx`的日志转换为`slog`结构化日志。
    4.  **创建连接池**: `pool, err := pgxpool.NewWithConfig(ctx, poolConfig)`。
    5.  **健康检查**: `if err := pool.Ping(ctx); err != nil { ... }`。
    6.  返回`*pgxpool.Pool`。

### 3.2 `redis.go` - Redis客户端工厂

*   **`RedisConfig` struct**: 定义`Addr`, `Password`, `DB`, `PoolSize`等。
*   **`NewRedisClient(cfg, logger, tracer)` function**:
    1.  **解析配置**: `opts, err := redis.ParseURL(cfg.Addr)` (如果地址是URL格式)，或手动设置`redis.Options`。
    2.  **创建客户端**: `rdb := redis.NewClient(opts)`。
    3.  **✨ 注入可观测性 (核心) ✨**:
        *   创建一个`internal/instrument.RedisHook`实例，它实现了`redis.Hook`接口。
        *   `rdb.AddHook(NewRedisHook(tracer, logger))`。
    4.  **健康检查**: `if err := rdb.Ping(ctx).Err(); err != nil { ... }`。
    5.  返回`*redis.Client`。

### 3.3 `internal/instrument/` - 可观测性集成实现

这是所有“魔法”发生的地方，它将被所有工厂函数使用。

*   **`pgx.go`**:
    *   **`PgxTracer` struct**: 实现了`pgx.QueryTracer`接口。
    *   **`TraceQueryStart(ctx, conn, data)`**:
        1.  从`ctx`中启动一个新的`trace.Span`，命名为`db.sql.query`。
        2.  为Span添加标准属性（`db.system`, `db.statement`, `db.user`）。
        3.  返回一个新的`ctx`，其中包含了这个子Span。
    *   **`TraceQueryEnd(ctx, conn, data, err)`**:
        1.  从`ctx`中获取Span。
        2.  记录错误（如果`err != nil`）。
        3.  结束Span。
*   **`redis.go`**:
    *   **`RedisHook` struct**: 实现了`redis.Hook`接口。
    *   **`DialHook(next)`**: 包装建连操作。
    *   **`ProcessHook(next)`**:
        1.  在`next(ctx, cmd)`执行**前**，启动一个Span，命名为`db.redis.command`，并记录命令和参数。
        2.  在执行**后**，记录错误并结束Span。
*   **`mongo.go`**:
    *   **`MongoCommandMonitor` struct**:
    *   **`Started(ctx, evt)`**: 启动Span。
    *   **`Succeeded(ctx, evt)`**: 成功结束Span。
    *   **`Failed(ctx, evt)`**: 记录错误并结束Span。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`pkg/database`：
1.  **工厂模式**: 为每种数据库提供了统一、简单的`New...`创建函数，屏蔽了底层驱动的复杂初始化过程。
2.  **可观测性内建**: 通过利用各个数据库驱动提供的**钩子(Hook)、拦截器(Interceptor)或监视器(Monitor)**机制，以**装饰器模式**无侵入地将分布式追踪、结构化日志和指标监控注入到每一次数据库操作中。
3.  **性能与可靠性优先**:
    *   默认采用如`pgxpool`这样的高性能连接池。
    *   所有工厂函数都包含一个强制的健康检查步骤，确保返回的客户端是立即可用的。
    *   封装了连接池的最佳实践配置。
4.  **依赖注入**: 明确地将`Config`, `Logger`, `Tracer`作为参数注入，而不是使用全局变量，这使得代码清晰、易于测试。
5.  **解耦**: 业务层的`Repository`只与返回的**标准客户端接口**（如`*pgxpool.Pool`）交互，完全无需关心其是如何被创建、配置和instrument的。

这种架构确保了`pkg/database`能够为所有后端服务提供一个**统一、可靠、高性能且完全可观测**的数据访问基础，是构建一个健壮的、易于维护的分布式系统的关键。