好的，遵照您的指示。我将为您生成一份专门针对 **`wallet-meta-service`** 的、极致细化的、生产级的**软件需求规格说明书 (SRS)**。

这份文档将详细定义这个服务的功能、接口、数据模型和非功能性要求，使其成为钱包个性化和辅助功能的核心。

---
### CINA.CLUB - `wallet-meta-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-28**  
**文档负责人:** [Web3产品/技术负责人]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB的Web3钱包功能旨在提供卓越的用户体验，而不仅仅是基础的资产管理。`wallet-meta-service` 的目的在于构建一个**中心化的、可靠的钱包元数据管理服务**。它负责存储和管理所有与用户钱包相关的、**非链上、非敏感**的个性化和辅助性数据，如钱包名称、自定义代币、地址簿等。通过将这些元数据与核心的链上交互分离，本服务极大地丰富了钱包的用户体验，同时保持了核心区块链交互服务的纯粹性。

#### 1.2. 服务范围
本服务 **负责**:
*   **钱包档案管理**: 允许用户创建、读取、更新、删除（CRUD）与其`userId`和`walletAddress`关联的钱包档案，主要包括**钱包名称/标签**和所属链。
*   **自定义代币管理**: 允许用户为其在特定链上的钱包，手动添加和管理**自定义代币（Token）**的合约地址列表。
*   **地址簿管理**: 为用户提供一个跨链的、私人的**地址簿**功能，用于存储和备注常用的联系人地址。
*   **DApp收藏夹管理**: (可选) 允许用户收藏常用的DApp，并在所有设备间同步。
*   **数据同步与备份**: 为上述所有元数据提供云端同步和备份。

本服务 **不负责**:
*   **任何私钥、助记词或密码学操作**: 这是客户端`core/crypto`的职责。
*   **任何与区块链的直接交互**: 如查询余额、广播交易，这是`blockchain-gateway-service`的职责。
*   **任何市场数据的获取**: 如代币价格，这是`market-data-service`的职责。
*   **用户核心身份管理**: 由`user-core-service`负责。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 通过API Gateway调用本服务，进行所有钱包个性化设置。
*   **`blockchain-gateway-service`**: (可选) 调用本服务，获取某个用户地址需要查询余额的自定义代币列表。
*   **CINA.CLUB平台管理员**: 通过后台查看统计数据（如热门自定义代币）。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`wallet-meta-service` 是Web3钱包功能的“**用户体验增强层**”和“**个性化配置中心**”。它是一个典型的CRUD密集型服务，为非托管钱包的“冷冰冰”的地址，赋予了用户友好的名称、备注和组织结构。它与`blockchain-gateway-service`（负责“链上事实”）和`market-data-service`（负责“市场价格”）紧密协作，共同为前端构建丰富的钱包视图。

#### 2.2. 主要功能概述
*   支持用户管理多个钱包的个性化档案。
*   提供多链的自定义代币和地址簿管理。
*   为所有钱包元数据提供可靠的云端同步。
*   严格的用户数据隔离和隐私保护。

---

### 3. 核心流程图

#### 3.1 用户添加一个自定义代币并刷新资产列表

```mermaid
sequenceDiagram
    participant Client
    participant WalletMeta as wallet-meta-service
    participant BlockchainGateway as blockchain-gateway-service
    
    Client->>WalletMeta: 1. POST /me/wallets/{walletId}/tokens (contract_address, chain)
    WalletMeta->>DB: 2. 在`wallet_custom_tokens`表中插入新记录
    WalletMeta-->>Client: 3. 201 Created
    
    note over Client: 用户触发资产列表刷新
    
    Client->>WalletMeta: 4. GET /me/wallets/{walletId}/tokens
    WalletMeta-->>Client: 5. (返回包含新代币在内的自定义代币列表)
    
    Client->>BlockchainGateway: 6. GET /balance (address, chain, token_contracts: [..., new_token_address])
    BlockchainGateway-->>Client: 7. (返回所有代币的余额，包括新添加的)
    
    note over Client: UI上显示出新的代币及其余额
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 钱包档案管理
*   **FR4.1.1 (创建钱包档案)**: 当用户在客户端创建一个新钱包或导入一个钱包后，客户端**必须**调用本服务的API，为该`walletAddress`创建一个关联的档案记录。
*   **FR4.1.2 (多钱包支持)**: 一个`userId`下可以关联多个钱包档案，每个档案由`walletAddress`和`chain`唯一标识。
*   **FR4.1.3 (CRUD操作)**: 用户必须能为每个钱包档案设置和修改一个**自定义名称(name/label)**。

#### 4.2. 自定义代币管理
*   **FR4.2.1 (添加代币)**: 用户必须能通过输入**代币的合约地址**，将其添加到一个特定的钱包档案中。
*   **FR4.2.2 (自动获取元数据 - 可选)**: 在添加时，本服务可以（可选地、异步地）调用`blockchain-gateway-service`，尝试获取该合约地址的`name`, `symbol`, `decimals`等信息，并填充到记录中。
*   **FR4.2.3 (列表与移除)**: 用户必须能查看和移除已添加的自定义代币。

#### 4.3. 地址簿管理
*   **FR4.3.1 (添加联系人)**: 用户必须能将一个**区块链地址**添加为其联系人，并为其关联一个**名称/备注**和所属的**链**。
*   **FR4.3.2 (跨钱包共享)**: 地址簿是与用户的`userId`关联的，而非单个钱包地址。用户在任何一个钱包下进行转账时，都可以从同一个地址簿中选择联系人。
*   **FR4.3.3 (CRUD操作)**: 用户必须能对地址簿联系人进行完整的CRUD操作。

#### 4.4. DApp收藏夹 (可选)
*   **FR4.4.1 (收藏)**: 用户在DApp浏览器中，可以将当前访问的DApp URL添加的收藏夹。
*   **FR4.4.2 (管理)**: 用户可以查看、编辑和删除其收藏的DApp列表。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/wallet-meta`
*   **认证**: 用户JWT。
*   **核心端点**:
    *   **Wallets**:
        *   `POST /me/wallets`: 创建/关联一个新的钱包档案。Request: `{ address, chain, name }`
        *   `GET /me/wallets`: 获取用户的所有钱包档案。
        *   `PUT /me/wallets/{walletId}`: 更新钱包名称。
    *   **Custom Tokens**:
        *   `POST /me/wallets/{walletId}/tokens`: 添加自定义代币。Request: `{ contractAddress, chain }`
        *   `GET /me/wallets/{walletId}/tokens`: 获取某钱包的自定义代币列表。
        *   `DELETE /me/wallets/{walletId}/tokens/{tokenId}`: 移除自定义代币。
    *   **Address Book**:
        *   `POST /me/address-book`: 添加联系人。Request: `{ address, chain, name, notes }`
        *   `GET /me/address-book`: 获取地址簿列表。
        *   `PUT /me/address-book/{contactId}`: 更新联系人。
        *   `DELETE /me/address-book/{contactId}`: 删除联系人。

#### 5.2. 内部gRPC API接口 (S2S)
*   **Package**: `hina.v1.wallet_meta`
*   **核心RPC (供其他服务调用)**:
    *   `rpc GetUserWallets(GetUserWalletsRequest) returns (GetUserWalletsResponse)`
    *   `rpc GetWalletCustomTokens(GetWalletCustomTokensRequest) returns (GetWalletCustomTokensResponse)`

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`user_wallets`**:
    *   `id (PK, UUID)`
    *   `user_id (FK, INDEX)`
    *   `address (VARCHAR(255), INDEX)`: 钱包地址
    *   `chain (VARCHAR(50), INDEX)`: 如 "bsc", "ethereum"
    *   `name (VARCHAR(100))`: 用户自定义名称
    *   UNIQUE on (`user_id`, `address`, `chain`)
*   **`wallet_custom_tokens`**:
    *   `id (PK, UUID)`
    *   `wallet_id (FK, INDEX)`
    *   `contract_address (VARCHAR(255))`
    *   `symbol (VARCHAR(20), nullable)`
    *   `decimals (INT, nullable)`
    *   UNIQUE on (`wallet_id`, `contract_address`)
*   **`user_address_book`**:
    *   `id (PK, UUID)`
    *   `user_id (FK, INDEX)`
    *   `address (VARCHAR(255))`
    *   `chain (VARCHAR(50))`
    *   `name (VARCHAR(100))`
    *   `notes (TEXT, nullable)`
    *   UNIQUE on (`user_id`, `address`, `chain`)

---

### 7. 非功能性需求 (Non-Functional Requirements)

*   **NFR7.1 (性能)**:
    *   所有API的P99延迟应 `< 150ms`。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。本服务的故障会影响钱包的个性化体验，但不影响核心的资产安全和交易功能。
    *   **数据一致性**: 用户的元数据在多设备间必须保持最终一致性。
*   **NFR7.3 (可扩展性)**:
    *   服务应为无状态，可水平扩展。
    *   数据库可通过读写分离进行优化。
*   **NFR7.4 (安全性)**:
    *   **数据隔离**: 必须通过`user_id`进行严格的数据隔离，确保用户只能访问自己的元数据。
    *   **输入验证**: 对所有用户输入的地址、合约地址等，进行格式合法性校验。

---

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。其关系模型和事务能力非常适合管理这些结构化的元数据。
*   **架构**: 遵循平台统一的整洁架构。
*   **依赖**:
    *   依赖`user-core-service`进行用户认证。
    *   被`blockchain-gateway-service`依赖，以提供更丰富的查询能力。

---
这份SRS为`wallet-meta-service`的设计和实现提供了坚实、全面的指导。通过将钱包的个性化元数据管理抽象成一个独立的服务，它有效地降低了其他Web3核心服务（如`blockchain-gateway-service`）的复杂性，同时为用户提供了丰富、流畅的钱包使用体验，是构建一个成功的、用户友好的Web3产品的关键一环。