#!/usr/bin/env bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB 核心库编译脚本
# 将 /core 目录编译为所有前端平台所需的库文件

set -euo pipefail

# 导入共享函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# shellcheck source=../lib/helpers.sh
source "$SCRIPT_DIR/../lib/helpers.sh"

# 脚本配置
readonly SCRIPT_NAME="core-builder"
readonly CORE_DIR="core"
readonly CORE_MODULE="github.com/cina-club/cina.club-monorepo/core"

# 支持的目标平台
readonly SUPPORTED_TARGETS=(
    "android"
    "ios"
    "wasm"
    "windows"
    "all"
)

# 默认目标
DEFAULT_TARGET="all"

# 编译产物路径配置
declare -A OUTPUT_PATHS=(
    ["android"]="apps/android/app/libs/core-go.aar"
    ["ios"]="apps/apple/Frameworks/CoreGo.xcframework"
    ["wasm"]="apps/web/static/core.wasm"
    ["windows"]="apps/windows/libs/x64/core-go.dll"
)

# 显示使用说明
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Compile the core Go library for specified target platforms.

Options:
    -t, --target TARGET     Target platform(s) to build for
                           Supported: ${SUPPORTED_TARGETS[*]}
                           Default: $DEFAULT_TARGET
    -c, --clean            Clean build artifacts before compilation
    -v, --verbose          Enable verbose output
    -o, --optimize         Enable optimization flags
    -h, --help             Show this help message

Examples:
    $0                                    # Build for all targets
    $0 --target android                   # Build only Android AAR
    $0 --target ios,android               # Build iOS and Android
    $0 --clean --target all               # Clean and build all
    DEBUG=1 $0 --target wasm              # Build WASM with debug output

Environment Variables:
    DEBUG=1                Enable debug output
    DRY_RUN=1             Show commands without executing
    GO_VERSION            Specific Go version to use
    GOMOBILE_VERSION      Specific gomobile version to use
    CGO_ENABLED           Enable/disable CGO (default: 1)
EOF
}

# 解析命令行参数
parse_args() {
    local target="$DEFAULT_TARGET"
    local clean=false
    local verbose=false
    local optimize=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--target)
                target="$2"
                shift 2
                ;;
            -c|--clean)
                clean=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -o|--optimize)
                optimize=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 设置全局变量
    TARGET="$target"
    CLEAN="$clean"
    VERBOSE="$verbose"
    OPTIMIZE="$optimize"
    
    # 启用详细输出
    if [[ "$verbose" == "true" ]]; then
        export DEBUG=1
    fi
}

# 验证目标平台
validate_targets() {
    local targets
    IFS=',' read -ra targets <<< "$TARGET"
    
    for target in "${targets[@]}"; do
        target=$(echo "$target" | xargs)  # 去除空格
        if [[ ! " ${SUPPORTED_TARGETS[*]} " =~ " $target " ]]; then
            error "Unsupported target: $target"
            error "Supported targets: ${SUPPORTED_TARGETS[*]}"
            exit 1
        fi
    done
    
    debug "Validated targets: ${targets[*]}"
}

# 检查必需的工具
check_tools() {
    step "Checking required tools"
    
    # 检查 Go
    if ! check_command "go" "Install Go from https://golang.org/dl/"; then
        exit 1
    fi
    
    # 检查 Go 版本
    local go_version
    go_version=$(go version | awk '{print $3}' | sed 's/go//')
    info "Using Go version: $go_version"
    
    # 检查最低版本要求
    if ! version_compare "$go_version" ">=" "1.22.0"; then
        error "Go version 1.22.0 or higher is required, found: $go_version"
        exit 1
    fi
    
    # 检查目标特定的工具
    local targets
    IFS=',' read -ra targets <<< "$TARGET"
    
    if [[ " ${targets[*]} " =~ " all " ]]; then
        targets=("android" "ios" "wasm" "windows")
    fi
    
    for target in "${targets[@]}"; do
        case "$target" in
            "android"|"ios")
                if ! check_command "gomobile" "Install gomobile: go install golang.org/x/mobile/cmd/gomobile@latest && gomobile init"; then
                    exit 1
                fi
                ;;
            "wasm")
                # WASM 编译只需要标准 Go 工具链
                ;;
            "windows")
                # C-shared 编译只需要标准 Go 工具链和 CGO
                if [[ "$(get_env "CGO_ENABLED" "1")" != "1" ]]; then
                    warn "CGO is disabled, Windows DLL compilation may fail"
                fi
                ;;
        esac
    done
    
    success "All required tools are available"
}

# 检查项目结构
check_project_structure() {
    step "Checking project structure"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 检查核心目录
    if ! check_dir "$project_root/$CORE_DIR"; then
        exit 1
    fi
    
    # 检查 go.mod 文件
    if ! check_file "$project_root/$CORE_DIR/go.mod"; then
        error "go.mod not found in core directory"
        exit 1
    fi
    
    # 检查核心导出文件
    local required_files=(
        "$project_root/$CORE_DIR/exports_mobile.go"
        "$project_root/$CORE_DIR/exports_wasm.go"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            warn "Export file not found: $file"
            warn "Some compilation targets may fail"
        fi
    done
    
    success "Project structure is valid"
}

# 清理构建产物
clean_build_artifacts() {
    if [[ "$CLEAN" != "true" ]]; then
        return 0
    fi
    
    step "Cleaning build artifacts"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 清理所有编译产物
    for target in "${!OUTPUT_PATHS[@]}"; do
        local output_path="$project_root/${OUTPUT_PATHS[$target]}"
        local output_dir
        output_dir="$(dirname "$output_path")"
        
        if [[ -f "$output_path" ]]; then
            info "Cleaning: $output_path"
            safe_remove "$output_path"
        fi
        
        # 清理可能的中间文件
        case "$target" in
            "android")
                safe_remove "$output_dir"/*.aar
                safe_remove "$output_dir"/*.jar
                ;;
            "ios")
                safe_remove "$output_dir"/*.xcframework
                ;;
            "wasm")
                safe_remove "$output_dir"/*.wasm
                safe_remove "$output_dir"/wasm_exec.js
                ;;
            "windows")
                safe_remove "$output_dir"/*.dll
                safe_remove "$output_dir"/*.h
                ;;
        esac
    done
    
    success "Cleaned build artifacts"
}

# 编译 Android AAR
build_android() {
    step "Building Android AAR"
    
    local project_root
    project_root="$(get_project_root)"
    local output_path="$project_root/${OUTPUT_PATHS["android"]}"
    local output_dir
    output_dir="$(dirname "$output_path")"
    
    # 确保输出目录存在
    ensure_dir "$output_dir"
    
    # 构建 gomobile 命令
    local cmd="gomobile bind"
    cmd="$cmd -target=android"
    cmd="$cmd -o $output_path"
    
    if [[ "$OPTIMIZE" == "true" ]]; then
        cmd="$cmd -ldflags='-s -w'"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        cmd="$cmd -v"
    fi
    
    cmd="$cmd $CORE_MODULE"
    
    # 执行编译
    info "Compiling Android AAR..."
    debug "Command: $cmd"
    
    if ! run_in_dir "$project_root" "$cmd"; then
        error "Android AAR compilation failed"
        return 1
    fi
    
    # 验证产物
    if [[ -f "$output_path" ]]; then
        local file_size
        file_size=$(du -h "$output_path" | cut -f1)
        success "Android AAR built successfully: $output_path ($file_size)"
    else
        error "Android AAR not found after compilation"
        return 1
    fi
}

# 编译 iOS Framework
build_ios() {
    step "Building iOS xcframework"
    
    local project_root
    project_root="$(get_project_root)"
    local output_path="$project_root/${OUTPUT_PATHS["ios"]}"
    local output_dir
    output_dir="$(dirname "$output_path")"
    
    # 确保输出目录存在
    ensure_dir "$output_dir"
    
    # 构建 gomobile 命令
    local cmd="gomobile bind"
    cmd="$cmd -target=ios"
    cmd="$cmd -o $output_path"
    
    if [[ "$OPTIMIZE" == "true" ]]; then
        cmd="$cmd -ldflags='-s -w'"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        cmd="$cmd -v"
    fi
    
    cmd="$cmd $CORE_MODULE"
    
    # 执行编译
    info "Compiling iOS xcframework..."
    debug "Command: $cmd"
    
    if ! run_in_dir "$project_root" "$cmd"; then
        error "iOS xcframework compilation failed"
        return 1
    fi
    
    # 验证产物
    if [[ -d "$output_path" ]]; then
        local framework_size
        framework_size=$(du -sh "$output_path" | cut -f1)
        success "iOS xcframework built successfully: $output_path ($framework_size)"
    else
        error "iOS xcframework not found after compilation"
        return 1
    fi
}

# 编译 WebAssembly
build_wasm() {
    step "Building WebAssembly module"
    
    local project_root
    project_root="$(get_project_root)"
    local output_path="$project_root/${OUTPUT_PATHS["wasm"]}"
    local output_dir
    output_dir="$(dirname "$output_path")"
    local wasm_exec_js="$output_dir/wasm_exec.js"
    
    # 确保输出目录存在
    ensure_dir "$output_dir"
    
    # 设置 WASM 编译环境
    export GOOS=js
    export GOARCH=wasm
    
    # 构建编译命令
    local cmd="go build"
    
    if [[ "$OPTIMIZE" == "true" ]]; then
        cmd="$cmd -ldflags='-s -w'"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        cmd="$cmd -v"
    fi
    
    cmd="$cmd -o $output_path"
    cmd="$cmd $CORE_MODULE/exports/wasm"
    
    # 执行编译
    info "Compiling WebAssembly module..."
    debug "Command: $cmd"
    debug "GOOS=$GOOS GOARCH=$GOARCH"
    
    if ! run_in_dir "$project_root" "$cmd"; then
        error "WebAssembly compilation failed"
        return 1
    fi
    
    # 复制 wasm_exec.js
    info "Copying wasm_exec.js..."
    local go_root
    go_root="$(go env GOROOT)"
    local wasm_exec_source="$go_root/misc/wasm/wasm_exec.js"
    
    if [[ -f "$wasm_exec_source" ]]; then
        if ! run_cmd "cp '$wasm_exec_source' '$wasm_exec_js'"; then
            warn "Failed to copy wasm_exec.js, but continuing..."
        fi
    else
        warn "wasm_exec.js not found in Go installation"
    fi
    
    # 验证产物
    if [[ -f "$output_path" ]]; then
        local file_size
        file_size=$(du -h "$output_path" | cut -f1)
        success "WebAssembly module built successfully: $output_path ($file_size)"
    else
        error "WebAssembly module not found after compilation"
        return 1
    fi
}

# 编译 Windows DLL
build_windows() {
    step "Building Windows DLL"
    
    local project_root
    project_root="$(get_project_root)"
    local output_path="$project_root/${OUTPUT_PATHS["windows"]}"
    local output_dir
    output_dir="$(dirname "$output_path")"
    local header_file="${output_path%.dll}.h"
    
    # 确保输出目录存在
    ensure_dir "$output_dir"
    
    # 构建编译命令
    local cmd="go build"
    cmd="$cmd -buildmode=c-shared"
    
    if [[ "$OPTIMIZE" == "true" ]]; then
        cmd="$cmd -ldflags='-s -w'"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        cmd="$cmd -v"
    fi
    
    cmd="$cmd -o $output_path"
    cmd="$cmd $CORE_MODULE/exports/c"
    
    # 执行编译
    info "Compiling Windows DLL..."
    debug "Command: $cmd"
    
    if ! run_in_dir "$project_root" "$cmd"; then
        error "Windows DLL compilation failed"
        return 1
    fi
    
    # 验证产物
    if [[ -f "$output_path" ]]; then
        local dll_size
        dll_size=$(du -h "$output_path" | cut -f1)
        success "Windows DLL built successfully: $output_path ($dll_size)"
        
        if [[ -f "$header_file" ]]; then
            info "Header file generated: $header_file"
        fi
    else
        error "Windows DLL not found after compilation"
        return 1
    fi
}

# 为指定目标编译
build_for_targets() {
    local targets
    IFS=',' read -ra targets <<< "$TARGET"
    
    # 如果目标包含 "all"，替换为所有支持的目标（除了 "all" 本身）
    if [[ " ${targets[*]} " =~ " all " ]]; then
        targets=("android" "ios" "wasm" "windows")
    fi
    
    local success_count=0
    local total_count=${#targets[@]}
    
    info "Building for targets: ${targets[*]}"
    
    for target in "${targets[@]}"; do
        target=$(echo "$target" | xargs)  # 去除空格
        
        case "$target" in
            "android")
                if build_android; then
                    ((success_count++))
                fi
                ;;
            "ios")
                if build_ios; then
                    ((success_count++))
                fi
                ;;
            "wasm")
                if build_wasm; then
                    ((success_count++))
                fi
                ;;
            "windows")
                if build_windows; then
                    ((success_count++))
                fi
                ;;
            *)
                error "Unknown target: $target"
                ;;
        esac
    done
    
    # 报告结果
    if [[ $success_count -eq $total_count ]]; then
        success "All targets built successfully ($success_count/$total_count)"
    else
        error "Some targets failed to build ($success_count/$total_count succeeded)"
        exit 1
    fi
}

# 显示构建统计
show_build_stats() {
    step "Build Statistics"
    
    local project_root
    project_root="$(get_project_root)"
    
    info "Build artifacts:"
    
    for target in "${!OUTPUT_PATHS[@]}"; do
        local output_path="$project_root/${OUTPUT_PATHS[$target]}"
        
        if [[ -f "$output_path" ]] || [[ -d "$output_path" ]]; then
            local size
            size=$(du -sh "$output_path" | cut -f1)
            info "  $target: $output_path ($size)"
        else
            debug "  $target: not built"
        fi
    done
}

# 主函数
main() {
    # 初始化脚本
    init_script "$SCRIPT_NAME"
    
    # 解析命令行参数
    parse_args "$@"
    
    # 验证参数
    validate_targets
    
    # 检查工具和项目结构
    check_tools
    check_project_structure
    
    # 清理构建产物
    clean_build_artifacts
    
    # 执行编译
    build_for_targets
    
    # 显示统计信息
    show_build_stats
    
    # 完成
    finish_script "$SCRIPT_NAME"
}

# 只有在直接执行时才运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 