# CINA.CLUB Platform - Kong Gateway System Namespace
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Kong system namespace for platform infrastructure
apiVersion: v1
kind: Namespace
metadata:
  name: kong-system
  labels:
    name: kong-system
    component: platform-infrastructure
    tier: system
    app.kubernetes.io/name: kong-gateway
    app.kubernetes.io/component: system
    app.kubernetes.io/part-of: cina-club-platform
    app.kubernetes.io/managed-by: platform-engineering
  annotations:
    description: "Kong Gateway system namespace - managed by platform engineering team"
    contact: "<EMAIL>"
    documentation: "https://docs.cina.club/platform/api-gateway"

---
# Resource Quota for Kong system
apiVersion: v1
kind: ResourceQuota
metadata:
  name: kong-system-quota
  namespace: kong-system
  labels:
    app: resource-quota
    component: platform-infrastructure
spec:
  hard:
    # Compute resources for Kong system
    requests.cpu: "4"        # 4 CPU cores for Kong components
    requests.memory: "8Gi"   # 8GB memory for Kong components
    limits.cpu: "8"          # 8 CPU cores max
    limits.memory: "16Gi"    # 16GB memory max
    
    # Storage resources
    requests.storage: "20Gi" # 20GB storage for configs and logs
    persistentvolumeclaims: "5"
    
    # Object counts
    pods: "20"
    services: "10"
    secrets: "20"
    configmaps: "20"
    ingresses.networking.k8s.io: "5"

---
# Network Policy for Kong system isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kong-system-network-policy
  namespace: kong-system
  labels:
    app: network-policy
    component: security
spec:
  podSelector: {}  # Apply to all pods in namespace
  policyTypes:
    - Ingress
    - Egress
  
  # Ingress rules for Kong system
  ingress:
    # Allow traffic from internet to Kong proxy
    - from: []  # Allow from any source (internet traffic)
      ports:
        - protocol: TCP
          port: 8000  # HTTP proxy port
        - protocol: TCP
          port: 8443  # HTTPS proxy port
    
    # Allow traffic from monitoring namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8100  # Metrics port
    
    # Allow traffic from logging namespace  
    - from:
        - namespaceSelector:
            matchLabels:
              name: logging
      ports:
        - protocol: TCP
          port: 8080  # Logging port

  # Egress rules for Kong system
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow HTTPS to external services (for JWKS, etc.)
    - to: []
      ports:
        - protocol: TCP
          port: 443
    
    # Allow HTTP to external services
    - to: []
      ports:
        - protocol: TCP
          port: 80
    
    # Allow communication to all application namespaces
    - to:
        - namespaceSelector:
            matchLabels:
              cina-club.com/allow-kong: "true"
    
    # Allow communication to monitoring
    - to:
        - namespaceSelector:
            matchLabels:
              name: monitoring
    
    # Allow communication to logging
    - to:
        - namespaceSelector:
            matchLabels:
              name: logging 