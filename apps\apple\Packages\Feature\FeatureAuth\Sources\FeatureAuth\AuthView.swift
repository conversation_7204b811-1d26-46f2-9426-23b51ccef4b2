/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import SwiftUI
import AppCore
import DataLayer
import DesignSystem
import Combine

/// Authentication view
public struct AuthView: View {
    @StateObject private var viewModel = AuthViewModel()
    @State private var isSignUp = false
    
    public init() {}
    
    public var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.lg) {
                    // Header
                    VStack(spacing: Spacing.md) {
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 80))
                            .foregroundColor(.cinaPrimary)
                        
                        Text("Welcome to CINA.CLUB")
                            .font(.cinaLargeTitle)
                            .foregroundColor(.cinaLabel)
                        
                        Text("Your intelligent digital life companion")
                            .font(.cinaSubheadline)
                            .foregroundColor(.cinaSecondaryLabel)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, Spacing.xl)
                    
                    // Auth form
                    VStack(spacing: Spacing.md) {
                        InputField(
                            title: "Email",
                            placeholder: "Enter your email",
                            text: $viewModel.email,
                            errorMessage: viewModel.emailError
                        )
                        
                        InputField(
                            title: "Password",
                            placeholder: "Enter your password",
                            text: $viewModel.password,
                            isSecure: true,
                            errorMessage: viewModel.passwordError
                        )
                        
                        if isSignUp {
                            InputField(
                                title: "Confirm Password",
                                placeholder: "Confirm your password",
                                text: $viewModel.confirmPassword,
                                isSecure: true,
                                errorMessage: viewModel.confirmPasswordError
                            )
                            
                            InputField(
                                title: "Display Name",
                                placeholder: "Enter your display name",
                                text: $viewModel.displayName,
                                errorMessage: viewModel.displayNameError
                            )
                        }
                        
                        // Error message
                        if let errorMessage = viewModel.errorMessage {
                            Text(errorMessage)
                                .font(.cinaCallout)
                                .foregroundColor(.cinaError)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: CornerRadius.sm)
                                        .fill(Color.cinaError.opacity(0.1))
                                )
                        }
                        
                        // Primary action button
                        PrimaryButton(
                            title: isSignUp ? "Sign Up" : "Sign In",
                            isLoading: viewModel.isLoading,
                            isDisabled: !viewModel.isFormValid
                        ) {
                            Task {
                                if isSignUp {
                                    await viewModel.signUp()
                                } else {
                                    await viewModel.signIn()
                                }
                            }
                        }
                        
                        // Toggle button
                        SecondaryButton(
                            title: isSignUp ? "Already have an account? Sign In" : "Don't have an account? Sign Up"
                        ) {
                            withAnimation(.easeInOut) {
                                isSignUp.toggle()
                                viewModel.clearErrors()
                            }
                        }
                    }
                    .padding(.horizontal, Spacing.lg)
                }
            }
            .navigationTitle("Authentication")
            .navigationBarHidden(true)
        }
    }
}

/// Auth view model
@MainActor
public final class AuthViewModel: ObservableObject {
    @Published var email = ""
    @Published var password = ""
    @Published var confirmPassword = ""
    @Published var displayName = ""
    
    @Published var emailError: String?
    @Published var passwordError: String?
    @Published var confirmPasswordError: String?
    @Published var displayNameError: String?
    @Published var errorMessage: String?
    
    @Published var isLoading = false
    
    private let appCore = AppCore.shared
    private let dataLayer = DataLayer.shared
    private var cancellables = Set<AnyCancellable>()
    
    public init() {
        setupValidation()
    }
    
    private func setupValidation() {
        // Email validation
        $email
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] email in
                self?.validateEmail(email)
            }
            .store(in: &cancellables)
        
        // Password validation
        $password
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] password in
                self?.validatePassword(password)
            }
            .store(in: &cancellables)
        
        // Confirm password validation
        Publishers.CombineLatest($password, $confirmPassword)
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] password, confirmPassword in
                self?.validateConfirmPassword(password, confirmPassword)
            }
            .store(in: &cancellables)
    }
    
    private func validateEmail(_ email: String) {
        guard !email.isEmpty else {
            emailError = nil
            return
        }
        
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        
        if !emailPredicate.evaluate(with: email) {
            emailError = "Please enter a valid email address"
        } else {
            emailError = nil
        }
    }
    
    private func validatePassword(_ password: String) {
        guard !password.isEmpty else {
            passwordError = nil
            return
        }
        
        if password.count < 8 {
            passwordError = "Password must be at least 8 characters"
        } else {
            passwordError = nil
        }
    }
    
    private func validateConfirmPassword(_ password: String, _ confirmPassword: String) {
        guard !confirmPassword.isEmpty else {
            confirmPasswordError = nil
            return
        }
        
        if password != confirmPassword {
            confirmPasswordError = "Passwords do not match"
        } else {
            confirmPasswordError = nil
        }
    }
    
    public var isFormValid: Bool {
        !email.isEmpty &&
        !password.isEmpty &&
        emailError == nil &&
        passwordError == nil &&
        confirmPasswordError == nil
    }
    
    public func clearErrors() {
        emailError = nil
        passwordError = nil
        confirmPasswordError = nil
        displayNameError = nil
        errorMessage = nil
    }
    
    public func signIn() async {
        guard isFormValid else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            try await appCore.authenticate(email: email, password: password)
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    public func signUp() async {
        guard isFormValid else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // TODO: Implement sign up logic
            try await appCore.authenticate(email: email, password: password)
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
}

#Preview {
    AuthView()
} 