好的，遵照您的指示。我将为您生成一份专门针对 **`recommendation-service` (推荐编排服务)** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`recommendation-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**可配置的推荐管道(Pipeline)的实现、多路召回与多阶段排序的编排、作为业务与算法桥梁的重排层，以及作为一个高性能在线服务的技术细节**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `recommendation-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `recommendation-service-srs.md` (v1.0)
**核心架构**: 整洁架构 + 可配置的推荐管道(Pipeline) + 策略模式(for re-ranking)

## 1. 概述

`recommendation-service` 是CINA.CLUB个性化体验的“**总导演**”和“**流程引擎**”。它是一个**高性能、无状态的在线编排服务**。其架构设计的核心目标是：
1.  **流程的灵活性与可扩展性**: 必须能够轻松地通过**配置**，来定义和调整不同推荐场景（如视频Feed、相关服务推荐）的“召回->排序->重排”流程，而无需修改主代码。
2.  **高性能与低延迟**: 整个处理管道的P95延迟必须控制在200ms以内。
3.  **算法与业务的解耦**: 将纯粹的算法逻辑（召回、排序）与业务规则（过滤、打散、强插）清晰地分离开来。
4.  **可测试性**: 推荐管道的每个阶段都应该是可独立测试的。
5.  **可靠性与优雅降级**: 在下游服务（召回、排序）发生故障时，能优雅地降级并返回部分可用的结果。

本架构设计通过采用**整洁架构**，并在应用层实现一个**可配置的、基于阶段(Stage)的推荐管道**，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (推荐管道执行流程)

```mermaid
graph TD
    subgraph "API Layer"
        A[gRPC/REST Handler<br/><em>adapter/transport</em>]
    end

    subgraph "Application Layer"
        style "Application Layer" fill:#e0f7fa
        B[RecommendationPipeline<br/><em>application/pipeline</em>]
        C[RecallStage]
        D[FilterStage]
        E[RankStage]
        F[ReRankStage]
    end

    subgraph "Domain Layer"
        style "Domain Layer" fill:#f3e5f5
        G[ReRank Strategies<br/>(Diversity, Freshness, ...)<br/><em>domain/rerank</em>]
    end

    subgraph "Adapter Layer (Clients)"
        style "Adapter Layer (Clients)" fill:#e8f5e9
        H[RecallServiceClient]
        I[RankingServiceClient]
        J[FeatureStoreClient]
        K[SeenFilterCache (Redis)]
    end

    A -- "1. /feed/{scene}" --> B
    B -- "2. Executes Pipeline Stages Sequentially" --> C
    C -- "3. Parallel Recall" --> H
    H -- "Returns Candidate IDs" --> C
    C -- "Aggregates & Deduplicates" --> B
    
    B --> D
    D -- "4. Filter Seen Items" --> K
    D -- "Returns filtered IDs" --> B
    
    B --> E
    E -- "5. Fetch Features" --> J
    E -- "6. Call Ranking Service" --> I
    I -- "Returns Ranked Items" --> E
    E -- "Returns ranked list" --> B

    B --> F
    F -- "7. Apply Re-ranking Strategies" --> G
    G -- "Re-arranges the list" --> F
    F -- "Returns final list" --> B
    
    B -- "8. Return to Handler" --> A
```

### 2.2 最终目录结构 (`services/recommendation-service/`)

```
recommendation-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_seen_filter.go # 已曝光/已读过滤的缓存实现
│   │   ├── client/                  # 所有下游服务的gRPC客户端
│   │   │   ├── recall_client.go
│   │   │   └── ranking_client.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/              # (如果需要) 访问特征存储的仓储
│   ├── application/
│   │   ├── port/
│   │   │   └── ... (接口定义)
│   │   └── pipeline/                # ✨ 推荐管道核心实现 ✨
│   │       ├── interface.go         # 定义Pipeline和Stage接口
│   │       ├── factory.go           # 根据场景配置创建Pipeline实例
│   │       ├── pipeline.go          # Pipeline的执行逻辑
│   │       └── stages/              # ✨ 各个管道阶段的具体实现 ✨
│   │           ├── recall_stage.go
│   │           ├── filter_stage.go
│   │           ├── rank_stage.go
│   │           └── rerank_stage.go
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── rerank/                  # ✨ 重排策略的领域逻辑 ✨
│           ├── interface.go
│           ├── diversity_strategy.go
│           └── freshness_strategy.go
├── config/
│   └── pipelines.yaml              # ✨ 推荐管道配置文件 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/pipelines.yaml` - 推荐流程的“可执行蓝图”

这是实现流程灵活性的核心。
```yaml
pipelines:
  - scene: "video_feed_main" # 主视频Feed流
    description: "为用户提供个性化的、多样化的视频流"
    stages:
      - name: "recall"
        type: "recall_stage"
        timeout_ms: 50
        params:
          strategies: ["hot", "item_cf", "vector_semantic", "follow"]
          target_count: 500
      - name: "filter"
        type: "filter_stage"
        params:
          filters: ["seen", "disliked"]
      - name: "rank"
        type: "rank_stage"
        timeout_ms: 100
        params:
          model_name: "video_feed_deepfm_v1"
        fallback: # 如果此阶段失败
          type: "recall_result_passthrough" # 直接使用召回结果
      - name: "rerank"
        type: "rerank_stage"
        params:
          strategies:
            - name: "diversity"
              params: { window_size: 5, field: "category_id" }
            - name: "interleave_new"
              params: { ratio: 0.1 }
            - name: "sticky_top" # 强插运营内容
              params: { item_ids: ["op_video_1", "op_video_2"] }

  - scene: "related_videos" # 视频详情页的相关推荐
    stages:
      - name: "recall"
        type: "recall_stage"
        params:
          strategies: ["item_cf_by_seed", "vector_by_seed"]
          target_count: 100
      - name: "rank"
        # ...
```

### 3.2 `domain/rerank/` - 重排策略层 (业务与算法的桥梁)

*   **`interface.go`**: 定义`ReRankStrategy`接口。
    ```go
    type ReRankContext struct { /* UserInfo, RequestContext, ... */ }
    type ReRankStrategy interface {
        // ReRank接收一个已排序的列表，并返回一个重排后的列表
        ReRank(ctx context.Context, rankedItems []*Item, context *ReRankContext) []*Item
    }
    ```
*   **具体策略实现 (`diversity_strategy.go`, ...)**:
    *   `DiversityStrategy`: 实现`ReRank`方法。在一个滑动窗口内，如果发现连续出现相同`field`（如`category_id`）的item，则将其往后移动。
    *   `StickyTopStrategy`: 实现`ReRank`方法。将指定的运营内容强行插入到列表的顶部或指定位置。

### 3.3 `application/pipeline/` - 推荐管道引擎

*   **`interface.go`**:
    *   `Pipeline`接口: `Execute(ctx, requestContext) (*Result, error)`。
    *   `Stage`接口: `Execute(ctx, pipelineContext) error`。`pipelineContext`是一个在所有Stage之间传递的对象，包含了用户画像、候选集等所有中间数据。
*   **`factory.go`**:
    *   **`PipelineFactory`**: 在启动时加载并解析`pipelines.yaml`。
    *   **`GetPipeline(scene string)`**: 根据场景ID，动态地创建并组装一个`Pipeline`实例，该实例包含一个`Stage`的切片。
*   **`pipeline.go`**:
    *   **`Pipeline.Execute()`**: 这是主执行逻辑。它按顺序遍历其包含的所有`Stage`，并调用每个`Stage`的`Execute`方法。它负责管理超时和错误处理。
*   **`stages/`**: **这是每个阶段的具体实现**。
    *   **`recall_stage.go`**:
        *   `Execute`方法会解析`params.strategies`，然后**并行地**向`recall_client`发起多个召回请求。
        *   使用`errgroup`来管理并发，并设置超时。
        *   聚合、去重所有召回结果，并将其放入`pipelineContext`中，传递给下一阶段。
    *   **`rank_stage.go`**:
        *   `Execute`方法从`pipelineContext`中获取候选集和用户/物料特征。
        *   调用`ranking_client.Rank(...)`。
        *   将排序后的结果写回`pipelineContext`。
        *   **实现优雅降级**: 如果`ranking_client`调用失败或超时，它会检查`fallback`配置，并执行相应的降级逻辑（如直接使用召回结果）。
    *   **`rerank_stage.go`**:
        *   `Execute`方法会根据`params.strategies`，按顺序应用`domain/rerank`中的多个重排策略。

### 3.4 `adapter/` - 适配层 (I/O & Infrastructure)

*   **`adapter/client/`**: 封装所有对下游服务（`recall`, `ranking`, `feature_store`）的gRPC调用。**必须**内置`pkg/middleware`提供的追踪和指标，并配置合理的超时和重试。
*   **`adapter/cache/redis_seen_filter.go`**:
    *   实现`SeenFilter`接口。
    *   **使用Redis Bloom Filter或Cuckoo Filter**来高效地存储和查询用户已曝光过的物料ID。
    *   **理由**: 相比Set，布隆/布谷过滤器在处理海量数据时，空间效率极高，虽然有极小的误判率（可能将未见过的误判为见过），但这在推荐场景中是完全可以接受的。
*   **`adapter/grpc/handler.go`**:
    *   实现gRPC接口。
    *   主要职责是解析请求，调用`pipeline.Factory.GetPipeline(scene)`获取管道实例，然后执行`pipeline.Execute()`。
    *   **异步记录曝光**: 在成功返回结果后，**启动一个goroutine**来将曝光日志发送到Kafka。这个操作绝不能阻塞主请求的返回。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`recommendation-service`：
1.  **可配置的管道架构**: 将复杂的推荐流程抽象为一系列可配置、可插拔的**阶段(Stage)**。这使得运营和算法工程师可以通过修改YAML文件来快速地试验和调整推荐策略（如增减召回通路、更换排序模型、调整重排逻辑），实现了极高的灵活性和迭代速度。
2.  **职责分离的微服务协同**:
    *   **本服务 (Orchestrator)**: 负责“流程是什么”。
    *   **`recall-service`**: 负责“从哪里找”。
    *   **`ranking-service`**: 负责“哪个更好”。
    *   这种分离使得每个组件都可以独立优化和扩展。
3.  **算法与业务的桥梁**: **重排(Re-ranking)阶段**被明确设计为算法模型输出和最终业务目标之间的“翻译层”和“调和层”，负责实现多样性、新颖性、公平性等算法本身难以优化的业务指标。
4.  **性能与可靠性优先**:
    *   通过并行召回、高效缓存（布隆过滤器）等手段优化延迟。
    *   通过为每个关键阶段设计明确的**优雅降级(Fallback)**策略，确保了在下游服务部分故障时，系统依然能够为用户提供可用的推荐服务。

这种架构确保了`recommendation-service`能够作为一个**强大、灵活、可靠且易于迭代**的推荐中枢，为CINA.CLUB平台的所有个性化场景提供世界一流的推荐体验。