/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 15:45:00
 * Modified: 2025-01-23 15:45:00
 */

import { apiClient } from '@/lib/api-client';
import type { 
  Content, 
  ContentStatus, 
  ContentType,
  ModerationAction,
  FlagReason,
  ContentCategory,
  ModerationQueueItem,
  ContentMetrics,
  ContentAnalytics,
  ContentListParams,
  ContentListResponse,
  BulkContentOperation,
  ContentActionResult
} from '@/types/content';

/**
 * Content Management API Service
 * Provides comprehensive content moderation and management functionality
 */
export class ContentApiService {
  private static readonly BASE_PATH = '/api/v1/content';

  /**
   * Get content list with filtering and pagination
   */
  static async getContent(params?: ContentListParams): Promise<ContentListResponse> {
    const response = await apiClient.get(this.BASE_PATH, { params });
    return response.data;
  }

  /**
   * Get content by ID with detailed information
   */
  static async getContentById(id: string): Promise<Content> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}`);
    return response.data;
  }

  /**
   * Get moderation queue with pending content
   */
  static async getModerationQueue(params?: {
    priority?: 'low' | 'medium' | 'high' | 'critical';
    type?: ContentType;
    flagReason?: FlagReason;
    limit?: number;
    offset?: number;
  }): Promise<{
    items: ModerationQueueItem[];
    total: number;
    estimatedReviewTime: number;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/moderation/queue`, { params });
    return response.data;
  }

  /**
   * Moderate content (approve, reject, flag, etc.)
   */
  static async moderateContent(
    id: string, 
    action: ModerationAction, 
    reason?: string,
    metadata?: Record<string, any>
  ): Promise<ContentActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/moderate`, {
      action,
      reason,
      metadata
    });
    return response.data;
  }

  /**
   * Bulk moderation actions
   */
  static async bulkModerate(operation: BulkContentOperation): Promise<{
    success: boolean;
    processed: number;
    failed: number;
    results: Array<{
      contentId: string;
      success: boolean;
      error?: string;
    }>;
  }> {
    const response = await apiClient.post(`${this.BASE_PATH}/moderation/bulk`, operation);
    return response.data;
  }

  /**
   * Flag content for review
   */
  static async flagContent(
    id: string, 
    reason: FlagReason, 
    description?: string
  ): Promise<ContentActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/flag`, {
      reason,
      description
    });
    return response.data;
  }

  /**
   * Update content status
   */
  static async updateContentStatus(id: string, status: ContentStatus): Promise<Content> {
    const response = await apiClient.patch(`${this.BASE_PATH}/${id}/status`, { status });
    return response.data;
  }

  /**
   * Get content analytics
   */
  static async getContentAnalytics(params?: {
    timeRange?: '7d' | '30d' | '90d' | '1y';
    contentType?: ContentType;
    category?: string;
  }): Promise<ContentAnalytics> {
    const response = await apiClient.get(`${this.BASE_PATH}/analytics`, { params });
    return response.data;
  }

  /**
   * Get content metrics for specific content
   */
  static async getContentMetrics(id: string): Promise<ContentMetrics> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/metrics`);
    return response.data;
  }

  /**
   * Get content categories
   */
  static async getCategories(): Promise<ContentCategory[]> {
    const response = await apiClient.get(`${this.BASE_PATH}/categories`);
    return response.data;
  }

  /**
   * Create content category
   */
  static async createCategory(category: Omit<ContentCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<ContentCategory> {
    const response = await apiClient.post(`${this.BASE_PATH}/categories`, category);
    return response.data;
  }

  /**
   * Update content category
   */
  static async updateCategory(id: string, category: Partial<ContentCategory>): Promise<ContentCategory> {
    const response = await apiClient.put(`${this.BASE_PATH}/categories/${id}`, category);
    return response.data;
  }

  /**
   * Delete content category
   */
  static async deleteCategory(id: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete(`${this.BASE_PATH}/categories/${id}`);
    return response.data;
  }

  /**
   * Search content by query
   */
  static async searchContent(
    query: string, 
    filters?: {
      type?: ContentType;
      status?: ContentStatus;
      category?: string;
      dateFrom?: string;
      dateTo?: string;
    }
  ): Promise<Content[]> {
    const response = await apiClient.get(`${this.BASE_PATH}/search`, {
      params: { q: query, ...filters }
    });
    return response.data;
  }

  /**
   * Get trending content
   */
  static async getTrendingContent(
    timeRange: '24h' | '7d' | '30d' = '24h',
    type?: ContentType
  ): Promise<Array<Content & { trendScore: number; growthRate: number }>> {
    const response = await apiClient.get(`${this.BASE_PATH}/trending`, {
      params: { timeRange, type }
    });
    return response.data;
  }

  /**
   * Get content moderation history
   */
  static async getModerationHistory(id: string): Promise<Array<{
    id: string;
    action: ModerationAction;
    moderator: string;
    reason?: string;
    timestamp: string;
    metadata?: Record<string, any>;
  }>> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/moderation/history`);
    return response.data;
  }

  /**
   * Get content reports/flags
   */
  static async getContentReports(id: string): Promise<Array<{
    id: string;
    reason: FlagReason;
    description?: string;
    reportedBy: string;
    reportedAt: string;
    status: 'pending' | 'reviewed' | 'dismissed';
  }>> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/reports`);
    return response.data;
  }

  /**
   * Export content data
   */
  static async exportContent(
    filters?: ContentListParams,
    format: 'csv' | 'excel' | 'json' = 'csv'
  ): Promise<Blob> {
    const response = await apiClient.post(`${this.BASE_PATH}/export`, {
      filters,
      format
    }, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Get content engagement metrics
   */
  static async getEngagementMetrics(
    timeRange: '7d' | '30d' | '90d' = '30d'
  ): Promise<{
    totalViews: number;
    totalLikes: number;
    totalShares: number;
    totalComments: number;
    engagementRate: number;
    topContent: Array<{
      id: string;
      title: string;
      views: number;
      engagementScore: number;
    }>;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/metrics/engagement`, {
      params: { timeRange }
    });
    return response.data;
  }

  /**
   * Get content performance by category
   */
  static async getCategoryPerformance(): Promise<Array<{
    category: string;
    contentCount: number;
    averageViews: number;
    engagementRate: number;
    trending: boolean;
  }>> {
    const response = await apiClient.get(`${this.BASE_PATH}/metrics/categories`);
    return response.data;
  }

  /**
   * Schedule content publication
   */
  static async scheduleContent(
    id: string, 
    publishAt: string
  ): Promise<ContentActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/schedule`, { publishAt });
    return response.data;
  }

  /**
   * Cancel scheduled content publication
   */
  static async cancelScheduledContent(id: string): Promise<ContentActionResult> {
    const response = await apiClient.delete(`${this.BASE_PATH}/${id}/schedule`);
    return response.data;
  }

  /**
   * Get AI moderation suggestions
   */
  static async getAIModerationSuggestions(id: string): Promise<{
    riskScore: number;
    suggestedAction: ModerationAction;
    reasoning: string;
    confidence: number;
    flags: Array<{
      type: string;
      severity: 'low' | 'medium' | 'high';
      description: string;
    }>;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/ai-moderation`);
    return response.data;
  }
}

export default ContentApiService; 