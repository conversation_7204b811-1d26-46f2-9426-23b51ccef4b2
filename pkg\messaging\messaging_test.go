/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import (
	"context"
	"testing"

	"github.com/segmentio/kafka-go"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// TestMessage 测试消息
type TestMessage struct {
	Content string `json:"content"`
}

// 实现 proto.Message 接口
func (m *TestMessage) Reset()                             { *m = TestMessage{} }
func (m *TestMessage) String() string                     { return m.Content }
func (m *TestMessage) ProtoMessage()                      {}
func (m *TestMessage) ProtoReflect() protoreflect.Message { return nil }

func TestProtobufSerializer(t *testing.T) {
	serializer := NewProtobufSerializer()

	// 由于我们的测试消息没有真正的 protobuf 反射支持，
	// 我们只测试序列化器的创建和接口
	if serializer == nil {
		t.Fatal("Expected non-nil serializer")
	}

	// 测试 nil 消息处理
	_, err := serializer.Marshal(nil)
	if err == nil {
		t.Error("Expected error for nil message")
	}

	// 测试空数据反序列化
	msg := &EmptyMessage{}
	err = serializer.Unmarshal(nil, msg)
	if err == nil {
		t.Error("Expected error for nil data")
	}

	// 测试 nil 消息反序列化
	err = serializer.Unmarshal([]byte("test"), nil)
	if err == nil {
		t.Error("Expected error for nil message")
	}

	t.Log("Serializer interface test passed")
}

func TestMockProducer(t *testing.T) {
	producer := NewMockProducer()
	ctx := context.Background()
	msg := &EmptyMessage{}

	// 测试发布消息
	err := producer.Publish(ctx, "test-topic", "test-key", msg)
	if err != nil {
		t.Fatalf("Publish failed: %v", err)
	}

	// 验证消息被保存
	messages := producer.GetMessages()
	if len(messages) != 1 {
		t.Fatalf("Expected 1 message, got %d", len(messages))
	}

	if messages[0].Topic != "test-topic" {
		t.Errorf("Expected topic 'test-topic', got '%s'", messages[0].Topic)
	}

	if messages[0].Key != "test-key" {
		t.Errorf("Expected key 'test-key', got '%s'", messages[0].Key)
	}
}

func TestMockConsumerHandler(t *testing.T) {
	handler := NewMockConsumerHandler()
	ctx := context.Background()
	msg := &EmptyMessage{}

	// 测试处理消息
	err := handler.Handle(ctx, msg)
	if err != nil {
		t.Fatalf("Handle failed: %v", err)
	}

	// 验证消息被处理
	messages := handler.GetMessages()
	if len(messages) != 1 {
		t.Fatalf("Expected 1 message, got %d", len(messages))
	}

	emptyMsg, ok := messages[0].(*EmptyMessage)
	if !ok {
		t.Fatalf("Expected EmptyMessage, got %T", messages[0])
	}

	// EmptyMessage 没有字段，只验证类型正确即可
	if emptyMsg == nil {
		t.Error("Expected non-nil EmptyMessage")
	}
}

func TestRetryLogic(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{"nil error", nil, false},
		{"retryable error", WrapRetryableError(ErrPublishTimeout), true},
		{"non-retryable error", WrapNonRetryableError(ErrUnknownEventType), false},
		{"unknown event type", ErrUnknownEventType, false},
		{"deserialization fail", ErrDeserializationFail, false},
		{"generic error", ErrProducerClosed, true}, // 默认可重试
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsRetryableError(tt.err)
			if result != tt.expected {
				t.Errorf("IsRetryableError(%v) = %v, expected %v", tt.err, result, tt.expected)
			}
		})
	}
}

func TestEventTypeExtraction(t *testing.T) {
	// 测试 nil 消息
	eventType := GetEventType(nil)
	if eventType != "" {
		t.Errorf("Expected empty string for nil message, got %s", eventType)
	}

	// 由于我们的测试消息没有实际的 protobuf 反射，跳过实际的反射测试
	// 在实际应用中，应该使用从 .proto 文件生成的消息
	t.Log("EventType extraction test passed (using nil message)")
}

func TestHeaderOperations(t *testing.T) {
	headers := []kafka.Header{
		{Key: "key1", Value: []byte("value1")},
		{Key: "key2", Value: []byte("value2")},
	}

	// 测试 HeadersToMap
	headerMap := HeadersToMap(headers)
	if len(headerMap) != 2 {
		t.Fatalf("Expected 2 headers, got %d", len(headerMap))
	}

	if headerMap["key1"] != "value1" {
		t.Errorf("Expected 'value1', got '%s'", headerMap["key1"])
	}

	// 测试 MapToHeaders
	newHeaders := MapToHeaders(headerMap)
	if len(newHeaders) != 2 {
		t.Fatalf("Expected 2 headers, got %d", len(newHeaders))
	}
}

func TestConfigValidation(t *testing.T) {
	// 测试生产者配置验证
	producerConfig := KafkaProducerConfig{}
	err := producerConfig.Validate()
	if err == nil {
		t.Error("Expected validation error for empty brokers")
	}

	producerConfig.Brokers = []string{"localhost:9092"}
	err = producerConfig.Validate()
	if err != nil {
		t.Errorf("Unexpected validation error: %v", err)
	}

	// 测试消费者配置验证
	consumerConfig := KafkaConsumerConfig{}
	err = consumerConfig.Validate()
	if err == nil {
		t.Error("Expected validation error for empty config")
	}

	consumerConfig.Brokers = []string{"localhost:9092"}
	consumerConfig.GroupID = "test-group"
	consumerConfig.Topics = []string{"test-topic"}
	err = consumerConfig.Validate()
	if err != nil {
		t.Errorf("Unexpected validation error: %v", err)
	}
}

func TestDLQConfig(t *testing.T) {
	dlqConfig := DLQConfig{
		Enabled:     true,
		TopicSuffix: "dlq",
	}

	dlqTopic := dlqConfig.GetDLQTopic("original-topic")
	expected := "original-topic.dlq"
	if dlqTopic != expected {
		t.Errorf("Expected '%s', got '%s'", expected, dlqTopic)
	}

	// 测试禁用 DLQ
	dlqConfig.Enabled = false
	dlqTopic = dlqConfig.GetDLQTopic("original-topic")
	if dlqTopic != "" {
		t.Errorf("Expected empty string when DLQ disabled, got '%s'", dlqTopic)
	}
}

func TestEmptyMessage(t *testing.T) {
	msg := &EmptyMessage{}

	// 测试基本方法
	msg.Reset()
	str := msg.String()
	if str != "EmptyMessage{}" {
		t.Errorf("Expected 'EmptyMessage{}', got '%s'", str)
	}

	// 测试 proto.Message 接口
	msg.ProtoMessage()

	// 测试 ProtoReflect
	reflection := msg.ProtoReflect()
	if reflection != nil {
		t.Error("Expected nil from ProtoReflect")
	}
}

// BenchmarkSerialization 序列化性能测试（简化版）
func BenchmarkSerialization(b *testing.B) {
	// 基准测试简化为只测试序列化器创建
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewProtobufSerializer()
	}
}

// BenchmarkMockProducer Mock生产者性能测试
func BenchmarkMockProducer(b *testing.B) {
	producer := NewMockProducer()
	ctx := context.Background()
	msg := &EmptyMessage{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := producer.Publish(ctx, "test-topic", "test-key", msg)
		if err != nil {
			b.Fatal(err)
		}
	}
}
