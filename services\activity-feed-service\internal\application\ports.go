/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package application

import (
	"context"
	"time"

	"cina.club/services/activity-feed-service/internal/domain"
)

// FeedRepository defines the interface for feed item persistence
type FeedRepository interface {
	// Create a new feed item
	CreateFeedItem(ctx context.Context, item *domain.ActivityFeedItem) error

	// Update an existing feed item (for aggregation)
	UpdateFeedItem(ctx context.Context, item *domain.ActivityFeedItem) error

	// Get a specific feed item by ID
	GetFeedItem(ctx context.Context, itemID string) (*domain.ActivityFeedItem, error)

	// Get feed items for a user with pagination
	GetUserFeedItems(ctx context.Context, userID string, feedType domain.FeedType, limit, offset int) ([]*domain.ActivityFeedItem, error)

	// Get unread feed items for a user
	GetUnreadFeedItems(ctx context.Context, userID string, feedType domain.FeedType, limit int) ([]*domain.ActivityFeedItem, error)

	// Mark items as read
	MarkItemsAsRead(ctx context.Context, userID string, itemIDs []string) error

	// Mark all items of a feed type as read
	MarkAllFeedAsRead(ctx context.Context, userID string, feedType domain.FeedType) error

	// Get total count of feed items for a user
	GetUserFeedCount(ctx context.Context, userID string, feedType domain.FeedType) (int64, error)

	// Get user activity statistics
	GetUserActivityStats(ctx context.Context, userID string, since time.Time) (*domain.UserActivityStats, error)

	// Delete old feed items (cleanup)
	DeleteOldFeedItems(ctx context.Context, olderThan time.Time) (int64, error)

	// Batch operations for performance
	BatchCreateFeedItems(ctx context.Context, items []*domain.ActivityFeedItem) error
	BatchUpdateFeedItems(ctx context.Context, items []*domain.ActivityFeedItem) error
}

// UnreadCountRepository defines the interface for managing unread counts
type UnreadCountRepository interface {
	// Get unread count for a specific feed type
	GetUnreadCount(ctx context.Context, userID string, feedType domain.FeedType) (int, error)

	// Get all unread counts for a user
	GetAllUnreadCounts(ctx context.Context, userID string) (map[domain.FeedType]int, error)

	// Increment unread count atomically
	IncrementUnreadCount(ctx context.Context, userID string, feedType domain.FeedType) error

	// Decrement unread count atomically
	DecrementUnreadCount(ctx context.Context, userID string, feedType domain.FeedType, count int) error

	// Set unread count to a specific value
	SetUnreadCount(ctx context.Context, userID string, feedType domain.FeedType, count int) error

	// Reset all unread counts for a user
	ResetAllUnreadCounts(ctx context.Context, userID string) error

	// Reset unread count for a specific feed type
	ResetUnreadCount(ctx context.Context, userID string, feedType domain.FeedType) error
}

// EventPublisher defines the interface for publishing events
type EventPublisher interface {
	// Publish feed unread count changed event
	PublishFeedUnreadCountChanged(ctx context.Context, event *FeedUnreadCountChangedEvent) error

	// Publish feed item created event
	PublishFeedItemCreated(ctx context.Context, event *FeedItemCreatedEvent) error

	// Publish feed item updated event
	PublishFeedItemUpdated(ctx context.Context, event *FeedItemUpdatedEvent) error

	// Publish feed items marked as read event
	PublishFeedItemsMarkedRead(ctx context.Context, event *FeedItemsMarkedReadEvent) error
}

// Event types for publishing

// FeedUnreadCountChangedEvent represents an unread count change event
type FeedUnreadCountChangedEvent struct {
	UserID      string          `json:"user_id"`
	FeedType    domain.FeedType `json:"feed_type"`
	UnreadCount int             `json:"unread_count"`
	TotalUnread int             `json:"total_unread"`
	Timestamp   time.Time       `json:"timestamp"`
}

// FeedItemCreatedEvent represents a feed item creation event
type FeedItemCreatedEvent struct {
	UserID       string                   `json:"user_id"`
	FeedType     domain.FeedType          `json:"feed_type"`
	ActivityType domain.ActivityType      `json:"activity_type"`
	Item         *domain.ActivityFeedItem `json:"item"`
	Timestamp    time.Time                `json:"timestamp"`
}

// FeedItemUpdatedEvent represents a feed item update event
type FeedItemUpdatedEvent struct {
	UserID       string                   `json:"user_id"`
	FeedType     domain.FeedType          `json:"feed_type"`
	ActivityType domain.ActivityType      `json:"activity_type"`
	Item         *domain.ActivityFeedItem `json:"item"`
	UpdateType   string                   `json:"update_type"` // "aggregated", "marked_read", etc.
	Timestamp    time.Time                `json:"timestamp"`
}

// FeedItemsMarkedReadEvent represents items being marked as read
type FeedItemsMarkedReadEvent struct {
	UserID      string          `json:"user_id"`
	FeedType    domain.FeedType `json:"feed_type"`
	ItemIDs     []string        `json:"item_ids"`
	MarkAllRead bool            `json:"mark_all_read"`
	Timestamp   time.Time       `json:"timestamp"`
}

// SystemAnnouncementRequest represents a system announcement creation request
type SystemAnnouncementRequest struct {
	Title         string     `json:"title"`
	Message       string     `json:"message"`
	TargetUserIDs []string   `json:"target_user_ids,omitempty"` // If empty, send to all users
	DeepLinkURL   string     `json:"deep_link_url,omitempty"`
	IconURL       string     `json:"icon_url,omitempty"`
	ImageURL      string     `json:"image_url,omitempty"`
	Priority      string     `json:"priority,omitempty"` // "low", "normal", "high"
	ExpiresAt     *time.Time `json:"expires_at,omitempty"`
}

// FeedItemsResponse represents the response for feed items query
type FeedItemsResponse struct {
	Items      []*domain.ActivityFeedItem `json:"items"`
	TotalCount int64                      `json:"total_count"`
	HasMore    bool                       `json:"has_more"`
	NextOffset int                        `json:"next_offset,omitempty"`
}

// CleanupResult represents the result of cleanup operations
type CleanupResult struct {
	DeletedCount  int64     `json:"deleted_count"`
	ProcessedTime time.Time `json:"processed_time"`
	Errors        []string  `json:"errors,omitempty"`
}
