package aic

import (
	"context"
	"time"
)

// AIServiceProvider defines the core interface for AI service management
type AIServiceProvider interface {
	// LoadModel loads an AI model with specific configuration
	LoadModel(ctx context.Context, config ModelConfig) (AIModel, error)

	// UnloadModel removes a model from memory
	UnloadModel(ctx context.Context, modelID string) error

	// ListLoadedModels returns all currently loaded models
	ListLoadedModels(ctx context.Context) ([]ModelInfo, error)

	// CreateInferenceSession initializes a new inference session
	CreateInferenceSession(ctx context.Context, modelID string, options *SessionOptions) (InferenceSession, error)
}

// AIModel represents a loaded AI model with core capabilities
type AIModel interface {
	// GetModelInfo returns detailed information about the model
	GetModelInfo() ModelInfo

	// Predict generates a prediction based on the input prompt
	Predict(ctx context.Context, prompt string, config *InferenceConfig) (PredictionResult, error)

	// EvaluateFairness performs a comprehensive fairness assessment
	EvaluateFairness(ctx context.Context) (FairnessReport, error)

	// GetCapabilities returns the supported capabilities of the model
	GetCapabilities() ModelCapabilities

	// Initialize prepares the model for use, performing any necessary setup
	Initialize() error
}

// InferenceSession manages an active AI inference session
type InferenceSession interface {
	// GenerateTokens produces tokens for a given prompt
	GenerateTokens(ctx context.Context, prompt string) ([]Token, error)

	// StreamTokens allows streaming token generation with a callback
	StreamTokens(ctx context.Context, prompt string, callback TokenCallback) error

	// GetSessionMetrics provides performance and usage metrics
	GetSessionMetrics() SessionMetrics

	// Close terminates the inference session
	Close() error
}

// ModelCapabilities describes the supported features of an AI model
type ModelCapabilities struct {
	// Supported model types (text, vision, audio, etc.)
	SupportedTypes []ModelType

	// Maximum context length
	MaxContextLength int

	// Supported inference modes
	InferenceModes []InferenceMode

	// Special capabilities
	SpecialCapabilities map[string]bool
}

// InferenceMode represents different inference strategies
type InferenceMode string

const (
	InferenceModeStandard   InferenceMode = "STANDARD"
	InferenceModeFewShot    InferenceMode = "FEW_SHOT"
	InferenceModeZeroShot   InferenceMode = "ZERO_SHOT"
	InferenceModeContinuous InferenceMode = "CONTINUOUS"
)

// SessionOptions provides configuration for creating an inference session
type SessionOptions struct {
	// Timeout for the entire session
	Timeout time.Duration

	// Maximum number of tokens to generate
	MaxTokens int

	// Inference mode to use
	Mode InferenceMode

	// Additional custom options
	CustomOptions map[string]interface{}
}

// PredictionResult encapsulates the output of an AI prediction
type PredictionResult struct {
	// Generated text
	Text string

	// Individual tokens
	Tokens []Token

	// Metadata about the prediction
	Metadata PredictionMetadata
}

// PredictionMetadata provides additional information about a prediction
type PredictionMetadata struct {
	// Unique identifier for the prediction
	ID string

	// Timestamp of prediction
	Timestamp time.Time

	// Total tokens used
	TotalTokens int

	// Inference duration
	Duration time.Duration

	// Confidence scores
	Confidence map[string]float64
}

// FairnessReport provides a comprehensive assessment of model fairness
type FairnessReport struct {
	// Overall fairness score
	OverallScore float64

	// Detailed fairness metrics by demographic group
	DemographicMetrics map[string]FairnessMetrics

	// Identified biases and recommendations
	BiasFindings []BiasFinding
}

// FairnessMetrics quantifies different aspects of model fairness
type FairnessMetrics struct {
	// Statistical parity
	DemographicParity float64

	// True positive rate equality
	EqualOpportunity float64

	// Positive predictive value equality
	PredictiveParity float64
}

// BiasFinding describes a specific bias discovered in the model
type BiasFinding struct {
	// Type of bias
	Type string

	// Severity of the bias
	Severity Biasseverity

	// Detailed description
	Description string

	// Recommended mitigation strategies
	Recommendations []string
}

// Biasseverity represents the impact of a discovered bias
type Biasseverity string

const (
	BiasSeverityLow      Biasseverity = "LOW"
	BiasSeverityMedium   Biasseverity = "MEDIUM"
	BiasSeverityHigh     Biasseverity = "HIGH"
	BiasSeverityCritical Biasseverity = "CRITICAL"
)

// TokenCallback defines the interface for streaming token generation
type TokenCallback interface {
	// OnToken is called for each generated token
	OnToken(token *Token) error

	// OnComplete is called when token generation is finished
	OnComplete(result *PredictionResult) error

	// OnError handles any errors during token generation
	OnError(err error)
}

// SessionMetrics provides performance insights for an inference session
type SessionMetrics struct {
	// Total number of tokens generated
	TotalTokensGenerated int

	// Average token generation time
	AverageTokenLatency time.Duration

	// Peak memory usage
	PeakMemoryUsage int64

	// Number of inference calls
	InferenceCount int

	// Timestamp of session start
	StartTime time.Time

	// Session duration
	Duration time.Duration
}
