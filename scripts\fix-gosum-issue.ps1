# CINA.CLUB Go.sum 修复脚本

Write-Host "Fixing go.sum issues..." -ForegroundColor Green

$services = Get-ChildItem -Path "services" -Directory

foreach ($service in $services) {
    $serviceName = $service.Name
    Write-Host "Fixing: $serviceName" -ForegroundColor Yellow
    
    $currentLocation = Get-Location
    Set-Location $service.FullName
    
    try {
        # Remove existing go.sum to force regeneration
        if (Test-Path "go.sum") {
            Remove-Item "go.sum" -Force
            Write-Host "  Removed old go.sum" -ForegroundColor Gray
        }
        
        # Clean module cache
        go clean -modcache
        
        # Run go mod download to rebuild dependencies
        Write-Host "  Running go mod download..." -ForegroundColor Cyan
        go mod download 2>$null
        
        # Run go mod tidy to regenerate go.sum
        Write-Host "  Running go mod tidy..." -ForegroundColor Cyan
        go mod tidy 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  SUCCESS: $serviceName" -ForegroundColor Green
        } else {
            Write-Host "  WARNING: $serviceName had issues" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "  ERROR: $serviceName - $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        Set-Location $currentLocation
    }
}

Write-Host ""
Write-Host "Go.sum fix completed!" -ForegroundColor Green
Write-Host "Run batch test to verify results" -ForegroundColor Yellow 