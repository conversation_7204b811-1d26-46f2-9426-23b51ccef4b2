好的，遵照您的指示。我将为您生成`L3_Core_Capabilities_Deep_Dive/`目录下的全部六份核心文档的、详细的、生产级的内容。

这组文档旨在对平台级的、跨多个服务的**横切关注点**进行深入的技术剖析。它们是连接高层原则(L1, L2)和具体服务实现(L4, SRS)的桥梁，为开发人员提供了“如何正确实现某个通用能力”的权威指南。

---
### 文件1: `docs/architecture/L3_Core_Capabilities_Deep_Dive/01_authentication_and_authorization.md`

```markdown
# 深度解析: 认证与授权 (Authentication & Authorization)

**文档状态**: 已采纳 (Accepted)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**相关服务/包**: `user-core-service`, `api-gateway`, `pkg/auth`

## 1. 概述

本文档深度剖析CINA.CLUB平台的认证（“你是谁？”）与授权（“你能做什么？”）机制。平台采用基于 **JWT (JSON Web Token)** 和 **RBAC (Role-Based Access Control)** 的标准化模型，并为服务间通信设计了独立的S2S认证方案，共同构成了平台的零信任安全基础。

---

## 2. 最终用户认证与会话管理

### 2.1 令牌类型与生命周期

平台使用两种JWT令牌来管理用户会话：

*   **Access Token**:
    *   **用途**: 用于客户端访问所有受保护的API资源。
    *   **格式**: JWS (JSON Web Signature)，使用 **RS256/ES256** 非对称签名算法。
    *   **生命周期**: **极短（15分钟）**。这降低了令牌泄露的风险。
    *   **Payload (Claims)**: 必须包含 `iss` (签发者), `sub` (用户ID), `aud` (受众), `exp` (过期时间), `iat` (签发时间), `jti` (唯一ID), 以及 `roles` 和 `deviceId`。
*   **Refresh Token**:
    *   **用途**: 用于在Access Token过期后，静默地获取新的Access Token。
    *   **格式**: 一个**不透明的、高熵的随机字符串**。它本身不包含任何用户信息。
    *   **生命周期**: **长（30天）**。
    *   **存储**:
        *   **前端**: 必须存储在安全的、HttpOnly、Secure、SameSite=Strict的Cookie中（Web端），或系统的Keychain/Keystore中（移动端）。
        *   **后端 (`user-core-service`)**: 存储其**SHA-256哈希值**，并与`userId`, `deviceId`, `userAgent`, `expiresAt`关联。

### 2.2 登录与令牌刷新流程

```mermaid
sequenceDiagram
    participant Client
    participant UserCore as user-core-service
    
    Client->>UserCore: 1. /auth/login (凭证)
    UserCore->>UserCore: 2. 验证凭证, 生成令牌对
    UserCore-->>Client: 3. 返回 Access Token 和 Refresh Token
    
    note over Client: Access Token 过期后...
    
    Client->>UserCore: 4. /auth/token/refresh (携带Refresh Token)
    UserCore->>DB: 5. 验证Refresh Token哈希值
    alt 验证通过
        UserCore->>UserCore: 6. 签发新的令牌对 (刷新令牌旋转)
        UserCore->>DB: 7. 使旧的Refresh Token失效
        UserCore-->>Client: 8. 返回新的Access Token和Refresh Token
    else 验证失败
        UserCore-->>Client: 9. 401 Unauthenticated, 要求重新登录
    end
```

### 2.3 JWT签名验证

*   **机制**: 所有需要验证Access Token的服务（主要是`api-gateway`）都通过`user-core-service`暴露的`/.well-known/jwks.json` (JSON Web Key Set) 端点获取公钥。
*   **缓存**: JWKS公钥集必须被调用方积极缓存，并设置合理的缓存刷新机制（如基于HTTP缓存头或固定时间间隔），以避免对`user-core-service`造成性能瓶颈。

---

## 3. 服务间 (S2S) 认证

*   **目的**: 确保一个微服务（如`billing-service`）收到的请求确实来自另一个合法的微服务（如`service-offering-service`），而不是被伪造的。
*   **机制**:
    1.  **密钥对**: 每个微服务在部署时，都会被分配一对唯一的**S2S密钥对（公钥/私钥）**。私钥通过安全的Secrets Management工具注入。公钥可以被注册到一个内部的服务发现或配置中心。
    2.  **S2S Token**: 当服务A要调用服务B时，服务A使用自己的**私钥**签发一个极短生命周期（如1分钟）的JWT。该JWT的claims包含`iss: "service-A"`, `aud: "service-B"`。
    3.  **验证**: 服务B收到请求后，使用`pkg/auth`的S2S拦截器。拦截器获取`iss` claim，然后从配置中心获取服务A的**公钥**，并用其验证签名。

---

## 4. 授权 (RBAC)

*   **模型**:
    *   **Permission (权限)**: 一个原子操作的描述，如`user:read`, `service:create`。
    *   **Role (角色)**: 一组权限的集合，如`ADMIN`, `CONTENT_CURATOR`。
    *   **分配**: 角色被分配给用户。
*   **执行**:
    1.  `user-core-service`负责定义和管理Permission, Role以及它们之间的映射关系。
    2.  用户的Access Token中包含其拥有的`roles`列表。
    3.  受保护的gRPC端点需要声明其所需的`permission`。
    4.  `pkg/auth`的RBAC拦截器在验证token后，根据用户的`roles`和预加载的“角色-权限”映射，检查用户是否拥有所需的权限。

---

## 5. 实现细节与相关包

*   **`user-core-service`**: JWT的签发、刷新、吊销，以及角色权限管理的权威数据源。
*   **`pkg/auth`**: 提供标准化的gRPC拦截器，用于在每个服务中执行JWT验证和RBAC检查。
*   **`api-gateway`**: 负责对所有外部流量进行第一道JWT验证。
```
---