# CINA.CLUB Platform - Admin BFF Service Ingress Configuration
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Admin BFF Service Ingress - High Security Admin Backend for Frontend
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: admin-bff-ingress
  namespace: admin
  labels:
    app: admin-bff-service
    component: ingress
    tier: admin
    service: admin-bff
    security-level: maximum
  annotations:
    # Kong Ingress Controller configuration
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Admin BFF requires strictest security: admin JWT + admin rate limits + restrictive CORS
    konghq.com/plugins: "jwt-validator-admin, rate-limit-admin, cors-restrictive, prometheus-metrics, opentelemetry-tracing, request-id, request-size-limit"
    
    # Protocol configuration for gRPC backend
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Admin-optimized timeouts (moderate for admin operations)
    konghq.com/read-timeout: "60000"        # 1 minute for admin operations
    konghq.com/write-timeout: "60000"       # 1 minute
    konghq.com/connect-timeout: "3000"      # 3 seconds
    
    # Security headers for admin
    konghq.com/response-headers: |
      Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
      X-Content-Type-Options: nosniff
      X-Frame-Options: DENY
      X-XSS-Protection: 1; mode=block
      Referrer-Policy: strict-origin-when-cross-origin
      Content-Security-Policy: default-src 'self'; frame-ancestors 'none'
    
    # Upstream configuration
    konghq.com/host-header: "admin-bff-service.admin.svc.cluster.local"
    
    # Description and metadata
    description: "Admin BFF Service for administrative backend operations"
    service-owner: "<EMAIL>"
    api-version: "v1"
    security-classification: "restricted"

spec:
  # TLS configuration for admin domain
  tls:
    - hosts:
        - "admin-api.cina.club"
      secretName: "cina-club-admin-api-tls"
  
  rules:
    - host: "admin-api.cina.club"              # Dedicated admin API domain
      http:
        paths:
          # User management endpoints (admin JWT required)
          - path: /api/v1/admin/users
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Content management
          - path: /api/v1/admin/content
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # System configuration
          - path: /api/v1/admin/system
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Analytics and reports
          - path: /api/v1/admin/analytics
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Billing and subscriptions
          - path: /api/v1/admin/billing
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Security and audit logs
          - path: /api/v1/admin/security
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Platform management
          - path: /api/v1/admin/platform
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080

---
# Super Admin Endpoints (highest privilege level)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: admin-bff-super-admin-ingress
  namespace: admin
  labels:
    app: admin-bff-service
    component: ingress
    tier: super-admin
    security-level: maximum
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Super admin endpoints with maximum security and minimal rate limits
    konghq.com/plugins: "jwt-validator-admin, rate-limit-admin, cors-restrictive, prometheus-metrics, opentelemetry-tracing, request-id, request-size-limit"
    
    # Protocol configuration
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Enhanced security headers for super admin
    konghq.com/response-headers: |
      Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
      X-Content-Type-Options: nosniff
      X-Frame-Options: DENY
      X-XSS-Protection: 1; mode=block
      Referrer-Policy: no-referrer
      Content-Security-Policy: default-src 'none'; frame-ancestors 'none'
      X-Super-Admin: true
    
    # Description
    description: "Super admin endpoints with highest privilege access"

spec:
  tls:
    - hosts:
        - "admin-api.cina.club"
      secretName: "cina-club-admin-api-tls"
  
  rules:
    - host: "admin-api.cina.club"
      http:
        paths:
          # Super admin system control
          - path: /api/v1/admin/super/system
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Super admin user management (all permissions)
          - path: /api/v1/admin/super/users
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Platform configuration (infrastructure level)
          - path: /api/v1/admin/super/platform
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Emergency controls
          - path: /api/v1/admin/super/emergency
            pathType: Prefix
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080

---
# Admin Health and Monitoring Endpoints
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: admin-bff-monitoring-ingress
  namespace: admin
  labels:
    app: admin-bff-service
    component: ingress
    tier: monitoring
    security-level: internal
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Monitoring endpoints with reduced authentication (internal access)
    konghq.com/plugins: "rate-limit-ip, prometheus-metrics, request-id"
    
    # Protocol configuration
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Description
    description: "Admin BFF health and monitoring endpoints for internal use"

spec:
  tls:
    - hosts:
        - "admin-api.cina.club"
      secretName: "cina-club-admin-api-tls"
  
  rules:
    - host: "admin-api.cina.club"
      http:
        paths:
          # Health check endpoint
          - path: /api/v1/admin/health
            pathType: Exact
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Metrics endpoint (for internal monitoring)
          - path: /api/v1/admin/metrics
            pathType: Exact
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080
          
          # Service info
          - path: /api/v1/admin/service-info
            pathType: Exact
            backend:
              service:
                name: admin-bff-service
                port:
                  number: 8080

---
# Kong Service for Admin BFF Service
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: admin-bff-kong-service
  namespace: admin
  labels:
    app: admin-bff-service
    component: kong-service
  annotations:
    description: "Kong service configuration for Admin BFF Service"
    security-level: "maximum"

spec:
  # Backend service configuration
  host: "admin-bff-service.admin.svc.cluster.local"
  port: 8080
  protocol: "grpc"                         # gRPC protocol for backend
  
  # Admin-optimized connection settings
  connect_timeout: 3000                    # 3 seconds fast connect
  read_timeout: 60000                      # 1 minute for admin operations
  write_timeout: 60000                     # 1 minute
  
  # Retry configuration (minimal retries for admin operations)
  retries: 1                               # Only 1 retry for admin operations
  
  # Load balancing
  path: "/"

---
# Kong Upstream for Admin BFF Service
apiVersion: configuration.konghq.com/v1
kind: KongUpstream
metadata:
  name: admin-bff-upstream
  namespace: admin
  labels:
    app: admin-bff-service
    component: kong-upstream
  annotations:
    description: "Kong upstream for Admin BFF Service with high security"
    security-level: "maximum"

spec:
  # Load balancing algorithm for admin workloads
  algorithm: "round-robin"                 # Standard round-robin for admin
  
  # Health checks for admin services (more stringent)
  healthchecks:
    active:
      http_path: "/health"
      https_verify_certificate: true       # Verify certificates for admin
      healthy:
        interval: 20                       # Frequent health checks for admin
        successes: 3                       # Require 3 successes for admin
      unhealthy:
        interval: 10                       # Fast unhealthy detection
        tcp_failures: 2                    # Low tolerance for admin failures
        http_failures: 2
        timeouts: 2
    
    passive:
      healthy:
        successes: 3
      unhealthy:
        tcp_failures: 2                    # Strict passive health checks
        http_failures: 2
        timeouts: 2
  
  # Connection slots for admin service (fewer for security)
  slots: 100                               # Limited slots for admin security 