# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB Python 服务标准化基础镜像
# 多阶段构建，优化镜像大小和安全性

# Build stage
FROM python:3.11-slim AS builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 升级 pip 和安装工具
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# 首先复制依赖文件以利用 Docker 缓存
COPY requirements.txt requirements-dev.txt* ./

# 安装 Python 依赖到独立目录
RUN pip install --no-cache-dir --user -r requirements.txt

# 复制源代码
COPY . .

# 运行单元测试（可选，在 CI 中启用）
# RUN python -m pytest tests/ -v

# Production stage
FROM python:3.11-slim AS production

# 创建非特权用户
RUN groupadd --gid 1000 appuser && \
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置工作目录
WORKDIR /app

# 从 builder 阶段复制已安装的 Python 包
COPY --from=builder /root/.local /home/<USER>/.local

# 复制应用代码
COPY --chown=appuser:appuser . .

# 切换到非特权用户
USER appuser

# 设置 Python 路径
ENV PATH=/home/<USER>/.local/bin:$PATH \
    PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动应用
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

# Development stage (可选，用于开发环境)
FROM production AS development

# 切换回 root 用户安装开发工具
USER root

# 安装开发依赖
COPY requirements-dev.txt* ./
RUN pip install --no-cache-dir -r requirements-dev.txt || true

# 安装调试工具
RUN pip install --no-cache-dir \
    debugpy \
    ipdb \
    watchdog[watchmedo]

# 切换回应用用户
USER appuser

# 启用开发模式
ENV ENVIRONMENT=development \
    DEBUG=true

# 开发模式下的启动命令（支持热重载）
CMD ["watchmedo", "auto-restart", "--directory=.", "--pattern=*.py", "--recursive", "--", \
     "python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"] 