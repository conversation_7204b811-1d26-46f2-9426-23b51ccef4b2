/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 19:15:00
 * Modified: 2025-01-23 19:15:00
 */

import * as Sentry from '@sentry/react';

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

const isProduction = process.env.NODE_ENV === 'production';

/**
 * A structured logger that sends logs to Sentry in production
 * and uses the console in development.
 */
class Logger {
  private log(level: LogLevel, message: string, context?: Record<string, any>) {
    if (isProduction) {
      Sentry.captureMessage(message, {
        level: level,
        extra: context,
      });
    } else {
      const timestamp = new Date().toISOString();
      console[level](`[${timestamp}] [${level.toUpperCase()}] ${message}`, context || '');
    }
  }

  debug(message: string, context?: Record<string, any>) {
    // Debug logs are often too noisy for production
    if (!isProduction) {
      this.log('debug', message, context);
    }
  }

  info(message: string, context?: Record<string, any>) {
    this.log('info', message, context);
  }

  warn(message: string, context?: Record<string, any>) {
    this.log('warn', message, context);
  }

  error(error: Error, context?: Record<string, any>) {
    if (isProduction) {
      Sentry.captureException(error, {
        extra: context,
      });
    } else {
      const timestamp = new Date().toISOString();
      console.error(`[${timestamp}] [ERROR] ${error.message}`, {
        ...context,
        stack: error.stack,
      });
    }
  }
}

export const logger = new Logger(); 