/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using Microsoft.UI.Xaml.Controls;
using System;

namespace CinaClub.App.Services;

/// <summary>
/// 导航服务接口
/// 提供页面导航功能
/// </summary>
public interface INavigationService
{
    /// <summary>
    /// 初始化导航服务
    /// </summary>
    /// <param name="frame">导航框架</param>
    void Initialize(Frame frame);

    /// <summary>
    /// 导航到指定页面类型
    /// </summary>
    /// <typeparam name="T">页面类型</typeparam>
    /// <param name="parameter">导航参数</param>
    /// <returns>是否导航成功</returns>
    bool NavigateTo<T>(object? parameter = null) where T : class;

    /// <summary>
    /// 导航到指定页面类型
    /// </summary>
    /// <param name="pageType">页面类型</param>
    /// <param name="parameter">导航参数</param>
    /// <returns>是否导航成功</returns>
    bool NavigateTo(Type pageType, object? parameter = null);

    /// <summary>
    /// 后退导航
    /// </summary>
    /// <returns>是否可以后退</returns>
    bool GoBack();

    /// <summary>
    /// 前进导航
    /// </summary>
    /// <returns>是否可以前进</returns>
    bool GoForward();

    /// <summary>
    /// 是否可以后退
    /// </summary>
    bool CanGoBack { get; }

    /// <summary>
    /// 是否可以前进
    /// </summary>
    bool CanGoForward { get; }

    /// <summary>
    /// 当前页面类型
    /// </summary>
    Type? CurrentPageType { get; }

    /// <summary>
    /// 清空导航历史
    /// </summary>
    void ClearHistory();
} 