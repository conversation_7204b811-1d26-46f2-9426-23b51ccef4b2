﻿好的，遵照您的指示，我们来生成一份为 `embedding-service` (共享文本嵌入服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多模态支持**: 明确将服务范围扩展到支持**文本、图片和（未来）音频**等多种模态的嵌入生成。
2.  **模型路由与降级策略**: 引入更智能的模型路由逻辑，支持基于成本、性能和任务类型的动态选择，并定义详细的故障降级策略。
3.  **用量追踪与计费集成**: 详细定义如何追踪每个请求的token消耗，并与`billing-service`协同，以支持对内部服务调用方的成本分摊或限额。
4.  **模型健康检查与缓存**: 增加对下游模型服务的健康检查机制，并细化缓存策略。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、高可用、经济高效且能支撑整个平台多模态语义理解需求的AI基础设施。

---

### CINA.CLUB - embedding-service 需求规格说明书

**版本: 2.0 (生产级定义，支持多模态与高级路由)**  
**发布日期: 2025-06-22**  
**最后修订日期: 2025-06-22**  
**文档负责人:** [AI平台/算法团队负责人名称]  
**审批人:** [CTO/AI总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台的核心竞争力之一是其强大的AI能力和智能搜索功能。为了支持这些功能，平台需要将海量的、多模态的非结构化数据（文本、图片等）转换为机器可以理解的数学表示（嵌入向量）。`embedding-service` 的目的在于提供一个**统一、高性能、高可用、可扩展且经济高效**的嵌入生成服务，作为平台所有语义理解、相似性计算和多模态搜索任务的**核心基础设施**。

#### 1.2. 服务范围
本服务 **负责**:
*   **多模态嵌入生成**:
    *   接收来自其他内部微服务的单个或批量**文本**输入，并将其转换为高维向量。
    *   接收**图片**的引用（URL或`file-storage-service`的key），并将其转换为高维向量。
    *   （未来）支持**音频**等其他模态。
*   **多模型抽象与路由**:
    *   支持并抽象对多种不同嵌入模型（如OpenAI, Google, Cohere的商业API，或自托管的Sentence-Transformers, CLIP等模型）的调用。
    *   根据请求的任务类型、成本和性能要求，智能地将请求路由到最合适的模型。
*   **批量处理与性能优化**: 高效地处理批量请求，并对结果进行可选的缓存。
*   **用量追踪与计费协同**:
    *   精确记录每次调用的token消耗或计费单位。
    *   提供API供`billing-service`查询各服务的用量，以实现成本分摊。
*   **模型健康检查与降级**: 定期检查下游模型服务的可用性，并在主模型故障时自动降级到备用模型。

本服务 **不负责**:
*   **训练或托管机器学习模型推理服务**: 本服务调用专门的模型推理服务，而不自己加载和运行模型。
*   **存储生成的嵌入向量**: 向量的持久化由调用方服务负责（如`personal-kb-service`存入VectorDB）。
*   **向量相似性搜索**: 由向量数据库或`search-service`负责。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部所有需要语义理解的微服务 (主要)**:
    *   `personal-kb-service`, `shared-kb-service`, `memory-service`: 为知识内容生成嵌入。
    *   `search-service`: 将用户查询转换为向量以进行语义搜索。
    *   `ai-assistant-service`: 理解用户输入或工具输出的语义。
    *   `content-moderation-service`: （可选）使用语义相似度检测重复或违规内容。

#### 1.4. 定义与缩略语
*   **Embedding**: 嵌入向量，数据在低维空间的数学表示。
*   **Modality**: 模态，指数据的类型，如文本、图像、音频。
*   **CLIP (Contrastive Language-Image Pre-Training)**: 一种能为文本和图像生成可对齐向量的模型。
*   **S2S**: 服务间通信。
*   **Circuit Breaker**: 熔断器模式。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`embedding-service` 是CINA.CLUB AI基础设施的“**通用翻译器**”和“**语义编码中心**”。它是一个高度专业化的、无状态的计算代理服务，将平台中所有需要进行语义理解的多模态数据“翻译”成统一的向量空间表示，为上层的搜索、推荐、问答和个性化AI功能提供最基础的“燃料”。

#### 2.2. 主要功能概述
*   提供统一的、多模态、多模型的嵌入生成API。
*   智能路由与故障降级。
*   内置用量追踪与缓存。
*   与模型推理服务的完全解耦。

### 3. 核心流程图

#### 3.1. 处理一个带智能路由的批量文本嵌入请求
```mermaid
sequenceDiagram
    participant Requester as "Calling Service (e.g., PKB)"
    participant EmbeddingService as ES
    participant Redis
    participant ModelRouter as "Model Router"
    participant PrimaryModel as "Primary Model API (e.g., OpenAI)"
    participant FallbackModel as "Fallback Model API (e.g., Self-hosted)"

    Requester->>ES: 1. POST /embed (texts: ["text1", "text2"], task_type: "search_document")
    
    ES->>Redis: 2. Check cache for "text1", "text2"
    Redis-->>ES: (Found "text1", not "text2")
    
    ES->>ModelRouter: 3. For "text2", select model for task_type="search_document"
    ModelRouter->>ES: (Select PrimaryModel based on rules)
    
    ES->>PrimaryModel: 4. **[Attempt 1]** Request embedding for "text2"
    
    alt PrimaryModel is healthy
        PrimaryModel-->>ES: 5a. (Embedding for "text2")
    else PrimaryModel fails or times out
        Note over ES: Circuit Breaker for PrimaryModel opens!
        ES->>ModelRouter: 5b. Request a fallback model
        ModelRouter-->>ES: (Select FallbackModel)
        ES->>FallbackModel: 6b. **[Attempt 2]** Request embedding for "text2"
        FallbackModel-->>ES: (Embedding from fallback)
    end
    
    ES->>Redis: 7. Cache the new embedding for "text2"
    ES->>ES: 8. Record token usage for the API call
    
    ES-->>Requester: 9. 200 OK (Return combined embeddings for "text1" and "text2")
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 多模态嵌入生成
*   **FR4.1.1 (文本嵌入)**: 系统必须提供API，接收一个或多个文本字符串，返回对应的嵌入向量。
*   **FR4.1.2 (图像嵌入)**: 系统必须提供API，接收一个或多个图像的URL或`fileKey`，返回对应的嵌入向量。
*   **FR4.1.3 (统一接口)**: 推荐使用统一的API接口，通过输入对象的`type`字段来区分不同模态。

#### 4.2. 智能路由与降级
*   **FR4.2.1 (模型目录)**: 系统必须维护一个可配置的模型目录，包含每个模型的元数据：`model_name`, `provider`, `api_endpoint`, `modality`, `dimension`, `cost_per_token`, `supported_task_types` (`search_query`, `search_document`, `classification`等)。
*   **FR4.2.2 (路由策略)**: API请求中可包含`task_type`和`quality_preference` ("high", "low_cost")等提示。路由逻辑根据这些提示和模型目录，选择最合适的模型。
*   **FR4.2.3 (健康检查)**: 系统必须有后台任务，定期对所有下游模型服务进行健康检查。
*   **FR4.2.4 (故障降级)**:
    *   必须为每个模型配置一个或多个备用(fallback)模型。
    *   当对主模型的调用连续失败或超时达到阈值时，**熔断器(Circuit Breaker)**必须打开，并在一段时间内自动将请求路由到备用模型。

#### 4.3. 用量追踪与计费协同
*   **FR4.3.1 (Token计算)**: 对于文本嵌入，系统必须能准确计算输入的token数量。
*   **FR4.3.2 (用量记录)**: 每次API调用，系统都必须记录：`calling_service_id`, `model_used`, `tokens_consumed` (或`images_processed`)。此记录应被持久化或发送到`analytics-service`。
*   **FR4.3.3 (限额检查 - 可选)**: （高级）在处理请求前，可以调用`billing-service`检查调用方服务是否超出了其月度使用限额。

#### 4.4. 性能优化
*   **FR4.4.1 (批量处理)**: API必须支持一次性接收一个输入列表。服务内部应将大的请求列表分割成符合下游模型服务批量处理上限的小批次，并**并行**发送。
*   **FR4.4.2 (结果缓存)**: 系统必须对嵌入结果进行缓存。缓存的Key必须是`hash(input_content) + model_name`的组合。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC/RESTful API接口 (S2S)
*   **版本**: `/api/v1/embeddings`
*   **认证**: 严格的S2S认证。
*   **核心端点**:
    *   `POST /embed`: **统一嵌入生成接口**。
        *   **Request Body**: `GenerateEmbeddingsRequest { inputs: [InputItem], model_name?: string, task_type?: string }`
        *   `InputItem`: `{ type: "TEXT"|"IMAGE", content: "text_string"|"image_url_or_key" }`
        *   **Response**: `GenerateEmbeddingsResponse { model_used, data: [{index, embedding}], usage: {tokens, ...} }`

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型
*   **无核心持久化数据库**: 本服务设计为**无状态**。
*   **配置**: 模型目录和服务路由策略存储在**配置文件(YAML)**或**配置中心**（如Consul, Nacos）中，以便动态更新。
*   **缓存 (Redis)**:
    *   `cache:embedding:{model_name}:{hash_of_input}` -> 二进制编码的向量。
*   **用量记录**:
    *   **方案A (轻量)**: 将用量记录作为结构化日志输出，由日志系统收集分析。
    *   **方案B (可靠)**: 将用量记录作为事件发送到Kafka，由`analytics-service`消费。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: P99延迟（不含外部API或模型推理时间）应 `< 50ms`。
*   **吞吐量**: 能够通过水平扩展，满足平台所有服务对嵌入生成的总需求。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。本服务的故障将影响所有AI和搜索功能。
*   **容错**: 必须实现**FR4.2.4**中定义的、基于熔断器的故障降级机制，这是保证高可用的关键。

#### 7.3. 可扩展性需求
*   服务必须是无状态的，易于水平扩展。

#### 7.4. 安全性需求
*   **S2S认证**: 保护API端点。
*   **API密钥安全**: 所有外部API Key必须安全存储在Vault或KMS中，并通过安全的配置管理注入到服务中。

### 8. 技术约束与选型建议
*   **语言**: Go。其高并发模型非常适合处理大量并行的API调用和I/O。
*   **核心架构**:
    *   采用“**适配器(Adapter)**”模式，为每个下游模型服务（OpenAI, Self-hosted CLIP等）实现一个统一的`EmbeddingProvider`接口。
    *   “**路由(Router)**”和“**熔断器(Circuit Breaker)**”是架构的核心组件。
*   **与Python模型服务的通信**: 如果自托管模型，推荐使用**gRPC**与Python写的模型推理服务进行通信，以获得更好的性能和类型安全。
*   **成本控制**: 智能路由和结果缓存是控制外部API调用成本的关键。

---
这份版本2.0的SRS文档为`embedding-service`构建了一个现代化、高可用、多模态的AI基础设施。它通过将模型细节抽象化，并引入智能路由和容错机制，为整个CINA.CLUB平台的语义理解能力提供了坚实、经济且可靠的基础。