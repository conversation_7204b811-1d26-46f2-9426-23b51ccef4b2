好的，遵照您的指示。我将为您生成一份专门针对 **`pkg/errors`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`pkg/errors` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到代码实现的层面，详细阐述**自定义错误类型`AppError`的实现、错误包装(Wrapping)与堆栈跟踪、以及与gRPC `status`包的双向无损转换机制**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `pkg/errors` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `pkg/errors-srs.md` (v1.0)
**核心架构**: 自定义错误类型 + Go标准错误接口实现 + gRPC状态转换器

## 1. 概述

`pkg/errors` 是CINA.CLUB后端微服务生态中，用于**规范化错误处理**的基础核心库。它提供了一套工具，用于创建、包装、检查和传递结构化的错误信息。其架构设计的核心目标是：
1.  **结构化与信息丰富**: 创建的错误对象不仅包含错误消息，还应携带**错误码、元数据、堆栈跟踪和原始错误链**。
2.  **与Go标准库的兼容性**: 自定义的错误类型**必须**完美实现Go 1.13+引入的`error`接口标准，特别是`Unwrap()`方法，以支持`errors.Is()`和`errors.As()`。
3.  **与gRPC的无缝集成**: 必须能将丰富的内部错误信息，无损地编码进gRPC的`status`中，并在接收端进行解码恢复。
4.  **易用性**: 为开发者提供简洁的、符合直觉的API来创建和处理错误。

本架构设计通过定义一个核心的`AppError`结构体，并为其实现所有必要的接口，来构建一个强大而易用的错误处理框架。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (错误创建、包装与转换流程)

```mermaid
graph TD
    subgraph "业务代码 (Service Layer)"
        A[e.g., repo.GetByID() returns pgx.ErrNoRows]
        B[e.g., service.GetUser() logic]
    end

    subgraph "pkg/errors"
        style "pkg/errors" fill:#e0f7fa
        C[Wrap() function]
        D[AppError struct<br/>{Code, Msg, Cause, Stack, Meta}]
        E[ToGRPCStatus() function]
        F[FromGRPCError() function]
    end
    
    subgraph "gRPC Layer"
        G[gRPC Handler]
        H[gRPC Status with Details<br/>(contains AppErrorDetail proto)]
        I[gRPC Client]
    end
    
    subgraph "pkg/errors 依赖"
        P1[Go stdlib 'errors']
        P2[gRPC 'status' package]
        P3['github.com/pkg/errors' for stack trace]
    end

    A -- "1. Raw DB error" --> B
    B -- "2. Wraps raw error" --> C
    C -- "Creates" --> D
    
    B -- "3. Returns AppError" --> G
    G -- "4. Converts to gRPC Status" --> E
    E -- "Creates" --> H
    
    H -- "5. Transmitted over network" --> I
    I -- "6. Converts from gRPC Status" --> F
    F -- "Reconstructs" --> D
```

### 2.2 最终目录结构 (`pkg/errors/`)

```
pkg/errors/
├── errors.go           # ✨ 核心AppError struct和构造函数的定义 ✨
├── codes.go            # ✨ ErrorCode枚举和与gRPC Code的映射 ✨
├── grpc.go             # ✨ ToGRPCStatus和FromGRPCError的实现 ✨
├── stack.go            # (内部) 堆栈跟踪的捕获逻辑
└── errors_test.go      # 单元测试
```

---

## 3. 各层职责深度解析

### 3.1 `codes.go` - 错误码定义

*   **职责**: 定义平台统一的、业务无关的错误码。
*   **实现**:
    ```go
    package errors

    import "google.golang.org/grpc/codes"

    type ErrorCode string

    const (
        OK                ErrorCode = "OK"
        InvalidArgument   ErrorCode = "InvalidArgument"
        NotFound          ErrorCode = "NotFound"
        // ... 其他所有错误码
        Internal          ErrorCode = "Internal"
    )

    // errorCodeToGRPCCodeMap 定义了从平台错误码到gRPC状态码的映射
    var errorCodeToGRPCCodeMap = map[ErrorCode]codes.Code{
        OK:              codes.OK,
        InvalidArgument: codes.InvalidArgument,
        NotFound:        codes.NotFound,
        // ...
        Internal:        codes.Internal,
    }

    // grpcCodeToErrorCodeMap 是反向映射
    var grpcCodeToErrorCodeMap = map[codes.Code]ErrorCode{ ... }
    ```

### 3.2 `stack.go` - 堆栈跟踪

*   **职责**: 封装堆栈跟踪的捕获。
*   **实现**: **直接利用`github.com/pkg/errors`库的堆栈捕获能力**。`pkg/errors`是事实上的工业标准，提供了比Go标准库更丰富的堆栈信息。
    ```go
    // stack.go
    import "github.com/pkg/errors"

    type stackTracer interface {
        StackTrace() errors.StackTrace
    }
    
    // newStack(skip int) 在创建AppError时被调用
    ```

### 3.3 `errors.go` - 核心`AppError`实现

*   **`AppError` struct**:
    ```go
    type AppError struct {
        Code     ErrorCode
        Message  string
        Cause    error
        Metadata map[string]string
        stack    stackTracer // 存储堆栈信息
    }
    ```
*   **实现标准`error`接口**:
    ```go
    func (e *AppError) Error() string {
        return fmt.Sprintf("code: %s, msg: %s, cause: %v", e.Code, e.Message, e.Cause)
    }
    ```
*   **实现`Unwrap()`以支持错误链**:
    ```go
    func (e *AppError) Unwrap() error {
        return e.Cause
    }
    ```
*   **实现`Format()`以支持`%+v`打印堆栈**:
    ```go
    func (e *AppError) Format(s fmt.State, verb rune) {
        // ... 实现逻辑，当verb为'v'且带有'+'标志时，打印详细信息和堆栈跟踪 ...
        // 参考 `github.com/pkg/errors` 的实现
    }
    ```
*   **构造函数 (`New`, `Wrap`, ...)**:
    *   **核心逻辑**: 在创建`AppError`实例时，**立即调用`newStack()`来捕获当前的调用堆栈**。
    *   `Wrap`函数会将传入的`cause`错误存入`Cause`字段。

### 3.4 `grpc.go` - gRPC转换器

*   **职责**: 实现`AppError`与gRPC `status`之间的无损转换。
*   **`ToGRPCStatus(err error)` function**:
    1.  **检查`err`类型**: 使用`errors.As(err, &appErr)`来查找错误链中的第一个`AppError`。
    2.  **如果不是`AppError`**: 将其视为未知内部错误，创建一个`codes.Internal`的status，并将`err.Error()`作为消息。
    3.  **如果是`AppError`**:
        a. `grpcCode := errorCodeToGRPCCodeMap[appErr.Code]`，获取对应的gRPC状态码。
        b. `st := status.New(grpcCode, appErr.Message)`，创建基础status。
        c. **✨ 附加Details (核心) ✨**:
            i.  创建一个`errors_v1.AppErrorDetail` Protobuf消息。
            ii. `detail.ErrorCode = string(appErr.Code)`。
            iii. `detail.Metadata = appErr.Metadata`。
            iv. `finalSt, err := st.WithDetails(detail)`，将`detail`附加到status中。
        d. 返回最终的`finalSt`。
*   **`FromGRPCError(err error)` function**:
    1.  **获取status**: `st, ok := status.FromError(err)`。如果转换失败，说明不是一个gRPC错误，返回一个Internal `AppError`。
    2.  **遍历Details**: `for _, detail := range st.Details() { ... }`。
    3.  **检查Detail类型**: `if d, ok := detail.(*errors_v1.AppErrorDetail); ok { ... }`。
    4.  **如果找到`AppErrorDetail`**:
        a. 使用`d.ErrorCode`和`st.Message()`来重建一个`AppError`。
        b. 将`d.Metadata`也恢复。
        c. 返回这个重建的`AppError`。
    5.  **如果没找到**: 根据`st.Code()`，使用`grpcCodeToErrorCodeMap`将其转换为平台的`ErrorCode`，并创建一个新的`AppError`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`pkg/errors`：
1.  **结构化的`AppError`**: 定义了一个包含**Code, Message, Cause, Metadata, Stack**五个核心元素的错误结构体，信息极其丰富。
2.  **拥抱Go标准**: 通过实现`Unwrap()`等接口，与Go语言的现代错误处理机制（`errors.Is`, `errors.As`）完全兼容。
3.  **利用gRPC Details**: 充分利用gRPC `status`的`details`字段，实现了在服务间传递**结构化的、机器可读的**错误信息，而不仅仅是一个字符串消息。这为客户端或API网关进行精细化的错误处理（如自动i18n转换）提供了可能。
4.  **清晰的转换逻辑**: 提供了`ToGRPCStatus`和`FromGRPCError`两个明确的函数作为错误在业务域和传输域之间的“转换门”，使得错误处理流程非常清晰。
5.  **开发者友好**: 提供了`New`, `Wrap`, `WithMeta`, `IsCode`等一系列便捷的API，简化了日常的错误处理代码。

这种架构确保了CINA.CLUB的错误处理体系是**统一的、可追溯的、信息丰富的、并且对分布式环境友好的**，是构建一个健壮、可维护的微服务系统的基础。