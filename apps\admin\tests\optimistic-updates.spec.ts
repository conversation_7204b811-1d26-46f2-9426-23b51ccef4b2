/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 19:05:00
 * Modified: 2025-01-23 19:05:00
 */

import { test, expect } from '@playwright/test';
import { http, HttpResponse } from 'msw';
import { server } from '../mocks/server'; // Assuming you have this from previous steps

test.describe('Optimistic UI Updates', () => {
  test.beforeEach(async ({ page }) => {
    // Log in as an admin
    await page.goto('/login');
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password');
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('should optimistically remove a user from the list and roll back on error', async ({ page }) => {
    // **1. Setup MSW to return an error for the delete request**
    server.use(
      http.delete('*/api/v1/users/:id', () => {
        return new HttpResponse(null, { status: 500 });
      })
    );

    // **2. Navigate to the user list**
    await page.goto('/users');
    const userEmailToDelete = '<EMAIL>'; // An email from your mock data
    const userRow = page.locator(`tr:has-text("${userEmailToDelete}")`);
    await expect(userRow).toBeVisible();

    // **3. Click the delete button**
    await userRow.getByRole('button', { name: '删除' }).click();
    await page.getByRole('button', { name: 'OK' }).click(); // Confirm in modal

    // **4. Assert that the user is REMOVED from the UI immediately**
    await expect(userRow).not.toBeVisible({ timeout: 1000 }); // Check for immediate removal

    // **5. Assert that an error message appears and the user reappears (rollback)**
    await expect(page.locator('.ant-message-error')).toBeVisible();
    await expect(page.locator('.ant-message-error')).toHaveText(/Failed to delete user/);
    
    // After the rollback, the user should be visible again
    await expect(userRow).toBeVisible();
  });
}); 