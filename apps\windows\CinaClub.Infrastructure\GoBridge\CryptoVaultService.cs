/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using CinaClub.Core.Interfaces;

namespace CinaClub.Infrastructure.GoBridge;

/// <summary>
/// 加密保险库服务实现
/// 提供E2EE加密/解密功能，通过Go核心库实现
/// </summary>
public class CryptoVaultService : ICryptoVault, IDisposable
{
    private readonly ILogger<CryptoVaultService> _logger;
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public CryptoVaultService(ILogger<CryptoVaultService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 检查Go库是否可用
        if (!NativeMethods.IsLibraryReady())
        {
            throw new InvalidOperationException("Go核心库未就绪");
        }

        _logger.LogInformation("CryptoVaultService初始化成功");
    }

    /// <summary>
    /// 从密码派生主密钥
    /// </summary>
    /// <param name="password">用户密码</param>
    /// <param name="salt">盐值（可选）</param>
    /// <returns>派生的密钥</returns>
    public async Task<byte[]> DeriveKeyFromPasswordAsync(string password, byte[]? salt = null)
    {
        if (string.IsNullOrEmpty(password))
        {
            throw new ArgumentException("密码不能为空", nameof(password));
        }

        return await Task.Run(() =>
        {
            _logger.LogDebug("开始派生密钥");

            var goPassword = GoString.FromString(password);
            var goSalt = salt != null ? GoSlice.FromByteArray(salt) : new GoSlice();

            try
            {
                var result = NativeMethods.DeriveKeyFromPassword(goPassword, goSalt);
                
                if (result.Error.HasError)
                {
                    var errorMsg = result.Error.Message.ToString();
                    _logger.LogError("密钥派生失败: {Error}", errorMsg);
                    throw new InvalidOperationException($"密钥派生失败: {errorMsg}");
                }

                var derivedKey = result.Data.ToByteArray();
                _logger.LogDebug("密钥派生成功，长度: {Length}", derivedKey.Length);
                
                result.Free();
                return derivedKey;
            }
            finally
            {
                goPassword.Free();
                goSalt.Free();
            }
        });
    }

    /// <summary>
    /// 加密数据
    /// </summary>
    /// <param name="key">加密密钥</param>
    /// <param name="plaintext">明文数据</param>
    /// <returns>加密后的数据</returns>
    public async Task<byte[]> EncryptAsync(byte[] key, byte[] plaintext)
    {
        if (key == null || key.Length == 0)
        {
            throw new ArgumentException("密钥不能为空", nameof(key));
        }

        if (plaintext == null)
        {
            throw new ArgumentNullException(nameof(plaintext));
        }

        return await Task.Run(() =>
        {
            _logger.LogDebug("开始加密数据，长度: {Length}", plaintext.Length);

            var goKey = GoSlice.FromByteArray(key);
            var goPlaintext = GoSlice.FromByteArray(plaintext);

            try
            {
                var result = NativeMethods.EncryptSymmetric(goKey, goPlaintext);
                
                if (result.Error.HasError)
                {
                    var errorMsg = result.Error.Message.ToString();
                    _logger.LogError("加密失败: {Error}", errorMsg);
                    throw new InvalidOperationException($"加密失败: {errorMsg}");
                }

                var ciphertext = result.Data.ToByteArray();
                _logger.LogDebug("加密成功，密文长度: {Length}", ciphertext.Length);
                
                result.Free();
                return ciphertext;
            }
            finally
            {
                goKey.Free();
                goPlaintext.Free();
            }
        });
    }

    /// <summary>
    /// 解密数据
    /// </summary>
    /// <param name="key">解密密钥</param>
    /// <param name="ciphertext">密文数据</param>
    /// <returns>解密后的数据</returns>
    public async Task<byte[]> DecryptAsync(byte[] key, byte[] ciphertext)
    {
        if (key == null || key.Length == 0)
        {
            throw new ArgumentException("密钥不能为空", nameof(key));
        }

        if (ciphertext == null)
        {
            throw new ArgumentNullException(nameof(ciphertext));
        }

        return await Task.Run(() =>
        {
            _logger.LogDebug("开始解密数据，长度: {Length}", ciphertext.Length);

            var goKey = GoSlice.FromByteArray(key);
            var goCiphertext = GoSlice.FromByteArray(ciphertext);

            try
            {
                var result = NativeMethods.DecryptSymmetric(goKey, goCiphertext);
                
                if (result.Error.HasError)
                {
                    var errorMsg = result.Error.Message.ToString();
                    _logger.LogError("解密失败: {Error}", errorMsg);
                    throw new InvalidOperationException($"解密失败: {errorMsg}");
                }

                var plaintext = result.Data.ToByteArray();
                _logger.LogDebug("解密成功，明文长度: {Length}", plaintext.Length);
                
                result.Free();
                return plaintext;
            }
            finally
            {
                goKey.Free();
                goCiphertext.Free();
            }
        });
    }

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    /// <param name="keySize">密钥长度（字节）</param>
    /// <returns>生成的密钥</returns>
    public async Task<byte[]> GenerateRandomKeyAsync(int keySize = 32)
    {
        if (keySize <= 0)
        {
            throw new ArgumentException("密钥长度必须大于0", nameof(keySize));
        }

        return await Task.Run(() =>
        {
            _logger.LogDebug("生成随机密钥，长度: {Size}", keySize);

            var result = NativeMethods.GenerateRandomKey(keySize);
            
            if (result.Error.HasError)
            {
                var errorMsg = result.Error.Message.ToString();
                _logger.LogError("密钥生成失败: {Error}", errorMsg);
                throw new InvalidOperationException($"密钥生成失败: {errorMsg}");
            }

            var key = result.Data.ToByteArray();
            _logger.LogDebug("随机密钥生成成功");
            
            result.Free();
            return key;
        });
    }

    /// <summary>
    /// 获取Go库版本信息
    /// </summary>
    /// <returns>版本字符串</returns>
    public string GetVersion()
    {
        var goVersion = NativeMethods.GetVersion();
        try
        {
            return goVersion.ToString();
        }
        finally
        {
            goVersion.Free();
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的实际实现
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _logger.LogInformation("CryptoVaultService正在释放资源");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~CryptoVaultService()
    {
        Dispose(false);
    }
} 