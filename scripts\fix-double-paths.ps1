# CINA.CLUB 双重路径修复脚本

Write-Host "Fixing double path issues..." -ForegroundColor Green

$fixedFiles = 0
$services = Get-ChildItem -Path "services" -Directory

foreach ($service in $services) {
    $serviceName = $service.Name
    Write-Host "Processing: $serviceName" -ForegroundColor Yellow
    
    $goFiles = Get-ChildItem -Path $service.FullName -Filter "*.go" -Recurse
    
    foreach ($goFile in $goFiles) {
        try {
            $content = Get-Content $goFile.FullName -Raw
            if ([string]::IsNullOrWhiteSpace($content)) { continue }
            
            $original = $content
            
            # Fix double path issues
            $content = $content -replace "cina\.club/services/cina\.club/services/", "cina.club/services/"
            $content = $content -replace "cina\.club/services/cina\.club/pkg/", "cina.club/pkg/"
            $content = $content -replace "cina\.club/services/cina\.club/core/", "cina.club/core/"
            
            if ($content -ne $original) {
                $utf8 = New-Object System.Text.UTF8Encoding $false
                [System.IO.File]::WriteAllText($goFile.FullName, $content, $utf8)
                $fixedFiles++
                Write-Host "  Fixed double paths in: $($goFile.Name)" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "  Error: $($goFile.Name)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "Fixed double paths in $fixedFiles files" -ForegroundColor Green

# Quick test on API gateway
Write-Host "Testing API Gateway compilation..." -ForegroundColor Yellow
$currentLocation = Get-Location
Set-Location "services\api-gateway-service"

$result = go build ./... 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "API Gateway: SUCCESS!" -ForegroundColor Green
} else {
    Write-Host "API Gateway: Still has issues" -ForegroundColor Red
    Write-Host $result
}

Set-Location $currentLocation

Write-Host ""
Write-Host "Run full batch test: .\scripts\simple-batch-test.ps1" -ForegroundColor Yellow 