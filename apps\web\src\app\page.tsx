export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl sm:text-6xl font-bold text-gray-900 mb-6">
            Welcome to{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              CINA.CLUB
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Your comprehensive digital life platform. Connect, create, and explore 
            with advanced AI assistance, secure communication, and innovative features.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Started
            </button>
            <button className="bg-white text-blue-600 px-8 py-3 rounded-lg text-lg font-semibold border-2 border-blue-600 hover:bg-blue-50 transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Platform Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <div className="text-blue-600 mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Available Services
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {services.map((service, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {service.name}
                </h3>
                <p className="text-gray-600 text-sm mb-3">
                  {service.description}
                </p>
                <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                  service.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {service.status === 'active' ? 'Active' : 'Coming Soon'}
                </span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Status Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Platform Status
          </h2>
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="flex items-center justify-center mb-4">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-lg font-semibold text-gray-900">
                All Systems Operational
              </span>
            </div>
            <p className="text-gray-600">
              API Gateway, User Services, and Core Infrastructure are running smoothly.
            </p>
            <div className="mt-6 text-sm text-gray-500">
              Last updated: {new Date().toLocaleString()}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

// Features data
const features = [
  {
    icon: '🤖',
    title: 'AI Assistant',
    description: 'Advanced AI-powered assistance for productivity and creativity.'
  },
  {
    icon: '💬',
    title: 'Secure Chat',
    description: 'End-to-end encrypted messaging with real-time communication.'
  },
  {
    icon: '🔍',
    title: 'Smart Search',
    description: 'Intelligent search across all your content and data.'
  },
  {
    icon: '💰',
    title: 'Digital Payments',
    description: 'Secure billing and payment processing integration.'
  },
  {
    icon: '📊',
    title: 'Analytics',
    description: 'Comprehensive insights and data analytics dashboard.'
  },
  {
    icon: '🛡️',
    title: 'Security',
    description: 'Enterprise-grade security with advanced authentication.'
  }
];

// Services data
const services = [
  { name: 'User Core', description: 'User management and authentication', status: 'active' },
  { name: 'API Gateway', description: 'Unified API access point', status: 'active' },
  { name: 'AI Assistant', description: 'Intelligent virtual assistant', status: 'active' },
  { name: 'Chat System', description: 'Real-time messaging platform', status: 'active' },
  { name: 'Search Engine', description: 'Advanced search capabilities', status: 'development' },
  { name: 'Billing System', description: 'Payment and subscription management', status: 'active' },
  { name: 'Analytics', description: 'Data insights and reporting', status: 'development' },
  { name: 'File Storage', description: 'Secure cloud file management', status: 'development' }
]; 