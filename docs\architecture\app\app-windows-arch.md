好的，遵照您的指示。我将为您生成一份专门针对 **CINA.CLUB 原生Windows桌面应用** 的、极致细化的、生产级**架构设计文档**。

这份架构将：
1.  **完全对齐**我们之前定义的**Go-Centric全栈架构**和**Monorepo**模式。
2.  采用微软推荐的**现代Windows开发技术栈**，以**WinUI 3**和**C#**为核心。
3.  通过**CGO**和**P/Invoke (Platform Invoke)**，将在Go中实现的核心逻辑（加密、同步、AI）无缝集成到Windows原生应用中。
4.  采用清晰的**模块化**和**分层**设计，以应对CINA.CLUB平台的复杂性。

---
### CINA.CLUB - 原生Windows桌面应用 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [Windows架构师/桌面端负责人]

## 1. 概述与设计哲学

### 1.1 项目愿景与目的
CINA.CLUB Windows应用旨在为桌面用户提供一个**功能完整、性能强大、体验现代**的原生客户端。它不仅是平台所有服务的桌面入口，更是生产力场景（如深度使用PKB、专业服务交易）的核心载体。本架构的目标是构建一个充分利用Windows平台特性、遵循现代桌面应用设计模式，并与平台Go-Centric理念深度融合的高质量Windows应用。

### 1.2 核心设计哲学
1.  **现代原生UI (Modern Native UI)**: 采用微软最新的**WinUI 3**框架构建整个应用界面，确保应用具有现代化的Fluent Design外观，并能充分利用Windows 11/10的系统特性。
2.  **MVVM架构模式 (Model-View-ViewModel)**: 这是.NET桌面开发的标准和最佳实践，通过XAML的数据绑定特性，实现UI（View）与UI逻辑（ViewModel）的彻底解耦。
3.  **异步与响应式 (Asynchronous & Reactive)**: 广泛使用`async/await`和`System.Reactive` (Rx.NET) 或社区的MVVM响应式库（如ReactiveUI），来处理异步操作和构建响应式数据流。
4.  **依赖注入 (Dependency Injection)**: 使用`Microsoft.Extensions.DependencyInjection`等标准的DI容器来管理应用的组件和服务，提高模块化和可测试性。
5.  **Go核心，C#胶水，WinUI皮肤**:
    *   **Go (via CGO)**: 执行最核心、最复杂的计算和协议（加密、同步、AI）。
    *   **C#**: 作为主要的Windows应用开发语言，负责业务逻辑编排、Windows API交互，并作为连接UI与Go核心的“胶水”。
    *   **WinUI 3 / XAML**: 作为唯一的UI技术栈，构建整个应用的界面。

---

## 2. 核心技术选型 (Modern Windows Development)

| 领域             | 技术选型                                         | 理由                                                                 |
|------------------|--------------------------------------------------|----------------------------------------------------------------------|
| **UI框架**         | **WinUI 3** (Windows App SDK)                    | 微软官方最新的原生UI框架，性能卓越，设计现代，是未来Windows应用开发的方向。|
| **编程语言**     | **C# 12** (.NET 8)                               | 现代、强大、类型安全的.NET平台主力语言。                             |
| **核心逻辑集成** | **Go (CGO)** -> **C-Style DLL** -> **C# (P/Invoke)** | 将Go核心库编译为标准的C动态链接库(.dll)，通过C#的P/Invoke机制进行调用。 |
| **异步处理**     | **`async/await` & TPL (Task Parallel Library)**  | .NET内置的、强大的异步编程模型。                                     |
| **架构模式**     | **MVVM (Model-View-ViewModel)**                  | .NET桌面开发的黄金标准。                                             |
| **数据请求**     | **Grpc.Net.Client**                              | .NET平台官方高性能的gRPC客户端。                                     |
| **本地数据库**   | **SQLite** (使用`Microsoft.Data.Sqlite`)         | 轻量、快速、跨平台，与Go核心通过CGO访问的SQLite数据库保持一致。      |
| **本地AI推理**   | **Go (CGO) -> `llama.cpp` -> DLL -> P/Invoke**   | 与移动端架构一致，通过Go层封装对C++推理库的调用。                     |
| **E2EE加密**     | **Go -> DLL -> P/Invoke**                        | 保证加密算法与所有其他平台100%一致。                                 |
| **安全存储**     | **Windows Data Protection API (DPAPI)**          | 用于安全地加密和存储需要持久化的密钥（如加密后的DEK）。                  |

---

## 3. Monorepo模块化架构

Windows项目采用多项目解决方案（Solution）的结构，这在逻辑上等同于Monorepo中的模块化。

### 3.1 项目模块结构 (`apps/desktop/windows/`)

```
windows/
├── CinaClub.sln                # Visual Studio解决方案文件
│
├── CinaClub.App/               # ✨ 主应用项目 (WinUI 3) ✨
│   ├── Package.appxmanifest
│   ├── MainWindow.xaml / .cs
│   ├── App.xaml / .cs
│   ├── Views/                  # 1. 视图 (XAML)
│   │   ├── LoginPage.xaml
│   │   └── ChatPage.xaml
│   ├── ViewModels/             # 2. 视图模型
│   │   ├── LoginViewModel.cs
│   │   └── ChatViewModel.cs
│   ├── Navigation/             # 3. 导航服务
│   └── DI/                     # 4. 依赖注入配置
│
├── CinaClub.Core/              # ✨ 核心业务逻辑与领域层 (.NET Standard) ✨
│   ├── UseCases/
│   ├── Repositories/ (Interfaces)
│   └── Models/ (Domain Models)
│
├── CinaClub.Infrastructure/    # ✨ 基础设施与数据层 (.NET Standard) ✨
│   ├── Repositories/ (Implementations)
│   ├── Network/                # gRPC客户端封装
│   ├── Database/               # SQLite (Room-like)封装
│   └── GoBridge/               # 5. ✨ Go核心的C# P/Invoke封装层 ✨
│
└── libs/
    └── x64/
        └── core_go.dll         # 6. Go编译的C动态链接库
        └── core_go.h           # Go生成的C头文件
```

### 3.2 各模块深度解析

#### `CinaClub.Infrastructure/GoBridge/` - Go核心的C#封装层

这是连接C#世界和Go/C++世界的**关键桥梁**。

*   **职责**:
    1.  **P/Invoke声明**: 使用`[DllImport("core_go.dll")]`特性，声明所有从Go DLL中导出的C风格函数。
    2.  **安全句柄 (SafeHandle)**: 为Go返回的指针（如`LLMSession*`）创建`SafeHandle`的子类，以确保资源的自动、安全释放（RAII模式），防止内存泄漏。
    3.  **C#友好封装**: 将C风格的函数（使用`IntPtr`, `byte[]`）封装成现代的、面向对象的C#类和方法。
    4.  **异步与流式处理**: 将Go中基于回调或channel的流式API，封装成C#的`IAsyncEnumerable<string>`或`IObservable<string>` (Rx.NET)。

    **示例: `CryptoVault.cs`**
    ```csharp
    // CinaClub.Infrastructure/GoBridge/CryptoVault.cs
    internal static class NativeCrypto
    {
        [DllImport("core_go.dll", CallingConvention = CallingConvention.Cdecl)]
        // Go的导出函数通常返回一个包含数据和长度的struct，或需要调用者提供缓冲区
        internal static extern GoSlice EncryptSymmetric(GoSlice key, GoSlice plaintext);
        
        [DllImport("core_go.dll", CallingConvention = CallingConvention.Cdecl)]
        internal static extern void FreeGoSlice(GoSlice slice);
    }

    public class CryptoVault : ICryptoVault
    {
        public async Task<byte[]> EncryptAsync(byte[] key, byte[] plaintext)
        {
            return await Task.Run(() => // 在后台线程执行CPU密集型操作
            {
                // ... 将.NET byte[] 转换为 GoSlice ...
                var encryptedSlice = NativeCrypto.EncryptSymmetric(keySlice, plaintextSlice);
                // ... 将返回的GoSlice转换为 .NET byte[] ...
                // ... 调用 NativeCrypto.FreeGoSlice() 释放Go分配的内存 ...
                return result;
            });
        }
    }
    ```

#### `CinaClub.Infrastructure/` - 基础设施与数据层

*   **`Network/`**: 封装所有**`Grpc.Net.Client`**客户端。包含一个gRPC拦截器，用于自动附加认证Token。
*   **`Database/`**: 封装对**`Microsoft.Data.Sqlite`**的调用，提供一个类似Room的简单ORM或Repository基类。
*   **`Repositories/`**: 实现`CinaClub.Core`中定义的`IRepository`接口。它会调用`Network`层、`Database`层和`GoBridge`层来完成数据操作。

#### `CinaClub.Core/` - 核心业务逻辑与领域层

*   **`Models/`**: 定义纯C#的POCO（Plain Old CLR Object）来表示领域模型，如`User`, `Order`。
*   **`Repositories/`**: 定义仓储**接口**，如`IUserRepository`。
*   **`UseCases/`**: 封装单一业务职责的类，如`LoginUseCase`。它依赖仓储接口。

#### `CinaClub.App/` - 表现层

*   **`Views/`**: **XAML文件**和其code-behind (`.xaml.cs`)。View层非常“薄”，只包含UI布局和到ViewModel的**数据绑定 (Binding)**。
*   **`ViewModels/`**:
    *   继承自一个`BaseViewModel`（实现了`INotifyPropertyChanged`）。
    *   通过**构造函数注入**`UseCase`或`Repository`。
    *   持有UI状态（通过可观察的属性）。
    *   包含响应UI事件的**命令 (Commands)**。
*   **`Navigation/`**: 一个`NavigationService`，实现了Coordinator模式，负责窗口和页面的切换。

---

## 4. 核心功能实现架构

### 4.1 E2EE功能 (PKB, Memory)

流程与iOS/Android完全一致，仅技术实现不同。
1.  **密钥管理**: MEK由用户主密码通过`GoBridge`->`core/crypto`的`DeriveKeyFromPassword`派生。加密后的DEK使用**Windows DPAPI**进行存储，以绑定到当前用户或机器，增加安全性。
2.  **加解密**: `PKBRepository`在读写数据时，调用`GoBridge`中的`CryptoVault.EncryptAsync/DecryptAsync`方法，所有密码学计算都在Go层完成。

### 4.2 本地LLM聊天

1.  **模型管理**: 与移动端类似，由一个C#层的`ModelManager`负责与`model-management-service`通信，下载和管理模型文件。
2.  **推理流程**:
    a. `ChatViewModel`调用`SendMessageUseCase`。
    b. UseCase调用`AICore.PredictStreamAsync(prompt)`（来自`GoBridge`）。
    c. `AICore`的P/Invoke方法调用Go DLL中的`PredictStream`函数，并传递一个**C#委托(delegate)**作为回调函数。
    d. Go层在后台线程中通过CGO调用`llama.cpp`进行推理。
    e. `llama.cpp`每生成一个token，就通过CGO回调到Go层。
    f. Go层再通过**函数指针**调用传入的C#委托，将token传递回C#世界。
    g. C#委托在收到token后，通过`SynchronizationContext`或`DispatcherQueue`安全地更新UI线程上的`ChatViewModel`的`@Published`属性。
    h. WinUI 3的UI通过数据绑定自动更新，呈现打字机效果。

## 5. 总结

本原生Windows应用架构是一个**利用最新.NET技术栈，并与平台Go核心深度集成的现代化解决方案**。

*   **技术现代**: 全面采用WinUI 3, .NET 8, C# 12, MVVM，确保了应用的性能和长期可维护性。
*   **逻辑复用**: 通过**Go (CGO) -> C DLL -> C# (P/Invoke)** 这条黄金路径，将平台最关键的加密、同步和AI逻辑无缝、高效地集成到Windows应用中，保证了全平台的功能一致性。
*   **架构清晰**: 通过多项目解决方案和严格的分层（MVVM + Clean Architecture），有效管理了大型桌面应用的复杂性，使得各部分职责分明，易于开发和测试。
*   **原生优势**: 充分利用了Windows平台的原生能力，如DPAPI安全存储和强大的桌面UI框架，能为用户提供最佳的生产力体验。

这个架构为CINA.CLUB在Windows桌面端的成功奠定了坚实的基础，确保了它不仅是一个功能的简单移植，而是一个真正的一等公民原生应用。