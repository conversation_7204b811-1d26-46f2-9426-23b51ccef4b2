/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React from 'react'
import { BrowserRouter } from 'react-router-dom'
import { ProConfigProvider } from '@ant-design/pro-components'
import { App as AntdApp } from 'antd'

import Router from './router'
import { useAuthStore } from './store/auth'

const App: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore()

  return (
    <ProConfigProvider hashed={false}>
      <AntdApp>
        <BrowserRouter>
          <Router />
        </BrowserRouter>
      </AntdApp>
    </ProConfigProvider>
  )
}

export default App 