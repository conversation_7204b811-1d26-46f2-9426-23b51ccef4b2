﻿好的，遵照您的指示，我们来生成一份为 `chat-websocket-server` (实时聊天WebSocket服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多设备同步**: 明确定义用户在多个设备上同时登录时，消息、已读状态、在线状态的同步逻辑。
2.  **实时事件协议**: 细化WebSocket事件协议，使其更加结构化和可扩展，并使用二进制格式（如Protobuf）以提升性能。
3.  **在线状态(Presence)深化**: 增加对更丰富在线状态（如“忙碌”、“离开”）和“正在输入”指示器的详细描述。
4.  **与`chat-api-service`的交互**: 明确本服务在权限校验、消息持久化等方面与`chat-api-service`的协同方式。
5.  **高可用与可扩展性策略**: 详细阐述如何通过多实例部署、Redis和负载均衡器实现高可用和水平扩展。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个能够支撑海量并发长连接、低延迟、高可靠的实时通信网关。

---

### CINA.CLUB - chat-websocket-server 需求规格说明书

**版本: 2.0 (生产级定义，支持多设备同步与高级实时事件)**  
**发布日期: 2025-06-20**  
**最后修订日期: 2025-06-20**  
**文档负责人:** [实时通信团队负责人/架构师名称]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求: WebSocket事件协议](#5-接口需求-websocket事件协议)
6.  [数据需求 (运行时数据)](#6-数据需求-运行时数据)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台的核心功能之一是提供用户间的即时通讯能力。`chat-websocket-server` 作为实时通信的**骨干网关**，其目的在于提供一个极低延迟、高并发、高可用且可水平扩展的WebSocket服务。它负责处理所有实时的消息传递、用户状态同步和互动事件，是用户感知“实时性”的关键。

#### 1.2. 服务范围
本服务 **负责**:
*   **管理海量并发长连接**: 接受并维护来自客户端的长期WebSocket连接。
*   **连接认证**: 对每个连接进行用户身份认证。
*   **实时消息路由与广播**:
    *   接收客户端发送的聊天消息。
    *   将其近乎实时地广播给目标房间内的**所有在线成员的所有设备**。
*   **消息持久化卸载**: 将收到的消息异步地发布到消息队列，供`chat-api-service`进行持久化。
*   **用户在线状态 (Presence) 管理**: 管理和广播用户的`ONLINE`/`OFFLINE`状态。
*   **实时互动事件广播**: 处理和广播其他实时互动事件，如“正在输入...”和“消息已读回执”。
*   **多设备同步**: 确保用户在一个设备上的操作（如发送消息、标记已读）能实时同步到其所有其他在线设备。

本服务 **不负责**:
*   消息的长期持久化存储 (由 `chat-api-service` 负责)。
*   聊天室的创建与成员管理 (由 `chat-api-service` 负责)。
*   获取聊天历史记录 (由 `chat-api-service` 负责)。
*   发送离线推送通知 (本服务仅在需要时，向`notification-dispatch-service`发布一个“需要推送”的事件)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 唯一的直接交互方，通过WebSocket协议与本服务通信。
*   **`user-core-service`**: (被本服务调用) 验证JWT。
*   **`chat-api-service`**: (被本服务调用) 进行权限校验（如用户是否可发言）。
*   **`notification-dispatch-service`**: (被本服务调用或通过MQ) 触发离线推送。
*   **Redis**: (核心依赖) 用于多实例间的状态同步和消息广播。

#### 1.4. 定义与缩略语
*   **WSS**: WebSocket Secure (over TLS)。
*   **Connection/Socket**: 一个客户端与服务器建立的WebSocket连接。
*   **Presence**: 用户在线状态。
*   **C2S / S2C**: Client-to-Server / Server-to-Client (指WebSocket事件方向)。
*   **Pub/Sub**: 发布/订阅模式。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`chat-websocket-server` 是CINA.CLUB平台的“**神经网络**”。它是一个高性能、有状态（状态主要在Redis中）的服务，专注于处理海量并发长连接和消息的低延迟转发。它与负责“历史与管理”的 `chat-api-service` 形成动静分离，共同构成了完整的聊天解决方案。本服务的性能和稳定性直接决定了平台通信功能的最终用户体验。

#### 2.2. 主要功能概述
*   安全的、基于JWT的WebSocket连接认证。
*   基于Go并发模型的高性能事件处理。
*   基于Redis Pub/Sub实现的多实例可扩展的房间管理和消息广播。
*   精确的多设备状态同步。
*   通过消息队列异步化消息持久化，保证实时性能。

### 3. 核心流程图

#### 3.1. 用户A向房间R发送消息，用户B和用户C在线
```mermaid
sequenceDiagram
    participant ClientA
    participant WSServer_1 as "WS Server 1"
    participant Redis_PubSub as "Redis (Pub/Sub)"
    participant MQ_Kafka as "Kafka/MQ"
    participant WSServer_2 as "WS Server 2"
    participant ClientB
    participant ClientC

    ClientA->>WSServer_1: 1. [C2S_SEND_MESSAGE]
    
    WSServer_1->>WSServer_1: 2. Auth & Permission Check
    
    WSServer_1->>MQ_Kafka: 3. Publish RawChatMessageEvent (for persistence)
    
    WSServer_1->>Redis_PubSub: 4. **PUBLISH** on channel `chat_room:R` with MessagePayload
    
    Redis_PubSub-->>WSServer_1: (Receive own message)
    Redis_PubSub-->>WSServer_2: (Receive message)
    
    WSServer_1->>ClientA: 5a. [S2C_NEW_MESSAGE] (Sync to self other devices)
    
    WSServer_2->>ClientB: 5b. [S2C_NEW_MESSAGE]
    WSServer_2->>ClientC: 5c. [S2C_NEW_MESSAGE]
    
    WSServer_1-->>ClientA: 6. [S2C_MESSAGE_ACK] (Confirm message processed)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 连接管理与认证
*   **FR4.1.1 (连接建立)**: 系统必须能通过HTTP Upgrade请求，建立WSS连接。
*   **FR4.1.2 (认证)**: WebSocket连接建立后，客户端发送的第一个事件必须是`C2S_AUTHENTICATE`，其中包含有效的用户JWT。
    *   系统必须使用公钥验证JWT。认证失败的连接必须立即关闭。
    *   认证成功后，系统必须将`userId`和`deviceId`与该连接的`connectionId`关联，并存储在Redis中。
*   **FR4.1.3 (多设备支持)**: 系统必须支持单个用户通过多个设备同时连接（即一个`userId`对应多个`connectionId`）。
*   **FR4.1.4 (心跳与保活)**: 客户端需定期发送心跳事件（如`C2S_PING`），服务器响应`S2C_PONG`。若在规定时间内未收到心跳，服务器应主动关闭连接。
*   **FR4.1.5 (优雅关闭)**: 系统必须能优雅地处理连接的正常关闭和意外断开，并触发相应的清理逻辑（如更新在线状态）。

#### 4.2. 实时消息处理
*   **FR4.2.1 (接收与校验)**: 系统应能接收来自客户端的`C2S_SEND_MESSAGE`事件。在处理前，必须调用`chat-api-service`的内部接口，验证发送者是否是该房间的成员且未被禁言。
*   **FR4.2.2 (消息持久化卸载)**: 校验通过后，系统必须立即将包含完整信息的消息对象异步地发布到消息队列（如Kafka），供`chat-api-service`消费。**本服务不等待持久化完成**。
*   **FR4.2.3 (实时广播)**: 系统必须将消息通过Redis的Pub/Sub机制，广播到目标房间对应的Channel。所有订阅了该Channel的服务器实例都会收到消息，并将其推送给连接在本实例上的、属于该房间的客户端。
*   **FR4.2.4 (发送方确认)**: 在消息成功发布到MQ和Redis Pub/Sub后，系统应向消息发送方客户端发送一个确认回执 (`S2C_MESSAGE_SENT_ACK`)，其中包含客户端临时ID和服务器生成的`messageId`，用于UI更新。
*   **FR4.2.5 (发送方多设备同步)**: 消息发送者A也应收到自己发送的`S2C_NEW_MESSAGE`事件，以确保其所有设备（如手机和电脑）的聊天记录同步。

#### 4.3. 在线状态 (Presence) 与互动事件
*   **FR4.3.1 (在线状态管理)**:
    *   当用户第一个连接认证成功时，系统更新其在Redis中的状态为`ONLINE`，并向其好友/联系人广播`S2C_USER_PRESENCE_UPDATE`事件。
    *   当用户所有连接都断开后，系统更新其状态为`OFFLINE`，并记录最后在线时间，再次广播状态更新。
*   **FR4.3.2 (“正在输入...” 指示器)**:
    *   接收`C2S_TYPING_INDICATOR`事件。
    *   将`S2C_TYPING_STATUS_UPDATE`事件广播给目标房间内的其他成员。此事件是短暂的，不持久化。
*   **FR4.3.3 (已读回执同步)**:
    *   客户端在一个设备上将消息标记为已读后，会调用`chat-api-service`的API。
    *   `chat-api-service`处理后，可以发布一个`UserReadReceiptUpdatedEvent`。
    *   本服务消费此事件，并将`S2C_READ_RECEIPT_UPDATE`事件广播给该用户的所有**其他在线设备**，以同步已读状态。

#### 4.4. 离线推送协调
*   **FR4.4.1 (离线判断)**: 在广播消息时，对于每个目标接收者，系统需要检查其在线状态。
*   **FR4.4.2 (触发通知)**: 如果目标用户`OFFLINE`，系统必须向`notification-dispatch-service`发布一个`DispatchNotificationCommand`事件，请求发送离线推送。

### 5. 接口需求: WebSocket事件协议

*   **消息格式**: **Protocol Buffers (Protobuf)**。因其高效的二进制序列化、强类型和向后兼容性，是生产级实时通信的首选。
*   **事件信封 (Envelope)**: 所有事件都应包裹在一个信封中。
    ```protobuf
    message WebSocketEvent {
      string event_id = 1; // 客户端生成的请求ID
      oneof payload {
        AuthenticateRequest auth_request = 10;
        SendMessageRequest send_message_request = 11;
        // ...其他C2S事件
        
        NewMessageEvent new_message_event = 100;
        MessageSentAck message_sent_ack = 101;
        // ...其他S2C事件
      }
    }
    ```
*   **核心C2S事件 (Client-to-Server)**:
    *   `AuthenticateRequest { token: string }`
    *   `SendMessageRequest { room_id: string, content: MessageContent, client_temp_id: string, reply_to_message_id?: string }`
    *   `TypingIndicator { room_id: string, is_typing: boolean }`
    *   `Ping {}`
*   **核心S2C事件 (Server-to-Client)**:
    *   `AuthenticationResult { success: boolean, error_message?: string }`
    *   `NewMessageEvent { message: ChatMessage }` (ChatMessage为从`chat-api-service`同步的完整消息结构)
    *   `MessageSentAck { client_temp_id: string, server_message_id: string, server_timestamp: int64 }`
    *   `TypingStatusUpdate { room_id: string, user: { id, name }, is_typing: boolean }`
    *   `ReadReceiptUpdate { room_id: string, last_read_timestamp: int64 }`
    *   `UserPresenceUpdate { user_id: string, status: "ONLINE" | "OFFLINE", last_seen_timestamp?: int64 }`
    *   `Pong {}`

### 6. 数据需求 (运行时数据)

本服务是**无状态**的，但依赖Redis来存储和同步所有**运行时状态**。

*   **`connections:{userId}` (Redis Set)**: 存储一个用户所有在线设备的`connectionId`。
*   **`connection_info:{connectionId}` (Redis Hash)**: 存储一个连接的元数据，如`userId`, `deviceId`, `serverNodeId`。
*   **`user_presence:{userId}` (Redis String or Hash)**: 存储用户的在线状态 (`ONLINE`/`OFFLINE`)。
*   **`room_subscriptions:{roomId}` (Redis Set)**: 存储所有加入了该房间的`userId`。
*   **Redis Pub/Sub Channels**:
    *   `chat_room:{roomId}`: 用于广播指定房间的消息。
    *   `user_notifications:{userId}`: 用于向指定用户的所有设备广播个人事件（如已读同步）。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求 (最高优先级)
*   **消息延迟 (P99)**: < 150ms (从服务器接收到广播给其他在线用户)。
*   **并发连接数 (CCU)**: 单个Go实例（标准云VM配置）应能稳定处理 **50,000+** 的并发WebSocket连接。
*   **消息吞吐量**: > 10,000 消息/秒/实例。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **无单点故障**: 通过多实例部署和Redis集群实现。单个WS服务器实例故障，客户端应能通过负载均衡器快速重连到其他健康实例。
*   **消息不丢失**: 保证将消息成功投递到MQ，实现持久化。

#### 7.3. 可扩展性需求
*   **水平扩展**: 系统必须支持通过增加服务器实例来线性扩展总并发连接数。
*   **负载均衡**: 需要支持WebSocket的粘性会话（Sticky Sessions，基于IP或Cookie）或更高级的负载均衡策略，以优化重连。

#### 7.4. 安全性需求
*   所有连接必须使用**WSS (TLS)**。
*   强制的JWT连接认证，并对JWT进行生命周期检查。
*   对客户端发送的事件进行严格的速率限制，防止消息泛滥攻击。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **WebSocket库**: `gorilla/websocket` 或 `nhooyr.io/websocket` 等经过生产验证的高性能库。
*   **状态管理**: Redis。其高性能的Set、Hash和Pub/Sub功能是本服务实现可扩展性的基石。
*   **解耦**: 与`chat-api-service`通过消息队列解耦，是保证本服务低延迟的关键。

---
这份版本2.0的SRS文档为`chat-websocket-server`构建了一个高性能、高可用、可扩展的实时通信基础设施。它专注于其核心职责——**管理连接和低延迟转发**，并与其他服务清晰地界定了边界和交互方式，是CINA.CLUB平台提供卓越实时体验的保障。