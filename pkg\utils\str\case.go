/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package str provides string manipulation utilities that complement the standard strings package.
package str

import (
	"regexp"
	"strings"
	"unicode"
)

var (
	// camelCaseRegex matches transitions from lowercase to uppercase in camelCase
	camelCaseRegex = regexp.MustCompile(`([a-z])([A-Z])`)

	// snakeCaseRegex matches underscores for splitting snake_case
	snakeCaseRegex = regexp.MustCompile(`_+`)

	// kebabCaseRegex matches dashes for splitting kebab-case
	kebabCaseRegex = regexp.MustCompile(`-+`)

	// wordSeparatorRegex matches various word separators
	wordSeparatorRegex = regexp.MustCompile(`[\s\-_]+`)
)

// ToSnakeCase converts a string to snake_case.
// Supports converting from camelCase, PascalCase, kebab-case, and space-separated words.
//
// Example:
//
//	str.ToSnakeCase("HelloWorld")     // returns "hello_world"
//	str.ToSnakeCase("helloWorld")     // returns "hello_world"
//	str.ToSnakeCase("hello-world")    // returns "hello_world"
//	str.ToSnakeCase("hello world")    // returns "hello_world"
//	str.ToSnakeCase("XMLHttpRequest") // returns "xml_http_request"
func ToSnakeCase(s string) string {
	if s == "" {
		return ""
	}

	// Handle camelCase and PascalCase by inserting underscores before uppercase letters
	s = camelCaseRegex.ReplaceAllString(s, "${1}_${2}")

	// Replace various separators with underscores
	s = wordSeparatorRegex.ReplaceAllString(s, "_")

	// Convert to lowercase
	s = strings.ToLower(s)

	// Clean up multiple underscores
	s = regexp.MustCompile(`_+`).ReplaceAllString(s, "_")

	// Remove leading and trailing underscores
	s = strings.Trim(s, "_")

	return s
}

// ToCamelCase converts a string to camelCase.
// Supports converting from snake_case, kebab-case, PascalCase, and space-separated words.
//
// Example:
//
//	str.ToCamelCase("hello_world")    // returns "helloWorld"
//	str.ToCamelCase("hello-world")    // returns "helloWorld"
//	str.ToCamelCase("hello world")    // returns "helloWorld"
//	str.ToCamelCase("HelloWorld")     // returns "helloWorld"
func ToCamelCase(s string) string {
	if s == "" {
		return ""
	}

	// Split by various separators
	words := wordSeparatorRegex.Split(s, -1)

	// Filter out empty strings
	var filteredWords []string
	for _, word := range words {
		if word != "" {
			filteredWords = append(filteredWords, word)
		}
	}

	if len(filteredWords) == 0 {
		return ""
	}

	// First word is lowercase, subsequent words are title case
	result := strings.ToLower(filteredWords[0])
	for i := 1; i < len(filteredWords); i++ {
		result += strings.Title(strings.ToLower(filteredWords[i]))
	}

	return result
}

// ToPascalCase converts a string to PascalCase.
// Supports converting from snake_case, kebab-case, camelCase, and space-separated words.
//
// Example:
//
//	str.ToPascalCase("hello_world")   // returns "HelloWorld"
//	str.ToPascalCase("hello-world")   // returns "HelloWorld"
//	str.ToPascalCase("hello world")   // returns "HelloWorld"
//	str.ToPascalCase("helloWorld")    // returns "HelloWorld"
func ToPascalCase(s string) string {
	if s == "" {
		return ""
	}

	// Split by various separators
	words := wordSeparatorRegex.Split(s, -1)

	// Filter out empty strings and title case each word
	var result strings.Builder
	for _, word := range words {
		if word != "" {
			result.WriteString(strings.Title(strings.ToLower(word)))
		}
	}

	return result.String()
}

// ToKebabCase converts a string to kebab-case.
// Supports converting from camelCase, PascalCase, snake_case, and space-separated words.
//
// Example:
//
//	str.ToKebabCase("HelloWorld")     // returns "hello-world"
//	str.ToKebabCase("helloWorld")     // returns "hello-world"
//	str.ToKebabCase("hello_world")    // returns "hello-world"
//	str.ToKebabCase("hello world")    // returns "hello-world"
func ToKebabCase(s string) string {
	if s == "" {
		return ""
	}

	// Handle camelCase and PascalCase by inserting dashes before uppercase letters
	s = camelCaseRegex.ReplaceAllString(s, "${1}-${2}")

	// Replace various separators with dashes
	s = wordSeparatorRegex.ReplaceAllString(s, "-")

	// Convert to lowercase
	s = strings.ToLower(s)

	// Clean up multiple dashes
	s = regexp.MustCompile(`-+`).ReplaceAllString(s, "-")

	// Remove leading and trailing dashes
	s = strings.Trim(s, "-")

	return s
}

// ToTitleCase converts a string to Title Case.
// Each word is capitalized, and words are separated by spaces.
//
// Example:
//
//	str.ToTitleCase("hello_world")    // returns "Hello World"
//	str.ToTitleCase("hello-world")    // returns "Hello World"
//	str.ToTitleCase("helloWorld")     // returns "Hello World"
func ToTitleCase(s string) string {
	if s == "" {
		return ""
	}

	// Handle camelCase and PascalCase by inserting spaces before uppercase letters
	s = camelCaseRegex.ReplaceAllString(s, "${1} ${2}")

	// Replace various separators with spaces
	s = wordSeparatorRegex.ReplaceAllString(s, " ")

	// Split into words and title case each
	words := strings.Fields(s)
	for i, word := range words {
		words[i] = strings.Title(strings.ToLower(word))
	}

	return strings.Join(words, " ")
}

// IsSnakeCase checks if a string is in snake_case format.
//
// Example:
//
//	str.IsSnakeCase("hello_world")    // returns true
//	str.IsSnakeCase("helloWorld")     // returns false
func IsSnakeCase(s string) bool {
	if s == "" {
		return true
	}

	// Check if it contains only lowercase letters, digits, and underscores
	for _, r := range s {
		if !unicode.IsLower(r) && !unicode.IsDigit(r) && r != '_' {
			return false
		}
	}

	// Check if it doesn't start or end with underscore
	if strings.HasPrefix(s, "_") || strings.HasSuffix(s, "_") {
		return false
	}

	// Check if it doesn't have consecutive underscores
	if strings.Contains(s, "__") {
		return false
	}

	return true
}

// IsCamelCase checks if a string is in camelCase format.
//
// Example:
//
//	str.IsCamelCase("helloWorld")     // returns true
//	str.IsCamelCase("HelloWorld")     // returns false (this is PascalCase)
//	str.IsCamelCase("hello_world")    // returns false
func IsCamelCase(s string) bool {
	if s == "" {
		return true
	}

	// Must start with lowercase letter
	if !unicode.IsLower(rune(s[0])) {
		return false
	}

	// Check if it contains only letters and digits, no separators
	for _, r := range s {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) {
			return false
		}
	}

	return true
}

// IsPascalCase checks if a string is in PascalCase format.
//
// Example:
//
//	str.IsPascalCase("HelloWorld")    // returns true
//	str.IsPascalCase("helloWorld")    // returns false (this is camelCase)
//	str.IsPascalCase("hello_world")   // returns false
func IsPascalCase(s string) bool {
	if s == "" {
		return true
	}

	// Must start with uppercase letter
	if !unicode.IsUpper(rune(s[0])) {
		return false
	}

	// Check if it contains only letters and digits, no separators
	for _, r := range s {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) {
			return false
		}
	}

	return true
}

// IsKebabCase checks if a string is in kebab-case format.
//
// Example:
//
//	str.IsKebabCase("hello-world")    // returns true
//	str.IsKebabCase("helloWorld")     // returns false
func IsKebabCase(s string) bool {
	if s == "" {
		return true
	}

	// Check if it contains only lowercase letters, digits, and dashes
	for _, r := range s {
		if !unicode.IsLower(r) && !unicode.IsDigit(r) && r != '-' {
			return false
		}
	}

	// Check if it doesn't start or end with dash
	if strings.HasPrefix(s, "-") || strings.HasSuffix(s, "-") {
		return false
	}

	// Check if it doesn't have consecutive dashes
	if strings.Contains(s, "--") {
		return false
	}

	return true
}
