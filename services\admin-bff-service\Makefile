# Admin BFF Service Makefile

.PHONY: help build run test clean docker-build docker-run lint fmt deps

# 默认目标
help:
	@echo "Available commands:"
	@echo "  build              - Build the application"
	@echo "  run                - Run the application locally"
	@echo "  test               - Run tests"
	@echo "  clean              - Clean build artifacts"
	@echo "  docker-build       - Build Docker image"
	@echo "  docker-run         - Run Docker container"
	@echo "  lint               - Run linter"
	@echo "  fmt                - Format code"
	@echo "  deps               - Download dependencies"

# 应用程序配置
APP_NAME := admin-bff-service
BINARY_NAME := admin-bff-service
DOCKER_IMAGE := $(APP_NAME):latest

# Go配置
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

# 构建目录
BUILD_DIR := ./bin
MAIN_PATH := ./cmd/server

# 编译应用
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -a -installsuffix cgo \
		-ldflags '-extldflags "-static"' \
		-o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "Build completed: $(BUILD_DIR)/$(BINARY_NAME)"

# 本地运行
run:
	@echo "Running $(APP_NAME)..."
	$(GOCMD) run $(MAIN_PATH)/main.go

# 运行测试
test:
	@echo "Running tests..."
	$(GOTEST) -v -race -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 清理构建产物
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 构建Docker镜像
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .
	@echo "Docker image built: $(DOCKER_IMAGE)"

# 运行Docker容器
docker-run: docker-build
	@echo "Running Docker container..."
	docker run -d \
		--name $(APP_NAME) \
		-p 8080:8080 \
		-e REDIS_ADDRESS=localhost:6379 \
		-e LOG_LEVEL=debug \
		$(DOCKER_IMAGE)

# 代码检查
lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run ./...; \
	else \
		echo "golangci-lint not found. Install it with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
		exit 1; \
	fi

# 格式化代码
fmt:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...
	@if command -v goimports >/dev/null 2>&1; then \
		goimports -w .; \
	else \
		echo "goimports not found. Install it with: go install golang.org/x/tools/cmd/goimports@latest"; \
	fi

# 下载依赖
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 检查服务健康状态
health:
	@echo "Checking service health..."
	@curl -f http://localhost:8080/health || echo "Service is not running"

# 开发环境设置
dev-setup:
	@echo "Setting up development environment..."
	@$(MAKE) deps
	@echo "Installing development tools..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install golang.org/x/tools/cmd/goimports@latest
	@echo "Development environment setup completed!"

# 生产环境构建
prod-build:
	@echo "Building for production..."
	@$(MAKE) clean
	@$(MAKE) test
	@$(MAKE) lint
	@$(MAKE) build
	@$(MAKE) docker-build
	@echo "Production build completed!" 