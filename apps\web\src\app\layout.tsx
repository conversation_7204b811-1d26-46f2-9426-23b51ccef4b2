import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'CINA.CLUB',
  description: 'CINA.CLUB - Your Digital Life Platform',
  keywords: ['social', 'platform', 'community', 'digital life'],
  authors: [{ name: 'CINA.CLUB Team' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50 text-gray-900`}>
        <div className="min-h-full">
          <header className="bg-white shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-4">
                <div className="flex items-center">
                  <h1 className="text-2xl font-bold text-primary-600">
                    CINA.CLUB
                  </h1>
                </div>
                <nav className="hidden md:flex space-x-8">
                  <a href="/" className="text-gray-600 hover:text-primary-600 transition-colors">
                    Home
                  </a>
                  <a href="/about" className="text-gray-600 hover:text-primary-600 transition-colors">
                    About
                  </a>
                  <a href="/contact" className="text-gray-600 hover:text-primary-600 transition-colors">
                    Contact
                  </a>
                </nav>
                <div className="flex items-center space-x-4">
                  <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Sign In
                  </button>
                </div>
              </div>
            </div>
          </header>
          
          <main className="flex-1">
            {children}
          </main>
          
          <footer className="bg-gray-800 text-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="text-center">
                <p>&copy; 2025 CINA.CLUB. All rights reserved.</p>
                <p className="mt-2 text-gray-400">
                  Building the future of digital communities.
                </p>
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
} 