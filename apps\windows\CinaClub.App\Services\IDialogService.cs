/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System.Threading.Tasks;
using Microsoft.UI.Xaml.Controls;

namespace CinaClub.App.Services;

/// <summary>
/// 对话框服务接口
/// 提供各种类型的对话框显示功能
/// </summary>
public interface IDialogService
{
    /// <summary>
    /// 显示信息对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <returns>异步任务</returns>
    Task ShowInfoAsync(string title, string message);

    /// <summary>
    /// 显示警告对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <returns>异步任务</returns>
    Task ShowWarningAsync(string title, string message);

    /// <summary>
    /// 显示错误对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <returns>异步任务</returns>
    Task ShowErrorAsync(string title, string message);

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <param name="primaryButtonText">主按钮文本（默认"确定"）</param>
    /// <param name="secondaryButtonText">次按钮文本（默认"取消"）</param>
    /// <returns>用户选择结果，true表示确定，false表示取消</returns>
    Task<bool> ShowConfirmAsync(string title, string message, 
        string primaryButtonText = "确定", string secondaryButtonText = "取消");

    /// <summary>
    /// 显示输入对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">提示消息</param>
    /// <param name="placeholder">输入框占位符</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>用户输入的值，如果取消则返回null</returns>
    Task<string?> ShowInputAsync(string title, string message, 
        string placeholder = "", string defaultValue = "");

    /// <summary>
    /// 显示自定义内容对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="content">自定义内容</param>
    /// <param name="primaryButtonText">主按钮文本</param>
    /// <param name="secondaryButtonText">次按钮文本</param>
    /// <param name="closeButtonText">关闭按钮文本</param>
    /// <returns>对话框结果</returns>
    Task<ContentDialogResult> ShowCustomAsync(string title, object content,
        string? primaryButtonText = null, string? secondaryButtonText = null, 
        string? closeButtonText = "关闭");

    /// <summary>
    /// 显示加载对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">加载消息</param>
    /// <returns>对话框控制器，可用于更新消息或关闭对话框</returns>
    Task<ILoadingDialogController> ShowLoadingAsync(string title, string message);
}

/// <summary>
/// 加载对话框控制器接口
/// </summary>
public interface ILoadingDialogController
{
    /// <summary>
    /// 更新加载消息
    /// </summary>
    /// <param name="message">新的消息</param>
    Task UpdateMessageAsync(string message);

    /// <summary>
    /// 更新进度（0-100）
    /// </summary>
    /// <param name="progress">进度值</param>
    Task UpdateProgressAsync(double progress);

    /// <summary>
    /// 关闭加载对话框
    /// </summary>
    Task CloseAsync();
} 