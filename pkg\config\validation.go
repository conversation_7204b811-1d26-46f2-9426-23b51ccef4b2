/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:30:00
Modified: 2025-01-21 10:30:00
*/

package config

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"sync"

	"github.com/go-playground/validator/v10"
)

var (
	// Global validator instance
	validate *validator.Validate
	once     sync.Once
)

// getValidator returns the singleton validator instance
func getValidator() *validator.Validate {
	once.Do(func() {
		validate = validator.New()
		registerCustomValidations()
	})
	return validate
}

// validateStruct validates a struct using the global validator
func validateStruct(s interface{}) error {
	return getValidator().Struct(s)
}

// ValidateStruct validates a struct using the global validator (public interface)
func ValidateStruct(s interface{}) error {
	return validateStruct(s)
}

// AddCustomValidation adds a custom validation rule
func AddCustomValidation(tag string, fn validator.Func) error {
	return getValidator().RegisterValidation(tag, fn)
}

// registerCustomValidations registers all custom validation rules
func registerCustomValidations() {
	// DSN validation for database connection strings
	validate.RegisterValidation("dsn", validateDSN)
	
	// Service mode validation (development, staging, production)
	validate.RegisterValidation("servicemode", validateServiceMode)
	
	// Log level validation (debug, info, warn, error)
	validate.RegisterValidation("loglevel", validateLogLevel)
}

// validateDSN validates database connection strings
func validateDSN(fl validator.FieldLevel) bool {
	dsn := fl.Field().String()
	if dsn == "" {
		return false
	}

	// Basic URL parsing check
	u, err := url.Parse(dsn)
	if err != nil {
		return false
	}

	// Must have a scheme (postgres, mysql, etc.)
	if u.Scheme == "" {
		return false
	}

	// Must have a host
	if u.Host == "" {
		return false
	}

	return true
}

// validateServiceMode validates service deployment modes
func validateServiceMode(fl validator.FieldLevel) bool {
	mode := strings.ToLower(fl.Field().String())
	validModes := []string{"development", "staging", "production", "test"}
	
	for _, validMode := range validModes {
		if mode == validMode {
			return true
		}
	}
	
	return false
}

// validateLogLevel validates logging levels
func validateLogLevel(fl validator.FieldLevel) bool {
	level := strings.ToLower(fl.Field().String())
	validLevels := []string{"debug", "info", "warn", "warning", "error", "fatal", "panic"}
	
	for _, validLevel := range validLevels {
		if level == validLevel {
			return true
		}
	}
	
	return false
}

// ValidationError represents a configuration validation error
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return "validation failed"
	}
	
	var messages []string
	for _, err := range ve {
		messages = append(messages, err.Message)
	}
	
	return fmt.Sprintf("validation failed: %s", strings.Join(messages, "; "))
}

// FormatValidationError formats validator errors into a more readable format
func FormatValidationError(err error) error {
	if err == nil {
		return nil
	}

	// Check if it's already a ValidationErrors
	if ve, ok := err.(ValidationErrors); ok {
		return ve
	}

	// Check if it's validator.ValidationErrors
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var formattedErrors ValidationErrors
		
		for _, fieldError := range validationErrors {
			formattedError := ValidationError{
				Field: fieldError.Field(),
				Tag:   fieldError.Tag(),
				Value: fmt.Sprintf("%v", fieldError.Value()),
			}
			
			// Generate human-readable error messages
			switch fieldError.Tag() {
			case "required":
				formattedError.Message = fmt.Sprintf("field '%s' is required", fieldError.Field())
			case "min":
				formattedError.Message = fmt.Sprintf("field '%s' must be at least %s", fieldError.Field(), fieldError.Param())
			case "max":
				formattedError.Message = fmt.Sprintf("field '%s' must be at most %s", fieldError.Field(), fieldError.Param())
			case "gte":
				formattedError.Message = fmt.Sprintf("field '%s' must be greater than or equal to %s", fieldError.Field(), fieldError.Param())
			case "lte":
				formattedError.Message = fmt.Sprintf("field '%s' must be less than or equal to %s", fieldError.Field(), fieldError.Param())
			case "oneof":
				formattedError.Message = fmt.Sprintf("field '%s' must be one of: %s", fieldError.Field(), fieldError.Param())
			case "dsn":
				formattedError.Message = fmt.Sprintf("field '%s' must be a valid database connection string", fieldError.Field())
			case "servicemode":
				formattedError.Message = fmt.Sprintf("field '%s' must be one of: development, staging, production, test", fieldError.Field())
			case "loglevel":
				formattedError.Message = fmt.Sprintf("field '%s' must be a valid log level (debug, info, warn, error, fatal, panic)", fieldError.Field())
			default:
				formattedError.Message = fmt.Sprintf("field '%s' failed validation for tag '%s'", fieldError.Field(), fieldError.Tag())
			}
			
			formattedErrors = append(formattedErrors, formattedError)
		}
		
		return formattedErrors
	}

	// Return original error if not a validation error
	return err
}

// ValidateAndFormat validates a struct and returns formatted errors
func ValidateAndFormat(s interface{}) error {
	err := ValidateStruct(s)
	return FormatValidationError(err)
}

// RegisterEmailValidation registers email validation
func RegisterEmailValidation() error {
	return AddCustomValidation("email", func(fl validator.FieldLevel) bool {
		email := fl.Field().String()
		// Simple email regex pattern
		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		return emailRegex.MatchString(email)
	})
}

// RegisterURLValidation registers URL validation
func RegisterURLValidation() error {
	return AddCustomValidation("url", func(fl validator.FieldLevel) bool {
		urlStr := fl.Field().String()
		_, err := url.ParseRequestURI(urlStr)
		return err == nil
	})
} 