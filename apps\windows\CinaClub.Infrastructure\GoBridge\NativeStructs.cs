/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System;
using System.Runtime.InteropServices;

namespace CinaClub.Infrastructure.GoBridge;

/// <summary>
/// Go slice结构体 - 用于与Go代码交互的字节数组
/// </summary>
[StructLayout(LayoutKind.Sequential)]
public struct GoSlice
{
    /// <summary>
    /// 数据指针
    /// </summary>
    public IntPtr Data;

    /// <summary>
    /// 长度
    /// </summary>
    public long Length;

    /// <summary>
    /// 容量
    /// </summary>
    public long Capacity;

    /// <summary>
    /// 从.NET字节数组创建GoSlice
    /// </summary>
    /// <param name="data">字节数组</param>
    /// <returns>GoSlice结构体</returns>
    public static GoSlice FromByteArray(byte[] data)
    {
        if (data == null || data.Length == 0)
        {
            return new GoSlice { Data = IntPtr.Zero, Length = 0, Capacity = 0 };
        }

        var ptr = Marshal.AllocHGlobal(data.Length);
        Marshal.Copy(data, 0, ptr, data.Length);

        return new GoSlice
        {
            Data = ptr,
            Length = data.Length,
            Capacity = data.Length
        };
    }

    /// <summary>
    /// 转换为.NET字节数组
    /// </summary>
    /// <returns>字节数组</returns>
    public byte[] ToByteArray()
    {
        if (Data == IntPtr.Zero || Length == 0)
        {
            return Array.Empty<byte>();
        }

        var result = new byte[Length];
        Marshal.Copy(Data, result, 0, (int)Length);
        return result;
    }

    /// <summary>
    /// 释放非托管内存
    /// </summary>
    public void Free()
    {
        if (Data != IntPtr.Zero)
        {
            Marshal.FreeHGlobal(Data);
            Data = IntPtr.Zero;
            Length = 0;
            Capacity = 0;
        }
    }
}

/// <summary>
/// Go string结构体
/// </summary>
[StructLayout(LayoutKind.Sequential)]
public struct GoString
{
    /// <summary>
    /// 字符串数据指针
    /// </summary>
    public IntPtr Data;

    /// <summary>
    /// 字符串长度
    /// </summary>
    public long Length;

    /// <summary>
    /// 从.NET字符串创建GoString
    /// </summary>
    /// <param name="str">字符串</param>
    /// <returns>GoString结构体</returns>
    public static GoString FromString(string str)
    {
        if (string.IsNullOrEmpty(str))
        {
            return new GoString { Data = IntPtr.Zero, Length = 0 };
        }

        var bytes = System.Text.Encoding.UTF8.GetBytes(str);
        var ptr = Marshal.AllocHGlobal(bytes.Length);
        Marshal.Copy(bytes, 0, ptr, bytes.Length);

        return new GoString
        {
            Data = ptr,
            Length = bytes.Length
        };
    }

    /// <summary>
    /// 转换为.NET字符串
    /// </summary>
    /// <returns>字符串</returns>
    public override string ToString()
    {
        if (Data == IntPtr.Zero || Length == 0)
        {
            return string.Empty;
        }

        var bytes = new byte[Length];
        Marshal.Copy(Data, bytes, 0, (int)Length);
        return System.Text.Encoding.UTF8.GetString(bytes);
    }

    /// <summary>
    /// 释放非托管内存
    /// </summary>
    public void Free()
    {
        if (Data != IntPtr.Zero)
        {
            Marshal.FreeHGlobal(Data);
            Data = IntPtr.Zero;
            Length = 0;
        }
    }
}

/// <summary>
/// Go错误结构体
/// </summary>
[StructLayout(LayoutKind.Sequential)]
public struct GoError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public int Code;

    /// <summary>
    /// 错误消息
    /// </summary>
    public GoString Message;

    /// <summary>
    /// 是否有错误
    /// </summary>
    public bool HasError => Code != 0;

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Free()
    {
        Message.Free();
    }
}

/// <summary>
/// 加密结果结构体
/// </summary>
[StructLayout(LayoutKind.Sequential)]
public struct CryptoResult
{
    /// <summary>
    /// 加密/解密后的数据
    /// </summary>
    public GoSlice Data;

    /// <summary>
    /// 错误信息
    /// </summary>
    public GoError Error;

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Free()
    {
        Data.Free();
        Error.Free();
    }
}

/// <summary>
/// AI推理结果结构体
/// </summary>
[StructLayout(LayoutKind.Sequential)]
public struct AIResult
{
    /// <summary>
    /// 生成的文本
    /// </summary>
    public GoString Text;

    /// <summary>
    /// 是否完成
    /// </summary>
    public bool IsFinished;

    /// <summary>
    /// 错误信息
    /// </summary>
    public GoError Error;

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Free()
    {
        Text.Free();
        Error.Free();
    }
}

/// <summary>
/// 回调函数类型定义
/// </summary>
public delegate void TokenCallback(GoString token);

/// <summary>
/// 进度回调函数类型定义
/// </summary>
public delegate void ProgressCallback(float progress, GoString status); 