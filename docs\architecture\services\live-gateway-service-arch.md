好的，遵照您的指示。我将为您生成一份专门针对 **`live-gateway-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`live-gateway-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**作为媒体服务器代理的适配器模式、实时回调处理的可靠性与幂等性、与`live-api-service`的清晰职责划分，以及作为一个无状态网关的高性能和高可用性设计**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `live-gateway-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `live-gateway-service-srs.md` (v1.0)
**核心架构**: 整洁架构 + 适配器模式(Adapter Pattern) + 代理模式(Proxy)

## 1. 概述

`live-gateway-service` 是CINA.CLUB直播生态的“**物理层网关**”和“**媒体服务器大管家**”。它是一个轻量级、高性能、通常无状态的代理服务。其架构设计的核心目标是：
1.  **抽象与解耦**: 将底层具体、异构的流媒体服务器（如SRS）的API和回调机制，抽象成一套统一、稳定的内部接口，对上层业务服务（`live-api-service`）完全透明。
2.  **实时鉴权**: 为所有媒体流的接入提供一个可靠、低延迟的安全验证点。
3.  **可靠的回调处理**: 确保来自媒体服务器的每一个“物理状态”变更事件都能被准确捕获，并转换为有业务含义的指令通知给上游。
4.  **高可用与性能**: 作为一个关键路径上的服务，必须具备高可用性，且其引入的延迟必须控制在最低水平。

本架构设计通过采用**整洁架构**，并以**适配器模式**为核心，将与具体媒体服务器的交互逻辑完全封装起来。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (回调处理与API代理)

```mermaid
graph TD
    subgraph "上游业务服务"
        LAS[live-api-service]
    end
    
    subgraph "底层媒体服务器"
        MS[SRS/LiveKit Cluster]
    end

    subgraph "LiveGatewayService"
        style LiveGatewayService fill:#e0f7fa
        A[gRPC API Layer<br/><em>adapter/transport/grpc</em>]
        B[HTTP Webhook Layer<br/><em>adapter/transport/http</em>]
        C[Application Service<br/><em>application/service</em>]
        D{MediaServerAdapter<br/><em>(interface)</em>}
        E[SRSAdapter<br/><em>adapter/provider</em>]
        F[LASClient (gRPC)<br/><em>adapter/client</em>]
        G[Redis Store<br/><em>adapter/cache</em>]
    end

    %% API Flow
    LAS -- "1. RequestPushURL" --> A
    A -- "调用" --> C
    C -- "2. Generate stream_key, token" --> C
    C -- "3. Store mapping in Redis" --> G
    C -- "4. Construct URL" --> C
    C -- "返回" --> A --> LAS

    %% Webhook Flow
    MS -- "5. [HTTP Callback] on_publish" --> B
    B -- "6. Validate & call" --> C
    C -- "7. ✨ Auth via gRPC ✨" --> F
    F -- "CheckPushAuth" --> LAS
    LAS -- "Auth Result" --> F
    C -- "8. Return 200 OK to" --> B
    B --> MS
    
    C -- "9. [Async] Notify LAS of event" --> F
    F -- "NotifyStreamPublished" --> LAS
```

### 2.2 最终目录结构 (`services/live-gateway-service/`)

```
live-gateway-service/
├── cmd/server/
│   └── main.go                 # 统一启动入口 (gRPC和HTTP服务器)
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_store.go  # 存储stream_key到roomId的映射
│   │   ├── client/
│   │   │   └── live_api_client.go # 调用live-api-service
│   │   ├── provider/           # ✨ 媒体服务器适配器实现 ✨
│   │   │   ├── interface.go    # 定义MediaServerAdapter接口
│   │   │   └── srs_adapter.go
│   │   └── transport/
│   │       ├── grpc/
│   │       │   └── handler.go  # 实现对live-api-service的内部gRPC API
│   │       └── http/
│   │           └── srs_webhook_handler.go # ✨ 实现对SRS的HTTP回调接口 ✨
│   ├── application/
│   │   ├── port/
│   │   │   ├── cache.go
│   │   │   ├── provider.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── gateway_service.go # 核心应用服务实现
│   └── domain/
│       └── model/
│           └── alias.go
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Gateway's Core Concepts)

BFF/Gateway类型服务的领域层通常比较“薄”，因为它主要负责代理和转换，不包含复杂的业务规则。

*   `domain/model/`: 定义`Stream`, `PushURL`, `PlayURL`等核心数据结构。这些结构是平台内部对媒体流的统一表示，与具体提供商无关。
*   例如，`Stream` `struct`可能包含`RoomID`, `UserID`, `StreamKey`, `Status`等。

### 3.2 `application/` - 应用层 (The Orchestrator)

*   **`application/service/gateway_service.go`**: **这是所有流程的编排者**。
    *   **`GatewayService` struct**: 注入`Repository`, `Cache`, `MediaServerAdapter`, `LiveAPIClient`等所有依赖。
    *   **`RequestPushURL(ctx, roomID, userID)`**:
        1.  生成一个唯一的、安全的`stream_key`和`auth_token`。
        2.  调用`cache.StoreStreamKeyMapping(streamKey, roomID, userID)`，将映射关系存入Redis，并设置一个合理的TTL（如直播预计最大时长）。
        3.  使用`MediaServerAdapter`的方法，根据模板和生成的key/token，构造一个完整的RTMP/SRT推流地址。
        4.  返回地址。
    *   **`HandlePublishWebhook(ctx, streamKey, token)`**: **这是性能和可靠性的关键路径**。
        1.  **同步鉴权**:
            a. 调用`liveAPIClient.CheckPushAuthentication(streamKey, token)`。
            b. **这是一个同步阻塞调用**，因为媒体服务器正在等待响应。
            c. 根据`live-api-service`返回的结果，直接向上层(http handler)返回`allow=true`或`allow=false`。
        2.  **异步通知**:
            a. 如果鉴权成功，**启动一个新的goroutine**（或发送到内部的轻量级任务队列），来执行后续的通知操作。
            b. 在这个goroutine中，调用`liveAPIClient.NotifyStreamPublished(...)`。
        *   **设计决策**: 将“鉴权决策”（必须同步）和“状态通知”（可以异步）分离，确保了对媒体服务器回调的**毫秒级响应**。
    *   **`HandleUnpublishWebhook(ctx, streamKey)`**:
        *   从`cache`中获取`roomID`。
        *   **异步地**调用`liveAPIClient.NotifyStreamInterrupted(roomID)`。

### 3.3 `adapter/` - 适配层 (The Bridge to the World)

*   **`adapter/provider/`**: **适配器模式，封装与具体媒体服务器的交互**。
    *   `interface.go`: 定义`MediaServerAdapter`接口。
        ```go
        type MediaServerAdapter interface {
            // 从Webhook请求中解析出streamKey和token
            ParsePublishRequest(req *http.Request) (streamKey, token string, err error)
            // 响应给媒体服务器的成功/失败格式
            RespondAuthSuccess(w http.ResponseWriter)
            RespondAuthFailure(w http.ResponseWriter)
            // 调用媒体服务器的管理API
            KickStream(ctx, streamKey) error
        }
        ```
    *   `srs_adapter.go`: 实现`MediaServerAdapter`接口，专门处理SRS的HTTP API和回调格式。例如，SRS要求成功时HTTP body返回`0`。
*   **`adapter/transport/http/srs_webhook_handler.go`**:
    *   **职责**:
        1.  接收所有来自SRS的回调请求（`/hooks/srs/on_publish`, ...）。
        2.  **安全验证**: 检查请求来源IP是否在白名单内，或验证一个共享的`secret`。
        3.  **调用`MediaServerAdapter`**来解析请求，获取`streamKey`等参数。
        4.  调用`application.GatewayService`的对应方法来处理。
        5.  **调用`MediaServerAdapter`**来向SRS返回格式正确的响应。
*   **`adapter/cache/redis_store.go`**:
    *   实现`port.Cache`接口，使用Redis来存储临时的`stream_key` -> `room_id`映射。
*   **`adapter/client/live_api_client.go`**:
    *   封装对`live-api-service`所有内部gRPC接口的调用。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`live-gateway-service`：
1.  **清晰的代理职责**: 本服务被设计为一个**纯粹的、轻量级的代理和适配器**。它不包含任何复杂的业务状态，只负责在“业务世界”（`live-api-service`）和“物理世界”（媒体服务器）之间进行安全的“翻译”和“传达”。
2.  **适配器模式隔离技术细节**: 通过`MediaServerAdapter`，将与具体媒体服务器（如SRS）的所有交互细节（API调用、回调格式）完全封装起来。未来如果需要更换媒体服务器（如换成LiveKit），只需要实现一个新的适配器，上层应用代码无需任何改动。
3.  **性能优先的回调处理**: 明确区分了Webhook处理中的**同步（鉴权）**和**异步（通知）**部分，确保了对媒体服务器的响应延迟被控制在最低水平，这是保证直播系统稳定性的关键。
4.  **无状态设计**: 服务本身是无状态的，所有需要的临时状态（如`stream_key`映射）都存储在Redis中，使其可以轻松地水平扩展和实现高可用。
5.  **安全性**: 通过Webhook的来源验证和为推拉流地址动态生成带签名的token，构成了媒体流接入和分发的安全屏障。

这种架构确保了`live-gateway-service`能够作为一个**高性能、高可用、可灵活替换底层实现**的媒体网关，为CINA.CLUB的直播业务提供坚实、可靠的技术支撑。