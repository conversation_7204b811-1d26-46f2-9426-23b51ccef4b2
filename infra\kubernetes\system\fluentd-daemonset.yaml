 # Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

apiVersion: v1
kind: Namespace
metadata:
  name: logging
  labels:
    app.kubernetes.io/name: logging
    app.kubernetes.io/component: system

# Security configuration moved to secrets/elasticsearch-credentials.yaml

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: logging
  labels:
    app.kubernetes.io/name: fluentd
    app.kubernetes.io/component: log-collector
data:
  fluent.conf: |
    <source>
      @type tail
      @id in_tail_container_logs
      path /var/log/containers/*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      read_from_head true
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
      </parse>
    </source>

    <filter kubernetes.**>
      @type kubernetes_metadata
    </filter>

    <match kubernetes.**>
      @type elasticsearch
      host "#{ENV['ELASTICSEARCH_HOST']}"
      port "#{ENV['ELASTICSEARCH_PORT']}"
      scheme "#{ENV['ELASTICSEARCH_SCHEME'] || 'http'}"
      ssl_verify "#{ENV['ELASTICSEARCH_SSL_VERIFY'] || 'true'}"
      user "#{ENV['ELASTICSEARCH_USER']}"
      password "#{ENV['ELASTICSEARCH_PASSWORD']}"
      index_name "#{ENV['ELASTICSEARCH_INDEX_NAME'] || 'logstash'}"
      type_name fluentd
      include_tag_key true
      tag_key @log_name
      flush_interval 1s
    </match>

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluentd
  namespace: logging
  labels:
    app.kubernetes.io/name: fluentd
    app.kubernetes.io/component: log-collector
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: fluentd
      app.kubernetes.io/component: log-collector
  template:
    metadata:
      labels:
        app.kubernetes.io/name: fluentd
        app.kubernetes.io/component: log-collector
    spec:
      serviceAccountName: fluentd-secure
      securityContext:
        runAsNonRoot: true
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
        seccompProfile:
          type: RuntimeDefault
      tolerations:
      - key: node-role.kubernetes.io/master
        effect: NoSchedule
      containers:
      - name: fluentd
        image: fluent/fluentd-kubernetes-daemonset:v1.16-debian-elasticsearch7-1
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false  # Fluentd needs to write to its buffer directory
          capabilities:
            drop:
              - ALL
        env:
        - name: ELASTICSEARCH_HOST
          value: "elasticsearch.logging.svc.cluster.local"
        - name: ELASTICSEARCH_PORT
          value: "9200"
        - name: ELASTICSEARCH_SCHEME
          value: "http"
        - name: ELASTICSEARCH_USER
          valueFrom:
            secretKeyRef:
              name: elasticsearch-credentials
              key: username
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elasticsearch-credentials
              key: password
        - name: ELASTICSEARCH_INDEX_NAME
          value: "cina-club-logs"
        resources:
          limits:
            memory: 512Mi
            cpu: 200m
          requests:
            cpu: 100m
            memory: 256Mi
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: fluentd-config
          mountPath: /fluentd/etc
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: fluentd-config
        configMap:
          name: fluentd-config