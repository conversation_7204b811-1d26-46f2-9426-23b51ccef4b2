// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"cina.club/services/analytics-service/internal/application/service"
)

// UserAnalyticsHandler 鐢ㄦ埛鍒嗘瀽HTTP澶勭悊鍣?
type UserAnalyticsHandler struct {
	userAnalyticsService *service.UserAnalyticsService
}

// NewUserAnalyticsHandler 鍒涘缓鐢ㄦ埛鍒嗘瀽澶勭悊鍣?
func NewUserAnalyticsHandler(userAnalyticsService *service.UserAnalyticsService) *UserAnalyticsHandler {
	return &UserAnalyticsHandler{
		userAnalyticsService: userAnalyticsService,
	}
}

// GetUserProfileFeatures 鑾峰彇鐢ㄦ埛鐢诲儚鐗瑰緛
func (h *UserAnalyticsHandler) GetUserProfileFeatures(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"error":   "Missing parameter",
			"message": "User ID is required",
		})
		return
	}

	features, err := h.userAnalyticsService.GetUserProfileFeatures(c.Request.Context(), userID)
	if err != nil {
		logrus.WithError(err).WithField("user_id", userID).Error("Failed to get user profile features")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve user profile features",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    features,
	})
}

// GetUserBehaviorAnalysis 鑾峰彇鐢ㄦ埛琛屼负鍒嗘瀽
func (h *UserAnalyticsHandler) GetUserBehaviorAnalysis(c *gin.Context) {
	// 瑙ｆ瀽鏃堕棿鑼冨洿鍙傛暟
	timeRange, err := parseTimeRange(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameters",
			"message": err.Error(),
		})
		return
	}

	// 瑙ｆ瀽杩囨护鏉′欢
	filters := make(map[string]interface{})

	if deviceType := c.Query("device_type"); deviceType != "" {
		filters["device_type"] = deviceType
	}

	if country := c.Query("country"); country != "" {
		filters["country"] = country
	}

	if userSegment := c.Query("user_segment"); userSegment != "" {
		filters["user_segment"] = userSegment
	}

	analysis, err := h.userAnalyticsService.GetUserBehaviorAnalysis(c.Request.Context(), timeRange, filters)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"time_range": timeRange,
			"filters":    filters,
		}).Error("Failed to get user behavior analysis")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve user behavior analysis",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analysis,
	})
}

// GetCohortAnalysis 鑾峰彇闃熷垪鍒嗘瀽
func (h *UserAnalyticsHandler) GetCohortAnalysis(c *gin.Context) {
	cohortType := c.DefaultQuery("cohort_type", "monthly")

	// 楠岃瘉cohort_type
	if cohortType != "monthly" && cohortType != "weekly" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameter",
			"message": "cohort_type must be 'monthly' or 'weekly'",
		})
		return
	}

	// 瑙ｆ瀽鏃堕棿鑼冨洿鍙傛暟
	timeRange, err := parseTimeRange(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameters",
			"message": err.Error(),
		})
		return
	}

	analysis, err := h.userAnalyticsService.GetCohortAnalysis(c.Request.Context(), cohortType, timeRange)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"cohort_type": cohortType,
			"time_range":  timeRange,
		}).Error("Failed to get cohort analysis")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve cohort analysis",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analysis,
	})
}

// GetFunnelAnalysis 鑾峰彇婕忔枟鍒嗘瀽
func (h *UserAnalyticsHandler) GetFunnelAnalysis(c *gin.Context) {
	funnelName := c.Query("funnel_name")
	if funnelName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Missing parameter",
			"message": "funnel_name is required",
		})
		return
	}

	// 瑙ｆ瀽鏃堕棿鑼冨洿鍙傛暟
	timeRange, err := parseTimeRange(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameters",
			"message": err.Error(),
		})
		return
	}

	analysis, err := h.userAnalyticsService.GetFunnelAnalysis(c.Request.Context(), funnelName, timeRange)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"funnel_name": funnelName,
			"time_range":  timeRange,
		}).Error("Failed to get funnel analysis")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve funnel analysis",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    analysis,
	})
}

// GetUserSegmentation 鑾峰彇鐢ㄦ埛鍒嗙兢
func (h *UserAnalyticsHandler) GetUserSegmentation(c *gin.Context) {
	// 瑙ｆ瀽鍒嗙兢鏉′欢
	segmentCriteria := make(map[string]interface{})

	if minOrdersStr := c.Query("min_orders"); minOrdersStr != "" {
		if minOrders, err := strconv.Atoi(minOrdersStr); err == nil {
			segmentCriteria["min_orders"] = minOrders
		}
	}

	if minSpentStr := c.Query("min_spent"); minSpentStr != "" {
		if minSpent, err := strconv.ParseFloat(minSpentStr, 64); err == nil {
			segmentCriteria["min_spent"] = minSpent
		}
	}

	if minActivityScoreStr := c.Query("min_activity_score"); minActivityScoreStr != "" {
		if minActivityScore, err := strconv.ParseFloat(minActivityScoreStr, 64); err == nil {
			segmentCriteria["min_activity_score"] = minActivityScore
		}
	}

	if registrationPeriod := c.Query("registration_period"); registrationPeriod != "" {
		segmentCriteria["registration_period"] = registrationPeriod
	}

	if category := c.Query("preferred_category"); category != "" {
		segmentCriteria["preferred_category"] = category
	}

	segments, err := h.userAnalyticsService.GetUserSegmentation(c.Request.Context(), segmentCriteria)
	if err != nil {
		logrus.WithError(err).WithField("criteria", segmentCriteria).Error("Failed to get user segmentation")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve user segmentation",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    segments,
		"meta": gin.H{
			"count":    len(segments),
			"criteria": segmentCriteria,
		},
	})
}
