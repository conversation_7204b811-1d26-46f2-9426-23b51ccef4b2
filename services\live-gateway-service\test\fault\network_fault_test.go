package fault

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/application/service"
	"cina.club/services/live-gateway-service/internal/domain/model"
	"cina.club/services/live-gateway-service/internal/testutil/faults"
	"cina.club/services/live-gateway-service/internal/testutil/generators"
)

// Mock implementations
type mockCacheRepository struct {
	mock.Mock
}

func (m *mockCacheRepository) StoreStreamMapping(ctx context.Context, mapping *model.StreamMapping) error {
	args := m.Called(ctx, mapping)
	return args.Error(0)
}

func (m *mockCacheRepository) GetStreamMapping(ctx context.Context, streamKey string) (*model.StreamMapping, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamMapping), args.Error(1)
}

func (m *mockCacheRepository) DeleteStreamMapping(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) StoreNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNodeLoad(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) DeleteNodeLoad(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) ListNodeLoads(ctx context.Context) (map[string]*model.NodeLoad, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) StoreStreamStats(ctx context.Context, streamKey string, stats *model.StreamStats) error {
	args := m.Called(ctx, streamKey, stats)
	return args.Error(0)
}

func (m *mockCacheRepository) GetStreamStats(ctx context.Context, streamKey string) (*model.StreamStats, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamStats), args.Error(1)
}

func (m *mockCacheRepository) DeleteStreamStats(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *mockCacheRepository) Close() error {
	args := m.Called()
	return args.Error(0)
}

// Node operations
func (m *mockCacheRepository) StoreNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNode(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) DeleteNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error {
	args := m.Called(ctx, nodeID, status)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error {
	args := m.Called(ctx, nodeID, stats)
	return args.Error(0)
}

type mockMediaServerAdapter struct {
	mock.Mock
}

func (m *mockMediaServerAdapter) GetType() model.MediaServerType {
	args := m.Called()
	return args.Get(0).(model.MediaServerType)
}

func (m *mockMediaServerAdapter) ParseWebhookRequest(req *port.WebhookParseRequest) (*port.WebhookRequest, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.WebhookRequest), args.Error(1)
}

func (m *mockMediaServerAdapter) ValidateWebhookRequest(req *port.WebhookValidateRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) RespondWebhookSuccess(req *port.WebhookResponseRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) RespondWebhookFailure(req *port.WebhookErrorRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) GeneratePushURL(ctx context.Context, req *port.PushURLRequest) (*model.PushURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PushURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GeneratePlayURLs(ctx context.Context, req *port.RequestPlayURLsRequest) ([]*model.PlayURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.PlayURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GetStreamInfo(ctx context.Context, streamKey string) (*port.StreamInfo, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.StreamInfo), args.Error(1)
}

func (m *mockMediaServerAdapter) KickStream(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) GetServerStats(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockMediaServerAdapter) HealthCheck(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

type mockLoadBalancer struct {
	mock.Mock
}

func (m *mockLoadBalancer) RegisterNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) UpdateNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) GetNodeByID(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) GetAvailableNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) SelectNode(ctx context.Context, eligibleNodes []*model.MediaNode) (*model.MediaNode, error) {
	args := m.Called(ctx, eligibleNodes)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) SelectNodeByRequest(ctx context.Context, req *port.NodeSelectionRequest) (*model.MediaNode, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) UpdateNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *mockLoadBalancer) AddNode(ctx context.Context, node *model.MediaServerNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) RemoveNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

type mockLiveAPIClient struct {
	mock.Mock
}

func (m *mockLiveAPIClient) CheckPushAuth(ctx context.Context, req *port.AuthRequest) (*port.AuthResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.AuthResponse), args.Error(1)
}

func (m *mockLiveAPIClient) NotifyStreamPublished(ctx context.Context, event *port.StreamPublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyStreamUnpublished(ctx context.Context, event *port.StreamUnpublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyStreamKicked(ctx context.Context, event *port.StreamKickedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyRecordingCompleted(ctx context.Context, event *port.RecordingCompletedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) SubmitForModeration(ctx context.Context, event *port.ModerationSubmissionEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) SendEvent(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type mockEventPublisher struct {
	mock.Mock
}

func (m *mockEventPublisher) Publish(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishStreamEvent(ctx context.Context, event *model.WebhookEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishNodeEvent(ctx context.Context, nodeID string, eventType string, data interface{}) error {
	args := m.Called(ctx, nodeID, eventType, data)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishAlertEvent(ctx context.Context, alertType interface{}) error {
	args := m.Called(ctx, alertType)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type mockEventStore struct {
	mock.Mock
}

func (m *mockEventStore) StoreEvent(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventStore) QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error) {
	args := m.Called(ctx, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Event), args.Error(1)
}

func (m *mockEventStore) StoreBatch(ctx context.Context, events []*model.Event) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

func setupTestService(t *testing.T) port.GatewayService {
	// Setup mock dependencies
	cache := new(mockCacheRepository)
	mediaAdapter := new(mockMediaServerAdapter)
	loadBalancer := new(mockLoadBalancer)
	liveAPIClient := new(mockLiveAPIClient)
	eventPublisher := new(mockEventPublisher)
	eventStore := new(mockEventStore)
	logger := logrus.New()

	// Set up mock expectations for load balancer
	testNode := &model.MediaNode{
		ID:         "test-node",
		Address:    "127.0.0.1",
		Port:       1935,
		ServerType: model.MediaServerTypeSRS,
		Status:     model.NodeStatusActive,
	}
	loadBalancer.On("SelectNodeByRequest", mock.Anything, mock.AnythingOfType("*port.NodeSelectionRequest")).Return(testNode, nil)

	// Set up mock expectations for cache
	testMapping := &model.StreamMapping{
		StreamKey:  "test-stream",
		RoomID:     uuid.New(),
		UserID:     uuid.New(),
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}
	cache.On("GetStreamMapping", mock.Anything, mock.AnythingOfType("string")).Return(testMapping, nil)

	// Setup default config
	config := &service.GatewayConfig{
		DefaultTTL:               1 * time.Hour,
		MaxConcurrentStreams:     100,
		LoadBalanceStrategy:      "round_robin",
		EnableMetrics:            true,
		WebhookTimeout:           30 * time.Second,
		AuthTimeout:              10 * time.Second,
		AllowAuthFallbackOnError: false,
	}

	// Create service
	svc := service.NewGatewayService(
		cache,
		mediaAdapter,
		loadBalancer,
		liveAPIClient,
		eventPublisher,
		eventStore,
		config,
		logger,
	)

	return svc
}

func TestNetworkFaultInjection(t *testing.T) {
	// 设置测试环境
	ctx := context.Background()
	svc := setupTestService(t)
	streamGen := generators.NewStreamGenerator(time.Now().UnixNano())
	networkFaults := faults.NewNetworkFaultInjector()

	// 测试参数
	const (
		numStreams     = 10
		numOperations  = 100
		operationDelay = 10 * time.Millisecond
	)

	// 生成测试流
	streams := make([]string, numStreams)
	for i := range streams {
		streams[i] = streamGen.GenerateStreamKey()
	}

	// 测试网络延迟
	t.Run("network latency", func(t *testing.T) {
		networkFaults.AddFault("latency", faults.NetworkLatencyFault)
		networkFaults.Start()
		defer networkFaults.Stop()

		startTime := time.Now()
		for i := 0; i < numOperations; i++ {
			streamKey := streams[i%numStreams]
			err := networkFaults.InjectFault(ctx, "latency")
			if err != nil {
				continue
			}

			_, err = svc.GetStreamInfo(ctx, streamKey)
			assert.NoError(t, err)
		}
		duration := time.Since(startTime)

		// 验证延迟影响
		expectedMinDuration := time.Duration(numOperations) * operationDelay
		assert.Greater(t, duration, expectedMinDuration)
	})

	// 测试网络错误
	t.Run("network errors", func(t *testing.T) {
		networkFaults.AddFault("error", faults.NetworkErrorFault)
		networkFaults.Start()
		defer networkFaults.Stop()

		errorCount := 0
		for i := 0; i < numOperations; i++ {
			streamKey := streams[i%numStreams]
			err := networkFaults.InjectFault(ctx, "error")
			if err != nil {
				errorCount++
				continue
			}

			_, err = svc.GetStreamInfo(ctx, streamKey)
			if err != nil {
				errorCount++
			}
		}

		// 验证错误率
		errorRate := float64(errorCount) / float64(numOperations)
		assert.InDelta(t, faults.NetworkErrorFault.ErrorRate, errorRate, 0.05)
	})

	// 测试网络分区
	t.Run("network partition", func(t *testing.T) {
		partitionConfig := &faults.FaultConfig{
			Type:        faults.FaultTypeNetwork,
			Mode:        faults.FaultModePartition,
			Probability: 0.2,
			Duration:    time.Second,
		}
		networkFaults.AddFault("partition", partitionConfig)
		networkFaults.Start()
		defer networkFaults.Stop()

		// 创建多个节点
		nodeGen := generators.NewNodeGenerator(time.Now().UnixNano())
		nodes := make([]*model.MediaNode, numStreams)
		for i := range nodes {
			nodes[i] = nodeGen.GenerateMediaNode()
			require.NoError(t, svc.RegisterNode(ctx, nodes[i]))
		}

		partitionCount := 0
		for i := 0; i < numOperations; i++ {
			err := networkFaults.InjectFault(ctx, "partition")
			if err != nil {
				partitionCount++
				continue
			}

			// 尝试节点间通信
			_, err = svc.GetAllNodes(ctx)
			if err != nil {
				partitionCount++
			}
			time.Sleep(operationDelay)
		}

		// 验证分区发生率
		partitionRate := float64(partitionCount) / float64(numOperations)
		assert.InDelta(t, partitionConfig.Probability, partitionRate, 0.05)
	})

	// 测试恢复能力
	t.Run("recovery capability", func(t *testing.T) {
		// 配置高概率的网络错误
		severeErrorConfig := &faults.FaultConfig{
			Type:        faults.FaultTypeNetwork,
			Mode:        faults.FaultModeError,
			Probability: 0.8,
			ErrorRate:   0.8,
			Duration:    time.Second,
		}
		networkFaults.AddFault("severe_error", severeErrorConfig)

		// 第一阶段：注入严重错误
		networkFaults.Start()
		errorCount := 0
		for i := 0; i < numOperations/2; i++ {
			streamKey := streams[i%numStreams]
			err := networkFaults.InjectFault(ctx, "severe_error")
			if err != nil {
				errorCount++
				continue
			}

			_, err = svc.GetStreamInfo(ctx, streamKey)
			if err != nil {
				errorCount++
			}
			time.Sleep(operationDelay)
		}

		// 验证错误率很高
		firstPhaseErrorRate := float64(errorCount) / float64(numOperations/2)
		assert.Greater(t, firstPhaseErrorRate, 0.6)

		// 第二阶段：停止故障注入
		networkFaults.Stop()
		errorCount = 0
		for i := 0; i < numOperations/2; i++ {
			streamKey := streams[i%numStreams]
			_, err := svc.GetStreamInfo(ctx, streamKey)
			if err != nil {
				errorCount++
			}
			time.Sleep(operationDelay)
		}

		// 验证系统恢复
		secondPhaseErrorRate := float64(errorCount) / float64(numOperations/2)
		assert.Less(t, secondPhaseErrorRate, 0.1)
	})
}
