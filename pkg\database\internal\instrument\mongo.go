/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package instrument

import (
	"context"
	"errors"
	"log/slog"

	"go.mongodb.org/mongo-driver/event"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// spanContextKey is used to store spans in context
type spanContextKey string

const mongoSpanKey spanContextKey = "mongo_span"

// MongoCommandMonitor provides observability for MongoDB operations through
// OpenTelemetry tracing and structured logging
type MongoCommandMonitor struct {
	tracer trace.Tracer
	logger *slog.Logger
}

// NewMongoCommandMonitor creates a new MongoDB command monitor with integrated
// OpenTelemetry tracing and structured logging
func NewMongoCommandMonitor(tracer trace.Tracer, logger *slog.Logger) *event.CommandMonitor {
	monitor := &MongoCommandMonitor{
		tracer: tracer,
		logger: logger,
	}

	return &event.CommandMonitor{
		Started:   monitor.commandStarted,
		Succeeded: monitor.commandSucceeded,
		Failed:    monitor.commandFailed,
	}
}

// commandStarted is called when a MongoDB command starts
func (m *MongoCommandMonitor) commandStarted(ctx context.Context, evt *event.CommandStartedEvent) {
	if m.tracer == nil {
		return
	}

	// Create span for the MongoDB command
	spanName := "mongodb." + evt.CommandName
	ctx, span := m.tracer.Start(ctx, spanName, trace.WithSpanKind(trace.SpanKindClient))

	// Add standard attributes
	span.SetAttributes(
		attribute.String("db.system", "mongodb"),
		attribute.String("db.name", evt.DatabaseName),
		attribute.String("db.operation", evt.CommandName),
		attribute.String("db.mongodb.collection", getCollectionName(evt)),
		attribute.String("db.connection_id", evt.ConnectionID),
	)

	// Store the span in context for later use (would need proper context propagation in real implementation)
	_ = context.WithValue(ctx, mongoSpanKey, span)

	// Log command start
	if m.logger != nil {
		m.logger.Debug("MongoDB command started",
			"command", evt.CommandName,
			"database", evt.DatabaseName,
			"collection", getCollectionName(evt),
			"connection_id", evt.ConnectionID,
			"request_id", evt.RequestID,
		)
	}
}

// commandSucceeded is called when a MongoDB command succeeds
func (m *MongoCommandMonitor) commandSucceeded(ctx context.Context, evt *event.CommandSucceededEvent) {
	// Get span from context if it exists
	if spanValue := ctx.Value(mongoSpanKey); spanValue != nil {
		if span, ok := spanValue.(trace.Span); ok {
			span.SetStatus(codes.Ok, "")
			span.SetAttributes(
				attribute.Int64("db.response_time_ms", evt.DurationNanos/1_000_000),
			)
			span.End()
		}
	}

	// Log successful command
	if m.logger != nil {
		m.logger.Debug("MongoDB command succeeded",
			"command", evt.CommandName,
			"duration_ms", evt.DurationNanos/1_000_000,
			"request_id", evt.RequestID,
		)
	}
}

// commandFailed is called when a MongoDB command fails
func (m *MongoCommandMonitor) commandFailed(ctx context.Context, evt *event.CommandFailedEvent) {
	// Get span from context if it exists
	if spanValue := ctx.Value(mongoSpanKey); spanValue != nil {
		if span, ok := spanValue.(trace.Span); ok {
			span.SetStatus(codes.Error, evt.Failure)
			span.SetAttributes(
				attribute.Int64("db.response_time_ms", evt.DurationNanos/1_000_000),
				attribute.String("db.error", evt.Failure),
			)
			span.RecordError(errors.New(evt.Failure))
			span.End()
		}
	}

	// Log failed command
	if m.logger != nil {
		m.logger.Error("MongoDB command failed",
			"command", evt.CommandName,
			"duration_ms", evt.DurationNanos/1_000_000,
			"error", evt.Failure,
			"request_id", evt.RequestID,
		)
	}
}

// getCollectionName extracts collection name from command document
func getCollectionName(evt *event.CommandStartedEvent) string {
	if evt.Command == nil {
		return ""
	}

	// Look for collection name in various command types
	if collectionName, ok := evt.Command.Lookup(evt.CommandName).StringValueOK(); ok {
		return collectionName
	}

	// Fallback for commands that use different field names
	commandFields := []string{"collection", "coll", "create", "drop"}
	for _, field := range commandFields {
		if collectionName, ok := evt.Command.Lookup(field).StringValueOK(); ok {
			return collectionName
		}
	}

	return ""
}

// TracingConfig provides configuration for MongoDB tracing
type TracingConfig struct {
	// ServiceName is the name of the service for tracing
	ServiceName string

	// TracingEnabled controls whether tracing is enabled
	TracingEnabled bool

	// LogCommands controls whether to log MongoDB commands
	LogCommands bool

	// LogLevel sets the logging level for MongoDB operations
	LogLevel slog.Level
}

// DefaultTracingConfig returns default tracing configuration
func DefaultTracingConfig() TracingConfig {
	return TracingConfig{
		ServiceName:    "mongodb-client",
		TracingEnabled: true,
		LogCommands:    true,
		LogLevel:       slog.LevelDebug,
	}
}

// CreateTracingLogger creates a logger suitable for MongoDB command monitoring
func CreateTracingLogger(config TracingConfig) *slog.Logger {
	opts := &slog.HandlerOptions{
		Level: config.LogLevel,
	}

	handler := slog.NewTextHandler(nil, opts)
	return slog.New(handler)
}

// CreateTracingTracer creates a tracer for MongoDB operations
func CreateTracingTracer(serviceName string) trace.Tracer {
	if serviceName == "" {
		serviceName = "mongodb-client"
	}

	return otel.Tracer(serviceName)
}
