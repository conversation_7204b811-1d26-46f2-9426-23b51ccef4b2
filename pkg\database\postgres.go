/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16
Modified: 2025-01-16
*/

// Package database provides standardized database client factories with built-in observability
// for all CINA.CLUB backend services. It supports PostgreSQL, MongoDB, and Redis with
// automatic integration of tracing, logging, and metrics.
package database

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.opentelemetry.io/otel/trace"

	"cina.club/pkg/database/internal/instrument"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// PostgresConfig defines configuration for PostgreSQL client
type PostgresConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	SSLMode  string
}

// NewPostgres creates a new PostgreSQL database connection
func NewPostgres(config PostgresConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=UTC",
		config.Host,
		config.User,
		config.Password,
		config.Database,
		config.Port,
		config.SSLMode,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to postgres: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	return db, nil
}

// NewPostgresPool creates a new PostgreSQL connection pool with built-in observability.
// This function automatically integrates OpenTelemetry tracing and structured logging
// for all database operations performed through the returned pool.
//
// Parameters:
//   - ctx: Context for timeout and cancellation
//   - cfg: PostgreSQL configuration including DSN and connection pool settings
//   - logger: Structured logger for database operation logs
//   - tracer: OpenTelemetry tracer for distributed tracing
//
// Returns a *pgxpool.Pool that is ready to use with integrated observability,
// or an error if connection or configuration fails.
func NewPostgresPool(ctx context.Context, cfg PostgresConfig, logger *slog.Logger, tracer trace.Tracer) (*pgxpool.Pool, error) {
	// Parse the DSN and create pool config
	poolConfig, err := pgxpool.ParseConfig(fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=UTC",
		cfg.Host,
		cfg.User,
		cfg.Password,
		cfg.Database,
		cfg.Port,
		cfg.SSLMode,
	))
	if err != nil {
		return nil, fmt.Errorf("failed to parse postgres DSN: %w", err)
	}

	// Configure connection pool parameters
	poolConfig.MaxConns = 100
	poolConfig.MinConns = 10
	poolConfig.MaxConnLifetime = 1 * time.Hour
	poolConfig.MaxConnIdleTime = 30 * time.Minute
	poolConfig.HealthCheckPeriod = 1 * time.Minute

	// Integrate observability - this is the core value of this package
	if tracer != nil {
		// Create and inject our custom tracer that implements pgx.QueryTracer
		pgxTracer := instrument.NewPgxTracer(tracer, logger)
		poolConfig.ConnConfig.Tracer = pgxTracer
	}

	// Note: pgx v5 removed the Logger field from ConnConfig
	// Logging is now handled through the tracer interface
	if logger != nil {
		logger.Debug("PostgreSQL logging integrated through tracer")
	}

	// Configure SSL mode if specified
	if cfg.SSLMode != "" {
		// pgx parses SSL mode from DSN, but we can validate it here
		logger.Debug("PostgreSQL SSL mode configured", "ssl_mode", cfg.SSLMode)
	}

	// Create the connection pool
	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create postgres pool: %w", err)
	}

	// Perform health check to ensure the connection is working
	if err := performPostgresHealthCheck(ctx, pool, logger); err != nil {
		pool.Close()
		return nil, fmt.Errorf("postgres health check failed: %w", err)
	}

	logger.Info("PostgreSQL connection pool created successfully",
		"max_conns", 100,
		"min_conns", 10,
		"health_check_period", 1*time.Minute)

	return pool, nil
}

// performPostgresHealthCheck verifies the database connection is healthy
func performPostgresHealthCheck(ctx context.Context, pool *pgxpool.Pool, logger *slog.Logger) error {
	// Create a timeout context for the health check
	healthCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Perform a simple ping
	if err := pool.Ping(healthCtx); err != nil {
		logger.Error("PostgreSQL ping failed", "error", err)
		return fmt.Errorf("ping failed: %w", err)
	}

	// Perform a simple query to ensure read operations work
	var result int
	err := pool.QueryRow(healthCtx, "SELECT 1").Scan(&result)
	if err != nil {
		logger.Error("PostgreSQL health check query failed", "error", err)
		return fmt.Errorf("health check query failed: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("unexpected health check result: got %d, expected 1", result)
	}

	logger.Debug("PostgreSQL health check passed")
	return nil
}

// PostgresPool is a convenience wrapper that includes both the pool and logger
// for easier dependency injection in services
type PostgresPool struct {
	Pool   *pgxpool.Pool
	Logger *slog.Logger
}

// Close gracefully closes the connection pool
func (p *PostgresPool) Close() {
	if p.Pool != nil {
		p.Pool.Close()
		if p.Logger != nil {
			p.Logger.Info("PostgreSQL connection pool closed")
		}
	}
}

// NewPostgresPoolWithWrapper creates a PostgreSQL pool wrapped with additional utilities
func NewPostgresPoolWithWrapper(ctx context.Context, cfg PostgresConfig, logger *slog.Logger, tracer trace.Tracer) (*PostgresPool, error) {
	pool, err := NewPostgresPool(ctx, cfg, logger, tracer)
	if err != nil {
		return nil, err
	}

	return &PostgresPool{
		Pool:   pool,
		Logger: logger,
	}, nil
}

// Stats returns connection pool statistics
func (p *PostgresPool) Stats() *pgxpool.Stat {
	if p.Pool == nil {
		return nil
	}
	return p.Pool.Stat()
}

// LogStats logs current connection pool statistics
func (p *PostgresPool) LogStats() {
	if p.Pool == nil || p.Logger == nil {
		return
	}

	stats := p.Pool.Stat()
	p.Logger.Debug("PostgreSQL pool statistics",
		"total_conns", stats.TotalConns(),
		"acquired_conns", stats.AcquiredConns(),
		"idle_conns", stats.IdleConns(),
		"max_conns", stats.MaxConns(),
		"acquire_count", stats.AcquireCount(),
		"acquire_duration", stats.AcquireDuration(),
		"acquired_conns", stats.AcquiredConns(),
		"canceled_acquire_count", stats.CanceledAcquireCount(),
		"constructing_conns", stats.ConstructingConns(),
		"empty_acquire_count", stats.EmptyAcquireCount(),
		"idle_conns", stats.IdleConns(),
		"max_idle_destroy_count", stats.MaxIdleDestroyCount(),
		"max_lifetime_destroy_count", stats.MaxLifetimeDestroyCount())
}

// WithTransaction provides a helper for executing operations within a transaction
func (p *PostgresPool) WithTransaction(ctx context.Context, fn func(pgx.Tx) error) error {
	if p.Pool == nil {
		return fmt.Errorf("postgres pool is nil")
	}

	tx, err := p.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err := tx.Rollback(ctx); err != nil && p.Logger != nil {
			// Rollback error is expected if transaction was committed
			p.Logger.Debug("Transaction rollback result", "error", err)
		}
	}()

	if err := fn(tx); err != nil {
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}
