好的，遵照您的指示。我将为您生成一份专门针对 **`core/aic` (AI Core)** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`core/aic` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到代码实现的层面，详细阐述**如何通过CGO安全地与`llama.cpp`交互、如何管理模型和会话的生命周期、如何实现高性能的流式推理，以及如何为Go Mobile设计可靠的回调接口**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `core/aic` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `core/aic-srs.md` (v1.0)
**核心架构**: Go封装层 + CGO绑定 + `llama.cpp` C++核心

## 1. 概述

`core/aic` 是CINA.CLUB平台在**端侧实现本地大模型(LLM)推理**的核心能力库。它是一个高度专业化的、性能敏感的Go包，其主要职责是作为Go世界和底层C++推理引擎之间的**高性能、安全可靠的桥梁**。其架构设计的核心目标是：
1.  **性能最大化**: Go与C++之间的交互开销（CGO调用开销、内存拷贝）必须被最小化。
2.  **资源安全**: 必须对C++层分配的非托管内存（模型权重、推理上下文）进行精确的生命周期管理，**严防内存泄漏**。
3.  **并发安全**: 推理会话对象必须是线程安全的，允许多个goroutine排队使用同一个已加载的模型实例。
4.  **简洁的API**: 为上层调用者（Go Mobile/WASM封装层）提供一个极其简单的、抽象化的接口（如`PredictStream`），完全屏蔽底层的复杂性。
5.  **健壮的错误处理**: 能够捕获并向上层传递来自C++层的错误。

本架构设计通过**在Go中实现一个带锁的`Session`对象来管理C++指针**，并利用**回调和channel机制**实现异步流式推理，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (Go-CGO-C++交互模型)

```mermaid
graph TD
    subgraph "上层调用者 (Go Mobile/WASM Exports)"
        A[exports_mobile.go]
    end
    
    subgraph "core/aic (Go Layer)"
        style "core/aic (Go Layer)" fill:#e0f7fa
        B[Session struct<br/><em>{mutex, cLlamaCtx, ...}</em>]
        C[PredictStream method]
        D[CGO Bridge Functions<br/><em>(cgo_bindings.go)</em>]
    end

    subgraph "CGO (Go's Foreign Function Interface)"
        style "CGO (Go's Foreign Function Interface)" fill:#f3e5f5
        FFI
    end

    subgraph "llama.cpp (C/C++ Layer)"
        style "llama.cpp (C/C++ Layer)" fill:#e8f5e9
        E[C-API<br/>(llama.h)]
        F[Core C++ Implementation<br/>(ggml, ...)]
    end

    subgraph "Hardware"
        GPU[GPU (via Metal/CUDA)]
        CPU[CPU (via NEON/AVX)]
    end
    
    A -- "1. Calls NewSession" --> B
    B -- "2. Calls" --> D
    D -- "3. CGO call" --> FFI
    FFI -- " " --> E
    E -- "4. Loads model" --> F
    F -- "Uses" --> GPU & CPU
    
    A -- "5. Calls PredictStream" --> C
    C -- "6. Acquires lock on Session" --> B
    C -- "7. Calls predict in new goroutine" --> D
    
    %% Streaming callback
    D -- "8. CGO call with Go callback func ptr" --> FFI --> E
    E -- "Enters inference loop" --> F
    F -- "9. Generates token" --> E
    E -- "10. ✨ C++ calls Go callback ✨" --> FFI
    FFI -- " " --> D
    D -- "11. Writes token to channel" --> C
    C -- "Forwards token to caller" --> A
```

### 2.2 最终目录结构 (`core/aic/`)

```
core/aic/
├── third_party/
│   └── llama.cpp/            # ✨ Git Submodule: 指向llama.cpp的特定commit ✨
├── cgo_bindings.go           # ✨ CGO指令和C-Go函数签名定义 ✨
├── session.go                # ✨ Session对象的定义、创建和销毁 ✨
├── predict.go                # ✨ PredictStream方法的实现和推理循环 ✨
├── config.go                 # 定义SessionConfig和PredictConfig
├── exports_mobile.go         # 导出给Go Mobile的回调式接口
└── aic_test.go               # 单元测试 (需要mock CGO或使用真实模型)
```

---

## 3. 各层职责深度解析

### 3.1 `third_party/llama.cpp/` - C++核心

*   这是一个Git Submodule，确保了我们使用的`llama.cpp`版本是固定的、经过测试的。
*   CI/CD流程在clone代码时，需要使用`git submodule update --init`来拉取其代码。

### 3.2 `cgo_bindings.go` - CGO桥接层

这是Go与C++之间的“翻译官”，代码非常格式化。

*   **头文件**:
    ```go
    /*
    #cgo CFLAGS: -I${SRCDIR}/third_party/llama.cpp
    #cgo CXXFLAGS: -I${SRCDIR}/third_party/llama.cpp
    #cgo LDFLAGS: -L. -llama -lm -lstdc++
    // ... 平台特定的编译和链接指令 (如Metal for macOS)
    #include "llama.h"
    #include <stdlib.h>
    
    // C-Go的回调函数代理
    // 因为C不能直接调用Go的闭包，所以需要一个静态的C函数作为跳板
    int bridge_token_callback(void *user_data, const char *token);
    */
    import "C"
    ```
*   **Go回调导出**:
    ```go
    //export goTokenCallback
    func goTokenCallback(userData unsafe.Pointer, token *C.char) C.int {
        // 从userData中恢复Go的回调channel或接口
        callbackChan := *(*chan string)(userData)
        callbackChan <- C.GoString(token)
        return 0 // 返回0表示继续
    }
    ```
    这个`goTokenCallback`函数会被编译，并可以通过C语言的函数指针`C.bridge_token_callback`来调用。

### 3.3 `session.go` - 会话生命周期管理

*   **`Session` struct**:
    ```go
    type Session struct {
        mutex     sync.Mutex // ✨ 保证同一时间只有一个推理任务在使用该会话 ✨
        cModel    *C.struct_llama_model // C++层模型指针
        cContext  *C.struct_llama_context // C++层上下文指针
        modelPath string
    }
    ```
*   **`NewSession(modelPath, cfg)`**:
    1.  调用`C.llama_backend_init()`。
    2.  调用`C.llama_load_model_from_file()`加载模型，得到`cModel`。**如果失败，必须返回错误**。
    3.  调用`C.llama_new_context_with_model()`创建上下文，得到`cContext`。**如果失败，必须先调用`llama_free_model`释放模型，再返回错误**。
    4.  创建`Session`实例。
    5.  **✨ 使用`runtime.SetFinalizer`注册终结器 ✨**:
        ```go
        runtime.SetFinalizer(s, func(s *Session) {
            s.Close()
        })
        ```
        这是**防止内存泄漏的最后一道防线**。当`Session`对象被GC回收时，会自动调用`Close`方法。
*   **`Close()`**:
    *   获取锁`s.mutex.Lock()`。
    *   如果`cContext`不为nil，调用`C.llama_free(s.cContext)`。
    *   如果`cModel`不为nil，调用`C.llama_free_model(s.cModel)`。
    *   将指针设为nil。
    *   释放锁。

### 3.4 `predict.go` - 推理执行逻辑

*   **`PredictStream(ctx, prompt, cfg)`**:
    1.  **获取锁**: `s.mutex.Lock()`. **在方法结束时必须释放锁**: `defer s.mutex.Unlock()`。
    2.  **创建结果channel**: `tokenChan := make(chan string, 128)`。
    3.  **启动推理goroutine**:
        ```go
        go func() {
            defer close(tokenChan) // 保证channel最终会被关闭

            // 1. 将prompt转换为C字符串
            cPrompt := C.CString(prompt)
            defer C.free(unsafe.Pointer(cPrompt))
            
            // 2. 将结果channel的指针作为用户数据传递给C++
            userData := unsafe.Pointer(&tokenChan)

            // 3. ✨ 调用CGO桥接函数 ✨
            // cgo_predict是封装了llama_eval等循环的C函数
            C.cgo_predict(s.cContext, cPrompt, (C.go_token_callback)(C.bridge_token_callback), userData)
        }()
        ```
    4.  **立即返回channel**: `return tokenChan, nil`。

---

## 4. `exports_mobile.go` - Go Mobile适配层

*   **职责**: 将`core/aic`中基于Go channel的异步接口，转换为Go Mobile支持的**回调接口模式**。
*   **`MobileSession` struct**: 包装`core/aic.Session`。
*   **`TokenCallback` interface**: 定义需要原生层（Kotlin/Swift）实现的接口。
*   **`Predict(prompt, callback)` method**:
    1.  调用`internalSession.PredictStream(...)`获取到token channel。
    2.  **启动一个新的goroutine**来消费这个channel。
    3.  **在这个goroutine的循环中**:
        ```go
        for token := range tokenChan {
            callback.OnToken(token) // 调用原生层实现的回调方法
        }
        // channel关闭后，调用 OnEnd() 或 OnError()
        ```

## 5. 总结

本架构设计通过以下关键点来构建一个生产级的`core/aic`：
1.  **CGO作为薄桥梁**: 将`core/aic`设计为`llama.cpp`的一个轻量级、类型安全的Go封装，而不是重新实现其逻辑。
2.  **严格的资源管理**: 通过`Close()`方法和`runtime.SetFinalizer`，对C++层的非托管内存进行了双重保障，严防内存泄漏。
3.  **并发安全与异步化**:
    *   使用`sync.Mutex`确保了对单个模型实例的访问是线程安全的。
    *   通过将阻塞的推理循环放入独立的goroutine，并利用channel/回调进行通信，实现了高性能的、非阻塞的流式API。
4.  **清晰的跨语言接口**: 为Go Mobile设计了专门的回调式接口，解决了原生层与Go异步逻辑通信的难题。

这种架构确保了`core/aic`能够以一种**高性能、安全、可靠且对上层友好**的方式，将强大的C++推理引擎能力，无缝地集成到CINA.CLUB的全平台应用中。