/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { common } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { UserManager } from '../user/UserManager';
import { NetworkManager } from '../network/NetworkManager';
import { DatabaseManager } from '../database/DatabaseManager';
import { ConfigManager } from '../config/ConfigManager';
import { CryptoManager } from '../crypto/CryptoManager';
import { StorageManager } from '../storage/StorageManager';

/**
 * 依赖注入容器
 * 
 * 采用单例模式，负责创建和管理应用中所有的服务实例
 * 支持延迟初始化和依赖关系管理
 */
export class DependencyInjector {
  private static instance: DependencyInjector;
  private static readonly TAG = 'DependencyInjector';
  
  // 服务实例缓存
  private userManager?: UserManager;
  private networkManager?: NetworkManager;
  private databaseManager?: DatabaseManager;
  private configManager?: ConfigManager;
  private cryptoManager?: CryptoManager;
  private storageManager?: StorageManager;
  
  private context?: common.UIAbilityContext;
  private isInitialized: boolean = false;

  /**
   * 获取单例实例
   */
  static getInstance(): DependencyInjector {
    if (!DependencyInjector.instance) {
      DependencyInjector.instance = new DependencyInjector();
    }
    return DependencyInjector.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    // 私有构造函数确保单例
  }

  /**
   * 初始化依赖注入容器
   * @param context 应用上下文
   */
  initialize(context: common.UIAbilityContext): void {
    if (this.isInitialized) {
      hilog.warn(0x0000, DependencyInjector.TAG, 'DependencyInjector already initialized');
      return;
    }

    this.context = context;
    this.isInitialized = true;
    
    hilog.info(0x0000, DependencyInjector.TAG, 'DependencyInjector initialized successfully');
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    // 清理各个管理器
    this.userManager?.cleanup();
    this.networkManager?.cleanup();
    this.databaseManager?.cleanup();
    this.cryptoManager?.cleanup();
    this.storageManager?.cleanup();

    // 重置状态
    this.userManager = undefined;
    this.networkManager = undefined;
    this.databaseManager = undefined;
    this.configManager = undefined;
    this.cryptoManager = undefined;
    this.storageManager = undefined;
    
    this.context = undefined;
    this.isInitialized = false;

    hilog.info(0x0000, DependencyInjector.TAG, 'DependencyInjector cleaned up');
  }

  /**
   * 获取用户管理器
   */
  getUserManager(): UserManager {
    if (!this.userManager) {
      this.ensureInitialized();
      this.userManager = new UserManager(
        this.getStorageManager(),
        this.getCryptoManager(),
        this.getNetworkManager()
      );
      hilog.info(0x0000, DependencyInjector.TAG, 'UserManager created');
    }
    return this.userManager;
  }

  /**
   * 获取网络管理器
   */
  getNetworkManager(): NetworkManager {
    if (!this.networkManager) {
      this.ensureInitialized();
      this.networkManager = new NetworkManager(this.getConfigManager());
      hilog.info(0x0000, DependencyInjector.TAG, 'NetworkManager created');
    }
    return this.networkManager;
  }

  /**
   * 获取数据库管理器
   */
  getDatabaseManager(): DatabaseManager {
    if (!this.databaseManager) {
      this.ensureInitialized();
      this.databaseManager = new DatabaseManager(this.context!);
      hilog.info(0x0000, DependencyInjector.TAG, 'DatabaseManager created');
    }
    return this.databaseManager;
  }

  /**
   * 获取配置管理器
   */
  getConfigManager(): ConfigManager {
    if (!this.configManager) {
      this.ensureInitialized();
      this.configManager = new ConfigManager(this.context!);
      hilog.info(0x0000, DependencyInjector.TAG, 'ConfigManager created');
    }
    return this.configManager;
  }

  /**
   * 获取加密管理器
   */
  getCryptoManager(): CryptoManager {
    if (!this.cryptoManager) {
      this.ensureInitialized();
      this.cryptoManager = new CryptoManager();
      hilog.info(0x0000, DependencyInjector.TAG, 'CryptoManager created');
    }
    return this.cryptoManager;
  }

  /**
   * 获取存储管理器
   */
  getStorageManager(): StorageManager {
    if (!this.storageManager) {
      this.ensureInitialized();
      this.storageManager = new StorageManager(
        this.context!,
        this.getDatabaseManager()
      );
      hilog.info(0x0000, DependencyInjector.TAG, 'StorageManager created');
    }
    return this.storageManager;
  }

  /**
   * 检查是否已初始化
   */
  private ensureInitialized(): void {
    if (!this.isInitialized || !this.context) {
      throw new Error('DependencyInjector not initialized. Call initialize() first.');
    }
  }

  /**
   * 获取应用上下文
   */
  getContext(): common.UIAbilityContext {
    this.ensureInitialized();
    return this.context!;
  }

  /**
   * 检查初始化状态
   */
  isReady(): boolean {
    return this.isInitialized && this.context !== undefined;
  }
} 