# Frameworks Directory

/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

This directory contains pre-compiled frameworks that are used by the Apple applications.

## CoreGo.xcframework

The `CoreGo.xcframework` is a Universal Framework built from Go code using Go Mobile. It contains the core business logic shared across all Apple platforms.

### Contents

The framework includes the following Go modules compiled for Apple platforms:

- **Crypto Module** (`core/crypto`): End-to-end encryption functionality
- **Data Sync Module** (`core/datasync`): Data synchronization and chunking
- **AI Module** (`core/aic`): AI inference engine bindings

### Supported Architectures

- iOS: arm64, arm64-simulator, x86_64-simulator
- macOS: arm64, x86_64
- watchOS: arm64, arm64-simulator, x86_64-simulator
- visionOS: arm64, arm64-simulator

### Building CoreGo.xcframework

To rebuild the framework from Go source code:

```bash
# Navigate to the core directory
cd ../../core

# Build for iOS
gomobile bind -target=ios -o ../apps/apple/Frameworks/CoreGo.xcframework .

# Or use the provided build script
./scripts/build-apple.sh
```

### Integration

The framework is automatically linked to all Apple app targets through the Package.swift configuration. The GoBridge module provides Swift-friendly interfaces to the Go functionality.

### Version Management

- Framework version should match the core Go module version
- Update the framework when core logic changes
- Test all platforms after framework updates

## Usage Notes

- Do not modify files in this directory manually
- Framework updates should be done through the build process
- Ensure all team members have the same framework version
- The framework is platform-specific and cannot be used across different architectures 