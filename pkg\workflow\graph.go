/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package workflow

import (
	"fmt"
	"sort"
)

// Graph represents a directed acyclic graph (DAG) for workflow execution.
// It provides methods for building, validating, and traversing the workflow graph.
type Graph struct {
	// nodes maps node IDs to their definitions
	nodes map[string]*Node

	// edges stores the adjacency list representation of the graph
	// key is the source node ID, value is a slice of target node IDs
	edges map[string][]string

	// reverseEdges stores the reverse adjacency list
	// key is the target node ID, value is a slice of source node IDs
	reverseEdges map[string][]string

	// edgeConditions maps edge pairs to their conditions
	// key is "fromNode->toNode", value is the edge condition
	edgeConditions map[string]*EdgeCondition

	// workflow reference to the original workflow definition
	workflow *Workflow
}

// NewGraph creates a new graph from a workflow definition.
func NewGraph(workflow *Workflow) (*Graph, error) {
	if workflow == nil {
		return nil, fmt.Errorf("workflow cannot be nil")
	}

	if err := workflow.Validate(); err != nil {
		return nil, fmt.Errorf("invalid workflow: %w", err)
	}

	g := &Graph{
		nodes:          make(map[string]*Node),
		edges:          make(map[string][]string),
		reverseEdges:   make(map[string][]string),
		edgeConditions: make(map[string]*EdgeCondition),
		workflow:       workflow,
	}

	// Build node map
	for i := range workflow.Nodes {
		node := &workflow.Nodes[i]
		g.nodes[node.ID] = node
	}

	// Build edge maps
	for _, edge := range workflow.Edges {
		g.edges[edge.FromNode] = append(g.edges[edge.FromNode], edge.ToNode)
		g.reverseEdges[edge.ToNode] = append(g.reverseEdges[edge.ToNode], edge.FromNode)

		if edge.Condition != nil {
			edgeKey := fmt.Sprintf("%s->%s", edge.FromNode, edge.ToNode)
			g.edgeConditions[edgeKey] = edge.Condition
		}
	}

	// Validate that the graph is acyclic
	if err := g.validateAcyclic(); err != nil {
		return nil, err
	}

	return g, nil
}

// GetNode returns the node definition for the given ID.
func (g *Graph) GetNode(nodeID string) *Node {
	return g.nodes[nodeID]
}

// GetSuccessors returns the successor nodes of the given node.
func (g *Graph) GetSuccessors(nodeID string) []string {
	return g.edges[nodeID]
}

// GetPredecessors returns the predecessor nodes of the given node.
func (g *Graph) GetPredecessors(nodeID string) []string {
	return g.reverseEdges[nodeID]
}

// GetEdgeCondition returns the condition for the edge between two nodes.
func (g *Graph) GetEdgeCondition(fromNode, toNode string) *EdgeCondition {
	edgeKey := fmt.Sprintf("%s->%s", fromNode, toNode)
	return g.edgeConditions[edgeKey]
}

// GetRootNodes returns nodes with no predecessors (entry points).
func (g *Graph) GetRootNodes() []string {
	var roots []string
	for nodeID := range g.nodes {
		if len(g.reverseEdges[nodeID]) == 0 {
			roots = append(roots, nodeID)
		}
	}
	sort.Strings(roots) // For deterministic ordering
	return roots
}

// GetLeafNodes returns nodes with no successors (exit points).
func (g *Graph) GetLeafNodes() []string {
	var leaves []string
	for nodeID := range g.nodes {
		if len(g.edges[nodeID]) == 0 {
			leaves = append(leaves, nodeID)
		}
	}
	sort.Strings(leaves) // For deterministic ordering
	return leaves
}

// TopologicalSort returns a topologically sorted list of node IDs.
// This determines the execution order for the workflow.
func (g *Graph) TopologicalSort() ([]string, error) {
	// Use Kahn's algorithm for topological sorting
	inDegree := make(map[string]int)

	// Initialize in-degree count for all nodes
	for nodeID := range g.nodes {
		inDegree[nodeID] = 0
	}

	// Calculate in-degrees
	for _, successors := range g.edges {
		for _, successor := range successors {
			inDegree[successor]++
		}
	}

	// Queue for nodes with no incoming edges
	var queue []string
	for nodeID, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, nodeID)
		}
	}

	// Sort queue for deterministic ordering
	sort.Strings(queue)

	var result []string

	for len(queue) > 0 {
		// Remove node from queue
		current := queue[0]
		queue = queue[1:]
		result = append(result, current)

		// Update in-degrees of successors
		successors := g.edges[current]
		sort.Strings(successors) // For deterministic ordering

		for _, successor := range successors {
			inDegree[successor]--
			if inDegree[successor] == 0 {
				queue = append(queue, successor)
				// Re-sort queue to maintain deterministic ordering
				sort.Strings(queue)
			}
		}
	}

	// Check if all nodes were processed (no cycles)
	if len(result) != len(g.nodes) {
		return nil, fmt.Errorf("cycle detected in workflow graph")
	}

	return result, nil
}

// validateAcyclic checks if the graph contains cycles using DFS.
func (g *Graph) validateAcyclic() error {
	white := make(map[string]bool) // unvisited
	gray := make(map[string]bool)  // visiting
	black := make(map[string]bool) // visited

	// Initialize all nodes as white (unvisited)
	for nodeID := range g.nodes {
		white[nodeID] = true
	}

	// DFS from each unvisited node
	for nodeID := range white {
		if white[nodeID] {
			if err := g.dfsVisit(nodeID, white, gray, black); err != nil {
				return err
			}
		}
	}

	return nil
}

// dfsVisit performs depth-first search to detect cycles.
func (g *Graph) dfsVisit(nodeID string, white, gray, black map[string]bool) error {
	// Move node from white to gray
	white[nodeID] = false
	gray[nodeID] = true

	// Visit all successors
	for _, successor := range g.edges[nodeID] {
		if gray[successor] {
			// Back edge found - cycle detected
			return fmt.Errorf("cycle detected: edge from %s to %s creates a cycle", nodeID, successor)
		}

		if white[successor] {
			if err := g.dfsVisit(successor, white, gray, black); err != nil {
				return err
			}
		}
	}

	// Move node from gray to black
	gray[nodeID] = false
	black[nodeID] = true

	return nil
}

// FindPath finds a path between two nodes using BFS.
func (g *Graph) FindPath(fromNode, toNode string) ([]string, bool) {
	if fromNode == toNode {
		return []string{fromNode}, true
	}

	visited := make(map[string]bool)
	parent := make(map[string]string)
	queue := []string{fromNode}
	visited[fromNode] = true

	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]

		for _, successor := range g.edges[current] {
			if !visited[successor] {
				visited[successor] = true
				parent[successor] = current
				queue = append(queue, successor)

				if successor == toNode {
					// Reconstruct path
					var path []string
					node := toNode
					for node != "" {
						path = append([]string{node}, path...)
						node = parent[node]
					}
					return path, true
				}
			}
		}
	}

	return nil, false
}

// GetReachableNodes returns all nodes reachable from the given node.
func (g *Graph) GetReachableNodes(fromNode string) []string {
	visited := make(map[string]bool)
	var result []string

	g.dfsReachable(fromNode, visited, &result)

	// Remove the starting node itself
	var filtered []string
	for _, node := range result {
		if node != fromNode {
			filtered = append(filtered, node)
		}
	}

	sort.Strings(filtered)
	return filtered
}

// dfsReachable performs DFS to find all reachable nodes.
func (g *Graph) dfsReachable(nodeID string, visited map[string]bool, result *[]string) {
	visited[nodeID] = true
	*result = append(*result, nodeID)

	for _, successor := range g.edges[nodeID] {
		if !visited[successor] {
			g.dfsReachable(successor, visited, result)
		}
	}
}

// GetNodeDependencies returns all nodes that must be completed before the given node can execute.
func (g *Graph) GetNodeDependencies(nodeID string) []string {
	visited := make(map[string]bool)
	var result []string

	// DFS through reverse edges to find all dependencies
	g.dfsReverseDependencies(nodeID, visited, &result)

	// Remove the target node itself
	var filtered []string
	for _, node := range result {
		if node != nodeID {
			filtered = append(filtered, node)
		}
	}

	sort.Strings(filtered)
	return filtered
}

// dfsReverseDependencies performs DFS through reverse edges to find dependencies.
func (g *Graph) dfsReverseDependencies(nodeID string, visited map[string]bool, result *[]string) {
	visited[nodeID] = true
	*result = append(*result, nodeID)

	for _, predecessor := range g.reverseEdges[nodeID] {
		if !visited[predecessor] {
			g.dfsReverseDependencies(predecessor, visited, result)
		}
	}
}

// IsReadyToExecute checks if a node is ready to execute based on the current execution state.
func (g *Graph) IsReadyToExecute(nodeID string, state *ExecutionState) bool {
	// Check if node has already been executed
	if state.IsNodeCompleted(nodeID) || state.IsNodeFailed(nodeID) {
		return false
	}

	// Check if all predecessor nodes have completed successfully
	for _, predecessor := range g.reverseEdges[nodeID] {
		if !state.IsNodeCompleted(predecessor) {
			return false
		}
	}

	return true
}

// GetNextExecutableNodes returns nodes that are ready to be executed.
func (g *Graph) GetNextExecutableNodes(state *ExecutionState) []string {
	var ready []string

	for nodeID := range g.nodes {
		if g.IsReadyToExecute(nodeID, state) {
			ready = append(ready, nodeID)
		}
	}

	sort.Strings(ready)
	return ready
}

// ValidateNodeTypes checks if all node types in the graph are registered.
func (g *Graph) ValidateNodeTypes(registry NodeRegistry) error {
	for nodeID, node := range g.nodes {
		if registry.Get(node.Type) == nil {
			return fmt.Errorf("node %s has unregistered type: %s", nodeID, node.Type)
		}
	}
	return nil
}

// GetStats returns statistics about the graph.
func (g *Graph) GetStats() GraphStats {
	stats := GraphStats{
		NodeCount: len(g.nodes),
		EdgeCount: 0,
	}

	// Count edges
	for _, successors := range g.edges {
		stats.EdgeCount += len(successors)
	}

	// Count node types
	stats.NodeTypes = make(map[string]int)
	for _, node := range g.nodes {
		stats.NodeTypes[node.Type]++
	}

	// Find max depth
	roots := g.GetRootNodes()
	for _, root := range roots {
		depth := g.getMaxDepth(root, make(map[string]int))
		if depth > stats.MaxDepth {
			stats.MaxDepth = depth
		}
	}

	return stats
}

// getMaxDepth calculates the maximum depth from a given node.
func (g *Graph) getMaxDepth(nodeID string, memo map[string]int) int {
	if depth, exists := memo[nodeID]; exists {
		return depth
	}

	maxDepth := 0
	for _, successor := range g.edges[nodeID] {
		depth := g.getMaxDepth(successor, memo)
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	depth := maxDepth + 1
	memo[nodeID] = depth
	return depth
}

// GraphStats contains statistics about a workflow graph.
type GraphStats struct {
	NodeCount int            `json:"nodeCount"`
	EdgeCount int            `json:"edgeCount"`
	MaxDepth  int            `json:"maxDepth"`
	NodeTypes map[string]int `json:"nodeTypes"`
}

// String returns a string representation of the graph statistics.
func (gs GraphStats) String() string {
	return fmt.Sprintf("Nodes: %d, Edges: %d, Max Depth: %d, Types: %v",
		gs.NodeCount, gs.EdgeCount, gs.MaxDepth, gs.NodeTypes)
}
