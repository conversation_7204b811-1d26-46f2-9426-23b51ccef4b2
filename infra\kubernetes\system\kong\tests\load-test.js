/*
 * CINA.CLUB Kong Gateway - Load Test
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-27 12:00:00
 * Modified: 2025-01-27 12:00:00
 * 
 * Platform engineering K6 load test for Kong Gateway
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

// Test configuration
export const options = {
  stages: [
    { duration: '1m', target: 10 },   // Ramp up to 10 users
    { duration: '3m', target: 10 },   // Stay at 10 users
    { duration: '1m', target: 50 },   // Ramp up to 50 users
    { duration: '5m', target: 50 },   // Stay at 50 users
    { duration: '1m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate must be below 10%
    errors: ['rate<0.1'],             // Custom error rate must be below 10%
  },
};

// Test configuration
const BASE_URL = __ENV.KONG_PROXY_URL || 'http://kong-proxy.kong-system.svc.cluster.local:8000';

// Test scenarios
export default function() {
  // Test 1: Health check endpoint
  testHealthEndpoint();
  
  // Test 2: Public endpoints (no auth required)
  testPublicEndpoints();
  
  // Test 3: Rate limiting behavior
  testRateLimiting();
  
  // Test 4: CORS preflight requests
  testCorsPreflights();
  
  sleep(1);
}

function testHealthEndpoint() {
  const response = http.get(`${BASE_URL}/health`, {
    headers: {
      'User-Agent': 'k6-kong-load-test',
    },
  });
  
  const result = check(response, {
    'health endpoint status is 200': (r) => r.status === 200,
    'health endpoint response time < 200ms': (r) => r.timings.duration < 200,
  });
  
  errorRate.add(!result);
}

function testPublicEndpoints() {
  // Test authentication endpoints (should be public)
  const authEndpoints = [
    '/api/v1/auth/login',
    '/api/v1/auth/register',
  ];
  
  authEndpoints.forEach(endpoint => {
    const response = http.post(`${BASE_URL}${endpoint}`, {
      email: '<EMAIL>',
      password: 'testpassword',
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'k6-kong-load-test',
      },
    });
    
    // We expect these to fail with validation errors, not auth errors
    const result = check(response, {
      [`${endpoint} does not return auth error`]: (r) => r.status !== 401,
      [`${endpoint} response time < 500ms`]: (r) => r.timings.duration < 500,
    });
    
    errorRate.add(!result);
  });
}

function testRateLimiting() {
  // Make rapid requests to test rate limiting
  const responses = [];
  
  for (let i = 0; i < 20; i++) {
    const response = http.get(`${BASE_URL}/api/v1/health`, {
      headers: {
        'User-Agent': 'k6-rate-limit-test',
      },
    });
    responses.push(response);
  }
  
  // Check if rate limiting kicks in
  const rateLimitedResponses = responses.filter(r => r.status === 429);
  
  check(null, {
    'rate limiting is working': () => rateLimitedResponses.length > 0,
  });
}

function testCorsPreflights() {
  const response = http.options(`${BASE_URL}/api/v1/health`, null, {
    headers: {
      'Origin': 'https://cina.club',
      'Access-Control-Request-Method': 'GET',
      'Access-Control-Request-Headers': 'Authorization',
    },
  });
  
  const result = check(response, {
    'CORS preflight status is 200 or 204': (r) => r.status === 200 || r.status === 204,
    'CORS preflight has Access-Control-Allow-Origin': (r) => 
      r.headers['Access-Control-Allow-Origin'] !== undefined,
    'CORS preflight response time < 100ms': (r) => r.timings.duration < 100,
  });
  
  errorRate.add(!result);
}

// Setup function - runs once before all VUs
export function setup() {
  console.log('Starting Kong Gateway load test against:', BASE_URL);
  
  // Verify that Kong Gateway is accessible
  const response = http.get(`${BASE_URL}/health`);
  if (response.status !== 200) {
    throw new Error(`Kong Gateway is not accessible. Status: ${response.status}`);
  }
  
  return { baseUrl: BASE_URL };
}

// Teardown function - runs once after all VUs finish
export function teardown(data) {
  console.log('Kong Gateway load test completed for:', data.baseUrl);
} 