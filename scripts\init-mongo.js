// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

// CINA.CLUB MongoDB 初始化脚本

// 创建内容数据库
db = db.getSiblingDB('cina_content');

// 创建用户
db.createUser({
  user: 'cina_content_user',
  pwd: 'cina_content_password',
  roles: [
    {
      role: 'readWrite',
      db: 'cina_content'
    }
  ]
});

// 创建集合
db.createCollection('articles');
db.createCollection('comments');
db.createCollection('media_files');
db.createCollection('user_content');

// 创建索引
db.articles.createIndex({ "title": "text", "content": "text" });
db.articles.createIndex({ "author_id": 1 });
db.articles.createIndex({ "created_at": -1 });
db.articles.createIndex({ "tags": 1 });

db.comments.createIndex({ "article_id": 1 });
db.comments.createIndex({ "author_id": 1 });
db.comments.createIndex({ "created_at": -1 });

db.media_files.createIndex({ "owner_id": 1 });
db.media_files.createIndex({ "file_type": 1 });
db.media_files.createIndex({ "upload_date": -1 });

db.user_content.createIndex({ "user_id": 1 });
db.user_content.createIndex({ "content_type": 1 });
db.user_content.createIndex({ "created_at": -1 });

// 插入示例数据
db.articles.insertMany([
  {
    _id: ObjectId(),
    title: "Welcome to CINA.CLUB",
    content: "This is the first article in our platform.",
    author_id: "system",
    tags: ["welcome", "announcement"],
    status: "published",
    created_at: new Date(),
    updated_at: new Date()
  }
]);

print("MongoDB initialization completed successfully."); 