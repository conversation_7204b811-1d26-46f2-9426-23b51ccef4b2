好的，遵照您的指示。我将为您生成一份专门针对 **`core/datasync`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`core/datasync` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到代码实现的层面，详细阐述**客户端同步引擎的实现、版本向量(CRDT)冲突检测算法、内容定义分块(CDC)的应用，以及如何与`core/crypto`和API客户端协同工作**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `core/datasync` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `core/datasync-srs.md` (v1.0)
**核心架构**: 客户端同步引擎 + 版本向量(CRDT) + 内容定义分块(CDC)

## 1. 概述

`core/datasync` 是CINA.CLUB平台**E2EE数据同步的客户端核心逻辑库**。它是一个纯粹的、平台无关的Go包，负责实现与后端`cloud-sync-service`进行数据交换的复杂协议。其架构设计的核心目标是：
1.  **协议的精确实现**: 必须100%准确地实现与`cloud-sync-service`约定的、基于版本向量的同步协议。
2.  **效率**: 通过内容定义分块(CDC)和checksum去重，最大限度地减少不必要的数据上传，节省用户带宽和云端存储成本。
3.  **可靠性与原子性**: `push`和`pull`等核心操作必须是原子性的。任何步骤的失败都不能让本地或远程数据处于不一致的中间状态。
4.  **与加密解耦**: 同步逻辑本身不应关心数据是如何加密的。它只负责在适当的时候调用`core/crypto`提供的加密/解密接口。
5.  **易于集成的API**: 为上层的前端封装模块（`packages/go-core-wrapper`）提供简单、清晰的接口。

本架构设计通过构建一个**`SyncEngine`作为核心协调者**，并结合独立的**`VersionVector`和`Chunker`领域服务**，来实现一个健壮、高效的客户端同步引擎。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (Push Flow)

```mermaid
graph TD
    subgraph "上层调用者 (e.g., go-core-wrapper)"
        A[App Logic Layer]
    end

    subgraph "core/datasync (This Package)"
        style "core/datasync (This Package)" fill:#e0f7fa
        B[SyncEngine]
        C[VersionVector Service]
        D[Chunker Service]
    end
    
    subgraph "core/* (Dependencies)"
        style "core/* (Dependencies)" fill:#f3e5f5
        E[core/crypto]
        F[core/api (CloudSyncServiceClient)]
    end

    subgraph "后端服务"
        G[cloud-sync-service]
    end

    A -- "1. Push(itemData)" --> B
    
    B -- "2. Encrypt item data" --> E
    E -- "Returns encryptedData" --> B
    
    B -- "3. Chunk encrypted data" --> D
    D -- "Returns chunks with checksums" --> B

    B -- "4. Prepare PushRequest (metadata & checksums)" --> F
    F -- "gRPC Call to /push" --> G
    
    G -- "Returns missing_checksums & upload_urls" --> F
    
    B -- "5. Upload missing chunks" --> B
    note right of B
      For each missing chunk:
      - App layer gets data from local cache
      - Uploads directly to S3
    end
    
    B -- "6. Finalize push" --> F
    F -- "gRPC Call to /finalize" --> G
    
    G -- "Returns new VersionVector" --> F
    F -- "Returns new VersionVector" --> B
    B -- "Returns to caller" --> A
```

### 2.2 最终目录结构 (`core/datasync/`)

```
core/datasync/
├── engine.go               # ✨ SyncEngine的定义和核心Push/Pull流程 ✨
├── version_vector.go       # ✨ VersionVector的定义和比较/合并算法 ✨
├── chunker.go              # ✨ 内容定义分块(FastCDC)的实现 ✨
├── interface.go            # 定义本包需要外部实现的接口 (如CryptoProvider, APIClient)
├── model.go                # 定义本包内部使用的模型 (如PushableItem)
├── exports_mobile.go       # 导出给Go Mobile的接口
├── exports_wasm.go         # 导出给WASM的接口
└── datasync_test.go        # 单元测试与集成测试
```

---

## 3. 各层职责深度解析

### 3.1 `version_vector.go` - CRDT核心

*   **职责**: 实现无冲突复制数据类型(CRDT)的一种形式——版本向量。
*   **`VersionVector` type**: `type VersionVector map[string]int64`。
*   **`Compare(other VersionVector) ComparisonResult` method**:
    *   **实现**: 必须能正确识别`Ancestor`（本地版本旧）, `Descendant`（本地版本新）, `Equal`（版本相同）, 和 **`Conflict`（并发修改）** 这四种情况。
    *   **逻辑**:
        1.  初始化`hasLesser=false`, `hasGreater=false`。
        2.  遍历并集的所有`deviceID`。
        3.  比较`v1[id]`和`v2[id]`。
        4.  如果`v1[id] < v2[id]`, 设置`hasLesser=true`。
        5.  如果`v1[id] > v2[id]`, 设置`hasGreater=true`。
        6.  **最终判断**:
            *   `!hasLesser && !hasGreater` -> `Equal`
            *   `hasLesser && !hasGreater` -> `Ancestor`
            *   `!hasLesser && hasGreater` -> `Descendant`
            *   `hasLesser && hasGreater` -> **`Conflict`**

### 3.2 `chunker.go` - 高效分块器

*   **职责**: 将任意二进制数据高效地分割成内容定义的块。
*   **`ChunkData(data []byte, avgSize uint)` function**:
    *   **底层库**: **必须**使用`github.com/fastcdc/fastcdc`的Go实现。
    *   **哈希算法**: **必须**使用**SHA-256**。
    *   **实现**:
        1.  初始化FastCDC分块器。
        2.  使用`io.Reader`将`data`喂给分块器。
        3.  在一个循环中，接收分块器输出的每个数据块。
        4.  对于每个块，计算其SHA-256哈希值。
        5.  将`{Checksum, Data}`存入结果切片。

### 3.3 `engine.go` - 同步引擎

*   **`interface.go`**: 定义`SyncEngine`所需的依赖接口。
    ```go
    type CryptoProvider interface {
        Encrypt(data []byte) ([]byte, error)
        Decrypt(data []byte) ([]byte, error)
    }
    type APIClient interface {
        Push(ctx, req) (*PushResponse, error)
        Finalize(ctx, req) (*FinalizeResponse, error)
        // ...
    }
    type Uploader interface {
        UploadChunk(ctx, url string, data []byte) error
    }
    ```
    **设计决策**: 通过接口定义依赖，使得`SyncEngine`可以被独立测试，只需传入Mock的`CryptoProvider`和`APIClient`。
*   **`engine.go`**:
    *   **`SyncEngine` struct**: 包含`CryptoProvider`, `APIClient`, `Uploader`等接口类型的字段。
    *   **`Push(ctx, item PushableItem)` method**:
        1.  **加密**: `encrypted, err := s.crypto.Encrypt(item.Data)`。
        2.  **分块**: `chunks, err := chunker.ChunkData(encrypted, ...)`。
        3.  **第一阶段API调用 (`Push`)**:
            *   构造`PushRequest`，包含item元数据、版本向量和**所有块的checksums**。
            *   `resp, err := s.apiClient.Push(ctx, req)`。
            *   **处理冲突**: 如果`err`是`ConflictError`，则解析出冲突信息并立即返回给上层。
        4.  **上传缺失块**:
            *   `missingChunks := findMissing(chunks, resp.MissingChecksums)`。
            *   **并行上传**: 使用`errgroup`并行地为每个`missingChunk`调用`s.uploader.UploadChunk(...)`。任何一个上传失败都会导致整个`Push`操作失败。
        5.  **第二阶段API调用 (`Finalize`)**:
            *   如果所有块都上传成功，则调用`s.apiClient.Finalize(ctx, ...)`。
            *   返回`Finalize`调用成功后，服务端返回的最新版本向量。
    *   **`Pull(ctx, ...)` method**: 流程类似SRS，编排对`pull` API的调用和后续的数据下载与解密。

### 3.4 跨平台导出层 (`exports_*.go`)

*   **职责**: 将`SyncEngine`的复杂`struct`输入和输出，转换为Go Mobile/WASM支持的**简单类型（如JSON字符串）**。
*   **`MobileSyncEngine` struct**:
    *   包装一个`*SyncEngine`。
*   **`PushJSON(itemJSON string) (newVersionJSON string, errorJSON string)` method**:
    1.  将输入的`itemJSON`反序列化为`PushableItem` `struct`。
    2.  调用`internalEngine.Push(...)`。
    3.  将返回的`newVersionVector`和`error`分别序列化为JSON字符串。
    4.  返回这两个字符串。
    *   **前端的`go-core-wrapper`会负责这个JSON的序列化和反序列化过程**。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`core/datasync`：
1.  **精确的协议实现**: `VersionVector`和`Chunker`的实现，精确地对应了`cloud-sync-service`的协议要求，保证了客户端与服务器的正确协同。
2.  **分阶段原子操作**: 将`Push`操作清晰地拆分为**“声明变更(Push API)” -> “上传数据” -> “确认变更(Finalize API)”**三个阶段，并通过服务端的锁机制，保证了即使在并发环境下，每个`Push`操作也是原子性的。
3.  **依赖注入与可测试性**: `SyncEngine`通过接口来声明其对加密、网络和上传的依赖，使得其核心的、复杂的协议流程可以被完全独立地、可靠地进行单元测试。
4.  **为跨平台设计**: 通过一个专门的`exports`层，将复杂的Go `struct`与前端世界支持的简单JSON字符串进行转换，解决了跨语言调用的主要障碍。

这种架构确保了`core/datasync`能够作为一个**高效、可靠、且易于集成**的同步引擎，为CINA.CLUB所有前端平台提供坚实的E2EE数据同步与备份能力。