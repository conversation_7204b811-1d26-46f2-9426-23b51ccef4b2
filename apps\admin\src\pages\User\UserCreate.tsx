/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Switch, 
  Space, 
  message, 
  Row, 
  Col,
  Divider,
  Typography
} from 'antd'
import { ArrowLeftOutlined, SaveOutlined, SendOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

import { CreateUserFormData, createUserSchema } from '@/lib/validation'
import { UserRole } from '@/types/user'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { Option } = Select
const { TextArea } = Input
const { Title } = Typography

/**
 * 创建用户页面
 */
const UserCreate: React.FC = () => {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const { hasPermission } = usePermission()
  const [loading, setLoading] = useState(false)
  const [sendInvitation, setSendInvitation] = useState(true)

  // 权限检查
  const canCreate = hasPermission(Permission.USER_CREATE)

  // 角色选项
  const roleOptions = [
    { label: '超级管理员', value: UserRole.SUPER_ADMIN, disabled: true }, // 通常限制创建
    { label: '管理员', value: UserRole.ADMIN },
    { label: '运营人员', value: UserRole.OPERATIONS },
    { label: '客服人员', value: UserRole.CUSTOMER_SERVICE },
    { label: '内容审核员', value: UserRole.CONTENT_MODERATOR },
    { label: '数据分析师', value: UserRole.ANALYST },
    { label: '查看者', value: UserRole.VIEWER },
  ]

  // 表单提交
  const handleSubmit = async (values: CreateUserFormData) => {
    try {
      setLoading(true)
      
      // 验证表单数据
      const validation = createUserSchema.safeParse(values)
      if (!validation.success) {
        const errors = validation.error.errors
        errors.forEach(error => {
          message.error(`${error.path.join('.')}: ${error.message}`)
        })
        return
      }

      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      message.success(
        sendInvitation 
          ? '用户创建成功！邀请邮件已发送到用户邮箱。' 
          : '用户创建成功！'
      )
      
      navigate('/users')
    } catch (error) {
      message.error('创建用户失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 表单验证失败
  const handleSubmitFailed = (errorInfo: any) => {
    message.error('请检查表单中的错误信息')
    console.log('表单验证失败:', errorInfo)
  }

  if (!canCreate) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限创建用户</p>
        <Button onClick={() => navigate('/users')}>返回用户列表</Button>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/users')}
            >
              返回
            </Button>
            <Title level={3} style={{ margin: 0 }}>创建新用户</Title>
          </Space>
        </Col>
      </Row>

      {/* 创建表单 */}
      <Row gutter={24}>
        <Col xs={24} lg={16}>
          <Card title="基本信息" style={{ marginBottom: '24px' }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              onFinishFailed={handleSubmitFailed}
              initialValues={{
                sendInvitation: true,
                roles: [UserRole.VIEWER], // 默认角色
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="姓"
                    name="firstName"
                    rules={[
                      { required: true, message: '请输入姓' },
                      { min: 1, max: 50, message: '姓的长度应在1-50个字符之间' },
                    ]}
                  >
                    <Input placeholder="请输入姓" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="名"
                    name="lastName"
                    rules={[
                      { required: true, message: '请输入名' },
                      { min: 1, max: 50, message: '名的长度应在1-50个字符之间' },
                    ]}
                  >
                    <Input placeholder="请输入名" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="用户名"
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, max: 20, message: '用户名长度应在3-20个字符之间' },
                  { pattern: /^[a-zA-Z0-9_-]+$/, message: '用户名只能包含字母、数字、下划线和横线' },
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>

              <Form.Item
                label="邮箱地址"
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>

              <Form.Item
                label="手机号"
                name="phone"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
                ]}
              >
                <Input placeholder="请输入手机号（可选）" />
              </Form.Item>

              <Divider />

              <Form.Item
                label="用户角色"
                name="roles"
                rules={[
                  { required: true, message: '请至少选择一个角色' },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="请选择用户角色"
                  style={{ width: '100%' }}
                >
                  {roleOptions.map(option => (
                    <Option 
                      key={option.value} 
                      value={option.value}
                      disabled={option.disabled}
                    >
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Divider />

              <Form.Item
                label="发送邀请邮件"
                name="sendInvitation"
                valuePropName="checked"
              >
                <Switch 
                  checked={sendInvitation}
                  onChange={setSendInvitation}
                />
              </Form.Item>

              {sendInvitation && (
                <Form.Item
                  label="邀请信息"
                  name="invitationMessage"
                >
                  <TextArea 
                    rows={3}
                    placeholder="请输入邀请信息（可选）"
                    maxLength={500}
                    showCount
                  />
                </Form.Item>
              )}

              {!sendInvitation && (
                <Form.Item
                  label="临时密码"
                  name="temporaryPassword"
                  rules={[
                    { required: !sendInvitation, message: '请输入临时密码' },
                    { min: 8, message: '密码长度至少8位' },
                  ]}
                >
                  <Input.Password placeholder="请输入临时密码" />
                </Form.Item>
              )}

              {/* 表单操作按钮 */}
              <Form.Item style={{ marginTop: '32px' }}>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    icon={<SaveOutlined />}
                  >
                    创建用户
                  </Button>
                  <Button onClick={() => navigate('/users')}>
                    取消
                  </Button>
                  <Button 
                    type="dashed"
                    onClick={() => form.resetFields()}
                  >
                    重置表单
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 侧边栏信息 */}
        <Col xs={24} lg={8}>
          <Card title="创建说明" style={{ marginBottom: '24px' }}>
            <Space direction="vertical" size="middle">
              <div>
                <h4>💡 创建流程</h4>
                <ul style={{ paddingLeft: '16px', margin: 0 }}>
                  <li>填写用户基本信息</li>
                  <li>分配适当的角色权限</li>
                  <li>选择账户激活方式</li>
                  <li>发送邀请或设置临时密码</li>
                </ul>
              </div>
              
              <div>
                <h4>🔒 角色说明</h4>
                <ul style={{ paddingLeft: '16px', margin: 0 }}>
                  <li><strong>管理员</strong>：拥有大部分管理权限</li>
                  <li><strong>运营人员</strong>：负责业务运营</li>
                  <li><strong>客服人员</strong>：处理客户服务</li>
                  <li><strong>内容审核员</strong>：审核内容</li>
                  <li><strong>数据分析师</strong>：查看分析数据</li>
                  <li><strong>查看者</strong>：只读权限</li>
                </ul>
              </div>

              <div>
                <h4>📧 邀请方式</h4>
                <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
                  <strong>发送邀请邮件</strong>：用户将收到邮件邀请，需要设置密码后激活账户。
                </p>
                <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '14px' }}>
                  <strong>设置临时密码</strong>：直接创建账户，用户使用临时密码登录后需要修改密码。
                </p>
              </div>
            </Space>
          </Card>

          <Card title="安全提醒">
            <Space direction="vertical" size="small">
              <div style={{ fontSize: '14px', color: '#faad14' }}>
                ⚠️ 请谨慎分配管理员权限
              </div>
              <div style={{ fontSize: '14px', color: '#1890ff' }}>
                🛡️ 建议启用双因素认证
              </div>
              <div style={{ fontSize: '14px', color: '#52c41a' }}>
                ✅ 定期审查用户权限
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default UserCreate 