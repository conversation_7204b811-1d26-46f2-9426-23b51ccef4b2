#!/bin/bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# PostgreSQL 多数据库初始化脚本
# 为各个微服务创建独立的数据库

set -e

# 解析环境变量中的数据库列表
if [ -n "$POSTGRES_MULTIPLE_DATABASES" ]; then
    echo "Creating multiple databases: $POSTGRES_MULTIPLE_DATABASES"
    
    # 将逗号分隔的数据库名称转换为数组
    IFS=',' read -ra DATABASES <<< "$POSTGRES_MULTIPLE_DATABASES"
    
    for db in "${DATABASES[@]}"; do
        # 去除空格
        db=$(echo "$db" | xargs)
        
        if [ -n "$db" ]; then
            echo "Creating database: $db"
            
            # 创建数据库
            psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
                CREATE DATABASE "$db";
                GRANT ALL PRIVILEGES ON DATABASE "$db" TO "$POSTGRES_USER";
EOSQL
            
            echo "Database $db created successfully"
        fi
    done
    
    echo "All databases created successfully!"
else
    echo "POSTGRES_MULTIPLE_DATABASES not set, skipping database creation"
fi

# 创建扩展（如果需要）
echo "Creating extensions..."

# 为主数据库启用扩展
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm";
    CREATE EXTENSION IF NOT EXISTS "btree_gin";
EOSQL

# 为各个服务数据库启用扩展
if [ -n "$POSTGRES_MULTIPLE_DATABASES" ]; then
    IFS=',' read -ra DATABASES <<< "$POSTGRES_MULTIPLE_DATABASES"
    
    for db in "${DATABASES[@]}"; do
        db=$(echo "$db" | xargs)
        
        if [ -n "$db" ]; then
            echo "Setting up extensions for database: $db"
            
            psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$db" <<-EOSQL
                CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
                CREATE EXTENSION IF NOT EXISTS "pg_trgm";
                CREATE EXTENSION IF NOT EXISTS "btree_gin";
                CREATE EXTENSION IF NOT EXISTS "pgcrypto";
EOSQL
        fi
    done
fi

echo "Extensions setup completed!"

# 创建开发用户和角色（可选）
echo "Setting up development users..."

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- 创建只读用户
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'readonly_user') THEN
            CREATE ROLE readonly_user WITH LOGIN PASSWORD 'readonly123';
        END IF;
    END
    \$\$;

    -- 创建应用用户
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'app_user') THEN
            CREATE ROLE app_user WITH LOGIN PASSWORD 'app123';
        END IF;
    END
    \$\$;

    -- 授权
    GRANT CONNECT ON DATABASE "$POSTGRES_DB" TO readonly_user;
    GRANT CONNECT ON DATABASE "$POSTGRES_DB" TO app_user;
EOSQL

# 为各个服务数据库设置权限
if [ -n "$POSTGRES_MULTIPLE_DATABASES" ]; then
    IFS=',' read -ra DATABASES <<< "$POSTGRES_MULTIPLE_DATABASES"
    
    for db in "${DATABASES[@]}"; do
        db=$(echo "$db" | xargs)
        
        if [ -n "$db" ]; then
            echo "Setting up permissions for database: $db"
            
            psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$db" <<-EOSQL
                -- 为只读用户授权
                GRANT CONNECT ON DATABASE "$db" TO readonly_user;
                GRANT USAGE ON SCHEMA public TO readonly_user;
                GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
                ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO readonly_user;

                -- 为应用用户授权
                GRANT CONNECT ON DATABASE "$db" TO app_user;
                GRANT USAGE, CREATE ON SCHEMA public TO app_user;
                GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
                GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO app_user;
                ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO app_user;
                ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO app_user;
EOSQL
        fi
    done
fi

echo "PostgreSQL initialization completed successfully!" 