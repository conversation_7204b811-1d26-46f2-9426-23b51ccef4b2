好的，遵照您的指示。我将为您生成一份专门针对 **`shared-kb-service` (CKB)** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`shared-kb-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**结构化的内容策展工作流、与`billing-service`集成的多模式商业化访问控制、作为AI助手权威知识源的集成方式，以及内容版本控制与分析反馈**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `shared-kb-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `shared-kb-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 内容策展工作流 + 访问控制代理

## 1. 概述

`shared-kb-service` (CKB) 是CINA.CLUB平台的**内容价值高地**和**权威知识资产库**。它负责管理由平台官方或认证专家创作的高质量、结构化知识。其核心挑战在于：
1.  **专业的内容生命周期管理**: 需要支持一个完整的策展工作流，包括草稿、审核、发布、版本化和回滚。
2.  **灵活的商业化与访问控制**: 需要与`billing-service`深度集成，实现基于订阅、按次付费等多种复杂的访问控制策略。
3.  **高性能与高可用**: 作为AI助手和用户查询的权威信息源，其读取性能和可用性至关重要。
4.  **结构化与多模态内容**: 需要能处理类似Notion的、基于块(Block)的富文本内容，并嵌入多种媒体。
5.  **与AI生态的深度融合**: 既要作为AI的知识源，也要利用AI来增强内容（如生成摘要、翻译）。

本架构设计通过采用**整洁架构**，在领域层实现一个**内容策展状态机**，并在应用层作为一个**访问控制代理**，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (付费内容访问与AI增强流程)

```mermaid
graph TD
    subgraph "用户/AI"
        ClientApp
        AIAssistant[ai-assistant-service]
    end

    subgraph "SharedKBService (Access Control Proxy)"
        style SharedKBService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[QueryService<br/><em>application/query</em>]
        C[CommandService<br/><em>application/command</em>]
        D[CKBItemAggregate<br/><em>domain/aggregate</em>]
        E[Repository<br/><em>adapter/repository</em>]
        F[BillingClient<br/><em>adapter/client</em>]
        G[AIClient<br/><em>adapter/client</em>]
        H[Kafka Producer<br/><em>adapter/event</em>]
    end

    subgraph "平台协同服务"
        style "平台协同服务" fill:#f3e5f5
        BillingSvc[billing-service]
        AISvc_Upstream[ai-assistant-service]
        SearchIndexer[search-indexer-service]
    end

    %% Read Flow (Access Control)
    ClientApp -- "1. GetItem(itemId)" --> A
    A -- "调用" --> B
    B -- "2. Get item metadata (incl. access_policy)" --> E
    B -- "3. ✨ Check Access via BillingSvc ✨" --> F
    F -- "gRPC Call to /internal/access-check" --> BillingSvc
    alt "Access Granted"
        BillingSvc -- "{granted: true}" --> F
        B -- "4. Get full content from DB" --> E
        B -- "5. Return full content" --> A
    else "Payment Required"
        BillingSvc -- "{granted: false, purchase_info}" --> F
        B -- "6. Return preview + purchase_info" --> A
    end
    
    %% Write Flow (Curation & AI Enhancement)
    AdminUser -- "7. Create/Update Item" --> A
    A -- "调用" --> C
    C -- "8. Update CKBItem Aggregate" --> D & E
    C -- "9. Publish CKBItemUpdatedEvent" --> H
    H --> Kafka[(Kafka)]
    
    Kafka -- "10a. Trigger AI Enhancement" --> G
    G -- "gRPC Call to" --> AISvc_Upstream
    AISvc_Upstream -- "Returns summary/tags" --> G
    G -- "Update DB" --> E

    Kafka -- "10b. Trigger Indexing" --> SearchIndexer
```

### 2.2 最终目录结构 (`services/shared-kb-service/`)

```
shared-kb-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # ✨ 异步AI增强任务的独立Worker入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── billing_client.go
│   │   │   └── ai_assistant_client.go
│   │   ├── event/
│   │   │   ├── producer.go
│   │   │   └── ai_result_consumer.go # (可选)消费AI处理结果
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── postgres_repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── command/
│   │   │   └── ckb_command_service.go
│   │   └── query/
│   │       └── ckb_query_service.go
│   └── domain/
│       ├── aggregate/
│       │   └── ckb_item_aggregate.go # ✨ 封装内容策展状态机和版本控制 ✨
│       └── model/
│           └── alias.go
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Knowledge & Curation Rules)

*   `domain/model/`: 使用`/core/models`中与CKB相关的`struct`，如`CKBItem`, `ContentBlock`。
*   **`domain/aggregate/ckb_item_aggregate.go`**: **这是内容生命周期管理的核心**。
    *   **`CKBItem`聚合根**: 封装了`CKBItem`实体及其所有历史版本`CKBItemVersion`。
    *   **策展状态机方法**:
        *   `SubmitForReview(actor)`: 将状态从`DRAFT`变为`PENDING_REVIEW`。
        *   `Publish(actor)`: **必须**检查`actor`是否具有`CURATOR`或`ADMIN`角色。通过后，创建一个新的`Published`版本，并将主条目状态更新为`PUBLISHED`。
        *   `CreateNewVersion(actor, newContent)`: 基于当前已发布的版本，创建一个新的`DRAFT`版本供编辑。
        *   `RollbackToVersion(actor, versionNumber)`: 将一个旧的、已发布的版本重新设置为当前活跃版本。
    *   每个状态变更方法都会生成对应的领域事件，如`CKBItemPublishedEvent`。

### 3.2 `application/` - 应用层 (The Access Control & Curation Workflow)

采用CQRS思想，分离读写路径。

*   **`application/command/ckb_command_service.go`**: **处理所有写操作**。
    *   **`CreateItem(ctx, authorID, itemData)`**: 创建一个`CKBItem`聚合根，初始版本状态为`DRAFT`。
    *   **`UpdateItem(ctx, actorID, itemID, updateData)`**:
        1.  从仓储加载`CKBItem`聚合根。
        2.  调用`item.CreateNewVersion(actorID, updateData)`等领域方法来执行变更。
        3.  将变更后的聚合根交由仓储持久化。
        4.  发布`CKBItemUpdatedEvent`到Kafka，触发**异步AI增强**。
*   **`application/query/ckb_query_service.go`**: **处理所有读操作，是访问控制的核心实现者**。
    *   **`GetItem(ctx, userID, itemID)`**:
        1.  **步骤1 (获取元数据)**: 调用`repository.GetItemMetadata(itemID)`，获取条目的基本信息，特别是其`access_policy`。
        2.  **如果`access_policy`是`FREE`**: 直接进入步骤4。
        3.  **步骤2 (权限检查)**:
            a. **必须**调用`billing_client.CheckAccess(userID, requiredFeature)`。`requiredFeature`根据`access_policy`动态构建（如`"ckb:item:uuid-123"`或`"subscription:premium"`)。
            b. `billing-service`返回一个包含`granted`布尔值和（如果需要支付）`purchaseInfo`的结果。
        4.  **步骤3 (决策)**:
            a. **如果`granted`为`true`**: 调用`repository.GetItemFullContent(itemID)`获取完整内容并返回。
            b. **如果`granted`为`false`**: 调用`repository.GetItemPreviewContent(itemID)`只获取预览部分（如前200字），并附上`purchaseInfo`一起返回给客户端，由客户端引导用户支付。
    *   **`InternalSearchForAI(ctx, queryVector)`**:
        *   这是一个为`ai-assistant-service`优化的内部接口。
        *   它直接调用仓储（或`search-service`），**绕过付费墙**，搜索所有`PUBLISHED`状态的内容，为AI提供最全面的上下文。**此接口的访问必须有严格的S2S认证和ACL控制**。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库**: **PostgreSQL**。其强大的事务能力和JSONB字段非常适合存储结构化的块内容和版本历史。
*   **`adapter/client/`**:
    *   `billing_client.go`: 封装对`billing-service`的`CheckAccess`和`RecordUsage` gRPC调用。
    *   `ai_assistant_client.go`: 封装对`ai-assistant-service`的调用，用于请求摘要、翻译等。
*   **`adapter/event/`**:
    *   `producer.go`: 发布`CKBItemUpdatedEvent`等事件。
    *   `ai_result_consumer.go` (可选): 如果AI增强是异步回调模式，则需要一个消费者来处理结果并更新数据库。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现gRPC接口，将请求路由到`QueryService`或`CommandService`。

### 3.4 `cmd/worker/` - 异步AI增强任务处理器

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   消费来自本服务发布的`CKBItemUpdatedEvent`。
    *   对于每个事件，检查其内容是否需要AI增强。
    *   如果需要，则调用`ai_assistant_client`执行相应任务（生成摘要、提取关键词、翻译）。
    *   将处理结果通过调用本服务的`commandService.UpdateAIEnrichments`方法写回数据库。
*   **设计决策**: 将AI增强异步化，避免了在主写流程中等待耗时的AI API调用，保证了作者保存/发布操作的快速响应。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`shared-kb-service`：
1.  **代理模式的访问控制**: 将本服务设计为`billing-service`的一个“代理”。它不实现任何计费逻辑，只负责查询权限，并根据结果决定返回何种内容。这使得访问控制逻辑与内容管理逻辑完全解耦。
2.  **专业的策展工作流**: 通过`CKBItem`聚合根和状态机，实现了内容从草稿到发布、再到版本化和回滚的完整、专业的生命周期管理，保证了内容质量。
3.  **结构化内容模型**: 采用类似Notion的块编辑器数据模型(JSONB)，为处理富文本、多模态内容提供了极大的灵活性和可扩展性。
4.  **AI能力的深度集成**:
    *   **作为消费者**: 通过异步Worker利用AI能力来**增强**内容质量。
    *   **作为提供者**: 通过专门的内部API，将高质量的CKB内容作为**权威知识源**提供给AI助手。
5.  **事件驱动的解耦**: 所有耗时的、或需要通知下游系统的操作（如AI增强、索引），都通过Kafka事件进行解耦，保证了核心服务的性能和韧性。

这种架构确保了`shared-kb-service`能够作为一个**内容权威、商业模式灵活、且与AI生态深度融合**的知识管理平台，为CINA.CLUB平台沉淀和变现其核心知识资产。