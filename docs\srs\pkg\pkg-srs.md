好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/` 目录** 的、详细完整的生产级软件需求/设计规格说明书（SRS）。

这份文档不同于单个微服务的SRS，它更侧重于**库的设计、接口的定义、代码的规范和使用的约束**。它将作为所有后端开发人员的“工具箱说明书”和“编码规范”，确保平台基础能力的统一、高效和可靠。

---
### CINA.CLUB - 共享后端库 (`pkg/`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-25**  
**文档负责人:** [平台总架构师/首席工程师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体设计原则](#2-总体设计原则)
3.  [模块化需求 (按包拆分)](#3-模块化需求-按包拆分)
    *   [3.1 `pkg/auth`: 认证与授权](#31-pkgauth-认证与授权)
    *   [3.2 `pkg/config`: 配置管理](#32-pkgconfig-配置管理)
    *   [3.3 `pkg/database`: 数据库助手](#33-pkgdatabase-数据库助手)
    *   [3.4 `pkg/errors`: 标准化错误处理](#34-pkgerrors-标准化错误处理)
    *   [3.5 `pkg/logger`: 结构化日志](#35-pkglogger-结构化日志)
    *   [3.6 `pkg/messaging`: 消息队列客户端](#36-pkgmessaging-消息队列客户端)
    *   [3.7 `pkg/middleware`: 通用中间件](#37-pkgmiddleware-通用中间件)
    *   [3.8 `pkg/tracing`: 分布式追踪](#38-pkgtracing-分布式追踪)
    *   [3.9 `pkg/workflow`: 通用工作流引擎](#39-pkgworkflow-通用工作流引擎)
    *   [3.10 `pkg/utils`: 通用工具函数](#310-pkgutils-通用工具函数)
4.  [非功能性需求](#4-非功能性需求)
5.  [技术约束与开发规范](#5-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
`pkg/` 目录是CINA.CLUB后端Monorepo中的**后端专用共享代码库**。其目的在于抽象和封装所有微服务通用的、与具体业务逻辑无关的横切关注点（Cross-Cutting Concerns），如认证、配置、日志、数据库连接、消息队列交互等。通过提供一套高质量、标准化的“工具箱”，`pkg/` 旨在：
*   **最大化代码复用**: 避免在40多个微服务中重复造轮子。
*   **提升开发效率**: 让业务开发人员能专注于业务逻辑，而非基础设施细节。
*   **保证行为一致性**: 确保所有服务在日志记录、错误处理、认证等方面遵循统一规范。
*   **简化维护**: 对基础能力的升级或bug修复，只需修改`pkg/`中的一处代码，即可惠及所有服务。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**: 提供纯粹的、无业务逻辑的、可被所有**后端Go微服务**导入和使用的Go包。
*   **范围之外 (Out-of-Scope)**:
    *   任何包含具体业务逻辑的代码。
    *   任何被前端（通过Go Mobile/WASM）依赖的代码（这些代码应位于`/core`目录）。
    *   最终的可执行应用。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go开发人员**。

---

### 2. 总体设计原则

*   **零依赖于业务**: `pkg/`中的任何包都**绝不能**反向导入`services/`或`apps/`中的任何代码。
*   **接口而非实现**: 尽可能面向接口编程，方便单元测试和未来替换具体实现。
*   **明确的职责**: 每个子包都应有单一、明确的职责。
*   **最小化**: 只提供真正需要被共享的功能，避免过度设计。
*   **配置驱动**: 库的行为应通过配置进行调整，而不是硬编码。
*   **可观测性内建**: 所有与I/O相关的包（如database, messaging）都必须原生集成`tracing`和`logger`。
*   **高质量**: `pkg/`中的代码必须有最高的测试覆盖率（目标 > 90%）、最完善的文档和最严格的代码审查。

---

### 3. 模块化需求 (按包拆分)

#### 3.1 `pkg/auth`: 认证与授权
*   **职责**: 提供后端服务间（S2S）和对内（Admin）的认证与授权中间件。
*   **功能需求**:
    *   **FR3.1.1 (S2S认证中间件)**: 提供一个gRPC拦截器，能验证来自其他微服务的服务级JWT，确保S2S调用的合法性。
    *   **FR3.1.2 (Admin认证中间件)**: 提供一个gRPC拦截器，能验证来自管理后台的用户JWT（通过JWKS），并检查其是否拥有访问该RPC所需的角色/权限。
    *   **FR3.1.3 (JWT生成器 - 可选)**: 提供一个工具，用于生成测试或特定场景下所需的JWT。
*   **接口示例 (Go)**:
    ```go
    func S2SAuthInterceptor(s2sConfig S2SConfig) grpc.UnaryServerInterceptor
    func AdminRBACInterceptor(rbacConfig RBACConfig) grpc.UnaryServerInterceptor
    ```
*   **依赖**: `pkg/errors`, `pkg/logger`, `core/api` (用于权限定义)。

#### 3.2 `pkg/config`: 配置管理
*   **职责**: 提供统一的、从多源加载配置的能力。
*   **功能需求**:
    *   **FR3.2.1 (多源加载)**: 支持从YAML文件、环境变量和（未来）远程配置中心（如Consul）加载配置。
    *   **FR3.2.2 (优先级)**: 加载顺序有明确的优先级（如：环境变量 > 配置文件）。
    *   **FR3.2.3 (强类型解析)**: 能将加载的配置安全地解析（unmarshal）到Go的强类型`struct`中。
*   **接口示例 (Go)**:
    ```go
    func LoadConfig(path string, configStruct interface{}) error
    ```
*   **依赖**: `spf13/viper`库。

#### 3.3 `pkg/database`: 数据库助手
*   **职责**: 封装数据库连接和常用操作。
*   **功能需求**:
    *   **FR3.3.1 (PostgreSQL连接池)**: 提供一个函数，根据配置创建一个带可观测性（Tracing, Metrics）的、高性能的PostgreSQL连接池 (`pgxpool`)。
    *   **FR3.3.2 (GORM封装 - 可选)**: 如果项目使用GORM，提供一个函数创建带可观测性的`gorm.DB`实例。
    *   **FR3.3.3 (Redis客户端)**: 提供一个函数，创建带可观测性的`redis.Client`实例。
*   **接口示例 (Go)**:
    ```go
    func NewPostgresPool(ctx context.Context, cfg PostgresConfig) (*pgxpool.Pool, error)
    func NewRedisClient(cfg RedisConfig) (*redis.Client, error)
    ```
*   **依赖**: `jackc/pgx`, `go-redis/redis`, `pkg/config`, `pkg/tracing`。

#### 3.4 `pkg/errors`: 标准化错误处理
*   **职责**: 定义平台统一的错误码和错误结构，便于跨服务和面向客户端的错误处理。
*   **功能需求**:
    *   **FR3.4.1 (错误码定义)**: 定义一个枚举或常量集，包含所有业务无关的通用错误码（如 `InvalidArgument`, `NotFound`, `PermissionDenied`, `Unauthenticated`, `Internal`）。
    *   **FR3.4.2 (自定义错误类型)**: 定义一个`AppError` struct，包含`Code`, `Message`, `UnderlyingError`等字段。
    *   **FR3.4.3 (gRPC状态转换)**: 提供工具函数，能将`AppError`与标准的gRPC状态码进行双向转换。
*   **接口示例 (Go)**:
    ```go
    func NewAppError(code ErrorCode, msg string, cause error) *AppError
    func ToGRPCStatus(err error) *status.Status
    func FromGRPCError(err error) *AppError
    ```
*   **依赖**: `google.golang.org/grpc/status`。

#### 3.5 `pkg/logger`: 结构化日志
*   **职责**: 提供一个全平台统一的、配置好的结构化日志记录器。
*   **功能需求**:
    *   **FR3.5.1 (初始化)**: 提供一个`NewLogger`函数，根据配置（日志级别、格式-JSON/text）初始化一个`slog.Logger`实例。
    *   **FR3.5.2 (上下文注入)**: 提供工具函数，能方便地从`context.Context`中提取`trace_id`和`user_id`等信息，并将其作为固定字段添加到每条日志中。
*   **接口示例 (Go)**:
    ```go
    func NewLogger(cfg LoggerConfig) *slog.Logger
    func L(ctx context.Context) *slog.Logger // 从上下文中获取带追踪信息的Logger
    ```
*   **依赖**: `log/slog` (标准库)。

#### 3.6 `pkg/messaging`: 消息队列客户端
*   **职责**: 封装与Kafka交互的生产者和消费者逻辑。
*   **功能需求**:
    *   **FR3.6.1 (生产者封装)**: 提供一个`KafkaProducer`，其`Publish`方法能自动处理消息的序列化(Protobuf/JSON)、设置追踪头、并进行同步/异步发送。
    *   **FR3.6.2 (消费者组封装)**: 提供一个`KafkaConsumerGroup`，能简化消费者组的启动、事件循环、错误处理、优雅关闭，并自动处理追踪上下文的提取。
*   **接口示例 (Go)**:
    ```go
    type Producer interface {
        Publish(ctx context.Context, topic string, message proto.Message) error
    }
    func NewConsumerGroup(cfg KafkaConfig, handlerFunc func(ctx context.Context, msg *kafka.Message)) *ConsumerGroup
    ```
*   **依赖**: `segmentio/kafka-go`, `pkg/config`, `pkg/logger`, `pkg/tracing`。

#### 3.7 `pkg/middleware`: 通用中间件
*   **职责**: 提供可被所有gRPC服务复用的通用拦截器。
*   **功能需求**:
    *   **FR3.7.1 (日志拦截器)**: 记录每个gRPC请求的详细信息（方法、耗时、状态码、Peer地址）。
    *   **FR3.7.2 (追踪拦截器)**: 从请求元数据中提取Trace Context，或创建一个新的Trace，并注入到`context.Context`中。
    *   **FR3.7.3 (Panic恢复拦截器)**: 捕获请求处理过程中的panic，记录错误，并返回一个`Internal` gRPC错误，防止服务崩溃。
    *   **FR3.7.4 (指标拦截器)**: 记录gRPC请求的Prometheus指标（请求总数、延迟直方图、错误计数）。
*   **接口示例 (Go)**:
    ```go
    func LoggingInterceptor(logger *slog.Logger) grpc.UnaryServerInterceptor
    func TracingInterceptor() grpc.UnaryServerInterceptor
    // ...
    ```
*   **依赖**: `pkg/logger`, `pkg/tracing`, `pkg/errors`。

#### 3.8 `pkg/tracing`: 分布式追踪
*   **职责**: 封装OpenTelemetry的初始化和全局Tracer Provider。
*   **功能需求**:
    *   **FR3.8.1 (初始化)**: 提供一个`InitTracerProvider`函数，根据配置（Exporter类型-Jaeger/OTLP, 采样率, 服务名），初始化并注册一个全局的`TracerProvider`。
*   **接口示例 (Go)**:
    ```go
    func InitTracerProvider(ctx context.Context, cfg TracerConfig) (func(), error) // 返回一个shutdown函数
    ```
*   **依赖**: `go.opentelemetry.io/otel/*`。

#### 3.9 `pkg/workflow`: 通用工作流引擎
*   **职责**: 提供一个通用的、基于图的、无状态的工作流执行引擎核心库。
*   **功能需求**:
    *   **FR3.9.1 (图数据结构)**: 定义`WorkflowGraph`, `Node`, `Edge`等核心数据结构。
    *   **FR3.9.2 (执行器)**: 提供一个`Executor`，能接收一个`WorkflowGraph`和初始上下文，并按拓扑顺序执行图中的节点。
    *   **FR3.9.3 (节点接口)**: 定义一个`NodeExecutor`接口，所有具体的动作节点（如`SendNotificationNode`, `CallApiNode`）都必须实现此接口。引擎本身不包含任何具体节点的实现。
*   **接口示例 (Go)**:
    ```go
    type NodeExecutor interface {
        Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error)
    }
    type Executor struct { ... }
    func (e *Executor) Run(ctx context.Context, graph *WorkflowGraph, initialCtx map[string]interface{}) error
    ```

#### 3.10 `pkg/utils`: 通用工具函数
*   **职责**: 存放完全通用的、与任何领域无关的工具函数。
*   **内容**: 字符串操作、随机数生成、切片处理、时间工具等。
*   **约束**: 此包不应有任何外部依赖（除了Go标准库）。

---

### 4. 非功能性需求

*   **NFR4.1 (性能)**: `pkg/`中的代码必须是高性能的，避免不必要的内存分配和锁竞争。所有代码都需通过Go的性能剖析工具（pprof）进行审查。
*   **NFR4.2 (可靠性)**: 库必须是健壮的，能优雅地处理nil指针、并发访问等边界情况。
*   **NFR4.3 (可测试性)**: 所有公共函数和方法都必须有对应的单元测试。测试应覆盖正常路径、错误路径和边界情况。
*   **NFR4.4 (安全性)**: 库代码必须避免常见的安全漏洞，如未处理的错误、不安全的并发、注入风险等。

---

### 5. 技术约束与开发规范

*   **TC5.1 (语言版本)**: 所有代码必须兼容Go 1.21+。
*   **TC5.2 (依赖管理)**: 使用Go Modules。禁止在`pkg/`中引入庞大或有争议的第三方依赖。所有新依赖的引入都必须经过团队审查。
*   **TC5.3 (代码风格)**: 强制使用`gofmt`和`goimports`。所有代码必须通过`golangci-lint`的严格检查。
*   **TC5.4 (文档)**: 所有公共的类型、函数、方法都必须有清晰、标准的GoDoc注释。
*   **TC5.5 (版本控制)**: 对`pkg/`的修改必须通过Pull Request进行，并需要至少一名其他核心开发人员的批准。重大变更需要更新相关文档。

---
这份SRS为CINA.CLUB的`pkg/`共享库提供了全面的设计和实现指南。遵循这份文档，可以确保平台的基础设施层是坚固、一致且高效的，从而为上层业务服务的快速、高质量开发奠定坚实基础。