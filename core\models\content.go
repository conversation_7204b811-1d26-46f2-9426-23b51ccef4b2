// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-24 10:19:52
// Modified: 2025-06-24 10:19:52

package models

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// PersonalKBItem represents an item in a user's personal knowledge base
type PersonalKBItem struct {
	ID          uuid.UUID              `json:"id"`
	UserID      uuid.UUID              `json:"user_id"`
	Title       string                 `json:"title"`
	Content     string                 `json:"content"` // Encrypted content
	Type        ContentType            `json:"type"`
	Category    string                 `json:"category"`
	Tags        []string               `json:"tags"`
	Source      ContentSource          `json:"source"`
	SourceURL   string                 `json:"source_url,omitempty"`
	IsEncrypted bool                   `json:"is_encrypted"`
	KeyID       string                 `json:"key_id,omitempty"`
	Version     int64                  `json:"version"`
	FileSize    int64                  `json:"file_size,omitempty"`
	MimeType    string                 `json:"mime_type,omitempty"`
	Embedding   []float32              `json:"embedding,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	AccessedAt  time.Time              `json:"accessed_at"`
}

// ContentType represents the type of content
type ContentType string

const (
	ContentTypeText     ContentType = "TEXT"
	ContentTypeNote     ContentType = "NOTE"
	ContentTypeDocument ContentType = "DOCUMENT"
	ContentTypeImage    ContentType = "IMAGE"
	ContentTypeVideo    ContentType = "VIDEO"
	ContentTypeAudio    ContentType = "AUDIO"
	ContentTypeFile     ContentType = "FILE"
	ContentTypeLink     ContentType = "LINK"
	ContentTypeCode     ContentType = "CODE"
	ContentTypeMarkdown ContentType = "MARKDOWN"
)

// ContentSource represents where the content originated from
type ContentSource string

const (
	ContentSourceManual    ContentSource = "MANUAL"
	ContentSourceImport    ContentSource = "IMPORT"
	ContentSourceCapture   ContentSource = "CAPTURE"
	ContentSourceSync      ContentSource = "SYNC"
	ContentSourceAPI       ContentSource = "API"
	ContentSourceUpload    ContentSource = "UPLOAD"
	ContentSourceClipboard ContentSource = "CLIPBOARD"
)

// IsTextBased returns true if the content type is text-based
func (item *PersonalKBItem) IsTextBased() bool {
	return item.Type == ContentTypeText ||
		item.Type == ContentTypeNote ||
		item.Type == ContentTypeMarkdown ||
		item.Type == ContentTypeCode
}

// IsMediaContent returns true if the content is media (image, video, audio)
func (item *PersonalKBItem) IsMediaContent() bool {
	return item.Type == ContentTypeImage ||
		item.Type == ContentTypeVideo ||
		item.Type == ContentTypeAudio
}

// GetFormattedSize returns the file size formatted for display
func (item *PersonalKBItem) GetFormattedSize() string {
	if item.FileSize == 0 {
		return "Unknown size"
	}

	const unit = 1024
	if item.FileSize < unit {
		return fmt.Sprintf("%d B", item.FileSize)
	}

	div, exp := int64(unit), 0
	for n := item.FileSize / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	return fmt.Sprintf("%.1f %cB", float64(item.FileSize)/float64(div), "KMGTPE"[exp])
}

// SharedKBItem represents an item in the shared/commercial knowledge base
type SharedKBItem struct {
	ID            uuid.UUID              `json:"id"`
	CreatorID     uuid.UUID              `json:"creator_id"`
	Title         string                 `json:"title"`
	Description   string                 `json:"description"`
	Content       string                 `json:"content"`
	Type          ContentType            `json:"type"`
	Category      string                 `json:"category"`
	Tags          []string               `json:"tags"`
	Language      string                 `json:"language"`
	Status        PublicationStatus      `json:"status"`
	Visibility    VisibilityLevel        `json:"visibility"`
	Price         int64                  `json:"price"` // in cents, 0 for free
	Currency      string                 `json:"currency"`
	LicenseType   LicenseType            `json:"license_type"`
	ViewCount     int64                  `json:"view_count"`
	DownloadCount int64                  `json:"download_count"`
	Rating        float64                `json:"rating"`
	ReviewCount   int64                  `json:"review_count"`
	FileSize      int64                  `json:"file_size,omitempty"`
	MimeType      string                 `json:"mime_type,omitempty"`
	Embedding     []float32              `json:"embedding,omitempty"`
	Metadata      map[string]interface{} `json:"metadata"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	PublishedAt   *time.Time             `json:"published_at,omitempty"`
}

// PublicationStatus represents the publication status of shared content
type PublicationStatus string

const (
	PublicationStatusDraft     PublicationStatus = "DRAFT"
	PublicationStatusReview    PublicationStatus = "REVIEW"
	PublicationStatusPublished PublicationStatus = "PUBLISHED"
	PublicationStatusArchived  PublicationStatus = "ARCHIVED"
	PublicationStatusRejected  PublicationStatus = "REJECTED"
)

// VisibilityLevel represents who can access the content
type VisibilityLevel string

const (
	VisibilityLevelPublic      VisibilityLevel = "PUBLIC"
	VisibilityLevelUnlisted    VisibilityLevel = "UNLISTED"
	VisibilityLevelMembers     VisibilityLevel = "MEMBERS"
	VisibilityLevelSubscribers VisibilityLevel = "SUBSCRIBERS"
	VisibilityLevelPrivate     VisibilityLevel = "PRIVATE"
)

// LicenseType represents the licensing of the content
type LicenseType string

const (
	LicenseTypeStandard        LicenseType = "STANDARD"
	LicenseTypeCreativeCommons LicenseType = "CREATIVE_COMMONS"
	LicenseTypeCommercial      LicenseType = "COMMERCIAL"
	LicenseTypeEducational     LicenseType = "EDUCATIONAL"
	LicenseTypeCustom          LicenseType = "CUSTOM"
)

// IsPublished returns true if the content is published and visible
func (item *SharedKBItem) IsPublished() bool {
	return item.Status == PublicationStatusPublished
}

// IsFree returns true if the content is free to access
func (item *SharedKBItem) IsFree() bool {
	return item.Price == 0
}

// GetFormattedPrice returns the price formatted for display
func (item *SharedKBItem) GetFormattedPrice() string {
	if item.IsFree() {
		return "Free"
	}
	price := float64(item.Price) / 100.0
	return fmt.Sprintf("%.2f %s", price, item.Currency)
}

// GetRatingDisplay returns the rating formatted for display
func (item *SharedKBItem) GetRatingDisplay() string {
	if item.ReviewCount == 0 {
		return "No ratings yet"
	}
	return fmt.Sprintf("%.1f (%d reviews)", item.Rating, item.ReviewCount)
}

// ForumPost represents a post in the community forum
type ForumPost struct {
	ID          uuid.UUID              `json:"id"`
	AuthorID    uuid.UUID              `json:"author_id"`
	CategoryID  uuid.UUID              `json:"category_id"`
	Title       string                 `json:"title"`
	Content     string                 `json:"content"`
	ContentType ForumContentType       `json:"content_type"`
	Status      ForumPostStatus        `json:"status"`
	IsPinned    bool                   `json:"is_pinned"`
	IsLocked    bool                   `json:"is_locked"`
	Tags        []string               `json:"tags"`
	ViewCount   int64                  `json:"view_count"`
	ReplyCount  int64                  `json:"reply_count"`
	LikeCount   int64                  `json:"like_count"`
	LastReplyAt *time.Time             `json:"last_reply_at,omitempty"`
	LastReplyBy *uuid.UUID             `json:"last_reply_by,omitempty"`
	Attachments []Attachment           `json:"attachments"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// ForumContentType represents the format of forum content
type ForumContentType string

const (
	ForumContentTypeText     ForumContentType = "TEXT"
	ForumContentTypeMarkdown ForumContentType = "MARKDOWN"
	ForumContentTypeRichText ForumContentType = "RICH_TEXT"
)

// ForumPostStatus represents the status of a forum post
type ForumPostStatus string

const (
	ForumPostStatusDraft     ForumPostStatus = "DRAFT"
	ForumPostStatusPublished ForumPostStatus = "PUBLISHED"
	ForumPostStatusHidden    ForumPostStatus = "HIDDEN"
	ForumPostStatusDeleted   ForumPostStatus = "DELETED"
	ForumPostStatusFlagged   ForumPostStatus = "FLAGGED"
)

// Attachment represents a file attachment
type Attachment struct {
	ID       uuid.UUID `json:"id"`
	Name     string    `json:"name"`
	URL      string    `json:"url"`
	FileSize int64     `json:"file_size"`
	MimeType string    `json:"mime_type"`
}

// IsVisible returns true if the post is visible to users
func (post *ForumPost) IsVisible() bool {
	return post.Status == ForumPostStatusPublished
}

// CanReply returns true if users can reply to the post
func (post *ForumPost) CanReply() bool {
	return post.IsVisible() && !post.IsLocked
}

// QAQuestion represents a question in the Q&A system
type QAQuestion struct {
	ID                uuid.UUID              `json:"id"`
	AuthorID          uuid.UUID              `json:"author_id"`
	Title             string                 `json:"title"`
	Content           string                 `json:"content"`
	Tags              []string               `json:"tags"`
	Status            QAStatus               `json:"status"`
	Bounty            int64                  `json:"bounty"` // in platform coins
	ViewCount         int64                  `json:"view_count"`
	AnswerCount       int64                  `json:"answer_count"`
	VoteScore         int64                  `json:"vote_score"`
	HasAcceptedAnswer bool                   `json:"has_accepted_answer"`
	AcceptedAnswerID  *uuid.UUID             `json:"accepted_answer_id,omitempty"`
	LastActivityAt    time.Time              `json:"last_activity_at"`
	Metadata          map[string]interface{} `json:"metadata"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
}

// QAAnswer represents an answer to a Q&A question
type QAAnswer struct {
	ID         uuid.UUID              `json:"id"`
	QuestionID uuid.UUID              `json:"question_id"`
	AuthorID   uuid.UUID              `json:"author_id"`
	Content    string                 `json:"content"`
	Status     QAStatus               `json:"status"`
	VoteScore  int64                  `json:"vote_score"`
	IsAccepted bool                   `json:"is_accepted"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// QAStatus represents the status of Q&A content
type QAStatus string

const (
	QAStatusOpen     QAStatus = "OPEN"
	QAStatusAnswered QAStatus = "ANSWERED"
	QAStatusClosed   QAStatus = "CLOSED"
	QAStatusDeleted  QAStatus = "DELETED"
	QAStatusFlagged  QAStatus = "FLAGGED"
)

// IsOpen returns true if the question is open for answers
func (q *QAQuestion) IsOpen() bool {
	return q.Status == QAStatusOpen
}

// IsAnswered returns true if the question has been answered
func (q *QAQuestion) IsAnswered() bool {
	return q.HasAcceptedAnswer || q.Status == QAStatusAnswered
}

// HasBounty returns true if the question has a bounty
func (q *QAQuestion) HasBounty() bool {
	return q.Bounty > 0
}

// ShortVideo represents a short video content
type ShortVideo struct {
	ID           uuid.UUID              `json:"id"`
	CreatorID    uuid.UUID              `json:"creator_id"`
	Title        string                 `json:"title"`
	Description  string                 `json:"description"`
	VideoURL     string                 `json:"video_url"`
	ThumbnailURL string                 `json:"thumbnail_url"`
	Duration     time.Duration          `json:"duration"`
	FileSize     int64                  `json:"file_size"`
	Resolution   VideoResolution        `json:"resolution"`
	Status       VideoStatus            `json:"status"`
	Visibility   VisibilityLevel        `json:"visibility"`
	Tags         []string               `json:"tags"`
	ViewCount    int64                  `json:"view_count"`
	LikeCount    int64                  `json:"like_count"`
	ShareCount   int64                  `json:"share_count"`
	CommentCount int64                  `json:"comment_count"`
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	PublishedAt  *time.Time             `json:"published_at,omitempty"`
}

// VideoResolution represents video resolution
type VideoResolution string

const (
	VideoResolution480p  VideoResolution = "480p"
	VideoResolution720p  VideoResolution = "720p"
	VideoResolution1080p VideoResolution = "1080p"
	VideoResolution4K    VideoResolution = "4K"
)

// VideoStatus represents the status of a video
type VideoStatus string

const (
	VideoStatusUploading  VideoStatus = "UPLOADING"
	VideoStatusProcessing VideoStatus = "PROCESSING"
	VideoStatusReady      VideoStatus = "READY"
	VideoStatusFailed     VideoStatus = "FAILED"
	VideoStatusDeleted    VideoStatus = "DELETED"
)

// IsReady returns true if the video is ready for viewing
func (v *ShortVideo) IsReady() bool {
	return v.Status == VideoStatusReady
}

// IsPublic returns true if the video is publicly visible
func (v *ShortVideo) IsPublic() bool {
	return v.Visibility == VisibilityLevelPublic
}

// GetFormattedDuration returns the duration formatted for display
func (v *ShortVideo) GetFormattedDuration() string {
	seconds := int(v.Duration.Seconds())
	if seconds < 60 {
		return fmt.Sprintf("%ds", seconds)
	}
	minutes := seconds / 60
	remainingSeconds := seconds % 60
	return fmt.Sprintf("%d:%02d", minutes, remainingSeconds)
}
