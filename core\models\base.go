// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 16:30:00
// Modified: 2025-01-23 16:30:00

package models

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

// BaseModel defines common fields and behaviors for all data models
type BaseModel struct {
	ID        uuid.UUID              `json:"id" db:"id" validate:"required"`
	CreatedAt time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt time.Time              `json:"updated_at" db:"updated_at"`
	Version   int64                  `json:"version" db:"version"`
	Metadata  map[string]interface{} `json:"metadata,omitempty" db:"metadata"`
}

// SoftDeleteModel extends BaseModel with soft delete functionality
type SoftDeleteModel struct {
	BaseModel
	DeletedAt *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
	IsDeleted bool       `json:"is_deleted" db:"is_deleted"`
}

// AuditableModel extends BaseModel with audit trail
type AuditableModel struct {
	BaseModel
	CreatedBy uuid.UUID `json:"created_by" db:"created_by" validate:"required"`
	UpdatedBy uuid.UUID `json:"updated_by" db:"updated_by" validate:"required"`
}

// TenantModel extends BaseModel with multi-tenancy support
type TenantModel struct {
	BaseModel
	TenantID uuid.UUID `json:"tenant_id" db:"tenant_id" validate:"required"`
}

// Validatable interface for models that can be validated
type Validatable interface {
	Validate() error
	ValidateForCreate() error
	ValidateForUpdate() error
}

// Timestampable interface for models that manage timestamps
type Timestampable interface {
	SetTimestamps()
	UpdateTimestamp()
}

// Versionable interface for models that support versioning
type Versionable interface {
	IncrementVersion()
	GetVersion() int64
}

// Searchable interface for models that support search indexing
type Searchable interface {
	GetSearchDocument() map[string]interface{}
	GetSearchIndex() string
	GetSearchID() string
}

// Cacheable interface for models that support caching
type Cacheable interface {
	GetCacheKey() string
	GetCacheTTL() time.Duration
	ShouldCache() bool
}

// ModelValidator provides validation functionality
type ModelValidator struct {
	validator *validator.Validate
}

// NewModelValidator creates a new model validator
func NewModelValidator() *ModelValidator {
	v := validator.New()

	// Register custom validation tags
	v.RegisterValidation("uuid", validateUUID)
	v.RegisterValidation("enum", validateEnum)
	v.RegisterValidation("json", validateJSON)
	v.RegisterValidation("slug", validateSlug)
	v.RegisterValidation("phone", validatePhone)
	v.RegisterValidation("timezone", validateTimezone)

	return &ModelValidator{validator: v}
}

// Validate validates a model using struct tags
func (mv *ModelValidator) Validate(model interface{}) error {
	if err := mv.validator.Struct(model); err != nil {
		return mv.formatValidationError(err)
	}
	return nil
}

// ValidatePartial validates only the provided fields
func (mv *ModelValidator) ValidatePartial(model interface{}, fields ...string) error {
	if err := mv.validator.StructPartial(model, fields...); err != nil {
		return mv.formatValidationError(err)
	}
	return nil
}

// formatValidationError formats validation errors into user-friendly messages
func (mv *ModelValidator) formatValidationError(err error) error {
	var errors []string

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			field := strings.ToLower(e.Field())
			tag := e.Tag()

			switch tag {
			case "required":
				errors = append(errors, fmt.Sprintf("%s is required", field))
			case "email":
				errors = append(errors, fmt.Sprintf("%s must be a valid email address", field))
			case "min":
				errors = append(errors, fmt.Sprintf("%s must be at least %s characters", field, e.Param()))
			case "max":
				errors = append(errors, fmt.Sprintf("%s must not exceed %s characters", field, e.Param()))
			case "uuid":
				errors = append(errors, fmt.Sprintf("%s must be a valid UUID", field))
			default:
				errors = append(errors, fmt.Sprintf("%s validation failed: %s", field, tag))
			}
		}
	}

	return fmt.Errorf("validation failed: %s", strings.Join(errors, ", "))
}

// Custom validation functions
func validateUUID(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	_, err := uuid.Parse(value)
	return err == nil
}

func validateEnum(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	allowedValues := strings.Split(fl.Param(), " ")

	for _, allowed := range allowedValues {
		if value == allowed {
			return true
		}
	}
	return false
}

func validateJSON(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	return json.Valid([]byte(value))
}

func validateSlug(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	// Simple slug validation: lowercase letters, numbers, and hyphens
	for _, char := range value {
		if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9') || char == '-') {
			return false
		}
	}
	return len(value) > 0
}

func validatePhone(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	// Basic phone validation - starts with + and contains only digits and spaces
	if len(value) == 0 {
		return true // Allow empty for optional fields
	}

	if !strings.HasPrefix(value, "+") {
		return false
	}

	for i, char := range value[1:] {
		if !(char >= '0' && char <= '9') && char != ' ' && char != '-' {
			return false
		}
		if i == 0 && (char == ' ' || char == '-') {
			return false // First character after + must be digit
		}
	}
	return true
}

func validateTimezone(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return true // Allow empty for optional fields
	}

	_, err := time.LoadLocation(value)
	return err == nil
}

// BaseModel methods
func (bm *BaseModel) SetTimestamps() {
	now := time.Now().UTC()
	if bm.ID == uuid.Nil {
		bm.ID = uuid.New()
		bm.CreatedAt = now
		bm.Version = 1
	}
	bm.UpdatedAt = now
}

func (bm *BaseModel) UpdateTimestamp() {
	bm.UpdatedAt = time.Now().UTC()
	bm.Version++
}

func (bm *BaseModel) IncrementVersion() {
	bm.Version++
}

func (bm *BaseModel) GetVersion() int64 {
	return bm.Version
}

func (bm *BaseModel) IsNew() bool {
	return bm.ID == uuid.Nil || bm.CreatedAt.IsZero()
}

func (bm *BaseModel) GetMetadata(key string) interface{} {
	if bm.Metadata == nil {
		return nil
	}
	return bm.Metadata[key]
}

func (bm *BaseModel) SetMetadata(key string, value interface{}) {
	if bm.Metadata == nil {
		bm.Metadata = make(map[string]interface{})
	}
	bm.Metadata[key] = value
}

func (bm *BaseModel) RemoveMetadata(key string) {
	if bm.Metadata != nil {
		delete(bm.Metadata, key)
	}
}

// SoftDeleteModel methods
func (sdm *SoftDeleteModel) Delete() {
	now := time.Now().UTC()
	sdm.DeletedAt = &now
	sdm.IsDeleted = true
	sdm.UpdateTimestamp()
}

func (sdm *SoftDeleteModel) Restore() {
	sdm.DeletedAt = nil
	sdm.IsDeleted = false
	sdm.UpdateTimestamp()
}

func (sdm *SoftDeleteModel) IsActivelyDeleted() bool {
	return sdm.IsDeleted && sdm.DeletedAt != nil
}

// ModelRegistry provides a registry for all models in the system
type ModelRegistry struct {
	models map[string]reflect.Type
}

// NewModelRegistry creates a new model registry
func NewModelRegistry() *ModelRegistry {
	return &ModelRegistry{
		models: make(map[string]reflect.Type),
	}
}

// Register registers a model type with the registry
func (mr *ModelRegistry) Register(name string, model interface{}) {
	mr.models[name] = reflect.TypeOf(model)
}

// GetModelType returns the type for a registered model
func (mr *ModelRegistry) GetModelType(name string) (reflect.Type, bool) {
	modelType, exists := mr.models[name]
	return modelType, exists
}

// CreateInstance creates a new instance of a registered model
func (mr *ModelRegistry) CreateInstance(name string) (interface{}, error) {
	modelType, exists := mr.models[name]
	if !exists {
		return nil, fmt.Errorf("model %s not registered", name)
	}

	// Create pointer to struct if it's a struct type
	if modelType.Kind() == reflect.Ptr {
		return reflect.New(modelType.Elem()).Interface(), nil
	}
	return reflect.New(modelType).Interface(), nil
}

// ListModels returns all registered model names
func (mr *ModelRegistry) ListModels() []string {
	var names []string
	for name := range mr.models {
		names = append(names, name)
	}
	return names
}

// ValidationConfig provides configuration for model validation
type ValidationConfig struct {
	SkipValidation   bool
	ValidateOnCreate bool
	ValidateOnUpdate bool
	CustomValidators map[string]validator.Func
	RequiredFields   []string
	OptionalFields   []string
	ValidationGroups map[string][]string
}

// DefaultValidationConfig returns default validation configuration
func DefaultValidationConfig() *ValidationConfig {
	return &ValidationConfig{
		SkipValidation:   false,
		ValidateOnCreate: true,
		ValidateOnUpdate: true,
		CustomValidators: make(map[string]validator.Func),
		ValidationGroups: make(map[string][]string),
	}
}

// ModelTransformer provides data transformation utilities
type ModelTransformer struct {
	transformers map[string]func(interface{}) interface{}
}

// NewModelTransformer creates a new model transformer
func NewModelTransformer() *ModelTransformer {
	return &ModelTransformer{
		transformers: make(map[string]func(interface{}) interface{}),
	}
}

// RegisterTransformer registers a transformation function
func (mt *ModelTransformer) RegisterTransformer(name string, transformer func(interface{}) interface{}) {
	mt.transformers[name] = transformer
}

// Transform applies a transformation to a model
func (mt *ModelTransformer) Transform(name string, model interface{}) (interface{}, error) {
	transformer, exists := mt.transformers[name]
	if !exists {
		return nil, fmt.Errorf("transformer %s not found", name)
	}

	return transformer(model), nil
}

// Common transformers
func ToPublicView(model interface{}) interface{} {
	// Remove sensitive fields and internal metadata
	// This is a simplified implementation - in practice, you'd use reflection
	// to dynamically remove fields marked with specific tags
	return model
}

func ToAPIResponse(model interface{}) interface{} {
	// Transform model for API response
	return model
}

func ToSearchDocument(model interface{}) interface{} {
	// Transform model for search indexing
	return model
}

// Global instances
var (
	DefaultValidator   = NewModelValidator()
	GlobalRegistry     = NewModelRegistry()
	DefaultTransformer = NewModelTransformer()
)

// Initialize default transformers
func init() {
	DefaultTransformer.RegisterTransformer("public", ToPublicView)
	DefaultTransformer.RegisterTransformer("api", ToAPIResponse)
	DefaultTransformer.RegisterTransformer("search", ToSearchDocument)
}
