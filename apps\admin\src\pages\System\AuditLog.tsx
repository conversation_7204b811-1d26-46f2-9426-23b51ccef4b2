/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:05:00
 * Modified: 2025-01-23 16:05:00
 */

import React, { useState } from 'react';
import {
  Card,
  Table,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Typography,
  Tag,
  Drawer,
  Descriptions,
  Timeline,
  Alert,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  Modal,
  Form,
} from 'antd';
import {
  AuditOutlined,
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  EyeOutlined,
  DownloadOutlined,
  UserOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface AuditLogEntry {
  id: string;
  timestamp: string;
  action: string;
  resource: string;
  resourceId?: string;
  userId: string;
  userEmail: string;
  userName: string;
  ipAddress: string;
  userAgent: string;
  status: 'success' | 'failure' | 'warning';
  details: Record<string, any>;
  changes?: {
    before: Record<string, any>;
    after: Record<string, any>;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'authentication' | 'user_management' | 'system' | 'content' | 'security' | 'api';
}

interface AuditStats {
  totalEvents: number;
  todayEvents: number;
  failedEvents: number;
  securityEvents: number;
  topActions: Array<{ action: string; count: number }>;
  topUsers: Array<{ user: string; count: number }>;
}

const AuditLog: React.FC = () => {
  const [selectedEntry, setSelectedEntry] = useState<AuditLogEntry | null>(null);
  const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    action: '',
    category: '',
    status: '',
    severity: '',
    dateRange: null as any,
    userId: '',
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // Mock data - replace with actual API calls
  const { data: auditLogs = [], isLoading } = useQuery({
    queryKey: ['audit-logs', filters, pagination.current, pagination.pageSize],
    queryFn: async () => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockLogs: AuditLogEntry[] = [
        {
          id: '1',
          timestamp: '2025-01-23T15:30:00Z',
          action: 'user.login',
          resource: 'authentication',
          userId: 'user-123',
          userEmail: '<EMAIL>',
          userName: 'Admin User',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'success',
          details: { loginMethod: 'email', rememberMe: true },
          severity: 'low',
          category: 'authentication'
        },
        {
          id: '2',
          timestamp: '2025-01-23T15:25:00Z',
          action: 'user.create',
          resource: 'user',
          resourceId: 'user-456',
          userId: 'user-123',
          userEmail: '<EMAIL>',
          userName: 'Admin User',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'success',
          details: { 
            createdUser: '<EMAIL>',
            role: 'CONTENT_MODERATOR',
            invitationSent: true 
          },
          changes: {
            before: {},
            after: {
              email: '<EMAIL>',
              role: 'CONTENT_MODERATOR',
              status: 'PENDING'
            }
          },
          severity: 'medium',
          category: 'user_management'
        },
        {
          id: '3',
          timestamp: '2025-01-23T15:20:00Z',
          action: 'system.config.update',
          resource: 'system_config',
          resourceId: 'config-auth-timeout',
          userId: 'user-123',
          userEmail: '<EMAIL>',
          userName: 'Admin User',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'success',
          details: { configKey: 'auth.session_timeout' },
          changes: {
            before: { value: 7200 },
            after: { value: 3600 }
          },
          severity: 'high',
          category: 'system'
        },
        {
          id: '4',
          timestamp: '2025-01-23T15:15:00Z',
          action: 'content.moderate',
          resource: 'content',
          resourceId: 'content-789',
          userId: 'user-456',
          userEmail: '<EMAIL>',
          userName: 'Content Moderator',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          status: 'success',
          details: { 
            action: 'approve',
            reason: 'Content meets community guidelines',
            contentType: 'post'
          },
          severity: 'low',
          category: 'content'
        },
        {
          id: '5',
          timestamp: '2025-01-23T15:10:00Z',
          action: 'auth.failed_login',
          resource: 'authentication',
          userId: 'unknown',
          userEmail: '<EMAIL>',
          userName: 'Unknown',
          ipAddress: '********',
          userAgent: 'curl/7.68.0',
          status: 'failure',
          details: { 
            reason: 'invalid_credentials',
            attempts: 5,
            blocked: true
          },
          severity: 'critical',
          category: 'security'
        }
      ];

      return {
        data: mockLogs,
        total: 150,
        page: pagination.current,
        pageSize: pagination.pageSize
      };
    }
  });

  const { data: auditStats } = useQuery({
    queryKey: ['audit-stats'],
    queryFn: async (): Promise<AuditStats> => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        totalEvents: 1247,
        todayEvents: 89,
        failedEvents: 12,
        securityEvents: 5,
        topActions: [
          { action: 'user.login', count: 234 },
          { action: 'content.view', count: 189 },
          { action: 'user.update', count: 156 },
          { action: 'content.moderate', count: 98 },
          { action: 'system.config.view', count: 67 }
        ],
        topUsers: [
          { user: '<EMAIL>', count: 345 },
          { user: '<EMAIL>', count: 234 },
          { user: '<EMAIL>', count: 123 },
          { user: '<EMAIL>', count: 89 }
        ]
      };
    }
  });

  const handleViewDetails = (entry: AuditLogEntry) => {
    setSelectedEntry(entry);
    setIsDetailDrawerVisible(true);
  };

  const handleExport = () => {
    Modal.confirm({
      title: 'Export Audit Logs',
      content: 'This will export the filtered audit logs to a CSV file. Continue?',
      onOk: () => {
        // Simulate export
        console.log('Exporting audit logs with filters:', filters);
      }
    });
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'yellow';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failure': return <ExclamationCircleOutlined style={{ color: '#f5222d' }} />;
      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;
      default: return <InfoCircleOutlined />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'authentication': return 'blue';
      case 'user_management': return 'green';
      case 'system': return 'purple';
      case 'content': return 'cyan';
      case 'security': return 'red';
      case 'api': return 'orange';
      default: return 'default';
    }
  };

  const columns: ColumnsType<AuditLogEntry> = [
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp: string) => (
        <div>
          <div>{new Date(timestamp).toLocaleDateString()}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {new Date(timestamp).toLocaleTimeString()}
          </Text>
        </div>
      ),
      sorter: true,
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: 150,
      render: (action: string) => <Text code>{action}</Text>,
    },
    {
      title: 'User',
      key: 'user',
      width: 200,
      render: (_, record) => (
        <div>
          <div>
            <UserOutlined /> {record.userName}
          </div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.userEmail}
          </Text>
        </div>
      ),
    },
    {
      title: 'Resource',
      dataIndex: 'resource',
      key: 'resource',
      width: 120,
      render: (resource: string) => <Tag>{resource}</Tag>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Space>
          {getStatusIcon(status)}
          <Text>{status}</Text>
        </Space>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: string) => (
        <Tag color={getCategoryColor(category)}>
          {category.replace('_', ' ')}
        </Tag>
      ),
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)}>
          {severity.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'IP Address',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 120,
      render: (ip: string) => <Text code>{ip}</Text>,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetails(record)}
        >
          Details
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <AuditOutlined /> Audit Log
        </Title>
        <Paragraph type="secondary">
          Track and monitor all system activities and user actions.
        </Paragraph>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Events"
              value={auditStats?.totalEvents || 0}
              prefix={<AuditOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Today's Events"
              value={auditStats?.todayEvents || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Failed Events"
              value={auditStats?.failedEvents || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Security Events"
              value={auditStats?.securityEvents || 0}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={16}>
          <Col span={6}>
            <Input
              placeholder="Search actions, users, resources..."
              prefix={<SearchOutlined />}
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Category"
              style={{ width: '100%' }}
              value={filters.category}
              onChange={(value) => setFilters({ ...filters, category: value })}
              allowClear
            >
              <Option value="authentication">Authentication</Option>
              <Option value="user_management">User Management</Option>
              <Option value="system">System</Option>
              <Option value="content">Content</Option>
              <Option value="security">Security</Option>
              <Option value="api">API</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Status"
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              allowClear
            >
              <Option value="success">Success</Option>
              <Option value="failure">Failure</Option>
              <Option value="warning">Warning</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Severity"
              style={{ width: '100%' }}
              value={filters.severity}
              onChange={(value) => setFilters({ ...filters, severity: value })}
              allowClear
            >
              <Option value="low">Low</Option>
              <Option value="medium">Medium</Option>
              <Option value="high">High</Option>
              <Option value="critical">Critical</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <RangePicker
                value={filters.dateRange}
                onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
              />
              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={handleExport}
              >
                Export
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Audit Log Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={auditLogs.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: auditLogs.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
            onChange: (page, pageSize) => {
              setPagination({ ...pagination, current: page, pageSize: pageSize || 20 });
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Detail Drawer */}
      <Drawer
        title="Audit Log Details"
        width={600}
        open={isDetailDrawerVisible}
        onClose={() => setIsDetailDrawerVisible(false)}
      >
        {selectedEntry && (
          <div>
            <Descriptions title="Event Information" bordered column={1}>
              <Descriptions.Item label="Timestamp">
                {new Date(selectedEntry.timestamp).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="Action">
                <Text code>{selectedEntry.action}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Resource">
                <Space>
                  <Tag>{selectedEntry.resource}</Tag>
                  {selectedEntry.resourceId && (
                    <Text code>{selectedEntry.resourceId}</Text>
                  )}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="User">
                <div>
                  <div><UserOutlined /> {selectedEntry.userName}</div>
                  <Text type="secondary">{selectedEntry.userEmail}</Text>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Space>
                  {getStatusIcon(selectedEntry.status)}
                  <Text>{selectedEntry.status}</Text>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="Category">
                <Tag color={getCategoryColor(selectedEntry.category)}>
                  {selectedEntry.category.replace('_', ' ')}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Severity">
                <Tag color={getSeverityColor(selectedEntry.severity)}>
                  {selectedEntry.severity.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="IP Address">
                <Text code>{selectedEntry.ipAddress}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="User Agent">
                <Text code style={{ fontSize: '12px' }}>
                  {selectedEntry.userAgent}
                </Text>
              </Descriptions.Item>
            </Descriptions>

            {selectedEntry.details && Object.keys(selectedEntry.details).length > 0 && (
              <div style={{ marginTop: '24px' }}>
                <Title level={5}>Event Details</Title>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  {JSON.stringify(selectedEntry.details, null, 2)}
                </pre>
              </div>
            )}

            {selectedEntry.changes && (
              <div style={{ marginTop: '24px' }}>
                <Title level={5}>Changes Made</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Card title="Before" size="small">
                      <pre style={{ 
                        background: '#fff2f0', 
                        padding: '8px', 
                        borderRadius: '4px',
                        fontSize: '12px'
                      }}>
                        {JSON.stringify(selectedEntry.changes.before, null, 2)}
                      </pre>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="After" size="small">
                      <pre style={{ 
                        background: '#f6ffed', 
                        padding: '8px', 
                        borderRadius: '4px',
                        fontSize: '12px'
                      }}>
                        {JSON.stringify(selectedEntry.changes.after, null, 2)}
                      </pre>
                    </Card>
                  </Col>
                </Row>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default AuditLog; 