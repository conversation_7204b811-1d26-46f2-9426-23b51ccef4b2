好的，遵照您的指示。我将为您生成一份专门针对 **`billing-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`billing-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**灵活的产品目录、复杂的订阅生命周期管理、精确的计费与发票引擎、以及与多个金融相关服务（`payment-service`, `cina-coin-ledger-service`）的Saga事务协同**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `billing-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `billing-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + Saga编排模式 + 策略模式

## 1. 概述

`billing-service` 是CINA.CLUB平台的**商业化核心引擎**。其核心挑战在于：
1.  **模型的灵活性**: 需要支持未来多变的商业模式，包括固定订阅、按量计费、分层定价等。
2.  **精确性与可审计性**: 所有计费、发票计算必须100%准确，且所有操作都有据可查。
3.  **健壮的订阅生命周期**: 需要精确管理订阅的各种状态（试用、活跃、逾期、取消等）及其转换。
4.  **复杂的外部协同**: 作为**Saga协调者**，需要编排与`payment-service`、`cina-coin-ledger-service`和`user-core-service`等多个服务的复杂交互，并处理失败补偿。
5.  **高性能**: 用量上报接口需要高吞吐量，权限检查接口需要极低延迟。

本架构设计通过采用**整洁架构**，并结合**策略模式(Strategy Pattern)**来处理不同的定价模型，以及**编排式Saga**来管理分布式事务，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 整洁架构分层图

```mermaid
graph TD
    subgraph "外部世界 (Frameworks & Drivers)"
        A[gRPC/REST Server<br/>(adapter/transport)]
        B[PostgreSQL<br/>(adapter/repository)]
        C[Kafka Consumer/Producer<br/>(adapter/event)]
        D[S2S gRPC Clients<br/>(adapter/client)]
    end
    
    subgraph "应用层 (Application)"
        E[BillingService (Commands)<br/>(application/command)]
        F[BillingQueryService (Queries)<br/>(application/query)]
    end
    
    subgraph "领域层 (Domain)"
        G[Aggregates: Subscription, Invoice<br/>(domain/aggregate)]
        H[Entities: Product, Price, Feature<br/>(domain/model)]
        I[Interfaces: Repositories, Services<br/>(application/port)]
        J[Domain Services: Pricing, Proration<br/>(domain/service)]
    end
    
    A -- "调用" --> E & F
    E -- "使用" --> I
    F -- "使用" --> I
    B -- "实现" --> I
    C -- "实现/调用" --> E
    D -- "被...调用" --> E & J
    E & J -- "操作" --> G & H
```

### 2.2 最终目录结构 (`services/billing-service/`)

```
billing-service/
├── cmd/server/
│   └── main.go                 # API服务启动入口
├── cmd/worker/
│   └── main.go                 # ✨ 计费周期运行等后台任务的独立入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── payment_client.go
│   │   │   └── user_core_client.go
│   │   ├── event/
│   │   │   └── payment_consumer.go # 消费支付成功/失败事件
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── command/
│   │   │   └── billing_command_service.go # 处理创建订阅、计费运行等写操作
│   │   └── query/
│   │       └── billing_query_service.go   # 处理查询产品、订阅、发票等读操作
│   └── domain/
│       ├── aggregate/
│       │   ├── subscription_aggregate.go # 封装订阅状态机和核心业务规则
│       │   └── invoice_aggregate.go      # 封装发票生成和状态转换逻辑
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── interface.go        # 定义定价、按比例计费等策略接口
│           ├── pricing_strategy.go # ✨ 定价策略模式实现 ✨
│           └── proration_service.go  # 按比例计费计算服务
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Financial Rules)

*   **`domain/model/`**: 使用`/core/models`中定义的`Product`, `Price`, `Feature`等核心`struct`。
*   **`domain/aggregate/`**: **这是本服务最核心的业务规则封装**。
    *   `subscription_aggregate.go`:
        *   **`Subscription`聚合根**: 封装了订阅的所有状态和数据。
        *   **状态机方法**: `Upgrade()`, `Cancel()`, `Pause()`, `HandlePaymentFailure()`等方法，内部实现了严格的状态转换逻辑。例如，`Cancel()`方法会将`cancel_at_period_end`设置为`true`，而不是立即改变状态。`HandlePaymentFailure()`会将状态从`ACTIVE`变为`PAST_DUE`。
    *   `invoice_aggregate.go`:
        *   **`Invoice`聚合根**: 封装了发票和其行项目。
        *   **`GenerateForSubscription(sub, usage)`方法**: 这是一个核心的计费逻辑，它根据订阅信息和用量记录，计算出发票的所有行项目。
        *   **`Finalize()`方法**: 计算总额、税费、折扣，并将发票状态从`DRAFT`变为`OPEN`。
*   **`domain/service/`**: **这是实现定价灵活性的关键**。
    *   **`pricing_strategy.go`**:
        *   **`PricingStrategy`接口**: 定义`Calculate(usageRecords, priceInfo) Money`接口。
        *   **具体策略实现**:
            *   `FlatFeeStrategy`: 实现`PricingStrategy`，直接返回固定费用。
            *   `PerUnitStrategy`: 实现`PricingStrategy`，根据用量乘以单价。
            *   `TieredStrategy`: 实现`PricingStrategy`，实现复杂的阶梯定价逻辑。
        *   **`Invoice`聚合根在生成发票时，会根据`Price`的`pricing_model`字段，选择对应的策略来进行用量计费计算。**
    *   `proration_service.go`:
        *   `CalculateProration(fromSub, toSub)`: 一个纯函数，用于计算订阅升降级时需要补收的差价或生成的信用额度。

### 3.2 `application/` - 应用层 (The Business Workflows)

*   **`application/command/`**: **处理所有写操作和Saga编排**。
    *   `billing_command_service.go`:
        *   **`CreateSubscription(userID, priceID)`**:
            *   这是一个**Saga事务**。
            *   **步骤1**: 创建一个`Subscription`聚合根（初始状态可能为`TRIALING`或`ACTIVE`）。
            *   **步骤2**: 创建一个`Invoice`聚合根（如果不是免费试用）。
            *   **步骤3**: 调用`payment-service`发起支付。
            *   **步骤4**: 等待`payment-service`的异步事件回调。如果支付失败，则执行**补偿操作**（如取消订阅和发票）。
        *   **`RunBillingCycleForUser(userID)`**:
            *   由后台Worker调用，为单个用户执行计费。
            *   **步骤1**: 找到所有到期的订阅。
            *   **步骤2**: 为每个订阅生成新发票（调用`Invoice`聚合根的`Generate...`方法）。
            *   **步骤3**: 触发支付Saga。
*   **`application/query/`**: **处理所有读操作，并集成缓存**。
    *   `billing_query_service.go`:
        *   **`CheckAccess(userID, featureKey)`**:
            *   这是**性能极度敏感**的接口。
            *   **必须**使用缓存。首先检查Redis中是否存在`access:user_id:feature_key`的缓存。
            *   缓存未命中时，才查询数据库，获取用户的所有`ACTIVE`或`TRIALING`的订阅，检查其关联的`Feature`，然后将结果（`granted:true/false`）写入缓存并设置TTL。
        *   **`ListProducts()`, `GetUserSubscriptions(userID)`**: 从数据库查询并返回数据。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   实现`port.Repository`接口，负责与PostgreSQL交互。
    *   所有涉及金额的数据库字段**必须**使用`NUMERIC`或`BIGINT`（存储最小货币单位）类型。
    *   对`subscriptions`等核心表的更新，**必须**使用**乐观锁（version字段）**来处理并发。
*   **`adapter/client/`**:
    *   封装对`payment-service`和`user-core-service`的gRPC客户端调用。
    *   必须集成`pkg/tracing`和`pkg/errors`。
*   **`adapter/event/`**:
    *   `payment_consumer.go`: 消费来自`payment-service`的`PaymentSucceededEvent`或`PaymentFailedEvent`，并调用`billing_command_service`中相应的方法来继续或补偿Saga事务。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现gRPC接口，将读请求路由到`QueryService`，写请求路由到`CommandService`。

### 3.4 `cmd/worker/` - 后台计费任务

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    1.  **计费周期触发器 (Billing Cycle Runner)**:
        *   以**Kubernetes CronJob**的形式每日运行。
        *   **工作**: 从数据库中查询出所有在当天需要执行计费的用户ID。
        *   对于每个用户ID，向一个专门的**Kafka Topic (`billing-run-requests`)**或**Asynq任务队列**发送一个`RunBillingCycleForUser`任务。
    2.  **计费Worker**:
        *   一组独立的Pod，消费`billing-run-requests` Topic/Queue。
        *   每个Worker实例获取一个`userID`，并调用`application.command.RunBillingCycleForUser(userID)`来执行实际的计费逻辑。
*   **设计决策**: 将“触发”和“执行”分离，可以轻松地通过增加计费Worker的数量来**水平扩展计费处理能力**，避免单个CronJob成为瓶颈。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`billing-service`：
1.  **领域驱动设计**: 使用`Subscription`和`Invoice`聚合根封装了核心、复杂的业务规则和状态机，保证了内聚和一致性。
2.  **策略模式**: 通过`PricingStrategy`接口，将定价逻辑与核心计费流程解耦，使得未来添加新的定价模型变得简单。
3.  **CQRS思想**: 将高频、低延迟的读操作（如权限检查）与复杂的、事务性的写操作（如创建订阅）分离，便于针对性优化。
4.  **Saga编排**: 将本服务作为分布式事务的协调者，通过执行/补偿逻辑和事件驱动，保证了与外部金融服务的最终一致性。
5.  **可扩展的后台任务**: 将计费运行设计为可水平扩展的分布式任务，以应对大量用户的计费需求。

这种架构确保了`billing-service`在功能灵活、逻辑严谨的同时，也具备了生产环境所需的高性能、高可靠性和高可扩展性，能够作为CINA.CLUB平台商业化战略的坚实技术后盾。