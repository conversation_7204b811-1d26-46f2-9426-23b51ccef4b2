/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16
Modified: 2025-01-16
*/

package middleware

import (
	"context"
	"log/slog"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
	"google.golang.org/grpc/status"
)

// contextKey is used for storing logger in context
type contextKey string

const (
	loggerContextKey contextKey = "logger"
)

// contextWithLogger injects logger into context
func contextWithLogger(ctx context.Context, logger *slog.Logger) context.Context {
	if ctx == nil {
		ctx = context.Background()
	}
	return context.WithValue(ctx, loggerContextKey, logger)
}

// LoggingInterceptor creates a request logging interceptor
// Depends on Tracing middleware to get trace_id and span_id from context
func LoggingInterceptor(baseLogger *slog.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		startTime := time.Now()

		// 1. Create context Logger
		ctxLogger := createContextLogger(ctx, baseLogger)

		// 2. Inject new Logger into context
		newCtx := contextWithLogger(ctx, ctxLogger)

		// 3. Log request start
		ctxLogger.InfoContext(newCtx, "gRPC request started",
			slog.String("method", info.FullMethod),
			slog.String("peer_addr", getPeerAddr(ctx)),
			slog.String("user_agent", getUserAgent(ctx)),
			slog.String("request_id", getRequestID(ctx)),
		)

		// 4. Call downstream handler
		resp, err := handler(newCtx, req)

		// 5. Log request completion
		duration := time.Since(startTime)
		statusCode := codes.OK
		logLevel := slog.LevelInfo

		if err != nil {
			statusCode = status.Code(err)
			// Adjust log level based on error severity
			switch {
			case statusCode >= codes.Internal:
				logLevel = slog.LevelError
			case statusCode >= codes.InvalidArgument:
				logLevel = slog.LevelWarn
			}
		}

		logAttrs := []slog.Attr{
			slog.String("method", info.FullMethod),
			slog.String("status_code", statusCode.String()),
			slog.Int64("duration_ms", duration.Milliseconds()),
			slog.String("peer_addr", getPeerAddr(ctx)),
		}

		if err != nil {
			logAttrs = append(logAttrs, slog.String("error", err.Error()))
		}

		ctxLogger.LogAttrs(newCtx, logLevel, "gRPC request completed", logAttrs...)

		return resp, err
	}
}

// LoggingStreamInterceptor creates a stream request logging interceptor
func LoggingStreamInterceptor(baseLogger *slog.Logger) grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		startTime := time.Now()
		ctx := ss.Context()

		// 1. Create context Logger
		ctxLogger := createContextLogger(ctx, baseLogger)

		// 2. Wrap ServerStream
		wrappedStream := &loggingServerStream{
			ServerStream: ss,
			logger:       ctxLogger,
		}

		// 3. Log stream start
		ctxLogger.InfoContext(ctx, "gRPC stream started",
			slog.String("method", info.FullMethod),
			slog.String("peer_addr", getPeerAddr(ctx)),
			slog.String("user_agent", getUserAgent(ctx)),
			slog.String("request_id", getRequestID(ctx)),
		)

		// 4. Call downstream handler
		err := handler(srv, wrappedStream)

		// 5. Log stream completion
		duration := time.Since(startTime)
		statusCode := codes.OK
		logLevel := slog.LevelInfo

		if err != nil {
			statusCode = status.Code(err)
			switch {
			case statusCode >= codes.Internal:
				logLevel = slog.LevelError
			case statusCode >= codes.InvalidArgument:
				logLevel = slog.LevelWarn
			}
		}

		logAttrs := []slog.Attr{
			slog.String("method", info.FullMethod),
			slog.String("status_code", statusCode.String()),
			slog.Int64("duration_ms", duration.Milliseconds()),
			slog.String("peer_addr", getPeerAddr(ctx)),
		}

		if err != nil {
			logAttrs = append(logAttrs, slog.String("error", err.Error()))
		}

		ctxLogger.LogAttrs(ctx, logLevel, "gRPC stream completed", logAttrs...)

		return err
	}
}

// loggingServerStream wraps grpc.ServerStream to provide logging functionality
type loggingServerStream struct {
	grpc.ServerStream
	logger *slog.Logger
}

func (s *loggingServerStream) Context() context.Context {
	ctx := s.ServerStream.Context()
	return contextWithLogger(ctx, s.logger)
}

// createContextLogger creates a context Logger with tracing information
func createContextLogger(ctx context.Context, baseLogger *slog.Logger) *slog.Logger {
	logAttrs := []slog.Attr{}

	// Get trace_id and span_id from tracing context
	traceID := GetTraceID(ctx)
	spanID := GetSpanID(ctx)

	if traceID != "" {
		logAttrs = append(logAttrs, slog.String("trace_id", traceID))
	}
	if spanID != "" {
		logAttrs = append(logAttrs, slog.String("span_id", spanID))
	}

	// Try to get user information from context (if auth middleware has already processed)
	if userID := getUserIDFromContext(ctx); userID != "" {
		logAttrs = append(logAttrs, slog.String("user_id", userID))
	}

	// Add service information
	if serviceName := getServiceNameFromContext(ctx); serviceName != "" {
		logAttrs = append(logAttrs, slog.String("service", serviceName))
	}

	// Convert []slog.Attr to []any
	args := make([]any, 0, len(logAttrs)*2)
	for _, attr := range logAttrs {
		args = append(args, attr.Key, attr.Value)
	}
	return baseLogger.With(args...)
}

// getPeerAddr gets the client address
func getPeerAddr(ctx context.Context) string {
	if p, ok := peer.FromContext(ctx); ok {
		return p.Addr.String()
	}
	return "unknown"
}

// getUserAgent gets the user agent
func getUserAgent(ctx context.Context) string {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if userAgent := md.Get("user-agent"); len(userAgent) > 0 {
			return userAgent[0]
		}
	}
	return ""
}

// getRequestID gets the request ID
func getRequestID(ctx context.Context) string {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if requestID := md.Get("x-request-id"); len(requestID) > 0 {
			return requestID[0]
		}
	}
	return ""
}

// getUserIDFromContext gets user ID from context (set by auth middleware)
func getUserIDFromContext(ctx context.Context) string {
	// This implementation depends on how pkg/auth middleware stores user info in context
	// Assuming it uses a specific context key
	if userID := ctx.Value("user_id"); userID != nil {
		if uid, ok := userID.(string); ok {
			return uid
		}
	}
	return ""
}

// getServiceNameFromContext gets service name from context
func getServiceNameFromContext(ctx context.Context) string {
	if serviceName := ctx.Value("service_name"); serviceName != nil {
		if sn, ok := serviceName.(string); ok {
			return sn
		}
	}
	return ""
}

// LoggingOptions represents configuration options for logging interceptor
type LoggingOptions struct {
	// EnableRequestLogging whether to enable request logging (default: true)
	EnableRequestLogging bool
	// EnableResponseLogging whether to enable response logging (default: false, for performance)
	EnableResponseLogging bool
	// LogLevel log level (default: Info)
	LogLevel slog.Level
	// ExcludeMethods list of methods to exclude from logging
	ExcludeMethods []string
	// IncludeHeaders list of request headers to include in logs
	IncludeHeaders []string
}

// DefaultLoggingOptions returns default logging options
func DefaultLoggingOptions() *LoggingOptions {
	return &LoggingOptions{
		EnableRequestLogging:  true,
		EnableResponseLogging: false,
		LogLevel:              slog.LevelInfo,
		ExcludeMethods: []string{
			"/grpc.health.v1.Health/Check",
			"/grpc.health.v1.Health/Watch",
		},
		IncludeHeaders: []string{
			"user-agent",
			"x-request-id",
			"x-forwarded-for",
		},
	}
}

// LoggingInterceptorWithOptions creates a logging interceptor with custom options
func LoggingInterceptorWithOptions(baseLogger *slog.Logger, opts *LoggingOptions) grpc.UnaryServerInterceptor {
	if opts == nil {
		opts = DefaultLoggingOptions()
	}

	// Create method filter
	excludeMap := make(map[string]bool)
	for _, method := range opts.ExcludeMethods {
		excludeMap[method] = true
	}

	// Create header filter
	includeHeaders := make(map[string]bool)
	for _, header := range opts.IncludeHeaders {
		includeHeaders[header] = true
	}

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Check if this method should be excluded
		if excludeMap[info.FullMethod] {
			return handler(ctx, req)
		}

		startTime := time.Now()

		// Create context Logger
		ctxLogger := createContextLoggerWithOptions(ctx, baseLogger, includeHeaders)
		newCtx := contextWithLogger(ctx, ctxLogger)

		// Log request start
		if opts.EnableRequestLogging {
			ctxLogger.InfoContext(newCtx, "gRPC request started",
				slog.String("method", info.FullMethod),
				slog.String("peer_addr", getPeerAddr(ctx)),
			)
		}

		// Call downstream handler
		resp, err := handler(newCtx, req)

		// Log request completion
		duration := time.Since(startTime)
		statusCode := codes.OK
		logLevel := opts.LogLevel

		if err != nil {
			statusCode = status.Code(err)
			switch {
			case statusCode >= codes.Internal:
				logLevel = slog.LevelError
			case statusCode >= codes.InvalidArgument:
				logLevel = slog.LevelWarn
			}
		}

		logAttrs := []slog.Attr{
			slog.String("method", info.FullMethod),
			slog.String("status_code", statusCode.String()),
			slog.Int64("duration_ms", duration.Milliseconds()),
		}

		if err != nil {
			logAttrs = append(logAttrs, slog.String("error", err.Error()))
		}

		ctxLogger.LogAttrs(newCtx, logLevel, "gRPC request completed", logAttrs...)

		return resp, err
	}
}

// createContextLoggerWithOptions creates a context Logger with custom options
func createContextLoggerWithOptions(ctx context.Context, baseLogger *slog.Logger, includeHeaders map[string]bool) *slog.Logger {
	logAttrs := []slog.Attr{}

	// Add tracing information
	if traceID := GetTraceID(ctx); traceID != "" {
		logAttrs = append(logAttrs, slog.String("trace_id", traceID))
	}
	if spanID := GetSpanID(ctx); spanID != "" {
		logAttrs = append(logAttrs, slog.String("span_id", spanID))
	}

	// Add user information
	if userID := getUserIDFromContext(ctx); userID != "" {
		logAttrs = append(logAttrs, slog.String("user_id", userID))
	}

	// Add request header information
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		for header := range includeHeaders {
			if values := md.Get(header); len(values) > 0 {
				logAttrs = append(logAttrs, slog.String(header, values[0]))
			}
		}
	}

	// Convert []slog.Attr to []any
	args := make([]any, 0, len(logAttrs)*2)
	for _, attr := range logAttrs {
		args = append(args, attr.Key, attr.Value)
	}
	return baseLogger.With(args...)
}
