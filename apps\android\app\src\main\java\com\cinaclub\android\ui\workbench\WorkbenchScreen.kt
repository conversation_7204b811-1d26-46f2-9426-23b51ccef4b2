/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.workbench

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.cinaclub.android.ui.theme.CinaClubTheme

/**
 * 工作台界面 - CINA.CLUB 企业功能中心
 * 提供企业应用、工具和服务的统一入口
 * 
 * Features:
 * - 企业应用快捷访问
 * - 常用工具集成
 * - 审批流程入口
 * - 企业管理功能
 * - 第三方应用集成
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkbenchScreen(
    onToolClick: (WorkbenchTool) -> Unit = {},
    onManageEnterpriseClick: () -> Unit = {},
    onFindAppsClick: () -> Unit = {},
    onSubmitRequestClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    text = "工作台",
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF4A90E2)
            ),
            actions = {
                IconButton(onClick = onFindAppsClick) {
                    Icon(
                        Icons.Default.Add,
                        contentDescription = "添加应用",
                        tint = Color.White
                    )
                }
            }
        )

        // 快捷功能区
        QuickActionsSection(
            onManageEnterpriseClick = onManageEnterpriseClick,
            onSubmitRequestClick = onSubmitRequestClick,
            modifier = Modifier.padding(16.dp)
        )

        // 应用工具网格
        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            contentPadding = PaddingValues(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(workbenchTools) { tool ->
                WorkbenchToolItem(
                    tool = tool,
                    onClick = { onToolClick(tool) }
                )
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // 底部信息区
        BottomInfoSection(
            modifier = Modifier.padding(16.dp)
        )
    }
}

/**
 * 快捷功能区组件
 */
@Composable
private fun QuickActionsSection(
    onManageEnterpriseClick: () -> Unit,
    onSubmitRequestClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "快捷功能",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 企业管理
            QuickActionCard(
                title = "企业管理",
                subtitle = "组织架构、权限设置",
                icon = Icons.Default.Business,
                backgroundColor = Color(0xFFE3F2FD),
                iconColor = Color(0xFF1976D2),
                onClick = onManageEnterpriseClick,
                modifier = Modifier.weight(1f)
            )

            // 申请提交
            QuickActionCard(
                title = "申请提交",
                subtitle = "请假、报销、审批",
                icon = Icons.Default.Assignment,
                backgroundColor = Color(0xFFF3E5F5),
                iconColor = Color(0xFF7B1FA2),
                onClick = onSubmitRequestClick,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 快捷功能卡片
 */
@Composable
private fun QuickActionCard(
    title: String,
    subtitle: String,
    icon: ImageVector,
    backgroundColor: Color,
    iconColor: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .height(80.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = iconColor,
                modifier = Modifier.size(24.dp)
            )
            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF666666)
                )
            }
        }
    }
}

/**
 * 工作台工具项
 */
@Composable
private fun WorkbenchToolItem(
    tool: WorkbenchTool,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickable { onClick() }
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 图标背景
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(tool.backgroundColor),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = tool.icon,
                contentDescription = tool.name,
                tint = tool.iconColor,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 工具名称
        Text(
            text = tool.name,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF333333),
            maxLines = 1
        )

        // 新功能标记
        if (tool.isNew) {
            Text(
                text = "NEW",
                style = MaterialTheme.typography.labelSmall,
                color = Color(0xFFFF5722),
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}

/**
 * 底部信息区
 */
@Composable
private fun BottomInfoSection(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "应用中心",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                Text(
                    text = "查看全部 >",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF4A90E2)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "发现更多企业应用，提升工作效率",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF666666)
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 推荐应用标签
                val recommendedApps = listOf("会议室预订", "考勤打卡", "企业邮箱", "项目管理")
                recommendedApps.forEach { appName ->
                    AssistChip(
                        onClick = { /* TODO: 导航到应用详情 */ },
                        label = { Text(appName, style = MaterialTheme.typography.bodySmall) },
                        colors = AssistChipDefaults.assistChipColors(
                            containerColor = Color(0xFFF0F0F0)
                        )
                    )
                }
            }
        }
    }
}

/**
 * 工作台工具数据类
 */
data class WorkbenchTool(
    val id: String,
    val name: String,
    val icon: ImageVector,
    val backgroundColor: Color,
    val iconColor: Color,
    val isNew: Boolean = false,
    val category: String = ""
)

/**
 * 预定义工作台工具列表
 */
private val workbenchTools = listOf(
    WorkbenchTool("approval", "审批", Icons.Default.Assignment, Color(0xFFE8F5E8), Color(0xFF4CAF50)),
    WorkbenchTool("attendance", "打卡", Icons.Default.Schedule, Color(0xFFE3F2FD), Color(0xFF2196F3)),
    WorkbenchTool("report", "汇报", Icons.Default.Assessment, Color(0xFFFFF3E0), Color(0xFFFF9800)),
    WorkbenchTool("notice", "公告", Icons.Default.Campaign, Color(0xFFFCE4EC), Color(0xFFE91E63)),
    WorkbenchTool("meeting", "会议", Icons.Default.VideoCall, Color(0xFFF3E5F5), Color(0xFF9C27B0)),
    WorkbenchTool("document", "文档", Icons.Default.Description, Color(0xFFE0F2F1), Color(0xFF009688)),
    WorkbenchTool("calendar", "日程", Icons.Default.Event, Color(0xFFE8EAF6), Color(0xFF3F51B5)),
    WorkbenchTool("contacts", "通讯录", Icons.Default.Contacts, Color(0xFFE1F5FE), Color(0xFF03A9F4)),
    WorkbenchTool("mail", "邮件", Icons.Default.Email, Color(0xFFE0F7FA), Color(0xFF00BCD4)),
    WorkbenchTool("drive", "网盘", Icons.Default.CloudUpload, Color(0xFFE8F5E8), Color(0xFF8BC34A)),
    WorkbenchTool("finance", "财务", Icons.Default.AccountBalance, Color(0xFFFFF8E1), Color(0xFFFFC107)),
    WorkbenchTool("hr", "人事", Icons.Default.People, Color(0xFFEDE7F6), Color(0xFF673AB7), isNew = true),
    WorkbenchTool("project", "项目", Icons.Default.Dashboard, Color(0xFFEFEBE9), Color(0xFF795548)),
    WorkbenchTool("crm", "客户", Icons.Default.Person, Color(0xFFE0F2F1), Color(0xFF4CAF50)),
    WorkbenchTool("inventory", "库存", Icons.Default.Inventory, Color(0xFFFFF3E0), Color(0xFFFF9800)),
    WorkbenchTool("analytics", "分析", Icons.Default.Analytics, Color(0xFFE3F2FD), Color(0xFF2196F3), isNew = true)
)

@Preview(showBackground = true)
@Composable
fun WorkbenchScreenPreview() {
    CinaClubTheme {
        WorkbenchScreen()
    }
} 