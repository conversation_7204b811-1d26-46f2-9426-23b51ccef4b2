好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`core/api`** 这个具体目录的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档是整个平台**通信的基石**，定义了所有服务之间以及服务与客户端之间交互的“法律合同”。它的规范性和稳定性至关重要。

---
### CINA.CLUB - 共享核心API契约 (`core/api`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师]  
**审批人:** [CTO, 各技术团队负责人]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计原则与规范](#3-核心设计原则与规范)
4.  [目录与文件结构](#4-目录与文件结构)
5.  [功能需求 (按Protobuf文件类型)](#5-功能需求-按protobuf文件类型)
    *   [5.1 服务定义 (`*_service.proto`)](#51-服务定义-_serviceproto)
    *   [5.2 消息定义 (`*_model.proto`)](#52-消息定义-_modelproto)
    *   [5.3 通用类型 (`common.proto`)](#53-通用类型-commonproto)
    *   [5.4 错误定义 (`errors.proto`)](#54-错误定义-errorsproto)
    *   [5.5 事件定义 (`*_events.proto`)](#55-事件定义-_eventsproto)
6.  [代码生成与自动化](#6-代码生成与自动化)
7.  [版本控制与演进策略](#7-版本控制与演进策略)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的分布式微服务架构中，所有组件间的通信都需要一个**统一、明确、强类型**的契约。`core/api` 目录的目的就是作为这个契约的**唯一、权威的单一事实来源 (Single Source of Truth)**。它通过使用**Protocol Buffers (Protobuf)** 来定义所有的gRPC服务、RESTful API（通过`grpc-gateway`）、以及消息队列中的事件结构，旨在：
*   **确保通信的类型安全**: 在编译时发现接口不匹配的错误。
*   **实现语言无关性**: 为Go后端、Python Worker、TypeScript前端等自动生成类型一致的客户端和服务端代码。
*   **提供清晰的文档**: Protobuf定义本身就是一种清晰、易读的接口文档。
*   **保证向后兼容性**: 利用Protobuf的机制，安全地演进API而不破坏现有客户端。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   定义平台所有微服务的gRPC接口（Services, RPCs, Messages）。
    *   为RESTful API添加HTTP注解（`google.api.http`）。
    *   定义所有通过消息队列传递的领域事件的结构。
    *   定义平台通用的数据类型和错误结构。
*   **范围之外 (Out-of-Scope)**:
    *   任何接口的**实现逻辑**。
    *   任何特定语言的代码。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端和前端开发人员**。
*   **API文档的消费者**（如QA团队、第三方开发者）。
*   **代码生成自动化脚本**。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`core/api` 是位于`core/`目录下的最核心的子目录之一。它不依赖于任何其他代码，但被几乎所有需要通信的`services/`和`apps/`所依赖（通过生成的代码）。它是整个系统解耦和协同工作的基础。

#### 2.2. 设计原则
*   **契约即文档 (Contract-as-Documentation)**: `.proto`文件应编写得足够清晰，包含丰富的注释，使其本身就成为最好的接口文档。
*   **稳定性与向后兼容性 (Stability & Backward-Compatibility)**: 对已发布的API的修改必须严格遵循向后兼容原则。
*   **一致性 (Consistency)**: 所有API的设计必须遵循统一的命名约定和设计模式。
*   **最小化暴露 (Minimal Exposure)**: 只定义真正需要跨服务或对客户端暴露的接口和数据结构。
*   **自动化驱动 (Automation-Driven)**: 整个目录的设计都应服务于`buf`等工具的自动化代码生成和校验。

---

### 3. 核心设计原则与规范

#### 3.1. 命名约定
*   **包名 (Package)**: `hina.v1.<service_name>`，如 `hina.v1.user_core`。
*   **文件名**: 使用蛇形命名法，如 `user_core_service.proto`, `user_model.proto`。
*   **服务名 (Service)**: `PascalCase` + `Service`后缀，如 `UserCoreService`。
*   **RPC名**: `PascalCase`，动词开头，如 `GetUser`, `CreateUser`。
*   **消息名 (Message)**: `PascalCase`，清晰描述其内容。RPC请求/响应遵循`{RPCName}Request`/`{RPCName}Response`模式。
*   **字段名**: 使用蛇形命名法，如 `user_id`, `display_name`。

#### 3.2. 标准RPC设计模式
*   **CRUD**
    *   Create: `Create...Request` -> `...`
    *   Read: `Get...Request` -> `...` (单体), `List...Request` -> `List...Response` (列表)
    *   Update: `Update...Request` -> `...` (使用`google.protobuf.FieldMask`进行部分更新)
    *   Delete: `Delete...Request` -> `google.protobuf.Empty`
*   **列表分页 (Pagination)**: `List...Request`必须包含`int32 page_size`和`string page_token`。`List...Response`必须包含`repeated ... items`和`string next_page_token`。

#### 3.3. 字段编号规则
*   **严禁**修改或重用已发布的字段编号。
*   **严禁**删除已发布的字段。应使用`reserved`关键字进行标记，或使用`deprecated=true`选项。

---

### 4. 目录与文件结构

```
core/api/
├── buf.gen.yaml                # Buf: 代码生成配置
├── buf.yaml                    # Buf: Lint与Breaking Change检测配置
├── buf.lock                    # Buf: 依赖锁定文件
└── proto/
    └── v1/                     # API版本号
        ├── common.proto        # 通用共享类型
        ├── errors.proto        # 标准化错误详情
        │
        ├── user_core_service.proto  # 用户核心服务定义
        ├── user_model.proto         # 用户相关的消息定义
        ├── user_events.proto        # 用户相关的事件定义
        │
        ├── chat_api_service.proto   # 聊天服务定义
        ├── chat_model.proto         # 聊天相关的消息定义
        ├── chat_events.proto        # 聊天相关的事件定义
        │
        └── ... (每个服务都遵循 service/model/events 的文件拆分模式)
```
**设计理念**: 每个业务领域（通常对应一个微服务）的文件被组织在一起。
*   **`*_service.proto`**: 只包含`service`和`rpc`定义。
*   **`*_model.proto`**: 只包含该服务使用的`message`定义。
*   **`*_events.proto`**: 只包含该服务发布的领域事件的`message`定义。
这种拆分使得职责更清晰，并减少了因修改消息体而导致服务接口文件变更的可能性。

---

### 5. 功能需求 (按Protobuf文件类型)

#### 5.1. 服务定义 (`*_service.proto`)
*   **FR5.1.1 (gRPC定义)**: 必须清晰地定义每个RPC的请求和响应消息类型。
*   **FR5.1.2 (HTTP注解)**: 所有需要通过API Gateway对客户端暴露的RPC，**必须**添加`google.api.http`注解，以定义其RESTful映射。
    ```protobuf
    rpc GetUser(GetUserRequest) returns (User) {
      option (google.api.http) = {
        get: "/api/v1/users/{user_id}"
      };
    }
    ```
*   **FR5.1.3 (注释)**: 每个`service`和`rpc`都必须有详细的注释，解释其作用、参数和行为。

#### 5.2. 消息定义 (`*_model.proto`)
*   **FR5.2.1 (数据类型)**: 必须使用Protobuf的标准数据类型。对于时间，**必须**使用`google.protobuf.Timestamp`。对于可能为空的标量类型，使用包装类型（如`google.protobuf.StringValue`）或`optional`关键字。
*   **FR5.2.2 (验证规则)**: 对于需要验证的请求消息字段，**必须**使用`protoc-gen-validate`的规则进行注解。
    ```protobuf
    message CreateUserRequest {
      string email = 1 [(validate.rules).string.email = true];
      string password = 2 [(validate.rules).string = {min_len: 12}];
    }
    ```
*   **FR5.2.3 (注释)**: 每个`message`和`field`都必须有注释，解释其含义和任何约束。

#### 5.3. 通用类型 (`common.proto`)
*   **职责**: 定义被多个服务共享的、与具体业务无关的通用消息类型。
*   **内容**:
    *   `UUID`: `message UUID { string value = 1; }`
    *   `PaginationRequest`: `message PaginationRequest { int32 page_size=1; string page_token=2; }`
    *   `Empty`: `google.protobuf.Empty`的别名或直接使用。
    *   `GeoPoint`: `message GeoPoint { double latitude=1; double longitude=2; }`

#### 5.4. 错误定义 (`errors.proto`)
*   **职责**: 定义用于gRPC `details`字段传递的标准化错误信息。
*   **内容**:
    ```protobuf
    message AppErrorDetail {
      string error_code = 1; // 如 "InvalidArgument", "NotFound"
      map<string, string> metadata = 2; // 如 {"field": "email"}
    }
    ```

#### 5.5. 事件定义 (`*_events.proto`)
*   **职责**: 定义所有通过Kafka传递的领域事件的结构。
*   **内容**: 每个事件都是一个`message`，如`UserRegisteredEvent`。
*   **FR5.5.1 (包含上下文)**: 每个事件消息都应包含一个`EventHeader`字段，其中包含`event_id`, `timestamp`, `source_service`等元数据。

---

### 6. 代码生成与自动化

*   **FR6.1 (Buf集成)**: `core/api`目录必须是一个完整的`buf`模块。
    *   `buf.yaml`: `version: v1`, `name: buf.build/cinaclub/core`, 并配置严格的Lint和Breaking规则。
    *   `buf.gen.yaml`: 必须配置好所有目标语言的代码生成插件和选项。
        *   `protoc-gen-go`, `protoc-gen-go-grpc` (for Go)
        *   `protoc-gen-grpc-web` (for TypeScript/JS)
        *   `protoc-gen-openapiv2` (for REST gateway)
        *   `protoc-gen-validate` (for validation)
*   **FR6.2 (自动化脚本)**: `scripts/gen-proto.sh`脚本必须能一键执行`buf generate`，并将生成的代码输出到各自模块的指定位置（如`services/*/gen/`, `apps/*/src/gen/`）。

---

### 7. 版本控制与演进策略

*   **FR7.1 (严格向后兼容)**:
    *   **禁止**：删除字段、修改字段编号、修改字段类型。
    *   **允许**：新增字段（必须有新的、未被使用的编号）、新增RPC、新增服务。
*   **FR7.2 (废弃)**: 对于需要废弃的字段或RPC，**必须**使用`deprecated=true`选项进行标记，并提供清晰的迁移指南。
    ```protobuf
    string old_field = 1 [deprecated = true];
    ```
*   **FR7.3 (Breaking Change检测)**: CI流水线中**必须**包含`buf breaking --against .git#tag=v...`命令，以防止任何破坏性变更被合入主分支。
*   **FR7.4 (API版本ing)**: 所有API都在`v1`包下。当需要进行不兼容的大版本升级时，将创建新的`v2`目录，并与`v1`并行存在一段时间。

---

### 8. 技术约束与开发规范

*   **TC8.1 (Protobuf版本)**: 强制使用`proto3`语法。
*   **TC8.2 (开发规范)**:
    *   **强制使用**: 平台所有跨服务的通信契约**必须**在此定义。
    *   **评审流程**: 任何对`core/api`的修改都必须通过Pull Request提交，并需要**至少一名来自不同于作者所在团队的核心工程师**进行审查。
    *   **文档同步**: 修改`.proto`文件时，必须检查是否需要更新相关的SRS或架构文档。

---
这份SRS为`core/api`的设计和管理提供了坚实、全面的指导。通过严格遵守这些规范，CINA.CLUB平台可以确保其分布式系统的通信是**类型安全的、高效的、向后兼容的、并且自文档化的**，为平台的长期、稳定演进奠定了最重要的基础。