/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20
 * Modified: 2025-01-20
 */

# CINA.CLUB API Protocol Buffers v1

## 概述

本目录包含CINA.CLUB平台所有微服务的Protocol Buffers定义文件。这些proto文件定义了服务间通信的统一接口契约，支持gRPC和RESTful API（通过grpc-gateway）。

## 架构原则

- **Go-Centric全栈协同**: 后端采用Go，前端原生应用通过Go编写的核心逻辑库共享功能
- **API优先设计**: 所有通信通过良好定义的gRPC/Protobuf契约暴露
- **微服务架构**: 后端由高内聚、低耦合的独立微服务构成
- **多原生平台架构**: 为每个主流生态提供最佳原生UI框架
- **异步与事件驱动**: 广泛使用Kafka进行服务解耦和异步处理
- **隐私与安全内建**: 端到端加密(E2EE)和应用层加密(ALE)

## 文件结构

### 基础定义

| 文件 | 描述 | 大小 |
|------|------|------|
| `common.proto` | 通用共享类型和枚举定义 | 3.1KB |
| `errors.proto` | 标准化错误定义和错误处理结构 | 3.4KB |

### 核心服务

| 文件 | 服务 | 描述 | 大小 |
|------|------|------|------|
| `user_core.proto` | UserCoreService | 用户核心服务：认证、账户、等级、会员、权限、KYC | 7.2KB |
| `ai_assistant.proto` | AIAssistantService | AI助手服务：对话编排、LLM代理、工作流引擎 | 9.2KB |
| `personal_kb.proto` | PersonalKBService | 个人知识库服务：E2EE笔记管理、搜索、导入导出 | 11KB |
| `chat.proto` | ChatAPIService | 聊天服务：群聊管理、消息历史、实时通信 | 15KB |
| `live.proto` | LiveAPIService | 直播服务：直播间管理、状态机、观众互动 | 15KB |
| `payment.proto` | PaymentService | 支付服务：多网关支付、退款、争议处理 | 17KB |
| `billing.proto` | BillingService | 计费服务：订阅管理、定价模型、使用量统计 | 10KB |
| `notification.proto` | NotificationService | 通知服务：多渠道分发、模板引擎、防打扰策略 | 13KB |
| `search.proto` | SearchService | 搜索服务：混合搜索、个性化排序、索引管理 | 13KB |

### 社交与互动服务

| 文件 | 服务 | 描述 | 大小 |
|------|------|------|------|
| `social.proto` | SocialService | 社交服务：关注、好友、粉丝、拉黑、推荐 | 16KB |
| `review.proto` | ReviewService | 评价服务：多维度评价、信誉管理、举报申诉 | 18KB |

### AI与智能服务

| 文件 | 服务 | 描述 | 大小 |
|------|------|------|------|
| `embedding.proto` | EmbeddingService | 向量化服务：多模态向量生成、相似度计算、搜索 | 17KB |
| `memory.proto` | MemoryService | 记忆服务：E2EE个人记忆、RAG支持、生命周期管理 | 20KB |
| `routines.proto` | RoutinesService | 自动化规则：事件驱动工作流、规则引擎、模板 | 19KB |

### 市场与交易服务

| 文件 | 服务 | 描述 | 大小 |
|------|------|------|------|
| `service_offering.proto` | ServiceOfferingService | 服务市场：技能共享、预订管理、服务认证 | 18KB |

### 平台基础设施服务

| 文件 | 服务 | 描述 | 大小 |
|------|------|------|------|
| `content_moderation.proto` | ContentModerationService | 内容审核：AI+人工混合审核、敏感检测、违规处理 | 19KB |
| `file_storage.proto` | FileStorageService | 文件存储：多云存储、CDN加速、版本控制、安全扫描 | 20KB |

## 主要功能领域

### 1. 用户与身份管理
- **用户核心服务**: 统一认证、账户管理、等级系统、会员体系
- **社交服务**: 关注/好友关系、粉丝管理、拉黑功能、用户推荐
- **家庭族谱服务**: 家庭关系、族谱管理、亲缘关系

### 2. AI与个性化
- **AI助手服务**: Agentic工作流、LLM代理、对话编排
- **个人知识库**: E2EE笔记管理、混合搜索、智能标签
- **记忆服务**: 个人记忆管理、RAG支持、生命周期管理
- **自动化规则**: 用户自定义Routines、多源触发器
- **向量化服务**: 多模态向量生成、相似度计算、语义搜索

### 3. 实时通信与直播
- **聊天服务**: 持久化群聊、1v1聊天、多设备同步
- **直播服务**: 实时视频直播、互动管理、统计分析
- **WebSocket服务**: 实时连接管理、消息广播
- **IM服务**: 直播间弹幕、礼物系统、实时互动

### 4. 内容与知识
- **共享知识库**: 付费知识内容、策展工作流
- **社区论坛**: 富文本内容、热度算法、社区管理
- **短视频服务**: 视频上传、智能转码、CDN分发
- **快讯服务**: 7x24实时新闻、去重、增强发布
- **内容审核**: AI+人工混合审核、敏感词检测、举报处理

### 5. 市场与交易
- **服务市场**: 技能共享平台、服务预订、认证管理
- **支付系统**: 多网关路由、法币支付、争议处理
- **计费系统**: 订阅管理、阶梯定价、使用量计费
- **评价系统**: 多维度评价、信誉管理、举报申诉

### 6. 平台基础设施
- **搜索系统**: 混合搜索、语义检索、个性化排序
- **通知系统**: 多渠道分发、模板引擎、智能聚合
- **文件存储**: 多云存储、CDN加速、版本控制、安全扫描
- **密钥管理**: 信封加密、用户数据加密密钥管理

## 设计特性

### 端到端加密 (E2EE)
所有敏感用户数据（如个人笔记、私人消息）都通过`EncryptedData`类型进行加密存储和传输。

### 多语言支持
通过`LocalizedText`类型支持多语言内容管理。

### 地理位置
通过`Location`类型支持地理位置相关功能。

### 货币与支付
通过`Money`类型支持多币种金额表示和精确计算。

### 分页与搜索
统一的`PaginationRequest`/`PaginationResponse`和搜索模式。

### 时间管理
使用`google.protobuf.Timestamp`和`TimeRange`进行时间相关操作。

## HTTP API映射

所有gRPC服务都通过`google.api.http`注解映射为RESTful API，支持：
- GET: 获取资源
- POST: 创建资源
- PUT: 更新资源  
- DELETE: 删除资源

## 版本控制

当前版本为v1，遵循严格的向后兼容原则：
- ✅ 允许：新增字段、新增RPC、新增服务
- ❌ 禁止：删除字段、修改字段编号、修改字段类型

## 代码生成

使用`buf`工具链从proto文件自动生成：
- Go: 服务端存根、客户端、Protobuf结构体
- TypeScript: gRPC-Web客户端、消息类型定义
- OpenAPI: REST API文档规范
- 验证代码: 字段验证逻辑

## 统计信息

- **文件数量**: 19个proto文件
- **总代码行数**: ~12,000行
- **服务数量**: 17个主要服务
- **消息类型**: 500+个消息定义
- **RPC方法**: 300+个API端点

## 使用示例

### gRPC调用
```go
client := user_core.NewUserCoreServiceClient(conn)
response, err := client.GetProfile(ctx, &user_core.GetProfileRequest{
    UserId: "user123",
})
```

### REST API调用
```bash
curl -X GET "https://api.cina.club/api/v1/users/user123/profile" \
  -H "Authorization: Bearer <token>"
```

## 开发指南

1. **新增服务**: 创建新的`service_name.proto`文件
2. **修改现有服务**: 只能新增字段和RPC，不能删除或修改现有定义
3. **版本升级**: 当需要破坏性变更时，创建v2目录
4. **测试**: 使用`buf lint`和`buf breaking`验证变更

## 贡献

所有对proto文件的修改都必须：
1. 通过Pull Request提交
2. 经过至少一名核心工程师审查
3. 通过CI/CD流水线验证
4. 更新相关文档

---

本API设计为CINA.CLUB平台提供了强类型、高性能、可扩展的通信基础，支撑平台的Go-Centric全栈架构和微服务生态系统。 