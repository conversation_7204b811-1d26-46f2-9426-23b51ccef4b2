/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.calendar

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter

/**
 * Calendar screen following WeChat Work calendar design.
 * Shows monthly calendar view with events and scheduling.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    onEventClick: (CalendarEvent) -> Unit,
    onDateClick: (LocalDate) -> Unit,
    onAddEventClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var currentMonth by remember { mutableStateOf(YearMonth.now()) }
    val today = LocalDate.now()
    
    val events = remember {
        mapOf(
            LocalDate.of(2025, 6, 20) to listOf(
                CalendarEvent(
                    id = "1",
                    title = "预定会议...",
                    time = "14:00"
                )
            )
        )
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "${currentMonth.year}年${currentMonth.monthValue}月",
                    fontWeight = FontWeight.Medium
                )
            },
            navigationIcon = {
                IconButton(onClick = { /* Navigate back */ }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                IconButton(onClick = { /* Search events */ }) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "Search"
                    )
                }
                
                IconButton(onClick = { /* More options */ }) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "More"
                    )
                }
                
                IconButton(onClick = onAddEventClick) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add Event"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF4A90E2)
            )
        )
        
        // Calendar grid
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column {
                // Month navigation
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = { currentMonth = currentMonth.minusMonths(1) }
                    ) {
                        Icon(
                            imageVector = Icons.Default.ChevronLeft,
                            contentDescription = "Previous Month"
                        )
                    }
                    
                    Text(
                        text = "${currentMonth.year}年${currentMonth.monthValue}月",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    
                    IconButton(
                        onClick = { currentMonth = currentMonth.plusMonths(1) }
                    ) {
                        Icon(
                            imageVector = Icons.Default.ChevronRight,
                            contentDescription = "Next Month"
                        )
                    }
                }
                
                // Weekday headers
                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    val weekdays = listOf("日", "一", "二", "三", "四", "五", "六")
                    weekdays.forEach { day ->
                        Text(
                            text = day,
                            modifier = Modifier.weight(1f),
                            textAlign = TextAlign.Center,
                            color = Color(0xFF888888),
                            fontSize = 14.sp
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Calendar grid
                val firstDayOfMonth = currentMonth.atDay(1)
                val lastDayOfMonth = currentMonth.atEndOfMonth()
                val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7
                val daysInMonth = currentMonth.lengthOfMonth()
                
                LazyVerticalGrid(
                    columns = GridCells.Fixed(7),
                    modifier = Modifier.height(300.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp)
                ) {
                    // Empty cells for days before the first day of month
                    items(firstDayOfWeek) {
                        Box(modifier = Modifier.height(40.dp))
                    }
                    
                    // Days of the month
                    items(daysInMonth) { dayOfMonth ->
                        val date = currentMonth.atDay(dayOfMonth + 1)
                        val isToday = date == today
                        val hasEvents = events.containsKey(date)
                        
                        CalendarDay(
                            date = date,
                            isToday = isToday,
                            hasEvents = hasEvents,
                            onClick = { onDateClick(date) }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
        
        // Today's events section
        if (events.containsKey(today)) {
            Text(
                text = "${today.dayOfMonth}月${today.monthValue}日 周${getDayOfWeekChinese(today)} (今天)",
                color = Color(0xFF333333),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(16.dp)
            )
            
            events[today]?.forEach { event ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp)
                        .clickable { onEventClick(event) },
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Text(
                        text = event.title,
                        color = Color(0xFF333333),
                        fontSize = 14.sp,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun CalendarDay(
    date: LocalDate,
    isToday: Boolean,
    hasEvents: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(40.dp)
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (isToday) {
            Surface(
                modifier = Modifier.size(32.dp),
                color = Color(0xFF4A90E2),
                shape = CircleShape
            ) {
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = date.dayOfMonth.toString(),
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        } else {
            Text(
                text = date.dayOfMonth.toString(),
                color = Color(0xFF333333),
                fontSize = 14.sp
            )
        }
        
        if (hasEvents) {
            Box(
                modifier = Modifier
                    .size(4.dp)
                    .background(
                        color = if (isToday) Color.White else Color(0xFF4A90E2),
                        shape = CircleShape
                    )
                    .align(Alignment.BottomCenter)
            )
        }
    }
}

private fun getDayOfWeekChinese(date: LocalDate): String {
    return when (date.dayOfWeek.value) {
        1 -> "一"
        2 -> "二"
        3 -> "三"
        4 -> "四"
        5 -> "五"
        6 -> "六"
        7 -> "日"
        else -> ""
    }
}

// Data class for calendar events
data class CalendarEvent(
    val id: String,
    val title: String,
    val time: String,
    val description: String? = null
) 