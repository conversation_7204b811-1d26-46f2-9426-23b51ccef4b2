/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import (
	"crypto/tls"
	"time"
)

// KafkaProducerConfig 定义 Kafka 生产者的配置
type KafkaProducerConfig struct {
	// Brokers Kafka 代理服务器地址列表
	Brokers []string `yaml:"brokers" json:"brokers" validate:"required,min=1"`

	// Security 安全配置
	Security KafkaSecurityConfig `yaml:"security" json:"security"`

	// WriterConfig 写入器配置
	WriterConfig KafkaWriterConfig `yaml:"writer" json:"writer"`
}

// KafkaConsumerConfig 定义 Kafka 消费者的配置
type KafkaConsumerConfig struct {
	// Brokers Kafka 代理服务器地址列表
	Brokers []string `yaml:"brokers" json:"brokers" validate:"required,min=1"`

	// GroupID 消费者组ID
	GroupID string `yaml:"group_id" json:"group_id" validate:"required"`

	// Topics 要订阅的主题列表
	Topics []string `yaml:"topics" json:"topics" validate:"required,min=1"`

	// Security 安全配置
	Security KafkaSecurityConfig `yaml:"security" json:"security"`

	// ReaderConfig 读取器配置
	ReaderConfig KafkaReaderConfig `yaml:"reader" json:"reader"`

	// RetryConfig 重试配置
	RetryConfig RetryConfig `yaml:"retry" json:"retry"`

	// DLQConfig 死信队列配置
	DLQConfig DLQConfig `yaml:"dlq" json:"dlq"`
}

// KafkaSecurityConfig Kafka 安全配置
type KafkaSecurityConfig struct {
	// SASL 配置
	SASL SASLConfig `yaml:"sasl" json:"sasl"`

	// TLS 配置
	TLS TLSConfig `yaml:"tls" json:"tls"`
}

// SASLConfig SASL 认证配置
type SASLConfig struct {
	// Enabled 是否启用 SASL
	Enabled bool `yaml:"enabled" json:"enabled"`

	// Mechanism SASL 机制 (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512)
	Mechanism string `yaml:"mechanism" json:"mechanism" validate:"oneof=PLAIN SCRAM-SHA-256 SCRAM-SHA-512"`

	// Username 用户名
	Username string `yaml:"username" json:"username"`

	// Password 密码
	Password string `yaml:"password" json:"password"`
}

// TLSConfig TLS 配置
type TLSConfig struct {
	// Enabled 是否启用 TLS
	Enabled bool `yaml:"enabled" json:"enabled"`

	// InsecureSkipVerify 是否跳过证书验证
	InsecureSkipVerify bool `yaml:"insecure_skip_verify" json:"insecure_skip_verify"`

	// CertFile 证书文件路径
	CertFile string `yaml:"cert_file" json:"cert_file"`

	// KeyFile 私钥文件路径
	KeyFile string `yaml:"key_file" json:"key_file"`

	// CAFile CA 证书文件路径
	CAFile string `yaml:"ca_file" json:"ca_file"`
}

// KafkaWriterConfig Kafka 写入器配置
type KafkaWriterConfig struct {
	// BatchSize 批量大小
	BatchSize int `yaml:"batch_size" json:"batch_size" validate:"min=1" default:"100"`

	// BatchTimeout 批量超时时间
	BatchTimeout time.Duration `yaml:"batch_timeout" json:"batch_timeout" default:"1s"`

	// Async 是否异步写入
	Async bool `yaml:"async" json:"async" default:"true"`

	// Compression 压缩算法 (none, gzip, snappy, lz4, zstd)
	Compression string `yaml:"compression" json:"compression" validate:"oneof=none gzip snappy lz4 zstd" default:"snappy"`

	// MaxAttempts 最大重试次数
	MaxAttempts int `yaml:"max_attempts" json:"max_attempts" validate:"min=1" default:"3"`

	// WriteTimeout 写入超时时间
	WriteTimeout time.Duration `yaml:"write_timeout" json:"write_timeout" default:"10s"`

	// ReadTimeout 读取超时时间
	ReadTimeout time.Duration `yaml:"read_timeout" json:"read_timeout" default:"10s"`
}

// KafkaReaderConfig Kafka 读取器配置
type KafkaReaderConfig struct {
	// MinBytes 每次读取的最小字节数
	MinBytes int `yaml:"min_bytes" json:"min_bytes" validate:"min=1" default:"1"`

	// MaxBytes 每次读取的最大字节数
	MaxBytes int `yaml:"max_bytes" json:"max_bytes" validate:"min=1" default:"1048576"` // 1MB

	// MaxWait 最大等待时间
	MaxWait time.Duration `yaml:"max_wait" json:"max_wait" default:"1s"`

	// ReadTimeout 读取超时时间
	ReadTimeout time.Duration `yaml:"read_timeout" json:"read_timeout" default:"10s"`

	// CommitInterval 提交间隔
	CommitInterval time.Duration `yaml:"commit_interval" json:"commit_interval" default:"1s"`

	// StartOffset 起始偏移量 (first, last)
	StartOffset string `yaml:"start_offset" json:"start_offset" validate:"oneof=first last" default:"last"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	// Enabled 是否启用重试
	Enabled bool `yaml:"enabled" json:"enabled" default:"true"`

	// MaxAttempts 最大重试次数
	MaxAttempts int `yaml:"max_attempts" json:"max_attempts" validate:"min=1" default:"3"`

	// InitialInterval 初始重试间隔
	InitialInterval time.Duration `yaml:"initial_interval" json:"initial_interval" default:"1s"`

	// MaxInterval 最大重试间隔
	MaxInterval time.Duration `yaml:"max_interval" json:"max_interval" default:"30s"`

	// BackoffMultiplier 退避倍数
	BackoffMultiplier float64 `yaml:"backoff_multiplier" json:"backoff_multiplier" validate:"min=1.0" default:"2.0"`

	// Jitter 抖动因子 (0.0-1.0)
	Jitter float64 `yaml:"jitter" json:"jitter" validate:"min=0,max=1" default:"0.1"`
}

// DLQConfig 死信队列配置
type DLQConfig struct {
	// Enabled 是否启用死信队列
	Enabled bool `yaml:"enabled" json:"enabled" default:"true"`

	// TopicSuffix 死信队列主题后缀
	TopicSuffix string `yaml:"topic_suffix" json:"topic_suffix" default:"dlq"`

	// MaxRetentionTime 最大保留时间
	MaxRetentionTime time.Duration `yaml:"max_retention_time" json:"max_retention_time" default:"168h"` // 7 days
}

// Validate 验证配置的有效性
func (c *KafkaProducerConfig) Validate() error {
	if len(c.Brokers) == 0 {
		return ErrInvalidBrokers
	}

	if c.WriterConfig.BatchSize <= 0 {
		c.WriterConfig.BatchSize = 100
	}

	if c.WriterConfig.BatchTimeout <= 0 {
		c.WriterConfig.BatchTimeout = time.Second
	}

	return nil
}

// Validate 验证配置的有效性
func (c *KafkaConsumerConfig) Validate() error {
	if len(c.Brokers) == 0 {
		return ErrInvalidBrokers
	}

	if c.GroupID == "" {
		return ErrInvalidGroupID
	}

	if len(c.Topics) == 0 {
		return ErrInvalidTopics
	}

	if c.ReaderConfig.MinBytes <= 0 {
		c.ReaderConfig.MinBytes = 1
	}

	if c.ReaderConfig.MaxBytes <= 0 {
		c.ReaderConfig.MaxBytes = 1048576 // 1MB
	}

	if c.RetryConfig.MaxAttempts <= 0 {
		c.RetryConfig.MaxAttempts = 3
	}

	return nil
}

// ToTLSConfig 转换为 *tls.Config
func (c *TLSConfig) ToTLSConfig() *tls.Config {
	if !c.Enabled {
		return nil
	}

	return &tls.Config{
		InsecureSkipVerify: c.InsecureSkipVerify,
	}
}

// GetDLQTopic 获取死信队列主题名称
func (c *DLQConfig) GetDLQTopic(originalTopic string) string {
	if !c.Enabled {
		return ""
	}
	return originalTopic + "." + c.TopicSuffix
}
