/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

package auth_test

import (
	"context"
	"testing"

	"cina.club/pkg/auth"
)

// ExampleNewAuthSuite demonstrates how to configure and initialize the auth suite
func ExampleNewAuthSuite() {
	// Configure authentication components
	cfg := auth.Config{
		UserCoreJWKSUrl: "https://user-core-service/jwks",
		RBACPolicy: map[string][]string{
			"admin":  {"user.create", "user.delete", "service.manage"},
			"user":   {"user.read", "profile.update"},
			"viewer": {"user.read"},
		},
		S2SPublicKeys: map[string]string{
			"user-service": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----",
		},
		AllowedIssuers:  []string{"user-core-service"},
		AllowedAudience: []string{"api-gateway", "current-service"},
		ClockSkew:       5, // Allow 5 seconds clock drift
	}

	// Define RPC method to permission mappings
	rpcPermissions := map[string]string{
		"/UserService/CreateUser":   "user.create",
		"/UserService/GetUser":      "user.read",
		"/UserService/UpdateUser":   "user.update",
		"/UserService/DeleteUser":   "user.delete",
		"/ServiceService/ManageAll": "service.manage",
	}

	// Initialize the auth suite
	authSuite, err := auth.NewAuthSuite(cfg, rpcPermissions)
	if err != nil {
		panic(err)
	}

	// authSuite is now ready to use with gRPC servers
	_ = authSuite
}

// TestContextUtilities demonstrates context injection and extraction
func TestContextUtilities(t *testing.T) {
	// Create a sample authenticated user
	user := &auth.AuthenticatedUser{
		ID:    "user123",
		Email: "<EMAIL>",
		Roles: []string{"admin", "user"},
	}

	// Inject user into context
	ctx := auth.NewContextWithUser(context.Background(), user)

	// Extract user from context
	extractedUser, err := auth.UserFromContext(ctx)
	if err != nil {
		t.Fatalf("Expected user in context, got error: %v", err)
	}

	// Verify user data
	if extractedUser.ID != user.ID {
		t.Errorf("Expected user ID %s, got %s", user.ID, extractedUser.ID)
	}

	if extractedUser.Email != user.Email {
		t.Errorf("Expected user email %s, got %s", user.Email, extractedUser.Email)
	}

	if len(extractedUser.Roles) != len(user.Roles) {
		t.Errorf("Expected %d roles, got %d", len(user.Roles), len(extractedUser.Roles))
	}
}

// TestServiceContext demonstrates service identity context handling
func TestServiceContext(t *testing.T) {
	// Create a sample service identity
	service := &auth.ServiceIdentity{
		Name: "user-service",
	}

	// Inject service into context
	ctx := auth.NewContextWithService(context.Background(), service)

	// Extract service from context
	extractedService, err := auth.ServiceFromContext(ctx)
	if err != nil {
		t.Fatalf("Expected service in context, got error: %v", err)
	}

	// Verify service data
	if extractedService.Name != service.Name {
		t.Errorf("Expected service name %s, got %s", service.Name, extractedService.Name)
	}
}

// TestContextHelpers demonstrates helper functions for context checking
func TestContextHelpers(t *testing.T) {
	ctx := context.Background()

	// Initially, context should not have user or service
	if auth.HasUser(ctx) {
		t.Error("Expected no user in empty context")
	}

	if auth.HasService(ctx) {
		t.Error("Expected no service in empty context")
	}

	// Add user to context
	user := &auth.AuthenticatedUser{ID: "test-user"}
	ctxWithUser := auth.NewContextWithUser(ctx, user)

	if !auth.HasUser(ctxWithUser) {
		t.Error("Expected user in context after injection")
	}

	// Add service to context
	service := &auth.ServiceIdentity{Name: "test-service"}
	ctxWithService := auth.NewContextWithService(ctxWithUser, service)

	if !auth.HasUser(ctxWithService) {
		t.Error("Expected user to persist in context")
	}

	if !auth.HasService(ctxWithService) {
		t.Error("Expected service in context after injection")
	}
}
