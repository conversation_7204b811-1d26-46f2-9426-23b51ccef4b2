/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package application

// App represents the main application structure holding all services
type App struct {
	QueryService *QueryService
	Dispatcher   *Dispatcher
}

// NewApp creates a new application instance
func NewApp(queryService *QueryService, dispatcher *Dispatcher) *App {
	return &App{
		QueryService: queryService,
		Dispatcher:   dispatcher,
	}
}
