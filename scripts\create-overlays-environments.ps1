# CINA.CLUB Platform - Create Overlays for All Microservices
# Copyright (c) 2025 Cina.Club

param(
    [string[]]$Environments = @("dev", "staging", "prod")
)

# Environment configurations
$envConfigs = @{
    "dev" = @{ Domain = "dev-api.cina.club"; Replicas = 2 }
    "staging" = @{ Domain = "staging-api.cina.club"; Replicas = 3 }
    "prod" = @{ Domain = "api.cina.club"; Replicas = 5 }
}

# Get all services with deploy/base directories
$services = Get-ChildItem -Path "services" -Directory | Where-Object { 
    Test-Path "$($_.FullName)/deploy/base" 
} | Select-Object -ExpandProperty Name

Write-Host "Creating overlays for $($services.Count) services across $($Environments.Count) environments..." -ForegroundColor Green

foreach ($service in $services) {
    foreach ($env in $Environments) {
        $overlayDir = "services/$service/deploy/overlays/$env"
        $config = $envConfigs[$env]
        
        # Create directory
        if (!(Test-Path $overlayDir)) {
            New-Item -ItemType Directory -Path $overlayDir -Force | Out-Null
        }
        
        # Create kustomization.yaml
        $kustomizationContent = @"
# CINA.CLUB Platform - $service $env Environment
# Copyright (c) 2025 Cina.Club

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

bases:
  - ../../base

patchesStrategicMerge:
  - patch-ingress-host.yaml

commonLabels:
  environment: $env

commonAnnotations:
  environment: "$env"
  domain: "$($config.Domain)"
"@
        Set-Content -Path "$overlayDir/kustomization.yaml" -Value $kustomizationContent -Encoding UTF8
        
        # Create ingress host patch
        $ingressName = $service.Replace('-service', '') + '-ingress'
        $ingressPatchContent = @"
# CINA.CLUB Platform - $service $env Ingress Host Patch
# Copyright (c) 2025 Cina.Club

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: $ingressName
spec:
  tls:
    - hosts:
        - "$($config.Domain)"
      secretName: "cina-club-$env-api-tls"
  rules:
    - host: "$($config.Domain)"
"@
        Set-Content -Path "$overlayDir/patch-ingress-host.yaml" -Value $ingressPatchContent -Encoding UTF8
        
        Write-Host "Created overlay: $overlayDir" -ForegroundColor Cyan
    }
}

Write-Host "Completed creating overlays for all microservices!" -ForegroundColor Green 