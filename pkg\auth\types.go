/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

package auth

// AuthenticatedUser represents a verified end-user from JWT validation
type AuthenticatedUser struct {
	ID    string   `json:"id"`
	Roles []string `json:"roles"`
	Email string   `json:"email,omitempty"`
	// Additional fields can be extracted from JWT claims as needed
}

// ServiceIdentity represents a verified service identity from S2S JWT validation
type ServiceIdentity struct {
	Name string `json:"name"` // e.g., "user-core-service"
} 