# CINA.CLUB Platform - System Validation Script (PowerShell)
# Copyright (c) 2025 Cina.Club
# All rights reserved.

param(
    [Parameter(HelpMessage="验证模式: dry-run 或 live")]
    [ValidateSet("dry-run", "live")]
    [string]$Mode = "dry-run",
    
    [Parameter(HelpMessage="显示详细输出")]
    [switch]$Verbose,
    
    [Parameter(HelpMessage="显示帮助信息")]
    [switch]$Help
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
CINA.CLUB 平台系统验证脚本 (PowerShell)

用法:
    .\Validate-All.ps1 [选项]

选项:
    -Mode <string>      验证模式 (dry-run|live) [默认: dry-run]
    -Verbose           显示详细输出
    -Help              显示此帮助信息

示例:
    .\Validate-All.ps1                    # 干运行验证
    .\Validate-All.ps1 -Mode live         # 实际集群验证
    .\Validate-All.ps1 -Verbose          # 详细输出模式
"@
    exit 0
}

# 全局变量
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SystemDir = Split-Path -Parent $ScriptDir
$LogDir = Join-Path $ScriptDir "logs"
$ReportFile = Join-Path $ScriptDir "validation-report.md"

# 创建日志目录
if (-not (Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# 颜色函数
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

function Log-Info { 
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [INFO] $Message"
    Write-ColorText $logMessage -Color Cyan
    Add-Content -Path (Join-Path $LogDir "validation.log") -Value $logMessage
}

function Log-Success { 
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [SUCCESS] $Message"
    Write-ColorText $logMessage -Color Green
    Add-Content -Path (Join-Path $LogDir "validation.log") -Value $logMessage
}

function Log-Warning { 
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [WARNING] $Message"
    Write-ColorText $logMessage -Color Yellow
    Add-Content -Path (Join-Path $LogDir "validation.log") -Value $logMessage
}

function Log-Error { 
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [ERROR] $Message"
    Write-ColorText $logMessage -Color Red
    Add-Content -Path (Join-Path $LogDir "validation.log") -Value $logMessage
}

# 检查YAML文件语法
function Test-YamlSyntax {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        return $false
    }
    
    try {
        # 尝试解析YAML文件
        $content = Get-Content $FilePath -Raw
        
        # 基本YAML语法检查
        if ([string]::IsNullOrWhiteSpace($content)) {
            return $false
        }
        
        # 检查基本结构
        $lines = $content -split "`n"
        $indentationValid = $true
        $previousIndent = -1
        
        foreach ($line in $lines) {
            $trimmedLine = $line.Trim()
            if ([string]::IsNullOrWhiteSpace($trimmedLine) -or $trimmedLine.StartsWith("#")) {
                continue
            }
            
            # 简单的缩进检查
            $currentIndent = $line.Length - $line.TrimStart().Length
            if ($currentIndent % 2 -ne 0 -and $trimmedLine -notmatch "^-\s") {
                $indentationValid = $false
                break
            }
        }
        
        return $indentationValid
    }
    catch {
        return $false
    }
}

# 验证配置文件
function Test-Configurations {
    Log-Info "验证配置文件..."
    
    # 定义要检查的配置文件
    $configFiles = @(
        "$SystemDir\kong\namespace.yaml",
        "$SystemDir\kong\control-plane\deployment.yaml",
        "$SystemDir\kong\data-plane\deployment.yaml",
        "$SystemDir\kong\data-plane\service.yaml",
        "$SystemDir\prometheus.yaml",
        "$SystemDir\fluentd-daemonset.yaml",
        "$SystemDir\cert-manager.yaml",
        "$SystemDir\observability\jaeger.yaml",
        "$SystemDir\automation\backup-restore.yaml",
        "$SystemDir\secrets\elasticsearch-credentials.yaml",
        "$SystemDir\secrets\cert-manager-secure.yaml"
    )
    
    $totalFiles = 0
    $validFiles = 0
    $invalidFiles = @()
    
    foreach ($file in $configFiles) {
        $totalFiles++
        if (Test-Path $file) {
            if (Test-YamlSyntax $file) {
                Log-Success "配置文件验证通过: $(Split-Path -Leaf $file)"
                $validFiles++
            } else {
                Log-Error "配置文件语法错误: $(Split-Path -Leaf $file)"
                $invalidFiles += $file
            }
        } else {
            Log-Warning "配置文件不存在: $(Split-Path -Leaf $file)"
            $invalidFiles += $file
        }
    }
    
    Log-Info "配置文件验证完成: $validFiles/$totalFiles 个文件通过"
    
    return @{
        Total = $totalFiles
        Valid = $validFiles
        Invalid = $invalidFiles
    }
}

# 验证Kustomize结构
function Test-KustomizeStructure {
    Log-Info "验证Kustomize配置结构..."
    
    $kustomizeDirs = @(
        "$SystemDir\base",
        "$SystemDir\overlays\production",
        "$SystemDir\overlays\development"
    )
    
    $totalDirs = 0
    $validDirs = 0
    
    foreach ($dir in $kustomizeDirs) {
        $totalDirs++
        if (Test-Path $dir) {
            $kustomizeFile = Join-Path $dir "kustomization.yaml"
            if (Test-Path $kustomizeFile) {
                if (Test-YamlSyntax $kustomizeFile) {
                    Log-Success "Kustomize配置验证通过: $(Split-Path -Leaf $dir)"
                    $validDirs++
                } else {
                    Log-Error "Kustomize配置语法错误: $(Split-Path -Leaf $dir)"
                }
            } else {
                Log-Warning "缺少kustomization.yaml: $(Split-Path -Leaf $dir)"
            }
        } else {
            Log-Warning "Kustomize目录不存在: $(Split-Path -Leaf $dir)"
        }
    }
    
    Log-Info "Kustomize结构验证完成: $validDirs/$totalDirs 个目录通过"
    
    return @{
        Total = $totalDirs
        Valid = $validDirs
    }
}

# 验证安全配置
function Test-SecurityConfigurations {
    Log-Info "验证安全配置..."
    
    $securityChecks = 0
    $securityIssues = 0
    
    # 检查Secret文件
    $secretFiles = Get-ChildItem -Path "$SystemDir\secrets" -Filter "*.yaml" -ErrorAction SilentlyContinue
    if ($secretFiles) {
        foreach ($secretFile in $secretFiles) {
            if (Test-YamlSyntax $secretFile.FullName) {
                $content = Get-Content $secretFile.FullName -Raw
                if ($content -match "kind:\s*Secret") {
                    Log-Success "Secret配置验证通过: $($secretFile.Name)"
                    $securityChecks++
                } else {
                    Log-Warning "文件不是有效的Secret配置: $($secretFile.Name)"
                    $securityIssues++
                }
            } else {
                Log-Error "Secret文件语法错误: $($secretFile.Name)"
                $securityIssues++
            }
        }
    } else {
        Log-Warning "未找到Secret配置文件"
        $securityIssues++
    }
    
    # 检查RBAC配置
    $deploymentFiles = Get-ChildItem -Path $SystemDir -Filter "*.yaml" -Recurse | Where-Object { 
        $_.Name -match "deployment|rbac|role" 
    }
    
    $rbacFound = $false
    foreach ($file in $deploymentFiles) {
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content -match "serviceAccountName:|ServiceAccount|Role|ClusterRole") {
            $rbacFound = $true
            break
        }
    }
    
    if ($rbacFound) {
        Log-Success "发现RBAC配置"
        $securityChecks++
    } else {
        Log-Warning "未发现RBAC配置"
        $securityIssues++
    }
    
    Log-Info "安全配置验证完成: $securityChecks 项通过, $securityIssues 项需要关注"
    
    return @{
        Checks = $securityChecks
        Issues = $securityIssues
    }
}

# 验证组件配置
function Test-ComponentConfigurations {
    Log-Info "验证各组件配置..."
    
    $components = @{
        "Kong Gateway" = @("$SystemDir\kong\namespace.yaml", "$SystemDir\kong\data-plane\deployment.yaml")
        "Prometheus" = @("$SystemDir\prometheus.yaml")
        "FluentD" = @("$SystemDir\fluentd-daemonset.yaml")
        "Cert-Manager" = @("$SystemDir\cert-manager.yaml")
        "Jaeger" = @("$SystemDir\observability\jaeger.yaml")
        "Velero" = @("$SystemDir\automation\backup-restore.yaml")
    }
    
    $componentResults = @{}
    
    foreach ($componentName in $components.Keys) {
        $componentFiles = $components[$componentName]
        $componentValid = $true
        
        foreach ($file in $componentFiles) {
            if (-not (Test-Path $file)) {
                Log-Warning "$componentName 配置文件不存在: $(Split-Path -Leaf $file)"
                $componentValid = $false
            } elseif (-not (Test-YamlSyntax $file)) {
                Log-Error "$componentName 配置文件语法错误: $(Split-Path -Leaf $file)"
                $componentValid = $false
            }
        }
        
        if ($componentValid) {
            Log-Success "$componentName 配置验证通过"
        } else {
            Log-Warning "$componentName 配置存在问题"
        }
        
        $componentResults[$componentName] = $componentValid
    }
    
    return $componentResults
}

# 生成验证报告
function New-ValidationReport {
    param(
        [hashtable]$ConfigResults,
        [hashtable]$KustomizeResults,
        [hashtable]$SecurityResults,
        [hashtable]$ComponentResults
    )
    
    Log-Info "生成验证报告..."
    
    # 生成报告内容 - 使用字符串拼接避免语法错误
    $reportHeader = @"
# CINA.CLUB 平台系统验证报告

**验证时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')  
**验证版本**: v1.0  
**验证模式**: $Mode  
**Copyright (c) 2025 Cina.Club**

## 验证总览

本次验证涵盖以下关键项目：
* 配置文件语法验证  
* Kustomize 结构验证
* 安全配置验证
* 组件配置验证

## 验证结果详情

### 1. 配置文件验证
- 总文件数: $($ConfigResults.Total)
- 验证通过: $($ConfigResults.Valid)
- 验证失败: $($ConfigResults.Total - $ConfigResults.Valid)

### 2. Kustomize 结构验证
- 总目录数: $($KustomizeResults.Total)
- 验证通过: $($KustomizeResults.Valid)
- 验证失败: $($KustomizeResults.Total - $KustomizeResults.Valid)

### 3. 安全配置验证
- 安全检查通过: $($SecurityResults.Checks)
- 需要关注项目: $($SecurityResults.Issues)

### 4. 组件配置验证
"@

    # 生成组件状态
    $componentStatus = ""
    foreach ($component in $ComponentResults.Keys) {
        $status = if ($ComponentResults[$component]) { "验证通过" } else { "需要检查" }
        $componentStatus += "- $component : $status`n"
    }
    
    # 生成可部署组件列表
    $deployableComponents = ""
    $issueComponents = ""
    foreach ($component in $ComponentResults.Keys) {
        if ($ComponentResults[$component]) {
            $deployableComponents += "- $component`n"
        } else {
            $issueComponents += "- $component (配置需要检查)`n"
        }
    }
    
    $reportFooter = @"

## 部署建议

### 立即可部署的组件
$deployableComponents
### 需要检查的组件
$issueComponents

## 后续行动项

### 短期 (本周)
- [ ] 修复配置文件语法错误
- [ ] 完善缺失的配置文件  
- [ ] 在Kubernetes集群中执行实际验证

### 中期 (本月)
- [ ] 配置存储后端用于Velero
- [ ] 设置监控告警接收器
- [ ] 建立运维操作手册

## 验证结论

**整体评估**: $(if (($ConfigResults.Valid + $KustomizeResults.Valid + $SecurityResults.Checks) -gt 10) { "验证通过" } else { "部分通过" })  
**配置质量**: $(if ($ConfigResults.Valid -eq $ConfigResults.Total) { "优秀" } else { "良好" })  
**安全配置**: $(if ($SecurityResults.Issues -eq 0) { "安全" } else { "需要关注" })

所有核心配置已完成基础验证。在Windows环境中进行了配置文件语法和结构检查。
建议在Kubernetes集群环境中进行完整的功能验证。

---
**报告生成时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')  
**验证脚本版本**: v1.0 (PowerShell)
"@

    $reportContent = $reportHeader + "`n" + $componentStatus + $reportFooter

    Set-Content -Path $ReportFile -Value $reportContent -Encoding UTF8
    Log-Success "验证报告已生成: $ReportFile"
}

# 主函数
function Main {
    Log-Info "开始 CINA.CLUB 平台系统验证..."
    Log-Info "验证模式: $Mode"
    Log-Info "运行环境: PowerShell (Windows)"
    
    # 清理旧日志
    $logFile = Join-Path $LogDir "validation.log"
    if (Test-Path $logFile) {
        Remove-Item $logFile -Force
    }
    
    try {
        # 执行各项验证
        $configResults = Test-Configurations
        $kustomizeResults = Test-KustomizeStructure
        $securityResults = Test-SecurityConfigurations
        $componentResults = Test-ComponentConfigurations
        
        # 生成验证报告
        New-ValidationReport -ConfigResults $configResults -KustomizeResults $kustomizeResults -SecurityResults $securityResults -ComponentResults $componentResults
        
        # 计算总体结果
        $totalChecks = $configResults.Valid + $kustomizeResults.Valid + $securityResults.Checks
        $totalItems = $configResults.Total + $kustomizeResults.Total + 2  # +2 for security base checks
        
        Log-Success "所有验证步骤完成！"
        Log-Info "总体通过率: $([math]::Round(($totalChecks / $totalItems) * 100, 1))%"
        Log-Info "查看详细报告: $ReportFile"
        Log-Info "查看详细日志: $logFile"
        
        # 根据结果设置退出代码
        if ($totalChecks -eq $totalItems) {
            Log-Success "验证完全通过！"
            exit 0
        } elseif ($totalChecks -gt ($totalItems * 0.8)) {
            Log-Warning "验证大部分通过，有少量问题需要关注"
            exit 1
        } else {
            Log-Error "验证发现多个问题，需要修复后重新验证"
            exit 2
        }
    }
    catch {
        Log-Error "验证过程中发生错误: $($_.Exception.Message)"
        exit 3
    }
}

# 执行主函数
Main 