好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/logger`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`pkg/logger`的职责、接口、集成策略和最佳实践，作为所有后端服务统一日志记录的权威设计蓝图。

---
### CINA.CLUB - 共享后端库 `pkg/logger` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师/SRE负责人]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计与集成策略](#3-核心设计与集成策略)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [使用示例与最佳实践](#6-使用示例与最佳实践)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在分布式微服务架构中，日志是进行问题排查、性能监控、安全审计和业务分析的生命线。如果每个服务都以不同的格式和方式记录日志，将会给日志的集中收集、解析和查询带来巨大挑战。`pkg/logger` 包的目的在于提供一个**统一、高性能、结构化的日志记录解决方案**。它封装了底层的日志库，为所有后端服务提供一个标准化的接口，确保所有日志都遵循一致的格式和上下文约定，从而极大地提升平台的可观测性。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   提供一个工厂函数，用于创建和配置一个标准化的日志记录器实例。
    *   强制输出**结构化日志 (JSON格式)**。
    *   支持动态配置日志级别（DEBUG, INFO, WARN, ERROR）。
    *   自动将请求上下文中的**核心追踪信息**（如`trace_id`, `user_id`）注入到日志字段中。
    *   提供与`pkg/errors`的集成，能自动记录错误的详细信息（包括堆栈跟踪）。
*   **范围之外 (Out-of-Scope)**:
    *   **日志收集、存储与查询**: 这是由日志基础设施（如Fluentd, Logstash, Loki, Elasticsearch）负责的。
    *   **日志轮转 (Log Rotation)**: 在容器化环境中，通常由容器运行时或日志收集代理负责。本包只负责将日志输出到`stdout`/`stderr`。
    *   任何业务逻辑。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。
*   **SRE/DevOps/安全团队**（间接），他们是日志的最终消费者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/logger` 是位于`pkg/`目录下的一个基础核心库。它被设计为一个轻量级的封装层，几乎被所有其他的`pkg/`包和所有的`services/`包依赖。它的核心职责是“**规范化**”，而不是“发明”一个新的日志库。

#### 2.2. 设计原则
*   **结构化优先 (Structured First)**: 所有日志**必须**是JSON格式，便于机器解析和查询。
*   **上下文感知 (Context-Aware)**: 日志记录必须与请求上下文(`context.Context`)紧密集成，自动携带追踪信息。
*   **高性能**: 日志记录操作不应成为服务的性能瓶颈。选择的底层库必须是高性能的，避免不必要的内存分配。
*   **配置驱动**: 日志级别、输出格式等应通过`pkg/config`进行配置。
*   **开发者友好**: 提供简洁的API，让开发者能轻松地记录带有丰富上下文的日志。

---

### 3. 核心设计与集成策略

#### 3.1. 标准日志字段
所有通过`pkg/logger`输出的日志，都必须包含一组标准的核心字段，以保证日志的可查询性和一致性。

```json
{
  "ts": "2025-06-26T10:00:00.123Z",
  "level": "info",
  "msg": "User logged in successfully",
  "service": {
    "name": "user-core-service",
    "version": "v1.2.3"
  },
  "trace": {
    "trace_id": "a1b2c3d4...",
    "span_id": "e5f6g7h8..."
  },
  "caller": "auth/service.go:123",
  "user_id": "usr_abc123",
  "custom_field": "some_value"
}
```
*   `ts`: 时间戳 (ISO 8601格式)。
*   `level`: 日志级别 (`debug`, `info`, `warn`, `error`)。
*   `msg`: 日志主消息。
*   `service`: 服务信息，在初始化时注入。
*   `trace`: 分布式追踪信息，从上下文中自动提取。
*   `caller`: (可选，用于DEBUG级别) 代码调用位置。
*   `user_id`: (如果存在于上下文中) 当前操作的用户ID。
*   `...`: 其他自定义的键值对。

#### 3.2. 上下文传播
`pkg/logger` 的核心是利用`context.Context`来传递和附加日志字段。
1.  在gRPC的顶层中间件（`pkg/middleware`）中，一个新的请求进入时，会从元数据中提取`trace_id`, `user_id`等信息。
2.  这些信息被附加到一个新的`slog.Logger`实例上，形成一个**带有上下文的Logger**。
3.  这个新的Logger实例被**注入到`context.Context`中**。
4.  在后续的业务逻辑中，任何地方需要记录日志时，都应从`context.Context`中获取这个Logger实例，从而自动包含所有上游附加的字段。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 日志记录器工厂
*   **FR4.1.1 (初始化函数)**: 必须提供一个主函数 `New(cfg LoggerConfig)`，用于创建一个全局的、基础的`slog.Logger`实例。
*   **FR4.1.2 (配置化)**: `LoggerConfig`结构体必须支持配置：
    *   `Level`: 日志级别 (`"debug"`, `"info"`, `"warn"`, `"error"`)。
    *   `Format`: 输出格式 (`"json"`, `"text"`)，生产环境强制为`json`。
    *   `AddSource`: 是否在日志中包含代码源文件和行号信息（生产环境建议关闭以提升性能）。
*   **FR4.1.3 (标准字段注入)**: `New`函数创建的Logger必须能自动添加`service.name`和`service.version`等静态字段。

#### 4.2. 上下文感知日志
*   **FR4.2.1 (上下文注入)**: 必须提供`NewContextWithLogger(ctx, logger)`和`ContextWithAttrs(ctx, attrs ...slog.Attr)`函数，用于将Logger或附加字段注入到新的上下文中。
*   **FR4.2.2 (上下文提取)**: 必须提供`FromContext(ctx)`函数。
    *   该函数从`context.Context`中提取Logger实例。
    *   如果上下文中不存在Logger，它必须返回一个默认的全局Logger实例，以确保日志记录总能成功，防止空指针。
*   **FR4.2.3 (快捷函数)**: 提供`Debug(ctx, msg, args...)`, `Info(ctx, msg, args...)`, `Warn(ctx, msg, args...)`, `Error(ctx, err, msg, args...)`等一系列快捷函数。这些函数内部会自动调用`FromContext(ctx)`。

#### 4.3. 错误日志集成
*   **FR4.3.1 (与`pkg/errors`集成)**: `Error`快捷函数在接收到一个`error`类型的参数时，必须能智能地处理它。
    *   如果`err`是一个`pkg/errors.AppError`，则自动将其`Code`, `Message`, `Cause`, `Metadata`等结构化信息作为独立的字段记录到日志中。
    *   如果`err`支持堆栈跟踪（如`github.com/pkg/errors`），则自动记录`stack_trace`字段。
    *   将原始错误消息记录在`error`字段中。

---

### 5. 接口定义 (API Specification)

```go
// pkg/logger/config.go
type LoggerConfig struct {
    Level     string `mapstructure:"level" validate:"required,oneof=debug info warn error"`
    Format    string `mapstructure:"format" validate:"required,oneof=json text"`
    AddSource bool   `mapstructure:"add_source"`
}

// pkg/logger/logger.go
import "log/slog"

// New 创建一个基础的、配置好的slog.Logger。
// serviceName和serviceVersion将在每条日志中作为固定字段。
func New(cfg LoggerConfig, serviceName, serviceVersion string) *slog.Logger

// --- Contextual Logger Functions ---

// FromContext 从上下文中获取Logger。如果不存在，则返回一个默认的全局Logger。
func FromContext(ctx context.Context) *slog.Logger

// ContextWithAttrs 返回一个新的上下文，其中包含了附加了指定属性的Logger。
func ContextWithAttrs(ctx context.Context, args ...interface{}) context.Context

// --- Shortcut Functions ---

func Debug(ctx context.Context, msg string, args ...interface{})
func Info(ctx context.Context, msg string, args ...interface{})
func Warn(ctx context.Context, msg string, args ...interface{})
// Error 函数特殊处理error类型
func Error(ctx context.Context, err error, msg string, args ...interface{})
```

---

### 6. 使用示例与最佳实践

#### 6.1. 在gRPC中间件中注入上下文
```go
// pkg/middleware/logging.go
func LoggingInterceptor(logger *slog.Logger) grpc.UnaryServerInterceptor {
    return func(ctx context.Context, req interface{}, ...) (interface{}, error) {
        // 从请求中提取trace_id, user_id等
        traceID := ...
        userID := ...

        // 创建带上下文的logger
        ctxLogger := logger.With("trace_id", traceID, "user_id", userID)
        // 将新的logger注入到新的上下文中
        newCtx := logger.NewContextWithLogger(ctx, ctxLogger)

        // ... 调用handler ...
        
        // 使用带上下文的logger记录请求结束日志
        logger.FromContext(newCtx).Info("request completed", "duration_ms", ...)

        return resp, err
    }
}
```

#### 6.2. 在业务代码中使用
```go
// services/some-service/internal/application/service.go
func (s *someService) DoSomething(ctx context.Context, userID string) error {
    // 直接使用快捷函数，它会自动从ctx中获取带trace_id和user_id的logger
    logger.Info(ctx, "starting to do something for user")
    
    _, err := s.repo.GetUser(ctx, userID)
    if err != nil {
        // Error函数会自动处理err的结构化信息和堆栈
        logger.Error(ctx, err, "failed to get user from repo")
        return app_errors.Wrap(err, app_errors.NotFound, "user not found")
    }

    logger.Info(ctx, "successfully did something", "result", "some_value")
    return nil
}
```

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   日志记录的性能开销必须极低。**零分配(Zero-allocation)**是禁用`DEBUG`级别和`AddSource`时的目标。
    *   应使用`log/slog`或`zerolog`等公认的高性能结构化日志库作为底层实现。
*   **NFR7.2 (可靠性)**: 日志库本身不能崩溃或panic，即使在并发写入或接收到异常数据时。
*   **NFR7.3 (可测试性)**: 提供一个`NewTestLogger()`函数，它将日志输出到一个`bytes.Buffer`中，方便在单元测试中对日志输出进行断言。

---

### 8. 技术约束与开发规范

*   **TC8.1 (核心依赖)**: 强制使用Go 1.21+ 的 **`log/slog`** 标准库作为核心API。这确保了长期的稳定性和社区兼容性。
*   **TC8.2 (开发规范)**:
    *   **强制使用**: 所有后端服务**必须**使用`pkg/logger`进行日志记录。
    *   **上下文传递**: **必须**将`context.Context`作为第一个参数传递给所有需要日志记录的函数。
    *   **使用快捷函数**: 鼓励使用`logger.Info(ctx, ...)`等快捷函数，而不是`logger.FromContext(ctx).Info(...)`，以保持代码简洁。
    *   **键值对**: 所有自定义日志字段必须成对出现（`key, value, key, value, ...`）。Key必须是字符串常量。
    *   **禁止拼接**: **严禁**使用字符串拼接（如`fmt.Sprintf`）来构造日志消息，所有变量都应作为独立的键值对字段添加。
        *   **错误示例**: `logger.Info(ctx, fmt.Sprintf("User %s logged in", userID))`
        *   **正确示例**: `logger.Info(ctx, "User logged in", "user_id", userID)`

---
这份SRS为`pkg/logger`库的设计和实现提供了坚实的基础。通过强制所有后端服务使用这个标准化的日志包，CINA.CLUB平台可以确保其日志系统的**一致性、可查询性和高性能**，为平台的稳定运维和快速故障排查提供了最关键的数据支持。