好的，遵照您的指示。我将为您生成一份专门针对 **`ranking-service` (排序模型服务)** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`ranking-service-srs.md`，并详细阐述其**作为高性能推理服务的技术实现、与专业推理服务器(如Triton)的集成、模型热更新机制，以及作为一个计算密集型服务的部署和运维策略**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `ranking-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `ranking-service-srs.md` (v1.0)
**核心架构**: 轻量级代理服务 + 专用推理服务器 (Proxy to Inference Server)

## 1. 概述

`ranking-service` 是推荐系统漏斗中**最核心的“决策大脑”**。它是一个**计算密集型、对延迟极度敏感**的在线服务。其架构设计的核心目标是：
1.  **极致的低延迟与高吞吐**: 必须能在几十毫秒内完成对数百个候选物料的批量打分。
2.  **与模型格式解耦**: 架构应能支持TensorFlow, PyTorch, ONNX等多种主流模型格式，而不需修改服务主逻辑。
3.  **可靠的模型管理**: 必须支持模型的动态加载和版本热切换，实现平滑的模型更新，无服务中断。
4.  **资源利用率最大化**: 必须能充分利用GPU等昂贵硬件的并行计算能力。
5.  **职责分离**: 将**业务逻辑/API服务**与**底层的模型计算**分离开来，使两者可以独立优化和演进。

为了实现这些目标，本架构**不采用**在应用服务中直接加载和运行模型（如用Python写一个Flask/FastAPI服务然后导入`tensorflow`库）的传统方式，而是采用**业界推荐的最佳实践：使用一个专用的、高性能的推理服务器**。

---

## 2. 架构图与组件职责

### 2.1 核心架构图 (代理 + 推理服务器模式)

```mermaid
graph TD
    subgraph "上游调用方"
        A[recommendation-service]
    end

    subgraph "Ranking Service Pod (Kubernetes)"
        style "Ranking Service Pod (Kubernetes)" fill:#e0f7fa
        
        subgraph "Container 1: Ranking API Proxy (Go)"
            style "Container 1: Ranking API Proxy (Go)" fill:#e3f2fd
            B[gRPC Server<br/><em>(adapter/transport)</em>]
            C[Application Service<br/><em>(application/service)</em>]
            D[Triton gRPC Client<br/><em>(adapter/client)</em>]
        end
        
        subgraph "Container 2: Triton Inference Server (C++)"
            style "Container 2: Triton Inference Server (C++)" fill:#e8f5e9
            E[Triton Server]
            F[Model Repository<br/><em>(mounted from S3/NFS)</em>]
            G[TensorRT Backend]
            H[ONNXRuntime Backend]
            I[PyTorch Backend]
        end
    end

    subgraph "外部依赖"
        S3[Model Storage (S3/MinIO)]
    end
    
    A -- "1. RankCandidates gRPC Request" --> B
    B -- "调用" --> C
    
    C -- "2. 特征预处理, 构建推理请求" --> C
    C -- "3. ✨ gRPC Call to localhost ✨" --> D
    D -- "gRPC (localhost:8001)" --> E
    
    E -- "4. 加载模型" --> F
    F -- "定期从S3同步" --> S3
    
    E -- "5. 根据模型类型, 选择后端执行" --> G & H & I
    
    E -- "6. 返回推理结果" --> D
    D -- "返回" --> C
    C -- "7. 结果后处理, 排序" --> C
    
    C -- "8. 返回gRPC响应" --> B
```

**设计决策**:
*   `ranking-service`在Kubernetes中将以一个**Pod**的形式部署。
*   这个Pod包含**两个紧密协作的容器**：
    1.  **`ranking-proxy` (Go)**: 我们自己开发的轻量级Go服务。它负责处理外部gRPC请求、进行特征预处理、调用Triton，并对结果进行后处理。
    2.  **`tritonserver` (C++)**: NVIDIA官方提供的、高性能的推理服务器容器。它负责所有与硬件和模型计算相关的繁重工作。
*   两者通过Pod内的`localhost`网络进行通信，延迟极低。

### 2.2 最终目录结构 (`services/ranking-service/`)

```
ranking-service/
├── cmd/proxy/
│   └── main.go                 # ✨ Go代理服务的唯一入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   └── triton_client.go # ✨ 调用Triton gRPC接口的客户端 ✨
│   │   └── grpc/
│   │       └── handler.go      # 实现对外的RankCandidates gRPC接口
│   ├── application/
│   │   ├── port/
│   │   │   └── ...
│   │   └── service/
│   │       └── ranking_proxy_service.go # 核心应用服务
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── feature_preprocessor.go # ✨ 特征预处理服务 ✨
├── deploy/                     # ✨ 部署相关配置 ✨
│   ├── triton_model_repository/ # Triton的模型仓库目录结构
│   │   ├── video_deepfm_v1/
│   │   │   ├── 1/              # 模型版本1
│   │   │   │   └── model.savedmodel/ or model.onnx
│   │   │   └── config.pbtxt    # ✨ Triton的模型配置文件 ✨
│   │   └── ...
│   └── helm/                   # (或Kustomize)
│       └── values.yaml         # 定义Pod部署的配置
├── go.mod
└── Dockerfile.proxy
```

---

## 3. 各层职责深度解析

### 3.1 Triton Inference Server (Container 2)

这是我们选择的**专用推理服务器**。
*   **职责**:
    *   **模型管理**: 监控`model_repository`目录的变化，自动加载新模型或新版本，并卸载旧版本，实现**无缝热更新**。
    *   **多后端支持**: 原生支持TensorFlow, PyTorch(via TorchScript/LibTorch), ONNX Runtime, TensorRT等多种后端，我们无需关心模型的原始格式。
    *   **动态批处理 (Dynamic Batching)**: **核心功能**。Triton能将在一个短时间窗口内收到的多个独立推理请求，自动合并成一个大的批次(batch)再送入GPU计算，极大地提升了GPU利用率和吞吐量。
    *   **协议支持**: 同时暴露gRPC和HTTP/REST接口。
*   **`deploy/triton_model_repository/`**:
    *   **`config.pbtxt`**: 这是Triton的模型配置文件。我们在这里定义模型的输入/输出张量名称、数据类型、维度，以及最重要的**动态批处理策略**。
        ```protobuf
        # deploy/triton_model_repository/video_deepfm_v1/config.pbtxt
        name: "video_deepfm_v1"
        platform: "tensorflow_savedmodel"
        max_batch_size: 256
        dynamic_batching {
          preferred_batch_size: [64, 128]
          max_queue_delay_microseconds: 1000 // 最大等待1ms来形成一个batch
        }
        // ... input/output definitions
        ```

### 3.2 Ranking API Proxy (Go - Container 1)

这是我们自己开发的、与业务紧相关的部分。

*   **`domain/service/feature_preprocessor.go`**:
    *   **`FeaturePreprocessor`**: 一个无状态的领域服务。
    *   **`Process(userFeatures, itemFeatures)`**: 负责将`recommendation-service`传来的、人类可读的特征（如`{"category": "music"}`），转换为模型需要的数值化、张量化的格式（如查询词典得到`category_id: 123`，然后进行one-hot编码或embedding lookup）。
*   **`application/service/ranking_proxy_service.go`**:
    *   **`RankingProxyService`**: 实现核心业务流程。
    *   **`Rank(ctx, request)`**:
        1.  接收到包含一个用户和数百个候选物料的请求。
        2.  **并行地**为所有物料调用`featurePreprocessor.Process`进行特征预处理。
        3.  将所有预处理好的特征，**组装成一个大的、符合`config.pbtxt`定义的gRPC请求**（即`TritonInferRequest`）。
        4.  调用`triton_client.Infer(ctx, request)`。
        5.  接收到Triton返回的包含所有物料分数的响应。
        6.  **结果后处理**: 将分数与物料ID进行匹配，并按主目标分数（如pCTR）进行降序排序。
        7.  返回排序后的列表。
*   **`adapter/client/triton_client.go`**:
    *   封装对运行在`localhost:8001`的Triton gRPC服务器的调用。
    *   使用由Triton官方提供的Protobuf文件生成的Go客户端。
*   **`adapter/grpc/handler.go`**:
    *   实现`ranking-service.proto`中定义的`RankCandidates`接口。

## 4. 部署与运维

*   **模型同步**: `triton_model_repository`目录应被挂载为一个**共享存储卷**（如NFS, S3FS, GCS FUSE）。离线的模型训练平台在完成训练后，将新模型按照Triton要求的目录结构上传到该存储中。Triton会自动发现并加载。
*   **Pod部署**:
    *   使用一个**Pod**部署两个容器是关键，保证了它们共享同一个网络命名空间，可以通过`localhost`高效通信。
    *   通常部署为一个Kubernetes `Deployment`。
*   **资源配置**: Pod的资源请求和限制**必须**包含GPU资源。例如：`nvidia.com/gpu: 1`。
*   **自动伸缩**: 配置**HPA (Horizontal Pod Autoscaler)**，根据**GPU利用率**或**QPS**来自动增减Pod副本数。

## 5. 总结

本架构设计通过采用**“轻量级Go代理 + 专用C++推理服务器”**的模式，构建了一个生产级的`ranking-service`：
1.  **职责分离，强强联合**:
    *   **Go代理**: 负责处理与业务相关的、逻辑复杂的**特征工程**和**API协议**。发挥了Go在并发、网络和业务逻辑开发上的优势。
    *   **Triton服务器**: 负责所有与硬件相关的、计算密集的**模型推理**。发挥了C++和专用服务器在极致性能、资源管理和模型兼容性上的优势。
2.  **高性能与高资源利用率**: 通过Triton的**动态批处理**等高级功能，能够将多个并发的请求合并，最大化地压榨GPU性能，在降低平均延迟的同时极大地提升了吞吐量。
3.  **模型管理的解耦与自动化**: 模型更新与服务部署完全解耦。算法工程师只需将新模型上传到模型仓库，Triton即可自动完成**热加载**，实现了真正的持续部署(CD)。
4.  **技术异构的灵活性**: Triton原生支持多种模型后端，使得算法团队可以自由选择使用TensorFlow, PyTorch或ONNX，而无需`ranking-service`的Go代码做任何改动。

这种架构是业界构建大规模、低延迟机器学习在线服务（如推荐排序、广告预估）的**事实标准和最佳实践**，能够为CINA.CLUB的个性化推荐系统提供一个强大、稳定且可演进的排序引擎。