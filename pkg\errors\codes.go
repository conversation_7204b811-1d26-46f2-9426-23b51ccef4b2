/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package errors

import (
	"google.golang.org/grpc/codes"
)

// ErrorCode 定义平台统一的错误码类型
type ErrorCode string

// 错误码常量定义
const (
	// OK 表示成功，无错误
	OK ErrorCode = "OK"

	// InvalidArgument 表示客户端指定了无效参数
	InvalidArgument ErrorCode = "InvalidArgument"

	// Unauthenticated 表示请求没有有效的认证凭据
	Unauthenticated ErrorCode = "Unauthenticated"

	// PermissionDenied 表示客户端权限不足，无法执行指定操作
	PermissionDenied ErrorCode = "PermissionDenied"

	// NotFound 表示请求的资源不存在
	NotFound ErrorCode = "NotFound"

	// AlreadyExists 表示尝试创建的资源已存在
	AlreadyExists ErrorCode = "AlreadyExists"

	// ResourceExhausted 表示资源配额用尽或达到速率限制
	ResourceExhausted ErrorCode = "ResourceExhausted"

	// Canceled 表示请求被客户端取消
	Canceled ErrorCode = "Canceled"

	// Internal 表示服务器内部错误
	Internal ErrorCode = "Internal"

	// Unavailable 表示服务暂时不可用，通常是可重试的
	Unavailable ErrorCode = "Unavailable"

	// DeadlineExceeded 表示操作超时
	DeadlineExceeded ErrorCode = "DeadlineExceeded"

	// FailedPrecondition 表示系统状态不满足操作前提条件
	FailedPrecondition ErrorCode = "FailedPrecondition"

	// Aborted 表示操作被中止，通常由于并发冲突
	Aborted ErrorCode = "Aborted"

	// OutOfRange 表示操作超出有效范围
	OutOfRange ErrorCode = "OutOfRange"

	// Unimplemented 表示操作未实现或不支持
	Unimplemented ErrorCode = "Unimplemented"

	// DataLoss 表示不可恢复的数据丢失或损坏
	DataLoss ErrorCode = "DataLoss"
)

// errorCodeToGRPCCodeMap 定义了从平台错误码到gRPC状态码的映射
var errorCodeToGRPCCodeMap = map[ErrorCode]codes.Code{
	OK:                 codes.OK,
	InvalidArgument:    codes.InvalidArgument,
	Unauthenticated:    codes.Unauthenticated,
	PermissionDenied:   codes.PermissionDenied,
	NotFound:           codes.NotFound,
	AlreadyExists:      codes.AlreadyExists,
	ResourceExhausted:  codes.ResourceExhausted,
	Canceled:           codes.Canceled,
	Internal:           codes.Internal,
	Unavailable:        codes.Unavailable,
	DeadlineExceeded:   codes.DeadlineExceeded,
	FailedPrecondition: codes.FailedPrecondition,
	Aborted:            codes.Aborted,
	OutOfRange:         codes.OutOfRange,
	Unimplemented:      codes.Unimplemented,
	DataLoss:           codes.DataLoss,
}

// grpcCodeToErrorCodeMap 定义了从gRPC状态码到平台错误码的反向映射
var grpcCodeToErrorCodeMap = map[codes.Code]ErrorCode{
	codes.OK:                 OK,
	codes.InvalidArgument:    InvalidArgument,
	codes.Unauthenticated:    Unauthenticated,
	codes.PermissionDenied:   PermissionDenied,
	codes.NotFound:           NotFound,
	codes.AlreadyExists:      AlreadyExists,
	codes.ResourceExhausted:  ResourceExhausted,
	codes.Canceled:           Canceled,
	codes.Internal:           Internal,
	codes.Unavailable:        Unavailable,
	codes.DeadlineExceeded:   DeadlineExceeded,
	codes.FailedPrecondition: FailedPrecondition,
	codes.Aborted:            Aborted,
	codes.OutOfRange:         OutOfRange,
	codes.Unimplemented:      Unimplemented,
	codes.DataLoss:           DataLoss,
}

// ToGRPCCode 将平台错误码转换为gRPC状态码
func (c ErrorCode) ToGRPCCode() codes.Code {
	if grpcCode, ok := errorCodeToGRPCCodeMap[c]; ok {
		return grpcCode
	}
	return codes.Internal
}

// FromGRPCCode 将gRPC状态码转换为平台错误码
func FromGRPCCode(code codes.Code) ErrorCode {
	if errorCode, ok := grpcCodeToErrorCodeMap[code]; ok {
		return errorCode
	}
	return Internal
}

// String 返回错误码的字符串表示
func (c ErrorCode) String() string {
	return string(c)
}

// IsValid 检查错误码是否有效
func (c ErrorCode) IsValid() bool {
	_, ok := errorCodeToGRPCCodeMap[c]
	return ok
}
