# 数据流程图

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

本文档详细描述CINA.CLUB平台中各种业务场景的数据流程，包括用户认证、消息传递、数据同步等关键流程。

## 用户认证流程

### 用户注册流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant User as 用户服务
    participant Core as Go核心库
    participant DB as 数据库
    participant Email as 邮件服务
    participant SMS as 短信服务

    Note over Client, SMS: 用户注册流程

    Client->>Gateway: 1. 提交注册信息
    Gateway->>Auth: 2. 验证请求格式
    Auth->>User: 3. 检查用户是否存在
    User->>DB: 4. 查询用户数据
    DB-->>User: 5. 返回查询结果
    
    alt 用户已存在
        User-->>Gateway: 6a. 返回用户已存在错误
        Gateway-->>Client: 7a. 注册失败响应
    else 用户不存在
        User->>Core: 6b. 生成加密密钥
        Core-->>User: 7b. 返回密钥对
        User->>DB: 8b. 创建用户记录
        DB-->>User: 9b. 确认创建成功
        
        par 发送验证码
            User->>Email: 10b. 发送邮箱验证码
            User->>SMS: 11b. 发送手机验证码
        end
        
        User-->>Gateway: 12b. 返回注册成功
        Gateway-->>Client: 13b. 注册成功响应
    end
```

### 用户登录流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant User as 用户服务
    participant Core as Go核心库
    participant DB as 数据库
    participant Cache as Redis缓存

    Note over Client, Cache: 用户登录流程

    Client->>Gateway: 1. 提交登录凭据
    Gateway->>Auth: 2. 转发登录请求
    Auth->>User: 3. 验证用户凭据
    User->>DB: 4. 查询用户信息
    DB-->>User: 5. 返回用户数据
    
    alt 凭据无效
        User-->>Gateway: 6a. 返回认证失败
        Gateway-->>Client: 7a. 登录失败响应
    else 凭据有效
        User->>Core: 6b. 生成JWT令牌
        Core-->>User: 7b. 返回加密令牌
        User->>Cache: 8b. 缓存会话信息
        Cache-->>User: 9b. 确认缓存成功
        User-->>Gateway: 10b. 返回令牌
        Gateway-->>Client: 11b. 登录成功响应
        
        Note over Client: 客户端保存令牌
        Client->>Client: 12b. 存储访问令牌
    end
```

### 多因素认证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant MFA as MFA服务
    participant User as 用户服务
    participant SMS as 短信服务
    participant TOTP as TOTP验证

    Note over Client, TOTP: 多因素认证流程

    Client->>Gateway: 1. 第一因子验证完成
    Gateway->>Auth: 2. 请求第二因子验证
    Auth->>MFA: 3. 启动MFA流程
    MFA->>User: 4. 获取用户MFA设置
    User-->>MFA: 5. 返回MFA配置
    
    alt SMS验证
        MFA->>SMS: 6a. 发送验证码
        SMS-->>MFA: 7a. 确认发送成功
        MFA-->>Gateway: 8a. 等待验证码输入
        Gateway-->>Client: 9a. 请求输入验证码
        Client->>Gateway: 10a. 提交验证码
        Gateway->>MFA: 11a. 验证验证码
    else TOTP验证
        MFA-->>Gateway: 6b. 请求TOTP代码
        Gateway-->>Client: 7b. 请求TOTP输入
        Client->>Gateway: 8b. 提交TOTP代码
        Gateway->>TOTP: 9b. 验证TOTP代码
        TOTP-->>MFA: 10b. 返回验证结果
    end
    
    MFA-->>Auth: 12. 返回MFA验证结果
    
    alt MFA成功
        Auth-->>Gateway: 13a. 认证完成
        Gateway-->>Client: 14a. 登录成功
    else MFA失败
        Auth-->>Gateway: 13b. 认证失败
        Gateway-->>Client: 14b. 登录失败
    end
```

## 消息传递流程

### 实时聊天消息流程

```mermaid
sequenceDiagram
    participant ClientA as 发送者客户端
    participant Gateway as API网关
    participant Chat as 聊天服务
    participant Core as Go核心库
    participant MQ as 消息队列
    participant DB as 数据库
    participant WS as WebSocket服务
    participant ClientB as 接收者客户端

    Note over ClientA, ClientB: 实时聊天消息流程

    ClientA->>Gateway: 1. 发送消息(加密)
    Gateway->>Chat: 2. 转发到聊天服务
    Chat->>Core: 3. 验证消息格式
    Core-->>Chat: 4. 返回验证结果
    
    par 存储消息
        Chat->>DB: 5a. 持久化消息
        DB-->>Chat: 6a. 确认存储成功
    and 分发消息
        Chat->>MQ: 5b. 发布到消息队列
        MQ->>WS: 6b. 通知WebSocket服务
        WS->>ClientB: 7b. 实时推送消息
    end
    
    Chat-->>Gateway: 8. 返回发送确认
    Gateway-->>ClientA: 9. 发送成功响应
    
    Note over ClientB: 接收者收到消息
    ClientB->>ClientB: 10. 解密并显示消息
```

### 群聊消息分发流程

```mermaid
sequenceDiagram
    participant Sender as 发送者
    participant Chat as 聊天服务
    participant Group as 群组服务
    participant MQ as 消息队列
    participant WS as WebSocket服务
    participant Member1 as 群成员1
    participant Member2 as 群成员2
    participant MemberN as 群成员N

    Note over Sender, MemberN: 群聊消息分发流程

    Sender->>Chat: 1. 发送群聊消息
    Chat->>Group: 2. 获取群成员列表
    Group-->>Chat: 3. 返回成员列表
    Chat->>MQ: 4. 发布群消息事件
    
    par 并行分发给所有成员
        MQ->>WS: 5a. 分发给成员1
        WS->>Member1: 6a. 推送消息
        
        MQ->>WS: 5b. 分发给成员2
        WS->>Member2: 6b. 推送消息
        
        MQ->>WS: 5c. 分发给成员N
        WS->>MemberN: 6c. 推送消息
    end
    
    Chat-->>Sender: 7. 返回发送确认
```

## 数据同步流程

### 端到端加密数据同步

```mermaid
sequenceDiagram
    participant ClientA as 客户端A
    participant Sync as 同步服务
    participant Core as Go核心库
    parameter Conflict as 冲突解决
    participant DB as 数据库
    participant ClientB as 客户端B

    Note over ClientA, ClientB: 端到端加密数据同步

    ClientA->>Sync: 1. 上传加密数据块
    Sync->>Core: 2. 验证数据完整性
    Core-->>Sync: 3. 返回验证结果
    Sync->>DB: 4. 存储加密数据
    DB-->>Sync: 5. 确认存储成功
    
    Note over Sync: 检测到数据变更
    
    Sync->>ClientB: 6. 通知数据更新
    ClientB->>Sync: 7. 请求最新数据
    Sync->>DB: 8. 查询加密数据
    DB-->>Sync: 9. 返回加密数据
    
    alt 无冲突
        Sync-->>ClientB: 10a. 返回数据块
        ClientB->>Core: 11a. 解密数据
        Core-->>ClientB: 12a. 返回明文数据
    else 有冲突
        Sync->>Conflict: 10b. 启动冲突解决
        Conflict->>Core: 11b. 执行CRDT合并
        Core-->>Conflict: 12b. 返回合并结果
        Conflict-->>Sync: 13b. 返回解决结果
        Sync-->>ClientB: 14b. 返回合并后数据
    end
```

### PKB数据同步流程

```mermaid
sequenceDiagram
    participant Mobile as 移动客户端
    participant Web as Web客户端
    participant PKB as PKB服务
    participant Vector as 向量数据库
    participant Search as 搜索引擎
    participant DB as 主数据库

    Note over Mobile, DB: PKB数据同步流程

    Mobile->>PKB: 1. 创建新笔记(加密)
    PKB->>DB: 2. 存储加密笔记
    DB-->>PKB: 3. 确认存储成功
    
    par 生成向量嵌入
        PKB->>Vector: 4a. 生成文本向量
        Vector-->>PKB: 5a. 返回向量数据
        PKB->>Search: 6a. 更新搜索索引
        Search-->>PKB: 7a. 确认索引更新
    and 同步到其他设备
        PKB->>Web: 4b. 推送数据变更
        Web->>PKB: 5b. 拉取最新数据
        PKB-->>Web: 6b. 返回同步数据
    end
    
    PKB-->>Mobile: 8. 同步完成确认
```

## AI交互流程

### AI助手对话流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant AI as AI服务
    participant Core as Go核心库
    participant Model as 本地模型
    participant Cloud as 云端API
    participant Context as 上下文管理
    participant PKB as 知识库

    Note over Client, PKB: AI助手对话流程

    Client->>AI: 1. 发送用户问题
    AI->>Context: 2. 获取对话上下文
    Context-->>AI: 3. 返回历史上下文
    AI->>PKB: 4. 查询相关知识
    PKB-->>AI: 5. 返回相关内容
    
    alt 本地处理
        AI->>Core: 6a. 调用本地模型
        Core->>Model: 7a. 执行推理
        Model-->>Core: 8a. 返回推理结果
        Core-->>AI: 9a. 返回处理结果
    else 云端处理
        AI->>Cloud: 6b. 调用云端API
        Cloud-->>AI: 7b. 返回API响应
    end
    
    AI->>Context: 10. 更新对话上下文
    Context-->>AI: 11. 确认更新成功
    AI-->>Client: 12. 返回AI回复
```

### 智能搜索流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Search as 搜索服务
    participant AI as AI服务
    participant Vector as 向量数据库
    participant ES as Elasticsearch
    participant Ranking as 排序算法

    Note over Client, Ranking: 智能搜索流程

    Client->>Search: 1. 提交搜索查询
    Search->>AI: 2. 理解查询意图
    AI-->>Search: 3. 返回语义理解
    
    par 多路召回
        Search->>Vector: 4a. 向量相似性搜索
        Vector-->>Search: 5a. 返回相似向量
        
        Search->>ES: 4b. 关键词搜索
        ES-->>Search: 5b. 返回匹配结果
        
        Search->>AI: 4c. 语义扩展搜索
        AI-->>Search: 5c. 返回扩展结果
    end
    
    Search->>Ranking: 6. 合并搜索结果
    Ranking->>AI: 7. 智能排序
    AI-->>Ranking: 8. 返回排序结果
    Ranking-->>Search: 9. 返回最终结果
    Search-->>Client: 10. 返回搜索结果
```

## 支付交易流程

### 在线支付流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Payment as 支付服务
    participant Order as 订单服务
    participant ThirdPay as 第三方支付
    participant DB as 数据库
    participant Notify as 通知服务

    Note over Client, Notify: 在线支付流程

    Client->>Gateway: 1. 发起支付请求
    Gateway->>Payment: 2. 创建支付订单
    Payment->>Order: 3. 验证订单信息
    Order-->>Payment: 4. 返回订单详情
    Payment->>DB: 5. 创建支付记录
    DB-->>Payment: 6. 确认记录创建
    
    Payment->>ThirdPay: 7. 调用第三方支付
    ThirdPay-->>Payment: 8. 返回支付URL
    Payment-->>Gateway: 9. 返回支付链接
    Gateway-->>Client: 10. 跳转到支付页面
    
    Note over Client, ThirdPay: 用户完成支付
    
    ThirdPay->>Payment: 11. 支付结果回调
    Payment->>DB: 12. 更新支付状态
    DB-->>Payment: 13. 确认状态更新
    Payment->>Order: 14. 更新订单状态
    Order-->>Payment: 15. 确认订单更新
    Payment->>Notify: 16. 发送支付通知
    Notify-->>Client: 17. 推送支付结果
```

## 系统监控流程

### 性能监控数据流

```mermaid
sequenceDiagram
    participant App as 应用服务
    participant Metrics as 指标收集
    participant Prometheus as Prometheus
    participant Grafana as Grafana
    participant Alert as 告警管理
    participant Admin as 运维人员

    Note over App, Admin: 性能监控数据流

    loop 定期收集指标
        App->>Metrics: 1. 发送性能指标
        Metrics->>Prometheus: 2. 存储时序数据
        Prometheus-->>Metrics: 3. 确认存储成功
    end
    
    Grafana->>Prometheus: 4. 查询监控数据
    Prometheus-->>Grafana: 5. 返回时序数据
    
    alt 正常情况
        Grafana-->>Admin: 6a. 显示正常仪表板
    else 异常检测
        Prometheus->>Alert: 6b. 触发告警规则
        Alert->>Admin: 7b. 发送告警通知
        Admin->>App: 8b. 执行应急响应
    end
```

---

**注：所有流程图都使用Mermaid格式，可以在支持的编辑器中直接渲染查看。** 