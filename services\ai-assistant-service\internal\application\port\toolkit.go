package port

import (
	"context"
	"encoding/json"
)

// Tool 定义工具接口
type Tool interface {
	// Name 返回工具的唯一标识符
	Name() string

	// Description 返回工具的描述，用于LLM理解工具功能
	Description() string

	// InputSchema 返回输入参数的JSON Schema
	InputSchema() *JSONSchema

	// OutputSchema 返回输出结果的JSON Schema
	OutputSchema() *JSONSchema

	// Execute 执行工具逻辑
	Execute(ctx context.Context, inputs map[string]interface{}) (*ToolResult, error)

	// Category 返回工具分类
	Category() ToolCategory

	// RequiresAuth 是否需要用户认证
	RequiresAuth() bool

	// IsAsync 是否为异步工具（可能需要轮询结果）
	IsAsync() bool
}

// Toolkit toolkit interface
type Toolkit interface {
	// RegisterTool registers a tool
	RegisterTool(tool Tool) error

	// GetTool gets tool by name
	GetTool(name string) (Tool, error)

	// GetAllTools gets all tools
	GetAllTools() map[string]Tool

	// GetToolsByCategory gets tools by category
	GetToolsByCategory(category ToolCategory) []Tool

	// GetToolSchemas gets all tool schemas (used for LLM planning)
	GetToolSchemas() []*ToolSchema

	// ValidateToolCall validates tool call parameters
	ValidateToolCall(toolName string, inputs map[string]interface{}) error

	// ExecuteTool executes a tool with given inputs
	ExecuteTool(ctx context.Context, toolName string, inputs map[string]interface{}) (*ToolResult, error)
}

// ToolCategory 工具分类
type ToolCategory string

const (
	ToolCategoryLLM       ToolCategory = "llm"       // LLM调用工具
	ToolCategoryMCP       ToolCategory = "mcp"       // 微服务连接点
	ToolCategorySearch    ToolCategory = "search"    // 搜索相关
	ToolCategorySchedule  ToolCategory = "schedule"  // 日程管理
	ToolCategoryKnowledge ToolCategory = "knowledge" // 知识管理
	ToolCategoryMemory    ToolCategory = "memory"    // 记忆管理
	ToolCategoryUser      ToolCategory = "user"      // 用户相关
	ToolCategoryContent   ToolCategory = "content"   // 内容处理
	ToolCategoryExternal  ToolCategory = "external"  // 外部API
	ToolCategoryUtility   ToolCategory = "utility"   // 工具类
)

// ToolResult 工具执行结果
type ToolResult struct {
	Success   bool                   `json:"success"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	NextSteps []string               `json:"next_steps,omitempty"` // 建议的下一步操作
}

// ToolSchema 工具Schema（用于LLM理解）
type ToolSchema struct {
	Name         string      `json:"name"`
	Description  string      `json:"description"`
	Category     string      `json:"category"`
	InputSchema  *JSONSchema `json:"input_schema"`
	Examples     []Example   `json:"examples,omitempty"`
	RequiresAuth bool        `json:"requires_auth"`
	IsAsync      bool        `json:"is_async"`
}

// JSONSchema JSON Schema定义
type JSONSchema struct {
	Type        string                 `json:"type"`
	Properties  map[string]*JSONSchema `json:"properties,omitempty"`
	Required    []string               `json:"required,omitempty"`
	Items       *JSONSchema            `json:"items,omitempty"`
	Description string                 `json:"description,omitempty"`
	Default     interface{}            `json:"default,omitempty"`
	Enum        []interface{}          `json:"enum,omitempty"`
	Format      string                 `json:"format,omitempty"`
	Minimum     *float64               `json:"minimum,omitempty"`
	Maximum     *float64               `json:"maximum,omitempty"`
	MinLength   *int                   `json:"minLength,omitempty"`
	MaxLength   *int                   `json:"maxLength,omitempty"`
}

// Example 工具使用示例
type Example struct {
	Description string                 `json:"description"`
	Input       map[string]interface{} `json:"input"`
	Output      map[string]interface{} `json:"output"`
}

// NewToolResult 创建成功的工具结果
func NewToolResult(data map[string]interface{}) *ToolResult {
	return &ToolResult{
		Success: true,
		Data:    data,
	}
}

// NewToolError 创建失败的工具结果
func NewToolError(err string) *ToolResult {
	return &ToolResult{
		Success: false,
		Error:   err,
	}
}

// AddMetadata 添加元数据
func (tr *ToolResult) AddMetadata(key string, value interface{}) {
	if tr.Metadata == nil {
		tr.Metadata = make(map[string]interface{})
	}
	tr.Metadata[key] = value
}

// AddNextStep 添加建议的下一步操作
func (tr *ToolResult) AddNextStep(step string) {
	tr.NextSteps = append(tr.NextSteps, step)
}

// ToJSON 序列化为JSON
func (tr *ToolResult) ToJSON() ([]byte, error) {
	return json.Marshal(tr)
}

// NewStringSchema 创建字符串类型的Schema
func NewStringSchema(description string, required bool) *JSONSchema {
	return &JSONSchema{
		Type:        "string",
		Description: description,
	}
}

// NewIntegerSchema 创建整数类型的Schema
func NewIntegerSchema(description string, min, max *float64) *JSONSchema {
	return &JSONSchema{
		Type:        "integer",
		Description: description,
		Minimum:     min,
		Maximum:     max,
	}
}

// NewBooleanSchema 创建布尔类型的Schema
func NewBooleanSchema(description string) *JSONSchema {
	return &JSONSchema{
		Type:        "boolean",
		Description: description,
	}
}

// NewArraySchema 创建数组类型的Schema
func NewArraySchema(description string, items *JSONSchema) *JSONSchema {
	return &JSONSchema{
		Type:        "array",
		Description: description,
		Items:       items,
	}
}

// NewObjectSchema 创建对象类型的Schema
func NewObjectSchema(description string, properties map[string]*JSONSchema, required []string) *JSONSchema {
	return &JSONSchema{
		Type:        "object",
		Description: description,
		Properties:  properties,
		Required:    required,
	}
}

// AddProperty 向对象Schema添加属性
func (js *JSONSchema) AddProperty(name string, property *JSONSchema) {
	if js.Properties == nil {
		js.Properties = make(map[string]*JSONSchema)
	}
	js.Properties[name] = property
}

// AddRequired 添加必需字段
func (js *JSONSchema) AddRequired(field string) {
	js.Required = append(js.Required, field)
}

// SetEnum 设置枚举值
func (js *JSONSchema) SetEnum(values ...interface{}) *JSONSchema {
	js.Enum = values
	return js
}

// SetFormat 设置格式
func (js *JSONSchema) SetFormat(format string) {
	js.Format = format
}

// SetDefault 设置默认值
func (js *JSONSchema) SetDefault(value interface{}) *JSONSchema {
	js.Default = value
	return js
}

// NewNumberSchema 创建数字类型的Schema
func NewNumberSchema(description string, min, max *float64) *JSONSchema {
	return &JSONSchema{
		Type:        "number",
		Description: description,
		Minimum:     min,
		Maximum:     max,
	}
}
