好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`infra/` 目录** 的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档不同于应用或库的SRS，它更像是一份**基础设施架构蓝图**和**运维规范手册**。其核心目标是定义一套**自动化的、可复制的、安全可靠的**基础设施管理体系，以支持整个CINA.CLUB平台的开发、测试和生产环境。

---
### CINA.CLUB - 基础设施即代码 (`infra/`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [平台工程/DevOps/SRE负责人]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计原则与策略](#3-核心设计原则与策略)
4.  [功能需求 (按子目录拆分)](#4-功能需求-按子目录拆分)
    *   [4.1 `infra/docker`: 容器化基础](#41-infradocker-容器化基础)
    *   [4.2 `infra/terraform`: 云资源编排](#42-infraterraform-云资源编排)
    *   [4.3 `infra/kubernetes`: 应用部署与编排](#43-infrakubernetes-应用部署与编排)
5.  [环境管理策略](#5-环境管理策略)
6.  [非功能性需求](#6-非功能性需求)
7.  [技术约束与开发规范](#7-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB平台由数十个微服务和多个前端应用构成，运行在复杂的云环境中。为了高效、可靠、安全地管理和部署这些组件，必须采用**基础设施即代码 (Infrastructure as Code, IaC)** 的方法。`infra/` 目录的目的就是作为平台所有基础设施配置的**唯一、权威的单一事实来源**。它通过使用Terraform、Kubernetes YAML、Dockerfiles等声明式配置，旨在实现平台环境的**自动化创建、版本化管理和一键式复制**。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   定义所有应用的**容器化方案 (Docker)**。
    *   定义所有底层**云资源 (VPC, 数据库, 缓存, K8s集群等)** 的编排代码。
    *   定义所有应用在**Kubernetes**上的部署、配置和网络策略。
    *   定义用于**本地开发**的环境编排。
*   **范围之外 (Out-of-Scope)**:
    *   **应用本身的源代码**: 由`/services`, `/apps`, `/core`等目录负责。
    *   **CI/CD流水线的具体实现**: 由`/.github/workflows/`负责，但CI/CD会**调用**本目录下的配置和脚本。
    *   **具体的云提供商控制台操作**: 所有变更都应通过修改本目录下的代码并执行自动化流程来完成。

#### 1.3. 目标用户
*   **DevOps/SRE团队**: 本目录的主要维护者和使用者。
*   **后端开发人员**: 使用本地开发环境，并理解其服务是如何被部署的。
*   **CI/CD自动化系统**: 本目录代码的主要执行者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`infra/` 是Monorepo的**物理世界蓝图**。它定义了代码最终运行的环境。它与应用代码（`/services`, `/apps`）是分离的，但又紧密相关，确保了应用与其运行环境的一致性。

#### 2.2. 设计原则
*   **声明式 (Declarative)**: 永远描述“最终状态是什么”，而不是“如何达到那个状态”。
*   **不可变基础设施 (Immutable Infrastructure)**: 不对现有服务器进行修改。任何变更都通过创建新的资源（如新的Docker镜像、新的VM实例）并替换旧的资源来完成。
*   **环境一致性 (Environment Parity)**: 尽最大努力保持开发、Staging和生产环境的配置相似性，以减少“在我机器上是好的”这类问题。
*   **最小权限原则 (Least Privilege)**: 所有基础设施组件（如IAM角色、网络安全组）的权限都应被限制在最小必要范围。
*   **成本意识 (Cost-Awareness)**: 基础设施的设计应考虑成本优化，如使用Spot实例、自动伸缩、资源清理等。

---

### 3. 核心设计与集成策略

本基础设施架构基于**云原生**技术栈，以**Kubernetes**为核心。

1.  **云资源层 (Terraform)**: Terraform负责创建和管理所有基础云资源，包括网络(VPC)、数据库(RDS)、缓存(ElastiCache)、消息队列(MSK/Kafka)以及最核心的Kubernetes集群(EKS/GKE)。
2.  **容器化层 (Docker)**: 所有微服务和应用都必须被容器化。Dockerfile定义了如何构建这些镜像。
3.  **应用部署层 (Kubernetes + Kustomize/Helm)**: Kustomize或Helm负责将容器化的应用部署到Terraform创建的Kubernetes集群中，管理其副本数、配置、服务发现和网络策略。

**工作流**:
`Terraform 创建舞台 -> Docker 准备演员 -> Kubernetes 编排演出`

---

### 4. 功能需求 (按子目录拆分)

#### 4.1. `infra/docker`: 容器化基础

*   **职责**: 定义所有服务的标准化容器构建方式和本地开发环境。
*   **`infra/docker/base/`**:
    *   **FR4.1.1 (`Dockerfile.go`)**: 必须定义一个多阶段构建的、优化的Go服务基础镜像。
        *   **第一阶段 (builder)**: 使用标准的`golang`镜像，下载依赖、编译应用。
        *   **第二阶段 (final)**: 使用极简的`distroless`或`alpine`镜像，只拷贝编译好的二进制文件和必要的CA证书。这能极大地减小最终镜像体积和安全攻击面。
    *   **FR4.1.2 (`Dockerfile.python`)**: 为`model-management-service`的Python Worker定义类似的多阶段构建镜像。
*   **`infra/docker/dev/`**:
    *   **FR4.1.3 (`docker-compose.yml`)**: **必须**提供一个完整的、一键启动的本地开发环境。
        *   包含所有后端微服务。
        *   包含所有基础设施依赖：PostgreSQL, Redis, Kafka, Elasticsearch, MinIO (S3模拟)等。
        *   使用Docker的`host.docker.internal`来允许容器访问宿主机的服务。
        *   配置健康检查(healthcheck)，确保依赖服务启动后，业务服务才启动。
        *   配置热重载(hot-reload)，使用`air`或`realize`等工具，在Go代码变更时自动重新编译和重启服务。

#### 4.2. `infra/terraform`: 云资源编排

*   **职责**: 使用Terraform声明式地管理所有底层云资源。
*   **`infra/terraform/modules/`**:
    *   **FR4.2.1 (模块化)**: 必须将可复用的资源组合封装成模块，如`vpc`, `rds_postgres`, `eks_cluster`, `kafka_msk`。
    *   **FR4.2.2 (参数化)**: 模块必须是高度参数化的，通过输入变量来适应不同环境的需求（如实例大小、副本数）。
*   **`infra/terraform/environments/`**:
    *   **FR4.2.3 (环境隔离)**: 必须为每个环境（`dev`, `staging`, `prod`）创建一个独立的工作区(workspace)或目录。
    *   **FR4.2.4 (状态管理)**: Terraform的状态文件(`terraform.tfstate`)**必须**存储在远程的、支持锁定的后端（如S3 + DynamoDB），严禁提交到Git。
    *   **FR4.2.5 (变量管理)**: 生产环境的敏感变量（如数据库密码）必须通过安全的变量存储（如Vault, AWS Secrets Manager）注入，而不是硬编码在`.tfvars`文件中。

#### 4.3. `infra/kubernetes`: 应用部署与编排

*   **职责**: 定义所有服务在Kubernetes集群中的运行方式。
*   **技术选型**: **Kustomize** (推荐，因其无模板、基于YAML patch的特性更符合声明式理念)。
*   **`infra/kubernetes/base/`**:
    *   **FR4.3.1 (通用模板)**: 为每个微服务创建一个目录，包含其最基础的、与环境无关的Kubernetes资源清单：
        *   `deployment.yaml`: 定义部署，使用占位符镜像和副本数。
        *   `service.yaml`: 定义ClusterIP服务，用于服务发现。
        *   `hpa.yaml`: (可选) 定义HorizontalPodAutoscaler。
        *   `kustomization.yaml`: 声明此目录下的所有资源。
*   **`infra/kubernetes/overlays/`**:
    *   **FR4.3.2 (环境差异化)**: 为每个环境（`dev`, `staging`, `prod`）创建一个目录。
    *   **FR4.3.3 (配置注入)**: 使用`configMapGenerator`和`secretGenerator`来管理和注入特定环境的配置。
    *   **FR4.3.4 (Patches)**: 使用`patchesStrategicMerge`来覆盖`base/`中的配置，如修改副本数、资源限制(requests/limits)、镜像标签等。
    *   **FR4.3.5 (部署流程)**: CI/CD部署一个环境时，会执行类似`kustomize build infra/kubernetes/overlays/staging | kubectl apply -f -`的命令。
*   **`infra/kubernetes/system/`**: (新增)
    *   **FR4.3.6 (集群级组件)**: 存放集群范围内的系统组件的清单，如`ingress-nginx`控制器、`cert-manager`（用于TLS证书）、`prometheus-operator`、`fluentd-daemonset`等。

---

### 5. 环境管理策略

*   **`dev` (开发环境)**: 部署在云端，供开发人员进行集成测试。CI/CD在每次合并到`develop`分支时自动部署。
*   **`staging` (预发布环境)**: 一个与生产环境1:1的镜像环境。用于QA测试、性能测试和UAT。部署通常在发布前手动触发。
*   **`prod` (生产环境)**: 面向最终用户的环境。部署必须经过严格的审批流程，并采用蓝绿部署或金丝雀发布策略。

---

### 6. 非功能性需求

*   **NFR6.1 (安全性)**:
    *   **网络策略**: 默认情况下，Kubernetes Pod之间应禁止所有网络通信，只通过`NetworkPolicy`资源明确地开放必要的端口和服务间调用。
    *   **IAM集成**: Kubernetes Service Account必须与云提供商的IAM角色集成（如EKS的IRSA），实现对云资源的精细化权限控制。
    *   **镜像扫描**: CI/CD流水线必须包含对所有构建的Docker镜像进行漏洞扫描的步骤。
*   **NFR6.2 (成本效益)**:
    *   在非生产环境中使用可抢占/Spot实例来运行无状态应用。
    *   为所有部署配置合理的资源请求和限制，以提高集群资源利用率。
    *   配置HPA（Horizontal Pod Autoscaler）和CA（Cluster Autoscaler）实现自动伸缩。
*   **NFR6.3 (可恢复性)**: 所有有状态服务（数据库、缓存）的备份和恢复流程必须被自动化并定期演练。整个基础设施（通过Terraform）应能在数小时内在新区域被重建。

---

### 7. 技术约束与开发规范

*   **TC7.1 (核心工具)**:
    *   **容器化**: Docker
    *   **资源编排**: Terraform >= 1.5
    *   **应用部署**: Kubernetes >= 1.25, Kustomize >= 5.0
*   **TC7.2 (开发规范)**:
    *   **严禁手动变更**: **严禁**直接通过云控制台或`kubectl`手动修改由IaC管理的生产环境资源。所有变更都必须通过修改代码和执行PR流程来完成。
    *   **代码审查**: 对`infra/`目录的所有PR都必须经过DevOps/SRE团队的审查和批准。
    *   **Dry-Run**: 所有对基础设施的变更（`terraform plan`, `kustomize build`）都必须在CI中进行“试运行”，并将计划输出附加到PR中供审查。

---
这份SRS为`infra/`目录的设计和管理提供了坚实、全面的指导。通过严格遵循基础设施即代码(IaC)的最佳实践，CINA.CLUB平台可以构建一个**安全、可靠、高效且可预测**的运行环境，为上层应用的快速迭代和稳定运行提供强大的支持。