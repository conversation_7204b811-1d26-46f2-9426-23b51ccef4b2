/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useRef } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Tag, 
  Space, 
  Button, 
  Modal, 
  message, 
  Image, 
  Typography,
  Tooltip,
  Progress,
  Badge,
  Drawer,
  Form,
  Input,
  Select,
  Avatar,
  Divider
} from 'antd'
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-table'
import { 
  EyeOutlined, 
  CheckOutlined, 
  CloseOutlined, 
  FlagOutlined,
  UserOutlined,
  ClockCircleOutlined,
  ExclamationTriangleOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  PictureOutlined
} from '@ant-design/icons'

import { 
  Content, 
  ContentStatus, 
  ContentType, 
  ModerationQueueItem, 
  ModerationAction,
  FlagReason 
} from '@/types/content'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { Text, Paragraph, Title } = Typography
const { TextArea } = Input
const { Option } = Select

// Mock moderation queue data
const mockQueueItems: ModerationQueueItem[] = [
  {
    content: {
      id: '1',
      title: '分享一些生活小技巧',
      body: '今天想和大家分享一些日常生活中非常实用的小技巧，希望能够帮助到大家提高生活质量...',
      type: ContentType.POST,
      category: 'LIFESTYLE' as any,
      status: ContentStatus.PENDING_REVIEW,
      authorId: 'user123',
      authorName: '小明',
      authorAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=xiaoming',
      createdAt: '2025-01-23T09:00:00Z',
      updatedAt: '2025-01-23T09:00:00Z',
      tags: ['生活', '技巧', '分享'],
      attachments: [],
      metrics: {
        views: 0,
        likes: 0,
        dislikes: 0,
        shares: 0,
        comments: 0,
        reports: 1,
        engagementRate: 0,
        viralityScore: 0,
      },
      flags: [{
        id: 'flag1',
        contentId: '1',
        reason: FlagReason.SPAM,
        description: '疑似垃圾内容',
        reportedBy: 'user456',
        reportedAt: '2025-01-23T09:30:00Z',
        severity: 'MEDIUM',
        status: 'PENDING',
      }],
      aiModerationScore: 65,
      aiModerationFlags: ['potential_spam'],
    },
    priority: 'NORMAL',
    assignedTo: 'moderator1',
    flagCount: 1,
    aiRiskScore: 65,
    estimatedReviewTime: 5,
    similarContent: [],
  },
  {
    content: {
      id: '2',
      title: '最新的科技产品测评',
      body: '这款产品真的太棒了！强烈推荐大家购买，性价比超高，功能非常强大...',
      type: ContentType.POST,
      category: 'TECHNOLOGY' as any,
      status: ContentStatus.PENDING_REVIEW,
      authorId: 'user789',
      authorName: '科技达人',
      authorAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=tech',
      createdAt: '2025-01-23T08:30:00Z',
      updatedAt: '2025-01-23T08:30:00Z',
      tags: ['科技', '测评', '推荐'],
      attachments: [{
        id: 'img1',
        name: '产品图片.jpg',
        type: 'IMAGE',
        url: 'https://picsum.photos/400/300',
        size: 1024000,
        mimeType: 'image/jpeg',
      }],
      metrics: {
        views: 10,
        likes: 2,
        dislikes: 0,
        shares: 1,
        comments: 3,
        reports: 2,
        engagementRate: 0.6,
        viralityScore: 2.5,
      },
      flags: [{
        id: 'flag2',
        contentId: '2',
        reason: FlagReason.INAPPROPRIATE,
        description: '可能包含不当推广',
        reportedBy: 'user999',
        reportedAt: '2025-01-23T08:45:00Z',
        severity: 'HIGH',
        status: 'PENDING',
      }],
      aiModerationScore: 85,
      aiModerationFlags: ['promotional_content', 'potential_bias'],
    },
    priority: 'HIGH',
    flagCount: 2,
    aiRiskScore: 85,
    estimatedReviewTime: 10,
    similarContent: ['content_456', 'content_789'],
  },
  {
    content: {
      id: '3',
      title: '',
      body: '刚拍的美食照片，大家觉得怎么样？',
      type: ContentType.IMAGE,
      category: 'FOOD' as any,
      status: ContentStatus.PENDING_REVIEW,
      authorId: 'user456',
      authorName: '美食家',
      authorAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=food',
      createdAt: '2025-01-23T08:00:00Z',
      updatedAt: '2025-01-23T08:00:00Z',
      tags: ['美食', '摄影'],
      attachments: [{
        id: 'img2',
        name: '美食照片.jpg',
        type: 'IMAGE',
        url: 'https://picsum.photos/400/300?random=2',
        size: 2048000,
        mimeType: 'image/jpeg',
      }],
      metrics: {
        views: 5,
        likes: 1,
        dislikes: 0,
        shares: 0,
        comments: 1,
        reports: 0,
        engagementRate: 0.4,
        viralityScore: 1.2,
      },
      flags: [],
      aiModerationScore: 25,
      aiModerationFlags: [],
    },
    priority: 'LOW',
    flagCount: 0,
    aiRiskScore: 25,
    estimatedReviewTime: 2,
    similarContent: [],
  },
]

/**
 * 内容审核队列页面
 */
const ModerationQueue: React.FC = () => {
  const actionRef = useRef<ActionType>()
  const { hasPermission } = usePermission()
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [previewVisible, setPreviewVisible] = useState(false)
  const [selectedContent, setSelectedContent] = useState<Content | null>(null)
  const [actionModalVisible, setActionModalVisible] = useState(false)
  const [actionType, setActionType] = useState<ModerationAction | null>(null)
  const [form] = Form.useForm()

  // 权限检查
  const canModerate = hasPermission(Permission.CONTENT_MODERATE)

  // 获取审核队列数据
  const fetchModerationQueue = async (params: any) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        data: mockQueueItems,
        success: true,
        total: mockQueueItems.length,
      }
    } catch (error) {
      message.error('获取审核队列失败')
      return {
        data: [],
        success: false,
        total: 0,
      }
    }
  }

  // 获取风险等级颜色
  const getRiskColor = (score: number) => {
    if (score >= 80) return 'red'
    if (score >= 60) return 'orange'
    if (score >= 40) return 'yellow'
    return 'green'
  }

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'red'
      case 'HIGH': return 'orange'
      case 'NORMAL': return 'blue'
      case 'LOW': return 'green'
      default: return 'default'
    }
  }

  // 获取内容类型图标
  const getContentTypeIcon = (type: ContentType) => {
    switch (type) {
      case ContentType.POST: return <FileTextOutlined />
      case ContentType.IMAGE: return <PictureOutlined />
      case ContentType.VIDEO: return <PlayCircleOutlined />
      default: return <FileTextOutlined />
    }
  }

  // 预览内容
  const handlePreview = (content: Content) => {
    setSelectedContent(content)
    setPreviewVisible(true)
  }

  // 执行审核操作
  const handleModerationAction = (action: ModerationAction, content: Content) => {
    setActionType(action)
    setSelectedContent(content)
    setActionModalVisible(true)
  }

  // 提交审核决定
  const handleSubmitAction = async (values: any) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const actionTexts: Partial<Record<ModerationAction, string>> = {
        [ModerationAction.APPROVE]: '通过',
        [ModerationAction.REJECT]: '拒绝',
        [ModerationAction.FLAG]: '标记',
        [ModerationAction.HIDE]: '隐藏',
        [ModerationAction.DELETE]: '删除',
        [ModerationAction.QUARANTINE]: '隔离',
      }
      
      message.success(`内容${actionTexts[actionType!] || '操作'}成功`)
      setActionModalVisible(false)
      actionRef.current?.reload()
      form.resetFields()
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 批量审核
  const handleBulkAction = (action: ModerationAction) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要操作的内容')
      return
    }

    const actionTexts: Partial<Record<ModerationAction, string>> = {
      [ModerationAction.APPROVE]: '批量通过',
      [ModerationAction.REJECT]: '批量拒绝',
      [ModerationAction.FLAG]: '批量标记',
      [ModerationAction.HIDE]: '批量隐藏',
      [ModerationAction.DELETE]: '批量删除',
      [ModerationAction.QUARANTINE]: '批量隔离',
    }

    Modal.confirm({
      title: actionTexts[action] || '批量操作',
      content: `确定要对 ${selectedRowKeys.length} 个内容执行${action}操作吗？`,
      onOk: async () => {
        try {
          // Mock API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          message.success(`${actionTexts[action] || '批量操作'}成功`)
          setSelectedRowKeys([])
          actionRef.current?.reload()
        } catch (error) {
          message.error(`${actionTexts[action] || '批量操作'}失败`)
        }
      },
    })
  }

  // 表格列配置
  const columns: ProColumns<ModerationQueueItem>[] = [
    {
      title: '内容',
      key: 'content',
      width: 300,
      fixed: 'left',
      render: (_, record) => (
        <div>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            marginBottom: '8px' 
          }}>
            <Avatar
              src={record.content.authorAvatar}
              size={24}
              style={{ marginRight: '8px' }}
            />
            <Text strong>{record.content.authorName}</Text>
            <div style={{ marginLeft: '8px', fontSize: '16px', color: '#666' }}>
              {getContentTypeIcon(record.content.type)}
            </div>
          </div>
          <div style={{ marginBottom: '8px' }}>
            {record.content.title && (
              <Text strong>{record.content.title}</Text>
            )}
          </div>
          <Paragraph 
            ellipsis={{ rows: 2, expandable: false }}
            style={{ margin: 0, color: '#666' }}
          >
            {record.content.body}
          </Paragraph>
          <div style={{ marginTop: '8px' }}>
            <Space wrap>
              <Tag color="purple">{record.content.type}</Tag>
              {record.content.tags.slice(0, 2).map(tag => (
                <Tag key={tag} color="blue" style={{ fontSize: '11px' }}>
                  {tag}
                </Tag>
              ))}
              {record.content.tags.length > 2 && (
                <Tag color="default" style={{ fontSize: '11px' }}>
                  +{record.content.tags.length - 2}
                </Tag>
              )}
            </Space>
          </div>
        </div>
      ),
    },
    {
      title: 'AI 风险评分',
      key: 'aiRiskScore',
      width: 120,
      sorter: true,
      render: (_, record) => (
        <div style={{ textAlign: 'center' }}>
          <Progress
            type="circle"
            size={50}
            percent={record.aiRiskScore}
            strokeColor={getRiskColor(record.aiRiskScore)}
            format={(percent) => `${percent}`}
          />
          <div style={{ marginTop: '4px', fontSize: '12px', color: '#666' }}>
            风险评分
          </div>
        </div>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (_, record) => (
        <Tag color={getPriorityColor(record.priority)}>
          {record.priority}
        </Tag>
      ),
    },
    {
      title: '举报数',
      dataIndex: 'flagCount',
      key: 'flagCount',
      width: 80,
      sorter: true,
      render: (count: number) => (
        <Badge 
          count={count} 
          style={{ backgroundColor: count > 1 ? '#ff4d4f' : count > 0 ? '#faad14' : '#d9d9d9' }}
        />
      ),
    },
    {
      title: '预估时间',
      key: 'estimatedTime',
      width: 100,
      render: (_, record) => (
        <Space>
          <ClockCircleOutlined />
          <Text>{record.estimatedReviewTime}分钟</Text>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: ['content', 'createdAt'],
      key: 'createdAt',
      width: 150,
      valueType: 'dateTime',
      sorter: true,
      render: (_, record) => (
        <Text style={{ fontSize: '12px' }}>
          {new Date(record.content.createdAt).toLocaleString()}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="预览">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record.content)}
            />
          </Tooltip>
          {canModerate && (
            <>
              <Tooltip title="通过">
                <Button
                  type="text"
                  size="small"
                  icon={<CheckOutlined />}
                  style={{ color: '#52c41a' }}
                  onClick={() => handleModerationAction(ModerationAction.APPROVE, record.content)}
                />
              </Tooltip>
              <Tooltip title="拒绝">
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  style={{ color: '#ff4d4f' }}
                  onClick={() => handleModerationAction(ModerationAction.REJECT, record.content)}
                />
              </Tooltip>
              <Tooltip title="标记">
                <Button
                  type="text"
                  size="small"
                  icon={<FlagOutlined />}
                  style={{ color: '#faad14' }}
                  onClick={() => handleModerationAction(ModerationAction.FLAG, record.content)}
                />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ]

  if (!canModerate) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限访问内容审核功能</p>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <ProTable<ModerationQueueItem>
        headerTitle="内容审核队列"
        actionRef={actionRef}
        rowKey={(record) => record.content.id}
        columns={columns}
        request={fetchModerationQueue}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        tableAlertRender={({ selectedRowKeys }) => (
          <div>
            已选择 <strong>{selectedRowKeys.length}</strong> 项
          </div>
        )}
        tableAlertOptionRender={() => (
          <Space>
            <Button 
              size="small" 
              type="primary"
              onClick={() => handleBulkAction(ModerationAction.APPROVE)}
            >
              批量通过
            </Button>
            <Button 
              size="small" 
              danger
              onClick={() => handleBulkAction(ModerationAction.REJECT)}
            >
              批量拒绝
            </Button>
            <Button 
              size="small"
              onClick={() => handleBulkAction(ModerationAction.FLAG)}
            >
              批量标记
            </Button>
          </Space>
        )}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 1000 }}
      />

      {/* 内容预览抽屉 */}
      <Drawer
        title="内容预览"
        width={600}
        open={previewVisible}
        onClose={() => setPreviewVisible(false)}
      >
        {selectedContent && (
          <div>
            <Card title="基本信息" style={{ marginBottom: '16px' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>作者：</Text>{selectedContent.authorName}
                </Col>
                <Col span={12}>
                  <Text strong>类型：</Text>{selectedContent.type}
                </Col>
                <Col span={12}>
                  <Text strong>创建时间：</Text>
                  {new Date(selectedContent.createdAt).toLocaleString()}
                </Col>
                <Col span={12}>
                  <Text strong>举报数：</Text>
                  {selectedContent.flags.length}
                </Col>
              </Row>
            </Card>

            <Card title="内容详情" style={{ marginBottom: '16px' }}>
              {selectedContent.title && (
                <div style={{ marginBottom: '12px' }}>
                  <Text strong>标题：</Text>
                  <div>{selectedContent.title}</div>
                </div>
              )}
              <div style={{ marginBottom: '12px' }}>
                <Text strong>内容：</Text>
                <div style={{ marginTop: '8px' }}>{selectedContent.body}</div>
              </div>
              {selectedContent.attachments.length > 0 && (
                <div>
                  <Text strong>附件：</Text>
                  <div style={{ marginTop: '8px' }}>
                    {selectedContent.attachments.map(attachment => (
                      <div key={attachment.id} style={{ marginBottom: '8px' }}>
                        {attachment.type === 'IMAGE' ? (
                          <Image
                            src={attachment.url}
                            alt={attachment.name}
                            style={{ maxWidth: '200px', maxHeight: '150px' }}
                          />
                        ) : (
                          <Tag>{attachment.name}</Tag>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            <Card title="AI 风险评估">
              <div style={{ marginBottom: '12px' }}>
                <Text strong>风险评分：</Text>
                <Progress 
                  percent={selectedContent.aiModerationScore || 0} 
                  strokeColor={getRiskColor(selectedContent.aiModerationScore || 0)}
                  style={{ marginLeft: '12px' }}
                />
              </div>
              {selectedContent.aiModerationFlags && selectedContent.aiModerationFlags.length > 0 && (
                <div>
                  <Text strong>AI 标记：</Text>
                  <div style={{ marginTop: '8px' }}>
                    {selectedContent.aiModerationFlags.map(flag => (
                      <Tag key={flag} color="orange">
                        {flag}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Card>
          </div>
        )}
      </Drawer>

      {/* 审核操作弹窗 */}
      <Modal
        title={`${actionType === ModerationAction.APPROVE ? '通过' : 
               actionType === ModerationAction.REJECT ? '拒绝' : 
               actionType === ModerationAction.FLAG ? '标记' : '操作'}内容`}
        open={actionModalVisible}
        onCancel={() => setActionModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleSubmitAction} layout="vertical">
          <Form.Item
            label="操作原因"
            name="reason"
            rules={[{ required: true, message: '请输入操作原因' }]}
          >
            <TextArea rows={4} placeholder="请详细说明操作原因..." />
          </Form.Item>
          
          {actionType === ModerationAction.FLAG && (
            <Form.Item
              label="标记类型"
              name="flagType"
              rules={[{ required: true, message: '请选择标记类型' }]}
            >
              <Select placeholder="请选择标记类型">
                <Option value={FlagReason.SPAM}>垃圾内容</Option>
                <Option value={FlagReason.INAPPROPRIATE}>不当内容</Option>
                <Option value={FlagReason.HARASSMENT}>骚扰内容</Option>
                <Option value={FlagReason.HATE_SPEECH}>仇恨言论</Option>
                <Option value={FlagReason.VIOLENCE}>暴力内容</Option>
                <Option value={FlagReason.COPYRIGHT}>版权侵犯</Option>
                <Option value={FlagReason.MISINFORMATION}>虚假信息</Option>
                <Option value={FlagReason.OTHER}>其他</Option>
              </Select>
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  )
}

export default ModerationQueue 