/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00

Go核心库的C接口头文件
本文件定义了所有从Go DLL导出的C函数声明
供P/Invoke调用使用
*/

#ifndef CORE_GO_H
#define CORE_GO_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

// ====== 基础结构体定义 ======

// Go slice 结构体
typedef struct {
    void* data;
    int64_t len;
    int64_t cap;
} GoSlice;

// Go string 结构体
typedef struct {
    const char* p;
    int64_t n;
} GoString;

// 错误结构体
typedef struct {
    int32_t code;
    GoString message;
} GoError;

// 加密结果结构体
typedef struct {
    GoSlice data;
    GoError error;
} CryptoResult;

// AI推理结果结构体
typedef struct {
    GoString text;
    bool is_finished;
    GoError error;
} AIResult;

// 回调函数类型定义
typedef void (*TokenCallback)(GoString token);
typedef void (*ProgressCallback)(float progress, GoString status);

// ====== 加密相关函数 ======

// 从密码派生主密钥
extern CryptoResult DeriveKeyFromPassword(GoString password, GoSlice salt);

// 对称加密
extern CryptoResult EncryptSymmetric(GoSlice key, GoSlice plaintext);

// 对称解密
extern CryptoResult DecryptSymmetric(GoSlice key, GoSlice ciphertext);

// 生成随机密钥
extern CryptoResult GenerateRandomKey(int32_t key_size);

// ====== AI相关函数 ======

// 初始化AI引擎
extern GoError InitAIEngine(GoString model_path);

// 创建AI会话
extern void* CreateAISession();

// 销毁AI会话
extern void DestroyAISession(void* session);

// 同步预测
extern AIResult PredictSync(void* session, GoString prompt);

// 开始流式预测
extern GoError PredictStream(void* session, GoString prompt, TokenCallback callback);

// 停止当前预测
extern void StopPrediction(void* session);

// ====== 数据同步相关函数 ======

// 初始化数据同步引擎
extern GoError InitDataSync(GoString device_id);

// 同步数据到云端
extern GoError SyncToCloud(GoSlice data, GoString data_type, ProgressCallback callback);

// 从云端拉取数据
extern CryptoResult PullFromCloud(GoString data_type, ProgressCallback callback);

// ====== 内存管理函数 ======

// 释放Go分配的内存
extern void FreeGoMemory(void* ptr);

// 释放Go Slice
extern void FreeGoSlice(GoSlice slice);

// 释放Go String
extern void FreeGoString(GoString str);

// ====== 工具函数 ======

// 获取版本信息
extern GoString GetVersion();

// 检查库是否可用
extern bool IsLibraryReady();

// 设置日志级别
extern void SetLogLevel(int32_t level);

// ====== 常量定义 ======

// 日志级别
#define LOG_LEVEL_DEBUG    0
#define LOG_LEVEL_INFO     1
#define LOG_LEVEL_WARN     2
#define LOG_LEVEL_ERROR    3
#define LOG_LEVEL_FATAL    4

// 错误代码
#define ERROR_SUCCESS      0
#define ERROR_INVALID_ARGS 1001
#define ERROR_CRYPTO_FAIL  2001
#define ERROR_AI_FAIL      3001
#define ERROR_SYNC_FAIL    4001
#define ERROR_MEMORY_FAIL  5001

#ifdef __cplusplus
}
#endif

#endif // CORE_GO_H 