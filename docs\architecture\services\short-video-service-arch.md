好的，遵照您的指示。我将为您生成一份专门针对 **`short-video-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`short-video-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**分块上传与断点续传的实现、事件驱动的智能转码与内容处理工作流、基于CDN和签名URL的高性能安全分发，以及作为一个媒体处理中心的成本与性能优化**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `short-video-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `short-video-service-srs.md` (v2.0)
**核心架构**: 事件驱动的媒体处理管道 + 代理存储 + CDN分发

## 1. 概述

`short-video-service` 是CINA.CLUB平台的**富媒体内容核心引擎**。它负责处理从视频上传、智能处理到全球分发的整个端到端流程。其核心挑战在于：
1.  **大文件上传的可靠性**: 需要支持分块上传和断点续传，以应对不稳定的移动网络环境。
2.  **计算密集型工作流**: 视频转码、抽帧、ASR等都是计算密集型和长耗时的操作，必须通过异步工作流进行处理。
3.  **高性能、低成本分发**: 需要通过CDN为全球用户提供低延迟、无卡顿的播放体验，同时通过防盗链和自适应比特率(ABR)来保护内容和优化带宽成本。
4.  **智能内容理解**: 需要利用AI能力自动提取封面、字幕、标签等，以提升内容质量和可发现性。
5.  **与平台生态的协同**: 需要与`file-storage-service`(存储)、`content-moderation`(审核)、`search-indexer`(索引)等多个服务进行高效、可靠的交互。

本架构设计通过采用**事件驱动的、解耦的媒体处理管道**，将Go编写的**编排服务**与**专门的媒体处理Worker**分离，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (上传与异步处理管道)

```mermaid
graph TD
    subgraph "客户端"
        ClientApp
    end

    subgraph "ShortVideoService (Go - Orchestrator)"
        style ShortVideoService fill:#e0f7fa
        A[API Layer (gRPC/REST)<br/><em>adapter/transport</em>]
        B[Application Service<br/><em>application/service</em>]
        C[Repository<br/><em>adapter/repository</em>]
        D[Kafka Producer<br/><em>adapter/event</em>]
    end

    subgraph "MediaProcessingWorker (Go/Python)"
        style "MediaProcessingWorker (Go/Python)" fill:#e8f5e9
        E[Kafka Consumer]
        F[Processing Pipeline Executor]
        G[FFmpeg & AI Tools]
        H[Kafka Producer]
    end
    
    subgraph "下游依赖"
        style "下游依赖" fill:#f3e5f5
        S1[file-storage-service]
        S2[content-moderation-service]
        S3[ai-assistant-service (for ASR)]
        S4[CDN]
        Kafka[(Kafka)]
    end

    ClientApp -- "1. InitiateMultipartUpload" --> A
    A -- "调用" --> B
    B -- "2. Request Upload ID from" --> S1
    B -- "3. Create Video Metadata (UPLOADING)" --> C
    A -- "Returns uploadId, fileKey" --> ClientApp
    
    ClientApp -- "4. Upload Chunks (directly to S3 via presigned URLs from file-storage)" --> S1
    
    ClientApp -- "5. CompleteUpload" --> A
    A -- "调用" --> B
    B -- "6. Finalize with file-storage" --> S1
    B -- "7. Update Video Status (PROCESSING)" --> C
    B -- "8. ✨ Publish VideoProcessingJobRequest ✨" --> D
    D --> Kafka
    
    Kafka -- "9. Consume Job" --> E
    E -- "调用" --> F
    F -- "10. Download Original, Process with" --> S1 & G
    F -- "11. Upload Artifacts (HLS, covers) to" --> S1
    F -- "12. Submit for moderation" --> S2
    F -- "13. ✨ Publish VideoProcessingCompletedEvent ✨" --> H
    H --> Kafka

    Kafka -- "14. Consume Result" --> B
    B -- "15. Update Video Metadata & Artifacts" --> C
```

### 2.2 最终目录结构 (`services/short-video-service/`)

```
short-video-service/
├── cmd/server/
│   └── main.go                 # Go API服务启动入口
├── cmd/worker/
│   └── main.go                 # ✨ 媒体处理Worker的独立入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── file_storage_client.go
│   │   │   └── ai_assistant_client.go
│   │   ├── event/
│   │   │   ├── producer.go
│   │   │   └── result_consumer.go # 消费处理结果和审核结果
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── postgres_repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── video_service.go # 核心应用服务实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── cdn_url_signer.go # ✨ CDN签名URL生成服务 ✨
├── worker/                     # ✨ Worker的具体处理逻辑 ✨
│   ├── pipeline/
│   │   ├── executor.go
│   │   └── steps/              # 每个处理步骤的实现
│   │       ├── transcode_hls.go
│   │       ├── extract_cover.go
│   │       └── generate_subtitles.go
│   └── tools/
│       └── ffmpeg.go           # FFmpeg命令的Go语言封装
├── go.mod
└── Dockerfile.server
└── Dockerfile.worker           # 分别为Go服务和Worker创建Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Media Rules)

*   `domain/model/`: 使用`/core/models`中与视频相关的`struct`，如`Video`, `VideoArtifact`。
*   **`domain/service/cdn_url_signer.go`**:
    *   **`CDNURLSigner`**: 一个无状态的领域服务。
    *   **`Sign(baseURL, policy)`**: 根据所选CDN提供商（如CloudFront, Cloudflare）的规则，使用一个安全的、轮换的密钥，为一个URL生成一个带过期时间和权限策略的签名。

### 3.2 `application/` - 应用层 (The Orchestrator)

*   **`application/service/video_service.go`**: 实现核心业务流程。
    *   **`InitiateUpload(ctx, ...)`**:
        1.  调用`file-storage-service`的客户端，为其发起一个多部分上传，并获取`uploadId`。
        2.  在本地数据库创建一个`Video`元数据记录，状态为`UPLOADING`。
    *   **`CompleteUpload(ctx, fileKey, ...)`**:
        1.  调用`file-storage-service`客户端，完成多部分上传。
        2.  更新`Video`状态为`PROCESSING`。
        3.  **发布`VideoProcessingJobRequest`事件到Kafka**。事件中包含`fileKey`和原始文件的存储信息。
    *   **`HandleProcessingResult(ctx, resultEvent)` (由事件消费者调用)**:
        1.  根据`videoID`找到对应的`Video`记录。
        2.  如果处理成功，将所有产物（HLS清单、封面图、字幕文件等）的`storageKey`存入`video_artifacts`表。
        3.  更新`Video`状态为`PENDING_MODERATION`（或`PUBLISHED`，取决于策略）。
        4.  发布`VideoPublishedEvent`。
    *   **`GetPlayInfo(ctx, videoID)`**:
        1.  从数据库获取`Video`及其所有`VideoArtifacts`。
        2.  找到HLS主清单的URL。
        3.  **如果视频是私有的**: 调用`domain.CDNURLSigner.Sign()`为清单URL和所有媒体片段URL生成签名。
        4.  返回包含所有播放所需URL的`PlayInfo`对象。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/client/`**: 封装对`file-storage`, `ai-assistant`等服务的gRPC调用。
*   **`adapter/repository/`**: 使用**PostgreSQL**存储所有视频元数据。
*   **`adapter/event/`**:
    *   `producer.go`: 发布`VideoProcessingJobRequest`和`VideoPublishedEvent`。
    *   `result_consumer.go`: 消费来自`worker`的`VideoProcessingCompletedEvent`和来自`content-moderation-service`的`ModerationResultEvent`。

### 3.4 `worker/` 和 `cmd/worker/` - 媒体处理工作流

这是独立的、计算密集型的Worker服务。它可以与API服务分开部署和扩展。

*   **`worker/tools/ffmpeg.go`**:
    *   提供一个流畅的、类型安全的Go语言接口来构建和执行复杂的`ffmpeg`命令行。
    *   例如: `ffmpeg.New().Input("in.mp4").VideoCodec("libx264").Resolution("1280x720").Output("out.mp4").Run()`。
*   **`worker/pipeline/`**:
    *   **`executor.go`**: `PipelineExecutor`接收一个`VideoProcessingJobRequest`。
    *   **`run()`**:
        1.  从`file-storage-service`下载原始视频文件到临时工作目录。
        2.  **并行执行**多个处理步骤：
            *   `steps.transcode_hls.Run()`: 调用`ffmpeg`工具，生成多码率的HLS流。
            *   `steps.extract_cover.Run()`: 调用`ffmpeg`分析视频，找到最佳帧并截取为封面。
            *   `steps.generate_subtitles.Run()`: 调用`ffmpeg`提取音频，然后调用`ai-assistant`的ASR接口生成字幕。
        3.  将所有生成的产物上传回`file-storage-service`。
        4.  将视频关键帧和字幕文本，提交给`content-moderation-service`进行审核。
        5.  发布一个包含所有产物信息的`VideoProcessingCompletedEvent`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`short-video-service`：
1.  **编排器与Worker分离**:
    *   **Go API服务 (Orchestrator)**: 轻量级，负责处理元数据、协调上传、发布任务。
    *   **Go Worker (Processor)**: 重量级，负责所有计算密集型的媒体处理。
    *   两者通过**Kafka**彻底解耦，使得Worker集群可以根据任务队列的积压情况**独立、自动地伸缩**，这是应对视频处理流量波动的最佳实践。
2.  **代理存储**: 将所有文件的物理存储完全委托给`file-storage-service`，本服务只关心视频处理的逻辑和元数据管理，职责清晰。
3.  **管道化与并行处理**: 在Worker内部，将转码、封面提取、ASR等步骤设计为可并行执行的管道，最大限度地缩短了整体处理时间。
4.  **智能与AI增强**: 将AI能力（智能封面、ASR）作为处理管道的内置步骤，自动化地提升了内容的质量和可访问性。
5.  **安全分发**: 通过CDN和签名URL机制，实现了对视频内容的高性能、低成本且安全可控的分发。

这种架构确保了`short-video-service`能够以一种**高效、可靠、可扩展且智能**的方式，为CINA.CLUB平台提供强大的富媒体能力。