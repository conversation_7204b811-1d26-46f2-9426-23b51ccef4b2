# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB Prometheus 监控系统配置

apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    name: monitoring
    component: observability
    app.kubernetes.io/name: monitoring
    app.kubernetes.io/part-of: cina-club

---
# Prometheus Operator
apiVersion: helm.cattle.io/v1
kind: HelmChart
metadata:
  name: kube-prometheus-stack
  namespace: kube-system
spec:
  chart: kube-prometheus-stack
  repo: https://prometheus-community.github.io/helm-charts
  targetNamespace: monitoring
  version: "51.2.0"
  valuesContent: |-
    # 全局配置
    global:
      rbac:
        create: true
        pspEnabled: false
      imageRegistry: ""
    
    # Prometheus 配置
    prometheus:
      enabled: true
      
      prometheusSpec:
        # 存储配置
        retention: 30d
        retentionSize: 50GB
        
        storageSpec:
          volumeClaimTemplate:
            spec:
              storageClassName: gp3
              accessModes: ["ReadWriteOnce"]
              resources:
                requests:
                  storage: 100Gi
        
        # 资源配置
        resources:
          requests:
            memory: 2Gi
            cpu: 1000m
          limits:
            memory: 4Gi
            cpu: 2000m
        
        # 副本数
        replicas: 2
        
        # 分片配置
        shards: 1
        
        # 规则选择器
        ruleSelector:
          matchLabels:
            prometheus: kube-prometheus
            role: alert-rules
        
        # ServiceMonitor 选择器
        serviceMonitorSelector:
          matchLabels:
            team: platform
        
        # PodMonitor 选择器
        podMonitorSelector:
          matchLabels:
            team: platform
        
        # 外部标签
        externalLabels:
          cluster: cina-club-k8s
          region: us-west-2
        
        # 安全配置
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          fsGroup: 2000
        
        # 节点选择器
        nodeSelector:
          node-type: monitoring
        
        # 容忍度
        tolerations:
        - key: monitoring-workload
          operator: Equal
          value: "true"
          effect: NoSchedule
        
        # 亲和性
        affinity:
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: app.kubernetes.io/name
                    operator: In
                    values:
                    - prometheus
                topologyKey: kubernetes.io/hostname
        
        # 额外的抓取配置
        additionalScrapeConfigs:
        - job_name: 'kubernetes-pods'
          kubernetes_sd_configs:
          - role: pod
          relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
        
        # 远程写入配置（如果需要）
        # remoteWrite:
        # - url: "https://prometheus-remote-write.example.com/api/v1/write"
        #   basicAuth:
        #     username:
        #       name: prometheus-remote-write
        #       key: username
        #     password:
        #       name: prometheus-remote-write
        #       key: password
    
    # AlertManager 配置
    alertmanager:
      enabled: true
      
      alertmanagerSpec:
        # 副本数
        replicas: 3
        
        # 存储配置
        storage:
          volumeClaimTemplate:
            spec:
              storageClassName: gp3
              accessModes: ["ReadWriteOnce"]
              resources:
                requests:
                  storage: 10Gi
        
        # 资源配置
        resources:
          requests:
            memory: 256Mi
            cpu: 100m
          limits:
            memory: 512Mi
            cpu: 200m
        
        # 配置
        config:
          global:
            smtp_smarthost: 'smtp.example.com:587'
            smtp_from: '<EMAIL>'
          
          route:
            group_by: ['alertname', 'severity', 'service']
            group_wait: 30s
            group_interval: 5m
            repeat_interval: 12h
            receiver: 'default'
            routes:
            - match:
                severity: critical
              receiver: 'critical-alerts'
              group_wait: 10s
              repeat_interval: 1h
            - match:
                severity: warning
              receiver: 'warning-alerts'
          
          receivers:
          - name: 'default'
            slack_configs:
            - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
              channel: '#alerts'
              title: 'CINA.CLUB Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
          
          - name: 'critical-alerts'
            slack_configs:
            - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
              channel: '#critical-alerts'
              title: 'CRITICAL: CINA.CLUB Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
            email_configs:
            - to: '<EMAIL>'
              subject: 'CRITICAL: {{ .GroupLabels.alertname }}'
              body: |
                {{ range .Alerts }}
                Alert: {{ .Annotations.summary }}
                Description: {{ .Annotations.description }}
                {{ end }}
          
          - name: 'warning-alerts'
            slack_configs:
            - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
              channel: '#warnings'
              title: 'Warning: CINA.CLUB Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
    
    # Grafana 配置
    grafana:
      enabled: true
      
      # 管理员配置
      adminPassword: "admin123"  # 在生产环境中使用 Secret
      
      # 资源配置
      resources:
        requests:
          memory: 256Mi
          cpu: 100m
        limits:
          memory: 512Mi
          cpu: 200m
      
      # 持久化存储
      persistence:
        enabled: true
        storageClassName: gp3
        size: 10Gi
      
      # 配置
      grafana.ini:
        server:
          domain: grafana.cina.club
          root_url: https://grafana.cina.club
        security:
          admin_user: admin
        auth:
          disable_login_form: false
        auth.anonymous:
          enabled: false
        analytics:
          reporting_enabled: false
          check_for_updates: false
        log:
          mode: console
          level: info
      
      # 数据源配置
      datasources:
        datasources.yaml:
          apiVersion: 1
          datasources:
          - name: Prometheus
            type: prometheus
            url: http://kube-prometheus-stack-prometheus:9090
            access: proxy
            isDefault: true
      
      # 仪表板配置
      dashboardProviders:
        dashboardproviders.yaml:
          apiVersion: 1
          providers:
          - name: 'default'
            orgId: 1
            folder: ''
            type: file
            disableDeletion: false
            editable: true
            options:
              path: /var/lib/grafana/dashboards/default
      
      # 预装仪表板
      dashboards:
        default:
          # Kubernetes 集群监控
          kubernetes-cluster:
            gnetId: 7249
            revision: 1
            datasource: Prometheus
          
          # Node Exporter
          node-exporter:
            gnetId: 1860
            revision: 27
            datasource: Prometheus
          
          # Nginx Ingress Controller
          nginx-ingress:
            gnetId: 9614
            revision: 1
            datasource: Prometheus
    
    # Node Exporter 配置
    nodeExporter:
      enabled: true
      
    # Kube State Metrics 配置
    kubeStateMetrics:
      enabled: true
      
    # Prometheus Operator 配置
    prometheusOperator:
      enabled: true
      
      resources:
        requests:
          memory: 256Mi
          cpu: 100m
        limits:
          memory: 512Mi
          cpu: 200m

---
# 自定义 ServiceMonitor 示例
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cina-club-services
  namespace: monitoring
  labels:
    team: platform
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      component: backend
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s

---
# 自定义告警规则
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cina-club-rules
  namespace: monitoring
  labels:
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: cina-club.rules
    rules:
    # 高错误率告警
    - alert: HighErrorRate
      expr: |
        (
          rate(http_requests_total{status=~"5.."}[5m]) /
          rate(http_requests_total[5m])
        ) > 0.05
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High error rate detected"
        description: "Service {{ $labels.service }} has error rate above 5%"
    
    # 高延迟告警
    - alert: HighLatency
      expr: |
        histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1.0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High latency detected"
        description: "Service {{ $labels.service }} 95th percentile latency is above 1s"
    
    # Pod 重启告警
    - alert: PodRestartHigh
      expr: |
        rate(kube_pod_container_status_restarts_total[15m]) > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Pod restarting frequently"
        description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"
    
    # 内存使用率高告警
    - alert: HighMemoryUsage
      expr: |
        (
          container_memory_working_set_bytes{container!=""} /
          container_spec_memory_limit_bytes{container!=""} * 100
        ) > 90
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "High memory usage"
        description: "Container {{ $labels.container }} memory usage is above 90%" 