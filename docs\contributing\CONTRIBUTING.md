# 贡献指南

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

## 欢迎贡献

感谢您对CINA.CLUB项目的兴趣！我们欢迎各种形式的贡献，包括但不限于：

- 🐛 Bug报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献
- 🎨 UI/UX设计
- 🧪 测试和质量保证

## 开始之前

在开始贡献之前，请确保您已经：

1. 阅读了项目的[行为准则](CODE_OF_CONDUCT.md)
2. 了解了项目的[架构设计](../architecture/cina.club-monorepo-arch.md)
3. 设置了开发环境（参见[开发环境设置](../onboarding/development-environment-setup.md)）

## 贡献流程

### 1. 准备工作

```bash
# 1. Fork项目到您的GitHub账户

# 2. 克隆您的Fork
git clone https://github.com/your-username/cina-club-monorepo.git
cd cina-club-monorepo

# 3. 添加上游仓库
git remote add upstream https://github.com/cina-club/monorepo.git

# 4. 安装依赖
make setup-dev
```

### 2. 创建功能分支

```bash
# 从main分支创建新分支
git checkout main
git pull upstream main
git checkout -b feature/your-feature-name

# 或者对于bug修复
git checkout -b fix/bug-description
```

### 3. 开发和测试

```bash
# 进行开发工作
# ...

# 运行测试
make test

# 运行lint检查
make lint

# 构建项目
make build
```

### 4. 提交代码

```bash
# 添加更改
git add .

# 提交更改（遵循提交信息规范）
git commit -m "feat: add new feature description"
```

### 5. 推送和创建PR

```bash
# 推送分支
git push origin feature/your-feature-name

# 在GitHub上创建Pull Request
```

## 提交信息规范

我们使用[Conventional Commits](https://www.conventionalcommits.org/)规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 提交类型

- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整（不影响功能）
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建工具、辅助工具的变动

### 示例

```bash
feat(auth): add JWT token refresh mechanism
fix(api): resolve null pointer exception in user service
docs(readme): update installation instructions
refactor(core): simplify encryption key derivation
```

## 代码规范

### Go代码规范

- 使用`gofmt`格式化代码
- 遵循Go官方的代码风格指南
- 所有公共函数和结构体必须有注释
- 使用`golint`和`go vet`检查代码

```go
// UserService 用户服务接口
type UserService interface {
    // CreateUser 创建新用户
    CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
}
```

### TypeScript/ArkTS代码规范

- 使用ESLint和Prettier进行代码格式化
- 所有函数和类必须有JSDoc注释
- 遵循函数式编程原则

```typescript
/**
 * 用户管理器
 * 
 * @description 负责用户认证和状态管理
 */
export class UserManager {
    /**
     * 验证用户登录状态
     * 
     * @returns Promise<boolean> 是否已登录
     */
    async checkLoginStatus(): Promise<boolean> {
        // 实现逻辑
    }
}
```

### 数据库迁移规范

- 所有数据库变更必须通过迁移脚本
- 迁移文件命名：`YYYYMMDDHHMMSS_description.sql`
- 必须包含`UP`和`DOWN`迁移

## 测试要求

### 单元测试

- 所有新功能必须包含单元测试
- 测试覆盖率不低于80%
- 使用表驱动测试模式（Go）

```go
func TestUserService_CreateUser(t *testing.T) {
    tests := []struct {
        name    string
        input   *CreateUserRequest
        want    *User
        wantErr bool
    }{
        // 测试用例
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试逻辑
        })
    }
}
```

### 集成测试

- 关键API必须包含集成测试
- 使用Docker容器进行测试环境隔离

### 端到端测试

- 重要用户流程必须包含E2E测试
- 使用自动化测试工具

## Pull Request 要求

### PR标题格式

使用与提交信息相同的格式：

```
feat(scope): add new feature description
```

### PR描述模板

参见[PR模板](.github/pull_request_template.md)

### PR检查清单

- [ ] 代码已通过所有测试
- [ ] 代码已通过lint检查
- [ ] 更新了相关文档
- [ ] 添加了必要的测试
- [ ] 遵循了代码规范
- [ ] PR描述清晰完整

## 代码审查

### 审查标准

1. **功能性**: 代码是否正确实现了预期功能
2. **可读性**: 代码是否清晰易懂
3. **性能**: 是否存在性能问题
4. **安全性**: 是否存在安全漏洞
5. **可维护性**: 代码是否易于维护

### 审查流程

1. 自动化检查（CI/CD）
2. 技术负责人审查
3. 架构师审查（重大变更）
4. 最终批准和合并

## 发布流程

### 版本号规范

遵循[语义化版本](https://semver.org/)：

- `MAJOR.MINOR.PATCH`
- 主版本号：不兼容的API变更
- 次版本号：向后兼容的功能新增
- 补丁版本号：向后兼容的Bug修复

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 变更日志已更新
- [ ] 版本号已正确标记
- [ ] 发布说明已准备

## 问题报告

### Bug报告

使用[Issue模板](ISSUE_TEMPLATE.md)提交Bug报告，包含：

- 问题描述
- 复现步骤
- 预期行为
- 实际行为
- 环境信息
- 截图/日志

### 功能请求

- 清晰描述需要的功能
- 解释使用场景
- 考虑替代方案
- 评估实现复杂度

## 社区规范

### 沟通渠道

- **GitHub Issues**: Bug报告和功能请求
- **GitHub Discussions**: 技术讨论
- **Slack**: 实时沟通（内部团队）
- **邮件**: <EMAIL>

### 响应时间

- Bug报告：48小时内响应
- 功能请求：1周内响应
- Pull Request：72小时内审查

## 许可证

通过贡献代码，您同意您的贡献将在与项目相同的许可证下发布。

## 联系方式

如有疑问，请联系：

- **技术负责人**: <EMAIL>
- **项目维护者**: <EMAIL>
- **社区管理**: <EMAIL>

---

**感谢您的贡献！让我们一起构建更好的CINA.CLUB平台！** 🚀 