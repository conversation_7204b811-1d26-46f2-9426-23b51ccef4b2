{"openapi": "3.0.0", "info": {"title": "Cina.Club Admin BFF", "version": "1.0.0", "description": "API contract for the Backend-for-Frontend service for the Admin Dashboard."}, "paths": {"/api/v1/users": {"get": {"summary": "Get Users", "operationId": "getUsers", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "A list of users.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserListResponse"}}}}}}, "post": {"summary": "Create User", "operationId": "createUser", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "User created successfully.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/v1/users/{id}": {"get": {"summary": "Get User By ID", "operationId": "getUserById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single user.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "delete": {"summary": "Delete User", "operationId": "deleteUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "User deleted successfully."}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string", "format": "email"}, "username": {"type": "string"}}}, "UserListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "total": {"type": "integer"}}}, "CreateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "username": {"type": "string"}}}}}}