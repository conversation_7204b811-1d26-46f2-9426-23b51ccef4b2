好的，遵照您的指示。我将为您生成一份专门针对 **`market-data-service`** 的、极致细化的、生产级的**软件需求规格说明书 (SRS)**。

这份文档将详细定义这个服务的核心职责、数据拉取策略、缓存机制、接口和性能要求，使其成为平台所有加密货币市场数据的**唯一、高速、可靠的来源**。

---
### CINA.CLUB - `market-data-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-28**  
**文档负责人:** [Web3产品/技术负责人]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心架构与流程](#3-核心架构与流程)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB的Web3钱包功能需要为用户提供准确、实时的加密货币市场数据，以显示资产价值、进行交易决策和洞察市场动态。直接在客户端或多个后端服务中分别从外部数据源（如CoinGecko, CoinMarketCap）拉取数据，会造成数据不一致、API Key滥用、成本高昂和性能瓶颈。`market-data-service` 的目的在于构建一个**中心化的、高性能的、高可用的市场数据中台**。它通过**聚合、缓存和提供**标准化的加密货币市场数据，为平台所有需要行情数据的组件提供一个**统一、极速、可靠**的来源。

#### 1.2. 服务范围
本服务 **负责**:
*   **多数据源聚合**:
    *   从多个主流的第三方市场数据API（如CoinGecko, CoinMarketCap）**定期拉取**数据。
    *   对来自不同源的数据进行**清洗、聚合和交叉验证**，以提高数据质量和可靠性。
*   **核心数据管理**:
    *   提供**代币价格**: 实时价格、历史价格（K线）、24小时涨跌幅。
    *   提供**代币元数据**: 市值、流通量、官网链接、社交媒体链接等。
    *   维护一个平台**支持的代币列表**。
*   **高速缓存与服务**:
    *   将所有高频查询的市场数据**强制缓存**在内存/Redis中。
    *   提供**极低延迟**的API，供平台内外部查询市场数据。
*   **数据源健康检查与降级**:
    *   监控所有第三方数据源API的健康状况。
    *   在一个数据源失败时，能自动切换到备用数据源。

本服务 **不负责**:
*   **任何区块链交互**: 如查询链上余额，这是`blockchain-gateway-service`的职责。
*   **任何交易执行**: 如在DEX上进行Swap。
*   **用户个人资产的计算**。本服务只提供“单价”，总价的计算由客户端或调用方完成。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 通过API Gateway调用本服务，获取代币价格以计算资产总值，并展示行情信息。
*   **`blockchain-gateway-service`**: (可选) 调用本服务，为交易历史中的Token转账补充当时的价格。
*   **其他内部微服务**: 任何需要市场数据的服务，如DEX聚合功能。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`market-data-service` 是CINA.CLUB Web3功能域的“**行情数据中心**”和“**彭博终端**”。它是一个典型的**数据中台服务**，核心价值在于**“聚合”和“缓存”**。通过将对外部昂贵且有速率限制的API调用，收敛到这一个服务中，并利用高速缓存对外提供服务，它极大地降低了平台的外部依赖成本和数据获取延迟，同时提高了数据的可用性和一致性。

#### 2.2. 主要功能概述
*   支持多数据源、带故障降级的聚合拉取引擎。
*   基于Redis的、毫秒级响应的高速数据缓存。
*   提供全面的、标准化的代币价格和元数据查询API。
*   通过后台任务驱动数据更新，与在线API服务分离。

---

### 3. 核心架构与流程

#### 3.1 数据拉取与缓存更新流程 (后台任务)

```mermaid
sequenceDiagram
    participant Scheduler
    participant MarketDataService as MDS
    participant DataSourceA as "CoinGecko API"
    participant DataSourceB as "CoinMarketCap API"
    participant Redis

    Scheduler->>MDS: 1. Trigger "FetchPrices" cron job (e.g., every 60 seconds)
    
    MDS->>DataSourceA: 2a. [Attempt 1] Call API to get prices for top 250 coins
    alt DataSourceA is healthy
        DataSourceA-->>MDS: (Price data A)
    else DataSourceA fails
        Note over MDS: Circuit Breaker for A opens.
        MDS->>DataSourceB: 2b. [Fallback] Call API to get prices
        DataSourceB-->>MDS: (Price data B)
        Note over MDS: Use data B for this run.
    end

    MDS->>MDS: 3. Clean, aggregate, and standardize the price data
    
    MDS->>Redis: 4. Use `MSET` to atomically update all price keys (e.g., "price:bitcoin", "price:ethereum")
    
    Redis-->>MDS: (Update successful)
```

#### 3.2 客户端查询价格流程

```mermaid
sequenceDiagram
    participant Client
    participant MarketDataService as MDS
    participant Redis

    Client->>MDS: 1. GET /prices?ids=bitcoin,ethereum,solana
    MDS->>Redis: 2. Use `MGET` to fetch "price:bitcoin", "price:ethereum", "price:solana"
    Redis-->>MDS: 3. (Cached price data)
    MDS-->>Client: 4. Return the data
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 数据拉取与聚合引擎
*   **FR4.1.1 (后台任务驱动)**: 所有对外部数据源的拉取操作，**必须**在独立的后台Worker或CronJob中执行，与在线API服务完全分离。
*   **FR4.1.2 (多源适配)**: 必须为每个第三方数据源（CoinGecko, CMC等）实现一个统一的`MarketDataProvider`接口，封装其特定的API调用和数据格式。
*   **FR4.1.3 (聚合与清洗)**: 拉取到多个源的数据后，必须有一个聚合层，负责：
    *   将不同源的代币ID（如`"bitcoin"` vs `"BTC"`）映射到平台内部的统一ID。
    *   对于价格等关键数据，可以采用加权平均或选择中位数等策略，以平滑异常值。
*   **FR4.1.4 (故障降级)**: **必须**为数据源配置主备关系。当主数据源的API调用连续失败触发熔断时，拉取任务应自动切换到备用数据源。

#### 4.2. 数据缓存 (核心)
*   **FR4.2.1 (强制缓存)**: 所有对外提供查询的API，其数据**必须**直接来自**Redis缓存**。严禁在API请求路径上直接调用外部API。
*   **FR4.2.2 (数据结构)**:
    *   **实时价格**: 使用Redis的**String**或**Hash**。推荐`MSET`/`MGET`进行批量操作。
        *   Key: `market:price:{token_id}`
        *   Value: JSON字符串，如`{"usd": 60000, "cny": 420000}`。
    *   **代币列表**: 使用Redis的**Set**或**Sorted Set**存储平台支持的所有代币ID。
*   **FR4.2.3 (数据更新)**: 由后台拉取任务在完成数据处理后，主动、原子性地更新Redis中的数据。

#### 4.3. 提供的API数据类型
*   **FR4.3.1 (实时价格)**: `GetPrices`接口必须能批量查询一个或多个代币相对于多种法币（如USD, CNY, EUR）的价格。
*   **FR4.3.2 (代币列表与元数据)**: `ListTokens`接口必须能返回平台支持的、可搜索的代币列表，包含`id`, `symbol`, `name`, `logo_url`。
*   **FR4.3.3 (历史K线)**: `GetChartData`接口必须能提供标准格式的OHLCV（开、高、低、收、交易量）K线数据，支持不同时间粒度（日、周、月）。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部 gRPC API接口
*   **Package**: `hina.v1.market_data`
*   **认证**: 可选的用户JWT（用于速率限制）/ S2S Auth。
*   **核心RPC**:
    ```protobuf
    service MarketDataService {
      // 获取实时价格
      rpc GetPrices(GetPricesRequest) returns (GetPricesResponse);
      
      // 获取代币列表
      rpc ListTokens(ListTokensRequest) returns (ListTokensResponse);

      // 获取K线图数据
      rpc GetChartData(GetChartDataRequest) returns (GetChartDataResponse);
    }
    
    message GetPricesRequest {
      repeated string token_ids = 1;
      repeated string vs_currencies = 2; // e.g., ["usd", "cny"]
    }
    
    message GetPricesResponse {
      map<string, PriceData> prices = 1;
    }
    ```

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型
*   **缓存 (Redis)**: 这是本服务的主要“数据库”。
    *   `market:price:{token_id}` (String/JSON)
    *   `market:token_info:{token_id}` (Hash/JSON)
    *   `market:chart:daily:{token_id}` (Sorted Set or Time-series DB)
*   **持久化 (PostgreSQL, 可选)**:
    *   `tokens`: `id`, `symbol`, `name`, `platform_contract_address`。用于维护平台支持的代币主列表。
    *   `price_history_daily`: `token_id`, `date`, `open`, `high`, `low`, `close`。用于长期存储历史数据。

---

### 7. 非功能性需求 (Non-Functional Requirements)

*   **NFR7.1 (性能 - 最高优先级)**:
    *   **API延迟**: 所有API的P99延迟**必须 < 30ms**。这是通过强制从Redis读取来实现的。
    *   **后台任务**: 拉取任务的执行不应影响在线API的性能。
*   **NFR7.2 (可靠性与可用性)**:
    *   **API可用性**: > 99.99%。
    *   **数据新鲜度 (Freshness)**: 价格数据的延迟应小于拉取周期（如60秒）。
    *   **容错**: **必须**实现**FR4.1.4**中定义的数据源故障切换，确保即使一个外部API宕机，服务依然能提供数据。
*   **NFR7.3 (可扩展性)**:
    *   API服务应为无状态，可水平扩展。
    *   后台拉取任务可以部署为独立的Worker，并按需扩展。
    *   Redis集群必须是高可用、可扩展的。
*   **NFR7.4 (成本控制)**:
    *   必须严格遵守第三方API的速率限制。
    *   通过高效的批量拉取和缓存，将对外部API的调用次数降到最低。
    *   对API调用失败和费用进行监控和告警。

---

### 8. 技术约束与选型建议
*   **语言**: Go。其高并发模型非常适合编写高频的后台拉取任务和高性能的API。
*   **后台任务调度**: Kubernetes CronJob。
*   **数据源适配器**: 采用清晰的`Provider`接口和工厂模式，来管理多个数据源的实现。
*   **核心依赖**:
    *   `go-redis/redis`: 与Redis交互。
    *   标准库`net/http`: 调用外部RESTful API。

---
这份SRS为`market-data-service`的设计和实现提供了坚实、全面的指导。通过将数据拉取与服务提供分离，并以高速缓存为核心，本服务能够为整个CINA.CLUB平台提供**极速、可靠、且经济高效**的行情数据支持，是构建一流Web3用户体验不可或缺的一环。