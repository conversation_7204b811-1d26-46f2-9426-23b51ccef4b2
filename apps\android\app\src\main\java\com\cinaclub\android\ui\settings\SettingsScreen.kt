/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * Settings screen following WeChat design patterns.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateToAccount: () -> Unit,
    onNavigateToNotifications: () -> Unit,
    onNavigateToGeneral: () -> Unit,
    onNavigateToLanguage: () -> Unit,
    onNavigateToPrivacy: () -> Unit,
    onNavigateToAbout: () -> Unit,
    onLogout: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "设置",
                    fontWeight = FontWeight.Medium
                )
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF4A90E2)
            )
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Account & Security
            item {
                SettingsSection {
                    SettingsItem(
                        icon = Icons.Default.AccountCircle,
                        title = "账号与安全",
                        onClick = onNavigateToAccount
                    )
                }
            }
            
            // Notifications
            item {
                SettingsSection {
                    SettingsItem(
                        icon = Icons.Default.Notifications,
                        title = "消息通知",
                        onClick = onNavigateToNotifications
                    )
                }
            }
            
            // General
            item {
                SettingsSection {
                    SettingsItem(
                        icon = Icons.Default.Settings,
                        title = "通用",
                        onClick = onNavigateToGeneral
                    )
                    
                    Divider(color = Color(0xFFE0E0E0))
                    
                    SettingsItem(
                        icon = Icons.Default.Language,
                        title = "语言与地区",
                        onClick = onNavigateToLanguage
                    )
                }
            }
            
            // Privacy
            item {
                SettingsSection {
                    SettingsItem(
                        icon = Icons.Default.Lock,
                        title = "隐私",
                        onClick = onNavigateToPrivacy
                    )
                }
            }
            
            // About
            item {
                SettingsSection {
                    SettingsItem(
                        icon = Icons.Default.Info,
                        title = "关于",
                        onClick = onNavigateToAbout
                    )
                }
            }
            
            // Logout
            item {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onLogout() },
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "退出登录",
                            color = Color(0xFFE53935),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SettingsSection(
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        content()
    }
}

@Composable
private fun SettingsItem(
    icon: ImageVector,
    title: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color(0xFF666666)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Text(
            text = title,
            color = Color(0xFF333333),
            modifier = Modifier.weight(1f)
        )
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = null,
            tint = Color(0xFFBBBBBB)
        )
    }
} 