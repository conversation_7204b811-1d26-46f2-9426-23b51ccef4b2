好的，遵照您的指示。我将为您生成一份专门针对 **`family-tree-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将基于您提供的`family-tree-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**、**仓储模式**和**工作单元模式**，深入到代码实现的层面，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `family-tree-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `family-tree-service-srs.md` (v1.0)
**核心架构**: 整洁架构 (Clean Architecture)

## 1. 概述

`family-tree-service` 负责管理CINA.CLUB平台中结构化的家庭关系图谱。其核心挑战在于：
1.  **图数据的高效存储与查询**: 如何在关系型数据库中高效地表示和遍历图结构。
2.  **逻辑一致性保证**: 如何防止创建不合逻辑的关系（如循环依赖）。
3.  **清晰的职责分离**: 如何将复杂的图算法、业务规则和API接口解耦。

本架构设计通过采用**整洁架构**，将服务划分为独立的**领域层、应用层和适配层**，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 整洁架构分层图

```mermaid
graph TD
    subgraph "外部世界 (Frameworks & Drivers)"
        A[gRPC Server<br/>(adapter/grpc)]
        B[PostgreSQL<br/>(adapter/repository)]
        C[Kafka Consumer<br/>(adapter/event)]
    end
    
    subgraph "应用层 (Application)"
        E[FamilyTreeService<br/>(application/service)]
    end
    
    subgraph "领域层 (Domain)"
        F[FamilyGraph, Member, Relationship<br/>(domain/graph, domain/model)]
        G[Repository & Service Interfaces<br/>(application/port)]
    end
    
    A -- "调用" --> E
    E -- "使用" --> G
    B -- "实现" --> G
    C -- "调用" --> E
    E -- "操作" --> F
    
    style F fill:#ffb3b3,stroke:#333,stroke-width:2px
    style G fill:#ffb3b3,stroke:#333,stroke-width:2px
    style E fill:#b3d9ff,stroke:#333,stroke-width:2px
    style A fill:#b3ffb3,stroke:#333,stroke-width:2px
    style B fill:#b3ffb3,stroke:#333,stroke-width:2px
    style C fill:#b3ffb3,stroke:#333,stroke-width:2px
```

### 2.2 最终目录结构 (`services/family-tree-service/`)

```
family-tree-service/
├── cmd/server/
│   └── main.go                 # 服务启动入口，负责依赖注入和启动gRPC服务器
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   ├── server.go       # 注册gRPC服务和拦截器
│   │   │   ├── handler.go      # gRPC Handler实现，调用application service
│   │   │   └── converter.go    # Protobuf消息与领域模型的转换
│   │   ├── event/
│   │   │   └── user_consumer.go # 消费user-core-service事件的处理器
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型 (带gorm/db标签)
│   │       ├── repo.go         # 实现了所有仓储接口的struct
│   │       ├── member_repo.go  # FamilyMember仓储实现
│   │       ├── relation_repo.go # FamilyRelationship仓储实现
│   │       └── invitation_repo.go # RelationshipInvitation仓储实现
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go   # 定义所有仓储接口 (e.g., MemberRepository)
│   │   │   └── service.go      # 定义FamilyTreeService接口 (业务用例)
│   │   └── service/
│   │       └── service.go      # FamilyTreeService接口的实现
│   └── domain/
│       ├── model/              # 核心领域模型 (使用/core/models)
│       │   └── alias.go        # type Member = models.FamilyMember
│       ├── graph/              # ✨ 图处理核心逻辑 ✨
│       │   ├── graph.go        # FamilyGraph数据结构和图算法
│       │   ├── traversal.go    # 图遍历算法 (BFS, DFS)
│       │   └── validation.go   # 图逻辑校验 (如环路检测)
│       └── factory/              # (可选) 领域对象的创建工厂
│           └── invitation_factory.go
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Heart of Logic)

*   **`domain/model/`**:
    *   直接使用或别名化`/core/models`中定义的`FamilyMember`, `FamilyRelationship`等核心`struct`。这是业务语言的体现。
*   **`domain/graph/`**: **这是本服务最核心、最独特的部分**。
    *   `graph.go`: 定义`FamilyGraph`结构体。它不是一个数据库实体，而是一个**内存中的图表示**。它包含节点`map[UserID]*Member`和邻接表`map[UserID][]*Relationship`。
    *   `NewFamilyGraph(members, relations)`: 一个工厂函数，接收从数据库中取出的成员和关系列表，构建一个内存图对象。
    *   **图算法**: `FamilyGraph`结构体上将实现所有复杂的图操作方法：
        *   `FindPath(from, to UserID) []*Relationship`: 使用**双向广度优先搜索(Bi-directional BFS)**查找两个成员间的最短关系路径。
        *   `GetSubgraph(center UserID, depth int) *FamilyGraph`: 以一个成员为中心，获取指定深度的子图（用于生成族谱）。
        *   `DetectCycleOnAdd(newRelation Relationship) bool`: 在添加一条新关系**之前**，使用**深度优先搜索(DFS)**来检测是否会形成环路。这是防止逻辑悖论的关键。
        *   `ValidateConstraints(newRelation Relationship) error`: 检查生物学/社会学约束（如父母数量、年龄差）。

**设计决策**: 将图算法与数据库查询分离。仓储(repository)只负责从DB中高效地拉取构建图所需的数据，而所有复杂的遍历、路径查找、环路检测都在内存中的`FamilyGraph`对象上完成。这使得算法的实现更纯粹、更易于测试。

### 3.2 `application/` - 应用层 (The Use Cases)

*   **`application/port/`**: 定义了应用层与外部世界的契约。
    *   `repository.go`:
        ```go
        type Repository interface {
            // MemberRepository, RelationRepository, ...
            // 使用接口组合
        }
        type MemberRepository interface {
            // ...
        }
        // ...
        ```
    *   `service.go`:
        ```go
        type FamilyTreeService interface {
            CreateInvitation(ctx, fromUser, toUser, relType) error
            AcceptInvitation(ctx, user, inviteID) error
            GetFamilyGraph(ctx, user, targetUserID, depth) (*graph.FamilyGraph, error)
            // ...
        }
        ```
*   **`application/service/`**: 实现`FamilyTreeService`接口。这是业务流程的编排者。

    **示例: `AcceptInvitation` 流程**
    ```go
    func (s *service) AcceptInvitation(ctx, user, inviteID) error {
        // 1. 开启工作单元（事务）
        tx, err := s.db.Begin(ctx)
        defer tx.Rollback(ctx)

        // 2. 使用仓储获取数据
        invitation, err := s.repo.GetInvitationByID(ctx, tx, inviteID)
        // ... 检查邀请是否有效，user是否有权接受 ...
        
        // 3. ✨ 调用领域逻辑进行校验 ✨
        // 3a. 加载可能相关的图数据
        relatedMembers, relations, err := s.repo.GetRelativesForValidation(ctx, tx, fromID, toID)
        // 3b. 构建内存图
        currentGraph := graph.NewFamilyGraph(relatedMembers, relations)
        // 3c. 检查新关系是否会导致环路
        if currentGraph.DetectCycleOnAdd(newRelation) {
            return app_errors.New(app_errors.FailedPrecondition, "invalid relationship cycle")
        }
        
        // 4. 使用仓储持久化变更
        err = s.repo.CreateRelationship(ctx, tx, newRelation)
        err = s.repo.UpdateInvitationStatus(ctx, tx, inviteID, "ACCEPTED")
        
        // 5. 提交工作单元（事务）
        if err := tx.Commit(ctx); err != nil {
            return app_errors.Wrap(err, ...)
        }

        // 6. 发布领域事件（在事务成功后）
        s.eventProducer.Publish(ctx, ...)

        return nil
    }
    ```

### 3.3 `adapter/` - 适配层 (The Bridge to the World)

*   **`adapter/repository/`**:
    *   `model.go`: 定义与`family_members`, `family_relationships`等数据库表结构完全匹配的`struct`，带`gorm`或`db`标签。
    *   **仓储实现**:
        *   `GetRelativesForValidation`: 这是一个关键的、经过优化的SQL查询。它可能会使用**PostgreSQL的`WITH RECURSIVE` (CTE)**来高效地获取一个节点周围N度的所有亲属和关系，以供`domain/graph`构建内存图。
        *   所有方法都接收`pgx.Tx`，并遵循工作单元模式。
*   **`adapter/grpc/`**:
    *   `handler.go`: 接收gRPC请求，从请求中提取参数，调用`application.FamilyTreeService`的方法。
    *   `converter.go`: 负责将`domain/graph.FamilyGraph`等复杂的领域对象，转换为客户端易于渲染的、扁平化的Protobuf `message`。
        ```protobuf
        // family_tree_model.proto
        message GraphNode {
          string user_id = 1;
          string display_name = 2;
          string avatar_url = 3;
        }
        message GraphEdge {
          string from_node_id = 1;
          string to_node_id = 2;
          string relationship_type = 3;
        }
        message FamilyGraphResponse {
          repeated GraphNode nodes = 1;
          repeated GraphEdge edges = 2;
        }
        ```
*   **`adapter/event/`**:
    *   `user_consumer.go`: 实现一个Kafka消费者，监听来自`user-core-service`的`UserProfileUpdatedEvent`和`UserAccountHardDeletedEvent`。
    *   **处理逻辑**:
        *   收到`UserProfileUpdatedEvent`后，更新`family_members`表中的冗余信息（如`display_name`, `avatar_url`）。
        *   收到`UserAccountHardDeletedEvent`后，执行级联删除，清理与该用户相关的所有关系和邀请。

## 4. 数据库设计

*   **技术**: PostgreSQL。
*   **核心表**: `family_members`, `family_relationships`, `relationship_invitations` (如SRS v1.0所定义)。
*   **索引策略**:
    *   在`family_relationships`的`source_user_id`和`target_user_id`上创建**B-Tree索引**，以加速特定关系的查找。
    *   如果需要，可以创建**复合索引** `(source_user_id, relationship_type)`。
*   **递归查询 (CTE)**: 对于需要获取多代祖先或后代的场景，直接在数据库层面使用`WITH RECURSIVE`查询是一种高效的选择，可以作为`GetRelativesForValidation`的实现基础。

## 5. 总结

本架构设计通过严格的分层，将`family-tree-service`的核心复杂性进行了有效隔离：
*   **`domain/graph`** 封装了所有**图算法**，使其纯粹且可独立测试。
*   **`application/service`** 编排了所有**业务流程**，协调校验和持久化。
*   **`adapter/repository`** 负责所有**数据持久化**的细节，并为上层提供构建图所需的数据。
*   **`adapter/grpc`** 处理所有**API通信**的细节。

这种清晰的结构使得`family-tree-service`不仅功能强大，而且易于理解、维护和扩展。