<!--
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
-->

<Page x:Class="CinaClub.App.Views.LoginPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:controls="using:CommunityToolkit.WinUI.UI.Controls"
      Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid>
        <!-- 背景装饰 -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="{StaticResource CinaClubPrimary}" Offset="0" />
                <GradientStop Color="{StaticResource CinaClubSecondary}" Offset="1" />
            </LinearGradientBrush>
        </Grid.Background>

        <!-- 主要内容区域 -->
        <ScrollViewer HorizontalScrollMode="Disabled"
                      VerticalScrollMode="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      VerticalScrollBarVisibility="Auto">
            
            <Grid MaxWidth="800" 
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  Margin="20">
                
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="400" />
                </Grid.ColumnDefinitions>

                <!-- 左侧品牌展示区域 -->
                <StackPanel Grid.Column="0"
                           VerticalAlignment="Center"
                           Margin="0,0,40,0">
                    
                    <!-- Logo -->
                    <Image Source="ms-appx:///Assets/logo.png"
                           Width="120"
                           Height="120"
                           HorizontalAlignment="Left"
                           Margin="0,0,0,24" />
                    
                    <!-- 品牌标题 -->
                    <TextBlock Text="CINA.CLUB"
                              FontSize="48"
                              FontWeight="Bold"
                              Foreground="White"
                              Margin="0,0,0,16" />
                    
                    <!-- 副标题 -->
                    <TextBlock Text="您的智能生活助手"
                              FontSize="20"
                              Foreground="White"
                              Opacity="0.9"
                              Margin="0,0,0,32" />
                    
                    <!-- 特性列表 -->
                    <StackPanel Spacing="16">
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <FontIcon Glyph="&#xE72E;" 
                                     FontSize="20" 
                                     Foreground="White" 
                                     VerticalAlignment="Center" />
                            <TextBlock Text="端到端加密，保护您的隐私"
                                      FontSize="16"
                                      Foreground="White"
                                      VerticalAlignment="Center" />
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <FontIcon Glyph="&#xE8B8;" 
                                     FontSize="20" 
                                     Foreground="White" 
                                     VerticalAlignment="Center" />
                            <TextBlock Text="AI智能助手，提升工作效率"
                                      FontSize="16"
                                      Foreground="White"
                                      VerticalAlignment="Center" />
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <FontIcon Glyph="&#xE753;" 
                                     FontSize="20" 
                                     Foreground="White" 
                                     VerticalAlignment="Center" />
                            <TextBlock Text="多设备同步，随时随地访问"
                                      FontSize="16"
                                      Foreground="White"
                                      VerticalAlignment="Center" />
                        </StackPanel>
                    </StackPanel>
                </StackPanel>

                <!-- 右侧登录表单 -->
                <Border Grid.Column="1"
                       Style="{StaticResource CinaClubCardStyle}"
                       Background="{ThemeResource AcrylicBackgroundFillColorDefaultBrush}"
                       BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}"
                       CornerRadius="16"
                       Padding="32">
                    
                    <StackPanel Spacing="24">
                        <!-- 登录标题 -->
                        <TextBlock Text="登录账户"
                                  FontSize="28"
                                  FontWeight="SemiBold"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,8" />
                        
                        <!-- 欢迎文本 -->
                        <TextBlock Text="欢迎回来！请输入您的账户信息"
                                  FontSize="14"
                                  Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                  HorizontalAlignment="Center"
                                  TextWrapping="Wrap"
                                  Margin="0,0,0,16" />

                        <!-- 登录表单 -->
                        <StackPanel Spacing="16">
                            
                            <!-- 用户名输入 -->
                            <StackPanel Spacing="8">
                                <TextBlock Text="用户名/邮箱/手机号"
                                          FontSize="14"
                                          FontWeight="SemiBold" />
                                <TextBox x:Name="UsernameTextBox"
                                        Text="{x:Bind ViewModel.Username, Mode=TwoWay}"
                                        PlaceholderText="请输入用户名、邮箱或手机号"
                                        CornerRadius="8"
                                        Padding="12,8"
                                        FontSize="14" />
                            </StackPanel>

                            <!-- 密码输入 -->
                            <StackPanel Spacing="8">
                                <TextBlock Text="密码"
                                          FontSize="14"
                                          FontWeight="SemiBold" />
                                <PasswordBox x:Name="PasswordBox"
                                           Password="{x:Bind ViewModel.Password, Mode=TwoWay}"
                                           PlaceholderText="请输入密码"
                                           CornerRadius="8"
                                           Padding="12,8"
                                           FontSize="14" />
                            </StackPanel>

                            <!-- 记住我和忘记密码 -->
                            <Grid>
                                <CheckBox Content="记住我"
                                         IsChecked="{x:Bind ViewModel.RememberMe, Mode=TwoWay}"
                                         FontSize="14"
                                         HorizontalAlignment="Left" />
                                
                                <HyperlinkButton Content="忘记密码？"
                                               FontSize="14"
                                               HorizontalAlignment="Right"
                                               Click="{x:Bind ViewModel.ForgotPasswordCommand}" />
                            </Grid>

                            <!-- 登录按钮 -->
                            <Button Content="登录"
                                   Command="{x:Bind ViewModel.LoginCommand}"
                                   Style="{StaticResource CinaClubButtonStyle}"
                                   HorizontalAlignment="Stretch"
                                   Height="48"
                                   FontSize="16"
                                   IsEnabled="{x:Bind ViewModel.CanLogin, Mode=OneWay}"
                                   Margin="0,8,0,0">
                                <Button.Resources>
                                    <Style TargetType="Button" BasedOn="{StaticResource CinaClubButtonStyle}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Grid>
                                                        <Border x:Name="Background"
                                                               Background="{TemplateBinding Background}"
                                                               CornerRadius="8"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}">
                                                            <Grid>
                                                                <ContentPresenter x:Name="ContentPresenter"
                                                                                Content="{TemplateBinding Content}"
                                                                                HorizontalAlignment="Center"
                                                                                VerticalAlignment="Center"
                                                                                Visibility="{x:Bind ViewModel.IsNotLoggingIn, Mode=OneWay}" />
                                                                
                                                                <StackPanel Orientation="Horizontal"
                                                                          HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"
                                                                          Spacing="12"
                                                                          Visibility="{x:Bind ViewModel.IsLoggingIn, Mode=OneWay}">
                                                                    <ProgressRing IsActive="True"
                                                                                Width="20"
                                                                                Height="20"
                                                                                Foreground="White" />
                                                                    <TextBlock Text="正在登录..."
                                                                             Foreground="White"
                                                                             VerticalAlignment="Center" />
                                                                </StackPanel>
                                                            </Grid>
                                                        </Border>
                                                        
                                                        <VisualStateManager.VisualStateGroups>
                                                            <VisualStateGroup x:Name="CommonStates">
                                                                <VisualState x:Name="Normal" />
                                                                <VisualState x:Name="PointerOver">
                                                                    <VisualState.Setters>
                                                                        <Setter Target="Background.Opacity" Value="0.9" />
                                                                    </VisualState.Setters>
                                                                </VisualState>
                                                                <VisualState x:Name="Pressed">
                                                                    <VisualState.Setters>
                                                                        <Setter Target="Background.Opacity" Value="0.8" />
                                                                    </VisualState.Setters>
                                                                </VisualState>
                                                                <VisualState x:Name="Disabled">
                                                                    <VisualState.Setters>
                                                                        <Setter Target="Background.Opacity" Value="0.5" />
                                                                    </VisualState.Setters>
                                                                </VisualState>
                                                            </VisualStateGroup>
                                                        </VisualStateManager.VisualStateGroups>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Resources>
                            </Button>

                            <!-- 分隔线 -->
                            <Border Height="1"
                                   Background="{ThemeResource DividerStrokeColorDefaultBrush}"
                                   Margin="0,16,0,16" />

                            <!-- 注册链接 -->
                            <StackPanel Orientation="Horizontal"
                                       HorizontalAlignment="Center"
                                       Spacing="8">
                                <TextBlock Text="还没有账户？"
                                          FontSize="14"
                                          Foreground="{ThemeResource TextFillColorSecondaryBrush}" />
                                <HyperlinkButton Content="立即注册"
                                               FontSize="14"
                                               Padding="0"
                                               Click="{x:Bind ViewModel.NavigateToRegisterCommand}" />
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>
    </Grid>
</Page> 