// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-15 12:00:00
// Modified: 2025-01-15 12:00:00

package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"cina.club/services/analytics-service/internal/application/service"
)

// BusinessMetricsHandler Business metrics HTTP handler
type BusinessMetricsHandler struct {
	businessMetricsService *service.BusinessMetricsService
}

// NewBusinessMetricsHandler creates a new business metrics handler
func NewBusinessMetricsHandler(businessMetricsService *service.BusinessMetricsService) *BusinessMetricsHandler {
	return &BusinessMetricsHandler{
		businessMetricsService: businessMetricsService,
	}
}

// GetRevenueMetrics gets revenue metrics
func (h *BusinessMetricsHandler) GetRevenueMetrics(c *gin.Context) {
	// Parse time range parameters
	timeRange, err := parseTimeRange(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameters",
			"message": err.Error(),
		})
		return
	}

	metrics, err := h.businessMetricsService.GetRevenueMetrics(c.Request.Context(), timeRange)
	if err != nil {
		logrus.WithError(err).WithField("time_range", timeRange).Error("Failed to get revenue metrics")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve revenue metrics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// GetServicePerformance gets service performance metrics
func (h *BusinessMetricsHandler) GetServicePerformance(c *gin.Context) {
	// Parse time range parameters
	timeRange, err := parseTimeRange(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameters",
			"message": err.Error(),
		})
		return
	}

	performance, err := h.businessMetricsService.GetServicePerformance(c.Request.Context(), timeRange)
	if err != nil {
		logrus.WithError(err).WithField("time_range", timeRange).Error("Failed to get service performance")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve service performance",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    performance,
	})
}

// GetGeographicDistribution gets geographic distribution metrics
func (h *BusinessMetricsHandler) GetGeographicDistribution(c *gin.Context) {
	// Parse time range parameters
	timeRange, err := parseTimeRange(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid parameters",
			"message": err.Error(),
		})
		return
	}

	distribution, err := h.businessMetricsService.GetGeographicDistribution(c.Request.Context(), timeRange)
	if err != nil {
		logrus.WithError(err).WithField("time_range", timeRange).Error("Failed to get geographic distribution")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "Failed to retrieve geographic distribution",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    distribution,
	})
}
