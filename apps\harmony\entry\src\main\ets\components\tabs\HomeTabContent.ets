/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

/**
 * 首页Tab内容组件
 */
@Component
export struct HomeTabContent {
  @State private isLoading: boolean = false;

  build() {
    Column() {
      // 标题区域
      Text('CINA.CLUB')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 20, bottom: 8 })
      
      Text('智能生活，无限可能')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ bottom: 30 })
      
      // 功能卡片区域
      Column() {
        this.FeatureCard('🤖 AI助手', '智能对话，解答疑问')
        this.FeatureCard('📰 快讯中心', '实时资讯，一手掌握')
        this.FeatureCard('🛍️ 服务市场', '海量服务，按需选择')
        this.FeatureCard('📚 知识库', '个人笔记，加密存储')
      }
      .width('100%')
      
      Blank()
    }
    .width('100%')
    .height('100%')
    .padding({ left: 16, right: 16 })
    .justifyContent(FlexAlign.Start)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  FeatureCard(title: string, description: string) {
    Row() {
      Column() {
        Text(title)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .margin({ bottom: 4 })
        
        Text(description)
          .fontSize(14)
          .fontColor('#666666')
      }
      .alignItems(HorizontalAlign.Start)
      
      Blank()
      
      Text('>')
        .fontSize(16)
        .fontColor('#999999')
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#F5F5F5')
    .borderRadius(12)
    .margin({ bottom: 12 })
  }
} 