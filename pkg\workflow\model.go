/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package workflow provides a lightweight, stateless DAG-based workflow execution engine.
// It is designed to orchestrate complex, multi-step tasks with conditional logic and data flow.
package workflow

import (
	"encoding/json"
	"fmt"
)

// Workflow defines the complete structure of an executable workflow.
// It represents a Directed Acyclic Graph (DAG) composed of nodes and edges.
type Workflow struct {
	// ID is the unique identifier for this workflow
	ID string `json:"id,omitempty"`

	// Name is a human-readable name for the workflow
	Name string `json:"name,omitempty"`

	// Description provides details about what this workflow does
	Description string `json:"description,omitempty"`

	// Version indicates the workflow definition version
	Version string `json:"version,omitempty"`

	// Nodes defines all the execution units in the workflow
	Nodes []Node `json:"nodes"`

	// Edges defines the dependencies and data flow between nodes
	Edges []Edge `json:"edges"`

	// Metadata can store additional workflow-specific information
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Node represents a single execution unit in the workflow.
// Each node has a type that maps to a registered NodeExecutor implementation.
type Node struct {
	// ID is the unique identifier for this node within the workflow
	ID string `json:"id"`

	// Type specifies which NodeExecutor implementation to use
	// Examples: "api_call", "condition", "llm_prompt", "send_notification"
	Type string `json:"type"`

	// Name is a human-readable name for the node
	Name string `json:"name,omitempty"`

	// Description explains what this node does
	Description string `json:"description,omitempty"`

	// Inputs defines the input parameters for this node
	// Values can be static values or template expressions like "{{ .nodes.prev_node.outputs.result }}"
	Inputs map[string]interface{} `json:"inputs,omitempty"`

	// Retry defines retry configuration for this node
	Retry *RetryConfig `json:"retry,omitempty"`

	// Timeout specifies the maximum execution time for this node (in seconds)
	Timeout int `json:"timeout,omitempty"`

	// Metadata can store additional node-specific information
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Edge defines a dependency and data flow relationship between two nodes.
// It specifies which output from the source node should be passed to which input of the target node.
type Edge struct {
	// FromNode is the ID of the source node
	FromNode string `json:"fromNode"`

	// ToNode is the ID of the target node
	ToNode string `json:"toNode"`

	// FromOutput specifies which output field from the source node to use
	// If empty, all outputs from the source node are available to the target node
	FromOutput string `json:"fromOutput,omitempty"`

	// ToInput specifies which input field of the target node to populate
	// If empty, the edge represents a simple dependency without data transfer
	ToInput string `json:"toInput,omitempty"`

	// Condition defines when this edge should be followed
	// Used for conditional branching based on the source node's outputs
	Condition *EdgeCondition `json:"condition,omitempty"`

	// Metadata can store additional edge-specific information
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// EdgeCondition defines conditional logic for edge traversal.
// This enables conditional branching in workflows.
type EdgeCondition struct {
	// Type specifies the condition type: "equals", "not_equals", "exists", "not_exists", "expression"
	Type string `json:"type"`

	// Field specifies which output field from the source node to evaluate
	Field string `json:"field,omitempty"`

	// Value is the value to compare against (for "equals" and "not_equals" conditions)
	Value interface{} `json:"value,omitempty"`

	// Expression is a custom expression for complex conditions (for "expression" type)
	Expression string `json:"expression,omitempty"`
}

// RetryConfig defines retry behavior for node execution.
type RetryConfig struct {
	// MaxAttempts is the maximum number of retry attempts (including the initial attempt)
	MaxAttempts int `json:"maxAttempts"`

	// BackoffSeconds defines the base backoff time in seconds
	BackoffSeconds int `json:"backoffSeconds"`

	// BackoffMultiplier defines how much to multiply the backoff time for each retry
	BackoffMultiplier float64 `json:"backoffMultiplier,omitempty"`

	// RetryableErrors defines which error types should trigger a retry
	// If empty, all errors trigger retries
	RetryableErrors []string `json:"retryableErrors,omitempty"`
}

// NodeStatus represents the execution status of a node.
type NodeStatus string

const (
	// NodeStatusPending indicates the node is waiting to be executed
	NodeStatusPending NodeStatus = "pending"

	// NodeStatusRunning indicates the node is currently executing
	NodeStatusRunning NodeStatus = "running"

	// NodeStatusCompleted indicates the node executed successfully
	NodeStatusCompleted NodeStatus = "completed"

	// NodeStatusFailed indicates the node execution failed
	NodeStatusFailed NodeStatus = "failed"

	// NodeStatusSkipped indicates the node was skipped due to conditions
	NodeStatusSkipped NodeStatus = "skipped"
)

// NodeResult contains the execution result of a node.
type NodeResult struct {
	// NodeID is the ID of the node that was executed
	NodeID string `json:"nodeId"`

	// Status indicates the execution status
	Status NodeStatus `json:"status"`

	// Outputs contains the output data produced by the node
	Outputs map[string]interface{} `json:"outputs,omitempty"`

	// Error contains error information if the node failed
	Error string `json:"error,omitempty"`

	// StartTime is when the node started executing (RFC3339 format)
	StartTime string `json:"startTime,omitempty"`

	// EndTime is when the node finished executing (RFC3339 format)
	EndTime string `json:"endTime,omitempty"`

	// Duration is the execution time in milliseconds
	Duration int64 `json:"duration,omitempty"`

	// Attempt is the current attempt number (starts from 1)
	Attempt int `json:"attempt,omitempty"`

	// Metadata can store additional execution-specific information
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Validate performs basic validation on the workflow structure.
func (w *Workflow) Validate() error {
	if len(w.Nodes) == 0 {
		return fmt.Errorf("workflow must contain at least one node")
	}

	// Validate node IDs are unique
	nodeIDs := make(map[string]bool)
	for _, node := range w.Nodes {
		if node.ID == "" {
			return fmt.Errorf("node ID cannot be empty")
		}
		if nodeIDs[node.ID] {
			return fmt.Errorf("duplicate node ID: %s", node.ID)
		}
		nodeIDs[node.ID] = true

		if node.Type == "" {
			return fmt.Errorf("node %s must have a type", node.ID)
		}
	}

	// Validate edges reference existing nodes
	for _, edge := range w.Edges {
		if !nodeIDs[edge.FromNode] {
			return fmt.Errorf("edge references non-existent fromNode: %s", edge.FromNode)
		}
		if !nodeIDs[edge.ToNode] {
			return fmt.Errorf("edge references non-existent toNode: %s", edge.ToNode)
		}
		if edge.FromNode == edge.ToNode {
			return fmt.Errorf("edge cannot connect a node to itself: %s", edge.FromNode)
		}
	}

	return nil
}

// GetNode returns the node with the specified ID, or nil if not found.
func (w *Workflow) GetNode(nodeID string) *Node {
	for i := range w.Nodes {
		if w.Nodes[i].ID == nodeID {
			return &w.Nodes[i]
		}
	}
	return nil
}

// GetNodesByType returns all nodes of the specified type.
func (w *Workflow) GetNodesByType(nodeType string) []Node {
	var result []Node
	for _, node := range w.Nodes {
		if node.Type == nodeType {
			result = append(result, node)
		}
	}
	return result
}

// GetIncomingEdges returns all edges that have the specified node as their target.
func (w *Workflow) GetIncomingEdges(nodeID string) []Edge {
	var result []Edge
	for _, edge := range w.Edges {
		if edge.ToNode == nodeID {
			result = append(result, edge)
		}
	}
	return result
}

// GetOutgoingEdges returns all edges that have the specified node as their source.
func (w *Workflow) GetOutgoingEdges(nodeID string) []Edge {
	var result []Edge
	for _, edge := range w.Edges {
		if edge.FromNode == nodeID {
			result = append(result, edge)
		}
	}
	return result
}

// ToJSON serializes the workflow to JSON format.
func (w *Workflow) ToJSON() ([]byte, error) {
	return json.MarshalIndent(w, "", "  ")
}

// FromJSON deserializes a workflow from JSON format.
func FromJSON(data []byte) (*Workflow, error) {
	var workflow Workflow
	if err := json.Unmarshal(data, &workflow); err != nil {
		return nil, fmt.Errorf("failed to parse workflow JSON: %w", err)
	}

	if err := workflow.Validate(); err != nil {
		return nil, fmt.Errorf("invalid workflow: %w", err)
	}

	return &workflow, nil
}

// Clone creates a deep copy of the workflow.
func (w *Workflow) Clone() (*Workflow, error) {
	data, err := w.ToJSON()
	if err != nil {
		return nil, err
	}
	return FromJSON(data)
}
