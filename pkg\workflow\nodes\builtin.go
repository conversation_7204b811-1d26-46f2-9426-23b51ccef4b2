/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package nodes provides built-in node implementations for the workflow engine.
// These nodes are general-purpose and business-agnostic, suitable for common workflow patterns.
package nodes

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"cina.club/pkg/workflow"
)

// StartNode represents the entry point of a workflow.
// It simply passes through its inputs as outputs, useful for initializing workflow variables.
type StartNode struct{}

// Execute implements the NodeExecutor interface for StartNode.
func (n *StartNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
	outputs := make(map[string]interface{})

	// Copy all inputs to outputs
	for key, value := range inputs {
		outputs[key] = value
	}

	// Add execution metadata
	outputs["_startTime"] = time.Now().Format(time.RFC3339)
	outputs["_nodeType"] = "start"

	return outputs, nil
}

// EndNode represents the exit point of a workflow.
// It can aggregate results from multiple branches or simply mark workflow completion.
type EndNode struct{}

// Execute implements the NodeExecutor interface for EndNode.
func (n *EndNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
	outputs := make(map[string]interface{})

	// Copy all inputs to outputs
	for key, value := range inputs {
		outputs[key] = value
	}

	// Add execution metadata
	outputs["_endTime"] = time.Now().Format(time.RFC3339)
	outputs["_nodeType"] = "end"
	outputs["_status"] = "completed"

	return outputs, nil
}

// ConditionNode evaluates a condition and returns a boolean result.
// This is useful for conditional branching in workflows.
type ConditionNode struct{}

// Execute implements the NodeExecutor interface for ConditionNode.
func (n *ConditionNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
	// Expected inputs:
	// - "operator": string ("equals", "not_equals", "greater_than", "less_than", "contains", "exists")
	// - "left": interface{} (left operand)
	// - "right": interface{} (right operand, optional for some operators)

	operator, ok := inputs["operator"].(string)
	if !ok {
		return nil, fmt.Errorf("operator input is required and must be a string")
	}

	left := inputs["left"]
	right := inputs["right"]

	result, err := n.evaluateCondition(operator, left, right)
	if err != nil {
		return nil, fmt.Errorf("condition evaluation failed: %w", err)
	}

	outputs := map[string]interface{}{
		"result":    result,
		"operator":  operator,
		"left":      left,
		"right":     right,
		"_nodeType": "condition",
	}

	return outputs, nil
}

// evaluateCondition evaluates a condition based on the operator and operands.
func (n *ConditionNode) evaluateCondition(operator string, left, right interface{}) (bool, error) {
	switch operator {
	case "equals":
		return n.equals(left, right), nil
	case "not_equals":
		return !n.equals(left, right), nil
	case "greater_than":
		return n.greaterThan(left, right)
	case "less_than":
		return n.lessThan(left, right)
	case "greater_equal":
		gt, _ := n.greaterThan(left, right)
		eq := n.equals(left, right)
		return gt || eq, nil
	case "less_equal":
		lt, _ := n.lessThan(left, right)
		eq := n.equals(left, right)
		return lt || eq, nil
	case "contains":
		return n.contains(left, right)
	case "exists":
		return left != nil, nil
	case "not_exists":
		return left == nil, nil
	default:
		return false, fmt.Errorf("unknown operator: %s", operator)
	}
}

// equals checks if two values are equal.
func (n *ConditionNode) equals(left, right interface{}) bool {
	return reflect.DeepEqual(left, right)
}

// greaterThan compares two numeric values.
func (n *ConditionNode) greaterThan(left, right interface{}) (bool, error) {
	leftNum, err := n.toFloat64(left)
	if err != nil {
		return false, fmt.Errorf("left operand is not numeric: %w", err)
	}

	rightNum, err := n.toFloat64(right)
	if err != nil {
		return false, fmt.Errorf("right operand is not numeric: %w", err)
	}

	return leftNum > rightNum, nil
}

// lessThan compares two numeric values.
func (n *ConditionNode) lessThan(left, right interface{}) (bool, error) {
	leftNum, err := n.toFloat64(left)
	if err != nil {
		return false, fmt.Errorf("left operand is not numeric: %w", err)
	}

	rightNum, err := n.toFloat64(right)
	if err != nil {
		return false, fmt.Errorf("right operand is not numeric: %w", err)
	}

	return leftNum < rightNum, nil
}

// contains checks if left contains right (for strings and slices).
func (n *ConditionNode) contains(left, right interface{}) (bool, error) {
	switch l := left.(type) {
	case string:
		r, ok := right.(string)
		if !ok {
			return false, fmt.Errorf("right operand must be string when left is string")
		}
		return strings.Contains(l, r), nil
	case []interface{}:
		for _, item := range l {
			if reflect.DeepEqual(item, right) {
				return true, nil
			}
		}
		return false, nil
	default:
		return false, fmt.Errorf("contains operator not supported for type %T", left)
	}
}

// toFloat64 converts various numeric types to float64.
func (n *ConditionNode) toFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", value)
	}
}

// MergeNode combines outputs from multiple predecessor nodes.
// This is useful for joining parallel branches in a workflow.
type MergeNode struct{}

// Execute implements the NodeExecutor interface for MergeNode.
func (n *MergeNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
	outputs := make(map[string]interface{})

	// Copy all inputs to outputs
	for key, value := range inputs {
		outputs[key] = value
	}

	// Add merge metadata
	outputs["_nodeType"] = "merge"
	outputs["_mergedCount"] = len(inputs)

	return outputs, nil
}

// DelayNode introduces a delay in workflow execution.
// This is useful for rate limiting or waiting between operations.
type DelayNode struct{}

// Execute implements the NodeExecutor interface for DelayNode.
func (n *DelayNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
	// Expected inputs:
	// - "duration": string (duration in Go format like "5s", "1m", "2h") or number (seconds)

	var duration time.Duration
	var err error

	switch d := inputs["duration"].(type) {
	case string:
		duration, err = time.ParseDuration(d)
		if err != nil {
			return nil, fmt.Errorf("invalid duration format: %w", err)
		}
	case int:
		duration = time.Duration(d) * time.Second
	case float64:
		duration = time.Duration(d) * time.Second
	default:
		return nil, fmt.Errorf("duration must be a string or number")
	}

	startTime := time.Now()

	// Wait for the specified duration
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-time.After(duration):
		// Continue execution
	}

	outputs := map[string]interface{}{
		"_nodeType":   "delay",
		"duration":    duration.String(),
		"startTime":   startTime.Format(time.RFC3339),
		"endTime":     time.Now().Format(time.RFC3339),
		"actualDelay": time.Since(startTime).String(),
	}

	// Copy other inputs to outputs
	for key, value := range inputs {
		if key != "duration" {
			outputs[key] = value
		}
	}

	return outputs, nil
}

// TransformNode applies transformations to input data.
// This is useful for data mapping and manipulation in workflows.
type TransformNode struct{}

// Execute implements the NodeExecutor interface for TransformNode.
func (n *TransformNode) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
	// Expected inputs:
	// - "transformations": map[string]interface{} (mapping of output field to transformation)
	// - other fields: data to be transformed

	transformations, ok := inputs["transformations"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("transformations input is required and must be a map")
	}

	outputs := make(map[string]interface{})

	// Apply transformations
	for outputField, transformation := range transformations {
		switch t := transformation.(type) {
		case string:
			// Simple field mapping: "fieldName" -> copy value from that field
			if value, exists := inputs[t]; exists {
				outputs[outputField] = value
			}
		case map[string]interface{}:
			// Complex transformation with type and parameters
			result, err := n.applyTransformation(t, inputs)
			if err != nil {
				return nil, fmt.Errorf("transformation failed for field %s: %w", outputField, err)
			}
			outputs[outputField] = result
		default:
			// Direct value assignment
			outputs[outputField] = transformation
		}
	}

	// Add transformation metadata
	outputs["_nodeType"] = "transform"
	outputs["_transformationCount"] = len(transformations)

	return outputs, nil
}

// applyTransformation applies a complex transformation to input data.
func (n *TransformNode) applyTransformation(transformation map[string]interface{}, inputs map[string]interface{}) (interface{}, error) {
	transformType, ok := transformation["type"].(string)
	if !ok {
		return nil, fmt.Errorf("transformation type is required")
	}

	switch transformType {
	case "concat":
		return n.concatTransform(transformation, inputs)
	case "format":
		return n.formatTransform(transformation, inputs)
	case "math":
		return n.mathTransform(transformation, inputs)
	case "extract":
		return n.extractTransform(transformation, inputs)
	default:
		return nil, fmt.Errorf("unknown transformation type: %s", transformType)
	}
}

// concatTransform concatenates multiple fields into a string.
func (n *TransformNode) concatTransform(transformation map[string]interface{}, inputs map[string]interface{}) (interface{}, error) {
	fields, ok := transformation["fields"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("concat transformation requires 'fields' array")
	}

	separator := ""
	if sep, exists := transformation["separator"]; exists {
		separator = fmt.Sprintf("%v", sep)
	}

	var parts []string
	for _, field := range fields {
		fieldName := fmt.Sprintf("%v", field)
		if value, exists := inputs[fieldName]; exists {
			parts = append(parts, fmt.Sprintf("%v", value))
		}
	}

	return strings.Join(parts, separator), nil
}

// formatTransform formats a string with placeholders.
func (n *TransformNode) formatTransform(transformation map[string]interface{}, inputs map[string]interface{}) (interface{}, error) {
	template, ok := transformation["template"].(string)
	if !ok {
		return nil, fmt.Errorf("format transformation requires 'template' string")
	}

	// Simple placeholder replacement: {fieldName}
	result := template
	for key, value := range inputs {
		placeholder := "{" + key + "}"
		result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
	}

	return result, nil
}

// mathTransform performs mathematical operations.
func (n *TransformNode) mathTransform(transformation map[string]interface{}, inputs map[string]interface{}) (interface{}, error) {
	operation, ok := transformation["operation"].(string)
	if !ok {
		return nil, fmt.Errorf("math transformation requires 'operation' string")
	}

	operands, ok := transformation["operands"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("math transformation requires 'operands' array")
	}

	// Convert operands to numbers
	var numbers []float64
	for _, operand := range operands {
		switch op := operand.(type) {
		case string:
			// Field reference
			if value, exists := inputs[op]; exists {
				num, err := n.toFloat64(value)
				if err != nil {
					return nil, fmt.Errorf("operand %s is not numeric: %w", op, err)
				}
				numbers = append(numbers, num)
			}
		default:
			// Direct numeric value
			num, err := n.toFloat64(operand)
			if err != nil {
				return nil, fmt.Errorf("operand is not numeric: %w", err)
			}
			numbers = append(numbers, num)
		}
	}

	if len(numbers) == 0 {
		return nil, fmt.Errorf("no valid numeric operands found")
	}

	// Perform operation
	switch operation {
	case "add":
		result := 0.0
		for _, num := range numbers {
			result += num
		}
		return result, nil
	case "subtract":
		if len(numbers) < 2 {
			return nil, fmt.Errorf("subtract operation requires at least 2 operands")
		}
		result := numbers[0]
		for i := 1; i < len(numbers); i++ {
			result -= numbers[i]
		}
		return result, nil
	case "multiply":
		result := 1.0
		for _, num := range numbers {
			result *= num
		}
		return result, nil
	case "divide":
		if len(numbers) < 2 {
			return nil, fmt.Errorf("divide operation requires at least 2 operands")
		}
		result := numbers[0]
		for i := 1; i < len(numbers); i++ {
			if numbers[i] == 0 {
				return nil, fmt.Errorf("division by zero")
			}
			result /= numbers[i]
		}
		return result, nil
	default:
		return nil, fmt.Errorf("unknown math operation: %s", operation)
	}
}

// extractTransform extracts a value from a nested structure.
func (n *TransformNode) extractTransform(transformation map[string]interface{}, inputs map[string]interface{}) (interface{}, error) {
	path, ok := transformation["path"].(string)
	if !ok {
		return nil, fmt.Errorf("extract transformation requires 'path' string")
	}

	source := transformation["source"]
	if source == nil {
		source = "data" // default source field
	}

	sourceField := fmt.Sprintf("%v", source)
	sourceData, exists := inputs[sourceField]
	if !exists {
		return nil, fmt.Errorf("source field %s not found", sourceField)
	}

	// Simple path traversal: "field.subfield.index"
	parts := strings.Split(path, ".")
	current := sourceData

	for _, part := range parts {
		switch c := current.(type) {
		case map[string]interface{}:
			current = c[part]
		case []interface{}:
			// Try to parse part as index
			index, err := strconv.Atoi(part)
			if err != nil {
				return nil, fmt.Errorf("invalid array index: %s", part)
			}
			if index < 0 || index >= len(c) {
				return nil, fmt.Errorf("array index out of bounds: %d", index)
			}
			current = c[index]
		default:
			return nil, fmt.Errorf("cannot traverse path %s: invalid type at %s", path, part)
		}

		if current == nil {
			break
		}
	}

	return current, nil
}

// toFloat64 converts various numeric types to float64 for TransformNode.
func (n *TransformNode) toFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", value)
	}
}

// RegisterBuiltinNodes registers all built-in node types with the executor.
func RegisterBuiltinNodes(executor workflow.WorkflowEngine) {
	executor.RegisterNode("start", &StartNode{})
	executor.RegisterNode("end", &EndNode{})
	executor.RegisterNode("condition", &ConditionNode{})
	executor.RegisterNode("merge", &MergeNode{})
	executor.RegisterNode("delay", &DelayNode{})
	executor.RegisterNode("transform", &TransformNode{})
}
