# CINA.CLUB Platform - JWT Validator Plugin (Platform Standard)
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# JWT Validator Plugin - Platform Standard Authentication
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jwt-validator
  namespace: kong-system
  labels:
    app: kong-plugin
    component: authentication
    tier: platform-standard
    category: security
    scope: global
  annotations:
    description: "Platform standard JWT authentication plugin for CINA.CLUB services"
    usage: "Apply to protected routes via konghq.com/plugins annotation"
    owner: "<EMAIL>"
    documentation: "https://docs.cina.club/platform/auth/jwt"

plugin: jwt
config:
  # JWT signature verification
  key_claim_name: "kid"                    # Key ID claim for JWKS lookup
  claims_to_verify:                        # Claims to verify for security
    - "exp"                                # Expiration time (required)
    - "iss"                                # Issuer (required)
    - "aud"                                # Audience (required)
    - "iat"                                # Issued at time
    - "nbf"                                # Not before time
  
  # JWKS configuration for dynamic key retrieval
  secret_is_base64: false                  # JWKS keys are not base64 encoded
  run_on_preflight: true                   # Validate JWT on preflight requests
  
  # JWT token sources (where to find the JWT)
  uri_param_names:                         # Look for JWT in URL parameters
    - "jwt"
    - "token"
  header_names:                            # Look for JWT in headers
    - "Authorization"                      # Standard Authorization header
    - "X-Auth-Token"                       # Custom auth token header
  cookie_names:                            # Look for JWT in cookies
    - "auth-token"
    - "session-token"
  
  # Anonymous access configuration
  anonymous: null                          # No anonymous access (force authentication)
  
  # Clock skew tolerance (for time-based claims)
  maximum_expiration: 86400                # Maximum token lifetime: 24 hours
  clock_skew_in_seconds: 30                # Allow 30 seconds clock skew
  
  # Custom claims validation
  claims_to_verify_existence:              # Claims that must exist
    - "sub"                                # Subject (user ID)
    - "role"                               # User role
    - "scope"                              # Permission scope
  
  # JWKS URI configuration (dynamic key retrieval)
  # This will be set per service, as each service may have different JWKS endpoints
  
---
# JWT Validator Plugin for User Service (with JWKS)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jwt-validator-user-service
  namespace: kong-system
  labels:
    app: kong-plugin
    component: authentication
    service: user-core-service
    tier: platform-standard
  annotations:
    description: "JWT validator with JWKS from user-core-service"

plugin: jwt
config:
  # Inherit base configuration
  key_claim_name: "kid"
  claims_to_verify: ["exp", "iss", "aud", "iat"]
  secret_is_base64: false
  run_on_preflight: true
  
  # Token sources
  header_names: ["Authorization"]
  uri_param_names: ["jwt"]
  cookie_names: ["auth-token"]
  
  # JWKS configuration for user service
  key_sets:
    - name: "cina-club-user-keys"
      issuer: "https://auth.cina.club"      # Expected issuer
      # JWKS URI pointing to user-core-service
      jwks_uri: "http://user-core-service.user-identity.svc.cluster.local:8080/.well-known/jwks.json"
      # Cache configuration for performance
      cache_ttl: 3600                      # Cache JWKS for 1 hour
      timeout: 10000                       # 10 second timeout for JWKS fetch
  
  # Clock skew and expiration
  maximum_expiration: 86400
  clock_skew_in_seconds: 30
  
  # Required claims
  claims_to_verify_existence: ["sub", "role", "scope"]
  
  # Anonymous access disabled
  anonymous: null

---
# JWT Validator Plugin for Admin Services (stricter validation)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jwt-validator-admin
  namespace: kong-system
  labels:
    app: kong-plugin
    component: authentication
    tier: admin
    security-level: high
  annotations:
    description: "Strict JWT validator for admin services with enhanced security"

plugin: jwt
config:
  # Stricter configuration for admin services
  key_claim_name: "kid"
  claims_to_verify: ["exp", "iss", "aud", "iat", "nbf"]  # Include 'nbf' for admin
  secret_is_base64: false
  run_on_preflight: true
  
  # Only accept tokens from Authorization header (more secure)
  header_names: ["Authorization"]
  uri_param_names: []                      # Don't accept JWT from URL parameters
  cookie_names: []                         # Don't accept JWT from cookies
  
  # JWKS configuration
  key_sets:
    - name: "cina-club-admin-keys"
      issuer: "https://admin.auth.cina.club"  # Different issuer for admin
      jwks_uri: "http://user-core-service.user-identity.svc.cluster.local:8080/.well-known/admin-jwks.json"
      cache_ttl: 1800                      # Cache for 30 minutes (shorter for admin)
      timeout: 5000                        # 5 second timeout (faster for admin)
  
  # Stricter time constraints for admin
  maximum_expiration: 14400                # Maximum 4 hours for admin tokens
  clock_skew_in_seconds: 10                # Only 10 seconds clock skew
  
  # Required claims with admin-specific requirements
  claims_to_verify_existence: 
    - "sub"
    - "role"
    - "scope"
    - "admin_level"                        # Admin-specific claim
    - "permission_set"                     # Admin permission set
  
  # No anonymous access for admin
  anonymous: null

---
# JWT Validator Plugin for Public Services (optional JWT)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jwt-validator-optional
  namespace: kong-system
  labels:
    app: kong-plugin
    component: authentication
    tier: public
    mode: optional
  annotations:
    description: "Optional JWT validator for public services (anonymous allowed)"

plugin: jwt
config:
  # Base JWT configuration
  key_claim_name: "kid"
  claims_to_verify: ["exp", "iss"]         # Minimal verification for public
  secret_is_base64: false
  run_on_preflight: false                  # Skip preflight for public services
  
  # All token sources accepted
  header_names: ["Authorization", "X-Auth-Token"]
  uri_param_names: ["jwt", "token"]
  cookie_names: ["auth-token", "session-token"]
  
  # JWKS configuration
  key_sets:
    - name: "cina-club-public-keys"
      issuer: "https://auth.cina.club"
      jwks_uri: "http://user-core-service.user-identity.svc.cluster.local:8080/.well-known/jwks.json"
      cache_ttl: 7200                      # Cache for 2 hours
      timeout: 15000                       # 15 second timeout
  
  # Relaxed time constraints
  maximum_expiration: 172800               # 48 hours for public tokens
  clock_skew_in_seconds: 60                # 1 minute clock skew
  
  # Minimal required claims
  claims_to_verify_existence: ["sub"]      # Only subject required
  
  # Allow anonymous access (key feature for public services)
  anonymous: "anonymous-user"              # Create anonymous consumer if no JWT 