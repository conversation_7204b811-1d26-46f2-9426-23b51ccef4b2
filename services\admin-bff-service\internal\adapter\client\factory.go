/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package client

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

// ClientFactory gRPC client factory
type ClientFactory struct {
	logger      *logrus.Logger
	connections map[string]*grpc.ClientConn
	clients     *Clients
}

// Clients contains all downstream service clients
type Clients struct {
	UserCore             *UserCoreClient
	Billing              *BillingClient
	Social               *SocialClient
	ContentModeration    *ContentModerationClient
	ServiceOffering      *ServiceOfferingClient
	Analytics            *AnalyticsClient
	NotificationDispatch *NotificationDispatchClient
}

// ServiceConfig service configuration
type ServiceConfig struct {
	Address        string
	ConnectTimeout time.Duration
	RequestTimeout time.Duration
}

// ClientConfig client configuration
type ClientConfig struct {
	Services map[string]ServiceConfig
}

// NewClientFactory creates a new client factory
func NewClientFactory(logger *logrus.Logger, config ClientConfig) (*ClientFactory, error) {
	factory := &ClientFactory{
		logger:      logger,
		connections: make(map[string]*grpc.ClientConn),
		clients:     &Clients{},
	}

	// Initialize all service connections
	if err := factory.initializeConnections(config); err != nil {
		return nil, fmt.Errorf("failed to initialize connections: %w", err)
	}

	// Initialize clients
	factory.initializeClients()

	return factory, nil
}

// initializeConnections initializes all gRPC connections
func (f *ClientFactory) initializeConnections(config ClientConfig) error {
	serviceNames := []string{
		"user-core-service",
		"billing-service",
		"social-service",
		"content-moderation-service",
		"service-offering-service",
		"analytics-service",
		"notification-dispatch-service",
	}

	for _, serviceName := range serviceNames {
		serviceConfig, exists := config.Services[serviceName]
		if !exists {
			f.logger.WithField("service", serviceName).Warn("Service configuration not found, using defaults")
			serviceConfig = ServiceConfig{
				Address:        fmt.Sprintf("%s:50051", serviceName),
				ConnectTimeout: 10 * time.Second,
				RequestTimeout: 30 * time.Second,
			}
		}

		conn, err := f.createConnection(serviceName, serviceConfig)
		if err != nil {
			f.logger.WithError(err).WithField("service", serviceName).Error("Failed to create gRPC connection")
			return fmt.Errorf("failed to connect to %s: %w", serviceName, err)
		}

		f.connections[serviceName] = conn
		f.logger.WithFields(logrus.Fields{
			"service": serviceName,
			"address": serviceConfig.Address,
		}).Info("gRPC connection established")
	}

	return nil
}

// createConnection creates a single gRPC connection
func (f *ClientFactory) createConnection(serviceName string, config ServiceConfig) (*grpc.ClientConn, error) {
	ctx, cancel := context.WithTimeout(context.Background(), config.ConnectTimeout)
	defer cancel()

	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()), // Should use TLS in production environment
		grpc.WithBlock(),
		grpc.WithUnaryInterceptor(f.unaryClientInterceptor),
	}

	conn, err := grpc.DialContext(ctx, config.Address, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to dial %s at %s: %w", serviceName, config.Address, err)
	}

	return conn, nil
}

// initializeClients initializes all clients
func (f *ClientFactory) initializeClients() {
	f.clients.UserCore = NewUserCoreClient(
		f.connections["user-core-service"],
		f.logger.WithField("client", "user-core"),
	)

	f.clients.Billing = NewBillingClient(
		f.connections["billing-service"],
		f.logger.WithField("client", "billing"),
	)

	f.clients.Social = NewSocialClient(
		f.connections["social-service"],
		f.logger.WithField("client", "social"),
	)

	f.clients.ContentModeration = NewContentModerationClient(
		f.connections["content-moderation-service"],
		f.logger.WithField("client", "content-moderation"),
	)

	f.clients.ServiceOffering = NewServiceOfferingClient(
		f.connections["service-offering-service"],
		f.logger.WithField("client", "service-offering"),
	)

	f.clients.Analytics = NewAnalyticsClient(
		f.connections["analytics-service"],
		f.logger.WithField("client", "analytics"),
	)

	f.clients.NotificationDispatch = NewNotificationDispatchClient(
		f.connections["notification-dispatch-service"],
		f.logger.WithField("client", "notification-dispatch"),
	)
}

// GetClients gets all clients
func (f *ClientFactory) GetClients() *Clients {
	return f.clients
}

// Close closes all connections
func (f *ClientFactory) Close() error {
	var errors []error

	for serviceName, conn := range f.connections {
		if err := conn.Close(); err != nil {
			f.logger.WithError(err).WithField("service", serviceName).Error("Failed to close gRPC connection")
			errors = append(errors, err)
		} else {
			f.logger.WithField("service", serviceName).Info("gRPC connection closed")
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to close %d connections", len(errors))
	}

	return nil
}

// unaryClientInterceptor client interceptor that automatically adds authentication and tracing information
func (f *ClientFactory) unaryClientInterceptor(
	ctx context.Context,
	method string,
	req, reply interface{},
	cc *grpc.ClientConn,
	invoker grpc.UnaryInvoker,
	opts ...grpc.CallOption,
) error {
	// Get actor information from context
	actorID := getActorIDFromContext(ctx)
	actorEmail := getActorEmailFromContext(ctx)
	actorRoles := getActorRolesFromContext(ctx)
	traceID := getTraceIDFromContext(ctx)

	// Create outbound context, add authentication and tracing information
	outCtx := metadata.AppendToOutgoingContext(ctx,
		"x-actor-user-id", actorID,
		"x-actor-email", actorEmail,
		"x-actor-roles", actorRoles,
		"x-trace-id", traceID,
		"x-service-id", "admin-bff-service",
	)

	// Log call start
	start := time.Now()
	f.logger.WithFields(logrus.Fields{
		"method":   method,
		"actor_id": actorID,
		"trace_id": traceID,
		"target":   cc.Target(),
	}).Debug("gRPC call started")

	// Execute call
	err := invoker(outCtx, method, req, reply, cc, opts...)

	// Log completion
	duration := time.Since(start)
	logFields := logrus.Fields{
		"method":      method,
		"actor_id":    actorID,
		"trace_id":    traceID,
		"target":      cc.Target(),
		"duration_ms": duration.Milliseconds(),
	}

	if err != nil {
		f.logger.WithError(err).WithFields(logFields).Error("gRPC call failed")
	} else {
		f.logger.WithFields(logFields).Debug("gRPC call completed")
	}

	return err
}

// Helper functions to extract information from context

func getActorIDFromContext(ctx context.Context) string {
	if value := ctx.Value("actor_id"); value != nil {
		if actorID, ok := value.(string); ok {
			return actorID
		}
	}
	return ""
}

func getActorEmailFromContext(ctx context.Context) string {
	if value := ctx.Value("actor_email"); value != nil {
		if actorEmail, ok := value.(string); ok {
			return actorEmail
		}
	}
	return ""
}

func getActorRolesFromContext(ctx context.Context) string {
	if value := ctx.Value("actor_roles"); value != nil {
		if actorRoles, ok := value.(string); ok {
			return actorRoles
		}
	}
	return ""
}

func getTraceIDFromContext(ctx context.Context) string {
	if value := ctx.Value("trace_id"); value != nil {
		if traceID, ok := value.(string); ok {
			return traceID
		}
	}
	return ""
}

// HealthCheck health check for all service connections
func (f *ClientFactory) HealthCheck(ctx context.Context) map[string]bool {
	results := make(map[string]bool)

	for serviceName, conn := range f.connections {
		// Simple connection state check
		state := conn.GetState()
		results[serviceName] = state.String() == "READY"

		f.logger.WithFields(logrus.Fields{
			"service": serviceName,
			"state":   state.String(),
			"healthy": results[serviceName],
		}).Debug("Service health check")
	}

	return results
}

// GetServiceConnection gets connection for specified service (for special purposes)
func (f *ClientFactory) GetServiceConnection(serviceName string) (*grpc.ClientConn, bool) {
	conn, exists := f.connections[serviceName]
	return conn, exists
}
