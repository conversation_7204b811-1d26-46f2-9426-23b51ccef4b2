/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package middleware

import (
	"context"

	"cina.club/pkg/errors"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Validator 定义验证接口
// 所有需要验证的 Protobuf 消息生成的 Go struct 都应实现此接口
type Validator interface {
	Validate() error
}

// ValidationInterceptor 创建一个请求体验证拦截器
// 自动检查传入的 gRPC 请求消息，如果消息实现了 Validator 接口，则进行验证
func ValidationInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 检查请求消息是否实现了 Validator 接口
		if validator, ok := req.(Validator); ok {
			if err := validator.Validate(); err != nil {
				// 将验证错误转换为 InvalidArgument 类型的 gRPC 错误
				appErr := errors.Wrap(err, errors.InvalidArgument, "request validation failed")
				return nil, status.Error(codes.InvalidArgument, appErr.Error())
			}
		}

		// 如果验证通过或请求不需要验证，继续处理
		return handler(ctx, req)
	}
}

// ValidationStreamInterceptor 创建一个流请求验证拦截器
func ValidationStreamInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		// 对于流请求，我们需要包装 ServerStream 来拦截 RecvMsg 方法
		wrappedStream := &validationServerStream{
			ServerStream: ss,
		}

		return handler(srv, wrappedStream)
	}
}

// validationServerStream 包装 grpc.ServerStream 以提供验证功能
type validationServerStream struct {
	grpc.ServerStream
}

// RecvMsg 拦截接收消息的方法，对每个接收到的消息进行验证
func (s *validationServerStream) RecvMsg(m interface{}) error {
	// 先调用原始的 RecvMsg 方法
	if err := s.ServerStream.RecvMsg(m); err != nil {
		return err
	}

	// 如果消息实现了 Validator 接口，进行验证
	if validator, ok := m.(Validator); ok {
		if err := validator.Validate(); err != nil {
			appErr := errors.Wrap(err, errors.InvalidArgument, "stream message validation failed")
			return status.Error(codes.InvalidArgument, appErr.Error())
		}
	}

	return nil
}

// ValidationOptions 验证拦截器的配置选项
type ValidationOptions struct {
	// SkipValidation 是否跳过验证（用于测试）
	SkipValidation bool
	// ExcludeMethods 要排除验证的方法列表
	ExcludeMethods []string
	// CustomValidator 自定义验证函数
	CustomValidator func(req interface{}) error
}

// DefaultValidationOptions 返回默认的验证选项
func DefaultValidationOptions() *ValidationOptions {
	return &ValidationOptions{
		SkipValidation: false,
		ExcludeMethods: []string{
			"/grpc.health.v1.Health/Check",
			"/grpc.health.v1.Health/Watch",
		},
	}
}

// ValidationInterceptorWithOptions 创建一个带有自定义选项的验证拦截器
func ValidationInterceptorWithOptions(opts *ValidationOptions) grpc.UnaryServerInterceptor {
	if opts == nil {
		opts = DefaultValidationOptions()
	}

	// 创建方法过滤器
	excludeMap := make(map[string]bool)
	for _, method := range opts.ExcludeMethods {
		excludeMap[method] = true
	}

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 检查是否需要排除此方法
		if excludeMap[info.FullMethod] {
			return handler(ctx, req)
		}

		// 如果配置为跳过验证，直接处理
		if opts.SkipValidation {
			return handler(ctx, req)
		}

		// 使用自定义验证器（如果提供）
		if opts.CustomValidator != nil {
			if err := opts.CustomValidator(req); err != nil {
				appErr := errors.Wrap(err, errors.InvalidArgument, "custom validation failed")
				return nil, status.Error(codes.InvalidArgument, appErr.Error())
			}
		} else {
			// 使用默认的验证逻辑
			if validator, ok := req.(Validator); ok {
				if err := validator.Validate(); err != nil {
					appErr := errors.Wrap(err, errors.InvalidArgument, "request validation failed")
					return nil, status.Error(codes.InvalidArgument, appErr.Error())
				}
			}
		}

		return handler(ctx, req)
	}
}

// ValidationResult 验证结果
type ValidationResult struct {
	// Valid 是否有效
	Valid bool
	// Errors 验证错误列表
	Errors []error
	// FieldErrors 字段级别的错误
	FieldErrors map[string]error
}

// NewValidationResult 创建新的验证结果
func NewValidationResult() *ValidationResult {
	return &ValidationResult{
		Valid:       true,
		Errors:      []error{},
		FieldErrors: make(map[string]error),
	}
}

// AddError 添加验证错误
func (r *ValidationResult) AddError(err error) {
	r.Valid = false
	r.Errors = append(r.Errors, err)
}

// AddFieldError 添加字段级别的错误
func (r *ValidationResult) AddFieldError(field string, err error) {
	r.Valid = false
	r.FieldErrors[field] = err
}

// HasErrors 检查是否有错误
func (r *ValidationResult) HasErrors() bool {
	return !r.Valid
}

// Error 返回所有错误的字符串表示
func (r *ValidationResult) Error() string {
	if r.Valid {
		return ""
	}

	var msg string
	for _, err := range r.Errors {
		if msg != "" {
			msg += "; "
		}
		msg += err.Error()
	}

	for field, err := range r.FieldErrors {
		if msg != "" {
			msg += "; "
		}
		msg += field + ": " + err.Error()
	}

	return msg
}

// DetailedValidator 详细验证器接口
// 提供更详细的验证结果
type DetailedValidator interface {
	ValidateDetailed() *ValidationResult
}

// DetailedValidationInterceptor 创建一个详细验证拦截器
func DetailedValidationInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 优先使用详细验证器
		if detailedValidator, ok := req.(DetailedValidator); ok {
			result := detailedValidator.ValidateDetailed()
			if result.HasErrors() {
				appErr := errors.New(errors.InvalidArgument, result.Error())
				return nil, status.Error(codes.InvalidArgument, appErr.Error())
			}
		} else if validator, ok := req.(Validator); ok {
			// 回退到简单验证器
			if err := validator.Validate(); err != nil {
				appErr := errors.Wrap(err, errors.InvalidArgument, "request validation failed")
				return nil, status.Error(codes.InvalidArgument, appErr.Error())
			}
		}

		return handler(ctx, req)
	}
}

// ValidateMessage 验证单个消息的辅助函数
func ValidateMessage(msg interface{}) error {
	if validator, ok := msg.(Validator); ok {
		return validator.Validate()
	}
	return nil
}

// ValidateMessageDetailed 详细验证单个消息的辅助函数
func ValidateMessageDetailed(msg interface{}) *ValidationResult {
	if detailedValidator, ok := msg.(DetailedValidator); ok {
		return detailedValidator.ValidateDetailed()
	}

	if validator, ok := msg.(Validator); ok {
		result := NewValidationResult()
		if err := validator.Validate(); err != nil {
			result.AddError(err)
		}
		return result
	}

	return NewValidationResult()
}
