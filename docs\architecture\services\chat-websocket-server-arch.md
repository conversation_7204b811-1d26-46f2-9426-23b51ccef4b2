好的，遵照您的指示。我将为您生成一份专门针对 **`chat-websocket-server`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`chat-websocket-server-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将特别关注**海量并发连接管理、低延迟消息路由、多实例间的状态同步、与`chat-api-service`的协同，以及如何实现高可用和水平扩展**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `chat-websocket-server` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `chat-websocket-server-srs.md` (v2.0)
**核心架构**: 有状态的实时网关 + Redis驱动的状态同步

## 1. 概述

`chat-websocket-server` 是CINA.CLUB平台的“**实时神经网络**”。它是一个**有状态**的服务，专注于处理海量并发的WebSocket长连接和消息的低延迟转发。其核心挑战在于：
1.  **海量连接管理 (C10M问题)**: 如何高效地管理数百万级的并发WebSocket连接，并最小化每个连接的内存开销。
2.  **低延迟广播**: 如何将一条消息近乎实时地、可靠地广播给一个房间内的所有在线成员，即使这些成员连接在不同的服务器实例上。
3.  **多实例状态同步**: 如何在多个无状态的`chat-websocket-server`实例之间，同步用户的在线状态和房间订阅关系。
4.  **高可用与容错**: 单个服务器实例的故障不应影响整体服务，客户端应能快速、无缝地重连。
5.  **与API层的解耦**: 如何在保证实时性能的同时，将消息的持久化、权限校验等非实时逻辑卸载给`chat-api-service`。

本架构设计通过采用**Go的并发模型**、**Redis作为分布式状态后端和消息总线**、以及**事件驱动**的持久化卸载，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (多实例协同与状态同步)

```mermaid
graph LR
    subgraph "Clients"
        ClientA
        ClientB
        ClientC
    end

    subgraph "Load Balancer (L4/L7)"
        LB
    end

    subgraph "Chat-Websocket-Server-1 (Node 1)"
        style "Chat-Websocket-Server-1 (Node 1)" fill:#e0f7fa
        WSS1[WebSocket Server]
        Hub1[Hub]
        ClientConnA("Conn A")
        ClientConnB("Conn B")
    end
    
    subgraph "Chat-Websocket-Server-2 (Node 2)"
        style "Chat-Websocket-Server-2 (Node 2)" fill:#e0f7fa
        WSS2[WebSocket Server]
        Hub2[Hub]
        ClientConnC("Conn C")
    end
    
    subgraph "后端基础设施"
        style "后端基础设施" fill:#f3e5f5
        RedisPubSub[Redis (Pub/Sub)]
        RedisState[Redis (State Store)]
        Kafka
    end

    ClientA --> LB --> WSS1
    ClientB --> LB --> WSS1
    ClientC --> LB --> WSS2
    
    WSS1 -- "Manages" --> ClientConnA & ClientConnB
    WSS2 -- "Manages" --> ClientConnC
    
    ClientConnA --> Hub1
    ClientConnB --> Hub1
    ClientConnC --> Hub2
    
    Hub1 -- "4. PUBLISH to Redis Channel" --> RedisPubSub
    Hub1 -- "2. Update Presence/Room Info" --> RedisState
    Hub1 -- "3. Publish to Kafka (Persistence)" --> Kafka

    RedisPubSub -- "5. Fan-out Message" --> Hub1
    RedisPubSub -- "5. Fan-out Message" --> Hub2
    
    Hub1 -- "6. Push to local clients" --> ClientConnA & ClientConnB
    Hub2 -- "6. Push to local clients" --> ClientConnC
    
    Hub1 & Hub2 -- "1. Read Presence/Room Info" --> RedisState
```

### 2.2 最终目录结构 (`services/chat-websocket-server/`)

```
chat-websocket-server/
├── cmd/server/
│   └── main.go                 # 服务启动入口
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   └── chat_api_client.go # 调用chat-api-service进行权限校验
│   │   ├── event/
│   │   │   └── producer.go     # Kafka生产者, 用于消息持久化
│   │   ├── redis/
│   │   │   ├── presence_store.go # 在线状态存储实现
│   │   │   ├── room_store.go     # 房间订阅关系存储实现
│   │   │   └── broadcaster.go  # ✨ Redis Pub/Sub广播器实现 ✨
│   │   └── transport/
│   │       └── websocket/
│   │           └── handler.go  # 处理HTTP Upgrade请求和WebSocket连接
│   ├── application/
│   │   ├── port/
│   │   │   ├── broadcaster.go
│   │   │   ├── presence_store.go
│   │   │   └── room_store.go
│   │   └── service/            # ✨ 核心应用服务 ✨
│   │       └── hub.go          # Hub: 管理本实例所有客户端和房间
│   └── domain/
│       ├── model/
│       │   ├── client.go       # ✨ Client: 封装单个WebSocket连接 ✨
│       │   └── room.go         # Room: 逻辑上的房间, 只存在于Hub中
│       └── protocol/
│           └── protocol.go     # 定义WebSocket事件信封和序列化逻辑
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Real-time Primitives)

这是本服务实时处理逻辑的核心对象。

*   **`domain/model/client.go`**: **这是对单个WebSocket连接的封装**。
    *   **`Client` struct**:
        *   `hub *Hub`: 指向其所属的Hub。
        *   `conn *websocket.Conn`: 底层的WebSocket连接。
        *   `send chan []byte`: 一个带缓冲的channel，用作**发送队列**。Hub通过这个channel将消息发给客户端。
        *   `userID`, `deviceID`: 认证后获得的用户和设备ID。
    *   **`readPump()` method**:
        *   每个`Client`在一个**独立的goroutine**中运行此方法。
        *   它在一个无限循环中，从WebSocket连接中读取消息。
        *   读取到消息后，反序列化，并将其传递给`Hub`进行处理。
        *   处理连接关闭、ping/pong等。
    *   **`writePump()` method**:
        *   每个`Client`在另一个**独立的goroutine**中运行此方法。
        *   它在一个无限循环中，从`send` channel中读取待发送的消息。
        *   将消息写入WebSocket连接。
        *   **设计决策**: 读写分离到不同的goroutine，并使用channel作为队列，可以防止慢速客户端的写入操作阻塞读取操作，这是高性能WebSocket服务器的经典模式。

*   **`domain/model/room.go`**:
    *   `Room` struct:
        *   `roomID string`
        *   `clients map[*Client]bool`: **只包含连接在本服务器实例上的**、属于该房间的客户端集合。

### 3.2 `application/` - 应用层 (The Hub & Logic)

*   **`application/service/hub.go`**: **这是单个服务器实例的“中央调度室”**。
    *   **`Hub` struct**:
        *   `clients map[*Client]bool`: 本实例上的所有客户端。
        *   `rooms map[string]*Room`: 本实例上所有有活跃成员的房间。
        *   `broadcaster port.Broadcaster`: 广播器接口。
        *   `presenceStore port.PresenceStore`: 在线状态存储接口。
        *   `register chan *Client`: 客户端注册channel。
        *   `unregister chan *Client`: 客户端注销channel。
        *   `broadcast chan Message`: 待广播的消息channel。
    *   **`Run()` method**: Hub的核心事件循环，在一个**独立的goroutine**中运行。
        *   使用`select`监听所有channel。
        *   **处理注册/注销**:
            1.  当新客户端连接时，将其加入`clients` map。
            2.  调用`presenceStore.SetOnline()`更新其在Redis中的全局在线状态。
            3.  从`roomStore`获取该用户加入的所有房间，并将其`Client`对象添加到本地的`Room`中。
        *   **处理消息广播**:
            1.  从`Client.readPump`接收到一条新消息。
            2.  **权限校验**: 调用`chat-api-service`客户端，检查用户是否有权在该房间发言。
            3.  **持久化卸载**: 将消息发布到Kafka，供`chat-api-service`持久化。
            4.  **跨实例广播**: 调用`broadcaster.Broadcast(roomID, message)`。广播器会将消息发布到Redis Pub/Sub。
*   **`application/port/`**: 定义了`Broadcaster`, `PresenceStore`, `RoomStore`等接口，将Hub的逻辑与Redis的具体实现解耦。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/redis/`**: **这是实现多实例协同和状态同步的关键**。
    *   `broadcaster.go`:
        *   实现`port.Broadcaster`接口。
        *   `Broadcast`方法将消息`PUBLISH`到Redis的`chat_room:{roomId}`频道。
        *   在服务启动时，它会`SUBSCRIBE`所有需要的频道，并在一个goroutine中监听消息。收到消息后，将其推入`Hub`的`broadcast` channel，由Hub分发给本地的客户端。
    *   `presence_store.go`:
        *   实现`port.PresenceStore`接口。
        *   使用Redis的`SET`和`DEL`来管理`presence:{userId}`键，以标记用户在线状态。
        *   使用Redis的`SADD`/`SREM`/`SMEMBERS`来管理`connections:{userId}`集合，存储一个用户的所有`connectionId`。
    *   `room_store.go`:
        *   实现`port.RoomStore`接口。
        *   使用Redis的Set来管理每个房间的成员列表 (`room_members:{roomId}`)，以便在用户上线时知道需要将其加入哪些本地房间。
*   **`adapter/transport/websocket/`**:
    *   `handler.go`:
        *   一个标准的HTTP Handler，处理`/ws`端点。
        *   使用`websocket.Upgrader`将HTTP连接升级为WebSocket连接。
        *   升级成功后，创建一个`domain.Client`实例，并将其注册到`Hub`中 (`hub.register <- client`)。
        *   启动该`Client`的`readPump`和`writePump` goroutine。
*   **`adapter/event/`**:
    *   `producer.go`: 封装`pkg/messaging`的`Producer`，用于向Kafka发送`RawChatMessageEvent`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`chat-websocket-server`：
1.  **Actor模型思想**: 每个`Client`都是一个独立的、带消息队列的Actor，在自己的goroutine中运行，通过`Hub`进行通信，实现了高度并发。
2.  **Hub作为本地协调者**: 每个服务器实例一个`Hub`，管理本实例内的所有客户端和房间，处理本地消息分发。
3.  **Redis作为全局总线**:
    *   **Pub/Sub**: 用于跨实例的消息广播，是实现水平扩展的关键。
    *   **State Store**: 用于存储全局的、跨实例共享的状态（如谁在线、谁在哪个房间），使得服务器实例本身可以是**半有状态**的（只在内存中维护本地连接，而全局状态在Redis）。
4.  **动静分离**: 严格遵守边界，将所有非实时、持久化的操作（存储消息、管理群成员）通过Kafka或gRPC API委托给`chat-api-service`，保证了本服务的极低延迟。

这种架构确保了`chat-websocket-server`能够以极高的性能和可靠性，处理海量的并发长连接，为CINA.CLUB平台提供了世界一流的实时通信能力。