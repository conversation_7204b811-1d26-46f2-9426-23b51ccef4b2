/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:30:00
Modified: 2025-01-21 10:30:00
*/

package config_test

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"cina.club/pkg/config"

	"github.com/go-playground/validator/v10"
)

// ServiceConfig represents a typical microservice configuration
type ServiceConfig struct {
	Service  ServiceInfo    `mapstructure:"service" validate:"required"`
	Server   ServerConfig   `mapstructure:"server" validate:"required"`
	Database DatabaseConfig `mapstructure:"database" validate:"required"`
	Logger   LoggerConfig   `mapstructure:"logger"`
	Features FeatureConfig  `mapstructure:"features"`
}

type ServiceInfo struct {
	Name    string `mapstructure:"name" validate:"required" default:"my-service"`
	Version string `mapstructure:"version" validate:"required" default:"1.0.0"`
	Mode    string `mapstructure:"mode" validate:"servicemode" default:"development"`
}

type ServerConfig struct {
	Port         int           `mapstructure:"port" validate:"gte=1024,lte=65535" default:"8080"`
	Host         string        `mapstructure:"host" default:"localhost"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout" default:"30s"`
	WriteTimeout time.Duration `mapstructure:"write_timeout" default:"30s"`
	TLS          TLSConfig     `mapstructure:"tls"`
}

type TLSConfig struct {
	Enabled  bool   `mapstructure:"enabled" default:"false"`
	CertFile string `mapstructure:"cert_file"`
	KeyFile  string `mapstructure:"key_file"`
}

type DatabaseConfig struct {
	Postgres PostgresConfig `mapstructure:"postgres" validate:"required"`
	Redis    RedisConfig    `mapstructure:"redis"`
}

type PostgresConfig struct {
	DSN             string        `mapstructure:"dsn" validate:"required,dsn"`
	MaxOpenConns    int           `mapstructure:"max_open_conns" validate:"gte=1" default:"25"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns" validate:"gte=0" default:"25"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime" default:"5m"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time" default:"5m"`
}

type RedisConfig struct {
	Addr         string        `mapstructure:"addr" default:"localhost:6379"`
	Password     string        `mapstructure:"password"`
	DB           int           `mapstructure:"db" validate:"gte=0" default:"0"`
	PoolSize     int           `mapstructure:"pool_size" validate:"gte=1" default:"10"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout" default:"5s"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout" default:"3s"`
	WriteTimeout time.Duration `mapstructure:"write_timeout" default:"3s"`
}

type LoggerConfig struct {
	Level      string `mapstructure:"level" validate:"loglevel" default:"info"`
	Format     string `mapstructure:"format" validate:"oneof=json text" default:"json"`
	Output     string `mapstructure:"output" validate:"oneof=stdout stderr file" default:"stdout"`
	Filename   string `mapstructure:"filename"`
	MaxSize    int    `mapstructure:"max_size" validate:"gte=1" default:"100"`
	MaxBackups int    `mapstructure:"max_backups" validate:"gte=0" default:"3"`
	MaxAge     int    `mapstructure:"max_age" validate:"gte=0" default:"28"`
}

type FeatureConfig struct {
	EnableMetrics     bool `mapstructure:"enable_metrics" default:"true"`
	EnableTracing     bool `mapstructure:"enable_tracing" default:"true"`
	EnableProfiling   bool `mapstructure:"enable_profiling" default:"false"`
	EnableHealthcheck bool `mapstructure:"enable_healthcheck" default:"true"`
}

// ExampleLoadConfig demonstrates basic configuration loading
func ExampleLoadConfig() {
	// Create a sample configuration file
	configContent := `
service:
  name: "user-service"
  version: "1.2.0"
  mode: "production"

server:
  port: 8080
  host: "0.0.0.0"
  tls:
    enabled: true
    cert_file: "/etc/certs/server.crt"
    key_file: "/etc/certs/server.key"

database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/userdb"
    max_open_conns: 25
    max_idle_conns: 5
  redis:
    addr: "localhost:6379"
    db: 0
    pool_size: 10

logger:
  level: "info"
  format: "json"
  output: "stdout"

features:
  enable_metrics: true
  enable_tracing: true
  enable_profiling: false
`

	// Write to a temporary file
	tmpDir := os.TempDir()
	configPath := filepath.Join(tmpDir, "example_config.yaml")
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		fmt.Printf("Error writing config file: %v\n", err)
		return
	}
	defer os.Remove(configPath)

	// Load the configuration
	var cfg ServiceConfig
	err = config.LoadConfig(configPath, &cfg)
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		return
	}

	fmt.Printf("Service: %s v%s (%s)\n", cfg.Service.Name, cfg.Service.Version, cfg.Service.Mode)
	fmt.Printf("Server: %s:%d\n", cfg.Server.Host, cfg.Server.Port)
	fmt.Printf("Database: %s\n", cfg.Database.Postgres.DSN)
	fmt.Printf("Redis: %s\n", cfg.Database.Redis.Addr)
	fmt.Printf("Logger: %s/%s\n", cfg.Logger.Level, cfg.Logger.Format)

	// Output:
	// Service: user-service v1.2.0 (production)
	// Server: 0.0.0.0:8080
	// Database: postgres://user:pass@localhost:5432/userdb
	// Redis: localhost:6379
	// Logger: info/json
}

// ExampleLoadConfig_withEnvironmentVariables demonstrates environment variable overrides
func ExampleLoadConfig_withEnvironmentVariables() {
	// Set environment variables
	os.Setenv("CINA_SERVICE_MODE", "staging")
	os.Setenv("CINA_SERVER_PORT", "9090")
	os.Setenv("CINA_DATABASE_POSTGRES_MAX_OPEN_CONNS", "50")
	os.Setenv("CINA_LOGGER_LEVEL", "debug")

	defer func() {
		os.Unsetenv("CINA_SERVICE_MODE")
		os.Unsetenv("CINA_SERVER_PORT")
		os.Unsetenv("CINA_DATABASE_POSTGRES_MAX_OPEN_CONNS")
		os.Unsetenv("CINA_LOGGER_LEVEL")
	}()

	// Create a minimal configuration file
	configContent := `
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/userdb"
`

	tmpDir := os.TempDir()
	configPath := filepath.Join(tmpDir, "minimal_config.yaml")
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		fmt.Printf("Error writing config file: %v\n", err)
		return
	}
	defer os.Remove(configPath)

	// Load configuration - environment variables will override defaults
	var cfg ServiceConfig
	err = config.LoadConfig(configPath, &cfg)
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		return
	}

	fmt.Printf("Service Mode: %s (from env)\n", cfg.Service.Mode)
	fmt.Printf("Server Port: %d (from env)\n", cfg.Server.Port)
	fmt.Printf("DB Max Conns: %d (from env)\n", cfg.Database.Postgres.MaxOpenConns)
	fmt.Printf("Log Level: %s (from env)\n", cfg.Logger.Level)

	// Output:
	// Service Mode: staging (from env)
	// Server Port: 9090 (from env)
	// DB Max Conns: 50 (from env)
	// Log Level: debug (from env)
}

// ExampleMustLoadConfig demonstrates the must-load pattern
func ExampleMustLoadConfig() {
	// Create a valid configuration file
	configContent := `
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/userdb"
`

	tmpDir := os.TempDir()
	configPath := filepath.Join(tmpDir, "must_load_config.yaml")
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		fmt.Printf("Error writing config file: %v\n", err)
		return
	}
	defer os.Remove(configPath)

	// Load configuration - will panic if it fails
	var cfg ServiceConfig
	config.MustLoadConfig(configPath, &cfg)

	fmt.Printf("Configuration loaded successfully!\n")
	fmt.Printf("Service Name: %s (default)\n", cfg.Service.Name)
	fmt.Printf("Server Port: %d (default)\n", cfg.Server.Port)

	// Output:
	// Configuration loaded successfully!
	// Service Name: my-service (default)
	// Server Port: 8080 (default)
}

// ExampleLoader_LoadWithInfo demonstrates loading with detailed information
func ExampleLoader_LoadWithInfo() {
	// Set an environment variable
	os.Setenv("CINA_SERVER_PORT", "8888")
	defer os.Unsetenv("CINA_SERVER_PORT")

	// Create a configuration file
	configContent := `
database:
  postgres:
    dsn: "postgres://user:pass@localhost:5432/userdb"
`

	tmpDir := os.TempDir()
	configPath := filepath.Join(tmpDir, "info_config.yaml")
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		fmt.Printf("Error writing config file: %v\n", err)
		return
	}
	defer os.Remove(configPath)

	// Load with detailed information
	loader := config.NewLoader()
	var cfg ServiceConfig
	info, err := loader.LoadWithInfo(configPath, &cfg)
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		return
	}

	fmt.Printf("Config file: %s\n", info.ConfigFile)
	fmt.Printf("Environment variables found: %d\n", len(info.EnvVarsFound))
	fmt.Printf("Server port (from env): %d\n", cfg.Server.Port)

	// Output:
	// Config file: [temp_path]/info_config.yaml
	// Environment variables found: 1
	// Server port (from env): 8888
}

// ExampleAddCustomValidation demonstrates adding custom validation rules
func ExampleAddCustomValidation() {
	// Register a custom validation rule
	err := config.AddCustomValidation("apikey", func(fl validator.FieldLevel) bool {
		value := fl.Field().String()
		// API key should be at least 32 characters
		return len(value) >= 32
	})
	if err != nil {
		fmt.Printf("Error registering validation: %v\n", err)
		return
	}

	// Configuration with custom validation
	type APIConfig struct {
		APIKey string `validate:"required,apikey"`
	}

	// Test with valid API key
	validConfig := APIConfig{
		APIKey: "this-is-a-valid-api-key-with-enough-length",
	}

	err = config.ValidateStruct(validConfig)
	if err != nil {
		fmt.Printf("Validation error: %v\n", err)
		return
	}

	fmt.Println("Valid API key configuration passed validation")

	// Test with invalid API key
	invalidConfig := APIConfig{
		APIKey: "short",
	}

	err = config.ValidateStruct(invalidConfig)
	if err != nil {
		fmt.Printf("Expected validation error: %v\n", err)
	}

	// Output:
	// Valid API key configuration passed validation
	// Expected validation error: configuration validation failed:
	//   - field 'APIKey' failed validation rule 'apikey' (got: short)
}

// ExampleServiceMain demonstrates typical usage in a service main function
func Example_serviceMain() {
	// This is how you would typically use pkg/config in a service's main function

	configPath := "./config.yaml"

	// Load configuration with fail-fast behavior
	var cfg ServiceConfig
	if err := config.LoadConfig(configPath, &cfg); err != nil {
		// In a real service, you would log.Fatal here
		fmt.Printf("Failed to load configuration: %v\n", err)
		return
	}

	// Use the configuration to initialize other components
	fmt.Printf("Starting %s v%s in %s mode\n", cfg.Service.Name, cfg.Service.Version, cfg.Service.Mode)
	fmt.Printf("Server will listen on %s:%d\n", cfg.Server.Host, cfg.Server.Port)

	if cfg.Features.EnableMetrics {
		fmt.Println("Metrics enabled")
	}

	if cfg.Features.EnableTracing {
		fmt.Println("Tracing enabled")
	}

	// Example output (when config file exists):
	// Starting my-service v1.0.0 in development mode
	// Server will listen on localhost:8080
	// Metrics enabled
	// Tracing enabled
}
