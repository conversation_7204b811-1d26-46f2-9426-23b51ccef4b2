/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:45:00
 * Modified: 2025-01-23 16:45:00
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Typography,
  Space,
  Button,
  Table,
  Tag,
  Progress,
  List,
  Avatar,
  Tabs,
  Alert,
  Tooltip,
  Badge,
} from 'antd';
import {
  MessageOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  TrendingUpOutlined,
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  DownloadOutlined,
  EyeOutlined,
  HeartOutlined,
  ShareAltOutlined,
  CommentOutlined,
} from '@ant-design/icons';
import { Line, Column, Pie, Area } from '@ant-design/charts';
import { useQuery } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

interface ChatMetrics {
  totalMessages: number;
  activeUsers: number;
  averageResponseTime: number;
  messageGrowth: number;
  userGrowth: number;
  engagementRate: number;
  popularChannels: string[];
  peakHours: number[];
}

interface MessageVolumeData {
  date: string;
  messages: number;
  users: number;
  channels: number;
}

interface ChannelAnalytics {
  id: string;
  name: string;
  type: 'public' | 'private' | 'direct';
  messageCount: number;
  memberCount: number;
  averageResponseTime: number;
  engagementScore: number;
  lastActivity: string;
}

interface UserEngagement {
  id: string;
  name: string;
  avatar?: string;
  messageCount: number;
  reactionsGiven: number;
  reactionsReceived: number;
  averageSessionTime: number;
  lastSeen: string;
  engagementScore: number;
}

interface PopularContent {
  id: string;
  content: string;
  author: string;
  type: 'message' | 'image' | 'file' | 'link';
  reactions: number;
  replies: number;
  shares: number;
  timestamp: string;
  channel: string;
}

const ChatAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [dateRange, setDateRange] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data queries
  const { data: chatMetrics } = useQuery({
    queryKey: ['chat-metrics', timeRange],
    queryFn: async (): Promise<ChatMetrics> => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        totalMessages: 145678,
        activeUsers: 8934,
        averageResponseTime: 2.3,
        messageGrowth: 15.6,
        userGrowth: 8.9,
        engagementRate: 78.5,
        popularChannels: ['general', 'tech-talk', 'random', 'announcements'],
        peakHours: [9, 10, 11, 14, 15, 16, 20, 21]
      };
    }
  });

  const { data: messageVolumeData } = useQuery({
    queryKey: ['message-volume', timeRange],
    queryFn: async (): Promise<MessageVolumeData[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      const data: MessageVolumeData[] = [];
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      for (let i = 0; i < 30; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        data.push({
          date: date.toISOString().split('T')[0],
          messages: Math.floor(Math.random() * 5000) + 2000,
          users: Math.floor(Math.random() * 500) + 200,
          channels: Math.floor(Math.random() * 50) + 20
        });
      }
      return data;
    }
  });

  const { data: channelAnalytics } = useQuery({
    queryKey: ['channel-analytics'],
    queryFn: async (): Promise<ChannelAnalytics[]> => {
      await new Promise(resolve => setTimeout(resolve, 700));
      return [
        {
          id: '1',
          name: 'general',
          type: 'public',
          messageCount: 45678,
          memberCount: 1234,
          averageResponseTime: 1.8,
          engagementScore: 92,
          lastActivity: '2025-01-23T16:30:00Z'
        },
        {
          id: '2',
          name: 'tech-talk',
          type: 'public',
          messageCount: 23456,
          memberCount: 567,
          averageResponseTime: 3.2,
          engagementScore: 87,
          lastActivity: '2025-01-23T16:25:00Z'
        },
        {
          id: '3',
          name: 'announcements',
          type: 'public',
          messageCount: 1234,
          memberCount: 2345,
          averageResponseTime: 0.5,
          engagementScore: 65,
          lastActivity: '2025-01-23T15:00:00Z'
        }
      ];
    }
  });

  const { data: userEngagement } = useQuery({
    queryKey: ['user-engagement'],
    queryFn: async (): Promise<UserEngagement[]> => {
      await new Promise(resolve => setTimeout(resolve, 500));
      return [
        {
          id: '1',
          name: 'Alice Johnson',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Alice',
          messageCount: 2456,
          reactionsGiven: 1234,
          reactionsReceived: 3456,
          averageSessionTime: 45.6,
          lastSeen: '2025-01-23T16:30:00Z',
          engagementScore: 95
        },
        {
          id: '2',
          name: 'Bob Smith',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Bob',
          messageCount: 1876,
          reactionsGiven: 987,
          reactionsReceived: 2345,
          averageSessionTime: 38.2,
          lastSeen: '2025-01-23T16:15:00Z',
          engagementScore: 88
        }
      ];
    }
  });

  const { data: popularContent } = useQuery({
    queryKey: ['popular-content'],
    queryFn: async (): Promise<PopularContent[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return [
        {
          id: '1',
          content: 'Check out this amazing new feature we just launched! 🚀',
          author: 'Product Team',
          type: 'message',
          reactions: 156,
          replies: 45,
          shares: 23,
          timestamp: '2025-01-23T14:30:00Z',
          channel: 'announcements'
        },
        {
          id: '2',
          content: 'New architecture diagram for our microservices',
          author: 'Tech Lead',
          type: 'image',
          reactions: 89,
          replies: 34,
          shares: 12,
          timestamp: '2025-01-23T13:15:00Z',
          channel: 'tech-talk'
        }
      ];
    }
  });

  // Chart configurations
  const messageVolumeConfig = {
    data: messageVolumeData || [],
    xField: 'date',
    yField: 'messages',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 3,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Messages',
        value: datum.messages.toLocaleString()
      })
    }
  };

  const userActivityConfig = {
    data: messageVolumeData || [],
    xField: 'date',
    yField: 'users',
    smooth: true,
    color: '#52c41a',
    point: {
      size: 3,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Active Users',
        value: datum.users.toLocaleString()
      })
    }
  };

  const channelColumns: ColumnsType<ChannelAnalytics> = [
    {
      title: 'Channel',
      key: 'channel',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{
            width: '24px',
            height: '24px',
            borderRadius: '4px',
            backgroundColor: record.type === 'public' ? '#52c41a' : '#1890ff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '8px'
          }}>
            <Text style={{ color: 'white', fontSize: '12px' }}>
              {record.type === 'public' ? '#' : record.type === 'private' ? '🔒' : '@'}
            </Text>
          </div>
          <div>
            <Text strong>{record.name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.type}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: 'Messages',
      dataIndex: 'messageCount',
      key: 'messageCount',
      render: (count: number) => count.toLocaleString(),
      sorter: (a, b) => a.messageCount - b.messageCount
    },
    {
      title: 'Members',
      dataIndex: 'memberCount',
      key: 'memberCount',
      render: (count: number) => count.toLocaleString(),
      sorter: (a, b) => a.memberCount - b.memberCount
    },
    {
      title: 'Avg Response Time',
      dataIndex: 'averageResponseTime',
      key: 'averageResponseTime',
      render: (time: number) => `${time}min`,
      sorter: (a, b) => a.averageResponseTime - b.averageResponseTime
    },
    {
      title: 'Engagement',
      dataIndex: 'engagementScore',
      key: 'engagementScore',
      render: (score: number) => (
        <div>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={score > 80 ? '#52c41a' : score > 60 ? '#faad14' : '#f5222d'}
          />
          <Text style={{ fontSize: '12px' }}>{score}%</Text>
        </div>
      ),
      sorter: (a, b) => a.engagementScore - b.engagementScore
    },
    {
      title: 'Last Activity',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      render: (time: string) => {
        const diff = Date.now() - new Date(time).getTime();
        const minutes = Math.floor(diff / 60000);
        return <Text type="secondary">{minutes}m ago</Text>;
      }
    }
  ];

  const userEngagementColumns: ColumnsType<UserEngagement> = [
    {
      title: 'User',
      key: 'user',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.avatar} size="small" style={{ marginRight: '8px' }} />
          <div>
            <Text strong>{record.name}</Text>
            <br />
            <Badge 
              status={Date.now() - new Date(record.lastSeen).getTime() < 300000 ? 'success' : 'default'}
              text={Date.now() - new Date(record.lastSeen).getTime() < 300000 ? 'Online' : 'Offline'}
            />
          </div>
        </div>
      )
    },
    {
      title: 'Messages',
      dataIndex: 'messageCount',
      key: 'messageCount',
      render: (count: number) => count.toLocaleString(),
      sorter: (a, b) => a.messageCount - b.messageCount
    },
    {
      title: 'Reactions',
      key: 'reactions',
      render: (_, record) => (
        <div>
          <Text>Given: {record.reactionsGiven}</Text>
          <br />
          <Text type="secondary">Received: {record.reactionsReceived}</Text>
        </div>
      )
    },
    {
      title: 'Avg Session',
      dataIndex: 'averageSessionTime',
      key: 'averageSessionTime',
      render: (time: number) => `${time}min`,
      sorter: (a, b) => a.averageSessionTime - b.averageSessionTime
    },
    {
      title: 'Engagement Score',
      dataIndex: 'engagementScore',
      key: 'engagementScore',
      render: (score: number) => (
        <div style={{ textAlign: 'center' }}>
          <Progress 
            type="circle" 
            percent={score} 
            size={40}
            strokeColor={score > 80 ? '#52c41a' : score > 60 ? '#faad14' : '#f5222d'}
          />
        </div>
      ),
      sorter: (a, b) => a.engagementScore - b.engagementScore
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2}>
              <MessageOutlined /> Chat Analytics
            </Title>
            <Paragraph type="secondary">
              Analyze chat activity, user engagement, and conversation insights.
            </Paragraph>
          </div>
          <Space>
            <Select
              value={timeRange}
              onChange={setTimeRange}
              style={{ width: 120 }}
            >
              <Option value="1d">Last 24h</Option>
              <Option value="7d">Last 7 days</Option>
              <Option value="30d">Last 30 days</Option>
              <Option value="90d">Last 90 days</Option>
            </Select>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
            />
            <Button icon={<DownloadOutlined />}>
              Export Report
            </Button>
          </Space>
        </div>
      </div>

      {/* Key Metrics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Messages"
              value={chatMetrics?.totalMessages || 0}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                <TrendingUpOutlined style={{ color: '#52c41a' }} /> 
                {chatMetrics?.messageGrowth || 0}% vs last period
              </Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={chatMetrics?.activeUsers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                <TrendingUpOutlined style={{ color: '#52c41a' }} /> 
                {chatMetrics?.userGrowth || 0}% vs last period
              </Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Avg Response Time"
              value={chatMetrics?.averageResponseTime || 0}
              suffix="min"
              precision={1}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">Average time to respond</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Engagement Rate"
              value={chatMetrics?.engagementRate || 0}
              suffix="%"
              precision={1}
              prefix={<HeartOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">User engagement score</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Activity Overview */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={16}>
          <Card title="Message Volume Over Time" extra={<LineChartOutlined />}>
            <Line {...messageVolumeConfig} height={250} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="Popular Channels">
            <List
              dataSource={chatMetrics?.popularChannels || []}
              renderItem={(channel, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '6px',
                        backgroundColor: '#1890ff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <Text style={{ color: 'white', fontWeight: 'bold' }}>
                          #{index + 1}
                        </Text>
                      </div>
                    }
                    title={`#${channel}`}
                    description="High activity"
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* Detailed Analytics */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Channel Analytics" key="channels">
          <Card title="Channel Performance">
            <Table
              columns={channelColumns}
              dataSource={channelAnalytics || []}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </TabPane>

        <TabPane tab="User Engagement" key="users">
          <Card title="Top Engaged Users">
            <Table
              columns={userEngagementColumns}
              dataSource={userEngagement || []}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </TabPane>

        <TabPane tab="Popular Content" key="content">
          <Card title="Most Engaging Content">
            <List
              dataSource={popularContent || []}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Space key="stats">
                      <Tooltip title="Reactions">
                        <span><HeartOutlined /> {item.reactions}</span>
                      </Tooltip>
                      <Tooltip title="Replies">
                        <span><CommentOutlined /> {item.replies}</span>
                      </Tooltip>
                      <Tooltip title="Shares">
                        <span><ShareAltOutlined /> {item.shares}</span>
                      </Tooltip>
                    </Space>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={
                      <div>
                        <Text strong>{item.author}</Text>
                        <Tag color="blue" style={{ marginLeft: '8px' }}>
                          #{item.channel}
                        </Tag>
                        <Text type="secondary" style={{ marginLeft: '8px', fontSize: '12px' }}>
                          {new Date(item.timestamp).toLocaleString()}
                        </Text>
                      </div>
                    }
                    description={
                      <div>
                        <Paragraph ellipsis={{ rows: 2 }}>
                          {item.content}
                        </Paragraph>
                        <Tag>{item.type}</Tag>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </TabPane>

        <TabPane tab="Activity Patterns" key="patterns">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="User Activity Timeline">
                <Line {...userActivityConfig} height={300} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="Peak Hours">
                <div style={{ padding: '20px' }}>
                  {Array.from({ length: 24 }, (_, hour) => (
                    <div 
                      key={hour}
                      style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        marginBottom: '8px' 
                      }}
                    >
                      <Text style={{ width: '40px', fontSize: '12px' }}>
                        {hour.toString().padStart(2, '0')}:00
                      </Text>
                      <Progress
                        percent={chatMetrics?.peakHours.includes(hour) ? 
                          Math.random() * 40 + 60 : Math.random() * 30 + 10}
                        showInfo={false}
                        strokeColor={chatMetrics?.peakHours.includes(hour) ? '#52c41a' : '#d9d9d9'}
                        style={{ flex: 1, marginLeft: '12px' }}
                      />
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ChatAnalytics; 