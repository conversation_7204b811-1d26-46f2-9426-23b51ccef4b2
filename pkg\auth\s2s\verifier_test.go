/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 12:00:00
Modified: 2025-01-21 12:00:00
*/

package s2s

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"strings"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Test key pair for testing
const (
	testPrivateKeyPEM = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEA4f5wg5l2hKsTeNem/V41fGnJm6gOdrj8ym3rFkEjWT2btYNi
mDpAjjqJPhXWBNaW2ZOKt/+FKhE8Qz3IJ8r0J3JhQCWYrSWfHM5p3vQK2DDWSOP
5V7ZdSRuEaGjCfxIJpU6Xs8FqaLHxnJKU6XnKTQXqjSmQxgTTGKb0kWsHUPCjCCj
JqVNqXN6UVzGp8oYqnQlTGiJOp2kJ0n8Sk+Q5CtP1HfKCOOYr4j4YKTG3V1+7sKj
2VqM5WVZ9RrzBJy+AKtHPwWhJk6YklBDfuGr6HwKWUaWCWUOBWs1GhFnLa3HqTK3
JqJ1EhWOv6hJd1G0fVJcFqXvKdTIKKJKCyKmOwIDAQABAoIBAFjUKegYR9Xr1TnU
Xbj2s85c8q8rXJ1K4B2V7lBgcBxNL8Gm8gLqHsFz95vQNgWiVL8Nj4i2QDIFTLp
R0lfPv2zJ7pEj8OzQK1rTLQ0gKVx5CXLzFHGGKL0jKAzZJEP8MqF1JLSJRKKTbN
DlJQzxKLlKjNZ5aGGJ7R8E6wQxE9KHbxLxrG6q5KWEJQ8n2Jm1xEjDmvYFCxiQ1
t7XcYQAKNUGJj8XxQgWfKGLu8SqS9l7Lx6kNr8XGQ8W0tKfx4tFwKhCJJJtKJu
TSDJGRLHGZJmKQGGQFRJt7KKG1wn7KHf8rJ7H8GKjKLqJKVHJa5vFQEzHqVbGl
KtR7L9tPXFKxTEsCgYEA8JRFVE7JqzJ3TbZKJJ1Y3jKR8IKJV9W1sKSKs5q4V8Q
OZVLHj2pKJKG3J8JJ7ZJ8QJ7KJ5p5V8KVxJJ8KVxJJkW3Jkj3K8J5QJK1K5QJ
v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+
v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+
v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+v+
wIBAqAqKAQA6cBGiogBAAAAIBAAKCAQEA4f5wg5l2hKsTeNem/V41fGnJm6gOdr
-----END RSA PRIVATE KEY-----`

	testPublicKeyPEM = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41
fGnJm6gOdrj8ym3rFkEjWT2btYNimDpAjjqJPhXWBNaW2ZOKt/+FKhE8Qz3IJ8r0
J3JhQCWYrSWfHM5p3vQK2DDWSOP5V7ZdSRuEaGjCfxIJpU6Xs8FqaLHxnJKU6XnK
TQXqjSmQxgTTGKb0kWsHUPCjCCjJqVNqXN6UVzGp8oYqnQlTGiJOp2kJ0n8Sk+Q5
CtP1HfKCOOYr4j4YKTG3V1+7sKj2VqM5WVZ9RrzBJy+AKtHPwWhJk6YklBDfuGr6
HwKWUaWCWUOBWs1GhFnLa3HqTK3JqJ1EhWOv6hJd1G0fVJcFqXvKdTIKKJKCyKmO
wIDAQAB
-----END PUBLIC KEY-----`
)

// generateTestKeyPair generates a new RSA key pair for testing
func generateTestKeyPair() (*rsa.PrivateKey, string, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, "", err
	}

	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		return nil, "", err
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return privateKey, string(publicKeyPEM), nil
}

func TestStaticKeyProvider(t *testing.T) {
	privateKey, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	pemKeys := map[string]string{
		"test-service":  publicKeyPEM,
		"other-service": publicKeyPEM, // Reuse the same key for simplicity
	}

	provider, err := NewStaticKeyProvider(pemKeys)
	if err != nil {
		t.Fatalf("Failed to create static key provider: %v", err)
	}

	ctx := context.Background()

	// Test getting existing key
	key, err := provider.GetPublicKey(ctx, "test-service")
	if err != nil {
		t.Errorf("Failed to get public key for test-service: %v", err)
	}
	if key == nil {
		t.Error("Expected public key, got nil")
	}

	// Verify it's the correct key
	if rsaKey, ok := key.(*rsa.PublicKey); ok {
		if rsaKey.N.Cmp(privateKey.PublicKey.N) != 0 {
			t.Error("Retrieved public key doesn't match generated key")
		}
	} else {
		t.Error("Expected RSA public key")
	}

	// Test getting non-existent key
	_, err = provider.GetPublicKey(ctx, "non-existent")
	if err == nil {
		t.Error("Expected error for non-existent service")
	}
	if err != nil && !strings.Contains(err.Error(), "service not found") {
		t.Errorf("Expected service not found error, got: %v", err)
	}
}

func TestStaticKeyProvider_UpdateRemoveKey(t *testing.T) {
	_, publicKeyPEM1, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate first test key pair: %v", err)
	}

	privateKey2, publicKeyPEM2, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate second test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM1,
	})
	if err != nil {
		t.Fatalf("Failed to create static key provider: %v", err)
	}

	ctx := context.Background()

	// Test initial key
	key1, err := provider.GetPublicKey(ctx, "test-service")
	if err != nil {
		t.Fatalf("Failed to get initial public key: %v", err)
	}

	// Update key
	err = provider.UpdateKey("test-service", publicKeyPEM2)
	if err != nil {
		t.Fatalf("Failed to update key: %v", err)
	}

	// Verify key was updated
	key2, err := provider.GetPublicKey(ctx, "test-service")
	if err != nil {
		t.Fatalf("Failed to get updated public key: %v", err)
	}

	// Verify it's a different key
	rsaKey1 := key1.(*rsa.PublicKey)
	rsaKey2 := key2.(*rsa.PublicKey)
	if rsaKey1.N.Cmp(rsaKey2.N) == 0 {
		t.Error("Expected different key after update")
	}

	// Verify it's the correct new key
	if rsaKey2.N.Cmp(privateKey2.PublicKey.N) != 0 {
		t.Error("Updated key doesn't match second generated key")
	}

	// Remove key
	provider.RemoveKey("test-service")

	// Verify key was removed
	_, err = provider.GetPublicKey(ctx, "test-service")
	if err == nil {
		t.Error("Expected error after removing key")
	}
}

func TestStaticKeyProvider_InvalidKey(t *testing.T) {
	invalidKeys := map[string]string{
		"invalid-service": "invalid-pem-data",
	}

	_, err := NewStaticKeyProvider(invalidKeys)
	if err == nil {
		t.Error("Expected error for invalid PEM data")
	}
}

func TestNewVerifier(t *testing.T) {
	_, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM,
	})
	if err != nil {
		t.Fatalf("Failed to create key provider: %v", err)
	}

	verifier := NewVerifier(provider)
	if verifier == nil {
		t.Fatal("NewVerifier returned nil")
	}

	if verifier.clockSkew != 5*time.Second {
		t.Errorf("Expected default clock skew of 5s, got %v", verifier.clockSkew)
	}

	// Test with custom clock skew
	customSkew := 10 * time.Second
	verifier2 := NewVerifierWithClockSkew(provider, customSkew)
	if verifier2.clockSkew != customSkew {
		t.Errorf("Expected custom clock skew of %v, got %v", customSkew, verifier2.clockSkew)
	}
}

func TestVerifier_VerifyToken(t *testing.T) {
	privateKey, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM,
	})
	if err != nil {
		t.Fatalf("Failed to create key provider: %v", err)
	}

	verifier := NewVerifier(provider)
	ctx := context.Background()

	// Generate valid token
	tokenString, err := GenerateS2SToken(privateKey, "test-service", "target-service", 5*time.Minute)
	if err != nil {
		t.Fatalf("Failed to generate test token: %v", err)
	}

	// Test valid token verification
	claims, err := verifier.VerifyToken(ctx, tokenString, "target-service")
	if err != nil {
		t.Errorf("Failed to verify valid token: %v", err)
	}

	if claims == nil {
		t.Fatal("Expected claims, got nil")
	}

	if claims.Service != "test-service" {
		t.Errorf("Expected service 'test-service', got '%s'", claims.Service)
	}

	if claims.Issuer != "test-service" {
		t.Errorf("Expected issuer 'test-service', got '%s'", claims.Issuer)
	}

	if len(claims.Audience) != 1 || claims.Audience[0] != "target-service" {
		t.Errorf("Expected audience ['target-service'], got %v", claims.Audience)
	}
}

func TestVerifier_VerifyToken_InvalidCases(t *testing.T) {
	privateKey, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM,
	})
	if err != nil {
		t.Fatalf("Failed to create key provider: %v", err)
	}

	verifier := NewVerifier(provider)
	ctx := context.Background()

	tests := []struct {
		name        string
		setupToken  func() string
		expectedAud string
		expectError bool
	}{
		{
			name: "expired token",
			setupToken: func() string {
				token, _ := GenerateS2SToken(privateKey, "test-service", "target-service", -5*time.Minute)
				return token
			},
			expectedAud: "target-service",
			expectError: true,
		},
		{
			name: "wrong audience",
			setupToken: func() string {
				token, _ := GenerateS2SToken(privateKey, "test-service", "wrong-audience", 5*time.Minute)
				return token
			},
			expectedAud: "target-service",
			expectError: true,
		},
		{
			name: "invalid token format",
			setupToken: func() string {
				return "invalid.token.format"
			},
			expectedAud: "target-service",
			expectError: true,
		},
		{
			name: "empty token",
			setupToken: func() string {
				return ""
			},
			expectedAud: "target-service",
			expectError: true,
		},
		{
			name: "token signed by unknown service",
			setupToken: func() string {
				// Generate token with unknown service
				unknownKey, _ := rsa.GenerateKey(rand.Reader, 2048)
				token, _ := GenerateS2SToken(unknownKey, "unknown-service", "target-service", 5*time.Minute)
				return token
			},
			expectedAud: "target-service",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token := tt.setupToken()
			claims, err := verifier.VerifyToken(ctx, token, tt.expectedAud)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
				}
				if claims != nil {
					t.Error("Expected nil claims on error")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if claims == nil {
					t.Error("Expected claims but got nil")
				}
			}
		})
	}
}

func TestVerifier_ClockSkew(t *testing.T) {
	privateKey, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM,
	})
	if err != nil {
		t.Fatalf("Failed to create key provider: %v", err)
	}

	// Create verifier with 10 second clock skew
	verifier := NewVerifierWithClockSkew(provider, 10*time.Second)
	ctx := context.Background()

	// Test with token that will expire in the future (should always pass)
	now := time.Now()
	claims := &S2SClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    "test-service",
			Subject:   "test-service",
			Audience:  []string{"target-service"},
			ExpiresAt: jwt.NewNumericDate(now.Add(5 * time.Minute)), // Valid for 5 minutes
			NotBefore: jwt.NewNumericDate(now.Add(-1 * time.Minute)),
			IssuedAt:  jwt.NewNumericDate(now.Add(-1 * time.Minute)),
		},
		Service: "test-service",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	tokenString, err := token.SignedString(privateKey)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// Should succeed with valid token
	_, err = verifier.VerifyToken(ctx, tokenString, "target-service")
	if err != nil {
		t.Errorf("Valid token verification failed: %v", err)
	}

	// Test with token that expired way beyond clock skew tolerance
	claims.ExpiresAt = jwt.NewNumericDate(now.Add(-15 * time.Second)) // Expired 15 seconds ago (beyond 10s tolerance)
	token = jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	tokenString, err = token.SignedString(privateKey)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// Should fail because it's beyond clock skew tolerance
	_, err = verifier.VerifyToken(ctx, tokenString, "target-service")
	if err == nil {
		t.Error("Expected token verification to fail for token expired beyond clock skew")
	}
}

func TestGenerateS2SToken(t *testing.T) {
	privateKey, _, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	serviceName := "test-service"
	audience := "target-service"
	ttl := 5 * time.Minute

	tokenString, err := GenerateS2SToken(privateKey, serviceName, audience, ttl)
	if err != nil {
		t.Fatalf("Failed to generate S2S token: %v", err)
	}

	if tokenString == "" {
		t.Error("Generated token string is empty")
	}

	// Parse the token to verify its structure
	token, err := jwt.ParseWithClaims(tokenString, &S2SClaims{}, func(token *jwt.Token) (interface{}, error) {
		return &privateKey.PublicKey, nil
	})

	if err != nil {
		t.Fatalf("Failed to parse generated token: %v", err)
	}

	claims, ok := token.Claims.(*S2SClaims)
	if !ok {
		t.Fatal("Failed to extract S2S claims")
	}

	if claims.Service != serviceName {
		t.Errorf("Expected service '%s', got '%s'", serviceName, claims.Service)
	}

	if claims.Issuer != serviceName {
		t.Errorf("Expected issuer '%s', got '%s'", serviceName, claims.Issuer)
	}

	if len(claims.Audience) != 1 || claims.Audience[0] != audience {
		t.Errorf("Expected audience ['%s'], got %v", audience, claims.Audience)
	}

	// Verify TTL is approximately correct (within 1 second)
	expectedExpiry := time.Now().Add(ttl)
	actualExpiry := claims.ExpiresAt.Time
	diff := actualExpiry.Sub(expectedExpiry)
	if diff < -time.Second || diff > time.Second {
		t.Errorf("Token expiry time is incorrect. Expected ~%v, got %v (diff: %v)", expectedExpiry, actualExpiry, diff)
	}
}

func TestS2SClaims_Validation(t *testing.T) {
	privateKey, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM,
	})
	if err != nil {
		t.Fatalf("Failed to create key provider: %v", err)
	}

	verifier := NewVerifier(provider)
	ctx := context.Background()

	// Test token without service claim
	now := time.Now()
	claims := &S2SClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    "test-service",
			Subject:   "test-service",
			Audience:  []string{"target-service"},
			ExpiresAt: jwt.NewNumericDate(now.Add(5 * time.Minute)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
		// Service field is empty
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	tokenString, err := token.SignedString(privateKey)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	_, err = verifier.VerifyToken(ctx, tokenString, "target-service")
	if err == nil {
		t.Error("Expected error for token without service claim")
	}

	// Test token without audience
	claims.Service = "test-service"
	claims.Audience = nil

	token = jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	tokenString, err = token.SignedString(privateKey)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	_, err = verifier.VerifyToken(ctx, tokenString, "target-service")
	if err == nil {
		t.Error("Expected error for token without audience when audience validation is required")
	}

	// Test with empty audience string (should pass)
	_, err = verifier.VerifyToken(ctx, tokenString, "")
	if err != nil {
		t.Errorf("Unexpected error when not validating audience: %v", err)
	}
}

// Benchmark tests
func BenchmarkVerifier_VerifyToken(b *testing.B) {
	privateKey, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		b.Fatalf("Failed to generate test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM,
	})
	if err != nil {
		b.Fatalf("Failed to create key provider: %v", err)
	}

	verifier := NewVerifier(provider)
	ctx := context.Background()

	tokenString, err := GenerateS2SToken(privateKey, "test-service", "target-service", 5*time.Minute)
	if err != nil {
		b.Fatalf("Failed to generate test token: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := verifier.VerifyToken(ctx, tokenString, "target-service")
		if err != nil {
			b.Fatalf("Token verification failed: %v", err)
		}
	}
}

func BenchmarkGenerateS2SToken(b *testing.B) {
	privateKey, _, err := generateTestKeyPair()
	if err != nil {
		b.Fatalf("Failed to generate test key pair: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := GenerateS2SToken(privateKey, "test-service", "target-service", 5*time.Minute)
		if err != nil {
			b.Fatalf("Token generation failed: %v", err)
		}
	}
}

func BenchmarkStaticKeyProvider_GetPublicKey(b *testing.B) {
	_, publicKeyPEM, err := generateTestKeyPair()
	if err != nil {
		b.Fatalf("Failed to generate test key pair: %v", err)
	}

	provider, err := NewStaticKeyProvider(map[string]string{
		"test-service": publicKeyPEM,
	})
	if err != nil {
		b.Fatalf("Failed to create key provider: %v", err)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := provider.GetPublicKey(ctx, "test-service")
		if err != nil {
			b.Fatalf("Get public key failed: %v", err)
		}
	}
}
