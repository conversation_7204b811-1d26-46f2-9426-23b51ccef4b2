/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

// Package interceptor provides gRPC interceptors for authentication and authorization.
package interceptor

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"cina.club/pkg/auth/jwks"
)

const (
	authorizationHeader = "authorization"
	bearerPrefix        = "Bearer "
)

var (
	ErrMissingToken  = errors.New("missing authorization token")
	ErrInvalidToken  = errors.New("invalid authorization token")
	ErrTokenExpired  = errors.New("token expired")
	ErrInvalidClaims = errors.New("invalid token claims")
)

// UserClaims represents the JWT claims for a user token
type UserClaims struct {
	jwt.RegisteredClaims
	UserID string   `json:"user_id"`
	Email  string   `json:"email,omitempty"`
	Roles  []string `json:"roles,omitempty"`
}

// UserJWTInterceptor provides JWT validation for user tokens
type UserJWTInterceptor struct {
	jwksClient      *jwks.Client
	allowedIssuers  []string
	allowedAudience []string
	clockSkew       time.Duration
}

// NewUserJWTInterceptor creates a new user JWT interceptor
func NewUserJWTInterceptor(jwksClient *jwks.Client, allowedIssuers, allowedAudience []string, clockSkew time.Duration) *UserJWTInterceptor {
	return &UserJWTInterceptor{
		jwksClient:      jwksClient,
		allowedIssuers:  allowedIssuers,
		allowedAudience: allowedAudience,
		clockSkew:       clockSkew,
	}
}

// UnaryInterceptor returns a unary server interceptor for JWT validation
func (i *UserJWTInterceptor) UnaryInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		newCtx, err := i.validateAndInjectUser(ctx)
		if err != nil {
			return nil, status.Errorf(codes.Unauthenticated, "authentication failed: %v", err)
		}

		return handler(newCtx, req)
	}
}

// StreamInterceptor returns a stream server interceptor for JWT validation
func (i *UserJWTInterceptor) StreamInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		newCtx, err := i.validateAndInjectUser(ss.Context())
		if err != nil {
			return status.Errorf(codes.Unauthenticated, "authentication failed: %v", err)
		}

		// Create a new server stream with the updated context
		wrapped := &wrappedServerStream{ServerStream: ss, ctx: newCtx}
		return handler(srv, wrapped)
	}
}

// validateAndInjectUser validates the JWT token and injects user info into context
func (i *UserJWTInterceptor) validateAndInjectUser(ctx context.Context) (context.Context, error) {
	// Extract token from metadata
	tokenString, err := i.extractToken(ctx)
	if err != nil {
		return nil, err
	}

	// Parse and validate JWT
	claims, err := i.validateToken(tokenString)
	if err != nil {
		return nil, err
	}

	// Create authenticated user from claims
	user := &AuthenticatedUser{
		ID:    claims.UserID,
		Email: claims.Email,
		Roles: claims.Roles,
	}

	// Inject user into context
	return NewContextWithUser(ctx, user), nil
}

// extractToken extracts the JWT token from gRPC metadata
func (i *UserJWTInterceptor) extractToken(ctx context.Context) (string, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "", ErrMissingToken
	}

	authHeaders := md.Get(authorizationHeader)
	if len(authHeaders) == 0 {
		return "", ErrMissingToken
	}

	authHeader := authHeaders[0]
	if !strings.HasPrefix(authHeader, bearerPrefix) {
		return "", ErrInvalidToken
	}

	return strings.TrimPrefix(authHeader, bearerPrefix), nil
}

// validateToken validates the JWT token and returns the claims
func (i *UserJWTInterceptor) validateToken(tokenString string) (*UserClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &UserClaims{}, i.jwksClient.KeyFunc())
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrInvalidToken, err)
	}

	claims, ok := token.Claims.(*UserClaims)
	if !ok || !token.Valid {
		return nil, ErrInvalidToken
	}

	// Validate standard claims
	if err := i.validateStandardClaims(claims); err != nil {
		return nil, err
	}

	// Validate custom claims
	if claims.UserID == "" {
		return nil, fmt.Errorf("%w: missing user_id", ErrInvalidClaims)
	}

	return claims, nil
}

// validateStandardClaims validates standard JWT claims
func (i *UserJWTInterceptor) validateStandardClaims(claims *UserClaims) error {
	now := time.Now()

	// Check expiration with clock skew tolerance
	if claims.ExpiresAt != nil && now.Add(-i.clockSkew).After(claims.ExpiresAt.Time) {
		return ErrTokenExpired
	}

	// Check not before with clock skew tolerance
	if claims.NotBefore != nil && now.Add(i.clockSkew).Before(claims.NotBefore.Time) {
		return ErrInvalidToken
	}

	// Check issuer if configured
	if len(i.allowedIssuers) > 0 {
		issuerValid := false
		for _, allowedIssuer := range i.allowedIssuers {
			if claims.Issuer == allowedIssuer {
				issuerValid = true
				break
			}
		}
		if !issuerValid {
			return fmt.Errorf("%w: invalid issuer", ErrInvalidClaims)
		}
	}

	// Check audience if configured
	if len(i.allowedAudience) > 0 {
		audienceValid := false
		for _, tokenAud := range claims.Audience {
			for _, allowedAud := range i.allowedAudience {
				if tokenAud == allowedAud {
					audienceValid = true
					break
				}
			}
			if audienceValid {
				break
			}
		}
		if !audienceValid {
			return fmt.Errorf("%w: invalid audience", ErrInvalidClaims)
		}
	}

	return nil
}

// wrappedServerStream wraps grpc.ServerStream to provide a new context
type wrappedServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

// Context returns the wrapped context
func (w *wrappedServerStream) Context() context.Context {
	return w.ctx
}
