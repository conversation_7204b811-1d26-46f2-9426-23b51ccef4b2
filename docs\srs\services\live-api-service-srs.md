好的，遵照您的指示。我将为您生成一份专门针对 **`live-api-service`** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义这个新增的、作为直播功能业务核心的服务的功能、接口、数据模型、性能和可靠性需求，作为直播系统开发的权威依据。

---
### CINA.CLUB - `live-api-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [多媒体/实时通信产品负责人]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台引入实时视频直播功能，需要一个核心服务来管理直播的业务逻辑和生命周期。`live-api-service` 的目的在于构建这样一个**直播业务管理中心**。它负责处理直播间的创建、状态管理（开播、下播）、元数据配置、观众管理以及与平台其他服务的业务协同。本服务是整个直播功能的“大脑”，连接了主播、观众、媒体网关和平台的其他核心能力。

#### 1.2. 服务范围
本服务 **负责**:
*   **直播间管理**:
    *   提供API供主播创建、配置和管理其直播间。
    *   管理直播间的元数据（标题、封面、分类、公告等）。
*   **直播生命周期管理**:
    *   处理主播的**开播(Start Live)**和**下播(End Live)**请求。
    *   维护直播间**精确的状态机** (`IDLE`, `PREPARING`, `LIVE`, `ENDED`, `ARCHIVED`)。
    *   监听来自媒体服务器的推流中断等事件，并自动更新直播状态。
*   **观众与权限管理**:
    *   处理观众进入/离开直播间的逻辑。
    *   管理付费直播间、密码房、粉丝专属直播间等访问权限。
*   **与媒体网关协同**:
    *   在开播时，调用`live-gateway-service`生成安全的推流地址。
    *   在观众加入时，调用`live-gateway-service`获取可用的拉流地址。
*   **录制与回放协调**:
    *   在直播结束后，触发录制文件的处理流程。
    *   与`short-video-service`协同，将录制好的视频转换为可供点播的回放。
*   **数据聚合与分析**: 聚合直播的核心数据，如峰值在线人数(PCU)、总观看人次(PV)、收到的礼物价值等。

本服务 **不负责**:
*   **媒体流的处理与分发**: 由底层的**实时流媒体服务器 (如SRS)** 负责。
*   **推拉流的鉴权与地址生成**: 由`live-gateway-service`负责，本服务只做调用。
*   **直播间的实时互动消息 (弹幕、礼物)**: 由`live-im-service`负责。
*   **支付与计费**: 由`billing-service`和`payment-service`负责，本服务只在需要时调用它们。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用**: (主要) 主播端和观众端通过API Gateway与本服务交互。
*   **`live-gateway-service`**: (被本服务调用) 获取推拉流地址。
*   **`live-im-service`**: (调用本服务) 查询直播间状态、发送礼物时可能需要与本服务协同。
*   **`content-moderation-service`**: (被本服务调用) 定期提交直播截图进行审核。
*   **CINA.CLUB管理后台**: 管理员通过后台管理直播间、查看数据、处理违规。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`live-api-service` 是CINA.CLUB直播生态的**业务逻辑核心**和**状态管理器**。它不直接处理媒体流，而是作为直播流程的**中央协调者**，编排`live-gateway`（媒体）、`live-im`（互动）、`billing`（计费）等多个专业服务，共同完成一场完整的直播。其设计的核心在于一个健壮、精确的直播状态机。

#### 2.2. 主要功能概述
*   完整的直播间生命周期与状态机管理。
*   灵活的直播间元数据与权限配置。
*   作为协调者，与媒体网关和互动服务进行解耦协同。
*   支持直播录制与转点播回放的流程。
*   直播核心业务数据的聚合与分析。

---

### 3. 核心流程图

#### 3.1. 主播开播流程

```mermaid
sequenceDiagram
    participant Client as "主播端App"
    participant LiveAPIService as LAS
    participant LiveGatewayService as LGS
    participant UserCoreService as UCS
    participant DB as PostgreSQL
    
    Client->>LAS: 1. POST /my/live-rooms/start
    LAS->>UCS: 2. 权限检查 (e.g., 是否有开播权限)
    UCS-->>LAS: (权限OK)
    
    LAS->>DB: 3. 开启事务, 更新LiveRoom状态为 PREPARING
    
    LAS->>LGS: 4. RequestPushURL(room_id)
    LGS-->>LAS: (推流地址, 推流码)
    
    LAS->>DB: 5. 更新LiveRoom推流信息, 状态变为 LIVE, 提交事务
    
    LAS->>Kafka: 6. Publish LiveStartedEvent
    
    LAS-->>Client: 7. 200 OK (返回推流地址和推流码)
    
    Client->>MediaServer: 8. 开始推流 (RTMP/SRT)
```

#### 3.2. 媒体服务器推流中断回调流程
```mermaid
sequenceDiagram
    participant MediaServer as "SRS/LiveKit"
    participant LiveGatewayService as LGS
    participant LiveAPIService as LAS
    
    MediaServer->>LGS: 1. [Webhook] on_unpublish (stream_key)
    LGS->>LAS: 2. [gRPC/Event] NotifyStreamInterrupted(room_id)
    LAS->>DB: 3. 更新LiveRoom状态为 INTERRUPTED
    LAS->>LAS: 4. 启动一个定时器 (e.g., 5分钟后强制下播)
    LAS->>NotificationService: 5. 向主播发送“推流已中断”的通知
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 直播间管理
*   **FR4.1.1 (创建与配置)**: 主播必须能创建自己的直播间，并配置以下元数据：
    *   `title`, `description`, `cover_image_url`, `category_id`。
*   **FR4.1.2 (访问控制)**: 直播间必须支持多种访问模式：
    *   `PUBLIC`: 公开。
    *   `PASSWORD_PROTECTED`: 需要密码。
    *   `PAID`: 需要通过`billing-service`付费。
    *   `FANS_ONLY`: 仅关注主播的用户可进入。
*   **FR4.1.3 (直播公告)**: 主播在直播期间可以发布和修改直播间公告。

#### 4.2. 直播生命周期与状态机
*   **FR4.2.1 (状态机)**: 直播间必须有一个精确的状态机：
    *   `IDLE` (未开播) -> `PREPARING` (准备推流) -> `LIVE` (正在直播)
    *   `LIVE` -> `INTERRUPTED` (推流中断) -> `LIVE` (推流恢复)
    *   `LIVE`/`INTERRUPTED` -> `ENDED` (直播结束)
    *   `ENDED` -> `ARCHIVING` (录制转码中) -> `ARCHIVED` (已归档，可回放)
*   **FR4.2.2 (自动状态转换)**:
    *   **必须**能处理来自`live-gateway-service`的`on_publish`（推流成功）和`on_unpublish`（推流中断）回调，并相应地更新状态为`LIVE`或`INTERRUPTED`。
    *   在`INTERRUPTED`状态下，如果超过可配置的时间（如5分钟）仍未恢复推流，系统**必须**自动将直播状态变为`ENDED`。

#### 4.3. 录制与回放
*   **FR4.3.1 (录制触发)**: 在直播状态从`LIVE`变为`ENDED`时，系统**必须**能触发底层的媒体服务器停止录制。
*   **FR4.3.2 (转点播流程)**:
    *   直播结束后，系统**必须**发布一个`LiveRecordingReadyEvent`，其中包含录制文件在对象存储中的`fileKey`。
    *   **`short-video-service`**将消费此事件，并将录制文件导入其处理管道，进行转码和削减（如去掉开头结尾），最终生成一个可点播的回放视频。
    *   处理完成后，`short-video-service`会通知本服务，本服务将回放视频ID与直播场次关联起来。

#### 4.4. 数据聚合与监控
*   **FR4.4.1 (实时数据)**: 系统需要通过`live-im-service`和`live-gateway-service`的定期汇报（或查询），更新直播间的**当前在线人数**。
*   **FR4.4.2 (最终数据)**: 直播结束后，系统需要聚合整场直播的总数据，包括**峰值在线人数(PCU)**、**总观看人次(PV)**、**新增粉丝数**、**总礼物价值**等，并生成场次报告。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部gRPC API接口
*   **Package**: `hina.v1.live`
*   **核心RPC**:
    *   `rpc CreateLiveRoom(CreateLiveRoomRequest) returns (LiveRoom)`
    *   `rpc GetLiveRoomDetails(GetLiveRoomRequest) returns (LiveRoom)`
    *   `rpc StartLive(StartLiveRequest) returns (StartLiveResponse)`: 返回推流信息。
    *   `rpc EndLive(EndLiveRequest) returns (google.protobuf.Empty)`
    *   `rpc JoinLiveRoom(JoinLiveRoomRequest) returns (JoinLiveRoomResponse)`: 返回拉流地址和IM服务器地址。
    *   `rpc GetLiveRoomList(GetLiveRoomListRequest) returns (GetLiveRoomListResponse)`: 获取直播列表。

#### 5.2. 消息队列事件契约
*   **出站 (发布)**: `LiveStartedEvent`, `LiveEndedEvent`, `LiveRecordingReadyEvent`。
*   **入站 (消费)**: `RecordingProcessedEvent` (from `short-video-service`), `LiveStreamModerationAlertEvent` (from `content-moderation-service`)。

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`live_rooms`**:
    *   `id (PK)`, `owner_user_id (FK)`, `title`, `description`, `cover_image_url`, `category_id`.
    *   `status (VARCHAR, INDEX)`: `IDLE`, `LIVE`, `ENDED`, ...
    *   `access_control_config (JSONB)`: 存储密码、付费信息等。
    *   `stream_key (VARCHAR, UNIQUE, INDEX)`: 推流码。
*   **`live_sessions`**: (记录每一场直播)
    *   `id (PK)`, `room_id (FK)`, `start_time`, `end_time`.
    *   `pcu (INT)`, `total_pv (BIGINT)`, `total_gift_value (DECIMAL)`.
    *   `playback_video_id (UUID, nullable)`: 关联到`short-video-service`的回放视频ID。
*   **`live_stream_segments`**: (可选) 记录推流中断和恢复的时间段。

#### 6.2. 缓存 (Redis)
*   `live:room_status:{roomId}`: 缓存直播间的实时状态。
*   `live:online_count:{roomId}`: 缓存直播间的当前在线人数。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与延迟)**:
    *   开播/加入直播间的API P99延迟应 `< 500ms`。
    *   状态更新（如处理推流中断回调）必须在秒级内完成。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。
    *   **状态一致性**: 必须保证本服务的直播状态与底层媒体服务器的实际流状态最终一致。
*   **NFR7.3 (可扩展性)**: 服务应为无状态（或软状态，状态在Redis中），易于水平扩展。

---

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。需要处理事务和结构化元数据。
*   **核心协同**: 与`live-gateway-service`的交互是本服务的核心。两者之间的接口必须清晰、可靠。`live-gateway-service`负责“物理层”的媒体交互，本服务负责“逻辑层”的业务状态。

---
这份SRS为`live-api-service`的设计和实现提供了坚实、全面的指导。通过将业务逻辑、媒体网关和实时互动进行清晰地分层和解耦，CINA.CLUB平台能够构建一个功能强大、稳定可靠且可扩展的直播生态系统。