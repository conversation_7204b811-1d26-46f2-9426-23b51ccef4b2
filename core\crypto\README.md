# Cina.Club Cryptography Module

## Overview

The Cryptography module provides robust end-to-end encryption (E2EE) capabilities for the Cina.Club platform, ensuring secure data transmission and storage across multiple services and platforms.

## Key Features

- **End-to-End Encryption**: Secure data protection using advanced cryptographic algorithms
- **Flexible Key Management**: Comprehensive key vault with rotation and security mechanisms
- **Cross-Platform Support**: Compatible with mobile, web, and WASM environments
- **Dependency Injection**: Seamless integration with the core module's service architecture

## Encryption Mechanisms

### Supported Algorithms
- **Symmetric Encryption**: 
  - AES-256-GCM
  - ChaCha20-Poly1305
- **Key Derivation**: 
  - Argon2id (Resistant to side-channel attacks)

### Key Features
- Secure random key generation
- Key ID derivation
- Metadata encryption support
- Secure memory wiping

## Security Principles

1. **Type Safety**: Compile-time type checking for cryptographic operations
2. **Immutability**: Prevent direct key modifications
3. **Minimal Attack Surface**: Reduce potential security vulnerabilities
4. **Entropy Validation**: Ensure high-quality randomness in key generation

## Usage Examples

### Password-Based Encryption
```go
engine := crypto.NewE2EEEngine()
data := []byte("Sensitive Information")
password := "StrongPassword123!"

// Encrypt
encryptedData, err := engine.EncryptWithPassword(data, password)

// Decrypt
decryptedData, err := engine.DecryptWithPassword(encryptedData, password)
```

### Key-Based Encryption
```go
engine := crypto.NewE2EEEngine()

// Generate a secure key
key, err := engine.GenerateKey()

// Encrypt with the key
encryptedData, err := engine.EncryptWithKey(data, key)

// Decrypt with the key
decryptedData, err := engine.DecryptWithKey(encryptedData, key)
```

## Configuration Options

```go
config := crypto.CryptoServiceConfig{
    MaxKeyRotations:   3,      // Limit key rotation cycles
    MinKeyLength:      32,     // Minimum acceptable key length
    EnhancedSecurity:  true,   // Enable additional security checks
}
```

## Dependency Injection

The module supports seamless integration with the core module's dependency injection container:

```go
registrar := crypto.NewCryptoServiceRegistrar(config)
err := registrar.Register(di.GlobalContainer)
err = registrar.InitializeCryptoProviders()
```

## Security Checks

- **Entropy Validation**: Ensures cryptographically secure random number generation
- **Key Rotation Tracking**: Prevents excessive key reuse
- **Secure Memory Management**: Wipes sensitive data after use

## Threat Mitigation

- Protection against known cryptographic attacks
- Constant-time comparison to prevent timing attacks
- Secure random number generation
- Key rotation and expiration mechanisms

## Compliance

- NIST SP 800-38D (GCM Mode)
- OWASP Cryptographic Storage Cheat Sheet
- GDPR Data Protection Guidelines

## Future Roadmap

- [ ] Hardware Security Module (HSM) Integration
- [ ] Advanced Key Backup and Recovery
- [ ] Quantum-Resistant Cryptographic Algorithms

## Contributing

Please review our [Contribution Guidelines](../../docs/contributing/CONTRIBUTING.md) before making changes to the cryptography module.

## License

Copyright (c) 2025 Cina.Club. All rights reserved.

**Note**: Cryptographic implementations require careful review. Always consult security experts before deploying in production. 