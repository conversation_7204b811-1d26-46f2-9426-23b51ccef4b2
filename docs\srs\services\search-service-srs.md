﻿好的，遵照您的指示，我们来生成一份为 `search-service` (统一搜索聚合服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **混合搜索(Hybrid Search)作为核心**: 明确将结合**关键词(Keyword)、向量(Vector)、地理空间(Geo)**的混合搜索作为核心能力，并定义其融合与重排策略。
2.  **查询理解与意图识别**: 引入与`ai-assistant-service`的协同，对复杂查询进行NLU（自然语言理解），以生成更精准的底层查询语句。
3.  **个性化排序(Personalized Ranking)**: 详细定义如何整合用户画像和历史行为，对搜索结果进行个性化重排，提升用户体验。
4.  **分面与聚合**: 强化对搜索结果进行动态分面（Faceting）和聚合的能力，为客户端提供丰富的筛选维度。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标，特别是对低延迟的要求。

这份文档将描绘一个功能强大、智能、高度个性化，且能作为整个平台信息发现统一入口的搜索引擎。

---

### CINA.CLUB - search-service 需求规格说明书

**版本: 2.0 (生产级定义，集成混合搜索与个性化排序)**  
**发布日期: 2025-06-24**  
**最后修订日期: 2025-06-24**  
**文档负责人:** [搜索与推荐团队负责人/架构师名称]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台聚合了海量的、多样化的信息，包括服务、任务、知识库、社区内容和用户。为了让用户能够快速、精准、智能地发现所需信息，`search-service` 应运而生。其目的在于提供一个**高性能、智能化的统一搜索中枢**。它通过**混合搜索**技术，聚合来自平台底层搜索引擎的结果，并运用**个性化排序**和机器学习，为用户提供无缝、高效、且高度相关的信息发现体验。

#### 1.2. 服务范围
本服务 **负责**:
*   **统一搜索接口**: 提供统一的搜索API，接收用户的关键词查询、自然语言查询和高级筛选条件。
*   **查询理解与增强 (Query Understanding & Augmentation)**:
    *   进行拼写检查、同义词扩展。
    *   （与`ai-assistant-service`协同）对复杂查询进行NLU，提取意图和实体。
*   **混合查询生成与执行**:
    *   将用户查询转换为对底层搜索引擎（Elasticsearch/OpenSearch）的复杂**混合查询**语句，该语句结合了**关键词匹配(BM25)、向量相似性(k-NN)和地理空间(Geo)**搜索。
*   **结果聚合与重排 (Result Aggregation & Re-ranking)**:
    *   融合来自不同查询方式的结果，使用**Reciprocal Rank Fusion (RRF)**等算法进行初步重排。
    *   应用**个性化排序**模型，根据用户画像和历史行为对结果进行二次重排。
*   **分面与高亮**: 从搜索结果中动态聚合分面信息，并为返回结果生成高亮摘要。
*   **搜索建议与自动完成**: 提供输入提示功能。
*   **权限与隐私过滤**: 确保返回给用户的每一条结果都是其有权访问的。

本服务 **不负责**:
*   **数据索引**: 数据的索引工作由`search-indexer-service`负责。本服务专注于**查询(Querying)**。
*   **存储原始业务数据**。
*   **生成嵌入向量**: 由`embedding-service`负责。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 通过API Gateway调用本服务的统一搜索API。
*   **`ai-assistant-service`**: 当AI助手需要从平台多个数据源中查找信息以回答用户问题时，会调用本服务。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`search-service` 是平台的信息**发现引擎**和**流量分发中枢**。它位于用户查询和底层搜索引擎之间，扮演着“**查询策略师**”和“**结果排序专家**”的角色。它通过智能的查询构建、混合搜索和个性化排序，将最相关的信息呈现给用户，直接决定了用户发现平台内容和服务的效率与体验。

#### 2.2. 主要功能概述
*   高性能的、统一的混合搜索API。
*   集成了NLU的智能查询理解。
*   基于RRF和机器学习的、可个性化的多阶段排序。
*   支持高级筛选和动态分面。

### 3. 核心流程图

#### 3.1. 处理一个复杂的、个性化的混合搜索请求
```mermaid
sequenceDiagram
    participant Client
    participant SearchService as SS
    participant AI_Assistant as AIAS
    participant EmbeddingService as ES
    participant UserCoreService as UCS
    participant Elasticsearch as SearchEngine

    Client->>SS: 1. GET /search?q="北京带花园的宠物友好咖啡馆"&userId=...
    
    SS->>AIAS: 2. **[Query Understanding]** (Optional) Parse query to extract entities
    AI_Assistant-->>SS: (Entities: {loc: "北京", amenities: ["花园"], tags: ["宠物友好"], category: "咖啡馆"})
    
    SS->>EmbeddingService: 3. Get vector for "北京带花园的宠物友好咖啡馆"
    EmbeddingService-->>SS: (Query Vector)
    
    SS->>SearchEngine: 4. **[Hybrid Query]** Execute a single complex query: <br/> - **bool**: must match category & tags <br/> - **geo_distance**: filter by location <br/> - **knn**: search by query vector <br/> - **match**: search by keywords
    SearchEngine-->>SS: 5. (Initial list of ranked results)
    
    SS->>UCS: 6. **[Personalization]** Get user's profile & preferences
    UCS-->>SS: (User Profile)
    
    SS->>SS: 7. **[Re-ranking]** Apply personalization model. <br/> - Boost results from user's favorite providers. <br/> - Boost results from categories user frequently interacts with.
    
    SS->>SearchEngine: 8. (Optional) Request aggregations for faceting
    SearchEngine-->>SS: (Facet data)
    
    SS-->>Client: 9. 200 OK (Final, personalized, re-ranked, and faceted search results)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 查询理解与增强
*   **FR4.1.1 (基础处理)**: 系统必须对用户查询进行清理、标准化、拼写检查和同义词扩展。
*   **FR4.1.2 (NLU集成)**: 对于自然语言查询，系统应能调用`ai-assistant-service`的NLU能力，提取出结构化的查询意图和实体（如地点、类别、价格范围等），并用这些实体来构建精确的布尔过滤(boolean filter)条件。

#### 4.2. 混合搜索与结果融合
*   **FR4.2.1 (混合查询)**: 系统必须能构建和执行包含多种查询类型的**单一复合查询**到Elasticsearch/OpenSearch，而不是多次查询。这利用了搜索引擎的内部优化。
*   **FR4.2.2 (结果融合)**: 系统必须使用**Reciprocal Rank Fusion (RRF)**或类似算法，来融合和归一化来自不同查询部分（如关键词得分和向量相似度得分）的结果，生成一个初始的相关性排序。

#### 4.3. 个性化排序
*   **FR4.3.1 (用户画像获取)**: 如果请求包含用户信息，系统应能异步地从`user-core-service`和`analytics-service`获取该用户的简版画像，包括偏好的类别、历史互动的提供者、价格敏感度等。
*   **FR4.3.2 (重排模型)**:
    *   **初期**: 可以使用一个简单的**加权模型**，为符合用户偏好的结果项增加一个“奖励分”。
    *   **后期**: 可以训练一个轻量级的**Learning-to-Rank (LTR)**模型，使用更多特征（用户特征、物品特征、交叉特征）对初步排序的结果进行更精准的二次重排。

#### 4.4. 分面、高亮与搜索建议
*   **FR4.4.1 (动态分面)**: 系统必须能根据搜索结果集，动态地生成分面数据（如“分类”、“品牌”、“价格区间”及其对应的文档数），并返回给客户端用于构建筛选UI。
*   **FR4.4.2 (高亮)**: 系统必须能为返回结果的标题和摘要生成匹配关键词的高亮片段。
*   **FR4.4.3 (自动完成)**: 提供独立的`/suggest`接口，根据用户输入的前缀，快速返回搜索建议列表。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/search`
*   **认证**: 可选的用户JWT。
*   **核心端点**:
    *   `POST /`: **统一搜索接口**。
        *   **Request Body**: `UnifiedSearchRequest { query, filters: {...}, sort_by, geo_context, personalization_context, page, pageSize }`
        *   **Response**: `AggregatedSearchResult { items: [SearchResultItem], facets: [...], total_hits, ... }`
    *   `GET /suggest?prefix=...`: 搜索建议接口。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (内存中)
*   `UnifiedSearchRequest`, `AggregatedSearchResult`, `SearchResultItem`, `FacetGroup`。
*   `SearchResultItem`必须是一个标准化的、与源业务无关的结构。

#### 6.2. 数据持久化与存储
*   **无核心持久化数据库**: 本服务是**无状态**的计算和编排服务。
*   **依赖的数据源**: Elasticsearch / OpenSearch。索引的结构设计对本服务的性能至关重要。
*   **缓存 (Redis)**: 对不包含个性化信息的、高频的搜索结果进行缓存。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求 (最高优先级)
*   **API延迟**: **P95搜索响应时间应 < 300ms**。**P99 < 800ms**。
*   **搜索建议延迟**: P99 < 50ms。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **容错**: 对下游服务（ES/OS, AIAS, UCS）的调用失败必须有容错机制，并能返回部分或非个性化的结果。

#### 7.3. 可扩展性需求
*   服务应为无状态，易于水平扩展。
*   Elasticsearch集群本身必须是高可用、可扩展的。

#### 7.4. 安全性需求
*   **权限过滤**: 查询必须包含用户上下文，并由ES/OS的文档级安全(Document-Level Security)来强制执行，确保用户只能看到其有权访问的内容。
*   **防注入**: 对用户输入进行严格清理，防止查询注入攻击。

### 8. 技术约束与选型建议
*   **语言**: Go。其并发模型非常适合并行地调用多个外部服务（如AI NLU和用户画像）并聚合结果。
*   **搜索引擎**: Elasticsearch或OpenSearch。必须使用较新版本以获得对混合搜索（dense vector + sparse vector）的良好支持。
*   **LTR模型**: （高级）可以使用`xgboost`等模型进行离线训练，并将模型导出为可被ES/OS的LTR插件加载的格式。
*   **查询DSL**: 服务内部需要一个强大的查询构建器，能将结构化的请求参数，动态地转换为复杂的Elasticsearch Query DSL。

---
这份版本2.0的SRS文档为`search-service`构建了一个现代化、智能化、高度个性化的信息发现引擎。它通过将先进的混合搜索技术与个性化排序相结合，能够为CINA.CLUB用户提供世界一流的搜索体验，是平台内容和服务有效分发的关键。