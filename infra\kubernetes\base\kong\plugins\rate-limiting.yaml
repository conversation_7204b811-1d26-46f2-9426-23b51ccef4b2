# CINA.CLUB Platform - Rate Limiting Plugins (Platform Standards)
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Rate Limiting Plugin - Standard User Tier
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: rate-limit-user
  namespace: kong-system
  labels:
    app: kong-plugin
    component: rate-limiting
    tier: standard-user
    category: traffic-management
  annotations:
    description: "Standard rate limiting for authenticated users"
    usage: "Apply to user-accessible routes via konghq.com/plugins annotation"
    owner: "<EMAIL>"
    tier: "standard"

plugin: rate-limiting
config:
  # Rate limiting rules for standard users
  second: null                             # No per-second limit
  minute: 1000                             # 1000 requests per minute
  hour: 50000                              # 50,000 requests per hour
  day: 1000000                             # 1 million requests per day
  month: null                              # No monthly limit
  year: null                               # No yearly limit
  
  # Rate limiting policy
  policy: "redis"                          # Use Redis for distributed rate limiting
  fault_tolerant: true                     # Continue if Redis is unavailable
  hide_client_headers: false               # Show rate limit headers to client
  
  # Redis configuration (will be provided via environment)
  redis_host: "redis.infrastructure.svc.cluster.local"
  redis_port: 6379
  redis_timeout: 2000                      # 2 second timeout
  redis_database: 1                        # Use database 1 for rate limiting
  
  # Rate limiting key (how to identify unique clients)
  limit_by: "consumer"                     # Rate limit by authenticated user
  
  # Headers to include in response
  header_name: "X-RateLimit-Limit"
  remaining_header_name: "X-RateLimit-Remaining"
  reset_header_name: "X-RateLimit-Reset"

---
# Rate Limiting Plugin - Premium User Tier
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: rate-limit-premium
  namespace: kong-system
  labels:
    app: kong-plugin
    component: rate-limiting
    tier: premium-user
    category: traffic-management
  annotations:
    description: "Enhanced rate limiting for premium users"
    tier: "premium"

plugin: rate-limiting
config:
  # Higher limits for premium users
  second: null
  minute: 5000                             # 5000 requests per minute
  hour: 200000                             # 200,000 requests per hour
  day: 5000000                             # 5 million requests per day
  month: null
  year: null
  
  # Redis configuration
  policy: "redis"
  fault_tolerant: true
  hide_client_headers: false
  
  redis_host: "redis.infrastructure.svc.cluster.local"
  redis_port: 6379
  redis_timeout: 2000
  redis_database: 1
  
  # Rate limit by consumer (authenticated user)
  limit_by: "consumer"
  
  # Response headers
  header_name: "X-RateLimit-Limit"
  remaining_header_name: "X-RateLimit-Remaining"
  reset_header_name: "X-RateLimit-Reset"

---
# Rate Limiting Plugin - Admin Tier
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: rate-limit-admin
  namespace: kong-system
  labels:
    app: kong-plugin
    component: rate-limiting
    tier: admin
    category: traffic-management
  annotations:
    description: "High-limit rate limiting for admin users"
    tier: "admin"

plugin: rate-limiting
config:
  # Very high limits for admin users
  second: null
  minute: 10000                            # 10,000 requests per minute
  hour: 500000                             # 500,000 requests per hour
  day: 10000000                            # 10 million requests per day
  month: null
  year: null
  
  # Redis configuration
  policy: "redis"
  fault_tolerant: true
  hide_client_headers: false
  
  redis_host: "redis.infrastructure.svc.cluster.local"
  redis_port: 6379
  redis_timeout: 2000
  redis_database: 1
  
  # Rate limit by consumer
  limit_by: "consumer"
  
  # Response headers
  header_name: "X-RateLimit-Limit"
  remaining_header_name: "X-RateLimit-Remaining"
  reset_header_name: "X-RateLimit-Reset"

---
# Rate Limiting Plugin - IP-based (for anonymous/public endpoints)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: rate-limit-ip
  namespace: kong-system
  labels:
    app: kong-plugin
    component: rate-limiting
    tier: anonymous
    category: traffic-management
  annotations:
    description: "IP-based rate limiting for anonymous/public endpoints"
    tier: "anonymous"

plugin: rate-limiting
config:
  # Conservative limits for anonymous users
  second: null
  minute: 100                              # 100 requests per minute per IP
  hour: 1000                               # 1000 requests per hour per IP
  day: 10000                               # 10,000 requests per day per IP
  month: null
  year: null
  
  # Redis configuration
  policy: "redis"
  fault_tolerant: true
  hide_client_headers: false
  
  redis_host: "redis.infrastructure.svc.cluster.local"
  redis_port: 6379
  redis_timeout: 2000
  redis_database: 1
  
  # Rate limit by IP address
  limit_by: "ip"                           # Rate limit by client IP
  
  # Response headers
  header_name: "X-RateLimit-Limit"
  remaining_header_name: "X-RateLimit-Remaining"
  reset_header_name: "X-RateLimit-Reset"

---
# Rate Limiting Plugin - API Key based
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: rate-limit-api-key
  namespace: kong-system
  labels:
    app: kong-plugin
    component: rate-limiting
    tier: api-key
    category: traffic-management
  annotations:
    description: "API key based rate limiting for external integrations"
    tier: "api-integration"

plugin: rate-limiting
config:
  # Moderate limits for API key users
  second: null
  minute: 2000                             # 2000 requests per minute
  hour: 100000                             # 100,000 requests per hour
  day: 2000000                             # 2 million requests per day
  month: null
  year: null
  
  # Redis configuration
  policy: "redis"
  fault_tolerant: true
  hide_client_headers: false
  
  redis_host: "redis.infrastructure.svc.cluster.local"
  redis_port: 6379
  redis_timeout: 2000
  redis_database: 1
  
  # Rate limit by credential (API key)
  limit_by: "credential"                   # Rate limit by API key
  
  # Response headers
  header_name: "X-RateLimit-Limit"
  remaining_header_name: "X-RateLimit-Remaining"
  reset_header_name: "X-RateLimit-Reset"

---
# Response Rate Limiting Plugin (for expensive operations)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: response-rate-limit
  namespace: kong-system
  labels:
    app: kong-plugin
    component: rate-limiting
    tier: response-based
    category: traffic-management
  annotations:
    description: "Response-based rate limiting for expensive operations"
    use-case: "Apply to resource-intensive endpoints like file uploads, AI processing"

plugin: response-ratelimiting
config:
  # Rate limiting based on response headers
  limits:
    # Limit based on custom response headers
    video-uploads:
      minute: 5                            # 5 video uploads per minute
      hour: 20                             # 20 video uploads per hour
    ai-requests:
      minute: 10                           # 10 AI requests per minute
      hour: 100                            # 100 AI requests per hour
    file-conversions:
      minute: 20                           # 20 file conversions per minute
      hour: 200                            # 200 file conversions per hour
  
  # Rate limiting policy
  policy: "redis"
  fault_tolerant: true
  
  # Redis configuration
  redis_host: "redis.infrastructure.svc.cluster.local"
  redis_port: 6379
  redis_timeout: 2000
  redis_database: 2                        # Use database 2 for response rate limiting
  
  # Rate limiting key
  limit_by: "consumer"
  
  # Block on first violation
  block_on_first_violation: false
  hide_client_headers: false

---
# Advanced Rate Limiting Plugin (with sliding window)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: rate-limit-sliding-window
  namespace: kong-system
  labels:
    app: kong-plugin
    component: rate-limiting
    tier: advanced
    category: traffic-management
  annotations:
    description: "Advanced rate limiting with sliding window for burst protection"
    use-case: "Apply to endpoints that need burst protection"

plugin: rate-limiting-advanced
config:
  # Sliding window configuration
  limit:
    - 1000                                 # 1000 requests
  window_size:
    - 60                                   # in 60 seconds (sliding window)
  
  # Advanced settings
  sync_rate: 10                            # Sync rate for distributed counters
  strategy: "redis"                        # Use Redis strategy
  namespace: "kong-rl-advanced"            # Redis namespace
  
  # Redis configuration
  redis:
    host: "redis.infrastructure.svc.cluster.local"
    port: 6379
    timeout: 2000
    database: 3                            # Use database 3 for advanced rate limiting
  
  # Identifier for rate limiting
  identifier: "consumer"                   # Rate limit by consumer
  
  # Window configuration
  window_type: "sliding"                   # Sliding window for smoother rate limiting
  
  # Headers
  hide_client_headers: false
  
  # Error handling
  fault_tolerant: true 