﻿好的，遵照您的指示。我将根据我们刚才讨论的、整合了Zapier理念的宏大构想，重新生成一份**全新的、生产级的`routines-service`需求规格说明书**。

这份**版本 3.0**的SRS，将把`routines-service`从一个简单的“IF-THEN”规则引擎，**战略升级**为一个支持多步工作流、多源触发器、并与`connector-service`协同的**平台级工作流自动化中心**。

---
### CINA.CLUB - routines-service (工作流自动化服务) 需求规格说明书

**版本: 3.0 (生产级定义，平台自动化中心)**  
**发布日期: 2025-06-27**  
**文档负责人:** [平台总架构师/AI与自动化团队负责人]  
**审批人:** [CTO/产品总监]

#### 修订历史
| 版本 | 日期       | 修订人     | 修订描述                                                     |
|------|------------|------------|--------------------------------------------------------------|
| 2.0  | 2025-06-24 | Cina.Club  | 集成`pkg/workflow`，支持高级触发器。                         |
| 3.0  | 2025-06-27 | Cina.Club  | **战略升级为平台自动化中心，引入多步工作流、与`connector-service`协同、并明确了Zapier式的产品愿景。** |

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心架构与流程](#3-核心架构与流程)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了将CINA.CLUB打造成一个高度个性化和智能化的生活与工作“操作系统”，`routines-service` 旨在提供一个强大的**工作流自动化引擎**，其功能对标Zapier或IFTTT，但深度根植于CINA.CLUB的生态系统。本服务赋予用户创建**多步骤、有条件的自动化工作流 (Routines)** 的能力，将平台内外的各种**触发器(Triggers)**和**动作(Actions)**连接起来，实现任务的自动化处理、信息的无缝流转和跨应用的智能联动。

#### 1.2. 服务范围
本服务 **负责**:
*   **工作流(Routine)定义管理**: 提供API供用户CRUD和启用/禁用其自动化工作流。
*   **工作流存储与解析**: 安全地持久化用户定义的、基于图的JSON工作流定义。
*   **多源触发器引擎**:
    *   **事件驱动**: 消费来自平台事件总线的领域事件。
    *   **时间驱动**: 支持基于Cron表达式的定时触发。
    *   **轮询驱动 (Polling)**: 定期轮询其他服务的API以检测变更。
    *   **Webhook驱动**: 提供唯一的Webhook URL，接收外部系统的触发信号。
*   **工作流执行与编排**:
    *   **使用共享工作流引擎 (`pkg/workflow`)**，在接收到触发信号后，实例化并执行对应的工作流图(DAG)。
    *   管理节点间的数据流，支持将前序节点的输出作为后续节点的输入。
*   **与连接器服务的协同**:
    *   **调用 `connector-service`**: 在执行动作节点时，调用`connector-service`中对应的动作执行器。
*   **执行日志与历史**: 详细记录每个工作流的触发、执行步骤、状态和结果，供用户查看和调试。
*   **权限与安全沙箱**: 确保所有工作流的执行都严格遵守操作用户的权限。

本服务 **不负责**:
*   **触发器和动作的目录管理与实现**: 由`connector-service`负责。本服务只**消费**其目录，并**调用**其执行器。
*   **用户创建工作流的UI**: 由前端负责。
*   **外部应用认证(OAuth)**: 由`connector-service`负责。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用**: 用户通过UI创建和管理Routines。
*   **平台事件总线 (Kafka)**: 主要的事件触发源。
*   **`connector-service`**: (被本服务调用) 获取动作执行器。
*   **`user-core-service`**: (被本服务调用) 获取执行工作流所需的用户凭证/权限。
*   **外部系统**: (通过Webhook) 触发特定的Routine。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`routines-service` 是CINA.CLUB平台的“**自动化大脑**”和“**中央神经系统**”。它不再是一个简单的IF-THEN规则引擎，而是升级为一个**成熟的工作流即服务(Workflow-as-a-Service)平台**。它与`connector-service`（提供“手脚”）和`pkg/workflow`（提供“骨架”）紧密协作，共同构成了平台Zapier式功能的技术核心。

#### 2.2. 主要功能概述
*   基于`pkg/workflow`的、支持多步、条件分支的工作流执行。
*   事件、时间、轮询、Webhook四位一体的强大触发器引擎。
*   与`connector-service`解耦，动态执行平台所有可用的动作。
*   健壮的、权限安全的执行沙箱。
*   为用户提供完整的、可追溯的执行历史和调试能力。

---

### 3. 核心架构与流程

#### 3.1. 多步工作流的执行流程
**场景**: 用户设置了一个Routine: “当我在社区Q&A的回答被采纳时，如果悬赏金额大于100，就向我的‘工作’聊天室发送一条祝贺消息，并给我自己创建一个‘整理笔记’的日程。”

```mermaid
sequenceDiagram
    participant SourceService as community-qa
    participant MQ as Kafka
    participant RoutinesService as RS
    participant WorkflowEngine as "pkg/workflow (in-process)"
    participant ConnectorService as CS
    participant ChatAPIService as Chat
    participant ScheduleService as Schedule

    SourceService->>MQ: 1. Publish AnswerAcceptedEvent
    MQ-->>RS: 2. [Trigger] Consume event
    
    RS->>DB: 3. Find Routine matching this trigger
    RS->>WorkflowEngine: 4. **Build** executable graph from Routine's JSON definition
    
    note over WorkflowEngine: **Execution starts...**
    
    WorkflowEngine->>WorkflowEngine: 5. **Node 1: Condition** <br/> - Input: `{{ .trigger.payload.bounty_amount }}` <br/> - Logic: `> 100` <br/> - Result: `true`
    
    alt Condition is true
        WorkflowEngine->>RS: 6. **Node 2: Action - 'chat.send_message'**
        RS->>CS: 7. Request to execute 'chat.send_message' with inputs
        CS->>Chat: 8. Call Chat API with user's credentials
        Chat-->>CS: (Success)
        CS-->>RS: (Action Output: { message_id: "..." })
        RS-->>WorkflowEngine: 9. Store output in ExecutionState
        
        WorkflowEngine->>RS: 10. **Node 3: Action - 'schedule.create_event'**
        RS->>CS: 11. Request to execute 'schedule.create_event' with inputs
        CS->>Schedule: 12. Call Schedule API
        Schedule-->>CS: (Success)
        CS-->>RS: (Action Output: { event_id: "..." })
        RS-->>WorkflowEngine: 13. Store output
    end
    
    RS->>DB: 14. Log the entire execution result
```
---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 工作流(Routine)定义
*   **FR4.1.1 (图形化定义)**: Routine的定义必须是一个可序列化为JSON的**有向无环图(DAG)**结构，与`pkg/workflow`的定义兼容。
*   **FR4.1.2 (多步与分支)**: 定义必须支持线性的多步动作，以及基于条件节点的`IF-ELSE`分支。
*   **FR4.1.3 (数据映射)**: 定义必须支持将触发器的输出或前序动作的输出，映射为后续动作的输入。

#### 4.2. 触发器引擎
*   **FR4.2.1 (事件触发)**: 必须能消费来自Kafka的事件，并根据`eventType`和`payload`内容，高效地匹配到需要被触发的Routines。
*   **FR4.2.2 (时间触发)**: 必须有一个高精度的后台调度器（如Kubernetes CronJob或`gocron`），能按用户定义的Cron表达式触发Routines。
*   **FR4.2.3 (轮询触发)**:
    *   必须有一个后台轮询调度器，能根据Routine的定义，定期（如每5-15分钟）调用`connector-service`中定义的“轮询触发器”。
    *   轮询触发器内部会调用目标服务的API，并与上次的状态进行比较，以检测是否有新项目。
    *   **必须**实现去重逻辑，确保同一个变更只触发一次工作流。
*   **FR4.2.4 (Webhook触发)**: 为每个可由Webhook触发的Routine，生成一个唯一的、安全的、不可猜测的URL。

#### 4.3. 工作流执行
*   **FR4.3.1 (引擎集成)**: 必须深度集成`pkg/workflow`作为其核心执行器。
*   **FR4.3.2 (动态节点执行)**:
    *   `routines-service`本身**不实现**任何具体的动作逻辑。
    *   在执行一个动作节点时，它会调用`connector-service`提供的标准`ExecuteAction`接口，并将节点的类型（如`chat.send_message`）和输入参数传递过去。
*   **FR4.3.3 (权限模拟与安全沙箱)**:
    *   **这是核心安全要求**: 在调用`connector-service`执行动作前，`routines-service`必须从`user-core-service`获取一个代表该Routine所有者的、**短期有效的、范围受限的S2S令牌**。
    *   这个令牌将被传递给`connector-service`，并由`connector-service`在最终调用目标微服务时使用。这确保了所有动作都以用户的身份执行，无法越权。

#### 4.4. 执行历史与调试
*   **FR4.4.1 (完整日志)**: 每次工作流执行都必须记录一条详细的、结构化的日志到`routine_execution_logs`表。
*   **FR4.4.2 (可追溯性)**: 日志必须包含：触发器类型和数据快照、每个节点的执行状态（成功/失败）、输入（脱敏后）、输出、耗时和错误信息。
*   **FR4.4.3 (调试运行)**: 提供“试运行(Dry Run)”API。调用时，`connector-service`的动作执行器会返回模拟的成功响应，而不会产生实际副作用。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端RESTful API接口
*   **版本**: `/api/v1/routines`
*   **核心端点**: `GET /me`, `POST /me`, `PUT /me/{id}`, `PATCH /me/{id}/toggle`, `POST /me/{id}/dry-run`, `GET /me/{id}/logs`。

#### 5.2. 内部gRPC API接口 (S2S)
*   本服务主要通过消费事件来触发，较少需要被同步调用。但可以提供：
    *   `rpc TriggerRoutineManually(TriggerRoutineRequest) returns (ExecutionLog)`: 用于AI助手或测试。

#### 5.3. 与`connector-service`的协同接口
*   `routines-service`将作为`connector-service`的gRPC客户端。
*   **调用接口**: `connector.v1.ConnectorService.ExecuteAction(ctx, {action_name, inputs, auth_token})`

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`routine_definitions`**: `id`, `user_id`, `name`, `is_enabled`, `trigger_config (JSONB)`, **`workflow_graph (JSONB)`**。
*   **`routine_execution_logs`**: `id`, `routine_id`, `user_id`, `status`, `start_time`, `duration_ms`, `trigger_snapshot (JSONB)`, **`execution_trace (JSONB)`** (存储每个节点的详细IO和状态)。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与吞吐量)**:
    *   必须能处理平台级的事件洪峰，消费者组需能水平扩展。
    *   单个工作流的调度和执行开销（不含动作执行时间）P99应 < 200ms。
*   **NFR7.2 (可靠性)**:
    *   触发器不能丢失。
    *   工作流执行失败时，必须有明确的失败状态和可供排查的日志。对可重试的动作失败，应支持重试策略。
*   **NFR7.3 (可扩展性)**:
    *   服务和后台Worker（用于轮询）都必须是可水平扩展的。
*   **NFR7.4 (安全性)**:
    *   **权限沙箱**: **FR4.3.3**中定义的权限模拟机制是绝对核心。
    *   **资源限制**: 必须对用户可创建的Routine数量、单个工作流的节点数、每月执行次数进行限制，防止滥用。
    *   **防循环**: `pkg/workflow`必须能检测并拒绝带有循环的定义。

---

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **工作流引擎**: **强制**使用共享的`pkg/workflow`库。
*   **后台调度器**: 对于时间驱动和轮询驱动，使用Kubernetes CronJob或`gocron`库。
*   **解耦**: 与`connector-service`的解耦是本架构的关键。`routines-service`只关心“何时”和“如何”执行，而`connector-service`关心“执行什么”。

---
这份版本3.0的SRS文档将`routines-service`的定位提升到了一个全新的战略高度。它不再是一个简单的功能，而是**赋能用户、连接生态、释放平台组合潜力**的核心引擎，为CINA.CLUB构建了坚实的自动化和集成基础。