/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package slice provides generic slice operations that are not available in the standard library.
package slice

// Contains checks if a slice contains a specific value.
// Uses linear search and requires the element type to be comparable.
//
// Example:
//
//	found := slice.Contains([]int{1, 2, 3}, 2)        // returns true
//	found = slice.Contains([]string{"a", "b"}, "c")   // returns false
func Contains[T comparable](s []T, v T) bool {
	for _, item := range s {
		if item == v {
			return true
		}
	}
	return false
}

// ContainsAny checks if a slice contains any of the specified values.
//
// Example:
//
//	found := slice.ContainsAny([]int{1, 2, 3}, []int{2, 4})      // returns true
//	found = slice.ContainsAny([]string{"a", "b"}, []string{"c"}) // returns false
func ContainsAny[T comparable](s []T, values []T) bool {
	for _, value := range values {
		if Contains(s, value) {
			return true
		}
	}
	return false
}

// ContainsAll checks if a slice contains all of the specified values.
//
// Example:
//
//	found := slice.ContainsAll([]int{1, 2, 3}, []int{1, 2})      // returns true
//	found = slice.ContainsAll([]string{"a", "b"}, []string{"a", "c"}) // returns false
func ContainsAll[T comparable](s []T, values []T) bool {
	for _, value := range values {
		if !Contains(s, value) {
			return false
		}
	}
	return true
}

// Index returns the index of the first occurrence of the specified value in the slice.
// Returns -1 if the value is not found.
//
// Example:
//
//	index := slice.Index([]int{1, 2, 3, 2}, 2)      // returns 1
//	index = slice.Index([]string{"a", "b"}, "c")    // returns -1
func Index[T comparable](s []T, v T) int {
	for i, item := range s {
		if item == v {
			return i
		}
	}
	return -1
}

// LastIndex returns the index of the last occurrence of the specified value in the slice.
// Returns -1 if the value is not found.
//
// Example:
//
//	index := slice.LastIndex([]int{1, 2, 3, 2}, 2)      // returns 3
//	index = slice.LastIndex([]string{"a", "b"}, "c")    // returns -1
func LastIndex[T comparable](s []T, v T) int {
	for i := len(s) - 1; i >= 0; i-- {
		if s[i] == v {
			return i
		}
	}
	return -1
}

// Count returns the number of occurrences of the specified value in the slice.
//
// Example:
//
//	count := slice.Count([]int{1, 2, 3, 2, 2}, 2)      // returns 3
//	count = slice.Count([]string{"a", "b"}, "c")       // returns 0
func Count[T comparable](s []T, v T) int {
	count := 0
	for _, item := range s {
		if item == v {
			count++
		}
	}
	return count
}
