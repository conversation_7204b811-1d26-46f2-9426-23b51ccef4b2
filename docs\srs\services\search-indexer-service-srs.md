
### CINA.CLUB - search-indexer-service 需求规格说明书

**版本: 1.0**  
**发布日期: 2025-06-17**  
**最后修订日期: 2025-06-17**  
**文档负责人:** [搜索与推荐团队负责人/架构师名称]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [功能需求 (Functional Requirements)](#3-功能需求-functional-requirements)
4.  [接口需求 (Interface Requirements)](#4-接口需求-interface-requirements)
5.  [数据需求 (Data Requirements)](#5-数据需求-data-requirements)
6.  [非功能性需求 (Non-Functional Requirements)](#6-非功能性需求-non-functional-requirements)
7.  [技术约束与选型建议](#7-技术约束-与-选型建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了支持 CINA.CLUB 平台强大的统一搜索功能 (`search-service`)，需要一个可靠、高效的机制来将各个业务微服务中产生和变更的数据，实时地同步到中央搜索引擎（如 Elasticsearch/OpenSearch）中。`search-indexer-service` 的目的就是构建这样一个专用的、事件驱动的数据同步管道。它通过消费来自平台事件总线的领域事件，将异构的业务数据转换为标准化的、可供搜索的文档，并将其写入搜索引擎，从而保证搜索结果的实时性和准确性。

#### 1.2. 服务范围
本服务 **负责**:
*   **事件消费**: 消费来自平台各微服务发布的领域事件，如 `ServiceOfferingPublishedEvent`, `CKBItemUpdatedEvent`, `UserProfileUpdatedEvent`, `ForumThreadCreatedEvent` 等。
*   **数据转换与丰富**:
    *   将事件中的业务实体数据，转换为统一的、扁平化的搜索文档（Search Document）格式。
    *   （可选）在转换过程中，调用其他服务API来获取额外的数据以丰富搜索文档（例如，为服务项目文档补充提供者的评分信息）。
*   **数据索引**: 将转换后的搜索文档写入、更新或从Elasticsearch/OpenSearch中删除。
*   **索引管理**: （可选）协调搜索引擎的索引创建、别名切换、映射更新等生命周期管理任务。
*   **幂等性处理**: 确保对同一事件的重复处理不会产生副作用。
*   **错误处理与重试**: 处理事件消费和索引写入过程中的失败，并实现可靠的重试机制。

本服务 **不负责**:
*   产生业务领域事件 (它纯粹是事件的消费者)。
*   处理搜索查询 (由 `search-service` 负责)。
*   存储任何原始业务数据的主副本 (主数据仍在各自的业务服务中)。
*   提供面向最终用户的API。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部微服务 (通过消息队列)**: 平台所有需要其数据被搜索的微服务，都是本服务的事件源。
*   **Elasticsearch / OpenSearch (被本服务调用)**: 本服务是搜索引擎的主要写入方。
*   **CINA.CLUB平台管理员/DevOps**: 通过监控和管理接口，查看索引状态，处理失败的索引任务。

#### 1.4. 定义与缩略语
*   **Indexer**: 索引器，指本服务。
*   **Search Document**: 为搜索优化的、扁平化的数据结构，存储在搜索引擎中。
*   **ES/OS**: Elasticsearch / OpenSearch。
*   **Event-Driven**: 事件驱动。
*   **ETL/ELT**: 提取-转换-加载 / 提取-加载-转换。本服务执行的是一个轻量级的、实时的ETL过程。
*   **DLQ**: Dead-Letter Queue (死信队列)。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`search-indexer-service` 是平台**数据流向搜索引擎的唯一入口**，扮演着“数据管道工”和“翻译官”的角色。它将业务服务的“写模型”数据（通常是规范化的、存储在关系型数据库中）转换为搜索服务的“读模型”数据（非规范化的、为快速查询优化的文档）。这种分离使得业务服务无需关心搜索引擎的复杂性，也让搜索架构可以独立演进。

#### 2.2. 主要功能概述
*   事件驱动的、近实时的索引同步。
*   支持多种业务实体到统一搜索模型的转换。
*   高吞吐量、高可靠的事件处理能力。
*   与业务服务的完全解耦。

#### 2.3. 用户特征
本服务的“用户”是平台事件总线和搜索引擎。

#### 2.4. 约束与假设
*   假设平台有一个统一的、可靠的事件总线（消息队列，如Kafka）。
*   假设各业务服务会发布结构化、定义良好、包含必要信息的领域事件。
*   假设存在一个稳定、可用的Elasticsearch/OpenSearch集群。

### 3. 功能需求 (Functional Requirements)

#### 3.1. 事件消费与分发
*   **FR3.1.1 (多主题消费)**: 系统必须能同时消费来自多个不同事件主题（Topics）的消息。
*   **FR3.1.2 (事件分发)**: 系统内部必须有一个分发器（Dispatcher），能根据接收到的事件类型 (`eventType`)，将其路由到对应的处理器（Handler）。
*   **FR3.1.3 (并发处理)**: 系统必须能并发地处理来自不同分区或主题的事件，以最大化吞吐量。

#### 3.2. 数据转换 (Transformation Logic)
*   **FR3.2.1 (处理器实现)**: 必须为每一种需要索引的业务实体（如`ServiceOffering`, `CKBItem`, `User`等）实现一个专门的事件处理器。
*   **FR3.2.2 (数据映射)**: 每个处理器负责将特定事件的`payload`映射到一个统一的`SearchDocument`结构。这包括字段的重命名、数据类型的转换、以及将嵌套结构扁平化。
    *   **示例**: `ServiceOfferingPublishedEvent` -> `SearchDocument{ id: "svc_...", type: "service", title: "...", content: "...", provider_name: "...", location: "geo_point", ... }`
*   **FR3.2.3 (数据丰富 - 可选)**:
    *   在某些场景下，如果事件`payload`信息不足，处理器可能需要进行一次**同步API调用**来获取额外信息。
    *   **示例**: 为了给服务项目文档增加提供者的评分，处理器需要调用 `review-service` 的 `GET /target/user/{providerId}/summary` 接口。
    *   **注意**: 这种同步调用会增加处理延迟和复杂性，应尽量避免。更好的方式是让源事件本身包含足够的信息。
*   **FR3.2.4 (删除处理)**: 处理器必须能处理实体被删除的事件（如 `ServiceOfferingDeletedEvent`），并生成一个对搜索引擎的删除请求。

#### 3.3. 数据索引 (Indexing Logic)
*   **FR3.3.1 (写入ES/OS)**: 系统必须使用Elasticsearch/OpenSearch的官方Go客户端，将转换后的`SearchDocument`写入到正确的索引中。
*   **FR3.3.2 (批量写入)**: 为了提高性能，系统应将短时间内收到的多个文档聚合起来，使用ES/OS的`_bulk` API进行批量写入。
*   **FR3.3.3 (更新与删除)**: 系统必须能处理文档的更新（通过文档ID）和删除操作。

#### 3.4. 可靠性与错误处理
*   **FR3.4.1 (幂等性)**: 事件处理器必须是幂等的。通过使用事件ID或业务实体ID作为ES/OS文档的`_id`，可以天然地实现写入操作的幂等性。
*   **FR3.4.2 (重试机制)**: 对于可恢复的错误（如网络抖动、ES集群临时不可用），系统必须实现带指数退避的重试逻辑。
*   **FR3.4.3 (死信队列 - DLQ)**: 经过多次重试后仍然失败的事件（如格式错误、无法修复的数据问题），必须被发送到专门的死信队列中，并附带错误信息，供人工排查。
*   **FR3.4.4 (消息确认)**: 只有当文档成功写入ES/OS（或被确认送入DLQ）后，才能向消息队列确认（Acknowledge）该事件已被成功处理。这保证了“至少一次”的处理语义。

#### 3.5. 索引管理 (可选，高级功能)
*   **FR3.5.1 (全量重建)**: 系统应提供一个管理API或命令行工具，用于触发对某一类型数据的全量索引重建。这在索引映射变更或数据修复后非常有用。
*   **FR3.5.2 (零停机切换)**: 全量重建时，应采用“蓝绿部署”的模式：数据写入一个新的索引，完成后通过切换别名（Alias）的方式，无缝地将查询流量指向新索引，然后再删除旧索引。

### 4. 接口需求 (Interface Requirements)

#### 4.1. 消息队列事件契约 (主要入站接口)
*   **消费主题**: `cina-platform-events` (通配符模式，如 `*.events`) 或多个具体主题。
*   **事件格式 (示例)**:
    ```json
    {
      "event_id": "uuid",
      "event_type": "service.offering.published",
      "timestamp": "iso8601",
      "source_service": "service-offering-service",
      "payload": {
        "service_id": "uuid",
        "provider_id": "uuid",
        "title": "Professional Dog Walking",
        "description": "Daily walks for your furry friend.",
        "category": "pet-care",
        "tags": ["dog", "walking", "pets"],
        "price": 25.00,
        "currency": "USD",
        "location": { "lat": 34.05, "lon": -118.25 }
      }
    }
    ```

#### 4.2. 管理API接口 (内部)
*   **版本**: `/api/v1/indexer/admin`
*   **认证**: Admin角色JWT。
*   **核心端点**:
    *   `POST /reindex`: 触发全量索引重建。Request: `{ "target_type": "SERVICE_OFFERING" }`
    *   `GET /status`: 查看索引器服务的健康状况和队列积压情况。
    *   `GET /dlq/messages`: 查看死信队列中的消息。
    *   `POST /dlq/messages/{messageId}/retry`: 重试单条死信消息。

### 5. 数据需求 (Data Requirements)

#### 5.1. 核心数据模型 (内存中)
*   **`SearchDocument` (统一搜索文档结构)**:
    ```go
    type SearchDocument struct {
        ID          string      `json:"id"`           // 唯一的文档ID (e.g., "svc_uuid", "ckb_uuid")
        EntityType  string      `json:"entity_type"`  // "service", "ckb_item", "user", "forum_post"
        Title       string      `json:"title"`
        Content     string      `json:"content"`
        Tags        []string    `json:"tags"`
        UserID      string      `json:"user_id,omitempty"` // 作者ID
        UserName    string      `json:"user_name,omitempty"`
        Location    *GeoPoint   `json:"location,omitempty"` // 地理位置
        Price       *float64    `json:"price,omitempty"`
        Rating      *float32    `json:"rating,omitempty"`
        CreatedAt   time.Time   `json:"created_at"`
        // ...其他用于搜索、过滤、排序的公共字段
    }
    type GeoPoint struct {
        Lat float64 `json:"lat"`
        Lon float64 `json:"lon"`
    }
    ```

#### 5.2. 数据持久化与存储
*   **无核心持久化数据库**: 本服务是无状态的管道，不拥有自己的核心业务数据库。
*   **目标存储**: Elasticsearch / OpenSearch 集群。
*   **状态存储 (可选)**: 可以使用Redis来存储一些运行时的状态，如最近一次成功处理的事件时间戳等。

### 6. 非功能性需求 (Non-Functional Requirements)

#### 6.1. 性能需求
*   **吞吐量**: 必须能够跟上平台所有业务事件产生的峰值流量，不产生严重的队列积压。
*   **处理延迟 (End-to-End Latency)**: 从事件发布到数据在ES/OS中可被搜索的P99延迟应 `< 5秒`。

#### 6.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。本服务的宕机会导致搜索结果过时。
*   **数据不丢失**: 必须保证事件被“至少一次”地处理，不能因为服务崩溃而丢失索引更新。
*   **数据准确性**: 转换逻辑必须正确，保证搜索文档与源数据的一致性。

#### 6.3. 可扩展性需求
*   **服务可水平扩展**: 可以通过增加消费者实例数量来线性提升事件处理能力。
*   **ES/OS集群**也必须是可扩展的。

#### 6.4. 安全性需求
*   **S2S认证**: 对内部管理API和对其他服务的调用必须经过认证。
*   **凭证安全**: ES/OS集群的访问凭证必须安全存储和管理。

#### 6.5. 可维护性需求
*   **处理器插件化**: 添加对新业务实体的索引支持应尽可能简单，只需实现一个新的`EventHandler`接口并注册即可。
*   **配置驱动**: ES/OS的地址、索引名称、批量写入大小等参数都应是可配置的。

#### 6.6. 可观测性需求
*   **日志**: 详细记录每个接收到的事件、转换过程、对ES/OS的写入操作（包括成功和失败）。
*   **指标 (Metrics)**:
    *   MQ消息积压量 (Lag) (最关键的指标)。
    *   事件处理速率 (Events per second)。
    *   事件处理端到端延迟。
    *   ES/OS批量写入的成功/失败率和延迟。
    *   DLQ中的消息数量。
*   **追踪 (Tracing)**: 分布式追踪覆盖从事件消费到写入ES/OS的完整链路。
*   **告警 (Alerting)**:
    *   MQ Lag 持续超过阈值。
    *   DLQ中出现新消息。
    *   ES/OS写入失败率持续过高。

### 7. 技术约束与选型建议
*   **语言**: Go。其强大的并发模型（Goroutines 和 Channels）非常适合构建高性能的、并行的事件消费者。
*   **消息队列**: Kafka。其高吞吐量、持久化和分区特性非常适合作为平台事件总线。
*   **搜索引擎**: Elasticsearch 或 OpenSearch。
*   **核心架构**:
    *   使用Kafka消费者组来实现服务的水平扩展和负载均衡。
    *   每个消费者实例内部可以有多个goroutine来并行处理来自不同分区的消息。
    *   采用“适配器/处理器”设计模式，为每种`eventType`实现一个具体的处理逻辑。

---

这份生产级的SRS文档为`search-indexer-service`的开发提供了清晰而全面的指导。它将该服务定位为一个纯粹的、高可靠的、事件驱动的数据管道，有效地将复杂的业务数据与强大的搜索引擎连接起来，是实现高质量平台搜索功能的关键一环。