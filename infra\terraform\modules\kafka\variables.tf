# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

variable "cluster_name" {
  description = "Name of the MSK cluster"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the MSK cluster"
  type        = list(string)
}

variable "kafka_version" {
  description = "Specify the desired Kafka software version"
  type        = string
  default     = "2.8.1"
}

variable "number_of_broker_nodes" {
  description = "The desired total number of broker nodes in the kafka cluster"
  type        = number
  default     = 3
}

variable "broker_instance_type" {
  description = "Specify the instance type to use for the kafka brokers"
  type        = string
  default     = "kafka.t3.small"
}

variable "ebs_volume_size" {
  description = "The size in GiB of the EBS volume for the data drive on each broker node"
  type        = number
  default     = 100
}

variable "replication_factor" {
  description = "The default replication factor for topics"
  type        = number
  default     = 3
}

# Encryption
variable "kms_key_id" {
  description = "The ARN of the KMS key used for encryption at rest"
  type        = string
  default     = null
}

variable "encryption_in_transit_client_broker" {
  description = "Encryption setting for data in transit between clients and brokers"
  type        = string
  default     = "TLS"
  validation {
    condition     = contains(["TLS", "TLS_PLAINTEXT", "PLAINTEXT"], var.encryption_in_transit_client_broker)
    error_message = "Valid values are TLS, TLS_PLAINTEXT, or PLAINTEXT."
  }
}

variable "encryption_in_transit_in_cluster" {
  description = "Whether data communication among broker nodes is encrypted"
  type        = bool
  default     = true
}

# Authentication
variable "enable_scram_authentication" {
  description = "Enables SCRAM client authentication via AWS Secrets Manager"
  type        = bool
  default     = false
}

variable "certificate_authority_arns" {
  description = "List of ACM Certificate Authority Amazon Resource Names (ARNs)"
  type        = list(string)
  default     = []
}

# Logging
variable "enable_cloudwatch_logs" {
  description = "Indicates whether you want to enable or disable streaming broker logs to CloudWatch Logs"
  type        = bool
  default     = true
}

variable "log_retention_in_days" {
  description = "Specifies the number of days you want to retain log events"
  type        = number
  default     = 14
}

variable "enable_firehose_logs" {
  description = "Indicates whether you want to enable or disable streaming broker logs to Kinesis Data Firehose"
  type        = bool
  default     = false
}

variable "firehose_delivery_stream" {
  description = "Name of the Kinesis Data Firehose delivery stream"
  type        = string
  default     = null
}

variable "enable_s3_logs" {
  description = "Indicates whether you want to enable or disable streaming broker logs to S3"
  type        = bool
  default     = false
}

variable "s3_logs_bucket" {
  description = "Name of the S3 bucket to deliver logs to"
  type        = string
  default     = null
}

variable "s3_logs_prefix" {
  description = "Prefix to append to the folder name"
  type        = string
  default     = null
}

variable "allowed_cidr_blocks" {
  description = "List of CIDR blocks allowed to access the MSK cluster"
  type        = list(string)
  default     = ["10.0.0.0/16"]
}

variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
  default     = {}
} 