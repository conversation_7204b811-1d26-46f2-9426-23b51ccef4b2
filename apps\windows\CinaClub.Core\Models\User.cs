/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System;
using System.ComponentModel.DataAnnotations;

namespace CinaClub.Core.Models;

/// <summary>
/// 用户领域模型
/// </summary>
public class User
{
    /// <summary>
    /// 用户唯一标识
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    [Required]
    [StringLength(50, MinimumLength = 3)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    [StringLength(100)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 手机号码（E.164格式）
    /// </summary>
    [Phone]
    public string PhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// 头像URL
    /// </summary>
    public string AvatarUrl { get; set; } = string.Empty;

    /// <summary>
    /// 个人简介
    /// </summary>
    [StringLength(500)]
    public string Bio { get; set; } = string.Empty;

    /// <summary>
    /// 用户等级
    /// </summary>
    public int Level { get; set; } = 1;

    /// <summary>
    /// 经验值
    /// </summary>
    public long ExperiencePoints { get; set; } = 0;

    /// <summary>
    /// 灵境币余额
    /// </summary>
    public decimal CinaCoinBalance { get; set; } = 0;

    /// <summary>
    /// 用户状态
    /// </summary>
    public UserStatus Status { get; set; } = UserStatus.Active;

    /// <summary>
    /// 是否已验证邮箱
    /// </summary>
    public bool IsEmailVerified { get; set; } = false;

    /// <summary>
    /// 是否已验证手机
    /// </summary>
    public bool IsPhoneVerified { get; set; } = false;

    /// <summary>
    /// 是否VIP会员
    /// </summary>
    public bool IsVip { get; set; } = false;

    /// <summary>
    /// VIP到期时间
    /// </summary>
    public DateTime? VipExpiryDate { get; set; }

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// 用户偏好设置
    /// </summary>
    public UserPreferences Preferences { get; set; } = new();

    /// <summary>
    /// 是否为活跃用户（24小时内登录过）
    /// </summary>
    public bool IsActive => LastLoginAt.HasValue && 
                           (DateTime.UtcNow - LastLoginAt.Value).TotalHours < 24;

    /// <summary>
    /// 是否为新用户（注册不超过7天）
    /// </summary>
    public bool IsNewUser => (DateTime.UtcNow - CreatedAt).TotalDays <= 7;

    /// <summary>
    /// 获取用户显示名称（优先使用DisplayName，否则使用Username）
    /// </summary>
    public string GetDisplayName() => !string.IsNullOrEmpty(DisplayName) ? DisplayName : Username;

    /// <summary>
    /// 获取用户头像URL（如果没有设置则返回默认头像）
    /// </summary>
    public string GetAvatarUrl() => !string.IsNullOrEmpty(AvatarUrl) ? AvatarUrl : GetDefaultAvatarUrl();

    /// <summary>
    /// 获取默认头像URL
    /// </summary>
    private string GetDefaultAvatarUrl() => $"https://api.cinaclub.com/avatar/default?userId={UserId}";
}

/// <summary>
/// 用户状态枚举
/// </summary>
public enum UserStatus
{
    /// <summary>
    /// 活跃
    /// </summary>
    Active = 1,

    /// <summary>
    /// 暂停
    /// </summary>
    Suspended = 2,

    /// <summary>
    /// 禁用
    /// </summary>
    Disabled = 3,

    /// <summary>
    /// 待验证
    /// </summary>
    PendingVerification = 4
}

/// <summary>
/// 用户偏好设置
/// </summary>
public class UserPreferences
{
    /// <summary>
    /// 语言设置
    /// </summary>
    public string Language { get; set; } = "zh-CN";

    /// <summary>
    /// 时区设置
    /// </summary>
    public string TimeZone { get; set; } = "Asia/Shanghai";

    /// <summary>
    /// 主题设置（Light/Dark/Auto）
    /// </summary>
    public string Theme { get; set; } = "Auto";

    /// <summary>
    /// 是否启用通知
    /// </summary>
    public bool NotificationsEnabled { get; set; } = true;

    /// <summary>
    /// 是否启用邮件通知
    /// </summary>
    public bool EmailNotificationsEnabled { get; set; } = true;

    /// <summary>
    /// 是否启用短信通知
    /// </summary>
    public bool SmsNotificationsEnabled { get; set; } = false;

    /// <summary>
    /// 隐私级别（Public/Friends/Private）
    /// </summary>
    public string PrivacyLevel { get; set; } = "Friends";

    /// <summary>
    /// 是否启用AI助手
    /// </summary>
    public bool AIAssistantEnabled { get; set; } = true;

    /// <summary>
    /// 是否启用数据同步
    /// </summary>
    public bool DataSyncEnabled { get; set; } = true;
} 