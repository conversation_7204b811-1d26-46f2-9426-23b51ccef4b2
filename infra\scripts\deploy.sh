#!/bin/bash
# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=""
SERVICE=""
ACTION="apply"
DRY_RUN=false
VERBOSE=false

# Help function
show_help() {
    cat << EOF
CINA.CLUB Infrastructure Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV    Target environment (dev|staging|prod)
    -s, --service SERVICE    Specific service to deploy (optional)
    -a, --action ACTION      Action to perform (apply|plan|destroy) [default: apply]
    -d, --dry-run           Perform a dry run (plan only)
    -v, --verbose           Enable verbose output
    -h, --help              Show this help message

EXAMPLES:
    $0 -e dev                           # Deploy all services to dev
    $0 -e staging -s user-core-service  # Deploy specific service to staging
    $0 -e prod -a plan                  # Plan deployment to prod
    $0 -e dev -d                        # Dry run for dev environment

EOF
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# Validate environment
validate_environment() {
    case "$ENVIRONMENT" in
        dev|staging|prod)
            log_info "Deploying to environment: $ENVIRONMENT"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            log_error "Valid environments: dev, staging, prod"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check if kustomize is installed
    if ! command -v kustomize &> /dev/null; then
        log_error "kustomize is not installed"
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "terraform is not installed"
        exit 1
    fi
    
    # Check if current directory is correct
    if [[ ! -d "infra" ]]; then
        log_error "Please run this script from the project root directory"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Set up kubectl context
setup_kubectl_context() {
    local cluster_name="cina-club-${ENVIRONMENT}"
    local region="us-west-2"
    
    log_info "Setting up kubectl context for cluster: $cluster_name"
    
    # Update kubeconfig
    aws eks update-kubeconfig --region "$region" --name "$cluster_name" || {
        log_error "Failed to update kubeconfig for cluster: $cluster_name"
        exit 1
    }
    
    # Verify connection
    kubectl cluster-info > /dev/null || {
        log_error "Failed to connect to Kubernetes cluster"
        exit 1
    }
    
    log_success "Connected to Kubernetes cluster: $cluster_name"
}

# Deploy infrastructure (Terraform)
deploy_infrastructure() {
    log_info "Deploying infrastructure for environment: $ENVIRONMENT"
    
    cd "infra/terraform/environments/$ENVIRONMENT"
    
    # Initialize Terraform
    terraform init || {
        log_error "Terraform init failed"
        exit 1
    }
    
    # Plan
    if [[ "$ACTION" == "plan" ]] || [[ "$DRY_RUN" == true ]]; then
        terraform plan
        if [[ "$DRY_RUN" == true ]]; then
            cd - > /dev/null
            return
        fi
    fi
    
    # Apply or destroy
    case "$ACTION" in
        apply)
            terraform apply -auto-approve || {
                log_error "Terraform apply failed"
                exit 1
            }
            ;;
        destroy)
            terraform destroy -auto-approve || {
                log_error "Terraform destroy failed"
                exit 1
            }
            ;;
    esac
    
    cd - > /dev/null
    log_success "Infrastructure deployment completed"
}

# Deploy applications (Kubernetes)
deploy_applications() {
    log_info "Deploying applications for environment: $ENVIRONMENT"
    
    local overlay_path="infra/kubernetes/overlays/$ENVIRONMENT"
    
    if [[ ! -d "$overlay_path" ]]; then
        log_error "Overlay path not found: $overlay_path"
        exit 1
    fi
    
    if [[ "$DRY_RUN" == true ]] || [[ "$ACTION" == "plan" ]]; then
        log_info "Dry run - showing what would be deployed:"
        kustomize build "$overlay_path"
        return
    fi
    
    case "$ACTION" in
        apply)
            kustomize build "$overlay_path" | kubectl apply -f - || {
                log_error "Kubernetes deployment failed"
                exit 1
            }
            ;;
        destroy)
            kustomize build "$overlay_path" | kubectl delete -f - || {
                log_warning "Some resources may not have been deleted"
            }
            ;;
    esac
    
    log_success "Application deployment completed"
}

# Deploy specific service
deploy_service() {
    log_info "Deploying service: $SERVICE to environment: $ENVIRONMENT"
    
    local service_path="infra/kubernetes/base/$SERVICE"
    local overlay_path="infra/kubernetes/overlays/$ENVIRONMENT"
    
    if [[ ! -d "$service_path" ]]; then
        log_error "Service path not found: $service_path"
        exit 1
    fi
    
    # Create temporary kustomization for single service
    local temp_dir=$(mktemp -d)
    local temp_kustomization="$temp_dir/kustomization.yaml"
    
    cat > "$temp_kustomization" << EOF
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: cina-club-$ENVIRONMENT

resources:
- ../../$service_path

images:
- name: cina-club/$SERVICE
  newTag: ${ENVIRONMENT}-latest
EOF
    
    if [[ "$DRY_RUN" == true ]] || [[ "$ACTION" == "plan" ]]; then
        log_info "Dry run - showing what would be deployed:"
        kustomize build "$temp_dir"
        rm -rf "$temp_dir"
        return
    fi
    
    case "$ACTION" in
        apply)
            kustomize build "$temp_dir" | kubectl apply -f - || {
                log_error "Service deployment failed"
                rm -rf "$temp_dir"
                exit 1
            }
            ;;
        destroy)
            kustomize build "$temp_dir" | kubectl delete -f - || {
                log_warning "Some resources may not have been deleted"
            }
            ;;
    esac
    
    rm -rf "$temp_dir"
    log_success "Service deployment completed: $SERVICE"
}

# Wait for deployments to be ready
wait_for_deployments() {
    log_info "Waiting for deployments to be ready..."
    
    local namespace="cina-club-$ENVIRONMENT"
    
    # Wait for all deployments in the namespace
    kubectl wait --for=condition=available --timeout=300s deployment --all -n "$namespace" || {
        log_warning "Some deployments may not be ready yet"
    }
    
    log_success "All deployments are ready"
}

# Main deployment flow
main() {
    log_info "Starting CINA.CLUB infrastructure deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Action: $ACTION"
    if [[ "$SERVICE" != "" ]]; then
        log_info "Service: $SERVICE"
    fi
    if [[ "$DRY_RUN" == true ]]; then
        log_info "Mode: DRY RUN"
    fi
    
    validate_environment
    check_prerequisites
    
    if [[ "$ACTION" != "destroy" ]]; then
        setup_kubectl_context
    fi
    
    # Deploy infrastructure first
    if [[ "$SERVICE" == "" ]]; then
        deploy_infrastructure
        
        if [[ "$ACTION" == "apply" ]] && [[ "$DRY_RUN" == false ]]; then
            sleep 30  # Wait for infrastructure to stabilize
            setup_kubectl_context
        fi
        
        deploy_applications
        
        if [[ "$ACTION" == "apply" ]] && [[ "$DRY_RUN" == false ]]; then
            wait_for_deployments
        fi
    else
        deploy_service
        
        if [[ "$ACTION" == "apply" ]] && [[ "$DRY_RUN" == false ]]; then
            kubectl wait --for=condition=available --timeout=300s deployment "$SERVICE" -n "cina-club-$ENVIRONMENT" || {
                log_warning "Service deployment may not be ready yet: $SERVICE"
            }
        fi
    fi
    
    log_success "Deployment completed successfully!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -s|--service)
            SERVICE="$2"
            shift 2
            ;;
        -a|--action)
            ACTION="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required arguments
if [[ "$ENVIRONMENT" == "" ]]; then
    log_error "Environment is required"
    show_help
    exit 1
fi

# Validate action
case "$ACTION" in
    apply|plan|destroy)
        ;;
    *)
        log_error "Invalid action: $ACTION"
        log_error "Valid actions: apply, plan, destroy"
        exit 1
        ;;
esac

# Run main deployment
main 