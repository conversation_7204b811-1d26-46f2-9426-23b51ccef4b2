/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useRef } from 'react'
import { 
  Card, 
  Tag, 
  Space, 
  Button, 
  Modal, 
  message, 
  Typography,
  Badge, 
  Tooltip,
  Input,
  Select,
  Row,
  Col
} from 'antd'
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-table'
import { 
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined,
  CloudServerOutlined,
  DatabaseOutlined,
  ApiOutlined,
  MonitorOutlined,
  ExportOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

import { Service, ServiceStatus, HealthStatus, ServiceType } from '@/types/service'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { Text, Title } = Typography
const { Search } = Input
const { Option } = Select

// Mock services data
const mockServices: Service[] = [
  {
    id: '1',
    name: 'user-core-service',
    displayName: '用户核心服务',
    type: ServiceType.MICROSERVICE,
    status: ServiceStatus.RUNNING,
    health: HealthStatus.HEALTHY,
    version: '1.2.3',
    endpoint: '/api/users',
    port: 8001,
    host: 'localhost',
    environment: 'production',
    tags: ['core', 'user-management'],
    dependencies: ['database', 'cache'],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-23T10:00:00Z',
    lastHealthCheck: '2025-01-23T10:00:00Z',
  },
  {
    id: '2',
    name: 'content-service',
    displayName: '内容管理服务',
    type: ServiceType.MICROSERVICE,
    status: ServiceStatus.STOPPED,
    health: HealthStatus.CRITICAL,
    version: '2.1.0',
    endpoint: '/api/content',
    port: 8002,
    environment: 'production',
    tags: ['content', 'cms'],
    dependencies: ['database'],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-23T10:00:00Z',
    lastHealthCheck: '2025-01-23T10:00:00Z',
  },
  {
    id: '3',
    name: 'redis-cache',
    displayName: 'Redis 缓存',
    type: ServiceType.CACHE,
    status: ServiceStatus.RUNNING,
    health: HealthStatus.HEALTHY,
    version: '7.0.0',
    port: 6379,
    environment: 'production',
    tags: ['cache', 'redis'],
    dependencies: [],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-23T10:00:00Z',
    lastHealthCheck: '2025-01-23T10:00:00Z',
  },
  {
    id: '4',
    name: 'api-gateway',
    displayName: 'API 网关',
    type: ServiceType.GATEWAY,
    status: ServiceStatus.STARTING,
    health: HealthStatus.DEGRADED,
    version: '3.1.4',
    endpoint: '/api',
    port: 8000,
    environment: 'production',
    tags: ['gateway', 'routing'],
    dependencies: ['user-core-service'],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-23T10:00:00Z',
    lastHealthCheck: '2025-01-23T10:00:00Z',
  },
]

/**
 * 服务列表页面
 */
const ServiceList: React.FC = () => {
  const navigate = useNavigate()
  const actionRef = useRef<ActionType>()
  const { hasPermission } = usePermission()
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<ServiceStatus | 'ALL'>('ALL')

  const canView = hasPermission(Permission.SERVICE_VIEW)
  const canControl = hasPermission(Permission.SERVICE_CONTROL)

  const fetchServices = async (params: any) => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    let filtered = [...mockServices]
    
    // Search filter
    if (params.search || searchText) {
      const search = (params.search || searchText).toLowerCase()
      filtered = filtered.filter(service => 
        service.name.toLowerCase().includes(search) ||
        service.displayName.toLowerCase().includes(search) ||
        service.tags.some(tag => tag.toLowerCase().includes(search))
      )
    }
    
    // Status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(service => service.status === statusFilter)
    }
    
    return { data: filtered, success: true, total: filtered.length }
  }

  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.RUNNING: return 'green'
      case ServiceStatus.STARTING: return 'blue'
      case ServiceStatus.STOPPING: return 'orange'
      case ServiceStatus.STOPPED: return 'red'
      case ServiceStatus.ERROR: return 'red'
      default: return 'gray'
    }
  }

  const getHealthColor = (health: HealthStatus) => {
    switch (health) {
      case HealthStatus.HEALTHY: return 'success'
      case HealthStatus.DEGRADED: return 'warning'
      case HealthStatus.CRITICAL: return 'error'
      case HealthStatus.UNHEALTHY: return 'error'
      default: return 'default'
    }
  }

  const getServiceIcon = (type: ServiceType) => {
    switch (type) {
      case ServiceType.MICROSERVICE: return <ApiOutlined />
      case ServiceType.DATABASE: return <DatabaseOutlined />
      case ServiceType.CACHE: return <CloudServerOutlined />
      case ServiceType.GATEWAY: return <MonitorOutlined />
      default: return <CloudServerOutlined />
    }
  }

  const handleServiceAction = async (action: 'start' | 'stop' | 'restart', service: Service) => {
    if (!canControl) {
      message.warning('您没有权限执行此操作')
      return
    }

    const actionTexts = {
      start: '启动',
      stop: '停止',
      restart: '重启',
    }

    Modal.confirm({
      title: `${actionTexts[action]}服务`,
      content: `确定要${actionTexts[action]}服务 "${service.displayName}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 2000))
          message.success(`服务${actionTexts[action]}成功`)
          actionRef.current?.reload()
        } catch (error) {
          message.error(`服务${actionTexts[action]}失败`)
        }
      },
    })
  }

  const handleBulkAction = (action: 'start' | 'stop' | 'restart') => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要操作的服务')
      return
    }

    if (!canControl) {
      message.warning('您没有权限执行此操作')
      return
    }

    const actionTexts = {
      start: '启动',
      stop: '停止',
      restart: '重启',
    }

    Modal.confirm({
      title: `批量${actionTexts[action]}`,
      content: `确定要${actionTexts[action]} ${selectedRowKeys.length} 个服务吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 3000))
          message.success(`批量${actionTexts[action]}成功`)
          setSelectedRowKeys([])
          actionRef.current?.reload()
        } catch (error) {
          message.error(`批量${actionTexts[action]}失败`)
        }
      },
    })
  }

  const columns: ProColumns<Service>[] = [
    {
      title: '服务信息',
      key: 'service',
      fixed: 'left',
      width: 250,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ 
            marginRight: '12px', 
            fontSize: '20px', 
            color: getStatusColor(record.status) 
          }}>
            {getServiceIcon(record.type)}
          </div>
          <div>
            <div style={{ fontWeight: 500, marginBottom: '4px' }}>
              {record.displayName}
            </div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.name} v{record.version}
            </Text>
            <div style={{ marginTop: '4px' }}>
              <Space wrap>
                {record.tags.slice(0, 2).map(tag => (
                  <Tag key={tag} size="small" color="blue">
                    {tag}
                  </Tag>
                ))}
                {record.tags.length > 2 && (
                  <Tag size="small" color="default">
                    +{record.tags.length - 2}
                  </Tag>
                )}
              </Space>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ServiceStatus) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '健康状态',
      dataIndex: 'health',
      key: 'health',
      width: 120,
      render: (health: HealthStatus) => (
        <Badge status={getHealthColor(health) as any} text={health} />
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: ServiceType) => (
        <Tag color="purple">{type}</Tag>
      ),
    },
    {
      title: '端点',
      key: 'endpoint',
      width: 150,
      render: (_, record) => (
        <div>
          {record.endpoint && (
            <div style={{ fontSize: '12px' }}>
              <Text code>{record.endpoint}</Text>
            </div>
          )}
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.host}:{record.port}
          </Text>
        </div>
      ),
    },
    {
      title: '环境',
      dataIndex: 'environment',
      key: 'environment',
      width: 80,
      render: (env: string) => (
        <Tag color={env === 'production' ? 'red' : env === 'staging' ? 'orange' : 'green'}>
          {env}
        </Tag>
      ),
    },
    {
      title: '依赖',
      dataIndex: 'dependencies',
      key: 'dependencies',
      width: 80,
      render: (deps: string[]) => (
        <div>
          {deps.length > 0 ? (
            <Tooltip title={deps.join(', ')}>
              <Badge count={deps.length} style={{ backgroundColor: '#1890ff' }} />
            </Tooltip>
          ) : (
            <Text type="secondary">无</Text>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/services/${record.id}`)}
            />
          </Tooltip>
          {canControl && (
            <>
              {record.status === ServiceStatus.STOPPED ? (
                <Tooltip title="启动服务">
                  <Button
                    type="text"
                    size="small"
                    icon={<PlayCircleOutlined />}
                    style={{ color: '#52c41a' }}
                    onClick={() => handleServiceAction('start', record)}
                  />
                </Tooltip>
              ) : (
                <Tooltip title="停止服务">
                  <Button
                    type="text"
                    size="small"
                    icon={<PauseCircleOutlined />}
                    style={{ color: '#ff4d4f' }}
                    onClick={() => handleServiceAction('stop', record)}
                  />
                </Tooltip>
              )}
              <Tooltip title="重启服务">
                <Button
                  type="text"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => handleServiceAction('restart', record)}
                />
              </Tooltip>
              <Tooltip title="配置管理">
                <Button
                  type="text"
                  size="small"
                  icon={<SettingOutlined />}
                  onClick={() => navigate(`/services/${record.id}/config`)}
                />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ]

  if (!canView) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限查看服务列表</p>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部和过滤器 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row justify="space-between" align="middle" style={{ marginBottom: '16px' }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>服务管理</Title>
            <Text type="secondary">管理和监控所有系统服务状态</Text>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('导出功能开发中')}
              >
                导出配置
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => actionRef.current?.reload()}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 过滤器 */}
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索服务名称、标签..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={() => actionRef.current?.reload()}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={statusFilter}
              onChange={(value) => {
                setStatusFilter(value)
                actionRef.current?.reload()
              }}
              style={{ width: '100%' }}
              placeholder="状态"
            >
              <Option value="ALL">全部状态</Option>
              <Option value={ServiceStatus.RUNNING}>运行中</Option>
              <Option value={ServiceStatus.STOPPED}>已停止</Option>
              <Option value={ServiceStatus.STARTING}>启动中</Option>
              <Option value={ServiceStatus.STOPPING}>停止中</Option>
              <Option value={ServiceStatus.ERROR}>错误</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 服务列表表格 */}
      <ProTable<Service>
        headerTitle="服务列表"
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={(params) => fetchServices({ ...params, search: searchText })}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        tableAlertRender={({ selectedRowKeys }) => (
          <div>
            已选择 <strong>{selectedRowKeys.length}</strong> 个服务
          </div>
        )}
        tableAlertOptionRender={() => (
          <Space>
            <Button 
              size="small" 
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => handleBulkAction('start')}
            >
              批量启动
            </Button>
            <Button 
              size="small" 
              danger
              icon={<PauseCircleOutlined />}
              onClick={() => handleBulkAction('stop')}
            >
              批量停止
            </Button>
            <Button 
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => handleBulkAction('restart')}
            >
              批量重启
            </Button>
          </Space>
        )}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        search={false}
        options={{
          reload: () => actionRef.current?.reload(),
          density: true,
          fullScreen: true,
          setting: true,
        }}
        scroll={{ x: 1200 }}
      />
    </div>
  )
}

export default ServiceList
 