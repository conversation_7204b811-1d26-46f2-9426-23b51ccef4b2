// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-15 12:00:00
// Modified: 2025-01-15 12:00:00

// analytics-service Data Analytics Service
// The data brain of CINA.CLUB platform, providing aggregated data query APIs and data insights
package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	"cina.club/pkg/config"
	"cina.club/pkg/logger"
	"cina.club/pkg/tracing"

	"cina.club/services/analytics-service/internal/adapter/cache"
	"cina.club/services/analytics-service/internal/adapter/http/handler"
	"cina.club/services/analytics-service/internal/adapter/http/middleware"
	"cina.club/services/analytics-service/internal/adapter/repository"
	"cina.club/services/analytics-service/internal/application/service"
)

// AppConfig Application configuration
type AppConfig struct {
	HTTP struct {
		Port int `mapstructure:"port" default:"8080"`
	} `mapstructure:"http"`
	Redis struct {
		Addr         string        `mapstructure:"addr" default:"localhost:6379"`
		Password     string        `mapstructure:"password"`
		DB           int           `mapstructure:"db" default:"0"`
		PoolSize     int           `mapstructure:"pool_size" default:"10"`
		MinIdleConns int           `mapstructure:"min_idle_conns" default:"5"`
		DialTimeout  time.Duration `mapstructure:"dial_timeout" default:"5s"`
		ReadTimeout  time.Duration `mapstructure:"read_timeout" default:"3s"`
		WriteTimeout time.Duration `mapstructure:"write_timeout" default:"3s"`
		PoolTimeout  time.Duration `mapstructure:"pool_timeout" default:"4s"`
	} `mapstructure:"redis"`
	DataWarehouse struct {
		Host     string `mapstructure:"host" default:"localhost"`
		Port     int    `mapstructure:"port" default:"5432"`
		Database string `mapstructure:"database" default:"analytics"`
		Username string `mapstructure:"username" default:"postgres"`
		Password string `mapstructure:"password"`
	} `mapstructure:"data_warehouse"`
	ClickHouse struct {
		Host     string `mapstructure:"host" default:"localhost"`
		Port     int    `mapstructure:"port" default:"9000"`
		Database string `mapstructure:"database" default:"analytics"`
		Username string `mapstructure:"username" default:"default"`
		Password string `mapstructure:"password"`
	} `mapstructure:"clickhouse"`
}

func main() {
	// Load configuration
	var cfg AppConfig
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	if err := config.LoadConfig(configPath, &cfg); err != nil {
		logrus.Fatal("Failed to load configuration:", err)
	}

	// Initialize logger
	loggerConfig := logger.Config{
		Level:  "info",
		Format: "json",
	}
	appLogger, err := logger.NewLogger(&loggerConfig)
	if err != nil {
		logrus.Fatal("Failed to initialize logger:", err)
	}

	// Initialize tracing
	tracingConfig := tracing.Config{
		Enabled:     true,
		ServiceName: "analytics-service",
	}
	shutdownTracing, err := tracing.Init(tracingConfig)
	if err != nil {
		logrus.Fatal("Failed to initialize tracing:", err)
	}
	defer shutdownTracing(context.Background())

	// Set viper values from config
	viper.Set("redis.addr", cfg.Redis.Addr)
	viper.Set("redis.password", cfg.Redis.Password)
	viper.Set("redis.db", cfg.Redis.DB)
	viper.Set("redis.pool_size", cfg.Redis.PoolSize)
	viper.Set("redis.min_idle_conns", cfg.Redis.MinIdleConns)
	viper.Set("redis.dial_timeout", cfg.Redis.DialTimeout)
	viper.Set("redis.read_timeout", cfg.Redis.ReadTimeout)
	viper.Set("redis.write_timeout", cfg.Redis.WriteTimeout)
	viper.Set("redis.pool_timeout", cfg.Redis.PoolTimeout)

	// Initialize data warehouse connection
	dataWarehouse, err := repository.NewDataWarehouse(cfg.DataWarehouse.Host, cfg.DataWarehouse.Port,
		cfg.DataWarehouse.Database, cfg.DataWarehouse.Username, cfg.DataWarehouse.Password)
	if err != nil {
		logrus.Fatal("Failed to initialize data warehouse:", err)
	}
	defer dataWarehouse.Close()

	// Set ClickHouse configuration in viper
	viper.Set("clickhouse.addr", fmt.Sprintf("%s:%d", cfg.ClickHouse.Host, cfg.ClickHouse.Port))
	viper.Set("clickhouse.database", cfg.ClickHouse.Database)
	viper.Set("clickhouse.username", cfg.ClickHouse.Username)
	viper.Set("clickhouse.password", cfg.ClickHouse.Password)
	viper.Set("clickhouse.dial_timeout", "10s")

	// Initialize ClickHouse connection
	clickHouse, err := repository.NewClickHouseRepository()
	if err != nil {
		logrus.Fatal("Failed to initialize ClickHouse:", err)
	}
	defer clickHouse.Close()

	// Initialize Redis cache
	redisCache, err := cache.NewRedisCache()
	if err != nil {
		logrus.Fatal("Failed to initialize Redis cache:", err)
	}
	defer redisCache.Close()

	// Initialize repository layer
	kpiRepo := repository.NewKPIRepository(dataWarehouse, clickHouse)
	userAnalyticsRepo := repository.NewUserAnalyticsRepository(dataWarehouse, clickHouse)
	businessMetricsRepo := repository.NewBusinessMetricsRepository(dataWarehouse, clickHouse)

	// Initialize application service layer
	kpiService := service.NewKPIService(kpiRepo, redisCache)
	userAnalyticsService := service.NewUserAnalyticsService(userAnalyticsRepo, redisCache)
	businessMetricsService := service.NewBusinessMetricsService(businessMetricsRepo, redisCache)

	// Initialize HTTP handlers
	kpiHandler := handler.NewKPIHandler(kpiService)
	userAnalyticsHandler := handler.NewUserAnalyticsHandler(userAnalyticsService)
	businessMetricsHandler := handler.NewBusinessMetricsHandler(businessMetricsService)

	// Setup HTTP routing
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(middleware.HTTPLoggingMiddleware(appLogger))
	router.Use(middleware.CORSMiddleware())

	// API version routing group
	v1 := router.Group("/api/v1/analytics")
	v1.Use(middleware.AuthMiddleware()) // S2S authentication

	// KPI related routes
	v1.GET("/kpis/daily-summary", kpiHandler.GetDailySummary)
	v1.GET("/kpis/platform-overview", kpiHandler.GetPlatformOverview)
	v1.GET("/kpis/growth-metrics", kpiHandler.GetGrowthMetrics)

	// User analytics routes
	v1.GET("/users/profile-features/:userId", userAnalyticsHandler.GetUserProfileFeatures)
	v1.GET("/users/behavior-analysis", userAnalyticsHandler.GetUserBehaviorAnalysis)
	v1.GET("/users/cohort-analysis", userAnalyticsHandler.GetCohortAnalysis)
	v1.GET("/users/funnel-analysis", userAnalyticsHandler.GetFunnelAnalysis)

	// Business metrics routes
	v1.GET("/business/revenue-metrics", businessMetricsHandler.GetRevenueMetrics)
	v1.GET("/business/service-performance", businessMetricsHandler.GetServicePerformance)
	v1.GET("/business/geographic-distribution", businessMetricsHandler.GetGeographicDistribution)

	// Health check and metrics endpoints
	router.GET("/health", healthCheckHandler(dataWarehouse, clickHouse, redisCache))
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// Start HTTP server
	httpPort := cfg.HTTP.Port
	if httpPort == 0 {
		httpPort = 8080
	}

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", httpPort),
		Handler: router,
	}

	go func() {
		logrus.Infof("Analytics service listening on port %d", httpPort)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatal("Failed to serve HTTP:", err)
		}
	}()

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("Shutting down analytics service...")

	// Shutdown HTTP server
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logrus.Error("HTTP server shutdown error:", err)
	}

	logrus.Info("Analytics service stopped")
}

// healthCheckHandler Health check handler
func healthCheckHandler(dataWarehouse repository.DataWarehouseInterface,
	clickHouse repository.ClickHouseInterface,
	cache *cache.RedisCache) gin.HandlerFunc {
	return func(c *gin.Context) {
		status := gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Unix(),
			"service":   "analytics-service",
		}

		// Check data warehouse connection
		if err := dataWarehouse.Ping(); err != nil {
			status["status"] = "unhealthy"
			status["data_warehouse"] = "disconnected"
			c.JSON(http.StatusServiceUnavailable, status)
			return
		}
		status["data_warehouse"] = "connected"

		// Check ClickHouse connection
		if err := clickHouse.Ping(); err != nil {
			status["status"] = "unhealthy"
			status["clickhouse"] = "disconnected"
			c.JSON(http.StatusServiceUnavailable, status)
			return
		}
		status["clickhouse"] = "connected"

		// Check Redis connection - use context version
		ctx, cancel := context.WithTimeout(c.Request.Context(), 3*time.Second)
		defer cancel()
		if err := cache.Ping(ctx); err != nil {
			status["status"] = "unhealthy"
			status["cache"] = "disconnected"
			c.JSON(http.StatusServiceUnavailable, status)
			return
		}
		status["cache"] = "connected"

		c.JSON(http.StatusOK, status)
	}
}
