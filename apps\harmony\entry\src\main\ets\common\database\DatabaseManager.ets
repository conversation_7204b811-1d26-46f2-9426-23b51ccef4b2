/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { hilog } from '@kit.PerformanceAnalysisKit';
import { relationalStore } from '@kit.ArkData';
import { common } from '@kit.AbilityKit';

/**
 * 数据库管理器
 * 
 * 负责SQLite数据库的创建、管理和操作
 */
export class DatabaseManager {
  private static readonly TAG = 'DatabaseManager';
  
  private context: common.UIAbilityContext;
  private store: relationalStore.RdbStore | null = null;
  private isInitialized: boolean = false;

  constructor(context: common.UIAbilityContext) {
    this.context = context;
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      const config: relationalStore.StoreConfig = {
        name: 'cina_club.db',
        securityLevel: relationalStore.SecurityLevel.S1
      };

      this.store = await relationalStore.getRdbStore(this.context, config);
      await this.createTables();
      
      this.isInitialized = true;
      hilog.info(0x0000, DatabaseManager.TAG, 'Database initialized successfully');
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Database initialization failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行SQL查询
   */
  async query(sql: string, params?: Array<relationalStore.ValueType>): Promise<relationalStore.ResultSet> {
    await this.ensureInitialized();
    
    try {
      const resultSet = await this.store!.querySql(sql, params);
      hilog.debug(0x0000, DatabaseManager.TAG, `Query executed: ${sql}`);
      return resultSet;
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Query failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行SQL语句（非查询）
   */
  async execute(sql: string, params?: Array<relationalStore.ValueType>): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await this.store!.executeSql(sql, params);
      hilog.debug(0x0000, DatabaseManager.TAG, `SQL executed: ${sql}`);
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Execute SQL failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 插入数据
   */
  async insert(table: string, values: relationalStore.ValuesBucket): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const rowId = await this.store!.insert(table, values);
      hilog.debug(0x0000, DatabaseManager.TAG, `Insert into ${table}, rowId: ${rowId}`);
      return rowId;
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Insert failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新数据
   */
  async update(
    table: string, 
    values: relationalStore.ValuesBucket, 
    whereClause?: string, 
    whereArgs?: Array<relationalStore.ValueType>
  ): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const predicates = new relationalStore.RdbPredicates(table);
      if (whereClause && whereArgs) {
        predicates.equalTo(whereClause, whereArgs[0]);
      }
      
      const changedRows = await this.store!.update(values, predicates);
      hilog.debug(0x0000, DatabaseManager.TAG, `Update ${table}, changed rows: ${changedRows}`);
      return changedRows;
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Update failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除数据
   */
  async delete(
    table: string, 
    whereClause?: string, 
    whereArgs?: Array<relationalStore.ValueType>
  ): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const predicates = new relationalStore.RdbPredicates(table);
      if (whereClause && whereArgs) {
        predicates.equalTo(whereClause, whereArgs[0]);
      }
      
      const deletedRows = await this.store!.delete(predicates);
      hilog.debug(0x0000, DatabaseManager.TAG, `Delete from ${table}, deleted rows: ${deletedRows}`);
      return deletedRows;
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Delete failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 开始事务
   */
  async beginTransaction(): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await this.store!.beginTransaction();
      hilog.debug(0x0000, DatabaseManager.TAG, 'Transaction started');
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Begin transaction failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 提交事务
   */
  async commit(): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await this.store!.commit();
      hilog.debug(0x0000, DatabaseManager.TAG, 'Transaction committed');
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Commit transaction failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 回滚事务
   */
  async rollback(): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await this.store!.rollBack();
      hilog.debug(0x0000, DatabaseManager.TAG, 'Transaction rolled back');
    } catch (error) {
      hilog.error(0x0000, DatabaseManager.TAG, `Rollback transaction failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.store) {
      try {
        this.store = null;
        this.isInitialized = false;
        hilog.info(0x0000, DatabaseManager.TAG, 'DatabaseManager cleaned up');
      } catch (error) {
        hilog.error(0x0000, DatabaseManager.TAG, `Cleanup failed: ${error.message}`);
      }
    }
  }

  // ===================== 私有方法 =====================

  /**
   * 确保数据库已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * 创建数据库表
   */
  private async createTables(): Promise<void> {
    const tables = [
      // 用户本地缓存表
      `CREATE TABLE IF NOT EXISTS user_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        key TEXT NOT NULL,
        value TEXT,
        encrypted_value BLOB,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        UNIQUE(user_id, key)
      )`,
      
      // PKB条目表（加密存储）
      `CREATE TABLE IF NOT EXISTS pkb_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entry_id TEXT NOT NULL UNIQUE,
        user_id TEXT NOT NULL,
        title_encrypted BLOB,
        content_encrypted BLOB,
        tags TEXT,
        vector_embedding BLOB,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        synced_at INTEGER
      )`,
      
      // 聊天消息本地缓存表
      `CREATE TABLE IF NOT EXISTS chat_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id TEXT NOT NULL UNIQUE,
        chat_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        content_encrypted BLOB,
        message_type INTEGER NOT NULL,
        timestamp INTEGER NOT NULL,
        status INTEGER NOT NULL DEFAULT 0
      )`,
      
      // 设置表
      `CREATE TABLE IF NOT EXISTS app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at INTEGER NOT NULL
      )`
    ];

    for (const sql of tables) {
      try {
        await this.store!.executeSql(sql);
        hilog.debug(0x0000, DatabaseManager.TAG, 'Table created successfully');
      } catch (error) {
        hilog.error(0x0000, DatabaseManager.TAG, `Create table failed: ${error.message}`);
        throw error;
      }
    }
  }
} 