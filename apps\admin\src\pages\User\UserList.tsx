/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 17:30:00
*/

import React, { useRef, useState, useEffect, useCallback } from 'react'
import { Button, Tag, Space, Modal, message, Tooltip, Dropdown, Menu } from 'antd'
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-table'
import { PlusOutlined, ExportOutlined, UserOutlined, EllipsisOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useMutation, useQueryClient } from '@tanstack/react-query'

import { User, UserStatus, UserRole, UserQueryParams } from '@/types/user'
import { usePermission } from '@/hooks/usePermission'
import { Permission } from '@/types/user'
import { Loading } from '@/components/Loading'
import { VirtualTable } from '@/components/VirtualTable'
import { StatusTag, RoleTag } from '@/components/MemoizedTags'
import { UserApiService } from '@/services/UserApiService'
import { useAnalytics } from '@/hooks/useAnalytics'

// Mock data generation
const generateMockUsers = (count: number): User[] => {
  const users: User[] = []
  for (let i = 1; i <= count; i++) {
    users.push({
      id: i.toString(),
      email: `user${i}@example.com`,
      username: `user${i}`,
      firstName: `User`,
      lastName: `${i}`,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=user${i}`,
      status: i % 5 === 0 ? UserStatus.SUSPENDED : UserStatus.ACTIVE,
      roles: i % 3 === 0 ? [UserRole.ADMIN] : [UserRole.VIEWER],
      permissions: [Permission.USER_VIEW],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
      emailVerified: true,
      phoneVerified: false,
      twoFactorEnabled: i % 2 === 0,
    })
  }
  return users
}

const mockUsers = generateMockUsers(2000) // Generate 2000 mock users

// Status color mapping
const statusColors: Record<UserStatus, string> = {
  [UserStatus.ACTIVE]: 'green',
  [UserStatus.INACTIVE]: 'orange',
  [UserStatus.SUSPENDED]: 'red',
  [UserStatus.PENDING]: 'blue',
  [UserStatus.DELETED]: 'gray',
}

// Role color mapping
const roleColors: Record<UserRole, string> = {
  [UserRole.SUPER_ADMIN]: 'purple',
  [UserRole.ADMIN]: 'blue',
  [UserRole.OPERATIONS]: 'cyan',
  [UserRole.CUSTOMER_SERVICE]: 'green',
  [UserRole.CONTENT_MODERATOR]: 'orange',
  [UserRole.ANALYST]: 'geekblue',
  [UserRole.VIEWER]: 'gray',
}

/**
 * 用户列表页面
 */
const UserList: React.FC = () => {
  const navigate = useNavigate()
  const actionRef = useRef<ActionType>()
  const { hasPermission } = usePermission()
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [loading, setLoading] = useState(false)
  const [tableHeight, setTableHeight] = useState(window.innerHeight - 300)
  const queryClient = useQueryClient()
  const { trackEvent } = useAnalytics()

  useEffect(() => {
    const handleResize = () => {
      setTableHeight(window.innerHeight - 300)
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 权限检查
  const canView = hasPermission(Permission.USER_VIEW)
  const canCreate = hasPermission(Permission.USER_CREATE)
  const canEdit = hasPermission(Permission.USER_EDIT)
  const canDelete = hasPermission(Permission.USER_DELETE)

  // 表格列配置
  const columns: ProColumns<User>[] = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      fixed: 'left',
      width: 220,
      render: (_: any, record: User) => (
        <Space>
          <img
            src={record.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${record.username}`}
            alt={record.username}
            style={{ width: 32, height: 32, borderRadius: '50%' }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{record.firstName} {record.lastName}</div>
            <div style={{ fontSize: 12, color: '#666' }}>@{record.username}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      copyable: true,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record: User) => <StatusTag status={record.status} />,
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 200,
      render: (_, record: User) => (
        <Space wrap>
          {record.roles.map(role => <RoleTag key={role} role={role} />)}
        </Space>
      ),
    },
    {
      title: '验证状态',
      key: 'verification',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size={2}>
          <div style={{ fontSize: 12 }}>
            邮箱: {record.emailVerified ? 
              <Tag color="green" size="small">已验证</Tag> : 
              <Tag color="orange" size="small">未验证</Tag>
            }
          </div>
          <div style={{ fontSize: 12 }}>
            手机: {record.phoneVerified ? 
              <Tag color="green" size="small">已验证</Tag> : 
              <Tag color="orange" size="small">未验证</Tag>
            }
          </div>
        </Space>
      ),
    },
    {
      title: '2FA',
      dataIndex: 'twoFactorEnabled',
      key: 'twoFactorEnabled',
      width: 80,
      render: (enabled: boolean) => (
        <Tag color={enabled ? 'green' : 'default'} size="small">
          {enabled ? '已启用' : '未启用'}
        </Tag>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 150,
      valueType: 'dateTime',
      render: (_, record) => (
        <div style={{ fontSize: 12 }}>
          {record.lastLoginAt ? 
            new Date(record.lastLoginAt).toLocaleString() : 
            '从未登录'
          }
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      valueType: 'dateTime',
      hideInTable: true,
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 80,
      align: 'center',
      render: (_: any, record: User) => {
        const canPerformEdit = canEdit;
        const canPerformDelete = canDelete && record.status !== UserStatus.DELETED;
        const hasActions = canPerformEdit || canPerformDelete;

        const menuItems = [
          {
            key: 'view',
            label: 'View Details',
            onClick: () => navigate(`/users/${record.id}`),
          },
          canPerformEdit && {
            key: 'edit',
            label: 'Edit User',
            onClick: () => navigate(`/users/${record.id}/edit`),
          },
          canPerformDelete && {
            key: 'delete',
            label: 'Delete User',
            danger: true,
            onClick: () => handleDeleteUser(record),
          },
        ].filter(Boolean) as any[]; // Filter out false values from disabled actions

        if (!hasActions) {
          // If no actions are available, render nothing or a disabled placeholder
          return (
            <Tooltip title="No actions available">
              <Button type="text" icon={<EllipsisOutlined />} disabled />
            </Tooltip>
          );
        }

        return (
          <Dropdown overlay={<Menu items={menuItems} />} trigger={['click']}>
            <Button
              type="text"
              icon={<EllipsisOutlined />}
              aria-label={`Actions for user ${record.username}`}
            />
          </Dropdown>
        );
      },
    },
  ]

  // Optimistic Delete Mutation
  const deleteUserMutation = useMutation({
    mutationFn: (userId: string) => UserApiService.deleteUser(userId), // Assuming this API call exists
    onMutate: async (userId: string) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ['users'] });

      // Snapshot the previous value
      const previousUsers = queryClient.getQueryData(['users']);

      // Optimistically update to the new value
      queryClient.setQueryData(['users'], (old: any) => ({
        ...old,
        data: old.data.filter((user: User) => user.id !== userId),
      }));

      // Return a context object with the snapshotted value
      return { previousUsers };
    },
    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (err, userId, context) => {
      queryClient.setQueryData(['users'], context?.previousUsers);
      message.error('Failed to delete user. The user has been restored.');
    },
    // Always refetch after error or success:
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  const handleDeleteUser = useCallback((user: User) => {
    Modal.confirm({
      title: 'Confirm Deletion',
      content: `Are you sure you want to delete ${user.firstName}? This action cannot be undone.`,
      onOk: () => {
        deleteUserMutation.mutate(user.id);
      },
    });
  }, [deleteUserMutation]);

  const handleBulkAction = useCallback((action: string) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要操作的用户')
      return
    }

    const actionTexts = {
      activate: '激活',
      suspend: '暂停',
      delete: '删除',
    }

    Modal.confirm({
      title: `批量${actionTexts[action as keyof typeof actionTexts]}`,
      content: `确定要${actionTexts[action as keyof typeof actionTexts]} ${selectedRowKeys.length} 个用户吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 1000))
          message.success(`批量${actionTexts[action as keyof typeof actionTexts]}成功`)
          setSelectedRowKeys([])
          actionRef.current?.reload()
        } catch (error) {
          message.error(`批量${actionTexts[action as keyof typeof actionTexts]}失败`)
        }
      },
    })
  }, [selectedRowKeys])

  const handleExport = useCallback(async () => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      message.success('用户数据导出成功')
    } catch (error) {
      message.error('导出失败')
    } finally {
      setLoading(false)
    }
  }, [])

  if (!canView) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限查看用户列表</p>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <ProTable<User>
        headerTitle="用户管理 (Virtual-Scroll: 2000 items)"
        actionRef={actionRef}
        rowKey="id"
        loading={loading}
        columns={columns}
        request={fetchUsers}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
          <div>
            已选择 <strong>{selectedRowKeys.length}</strong> 项
            <a style={{ marginLeft: 8 }} onClick={onCleanSelected}>
              取消选择
            </a>
          </div>
        )}
        tableAlertOptionRender={() => (
          <Space>
            <Button size="small" onClick={() => handleBulkAction('activate')}>批量激活</Button>
            <Button size="small" onClick={() => handleBulkAction('suspend')}>批量暂停</Button>
            <Button size="small" danger onClick={() => handleBulkAction('delete')}>批量删除</Button>
          </Space>
        )}
        toolBarRender={() => [
          <Button key="export" icon={<ExportOutlined />} onClick={handleExport} loading={loading}>导出数据</Button>,
          canCreate && <Button key="create" type="primary" icon={<PlusOutlined />} onClick={() => navigate('/users/create')}>新建用户</Button>,
        ]}
        pagination={false}
        search={{
          labelWidth: 'auto',
        }}
        options={{
          reload: () => actionRef.current?.reload(),
          density: true,
          fullScreen: true,
          setting: true,
        }}
        scroll={{ y: tableHeight, x: 1200 }}
        components={{
          body: (rawData: readonly User[]) => (
            <VirtualTable<User>
              columns={columns}
              dataSource={rawData}
              scroll={{ y: tableHeight, x: 1200 }}
            />
          ),
        }}
      />
      <style jsx global>{`
        .virtual-table .ant-table-container {
          border: 1px solid #f0f0f0;
        }
        .virtual-table .ant-table-tbody > tr > td {
          border-bottom: 1px solid #f0f0f0;
          padding: 0;
        }
        .virtual-table-cell {
          box-sizing: border-box;
          padding: 8px;
          border-bottom: 1px solid #f0f0f0;
          height: 54px;
        }
        .react-resizable {
          position: relative;
        }
        .react-resizable-handle {
          position: absolute;
          width: 10px;
          height: 100%;
          bottom: 0;
          right: -5px;
          cursor: col-resize;
          z-index: 1;
        }

        /* Enhance focus visibility for all focusable elements */
        :focus-visible {
          outline: 2px solid #1DA57A;
          outline-offset: 2px;
          box-shadow: 0 0 0 4px rgba(29, 165, 122, 0.2);
        }
      `}</style>
    </div>
  )
}

export default UserList 