好的，遵照您的指示。我将为您生成一份专门针对 **`notification-dispatch-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`notification-dispatch-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**多渠道/多提供商的适配与智能路由、高级模板引擎、用户偏好与防打扰策略的实现，以及作为一个高吞吐量分发中枢的可靠性与可扩展性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `notification-dispatch-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `notification-dispatch-service-srs.md` (v2.0)
**核心架构**: 事件驱动 + 策略模式(Strategy Pattern) + 适配器模式(Adapter Pattern)

## 1. 概述

`notification-dispatch-service` 是CINA.CLUB平台用户触达的“**最后一公里执行者**”和“**智能通信网关**”。它是一个高吞吐量的后台服务，负责将平台内部的业务指令转化为对用户的实际通知。其核心挑战在于：
1.  **多渠道与异构API**: 需要适配多种通知渠道（Push, Email, SMS），且每个渠道下都有多个功能各异的第三方提供商（APNS, FCM, SendGrid, Twilio等）。
2.  **个性化与多语言**: 通知内容需要支持高度的个性化和多语言，以提升用户体验。
3.  **用户偏好与合规性**: 必须严格遵守用户设置的通知偏好和平台的防打扰策略（频率控制、静默时段），避免骚扰用户。
4.  **高吞吐与高送达率**: 需要能处理平台所有服务产生的高峰通知请求，并保证通知的可靠送达。
5.  **状态闭环**: 需要追踪每次发送的状态，并处理来自第三方服务商的异步回调，以优化发送策略。

本架构设计通过采用**事件驱动**的架构，结合**策略/适配器模式**来处理多渠道和多提供商，并内置一个强大的**决策与渲染引擎**，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (通知处理管道)

```mermaid
graph TD
    subgraph "上游服务"
        style "上游服务" fill:#eee
        SourceService[e.g., chat-api-service]
    end

    subgraph "NotificationDispatchService"
        style NotificationDispatchService fill:#e0f7fa
        A[Kafka Consumer<br/><em>adapter/event</em>]
        B[DispatchEngine<br/><em>application/service</em>]
        C[DecisionMaker<br/><em>domain/service</em>]
        D[TemplateEngine<br/><em>domain/service</em>]
        E{ChannelStrategyFactory<br/><em>domain/strategy</em>}
        F{Channel Strategies<br/>(Push, Email, SMS)}
        G[Provider Adapters<br/>(FCM, APNS, SendGrid)<br/><em>adapter/provider</em>]
        H[Repository<br/><em>adapter/repository</em>]
        I[UserCoreClient<br/><em>adapter/client</em>]
    end

    subgraph "外部依赖"
        style "外部依赖" fill:#f3e5f5
        UserCore[user-core-service]
        P1[FCM/APNS]
        P2[SendGrid/SES]
        P3[Twilio]
    end

    SourceService -- "1. Publish DispatchNotificationCommand" --> Kafka[(Kafka)]
    Kafka -- "2. Consume" --> A
    A -- "调用" --> B
    
    B -- "3. Get User Context" --> I
    I -- "获取偏好/Token/语言" --> UserCore
    UserCore -- "User Context" --> I --> B
    
    B -- "4. Make Channel Decision" --> C
    C -- "根据优先级/偏好/防打扰策略" --> C
    C -- "Returns ordered channel list [PUSH, EMAIL]" --> B
    
    B -- "5. Render Content" --> D
    D -- "使用模板和上下文" --> D
    D -- "Rendered Content" --> B

    loop For each channel in list
        B -- "6. Get Channel Strategy" --> E
        E -- "Returns PushStrategy" --> F
        
        B -- "7. Execute Strategy" --> F
        F -- "8. Use Provider Adapter" --> G
        G -- "9. Call External API" --> P1
    end
    
    F -- "10. Log dispatch attempt" --> H
```

### 2.2 最终目录结构 (`services/notification-dispatch-service/`)

```
notification-dispatch-service/
├── cmd/server/
│   └── main.go                 # API(Webhook)服务和事件消费者的统一入口
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   └── user_core_client.go
│   │   ├── event/
│   │   │   └── notification_consumer.go
│   │   ├── grpc/               # (可选)提供查询发送日志等内部API
│   │   │   └── handler.go
│   │   ├── http/
│   │   │   └── webhook_handler.go # 接收第三方状态回调
│   │   ├── provider/           # ✨ 提供商适配器实现 ✨
│   │   │   ├── interface.go
│   │   │   ├── push/
│   │   │   │   ├── fcm_adapter.go
│   │   │   │   └── apns_adapter.go
│   │   │   └── email/
│   │   │       └── sendgrid_adapter.go
│   │   └── repository/
│   │       └── postgres_repo.go # 存储发送日志
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── dispatch_engine.go # ✨ 核心应用服务, 流程编排者 ✨
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       ├── service/
│       │   ├── decision_maker.go # ✨ 渠道决策与防打扰服务 ✨
│       │   └── template_engine.go# ✨ 模板渲染服务 ✨
│       └── strategy/               # ✨ 渠道策略模式实现 ✨
│           ├── interface.go      # 定义ChannelStrategy接口
│           ├── push_strategy.go
│           ├── email_strategy.go
│           └── factory.go
├── config/
│   └── templates/              # ✨ 通知模板目录 ✨
│       ├── en/
│       │   └── task_accepted.json
│       └── zh/
│           └── task_accepted.json
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/templates/` - 通知模板库

这是实现内容与逻辑分离的关键。
```json
// config/templates/en/task_accepted.json
{
  "channels": {
    "push": {
      "title": "Your task '{{.task.title}}' was accepted!",
      "body": "{{.actor.name}} has accepted your task. Tap to see details."
    },
    "email": {
      "subject": "Great news! Your task '{{.task.title}}' has been accepted",
      "html_body_template": "task_accepted.html" // 指向一个HTML模板文件
    }
  }
}
```

### 3.2 `domain/` - 领域层 (The Communication Rules)

*   `domain/model/`: 定义`Notification`, `DispatchLog`等核心领域对象。
*   **`domain/service/template_engine.go`**:
    *   **`TemplateEngine`**: 在启动时加载所有`config/templates/`下的模板文件到内存中。
    *   **`Render(templateKey, lang, channel, context)`**:
        1.  根据`templateKey`和`lang`找到对应的模板。
        2.  使用Go的`text/template`或`html/template`，将`context`数据渲染到模板中。
        3.  返回渲染好的标题和内容。
*   **`domain/service/decision_maker.go`**: **这是智能分发和防打扰的核心**。
    *   **`DecisionMaker`**: 注入`Cache`（用于频率控制）等依赖。
    *   **`DecideChannels(request, userPreferences)`**:
        1.  **优先级过滤**: 根据`request.Priority`，确定候选渠道列表。
        2.  **用户偏好过滤**: 根据`userPreferences`，从候选列表中移除用户已关闭的渠道。
        3.  **防打扰检查**:
            a. **频率控制**: 对每个候选渠道，调用`cache.CheckFrequencyLimit(...)`检查是否超出频率限制。
            b. **静默时段**: 检查当前时间是否在用户的静默时段内，并过滤掉非紧急的通知。
        4.  返回一个**有序的、最终要执行的**渠道列表（如`[PUSH, EMAIL]`）。

*   **`domain/strategy/`**: **策略模式的实现，用于处理不同渠道的发送逻辑**。
    *   `interface.go`: 定义`ChannelStrategy`接口。
        ```go
        type ChannelStrategy interface {
            // Dispatch方法封装了该渠道的所有发送逻辑，包括选择提供商
            Dispatch(ctx, user, renderedContent) error
        }
        ```
    *   `push_strategy.go`:
        *   实现了`Dispatch`方法。
        *   内部逻辑：根据用户的设备类型（iOS/Android/HarmonyOS），从`provider.Factory`中获取对应的**`PushProviderAdapter`**（APNS/FCM/HMS）。
        *   调用适配器的`Send`方法。
    *   `factory.go`: `ChannelStrategyFactory`根据渠道类型（`PUSH`, `EMAIL`）返回一个具体的策略实例。

### 3.3 `application/` - 应用层 (The Dispatch Engine)

*   **`application/service/dispatch_engine.go`**: 实现`DispatchService`接口，是所有通知请求的唯一入口和流程编排者。
    *   **`ProcessNotification(ctx, command)`**:
        1.  **获取用户上下文**: 调用`userCoreClient.GetUserDetails()`获取用户偏好、语言、时区和所有渠道的联系方式/Token。
        2.  **决策**: 调用`domain.DecisionMaker.DecideChannels()`获取最终的渠道列表。
        3.  **如果渠道列表为空**: 记录为`SKIPPED_BY_POLICY`并结束。
        4.  **渲染**: 调用`domain.TemplateEngine.Render()`为每个目标渠道生成内容。
        5.  **循环执行**: **按顺序**遍历决策好的渠道列表。
            a. 使用`strategy.Factory`获取渠道策略。
            b. 调用`strategy.Dispatch()`。
            c. **降级逻辑**: 如果`Dispatch`返回一个可降级的错误（如用户没有有效的Push Token），则继续尝试下一个渠道。如果返回成功或不可恢复的错误，则中止流程。
        6.  **记录**: 所有发送尝试都必须被记录到`DispatchLog`中。

### 3.4 `adapter/` - 适配层 (The Bridge to External Systems)

*   **`adapter/provider/`**: **适配器模式的实现，用于适配不同第三方服务商**。
    *   `interface.go`: 定义`PushProvider`, `EmailProvider`等统一接口。
    *   `push/fcm_adapter.go`, `email/sendgrid_adapter.go`: 封装对具体外部API的HTTP/gRPC客户端调用、认证和错误处理。**每个适配器都应内置熔断器和重试逻辑**。
*   **`adapter/event/`**:
    *   `notification_consumer.go`: 封装`pkg/messaging`，消费平台所有服务发布的`DispatchNotificationCommand`事件，并调用`application.DispatchEngine.ProcessNotification`。
*   **`adapter/http/webhook_handler.go`**:
    *   提供`POST /webhooks/{provider}`端点。
    *   验证Webhook请求的签名。
    *   将回调内容（如`delivered`, `bounced`）解析后，更新`dispatch_logs`表中对应记录的状态。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`notification-dispatch-service`：
1.  **双重策略模式**:
    *   **渠道策略**: 使用`ChannelStrategy`（Push, Email, SMS）来封装不同渠道的业务逻辑（如Push需要判断设备类型）。
    *   **提供商适配器**: 在每个渠道策略内部，使用`ProviderAdapter`来封装对具体第三方服务商API的调用。
    *   这种双层抽象使得系统极度灵活和可扩展。
2.  **逻辑与执行分离的决策引擎**: `DecisionMaker`领域服务负责所有复杂的规则判断（偏好、防打扰），将“决定发什么、发给谁”的逻辑与“如何发送”的执行逻辑完全分离。
3.  **高吞吐的事件驱动架构**: 以Kafka消费者作为服务主入口，能够轻松应对平台流量洪峰，并通过增加消费者实例来水平扩展处理能力。
4.  **状态闭环与可观测性**: 通过详细的发送日志和对Webhook回调的处理，实现了对每次通知发送生命周期的完整追踪，为运营分析和发送策略优化提供了数据基础。

这种架构确保了`notification-dispatch-service`能够以一种**智能、可靠、尊重用户且成本可控**的方式，将平台的声音传递给每一位用户，是维系用户关系和提升产品价值的关键通信中枢。