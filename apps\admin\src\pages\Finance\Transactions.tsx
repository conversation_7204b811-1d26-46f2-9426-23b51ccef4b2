/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:35:00
 * Modified: 2025-01-23 16:35:00
 */

import React, { useState } from 'react';
import {
  Card,
  Table,
  Space,
  Button,
  Input,
  Select,
  DatePicker,
  Tag,
  Typography,
  Modal,
  Form,
  InputNumber,
  Tooltip,
  Drawer,
  Descriptions,
  Alert,
  Row,
  Col,
  Statistic,
  Progress,
  List,
  Avatar,
  Divider,
  Badge,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  EyeOutlined,
  UndoOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CreditCardOutlined,
  DollarOutlined,
  PayCircleOutlined,
  BankOutlined,
  WalletOutlined,
  ReloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

interface Transaction {
  id: string;
  type: 'payment' | 'refund' | 'subscription' | 'fee' | 'chargeback' | 'payout';
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed' | 'cancelled' | 'disputed';
  description: string;
  customer: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  paymentMethod: {
    type: 'credit_card' | 'paypal' | 'bank_transfer' | 'crypto' | 'wallet';
    last4?: string;
    brand?: string;
    details: string;
  };
  fees: {
    platform: number;
    payment: number;
    total: number;
  };
  metadata: {
    orderId?: string;
    subscriptionId?: string;
    invoiceId?: string;
    gateway: string;
    country: string;
    ip: string;
  };
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
  }>;
  createdAt: string;
  updatedAt: string;
  refundable: boolean;
  refundedAmount?: number;
}

interface TransactionStats {
  totalTransactions: number;
  totalAmount: number;
  successfulTransactions: number;
  failedTransactions: number;
  refundedAmount: number;
  averageAmount: number;
  topPaymentMethods: Array<{
    method: string;
    count: number;
    amount: number;
  }>;
}

const Transactions: React.FC = () => {
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
  const [isRefundModalVisible, setIsRefundModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    status: '',
    paymentMethod: '',
    dateRange: null as any,
    amountRange: { min: undefined, max: undefined }
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // Mock data queries
  const { data: transactions = [], isLoading } = useQuery({
    queryKey: ['transactions', filters, pagination.current, pagination.pageSize],
    queryFn: async (): Promise<{ data: Transaction[]; total: number }> => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTransactions: Transaction[] = [
        {
          id: 'txn_1',
          type: 'payment',
          amount: 29.99,
          currency: 'USD',
          status: 'completed',
          description: 'Professional Plan - Monthly Subscription',
          customer: {
            id: 'user_123',
            name: 'John Doe',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John'
          },
          paymentMethod: {
            type: 'credit_card',
            last4: '4242',
            brand: 'Visa',
            details: 'Visa ending in 4242'
          },
          fees: {
            platform: 0.87,
            payment: 1.17,
            total: 2.04
          },
          metadata: {
            orderId: 'ord_abc123',
            subscriptionId: 'sub_def456',
            gateway: 'stripe',
            country: 'US',
            ip: '*************'
          },
          timeline: [
            { status: 'pending', timestamp: '2025-01-23T15:30:00Z' },
            { status: 'completed', timestamp: '2025-01-23T15:30:15Z', note: 'Payment processed successfully' }
          ],
          createdAt: '2025-01-23T15:30:00Z',
          updatedAt: '2025-01-23T15:30:15Z',
          refundable: true
        },
        {
          id: 'txn_2',
          type: 'refund',
          amount: -19.99,
          currency: 'USD',
          status: 'completed',
          description: 'Refund for Basic Plan',
          customer: {
            id: 'user_456',
            name: 'Jane Smith',
            email: '<EMAIL>'
          },
          paymentMethod: {
            type: 'paypal',
            details: 'PayPal account'
          },
          fees: {
            platform: -0.58,
            payment: -0.78,
            total: -1.36
          },
          metadata: {
            orderId: 'ord_xyz789',
            gateway: 'paypal',
            country: 'CA',
            ip: '*************'
          },
          timeline: [
            { status: 'pending', timestamp: '2025-01-23T14:00:00Z' },
            { status: 'completed', timestamp: '2025-01-23T14:00:30Z', note: 'Refund processed' }
          ],
          createdAt: '2025-01-23T14:00:00Z',
          updatedAt: '2025-01-23T14:00:30Z',
          refundable: false,
          refundedAmount: 19.99
        },
        {
          id: 'txn_3',
          type: 'subscription',
          amount: 299.99,
          currency: 'USD',
          status: 'completed',
          description: 'Enterprise Plan - Annual Subscription',
          customer: {
            id: 'user_789',
            name: 'Bob Johnson',
            email: '<EMAIL>'
          },
          paymentMethod: {
            type: 'bank_transfer',
            details: 'Bank transfer'
          },
          fees: {
            platform: 8.70,
            payment: 2.99,
            total: 11.69
          },
          metadata: {
            subscriptionId: 'sub_ghi789',
            gateway: 'bank',
            country: 'UK',
            ip: '*************'
          },
          timeline: [
            { status: 'pending', timestamp: '2025-01-23T13:00:00Z' },
            { status: 'completed', timestamp: '2025-01-23T13:05:00Z', note: 'Bank transfer confirmed' }
          ],
          createdAt: '2025-01-23T13:00:00Z',
          updatedAt: '2025-01-23T13:05:00Z',
          refundable: true
        },
        {
          id: 'txn_4',
          type: 'payment',
          amount: 49.99,
          currency: 'USD',
          status: 'failed',
          description: 'Premium Plan - Monthly Subscription',
          customer: {
            id: 'user_101',
            name: 'Alice Brown',
            email: '<EMAIL>'
          },
          paymentMethod: {
            type: 'credit_card',
            last4: '1234',
            brand: 'Mastercard',
            details: 'Mastercard ending in 1234'
          },
          fees: {
            platform: 0,
            payment: 0,
            total: 0
          },
          metadata: {
            orderId: 'ord_fail123',
            gateway: 'stripe',
            country: 'US',
            ip: '*************'
          },
          timeline: [
            { status: 'pending', timestamp: '2025-01-23T12:00:00Z' },
            { status: 'failed', timestamp: '2025-01-23T12:00:10Z', note: 'Insufficient funds' }
          ],
          createdAt: '2025-01-23T12:00:00Z',
          updatedAt: '2025-01-23T12:00:10Z',
          refundable: false
        }
      ];

      return {
        data: mockTransactions,
        total: 156
      };
    }
  });

  const { data: transactionStats } = useQuery({
    queryKey: ['transaction-stats'],
    queryFn: async (): Promise<TransactionStats> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return {
        totalTransactions: 1247,
        totalAmount: 89456.78,
        successfulTransactions: 1156,
        failedTransactions: 91,
        refundedAmount: 3456.78,
        averageAmount: 71.78,
        topPaymentMethods: [
          { method: 'Credit Card', count: 856, amount: 65234.56 },
          { method: 'PayPal', count: 234, amount: 15678.90 },
          { method: 'Bank Transfer', count: 89, amount: 6789.12 },
          { method: 'Crypto', count: 45, amount: 1234.56 }
        ]
      };
    }
  });

  // Mutations
  const refundMutation = useMutation({
    mutationFn: async ({ transactionId, amount, reason }: { transactionId: string; amount: number; reason: string }) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      return { transactionId, amount, reason };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      setIsRefundModalVisible(false);
      form.resetFields();
    }
  });

  const exportMutation = useMutation({
    mutationFn: async ({ format, filters }: { format: 'csv' | 'excel' | 'pdf'; filters: any }) => {
      await new Promise(resolve => setTimeout(resolve, 1500));
      // Simulate file download
      const blob = new Blob(['Transaction data...'], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transactions.${format}`;
      a.click();
      URL.revokeObjectURL(url);
      return { success: true };
    }
  });

  const handleViewDetails = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsDetailDrawerVisible(true);
  };

  const handleRefund = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    form.setFieldsValue({
      amount: transaction.amount,
      reason: ''
    });
    setIsRefundModalVisible(true);
  };

  const handleExport = (format: 'csv' | 'excel' | 'pdf') => {
    exportMutation.mutate({ format, filters });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'pending': return 'orange';
      case 'failed': case 'cancelled': return 'red';
      case 'disputed': return 'purple';
      default: return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment': case 'subscription': return 'blue';
      case 'refund': return 'red';
      case 'fee': return 'orange';
      case 'chargeback': return 'purple';
      case 'payout': return 'green';
      default: return 'default';
    }
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'credit_card': return <CreditCardOutlined />;
      case 'paypal': return <PayCircleOutlined />;
      case 'bank_transfer': return <BankOutlined />;
      case 'crypto': return <WalletOutlined />;
      default: return <DollarOutlined />;
    }
  };

  const columns: ColumnsType<Transaction> = [
    {
      title: 'Transaction ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => <Text code>{id}</Text>
    },
    {
      title: 'Customer',
      key: 'customer',
      width: 200,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            src={record.customer.avatar} 
            icon={<UserOutlined />} 
            size="small"
            style={{ marginRight: '8px' }}
          />
          <div>
            <div><Text strong>{record.customer.name}</Text></div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.customer.email}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>
          {type.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Amount',
      key: 'amount',
      width: 120,
      render: (_, record) => (
        <div>
          <Text 
            strong 
            style={{ 
              color: record.amount < 0 ? '#f5222d' : '#52c41a',
              fontSize: '14px'
            }}
          >
            {record.amount < 0 ? '-' : ''}${Math.abs(record.amount).toFixed(2)}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '11px' }}>
            Fee: ${record.fees.total.toFixed(2)}
          </Text>
        </div>
      )
    },
    {
      title: 'Payment Method',
      key: 'paymentMethod',
      width: 150,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {getPaymentMethodIcon(record.paymentMethod.type)}
          <div style={{ marginLeft: '8px' }}>
            <div>{record.paymentMethod.details}</div>
            {record.paymentMethod.last4 && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                •••• {record.paymentMethod.last4}
              </Text>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Badge 
          status={status === 'completed' ? 'success' : status === 'pending' ? 'processing' : 'error'}
          text={status.toUpperCase()}
        />
      )
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => (
        <div>
          <div>{new Date(date).toLocaleDateString()}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {new Date(date).toLocaleTimeString()}
          </Text>
        </div>
      ),
      sorter: true
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          {record.refundable && record.status === 'completed' && (
            <Tooltip title="Process Refund">
              <Button 
                type="text" 
                icon={<UndoOutlined />} 
                size="small"
                onClick={() => handleRefund(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2}>
              <CreditCardOutlined /> Transaction History
            </Title>
            <Paragraph type="secondary">
              View and manage all payment transactions and refunds.
            </Paragraph>
          </div>
          <Space>
            <Button 
              icon={<FileExcelOutlined />}
              onClick={() => handleExport('excel')}
              loading={exportMutation.isPending}
            >
              Export Excel
            </Button>
            <Button 
              icon={<FilePdfOutlined />}
              onClick={() => handleExport('pdf')}
              loading={exportMutation.isPending}
            >
              Export PDF
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Transactions"
              value={transactionStats?.totalTransactions || 0}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Amount"
              value={transactionStats?.totalAmount || 0}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Success Rate"
              value={transactionStats ? 
                ((transactionStats.successfulTransactions / transactionStats.totalTransactions) * 100) : 0}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Refunded Amount"
              value={transactionStats?.refundedAmount || 0}
              prefix={<UndoOutlined />}
              precision={2}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={16}>
          <Col span={6}>
            <Input
              placeholder="Search transactions..."
              prefix={<SearchOutlined />}
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Type"
              style={{ width: '100%' }}
              value={filters.type}
              onChange={(value) => setFilters({ ...filters, type: value })}
              allowClear
            >
              <Option value="payment">Payment</Option>
              <Option value="refund">Refund</Option>
              <Option value="subscription">Subscription</Option>
              <Option value="fee">Fee</Option>
              <Option value="chargeback">Chargeback</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Status"
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              allowClear
            >
              <Option value="completed">Completed</Option>
              <Option value="pending">Pending</Option>
              <Option value="failed">Failed</Option>
              <Option value="cancelled">Cancelled</Option>
              <Option value="disputed">Disputed</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Payment Method"
              style={{ width: '100%' }}
              value={filters.paymentMethod}
              onChange={(value) => setFilters({ ...filters, paymentMethod: value })}
              allowClear
            >
              <Option value="credit_card">Credit Card</Option>
              <Option value="paypal">PayPal</Option>
              <Option value="bank_transfer">Bank Transfer</Option>
              <Option value="crypto">Cryptocurrency</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              style={{ width: '100%' }}
              value={filters.dateRange}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
        </Row>
      </Card>

      {/* Transactions Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={transactions.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: transactions.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} transactions`,
            onChange: (page, pageSize) => {
              setPagination({ ...pagination, current: page, pageSize: pageSize || 20 });
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Transaction Detail Drawer */}
      <Drawer
        title="Transaction Details"
        width={600}
        open={isDetailDrawerVisible}
        onClose={() => setIsDetailDrawerVisible(false)}
      >
        {selectedTransaction && (
          <div>
            <Descriptions title="Transaction Information" bordered column={1}>
              <Descriptions.Item label="Transaction ID">
                <Text code>{selectedTransaction.id}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Type">
                <Tag color={getTypeColor(selectedTransaction.type)}>
                  {selectedTransaction.type.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Amount">
                <Text 
                  strong 
                  style={{ 
                    color: selectedTransaction.amount < 0 ? '#f5222d' : '#52c41a',
                    fontSize: '16px'
                  }}
                >
                  ${Math.abs(selectedTransaction.amount).toFixed(2)} {selectedTransaction.currency}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Badge 
                  status={selectedTransaction.status === 'completed' ? 'success' : 
                          selectedTransaction.status === 'pending' ? 'processing' : 'error'}
                  text={selectedTransaction.status.toUpperCase()}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Description">
                {selectedTransaction.description}
              </Descriptions.Item>
              <Descriptions.Item label="Customer">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar 
                    src={selectedTransaction.customer.avatar} 
                    icon={<UserOutlined />} 
                    size="small"
                    style={{ marginRight: '8px' }}
                  />
                  <div>
                    <div>{selectedTransaction.customer.name}</div>
                    <Text type="secondary">{selectedTransaction.customer.email}</Text>
                  </div>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Payment Method">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {getPaymentMethodIcon(selectedTransaction.paymentMethod.type)}
                  <span style={{ marginLeft: '8px' }}>
                    {selectedTransaction.paymentMethod.details}
                  </span>
                </div>
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Title level={5}>Fee Breakdown</Title>
            <Descriptions bordered column={1} size="small">
              <Descriptions.Item label="Platform Fee">
                ${selectedTransaction.fees.platform.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="Payment Processing Fee">
                ${selectedTransaction.fees.payment.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="Total Fees">
                <Text strong>${selectedTransaction.fees.total.toFixed(2)}</Text>
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Title level={5}>Transaction Timeline</Title>
            <List
              dataSource={selectedTransaction.timeline}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Badge 
                        status={item.status === 'completed' ? 'success' : 
                               item.status === 'pending' ? 'processing' : 'error'}
                      />
                    }
                    title={item.status.toUpperCase()}
                    description={
                      <div>
                        <div>{new Date(item.timestamp).toLocaleString()}</div>
                        {item.note && <Text type="secondary">{item.note}</Text>}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />

            {selectedTransaction.refundable && selectedTransaction.status === 'completed' && (
              <div style={{ marginTop: '24px' }}>
                <Button 
                  type="primary" 
                  danger
                  icon={<UndoOutlined />}
                  onClick={() => handleRefund(selectedTransaction)}
                >
                  Process Refund
                </Button>
              </div>
            )}
          </div>
        )}
      </Drawer>

      {/* Refund Modal */}
      <Modal
        title="Process Refund"
        open={isRefundModalVisible}
        onCancel={() => setIsRefundModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => {
            if (selectedTransaction) {
              refundMutation.mutate({
                transactionId: selectedTransaction.id,
                amount: values.amount,
                reason: values.reason
              });
            }
          }}
        >
          <Alert
            message="Refund Warning"
            description="Processing a refund will return money to the customer and cannot be undone."
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <Form.Item
            name="amount"
            label="Refund Amount"
            rules={[
              { required: true, message: 'Please enter refund amount' },
              { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              prefix="$"
              precision={2}
              min={0.01}
              max={selectedTransaction?.amount || 0}
            />
          </Form.Item>

          <Form.Item
            name="reason"
            label="Refund Reason"
            rules={[{ required: true, message: 'Please provide a reason for the refund' }]}
          >
            <TextArea 
              rows={3}
              placeholder="Enter reason for refund..."
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                danger
                htmlType="submit"
                loading={refundMutation.isPending}
                icon={<UndoOutlined />}
              >
                Process Refund
              </Button>
              <Button onClick={() => setIsRefundModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Transactions; 