/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState } from 'react'
import { ProLayout, ProConfigProvider } from '@ant-design/pro-components'
import { 
  UserOutlined, 
  DashboardOutlined,
  TeamOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  DollarOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons'
import { Avatar, Dropdown, Badge, Button, Space } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'

import { useAuthStore, usePermission } from '@/store/auth'
import { useLogout } from '@/services/auth'
import { Permission } from '@/types/auth'

interface BasicLayoutProps {
  children: React.ReactNode
}

/**
 * 基础布局组件
 * 包含侧边栏、顶部导航、内容区域
 */
const BasicLayout: React.FC<BasicLayoutProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [collapsed, setCollapsed] = useState(false)
  
  const { user } = useAuthStore()
  const { hasPermission } = usePermission()
  const { mutate: logout, isLoading: isLoggingOut } = useLogout()

  // 菜单配置
  const menuData = [
    {
      path: '/dashboard',
      name: '仪表板',
      icon: <DashboardOutlined />,
    },
    {
      path: '/users',
      name: '用户管理',
      icon: <TeamOutlined />,
      hideInMenu: !hasPermission(Permission.USER_VIEW),
      routes: [
        {
          path: '/users',
          name: '用户列表',
        },
      ],
    },
    {
      path: '/services',
      name: '服务管理',
      icon: <ShoppingOutlined />,
      hideInMenu: !hasPermission(Permission.SERVICE_VIEW),
      routes: [
        {
          path: '/services',
          name: '服务列表',
        },
      ],
    },
    {
      path: '/orders',
      name: '订单管理',
      icon: <FileTextOutlined />,
      hideInMenu: !hasPermission(Permission.ORDER_VIEW),
      routes: [
        {
          path: '/orders',
          name: '订单列表',
        },
      ],
    },
    {
      path: '/content',
      name: '内容管理',
      icon: <FileTextOutlined />,
      hideInMenu: !hasPermission(Permission.CONTENT_VIEW),
    },
    {
      path: '/finance',
      name: '财务管理',
      icon: <DollarOutlined />,
      hideInMenu: !hasPermission(Permission.FINANCE_VIEW),
    },
    {
      path: '/analytics',
      name: '数据分析',
      icon: <BarChartOutlined />,
      hideInMenu: !hasPermission(Permission.ANALYTICS_VIEW),
    },
    {
      path: '/system',
      name: '系统配置',
      icon: <SettingOutlined />,
      hideInMenu: !hasPermission(Permission.SYSTEM_CONFIG),
    },
  ]

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人设置',
      onClick: () => navigate('/profile'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => logout(),
    },
  ]

  // 右侧工具栏
  const actionsRender = () => [
    <Badge key="notification" count={5} size="small">
      <Button
        type="text"
        icon={<BellOutlined />}
        onClick={() => navigate('/notifications')}
      />
    </Badge>,
    <Dropdown
      key="user"
      menu={{
        items: userMenuItems,
      }}
      placement="bottomRight"
      trigger={['click']}
    >
      <Space style={{ cursor: 'pointer', padding: '0 8px' }}>
        <Avatar
          size="small"
          src={user?.avatar}
          icon={!user?.avatar && <UserOutlined />}
        />
        <span>{user?.displayName || user?.username}</span>
      </Space>
    </Dropdown>,
  ]

  return (
    <ProConfigProvider hashed={false}>
      <ProLayout
        title="CINA.CLUB"
        logo="https://gw.alipayobjects.com/zos/antfincdn/PmY%24TNNDBI/logo.svg"
        layout="mix"
        splitMenus
        fixedHeader
        fixSiderbar
        collapsed={collapsed}
        onCollapse={setCollapsed}
        location={{
          pathname: location.pathname,
        }}
        route={{
          path: '/',
          routes: menuData,
        }}
        menuItemRender={(item, dom) => (
          <div
            onClick={() => {
              if (item.path) {
                navigate(item.path)
              }
            }}
          >
            {dom}
          </div>
        )}
        breadcrumbRender={(routers = []) => [
          {
            path: '/',
            breadcrumbName: '首页',
          },
          ...routers,
        ]}
        itemRender={(route, params, routes, paths) => {
          const first = routes.indexOf(route) === 0
          return first ? (
            <span onClick={() => navigate('/')}>{route.breadcrumbName}</span>
          ) : (
            <span>{route.breadcrumbName}</span>
          )
        }}
        footerRender={() => (
          <div style={{ textAlign: 'center', color: '#999' }}>
            CINA.CLUB 后台管理系统 ©2025 Created by Cina.Club
          </div>
        )}
        actionsRender={actionsRender}
        avatarProps={{
          src: user?.avatar,
          title: user?.displayName || user?.username,
          size: 'small',
        }}
        token={{
          colorPrimary: '#1890ff',
          sider: {
            colorMenuBackground: '#fff',
            colorMenuItemDivider: '#f0f0f0',
            colorBgMenuItemSelected: '#e6f7ff',
            colorTextMenuSelected: '#1890ff',
          },
        }}
      >
        <div style={{ minHeight: 'calc(100vh - 48px)' }}>
          {children}
        </div>
      </ProLayout>
    </ProConfigProvider>
  )
}

export default BasicLayout 