/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"cina.club/services/ai-assistant-service/internal/application/port"
	"cina.club/services/ai-assistant-service/internal/domain/model"
	"cina.club/services/ai-assistant-service/internal/domain/session"
)

// AIAssistantService AI assistant service
type AIAssistantService struct {
	sessionManager *session.SessionManager
	toolkit        port.Toolkit
	planner        *WorkflowPlanner
}

// NewAIAssistantService creates a new AI assistant service
func NewAIAssistantService(sessionManager *session.SessionManager, toolkit port.Toolkit) *AIAssistantService {
	return &AIAssistantService{
		sessionManager: sessionManager,
		toolkit:        toolkit,
		planner:        NewWorkflowPlanner(toolkit),
	}
}

// ProcessMessage processes user message
func (s *AIAssistantService) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// Get or create session
	dialogState, err := s.sessionManager.GetOrCreate(ctx, req.SessionID, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get or create session: %w", err)
	}

	// Add user message to conversation history
	dialogState.AddTextMessage(model.MessageRoleUser, req.Message)

	// Analyze user intent and generate workflow plan
	plan, err := s.planner.CreatePlan(ctx, req.Message, dialogState)
	if err != nil {
		return nil, fmt.Errorf("failed to create workflow plan: %w", err)
	}

	// Execute workflow plan if exists
	var executionResult *ExecutionResult
	if plan != nil {
		executionResult, err = s.executeWorkflow(ctx, dialogState, plan)
		if err != nil {
			return nil, fmt.Errorf("failed to execute workflow: %w", err)
		}
	}

	// Generate response
	response := s.generateResponse(ctx, dialogState, executionResult)

	// Add assistant response to conversation history
	dialogState.AddTextMessage(model.MessageRoleAssistant, response.Content)

	// Save session state
	err = s.sessionManager.Save(ctx, dialogState)
	if err != nil {
		return nil, fmt.Errorf("failed to save session: %w", err)
	}

	return &ProcessMessageResponse{
		SessionID:   dialogState.SessionID,
		Content:     response.Content,
		MessageType: response.MessageType,
		Metadata:    response.Metadata,
		Suggestions: response.Suggestions,
	}, nil
}

// executeWorkflow executes workflow
func (s *AIAssistantService) executeWorkflow(ctx context.Context, dialogState *model.DialogState, plan *model.WorkflowPlan) (*ExecutionResult, error) {
	// Create execution state
	executionState := model.NewExecutionState(plan)
	dialogState.SetExecutionState(executionState)

	// Mark as running
	executionState.MarkAsRunning()

	result := &ExecutionResult{
		PlanID:    plan.ID,
		Success:   true,
		Results:   make(map[string]interface{}),
		Errors:    make([]string, 0),
		StartTime: time.Now(),
	}

	// Execute each step
	for i, step := range plan.Steps {
		// Check dependencies
		if !s.checkDependencies(step, result.Results) {
			err := fmt.Sprintf("step %s dependencies not satisfied", step.ID)
			result.Errors = append(result.Errors, err)
			result.Success = false
			executionState.MarkAsFailed(fmt.Errorf(err))
			break
		}

		// Execute step
		stepResult, err := s.executeStep(ctx, step, result.Results)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("step %s failed: %v", step.ID, err))
			result.Success = false
			executionState.MarkAsFailed(err)
			break
		}

		// Save step result
		result.Results[step.ID] = stepResult
		dialogState.UpdateExecutionStep(i, stepResult)
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	if result.Success {
		executionState.MarkAsCompleted()
	}

	return result, nil
}

// executeStep executes a single step
func (s *AIAssistantService) executeStep(ctx context.Context, step model.WorkflowStep, previousResults map[string]interface{}) (*port.ToolResult, error) {
	// Prepare input parameters
	inputs := make(map[string]interface{})
	for key, value := range step.Inputs {
		// Handle variable substitution
		if strValue, ok := value.(string); ok && strings.HasPrefix(strValue, "${") && strings.HasSuffix(strValue, "}") {
			varName := strings.TrimSuffix(strings.TrimPrefix(strValue, "${"), "}")
			if varValue, exists := previousResults[varName]; exists {
				inputs[key] = varValue
			}
		} else {
			inputs[key] = value
		}
	}

	// Execute tool
	toolResult, err := s.toolkit.ExecuteTool(ctx, step.ToolID, inputs)
	if err != nil {
		return nil, err
	}

	return toolResult, nil
}

// checkDependencies checks step dependencies
func (s *AIAssistantService) checkDependencies(step model.WorkflowStep, results map[string]interface{}) bool {
	for _, dep := range step.Dependencies {
		if _, exists := results[dep]; !exists {
			return false
		}
	}
	return true
}

// generateResponse generates response
func (s *AIAssistantService) generateResponse(_ context.Context, _ *model.DialogState, executionResult *ExecutionResult) *ResponseContent {
	response := &ResponseContent{
		MessageType: "text",
		Metadata:    make(map[string]interface{}),
		Suggestions: make([]string, 0),
	}

	if executionResult == nil {
		// No workflow executed, generate simple response
		response.Content = "I understand your question, but I haven't found a suitable solution yet. Please provide more detailed information."
		response.Suggestions = []string{
			"You can try to describe your needs more specifically",
			"Or tell me what kind of help you want",
		}
		return response
	}

	if !executionResult.Success {
		// Execution failed
		response.Content = "Sorry, I encountered some issues while processing your request."
		if len(executionResult.Errors) > 0 {
			response.Content += fmt.Sprintf(" Error information: %s", strings.Join(executionResult.Errors, "; "))
		}
		response.Suggestions = []string{
			"Please try to re-describe your needs",
			"Or contact customer service for help",
		}
		return response
	}

	// Execution successful, generate response based on results
	response.Content = s.formatSuccessResponse(executionResult)
	response.Metadata["execution_time"] = executionResult.Duration.String()
	response.Metadata["steps_executed"] = len(executionResult.Results)

	// Generate suggestions
	response.Suggestions = s.generateSuggestions(executionResult)

	return response
}

// formatSuccessResponse formats successful response
func (s *AIAssistantService) formatSuccessResponse(result *ExecutionResult) string {
	if len(result.Results) == 0 {
		return "Your request has been processed successfully."
	}

	var parts []string
	for _, stepResult := range result.Results {
		if toolResult, ok := stepResult.(*port.ToolResult); ok && toolResult.Success {
			if content, exists := toolResult.Data["content"]; exists {
				parts = append(parts, fmt.Sprintf("%v", content))
			}
		}
	}

	if len(parts) > 0 {
		return strings.Join(parts, "\n\n")
	}

	return "Your request has been processed successfully."
}

// generateSuggestions generates suggestions
func (s *AIAssistantService) generateSuggestions(result *ExecutionResult) []string {
	suggestions := make([]string, 0)

	// Generate related suggestions based on execution results
	for _, stepResult := range result.Results {
		if toolResult, ok := stepResult.(*port.ToolResult); ok && toolResult.Success {
			suggestions = append(suggestions, toolResult.NextSteps...)
		}
	}

	if len(suggestions) == 0 {
		suggestions = []string{
			"Is there anything else you need help with?",
			"You can continue to ask questions",
		}
	}

	return suggestions
}

// GetSessionHistory gets session history
func (s *AIAssistantService) GetSessionHistory(ctx context.Context, sessionID string, limit int) (*SessionHistoryResponse, error) {
	messages, err := s.sessionManager.GetMessageHistory(ctx, sessionID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get session history: %w", err)
	}

	return &SessionHistoryResponse{
		SessionID: sessionID,
		Messages:  messages,
		Count:     len(messages),
	}, nil
}

// ClearSession clears session
func (s *AIAssistantService) ClearSession(ctx context.Context, sessionID string) error {
	return s.sessionManager.Delete(ctx, sessionID)
}

// GetAvailableTools gets available tools list
func (s *AIAssistantService) GetAvailableTools(ctx context.Context) ([]*port.ToolSchema, error) {
	return s.toolkit.GetToolSchemas(), nil
}

// Request and response structures

// ProcessMessageRequest message processing request
type ProcessMessageRequest struct {
	SessionID string `json:"session_id,omitempty"`
	UserID    string `json:"user_id"`
	Message   string `json:"message"`
}

// ProcessMessageResponse message processing response
type ProcessMessageResponse struct {
	SessionID   string                 `json:"session_id"`
	Content     string                 `json:"content"`
	MessageType string                 `json:"message_type"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Suggestions []string               `json:"suggestions,omitempty"`
}

// ResponseContent response content
type ResponseContent struct {
	Content     string                 `json:"content"`
	MessageType string                 `json:"message_type"`
	Metadata    map[string]interface{} `json:"metadata"`
	Suggestions []string               `json:"suggestions"`
}

// ExecutionResult execution result
type ExecutionResult struct {
	PlanID    string                 `json:"plan_id"`
	Success   bool                   `json:"success"`
	Results   map[string]interface{} `json:"results"`
	Errors    []string               `json:"errors"`
	StartTime time.Time              `json:"start_time"`
	EndTime   time.Time              `json:"end_time"`
	Duration  time.Duration          `json:"duration"`
}

// SessionHistoryResponse session history response
type SessionHistoryResponse struct {
	SessionID string          `json:"session_id"`
	Messages  []model.Message `json:"messages"`
	Count     int             `json:"count"`
}
