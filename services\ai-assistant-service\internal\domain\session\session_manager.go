/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package session

import (
	"context"
	"time"

	"cina.club/services/ai-assistant-service/internal/domain/model"
)

// SessionStore defines the interface for session storage
type SessionStore interface {
	// Get 鑾峰彇浼氳瘽鐘舵€?
	Get(ctx context.Context, sessionID string) (*model.DialogState, error)

	// Set 淇濆瓨浼氳瘽鐘舵€?
	Set(ctx context.Context, sessionID string, state *model.DialogState, ttl time.Duration) error

	// Delete 鍒犻櫎浼氳瘽鐘舵€?
	Delete(ctx context.Context, sessionID string) error

	// Exists 妫€鏌ヤ細璇濇槸鍚﹀瓨鍦?
	Exists(ctx context.Context, sessionID string) (bool, error)

	// ExtendTTL 寤堕暱浼氳瘽TTL
	ExtendTTL(ctx context.Context, sessionID string, ttl time.Duration) error
}

// SessionManager manages dialog sessions
type SessionManager struct {
	store SessionStore
}

// NewSessionManager creates a new session manager
func NewSessionManager(store SessionStore) *SessionManager {
	return &SessionManager{
		store: store,
	}
}

// GetOrCreate gets an existing session or creates a new one
func (sm *SessionManager) GetOrCreate(ctx context.Context, sessionID, userID string) (*model.DialogState, error) {
	if sessionID != "" {
		// Try to get existing session
		if state, err := sm.store.Get(ctx, sessionID); err == nil {
			return state, nil
		}
	}

	// Create new session
	state := model.NewDialogState(userID)
	if sessionID != "" {
		state.SessionID = sessionID
	}

	// Save the new session
	err := sm.store.Set(ctx, state.SessionID, state, 24*time.Hour)
	if err != nil {
		return nil, err
	}

	return state, nil
}

// Save saves a session state
func (sm *SessionManager) Save(ctx context.Context, state *model.DialogState) error {
	return sm.store.Set(ctx, state.SessionID, state, 24*time.Hour)
}

// Delete deletes a session
func (sm *SessionManager) Delete(ctx context.Context, sessionID string) error {
	return sm.store.Delete(ctx, sessionID)
}

// GetMessageHistory gets message history for a session
func (sm *SessionManager) GetMessageHistory(ctx context.Context, sessionID string, limit int) ([]model.Message, error) {
	state, err := sm.store.Get(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	return state.GetLastMessages(limit), nil
}

// Get 鑾峰彇浼氳瘽
func (sm *SessionManager) Get(ctx context.Context, sessionID string) (*model.DialogState, error) {
	return sm.store.Get(ctx, sessionID)
}

// Exists 妫€鏌ヤ細璇濇槸鍚﹀瓨鍦?
func (sm *SessionManager) Exists(ctx context.Context, sessionID string) (bool, error) {
	return sm.store.Exists(ctx, sessionID)
}

// UpdateExecutionState 鏇存柊鎵ц鐘舵€?
func (sm *SessionManager) UpdateExecutionState(ctx context.Context, sessionID string, state *model.ExecutionState) error {
	dialogState, err := sm.store.Get(ctx, sessionID)
	if err != nil {
		return err
	}

	dialogState.SetExecutionState(state)
	return sm.Save(ctx, dialogState)
}

// AddMessage 娣诲姞娑堟伅鍒颁細璇?
func (sm *SessionManager) AddMessage(ctx context.Context, sessionID string, role model.MessageRole, content []model.ContentItem) error {
	state, err := sm.store.Get(ctx, sessionID)
	if err != nil {
		return err
	}

	state.AddMessage(role, content)
	return sm.Save(ctx, state)
}

// AddTextMessage 娣诲姞鏂囨湰娑堟伅鍒颁細璇?
func (sm *SessionManager) AddTextMessage(ctx context.Context, sessionID, text string, role model.MessageRole) error {
	state, err := sm.store.Get(ctx, sessionID)
	if err != nil {
		return err
	}

	state.AddTextMessage(role, text)
	return sm.Save(ctx, state)
}

// CleanupExpiredSessions 娓呯悊杩囨湡浼氳瘽锛堝彲浠ラ€氳繃瀹氭椂浠诲姟璋冪敤锛?
func (sm *SessionManager) CleanupExpiredSessions(ctx context.Context) error {
	// 杩欎釜鏂规硶鐨勫叿浣撳疄鐜板彇鍐充簬瀛樺偍鍚庣
	// 瀵逛簬Redis锛屽彲浠ヤ緷璧朤TL鑷姩杩囨湡
	// 瀵逛簬鍏朵粬瀛樺偍锛屽彲鑳介渶瑕佹壂鎻忓苟鍒犻櫎杩囨湡浼氳瘽
	return nil
}

// GetActiveSessionCount 鑾峰彇娲昏穬浼氳瘽鏁伴噺锛堢敤浜庣洃鎺э級
func (sm *SessionManager) GetActiveSessionCount(ctx context.Context) (int64, error) {
	// 杩欎釜鏂规硶鐨勫叿浣撳疄鐜板彇鍐充簬瀛樺偍鍚庣
	// 闇€瑕佸瓨鍌ㄥ疄鐜版彁渚涚粺璁″姛鑳?
	return 0, nil
}
