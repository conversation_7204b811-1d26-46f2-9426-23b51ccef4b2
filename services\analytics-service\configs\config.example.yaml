# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# Analytics Service Configuration Example
# Copy this file to config.yaml and modify according to your environment

# HTTP Server Configuration
http:
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# Data Warehouse Configuration (PostgreSQL)
datawarehouse:
  host: localhost
  port: 5432
  database: analytics_dw
  username: analytics_user
  password: your_dw_password
  sslmode: require
  timezone: UTC
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 60 # minutes

# ClickHouse Configuration (Real-time Analytics)
clickhouse:
  addr: localhost:9000
  database: analytics_events
  username: clickhouse_user
  password: your_clickhouse_password
  dial_timeout: 10s
  read_timeout: 30s
  write_timeout: 30s

# Redis Cache Configuration
redis:
  addr: localhost:6379
  password: ""
  db: 0
  pool_size: 100
  min_idle_conns: 10
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s
  pool_timeout: 4s

# Logging Configuration
logging:
  level: info
  format: json
  output: stdout

# Monitoring Configuration
monitoring:
  metrics_path: /metrics
  health_path: /health
  prometheus:
    enabled: true
    namespace: analytics_service

# Cache Configuration
cache:
  default_ttl: 1h
  max_size: 1000000 # 1MB
  cleanup_interval: 10m

# Rate Limiting (optional)
rate_limit:
  enabled: true
  requests_per_second: 100
  burst: 200

# Security Configuration
security:
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]
  
# Environment
environment: development 