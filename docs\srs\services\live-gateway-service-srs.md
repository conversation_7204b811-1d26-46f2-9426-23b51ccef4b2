好的，遵照您的指示。我将为您生成一份专门针对 **`live-gateway-service` (媒体网关服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义这个新增的、作为直播功能“物理层”网关的服务的功能、接口、数据模型、性能和可靠性需求，作为直播系统开发的权威依据。

---
### CINA.CLUB - `live-gateway-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [多媒体/实时通信团队负责人]  
**审批人:** [CTO/架构师]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的直播系统中，需要一个专门的服务来处理与底层**实时流媒体服务器集群**的直接交互，并负责媒体流的**接入安全**和**分发策略**。`live-gateway-service` 的目的就是构建这样一个**媒体网关**。它将底层复杂的流媒体服务器（如SRS, LiveKit）的API和回调机制进行封装和抽象，为上层业务服务（`live-api-service`）提供简单、统一的接口，并为客户端生成安全、可用的推拉流地址。

#### 1.2. 服务范围
本服务 **负责**:
*   **推流鉴权**:
    *   提供一个**HTTP回调接口**，供媒体服务器在接收到推流请求时进行**实时鉴权**。
    *   验证推流码(`stream_key`)和认证令牌的有效性。
*   **推拉流地址生成**:
    *   根据`live-api-service`的请求，生成包含认证信息的、安全的**推流URL**。
    *   根据直播间信息和协议请求，生成CDN加速的、可能带**防盗链签名**的**拉流URL**。
*   **媒体服务器适配与交互**:
    *   封装对底层媒体服务器集群（如SRS）的管理API的调用，用于查询流状态、踢出违规推流等。
    *   作为**适配器(Adapter)**，屏蔽不同媒体服务器的实现差异。
*   **状态回调处理**:
    *   提供HTTP Webhook端点，接收来自媒体服务器的**各种实时事件回调**（如`on_publish`, `on_unpublish`, `on_dvr`等）。
    *   将这些“物理层”的事件，转换为平台内部的、有业务含义的指令或事件，通知`live-api-service`。
*   **负载均衡与调度 (可选)**: (高级) 在有多组媒体服务器集群时，负责将新的推流请求，调度到负载最低的集群上。

本服务 **不负责**:
*   **任何媒体流数据的处理、转发或转码**: 这完全是底层媒体服务器的职责。
*   **直播间的业务逻辑和状态管理**: 如“直播间是否付费”、“谁有权开播”等，这是`live-api-service`的职责。本服务只负责执行鉴权。
*   **直播间的IM互动消息**: 由`live-im-service`负责。
*   **面向最终用户的API**。

#### 1.3. 目标用户/调用方
*   **`live-api-service` (主要)**: 调用本服务获取推拉流地址。
*   **实时流媒体服务器 (SRS/LiveKit等)**: (主要) 通过HTTP回调调用本服务的鉴权和事件上报接口。
*   **CINA.CLUB平台管理员**: (通过后台) 查看媒体服务器状态，手动管理直播流。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`live-gateway-service` 是CINA.CLUB直播生态的“**物理层网关**”和“**媒体服务器大管家**”。它位于业务逻辑（`live-api-service`）和媒体基础设施（SRS集群, CDN）之间，扮演着**安全门卫、地址翻译官和状态通讯员**的角色。它将直播的业务层与技术实现层彻底解耦，使得平台可以在不影响业务逻辑的情况下，更换或升级底层的流媒体技术。

#### 2.2. 主要功能概述
*   基于回调的、实时的推流安全鉴权。
*   动态生成带认证和防盗链的推拉流地址。
*   作为适配器，封装对底层媒体服务器的API调用。
*   可靠地处理媒体服务器的事件回调，并通知上游业务。

---

### 3. 核心流程图

#### 3.1. 推流鉴权回调流程

```mermaid
sequenceDiagram
    participant Pusher as "主播推流客户端"
    participant MediaServer as "SRS/LiveKit"
    participant LiveGatewayService as LGS
    participant LiveAPIService as LAS
    
    Pusher->>MediaServer: 1. 推流到 rtmp://.../live/STREAM_KEY?token=...
    MediaServer->>LGS: 2. [HTTP POST] /on_publish (携带stream_key, token等参数)
    
    LGS->>LAS: 3. [gRPC] CheckPushAuthentication(stream_key, token)
    LAS->>DB: 4. 查询直播间状态和推流码是否匹配
    LAS-->>LGS: 5. (鉴权结果: { "allowed": true, "user_id": "..." })
    
    alt 鉴权通过
        LGS-->>MediaServer: 6a. **HTTP 200 OK** (返回0)
        MediaServer->>Pusher: 7a. 接受推流
        LGS->>LAS: 8a. [gRPC/Event] NotifyStreamPublished(room_id)
    else 鉴权失败
        LGS-->>MediaServer: 6b. **HTTP 403 Forbidden** (返回非0)
        MediaServer->>Pusher: 7b. 断开推流连接
    end
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 推流生命周期管理
*   **FR4.1.1 (推流鉴权回调)**:
    *   **必须**提供一个`POST /on_publish`的HTTP端点，供媒体服务器调用。
    *   在此接口中，**必须**同步调用`live-api-service`的`CheckPushAuthentication`接口进行业务层面的鉴权。
    *   响应**必须**遵循媒体服务器要求的格式（如SRS要求返回HTTP状态码200和body为`0`表示成功）。
*   **FR4.1.2 (推流成功通知)**: 鉴权成功后，**必须**异步调用`live-api-service`的`NotifyStreamPublished`接口，通知业务层推流已正式开始。
*   **FR4.1.3 (推流中断回调)**:
    *   **必须**提供一个`POST /on_unpublish`的HTTP端点。
    *   在此接口中，**必须**异步调用`live-api-service`的`NotifyStreamInterrupted`接口。

#### 4.2. 地址生成与分发
*   **FR4.2.1 (推流地址生成)**:
    *   必须提供一个内部gRPC接口`RequestPushURL(roomID, userID)`。
    *   生成的推流地址必须包含一个与`roomID`绑定的、唯一的`stream_key`，以及一个有时效性的`auth_token`。
*   **FR4.2.2 (拉流地址生成)**:
    *   必须提供一个内部gRPC接口`RequestPlayURL(roomID, protocol)`。
    *   根据`protocol`（`HLS`, `HTTP-FLV`, `WebRTC`）和直播间的`access_control`策略，生成最终的播放URL。
    *   **防盗链**: 对于需要保护的直播，返回的CDN URL**必须**是经过签名的（使用`domain/service/cdn_url_signer`）。

#### 4.3. 媒体服务器适配器
*   **FR4.3.1 (适配器接口)**: 必须定义一个`MediaServerProvider`接口，包含`KickStream(streamKey)`, `GetStreamStats(streamKey)`等方法。
*   **FR4.3.2 (具体实现)**: 为每种使用的媒体服务器（如SRS）实现该接口，封装对其管理API的调用。

#### 4.4. 其他事件回调处理
*   **FR4.4.1 (录制完成回调)**: 必须提供`POST /on_dvr`端点，在媒体服务器完成一个`.mp4`文件录制时被调用。处理器会将`file_path`等信息，通过`live-api-service`与对应的直播场次关联起来。
*   **FR4.4.2 (截图完成回调)**: 必须提供`POST /on_snapshot`端点，用于接收媒体服务器的定时截图，并将其提交给`content-moderation-service`。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC API接口 (供`live-api-service`调用)
*   **Package**: `hina.v1.live_gateway`
*   **核心RPC**:
    *   `rpc RequestPushURL(RequestPushURLRequest) returns (PushURLResponse)`
    *   `rpc RequestPlayURL(RequestPlayURLRequest) returns (PlayURLResponse)`
    *   `rpc KickStream(KickStreamRequest) returns (google.protobuf.Empty)` (Admin Only)

#### 5.2. 外部HTTP回调接口 (供媒体服务器调用)
*   **路径**: `/hooks/srs` (以SRS为例)
*   **核心端点**:
    *   `POST /on_publish`
    *   `POST /on_unpublish`
    *   `POST /on_dvr`
    *   `POST /on_snapshot`
*   **安全**: 这些端点**必须**通过IP白名单或共享密钥等方式进行保护，确保只有合法的媒体服务器才能调用。

---

### 6. 数据需求 (Data Requirements)

*   **无核心持久化数据库**: 本服务设计为**无状态**或**软状态**。
*   **状态存储 (Redis)**:
    *   `stream_key_to_room_id:{streamKey}` -> `roomId`。用于在回调时快速查找房间ID。
    *   `active_streams_on_node:{nodeId}`: (可选) 用于简单的负载均衡，记录每个媒体服务器节点上的活跃流数量。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与延迟)**:
    *   **回调接口**: `on_publish`等鉴权回调接口的P99延迟**必须 `< 50ms**。这是决定主播推流成功速度的关键。
    *   **gRPC接口**: 地址生成接口P99延迟 < 100ms。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。本服务的故障将导致所有新直播无法开始，和所有状态变更无法被感知。
    *   **幂等性**: 所有Webhook回调的处理都必须是幂等的。
*   **NFR7.3 (可扩展性)**: 服务应为无状态，易于水平扩展。

---

### 8. 技术约束与选型建议
*   **语言**: Go。其高并发、低延迟的特性非常适合构建网关类服务。
*   **HTTP框架**: `chi`或`gin`，用于实现高性能的Webhook端点。
*   **适配器模式**: 这是本服务的核心设计模式，用于解耦与具体媒体服务器的集成。
*   **安全性**: Webhook接口的访问控制是核心安全点，必须严格配置。

---
这份SRS为`live-gateway-service`的设计和实现提供了坚实、全面的指导。通过将直播的业务逻辑层与媒体技术实现层彻底分离，它极大地增强了整个直播系统的**灵活性、可维护性和安全性**，是构建一个专业级直播平台的关键一环。