// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 16:30:00
// Modified: 2025-01-23 16:30:00

package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"
)

// APIVersion represents an API version
type APIVersion string

const (
	V1 APIVersion = "v1"
	V2 APIVersion = "v2"
)

// StandardResponse provides a consistent response structure
type StandardResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	Metadata  *Metadata   `json:"metadata,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
	Version   APIVersion  `json:"version"`
	RequestID string      `json:"request_id"`
}

// APIError provides standardized error information
type APIError struct {
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details,omitempty"`
	Field      string                 `json:"field,omitempty"`
	Context    map[string]interface{} `json:"context,omitempty"`
	Timestamp  time.Time              `json:"timestamp"`
	TraceID    string                 `json:"trace_id,omitempty"`
	RetryAfter *int                   `json:"retry_after,omitempty"` // seconds
}

// Metadata provides additional response metadata
type Metadata struct {
	Pagination *PaginationInfo        `json:"pagination,omitempty"`
	Sorting    *SortingInfo           `json:"sorting,omitempty"`
	Filtering  *FilteringInfo         `json:"filtering,omitempty"`
	Cache      *CacheInfo             `json:"cache,omitempty"`
	Extra      map[string]interface{} `json:"extra,omitempty"`
}

// PaginationInfo provides pagination metadata
type PaginationInfo struct {
	Page       int  `json:"page"`
	PageSize   int  `json:"page_size"`
	TotalCount int  `json:"total_count"`
	TotalPages int  `json:"total_pages"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// SortingInfo provides sorting metadata
type SortingInfo struct {
	Field     string `json:"field"`
	Direction string `json:"direction"` // "asc" or "desc"
	Secondary string `json:"secondary,omitempty"`
}

// FilteringInfo provides filtering metadata
type FilteringInfo struct {
	Applied []FilterCriteria       `json:"applied"`
	Total   int                    `json:"total"`
	Context map[string]interface{} `json:"context,omitempty"`
}

// FilterCriteria represents a single filter criterion
type FilterCriteria struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // "eq", "ne", "gt", "lt", "gte", "lte", "in", "like"
	Value    interface{} `json:"value"`
}

// CacheInfo provides cache metadata
type CacheInfo struct {
	Hit         bool      `json:"hit"`
	TTL         int       `json:"ttl"` // seconds
	GeneratedAt time.Time `json:"generated_at"`
}

// StandardRequest provides a consistent request structure
type StandardRequest struct {
	Pagination *PaginationRequest `json:"pagination,omitempty"`
	Sorting    *SortingRequest    `json:"sorting,omitempty"`
	Filtering  *FilteringRequest  `json:"filtering,omitempty"`
	Fields     []string           `json:"fields,omitempty"`
	Include    []string           `json:"include,omitempty"`
	Context    RequestContext     `json:"context"`
}

// PaginationRequest provides pagination parameters
type PaginationRequest struct {
	Page     int `json:"page" validate:"min=1"`
	PageSize int `json:"page_size" validate:"min=1,max=100"`
}

// SortingRequest provides sorting parameters
type SortingRequest struct {
	Field     string `json:"field" validate:"required"`
	Direction string `json:"direction" validate:"oneof=asc desc"`
	Secondary string `json:"secondary,omitempty"`
}

// FilteringRequest provides filtering parameters
type FilteringRequest struct {
	Criteria []FilterCriteria       `json:"criteria"`
	Logic    string                 `json:"logic,omitempty"` // "and" or "or"
	Context  map[string]interface{} `json:"context,omitempty"`
}

// RequestContext provides request context information
type RequestContext struct {
	UserID      string            `json:"user_id,omitempty"`
	TenantID    string            `json:"tenant_id,omitempty"`
	RequestID   string            `json:"request_id"`
	TraceID     string            `json:"trace_id,omitempty"`
	SessionID   string            `json:"session_id,omitempty"`
	ClientInfo  *ClientInfo       `json:"client_info,omitempty"`
	Locale      string            `json:"locale,omitempty"`
	Timezone    string            `json:"timezone,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
	QueryParams map[string]string `json:"query_params,omitempty"`
}

// ClientInfo provides client information
type ClientInfo struct {
	UserAgent  string `json:"user_agent"`
	IPAddress  string `json:"ip_address"`
	Platform   string `json:"platform"`
	Version    string `json:"version"`
	DeviceType string `json:"device_type"`
}

// ResponseBuilder helps build standardized responses
type ResponseBuilder struct {
	version   APIVersion
	requestID string
	metadata  *Metadata
}

// NewResponseBuilder creates a new response builder
func NewResponseBuilder(version APIVersion, requestID string) *ResponseBuilder {
	return &ResponseBuilder{
		version:   version,
		requestID: requestID,
		metadata:  &Metadata{},
	}
}

// WithPagination adds pagination metadata
func (rb *ResponseBuilder) WithPagination(page, pageSize, totalCount int) *ResponseBuilder {
	totalPages := (totalCount + pageSize - 1) / pageSize
	rb.metadata.Pagination = &PaginationInfo{
		Page:       page,
		PageSize:   pageSize,
		TotalCount: totalCount,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
	return rb
}

// WithSorting adds sorting metadata
func (rb *ResponseBuilder) WithSorting(field, direction, secondary string) *ResponseBuilder {
	rb.metadata.Sorting = &SortingInfo{
		Field:     field,
		Direction: direction,
		Secondary: secondary,
	}
	return rb
}

// WithFiltering adds filtering metadata
func (rb *ResponseBuilder) WithFiltering(criteria []FilterCriteria, total int) *ResponseBuilder {
	rb.metadata.Filtering = &FilteringInfo{
		Applied: criteria,
		Total:   total,
	}
	return rb
}

// WithCache adds cache metadata
func (rb *ResponseBuilder) WithCache(hit bool, ttl int, generatedAt time.Time) *ResponseBuilder {
	rb.metadata.Cache = &CacheInfo{
		Hit:         hit,
		TTL:         ttl,
		GeneratedAt: generatedAt,
	}
	return rb
}

// WithExtra adds extra metadata
func (rb *ResponseBuilder) WithExtra(key string, value interface{}) *ResponseBuilder {
	if rb.metadata.Extra == nil {
		rb.metadata.Extra = make(map[string]interface{})
	}
	rb.metadata.Extra[key] = value
	return rb
}

// Success builds a success response
func (rb *ResponseBuilder) Success(data interface{}) *StandardResponse {
	return &StandardResponse{
		Success:   true,
		Data:      data,
		Metadata:  rb.metadata,
		Timestamp: time.Now().UTC(),
		Version:   rb.version,
		RequestID: rb.requestID,
	}
}

// Error builds an error response
func (rb *ResponseBuilder) Error(code, message, details string) *StandardResponse {
	return &StandardResponse{
		Success: false,
		Error: &APIError{
			Code:      code,
			Message:   message,
			Details:   details,
			Timestamp: time.Now().UTC(),
		},
		Metadata:  rb.metadata,
		Timestamp: time.Now().UTC(),
		Version:   rb.version,
		RequestID: rb.requestID,
	}
}

// ValidationError builds a validation error response
func (rb *ResponseBuilder) ValidationError(field, message string) *StandardResponse {
	return &StandardResponse{
		Success: false,
		Error: &APIError{
			Code:      "VALIDATION_ERROR",
			Message:   message,
			Field:     field,
			Timestamp: time.Now().UTC(),
		},
		Metadata:  rb.metadata,
		Timestamp: time.Now().UTC(),
		Version:   rb.version,
		RequestID: rb.requestID,
	}
}

// APIValidator provides request validation functionality
type APIValidator struct {
	rules map[string]ValidationRule
}

// ValidationRule defines a validation rule
type ValidationRule struct {
	Field     string
	Required  bool
	Type      string
	MinLength *int
	MaxLength *int
	MinValue  *float64
	MaxValue  *float64
	Pattern   string
	Enum      []string
	Custom    func(interface{}) error
}

// NewAPIValidator creates a new API validator
func NewAPIValidator() *APIValidator {
	return &APIValidator{
		rules: make(map[string]ValidationRule),
	}
}

// AddRule adds a validation rule
func (v *APIValidator) AddRule(rule ValidationRule) {
	v.rules[rule.Field] = rule
}

// Validate validates a request against defined rules
func (v *APIValidator) Validate(request interface{}) []ValidationError {
	var errors []ValidationError

	value := reflect.ValueOf(request)
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	for fieldName, rule := range v.rules {
		field := value.FieldByName(fieldName)
		if !field.IsValid() {
			continue
		}

		if err := v.validateField(field, rule); err != nil {
			errors = append(errors, ValidationError{
				Field:   fieldName,
				Message: err.Error(),
			})
		}
	}

	return errors
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (v *APIValidator) validateField(field reflect.Value, rule ValidationRule) error {
	// Required validation
	if rule.Required && field.IsZero() {
		return fmt.Errorf("field is required")
	}

	if field.IsZero() {
		return nil // Skip other validations for zero values
	}

	// Type validation
	switch rule.Type {
	case "string":
		if field.Kind() != reflect.String {
			return fmt.Errorf("field must be a string")
		}
		str := field.String()

		if rule.MinLength != nil && len(str) < *rule.MinLength {
			return fmt.Errorf("field must be at least %d characters", *rule.MinLength)
		}

		if rule.MaxLength != nil && len(str) > *rule.MaxLength {
			return fmt.Errorf("field must not exceed %d characters", *rule.MaxLength)
		}

		if rule.Enum != nil {
			valid := false
			for _, enum := range rule.Enum {
				if str == enum {
					valid = true
					break
				}
			}
			if !valid {
				return fmt.Errorf("field must be one of: %s", strings.Join(rule.Enum, ", "))
			}
		}

	case "number":
		var num float64
		switch field.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			num = float64(field.Int())
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			num = float64(field.Uint())
		case reflect.Float32, reflect.Float64:
			num = field.Float()
		default:
			return fmt.Errorf("field must be a number")
		}

		if rule.MinValue != nil && num < *rule.MinValue {
			return fmt.Errorf("field must be at least %f", *rule.MinValue)
		}

		if rule.MaxValue != nil && num > *rule.MaxValue {
			return fmt.Errorf("field must not exceed %f", *rule.MaxValue)
		}
	}

	// Custom validation
	if rule.Custom != nil {
		return rule.Custom(field.Interface())
	}

	return nil
}

// RequestParser provides request parsing utilities
type RequestParser struct {
	maxPageSize int
}

// NewRequestParser creates a new request parser
func NewRequestParser(maxPageSize int) *RequestParser {
	return &RequestParser{
		maxPageSize: maxPageSize,
	}
}

// ParsePagination parses pagination parameters from HTTP request
func (p *RequestParser) ParsePagination(r *http.Request) (*PaginationRequest, error) {
	pageStr := r.URL.Query().Get("page")
	pageSizeStr := r.URL.Query().Get("page_size")

	page := 1
	pageSize := 20 // default

	if pageStr != "" {
		var err error
		page, err = strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			return nil, fmt.Errorf("invalid page parameter")
		}
	}

	if pageSizeStr != "" {
		var err error
		pageSize, err = strconv.Atoi(pageSizeStr)
		if err != nil || pageSize < 1 {
			return nil, fmt.Errorf("invalid page_size parameter")
		}
	}

	if pageSize > p.maxPageSize {
		pageSize = p.maxPageSize
	}

	return &PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// ParseSorting parses sorting parameters from HTTP request
func (p *RequestParser) ParseSorting(r *http.Request) (*SortingRequest, error) {
	sortBy := r.URL.Query().Get("sort_by")
	sortOrder := r.URL.Query().Get("sort_order")

	if sortBy == "" {
		return nil, nil
	}

	if sortOrder == "" {
		sortOrder = "asc"
	}

	if sortOrder != "asc" && sortOrder != "desc" {
		return nil, fmt.Errorf("invalid sort_order, must be 'asc' or 'desc'")
	}

	return &SortingRequest{
		Field:     sortBy,
		Direction: sortOrder,
	}, nil
}

// ParseFiltering parses filtering parameters from HTTP request
func (p *RequestParser) ParseFiltering(r *http.Request) (*FilteringRequest, error) {
	filterStr := r.URL.Query().Get("filter")
	if filterStr == "" {
		return nil, nil
	}

	var criteria []FilterCriteria
	if err := json.Unmarshal([]byte(filterStr), &criteria); err != nil {
		return nil, fmt.Errorf("invalid filter format")
	}

	return &FilteringRequest{
		Criteria: criteria,
		Logic:    "and", // default
	}, nil
}

// ParseRequestContext parses request context from HTTP request
func (p *RequestParser) ParseRequestContext(r *http.Request) RequestContext {
	return RequestContext{
		RequestID: r.Header.Get("X-Request-ID"),
		TraceID:   r.Header.Get("X-Trace-ID"),
		SessionID: r.Header.Get("X-Session-ID"),
		Locale:    r.Header.Get("Accept-Language"),
		ClientInfo: &ClientInfo{
			UserAgent:  r.Header.Get("User-Agent"),
			IPAddress:  r.RemoteAddr,
			Platform:   r.Header.Get("X-Platform"),
			Version:    r.Header.Get("X-Client-Version"),
			DeviceType: r.Header.Get("X-Device-Type"),
		},
	}
}

// APIDocumentation provides API documentation standards
type APIDocumentation struct {
	Title       string                 `json:"title"`
	Version     string                 `json:"version"`
	Description string                 `json:"description"`
	BaseURL     string                 `json:"base_url"`
	Endpoints   []EndpointDoc          `json:"endpoints"`
	Models      map[string]interface{} `json:"models"`
	Examples    map[string]interface{} `json:"examples"`
}

// EndpointDoc documents an API endpoint
type EndpointDoc struct {
	Path        string                 `json:"path"`
	Method      string                 `json:"method"`
	Summary     string                 `json:"summary"`
	Description string                 `json:"description"`
	Parameters  []ParameterDoc         `json:"parameters"`
	RequestBody *RequestBodyDoc        `json:"request_body,omitempty"`
	Responses   map[string]ResponseDoc `json:"responses"`
	Tags        []string               `json:"tags"`
	Security    []string               `json:"security"`
}

// ParameterDoc documents a parameter
type ParameterDoc struct {
	Name        string      `json:"name"`
	In          string      `json:"in"` // "query", "path", "header"
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Type        string      `json:"type"`
	Example     interface{} `json:"example,omitempty"`
}

// RequestBodyDoc documents a request body
type RequestBodyDoc struct {
	Description string                 `json:"description"`
	Required    bool                   `json:"required"`
	Schema      map[string]interface{} `json:"schema"`
	Examples    map[string]interface{} `json:"examples"`
}

// ResponseDoc documents a response
type ResponseDoc struct {
	Description string                 `json:"description"`
	Schema      map[string]interface{} `json:"schema"`
	Examples    map[string]interface{} `json:"examples"`
}

// ConsistencyChecker validates API consistency
type ConsistencyChecker struct {
	rules []ConsistencyRule
}

// ConsistencyRule defines a consistency rule
type ConsistencyRule struct {
	Name        string
	Description string
	Check       func(interface{}) []ConsistencyViolation
}

// ConsistencyViolation represents a consistency violation
type ConsistencyViolation struct {
	Rule       string `json:"rule"`
	Message    string `json:"message"`
	Severity   string `json:"severity"` // "error", "warning", "info"
	Location   string `json:"location"`
	Suggestion string `json:"suggestion,omitempty"`
}

// NewConsistencyChecker creates a new consistency checker
func NewConsistencyChecker() *ConsistencyChecker {
	return &ConsistencyChecker{
		rules: []ConsistencyRule{
			{
				Name:        "response_structure",
				Description: "Ensure all responses follow standard structure",
				Check:       checkResponseStructure,
			},
			{
				Name:        "error_format",
				Description: "Ensure all errors follow standard format",
				Check:       checkErrorFormat,
			},
			{
				Name:        "pagination_consistency",
				Description: "Ensure pagination follows consistent patterns",
				Check:       checkPaginationConsistency,
			},
		},
	}
}

// CheckConsistency checks API consistency
func (cc *ConsistencyChecker) CheckConsistency(api interface{}) []ConsistencyViolation {
	var violations []ConsistencyViolation

	for _, rule := range cc.rules {
		ruleViolations := rule.Check(api)
		violations = append(violations, ruleViolations...)
	}

	return violations
}

// Consistency check functions
func checkResponseStructure(api interface{}) []ConsistencyViolation {
	// Implementation would check if responses follow StandardResponse structure
	return []ConsistencyViolation{}
}

func checkErrorFormat(api interface{}) []ConsistencyViolation {
	// Implementation would check if errors follow APIError structure
	return []ConsistencyViolation{}
}

func checkPaginationConsistency(api interface{}) []ConsistencyViolation {
	// Implementation would check if pagination follows consistent patterns
	return []ConsistencyViolation{}
}

// VersionManager manages API versioning
type VersionManager struct {
	versions map[APIVersion]VersionInfo
}

// VersionInfo contains version information
type VersionInfo struct {
	Version     APIVersion `json:"version"`
	Status      string     `json:"status"` // "active", "deprecated", "sunset"
	ReleaseDate time.Time  `json:"release_date"`
	SunsetDate  *time.Time `json:"sunset_date,omitempty"`
	Changes     []string   `json:"changes"`
	Migration   string     `json:"migration"`
}

// NewVersionManager creates a new version manager
func NewVersionManager() *VersionManager {
	return &VersionManager{
		versions: make(map[APIVersion]VersionInfo),
	}
}

// RegisterVersion registers a new API version
func (vm *VersionManager) RegisterVersion(info VersionInfo) {
	vm.versions[info.Version] = info
}

// GetVersion returns version information
func (vm *VersionManager) GetVersion(version APIVersion) (VersionInfo, bool) {
	info, exists := vm.versions[version]
	return info, exists
}

// ListVersions returns all registered versions
func (vm *VersionManager) ListVersions() []VersionInfo {
	var versions []VersionInfo
	for _, info := range vm.versions {
		versions = append(versions, info)
	}
	return versions
}

// IsSupported checks if a version is supported
func (vm *VersionManager) IsSupported(version APIVersion) bool {
	info, exists := vm.versions[version]
	if !exists {
		return false
	}

	return info.Status == "active" || info.Status == "deprecated"
}

// Global instances
var (
	DefaultResponseBuilder = NewResponseBuilder(V1, "")
	DefaultValidator       = NewAPIValidator()
	DefaultParser          = NewRequestParser(100)
	DefaultChecker         = NewConsistencyChecker()
	DefaultVersionManager  = NewVersionManager()
)

// Helper functions
func GenerateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

func ExtractUserID(ctx context.Context) string {
	if userID, ok := ctx.Value("user_id").(string); ok {
		return userID
	}
	return ""
}

func ExtractTenantID(ctx context.Context) string {
	if tenantID, ok := ctx.Value("tenant_id").(string); ok {
		return tenantID
	}
	return ""
}
