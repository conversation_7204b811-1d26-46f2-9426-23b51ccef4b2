/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Language selection screen following WeChat design.
 * Allows users to select their preferred language.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageScreen(
    onBackClick: () -> Unit,
    onLanguageSelected: (Language) -> Unit,
    currentLanguage: String,
    modifier: Modifier = Modifier
) {
    val languages = remember {
        listOf(
            Language(code = "zh_CN", name = "简体中文", isDefault = true),
            Language(code = "zh_TW", name = "繁體中文"),
            Language(code = "en", name = "English"),
            Language(code = "id", name = "Bahasa Indonesia (Indonesia)"),
            Language(code = "ms", name = "Bahasa Melayu (Malaysia)"),
            Language(code = "es", name = "español (Estados Unidos)"),
            Language(code = "ja", name = "日本語 (日本)"),
            Language(code = "th", name = "ไทย (ไทย)"),
            Language(code = "vi", name = "Tiếng Việt (Việt Nam)"),
            Language(code = "de", name = "Deutsch (Deutschland)"),
            Language(code = "fil", name = "Filipino")
        )
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "语言",
                    fontWeight = FontWeight.Medium
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                TextButton(
                    onClick = { /* Save changes */ }
                ) {
                    Text(
                        text = "保存",
                        color = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF4A90E2)
            )
        )
        
        // Language section header
        Text(
            text = "跟随系统语言",
            color = Color(0xFF333333),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(16.dp)
        )
        
        // Languages list
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            LazyColumn {
                items(languages) { language ->
                    LanguageItem(
                        language = language,
                        isSelected = language.name == currentLanguage,
                        onClick = { onLanguageSelected(language) }
                    )
                    
                    if (language != languages.last()) {
                        Divider(
                            modifier = Modifier.padding(start = 16.dp),
                            color = Color(0xFFE0E0E0)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun LanguageItem(
    language: Language,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = language.name,
            color = Color(0xFF333333),
            fontSize = 16.sp,
            modifier = Modifier.weight(1f)
        )
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = Color(0xFF4A90E2),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

// Data class for language options
data class Language(
    val code: String,
    val name: String,
    val isDefault: Boolean = false
) 