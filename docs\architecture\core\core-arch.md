好的，遵照您的指示。我将为您生成一份专门针对 **Monorepo `core/` 目录** 的、极致细化的、生产级**架构设计文档**。

这份文档将超越单纯的SRS，它会作为整个CINA.CLUB平台**技术体系的“宪法”**和**跨团队协作的“共同语言”**。它定义了连接所有后端、前端（iOS, Android, Web）的通用契约、模型和核心能力，是实现“Go-Centric”全栈协同的基石。

---
### CINA.CLUB - 共享核心库 (`core/`) 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [平台总架构师]

## 1. 概述

`core/` 目录是CINA.CLUB Monorepo的**心脏和基石**。它不是一个可独立运行的服务，而是一个**平台级的、跨所有技术栈共享的Go语言核心库**。其根本目的在于：**定义平台的“不变性”和“通用能力”，确保在快速演进的分布式系统中，所有组件都能基于一套稳定、权威、高效的基础进行协作。**

### 1.1 核心挑战
*   **跨语言一致性**: 如何确保Go后端、TypeScript前端、Python Worker对核心概念（如API、数据模型）的理解完全一致？
*   **逻辑复用最大化**: 如何将最复杂、最关键的逻辑（如E2EE加密）编写一次，并在所有端（后端、iOS, Android, Web）安全、高效地复用？
*   **依赖管理的纯洁性**: 如何构建一个不依赖任何具体业务实现，但又被所有业务实现依赖的纯粹基础？
*   **变更的全局影响**: 如何管理对`core/`的修改，以控制其对整个平台可能造成的“涟漪效应”？

### 1.2 架构愿景
本架构通过将`core/`设计为一个**多目标编译的Go模块**，利用**Protobuf**定义通信契约，并封装平台最底层的**通用模型和核心算法**，来应对上述挑战。它将成为平台技术稳定性的“压舱石”。

---

## 2. 架构图与目录结构

### 2.1 在Monorepo中的定位与依赖关系

```mermaid
graph TD
    subgraph "Monorepo Root"
        D_APPS[apps/*<br/>(Frontends)]
        D_SERVICES[services/*<br/>(Backends)]
        D_PKG[pkg/*<br/>(Backend Libs)]
        D_CORE[core/*<br/>(The Universal Core)]
    end

    D_APPS -- "依赖编译产物<br/>(WASM, .aar, .xcframework)" --> D_CORE
    D_SERVICES -- "直接Go import" --> D_CORE
    D_PKG -- "直接Go import" --> D_CORE
    
    style D_CORE fill:#b71c1c,stroke:#fff,stroke-width:4px,color:#fff
```
**黄金法则**: `core/`不依赖于`apps/`, `services/`, `pkg/`中的任何代码。依赖箭头永远指向`core/`。

### 2.2 最终目录结构 (`core/`)

```
core/
├── api/                        # 1. API契约中心 (The Language)
│   ├── buf.gen.yaml
│   ├── buf.yaml
│   └── proto/
│       └── v1/
│           ├── common.proto
│           ├── errors.proto
│           └── ... (所有服务的.proto文件)
├── models/                     # 2. 共享数据模型 (The Nouns)
│   ├── user.go
│   ├── service.go
│   ├── content.go
│   └── ... (所有核心业务对象的Go struct)
├── crypto/                     # 3. 端到端加密核心 (The Shield)
│   ├── e2ee.go
│   ├── exports_mobile.go
│   └── exports_wasm.go
├── datasync/                   # 4. 加密数据同步协议 (The Transporter)
│   ├── version_vector.go
│   ├── chunker.go
│   ├── engine.go
│   └── exports_*.go
├── aic/                        # 5. 本地AI核心 (The Brain)
│   ├── cgo_bindings.go
│   ├── engine.go
│   └── exports_mobile.go
└── go.mod                      # 核心包的Go模块定义
```

---

## 3. 各层职责深度解析

### 3.1 `core/api` - API契约中心 (The Language)

*   **职责**: **定义通信**。它是平台所有组件如何相互交谈的“语法和词典”。
*   **技术**: **Protocol Buffers (Protobuf) v3**。
*   **实现细节**:
    *   **服务定义 (`*_service.proto`)**: 定义gRPC服务和RPC方法。**必须**为需要对外的RPC添加`google.api.http`注解。
    *   **消息定义 (`*_model.proto`)**: 定义RPC使用的请求/响应消息体和通用的数据结构。**必须**对请求消息的字段使用`protoc-gen-validate`进行规则注解。
    *   **事件定义 (`*_events.proto`)**: 定义通过Kafka传递的领域事件的消息结构。
    *   **自动化**: 通过`buf`工具链，从此目录**自动生成**：
        1.  **Go**: 服务端存根、客户端、Protobuf Go结构体。
        2.  **TypeScript (for Frontends)**: gRPC-Web客户端、消息类型定义。
        3.  **OpenAPI v2/v3 Spec**: 用于生成REST API文档。
        4.  **Go验证代码**: `protoc-gen-validate`生成的`.pb.validate.go`文件。
*   **核心价值**: 保证了整个平台在API层面的**强类型一致性**和**向后兼容演进**。

### 3.2 `core/models` - 共享数据模型 (The Nouns)

*   **职责**: **定义业务核心概念**。它是平台业务领域在代码中的权威表示。
*   **技术**: **纯Go `struct`**。
*   **实现细节**:
    *   **纯粹性**: `struct`中**严禁**包含任何技术实现细节，特别是数据库ORM标签（`gorm`, `db`等）。它们是纯粹的数据容器。
    *   **来源**: 它们是业务分析和领域驱动设计(DDD)的产物，代表了如`User`, `Order`, `Review`等核心实体和值对象。
    *   **不可变性**: 推荐将模型设计为不可变的，任何修改都通过创建新实例来完成，以提高并发安全性和代码可预测性。
    *   **辅助方法**: 可以包含简单的、无副作用的辅助方法，如`User.FullName()`或`Order.IsCompleted()`。
*   **核心价值**: 为后端所有服务和前端核心逻辑提供了一套**统一的、稳定的业务对象内存表示**，避免了因数据结构不一致导致的各种bug。

### 3.3 `core/crypto` - 端到端加密核心 (The Shield)

*   **职责**: **提供密码学计算能力**。封装了平台E2EE方案所需的所有底层加密算法。
*   **技术**: Go标准库`crypto/...` 和 `golang.org/x/crypto`。
*   **实现细节**:
    *   **算法选择**: 强制使用业界推荐的现代、安全算法：
        *   **密钥派生**: **Argon2id**
        *   **对称加密**: **XChaCha20-Poly1305 (AEAD)**
        *   **公钥加密**: **X25519 (Key-agreement) + ChaCha20-Poly1305 (Box)**
        *   **数字签名**: **Ed25519**
    *   **安全封装**: API设计极其简单（如`Encrypt`, `Decrypt`），将随机数(nonce)管理、认证标签(tag)等所有复杂性全部封装在内部，防止调用者误用。
    *   **跨平台导出**: 通过构建标签和`exports_*.go`文件，将核心加密函数导出到Go Mobile和WASM。
*   **核心价值**: **保证了加密操作在所有平台上的绝对一致性和安全性**。这是实现可信E2EE的根本前提。

### 3.4 `core/datasync` - 加密数据同步协议 (The Transporter)

*   **职责**: **实现与`cloud-sync-service`交互的客户端同步协议**。
*   **技术**: Go语言实现的自定义协议逻辑。
*   **实现细节**:
    *   **版本控制**: 实现**版本向量(Version Vector)**的完整逻辑，包括比较（判断冲突）和合并。
    *   **数据处理**: 实现**内容定义分块(FastCDC)**和**SHA-256校验和**计算。
    *   **流程引擎**: 封装`push`和`pull`两个核心工作流。`push`流程负责加密、分块、去重、上传；`pull`流程负责拉取变更、下载、解密。
    *   **依赖关系**: 它会调用`core/crypto`进行加解密，并使用`core/api`生成的`cloud-sync-service`客户端来发起gRPC请求。
*   **核心价值**: 将复杂的、有状态的、对一致性要求极高的同步逻辑，封装成一个可靠的、可被所有前端复用的引擎。

### 3.5 `core/aic` - 本地AI核心 (The Brain)

*   **职责**: **提供调用本地AI推理引擎的统一接口**。
*   **技术**: **Go + CGO**。
*   **实现细节**:
    *   **绑定C++库**: 使用CGO绑定到高性能的C++推理库，如`llama.cpp`。
    *   **资源管理**: 在Go中实现对C++层模型和上下文的**安全内存管理**，使用`runtime.SetFinalizer`防止内存泄漏。
    *   **流式接口**: 提供支持Go channel或回调的流式生成接口，以实现打字机效果。
    *   **平台隔离**: 所有CGO相关的代码都通过构建标签（如`//go:build mobile`）隔离，确保`core/aic`在不支持CGO的环境（如WASM JS/GOOS）中依然可以编译。
*   **核心价值**: 将与底层硬件和C++库的复杂交互完全屏蔽，为上层应用提供一个简单的、统一的本地AI推理入口。

## 4. 总结

`core/` 目录的架构设计是CINA.CLUB平台实现其“Go-Centric”全栈协同愿景的技术核心。

*   **对于后端开发者**: 他们通过直接导入`core/api`和`core/models`，获得了统一的通信契约和业务模型。
*   **对于前端开发者**: 他们通过Go Mobile/WASM，获得了由`core/crypto`, `core/datasync`, `core/aic`提供的、经过验证的、高性能、高安全性的核心能力，而无需在每种前端语言（Swift, Kotlin, JS）中重复实现这些复杂逻辑。
*   **对于架构师和SRE**: `core/`是整个系统稳定性的核心控制点。对它的任何修改都意味着一次全局性的、需要审慎评估的变更。

通过这样一个精心设计的`core`库，平台在**开发效率、代码质量、系统性能、安全性和跨平台一致性**等多个维度上，都获得了巨大的架构优势。