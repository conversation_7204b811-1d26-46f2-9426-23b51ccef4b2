/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import (
	"context"
	"fmt"
	"log/slog"
	"math"
	"math/rand"
	"runtime/debug"
	"sync"
	"time"

	"github.com/segmentio/kafka-go"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// ConsumerHandler 消费者处理器接口
type ConsumerHandler interface {
	// Handle 处理单条消息
	Handle(ctx context.Context, msg proto.Message) error
}

// ConsumerGroup 消费者组
type ConsumerGroup struct {
	reader       *kafka.Reader
	handler      ConsumerHandler
	deserializer *Deserializer
	propagator   *Propagator
	logger       *slog.Logger
	tracer       trace.Tracer
	config       KafkaConsumerConfig
	dlqProducer  Producer
	closed       bool
	wg           sync.WaitGroup
	mu           sync.RWMutex
}

// NewConsumerGroup 创建新的消费者组
func NewConsumerGroup(
	cfg KafkaConsumerConfig,
	handler ConsumerHandler,
	eventTypeRegistry map[string]func() proto.Message,
	logger *slog.Logger,
	tracer trace.Tracer,
) (*ConsumerGroup, error) {
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	reader, err := NewKafkaReader(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create reader: %w", err)
	}

	// 创建反序列化器
	serializer := NewProtobufSerializer()
	deserializer := NewDeserializer(serializer)
	deserializer.RegisterEventTypes(eventTypeRegistry)

	// 创建死信队列生产者（如果启用）
	var dlqProducer Producer
	if cfg.DLQConfig.Enabled {
		dlqConfig := KafkaProducerConfig{
			Brokers:  cfg.Brokers,
			Security: cfg.Security,
			WriterConfig: KafkaWriterConfig{
				BatchSize:    10,
				BatchTimeout: time.Second,
				Async:        true,
				Compression:  "snappy",
				MaxAttempts:  3,
				WriteTimeout: 10 * time.Second,
				ReadTimeout:  10 * time.Second,
			},
		}

		dlqProducer, err = NewProducer(dlqConfig, logger, tracer)
		if err != nil {
			reader.Close()
			return nil, fmt.Errorf("failed to create DLQ producer: %w", err)
		}
	}

	return &ConsumerGroup{
		reader:       reader,
		handler:      handler,
		deserializer: deserializer,
		propagator:   NewPropagator(nil),
		logger:       logger,
		tracer:       tracer,
		config:       cfg,
		dlqProducer:  dlqProducer,
		closed:       false,
	}, nil
}

// Run 启动消费者组（阻塞操作）
func (cg *ConsumerGroup) Run(ctx context.Context) error {
	cg.mu.Lock()
	if cg.closed {
		cg.mu.Unlock()
		return ErrConsumerClosed
	}
	cg.mu.Unlock()

	cg.logger.Info("Starting consumer group",
		"group_id", cg.config.GroupID,
		"topics", cg.config.Topics,
	)

	for {
		select {
		case <-ctx.Done():
			cg.logger.Info("Consumer group context cancelled, shutting down")
			return ctx.Err()
		default:
			// 获取消息
			msg, err := cg.reader.FetchMessage(ctx)
			if err != nil {
				if ctx.Err() != nil {
					return ctx.Err()
				}
				cg.logger.Error("Failed to fetch message", "error", err)
				continue
			}

			// 异步处理消息
			cg.wg.Add(1)
			go cg.processMessage(ctx, msg)
		}
	}
}

// processMessage 处理单条消息
func (cg *ConsumerGroup) processMessage(ctx context.Context, msg kafka.Message) {
	defer cg.wg.Done()
	defer func() {
		if r := recover(); r != nil {
			cg.logger.Error("Message handler panic",
				"panic", r,
				"stack", string(debug.Stack()),
				"topic", msg.Topic,
				"partition", msg.Partition,
				"offset", msg.Offset,
			)
			// Panic 时仍然提交消息，避免阻塞分区
			if err := cg.reader.CommitMessages(ctx, msg); err != nil {
				cg.logger.Error("Failed to commit message after panic", "error", err)
			}
		}
	}()

	// 提取追踪上下文并创建新的 span
	spanCtx := cg.propagator.Extract(ctx, msg.Headers)
	spanCtx, span := cg.tracer.Start(spanCtx, "kafka.consume")
	defer span.End()

	// 添加消息信息到日志上下文
	headerMap := HeadersToMap(msg.Headers)
	eventType := headerMap[HeaderEventType]
	eventID := headerMap[HeaderEventID]

	logger := cg.logger.With(
		"topic", msg.Topic,
		"partition", msg.Partition,
		"offset", msg.Offset,
		"event_type", eventType,
		"event_id", eventID,
	)

	logger.Debug("Processing message")

	// 反序列化消息
	protoMsg, err := cg.deserializer.Deserialize(headerMap, msg.Value)
	if err != nil {
		logger.Error("Failed to deserialize message", "error", err)
		// 反序列化失败直接发送到 DLQ，不重试
		cg.sendToDLQ(spanCtx, msg, err, 0)
		cg.commitMessage(spanCtx, msg)
		return
	}

	// 处理消息（带重试）
	if err := cg.handleWithRetry(spanCtx, protoMsg, msg); err != nil {
		logger.Error("Failed to handle message after all retries", "error", err)
		cg.sendToDLQ(spanCtx, msg, err, cg.config.RetryConfig.MaxAttempts)
	}

	// 提交消息
	cg.commitMessage(spanCtx, msg)
}

// handleWithRetry 带重试的消息处理
func (cg *ConsumerGroup) handleWithRetry(ctx context.Context, msg proto.Message, originalMsg kafka.Message) error {
	if !cg.config.RetryConfig.Enabled {
		return cg.handler.Handle(ctx, msg)
	}

	var lastErr error
	for attempt := 1; attempt <= cg.config.RetryConfig.MaxAttempts; attempt++ {
		if err := cg.handler.Handle(ctx, msg); err == nil {
			if attempt > 1 {
				cg.logger.Info("Message handled successfully after retry",
					"attempt", attempt,
					"topic", originalMsg.Topic,
					"offset", originalMsg.Offset,
				)
			}
			return nil
		} else {
			lastErr = err

			// 检查是否可重试
			if !IsRetryableError(err) {
				cg.logger.Warn("Non-retryable error, giving up",
					"error", err,
					"attempt", attempt,
					"topic", originalMsg.Topic,
					"offset", originalMsg.Offset,
				)
				return err
			}

			if attempt < cg.config.RetryConfig.MaxAttempts {
				backoff := cg.calculateBackoff(attempt)
				cg.logger.Warn("Message handling failed, will retry",
					"error", err,
					"attempt", attempt,
					"max_attempts", cg.config.RetryConfig.MaxAttempts,
					"backoff", backoff,
					"topic", originalMsg.Topic,
					"offset", originalMsg.Offset,
				)

				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(backoff):
					// 继续重试
				}
			}
		}
	}

	return lastErr
}

// calculateBackoff 计算退避时间
func (cg *ConsumerGroup) calculateBackoff(attempt int) time.Duration {
	cfg := cg.config.RetryConfig

	// 指数退避
	backoff := float64(cfg.InitialInterval) * math.Pow(cfg.BackoffMultiplier, float64(attempt-1))

	// 限制最大退避时间
	if maxBackoff := float64(cfg.MaxInterval); backoff > maxBackoff {
		backoff = maxBackoff
	}

	// 添加抖动
	jitter := backoff * cfg.Jitter * (rand.Float64()*2 - 1) // [-jitter, +jitter]
	backoff += jitter

	// 确保不为负数
	if backoff < 0 {
		backoff = float64(cfg.InitialInterval)
	}

	return time.Duration(backoff)
}

// sendToDLQ 发送消息到死信队列
func (cg *ConsumerGroup) sendToDLQ(ctx context.Context, originalMsg kafka.Message, err error, attempts int) {
	if !cg.config.DLQConfig.Enabled || cg.dlqProducer == nil {
		return
	}

	dlqTopic := cg.config.DLQConfig.GetDLQTopic(originalMsg.Topic)
	if dlqTopic == "" {
		return
	}

	// 构建 DLQ 消息的元数据
	dlqHeaders := make(map[string]string)

	// 复制原始头
	for _, header := range originalMsg.Headers {
		dlqHeaders[header.Key] = string(header.Value)
	}

	// 添加 DLQ 特定头
	dlqHeaders["x-dlq-original-topic"] = originalMsg.Topic
	dlqHeaders["x-dlq-error"] = err.Error()
	dlqHeaders["x-dlq-attempts"] = fmt.Sprintf("%d", attempts)
	dlqHeaders["x-dlq-timestamp"] = time.Now().Format(time.RFC3339)

	// 将原始消息作为 DLQ 消息内容发送
	// 注意：这里直接发送原始的消息字节，DLQ 消息的元数据在 headers 中
	var emptyMsg = &EmptyMessage{}

	// 发送到 DLQ
	if err := cg.dlqProducer.PublishWithHeaders(ctx, dlqTopic, string(originalMsg.Key), emptyMsg, dlqHeaders); err != nil {
		cg.logger.Error("Failed to send message to DLQ",
			"error", err,
			"dlq_topic", dlqTopic,
			"original_topic", originalMsg.Topic,
			"offset", originalMsg.Offset,
		)
	} else {
		cg.logger.Info("Message sent to DLQ",
			"dlq_topic", dlqTopic,
			"original_topic", originalMsg.Topic,
			"offset", originalMsg.Offset,
			"attempts", attempts,
		)
	}
}

// commitMessage 提交消息
func (cg *ConsumerGroup) commitMessage(ctx context.Context, msg kafka.Message) {
	if err := cg.reader.CommitMessages(ctx, msg); err != nil {
		cg.logger.Error("Failed to commit message",
			"error", err,
			"topic", msg.Topic,
			"partition", msg.Partition,
			"offset", msg.Offset,
		)
	}
}

// Close 关闭消费者组
func (cg *ConsumerGroup) Close() error {
	cg.mu.Lock()
	defer cg.mu.Unlock()

	if cg.closed {
		return nil
	}

	cg.closed = true

	cg.logger.Info("Closing consumer group")

	// 等待所有消息处理完成
	cg.wg.Wait()

	// 关闭读取器
	if err := cg.reader.Close(); err != nil {
		cg.logger.Error("Failed to close reader", "error", err)
	}

	// 关闭 DLQ 生产者
	if cg.dlqProducer != nil {
		if err := cg.dlqProducer.Close(); err != nil {
			cg.logger.Error("Failed to close DLQ producer", "error", err)
		}
	}

	cg.logger.Info("Consumer group closed successfully")
	return nil
}

// EmptyMessage 空消息，用于 DLQ 场景
type EmptyMessage struct{}

// 实现 proto.Message 接口
func (m *EmptyMessage) Reset()         {}
func (m *EmptyMessage) String() string { return "EmptyMessage{}" }
func (m *EmptyMessage) ProtoMessage()  {}

// ProtoReflect 实现 protoreflect.ProtoMessage 接口
func (m *EmptyMessage) ProtoReflect() protoreflect.Message {
	// 简化实现，返回 nil 即可
	return nil
}

// DLQMessageInfo DLQ 消息信息（通过 headers 传递）
type DLQMessageInfo struct {
	OriginalTopic     string
	OriginalPartition int32
	OriginalOffset    int64
	OriginalKey       []byte
	OriginalValue     []byte
	Error             string
	Attempts          int32
	Timestamp         int64
}

// MockConsumerHandler 模拟消费者处理器，用于测试
type MockConsumerHandler struct {
	HandleFunc func(ctx context.Context, msg proto.Message) error
	Messages   []proto.Message
	mu         sync.Mutex
}

// NewMockConsumerHandler 创建模拟消费者处理器
func NewMockConsumerHandler() *MockConsumerHandler {
	return &MockConsumerHandler{
		Messages: make([]proto.Message, 0),
	}
}

// Handle 处理消息
func (m *MockConsumerHandler) Handle(ctx context.Context, msg proto.Message) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.Messages = append(m.Messages, msg)

	if m.HandleFunc != nil {
		return m.HandleFunc(ctx, msg)
	}

	return nil
}

// GetMessages 获取处理的消息
func (m *MockConsumerHandler) GetMessages() []proto.Message {
	m.mu.Lock()
	defer m.mu.Unlock()

	messages := make([]proto.Message, len(m.Messages))
	copy(messages, m.Messages)
	return messages
}

// Reset 重置
func (m *MockConsumerHandler) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.Messages = make([]proto.Message, 0)
	m.HandleFunc = nil
}
