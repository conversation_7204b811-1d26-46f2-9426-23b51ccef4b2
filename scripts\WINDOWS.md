# Windows 用户指南

CINA.CLUB 的开发脚本是为 Unix-like 环境设计的。Windows 用户需要安装额外的工具来运行这些脚本。

## 推荐方案：WSL2

Windows Subsystem for Linux 2 (WSL2) 是在 Windows 上运行 Linux 环境的最佳方案。

### 安装 WSL2

1. **启用 WSL 功能**（以管理员身份运行 PowerShell）：
   ```powershell
   dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
   dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
   ```

2. **重启计算机**

3. **安装 WSL2**：
   ```powershell
   wsl --install
   ```

4. **设置默认版本**：
   ```powershell
   wsl --set-default-version 2
   ```

5. **安装 Ubuntu**：
   ```powershell
   wsl --install -d Ubuntu
   ```

### 在 WSL2 中设置开发环境

1. **启动 WSL2**：
   ```bash
   wsl
   ```

2. **更新系统**：
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

3. **安装必需工具**：
   ```bash
   # Go
   wget https://go.dev/dl/go1.22.0.linux-amd64.tar.gz
   sudo tar -C /usr/local -xzf go1.22.0.linux-amd64.tar.gz
   echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
   
   # Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   
   # 其他工具
   sudo apt install -y git make curl wget unzip
   ```

4. **重新加载环境**：
   ```bash
   source ~/.bashrc
   newgrp docker
   ```

5. **克隆项目到 WSL2 文件系统**：
   ```bash
   # 在 WSL2 中克隆项目（更好的性能）
   git clone <your-repo-url> ~/cina-club
   cd ~/cina-club
   
   # 或者使用 Windows 文件系统中的项目
   cd /mnt/g/Root/66_Cina.Club/13_CINA.CLUB-Monorepo
   ```

6. **运行设置脚本**：
   ```bash
   make setup
   ```

## 替代方案：Git Bash

如果无法使用 WSL2，可以使用 Git Bash，但功能有限。

### 安装 Git Bash

1. 下载并安装 [Git for Windows](https://git-scm.com/download/win)
2. 安装时选择 "Git Bash Here" 选项

### 在 Git Bash 中运行

1. **右键点击项目文件夹** → **Git Bash Here**

2. **安装基本工具**：
   ```bash
   # 检查 Go
   go version
   
   # 检查 Node.js
   node --version
   
   # 安装包管理器
   npm install -g pnpm
   ```

3. **运行基本脚本**：
   ```bash
   # 只能运行部分脚本
   ./scripts/gen/proto.sh
   ./scripts/ci/lint.sh --fast
   ```

**注意**：Git Bash 环境限制较多，某些脚本可能无法正常工作。

## 替代方案：Docker 容器

使用 Docker 容器运行开发环境。

### 创建开发容器

1. **创建 Dockerfile**：
   ```dockerfile
   FROM ubuntu:22.04
   
   # 安装必需工具
   RUN apt-get update && apt-get install -y \
       curl \
       git \
       make \
       wget \
       unzip \
       build-essential \
       && rm -rf /var/lib/apt/lists/*
   
   # 安装 Go
   RUN wget https://go.dev/dl/go1.22.0.linux-amd64.tar.gz && \
       tar -C /usr/local -xzf go1.22.0.linux-amd64.tar.gz && \
       rm go1.22.0.linux-amd64.tar.gz
   ENV PATH=$PATH:/usr/local/go/bin
   
   # 安装 Node.js
   RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
       apt-get install -y nodejs
   
   WORKDIR /workspace
   ```

2. **构建镜像**：
   ```powershell
   docker build -t cina-dev .
   ```

3. **运行容器**：
   ```powershell
   docker run -it --rm -v ${PWD}:/workspace cina-dev bash
   ```

4. **在容器中开发**：
   ```bash
   make setup
   make gen
   make test
   ```

## VS Code 集成

使用 VS Code 的 Remote Development 功能：

### WSL2 集成

1. **安装扩展**：
   - Remote - WSL
   - Remote - Containers

2. **在 WSL2 中打开项目**：
   - `Ctrl+Shift+P`
   - 输入 "Remote-WSL: Open Folder in WSL"
   - 选择项目文件夹

### 容器集成

1. **创建 `.devcontainer/devcontainer.json`**：
   ```json
   {
     "name": "CINA.CLUB Dev",
     "dockerFile": "../Dockerfile",
     "settings": {
       "go.gopath": "/go",
       "go.goroot": "/usr/local/go"
     },
     "extensions": [
       "golang.go",
       "ms-vscode.vscode-typescript-next"
     ],
     "forwardPorts": [3000, 8080],
     "postCreateCommand": "make setup"
   }
   ```

2. **在容器中重新打开**：
   - `Ctrl+Shift+P`
   - 输入 "Remote-Containers: Reopen in Container"

## 性能优化

### WSL2 性能优化

1. **将项目放在 WSL2 文件系统中**：
   ```bash
   # 好：在 WSL2 文件系统中
   ~/projects/cina-club/

   # 差：在 Windows 文件系统中
   /mnt/c/projects/cina-club/
   ```

2. **配置 WSL2 资源限制**（创建 `%UserProfile%\.wslconfig`）：
   ```ini
   [wsl2]
   memory=8GB
   processors=4
   swap=2GB
   ```

3. **启用 systemd**（在 WSL2 中编辑 `/etc/wsl.conf`）：
   ```ini
   [boot]
   systemd=true
   ```

### Docker 性能优化

1. **启用 WSL2 后端**：
   - Docker Desktop → Settings → General → Use WSL 2 based engine

2. **增加资源分配**：
   - Docker Desktop → Settings → Resources

## 故障排除

### 常见问题

1. **权限错误**：
   ```bash
   chmod +x scripts/**/*.sh
   ```

2. **路径问题**：
   ```bash
   # 使用相对路径
   ./scripts/gen/proto.sh
   
   # 不要使用绝对路径
   /path/to/scripts/gen/proto.sh
   ```

3. **Docker 访问错误**：
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

4. **WSL2 网络问题**：
   ```powershell
   # 重启 WSL2
   wsl --shutdown
   wsl
   ```

### 获取帮助

如果遇到问题，可以：

1. **查看详细错误**：
   ```bash
   DEBUG=1 ./scripts/your-script.sh
   ```

2. **干运行模式**：
   ```bash
   DRY_RUN=1 ./scripts/your-script.sh
   ```

3. **检查环境**：
   ```bash
   make check-tools
   ```

4. **查看文档**：
   ```bash
   ./scripts/your-script.sh --help
   ```

## 总结

对于 Windows 用户，我们**强烈推荐使用 WSL2**，因为它提供了：

- ✅ 完整的 Linux 环境
- ✅ 良好的性能
- ✅ 与 Windows 的集成
- ✅ 完整的工具支持
- ✅ VS Code 集成

其他方案只应在无法使用 WSL2 时考虑。 