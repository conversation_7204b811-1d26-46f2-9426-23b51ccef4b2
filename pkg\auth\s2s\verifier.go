/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

// Package s2s provides service-to-service JWT token verification.
package s2s

import (
	"context"
	"crypto"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

const (
	defaultTokenTTL = 5 * time.Minute // S2S tokens should be short-lived
)

var (
	ErrInvalidToken     = errors.New("invalid S2S token")
	ErrServiceNotFound  = errors.New("service not found in key provider")
	ErrInvalidPublicKey = errors.New("invalid public key format")
	ErrTokenExpired     = errors.New("S2S token expired")
	ErrInvalidClaims    = errors.New("invalid S2S token claims")
)

// PublicKeyProvider defines the interface for retrieving service public keys
type PublicKeyProvider interface {
	GetPublicKey(ctx context.Context, serviceName string) (crypto.PublicKey, error)
}

// StaticKeyProvider implements PublicKeyProvider using a static map of service names to PEM-encoded public keys
type StaticKeyProvider struct {
	keys map[string]crypto.PublicKey
	mu   sync.RWMutex
}

// NewStaticKeyProvider creates a new static key provider from PEM-encoded public keys
func NewStaticKeyProvider(pemKeys map[string]string) (*StaticKeyProvider, error) {
	provider := &StaticKeyProvider{
		keys: make(map[string]crypto.PublicKey),
	}

	for serviceName, pemKey := range pemKeys {
		publicKey, err := parsePublicKeyFromPEM(pemKey)
		if err != nil {
			return nil, fmt.Errorf("invalid public key for service %s: %w", serviceName, err)
		}
		provider.keys[serviceName] = publicKey
	}

	return provider, nil
}

// GetPublicKey retrieves the public key for the specified service
func (p *StaticKeyProvider) GetPublicKey(ctx context.Context, serviceName string) (crypto.PublicKey, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	key, exists := p.keys[serviceName]
	if !exists {
		return nil, fmt.Errorf("%w: %s", ErrServiceNotFound, serviceName)
	}

	return key, nil
}

// UpdateKey updates the public key for a service
func (p *StaticKeyProvider) UpdateKey(serviceName, pemKey string) error {
	publicKey, err := parsePublicKeyFromPEM(pemKey)
	if err != nil {
		return fmt.Errorf("invalid public key: %w", err)
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	p.keys[serviceName] = publicKey
	return nil
}

// RemoveKey removes the public key for a service
func (p *StaticKeyProvider) RemoveKey(serviceName string) {
	p.mu.Lock()
	defer p.mu.Unlock()

	delete(p.keys, serviceName)
}

// S2SClaims represents the standard claims for service-to-service tokens
type S2SClaims struct {
	jwt.RegisteredClaims
	Service string `json:"service"` // Calling service name
}

// Verifier provides S2S JWT token verification
type Verifier struct {
	keyProvider PublicKeyProvider
	clockSkew   time.Duration
}

// NewVerifier creates a new S2S token verifier
func NewVerifier(keyProvider PublicKeyProvider) *Verifier {
	return &Verifier{
		keyProvider: keyProvider,
		clockSkew:   5 * time.Second, // Allow 5 seconds clock skew
	}
}

// NewVerifierWithClockSkew creates a new S2S token verifier with custom clock skew tolerance
func NewVerifierWithClockSkew(keyProvider PublicKeyProvider, clockSkew time.Duration) *Verifier {
	return &Verifier{
		keyProvider: keyProvider,
		clockSkew:   clockSkew,
	}
}

// VerifyToken verifies an S2S JWT token and returns the claims
func (v *Verifier) VerifyToken(ctx context.Context, tokenString string, expectedAudience string) (*S2SClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &S2SClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Extract the service name from claims to get the right public key
		claims, ok := token.Claims.(*S2SClaims)
		if !ok {
			return nil, ErrInvalidClaims
		}

		// Get public key for the calling service
		return v.keyProvider.GetPublicKey(ctx, claims.Service)
	})

	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrInvalidToken, err)
	}

	claims, ok := token.Claims.(*S2SClaims)
	if !ok || !token.Valid {
		return nil, ErrInvalidToken
	}

	// Validate standard claims with clock skew tolerance
	now := time.Now()

	// Check expiration
	if claims.ExpiresAt != nil && now.Add(-v.clockSkew).After(claims.ExpiresAt.Time) {
		return nil, ErrTokenExpired
	}

	// Check not before
	if claims.NotBefore != nil && now.Add(v.clockSkew).Before(claims.NotBefore.Time) {
		return nil, ErrInvalidToken
	}

	// Check audience if specified
	if expectedAudience != "" {
		if claims.Audience == nil || len(claims.Audience) == 0 {
			return nil, fmt.Errorf("%w: missing audience", ErrInvalidClaims)
		}

		audienceValid := false
		for _, aud := range claims.Audience {
			if aud == expectedAudience {
				audienceValid = true
				break
			}
		}

		if !audienceValid {
			return nil, fmt.Errorf("%w: invalid audience", ErrInvalidClaims)
		}
	}

	// Validate service claim
	if claims.Service == "" {
		return nil, fmt.Errorf("%w: missing service claim", ErrInvalidClaims)
	}

	return claims, nil
}

// parsePublicKeyFromPEM parses a PEM-encoded public key
func parsePublicKeyFromPEM(pemKey string) (crypto.PublicKey, error) {
	block, _ := pem.Decode([]byte(pemKey))
	if block == nil {
		return nil, ErrInvalidPublicKey
	}

	switch block.Type {
	case "PUBLIC KEY":
		return x509.ParsePKIXPublicKey(block.Bytes)
	case "RSA PUBLIC KEY":
		return x509.ParsePKCS1PublicKey(block.Bytes)
	default:
		return nil, fmt.Errorf("%w: unsupported key type %s", ErrInvalidPublicKey, block.Type)
	}
}

// GenerateS2SToken generates an S2S token for testing purposes
// Note: In production, each service should generate its own tokens using its private key
func GenerateS2SToken(privateKey *rsa.PrivateKey, serviceName, audience string, ttl time.Duration) (string, error) {
	now := time.Now()
	claims := &S2SClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    serviceName,
			Subject:   serviceName,
			Audience:  []string{audience},
			ExpiresAt: jwt.NewNumericDate(now.Add(ttl)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
		Service: serviceName,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	return token.SignedString(privateKey)
}
