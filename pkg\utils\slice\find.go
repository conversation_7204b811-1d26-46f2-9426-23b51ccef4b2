/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package slice

// Find returns the first element in the slice that matches the predicate function.
// Returns the element and true if found, or zero value and false if not found.
//
// Example:
//
//	num, found := slice.Find([]int{1, 2, 3, 4}, func(x int) bool { return x > 2 })
//	// num = 3, found = true
//
//	str, found := slice.Find([]string{"apple", "banana", "cherry"}, func(s string) bool { return len(s) > 6 })
//	// str = "banana", found = true
func Find[T any](s []T, predicate func(T) bool) (T, bool) {
	for _, item := range s {
		if predicate(item) {
			return item, true
		}
	}
	var zero T
	return zero, false
}

// FindIndex returns the index of the first element in the slice that matches the predicate function.
// Returns the index if found, or -1 if not found.
//
// Example:
//
//	index := slice.FindIndex([]int{1, 2, 3, 4}, func(x int) bool { return x > 2 })
//	// index = 2 (for element 3)
func FindIndex[T any](s []T, predicate func(T) bool) int {
	for i, item := range s {
		if predicate(item) {
			return i
		}
	}
	return -1
}

// Any returns true if at least one element in the slice matches the predicate function.
//
// Example:
//
//	hasEven := slice.Any([]int{1, 3, 5, 8}, func(x int) bool { return x%2 == 0 })
//	// hasEven = true (because of 8)
func Any[T any](s []T, predicate func(T) bool) bool {
	for _, item := range s {
		if predicate(item) {
			return true
		}
	}
	return false
}

// All returns true if all elements in the slice match the predicate function.
// Returns true for empty slices.
//
// Example:
//
//	allEven := slice.All([]int{2, 4, 6, 8}, func(x int) bool { return x%2 == 0 })
//	// allEven = true
//
//	allPositive := slice.All([]int{1, 2, -3, 4}, func(x int) bool { return x > 0 })
//	// allPositive = false (because of -3)
func All[T any](s []T, predicate func(T) bool) bool {
	for _, item := range s {
		if !predicate(item) {
			return false
		}
	}
	return true
}

// CountIf returns the number of elements in the slice that match the predicate function.
//
// Example:
//
//	evenCount := slice.CountIf([]int{1, 2, 3, 4, 5, 6}, func(x int) bool { return x%2 == 0 })
//	// evenCount = 3 (for 2, 4, 6)
func CountIf[T any](s []T, predicate func(T) bool) int {
	count := 0
	for _, item := range s {
		if predicate(item) {
			count++
		}
	}
	return count
}
