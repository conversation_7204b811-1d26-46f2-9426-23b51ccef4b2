/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package errors

import (
	"fmt"
	"io"
	"runtime"

	"github.com/pkg/errors"
)

// StackTracer 接口定义了获取堆栈跟踪的能力
type StackTracer interface {
	StackTrace() errors.StackTrace
}

// causer 接口定义了获取底层错误原因的能力
type causer interface {
	Cause() error
}

// stackTrace 实现堆栈跟踪功能
type stackTrace struct {
	stack errors.StackTrace
}

// newStack 创建一个新的堆栈跟踪，跳过指定数量的调用栈帧
func newStack(skip int) *stackTrace {
	const depth = 32
	var pcs [depth]uintptr
	n := runtime.Callers(skip+2, pcs[:]) // +2 跳过当前函数和runtime.Callers
	var st errors.StackTrace = make([]errors.Frame, n)
	for i := 0; i < n; i++ {
		st[i] = errors.Frame(pcs[i])
	}
	return &stackTrace{stack: st}
}

// StackTrace 返回堆栈跟踪信息
func (s *stackTrace) StackTrace() errors.StackTrace {
	return s.stack
}

// Format 实现fmt.Formatter接口，支持格式化输出
func (s *stackTrace) Format(st fmt.State, verb rune) {
	switch verb {
	case 'v':
		if st.Flag('+') {
			for _, f := range s.stack {
				fmt.Fprintf(st, "\n%+v", f)
			}
		}
	}
}

// HasStack 检查错误是否包含堆栈跟踪信息
func HasStack(err error) bool {
	if err == nil {
		return false
	}

	// 检查是否实现了StackTracer接口
	if _, ok := err.(StackTracer); ok {
		return true
	}

	// 检查是否是pkg/errors创建的错误
	if _, ok := err.(causer); ok {
		return true
	}

	return false
}

// GetStack 从错误中提取堆栈跟踪信息
func GetStack(err error) errors.StackTrace {
	if err == nil {
		return nil
	}

	// 首先尝试从StackTracer接口获取
	if st, ok := err.(StackTracer); ok {
		return st.StackTrace()
	}

	// 然后尝试从pkg/errors的错误类型获取
	type stackTracer interface {
		StackTrace() errors.StackTrace
	}

	if st, ok := err.(stackTracer); ok {
		return st.StackTrace()
	}

	return nil
}

// formatStack 格式化堆栈跟踪信息为字符串
func formatStack(stack errors.StackTrace) string {
	if stack == nil {
		return ""
	}

	return fmt.Sprintf("%+v", stack)
}

// printStack 将堆栈跟踪打印到指定的writer
func printStack(w io.Writer, stack errors.StackTrace) {
	if stack == nil {
		return
	}

	for _, frame := range stack {
		fmt.Fprintf(w, "%+v\n", frame)
	}
}
