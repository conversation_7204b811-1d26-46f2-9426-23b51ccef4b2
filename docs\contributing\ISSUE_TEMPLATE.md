# Issue模板

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

## Bug报告模板

### 问题描述
<!-- 简要描述遇到的问题 -->


### 复现步骤
<!-- 详细描述如何复现这个问题 -->
1. 
2. 
3. 

### 预期行为
<!-- 描述您期望发生的行为 -->


### 实际行为
<!-- 描述实际发生的行为 -->


### 环境信息
**客户端环境：**
- 操作系统：[例如：Windows 11, macOS 13.0, Ubuntu 22.04]
- 浏览器：[例如：Chrome 120.0, Safari 17.0]
- 应用版本：[例如：v1.0.0]
- 设备类型：[例如：PC, Mobile, Tablet]

**服务端环境：**
- Go版本：[例如：1.21.0]
- 数据库：[例如：PostgreSQL 15.0]
- 部署环境：[例如：Docker, Kubernetes]

### 日志和错误信息
<!-- 粘贴相关的日志或错误信息 -->
```
在此粘贴日志或错误信息
```

### 截图
<!-- 如果有助于说明问题，请提供截图 -->


### 附加信息
<!-- 任何其他有助于解决问题的信息 -->


---

## 功能请求模板

### 功能描述
<!-- 简要描述您希望增加的功能 -->


### 使用场景
<!-- 详细描述这个功能的使用场景和价值 -->


### 解决方案建议
<!-- 描述您期望的解决方案 -->


### 替代方案
<!-- 描述您考虑过的其他替代方案 -->


### 实现复杂度评估
<!-- 您认为这个功能的实现复杂度如何 -->
- [ ] 🟢 简单（1-3天）
- [ ] 🟡 中等（1-2周）
- [ ] 🔴 复杂（1个月以上）
- [ ] 🤷 不确定

### 优先级
<!-- 这个功能对您来说有多重要 -->
- [ ] 🔴 高（阻塞性问题）
- [ ] 🟡 中（重要但不阻塞）
- [ ] 🟢 低（便民功能）

### 附加信息
<!-- 任何其他相关信息，如设计图、参考链接等 -->


---

## 文档问题模板

### 文档位置
<!-- 指出存在问题的文档位置 -->
- 文件路径：
- 章节：
- 链接：

### 问题类型
- [ ] 📝 内容错误
- [ ] 🔗 链接失效
- [ ] 📖 内容缺失
- [ ] 🎨 格式问题
- [ ] 🌍 翻译问题
- [ ] 🔄 内容过时

### 问题描述
<!-- 详细描述文档中的问题 -->


### 建议修改
<!-- 提供您的修改建议 -->


---

## 性能问题模板

### 性能问题描述
<!-- 描述遇到的性能问题 -->


### 性能指标
<!-- 提供具体的性能数据 -->
- 响应时间：
- 吞吐量：
- 内存使用：
- CPU使用：

### 测试环境
<!-- 描述测试环境 -->
- 硬件配置：
- 网络环境：
- 数据量：
- 并发用户数：

### 分析和建议
<!-- 如果有初步分析结果，请提供 -->


---

## 安全问题模板

⚠️ **注意：请不要在公开Issue中报告安全漏洞**

如果您发现了安全漏洞，请发送邮件至：<EMAIL>

包含以下信息：
- 漏洞类型
- 影响范围
- 复现步骤
- 修复建议

我们将在24小时内回复您，并在修复后公开感谢您的贡献。

---

## 提交指南

### 提交前检查
- [ ] 我已经搜索了现有的Issues，确认这不是重复问题
- [ ] 我已经阅读了[贡献指南](CONTRIBUTING.md)
- [ ] 我已经提供了足够的信息来重现或理解问题
- [ ] 我使用了合适的标签来分类这个Issue

### 标签指南
请在创建Issue时选择合适的标签：

**类型标签：**
- `bug`: Bug报告
- `enhancement`: 功能请求
- `documentation`: 文档相关
- `performance`: 性能问题
- `security`: 安全问题

**优先级标签：**
- `priority:high`: 高优先级
- `priority:medium`: 中优先级
- `priority:low`: 低优先级

**组件标签：**
- `component:backend`: 后端相关
- `component:frontend`: 前端相关
- `component:mobile`: 移动端相关
- `component:api`: API相关
- `component:database`: 数据库相关

**状态标签：**
- `status:investigating`: 调查中
- `status:confirmed`: 已确认
- `status:in-progress`: 进行中
- `status:testing`: 测试中

### 响应时间预期
- **Bug报告**: 48小时内响应
- **功能请求**: 1周内响应
- **文档问题**: 72小时内响应
- **性能问题**: 48小时内响应
- **安全问题**: 24小时内响应

### 联系方式
如果您需要更快的响应或有其他疑问，请联系：
- **技术支持**: <EMAIL>
- **社区管理**: <EMAIL>
- **紧急问题**: <EMAIL>

---

**感谢您帮助改进CINA.CLUB项目！** 🙏 