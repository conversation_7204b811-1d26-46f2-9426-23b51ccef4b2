/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 15:40:00
 * Modified: 2025-01-23 20:40:00
 */

// Import the generated service
import { DefaultService } from '@/api/generated/services/DefaultService';
import type { 
  User, 
  UserRole, 
  UserStatus, 
  CreateUserRequest, 
  UpdateUserRequest,
  UserListParams,
  UserListResponse,
  Permission,
  UserActivity,
  UserStatistics,
  BulkUserOperation,
  BulkOperationResult
} from '@/types/user';

/**
 * User Management API Service
 * This service class now acts as a clean wrapper around the auto-generated API client,
 * mapping our internal types to the generated types if necessary and providing a consistent
 * interface for the rest of the application.
 */
export class UserApiService {
  private static readonly BASE_PATH = '/api/v1/users';

  /**
   * Get paginated list of users with filtering and sorting
   */
  static async getUsers(params: UserListParams): Promise<UserListResponse> {
    // Call the generated method
    return await DefaultService.getUsers(params.page, params.pageSize);
  }

  /**
   * Get user by ID with detailed information
   */
  static async getUserById(id: string): Promise<User> {
    return await DefaultService.getUserById(id);
  }

  /**
   * Create new user
   */
  static async createUser(userData: CreateUserRequest): Promise<User> {
    // The generated client expects the request body directly.
    return await DefaultService.createUser(userData);
  }

  /**
   * Update existing user
   * (Note: This endpoint wasn't in the sample OpenAPI spec, so we'd add it there first,
   * then regenerate the client. For now, this would be a manual call.)
   */
  static async updateUser(id: string, userData: UpdateUserRequest): Promise<User> {
    // This would ideally be: return await DefaultService.updateUser(id, userData);
    console.warn("updateUser is not yet generated from OpenAPI spec.");
    return {} as User;
  }

  /**
   * Delete user (soft delete)
   */
  static async deleteUser(id: string): Promise<{ success: boolean; message: string }> {
    await DefaultService.deleteUser(id);
    return { success: true, message: "User deleted successfully." };
  }

  /**
   * Update user status (enable/disable/suspend)
   */
  static async updateUserStatus(id: string, status: UserStatus): Promise<User> {
    const response = await DefaultService.updateUserStatus(id, status);
    return response;
  }

  /**
   * Assign role to user
   */
  static async assignRole(id: string, role: UserRole): Promise<User> {
    const response = await DefaultService.assignRole(id, role);
    return response;
  }

  /**
   * Update user permissions
   */
  static async updatePermissions(id: string, permissions: Permission[]): Promise<User> {
    const response = await DefaultService.updatePermissions(id, permissions);
    return response;
  }

  /**
   * Get user activity timeline
   */
  static async getUserActivity(id: string, limit = 50): Promise<UserActivity[]> {
    const response = await DefaultService.getUserActivity(id, limit);
    return response;
  }

  /**
   * Get user statistics
   */
  static async getUserStatistics(id: string): Promise<UserStatistics> {
    const response = await DefaultService.getUserStatistics(id);
    return response;
  }

  /**
   * Search users by query
   */
  static async searchUsers(query: string, filters?: Partial<UserListParams>): Promise<User[]> {
    const response = await DefaultService.searchUsers(query, filters);
    return response;
  }

  /**
   * Send invitation email to user
   */
  static async sendInvitation(email: string, role: UserRole): Promise<{ success: boolean; message: string }> {
    const response = await DefaultService.sendInvitation(email, role);
    return response;
  }

  /**
   * Resend invitation email
   */
  static async resendInvitation(id: string): Promise<{ success: boolean; message: string }> {
    const response = await DefaultService.resendInvitation(id);
    return response;
  }

  /**
   * Perform bulk operations on multiple users
   */
  static async bulkOperation(operation: BulkUserOperation): Promise<BulkOperationResult> {
    const response = await DefaultService.bulkOperation(operation);
    return response;
  }

  /**
   * Export users data
   */
  static async exportUsers(filters?: Partial<UserListParams>, format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const response = await DefaultService.exportUsers(filters, format);
    return response;
  }

  /**
   * Get user roles and permissions metadata
   */
  static async getRolesAndPermissions(): Promise<{ roles: UserRole[]; permissions: Permission[] }> {
    const response = await DefaultService.getRolesAndPermissions();
    return response;
  }

  /**
   * Validate user data before creation/update
   */
  static async validateUserData(userData: Partial<CreateUserRequest | UpdateUserRequest>): Promise<{
    valid: boolean;
    errors: Record<string, string[]>;
  }> {
    const response = await DefaultService.validateUserData(userData);
    return response;
  }

  /**
   * Check if email is available
   */
  static async checkEmailAvailability(email: string, excludeUserId?: string): Promise<{
    available: boolean;
    suggestions?: string[];
  }> {
    const response = await DefaultService.checkEmailAvailability(email, excludeUserId);
    return response;
  }

  /**
   * Get user login history
   */
  static async getUserLoginHistory(id: string, limit = 20): Promise<{
    logins: Array<{
      id: string;
      timestamp: string;
      ipAddress: string;
      userAgent: string;
      location?: string;
      success: boolean;
    }>;
    total: number;
  }> {
    const response = await DefaultService.getUserLoginHistory(id, limit);
    return response;
  }

  /**
   * Reset user password (admin action)
   */
  static async resetUserPassword(id: string, sendEmail = true): Promise<{
    success: boolean;
    message: string;
    temporaryPassword?: string;
  }> {
    const response = await DefaultService.resetUserPassword(id, sendEmail);
    return response;
  }

  /**
   * Lock/unlock user account
   */
  static async toggleUserLock(id: string, locked: boolean, reason?: string): Promise<User> {
    const response = await DefaultService.toggleUserLock(id, locked, reason);
    return response;
  }
}

export default UserApiService; 