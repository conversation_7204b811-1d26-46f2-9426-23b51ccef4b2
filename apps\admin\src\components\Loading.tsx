/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import { Spin, Skeleton, Result } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

// Loading types
export type LoadingType = 'spinner' | 'skeleton' | 'page' | 'table' | 'card' | 'button'

interface LoadingProps {
  type?: LoadingType
  size?: 'small' | 'default' | 'large'
  tip?: string
  spinning?: boolean
  children?: React.ReactNode
  rows?: number
  avatar?: boolean
  title?: boolean
  paragraph?: boolean
}

/**
 * 全局加载组件
 */
export const Loading: React.FC<LoadingProps> = ({
  type = 'spinner',
  size = 'default',
  tip,
  spinning = true,
  children,
  rows = 3,
  avatar = false,
  title = true,
  paragraph = true,
}) => {
  const customIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />

  switch (type) {
    case 'skeleton':
      return (
        <Skeleton
          loading={spinning}
          avatar={avatar}
          title={title}
          paragraph={{ rows }}
          active
        >
          {children}
        </Skeleton>
      )

    case 'page':
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '60vh' 
        }}>
          <Spin 
            indicator={customIcon} 
            size={size} 
            tip={tip || '页面加载中...'}
          />
        </div>
      )

    case 'table':
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          padding: '40px' 
        }}>
          <Spin 
            size={size} 
            tip={tip || '数据加载中...'}
          />
        </div>
      )

    case 'card':
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          padding: '20px' 
        }}>
          <Spin 
            size={size} 
            tip={tip}
          />
        </div>
      )

    case 'button':
      return (
        <Spin 
          size="small" 
          spinning={spinning}
        >
          {children}
        </Spin>
      )

    case 'spinner':
    default:
      return (
        <Spin 
          spinning={spinning}
          size={size}
          tip={tip}
          indicator={size === 'large' ? customIcon : undefined}
        >
          {children}
        </Spin>
      )
  }
}

/**
 * 页面级加载组件
 */
export const PageLoading: React.FC<{ tip?: string }> = ({ tip }) => (
  <Loading type="page" tip={tip} />
)

/**
 * 表格加载组件
 */
export const TableLoading: React.FC<{ tip?: string }> = ({ tip }) => (
  <Loading type="table" tip={tip} />
)

/**
 * 卡片加载组件
 */
export const CardLoading: React.FC<{ tip?: string }> = ({ tip }) => (
  <Loading type="card" tip={tip} />
)

/**
 * 按钮加载组件
 */
export const ButtonLoading: React.FC<{ 
  loading?: boolean
  children: React.ReactNode
}> = ({ loading = false, children }) => (
  <Loading type="button" spinning={loading}>
    {children}
  </Loading>
)

/**
 * 骨架屏加载组件
 */
export const SkeletonLoading: React.FC<{
  loading?: boolean
  rows?: number
  avatar?: boolean
  title?: boolean
  children?: React.ReactNode
}> = ({ 
  loading = true, 
  rows = 3, 
  avatar = false, 
  title = true, 
  children 
}) => (
  <Loading 
    type="skeleton" 
    spinning={loading}
    rows={rows}
    avatar={avatar}
    title={title}
  >
    {children}
  </Loading>
)

/**
 * 空状态加载组件
 */
export const EmptyLoading: React.FC<{
  title?: string
  description?: string
}> = ({ 
  title = '暂无数据', 
  description = '当前没有任何数据' 
}) => (
  <Result
    status="404"
    title={title}
    subTitle={description}
  />
)

export default Loading 