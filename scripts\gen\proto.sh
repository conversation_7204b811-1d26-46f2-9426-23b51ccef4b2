#!/usr/bin/env bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB Protobuf 代码生成脚本
# 为所有目标平台生成 gRPC/Protobuf 客户端代码

set -euo pipefail

# 导入共享函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# shellcheck source=../lib/helpers.sh
source "$SCRIPT_DIR/../lib/helpers.sh"

# 脚本配置
readonly SCRIPT_NAME="proto-generator"
readonly PROTO_DIR="core/api/proto/v1"
readonly BUF_CONFIG="core/api/buf.yaml"
readonly BUF_GEN_CONFIG="core/api/buf.gen.yaml"

# 支持的目标语言
readonly SUPPORTED_TARGETS=(
    "go"
    "typescript" 
    "swift"
    "kotlin"
    "python"
    "all"
)

# 默认目标
DEFAULT_TARGET="all"

# 显示使用说明
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Generate Protobuf/gRPC code for specified target languages.

Options:
    -t, --target TARGET     Target language(s) to generate code for
                           Supported: ${SUPPORTED_TARGETS[*]}
                           Default: $DEFAULT_TARGET
    -c, --clean            Clean generated code before generation
    -v, --verbose          Enable verbose output
    -h, --help             Show this help message

Examples:
    $0                                    # Generate for all targets
    $0 --target go                        # Generate only Go code
    $0 --target go,typescript             # Generate Go and TypeScript code
    $0 --clean --target all               # Clean and generate all
    DEBUG=1 $0 --target swift             # Generate Swift with debug output

Environment Variables:
    DEBUG=1                Enable debug output
    DRY_RUN=1             Show commands without executing
    BUF_VERSION           Specific buf version to use
EOF
}

# 解析命令行参数
parse_args() {
    local target="$DEFAULT_TARGET"
    local clean=false
    local verbose=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--target)
                target="$2"
                shift 2
                ;;
            -c|--clean)
                clean=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 设置全局变量
    TARGET="$target"
    CLEAN="$clean"
    VERBOSE="$verbose"
    
    # 启用详细输出
    if [[ "$verbose" == "true" ]]; then
        export DEBUG=1
    fi
}

# 验证目标语言
validate_targets() {
    local targets
    IFS=',' read -ra targets <<< "$TARGET"
    
    for target in "${targets[@]}"; do
        target=$(echo "$target" | xargs)  # 去除空格
        if [[ ! " ${SUPPORTED_TARGETS[*]} " =~ " $target " ]]; then
            error "Unsupported target: $target"
            error "Supported targets: ${SUPPORTED_TARGETS[*]}"
            exit 1
        fi
    done
    
    debug "Validated targets: ${targets[*]}"
}

# 检查必需的工具
check_tools() {
    step "Checking required tools"
    
    local required_tools=("buf")
    
    # 检查 buf 工具
    if ! check_command "buf" "Install buf from https://buf.build/docs/installation"; then
        exit 1
    fi
    
    # 检查 buf 版本
    local buf_version
    buf_version=$(buf --version | head -n1 | awk '{print $2}' || echo "unknown")
    info "Using buf version: $buf_version"
    
    # 如果指定了特定版本，进行验证
    if [[ -n "${BUF_VERSION:-}" ]]; then
        if [[ "$buf_version" != "$BUF_VERSION" ]]; then
            warn "Expected buf version $BUF_VERSION, but found $buf_version"
        fi
    fi
    
    success "All required tools are available"
}

# 检查项目结构
check_project_structure() {
    step "Checking project structure"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 检查必需的目录和文件
    local required_paths=(
        "$project_root/$PROTO_DIR"
        "$project_root/$BUF_CONFIG"
        "$project_root/$BUF_GEN_CONFIG"
    )
    
    for path in "${required_paths[@]}"; do
        if [[ -d "$path" ]]; then
            debug "Found directory: $path"
        elif [[ -f "$path" ]]; then
            debug "Found file: $path"
        else
            error "Required path not found: $path"
            exit 1
        fi
    done
    
    success "Project structure is valid"
}

# 清理生成的代码
clean_generated_code() {
    if [[ "$CLEAN" != "true" ]]; then
        return 0
    fi
    
    step "Cleaning generated code"
    
    local project_root
    project_root="$(get_project_root)"
    
    # Go 生成的代码目录
    local go_gen_dirs=(
        "services/*/gen"
        "pkg/*/gen"
    )
    
    # TypeScript 生成的代码目录
    local ts_gen_dirs=(
        "apps/web/src/lib/gen"
    )
    
    # Swift 生成的代码目录
    local swift_gen_dirs=(
        "apps/apple/Shared/Generated"
    )
    
    # Kotlin 生成的代码目录
    local kotlin_gen_dirs=(
        "apps/android/app/build/generated/source/proto"
    )
    
    # 清理所有生成目录
    local all_dirs=("${go_gen_dirs[@]}" "${ts_gen_dirs[@]}" "${swift_gen_dirs[@]}" "${kotlin_gen_dirs[@]}")
    
    for pattern in "${all_dirs[@]}"; do
        # 使用 find 来处理通配符
        find "$project_root" -path "*/$pattern" -type d 2>/dev/null | while read -r dir; do
            if [[ -d "$dir" ]]; then
                info "Cleaning: $dir"
                safe_remove "$dir"
            fi
        done
    done
    
    success "Cleaned generated code"
}

# 为指定目标生成代码
generate_for_targets() {
    step "Generating Protobuf code"
    
    local project_root
    project_root="$(get_project_root)"
    
    local targets
    IFS=',' read -ra targets <<< "$TARGET"
    
    # 如果目标包含 "all"，替换为所有支持的目标（除了 "all" 本身）
    if [[ " ${targets[*]} " =~ " all " ]]; then
        targets=("go" "typescript" "swift" "kotlin" "python")
    fi
    
    # 切换到项目根目录
    cd "$project_root"
    
    # 检查 buf 配置文件
    if [[ ! -f "$BUF_GEN_CONFIG" ]]; then
        error "buf.gen.yaml not found at $BUF_GEN_CONFIG"
        error "Please ensure the buf configuration is properly set up"
        exit 1
    fi
    
    # 显示将要生成的目标
    info "Generating code for targets: ${targets[*]}"
    
    # 运行 buf generate
    info "Running buf generate..."
    
    local buf_cmd="buf generate $PROTO_DIR"
    
    # 添加详细输出选项
    if [[ "$VERBOSE" == "true" ]]; then
        buf_cmd="$buf_cmd --verbose"
    fi
    
    if ! run_cmd "$buf_cmd"; then
        error "buf generate failed"
        error "Please check your .proto files and buf configuration"
        exit 1
    fi
    
    success "Code generation completed successfully"
}

# 验证生成的代码
validate_generated_code() {
    step "Validating generated code"
    
    local project_root
    project_root="$(get_project_root)"
    
    local validation_failed=false
    
    # 检查 Go 代码
    if [[ "$TARGET" == "all" ]] || [[ "$TARGET" =~ "go" ]]; then
        info "Validating Go generated code..."
        
        # 查找生成的 Go 文件
        local go_files
        go_files=$(find "$project_root" -name "*.pb.go" -o -name "*_grpc.pb.go" 2>/dev/null | head -5)
        
        if [[ -n "$go_files" ]]; then
            debug "Found Go generated files:"
            echo "$go_files" | while read -r file; do
                debug "  $file"
            done
            
            # 尝试编译检查语法
            if command -v go &> /dev/null; then
                if ! run_in_dir "$project_root" "go mod tidy" 2>/dev/null; then
                    warn "Failed to run 'go mod tidy', but continuing..."
                fi
            fi
        else
            warn "No Go generated files found"
        fi
    fi
    
    # 检查 TypeScript 代码
    if [[ "$TARGET" == "all" ]] || [[ "$TARGET" =~ "typescript" ]]; then
        info "Validating TypeScript generated code..."
        
        local ts_gen_dir="$project_root/apps/web/src/lib/gen"
        if [[ -d "$ts_gen_dir" ]]; then
            local ts_files
            ts_files=$(find "$ts_gen_dir" -name "*.ts" 2>/dev/null | wc -l)
            info "Found $ts_files TypeScript generated files"
        else
            warn "TypeScript generation directory not found: $ts_gen_dir"
        fi
    fi
    
    if [[ "$validation_failed" == "true" ]]; then
        error "Code validation failed"
        exit 1
    fi
    
    success "Generated code validation completed"
}

# 显示生成统计
show_generation_stats() {
    step "Generation Statistics"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 统计各种类型的生成文件
    local go_files ts_files swift_files kotlin_files
    
    go_files=$(find "$project_root" -name "*.pb.go" -o -name "*_grpc.pb.go" 2>/dev/null | wc -l)
    ts_files=$(find "$project_root" -path "*/gen/*.ts" 2>/dev/null | wc -l)
    swift_files=$(find "$project_root" -path "*/Generated/*.swift" 2>/dev/null | wc -l)
    kotlin_files=$(find "$project_root" -path "*/proto/*.kt" 2>/dev/null | wc -l)
    
    info "Generated files summary:"
    info "  Go files:         $go_files"
    info "  TypeScript files: $ts_files"
    info "  Swift files:      $swift_files"
    info "  Kotlin files:     $kotlin_files"
    
    local total_files=$((go_files + ts_files + swift_files + kotlin_files))
    info "  Total files:      $total_files"
}

# 主函数
main() {
    # 初始化脚本
    init_script "$SCRIPT_NAME"
    
    # 解析命令行参数
    parse_args "$@"
    
    # 验证参数
    validate_targets
    
    # 检查工具和项目结构
    check_tools
    check_project_structure
    
    # 清理旧的生成代码
    clean_generated_code
    
    # 生成代码
    generate_for_targets
    
    # 验证生成的代码
    validate_generated_code
    
    # 显示统计信息
    show_generation_stats
    
    # 完成
    finish_script "$SCRIPT_NAME"
}

# 只有在直接执行时才运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 