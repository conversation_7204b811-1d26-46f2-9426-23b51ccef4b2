好的，遵照您的指示。我将为您生成一份专门针对 **`digital-twin-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`digital-twin-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**灵活的Avatar资产与库存系统、Persona的演进与AI集成、以及为`metaverse-engine-service`提供高性能渲染数据**等核心挑战，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `digital-twin-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `digital-twin-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 为灵活数据结构优化的数据模型

## 1. 概述

`digital-twin-service` 是用户在CINA.CLUB数字世界中的**身份可视化与个性化层**。其核心挑战在于：
1.  **数据的灵活性与结构化**: Avatar和Persona的数据结构复杂且可能频繁演进，需要一个既灵活又结构化的存储方案。
2.  **高性能读取**: `metaverse-engine-service`需要以极低的延迟获取渲染Avatar所需的核心数据。
3.  **复杂的资产管理**: 需要管理平台级的资产目录、用户的个人库存，并处理与计费、游戏化系统的解锁联动。
4.  **与AI的深度协同**: Persona数据需要能被AI服务查询以提供个性化交互，也需要能被AI服务更新以实现演进。
5.  **解耦**: 必须将自身的元数据管理与3D文件的实际存储（`file-storage-service`）和实时状态同步（`metaverse-engine-service`）彻底解耦。

本架构设计通过采用**整洁架构**，并选择**对半结构化数据友好的数据库（如MongoDB或PostgreSQL+JSONB）**，同时通过**事件驱动**和**内部API**与平台其他服务协同，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (数据流与服务协同)

```mermaid
graph TD
    subgraph "客户端与元宇宙"
        ClientApp
        MetaverseEngine as metaverse-engine
    end

    subgraph "DigitalTwinService"
        A[API Layer (gRPC/REST)<br/><em>adapter/transport</em>]
        B[Application Service<br/><em>(application/service)</em>]
        C[Domain Logic & Models<br/><em>(domain/aggregate, domain/model)</em>]
        D[Repository<br/><em>(adapter/repository)</em>]
        E[Cache<br/><em>adapter/cache</em>]
    end

    subgraph "平台协同服务"
        F[user-core-service]
        G[ai-assistant-service]
        H[billing-service]
        I[gamification-service]
        J[file-storage-service]
    end
    
    ClientApp -- "1. 定制Avatar/Persona" --> A
    A -- "调用" --> B
    B -- "使用" --> C
    C -- "持久化" --> D

    MetaverseEngine -- "2. [高频] 获取渲染数据" --> A
    A -- "查询" --> B
    B -- "优先查缓存" --> E
    E -- "未命中则查DB" --> D

    B -- "3. 更新Persona" --> G
    G -- "返回推断的特质" --> B
    
    subgraph "资产解锁流程"
        H -- "4a. 购买成功事件" --> Kafka[(Kafka)]
        I -- "4b. 达成成就事件" --> Kafka
        Kafka -- "消费事件" --> B
        B -- "5. 向用户库存添加资产" --> D
    end
    
    C -- "需要3D模型文件" --> J
```

### 2.2 最终目录结构 (`services/digital-twin-service/`)

```
digital-twin-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_cache.go  # 缓存渲染数据
│   │   ├── client/
│   │   │   └── ai_assistant_client.go
│   │   ├── event/
│   │   │   └── asset_unlock_consumer.go # 消费来自billing/gamification的事件
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── twin_service.go # 核心应用服务实现
│   └── domain/
│       ├── aggregate/
│       │   └── avatar_aggregate.go # 封装Avatar外观和装备的业务规则
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── persona_analyzer.go # ✨ Persona分析与演进领域服务 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Digital Self)

*   `domain/model/`: 使用`/core/models`中与数字孪生相关的`struct`，如`Avatar`, `Persona`, `Asset`。
*   **`domain/aggregate/avatar_aggregate.go`**:
    *   **`Avatar`聚合根**: 封装了用户的`Avatar`实体，以及其当前装备的资产列表`EquippedAssets` (`map[SlotType]AssetID`)。
    *   **`EquipAsset(asset Asset)` method**:
        1.  **规则校验**: 检查传入的`Asset`的`slot`是否合法。
        2.  **规则校验**: (可选) 检查资产之间是否有互斥关系（如不能同时戴头盔和帽子）。
        3.  更新聚合根内部的`EquippedAssets` map。
    *   **`ApplyBlendShapes(shapes map[string]float32)` method**:
        1.  校验`shapes`中的key是否都是合法的Blend Shape名称。
        2.  校验每个shape的值是否在0-1之间。
        3.  更新聚合根的`BlendShapes`字段。
*   **`domain/service/persona_analyzer.go`**: **这是Persona演进的核心**。
    *   **`PersonaAnalyzer` service**: 一个无状态的领域服务。
    *   **`UpdateFromInteraction(currentPersona, interactionContext)` method**:
        *   接收当前的`Persona`对象和一次交互的上下文（如与AI的对话摘要）。
        *   **调用`ai-assistant-service`**: 将这些信息发送给AI，请求其分析并返回更新后的特质。例如，`prompt`: "根据以下对话，更新此用户的沟通风格和兴趣标签：... "。
        *   返回一个新的、合并了AI分析结果的`Persona`对象。

### 3.2 `application/` - 应用层 (The Use Cases)

*   `application/port/`: 定义`Repository`, `TwinService`等接口。
*   **`application/service/twin_service.go`**: 实现`TwinService`接口，是所有业务流程的编排者。
    *   **`GetAvatarRenderData(ctx, userID)`**:
        *   **性能关键路径**: 这是被`metaverse-engine`高频调用的接口。
        *   **步骤1**: **优先从缓存(`port.Cache`)中获取**。缓存的Key可以是`render-data:{userID}`。
        *   **步骤2 (缓存未命中)**:
            a. 从仓储中加载`Avatar`聚合根。
            b. 构造一个包含`base_model_key`, `blend_shapes`, `equipped_assets`列表的精简数据结构。
            c. 将结果写入缓存，并设置一个合理的TTL或使用事件驱动的失效策略。
        *   **步骤3**: 返回渲染数据。
    *   **`UpdateAvatarAppearance(ctx, userID, updates)`**:
        1.  从仓储加载`Avatar`聚合根。
        2.  调用`avatar.EquipAsset(...)`或`avatar.ApplyBlendShapes(...)`等领域方法。
        3.  将变更后的聚合根交由仓储持久化。
        4.  **主动失效缓存**: 调用`cache.InvalidateRenderData(userID)`。
    *   **`EvolvePersonaFromAI(ctx, userID, interactionContext)`**:
        1.  从仓储加载`Persona`。
        2.  调用`domain.PersonaAnalyzer.UpdateFromInteraction()`，获取新的`Persona`对象。
        3.  将新的`Persona`对象持久化。
    *   **`HandleAssetUnlockEvent(ctx, userID, assetID)` (由事件消费者调用)**:
        1.  调用`repository.AddAssetToInventory(userID, assetID)`，将新资产添加到用户的库存中。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库选型**: **MongoDB** 或 **PostgreSQL + JSONB**。
        *   **理由**: `Avatar`的`blend_shapes`和`Persona`的`persona_data`都是灵活的、可能频繁变更的半结构化数据，非常适合用文档型数据库或JSONB字段来存储。
    *   `repo.go`: 实现`port.Repository`接口。
*   **`adapter/cache/`**:
    *   `redis_cache.go`: 实现`port.Cache`接口，使用Redis来缓存`GetAvatarRenderData`的结果。
*   **`adapter/client/`**:
    *   `ai_assistant_client.go`: 封装对`ai-assistant-service`的gRPC调用，用于Persona分析。
*   **`adapter/event/`**:
    *   `asset_unlock_consumer.go`: 消费来自`billing-service`的`PremiumAssetPurchasedEvent`和来自`gamification-service`的`BadgeAwardedEvent`，并调用`application.TwinService.HandleAssetUnlockEvent`。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现gRPC接口，将请求路由到`application.TwinService`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`digital-twin-service`：
1.  **灵活的数据模型**: 采用对JSON友好的数据库（MongoDB/PostgreSQL+JSONB），以适应Avatar和Persona数据结构的未来演进。
2.  **性能优化的读路径**: 为被高频调用的“获取渲染数据”接口设计了专门的缓存层，确保了元宇宙场景的低延迟加载。
3.  **领域驱动的业务规则**: 将Avatar的外观变更和装备逻辑封装在`Avatar`聚合根中，保证了操作的有效性和一致性。
4.  **AI驱动的Persona演进**: 通过`PersonaAnalyzer`领域服务，清晰地定义了Persona如何通过与AI服务的交互而“成长”，实现了数字孪生的个性化演进。
5.  **事件驱动的资产管理**: 通过消费来自计费和游戏化服务的事件，实现了资产库存的异步、解耦的更新，符合微服务架构的最佳实践。

这种架构确保了`digital-twin-service`能够为平台提供一个功能强大、可扩展、且富有生命力的数字身份中心，为所有沉浸式和个性化AI体验奠定了坚实的数据基础。