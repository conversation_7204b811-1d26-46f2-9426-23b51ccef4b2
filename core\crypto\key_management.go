package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"sync"
	"time"

	"golang.org/x/crypto/argon2"
)

// KeyManagementSystem provides advanced key management capabilities
type KeyManagementSystem struct {
	mu               sync.RWMutex
	keyStore         map[string]*ManagedKey
	masterBackupKey  []byte
	rotationInterval time.Duration
}

// ManagedKey represents a cryptographic key with metadata
type ManagedKey struct {
	ID            string
	Key           []byte
	CreatedAt     time.Time
	LastRotatedAt time.Time
	UsageCount    int
	ExpiresAt     time.Time
	IsCompromised bool
}

// KeyBackup represents an encrypted key backup
type KeyBackup struct {
	EncryptedKey []byte
	Metadata     map[string]string
}

// NewKeyManagementSystem creates a new key management system
func NewKeyManagementSystem(rotationInterval time.Duration) (*KeyManagementSystem, error) {
	// Generate a master backup key
	backupKey, err := generateSecureKey(32)
	if err != nil {
		return nil, fmt.Errorf("failed to generate backup key: %w", err)
	}

	return &KeyManagementSystem{
		keyStore:         make(map[string]*ManagedKey),
		masterBackupKey:  backupKey,
		rotationInterval: rotationInterval,
	}, nil
}

// GenerateKey creates a new managed cryptographic key
func (kms *KeyManagementSystem) GenerateKey(keyPurpose string) (*ManagedKey, error) {
	kms.mu.Lock()
	defer kms.mu.Unlock()

	// Generate cryptographically secure key
	keyBytes, err := generateSecureKey(32)
	if err != nil {
		return nil, fmt.Errorf("key generation failed: %w", err)
	}

	// Create managed key
	now := time.Now()
	managedKey := &ManagedKey{
		ID:            generateKeyID(keyPurpose),
		Key:           keyBytes,
		CreatedAt:     now,
		LastRotatedAt: now,
		ExpiresAt:     now.Add(365 * 24 * time.Hour), // 1-year expiration
	}

	// Store the key
	kms.keyStore[managedKey.ID] = managedKey

	return managedKey, nil
}

// RotateKey performs key rotation for a specific key
func (kms *KeyManagementSystem) RotateKey(keyID string) (*ManagedKey, error) {
	kms.mu.Lock()
	defer kms.mu.Unlock()

	// Retrieve existing key
	existingKey, exists := kms.keyStore[keyID]
	if !exists {
		return nil, fmt.Errorf("key not found: %s", keyID)
	}

	// Check if rotation is necessary
	if time.Since(existingKey.LastRotatedAt) < kms.rotationInterval {
		return nil, errors.New("key rotation interval not met")
	}

	// Generate new key
	newKeyBytes, err := generateSecureKey(32)
	if err != nil {
		return nil, fmt.Errorf("key rotation failed: %w", err)
	}

	// Create rotated key
	now := time.Now()
	rotatedKey := &ManagedKey{
		ID:            keyID, // Maintain same ID
		Key:           newKeyBytes,
		CreatedAt:     existingKey.CreatedAt,
		LastRotatedAt: now,
		ExpiresAt:     now.Add(365 * 24 * time.Hour),
	}

	// Backup the old key before overwriting
	if err := kms.performKeyBackup(existingKey); err != nil {
		return nil, fmt.Errorf("key backup failed: %w", err)
	}

	// Store the rotated key
	kms.keyStore[keyID] = rotatedKey

	return rotatedKey, nil
}

// performKeyBackup creates an encrypted backup of a key
func (kms *KeyManagementSystem) performKeyBackup(key *ManagedKey) error {
	// Encrypt the key using the backup key
	encryptedKey, err := encryptWithBackupKey(kms.masterBackupKey, key.Key)
	if err != nil {
		return fmt.Errorf("key encryption failed: %w", err)
	}

	// Store backup metadata
	backup := KeyBackup{
		EncryptedKey: encryptedKey,
		Metadata: map[string]string{
			"key_id":      key.ID,
			"created_at":  key.CreatedAt.Format(time.RFC3339),
			"rotated_at":  key.LastRotatedAt.Format(time.RFC3339),
			"backup_time": time.Now().Format(time.RFC3339),
		},
	}

	// In a real-world scenario, you would persist this backup
	// to a secure, encrypted storage system
	_ = backup

	return nil
}

// encryptWithBackupKey encrypts data using the backup key
func encryptWithBackupKey(backupKey, data []byte) ([]byte, error) {
	block, err := aes.NewCipher(backupKey)
	if err != nil {
		return nil, fmt.Errorf("cipher creation failed: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("GCM creation failed: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("nonce generation failed: %w", err)
	}

	return gcm.Seal(nonce, nonce, data, nil), nil
}

// generateSecureKey creates a cryptographically secure random key
func generateSecureKey(length int) ([]byte, error) {
	key := make([]byte, length)
	if _, err := rand.Read(key); err != nil {
		return nil, err
	}
	return key, nil
}

// generateKeyID creates a unique identifier for a key
func generateKeyID(purpose string) string {
	// Create a hash combining purpose and timestamp
	hash := sha256.New()
	hash.Write([]byte(purpose))
	hash.Write([]byte(time.Now().UTC().String()))

	// Generate a base64 encoded ID
	return base64.URLEncoding.EncodeToString(hash.Sum(nil))[:16]
}

// SecureKeyExchange represents a secure key exchange mechanism
type SecureKeyExchange struct {
	publicKey  []byte
	privateKey []byte
}

// NewSecureKeyExchange initializes a new key exchange
func NewSecureKeyExchange() (*SecureKeyExchange, error) {
	// In a real-world scenario, this would use a proper key exchange algorithm like ECDH
	publicKey, err := generateSecureKey(32)
	if err != nil {
		return nil, err
	}

	privateKey, err := generateSecureKey(32)
	if err != nil {
		return nil, err
	}

	return &SecureKeyExchange{
		publicKey:  publicKey,
		privateKey: privateKey,
	}, nil
}

// DeriveSharedSecret demonstrates a simplified shared secret derivation
func (skx *SecureKeyExchange) DeriveSharedSecret(otherPublicKey []byte) ([]byte, error) {
	// Simplified key derivation - in practice, use a proper key exchange algorithm
	sharedSecret := make([]byte, 32)
	for i := range sharedSecret {
		sharedSecret[i] = skx.privateKey[i] ^ otherPublicKey[i]
	}

	// Apply key derivation function
	return argon2.IDKey(sharedSecret, otherPublicKey, 1, 64*1024, 4, 32), nil
}

// Example usage
func ExampleKeyManagement() {
	// Create key management system
	kms, err := NewKeyManagementSystem(30 * 24 * time.Hour) // Rotate every 30 days
	if err != nil {
		panic(err)
	}

	// Generate a new key
	key, err := kms.GenerateKey("user_encryption")
	if err != nil {
		panic(err)
	}

	// Rotate the key
	_, err = kms.RotateKey(key.ID)
	if err != nil {
		panic(err)
	}

	// Demonstrate secure key exchange
	alice, err := NewSecureKeyExchange()
	if err != nil {
		panic(err)
	}

	bob, err := NewSecureKeyExchange()
	if err != nil {
		panic(err)
	}

	// Derive shared secret
	aliceSharedSecret, err := alice.DeriveSharedSecret(bob.publicKey)
	if err != nil {
		panic(err)
	}

	bobSharedSecret, err := bob.DeriveSharedSecret(alice.publicKey)
	if err != nil {
		panic(err)
	}

	// Verify shared secrets match
	if !compareSecureBytes(aliceSharedSecret, bobSharedSecret) {
		panic("shared secrets do not match")
	}
}

// compareSecureBytes performs a constant-time comparison of byte slices
func compareSecureBytes(a, b []byte) bool {
	if len(a) != len(b) {
		return false
	}

	var result byte
	for i := 0; i < len(a); i++ {
		result |= a[i] ^ b[i]
	}
	return result == 0
}
