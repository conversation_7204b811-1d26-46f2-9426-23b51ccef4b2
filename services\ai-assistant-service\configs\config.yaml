# AI Assistant Service Configuration

server:
  port: 8080
  host: "0.0.0.0"
  mode: "release"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# 数据库配置
database:
  redis:
    host: "localhost"
    port: 6379
    password: ""
    db: 0
    pool_size: 10
    min_idle_conns: 5
    dial_timeout: 5s
    read_timeout: 3s
    write_timeout: 3s
    pool_timeout: 4s
    idle_check_frequency: 60s
    idle_timeout: 300s
    max_conn_age: 0s

# LLM配置
llm:
  providers:
    openai:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      models:
        - "gpt-3.5-turbo"
        - "gpt-4"
        - "gpt-4-turbo"
      default_model: "gpt-3.5-turbo"
      timeout: 30s
      max_retries: 3
    
    claude:
      api_key: "${CLAUDE_API_KEY}"
      base_url: "https://api.anthropic.com"
      models:
        - "claude-3-sonnet"
        - "claude-3-opus"
        - "claude-3-haiku"
      default_model: "claude-3-sonnet"
      timeout: 30s
      max_retries: 3

# 工具配置
tools:
  enabled_categories:
    - "llm"
    - "search"
    - "schedule"
    - "memory"
    - "knowledge"
    - "user"
  
  execution:
    timeout: 60s
    max_concurrent: 10
    retry_attempts: 2
  
  validation:
    strict_mode: true
    allow_unknown_fields: false

# 会话管理配置
session:
  default_ttl: 24h
  max_ttl: 168h  # 7 days
  cleanup_interval: 1h
  max_message_history: 100
  compression_enabled: true

# 工作流配置
workflow:
  max_steps: 20
  step_timeout: 30s
  parallel_execution: true
  max_parallel_steps: 5
  retry_failed_steps: true
  max_retries: 2

# 意图识别配置
intent:
  confidence_threshold: 0.6
  fallback_to_general: true
  context_window: 5  # 考虑最近5条消息的上下文
  
  # 实体提取配置
  entity_extraction:
    enabled: true
    max_entities: 10
    confidence_threshold: 0.7

# 记忆管理配置
memory:
  enabled: true
  retention_days: 90
  max_memories_per_user: 1000
  importance_threshold: 5
  auto_cleanup: true

# 知识库配置
knowledge:
  enabled: true
  max_results: 20
  relevance_threshold: 0.7
  cache_ttl: 1h

# 外部服务配置
external_services:
  search_service:
    host: "search-service"
    port: 9090
    timeout: 10s
    max_retries: 3
  
  schedule_service:
    host: "schedule-service"
    port: 9091
    timeout: 10s
    max_retries: 3
  
  memory_service:
    host: "memory-service"
    port: 9092
    timeout: 10s
    max_retries: 3
  
  knowledge_service:
    host: "knowledge-service"
    port: 9093
    timeout: 10s
    max_retries: 3
  
  user_service:
    host: "user-service"
    port: 9094
    timeout: 10s
    max_retries: 3

# 消息队列配置
messaging:
  kafka:
    brokers:
      - "localhost:9092"
    consumer_group: "ai-assistant-service"
    topics:
      events: "platform.events"
      responses: "ai.assistant.responses"
    batch_size: 100
    timeout: 10s

# 监控配置
monitoring:
  metrics:
    enabled: true
    port: 9090
    path: "/metrics"
  
  tracing:
    enabled: true
    service_name: "ai-assistant-service"
    jaeger_endpoint: "http://localhost:14268/api/traces"
    sample_rate: 0.1
  
  health_check:
    enabled: true
    path: "/health"
    interval: 30s

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file:
    enabled: false
    path: "/var/log/ai-assistant-service.log"
    max_size: 100
    max_backups: 5
    max_age: 30
    compress: true

# 安全配置
security:
  cors:
    enabled: true
    allowed_origins:
      - "*"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:
      - "Content-Type"
      - "Authorization"
      - "X-Requested-With"
    max_age: 86400
  
  rate_limiting:
    enabled: true
    requests_per_minute: 100
    burst_size: 20
  
  authentication:
    enabled: true
    jwt_secret: "${JWT_SECRET}"
    token_expiry: 24h

# 性能配置
performance:
  cache:
    enabled: true
    default_ttl: 300s
    max_size: 1000
  
  connection_pool:
    max_idle_conns: 100
    max_open_conns: 200
    conn_max_lifetime: 1h
  
  request_timeout: 30s
  shutdown_timeout: 30s

# 开发配置
development:
  debug: false
  mock_external_services: false
  enable_profiling: false
  profiling_port: 6060 