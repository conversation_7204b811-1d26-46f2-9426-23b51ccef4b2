好的，遵照您的指示。我将为您生成一份专门针对 **`search-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`search-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**混合搜索(Hybrid Search)的查询构建与结果融合、与AI服务的协同进行查询理解、个性化重排的实现，以及作为一个高性能在线查询引擎的技术细节**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `search-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `search-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 多阶段查询与排序管道 (Multi-Stage Query & Ranking Pipeline)

## 1. 概述

`search-service` 是CINA.CLUB平台的信息**发现引擎**和**流量分发中枢**。它是一个**高性能、无状态的在线查询服务**。其架构设计的核心目标是：
1.  **查询的智能化**: 需要能理解用户的自然语言查询，进行意图识别和语义扩展，而不仅仅是关键词匹配。
2.  **结果的精准性与相关性**: 必须结合**关键词(Keyword)、向量(Vector)、地理空间(Geo)**等多种信号，进行混合搜索，并通过科学的融合算法对结果进行排序。
3.  **高度个性化**: 搜索结果需要能反映用户的个人偏好和历史行为，实现“千人千面”的搜索体验。
4.  **极低的延迟**: 搜索是一个强交互场景，整个端到端的响应时间必须被严格控制在数百毫秒以内。
5.  **可扩展性**: 架构应能轻松地集成新的搜索信号、过滤条件和排序策略。

本架构设计通过构建一个**多阶段的、可插拔的查询与排序管道**，并与AI和用户画像服务深度协同，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (查询处理管道)

```mermaid
graph TD
    subgraph "客户端/调用方"
        A[ClientApp / ai-assistant]
    end

    subgraph "SearchService"
        style SearchService fill:#e0f7fa
        B[API Layer (gRPC/REST)<br/><em>adapter/transport</em>]
        C[SearchApplicationService<br/><em>application/service</em>]
        
        subgraph "Query Processing Pipeline"
            P1[Stage 1: Query Understanding]
            P2[Stage 2: Hybrid Query Building]
            P3[Stage 3: Result Fusion & Initial Ranking]
            P4[Stage 4: Personalization & Re-ranking]
        end

        D[Domain Services<br/><em>(e.g., QueryParser, ResultFuser)</em>]
        E[Downstream Clients<br/><em>(adapter/client)</em>]
    end
    
    subgraph "依赖服务与数据源"
        style "依赖服务与数据源" fill:#f3e5f5
        S1[ai-assistant-service (NLU)]
        S2[embedding-service]
        S3[user-core-service (Profile)]
        S4[Elasticsearch/OpenSearch]
    end

    A -- "1. SearchRequest" --> B
    B -- "调用" --> C
    C -- "2. Start Pipeline" --> P1
    
    P1 -- "a. Call NLU" --> E
    E -- "gRPC" --> S1
    P1 -- "b. Call Embedding" --> E
    E -- "gRPC" --> S2
    P1 -- "Augmented Query" --> P2
    
    P2 -- "3. Build ES/OS DSL Query" --> P2
    P2 -- "4. Execute Query" --> E
    E -- "HTTP" --> S4
    S4 -- "Raw Results (Keyword & Vector)" --> E
    
    P2 -- "Raw Results" --> P3
    P3 -- "5. Fuse with RRF" --> D
    D -- "Initial Ranked List" --> P3
    
    P3 -- "Initial List" --> P4
    P4 -- "6. Fetch User Profile" --> E
    E -- "gRPC" --> S3
    P4 -- "7. Apply Personalization" --> D
    
    P4 -- "Final List" --> C
    C -- "8. Return Response" --> B
```

### 2.2 最终目录结构 (`services/search-service/`)

```
search-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── ai_assistant_client.go
│   │   │   └── embedding_client.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       └── es_repo.go      # ✨ 封装对ES/OS的查询执行 ✨
│   ├── application/
│   │   ├── port/
│   │   │   └── ...
│   │   └── service/
│   │       └── search_service.go # 核心应用服务, 编排查询管道
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── query/                # ✨ 查询处理的核心领域逻辑 ✨
│           ├── builder.go        # 混合查询DSL构建器
│           ├── parser.go         # 查询理解与增强
│           └── ranker/
│               ├── fuser.go      # 结果融合 (RRF)
│               └── personalizer.go # 个性化重排
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/query/` - 领域层 (The Science of Search)

这是封装所有核心搜索算法和逻辑的地方。

*   **`parser.go`**: **查询理解**
    *   **`QueryParser`**: 一个领域服务，注入了`ai_assistant_client`。
    *   **`Parse(ctx, rawQuery)`**:
        1.  执行基础的清理：小写化、去除停用词。
        2.  调用外部词典或服务进行拼写检查和同义词扩展。
        3.  **调用`ai-assistant-service`的NLU接口**，从查询中提取出结构化的**意图(Intent)**和**槽位/实体(Slots/Entities)**。例如，`"北京的宠物咖啡馆"` -> `Intent: find_poi`, `Slots: {category: "咖啡馆", location: "北京", tag: "宠物"}`。
        4.  返回一个包含原始查询、增强后关键词、意图和实体的`ParsedQuery`对象。
*   **`builder.go`**: **混合查询构建器**
    *   **`QueryBuilder`**: 一个领域服务。
    *   **`Build(parsedQuery, queryVector)`**:
        *   接收`ParsedQuery`和查询向量。
        *   **构建一个复杂的ES/OS JSON查询体**。这是一个典型的**bool query**：
            *   `filter`部分：根据`ParsedQuery`中的实体，构建精确匹配的`term`或`geo_distance`过滤。
            *   `should`部分（用于打分）：
                *   一个`match`子句，用于关键词匹配（BM25得分）。
                *   一个`knn`子句，用于向量相似性搜索。
        *   （高级）使用`rank`部分的**Reciprocal Rank Fusion (RRF)**来让ES/OS在数据库层面直接进行结果融合，而不是在应用层做。
*   **`ranker/fuser.go`**: **结果融合器**
    *   **`ResultFuser`**: 如果RRF在应用层做，则由本服务实现。
    *   `Fuse(keywordResults, vectorResults)`: 接收两路带分数的排序列表，应用RRF算法，返回一个统一的排序列表。
*   **`ranker/personalizer.go`**: **个性化重排器**
    *   **`Personalizer`**: 注入了`user-core-client`和（未来）特征存储客户端。
    *   **`ReRank(ctx, initialRankedList, userID)`**:
        1.  获取用户画像（如偏好的分类、价格敏感度）。
        2.  遍历`initialRankedList`。
        3.  对于每个item，根据其特征和用户画像的匹配程度，计算一个**个性化加权分(boost score)**。
        4.  `final_score = initial_score * (1 + boost_score)`。
        5.  按`final_score`重新排序。

### 3.2 `application/` - 应用层 (The Query Pipeline Orchestrator)

*   **`application/service/search_service.go`**: **这是编排整个查询管道的核心**。
    *   **`SearchApplicationService`**: 注入所有`domain`服务和`adapter`。
    *   **`Search(ctx, request)`**:
        1.  **阶段1: 查询理解**: `parsedQuery, err := s.parser.Parse(ctx, request.Query)`。
        2.  **阶段2: 向量化**: `queryVector, err := s.embeddingClient.Embed(request.Query)`。
        3.  **阶段3: 查询构建与执行**:
            a. `esQuery, err := s.builder.Build(parsedQuery, queryVector)`。
            b. `esResponse, err := s.esRepo.Execute(ctx, esQuery)`。
        4.  **阶段4: 结果处理**:
            a. 从`esResponse`中解析出关键词和向量两路的结果。
            b. `initialList, err := s.fuser.Fuse(...)`。
            c. `finalList, err := s.personalizer.ReRank(ctx, initialList, request.UserID)`。
        5.  **阶段5: 格式化与返回**:
            a. 提取分面(aggregations)数据。
            b. 为结果生成高亮片段。
            c. 将最终结果转换为API响应DTO。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/es_repo.go`**:
    *   **`ESRepository`**: 封装对Elasticsearch/OpenSearch的所有HTTP API调用。
    *   使用官方的Go客户端。
    *   **核心职责**: 接收`QueryBuilder`生成的JSON查询体，并将其发送给ES/OS执行。
*   **`adapter/client/`**: 封装对`ai-assistant-service`(NLU)和`embedding-service`的gRPC调用。
*   **`adapter/grpc/handler.go`**: 实现`search-service.proto`中定义的gRPC服务。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`search-service`：
1.  **多阶段查询管道**: 将复杂的搜索过程拆分为**查询理解 -> 查询构建 -> 结果融合 -> 个性化重排**等多个独立的、可测试的阶段。这种管道化的设计使得添加新的信号或排序策略（如增加粗排阶段）变得非常容易。
2.  **AI驱动的查询理解**: 通过与`ai-assistant-service`的NLU能力协同，实现了从简单的“关键词匹配”到“语义意图理解”的智能化升级。
3.  **混合搜索与融合**: 明确了结合**关键词、向量、地理**等多种信号进行混合搜索，并通过**RRF**进行科学融合，这是现代搜索引擎保证高相关性和高召回率的最佳实践。
4.  **个性化为核心**: 将个性化重排作为管道的最后一环，确保了搜索结果不仅相关，而且是“为我量身定制的”。
5.  **职责清晰的领域逻辑**: 将复杂的算法逻辑（如Query Building, Fusing, Personalizing）都封装在`domain/query`下的各个领域服务中，使得应用层的编排逻辑保持简洁。

这种架构确保了`search-service`能够作为一个**智能、精准、高度个性化且性能卓越**的信息发现引擎，为CINA.CLUB平台的所有用户和内部系统提供世界一流的搜索体验。