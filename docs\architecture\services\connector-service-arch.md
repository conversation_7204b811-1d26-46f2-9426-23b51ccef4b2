好的，遵照您的指示。我将根据我们刚才讨论的、版本2.0的`connector-service` SRS，为您生成一份专门针对 **`connector-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将详细阐述其作为**平台能力适配层和外部连接中心**的技术实现细节，包括如何设计一个可扩展的连接器框架、如何安全地管理OAuth流程和凭证、以及如何为`routines-service`提供统一的执行接口，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `connector-service` (连接器服务) 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-28
**参考SRS**: `connector-service-srs.md` (v2.0)
**核心架构**: 插件式连接器框架 + 统一认证与执行网关

## 1. 概述

`connector-service` 的架构核心是**抽象、适配与安全**。它必须将平台内外部、形态各异的API，封装成统一的、可被`routines-service`消费的**触发器(Triggers)**和**动作(Actions)**，并在此过程中处理所有复杂的认证和凭证管理。

**核心技术挑战**:
1.  **插件式架构**: 如何设计一个框架，使得添加对一个新应用（如Notion）的支持，就像编写一个独立的“插件”一样简单。
2.  **安全凭证生命周期管理**: 如何设计一个健壮的流程，来处理OAuth令牌的获取、加密存储、自动刷新和安全使用。
3.  **统一执行与上下文传递**: 如何设计一个统一的`ExecuteAction`入口，能智能地为每个动作准备正确的认证凭证（内部S2S令牌 vs 外部OAuth令牌），并将它们安全地传递给执行逻辑。
4.  **无状态轮询**: 如何在服务本身无状态的情况下，实现对外部API的有状态轮询。

本架构通过**“连接器(Connector)”插件化框架**和**集中的执行上下文构建**来应对这些挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (内部)

```mermaid
graph TD
    subgraph "调用方"
        A[Client App (UI)]
        B[Routines Service (Workflow Engine)]
    end

    subgraph "Connector Service"
        C[API Layer (gRPC/REST)<br/>(adapter/transport)]
        
        subgraph "Application Layer (The Brains)"
            D[CatalogService<br/>(application/query)]
            E[ConnectionService (OAuth Flow)<br/>(application/command)]
            F[ExecutionService (Action/Trigger Runner)<br/>(application/command)]
        end
        
        subgraph "Connector Framework (The Plugin System)"
            G{Connector Registry}
            H[Connector Interface]
            I[Internal Connector Implementations]
            J[External Connector Implementations]
        end
        
        subgraph "Infrastructure Adapters (The Bridge)"
            K[Repository (PostgreSQL)]
            L[KMSProxy Client]
            M[Internal gRPC Clients]
            N[External HTTP Clients]
        end
    end

    A -- "UI Data: GET /catalog" --> C -- "calls" --> D
    A -- "Auth Flow: /connections/..." --> C -- "calls" --> E
    B -- "Execution: gRPC ExecuteAction" --> C -- "calls" --> F
    
    D & E & F -- "look up from" --> G
    G -- "manages instances of" --> H
    H -- "implemented by" --> I & J
    
    I -- "uses" --> M
    J -- "uses" --> N & L
    
    E & F & I & J -- "persist via" --> K
```

### 2.2 最终目录结构 (`services/connector-service/`)

```
connector-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── transport/              # 统一处理gRPC和HTTP
│   │   │   ├── grpc_handler.go
│   │   │   └── http_handler.go
│   │   ├── repository/
│   │   │   └── postgres_repo.go
│   │   └── client/
│   │       ├── kms_proxy_client.go
│   │       ├── <all_internal_service>_client.go
│   │       └── http_client_factory.go # ✨ 创建带自动刷新token的HTTP客户端 ✨
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       ├── catalog_service.go
│   │       ├── connection_service.go
│   │       └── execution_service.go
│   └── domain/
│       └── connector/
│           ├── registry.go
│           ├── interface.go
│           ├── context.go           # ✨ 定义 ExecutionContext ✨
│           ├── internal/
│           │   └── chat/
│           │       └── connector.go # 单文件实现一个简单的内部连接器
│           └── external/
│               └── google_drive/
│                   ├── connector.go     # 实现Connector和OAuthProvider接口
│                   ├── actions.go       # 实现Action接口
│                   └── triggers.go      # 实现Trigger接口
└── ...
```

---

## 3. 各层职责深度解析

### 3.1 `domain/connector/` - 连接器框架 (The Plugin System)

这是整个服务**可扩展性的核心**。

*   **`interface.go`**: 定义核心接口。
    ```go
    // Connector 是所有能力提供者的统一抽象
    type Connector interface {
        Manifest() Manifest
        Actions() map[string]Action
        Triggers() map[string]Trigger
    }

    // OAuthProvider 是需要OAuth2认证的外部连接器必须实现的接口
    type OAuthProvider interface {
        GetOAuth2Config() *oauth2.Config
        // ... (可选) 自定义处理token响应的逻辑
    }
    
    // Action 代表一个可执行的动作
    type Action interface {
        GetSchema() *jsonschema.Schema
        Execute(ctx ExecutionContext) (map[string]interface{}, error)
    }
    
    // ... Trigger 接口类似 ...
    ```

*   **`context.go`**: 定义`ExecutionContext`。
    ```go
    // ExecutionContext 包含了执行一个动作所需的所有上下文
    type ExecutionContext struct {
        context.Context
        UserID          uuid.UUID
        Inputs          map[string]interface{}
        // 关键: 包含一个可直接使用的、已认证的客户端
        AuthenticatedClient *http.Client // For external actions
        UserS2SToken        string       // For internal actions
    }
    ```

*   **`registry.go`**: `ConnectorRegistry`。
    *   在服务启动时，通过`init()`函数**扫描并注册**所有`internal/`和`external/`目录下的连接器实例。
    *   **实现**: 每个连接器包（如`google_drive`）的`connector.go`中会有一个`init()`函数：`func init() { registry.MustRegister(&GoogleDriveConnector{}) }`。

### 3.2 `application/` - 应用层 (The Brains)

*   **`service/connection_service.go`**:
    *   处理OAuth流程。当`HandleCallback`收到`code`后，它会：
        1.  从`registry`中找到实现了`OAuthProvider`接口的`Connector`。
        2.  使用`connector.GetOAuth2Config()`和`code`来交换token。
        3.  **加密`refresh_token`**: 调用`kmsProxyClient.Encrypt(refreshToken)`。
        4.  **持久化**: 调用`repo.CreateConnection(...)`。

*   **`service/execution_service.go`**: **核心执行逻辑**。
    *   **`ExecuteAction(ctx, actionName, inputs, userS2SToken)`**:
        1.  从`registry`中找到对应的`Connector`和`Action`。
        2.  **构建`ExecutionContext`**:
            a. 获取`Connector`的认证类型。
            b. **If `AuthType == INTERNAL`**:
                *   将`userS2SToken`放入`ExecutionContext`。
            c. **If `AuthType == OAUTH2`**:
                *   调用`repo.GetConnection(userID, connectorID)`获取用户连接凭证。
                *   调用`adapter/client.HTTPClientFactory.NewOAuthClient(ctx, connection)`。这个工厂方法是**关键**：
                    *   它使用`golang.org/x/oauth2`库，创建一个`http.Client`。
                    *   这个`http.Client`被配置为**自动处理token刷新**。它会在发送请求前检查token是否过期，如果过期，会透明地使用`refresh_token`获取新token，并将新token（加密后）写回数据库。
                *   将这个配置好的`http.Client`放入`ExecutionContext`。
        3.  **执行**: 调用`action.Execute(executionContext)`。

### 3.3 `adapter/client/http_client_factory.go` - 认证客户端工厂

这是实现透明令牌刷新的关键。
```go
// adapter/client/http_client_factory.go

// NewOAuthClient 创建一个能自动刷新token的HTTP客户端
func (f *Factory) NewOAuthClient(ctx context.Context, conn *models.UserConnection) *http.Client {
    // 1. 从KMSProxy解密refresh_token
    refreshToken := f.kmsProxy.Decrypt(conn.EncryptedRefreshToken)

    oauth2Config := f.connectorRegistry.Get(conn.ConnectorID).GetOAuth2Config()

    // 2. 创建一个 oauth2.TokenSource
    tokenSource := oauth2Config.TokenSource(ctx, &oauth2.Token{
        AccessToken:  conn.AccessToken,
        RefreshToken: refreshToken,
        Expiry:       conn.ExpiresAt,
    })

    // 3. (关键) 创建一个新的TokenSource，它能将刷新的token写回数据库
    persistingTokenSource := NewPersistingTokenSource(conn.ID, tokenSource, f.repo)

    // 4. 返回一个使用此TokenSource的http.Client
    return oauth2.NewClient(ctx, persistingTokenSource)
}
```
`PersistingTokenSource`是一个自定义的`oauth2.TokenSource`包装器，它在内部的`Token()`方法被调用（当令牌刷新时）后，会把新的token持久化到数据库。

## 4. 数据库与数据模型

*   **`user_connections` Table**:
    *   `id` (PK)
    *   `user_id` (INDEX)
    *   `connector_id` (INDEX)
    *   `status` (`ACTIVE`, `NEEDS_REAUTH`)
    *   `access_token` (TEXT, 可选，用于短期缓存)
    *   `encrypted_refresh_token` (BYTEA, **核心安全字段**)
    *   `token_expiry` (TIMESTAMPTZ)
    *   `scopes_granted` (TEXT[])

## 5. 总结

本架构设计通过以下关键点，构建了一个生产级的`connector-service`：
1.  **插件式框架 (`domain/connector`)**: 通过接口和自动注册机制，实现了极高的可扩展性。添加一个新应用的支持，只需编写一个独立的、符合规范的包。
2.  **集中的安全认证**: OAuth流程和高度敏感的凭证管理被集中在`application.ConnectionService`中，并由`KMSProxy`提供硬件级安全保障。
3.  **透明的令牌刷新**: 通过`HTTPClientFactory`和自定义的`TokenSource`，将复杂的令牌刷新逻辑对上层的`Action`实现完全屏蔽。
4.  **统一的执行上下文**: `ExecutionContext`为每个动作的执行准备好了一切所需（认证的客户端、输入参数），使得`Action`的实现可以非常纯粹和简单。
5.  **内外一致**: 无论是连接内部微服务还是外部SaaS，都遵循同一套`Connector` -> `Action`的抽象模型，降低了开发者的心智负担。

这种架构使得`connector-service`能够安全、可靠、高效地扮演其“万能插座”和“外交部”的角色，为CINA.CLUB的自动化和集成能力提供了无限的可能。