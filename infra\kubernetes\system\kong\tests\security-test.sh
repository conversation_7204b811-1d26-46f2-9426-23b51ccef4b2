#!/bin/bash

# CINA.CLUB Kong Gateway - Security Test
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00
#
# Platform engineering security tests for Kong Gateway

set -euo pipefail

# Test configuration
KONG_PROXY_URL="${KONG_PROXY_URL:-http://kong-proxy.kong-system.svc.cluster.local:8000}"

# Test JWT authentication failure
test_jwt_auth_failure() {
    echo "Testing JWT authentication failure..."
    
    # Test without JWT token
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
        "${KONG_PROXY_URL}/api/v1/users/profile")
    
    if [ "$status_code" = "401" ]; then
        echo "✅ Correctly rejected request without JWT"
        return 0
    else
        echo "❌ Expected 401, got $status_code"
        return 1
    fi
}

# Test invalid JWT token
test_invalid_jwt() {
    echo "Testing invalid JWT token..."
    
    local invalid_token="invalid.jwt.token"
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: Bearer $invalid_token" \
        "${KONG_PROXY_URL}/api/v1/users/profile")
    
    if [ "$status_code" = "401" ]; then
        echo "✅ Correctly rejected invalid JWT"
        return 0
    else
        echo "❌ Expected 401, got $status_code"
        return 1
    fi
}

# Test expired JWT token
test_expired_jwt() {
    echo "Testing expired JWT token..."
    
    # Create an expired JWT (this is a mock - replace with actual expired token)
    local expired_token="eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImV4cCI6MTUxNjIzOTAyMn0.expired"
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: Bearer $expired_token" \
        "${KONG_PROXY_URL}/api/v1/users/profile")
    
    if [ "$status_code" = "401" ]; then
        echo "✅ Correctly rejected expired JWT"
        return 0
    else
        echo "❌ Expected 401 for expired token, got $status_code"
        return 1
    fi
}

# Test JWT header extraction
test_jwt_header_extraction() {
    echo "Testing JWT header extraction..."
    
    # Test different header formats
    local formats=(
        "Bearer token123"
        "JWT token123"
        "token123"
    )
    
    for format in "${formats[@]}"; do
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: $format" \
            "${KONG_PROXY_URL}/api/v1/users/profile")
        
        # Should consistently reject invalid tokens
        if [ "$status_code" = "401" ]; then
            echo "✅ Correctly handled format: $format"
        else
            echo "❌ Unexpected response for format: $format (got $status_code)"
        fi
    done
    
    return 0
}

# Test rate limiting security
test_rate_limiting_security() {
    echo "Testing rate limiting security..."
    
    # Make rapid requests to trigger rate limiting
    local rate_limited=false
    
    for i in {1..30}; do
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" \
            "${KONG_PROXY_URL}/api/v1/auth/login" \
            -H "Content-Type: application/json" \
            -d '{"email":"<EMAIL>","password":"test"}')
        
        if [ "$status_code" = "429" ]; then
            rate_limited=true
            break
        fi
        
        sleep 0.1
    done
    
    if [ "$rate_limited" = true ]; then
        echo "✅ Rate limiting is working correctly"
        return 0
    else
        echo "❌ Rate limiting did not trigger"
        return 1
    fi
}

# Test CORS security
test_cors_security() {
    echo "Testing CORS security..."
    
    # Test with malicious origin
    local malicious_origin="https://malicious.example.com"
    local response_headers=$(curl -s -D - -o /dev/null \
        -H "Origin: $malicious_origin" \
        -H "Access-Control-Request-Method: POST" \
        "${KONG_PROXY_URL}/api/v1/auth/login")
    
    if echo "$response_headers" | grep -q "Access-Control-Allow-Origin: $malicious_origin"; then
        echo "❌ CORS allows malicious origin"
        return 1
    else
        echo "✅ CORS correctly blocks malicious origin"
        return 0
    fi
}

# Test security headers
test_security_headers() {
    echo "Testing security headers..."
    
    local response_headers=$(curl -s -D - -o /dev/null \
        "${KONG_PROXY_URL}/api/v1/health")
    
    local headers_to_check=(
        "X-Frame-Options"
        "X-Content-Type-Options"
        "X-XSS-Protection"
        "Strict-Transport-Security"
    )
    
    local missing_headers=0
    
    for header in "${headers_to_check[@]}"; do
        if echo "$response_headers" | grep -qi "$header"; then
            echo "✅ Security header present: $header"
        else
            echo "⚠️  Security header missing: $header"
            ((missing_headers++))
        fi
    done
    
    if [ $missing_headers -eq 0 ]; then
        echo "✅ All security headers present"
        return 0
    else
        echo "❌ $missing_headers security headers missing"
        return 1
    fi
}

# Main test execution
main() {
    echo "Starting Kong Gateway security tests..."
    
    local failed=0
    
    test_jwt_auth_failure || ((failed++))
    test_invalid_jwt || ((failed++))
    test_expired_jwt || ((failed++))
    test_jwt_header_extraction || ((failed++))
    test_rate_limiting_security || ((failed++))
    test_cors_security || ((failed++))
    test_security_headers || ((failed++))
    
    if [ $failed -eq 0 ]; then
        echo "🎉 All Kong Gateway security tests passed!"
        exit 0
    else
        echo "❌ $failed Kong Gateway security tests failed"
        exit 1
    fi
}

main "$@" 