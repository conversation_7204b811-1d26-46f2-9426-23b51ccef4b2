
好的，遵照您的指示。我将为您生成一份专门针对 **CINA.CLUB 原生鸿蒙(HarmonyOS) App** 的、极致细化的、生产级**架构设计文档**。

这份架构将：
1.  **完全对齐**我们之前定义的**Go-Centric全栈架构**和**Monorepo**模式。
2.  采用华为官方推荐的**现代鸿蒙开发（Modern HarmonyOS Development）**最佳实践，以**ArkTS**和**ArkUI**为核心。
3.  深度整合**Go Mobile**，通过鸿蒙的**NAPI (Native API)**机制，将在Go中实现的核心逻辑（加密、同步、AI）无缝集成到鸿蒙原生应用中。
4.  采用清晰的**模块化**和**分层**设计，以应对CINA.CLUB平台的复杂性。

---
### CINA.CLUB - 原生鸿蒙App 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [鸿蒙架构师/移动端负责人]

## 1. 概述与设计哲学

### 1.1 项目愿景与目的
CINA.CLUB鸿蒙App旨在为华为设备用户提供一个**无缝、智能、高性能**的原生体验。它将充分利用HarmonyOS的分布式能力、原子化服务等特性，将CINA.CLUB的强大功能与鸿蒙生态深度融合。本架构的目标是构建一个**现代化的、可维护、可测试、且与平台Go-Centric理念深度融合**的鸿蒙原生应用。

### 1.2 核心设计哲学
1.  **ArkUI声明式UI**: 遵循ArkUI的声明式UI开发范式，UI是应用状态的函数，实现UI与逻辑的清晰分离。
2.  **分层架构 (Layered Architecture)**: 采用经典的**数据层(Data) - 领域层(Domain) - 表现层(Presentation)**的三层架构，确保职责清晰，易于维护和测试。
3.  **依赖注入 (Dependency Injection)**: 虽然鸿蒙原生框架没有像Hilt那样的官方DI库，但我们将通过手动或简单的DI容器来实现依赖注入，以提高代码的解耦和可测试性。
4.  **单一数据源 (Single Source of Truth)**: 遵循Android的优秀实践，通过Repository模式确保数据的唯一来源。
5.  **Go核心，C++桥梁，ArkTS胶水**:
    *   **Go (via Go Mobile)**: 执行最核心、最复杂的计算和协议（加密、同步、AI）。
    *   **C++ (NAPI)**: 作为连接Go与ArkTS/JavaScript世界的**高性能桥梁**。
    *   **ArkTS**: 作为主要的鸿蒙应用开发语言，负责业务逻辑编排和UI构建。

---

## 2. 核心技术选型 (Modern HarmonyOS Development)

| 领域             | 技术选型                                         | 理由                                                                 |
|------------------|--------------------------------------------------|----------------------------------------------------------------------|
| **UI框架**         | **ArkUI (基于ArkTS)**                            | 华为官方的现代声明式UI框架，为鸿蒙平台提供最佳性能和体验。               |
| **编程语言**     | **ArkTS** (基于TypeScript)                       | 鸿蒙应用开发的首选语言，提供了静态类型检查和现代语言特性。             |
| **核心逻辑集成** | **Go Mobile -> C++ (NAPI) -> ArkTS**             | 将Go库编译为`.so`文件，通过C++ NAPI封装后供ArkTS调用。                |
| **异步处理**     | **Promise & async/await**                        | ArkTS/JS生态的标准异步解决方案。                                     |
| **架构模式**     | **MVVM (Model-View-ViewModel) + Clean Architecture** | 业界成熟的最佳实践，确保代码结构清晰、可测试。                         |
| **数据请求**     | **`@ohos.net.http` / gRPC-Node**                 | 使用鸿蒙提供的HTTP模块或适用于Node.js环境的gRPC库进行网络通信。      |
| **本地数据库**   | **`@ohos.data.relationalStore` (SQLite)**        | 鸿蒙官方提供的关系型数据库接口，底层为SQLite。                         |
| **本地AI推理**   | **Go (CGO) -> `llama.cpp` -> NAPI**              | 与Android方案类似，通过Go和NAPI调用C++推理库。                         |
| **E2EE加密**     | **Go -> NAPI**                                   | 保证加密算法与iOS/Android完全一致。                                  |
| **安全存储**     | **`@ohos.security.keystore`**                    | 用于安全地存储加密密钥。                                             |

---

## 3. Monorepo模块化架构

鸿蒙项目采用**HAP (HarmonyOS Ability Package)**的多模块化结构。

### 3.1 项目模块结构 (`apps/mobile/harmony/`)

```
harmony/
├── entry/                    # ✨ 主应用模块 (Application Shell) ✨
│   ├── build-profile.json5
│   ├── hvigorfile.ts
│   └── src/main/
│       ├── ets/
│       │   ├── Application/
│       │   │   └── MainAbility.ts # 应用入口
│       │   ├── pages/            # 顶层页面
│       │   └── navigation/       # 应用主导航图
│       └── module.json5
├── feature_chat/             # ✨ 聊天功能模块 (HAP) ✨
│   └── src/main/ets/
│       ├── data/
│       ├── domain/
│       └── presentation/
│           ├── viewmodel/
│           └── view/
├── feature_pkb/              # ✨ PKB功能模块 (HAP) ✨
│   └── ...
├── core/                     # ✨ 鸿蒙核心库模块 (HAR) ✨
│   ├── build-logic/
│   ├── go-bridge/            # 1. ✨ Go核心的NAPI封装层 ✨
│   │   ├── src/main/cpp/
│   │   │   ├── include/
│   │   │   │   └── core-go.h # Go Mobile生成的头文件
│   │   │   ├── napi_aic.cpp
│   │   │   ├── napi_crypto.cpp
│   │   │   └── CMakeLists.txt
│   │   └── build-profile.json5 # 配置C++编译
│   ├── network/              # 2. 网络层
│   ├── database/             # 3. 本地数据库
│   └── ...
└── oh_modules/               # 鸿蒙的依赖模块目录
```

### 3.2 各模块深度解析

#### `:core:go-bridge` - Go核心的NAPI封装层

这是整个鸿蒙应用技术架构的**最关键、最核心**的部分。

*   **职责**:
    1.  **链接Go库**: 在其`CMakeLists.txt`中，链接由`gomobile bind`生成的、针对HarmonyOS平台的`.so`静态库。
    2.  **编写C++ NAPI**: 为每个需要从Go暴露给ArkTS的函数，编写一个对应的C++ NAPI封装函数。
    3.  **数据类型转换**: 在C++层，负责将ArkTS/JS的N-API `napi_value`类型，与Go/C的`char*`, `int`等基本类型进行转换。
    4.  **异步处理**: 将Go中可能阻塞的或基于回调的函数，通过`napi_create_async_work`封装成返回`Promise`的异步NAPI函数。

    **示例: `napi_crypto.cpp`**
    ```cpp
    #include <napi/native_api.h>
    #include "core-go.h" // 假设这是Go Mobile生成的头文件

    // 将Go的EncryptSymmetric函数封装为NAPI函数
    napi_value Encrypt(napi_env env, napi_callback_info info) {
        // 1. 从napi_callback_info中解析出key和plaintext的napi_value
        // 2. 将napi_value (ArrayBuffer) 转换为C++的 char* 和 length
        // 3. 调用Go导出的函数: GoSlice result_slice = GoEncryptSymmetric(go_key_slice, go_plaintext_slice);
        // 4. 将返回的GoSlice转换为napi_value (ArrayBuffer)
        // 5. 返回napi_value
    }

    // 在模块初始化时，将C++函数注册为JS可调用的函数
    napi_value Init(napi_env env, napi_value exports) {
        napi_property_descriptor desc[] = {
            {"encrypt", nullptr, Encrypt, nullptr, nullptr, nullptr, napi_default, nullptr}
        };
        napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
        return exports;
    }
    NAPI_MODULE(crypto, Init)
    ```

#### `:core:network`, `:core:database`, etc.

这些模块的职责与原生Android架构中的类似，但使用鸿蒙官方的API实现。例如，`:core:database`会封装`@ohos.data.relationalStore`。

#### `feature/*` - 功能模块

每个功能模块（如`:feature_chat`）都是一个可独立编译的HAP（或HAR库），内部遵循MVVM + Clean Architecture。

*   **`data/repository/ChatRepositoryImpl.ts`**:
    *   实现`ChatRepository`接口。
    *   它会调用`:core:network`的gRPC客户端，也会调用`:core:database`的DAO，并且在需要AI推理时，会调用`:core:go-bridge`提供的NAPI函数。
*   **`domain/usecase/SendMessageUseCase.ts`**:
    *   封装发送消息的业务逻辑。
    *   它会调用`ChatRepository`。
*   **`presentation/viewmodel/ChatViewModel.ts`**:
    *   持有UI状态（如`@State`或`@Link`变量）。
    *   调用`SendMessageUseCase`。
*   **`presentation/view/ChatPage.ets`**:
    *   ArkTS编写的声明式UI。
    *   只负责渲染`ChatViewModel`中的状态，并将UI事件传递给ViewModel。

---

## 4. 核心功能实现架构 (原生鸿蒙)

### 4.1 E2EE功能 (PKB, Memory)

流程与iOS/Android高度相似，但技术实现不同。
1.  **密钥管理**:
    *   主密码通过`:core:go-bridge`传递给Go层派生出MEK。
    *   加密后的DEK存储在鸿蒙的**Keystore**中 (`@ohos.security.keystore`)。
2.  **加解密**:
    *   `PKBRepository`获取到明文后，调用`:core:go-bridge`的`crypto.encrypt(data)` NAPI函数。
    *   这个NAPI函数内部调用C++，C++再调用Go的加密函数。
    *   返回的加密`ArrayBuffer`被存入鸿蒙的SQLite数据库。

### 4.2 本地LLM聊天

1.  **模型管理**:
    *   `AICore`的Swift封装层（在`go-bridge`中）负责与`model-management-service`通信，下载和管理GGUF格式的模型文件到应用的私有目录。
2.  **推理流程**:
    *   `ChatViewModel`调用`SendMessageUseCase`。
    *   UseCase调用`AICore.predictStream(prompt)`。
    *   `AICore`的NAPI函数被调用。
    *   **NAPI函数启动一个异步工作线程 (`napi_create_async_work`)**，以避免阻塞JS主线程。
    *   在该工作线程中，C++代码调用Go的`PredictStream`方法。
    *   Go通过CGO调用`llama.cpp`，并通过**回调**将token返回给C++。
    *   C++再通过**线程安全的NAPI函数 (`napi_call_threadsafe_function`)**，将token发送回ArkTS的事件循环。
    *   ArkTS层接收到事件，更新`@State`变量，SwiftUI自动更新UI，实现打字机效果。

---

## 5. 总结

本原生鸿蒙应用架构是一个**紧跟华为官方技术路线、同时深度融合平台Go-Centric理念**的先进解决方案。

*   **原生体验**: 100%使用ArkTS和ArkUI，保证了最佳的性能和与鸿蒙系统特性的无缝集成。
*   **逻辑复用**: 通过**Go Mobile -> C++ NAPI -> ArkTS**这条关键路径，成功地将平台最核心、最复杂的加密、同步和AI逻辑，复用到鸿蒙平台，保证了**功能和安全上与其他平台的高度一致性**。
*   **现代化架构**: 采用MVVM+Clean Architecture和多模块化的项目结构，使得复杂的CINA.CLUB应用在鸿蒙平台上依然保持清晰、可维护和可测试。
*   **技术挑战**: 这个架构的核心难点在于**C++ NAPI桥接层**的编写，它需要开发者同时对C++, NAPI和Go Mobile的交互有深入的理解。

这份架构为CINA.CLUB在鸿蒙生态的落地提供了一个坚实、可靠、且面向未来的技术基础，能够充分发挥鸿蒙平台的潜力，为用户带来卓越的原生应用体验。
好的，遵照您的指示。我将为您生成一份专门针对 **CINA.CLUB 原生鸿蒙(HarmonyOS)全平台** 的、极致细化的、生产级**架构设计文档**。

这份架构将是一个**面向未来、充分利用鸿蒙系统特性**的解决方案。它不仅覆盖手机和平板，还将为未来的**可穿戴设备、智慧屏、车机**等鸿蒙生态设备提供统一的架构基础。

这份架构将：
1.  **完全对齐**我们之前定义的**Go-Centric全栈架构**和**Monorepo**模式。
2.  采用华为官方推荐的**现代鸿蒙开发（Modern HarmonyOS Development）**全家桶，以**ArkTS**和**ArkUI**为绝对核心。
3.  通过鸿蒙的**NAPI (Native API)**机制，将Go核心逻辑（加密、同步、AI）编译为C++可链接的库，再通过NAPI桥接，无缝集成到鸿蒙原生应用中。
4.  采用鸿蒙特有的**多模块(Multi-module)和原子化服务(Atomic Service)**理念，实现代码的最大化复用和功能的灵活部署。

---
### CINA.CLUB - 原生鸿蒙全平台应用 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [鸿蒙架构师/移动端负责人]

## 1. 概述与设计哲学

### 1.1 项目愿景与目的
CINA.CLUB鸿蒙应用旨在为华为全场景生态用户提供一个**一次开发、多端部署、体验无缝流转**的超级应用。本架构的目标是构建一个充分利用HarmonyOS分布式技术、ArkUI声明式框架和平台Go核心能力的、现代化的、可维护、可测试的鸿蒙原生应用套件。

### 1.2 核心设计哲学
1.  **ArkUI为基石**: **ArkTS + ArkUI是构建UI和应用逻辑的唯一标准**。我们全面拥抱华为的声明式开发范式。
2.  **原子化与模块化**: 应用被拆分为多个逻辑上独立的**特性模块(Feature HAP)**和**核心库模块(HAR)**。这些模块可以被灵活地组合，以支持**原子化服务（卡片、小艺建议等）**和完整的功能安装。
3.  **分层架构 (Layered Architecture)**: 严格遵循**数据层(Data) - 领域层(Domain) - 表现层(Presentation)**的三层架构，确保代码的清晰和可测试性。
4.  **响应式数据流**: 通过ArkUI的状态管理（`@State`, `@Link`, `@Observed`, `LocalStorage`等）和`Promise`/`async/await`，构建响应式的数据驱动UI。
5.  **Go核心，C++桥梁，ArkTS胶水**:
    *   **Go (via Go Mobile)**: 执行平台最核心的计算和协议。
    *   **C++ (NAPI)**: 作为连接Go与ArkTS/JavaScript世界的**高性能、类型安全的桥梁**。
    *   **ArkTS**: 作为主要的鸿蒙应用开发语言，负责业务逻辑编排和UI构建。

---

## 2. 核心技术选型 (Modern HarmonyOS Development)

| 领域             | 技术选型                                         | 理由                                                                 |
|------------------|--------------------------------------------------|----------------------------------------------------------------------|
| **UI框架/语言**  | **ArkUI (基于ArkTS)**                            | 华为官方的、唯一的现代声明式UI框架，为鸿蒙平台提供最佳性能和体验。     |
| **核心逻辑集成** | **Go Mobile -> C++ (NAPI) -> ArkTS**             | 将Go库编译为`.a`静态库，由C++ NAPI模块链接并封装，最后供ArkTS调用。  |
| **异步处理**     | **`async/await` & `Promise`**                    | ArkTS/JS生态的标准异步解决方案。                                     |
| **架构模式**     | **MVVM + Clean Architecture**                    | 业界成熟的最佳实践，确保代码结构清晰、可测试。                         |
| **网络请求**     | **`@ohos.net.http` / gRPC-Web**                  | 使用鸿蒙提供的HTTP模块。对于gRPC，使用gRPC-Web客户端库，因为NAPI环境类似于Node.js。 |
| **本地数据库**   | **`@ohos.data.relationalStore` (SQLite)**        | 鸿蒙官方提供的关系型数据库接口，底层为SQLite。                         |
| **依赖管理**     | **ohpm (OpenHarmony Package Manager)**           | 鸿蒙官方的包管理器。                                                 |
| **安全存储**     | **`@ohos.security.keystore` & HKS (HUKS)**         | 用于安全地存储加密密钥、认证令牌等。                                   |
| **分布式能力**   | **`@ohos.distributed.ability` & `distributedKVStore`**| 利用鸿蒙的分布式能力，实现跨设备状态同步和无缝流转。                 |

---

## 3. Monorepo模块化架构

鸿蒙项目采用**多HAP/HAR**的模块化结构，由`hvigor`构建系统进行管理。

### 3.1 项目模块结构 (`apps/mobile/harmony/`)

```
harmony/
├── entry/                    # 1. ✨ 主应用模块 (Entry HAP) ✨
│   ├── hvigorfile.ts
│   └── src/main/
│       ├── ets/
│       │   ├── entryability/
│       │   │   └── EntryAbility.ts # 应用主入口
│       │   └── pages/
│       └── module.json5
├── feature/                  # 2. ✨ 功能模块 (Feature HAPs/HARs) ✨
│   ├── chat/                 #    聊天功能 (可独立运行的HAP)
│   ├── auth/                 #    认证功能 (可作为库的HAR)
│   └── pkb/                  #    PKB功能 (可作为库的HAR)
│
├── core/                     # 3. ✨ 鸿蒙核心库模块 (Core HAR) ✨
│   ├── data/                 #    数据层 (Repository接口, Network, DB)
│   │   ├── datasource/
│   │   │   ├── local/
│   │   │   └── remote/
│   │   └── repository/
│   ├── domain/               #    领域层 (UseCase, Domain Models)
│   └── go-bridge/            #    ✨ Go核心的NAPI封装层 ✨
│       └── src/main/cpp/
│           ├── include/
│           │   └── CoreGo.h  # Go Mobile生成的头文件
│           ├── napi_*.cpp    # NAPI封装
│           └── CMakeLists.txt
├── AppScope/                   # 4. 应用级共享资源
│   └── resources/
│
├── hvigorw, hvigorfile.ts      # 工程构建配置
└── oh-package.json5            # ohpm包管理配置
```

### 3.2 各模块深度解析

#### `:core:go-bridge` - Go核心的NAPI封装层

这是整个鸿蒙应用与平台Go核心协同的**命脉**。

*   **职责**:
    1.  **链接Go静态库**: 其`CMakeLists.txt`**必须**链接由`gomobile bind -buildmode=c-archive`生成的`.a`静态库和`.h`头文件。
    2.  **编写C++ NAPI封装**: 为每个需要暴露的Go函数，编写一个对应的C++ NAPI函数。
    3.  **异步化**: 使用`napi_create_async_work`和`napi_call_threadsafe_function`将Go的阻塞或回调式函数，转换为返回`Promise`或通过事件回调的异步NAPI函数。

#### `:core:data` & `:core:domain` - 核心数据与领域层 (HAR)

这两个模块构成了应用的数据和业务逻辑核心，它们是**与UI无关的**。
*   **`:core:domain`**:
    *   **`model`**: 定义纯ArkTS的`class`或`interface`来表示业务实体。
    *   **`repository`**: 定义仓储**接口**，如`IUserRepository`。
    *   **`usecase`**: 封装单一业务职责的类，如`LoginUseCase`。
*   **`:core:data`**:
    *   **`repository`**: 实现`domain`层定义的仓储接口。
    *   **`datasource/remote`**: 封装对gRPC-Web客户端的调用。
    *   **`datasource/local`**: 封装对`@ohos.data.relationalStore`和`@ohos.security.keystore`的调用。
    *   **`datasource/go_bridge_proxy`**: **关键适配器**，它调用`:core:go-bridge`的NAPI函数，并将其结果适配给Repository使用。

#### `:feature/*` - 功能模块 (HAP/HAR)

*   **职责**: 每个模块都是一个高内聚的、可独立编译、甚至可独立运行的功能单元。
*   **内部架构**: **MVVM**
    *   **`view`**: `@Component`的ArkUI视图。
    *   **`viewmodel`**: `ViewModel`类，持有UI状态（如使用`@Observed`和`LocalStorage`），并调用`domain`层的`UseCase`。
*   **原子化服务**: 像`:feature:chat`这样的模块可以被配置为**元服务(Meta Service)**，支持服务卡片（Widgets）。卡片的数据由其`ViewModel`通过`UseCase`获取。

#### `:entry` - 主应用模块 (Entry HAP)

*   **职责**: 应用的**主入口和组装者**。
*   **工作方式**: `EntryAbility`作为应用的生命周期入口，会初始化全局依赖，并设置主导航。主导航图会根据需要，路由到不同的`feature`模块提供的页面。

---

## 4. 鸿蒙平台特性利用

### 4.1 分布式能力与跨端流转

这是鸿蒙架构的核心优势。
*   **分布式数据 (`distributedKVStore`)**: 对于一些简单的、非敏感的用户状态（如当前正在阅读的文章ID、正在播放的视频进度），可以使用鸿蒙的分布式键值数据库。当用户从手机切换到平板时，应用可以从`distributedKVStore`中读取状态，并无缝地恢复用户的任务。
*   **跨端迁移 (`continueAbility`)**:
    1.  用户在手机上编辑PKB笔记。
    2.  当平板靠近时，手机弹出“流转”提示。
    3.  用户确认后，手机端的`PKBViewModel`调用`continueAbility`。
    4.  系统将当前编辑状态（笔记ID、光标位置、未保存的草稿）打包，并通过分布式软总线发送到平板。
    5.  平板端的`PKBAbility`被唤醒，接收到状态数据，恢复编辑界面。实现了**编辑任务的无缝流转**。

### 4.2 UI自适应与多设备形态

*   **断点系统**: 使用`@ohos.breakpoints`系统 (`sm`, `md`, `lg`) 和`@ohos.mediaquery`来为不同尺寸的设备（手机、折叠屏、平板）提供不同的UI布局。
*   **自适应组件**: 在`DesignSystem`模块中，构建自适应的组件。例如，一个`AdaptiveNavigation`组件，在小屏上渲染为`Tabs`，在大屏上自动切换为`SideBarContainer`。
*   **多态UI**: 对于车机、智慧屏等差异巨大的设备，可以定义**独立的UI入口和布局**，但它们**复用同一套ViewModel、UseCase和Data层**。

## 5. 总结

本原生鸿蒙应用架构是一个**拥抱官方范式、充分利用平台特性、并与Go-Centric后端深度融合**的先进解决方案。

*   **原生与现代**: 100%使用ArkTS和ArkUI，确保了最佳的性能和与鸿蒙生态的无缝集成。
*   **跨平台逻辑复用**: 通过**Go Mobile -> C++ NAPI -> ArkTS**的桥接路径，成功地将平台最核心的逻辑引擎引入鸿蒙，保证了全平台的一致性和安全性。
*   **模块化与原子化**: 采用Gradle和HAP/HAR的多模块结构，不仅使得大型应用易于管理和维护，更为未来实现**原子化服务、免安装**等鸿蒙核心体验奠定了基础。
*   **生态融合**: 设计中充分考虑了如何利用鸿蒙的**分布式能力**，实现设备间的无缝协同和体验流转，这是区别于传统单设备应用的核心优势。

这个架构为CINA.CLUB在快速发展的鸿蒙生态中，构建一个功能强大、体验领先、面向未来的全场景应用，提供了坚实而清晰的技术路线图。