好的，遵照您的指示。我将为您生成一份专门针对 **Monorepo `/scripts` 目录** 的、极致细化的、生产级**架构设计与SRS文档**。

这份文档将详细阐述`/scripts`目录的设计哲学、功能范围、以及其中包含的关键脚本的具体需求和实现要点。它将作为所有开发人员和CI/CD系统进行日常开发、构建和运维操作的“快捷方式”和“标准操作流程(SOP)”。

---
### CINA.CLUB - 开发与运维脚本 (`/scripts`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [平台工程/DevOps负责人]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计原则](#3-核心设计原则)
4.  [目录与文件结构](#4-目录与文件结构)
5.  [功能需求 (按脚本拆分)](#5-功能需求-按脚本拆分)
    *   [5.1 `gen-proto.sh`: API代码生成脚本](#51-gen-protosh-api代码生成脚本)
    *   [5.2 `gen-gomobile.sh`: Go Mobile库编译脚本](#52-gen-gomobilesh-go-mobile库编译脚本)
    *   [5.3 `gen-wasm.sh`: WebAssembly编译脚本](#53-gen-wasmsh-webassembly编译脚本)
    *   [5.4 `db-migrate.sh`: 数据库迁移脚本](#54-db-migratesh-数据库迁移脚本)
    *   [5.5 `setup-dev-env.sh`: 本地开发环境设置脚本](#55-setup-dev-envsh-本地开发环境设置脚本)
    *   [5.6 `run-service.sh`: 本地运行单个服务脚本](#56-run-servicesh-本地运行单个服务脚本)
    *   [5.7 `lint.sh`: 代码规范检查脚本](#57-lintsh-代码规范检查脚本)
6.  [通用需求与最佳实践](#6-通用需求与最佳实践)
7.  [技术约束与开发规范](#7-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的大型Monorepo中，开发人员和CI/CD系统需要频繁执行一系列标准化的、多步骤的命令，如代码生成、编译、数据库迁移、环境设置等。`/scripts` 目录的目的在于提供一套**简单、健壮、可复用的Shell脚本**，将这些复杂或重复的操作封装成一键式命令。这旨在：
*   **简化开发工作流**: 让开发者无需记住冗长的命令和参数。
*   **保证操作一致性**: 确保无论是在开发人员的本地机器上还是在CI服务器上，相同的操作都以完全相同的方式执行。
*   **提升自动化水平**: 为CI/CD流水线提供稳定、可靠的调用入口。
*   **降低上手门槛**: 新成员可以通过运行一个脚本快速搭建好开发环境。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   提供一系列主要用**Bash**编写的、具有良好注释和错误处理的Shell脚本。
    *   封装对平台级工具（如`buf`, `gomobile`, `golang-migrate`）和自定义工具（在`/tools`中）的调用。
    *   编排一系列命令来完成一个特定的开发或运维任务。
*   **范围之外 (Out-of-Scope)**:
    *   **复杂的、需要解析代码或文件结构的逻辑**: 这类任务应在`/tools`目录中用Go语言编写成CLI工具，然后由`/scripts`中的脚本来调用。
    *   **CI/CD流水线的定义**: 由`/.github/workflows/`负责，但这些workflow会大量调用本目录下的脚本。

#### 1.3. 目标用户
*   **CINA.CLUB所有开发人员**。
*   **CI/CD自动化系统**。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`/scripts` 目录是Monorepo的**“操作手册”和“快捷工具栏”**。它与`/tools`目录协同工作，前者负责“编排”，后者负责“执行复杂任务”。所有脚本都应设计为可以从Monorepo的根目录执行。

#### 2.2. 设计原则
*   **可移植性 (Portability)**: 脚本应尽量使用POSIX兼容的Shell语法，确保能在主流的开发环境（Linux, macOS, Windows via WSL）中运行。
*   **幂等性 (Idempotency)**: 如果可能，脚本应设计为可重复运行而不会产生副作用。
*   **健壮的错误处理**: 任何命令失败都必须立即中止脚本（使用`set -e`），并输出清晰的错误信息。
*   **清晰的输出**: 脚本在执行时应打印出关键步骤信息，让用户知道它在做什么。可以使用颜色来区分信息、警告和错误。
*   **文档化**: 每个脚本的开头都必须有注释块，解释其用途、用法和所需的环境变量。

---

### 3. 核心设计原则

为简化调用和管理，强烈建议使用 **`Makefile`** 或 **`Taskfile.yml`** (推荐，因其跨平台和更现代的语法) 作为所有脚本的统一入口。开发者只需记住简单的命令，如`make gen-proto`或`task gen:proto`。

**示例 `Makefile`**:
```makefile
.PHONY: all proto mobile wasm lint

all: proto mobile wasm

proto:
	@echo ">> Generating Protobuf code..."
	@./scripts/gen-proto.sh

mobile:
	@echo ">> Generating Go Mobile bindings..."
	@./scripts/gen-gomobile.sh

wasm:
	@echo ">> Generating WebAssembly module..."
	@./scripts/gen-wasm.sh

lint:
	@echo ">> Running linters..."
	@./scripts/lint.sh
```

---

### 4. 目录与文件结构

```
scripts/
├── lib/                      # 1. 共享的Shell函数库
│   └── helpers.sh
├── gen-proto.sh              # 2. API代码生成脚本
├── gen-gomobile.sh           # 3. Go Mobile库编译脚本
├── gen-wasm.sh               # 4. WebAssembly编译脚本
├── db/                       # 5. 数据库相关脚本
│   ├── migrate.sh            #    运行数据库迁移
│   └── seed.sh               #    填充种子数据
├── setup-dev-env.sh          # 6. 本地开发环境设置脚本
├── run-service.sh            # 7. 本地运行单个服务脚本
└── lint.sh                   # 8. 代码规范检查脚本
```
---

### 5. 功能需求 (按脚本拆分)

#### 5.1. `lib/helpers.sh`: 共享函数库
*   **职责**: 存放被其他脚本共同调用的辅助Shell函数。
*   **功能需求**:
    *   **FR5.1.1 (日志函数)**: 提供`info`, `warn`, `error`函数，用于打印带颜色和时间戳的日志。
    *   **FR5.1.2 (命令检查)**: 提供`check_command`函数，用于检查`docker`, `buf`, `go`等必需的命令是否存在于`$PATH`中。
*   **使用**: 其他脚本通过`source ./lib/helpers.sh`来引入。

#### 5.2. `gen-proto.sh`: API代码生成脚本
*   **职责**: 一键式调用`buf`来生成所有gRPC/Protobuf相关的代码。
*   **功能需求**:
    *   **FR5.2.1 (命令检查)**: 必须首先检查`buf`命令是否存在。
    *   **FR5.2.2 (执行生成)**: 执行`buf generate core/api/proto/v1`命令。
    *   **FR5.2.3 (错误处理)**: 如果`buf`命令失败，必须以非零状态码退出，并显示`buf`的错误输出。

#### 5.3. `gen-gomobile.sh`: Go Mobile库编译脚本
*   **职责**: 将`/core`目录编译为Android (`.aar`)和iOS (`.xcframework`)库。
*   **功能需求**:
    *   **FR5.3.1 (环境检查)**: 检查`gomobile`命令是否存在，以及是否已正确设置Android NDK和iOS Xcode环境。
    *   **FR5.3.2 (编译Android)**: 执行`gomobile bind -target=android -o apps/mobile/android/libs/core-go.aar cinaclub.com/core/...`。
    *   **FR5.3.3 (编译iOS)**: 执行`gomobile bind -target=ios -o apps/mobile/ios/CoreGo.xcframework cinaclub.com/core/...`。

#### 5.4. `gen-wasm.sh`: WebAssembly编译脚本
*   **职责**: 将`/core`目录编译为WASM模块。
*   **功能需求**:
    *   **FR5.4.1 (编译)**: 执行`GOOS=js GOARCH=wasm go build -o apps/web/static/core-go.wasm cinaclub.com/core/exports_wasm.go`。
    *   **FR5.4.2 (复制胶水代码)**: 将Go SDK中的`wasm_exec.js`文件复制到`apps/web/src/lib/wasm/`目录。

#### 5.5. `db/migrate.sh`: 数据库迁移脚本
*   **职责**: 为指定的服务运行数据库迁移。
*   **功能需求**:
    *   **FR5.5.1 (参数化)**: 必须接收参数来指定目标服务和操作，如`./scripts/db/migrate.sh user-core-service up`。
    *   **FR5.5.2 (配置加载)**: 能够读取目标服务的开发环境配置文件，以获取数据库DSN。
    *   **FR5.5.3 (工具调用)**: 调用`golang-migrate` CLI工具，并传入正确的参数（数据库URL、迁移文件路径）。

#### 5.6. `setup-dev-env.sh`: 本地开发环境设置脚本
*   **职责**: 帮助新开发者快速设置好本地开发所需的一切。
*   **功能需求**:
    *   **FR5.6.1 (依赖检查)**: 检查并提示安装所有必需的工具（`go`, `docker`, `docker-compose`, `buf`, `golang-migrate`, `node`, `pnpm`等）。
    *   **FR5.6.2 (Git Hooks)**: 安装Git pre-commit钩子，用于在提交前自动运行`lint.sh`。
    *   **FR5.6.3 (代码生成)**: 自动调用`gen-proto.sh`等代码生成脚本，确保本地代码是最新的。
    *   **FR5.6.4 (启动容器)**: (可选) 提示用户是否要立即运行`docker-compose up -d`来启动本地依赖服务。

#### 5.7. `run-service.sh`: 本地运行单个服务脚本
*   **职责**: 在本地，以热重载模式启动一个指定的微服务进行调试。
*   **功能需求**:
    *   **FR5.7.1 (参数化)**: 必须接收服务名称作为参数，如`./scripts/run-service.sh user-core-service`。
    *   **FR5.7.2 (配置加载)**: 自动为该服务加载开发环境的配置文件。
    *   **FR5.7.3 (热重载)**: 使用`air`或`realize`等Go热重载工具来启动服务。`air`的配置文件 (`.air.toml`) 可以放在每个服务的根目录。

#### 5.8. `lint.sh`: 代码规范检查脚本
*   **职责**: 在整个代码库上运行所有的linter。
*   **功能需求**:
    *   **FR5.8.1 (并行执行)**: 如果可能，并行运行Go和TypeScript的lint。
    *   **FR5.8.2 (Go Lint)**: 调用`golangci-lint run ./...`。
    *   **FR5.8.3 (Protobuf Lint)**: 调用`buf lint core/api/proto/v1`。
    *   **FR5.8.4 (Frontend Lint)**: 调用`pnpm run lint`（假设在根`package.json`中定义了lint脚本）。

---

### 6. 通用需求与最佳实践

*   **FR6.1 (可执行权限)**: 所有`.sh`文件都必须有`+x`可执行权限。
*   **FR6.2 (Shebang)**: 所有脚本必须以`#!/usr/bin/env bash`开头，以保证兼容性。
*   **FR6.3 (错误中止)**: 所有脚本必须包含`set -euo pipefail`，确保在任何命令失败时立即退出。

---

### 7. 技术约束与开发规范

*   **TC7.1 (语言)**: 主要使用**Bash**。对于非常复杂的逻辑，应在`/tools`中用Go实现，然后由脚本调用。
*   **TC7.2 (依赖工具)**: 脚本可以假设开发和CI环境中已安装了`go`, `docker`, `buf`等核心工具。`setup-dev-env.sh`负责检查和提示安装。
*   **TC7.3 (开发规范)**:
    *   **禁止硬编码**: 严禁在脚本中硬编码路径、版本号等。应使用相对路径或环境变量。
    *   **清晰的注释**: 为复杂的命令或逻辑块添加清晰的注释。

---
这份SRS为`/scripts`目录的设计和管理提供了坚实、全面的指导。通过建立这样一套标准化的、健壮的脚本库，并以`Makefile`或`Taskfile`作为统一入口，CINA.CLUB平台可以极大地简化开发流程，提高自动化水平，并确保工程实践的一致性。