好的，收到您的指令。这个需求是对前端架构进行一次重大升级，从原先的React Native为主，演进为一个**更宏大、更具平台原生体验的“多技术栈、统一核心”架构**。新的架构需要分别针对Android/HarmonyOS、Apple生态、以及Windows平台提供最佳解决方案，同时还要保持核心逻辑的统一。

我将分两部分来完成您的请求：
1.  **重新设计并细化前端架构 (`apps/`)**，以适应新的多平台战略。
2.  **重新生成 `/scripts` 目录的SRS**，以支持这个新的、更复杂的构建和编译流程。



---
### 第二部分: `/scripts` 目录的全新SRS (v2.0)

为了支持上述更复杂的、多目标的编译流程，`/scripts`目录需要进行全面的重构和增强。

---
### CINA.CLUB - 开发与运维脚本 (`/scripts`) 需求规格说明书

**版本: 2.0 (多平台原生构建)**  
**发布日期: 2025-06-27**  
**文档负责人:** [平台工程/DevOps负责人]
**审批人:** [CTO]

#### 1. 引言

#### 1.1. 项目背景与目的
随着CINA.CLUB前端架构演进为面向多原生平台（Android/Kotlin, Apple/Swift, Web/WASM, Windows/C#），编译和构建流程变得空前复杂。`/scripts` 目录的目的在于提供一套**强大、可靠、参数化的构建与编排脚本**，将为不同平台编译共享核心库 (`/core`)、生成API代码、以及运行各平台特定构建任务的复杂操作，封装成简单、统一的命令。

#### 1.2. 范围与边界
*   **范围之内**:
    *   封装对`buf`, `gomobile`, Go WASM/C-shared编译器等工具的调用。
    *   管理和执行针对**所有四个前端平台**的代码生成和库编译。
    *   提供数据库迁移、环境设置、代码检查等通用运维脚本。
*   **范围之外**:
    *   特定平台的IDE构建流程（如Xcode Build, Gradle Build）。脚本只负责准备好它们所需的库和代码。

#### 2. 核心设计原则

*   **目标驱动 (Target-Driven)**: 所有编译脚本都必须接受一个或多个目标平台的参数（如`--target=android,ios`）。
*   **统一入口**: 使用`Makefile`或`Taskfile.yml`作为所有脚本的统一入口，如`task build:core --target=android`。
*   **错误处理与日志**: 保持健壮的错误处理和清晰的日志输出。

---

#### 3. 目录与文件结构 (v2.0)

```
scripts/
├── lib/
│   └── helpers.sh
├── gen/                      # ✨ 代码与库生成脚本 ✨
│   ├── proto.sh              # 1. 生成所有Protobuf代码
│   └── core.sh               # 2. 编译核心Go库 (多目标)
├── db/                       # 3. 数据库脚本
│   ├── migrate.sh
│   └── seed.sh
├── setup/                    # 4. 环境设置脚本
│   └── dev-env.sh
└── ci/                       # 5. CI专用脚本
    ├── lint.sh
    └── test.sh
```
**设计变更**: 将所有生成相关的脚本统一到`gen/`目录下，使职责更清晰。

---

#### 4. 功能需求 (按新脚本结构拆分)

##### 4.1. `gen/proto.sh`: API代码生成脚本
*   **职责**: 调用`buf`为**所有目标语言**生成API客户端和消息类型。
*   **功能需求**:
    *   **FR4.1.1 (多语言生成)**: 脚本内部调用`buf generate core/api/proto/v1`。`buf.gen.yaml`文件中必须配置好所有目标语言的生成规则：
        *   Go (`protoc-gen-go`, `protoc-gen-go-grpc`) -> 输出到各`services/*/gen/`
        *   TypeScript (`protoc-gen-ts`, `protoc-gen-grpc-web`) -> 输出到`apps/web/src/lib/gen/`
        *   Swift (`protoc-gen-swift`, `protoc-gen-grpc-swift`) -> 输出到`apps/apple/Shared/Generated/`
        *   Kotlin (`protoc-gen-grpc-kotlin`) -> 输出到`apps/android/app/build/generated/source/proto/`
    *   **FR4.1.2 (单命令)**: 开发者只需运行`task gen:proto`即可完成所有平台的API代码更新。

##### 4.2. `gen/core.sh`: 核心Go库编译脚本
*   **职责**: 将`/core`目录编译成所有前端平台所需的库文件。**这是新架构的核心脚本**。
*   **功能需求**:
    *   **FR4.2.1 (参数化)**: 必须接受`--target`参数，可以是`android`, `ios`, `wasm`, `windows`, 或`all`。
    *   **FR4.2.2 (Android目标)**:
        *   当`--target=android`时，执行`gomobile bind -target=android -o apps/android/app/libs/core-go.aar cinaclub.com/core`。
    *   **FR4.2.3 (Apple/iOS目标)**:
        *   当`--target=ios`时，执行`gomobile bind -target=ios -o apps/apple/Frameworks/CoreGo.xcframework cinaclub.com/core`。
    *   **FR4.2.4 (Web/WASM目标)**:
        *   当`--target=wasm`时，执行`GOOS=js GOARCH=wasm go build -o apps/web/static/core.wasm cinaclub.com/core/exports/wasm`。
    *   **FR4.2.5 (Windows目标)**:
        *   当`--target=windows`时，执行`go build -buildmode=c-shared -o apps/windows/libs/core-go.dll cinaclub.com/core/exports/c`。
    *   **FR4.2.6 (输出管理)**: 脚本必须确保编译产物被精确地放置到各个前端应用项目所需的位置。

##### 4.3. `db/migrate.sh`: 数据库迁移脚本
*   (与之前版本基本一致) 负责运行`golang-migrate`。

##### 4.4. `setup/dev-env.sh`: 本地开发环境设置脚本
*   **职责**: 一键式配置好一个完整的、可进行**所有平台**开发的本地环境。
*   **功能需求**:
    *   **FR4.4.1 (依赖检查增强)**: 除了Go/Docker/Node等，还需检查并提示安装：
        *   Android Studio 和 NDK。
        *   Xcode 和 Command Line Tools。
        *   .NET SDK 和 WinUI 3 开发环境。
    *   **FR4.4.2 (全量生成)**: 自动调用`task gen:proto`和`task gen:core --target=all`，为所有平台准备好初始代码和库。
    *   **FR4.4.3 (IDE配置)**: (可选) 自动生成或更新`.vscode/launch.json`，为调试各个前端应用和Go服务创建配置。

##### 4.5. `ci/lint.sh` & `ci/test.sh`: CI专用脚本
*   **职责**: 在CI环境中执行代码检查和测试。
*   **功能需求**:
    *   **`lint.sh`**: 运行`golangci-lint`, `buf lint`, `eslint`, `swiftlint`等所有平台的linter。
    *   **`test.sh`**:
        *   **参数化**: `test.sh --target=backend` 只运行Go后端测试。`test.sh --target=android` 只运行Android单元测试。
        *   **统一报告**: 脚本应将所有测试结果转换为统一的格式（如JUnit XML），以便CI平台解析和展示。

---
### 5. 总结

这个**版本2.0**的`/scripts`架构，是为了支撑**多原生平台**的前端战略而设计的。

*   **核心变更**: 将代码/库生成逻辑集中到`/gen`目录，并使核心编译脚本`gen/core.sh`**高度参数化和目标驱动**。
*   **开发者体验**: 开发者通过`Makefile`或`Taskfile.yml`提供的简单命令（如`task gen:all`, `task build:android`），即可完成复杂的跨平台构建任务，而无需关心底层的编译器标志和路径配置。
*   **CI/CD集成**: CI/CD流水线可以精确地调用这些脚本，实现对特定平台的、高效的持续集成和构建。例如，一个只修改了iOS UI的PR，CI可以只触发`task build:core --target=ios`和iOS应用的测试，而无需构建所有其他平台。

这套脚本架构为CINA.CLUB宏大的、多平台原生的前端战略提供了坚实、高效、可自动化的工程基础。