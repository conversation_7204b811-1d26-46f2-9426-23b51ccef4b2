好的，遵照您的指示。我将为您生成一份专门针对 **`chat-api-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`chat-api-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**海量消息的存储与查询性能、与`chat-websocket-server`的动静分离协同、高级群聊权限管理，以及与搜索、审核等服务的集成**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `chat-api-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `chat-api-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 事件驱动 + 为时间线数据优化的仓储

## 1. 概述

`chat-api-service` 是CINA.CLUB聊天系统的“**历史档案馆**”和“**管理中心**”。其核心挑战在于：
1.  **海量数据写入与存储**: 需要能承载平台所有聊天消息的持久化，数据量会以极快的速度增长。
2.  **高性能时间线读取**: 查询历史消息（典型的“下拉加载更多”）必须非常高效。
3.  **与实时层的解耦**: 必须与`chat-websocket-server`形成清晰的“动静分离”，由实时层处理连接和广播，本服务处理持久化和非实时管理。
4.  **复杂的权限逻辑**: 群聊管理（如谁能踢人、谁能禁言）需要精确的、基于角色的权限检查。
5.  **丰富的消息类型**: 需要支持文本、图片、卡片等多种结构化消息，并能被搜索和审核。

本架构设计通过采用**整洁架构**，并为数据持久化层选择**对时间线模型友好的数据库（如分布式SQL或NoSQL）**，以及通过**事件驱动**与平台其他服务解耦，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (动静分离与事件驱动)

```mermaid
graph TD
    subgraph "实时层 (Real-time Layer)"
        A[chat-websocket-server]
    end

    subgraph "平台事件总线 (Kafka)"
        B[RawChatMessageEvent]
        C[ChatMessagePublishedEvent]
    end

    subgraph "ChatAPIService (This Service)"
        D[Kafka Consumer<br/><em>(adapter/event)</em>]
        E[Application Service<br/><em>(application/service)</em>]
        F[Domain Logic<br/><em>(domain/aggregate, domain/service)</em>]
        G[Repository<br/><em>(adapter/repository)</em>]
        H[gRPC/REST API<br/><em>(adapter/transport)</em>]
        I[Kafka Producer<br/><em>(adapter/event)</em>]
    end
    
    subgraph "下游服务"
        J[Client App]
        K[search-indexer-service]
        L[notification-dispatch-service]
        M[content-moderation-service]
    end

    %% Flow
    A -- "1. 发送原始消息" --> B
    B -- "2. 消费" --> D
    D -- "调用" --> E
    E -- "使用" --> F
    F -- "持久化" --> G
    E -- "3. 发布已持久化消息" --> I
    I --> C
    
    C -- "4a. 索引" --> K
    C -- "4b. 触发@通知" --> L
    C -- "4c. 审核" --> M
    
    J -- "5. 查询历史/管理群" --> H
    H -- "调用" --> E
```

### 2.2 最终目录结构 (`services/chat-api-service/`)

```
chat-api-service/
├── cmd/server/
│   └── main.go                 # API服务和事件消费者的统一启动入口
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   └── handler.go      # gRPC Handler实现
│   │   ├── event/
│   │   │   ├── producer.go     # Kafka生产者
│   │   │   └── message_consumer.go # 消费来自WebSocket服务的原始消息
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型
│   │       ├── repo.go         # 实现了所有仓储接口的struct
│   │       ├── room_repo.go    # 聊天室仓储实现
│   │       └── message_repo.go # ✨ 消息仓储实现 (为时间线优化) ✨
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── chat_service.go # 核心应用服务实现
│   └── domain/
│       ├── aggregate/
│       │   └── room_aggregate.go # ✨ 封装群聊管理的核心业务规则 ✨
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── permission_service.go # ✨ 权限检查领域服务 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Communication Rules)

*   `domain/model/`: 使用`/core/models`中与聊天相关的`struct`，如`ChatMessage`, `ChatRoom`。
*   **`domain/service/permission_service.go`**: **这是封装复杂群聊权限检查的核心**。
    *   **`PermissionService` struct**: 一个无状态的领域服务。
    *   **`CanKickMember(actor, target, room)`**, **`CanMuteMember(...)`**, **`CanUpdateRoomInfo(...)`**等方法。
    *   **逻辑**: 这些方法接收操作者(actor)、目标(target)和房间聚合根(room aggregate)作为参数，并根据`room_members`表中的角色信息（`OWNER`, `ADMIN`, `MEMBER`），返回一个布尔值和/或一个错误。它封装了“群主可以踢管理员，但管理员不能踢群主”这类业务规则。
*   **`domain/aggregate/room_aggregate.go`**:
    *   **`Room`聚合根**: 封装了`ChatRoom`及其成员列表`RoomMembers`。
    *   **方法**: `AddMember()`, `RemoveMember()`, `UpdateRole()`等。
    *   **工作方式**: 在执行任何变更前，这些方法会先调用`PermissionService`来校验操作者的权限。校验通过后，才修改聚合根内部的状态，并记录一个领域事件（如`MemberKickedEvent`）。

### 3.2 `application/` - 应用层 (The Use Cases)

*   `application/port/`: 定义`Repository`和`ChatService`接口。
*   **`application/service/chat_service.go`**: 实现`ChatService`接口，是所有业务流程的编排者。
    *   **`PersistMessage(ctx, rawMsg)` (由事件消费者调用)**:
        1.  解析`rawMsg`，识别`@mentions`等。
        2.  调用`repository.CreateMessage(...)`将消息持久化。
        3.  原子性地更新`rooms`表的`last_message`和`updated_at`字段。
        4.  **发布`ChatMessagePublishedEvent`**到Kafka，通知下游服务（搜索、审核、通知）。
    *   **`CreateGroupRoom(ctx, creatorID, memberIDs, ...)`**:
        1.  开启工作单元（事务）。
        2.  调用`repository.CreateRoom()`和`repository.AddMembers()`。
        3.  提交事务。
    *   **`KickMember(ctx, actorID, targetID, roomID)`**:
        1.  从仓储中加载`Room`聚合根。
        2.  **调用`roomAggregate.RemoveMember(actorID, targetID)`**。聚合根内部会调用`PermissionService`进行权限检查。
        3.  如果成功，则调用仓储持久化变更。
    *   **`GetHistoricalMessages(ctx, roomID, beforeMessageID, limit)`**:
        1.  调用`message_repo.ListByRoomID(...)`来获取分页的历史消息。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**: **数据持久化层，为性能优化的关键**。
    *   **数据库选型演进**:
        *   **阶段1 (PostgreSQL)**:
            *   `chat_messages`表**必须**使用**分区(Partitioning)**，例如按`room_id`进行哈希分区，或按`timestamp`进行范围分区。
            *   在`(room_id, timestamp DESC)`上创建复合索引，以优化历史消息的分页查询。
        *   **阶段2 (分布式SQL - TiDB/CockroachDB)**:
            *   当单机PostgreSQL的写入或存储容量成为瓶颈时，平滑迁移到分布式SQL。这能水平扩展存储和写入能力。
        *   **阶段3 (NoSQL - Cassandra/ScyllaDB)**:
            *   对于极致的写入性能和可扩展性，可以考虑迁移到NoSQL。`Partition Key`为`room_id`, `Clustering Key`为`message_id` (TimeUUID)，可以实现极高的写吞吐和高效的时间线查询。
    *   `message_repo.go`: 实现`ListByRoomID`时，使用**Keyset Pagination (Seek Method)**而不是传统的`OFFSET`分页，以避免深度分页时的性能下降。查询条件是`WHERE room_id = ? AND timestamp < ? ORDER BY timestamp DESC LIMIT ?`。

*   **`adapter/event/`**:
    *   `message_consumer.go`:
        *   消费来自`chat-websocket-server`的`RawChatMessageEvent`。
        *   它是一个**高吞吐量**的消费者，可以配置多个实例并行处理。
        *   收到消息后，调用`application.ChatService.PersistMessage()`。
    *   `producer.go`:
        *   提供一个`EventProducer`，供`ChatService`在消息持久化后，发布`ChatMessagePublishedEvent`。

*   **`adapter/grpc/`**:
    *   实现所有非实时的管理API，如创建群聊、拉人、踢人、获取会话列表、获取历史消息等。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`chat-api-service`：
1.  **动静分离**: 通过Kafka事件总线，与`chat-websocket-server`实现了完美的动静分离。实时层只负责快速转发，持久化和后续处理的复杂性全部由本服务承担。
2.  **为时间线优化的数据层**: 明确了从分区表到分布式SQL再到NoSQL的数据库演进路径，并采用Keyset Pagination，确保了核心功能（拉取历史消息）的长期高性能。
3.  **领域驱动的权限管理**: 将复杂的群聊权限逻辑封装在`PermissionService`和`Room`聚合根中，使得业务规则清晰、内聚、易于测试和修改。
4.  **事件驱动的下游集成**: 在消息持久化后，通过发布标准化的`ChatMessagePublishedEvent`，优雅地与搜索、审核、通知等多个下游系统解耦。

这种架构确保了`chat-api-service`在承载海量消息数据和高并发管理操作的同时，依然保持了代码的清晰、逻辑的健壮和系统的高可扩展性，为CINA.CLUB平台提供了世界一流的通信数据中枢。