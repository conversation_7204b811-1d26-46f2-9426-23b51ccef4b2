/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-01 10:30:00
Modified: 2025-01-01 10:30:00
*/

package aic

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// ModelVersion represents a semantic version for AI models
type ModelVersion struct {
	Major int `json:"major"`
	Minor int `json:"minor"`
	Patch int `json:"patch"`
}

// String returns the string representation of the version
func (v ModelVersion) String() string {
	return fmt.Sprintf("%d.%d.%d", v.Major, v.Minor, v.Patch)
}

// IsCompatible checks if this version is compatible with another version
func (v ModelVersion) IsCompatible(other ModelVersion) bool {
	return v.Major == other.Major
}

// ModelMetadata contains comprehensive information about a model
type ModelMetadata struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	Version         ModelVersion           `json:"version"`
	Type            ModelType              `json:"type"`
	Provider        string                 `json:"provider"`
	Description     string                 `json:"description"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	Size            int64                  `json:"size_bytes"`
	Checksum        string                 `json:"checksum"`
	Dependencies    []string               `json:"dependencies"`
	Capabilities    ModelCapabilities      `json:"capabilities"`
	Performance     ModelPerformance       `json:"performance"`
	Compatibility   []ModelVersion         `json:"compatible_versions"`
	Tags            []string               `json:"tags"`
	Status          ModelStatus            `json:"status"`
	Configuration   map[string]interface{} `json:"configuration"`
	ValidationScore float64                `json:"validation_score"`
}

// ModelStatus represents the current status of a model
type ModelStatus string

const (
	ModelStatusDraft      ModelStatus = "DRAFT"
	ModelStatusValidating ModelStatus = "VALIDATING"
	ModelStatusReady      ModelStatus = "READY"
	ModelStatusDeprecated ModelStatus = "DEPRECATED"
	ModelStatusError      ModelStatus = "ERROR"
)

// ModelPerformance contains performance metrics for a model
type ModelPerformance struct {
	Latency         PerformanceMetric  `json:"latency"`
	Throughput      PerformanceMetric  `json:"throughput"`
	MemoryUsage     PerformanceMetric  `json:"memory_usage"`
	CPUUsage        PerformanceMetric  `json:"cpu_usage"`
	Accuracy        float64            `json:"accuracy"`
	F1Score         float64            `json:"f1_score"`
	BenchmarkScores map[string]float64 `json:"benchmark_scores"`
	LastBenchmark   time.Time          `json:"last_benchmark"`
}

// PerformanceMetric represents a performance measurement
type PerformanceMetric struct {
	Average float64 `json:"average"`
	Min     float64 `json:"min"`
	Max     float64 `json:"max"`
	P95     float64 `json:"p95"`
	P99     float64 `json:"p99"`
	Unit    string  `json:"unit"`
}

// ModelRegistry manages AI model lifecycle and metadata
type ModelRegistry interface {
	// Model Management
	RegisterModel(ctx context.Context, metadata ModelMetadata) error
	UnregisterModel(ctx context.Context, modelID string) error
	GetModel(ctx context.Context, modelID string) (*ModelMetadata, error)
	ListModels(ctx context.Context, filters ModelFilters) ([]ModelMetadata, error)
	UpdateModelMetadata(ctx context.Context, modelID string, metadata ModelMetadata) error

	// Version Management
	CreateModelVersion(ctx context.Context, modelID string, version ModelVersion) error
	GetModelVersions(ctx context.Context, modelID string) ([]ModelVersion, error)
	SetActiveVersion(ctx context.Context, modelID string, version ModelVersion) error
	CheckCompatibility(ctx context.Context, modelID string, requiredVersion ModelVersion) (bool, error)

	// Performance Monitoring
	RecordPerformanceMetrics(ctx context.Context, modelID string, metrics ModelPerformance) error
	GetPerformanceHistory(ctx context.Context, modelID string, timeRange TimeRange) ([]ModelPerformance, error)
	BenchmarkModel(ctx context.Context, modelID string) (*ModelPerformance, error)

	// Health and Validation
	ValidateModel(ctx context.Context, modelID string) (*ValidationResult, error)
	GetModelHealth(ctx context.Context, modelID string) (*HealthStatus, error)
	PerformHealthCheck(ctx context.Context) (*RegistryHealthStatus, error)
}

// ModelFilters defines filtering options for model queries
type ModelFilters struct {
	Type         *ModelType    `json:"type,omitempty"`
	Provider     *string       `json:"provider,omitempty"`
	Status       *ModelStatus  `json:"status,omitempty"`
	Tags         []string      `json:"tags,omitempty"`
	MinVersion   *ModelVersion `json:"min_version,omitempty"`
	MaxVersion   *ModelVersion `json:"max_version,omitempty"`
	CreatedAfter *time.Time    `json:"created_after,omitempty"`
	Limit        int           `json:"limit,omitempty"`
	Offset       int           `json:"offset,omitempty"`
}

// TimeRange represents a time period for queries
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// ValidationResult contains model validation information
type ValidationResult struct {
	IsValid     bool                   `json:"is_valid"`
	Score       float64                `json:"score"`
	Issues      []ValidationIssue      `json:"issues"`
	Suggestions []string               `json:"suggestions"`
	ValidatedAt time.Time              `json:"validated_at"`
	Details     map[string]interface{} `json:"details"`
}

// ValidationIssue represents a validation problem
type ValidationIssue struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"`
	Description string `json:"description"`
	Location    string `json:"location"`
	Suggestion  string `json:"suggestion"`
}

// HealthStatus represents the health of a model
type HealthStatus struct {
	IsHealthy       bool               `json:"is_healthy"`
	Status          string             `json:"status"`
	LastCheck       time.Time          `json:"last_check"`
	ResponseTime    time.Duration      `json:"response_time"`
	ErrorRate       float64            `json:"error_rate"`
	MemoryUsage     int64              `json:"memory_usage"`
	CPUUsage        float64            `json:"cpu_usage"`
	ActiveRequests  int                `json:"active_requests"`
	Issues          []string           `json:"issues"`
	Recommendations []string           `json:"recommendations"`
	Metrics         map[string]float64 `json:"metrics"`
}

// RegistryHealthStatus represents the overall health of the model registry
type RegistryHealthStatus struct {
	IsHealthy       bool                    `json:"is_healthy"`
	TotalModels     int                     `json:"total_models"`
	HealthyModels   int                     `json:"healthy_models"`
	FailedModels    int                     `json:"failed_models"`
	LastCheck       time.Time               `json:"last_check"`
	SystemMetrics   map[string]float64      `json:"system_metrics"`
	ModelStatuses   map[string]HealthStatus `json:"model_statuses"`
	Recommendations []string                `json:"recommendations"`
}

// DefaultModelRegistry implements the ModelRegistry interface
type DefaultModelRegistry struct {
	models        map[string]*ModelMetadata
	performance   map[string][]ModelPerformance
	health        map[string]*HealthStatus
	mu            sync.RWMutex
	validator     ModelValidator
	benchmarker   ModelBenchmarker
	healthChecker HealthChecker
}

// ModelValidator validates model integrity and compliance
type ModelValidator interface {
	Validate(ctx context.Context, metadata ModelMetadata) (*ValidationResult, error)
}

// ModelBenchmarker performs model performance benchmarking
type ModelBenchmarker interface {
	Benchmark(ctx context.Context, modelID string) (*ModelPerformance, error)
}

// HealthChecker monitors model health
type HealthChecker interface {
	CheckHealth(ctx context.Context, modelID string) (*HealthStatus, error)
}

// NewDefaultModelRegistry creates a new model registry instance
func NewDefaultModelRegistry(validator ModelValidator, benchmarker ModelBenchmarker, healthChecker HealthChecker) *DefaultModelRegistry {
	return &DefaultModelRegistry{
		models:        make(map[string]*ModelMetadata),
		performance:   make(map[string][]ModelPerformance),
		health:        make(map[string]*HealthStatus),
		validator:     validator,
		benchmarker:   benchmarker,
		healthChecker: healthChecker,
	}
}

// RegisterModel registers a new model in the registry
func (r *DefaultModelRegistry) RegisterModel(ctx context.Context, metadata ModelMetadata) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Validate the model before registration
	if r.validator != nil {
		validation, err := r.validator.Validate(ctx, metadata)
		if err != nil {
			return fmt.Errorf("model validation failed: %w", err)
		}
		if !validation.IsValid {
			return fmt.Errorf("model validation failed: %s", validation.Issues[0].Description)
		}
		metadata.ValidationScore = validation.Score
	}

	metadata.CreatedAt = time.Now()
	metadata.UpdatedAt = time.Now()
	metadata.Status = ModelStatusReady

	r.models[metadata.ID] = &metadata
	return nil
}

// GetModel retrieves a model by ID
func (r *DefaultModelRegistry) GetModel(ctx context.Context, modelID string) (*ModelMetadata, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	model, exists := r.models[modelID]
	if !exists {
		return nil, fmt.Errorf("model not found: %s", modelID)
	}

	// Return a copy to prevent external modifications
	modelCopy := *model
	return &modelCopy, nil
}

// ListModels returns a filtered list of models
func (r *DefaultModelRegistry) ListModels(ctx context.Context, filters ModelFilters) ([]ModelMetadata, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []ModelMetadata
	for _, model := range r.models {
		if r.matchesFilters(model, filters) {
			result = append(result, *model)
		}
	}

	// Apply pagination
	if filters.Offset > 0 && filters.Offset < len(result) {
		result = result[filters.Offset:]
	}
	if filters.Limit > 0 && filters.Limit < len(result) {
		result = result[:filters.Limit]
	}

	return result, nil
}

// matchesFilters checks if a model matches the given filters
func (r *DefaultModelRegistry) matchesFilters(model *ModelMetadata, filters ModelFilters) bool {
	if filters.Type != nil && model.Type != *filters.Type {
		return false
	}
	if filters.Provider != nil && model.Provider != *filters.Provider {
		return false
	}
	if filters.Status != nil && model.Status != *filters.Status {
		return false
	}
	if filters.MinVersion != nil && !model.Version.IsCompatible(*filters.MinVersion) {
		return false
	}
	if filters.CreatedAfter != nil && model.CreatedAt.Before(*filters.CreatedAfter) {
		return false
	}
	if len(filters.Tags) > 0 {
		tagMap := make(map[string]bool)
		for _, tag := range model.Tags {
			tagMap[tag] = true
		}
		for _, requiredTag := range filters.Tags {
			if !tagMap[requiredTag] {
				return false
			}
		}
	}
	return true
}

// RecordPerformanceMetrics records performance data for a model
func (r *DefaultModelRegistry) RecordPerformanceMetrics(ctx context.Context, modelID string, metrics ModelPerformance) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	metrics.LastBenchmark = time.Now()
	r.performance[modelID] = append(r.performance[modelID], metrics)

	// Keep only the last 1000 performance records
	if len(r.performance[modelID]) > 1000 {
		r.performance[modelID] = r.performance[modelID][len(r.performance[modelID])-1000:]
	}

	return nil
}

// GetPerformanceHistory retrieves performance history for a model
func (r *DefaultModelRegistry) GetPerformanceHistory(ctx context.Context, modelID string, timeRange TimeRange) ([]ModelPerformance, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	history, exists := r.performance[modelID]
	if !exists {
		return []ModelPerformance{}, nil
	}

	var result []ModelPerformance
	for _, perf := range history {
		if perf.LastBenchmark.After(timeRange.Start) && perf.LastBenchmark.Before(timeRange.End) {
			result = append(result, perf)
		}
	}

	return result, nil
}

// BenchmarkModel performs a comprehensive benchmark of a model
func (r *DefaultModelRegistry) BenchmarkModel(ctx context.Context, modelID string) (*ModelPerformance, error) {
	if r.benchmarker == nil {
		return nil, fmt.Errorf("no benchmarker configured")
	}

	performance, err := r.benchmarker.Benchmark(ctx, modelID)
	if err != nil {
		return nil, fmt.Errorf("benchmarking failed: %w", err)
	}

	// Record the benchmark results
	if err := r.RecordPerformanceMetrics(ctx, modelID, *performance); err != nil {
		return nil, fmt.Errorf("failed to record benchmark results: %w", err)
	}

	return performance, nil
}

// GetModelHealth retrieves the current health status of a model
func (r *DefaultModelRegistry) GetModelHealth(ctx context.Context, modelID string) (*HealthStatus, error) {
	r.mu.RLock()
	health, exists := r.health[modelID]
	r.mu.RUnlock()

	if !exists || time.Since(health.LastCheck) > 5*time.Minute {
		// Refresh health status
		if r.healthChecker != nil {
			newHealth, err := r.healthChecker.CheckHealth(ctx, modelID)
			if err != nil {
				return nil, fmt.Errorf("health check failed: %w", err)
			}

			r.mu.Lock()
			r.health[modelID] = newHealth
			r.mu.Unlock()

			return newHealth, nil
		}
	}

	if health == nil {
		return &HealthStatus{
			IsHealthy: false,
			Status:    "Unknown",
			LastCheck: time.Now(),
		}, nil
	}

	// Return a copy
	healthCopy := *health
	return &healthCopy, nil
}

// UnregisterModel removes a model from the registry
func (r *DefaultModelRegistry) UnregisterModel(ctx context.Context, modelID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	delete(r.models, modelID)
	delete(r.performance, modelID)
	delete(r.health, modelID)

	return nil
}

// UpdateModelMetadata updates the metadata of an existing model
func (r *DefaultModelRegistry) UpdateModelMetadata(ctx context.Context, modelID string, metadata ModelMetadata) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	_, exists := r.models[modelID]
	if !exists {
		return fmt.Errorf("model not found: %s", modelID)
	}

	metadata.UpdatedAt = time.Now()
	r.models[modelID] = &metadata

	return nil
}

// CreateModelVersion creates a new version of an existing model
func (r *DefaultModelRegistry) CreateModelVersion(ctx context.Context, modelID string, version ModelVersion) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	model, exists := r.models[modelID]
	if !exists {
		return fmt.Errorf("model not found: %s", modelID)
	}

	// Add version to compatibility list if not already present
	for _, v := range model.Compatibility {
		if v == version {
			return fmt.Errorf("version %s already exists", version.String())
		}
	}

	model.Compatibility = append(model.Compatibility, version)
	model.UpdatedAt = time.Now()

	return nil
}

// GetModelVersions returns all versions of a model
func (r *DefaultModelRegistry) GetModelVersions(ctx context.Context, modelID string) ([]ModelVersion, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	model, exists := r.models[modelID]
	if !exists {
		return nil, fmt.Errorf("model not found: %s", modelID)
	}

	return append([]ModelVersion{}, model.Compatibility...), nil
}

// SetActiveVersion sets the active version of a model
func (r *DefaultModelRegistry) SetActiveVersion(ctx context.Context, modelID string, version ModelVersion) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	model, exists := r.models[modelID]
	if !exists {
		return fmt.Errorf("model not found: %s", modelID)
	}

	model.Version = version
	model.UpdatedAt = time.Now()

	return nil
}

// CheckCompatibility checks if a model is compatible with a required version
func (r *DefaultModelRegistry) CheckCompatibility(ctx context.Context, modelID string, requiredVersion ModelVersion) (bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	model, exists := r.models[modelID]
	if !exists {
		return false, fmt.Errorf("model not found: %s", modelID)
	}

	return model.Version.IsCompatible(requiredVersion), nil
}

// ValidateModel validates a model using the configured validator
func (r *DefaultModelRegistry) ValidateModel(ctx context.Context, modelID string) (*ValidationResult, error) {
	if r.validator == nil {
		return nil, fmt.Errorf("no validator configured")
	}

	model, err := r.GetModel(ctx, modelID)
	if err != nil {
		return nil, err
	}

	return r.validator.Validate(ctx, *model)
}

// PerformHealthCheck performs a comprehensive health check of the registry
func (r *DefaultModelRegistry) PerformHealthCheck(ctx context.Context) (*RegistryHealthStatus, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	status := &RegistryHealthStatus{
		IsHealthy:     true,
		TotalModels:   len(r.models),
		HealthyModels: 0,
		FailedModels:  0,
		LastCheck:     time.Now(),
		SystemMetrics: make(map[string]float64),
		ModelStatuses: make(map[string]HealthStatus),
	}

	// Check each model's health
	for modelID := range r.models {
		health, err := r.GetModelHealth(ctx, modelID)
		if err != nil {
			status.FailedModels++
			status.IsHealthy = false
			continue
		}

		status.ModelStatuses[modelID] = *health
		if health.IsHealthy {
			status.HealthyModels++
		} else {
			status.FailedModels++
			status.IsHealthy = false
		}
	}

	// Add system-level metrics
	status.SystemMetrics["registry_uptime"] = time.Since(time.Now().Add(-24 * time.Hour)).Hours()
	status.SystemMetrics["model_load_factor"] = float64(status.HealthyModels) / float64(status.TotalModels)

	return status, nil
}
