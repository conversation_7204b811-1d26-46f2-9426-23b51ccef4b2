好的，遵照您的指示。我将为您生成一份专门针对 **CINA.CLUB Apple全平台生态** 的、极致细化的、生产级**架构设计文档**。

这份架构将超越单一的iOS App，它将是一个**统一的、跨Apple所有设备（iOS, iPadOS, macOS, watchOS, visionOS）的整体解决方案**。其核心理念是：**“一次核心逻辑编写，多平台原生UI适配”**。

这份架构将：
1.  **完全对齐**我们之前定义的**Go-Centric全栈架构**和**Monorepo**模式。
2.  采用苹果官方**最新的、统一的技术栈**（SwiftUI, Swift Concurrency, Combine）。
3.  通过**Go Mobile**和**Swift Package Manager**，将在Go中实现的核心逻辑（加密、同步、AI）构建成一个可被所有Apple平台共享的核心业务层。
4.  采用**模块化**和**分层**的设计，并为不同设备的特性（如macOS的菜单栏、watchOS的复杂功能）进行针对性设计。

---
### CINA.CLUB - Apple平台应用 统一架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [Apple平台架构师]

## 1. 概述与设计哲学

### 1.1 项目愿景与目的
CINA.CLUB在Apple生态中的目标是提供一个**无缝、连贯、深度集成**的用户体验，无论用户是在iPhone上聊天，在iPad上管理PKB，在Mac上进行专业服务交易，还是在Apple Watch上接收即时通知。本架构旨在构建一个**共享核心逻辑、但为每个平台提供独特原生体验**的应用套件，充分利用苹果生态的协同优势。

### 1.2 核心设计哲学
1.  **SwiftUI为本 (SwiftUI First)**: SwiftUI是构建跨所有Apple平台UI的**唯一标准**。它能天然地适配不同屏幕尺寸和交互模式。
2.  **数据与逻辑先行 (Data & Logic First)**: 应用的核心是其数据和业务逻辑，UI只是这些核心的一种表现形式。我们将构建一个强大的、与UI无关的核心业务层。
3.  **模块化与可复用 (Modularity & Reusability)**: 使用**Swift Package Manager (SPM)**将代码库拆分为多个独立的、可复用的模块（包）。
4.  **响应式与异步 (Reactive & Asynchronous)**: 全面拥抱**Combine**和**Swift Concurrency (`async/await`)**，构建现代、健壮、易于理解的异步数据流。
5.  **Go为引擎，Swift为车身 (Go as the Engine, Swift as the Body)**:
    *   **Go (via Go Mobile)**: 执行平台最核心、最普适的计算密集型和安全敏感型任务。
    *   **Swift**: 作为Apple生态的母语，负责构建所有原生UI、编排业务逻辑，并作为连接Go核心与系统框架的“粘合剂”。

---

## 2. 核心技术选型 (Apple Ecosystem)

| 领域             | 技术选型                                         | 理由                                                                 |
|------------------|--------------------------------------------------|----------------------------------------------------------------------|
| **UI框架**         | **SwiftUI**                                      | 苹果官方的、跨所有Apple平台的声明式UI框架。                           |
| **编程语言**     | **Swift 5.9+**                                   | 安全、快速、现代。                                                   |
| **核心逻辑引擎** | **Go Mobile** (编译为`CoreGo.xcframework`)         | 复用`/core`中的Go代码，保证全平台逻辑一致。                            |
| **响应式编程**   | **Combine** & **Swift Concurrency (`async/await`)** | 官方推荐的异步编程模型，SwiftUI原生支持。                            |
| **架构模式**     | **MVVM + Coordinator Pattern**                   | 清晰分离职责，提高可测试性，集中管理导航逻辑。                         |
| **API客户端**    | **gRPC-Swift**                                   | 苹果官方支持的gRPC客户端，类型安全，性能高。                           |
| **本地数据库**   | **SwiftData** (或 **Core Data**)                   | 官方对象图管理和持久化框架。SwiftData更现代，与SwiftUI集成更好。       |
| **依赖管理**     | **Swift Package Manager (SPM)**                  | 官方的、集成的依赖管理工具。                                         |
| **安全存储**     | **Keychain Services**                            | 系统级的安全存储，用于保存密钥等敏感信息。                             |

---

## 3. Monorepo模块化架构

iOS, macOS等项目将位于`apps/apple/`目录下，并共享一系列本地Swift包。

### 3.1 项目模块结构 (`apps/apple/`)

```
apple/
├── CinaClub.xcodeproj/       # Xcode项目，管理所有Target
│
├── Targets/                    # ✨ 应用目标 (Targets) ✨
│   ├── iOS/
│   │   ├── CinaClubApp.swift
│   │   └── Info.plist
│   ├── iPadOS/
│   │   └── ... (特定于iPad的配置)
│   ├── macOS/
│   │   ├── CinaClubApp.swift
│   │   ├── MainMenu.swift      # 菜单栏定义
│   │   └── Info.plist
│   ├── watchOS/
│   │   └── ... (Watch App和Complication)
│   └── visionOS/
│       └── ... (visionOS App)
│
├── Packages/                   # ✨ 共享的本地Swift包 (SPM) ✨
│   ├── AppCore/                # 1. 应用核心业务逻辑
│   ├── DesignSystem/           # 2. UI组件与设计系统
│   ├── DataLayer/              # 3. 数据层 (Repository, Network, DB)
│   ├── GoBridge/               # 4. ✨ Go核心的Swift封装层 ✨
│   └── Feature/                # 5. 功能模块
│       ├── Chat/
│       ├── Auth/
│       └── PKB/
│
└── Frameworks/
    └── CoreGo.xcframework      # 6. Go Mobile编译产物
```

### 3.2 各Swift包深度解析 (`Packages/`)

#### `Packages/GoBridge` - Go核心的Swift封装层

*   **职责**: **作为Swift世界与Go世界的唯一、安全通道**。
*   **内容**:
    *   **依赖**: 依赖`CoreGo.xcframework`。
    *   **封装类**: 为每个Go核心模块（`crypto`, `datasync`, `aic`）创建一个对应的Swift `actor`或`class`。
    *   **接口转换**: 将Go Mobile导出的C风格、基于回调或`error`返回的函数，转换为**现代、符合Swift语言习惯**的接口。
        *   **`async/await`**: `func doSomething(..., callback)` -> `func doSomething(...) async throws -> ResultType`
        *   **流式API**: Go的回调接口 -> **`AsyncThrowingStream<String, Error>`**
*   **示例 (`CryptoVault.swift`)**:
    ```swift
    import CoreGo // Go Mobile生成的模块
    
    public actor CryptoVault {
        public func encrypt(data: Data, with key: Data) async throws -> Data {
            // 将Swift的Data转换为Go的GoSlice
            let plaintextSlice = GoSlice(data: UnsafeMutableRawPointer(mutating: (data as NSData).bytes), len: data.count, cap: data.count)
            // ...
            
            // 调用Go函数
            let resultSlice = CoreGoEncryptSymmetric(keySlice, plaintextSlice)
            
            // 将返回的GoSlice转换回Swift的Data，并处理错误
            // ...
            return resultData
        }
    }
    ```

#### `Packages/DataLayer` - 数据层

*   **职责**: 封装所有数据来源（网络、本地数据库、Go核心），为上层提供统一、干净的数据访问接口。
*   **内容**:
    *   **`Network`**: 封装`gRPC-Swift`客户端，提供如`UserService`、`ChatService`等API客户端。包含一个gRPC拦截器，用于自动注入认证Token。
    *   **`Database`**: 封装**SwiftData**。定义`@Model`类（如`PKBItemEntity`），并提供`ModelContainer`。
    *   **`Repositories`**: 实现**Repository Pattern**。
        *   **`UserRepository.swift`**: 它的`fetchUser`方法会先从本地数据库读取缓存，同时发起网络请求，获取到新数据后更新数据库，并通过一个`AsyncStream`或`CurrentValueSubject` (Combine)将最新的用户数据流式地返回给调用者。

#### `Packages/AppCore` - 应用核心业务逻辑

*   **职责**: 包含与UI无关的、可被所有功能模块共享的**应用级业务逻辑**和**领域模型**。
*   **内容**:
    *   **`DomainModels`**: 定义纯Swift的`struct`来表示`User`, `Order`等。它们是从`DataLayer`的模型转换而来的，不包含任何持久化或网络细节。
    *   **`UseCases`**: 封装单一业务职责的类，如`LoginUseCase`, `PostReviewUseCase`。它们是无状态的，接收Repository接口作为依赖。
    *   **`Coordinators`**: 定义**导航逻辑**。`AppCoordinator`是根，它会创建并管理`AuthCoordinator`, `MainCoordinator`等子协调器。

#### `Packages/DesignSystem` - UI组件与设计系统

*   **职责**: CINA.CLUB在Apple生态中的视觉**单一事实来源**。
*   **内容**:
    *   **`Tokens`**: `Color.swift`, `Typography.swift`, `Spacing.swift`等，定义了设计系统的基础元素。
    *   **`Components`**: 一系列可复用的、平台自适应的**SwiftUI组件**，如`PrimaryButton`, `AvatarView`, `CardView`。
    *   **`Modifiers`**: 自定义的SwiftUI视图修饰符。

#### `Packages/Feature/*` - 功能模块

*   **职责**: 每个子包都是一个完整、垂直的功能切片。
*   **内部架构**: 每个`Feature`包内部都遵循**MVVM + Coordinator**。
    *   **`Scenes`**: 包含`View`和`ViewModel`。
    *   **`Coordinator`**: 定义该功能模块内部的导航逻辑。
    *   **`DI`**: (可选) 定义该模块向外暴露和对内注入的依赖接口。

---

## 4. 跨平台UI适配策略

SwiftUI的强大之处在于其自适应性。我们的策略是：

1.  **构建通用组件**: 在`DesignSystem`中构建的组件，如`CardView`，默认就能在iOS, iPadOS, macOS上良好地工作。
2.  **平台特定视图**: 对于需要显著不同布局的场景，我们在View文件中使用`#if os(iOS)`或`#if os(macOS)`等编译指令来提供特定平台的实现。
    ```swift
    // ProfileView.swift
    var body: some View {
        #if os(iOS)
        // 使用垂直列表布局
        List { ... }
        #elseif os(macOS)
        // 使用分栏布局
        HSplitView { ... }
        #endif
    }
    ```
3.  **设备特性利用**:
    *   **macOS**: 在`Targets/macOS/`中，实现`MenuBarExtra`来提供菜单栏快捷入口。
    *   **watchOS**: 在`Targets/watchOS/`中，为关键通知实现`Complication`。
    *   **iPadOS**: 利用`NavigationSplitView`实现多栏布局。
    *   **visionOS**: 为空间计算环境设计专门的3D交互和窗口布局。

## 5. 总结

本原生Apple平台架构是一个**统一、模块化、面向未来**的解决方案。

*   **统一核心**: 通过**Go Mobile**和**`GoBridge`**，所有Apple平台共享同一套高性能、高安全性的核心逻辑，保证了功能的一致性，并极大地减少了重复开发。
*   **现代化技术栈**: 全面拥抱SwiftUI, Combine和Swift Concurrency，使得代码简洁、健壮、易于维护，并能获得最佳性能。
*   **模块化与可扩展**: 使用**SPM**将应用拆分为多个独立的包，降低了耦合度，加速了编译，并使得团队可以并行开发不同的功能模块。
*   **真正的平台原生**: 在共享核心逻辑的基础上，为每个Apple设备（iPhone, iPad, Mac, Watch...）的独特交互范式和系统特性提供了**量身定制的原生UI和体验**。

这种架构确保了CINA.CLUB能够在整个苹果生态系统中，为用户提供一个无缝连接、体验一流的顶级原生应用套件。