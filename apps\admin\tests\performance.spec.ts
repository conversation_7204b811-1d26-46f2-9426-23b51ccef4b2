/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 19:00:00
 * Modified: 2025-01-23 19:00:00
 */

import { test, expect, devices } from '@playwright/test';
import { playAudit } from 'playwright-lighthouse';

test.describe('Performance Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Log in before each test
    await page.goto('/login');
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password');
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('Lighthouse audit for Core Web Vitals on Dashboard', async ({ page }) => {
    test.setTimeout(120000); // Increase timeout for Lighthouse audit

    await playAudit({
      page: page,
      port: 9222, // Default port for lighthouse
      thresholds: {
        performance: 80,
        accessibility: 90,
        'best-practices': 90,
        seo: 80,
      },
      reports: {
        formats: {
          html: true, // Save report as html
        },
        name: `lighthouse-report-dashboard`, // Report file name
        directory: `lighthouse-reports`, // Report directory
      }
    });
  });

  test('Large dataset rendering performance on User List', async ({ page }) => {
    await page.goto('/users');
    
    // The virtualized table should render quickly.
    // We can assert that the first row is visible within a short timeout.
    const firstRow = page.locator('.virtual-grid .virtual-table-cell').first();
    await expect(firstRow).toBeVisible({ timeout: 5000 }); // 5 second timeout
    
    // Also check that the page is responsive by interacting with it
    await page.getByRole('button', { name: '新建用户' }).click();
    await expect(page).toHaveURL(/.*\/users\/create/);
  });

  test('Mobile responsiveness on Dashboard', async ({ page, browserName }) => {
    // Use a mobile viewport
    const mobile = devices['iPhone 13'];
    await page.setViewportSize(mobile.viewport);

    await page.goto('/dashboard');

    // Check if the layout adapts correctly. 
    // For example, a mobile menu button should be visible.
    const mobileMenuButton = page.locator('.ant-layout-sider-trigger');
    await expect(mobileMenuButton).toBeVisible();

    // Take a screenshot to visually inspect the mobile layout
    await page.screenshot({ path: `screenshots/mobile-dashboard-${browserName}.png`, fullPage: true });
  });
}); 