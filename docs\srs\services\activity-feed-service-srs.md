﻿好的，遵照您的指示，我们来生成一份为 `activity-feed-service` (活动流服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **引入多Feed流概念**: 不再只有一个单一的活动流，而是支持多种类型的Feed，如“通知(Notifications)”、“互动(Interactions)”、“关注(Following)”，让用户可以分类查看。
2.  **强化聚合与降噪**: 详细定义内容的聚合（“N人赞了你”）和降噪逻辑，提升信息质量。
3.  **实时更新支持**: 明确与 `chat-websocket-server` 的联动机制，实现未读数和Feed列表的实时更新。
4.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持多Feed流和聚合功能。
5.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和扩展性指标。

这份文档将描绘一个功能强大、用户体验优秀、且能应对海量事件的活动与通知中心。

---

### CINA.CLUB - activity-feed-service 需求规格说明书

**版本: 2.0 (生产级定义，支持多Feed流与实时更新)**  
**发布日期: 2025-06-19**  
**最后修订日期: 2025-06-19**  
**文档负责人:** [用户体验/平台工程团队负责人名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了给CINA.CLUB用户提供一个统一、清晰、无干扰的方式来回顾平台上的重要动态和个人相关的通知，`activity-feed-service` 应运而生。其目的在于构建一个可靠、高性能、可查询的用户个人活动与通知中心。它通过消费平台事件，将对用户有意义的事件持久化，并通过智能聚合与分类，以**多Feed流**的形式呈现给用户，确保用户既不会错过重要信息，也不会被信息噪音淹没。

#### 1.2. 服务范围
本服务 **负责**:
*   **事件消费与分发**: 消费来自平台各微服务的领域事件，并根据事件类型将其分发到不同的Feed流处理器。
*   **多Feed流管理**: 为每个用户维护多种类型的活动流，如：
    *   **通知Feed (Notifications)**: 系统公告、账户安全、任务/服务关键状态变更等高优先级信息。
    *   **互动Feed (Interactions)**: 谁赞了我的帖子、谁评论了我的回答、谁@了我等社交互动信息。
    *   **关注Feed (Following)**: 我关注的人发布了新内容等动态。
*   **活动条目生成与持久化**: 将事件转换为标准化的、用户可见的`ActivityFeedItem`，并持久化存储。
*   **智能聚合与降噪**: 将短时间内的多个同类活动（如点赞）聚合成一条，减少信息噪音。
*   **未读状态管理**: 精确管理每个Feed流的未读条目数，并支持单条和全部标记已读。
*   **API提供**: 提供API供客户端分页查询各Feed流，并获取未读数。
*   **实时更新协调**: 在新活动产生时，发布事件通知`chat-websocket-server`，以便向在线客户端实时推送更新提醒（如小红点）。

本服务 **不负责**:
*   实时发送Push/Email/SMS通知 (由`notification-dispatch-service`负责)。本服务是“历史记录”，而前者是“即时送达”。
*   产生源业务事件 (本服务是事件的消费者)。
*   复杂的社交Feed流算法（如推荐、排序） (本服务主要按时间倒序展示)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部微服务 (通过消息队列)**: 主要的事件来源方。
*   **CINA.CLUB客户端应用 (主要)**: 通过API查询并展示用户的“通知中心”列表。
*   **`chat-websocket-server`**: (可选) 消费本服务发布的`FeedUnreadCountChangedEvent`，以向客户端实时推送未读数更新。
*   **CINA.CLUB平台管理员**: 通过API向所有用户发布系统公告类型的活动。

#### 1.4. 定义与缩略语
*   **Feed**: 活动流，按特定主题（如通知、互动）聚合的事件列表。
*   **Item**: `ActivityFeedItem`，Feed中的一个独立条目。
*   **Aggregation**: 聚合，将多个相似活动合并为一条。
*   **EDA**: Event-Driven Architecture (事件驱动架构)。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`activity-feed-service` 是CINA.CLUB平台的用户**信息聚合与历史追溯中心**。它通过订阅平台事件总线，将分散在各个微服务中的、与用户相关的业务事件，汇集成多个分类清晰、主次分明的视图。它与`notification-dispatch-service`共同构成了平台完整的用户触达和信息通知体系，一个负责“历史”，一个负责“当下”。

#### 2.2. 主要功能概述
*   事件驱动的、多Feed流的活动记录生成。
*   高效、可分页的活动流查询。
*   智能的活动聚合与降噪。
*   精确的、分Feed的未读状态管理。
*   支持实时更新通知。

### 3. 核心流程图

#### 3.1. 事件处理与Feed生成流程
```mermaid
sequenceDiagram
    participant SourceService as "Source Service (e.g., community-forum)"
    participant MQ as "Message Queue"
    participant ActivityFeedService as AFS
    participant Redis
    participant DB as "MongoDB/Cassandra"
    participant WebSocketServer as "chat-websocket-server"

    SourceService->>MQ: 1. Publish ForumPostLikedEvent
    
    MQ-->>AFS: 2. Consume event
    AFS->>AFS: 3. **[Dispatch]** Route to "Interactions" Feed Handler
    
    Note right of AFS: **[Aggregation Logic]**
    AFS->>Redis: 4. Check for recent similar activities (e.g., ZRANGEBYSCORE on a sorted set)
    alt Aggregation Threshold Not Met
        AFS->>DB: 5a. Create new ActivityFeedItem (e.g., "张三赞了你的帖子")
    else Aggregation Threshold Met
        AFS->>DB: 5b. Update existing Item (e.g., "张三、李四等2人赞了你的帖子")
    end
    
    AFS->>Redis: 6. **Atomically INCR** unread count for user's "Interactions" Feed
    
    AFS->>MQ: 7. **Publish** FeedUnreadCountChangedEvent {userId, feedType: "INTERACTIONS", newCount: ...}
    
    MQ-->>WebSocketServer: 8. Consume event
    WebSocketServer-->>Client: 9. Push real-time unread count update
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 事件驱动与Feed生成
*   **FR4.1.1 (事件消费)**: 系统必须能消费来自MQ的多种领域事件。
*   **FR4.1.2 (Feed分发)**: 系统必须有一个分发器，能根据`eventType`将事件路由到正确的Feed处理器（如`NotificationsFeedHandler`, `InteractionsFeedHandler`）。
*   **FR4.1.3 (事件转换)**: 每个处理器负责将事件`payload`转换为一个或多个标准化的`ActivityFeedItem`实体。
    *   **示例**: 一个`TaskAcceptedEvent`会为任务发布者生成一条`Notifications` Feed Item，内容为“您的任务已被接受”。
*   **FR4.1.4 (内容丰富)**: （可选）在转换过程中，处理器可以调用其他服务（如`user-core-service`）来获取额外信息（如用户名）以构建用户友好的通知文本。此调用必须有缓存和容错。

#### 4.2. 智能聚合与降噪
*   **FR4.2.1 (聚合规则)**: 系统必须支持可配置的聚合规则。
    *   **规则定义**: `(feedType, activityType, targetId, time_window)` -> `aggregation_template`
    *   **示例**: 对于`Interactions` Feed中，`POST_LIKE`类型的活动，如果针对同一个`postId`，在`5分钟`内发生，则进行聚合。
*   **FR4.2.2 (聚合实现)**:
    *   当一个可聚合的活动到来时，系统检查在时间窗口内是否已存在一个聚合中的Item。
    *   如果存在，则更新该Item（如更新参与者列表和计数），并重置其“最后更新时间”。
    *   如果不存在，则创建一个新的、可被后续活动聚合的Item。
    *   此逻辑推荐使用Redis的Sorted Set或类似数据结构高效实现。

#### 4.3. 未读状态管理
*   **FR4.3.1 (分Feed计数)**: 系统必须为用户的**每个Feed流**独立维护一个未读计数。
*   **FR4.3.2 (原子更新)**: 新Item创建或聚合时，必须原子性地增加对应Feed的未读数（在Redis中`INCR`）。
*   **FR4.3.3 (标记已读)**:
    *   系统必须提供API，允许用户将一个或多个指定的Item标记为已读。
    *   系统必须提供API，允许用户将某个**特定Feed流**的所有未读Item一次性标记为已读。
*   **FR4.3.4 (计数扣减)**: 标记已读操作必须原子性地减少对应的未读数。

#### 4.4. 实时更新协调
*   **FR4.4.1 (事件发布)**: 当任何一个Feed的未读数发生变化时，本服务必须发布一个`FeedUnreadCountChangedEvent`到MQ。
*   **FR4.4.2 (事件内容)**: 事件中必须包含`userId`, `feedType`, 和最新的`unreadCount`。
*   **FR4.4.3**: 当一个Item的内容被更新时（如聚合了新的点赞者），也应触发类似事件，以便客户端实时更新UI。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端RESTful API接口
*   **版本**: `/api/v1/activity-feed`
*   **认证**: 用户JWT。
*   **核心端点**:
    *   `GET /me/feeds/{feedType}?page=...&limit=...`: 获取指定Feed流的活动列表。
        *   `feedType` (enum): `notifications`, `interactions`, `following`.
    *   `GET /me/feeds/summary`: 获取所有Feed流的未读数摘要。
        *   **Response (200 OK)**: `{ "notifications": 5, "interactions": 23, "following": 2 }`
    *   `POST /me/feeds/{feedType}/mark-all-read`: 将指定Feed流所有条目标记为已读。
    *   `POST /me/items/mark-read`: 标记一个或多个具体条目为已读。
        *   **Request Body**: `{ "item_ids": ["uuid1", "uuid2"] }`

#### 5.2. 管理后台API接口 (S2S)
*   `POST /internal/system-announcement`: (Admin Auth) 发布系统公告。
    *   **Request**: `{ "target_user_ids"?: [], "title": "...", "message": "...", "deep_link_url": "..." }`
    *   此接口会为目标用户在`Notifications` Feed中创建一条Item。

#### 5.3. 消息队列事件契约
*   **入站 (消费)**: 消费平台所有相关的领域事件。
*   **出站 (发布)**:
    *   `FeedUnreadCountChangedEvent { userId, feedType, unreadCount }`
    *   `FeedItemUpdatedEvent { userId, feedType, item }`

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (MongoDB/Cassandra Schema)
*   **`activity_feed_items` Collection/Table**:
    *   **Partition Key**: `userId`
    *   **Clustering Key**: `createdAt` (DESC)
    *   **Schema**:
        ```json
        {
          "_id": "ObjectId",
          "item_id": "uuid_string",
          "user_id": "user_uuid_string",
          "feed_type": "INTERACTIONS",
          "activity_type": "POST_LIKE",
          "created_at": "ISODate",
          "is_read": false,
          
          "is_aggregated": true,
          "aggregated_actors": [ // 聚合的参与者
            { "id": "user1", "name": "张三" },
            { "id": "user2", "name": "李四" }
          ],
          "aggregation_count": 2,

          "display": { // 用于UI渲染的标准化数据
            "title_template": "{actors} an {count} others liked your post {target}.",
            "actors": [/* ... */],
            "target": { "id": "post123", "name": "My Great Post", "type": "FORUM_POST" }
          },
          
          "deep_link_url": "cinaclub://post/post123",
          "image_url": "http://.../icon_like.png"
        }
        ```

#### 6.2. 数据持久化与存储
*   **数据库**: **MongoDB** 或 **Cassandra/ScyllaDB** 是理想选择。
    *   **理由**: 活动流是典型的“时间线”数据，写入量极大，且查询模式简单（按用户ID和时间倒序分页）。NoSQL数据库的水平扩展能力和灵活的Schema非常适合这种场景。Cassandra/ScyllaDB在处理这种时间序列写入密集型负载方面尤其出色。
*   **缓存 (Redis)**:
    *   `unread_counts:{userId}` (Redis Hash): `FIELD`为`feedType` (e.g., "notifications"), `VALUE`为未读数。
    *   `aggregation_tracker:{feedType}:{targetId}` (Redis Sorted Set): 用于高效实现聚合逻辑。`MEMBER`为`actorId`, `SCORE`为时间戳。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **事件消费延迟**: 从消息进入MQ到被本服务处理并写入DB的P99延迟应 `< 50ms`。
*   **API延迟**:
    *   `GET /me/feeds/summary` (未读数) P99 < 20ms (直接从Redis读取)。
    *   `GET /me/feeds/{feedType}` (列表) P99 < 100ms。
*   **写入吞吐量**: 能够处理平台所有业务事件产生的峰值写入量，支持每秒数万次事件写入。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **事件消费**: 必须可靠，使用MQ的ACK机制，对于处理失败的事件应放入DLQ。

#### 7.3. 可扩展性需求
*   服务可水平扩展。
*   数据库（MongoDB/Cassandra）和Redis集群必须支持水平扩展/分片。

#### 7.4. 安全性需求
*   API必须经过严格的用户认证，确保用户只能看到自己的活动流。
*   Admin API必须有角色权限控制。

### 8. 技术约束与选型建议
*   **语言**: Go。其高并发模型非常适合处理海量的事件写入。
*   **核心架构**: 事件驱动，与平台其他服务完全解耦。
*   **数据库选择**: 优先考虑NoSQL（MongoDB或Cassandra），以应对巨大的数据量和高写入负载。
*   **实时更新**: 与`chat-websocket-server`通过MQ的联动是实现优秀用户体验的关键。

---
这份版本2.0的SRS文档为`activity-feed-service`构建了一个更加成熟和用户友好的信息聚合中心。通过引入多Feed流和智能聚合机制，它能在保证信息全面性的同时，极大地提升信噪比，为用户提供清晰、高效的动态感知体验。