/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"golang.org/x/sync/errgroup"

	"cina.club/services/admin-bff-service/internal/adapter/client"
	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// Context key types to avoid collisions
type contextKey string

const (
	actorIDKey    contextKey = "actor_id"
	actorEmailKey contextKey = "actor_email"
	actorRolesKey contextKey = "actor_roles"
)

// BFFService implements the BFF service
type BFFService struct {
	sessionStore port.SessionStore
	auditLogger  port.AuditLogger
	clients      *client.Clients
	logger       *logrus.Logger
}

// NewBFFService creates a new BFF service
func NewBFFService(
	sessionStore port.SessionStore,
	auditLogger port.AuditLogger,
	clients *client.Clients,
	logger *logrus.Logger,
) port.BFFService {
	return &BFFService{
		sessionStore: sessionStore,
		auditLogger:  auditLogger,
		clients:      clients,
		logger:       logger,
	}
}

// Authentication and session management

// CreateSession creates a new session
func (s *BFFService) CreateSession(ctx context.Context, employee *model.Employee, ipAddress, userAgent string) (*model.AdminSession, error) {
	sessionID := uuid.New().String()
	now := time.Now()

	session := &model.AdminSession{
		ID:         sessionID,
		EmployeeID: employee.ID,
		Email:      employee.Email,
		Name:       employee.Name,
		Roles:      employee.Roles,
		CreatedAt:  now,
		ExpiresAt:  now.Add(24 * time.Hour), // Expires in 24 hours
		LastUsedAt: now,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
	}

	if err := s.sessionStore.CreateSession(ctx, session); err != nil {
		s.logger.WithError(err).Error("Failed to create admin session")
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// Log login audit entry
	auditEntry := model.NewAuditLogEntry(employee.ID, employee.Email, ipAddress)
	auditEntry.SetResource(model.ResourceSession, sessionID, model.ActionLogin)
	auditEntry.SetUserAgent(userAgent)
	auditEntry.SetResponse(200, "Login successful", true)

	if err := s.auditLogger.LogEntry(ctx, auditEntry); err != nil {
		s.logger.WithError(err).Warn("Failed to log login audit entry")
	}

	s.logger.WithFields(logrus.Fields{
		"session_id":  session.ID,
		"employee_id": employee.ID,
		"email":       employee.Email,
	}).Info("Admin session created successfully")

	return session, nil
}

// ValidateSession validates a session
func (s *BFFService) ValidateSession(ctx context.Context, sessionID string) (*model.AdminSession, error) {
	session, err := s.sessionStore.GetSession(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("invalid session: %w", err)
	}

	// Update last used time
	session.UpdateLastUsed()
	if err := s.sessionStore.UpdateSession(ctx, session); err != nil {
		s.logger.WithError(err).Warn("Failed to update session last used time")
	}

	return session, nil
}

// RefreshSession refreshes a session
func (s *BFFService) RefreshSession(ctx context.Context, sessionID string) (*model.AdminSession, error) {
	session, err := s.sessionStore.GetSession(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("session not found: %w", err)
	}

	// Extend expiration time
	session.ExpiresAt = time.Now().Add(24 * time.Hour)
	session.UpdateLastUsed()

	if err := s.sessionStore.UpdateSession(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to refresh session: %w", err)
	}

	s.logger.WithField("session_id", sessionID).Debug("Session refreshed")
	return session, nil
}

// DestroySession destroys a session
func (s *BFFService) DestroySession(ctx context.Context, sessionID string) error {
	if err := s.sessionStore.DeleteSession(ctx, sessionID); err != nil {
		return fmt.Errorf("failed to destroy session: %w", err)
	}

	s.logger.WithField("session_id", sessionID).Info("Session destroyed")
	return nil
}

// DestroyAllUserSessions destroys all sessions for a user
func (s *BFFService) DestroyAllUserSessions(ctx context.Context, employeeID string) error {
	if err := s.sessionStore.DeleteAllSessionsByEmployeeID(ctx, employeeID); err != nil {
		return fmt.Errorf("failed to destroy user sessions: %w", err)
	}

	s.logger.WithField("employee_id", employeeID).Info("All user sessions destroyed")
	return nil
}

// User management aggregated API

// GetUsers gets user list
func (s *BFFService) GetUsers(ctx context.Context, filter port.UserFilter) (*port.UsersResponse, error) {
	// Call UserCore service to get user list
	usersData, err := s.clients.UserCore.GetUsers(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get users from UserCore service")
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Convert data format
	users := make([]*port.UserSummaryDTO, len(usersData.Users))
	for i, userData := range usersData.Users {
		users[i] = &port.UserSummaryDTO{
			ID:             userData.ID,
			Email:          userData.Email,
			Username:       userData.Username,
			DisplayName:    userData.DisplayName,
			Status:         userData.Status,
			Level:          userData.Level,
			CreatedAt:      userData.CreatedAt,
			LastLoginAt:    userData.LastLoginAt,
			FollowersCount: userData.FollowersCount,
			IsVerified:     userData.IsVerified,
			IsPremium:      userData.IsPremium,
		}
	}

	totalPages := int((usersData.Total + int64(filter.PageSize) - 1) / int64(filter.PageSize))

	return &port.UsersResponse{
		Users:      users,
		Total:      usersData.Total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetUserFullProfile gets user's complete profile (aggregating data from multiple services)
func (s *BFFService) GetUserFullProfile(ctx context.Context, userID string) (*port.UserFullProfileDTO, error) {
	// Use errgroup to call multiple services concurrently
	g, ctx := errgroup.WithContext(ctx)

	var userCoreData *client.UserCoreData
	var billingData *client.BillingData
	var socialData *client.SocialData

	// Call UserCore service concurrently
	g.Go(func() error {
		var err error
		userCoreData, err = s.clients.UserCore.GetUserForAdmin(ctx, userID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to get user core data")
			return fmt.Errorf("failed to get user core data: %w", err)
		}
		return nil
	})

	// Call Billing service concurrently
	g.Go(func() error {
		var err error
		billingData, err = s.clients.Billing.GetUserSubscriptions(ctx, userID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to get user billing data")
			return fmt.Errorf("failed to get user billing data: %w", err)
		}
		return nil
	})

	// Call Social service concurrently
	g.Go(func() error {
		var err error
		socialData, err = s.clients.Social.GetUserSocialStats(ctx, userID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to get user social data")
			return fmt.Errorf("failed to get user social data: %w", err)
		}
		return nil
	})

	// Wait for all calls to complete
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// Aggregate data and convert to DTO
	profile := &port.UserFullProfileDTO{
		// Basic information (from UserCore)
		ID:          userCoreData.ID,
		Email:       userCoreData.Email,
		Username:    userCoreData.Username,
		DisplayName: userCoreData.DisplayName,
		Avatar:      userCoreData.Avatar,
		Bio:         userCoreData.Bio,
		Status:      userCoreData.Status,
		Level:       userCoreData.Level,
		CreatedAt:   userCoreData.CreatedAt,
		UpdatedAt:   userCoreData.UpdatedAt,
		LastLoginAt: userCoreData.LastLoginAt,

		// Verification information (from UserCore)
		IsVerified:     userCoreData.IsVerified,
		VerifiedAt:     userCoreData.VerifiedAt,
		KYCStatus:      userCoreData.KYCStatus,
		KYCCompletedAt: userCoreData.KYCCompletedAt,

		// Social statistics (from Social)
		FollowersCount: socialData.FollowersCount,
		FollowingCount: socialData.FollowingCount,
		PostsCount:     socialData.PostsCount,
		LikesCount:     socialData.LikesCount,

		// Subscription information (from Billing)
		IsPremium:        billingData.IsPremium,
		PremiumStartAt:   billingData.PremiumStartAt,
		PremiumExpireAt:  billingData.PremiumExpireAt,
		SubscriptionPlan: billingData.SubscriptionPlan,

		// Financial information (from Billing)
		CoinBalance:    billingData.CoinBalance,
		TotalSpent:     billingData.TotalSpent,
		TotalEarned:    billingData.TotalEarned,
		UnpaidInvoices: billingData.UnpaidInvoices,

		// Content statistics (mock data, should be from Content service in actual implementation)
		PublishedContent: 25,
		PendingContent:   2,
		RejectedContent:  1,

		// Behavior statistics (from UserCore)
		LoginCount:   userCoreData.LoginCount,
		LastActiveAt: userCoreData.LastActiveAt,
		DeviceCount:  userCoreData.DeviceCount,

		// Risk information (from UserCore)
		WarningCount:    userCoreData.WarningCount,
		SuspensionCount: userCoreData.SuspensionCount,
		LastSuspendedAt: userCoreData.LastSuspendedAt,
		ReportedCount:   userCoreData.ReportedCount,
	}

	s.logger.WithField("user_id", userID).Debug("User full profile aggregated successfully")
	return profile, nil
}

// SuspendUser suspends a user
func (s *BFFService) SuspendUser(ctx context.Context, actorInfo *port.ActorInfo, userID string, reason string) error {
	// Add actor information to context
	ctx = s.addActorToContext(ctx, actorInfo)

	// Call UserCore service to suspend user
	if err := s.clients.UserCore.SuspendUser(ctx, userID, reason); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":  userID,
			"actor_id": actorInfo.EmployeeID,
			"reason":   reason,
		}).Error("Failed to suspend user")
		return fmt.Errorf("failed to suspend user: %w", err)
	}

	// Log audit entry
	auditEntry := model.NewAuditLogEntry(actorInfo.EmployeeID, actorInfo.Email, actorInfo.IPAddress)
	auditEntry.SetResource(model.ResourceUser, userID, model.ActionUserSuspend)
	auditEntry.SetUserAgent(actorInfo.UserAgent)
	auditEntry.SetResponse(200, "User suspended successfully", true)
	auditEntry.AddMetadata("reason", reason)

	if err := s.auditLogger.LogEntry(ctx, auditEntry); err != nil {
		s.logger.WithError(err).Warn("Failed to log suspend user audit entry")
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"actor_id": actorInfo.EmployeeID,
		"reason":   reason,
	}).Info("User suspended successfully")

	return nil
}

// RestoreUser restores a user
func (s *BFFService) RestoreUser(ctx context.Context, actorInfo *port.ActorInfo, userID string, reason string) error {
	ctx = s.addActorToContext(ctx, actorInfo)

	if err := s.clients.UserCore.RestoreUser(ctx, userID, reason); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":  userID,
			"actor_id": actorInfo.EmployeeID,
			"reason":   reason,
		}).Error("Failed to restore user")
		return fmt.Errorf("failed to restore user: %w", err)
	}

	// Log audit entry
	auditEntry := model.NewAuditLogEntry(actorInfo.EmployeeID, actorInfo.Email, actorInfo.IPAddress)
	auditEntry.SetResource(model.ResourceUser, userID, model.ActionUserRestore)
	auditEntry.SetUserAgent(actorInfo.UserAgent)
	auditEntry.SetResponse(200, "User restored successfully", true)
	auditEntry.AddMetadata("reason", reason)

	if err := s.auditLogger.LogEntry(ctx, auditEntry); err != nil {
		s.logger.WithError(err).Warn("Failed to log restore user audit entry")
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"actor_id": actorInfo.EmployeeID,
		"reason":   reason,
	}).Info("User restored successfully")

	return nil
}

// UpdateUserStatus updates user status
func (s *BFFService) UpdateUserStatus(ctx context.Context, actorInfo *port.ActorInfo, userID string, status string) error {
	ctx = s.addActorToContext(ctx, actorInfo)

	// Call corresponding operation based on status
	switch status {
	case "suspended":
		return s.SuspendUser(ctx, actorInfo, userID, "Status updated to suspended")
	case "active":
		return s.RestoreUser(ctx, actorInfo, userID, "Status updated to active")
	default:
		return fmt.Errorf("unsupported status: %s", status)
	}
}

// Content management aggregated API

// GetModerationQueue gets content moderation queue
func (s *BFFService) GetModerationQueue(ctx context.Context, filter port.ModerationFilter) (*port.ModerationQueueResponse, error) {
	moderationData, err := s.clients.ContentModeration.GetModerationQueue(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get moderation queue")
		return nil, fmt.Errorf("failed to get moderation queue: %w", err)
	}

	// Convert data format
	tasks := make([]*port.ModerationTaskDTO, len(moderationData.Tasks))
	for i, taskData := range moderationData.Tasks {
		tasks[i] = &port.ModerationTaskDTO{
			ID:          taskData.ID,
			ContentID:   taskData.ContentID,
			ContentType: taskData.ContentType,
			Title:       taskData.Title,
			AuthorID:    taskData.AuthorID,
			AuthorName:  taskData.AuthorName,
			Status:      taskData.Status,
			Priority:    taskData.Priority,
			ReportCount: taskData.ReportCount,
			CreatedAt:   taskData.CreatedAt,
			AssignedTo:  taskData.AssignedTo,
			ReviewedAt:  taskData.ReviewedAt,
			ReviewedBy:  taskData.ReviewedBy,
		}
	}

	totalPages := int((moderationData.Total + int64(filter.PageSize) - 1) / int64(filter.PageSize))

	return &port.ModerationQueueResponse{
		Tasks:      tasks,
		Total:      moderationData.Total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalPages: totalPages,
	}, nil
}

// ApproveContent approves content
func (s *BFFService) ApproveContent(ctx context.Context, actorInfo *port.ActorInfo, contentID string, reason string) error {
	ctx = s.addActorToContext(ctx, actorInfo)

	// In actual implementation, call content moderation service
	// err := s.clients.ContentModeration.ApproveContent(ctx, contentID, reason)

	// Log audit entry
	auditEntry := model.NewAuditLogEntry(actorInfo.EmployeeID, actorInfo.Email, actorInfo.IPAddress)
	auditEntry.SetResource(model.ResourceContent, contentID, model.ActionContentApprove)
	auditEntry.SetUserAgent(actorInfo.UserAgent)
	auditEntry.SetResponse(200, "Content approved", true)
	auditEntry.AddMetadata("reason", reason)

	if err := s.auditLogger.LogEntry(ctx, auditEntry); err != nil {
		s.logger.WithError(err).Warn("Failed to log approve content audit entry")
	}

	s.logger.WithFields(logrus.Fields{
		"content_id": contentID,
		"actor_id":   actorInfo.EmployeeID,
		"reason":     reason,
	}).Info("Content approved")

	return nil
}

// RejectContent rejects content
func (s *BFFService) RejectContent(ctx context.Context, actorInfo *port.ActorInfo, contentID string, reason string) error {
	ctx = s.addActorToContext(ctx, actorInfo)

	// In actual implementation, call content moderation service
	// err := s.clients.ContentModeration.RejectContent(ctx, contentID, reason)

	// Log audit entry
	auditEntry := model.NewAuditLogEntry(actorInfo.EmployeeID, actorInfo.Email, actorInfo.IPAddress)
	auditEntry.SetResource(model.ResourceContent, contentID, model.ActionContentReject)
	auditEntry.SetUserAgent(actorInfo.UserAgent)
	auditEntry.SetResponse(200, "Content rejected", true)
	auditEntry.AddMetadata("reason", reason)

	if err := s.auditLogger.LogEntry(ctx, auditEntry); err != nil {
		s.logger.WithError(err).Warn("Failed to log reject content audit entry")
	}

	s.logger.WithFields(logrus.Fields{
		"content_id": contentID,
		"actor_id":   actorInfo.EmployeeID,
		"reason":     reason,
	}).Info("Content rejected")

	return nil
}

// Order and payment management aggregated API

// GetOrders gets order list
func (s *BFFService) GetOrders(ctx context.Context, filter port.OrderFilter) (*port.OrdersResponse, error) {
	ordersData, err := s.clients.ServiceOffering.GetOrders(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get orders")
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	// Convert data format
	orders := make([]*port.OrderSummaryDTO, len(ordersData.Orders))
	for i, orderData := range ordersData.Orders {
		orders[i] = &port.OrderSummaryDTO{
			ID:          orderData.ID,
			UserID:      orderData.UserID,
			UserName:    orderData.UserName,
			ServiceID:   orderData.ServiceID,
			ServiceName: orderData.ServiceName,
			Status:      orderData.Status,
			Amount:      orderData.Amount,
			Currency:    orderData.Currency,
			CreatedAt:   orderData.CreatedAt,
			PaidAt:      orderData.PaidAt,
		}
	}

	totalPages := int((ordersData.Total + int64(filter.PageSize) - 1) / int64(filter.PageSize))

	return &port.OrdersResponse{
		Orders:     orders,
		Total:      ordersData.Total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetOrderDetails gets order details
func (s *BFFService) GetOrderDetails(ctx context.Context, orderID string) (*port.OrderDetailsDTO, error) {
	// In actual implementation, need to aggregate data from multiple services
	// Here return mock data
	return &port.OrderDetailsDTO{
		OrderSummaryDTO: port.OrderSummaryDTO{
			ID:          orderID,
			UserID:      "user_1",
			UserName:    "User Name",
			ServiceID:   "service_1",
			ServiceName: "Service Name",
			Status:      "paid",
			Amount:      100.00,
			Currency:    "USD",
			CreatedAt:   time.Now().Add(-2 * time.Hour),
			PaidAt:      timePtr(time.Now().Add(-1 * time.Hour)),
		},
		Description:   "Service description",
		Items:         []port.OrderItemDTO{},
		PaymentMethod: "credit_card",
		PaymentStatus: "completed",
		InvoiceURL:    "https://example.com/invoice/" + orderID,
		RefundAmount:  0,
		Metadata:      make(map[string]interface{}),
	}, nil
}

// CancelOrder cancels an order
func (s *BFFService) CancelOrder(ctx context.Context, actorInfo *port.ActorInfo, orderID string, reason string) error {
	ctx = s.addActorToContext(ctx, actorInfo)

	// In actual implementation, call related services
	// err := s.clients.ServiceOffering.CancelOrder(ctx, orderID, reason)

	// Log audit entry
	auditEntry := model.NewAuditLogEntry(actorInfo.EmployeeID, actorInfo.Email, actorInfo.IPAddress)
	auditEntry.SetResource(model.ResourceOrder, orderID, model.ActionOrderCancel)
	auditEntry.SetUserAgent(actorInfo.UserAgent)
	auditEntry.SetResponse(200, "Order cancelled", true)
	auditEntry.AddMetadata("reason", reason)

	if err := s.auditLogger.LogEntry(ctx, auditEntry); err != nil {
		s.logger.WithError(err).Warn("Failed to log cancel order audit entry")
	}

	s.logger.WithFields(logrus.Fields{
		"order_id": orderID,
		"actor_id": actorInfo.EmployeeID,
		"reason":   reason,
	}).Info("Order cancelled")

	return nil
}

// RefundOrder processes order refund
func (s *BFFService) RefundOrder(ctx context.Context, actorInfo *port.ActorInfo, orderID string, amount float64, reason string) error {
	ctx = s.addActorToContext(ctx, actorInfo)

	// In actual implementation, call related services
	// err := s.clients.Payment.RefundOrder(ctx, orderID, amount, reason)

	// Log audit entry
	auditEntry := model.NewAuditLogEntry(actorInfo.EmployeeID, actorInfo.Email, actorInfo.IPAddress)
	auditEntry.SetResource(model.ResourceOrder, orderID, model.ActionOrderRefund)
	auditEntry.SetUserAgent(actorInfo.UserAgent)
	auditEntry.SetResponse(200, "Order refunded", true)
	auditEntry.AddMetadata("reason", reason)
	auditEntry.AddMetadata("amount", amount)

	if err := s.auditLogger.LogEntry(ctx, auditEntry); err != nil {
		s.logger.WithError(err).Warn("Failed to log refund order audit entry")
	}

	s.logger.WithFields(logrus.Fields{
		"order_id": orderID,
		"actor_id": actorInfo.EmployeeID,
		"amount":   amount,
		"reason":   reason,
	}).Info("Order refunded")

	return nil
}

// Analytics and reporting aggregated API

// GetDashboardSummary gets dashboard summary
func (s *BFFService) GetDashboardSummary(ctx context.Context) (*port.DashboardSummaryDTO, error) {
	dashboardData, err := s.clients.Analytics.GetDashboardSummary(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get dashboard summary")
		return nil, fmt.Errorf("failed to get dashboard summary: %w", err)
	}

	return &port.DashboardSummaryDTO{
		TotalUsers:        dashboardData.TotalUsers,
		ActiveUsers:       dashboardData.ActiveUsers,
		NewUsersToday:     dashboardData.NewUsersToday,
		PremiumUsers:      dashboardData.PremiumUsers,
		TotalContent:      dashboardData.TotalContent,
		PendingModeration: dashboardData.PendingModeration,
		PublishedToday:    dashboardData.PublishedToday,
		TotalRevenue:      dashboardData.TotalRevenue,
		RevenueToday:      dashboardData.RevenueToday,
		PendingPayments:   dashboardData.PendingPayments,
		SystemHealth:      dashboardData.SystemHealth,
		ActiveSessions:    dashboardData.ActiveSessions,
		ErrorRate:         dashboardData.ErrorRate,
	}, nil
}

// GetUserAnalytics gets user analytics data
func (s *BFFService) GetUserAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.UserAnalyticsDTO, error) {
	// In actual implementation, call Analytics service to get data
	// Here return mock data
	return &port.UserAnalyticsDTO{
		TimeRange:    timeRange,
		NewUsers:     []port.DailyMetric{},
		ActiveUsers:  []port.DailyMetric{},
		UsersByLevel: []port.LevelMetric{},
		Retention: port.RetentionMetric{
			Day1:  0.85,
			Day7:  0.65,
			Day30: 0.45,
		},
	}, nil
}

// GetContentAnalytics gets content analytics data
func (s *BFFService) GetContentAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.ContentAnalyticsDTO, error) {
	// In actual implementation, call Analytics service to get data
	return &port.ContentAnalyticsDTO{
		TimeRange:     timeRange,
		NewContent:    []port.DailyMetric{},
		ContentByType: []port.TypeMetric{},
		ModerationMetrics: port.ModerationMetrics{
			Pending:  25,
			Approved: 450,
			Rejected: 15,
		},
	}, nil
}

// GetRevenueAnalytics gets revenue analytics data
func (s *BFFService) GetRevenueAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.RevenueAnalyticsDTO, error) {
	// In actual implementation, call Analytics service to get data
	return &port.RevenueAnalyticsDTO{
		TimeRange:       timeRange,
		DailyRevenue:    []port.DailyMetric{},
		RevenueBySource: []port.SourceMetric{},
		TopServices:     []port.ServiceMetric{},
	}, nil
}

// System management API

// GetSystemHealth gets system health status
func (s *BFFService) GetSystemHealth(ctx context.Context) (*port.SystemHealthDTO, error) {
	// In actual implementation, need to check health status of various services
	// Here return mock data
	return &port.SystemHealthDTO{
		OverallStatus: "healthy",
		Services:      []port.ServiceHealthDTO{},
		Metrics: port.SystemMetricsDTO{
			CPUUsage:    45.2,
			MemoryUsage: 67.8,
			DiskUsage:   23.5,
			NetworkIO: port.NetworkIOMetric{
				BytesIn:  1024000,
				BytesOut: 2048000,
			},
		},
		LastChecked: time.Now(),
	}, nil
}

// GetAuditLogs gets audit logs
func (s *BFFService) GetAuditLogs(ctx context.Context, filter model.AuditLogFilter) (*port.AuditLogsResponse, error) {
	logs, err := s.auditLogger.GetLogs(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get audit logs")
		return nil, fmt.Errorf("failed to get audit logs: %w", err)
	}

	total, err := s.auditLogger.GetLogCount(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get audit log count")
		total = int64(len(logs))
	}

	// Set pagination default values
	pageSize := filter.PageSize
	if pageSize <= 0 {
		pageSize = 50
	}
	page := filter.Page
	if page <= 0 {
		page = 1
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &port.AuditLogsResponse{
		Logs:       logs,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// Helper methods

// addActorToContext adds actor information to context
func (s *BFFService) addActorToContext(ctx context.Context, actorInfo *port.ActorInfo) context.Context {
	ctx = context.WithValue(ctx, actorIDKey, actorInfo.EmployeeID)
	ctx = context.WithValue(ctx, actorEmailKey, actorInfo.Email)
	ctx = context.WithValue(ctx, actorRolesKey, strings.Join(actorInfo.Roles, ","))
	return ctx
}

// timePtr returns a time pointer
func timePtr(t time.Time) *time.Time {
	return &t
}
