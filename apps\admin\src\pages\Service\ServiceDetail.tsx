/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Tag, Space, Button, Descriptions, Statistic, Modal, message, Tabs, Table, Badge, Typography, Progress } from 'antd'
import { ArrowLeftOutlined, PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, SettingOutlined } from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'

import { Service, ServiceStatus, HealthStatus, ServiceType, ServiceLog } from '@/types/service'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'
import { Loading } from '@/components/Loading'

const { TabPane } = Tabs
const { Title, Text } = Typography

// Mock service data
const mockService: Service = {
  id: '1',
  name: 'user-core-service',
  displayName: '用户核心服务',
  type: ServiceType.MICROSERVICE,
  status: ServiceStatus.RUNNING,
  health: HealthStatus.HEALTHY,
  version: '1.2.3',
  endpoint: '/api/users',
  port: 8001,
  host: 'localhost',
  environment: 'production',
  tags: ['core', 'user-management'],
  dependencies: ['database', 'cache'],
  createdAt: '2025-01-01T00:00:00Z',
  updatedAt: '2025-01-23T10:00:00Z',
  lastHealthCheck: '2025-01-23T10:00:00Z',
}

// Mock logs
const mockLogs: ServiceLog[] = [
  {
    id: '1',
    serviceId: '1',
    timestamp: '2025-01-23T10:00:00Z',
    level: 'INFO',
    message: 'Service health check passed',
    source: 'health-checker',
  },
  {
    id: '2',
    serviceId: '1',
    timestamp: '2025-01-23T09:58:30Z',
    level: 'WARN',
    message: 'High memory usage detected',
    source: 'monitor',
  },
]

/**
 * 服务详情页面
 */
const ServiceDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { hasPermission } = usePermission()
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)

  const canView = hasPermission(Permission.SERVICE_VIEW)
  const canControl = hasPermission(Permission.SERVICE_CONTROL)

  useEffect(() => {
    const fetchService = async () => {
      setLoading(true)
      await new Promise(resolve => setTimeout(resolve, 1000))
      setService(mockService)
      setLoading(false)
    }
    if (id) fetchService()
  }, [id])

  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.RUNNING: return 'green'
      case ServiceStatus.STOPPED: return 'red'
      default: return 'gray'
    }
  }

  const getHealthColor = (health: HealthStatus) => {
    switch (health) {
      case HealthStatus.HEALTHY: return 'success'
      case HealthStatus.DEGRADED: return 'warning'
      case HealthStatus.CRITICAL: return 'error'
      default: return 'default'
    }
  }

  const handleServiceAction = (action: 'start' | 'stop' | 'restart') => {
    Modal.confirm({
      title: `${action}服务`,
      content: `确定要${action}服务吗？`,
      onOk: async () => {
        await new Promise(resolve => setTimeout(resolve, 1000))
        message.success(`服务${action}成功`)
      },
    })
  }

  const logColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '级别',
      dataIndex: 'level',
      render: (level: string) => <Tag color={level === 'ERROR' ? 'red' : level === 'WARN' ? 'orange' : 'blue'}>{level}</Tag>,
    },
    {
      title: '来源',
      dataIndex: 'source',
    },
    {
      title: '消息',
      dataIndex: 'message',
    },
  ]

  if (loading) return <Loading type="page" />

  if (!service || !canView) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>{!service ? '服务不存在' : '权限不足'}</h3>
        <Button onClick={() => navigate('/services')}>返回服务列表</Button>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={() => navigate('/services')}>返回</Button>
            <Title level={3} style={{ margin: 0 }}>{service.displayName}</Title>
            <Tag color={getStatusColor(service.status)}>{service.status}</Tag>
            <Badge status={getHealthColor(service.health) as any} text={service.health} />
          </Space>
        </Col>
        <Col>
          {canControl && (
            <Space>
              {service.status === ServiceStatus.STOPPED ? (
                <Button type="primary" icon={<PlayCircleOutlined />} onClick={() => handleServiceAction('start')}>启动</Button>
              ) : (
                <Button danger icon={<PauseCircleOutlined />} onClick={() => handleServiceAction('stop')}>停止</Button>
              )}
              <Button icon={<ReloadOutlined />} onClick={() => handleServiceAction('restart')}>重启</Button>
              <Button icon={<SettingOutlined />}>配置</Button>
            </Space>
          )}
        </Col>
      </Row>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card><Statistic title="运行时间" value={99.9} suffix="%" /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="CPU 使用率" value={45} suffix="%" /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="内存使用率" value={50} suffix="%" /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="请求速率" value={25.5} suffix="req/s" /></Card>
        </Col>
      </Row>

      {/* 详细信息 */}
      <Card>
        <Tabs>
          <TabPane tab="基本信息" key="info">
            <Descriptions bordered>
              <Descriptions.Item label="服务名称">{service.name}</Descriptions.Item>
              <Descriptions.Item label="版本">{service.version}</Descriptions.Item>
              <Descriptions.Item label="类型">{service.type}</Descriptions.Item>
              <Descriptions.Item label="端点">{service.endpoint}</Descriptions.Item>
              <Descriptions.Item label="地址">{service.host}:{service.port}</Descriptions.Item>
              <Descriptions.Item label="环境">{service.environment}</Descriptions.Item>
            </Descriptions>
          </TabPane>
          <TabPane tab="日志" key="logs">
            <Table columns={logColumns} dataSource={mockLogs} rowKey="id" />
          </TabPane>
          <TabPane tab="配置" key="config">
            <Text>配置管理功能开发中...</Text>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default ServiceDetail
 