/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package conv

import (
	"fmt"
	"strconv"
)

// Atoi converts a string to an integer, returning defaultValue if conversion fails.
// This is useful when you want to parse potentially invalid numeric strings
// without handling errors explicitly.
//
// Example:
//
//	value := conv.Atoi("123", 0)     // returns 123
//	value = conv.Atoi("invalid", 0)  // returns 0
//	value = conv.Atoi("", -1)        // returns -1
func Atoi(s string, defaultValue int) int {
	if v, err := strconv.Atoi(s); err == nil {
		return v
	}
	return defaultValue
}

// Atoi64 converts a string to an int64, returning defaultValue if conversion fails.
//
// Example:
//
//	value := conv.Atoi64("123", 0)     // returns 123
//	value = conv.Atoi64("invalid", 0)  // returns 0
func Atoi64(s string, defaultValue int64) int64 {
	if v, err := strconv.ParseInt(s, 10, 64); err == nil {
		return v
	}
	return defaultValue
}

// ParseFloat converts a string to a float64, returning defaultValue if conversion fails.
//
// Example:
//
//	value := conv.ParseFloat("123.45", 0.0)     // returns 123.45
//	value = conv.ParseFloat("invalid", 0.0)     // returns 0.0
func ParseFloat(s string, defaultValue float64) float64 {
	if v, err := strconv.ParseFloat(s, 64); err == nil {
		return v
	}
	return defaultValue
}

// ParseBool converts a string to a boolean, returning defaultValue if conversion fails.
// It recognizes "1", "t", "T", "TRUE", "true", "True" as true
// and "0", "f", "F", "FALSE", "false", "False" as false.
//
// Example:
//
//	value := conv.ParseBool("true", false)      // returns true
//	value = conv.ParseBool("invalid", false)    // returns false
func ParseBool(s string, defaultValue bool) bool {
	if v, err := strconv.ParseBool(s); err == nil {
		return v
	}
	return defaultValue
}

// ToString converts any value to its string representation using fmt.Sprintf.
// This is useful for converting various types to strings for logging or display.
//
// Example:
//
//	str := conv.ToString(123)           // returns "123"
//	str = conv.ToString(123.45)         // returns "123.45"
//	str = conv.ToString(true)           // returns "true"
//	str = conv.ToString([]int{1, 2, 3}) // returns "[1 2 3]"
func ToString(v interface{}) string {
	if v == nil {
		return ""
	}

	// Handle common types efficiently
	switch val := v.(type) {
	case string:
		return val
	case []byte:
		return string(val)
	case int:
		return strconv.Itoa(val)
	case int64:
		return strconv.FormatInt(val, 10)
	case float64:
		return strconv.FormatFloat(val, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(val)
	case fmt.Stringer:
		return val.String()
	default:
		return fmt.Sprintf("%v", v)
	}
}

// ToStringSlice converts a slice of any type to a slice of strings.
// Each element is converted using ToString.
//
// Example:
//
//	strings := conv.ToStringSlice([]int{1, 2, 3})        // returns ["1", "2", "3"]
//	strings = conv.ToStringSlice([]bool{true, false})    // returns ["true", "false"]
func ToStringSlice[T any](slice []T) []string {
	if slice == nil {
		return nil
	}

	result := make([]string, len(slice))
	for i, v := range slice {
		result[i] = ToString(v)
	}
	return result
}
