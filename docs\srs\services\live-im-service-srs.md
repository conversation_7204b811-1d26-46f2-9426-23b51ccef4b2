好的，遵照您的指示。我将为您生成一份专门针对 **`live-im-service` (直播互动IM服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义这个新增的、为直播场景优化的实时消息服务的功能、接口、数据模型、性能和可靠性需求，作为直播系统开发的权威依据。

---
### CINA.CLUB - `live-im-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [多媒体/实时通信产品负责人]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求: WebSocket事件协议](#5-接口需求-websocket事件协议)
6.  [数据需求 (运行时数据)](#6-数据需求-运行时数据)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的直播功能中，观众与主播、观众与观众之间的实时互动是提升直播体验、增强社区氛围和实现商业变现的关键。`live-im-service` 的目的在于构建一个**超低延迟、超高并发、专为直播场景优化的实时消息广播系统**。它负责处理直播间内所有**非媒体数据**的实时交互，如弹幕、点赞、礼物赠送、用户加入/离开通知等。

#### 1.2. 服务范围
本服务 **负责**:
*   **海量WebSocket连接管理**: 接受并维护来自直播间所有参与者（主播、观众）的长期WebSocket连接。
*   **房间(Room)与订阅管理**:
    *   处理用户进入和离开特定直播间的逻辑。
    *   在分布式环境中，维护每个直播间的在线用户订阅列表。
*   **实时消息广播**:
    *   接收客户端发送的互动消息（弹幕、点赞、礼物等）。
    *   以**极低延迟**，将消息广播给同一直播间内的**所有在线成员**。
*   **权限与反垃圾**:
    *   执行基本的发言权限检查（如用户是否被禁言）。
    *   实施高频消息的速率限制，防止“刷屏”攻击。
*   **与业务服务的协同**:
    *   在处理礼物等付费互动时，调用`billing-service`或`cina-coin-ledger-service`进行扣款。

本服务 **不负责**:
*   **任何媒体流数据的处理与分发**: 由媒体服务器集群负责。
*   **直播间的业务逻辑和状态管理**: 由`live-api-service`负责。
*   **互动消息的持久化存储**: 本服务是一个**近乎无状态**的实时消息总线，**默认不存储**弹幕等消息。如果需要，只会将聚合后的数据或采样数据异步发送到分析系统。
*   **通用的1对1或群组聊天**: 由`chat-websocket-server`和`chat-api-service`负责。本服务专为直播间的“广播”场景优化。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 直播间的所有参与者通过WebSocket与本服务通信。
*   **`live-api-service`**: (调用本服务) 在需要时（如发送系统消息、全局禁言）可以通过内部API或消息队列向本服务发送指令。
*   **`billing-service` / `cina-coin-ledger-service`**: (被本服务调用) 处理礼物赠送的扣款。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`live-im-service` 是CINA.CLUB直播生态的“**现场气氛组**”和“**互动消息总线**”。它与负责媒体流的`live-gateway`和负责业务逻辑的`live-api`构成了直播系统的三驾马车。本服务的设计完全为**大规模、低延迟的广播场景**进行优化，其性能直接决定了直播互动的实时性和流畅度。它与`chat-websocket-server`是并行的实时系统，但场景和优化方向完全不同（广播 vs. 持久化小群聊）。

#### 2.2. 主要功能概述
*   支持超大规模并发WebSocket连接。
*   基于Redis Pub/Sub的、极低延迟的多实例消息广播。
*   专为直播间优化的、轻量级的实时互动协议。
*   与计费系统协同，支持实时虚拟礼物。
*   内置防刷屏和速率限制机制。

---

### 3. 核心流程图

#### 3.1. 观众B发送弹幕，广播给房间内所有人

```mermaid
sequenceDiagram
    participant ClientB as "观众B"
    participant IMService_1 as "IM-Service-Node-1"
    participant Redis_PubSub as "Redis (Pub/Sub)"
    participant IMService_2 as "IM-Service-Node-2"
    participant ClientA as "主播A"
    participant ClientC as "观众C"

    ClientB->>IMService_1: 1. [C2S_SEND_BARRAGE] { content: "666" }
    
    IMService_1->>IMService_1: 2. 权限校验 & 速率限制
    
    alt 校验通过
        IMService_1->>Redis_PubSub: 3. **PUBLISH** on channel `live_im_room:{roomId}` with BarrageMessagePayload
    else 校验失败
        IMService_1-->>ClientB: [S2C_ERROR] { message: "发言太频繁" }
    end
    
    Redis_PubSub-->>IMService_1: (Receive message)
    Redis_PubSub-->>IMService_2: (Receive message)
    
    IMService_1-->>ClientB: 4a. [S2C_NEW_BARRAGE] (消息回显给自己)
    IMService_2-->>ClientA: 4b. [S2C_NEW_BARRAGE]
    IMService_2-->>ClientC: 4c. [S2C_NEW_BARRAGE]
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 连接与房间管理
*   **FR4.1.1 (连接认证)**:
    *   客户端在连接WebSocket后，必须发送一个`C2S_AUTHENTICATE`事件，其中包含有效的用户JWT和要进入的`roomID`。
    *   服务验证JWT，并调用`live-api-service`检查用户是否有权进入该房间。
*   **FR4.1.2 (房间订阅)**: 认证成功后，服务实例必须将该客户端的连接信息，添加到该`roomID`的本地订阅列表中，并将`userID`注册到Redis的全局房间成员集合中。
*   **FR4.1.3 (心跳与断线)**: 必须实现心跳保活机制。当连接断开时，必须将其从本地和全局的房间订阅列表中移除，并广播一条`S2C_USER_LEAVE`消息。

#### 4.2. 实时消息处理
*   **FR4.2.1 (弹幕消息)**:
    *   接收`C2S_SEND_BARRAGE`事件。
    *   **必须**对用户的发言频率进行速率限制（如每秒最多1条）。
    *   **必须**检查用户是否被禁言。
    *   通过Redis Pub/Sub将`S2C_NEW_BARRAGE`广播给房间内所有成员。
*   **FR4.2.2 (点赞消息)**:
    *   接收`C2S_SEND_LIKE`事件。
    *   为了性能，点赞消息**不应**逐条广播。应在服务器端进行**聚合**，例如每秒钟将该秒内收到的所有点赞消息，聚合成一条`S2C_LIKE_BURST`消息（包含点赞总数）进行广播。
*   **FR4.2.3 (礼物消息 - 核心商业化)**:
    *   接收`C2S_SEND_GIFT`事件，包含`giftId`和`count`。
    *   **这是一个Saga事务**:
        a. **[Execute]** **同步地**调用`cina-coin-ledger-service`的`Debit`接口，尝试扣除用户账户中的虚拟货币。
        b. **如果扣款成功**: 通过Redis Pub/Sub广播一条`S2C_NEW_GIFT`消息，其中包含赠送者、接收者、礼物信息和特效。
        c. **如果扣款失败**: 向发送方客户端返回一个`S2C_ERROR`消息，告知余额不足。

#### 4.3. 系统与状态消息
*   **FR4.3.1 (用户进出)**: 当有用户加入或离开房间时，**必须**广播`S2C_USER_ENTER` / `S2C_USER_LEAVE`消息。
*   **FR4.3.2 (在线列表)**: （可选，用于小房间）提供API或事件，获取房间当前的在线用户列表。对于大房间，只应显示部分代表性用户。
*   **FR4.3.3 (系统公告)**: 必须提供一个内部接口（如消费一个Kafka消息），允许`live-api-service`向指定直播间广播一条系统消息（如`S2C_NEW_ANNOUNCEMENT`）。

---

### 5. 接口需求: WebSocket事件协议

*   **消息格式**: **Protocol Buffers (Protobuf)**。
*   **核心C2S事件 (Client-to-Server)**:
    *   `AuthenticateRequest { user_jwt: string, room_id: string }`
    *   `SendBarrageRequest { content: string }`
    *   `SendLikeRequest { count: int32 }`
    *   `SendGiftRequest { gift_id: string, count: int32, target_user_id: string }`
    *   `Ping {}`
*   **核心S2C事件 (Server-to-Client)**:
    *   `AuthenticationResult { success: boolean, room_info: RoomInfo, online_users: UserList }`
    *   `NewBarrageEvent { from_user: UserInfo, content: string }`
    *   `LikeBurstEvent { count: int32 }`
    *   `NewGiftEvent { from_user: UserInfo, to_user: UserInfo, gift: GiftInfo, count: int32 }`
    *   `UserEnterEvent { user: UserInfo }`
    *   `UserLeaveEvent { user_id: string }`
    *   `ErrorEvent { code: int32, message: string }`
    *   `Pong {}`

---

### 6. 数据需求 (运行时数据)

本服务是**近乎无状态**的，所有跨实例的共享状态都存储在**Redis**中。

*   **`live:im:room_members:{roomId}` (Redis Set)**: 存储一个房间所有在线用户的`userID`集合。
*   **`live:im:user_rooms:{userId}` (Redis Set)**: 存储一个用户当前所在的所有房间的`roomId`集合。
*   **`live:im:user_mute_status:{roomId}:{userId}` (Redis String with TTL)**: 存储用户的禁言状态和时长。
*   **`live:im:barrage_rate_limit:{roomId}:{userId}` (Redis Counter with TTL)**: 用于发言频率控制。
*   **Redis Pub/Sub Channels**:
    *   `live_im_room:{roomId}`: 用于广播指定房间的互动消息。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与延迟 - 最高优先级)**:
    *   **消息广播端到端延迟**: 从一个客户端发送消息，到同一房间的其他客户端接收到消息，P99延迟**必须 `< 200ms**。
    *   **并发连接数 (CCU)**: 单个服务实例应能处理**50,000+**的并发WebSocket连接。整个集群应能支持百万级总CCU。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。
    *   **无单点故障**: 通过多实例部署和Redis集群实现。
*   **NFR7.3 (可扩展性)**: 系统必须支持通过增加服务器实例来线性扩展总并发连接数。
*   **NFR7.4 (成本控制)**: 消息处理（特别是点赞）的聚合策略，是控制Redis Pub/Sub和客户端CPU负载的关键。

---

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **WebSocket库**: `gorilla/websocket` 或 `nhooyr.io/websocket`。
*   **核心架构**: 与`chat-websocket-server`类似，采用**Hub + Client Goroutine**的模式，但**为广播场景进行深度优化**。
*   **状态管理**: **Redis**是实现多实例协同的唯一和最佳选择。
*   **与业务解耦**: 严格遵守边界，付费逻辑调用`billing/ledger`服务，业务状态查询调用`live-api`服务。本服务只做实时消息的路由和广播。

---
这份SRS为`live-im-service`的设计和实现提供了坚实、全面的指导。通过将其设计为一个专为大规模广播场景优化的、轻量级的实时消息总线，CINA.CLUB平台能够为其直播功能提供一个**互动性强、体验流畅、且高度可扩展**的互动层。