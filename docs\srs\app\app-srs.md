好的，遵照您的指示。我将为您生成一份专门针对 **`apps/` 目录** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义CINA.CLUB平台下所有前端应用（包括后台管理）的功能范围、技术架构、模块化策略和非功能性需求。它将作为所有前端团队的**顶层设计纲领**和**开发规范**。

---
### CINA.CLUB - 前端应用层 (`apps/`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [前端总架构师]  
**审批人:** [CTO, 产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [通用功能需求](#3-通用功能需求)
4.  [平台特定应用需求](#4-平台特定应用需求)
    *   [4.1 `apps/admin-frontend`: 后台管理系统](#41-appsadmin-frontend-后台管理系统)
    *   [4.2 `apps/android`: Android原生应用](#42-appsandroid-android原生应用)
    *   [4.3 `apps/harmony`: HarmonyOS原生应用](#43-appsharmony-harmonyos原生应用)
    *   [4.4 `apps/apple`: Apple生态原生应用](#44-appsapple-apple生态原生应用)
    *   [4.5 `apps/web`: 面向用户的Web应用](#45-appsweb-面向用户的web应用)
    *   [4.6 `apps/windows`: Windows原生应用](#46-appswindows-windows原生应用)
5.  [通用非功能性需求](#5-通用非功能性需求)
6.  [技术架构与原则](#6-技术架构与原则)

---

### 1. 引言

#### 1.1. 项目背景与目的
`apps/` 目录包含了CINA.CLUB平台所有面向最终用户和内部员工的客户端应用程序。本SRS旨在为这些应用定义一个统一的、高层次的架构愿景和功能需求，确保在多个不同技术栈和目标平台上，依然能提供**一致的核心体验、极致的性能和最高的安全性**。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   定义每个前端应用的总体架构、技术选型和模块化策略。
    *   定义所有前端应用必须实现的通用功能（如认证、E2EE、多语言）。
    *   定义每个应用需要适配和呈现的核心业务功能。
    *   定义前端的性能、安全和可靠性标准。
*   **范围之外 (Out-of-Scope)**:
    *   具体的UI像素级设计。
    *   后端服务的实现细节。

#### 1.3. 目标用户
*   **最终用户**: CINA.CLUB的普通消费者、服务提供方等。
*   **内部员工**: 平台管理员、运营、客服等。

---

### 2. 总体描述

#### 2.1. 核心设计哲学: “共享核心，原生呈现”
所有前端应用都遵循这一核心哲学。它们共享一个用Go语言编写的、位于`/core`目录的核心逻辑库，但使用各自平台的最佳原生UI框架进行界面渲染和交互，以实现性能和体验的最大化。

#### 2.2. 应用清单
*   **`admin-frontend`**: 基于Web的企业级后台管理系统。
*   **`android`**: 面向Android手机和平板的原生应用。
*   **`harmony`**: 面向华为HarmonyOS设备的原生应用。
*   **`apple`**: 面向iOS, iPadOS, macOS, watchOS, visionOS的通用应用。
*   **`web`**: 面向现代浏览器的轻量级Web门户。
*   **`windows`**: 面向Windows桌面的原生应用。

---

### 3. 通用功能需求 (所有应用必须实现)

*   **FR3.1 (用户认证与会话)**:
    *   **FR3.1.1**: 必须支持基于`user-core-service`的手机/密码登录和令牌刷新机制。
    *   **FR3.1.2**: 必须安全地存储认证令牌（在内存和系统安全区中）。
    *   **FR3.1.3**: 必须能在API请求中自动附加有效的Access Token。
*   **FR3.2 (端到端加密 - E2EE)**:
    *   **FR3.2.1**: **必须**在本地实现一个安全的密钥管理模块，用于从用户主密码派生和管理加密密钥（DEK）。
    *   **FR3.2.2**: 在处理PKB和个人记忆等敏感数据时，**必须**在发送到网络前进行本地加密，在从网络接收后进行本地解密。
    *   **FR3.2.3**: 所有加密/解密操作**必须**调用由`/core/crypto`编译的共享核心库。
*   **FR3.3 (数据同步)**:
    *   **FR3.3.1**: **必须**实现与`cloud-sync-service`交互的客户端同步协议，用于E2EE数据的备份和跨设备同步。
    *   **FR3.3.2**: 所有同步协议的逻辑**必须**调用由`/core/datasync`编译的共享核心库。
*   **FR3.4 (本地AI)**:
    *   **FR3.4.1**: 必须实现一个模型管理器，能从`model-management-service`下载、缓存和更新端侧AI模型。
    *   **FR3.4.2**: **必须**能调用由`/core/aic`编译的共享核心库，在设备上执行模型推理。
*   **FR3.5 (实时通信)**:
    *   **FR3.5.1**: 必须能与`chat-websocket-server`和`live-im-service`建立和维护一个或多个稳定的WebSocket长连接。
    *   **FR3.5.2**: 必须能处理Protobuf格式的二进制WebSocket消息。
*   **FR3.6 (国际化 - i18n)**:
    *   **FR3.6.1**: 所有UI文本**必须**通过i18n key进行引用，而不是硬编码。
    *   **FR3.6.2**: 必须能根据用户偏好或系统设置，从CDN动态加载对应的语言包。

---

### 4. 平台特定应用需求

#### 4.1. `apps/admin-frontend`: 后台管理系统
*   **FR4.1.1 (功能范围)**: 必须提供对用户、内容、交易、配置等所有平台核心资源的CRUD管理界面。
*   **FR4.1.2 (技术架构)**:
    *   **类型**: 单页应用 (SPA)。
    *   **UI框架**: React + Ant Design Pro。
    *   **API交互**: 通过RESTful API与`admin-bff-service`交互。
*   **FR4.1.3 (核心特性)**:
    *   **数据密集型界面**: 必须提供强大的表格、表单、图表和数据可视化能力。
    *   **精细化权限**: UI的菜单、页面、按钮必须能根据登录员工的角色和权限动态渲染。
    *   **SSO集成**: 必须支持与公司IT系统的单点登录。

#### 4.2. `apps/android`: Android原生应用
*   **FR4.2.1 (功能范围)**: 实现CINA.CLUB面向最终用户的所有核心功能。
*   **FR4.2.2 (技术架构)**:
    *   **语言/UI**: Kotlin + Jetpack Compose。
    *   **架构模式**: MVI (Model-View-Intent) 或 MVVM。
    *   **核心逻辑**: 通过JNI调用Go Mobile编译的`.aar`库。
*   **FR4.2.3 (核心特性)**:
    *   **原生体验**: UI和交互必须严格遵循Material Design规范。
    *   **系统集成**: 深度集成Android特性，如后台任务、通知渠道、小组件(Widgets)。
    *   **性能**: 必须为低端机型进行性能优化，控制内存和电量消耗。

#### 4.3. `apps/harmony`: HarmonyOS原生应用
*   **FR4.3.1 (功能范围)**: 目标是实现与Android应用对等的核心功能。
*   **FR4.3.2 (技术架构)**:
    *   **语言/UI**: ArkTS + ArkUI。
    *   **核心逻辑**: **优先复用**Android的Go Mobile `.aar`库（通过HarmonyOS的兼容层）。
*   **FR4.3.3 (核心特性)**:
    *   **UI适配**: UI风格需要适配HarmonyOS的设计语言。
    *   **特性利用**: 利用HarmonyOS的分布式能力，如卡片(Widgets)、跨设备流转。
    *   **代码复用**: 架构上应最大化地复用`apps/android`中与平台无关的ViewModel和Service层代码。

#### 4.4. `apps/apple`: Apple生态原生应用
*   **FR4.4.1 (功能范围)**: 在iOS, iPadOS, macOS等平台上提供核心功能，并在watchOS, visionOS上提供适配的轻量级功能。
*   **FR4.2.2 (技术架构)**:
    *   **语言/UI**: Swift + SwiftUI。
    *   **架构模式**: MVVM-C 或 TCA。
    *   **核心逻辑**: 通过桥接调用Go Mobile编译的`.xcframework`。
*   **FR4.2.3 (核心特性)**:
    *   **生态统一**: 一套核心代码库，通过SPM进行模块化，服务于所有Apple设备。
    *   **原生体验**: 严格遵循Apple Human Interface Guidelines (HIG)。
    *   **平台适配**: 为macOS提供窗口管理和菜单栏支持；为watchOS提供 complication 和独立网络能力；为visionOS提供空间计算的UI适配。

#### 4.5. `apps/web`: 面向用户的Web应用
*   **FR4.5.1 (功能范围)**: 提供一个轻量级的门户和核心功能的Web版本，侧重于内容展示和新用户引导。
*   **FR4.5.2 (技术架构)**:
    *   **框架**: SvelteKit。
    *   **核心逻辑**: 通过JavaScript胶水代码调用Go编译的`.wasm`模块。
*   **FR4.5.3 (核心特性)**:
    *   **性能与SEO**: 必须利用SvelteKit的服务端渲染(SSR)和静态站点生成(SSG)能力，以获得最佳的加载性能和搜索引擎友好性。
    *   **响应式设计**: 必须完美适配桌面和移动浏览器。

#### 4.6. `apps/windows`: Windows原生应用
*   **FR4.6.1 (功能范围)**: 提供一个功能强大的、面向专业用户和内容创作者的桌面客户端。
*   **FR4.6.2 (技术架构)**:
    *   **语言/UI**: C# + WinUI 3。
    *   **核心逻辑**: 通过P/Invoke调用Go编译的C-shared library (`.dll`)。
*   **FR4.6.3 (核心特性)**:
    *   **系统集成**: 深度集成Windows特性，如文件系统、多窗口、系统托盘、通知中心。
    *   **生产力**: 侧重于提供高效的多任务处理和内容创作工具。

---

### 5. 通用非功能性需求

*   **NFR5.1 (性能)**: 所有应用必须满足其目标平台的性能基线（启动时间、帧率、响应延迟）。
*   **NFR5.2 (可靠性)**: 移动端应用的Crash-Free Users > 99.8%。
*   **NFR5.3 (安全性)**:
    *   **必须**安全地处理和存储用户凭证。
    *   **必须**对所有用户输入进行验证和清理，防止注入等攻击。
    *   **必须**使用HTTPS进行所有网络通信。
*   **NFR5.4 (可访问性 - Accessibility)**: 所有面向用户的应用都应遵循WCAG 2.1 AA或平台等效标准，支持屏幕阅读器、动态字体和键盘导航。
*   **NFR5.5 (电量与资源消耗)**: 移动端应用在后台时，必须严格控制其CPU、网络和电量消耗。

---

### 6. 技术架构与原则

所有前端应用都必须遵循平台顶层SRS中定义的**“Go-Centric全栈”**技术架构和**“共享核心，原生呈现”**的设计哲学。

*   **共享库**:
    *   **`/core` (Go)**: 是所有应用最底层、最核心的逻辑来源。
    *   **`/packages` (TypeScript/JS - 如果使用RN)** 或 **原生包管理器中的共享模块 (SPM/Gradle)**: 是各平台内部的、与UI相关的逻辑和组件的复用单元。
*   **API交互**:
    *   所有应用都通过由`/core/api`自动生成的、类型安全的客户端与后端通信。
    *   使用`TanStack Query`模式（或其原生等效实现）来管理服务器状态。

---
这份SRS为CINA.CLUB平台的所有前端应用提供了一个统一的、高层次的、可执行的设计纲领。它通过一个雄心勃勃但技术上清晰的**多原生平台 + 共享Go核心**战略，确保了在为每个平台提供最佳用户体验的同时，最大化地复用了核心业务逻辑，实现了开发效率和最终产品质量的最佳平衡。