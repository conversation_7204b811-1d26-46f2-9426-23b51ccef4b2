# Activity Feed Service

Activity feed service for CINA.CLUB platform, providing users with a unified, clear, and intelligent activity and notification center.

## 📋 Table of Contents

- [Project Overview](#project-overview)
- [Core Features](#core-features)
- [Architecture Design](#architecture-design)
- [Quick Start](#quick-start)
- [API Documentation](#api-documentation)
- [Configuration](#configuration)
- [Deployment Guide](#deployment-guide)
- [Monitoring & Operations](#monitoring--operations)
- [Development Guide](#development-guide)

## 🎯 Project Overview

Activity Feed Service is one of the core components of the CINA.CLUB platform, responsible for:

- **Event-driven Activity Feed Generation**: Consuming business events from various platform services to generate personalized activity feeds for users
- **Multi-Feed Stream Management**: Supporting three different types of feed streams: notifications, interactions, and following
- **Intelligent Aggregation & Noise Reduction**: Automatically aggregating similar activities to reduce information noise and improve user experience
- **Real-time Unread Count Management**: Accurately tracking unread status for each feed stream with real-time updates
- **High-performance Timeline Queries**: Optimized paginated queries supporting fast retrieval of massive data

### Business Value

- **Information Integration**: Unifying user-related events scattered across various services
- **User Experience**: Providing clear dynamic perception by avoiding information overload through intelligent aggregation
- **Real-time**: Supporting real-time push notifications to ensure users receive important information promptly
- **Scalability**: Event-driven architecture that easily extends to new activity types

## 🌟 Core Features

### Multi-Feed Stream Support

- **Notifications Feed**: High-priority information such as system announcements, account security, task status changes
- **Interactions Feed**: Social interaction information such as likes, comments, mentions, follows
- **Following Feed**: New content dynamics from followed users

### Intelligent Aggregation Mechanism

```
Original events:
- John liked your post
- Jane liked your post
- Bob liked your post

After aggregation:
- John, Jane, Bob and 3 others liked your post
```

### Real-time Unread Count Management

- Support for unread counts by feed type
- Atomic increment/decrement operations
- Real-time push of unread count change events

### High-performance Data Storage

- **MongoDB/Cassandra**: Optimal choice for timeline data
- **Redis**: Unread count caching and aggregation lock management
- **Sharding Strategy**: Sharding by user ID, supporting horizontal scaling

## 🏗️ Architecture Design

### Overall Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Event Bus     │    │  Activity Feed  │    │  WebSocket      │
│   (Kafka)       │───▶│    Service      │───▶│    Server       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │    Storage      │
                       │  MongoDB/Redis  │
                       └─────────────────┘
```

### Core Components

- **Event Consumer**: Consumes platform events and triggers feed generation
- **Aggregation Engine**: Intelligent aggregation logic processor
- **Feed Manager**: CRUD management for feed items
- **Unread Counter**: Precise unread count management
- **API Gateway**: RESTful API external service

### Event Flow Processing

```mermaid
sequenceDiagram
    participant ES as Event Source
    participant AFS as Activity Feed Service
    participant AGG as Aggregation Engine
    participant DB as Database
    participant REDIS as Redis
    participant WS as WebSocket Server

    ES->>AFS: Publish Event
    AFS->>AGG: Process with Aggregation
    AGG->>DB: Create/Update Feed Item
    AGG->>REDIS: Update Unread Count
    AFS->>WS: Publish Unread Count Event
    WS->>Client: Real-time Update
```

## 🚀 Quick Start

### Environment Requirements

- Go 1.22+
- MongoDB 4.4+
- Redis 6.0+
- Kafka 2.8+

### Local Development

1. **Clone Repository**
```bash
cd services/activity-feed-service
```

2. **Install Dependencies**
```bash
go mod download
```

3. **Start Dependent Services**
```bash
# Use Docker Compose to start MongoDB, Redis, Kafka
docker-compose up -d mongodb redis kafka
```

4. **Configure Environment**
```bash
cp configs/config.yaml configs/config.local.yaml
# Edit configuration file to match local environment
```

5. **Run Service**
```bash
go run cmd/server/main.go
```

6. **Verify Service**
```bash
curl http://localhost:8080/health
```

### Docker Deployment

```bash
# Build image
docker build -t activity-feed-service .

# Run container
docker run -p 8080:8080 \
  -e MONGODB_URI=mongodb://localhost:27017 \
  -e REDIS_ADDR=localhost:6379 \
  -e KAFKA_BROKERS=localhost:9092 \
  activity-feed-service
```

## 📚 API Documentation

### User Feed API

#### Get Feed Summary
```http
GET /api/v1/activity-feed/me/feeds/summary
Authorization: Bearer <token>
```

**Response Example:**
```json
{
  "user_id": "user123",
  "unread_counts": {
    "NOTIFICATIONS": 5,
    "INTERACTIONS": 23,
    "FOLLOWING": 2
  },
  "total_unread": 30,
  "last_updated_at": "2024-06-19T10:30:00Z"
}
```

#### Get Feed Items List
```http
GET /api/v1/activity-feed/me/feeds/{feedType}?page=1&limit=20
Authorization: Bearer <token>
```

**Path Parameters:**
- `feedType`: `notifications` | `interactions` | `following`

**Query Parameters:**
- `page`: Page number, default 1
- `limit`: Items per page, default 20, max 100

**Response Example:**
```json
{
  "items": [
    {
      "id": "item123",
      "item_id": "uuid-123",
      "user_id": "user123",
      "feed_type": "INTERACTIONS",
      "activity_type": "POST_LIKE",
      "created_at": "2024-06-19T10:30:00Z",
      "updated_at": "2024-06-19T10:30:00Z",
      "is_read": false,
      "is_aggregated": true,
      "aggregated_actors": [
        {"id": "user456", "name": "John"},
        {"id": "user789", "name": "Jane"}
      ],
      "aggregation_count": 2,
      "display": {
        "title": "John, Jane and 2 others liked your post",
        "actors": [
          {"id": "user456", "name": "John"},
          {"id": "user789", "name": "Jane"}
        ],
        "target": {
          "id": "post123",
          "name": "My Amazing Post",
          "type": "POST"
        },
        "icon_url": "/icons/like.png"
      },
      "deep_link_url": "cinaclub://post/post123"
    }
  ],
  "total_count": 156,
  "has_more": true,
  "next_offset": 20
}
```

#### Mark Items as Read
```http
POST /api/v1/activity-feed/items/mark-read
Authorization: Bearer <token>
Content-Type: application/json

{
  "item_ids": ["item123", "item456"]
}
```

#### Mark Entire Feed as Read
```http
POST /api/v1/activity-feed/me/feeds/{feedType}/mark-all-read
Authorization: Bearer <token>
```

### Admin API

#### Create System Announcement
```http
POST /api/v1/activity-feed/admin/system-announcement
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "title": "System Maintenance Notice",
  "message": "The system will undergo maintenance tonight from 22:00-23:00. Services may be unavailable during this period.",
  "deep_link_url": "cinaclub://announcement/123",
  "target_users": ["user123", "user456"],
  "priority": 3
}
```

#### Get User Activity Statistics
```http
GET /api/v1/activity-feed/admin/users/{userID}/stats?since=2024-01-01T00:00:00Z
Authorization: Bearer <admin-token>
```

#### Cleanup Expired Data
```http
POST /api/v1/activity-feed/admin/cleanup
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "retention_days": 90
}
```

## ⚙️ Configuration

### Main Configuration Items

```yaml
# Service configuration
service:
  name: "activity-feed-service"
  version: "1.0.0"
  port: 8080

# Database configuration
database:
  mongodb:
    uri: "mongodb://localhost:27017"
    database: "cina_club_activity_feed"
  redis:
    addresses: ["localhost:6379"]

# Feed service configuration
feed_service:
  pagination:
    default_page_size: 20
    max_page_size: 100
  aggregation:
    default_time_window: "5m"
    max_aggregation_size: 50
  cleanup:
    retention_period: "90d"
```

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Service port | 8080 |
| `MONGODB_URI` | MongoDB connection string | mongodb://localhost:27017 |
| `REDIS_ADDR` | Redis address | localhost:6379 |
| `KAFKA_BROKERS` | Kafka broker list | localhost:9092 |
| `JWT_SECRET` | JWT secret | - |
| `LOG_LEVEL` | Log level | info |

## 🚢 Deployment Guide

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: activity-feed-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: activity-feed-service
  template:
    metadata:
      labels:
        app: activity-feed-service
    spec:
      containers:
      - name: activity-feed-service
        image: cina-club/activity-feed-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: uri
        - name: REDIS_ADDR
          value: "redis-service:6379"
        - name: KAFKA_BROKERS
          value: "kafka-service:9092"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Service Configuration

```yaml
apiVersion: v1
kind: Service
metadata:
  name: activity-feed-service
spec:
  selector:
    app: activity-feed-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

## 📊 Monitoring & Operations

### Health Checks

```bash
# Basic health check
curl http://localhost:8080/health

# Detailed health check
curl http://localhost:8080/health?detail=true
```

### Metrics Monitoring

The service provides Prometheus format metrics:

```bash
curl http://localhost:8080/metrics
```

**Key Metrics:**
- `activity_feed_events_processed_total`: Total number of processed events
- `activity_feed_items_created_total`: Total number of created feed items
- `activity_feed_aggregations_performed_total`: Number of aggregations performed
- `activity_feed_api_requests_duration_seconds`: API request duration
- `activity_feed_unread_count_updates_total`: Number of unread count updates

### Log Monitoring

Log format supports JSON and text formats, including the following key fields:
- `timestamp`: Timestamp
- `level`: Log level
- `message`: Log message
- `user_id`: User ID (if applicable)
- `event_type`: Event type (if applicable)
- `feed_type`: Feed type (if applicable)
- `trace_id`: Trace ID

### Performance Optimization

**Database Optimization:**
```javascript
// MongoDB indexes
db.activity_feed_items.createIndex({ "user_id": 1, "feed_type": 1, "created_at": -1 })
db.activity_feed_items.createIndex({ "user_id": 1, "is_read": 1, "feed_type": 1 })
```

**Redis Optimization:**
- Use Hash structure to store user unread counts
- Set reasonable TTL to avoid memory leaks
- Enable Redis Cluster for horizontal scaling support

## 💻 Development Guide

### Project Structure

```
services/activity-feed-service/
├── cmd/
│   └── server/             # Service entry point
├── internal/
│   ├── adapter/            # Adapter layer
│   │   ├── event/          # Event handling
│   │   ├── cache/          # Cache adapter
│   │   ├── grpc/           # gRPC adapter
│   │   └── repository/     # Data access
│   ├── application/        # Application layer
│   │   ├── handlers/       # Event handlers
│   │   └── query/          # Query services
│   └── domain/             # Domain layer
│       ├── models/         # Domain models
│       └── interfaces/     # Domain interfaces
├── configs/                # Configuration files
├── scripts/                # Script tools
├── docs/                   # Documentation
└── README.md
```

### Adding New Event Types

1. **Define Event Structure**
```go
// internal/domain/events.go
type NewEventType struct {
    BaseEvent
    // Add specific fields
}
```

2. **Implement Event Handler**
```go
// internal/application/handlers/new_event_handler.go
func (h *NewEventHandler) Handle(ctx context.Context, event domain.Event) error {
    // Implement handling logic
}
```

3. **Register Event Mapping**
```go
// internal/application/dispatcher.go
d.eventHandlers["new_event_type"] = "new_event_handler"
```

### Testing

Run tests:
```bash
go test ./...
```

Run tests with coverage:
```bash
go test -cover ./...
```

### Code Quality

Format code:
```bash
go fmt ./...
```

Run linter:
```bash
golangci-lint run
```

### Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

If you have any questions or need help, please:

1. Check the [documentation](docs/)
2. Search [existing issues](https://github.com/cina-club/activity-feed-service/issues)
3. Create a [new issue](https://github.com/cina-club/activity-feed-service/issues/new)

## 🔄 Changelog

See [CHANGELOG.md](CHANGELOG.md) for details about changes in each version.

---

Made with ❤️ by the CINA.CLUB team 