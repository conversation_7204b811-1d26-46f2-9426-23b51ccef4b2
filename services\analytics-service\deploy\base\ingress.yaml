# CINA.CLUB Platform - Analytics Service Ingress Configuration
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Analytics Service Ingress - Big Data Processing and Business Intelligence
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: analytics-ingress
  namespace: analytics
  labels:
    app: analytics-service
    component: ingress
    tier: application
    service: analytics
  annotations:
    # Kong Ingress Controller configuration
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Analytics requires authentication + standard rate limits
    konghq.com/plugins: "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
    
    # Protocol configuration for gRPC backend
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Analytics-optimized timeouts (longer for big data processing)
    konghq.com/read-timeout: "180000"       # 3 minutes for analytics processing
    konghq.com/write-timeout: "180000"      # 3 minutes
    konghq.com/connect-timeout: "5000"      # 5 seconds
    
    # Upstream configuration
    konghq.com/host-header: "analytics-service.analytics.svc.cluster.local"
    
    # Description and metadata
    description: "Analytics Service for business intelligence and data insights"
    service-owner: "<EMAIL>"
    api-version: "v1"

spec:
  # TLS configuration
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  
  rules:
    - host: "api.cina.club"
      http:
        paths:
          # User analytics and insights (JWT required)
          - path: /api/v1/analytics/users
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Content analytics and metrics
          - path: /api/v1/analytics/content
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Platform usage analytics
          - path: /api/v1/analytics/usage
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Performance metrics
          - path: /api/v1/analytics/performance
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Custom reports and dashboards
          - path: /api/v1/analytics/reports
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Real-time analytics
          - path: /api/v1/analytics/realtime
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Export and data downloads
          - path: /api/v1/analytics/exports
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080

---
# Admin Analytics Endpoints (for business intelligence)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: analytics-admin-ingress
  namespace: analytics
  labels:
    app: analytics-service
    component: ingress
    tier: admin
    security-level: admin
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Admin analytics require admin authentication
    konghq.com/plugins: "jwt-validator-admin, rate-limit-admin, cors-restrictive, prometheus-metrics, opentelemetry-tracing, request-id"
    
    # Protocol configuration
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Extended timeouts for complex admin reports
    konghq.com/read-timeout: "300000"       # 5 minutes for complex admin reports
    konghq.com/write-timeout: "300000"      # 5 minutes
    
    # Description
    description: "Admin analytics endpoints for business intelligence and platform insights"

spec:
  tls:
    - hosts:
        - "admin-api.cina.club"
      secretName: "cina-club-admin-api-tls"
  
  rules:
    - host: "admin-api.cina.club"
      http:
        paths:
          # Platform-wide analytics
          - path: /api/v1/admin/analytics/platform
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Revenue and business metrics
          - path: /api/v1/admin/analytics/revenue
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # User behavior analytics
          - path: /api/v1/admin/analytics/behavior
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # A/B testing results
          - path: /api/v1/admin/analytics/experiments
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # System performance analytics
          - path: /api/v1/admin/analytics/system
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080

---
# Public Analytics Endpoints (limited insights)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: analytics-public-ingress
  namespace: analytics
  labels:
    app: analytics-service
    component: ingress
    tier: public
    security-level: public
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Public analytics with IP-based rate limiting
    konghq.com/plugins: "rate-limit-ip, cors-global, prometheus-metrics, request-id"
    
    # Protocol configuration
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Description
    description: "Public analytics endpoints for general platform statistics"

spec:
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  
  rules:
    - host: "api.cina.club"
      http:
        paths:
          # Public platform statistics
          - path: /api/v1/analytics/public/stats
            pathType: Prefix
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080
          
          # Service health check
          - path: /api/v1/analytics/health
            pathType: Exact
            backend:
              service:
                name: analytics-service
                port:
                  number: 8080

---
# Kong Service for Analytics Service
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: analytics-kong-service
  namespace: analytics
  labels:
    app: analytics-service
    component: kong-service
  annotations:
    description: "Kong service configuration for Analytics Service"

spec:
  # Backend service configuration
  host: "analytics-service.analytics.svc.cluster.local"
  port: 8080
  protocol: "grpc"                         # gRPC protocol for backend
  
  # Analytics-optimized connection settings (longer for big data)
  connect_timeout: 5000                    # 5 seconds
  read_timeout: 180000                     # 3 minutes for analytics processing
  write_timeout: 180000                    # 3 minutes
  
  # Standard retry configuration for analytics operations
  retries: 2                               # 2 retries for analytics operations
  
  # Load balancing
  path: "/"

---
# Kong Upstream for Analytics Service
apiVersion: configuration.konghq.com/v1
kind: KongUpstream
metadata:
  name: analytics-upstream
  namespace: analytics
  labels:
    app: analytics-service
    component: kong-upstream
  annotations:
    description: "Kong upstream for Analytics Service with big data processing support"

spec:
  # Load balancing algorithm for analytics workloads
  algorithm: "least-connections"           # Better for long-running analytics requests
  
  # Health checks for analytics services
  healthchecks:
    active:
      http_path: "/health"
      https_verify_certificate: false
      healthy:
        interval: 30                       # Less frequent for analytics services
        successes: 2
      unhealthy:
        interval: 20
        tcp_failures: 3                    # Tolerance for analytics processing loads
        http_failures: 3
        timeouts: 3
    
    passive:
      healthy:
        successes: 2
      unhealthy:
        tcp_failures: 3
        http_failures: 3
        timeouts: 3
  
  # Connection slots for analytics service
  slots: 500                               # Moderate slots for analytics processing 