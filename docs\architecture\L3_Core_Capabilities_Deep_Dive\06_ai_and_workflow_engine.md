# 文件6: `docs/architecture/L3_Core_Capabilities_Deep_Dive/06_ai_and_workflow_engine.md`

(内容大G纲)
*   **概述**: 剖析平台AI和自动化能力的核心技术实现。
*   **工作流引擎 (`pkg/workflow`)**:
    *   **设计哲学**: 无状态、可嵌入的DAG执行器。
    *   **核心概念**: 图(Graph)、节点(Node)、边(Edge)、执行器(Executor)、节点执行器接口(`NodeExecutor`)。
    *   **数据流**: 解释如何使用`{{ .nodes.node_1.outputs.result }}`语法实现节点间数据传递。
    *   **控制流**: 解释如何通过`condition`节点实现条件分支。
*   **智能代理架构 (`ai-assistant-service`)**:
    *   **ReAct模式**: 描述如何通过“思考 -> 动作 -> 观察”的循环，让LLM动态地构建和执行工作流。
    *   **工具(Tool)的定义**: 解释每个工具（对内API调用、LLM调用）如何被封装成一个可被工作流引擎执行的`NodeExecutor`。
    *   提供一个完整的Agent执行流程图。
*   **端侧AI分发与执行**:
    *   **后端 (`model-management-service`)**: 描述模型优化（量化、转换）流水线和基于设备能力的部署策略。
    *   **前端 (`core/aic`)**: 描述如何通过CGO调用`llama.cpp`等原生库，实现本地推理。
    *   **端云协同**: 描述端侧模型何时可以独立工作，何时需要请求云端更强大的模型。
```