/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package logger

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"cina.club/services/admin-bff-service/internal/domain/model"
)

// Mock <PERSON> for testing
type MockKafkaProducerAdvanced struct {
	mock.Mock
	messages []KafkaMessage
	mu       sync.Mutex
}

type KafkaMessage struct {
	Topic string
	Key   string
	Value []byte
}

func (m *MockKafkaProducerAdvanced) SendMessage(topic string, key string, value []byte) error {
	args := m.Called(topic, key, value)

	m.mu.Lock()
	defer m.mu.Unlock()
	m.messages = append(m.messages, KafkaMessage{
		Topic: topic,
		Key:   key,
		Value: value,
	})

	return args.Error(0)
}

func (m *MockKafkaProducerAdvanced) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockKafkaProducerAdvanced) GetMessages() []KafkaMessage {
	m.mu.Lock()
	defer m.mu.Unlock()
	result := make([]KafkaMessage, len(m.messages))
	copy(result, m.messages)
	return result
}

func (m *MockKafkaProducerAdvanced) ClearMessages() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.messages = []KafkaMessage{}
}

func TestNewMockKafkaProducer(t *testing.T) {
	logger := logrus.New()
	producer := NewMockKafkaProducer(logger)

	assert.NotNil(t, producer)
	assert.Implements(t, (*KafkaProducer)(nil), producer)
}

func TestMockKafkaProducer_SendMessage(t *testing.T) {
	logger := logrus.New()
	producer := NewMockKafkaProducer(logger)

	err := producer.SendMessage("test-topic", "test-key", []byte("test-value"))

	assert.NoError(t, err)
}

func TestMockKafkaProducer_Close(t *testing.T) {
	logger := logrus.New()
	producer := NewMockKafkaProducer(logger)

	err := producer.Close()

	assert.NoError(t, err)
}

func TestNewAuditLogger(t *testing.T) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"
	enabled := true

	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, enabled)

	assert.NotNil(t, auditLogger)

	// Clean up
	if closer, ok := auditLogger.(interface{ Close() error }); ok {
		closer.Close()
	}

	producer.AssertExpectations(t)
}

func TestAuditLogger_LogEntry(t *testing.T) {
	logger := logrus.New()
	topic := "test-audit-logs"

	t.Run("successful log entry - enabled", func(t *testing.T) {
		producer := &MockKafkaProducerAdvanced{}
		producer.ClearMessages()
		producer.On("SendMessage", topic, "actor123", mock.Anything).Return(nil)
		producer.On("Close").Return(nil)

		auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
		defer auditLogger.Close()

		entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
		entry.SetResource("user", "user456", "login")

		err := auditLogger.LogEntry(context.Background(), entry)

		assert.NoError(t, err)

		// Wait for async processing
		time.Sleep(200 * time.Millisecond)

		messages := producer.GetMessages()
		assert.GreaterOrEqual(t, len(messages), 1)

		producer.AssertExpectations(t)
	})

	t.Run("log entry disabled", func(t *testing.T) {
		producer := &MockKafkaProducerAdvanced{}
		producer.ClearMessages()
		producer.On("Close").Return(nil)

		auditLogger := NewAuditLogger(logger, producer, topic, false).(*AuditLogger)
		defer auditLogger.Close()

		entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")

		err := auditLogger.LogEntry(context.Background(), entry)

		assert.NoError(t, err)

		// Wait a bit and ensure no messages were sent
		time.Sleep(100 * time.Millisecond)
		messages := producer.GetMessages()
		assert.Empty(t, messages)
	})

	t.Run("invalid audit log entry", func(t *testing.T) {
		producer := &MockKafkaProducerAdvanced{}
		producer.On("Close").Return(nil)

		auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
		defer auditLogger.Close()

		// Create invalid entry (missing actor ID)
		entry := &model.AuditLogEntry{
			ActorID: "", // Invalid: empty actor ID
			Action:  "test_action",
		}

		err := auditLogger.LogEntry(context.Background(), entry)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid audit log entry")
	})

	t.Run("context with trace ID", func(t *testing.T) {
		producer := &MockKafkaProducerAdvanced{}
		producer.ClearMessages()
		producer.On("SendMessage", topic, "actor123", mock.Anything).Return(nil)
		producer.On("Close").Return(nil)

		auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
		defer auditLogger.Close()

		ctx := context.WithValue(context.Background(), auditTraceIDKey, "trace-456")
		entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
		entry.SetResource("user", "user456", "login")

		err := auditLogger.LogEntry(ctx, entry)

		assert.NoError(t, err)
		assert.Equal(t, "trace-456", entry.TraceID)

		// Wait for async processing
		time.Sleep(200 * time.Millisecond)

		producer.AssertExpectations(t)
	})
}

func TestAuditLogger_LogAction(t *testing.T) {
	logger := logrus.New()
	topic := "test-audit-logs"

	producer := &MockKafkaProducerAdvanced{}
	producer.On("SendMessage", topic, "actor123", mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	err := auditLogger.LogAction(
		context.Background(),
		"actor123",
		"<EMAIL>",
		"***********",
		"user_suspend",
		"user",
		"user456",
	)

	assert.NoError(t, err)

	// Wait for async processing
	time.Sleep(200 * time.Millisecond)

	messages := producer.GetMessages()
	assert.GreaterOrEqual(t, len(messages), 1)

	producer.AssertExpectations(t)
}

func TestAuditLogger_LogHTTPRequest(t *testing.T) {
	logger := logrus.New()
	topic := "test-audit-logs"

	producer := &MockKafkaProducerAdvanced{}
	producer.On("SendMessage", topic, "actor123", mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	body := map[string]interface{}{
		"email": "<EMAIL>",
		"name":  "Test User",
	}

	err := auditLogger.LogHTTPRequest(
		context.Background(),
		"actor123",
		"<EMAIL>",
		"***********",
		"POST",
		"/api/users",
		body,
		201,
		150,
		true,
	)

	assert.NoError(t, err)

	// Wait for async processing
	time.Sleep(200 * time.Millisecond)

	messages := producer.GetMessages()
	assert.GreaterOrEqual(t, len(messages), 1)

	producer.AssertExpectations(t)
}

func TestAuditLogger_LogBatch(t *testing.T) {
	logger := logrus.New()
	topic := "test-audit-logs"

	producer := &MockKafkaProducerAdvanced{}
	producer.On("SendMessage", topic, mock.Anything, mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	entries := []*model.AuditLogEntry{
		model.NewAuditLogEntry("actor1", "<EMAIL>", "***********"),
		model.NewAuditLogEntry("actor2", "<EMAIL>", "192.168.1.2"),
		model.NewAuditLogEntry("actor3", "<EMAIL>", "192.168.1.3"),
	}

	for _, entry := range entries {
		entry.SetResource("user", "userX", "login")
	}

	err := auditLogger.LogBatch(context.Background(), entries)

	assert.NoError(t, err)

	// Wait for async processing
	time.Sleep(300 * time.Millisecond)

	messages := producer.GetMessages()
	assert.GreaterOrEqual(t, len(messages), 3)

	producer.AssertExpectations(t)
}

func TestAuditLogger_GetLogs(t *testing.T) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	filter := model.AuditLogFilter{
		ActorID:      "actor123",
		ResourceType: "user",
		Action:       "login",
	}

	logs, err := auditLogger.GetLogs(context.Background(), filter)

	assert.NoError(t, err)
	assert.NotNil(t, logs)
	assert.Empty(t, logs) // Mock implementation returns empty slice
}

func TestAuditLogger_GetLogCount(t *testing.T) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	filter := model.AuditLogFilter{
		ActorID: "actor123",
	}

	count, err := auditLogger.GetLogCount(context.Background(), filter)

	assert.NoError(t, err)
	assert.Equal(t, int64(0), count) // Mock implementation returns 0
}

func TestAuditLogger_Close(t *testing.T) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)

	err := auditLogger.Close()

	assert.NoError(t, err)
	producer.AssertExpectations(t)
}

func TestAuditLogger_BufferOverflow(t *testing.T) {
	logger := logrus.New()
	topic := "test-audit-logs"

	producer := &MockKafkaProducerAdvanced{}
	// Simulate slow Kafka producer
	producer.On("SendMessage", topic, mock.Anything, mock.Anything).Return(nil).Run(func(args mock.Arguments) {
		time.Sleep(10 * time.Millisecond) // Slow down to cause buffer overflow
	})
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	// Send many entries quickly to overflow buffer
	for i := 0; i < 1010; i++ { // More than buffer size of 1000
		entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
		entry.SetResource("user", "userX", "action")

		err := auditLogger.LogEntry(context.Background(), entry)
		// Some entries may fail due to buffer overflow, which is expected
		if err != nil {
			assert.Contains(t, err.Error(), "failed to send audit log to Kafka")
		}
	}

	// Wait for processing to complete
	time.Sleep(500 * time.Millisecond)

	producer.AssertExpectations(t)
}

func TestAuditLogger_KafkaError(t *testing.T) {
	logger := logrus.New()
	topic := "test-audit-logs"

	producer := &MockKafkaProducerAdvanced{}
	kafkaError := errors.New("kafka connection failed")
	producer.On("SendMessage", topic, "actor123", mock.Anything).Return(kafkaError)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
	entry.SetResource("user", "user456", "login")

	// LogEntry returns error when Kafka fails
	err := auditLogger.LogEntry(context.Background(), entry)

	// Wait for async processing to complete and error to propagate
	time.Sleep(200 * time.Millisecond)

	// The error should be logged but LogEntry itself doesn't return kafka errors
	// since it's async. We can verify the producer was called.
	assert.NoError(t, err) // LogEntry doesn't return kafka errors directly

	producer.AssertExpectations(t)
}

func TestAuditLogger_AsyncProcessing(t *testing.T) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("SendMessage", topic, mock.Anything, mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	// Send multiple entries rapidly
	numEntries := 25
	for i := 0; i < numEntries; i++ {
		entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
		entry.SetResource("user", "userX", "action")

		err := auditLogger.LogEntry(context.Background(), entry)
		assert.NoError(t, err)
	}

	// Wait for async processing
	time.Sleep(500 * time.Millisecond)

	messages := producer.GetMessages()
	assert.Equal(t, numEntries, len(messages))

	producer.AssertExpectations(t)
}

func TestExtractTraceID(t *testing.T) {
	tests := []struct {
		name     string
		ctx      context.Context
		expected string
	}{
		{
			name:     "context with trace ID",
			ctx:      context.WithValue(context.Background(), auditTraceIDKey, "trace-123"),
			expected: "trace-123",
		},
		{
			name:     "context without trace ID",
			ctx:      context.Background(),
			expected: "",
		},
		{
			name:     "context with non-string trace ID",
			ctx:      context.WithValue(context.Background(), auditTraceIDKey, 12345),
			expected: "",
		},
		{
			name:     "nil context",
			ctx:      nil,
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractTraceID(tt.ctx)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDefaultAuditLoggerConfig(t *testing.T) {
	config := DefaultAuditLoggerConfig()

	assert.True(t, config.Enabled)
	assert.Equal(t, "admin-audit-logs", config.Topic)
	assert.Equal(t, 1000, config.BufferSize)
	assert.Equal(t, 10, config.BatchSize)
}

func TestAuditLogger_GracefulShutdown(t *testing.T) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("SendMessage", topic, mock.Anything, mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)

	// Add some entries to buffer
	for i := 0; i < 5; i++ {
		entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
		entry.SetResource("user", "userX", "action")
		auditLogger.LogEntry(context.Background(), entry)
	}

	// Close should process remaining entries
	err := auditLogger.Close()

	assert.NoError(t, err)

	// Verify all messages were processed
	messages := producer.GetMessages()
	assert.Equal(t, 5, len(messages))

	producer.AssertExpectations(t)
}

func TestAuditLogger_ConcurrentAccess(t *testing.T) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("SendMessage", topic, mock.Anything, mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	// Test concurrent logging
	numGoroutines := 10
	entriesPerGoroutine := 5
	var wg sync.WaitGroup

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			for j := 0; j < entriesPerGoroutine; j++ {
				entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
				entry.SetResource("user", "userX", "action")
				err := auditLogger.LogEntry(context.Background(), entry)
				assert.NoError(t, err)
			}
		}(i)
	}

	wg.Wait()

	// Wait for async processing
	time.Sleep(500 * time.Millisecond)

	messages := producer.GetMessages()
	assert.Equal(t, numGoroutines*entriesPerGoroutine, len(messages))

	producer.AssertExpectations(t)
}

// Benchmark tests
func BenchmarkAuditLogger_LogEntry(b *testing.B) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("SendMessage", topic, mock.Anything, mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	entry := model.NewAuditLogEntry("actor123", "<EMAIL>", "***********")
	entry.SetResource("user", "user456", "login")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		auditLogger.LogEntry(context.Background(), entry)
	}
}

func BenchmarkAuditLogger_LogAction(b *testing.B) {
	logger := logrus.New()
	producer := &MockKafkaProducerAdvanced{}
	topic := "test-audit-logs"

	producer.On("SendMessage", topic, mock.Anything, mock.Anything).Return(nil)
	producer.On("Close").Return(nil)

	auditLogger := NewAuditLogger(logger, producer, topic, true).(*AuditLogger)
	defer auditLogger.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		auditLogger.LogAction(
			context.Background(),
			"actor123",
			"<EMAIL>",
			"***********",
			"user_login",
			"user",
			"user456",
		)
	}
}
