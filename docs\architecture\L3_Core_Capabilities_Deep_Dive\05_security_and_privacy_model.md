
### 文件5: `docs/architecture/L3_Core_Capabilities_Deep_Dive/05_security_and_privacy_model.md`

(内容大纲)
*   **概述**: CINA.CLUB安全与隐私的“白皮书”，阐述平台的零知识和纵深防御理念。
*   **数据分类**: 定义数据的敏感等级（如`Public`, `Internal`, `Confidential`, `Restricted-E2EE`）。
*   **加密策略**:
    *   **传输中加密**: 强制TLS 1.3。内部S2S强制mTLS。
    *   **静态加密**: 所有云存储（DB, 对象存储）必须开启服务商提供的静态加密。
    *   **应用层加密 (ALE)**: 详细描述`key-management-proxy-service`的信封加密模型，保护后端存储的敏感PII。提供序列图。
    *   **端到端加密 (E2EE)**: 详细描述`core/crypto`和`core/datasync`如何协同工作，在客户端实现数据的加解密和同步。提供序列图。
*   **网络安全**:
    *   VPC、子网、安全组、NACL的设计原则。
    *   Kubernetes网络策略：默认拒绝，按需开放。
*   **Secrets Management**:
    *   所有密钥、密码、证书**必须**存储在Vault或AWS/Google Secrets Manager中。
    *   通过IAM角色和Workload Identity机制，安全地将Secrets注入到Pod中。
*   **合规性**: 简述平台如何满足GDPR等法规的关键要求（如被遗忘权、数据可携带权）。

---