好的，遵照您的指示。我将根据我们对`ai-assistant-service`和`routines-service`的宏大构想，重新生成一份**全新的、生产级的`pkg/workflow`需求规格说明书**。

这份**版本 2.0**的SRS，将把`pkg/workflow`从一个简单的执行器，**战略升级**为一个更强大的、支持复杂数据流、条件逻辑和错误处理的**通用流程编排内核**，使其能完美支撑上层服务的复杂需求。

---
### CINA.CLUB - 共享后端库 `pkg/workflow` (工作流引擎内核) 需求规格说明书

**版本: 2.0 (生产级定义，支持高级数据流与控制流)**  
**发布日期: 2025-06-28**  
**文档负责人:** [平台总架构师/首席工程师]  
**审批人:** [CTO]

#### 修订历史
| 版本 | 日期       | 修订人     | 修订描述                                                     |
|------|------------|------------|--------------------------------------------------------------|
| 1.0  | 2025-06-26 | Cina.Club  | 初始版本，定义了基于DAG的基础执行器。                        |
| 2.0  | 2025-06-28 | Cina.Club  | **引入高级数据流(JSONPath)、控制流(条件边)、错误处理和可观测性，使其能支撑平台级的自动化需求。** |

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计与执行模型](#3-核心设计与执行模型)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [使用示例与最佳实践](#6-使用示例-与-最佳实践)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束-与-开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB平台中的`ai-assistant-service`（用于动态任务规划）和`routines-service`（用于用户自定义自动化）都需要一个强大的底层引擎来执行由多个步骤组成的复杂工作流。`pkg/workflow` 的目的在于提供一个**通用的、无状态的、高性能的、基于有向无环图(DAG)的流程编排内核**。它负责解析工作流定义，管理节点间的**数据流**和**控制流**，并按序调用由上层服务注册的执行单元，从而为平台提供可复用的、强大的流程自动化基础能力。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   定义工作流、节点(Node)和边(Edge)的标准化JSON数据结构。
    *   提供一个工作流**执行器(Executor)**，能够解析图结构并按拓扑顺序执行。
    *   **高级数据流管理**: 支持使用**JSONPath**表达式，从全局上下文中引用和传递数据。
    *   **高级控制流管理**: 支持在**边(Edge)**上定义执行条件，实现IF-ELSE和Switch-Case等逻辑。
    *   定义一个可扩展的**节点执行器接口 (`NodeExecutor`)**。
    *   提供基本的**错误处理**和**可观测性**钩子。
*   **范围之外 (Out-of-Scope)**:
    *   **工作流的持久化、状态恢复、长时间等待**: 本引擎是**无状态的、同步执行**的。所有与持久化和长时运行相关的状态管理，由**调用方服务**（如`routines-service`）负责。
    *   **具体动作的实现**: 本包不包含任何业务动作的实现。
    *   **分布式事务 (Saga)**: 本引擎不提供开箱即用的Saga支持。但上层服务可以利用其错误处理和执行日志来实现Saga模式。

#### 1.3. 目标用户
*   **CINA.CLUB后端服务**中需要流程编排能力的开发者，主要是`ai-assistant-service`和`routines-service`。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/workflow` 是位于`pkg/`目录下的一个高度抽象、通用的逻辑库。它不依赖任何业务服务，是平台自动化和智能代理能力的技术基石。

#### 2.2. 设计原则
*   **内核与实现分离**: 引擎只负责**“如何流转”**，上层服务负责提供**“每个节点做什么”**。
*   **数据驱动**: 整个工作流的结构、数据流和控制流，完全由一个可序列化的JSON定义。
*   **无状态与可预测**: 引擎核心是纯粹的函数，给定相同的输入（图定义、初始状态），执行路径和最终状态是可预测的。
*   **可观测性**: 引擎的每一步执行都必须是可被追踪和记录的。

---

### 3. 核心设计与执行模型

#### 3.1. 核心数据结构 (JSON & Go Struct)
*   **`Workflow`**: `{"nodes": [...], "edges": [...]}`
*   **`Node`**: `{"id": "...", "type": "...", "inputs": {...}}`
*   **`Edge`**:
    *   **数据边**: `{"from": "node_A.output_1", "to": "node_B.input_1"}`
    *   **控制边 (带条件)**: `{"from": "node_A", "to": "node_B", "condition": "{{ .nodes.node_A.outputs.status == 'SUCCESS' }}"}`
*   **`ExecutionState`**: 一个大的`map[string]interface{}`，在执行过程中不断被填充。其结构为：
    ```json
    {
      "trigger": { ...initial data... },
      "nodes": {
        "node_A": { "inputs": {...}, "outputs": {...}, "status": "SUCCESS" },
        "node_B": { "inputs": {...}, "status": "PENDING" }
      }
    }
    ```

#### 3.2. 高级数据流 (JSONPath)
*   **FR3.2.1 (输入映射)**: 节点的`inputs`字段中的值，如果是一个字符串且符合`{{...}}`格式，则被视为一个**表达式**。
*   **FR3.2.2 (表达式引擎)**: 必须集成一个强大的表达式求值引擎（如`antonmedv/expr`或标准的JSONPath库），它能以`ExecutionState`为根对象，解析路径。
*   **FR3.2.3 (示例)**:
    *   `"user_id": "{{ .trigger.payload.user_id }}"` -> 从触发器数据中获取`user_id`。
    *   `"full_name": "{{ .nodes.get_user.outputs.first_name + ' ' + .nodes.get_user.outputs.last_name }}"` -> 拼接上一个节点的输出。

#### 3.3. 高级控制流 (Conditional Edges)
*   **FR3.3.1 (边上条件)**: `Edge`对象可以包含一个可选的`condition`字段，其值也是一个**表达式**。
*   **FR3.3.2 (执行逻辑)**: 在一个节点执行成功后，引擎会遍历其所有的出边(outgoing edges)。对于每一条边：
    *   如果边上没有`condition`，则目标节点被标记为可执行。
    *   如果边上有`condition`，则使用表达式引擎对其求值。只有当结果为`true`时，目标节点才被标记为可执行。
*   **FR3.3.3 (实现复杂逻辑)**: 这个机制可以轻松实现`IF-ELSE`（一个节点有两条带互斥条件的出边）和`Switch-Case`（一个节点有多条带不同条件的出边）。

#### 3.4. 执行模型 (基于依赖分析的并发执行)
1.  **构建依赖图**: 执行器首先解析`Workflow`定义，构建一个包含所有节点和依赖关系的内存图。
2.  **确定就绪队列**: 找到所有入度为0的节点（通常是`trigger`节点），将它们放入“就绪队列”。
3.  **并发执行循环**:
    a. 只要“就绪队列”不为空，就从中取出所有节点。
    b. **并发地**执行这些节点（每个节点在一个goroutine中）。
    c. 对于每个成功执行的节点，更新其在`ExecutionState`中的状态和输出。
    d. 检查该节点的所有**出边**，评估其`condition`。
    e. 对于满足条件的边，将其目标节点的**入度减1**。
    f. 如果某个目标节点的入度变为0，则将其加入“就绪队列”。
4.  循环直到没有可执行的节点。

**设计决策**: 采用并发执行模型能极大地提升不相关节点并行执行的效率，但对上层注册的`NodeExecutor`的**线程安全性**提出了要求。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 执行器 (Executor)
*   **FR4.1.1 (主执行函数)**: 必须提供`Executor.Run(ctx, workflow, initialState)`。
*   **FR4.1.2 (节点注册)**: `Executor`必须提供`RegisterNodeExecutor(typeName string, executor NodeExecutor)`方法。
*   **FR4.1.3 (并发控制)**: `Executor`的构造函数应能接收一个`concurrency`参数，以限制同时执行的goroutine数量。
*   **FR4.1.4 (错误处理策略)**:
    *   默认情况下，任何节点失败，整个工作流立即停止并返回错误。
    *   **必须**支持在节点定义中配置`"on_error": "continue"`策略，允许工作流在某个非关键节点失败时继续执行。
*   **FR4.1.5 (可观测性钩子)**: `Executor`必须提供钩子(hooks)，允许调用方注入回调函数，以在节点执行前/后、工作流开始/结束时执行自定义逻辑（如记录详细日志、更新指标）。

#### 4.2. 内置节点
*   **FR4.2.1 (逻辑节点)**: 必须内置`core.if` (条件分支), `core.switch` (多路分支), `core.merge` (合并分支), `core.noop` (空操作)等与业务无关的纯逻辑控制节点。
*   **FR4.2.2 (数据操作节点)**: 必须内置`core.set_variable`（设置一个变量）, `core.transform`（使用表达式对数据进行转换）等节点。

---

### 5. 接口定义 (API Specification)

```go
// pkg/workflow/executor.go

// NodeExecutor 是所有具体动作节点必须实现的接口。
type NodeExecutor interface {
    Execute(ctx context.Context, state *ExecutionState, inputs map[string]interface{}) (outputs map[string]interface{}, err error)
}

// ExecutionHooks 定义了执行过程中的回调钩子。
type ExecutionHooks struct {
    OnWorkflowStart func(ctx context.Context, wf *Workflow)
    OnNodeComplete  func(ctx context.Context, nodeID string, state *ExecutionState)
    // ...
}

// Executor 是工作流的执行引擎。
type Executor struct { ... }

func NewExecutor(opts ...ExecutorOption) *Executor

// RegisterNodeExecutor 注册一个节点类型及其执行器。
func (e *Executor) RegisterNodeExecutor(typeName string, executor NodeExecutor) error

// Run 执行一个工作流。
func (e *Executor) Run(ctx context.Context, wf *Workflow, initialState map[string]interface{}) (*ExecutionState, error)
```

---

### 6. 使用示例与最佳实践

**最佳实践**: 上层服务（如`routines-service`）应创建一个**单例的`Executor`实例**，并在服务启动时，将所有它需要的`NodeExecutor`（通过`connector-service`适配）注册进去。在处理每个请求时，直接使用这个配置好的实例来执行工作流。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   引擎调度开销必须极小。
    *   表达式求值必须高效，避免使用反射。
*   **NFR7.2 (可靠性)**:
    *   DAG校验必须能捕获所有循环依赖。
    *   并发执行模型必须是线程安全的，没有数据竞争。
*   **NFR7.3 (可测试性)**: 引擎核心逻辑（图调度、数据流）必须与节点执行逻辑解耦，并有独立的单元测试。
*   **NFR7.4 (安全性)**:
    *   表达式引擎必须运行在**沙箱模式**下，严禁其访问文件系统、网络或执行任意代码。只能访问传入的`ExecutionState`对象。

---

### 8. 技术约束与开发规范

*   **TC8.1 (核心依赖)**:
    *   **表达式引擎**: **`antonmedv/expr`**。它高性能、安全，并支持自定义函数。
    *   **图处理**: 可以不引入外部库，手动实现基于邻接表和入度计数器的DAG执行逻辑。
*   **TC8.2 (开发规范)**:
    *   **节点执行器是关键**: `NodeExecutor`的实现**必须是无状态和线程安全的**。所有状态都应来自传入的`inputs`和注入的客户端。
    *   **错误处理**: `NodeExecutor`返回的`error`应尽可能使用`pkg/errors`中的`AppError`，以便上层能根据错误类型进行判断。

---
这份版本2.0的SRS将`pkg/workflow`定义为一个强大而灵活的流程编排内核。通过支持高级的数据流和控制流，它不再仅仅是一个简单的顺序执行器，而是一个能够支撑CINA.CLUB平台复杂、动态、智能的自动化和Agentic工作流的坚实基础。