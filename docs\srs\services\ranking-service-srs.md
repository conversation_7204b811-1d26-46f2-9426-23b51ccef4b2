好的，遵照您的指示。我将为您生成一份专门针对 **`ranking-service` (排序模型服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义这个新增的、作为推荐系统“大脑”的服务的功能、接口、数据依赖、性能和可靠性需求，作为推荐系统开发的核心依据。

---
### CINA.CLUB - `ranking-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [推荐算法/机器学习工程负责人]  
**审批人:** [CTO/AI总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的个性化推荐系统中，在“召回(Recall)”阶段之后，需要一个更精准的阶段来对候选集进行排序，以最大化用户的点击率(CTR)、完播率(CVR)等核心业务指标。`ranking-service` 的目的在于构建一个**高性能、可扩展、支持复杂机器学习模型的在线排序服务**。它负责接收由`recommendation-service`传入的候选物料和用户上下文，加载相应的排序模型，进行实时预测，并返回一个经过精准打分和排序的物料列表。

#### 1.2. 服务范围
本服务 **负责**:
*   **统一排序接口**: 提供一个统一的gRPC API，接收排序请求，包含用户特征、一批候选物料的特征，以及要使用的**模型名称和版本**。
*   **模型加载与管理**:
    *   能够从一个模型仓库（如S3, GCS）中，**动态地加载**指定的机器学习排序模型到内存中。
    *   支持多种模型格式（如TensorFlow SavedModel, ONNX, TorchScript）。
    *   支持模型的**热更新(Hot Reloading)**，无需重启服务即可加载新版模型。
*   **实时预测 (Inference)**:
    *   接收特征数据，将其转换为模型所需的输入张量(Tensor)。
    *   调用底层的机器学习推理引擎（如TensorFlow Serving, ONNX Runtime）执行模型预测。
    *   为每个候选物料输出一个或多个预测分数（如pCTR, pCVR）。
*   **结果返回**: 将带有预测分数的、排序后的物料列表返回给调用方。

本服务 **不负责**:
*   **推荐流程的编排**: 由`recommendation-service`负责。
*   **召回**: 由`recall-service`负责。
*   **特征的获取与拼接**: 主要由`recommendation-service`负责，本服务只接收已准备好的特征。
*   **模型的训练**: 由离线的数据平台负责。
*   **提供面向最终用户的API**。

#### 1.3. 目标用户/调用方
*   **`recommendation-service` (唯一)**: 是本服务的唯一调用方。
*   **MLOps/算法工程师**: (管理用户) 负责向本服务部署和更新排序模型。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`ranking-service` 是推荐系统漏斗中**最核心的“决策大脑”**。它位于召回和重排之间，扮演着“**精准预言家**”的角色。它通过复杂的机器学习模型，对每个“用户-物料”对的匹配程度进行量化打分，其预测的准确性直接决定了最终推荐结果的质量和平台的业务收益。这是一个**计算密集型、对延迟极度敏感**的在线服务。

#### 2.2. 主要功能概述
*   支持多种机器学习模型和推理引擎的、统一的在线预测服务。
*   支持模型的动态加载和热更新。
*   高性能的、批量的实时预测能力。
*   与推荐编排服务的完全解耦。

---

### 3. 核心流程图

#### 3.1. 处理一次批量排序请求

```mermaid
sequenceDiagram
    participant RecoService as recommendation-service
    participant RankingService
    participant ModelRepo as "Model Repository (S3)"
    participant InferenceEngine as "TF Serving / ONNX Runtime"

    RecoService->>RankingService: 1. RankCandidatesRequest(userFeatures, itemFeaturesList, model_name)
    
    RankingService->>RankingService: 2. **[模型加载]** 检查所需模型是否已在内存中
    alt Model not loaded
        RankingService->>ModelRepo: 3a. 从S3下载模型文件
        RankingService->>InferenceEngine: 3b. 指令推理引擎加载模型
    end
    
    RankingService->>RankingService: 4. **[特征预处理]**<br/>- 将user/item features转换为模型输入张量(Tensors)
    
    RankingService->>InferenceEngine: 5. **[实时预测]** 执行批量预测 (Inference)
    InferenceEngine-->>RankingService: 6. 返回每个item的预测分数 (e.g., pCTR)
    
    RankingService->>RankingService: 7. **[结果处理]**<br/>- 将分数附加到item上<br/>- 按分数降序排序
    
    RankingService-->>RecoService: 8. 返回排序后的物料列表及分数
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 模型管理
*   **FR4.1.1 (动态加载)**: 服务在启动时或通过管理API，**必须**能够根据配置文件或指令，从指定的模型仓库（如S3路径）中拉取模型文件到本地。
*   **FR4.1.2 (模型热更新)**: **必须**支持在不中断服务的情况下，加载新版本的模型并平滑地切换。
    *   **实现**: 采用“版本指针”的模式。服务内部维护一个指向当前活跃模型实例的原子指针。加载新模型在后台完成，成功后再原子性地切换指针。旧模型在没有请求使用后，由GC回收。
*   **FR4.1.3 (多模型支持)**: 必须能同时在内存中加载多个不同的模型（如`video_feed_v1`, `video_feed_v2`, `service_reco_v1`），并能根据请求中指定的`model_name`来选择使用哪个模型进行预测。

#### 4.2. 实时预测
*   **FR4.2.1 (批量预测)**: API接口和内部处理逻辑**必须**是面向批量的。即一次请求可以包含一个用户和数百个候选物料，模型应对所有物料进行一次性批量预测，以充分利用GPU/CPU的并行计算能力。
*   **FR4.2.2 (特征工程)**: 本服务负责执行**在线阶段的最后一步特征处理**。
    *   **输入**: 接收已经处理好的用户和物料特征（可能是数值、类别ID、或嵌入向量）。
    *   **处理**: 将这些特征转换为符合模型输入`signature`的张量(Tensor)格式。
*   **FR4.2.3 (多目标预测)**: （高级）模型可以支持多目标输出，如同时预测pCTR（点击率）、pCVR（完播率）、pLike（点赞率）等。API响应中应能包含所有这些分数。

#### 4.3. A/B测试支持
*   **FR4.3.1 (模型版本路由)**: 上游的`recommendation-service`在调用本服务时，可以根据A/B实验的分配，在请求中指定使用`model_name: "video_feed_v1_exp"`这样的实验模型，本服务必须能路由到对应的模型实例。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC API接口 (供`recommendation-service`调用)
*   **Package**: `hina.v1.ranking`
*   **核心RPC**:
    ```protobuf
    service RankingService {
      // 对一批候选物料进行打分和排序
      rpc RankCandidates(RankCandidatesRequest) returns (RankCandidatesResponse);
    }

    message Feature {
      // 使用oneof来支持多种特征类型
      oneof value {
        float float_value = 1;
        int64 int_value = 2;
        string string_value = 3;
        repeated float vector_value = 4;
      }
    }
    
    message FeatureSet {
      map<string, Feature> features = 1;
    }

    message RankCandidatesRequest {
      string user_id = 1;
      FeatureSet user_features = 2;
      repeated FeatureSet candidate_item_features = 3; // 每个物料的特征
      string model_name = 4; // e.g., "video_feed_deepfm_v2"
    }

    message RankedItem {
      string id = 1; // 物料的原始ID
      map<string, float> scores = 2; // e.g., {"pctr": 0.8, "pcvr": 0.6}
    }

    message RankCandidatesResponse {
      repeated RankedItem items = 1; // 按主目标分数降序排列
    }
    ```

#### 5.2. 管理API接口 (内部)
*   `POST /admin/models/reload`: 触发服务从模型仓库重新加载所有或指定的模型。

---

### 6. 数据需求 (Data Requirements)

*   **无核心持久化数据库**: 本服务是**完全无状态**的计算服务。
*   **依赖的数据源**:
    *   **模型仓库 (S3/MinIO)**: 存储序列化后的模型文件。
    *   **(间接)** **特征存储**: 特征由`recommendation-service`传入。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与延迟 - 最高优先级)**:
    *   **API端到端延迟**: 对于一个包含200个候选物料的批量请求，P95**必须 `< 50ms**。P99 < 80ms。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。
    *   **模型加载失败**: 如果一个新模型加载失败，服务**绝不能**崩溃，而应继续使用旧的、可用的模型，并记录严重错误和告警。
*   **NFR7.3 (可扩展性)**: 服务应为无状态，易于通过增加实例和GPU资源来水平扩展。
*   **NFR7.4 (资源利用率)**: 需要对CPU/GPU的使用率进行密切监控，确保资源被充分利用。

---

### 8. 技术约束与选型建议
*   **语言**: **Go + CGO**。

    *   **Go + CGO**: 如果追求极致性能和与后端技术栈的统一，可以使用Go来编写服务，并通过CGO调用底层的TensorFlow C++ API或ONNX Runtime C API。这会增加开发和部署的复杂性。
*   **推理引擎**:
    *   **TensorFlow Serving / Triton Inference Server (NVIDIA)**: **强烈推荐**。这些是专为在线推理设计的、生产级的服务器。它们内置了模型管理、版本控制、批量处理、多GPU支持等所有需要的功能。`ranking-service`可以简化为只是一个调用这些推理服务器的轻量级代理。
    *   **ONNX Runtime**: 如果选择自建推理服务，ONNX Runtime是一个跨平台、高性能的优秀选择。
*   **部署模型**: 本服务通常需要部署在**带GPU的**计算实例上，以满足低延迟和高吞吐量的要求。

---
这份SRS为`ranking-service`的设计和实现提供了坚实、全面的指导。通过将其设计为一个**专业的、高性能的、模型可热更新的在线预测服务**，它能够将离线训练出的复杂机器学习模型的能力，转化为实际的、可带来业务增长的在线推荐效果，是整个个性化推荐系统的核心决策引擎。