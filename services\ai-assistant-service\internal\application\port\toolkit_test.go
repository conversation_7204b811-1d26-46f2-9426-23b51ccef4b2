/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package port

import (
	"context"
	"testing"
)

func TestToolCategory(t *testing.T) {
	// Test that all tool categories are properly defined
	categories := []ToolCategory{
		ToolCategorySearch,
		ToolCategorySchedule,
		ToolCategoryMemory,
		ToolCategoryKnowledge,
		ToolCategoryUser,
		ToolCategoryLLM,
		ToolCategoryMCP,
		ToolCategoryContent,
		ToolCategoryExternal,
		ToolCategoryUtility,
	}

	for _, category := range categories {
		if string(category) == "" {
			t.Errorf("Tool category should not be empty string")
		}
	}
}

func TestNewToolResult(t *testing.T) {
	data := map[string]interface{}{
		"key1": "value1",
		"key2": 123,
	}

	result := NewToolResult(data)

	if result == nil {
		t.Fatal("NewToolResult should not return nil")
	}

	if result.Data == nil {
		t.Error("Result data should not be nil")
	}

	if result.Data["key1"] != "value1" {
		t.Error("Result data not set correctly")
	}

	if result.Data["key2"] != 123 {
		t.Error("Result data not set correctly")
	}

	if result.Error != "" {
		t.Error("Error should be empty for successful result")
	}

	if !result.Success {
		t.Error("Success should be true for new result")
	}
}

func TestNewToolError(t *testing.T) {
	errorMsg := "something went wrong"

	result := NewToolError(errorMsg)

	if result == nil {
		t.Fatal("NewToolError should not return nil")
	}

	if result.Error != errorMsg {
		t.Errorf("Expected error '%s', got '%s'", errorMsg, result.Error)
	}

	if result.Success {
		t.Error("Success should be false for error result")
	}

	if result.Data != nil {
		t.Error("Data should be nil for error result")
	}
}

func TestToolResult_AddMetadata(t *testing.T) {
	result := NewToolResult(map[string]interface{}{"test": "data"})

	result.AddMetadata("key1", "value1")
	result.AddMetadata("key2", 123)

	if result.Metadata == nil {
		t.Fatal("Metadata should not be nil after adding items")
	}

	if result.Metadata["key1"] != "value1" {
		t.Error("Metadata key1 not set correctly")
	}

	if result.Metadata["key2"] != 123 {
		t.Error("Metadata key2 not set correctly")
	}
}

func TestToolResult_AddNextStep(t *testing.T) {
	result := NewToolResult(map[string]interface{}{"test": "data"})

	result.AddNextStep("Step 1")
	result.AddNextStep("Step 2")

	if len(result.NextSteps) != 2 {
		t.Errorf("Expected 2 next steps, got %d", len(result.NextSteps))
	}

	if result.NextSteps[0] != "Step 1" {
		t.Error("First next step not set correctly")
	}

	if result.NextSteps[1] != "Step 2" {
		t.Error("Second next step not set correctly")
	}
}

func TestJSONSchema(t *testing.T) {
	schema := &JSONSchema{
		Type: "object",
		Properties: map[string]*JSONSchema{
			"name": {
				Type:        "string",
				Description: "Name field",
			},
			"age": {
				Type:        "integer",
				Description: "Age field",
			},
		},
		Required: []string{"name"},
	}

	if schema.Type != "object" {
		t.Error("Schema type not set correctly")
	}

	if len(schema.Properties) != 2 {
		t.Errorf("Expected 2 properties, got %d", len(schema.Properties))
	}

	if len(schema.Required) != 1 {
		t.Errorf("Expected 1 required field, got %d", len(schema.Required))
	}

	nameProperty := schema.Properties["name"]
	if nameProperty.Type != "string" {
		t.Error("Name property type not set correctly")
	}
}

func TestToolSchema(t *testing.T) {
	schema := &ToolSchema{
		Name:         "test_tool",
		Description:  "A test tool",
		Category:     string(ToolCategoryLLM),
		RequiresAuth: true,
		IsAsync:      false,
		InputSchema:  &JSONSchema{Type: "object"},
	}

	if schema.Name != "test_tool" {
		t.Error("Tool name not set correctly")
	}

	if schema.Description != "A test tool" {
		t.Error("Tool description not set correctly")
	}

	if schema.Category != string(ToolCategoryLLM) {
		t.Error("Tool category not set correctly")
	}

	if !schema.RequiresAuth {
		t.Error("RequiresAuth not set correctly")
	}

	if schema.IsAsync {
		t.Error("IsAsync not set correctly")
	}

	if schema.InputSchema == nil {
		t.Error("InputSchema should not be nil")
	}
}

// MockTool for testing interfaces
type MockTool struct {
	name        string
	description string
	category    ToolCategory
	result      *ToolResult
	err         error
}

func (m *MockTool) Name() string              { return m.name }
func (m *MockTool) Description() string       { return m.description }
func (m *MockTool) Category() ToolCategory    { return m.category }
func (m *MockTool) RequiresAuth() bool        { return false }
func (m *MockTool) IsAsync() bool             { return false }
func (m *MockTool) InputSchema() *JSONSchema  { return nil }
func (m *MockTool) OutputSchema() *JSONSchema { return nil }
func (m *MockTool) Execute(ctx context.Context, inputs map[string]interface{}) (*ToolResult, error) {
	return m.result, m.err
}

func TestToolInterface(t *testing.T) {
	tool := &MockTool{
		name:        "mock_tool",
		description: "A mock tool for testing",
		category:    ToolCategoryLLM,
		result:      NewToolResult(map[string]interface{}{"success": true}),
		err:         nil,
	}

	// Test that MockTool implements Tool interface
	var _ Tool = tool

	// Test methods
	if tool.Name() != "mock_tool" {
		t.Error("Name method not working correctly")
	}

	if tool.Description() != "A mock tool for testing" {
		t.Error("Description method not working correctly")
	}

	if tool.Category() != ToolCategoryLLM {
		t.Error("Category method not working correctly")
	}

	result, err := tool.Execute(context.Background(), nil)
	if err != nil {
		t.Errorf("Execute should not return error: %v", err)
	}

	if result == nil {
		t.Error("Execute should return result")
	}
}
