# AI Assistant Service

CINA.CLUB平台的智能助手服务，提供多模态对话、工作流编排、智能工具调用等功能。

## 🚀 核心功能

### 智能对话系统
- **多轮对话管理**: 维护用户会话状态，支持上下文理解
- **意图识别**: 自动识别用户意图并生成相应的执行计划
- **多模态支持**: 支持文本、图片、音频、视频等多种内容类型
- **实时响应**: 提供流式响应和WebSocket连接

### 工作流编排引擎
- **动态规划**: 根据用户意图自动生成工作流计划
- **并行执行**: 支持步骤并行执行，提高响应速度
- **错误处理**: 完善的错误处理和重试机制
- **状态管理**: 实时跟踪工作流执行状态

### 智能工具系统
- **LLM调用**: 集成多种大语言模型（OpenAI、Claude、Gemini等）
- **服务搜索**: 搜索平台服务提供商
- **日程管理**: 创建和管理用户日程
- **记忆查询**: 查询用户历史记忆和偏好
- **知识库**: 查询平台知识库和FAQ
- **用户信息**: 获取用户资料和订单历史

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    AI Assistant Service                     │
├─────────────────────────────────────────────────────────────┤
│  HTTP API  │  WebSocket  │  gRPC  │  Message Queue        │
├─────────────────────────────────────────────────────────────┤
│                  Application Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Assistant Service│  │ Workflow Planner│  │ Tool Manager │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Domain Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Dialog State    │  │ Workflow Model  │  │ Tool Model   │ │
│  │ Session Manager │  │ Execution State │  │ Schema       │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Redis Store     │  │ HTTP Client     │  │ gRPC Client  │ │
│  │ Message Queue   │  │ LLM Provider    │  │ Monitoring   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📋 技术规格

### 核心技术栈
- **语言**: Go 1.22+
- **框架**: Gin (HTTP), gRPC
- **存储**: Redis (会话状态)
- **消息队列**: Apache Kafka
- **监控**: Prometheus + Jaeger
- **配置**: YAML + 环境变量

### 性能指标
- **响应时间**: < 100ms (简单查询), < 500ms (复杂工作流)
- **并发处理**: 1000+ 并发会话
- **可用性**: 99.9%
- **扩展性**: 水平扩展支持

## 🚀 快速开始

### 环境要求
- Go 1.22+
- Redis 6.0+
- Kafka 2.8+ (可选)

### 安装依赖
```bash
go mod download
```

### 配置环境变量
```bash
export OPENAI_API_KEY="your-openai-api-key"
export CLAUDE_API_KEY="your-claude-api-key"
export JWT_SECRET="your-jwt-secret"
export REDIS_URL="redis://localhost:6379"
```

### 启动服务
```bash
# 开发模式
go run cmd/server/main.go

# 生产模式
go build -o ai-assistant-service cmd/server/main.go
./ai-assistant-service
```

### Docker部署
```bash
# 构建镜像
docker build -t ai-assistant-service .

# 运行容器
docker run -p 8080:8080 \
  -e OPENAI_API_KEY="your-api-key" \
  -e REDIS_URL="redis://redis:6379" \
  ai-assistant-service
```

## 📡 API文档

### 对话接口

#### 发送消息
```http
POST /api/v1/chat
Content-Type: application/json

{
  "user_id": "user-123",
  "session_id": "session-456", // 可选，不提供则创建新会话
  "message": "我想找一个摄影师"
}
```

**响应**:
```json
{
  "session_id": "session-456",
  "content": "我为您找到了几位优秀的摄影师...",
  "message_type": "text",
  "metadata": {
    "execution_time": "150ms",
    "steps_executed": 2
  },
  "suggestions": [
    "查看摄影师详细信息",
    "预约摄影服务",
    "查看用户评价"
  ]
}
```

#### 获取会话历史
```http
GET /api/v1/chat/{session_id}/history?limit=20
```

#### 清除会话
```http
DELETE /api/v1/chat/{session_id}
```

### 工具接口

#### 获取可用工具
```http
GET /api/v1/tools
```

**响应**:
```json
{
  "tools": [
    {
      "name": "search_services",
      "description": "搜索平台上的服务提供商",
      "category": "search",
      "input_schema": {...},
      "requires_auth": false,
      "is_async": false
    }
  ],
  "count": 6
}
```

### 健康检查
```http
GET /api/v1/health
```

## 🔧 配置说明

### 服务配置
```yaml
server:
  port: 8080
  host: "0.0.0.0"
  mode: "release"
```

### LLM配置
```yaml
llm:
  providers:
    openai:
      api_key: "${OPENAI_API_KEY}"
      default_model: "gpt-3.5-turbo"
      timeout: 30s
```

### 工具配置
```yaml
tools:
  enabled_categories:
    - "llm"
    - "search"
    - "schedule"
  execution:
    timeout: 60s
    max_concurrent: 10
```

## 🛠️ 开发指南

### 添加新工具

1. **实现Tool接口**:
```go
type CustomTool struct {
    name        string
    description string
}

func (t *CustomTool) Name() string {
    return t.name
}

func (t *CustomTool) Execute(ctx context.Context, inputs map[string]interface{}) (*ToolResult, error) {
    // 实现工具逻辑
    return NewToolResult(map[string]interface{}{
        "result": "success",
    }), nil
}
```

2. **注册工具**:
```go
toolkit.RegisterTool(NewCustomTool())
```

### 扩展意图识别

1. **添加新意图类型**:
```go
const IntentCustom IntentType = "custom"
```

2. **实现意图分析逻辑**:
```go
func (p *WorkflowPlanner) analyzeCustomIntent(message string) *Intent {
    // 实现意图识别逻辑
}
```

3. **创建工作流计划**:
```go
func (p *WorkflowPlanner) createCustomPlan(intent *Intent) *WorkflowPlan {
    // 实现计划创建逻辑
}
```

## 📊 监控和运维

### 健康检查
- **端点**: `/api/v1/health`
- **指标**: 服务状态、依赖健康度
- **告警**: 服务不可用、响应时间过长

### 性能监控
- **Prometheus指标**: 
  - `ai_assistant_requests_total`
  - `ai_assistant_request_duration_seconds`
  - `ai_assistant_active_sessions`
  - `ai_assistant_tool_executions_total`

### 分布式追踪
- **Jaeger集成**: 完整的请求链路追踪
- **采样率**: 可配置（默认10%）

### 日志管理
- **结构化日志**: JSON格式
- **日志级别**: DEBUG、INFO、WARN、ERROR
- **日志轮转**: 自动清理历史日志

## 🔒 安全考虑

### 认证授权
- **JWT Token**: 用户身份验证
- **API Key**: 外部服务调用
- **权限控制**: 基于角色的访问控制

### 数据安全
- **敏感数据**: 加密存储
- **会话隔离**: 用户数据严格隔离
- **审计日志**: 完整的操作记录

### 网络安全
- **CORS配置**: 跨域请求控制
- **Rate Limiting**: 防止API滥用
- **TLS加密**: 传输层安全

## 🧪 测试

### 单元测试
```bash
go test ./...
```

### 集成测试
```bash
go test -tags=integration ./...
```

### 性能测试
```bash
go test -bench=. ./...
```

### API测试
```bash
# 使用curl测试
curl -X POST http://localhost:8080/api/v1/chat \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test-user","message":"你好"}'
```

## 📈 部署和扩展

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-assistant-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-assistant-service
  template:
    metadata:
      labels:
        app: ai-assistant-service
    spec:
      containers:
      - name: ai-assistant-service
        image: ai-assistant-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-secrets
              key: openai-api-key
```

### 水平扩展
- **无状态设计**: 支持多实例部署
- **负载均衡**: 通过Nginx或云负载均衡器
- **会话共享**: Redis集群存储会话状态

### 容灾备份
- **多可用区**: 跨AZ部署
- **数据备份**: Redis数据定期备份
- **故障转移**: 自动故障检测和切换

## 🤝 贡献指南

### 开发流程
1. Fork项目
2. 创建特性分支
3. 提交代码
4. 创建Pull Request

### 代码规范
- **Go代码**: 遵循Go官方规范
- **注释**: 完整的函数和类型注释
- **测试**: 新功能必须包含测试
- **文档**: 更新相关文档

### 提交规范
```
feat: 添加新的工具类型
fix: 修复会话过期问题
docs: 更新API文档
test: 添加集成测试
```

## 📞 支持和联系

- **文档**: [技术文档](./docs/)
- **问题反馈**: [GitHub Issues](https://github.com/cina-club/monorepo/issues)
- **技术支持**: <EMAIL>

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。 