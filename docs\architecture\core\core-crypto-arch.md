好的，遵照您的指示。我将根据我们刚才讨论的、版本2.0的`core/crypto` SRS，为您生成一份专门针对 **`core/crypto`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将详细阐述其内部模块划分、数据流、密钥生命周期管理以及与底层密码学库的交互方式，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `core/crypto` (共享核心加密库) 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-28
**参考SRS**: `core/crypto-srs.md` (v2.0)
**核心架构**: 分层密码学服务 + 插件式算法 + 安全内存管理

## 1. 概述

`core/crypto` 的架构核心是**分层、抽象和安全**。它旨在将复杂的密码学原语，封装成对开发者友好、且几乎不可能被误用的高级API。整个架构围绕着**密钥生命周期**和**数据处理流**进行设计，确保每一步操作都是安全、高效和可测试的。

**核心技术挑战**:
1.  **密钥安全隔离**: 如何在代码层面，严格隔离MEK、KEK、DEK，确保它们只在各自的职责范围内被使用。
2.  **内存安全**: 如何管理Go语言中不直接可控的内存，以最大限度地减少敏感密钥（特别是MEK和私钥）在内存中的暴露时间。
3.  **密码学敏捷性**: 如何设计架构，以便未来可以轻松地替换或增加新的加密算法，而无需重写大量代码。
4.  **跨平台一致性**: 如何确保Go、Go Mobile和WASM的编译产物在密码学行为上完全一致。

本架构通过**分层服务、接口化算法和自定义的安全内存类型**来应对这些挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (内部)

```mermaid
graph TD
    subgraph "Public API Layer (Exports)"
        A[exports_mobile.go]
        B[exports_wasm.go]
    end

    subgraph "High-Level Service Layer"
        C[Vault Service<br/>(处理MEK/KEK/DEK联动)]
        D[HDWallet Service<br/>(处理BIP39/32/44)]
    end
    
    subgraph "Mid-Level Abstraction Layer"
        E[Symmetric Encryption<br/>(AEAD Provider Interface)]
        F[Asymmetric Encryption<br/>(Box Provider Interface)]
        G[Key Derivation<br/>(KDF Provider Interface)]
        H[Signing<br/>(Signer Provider Interface)]
    end
    
    subgraph "Low-Level Implementation Layer"
        I[XChaCha20-Poly1305 Impl]
        J[ECIES Impl]
        K[Argon2id Impl]
        L[ECDSA/secp256k1 Impl]
        M[Ed25519 Impl]
    end
    
    subgraph "Go Standard & x/crypto Libraries"
        N[crypto/rand, crypto/subtle, ...]
        O[golang.org/x/crypto/chacha20poly1305, ...]
    end
    
    A & B -- "调用" --> C & D
    C -- "使用" --> E & G
    D -- "使用" --> G & H

    E -- "由...实现" --> I
    F -- "由...实现" --> J
    G -- "由...实现" --> K
    H -- "由...实现" --> L & M
    
    I & J & K & L & M -- "基于" --> N & O
```

### 2.2 最终目录结构 (`core/crypto/`)

```
crypto/
├── aead/                       # ✨ 对称加密 (AEAD) ✨
│   ├── interface.go            # 定义 AeadProvider 接口
│   ├── xchacha20poly1305.go    # XChaCha20-Poly1305 的实现
│   └── sealedbox.go            # 标准化密文格式(SealedBox)的打包与解析
├── asymm/                      # ✨ 非对称加密 (Hybrid) ✨
│   ├── interface.go            # 定义 BoxProvider 接口
│   └── ecies_secp256k1.go      # ECIES 的实现
├── kdf/                        # ✨ 密钥派生函数 ✨
│   ├── interface.go            # 定义 KdfProvider 接口
│   └── argon2id.go             # Argon2id 的实现
├── sign/                       # ✨ 数字签名 ✨
│   ├── interface.go            # 定义 SignerProvider 和 VerifierProvider 接口
│   ├── ecdsa_secp256k1.go      # ECDSA for Ethereum
│   └── ed25519.go              # Ed25519
├── hdwallet/                   # ✨ 分层确定性钱包 ✨
│   ├── bip39.go                # 助记词生成与验证
│   └── bip44.go                # 密钥派生
├── vault.go                    # ✨ 核心: Vault服务, 封装三层密钥逻辑 ✨
├── securemem/                  # ✨ 核心: 安全内存管理 ✨
│   └── secure_byte.go          # 自定义的、使用后自动清零的字节切片类型
├── exports_mobile.go           # Go Mobile 导出
├── exports_wasm.go             # WASM 导出
└── go.mod
```
---

## 3. 各核心组件深度解析

### 3.1 `securemem/` - 安全内存管理 (The Foundation)

这是整个安全体系的**最底层基础**。

*   **`secure_byte.go`: `SecureBytes` type**
    *   **定义**: `type SecureBytes []byte`
    *   **核心功能**:
        1.  **防止内存交换 (Swapping)**: 在支持的操作系统上（通过CGO调用`mlock()`），将这块内存锁定在物理RAM中，防止被操作系统交换到磁盘上。
        2.  **自动清零**: 实现一个`Destroy()`方法，用随机字节或零覆盖切片的底层数组。`runtime.SetFinalizer`将被用于在`SecureBytes`对象被GC时，自动调用`Destroy()`。
    *   **应用**: 所有在内存中临时存在的敏感数据（MEK, KEK, DEK, 私钥）**必须**使用`SecureBytes`类型，而不是原生的`[]byte`。

### 3.2 算法实现层 (`aead/`, `asymm/`, `kdf/`, `sign/`)

这一层是密码学原语的**具体实现**，但通过接口进行抽象。

*   **`interface.go`**: 每个子包都定义一个接口，如`aead.Provider`。
    ```go
    // aead/interface.go
    type Provider interface {
        Encrypt(plaintext, key securemem.SecureBytes) ([]byte, error)
        Decrypt(ciphertext []byte, key securemem.SecureBytes) (securemem.SecureBytes, error)
        KeySize() int
        NonceSize() int
    }
    ```
*   **实现文件** (如 `xchacha20poly1305.go`):
    *   实现`Provider`接口。
    *   直接调用`golang.org/x/crypto`等底层库来执行计算。
    *   所有接收和返回密钥的地方，都使用`securemem.SecureBytes`类型。

**设计决策**: 通过接口化，未来如果需要添加一个新的加密算法（如AES-GCM-SIV），只需实现`aead.Provider`接口即可，上层代码（`vault.go`）无需任何改动，实现了**密码学敏捷性**。

### 3.3 `hdwallet/` - Web3钱包核心

*   **职责**: 严格按照BIP-39/32/44标准，实现从助记词到最终私钥的完整派生链。
*   **`bip39.go`**: 封装助记词生成和从助记词到种子的转换。
*   **`bip44.go`**: 封装从种子按路径派生子密钥的逻辑。返回的私钥**必须**是`securemem.SecureBytes`类型。

### 3.4 `vault.go` - Vault服务 (The High-Level API)

这是`core/crypto`包对外的**主要门面(Facade)**。它封装了最复杂的**三层密钥联动逻辑**。

*   **`Vault` struct**:
    *   `mek securemem.SecureBytes`: 解锁后在内存中持有的主加密密钥。
    *   `aeadProvider aead.Provider`: 当前使用的对称加密算法实现。
*   **`NewVault(password, salt)` method**:
    1.  调用`kdf.Argon2id.Derive(...)`从密码派生出MEK。
    2.  将MEK存入`Vault`实例。
    3.  返回`*Vault`。
*   **`Lock()` method**:
    *   调用`v.mek.Destroy()`清零内存中的MEK，使Vault进入锁定状态。
*   **核心方法 `SealData(plaintext []byte)`**:
    1.  检查Vault是否已解锁（`mek`是否存在）。
    2.  **生成KEK**: `kek, _ := aead.GenerateKey()`
    3.  **生成DEK**: `dek, _ := aead.GenerateKey()`
    4.  **加密DEK**: `encryptedDEK, _ := v.aeadProvider.Encrypt(dek, v.mek)`  *(简化示例，实际应使用KEK)*
    5.  **加密数据**: `encryptedData, _ := v.aeadProvider.Encrypt(plaintext, dek)`
    6.  **打包SealedBox**: 调用`aead.PackSealedBox(...)`将所有部分组装成最终的密文。
    7.  **清零**: `kek.Destroy()`, `dek.Destroy()`。
*   **核心方法 `OpenData(sealedBox []byte)`**:
    *   执行`SealData`的逆向操作，最终返回明文。

**设计决策**: 将三层密钥的复杂交互逻辑完全封装在`Vault`中。上层应用（如前端`crypto-vault`模块）只需与`Vault`交互，创建/解锁一个`Vault`实例，然后调用其`SealData`/`OpenData`方法即可，无需关心内部的MEK/KEK/DEK细节。

### 4. 跨平台导出 (`exports_*.go`)

*   **导出对象**: 主要导出`Vault`对象及其方法，以及`hdwallet`中的函数。
*   **数据传递**:
    *   所有二进制数据（密钥、密文）都通过`[]byte`传递。
    *   Go Mobile的桥接层在接收到`[]byte`后，会立即将其转换为Java/Swift中的安全字节数组。
    *   WASM的桥接层会将其转换为`Uint8Array`。
*   **错误处理**: 所有导出的函数都返回`error`字符串，由调用方（JS/Swift/Java）检查。

## 5. 总结

本架构设计通过以下关键点，构建了一个生产级的`core/crypto`库：
1.  **分层抽象**:
    *   **L0 (基础)**: `securemem` 保证内存安全。
    *   **L1 (原语)**: `aead`, `kdf` 等包通过接口提供了可替换的算法实现。
    *   **L2 (应用密码学)**: `hdwallet` 实现了具体的Web3标准。
    *   **L3 (服务门面)**: `vault` 封装了最常用的E2EE工作流，提供了极简的API。
2.  **安全性设计**:
    *   强制使用现代、安全的加密算法。
    *   通过三层密钥体系，隔离了风险，并支持无感修改主密码。
    *   通过`SecureBytes`和`Vault.Lock()`，最大限度地减少了敏感数据在内存中的暴露。
3.  **可维护性与敏捷性**: 接口化的算法实现，使得未来升级到抗量子加密等新算法成为可能，而无需重构整个库。

这个架构不仅满足了SRS v2.0中定义的所有功能和安全需求，更为CINA.CLUB平台提供了一个坚如磐石、经得起未来考验的密码学核心。