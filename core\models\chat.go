// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-24 10:19:52
// Modified: 2025-06-24 10:19:52

package models

import (
	"time"
)

// ChatRoom represents a chat room/conversation
type ChatRoom struct {
	ID          string   `json:"id"`
	Type        RoomType `json:"type"`
	Name        string   `json:"name,omitempty"`
	Description string   `json:"description,omitempty"`
	AvatarURL   string   `json:"avatar_url,omitempty"`
	CreatorID   string   `json:"creator_id"`
	CreatedAt   int64    `json:"created_at"`
	UpdatedAt   int64    `json:"updated_at"`
}

// RoomType represents the type of chat room
type RoomType string

const (
	RoomTypeOneToOne RoomType = "ONE_TO_ONE"
	RoomTypeGroup    RoomType = "GROUP"
	RoomTypeChannel  RoomType = "CHANNEL"
)

// ChatMessage represents a chat message
type ChatMessage struct {
	ID               string                 `json:"id"`
	RoomID           string                 `json:"room_id"`
	SenderID         string                 `json:"sender_id"`
	Type             MessageType            `json:"type"`
	Content          map[string]interface{} `json:"content"`
	Mentions         []string               `json:"mentions,omitempty"`
	ReplyToMessageID string                 `json:"reply_to_message_id,omitempty"`
	Timestamp        int64                  `json:"timestamp"`
	Status           MessageStatus          `json:"status"`
	CreatedAt        int64                  `json:"created_at"`
	UpdatedAt        int64                  `json:"updated_at"`
}

// MessageType represents the type of message
type MessageType string

const (
	MessageTypeText     MessageType = "TEXT"
	MessageTypeImage    MessageType = "IMAGE"
	MessageTypeVideo    MessageType = "VIDEO"
	MessageTypeAudio    MessageType = "AUDIO"
	MessageTypeFile     MessageType = "FILE"
	MessageTypeLocation MessageType = "LOCATION"
	MessageTypeCard     MessageType = "CARD"
	MessageTypeSystem   MessageType = "SYSTEM"
	MessageTypeRevoked  MessageType = "REVOKED"
)

// MessageStatus represents the status of a message
type MessageStatus string

const (
	MessageStatusSending   MessageStatus = "SENDING"
	MessageStatusSent      MessageStatus = "SENT"
	MessageStatusDelivered MessageStatus = "DELIVERED"
	MessageStatusRead      MessageStatus = "READ"
	MessageStatusFailed    MessageStatus = "FAILED"
	MessageStatusDeleted   MessageStatus = "DELETED"
)

// RoomMember represents a member of a chat room
type RoomMember struct {
	RoomID            string     `json:"room_id"`
	UserID            string     `json:"user_id"`
	Role              MemberRole `json:"role"`
	JoinedAt          int64      `json:"joined_at"`
	LastReadTimestamp int64      `json:"last_read_timestamp"`
	IsMuted           bool       `json:"is_muted"`
	MutedUntil        int64      `json:"muted_until"`
	CreatedAt         int64      `json:"created_at"`
	UpdatedAt         int64      `json:"updated_at"`
}

// MemberRole represents the role of a room member
type MemberRole string

const (
	MemberRoleOwner  MemberRole = "OWNER"
	MemberRoleAdmin  MemberRole = "ADMIN"
	MemberRoleMember MemberRole = "MEMBER"
)

// RoomSession represents a user's session in a chat room
type RoomSession struct {
	ID            string `json:"id"`
	UserID        string `json:"user_id"`
	RoomID        string `json:"room_id"`
	StartTime     int64  `json:"start_time"`
	EndTime       int64  `json:"end_time"`
	LastMessage   string `json:"last_message,omitempty"`
	LastMessageAt int64  `json:"last_message_at"`
	UnreadCount   int64  `json:"unread_count"`
	IsMuted       bool   `json:"is_muted"`
	CreatedAt     int64  `json:"created_at"`
	UpdatedAt     int64  `json:"updated_at"`
}

// ChatRoomInvite represents an invitation to join a chat room
type ChatRoomInvite struct {
	ID        string       `json:"id"`
	RoomID    string       `json:"room_id"`
	InviterID string       `json:"inviter_id"`
	InviteeID string       `json:"invitee_id"`
	Status    InviteStatus `json:"status"`
	Message   string       `json:"message,omitempty"`
	ExpiresAt int64        `json:"expires_at"`
	UsedAt    int64        `json:"used_at"`
	CreatedAt int64        `json:"created_at"`
	UpdatedAt int64        `json:"updated_at"`
}

// InviteStatus represents the status of a room invitation
type InviteStatus string

const (
	InviteStatusPending  InviteStatus = "PENDING"
	InviteStatusAccepted InviteStatus = "ACCEPTED"
	InviteStatusDeclined InviteStatus = "DECLINED"
	InviteStatusExpired  InviteStatus = "EXPIRED"
	InviteStatusCanceled InviteStatus = "CANCELED"
)

// ChatRoom methods

// IsPrivate checks if the room is private (one-to-one)
func (room *ChatRoom) IsPrivate() bool {
	return room.Type == RoomTypeOneToOne
}

// IsGroup checks if the room is a group chat
func (room *ChatRoom) IsGroup() bool {
	return room.Type == RoomTypeGroup
}

// IsOneToOne checks if the room is a one-to-one chat
func (room *ChatRoom) IsOneToOne() bool {
	return room.Type == RoomTypeOneToOne
}

// IsChannel checks if the room is a channel
func (room *ChatRoom) IsChannel() bool {
	return room.Type == RoomTypeChannel
}

// ChatMessage methods

// IsText checks if the message is a text message
func (msg *ChatMessage) IsText() bool {
	return msg.Type == MessageTypeText
}

// IsTextMessage checks if the message is a text message (alias for backwards compatibility)
func (msg *ChatMessage) IsTextMessage() bool {
	return msg.IsText()
}

// IsMedia checks if the message contains media
func (msg *ChatMessage) IsMedia() bool {
	return msg.Type == MessageTypeImage ||
		msg.Type == MessageTypeVideo ||
		msg.Type == MessageTypeAudio
}

// IsMediaMessage checks if the message contains media (alias for backwards compatibility)
func (msg *ChatMessage) IsMediaMessage() bool {
	return msg.IsMedia()
}

// IsSystemMessage checks if the message is a system message
func (msg *ChatMessage) IsSystemMessage() bool {
	return msg.Type == MessageTypeSystem
}

// IsRevoked checks if the message has been revoked
func (msg *ChatMessage) IsRevoked() bool {
	return msg.Type == MessageTypeRevoked
}

// GetTextContent returns the text content of the message
func (msg *ChatMessage) GetTextContent() string {
	if text, ok := msg.Content["text"].(string); ok {
		return text
	}
	return ""
}

// HasMentions checks if the message contains mentions
func (msg *ChatMessage) HasMentions() bool {
	return len(msg.Mentions) > 0
}

// RoomMember methods

// IsOwner checks if the member is the owner of the room
func (member *RoomMember) IsOwner() bool {
	return member.Role == MemberRoleOwner
}

// IsAdmin checks if the member is an admin
func (member *RoomMember) IsAdmin() bool {
	return member.Role == MemberRoleAdmin
}

// IsMember checks if the member is a regular member
func (member *RoomMember) IsMember() bool {
	return member.Role == MemberRoleMember
}

// CanModerate checks if the member can moderate the room
func (member *RoomMember) CanModerate() bool {
	return member.IsOwner() || member.IsAdmin()
}

// CanManage checks if the member can manage the room (alias for backwards compatibility)
func (member *RoomMember) CanManage() bool {
	return member.CanModerate()
}

// IsMutedAt checks if the member is muted at a specific timestamp
func (member *RoomMember) IsMutedAt(timestamp int64) bool {
	if !member.IsMuted {
		return false
	}
	if member.MutedUntil == 0 {
		return true // Permanent mute
	}
	return timestamp < member.MutedUntil
}

// IsActiveMute checks if the member is currently muted (alias for backwards compatibility)
func (member *RoomMember) IsActiveMute() bool {
	return member.IsMutedAt(time.Now().Unix())
}

// RoomSession methods

// IsActive checks if the session is currently active
func (session *RoomSession) IsActive() bool {
	return session.EndTime == 0
}

// GetDuration returns the duration of the session in seconds
func (session *RoomSession) GetDuration() int64 {
	if session.IsActive() {
		return 0 // Still active
	}
	return session.EndTime - session.StartTime
}

// ChatRoomInvite methods

// IsExpired checks if the invitation has expired
func (invite *ChatRoomInvite) IsExpired() bool {
	return invite.ExpiresAt > 0 && time.Now().Unix() > invite.ExpiresAt
}

// IsUsed checks if the invitation has been used
func (invite *ChatRoomInvite) IsUsed() bool {
	return invite.UsedAt > 0
}

// IsActive checks if the invitation is still active
func (invite *ChatRoomInvite) IsActive() bool {
	return invite.Status == InviteStatusPending && !invite.IsExpired() && !invite.IsUsed()
}
