/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

/**
 * 用户角色枚举
 */
export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  OPERATIONS = 'OPERATIONS',
  CUSTOMER_SERVICE = 'CUSTOMER_SERVICE',
  CONTENT_MODERATOR = 'CONTENT_MODERATOR',
  ANALYST = 'ANALYST',
  VIEWER = 'VIEWER',
}

/**
 * 权限枚举
 */
export enum Permission {
  // 用户管理
  USER_VIEW = 'user:view',
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_SUSPEND = 'user:suspend',
  USER_DELETE = 'user:delete',

  // 服务管理
  SERVICE_VIEW = 'service:view',
  SERVICE_CREATE = 'service:create',
  SERVICE_UPDATE = 'service:update',
  SERVICE_DELETE = 'service:delete',

  // 内容管理
  CONTENT_VIEW = 'content:view',
  CONTENT_MODERATE = 'content:moderate',
  CONTENT_DELETE = 'content:delete',

  // 订单管理
  ORDER_VIEW = 'order:view',
  ORDER_UPDATE = 'order:update',
  ORDER_REFUND = 'order:refund',

  // 财务管理
  FINANCE_VIEW = 'finance:view',
  FINANCE_WITHDRAW = 'finance:withdraw',
  FINANCE_ADJUST = 'finance:adjust',

  // 系统管理
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_MONITOR = 'system:monitor',
  SYSTEM_LOG = 'system:log',

  // 数据分析
  ANALYTICS_VIEW = 'analytics:view',
  ANALYTICS_EXPORT = 'analytics:export',
}

/**
 * 登录用户信息
 */
export interface AuthUser {
  id: string
  username: string
  email: string
  displayName: string
  avatar?: string
  roles: UserRole[]
  permissions: Permission[]
  departmentId?: string
  departmentName?: string
  lastLoginAt: string
  createdAt: string
}

/**
 * 登录请求
 */
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
}

/**
 * 登录响应
 */
export interface LoginResponse {
  token: string
  refreshToken: string
  expiresIn: number
  user: AuthUser
}

/**
 * 刷新令牌请求
 */
export interface RefreshTokenRequest {
  refreshToken: string
}

/**
 * 认证状态
 */
export interface AuthState {
  isAuthenticated: boolean
  user: AuthUser | null
  token: string | null
  refreshToken: string | null
  loading: boolean
  error: string | null
}

/**
 * 权限检查函数类型
 */
export type PermissionChecker = (permission: Permission | Permission[]) => boolean

/**
 * 角色检查函数类型
 */
export type RoleChecker = (role: UserRole | UserRole[]) => boolean 