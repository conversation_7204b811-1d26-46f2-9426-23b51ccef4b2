/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 17:40:00
*/

import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { ConfigProvider, App as AntdApp } from 'antd'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import * as Sentry from "@sentry/react"
import posthog from 'posthog-js'
import 'antd/dist/reset.css'

import App from './App'
import './index.css'

Sentry.init({
    dsn: "YOUR_SENTRY_DSN", // Replace with your actual DSN
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration({
        // Additional Replay configuration goes in here
        maskAllText: false,
        blockAllMedia: false,
      }),
    ],
    // Performance Monitoring
    tracesSampleRate: 1.0, 
    tracePropagationTargets: ["localhost", /^https:\/\/yourserver\.io\/api/],
    // Session Replay
    replaysSessionSampleRate: 0.1, 
    replaysOnErrorSampleRate: 1.0, 
  });

// PostHog Initialization
if (import.meta.env.VITE_NODE_ENV === 'production') {
    posthog.init('YOUR_POSTHOG_KEY', {
      api_host: 'YOUR_POSTHOG_HOST',
      person_profiles: 'identified_only', // or 'always'
      // Link Sentry and PostHog sessions
      session_recording: {
        linked_session: true
      }
    });
  }

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60, // 1 minute
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
      retry: 1, // Retry failed requests once
    },
    mutations: {
      onError: (error) => {
        // Here you can add a global error handler for mutations
        // For example, using a toast notification library
        console.error("Mutation failed:", error)
      }
    }
  },
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#1DA57A',
          },
        }}
      >
        <BrowserRouter>
          <AntdApp>
            <App />
          </AntdApp>
        </BrowserRouter>
      </ConfigProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  </React.StrictMode>,
) 