# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-15 12:00:00
# Modified: 2025-01-15 12:00:00

# Test configuration for analytics service

http:
  port: 8080

# Data warehouse configuration (test)
datawarehouse:
  host: localhost
  port: 5432
  database: analytics_test
  username: test_user
  password: test_pass
  sslmode: disable
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 300

# ClickHouse configuration (test)
clickhouse:
  addr: localhost:9000
  database: analytics_test
  username: test_user
  password: test_pass
  dial_timeout: 30
  max_open_conns: 10
  max_idle_conns: 5

# Redis cache configuration (test)
redis:
  addr: localhost:6379
  password: ""
  db: 1
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5
  read_timeout: 3
  write_timeout: 3

# Logging configuration
log:
  level: debug
  format: json
  output: stdout

# Tracing configuration
tracing:
  enabled: false
  service_name: analytics-service-test
  jaeger_endpoint: http://localhost:14268/api/traces

# Metrics configuration
metrics:
  enabled: true
  port: 9090
  path: /metrics 