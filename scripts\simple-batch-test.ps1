# CINA.CLUB 简化批量编译测试脚本

Write-Host "Starting batch compilation test..." -ForegroundColor Green

$successCount = 0
$failureCount = 0
$results = @()

# Get all service directories
$serviceDirectories = Get-ChildItem -Path "services" -Directory | Where-Object { 
    Test-Path (Join-Path $_.FullName "go.mod") 
}

Write-Host "Found $($serviceDirectories.Count) service modules" -ForegroundColor Cyan

foreach ($serviceDir in $serviceDirectories) {
    $serviceName = $serviceDir.Name
    $servicePath = $serviceDir.FullName
    
    Write-Host "Testing $serviceName..." -ForegroundColor Yellow
    
    # Change to service directory
    $currentLocation = Get-Location
    Set-Location $servicePath
    
    try {
        # Run go build
        $buildOutput = go build ./... 2>&1
        $buildSuccess = $LASTEXITCODE -eq 0
        
        if ($buildSuccess) {
            Write-Host "  SUCCESS: $serviceName compiled" -ForegroundColor Green
            $successCount++
            $results += @{
                Service = $serviceName
                Status = "Success"
                Errors = @()
            }
        } else {
            Write-Host "  FAILED: $serviceName compilation failed" -ForegroundColor Red
            $failureCount++
            
            # Extract key errors
            $errors = $buildOutput | Where-Object { $_ -match "error|Error|cannot find" } | Select-Object -First 3
            $results += @{
                Service = $serviceName
                Status = "Failed"
                Errors = $errors
            }
            
            # Show first error for quick diagnosis
            if ($errors.Count -gt 0) {
                Write-Host "    Error: $($errors[0])" -ForegroundColor Red
            }
        }
    }
    catch {
        Write-Host "  ERROR: Exception during test - $($_.Exception.Message)" -ForegroundColor Red
        $failureCount++
    }
    finally {
        Set-Location $currentLocation
    }
    
    Start-Sleep -Milliseconds 200
}

# Generate summary report
Write-Host ""
Write-Host "=== BATCH COMPILATION TEST SUMMARY ===" -ForegroundColor Blue
Write-Host "Total Services: $($serviceDirectories.Count)" -ForegroundColor Cyan
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failureCount" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($successCount / $serviceDirectories.Count) * 100, 1))%" -ForegroundColor Cyan

# Show successful services
Write-Host ""
Write-Host "=== SUCCESSFUL SERVICES ===" -ForegroundColor Green
$successfulServices = $results | Where-Object { $_.Status -eq "Success" }
foreach ($service in $successfulServices) {
    Write-Host "  ✓ $($service.Service)" -ForegroundColor Green
}

# Show failed services with errors
Write-Host ""
Write-Host "=== FAILED SERVICES ===" -ForegroundColor Red
$failedServices = $results | Where-Object { $_.Status -eq "Failed" }
foreach ($service in $failedServices) {
    Write-Host "  ✗ $($service.Service)" -ForegroundColor Red
    foreach ($error in $service.Errors) {
        if ($error) {
            Write-Host "    - $error" -ForegroundColor Yellow
        }
    }
}

# Save detailed results to file
$reportContent = "# CINA.CLUB Batch Compilation Results`n`n"
$reportContent += "**Test Date**: $(Get-Date)`n"
$reportContent += "**Total Services**: $($serviceDirectories.Count)`n"
$reportContent += "**Successful**: $successCount`n"
$reportContent += "**Failed**: $failureCount`n"
$reportContent += "**Success Rate**: $([math]::Round(($successCount / $serviceDirectories.Count) * 100, 1))%`n`n"

$reportContent += "## Successful Services`n`n"
foreach ($service in $successfulServices) {
    $reportContent += "- ✅ $($service.Service)`n"
}

$reportContent += "`n## Failed Services`n`n"
foreach ($service in $failedServices) {
    $reportContent += "### ❌ $($service.Service)`n"
    foreach ($error in $service.Errors) {
        if ($error) {
            $reportContent += "- $error`n"
        }
    }
    $reportContent += "`n"
}

$reportContent | Out-File -FilePath "BATCH_COMPILE_RESULTS.md" -Encoding UTF8

Write-Host ""
Write-Host "Detailed report saved to: BATCH_COMPILE_RESULTS.md" -ForegroundColor Yellow

if ($failureCount -gt 0) {
    Write-Host ""
    Write-Host "Next steps to improve compilation success:" -ForegroundColor Cyan
    Write-Host "1. Review failed services in the detailed report" -ForegroundColor White
    Write-Host "2. Fix dependency issues with 'go mod tidy'" -ForegroundColor White
    Write-Host "3. Remove empty Go files" -ForegroundColor White
    Write-Host "4. Standardize go.mod configurations" -ForegroundColor White
} 