 /*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using CinaClub.Core.Interfaces;

namespace CinaClub.Infrastructure.Security;

/// <summary>
/// Windows安全存储服务实现
/// 使用Windows Data Protection API (DPAPI) 进行数据加密
/// </summary>
public class WindowsSecureStorageService : ISecureStorageService
{
    private readonly ILogger<WindowsSecureStorageService> _logger;
    private readonly string _storageDirectory;
    private const string StorageFolderName = "CinaClub";
    private const string FileExtension = ".secure";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public WindowsSecureStorageService(ILogger<WindowsSecureStorageService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 设置存储目录
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
        _storageDirectory = Path.Combine(appDataPath, StorageFolderName, "SecureStorage");
        
        // 确保目录存在
        Directory.CreateDirectory(_storageDirectory);
        
        _logger.LogInformation("WindowsSecureStorageService初始化，存储目录: {Directory}", _storageDirectory);
    }

    /// <summary>
    /// 安全存储字符串值
    /// </summary>
    /// <param name="key">存储键</param>
    /// <param name="value">存储值</param>
    /// <returns>存储结果</returns>
    public async Task<bool> SetAsync(string key, string value)
    {
        if (string.IsNullOrEmpty(key))
        {
            throw new ArgumentException("Key不能为空", nameof(key));
        }

        if (value == null)
        {
            throw new ArgumentNullException(nameof(value));
        }

        try
        {
            var bytes = Encoding.UTF8.GetBytes(value);
            return await SetBytesAsync(key, bytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "存储字符串值失败，键: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 获取安全存储的字符串值
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>存储值，如果不存在则返回null</returns>
    public async Task<string?> GetAsync(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            throw new ArgumentException("Key不能为空", nameof(key));
        }

        try
        {
            var bytes = await GetBytesAsync(key);
            if (bytes == null)
            {
                return null;
            }

            return Encoding.UTF8.GetString(bytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取字符串值失败，键: {Key}", key);
            return null;
        }
    }

    /// <summary>
    /// 安全存储字节数组
    /// </summary>
    /// <param name="key">存储键</param>
    /// <param name="value">存储值</param>
    /// <returns>存储结果</returns>
    public async Task<bool> SetBytesAsync(string key, byte[] value)
    {
        if (string.IsNullOrEmpty(key))
        {
            throw new ArgumentException("Key不能为空", nameof(key));
        }

        if (value == null)
        {
            throw new ArgumentNullException(nameof(value));
        }

        return await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("开始存储数据，键: {Key}, 长度: {Length}", key, value.Length);

                // 使用DPAPI加密数据
                var encryptedData = ProtectedData.Protect(value, null, DataProtectionScope.CurrentUser);

                // 写入文件
                var filePath = GetFilePath(key);
                File.WriteAllBytes(filePath, encryptedData);

                _logger.LogDebug("数据存储成功，键: {Key}", key);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "存储字节数组失败，键: {Key}", key);
                return false;
            }
        });
    }

    /// <summary>
    /// 获取安全存储的字节数组
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>存储值，如果不存在则返回null</returns>
    public async Task<byte[]?> GetBytesAsync(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            throw new ArgumentException("Key不能为空", nameof(key));
        }

        return await Task.Run(() =>
        {
            try
            {
                var filePath = GetFilePath(key);
                if (!File.Exists(filePath))
                {
                    _logger.LogDebug("文件不存在，键: {Key}", key);
                    return null;
                }

                _logger.LogDebug("开始读取数据，键: {Key}", key);

                // 读取加密数据
                var encryptedData = File.ReadAllBytes(filePath);

                // 使用DPAPI解密数据
                var decryptedData = ProtectedData.Unprotect(encryptedData, null, DataProtectionScope.CurrentUser);

                _logger.LogDebug("数据读取成功，键: {Key}, 长度: {Length}", key, decryptedData.Length);
                return decryptedData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取字节数组失败，键: {Key}", key);
                return null;
            }
        });
    }

    /// <summary>
    /// 检查指定键是否存在
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ContainsKeyAsync(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            throw new ArgumentException("Key不能为空", nameof(key));
        }

        return await Task.Run(() =>
        {
            var filePath = GetFilePath(key);
            return File.Exists(filePath);
        });
    }

    /// <summary>
    /// 删除指定键的存储值
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>删除结果</returns>
    public async Task<bool> RemoveAsync(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            throw new ArgumentException("Key不能为空", nameof(key));
        }

        return await Task.Run(() =>
        {
            try
            {
                var filePath = GetFilePath(key);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogDebug("删除数据成功，键: {Key}", key);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除数据失败，键: {Key}", key);
                return false;
            }
        });
    }

    /// <summary>
    /// 清空所有存储的值
    /// </summary>
    /// <returns>清空结果</returns>
    public async Task<bool> ClearAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                if (Directory.Exists(_storageDirectory))
                {
                    var files = Directory.GetFiles(_storageDirectory, $"*{FileExtension}");
                    foreach (var file in files)
                    {
                        File.Delete(file);
                    }
                }

                _logger.LogInformation("清空所有存储数据成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空所有存储数据失败");
                return false;
            }
        });
    }

    /// <summary>
    /// 获取所有存储的键
    /// </summary>
    /// <returns>键的集合</returns>
    public async Task<IEnumerable<string>> GetAllKeysAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                if (!Directory.Exists(_storageDirectory))
                {
                    return Enumerable.Empty<string>();
                }

                var files = Directory.GetFiles(_storageDirectory, $"*{FileExtension}");
                var keys = files.Select(file => 
                {
                    var fileName = Path.GetFileNameWithoutExtension(file);
                    return DecodeKey(fileName);
                }).ToList();

                return keys;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有键失败");
                return Enumerable.Empty<string>();
            }
        });
    }

    /// <summary>
    /// 获取文件路径
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>文件路径</returns>
    private string GetFilePath(string key)
    {
        // 对键进行编码以避免文件名非法字符
        var encodedKey = EncodeKey(key);
        return Path.Combine(_storageDirectory, $"{encodedKey}{FileExtension}");
    }

    /// <summary>
    /// 编码键名（避免文件名非法字符）
    /// </summary>
    /// <param name="key">原始键名</param>
    /// <returns>编码后的键名</returns>
    private string EncodeKey(string key)
    {
        var bytes = Encoding.UTF8.GetBytes(key);
        return Convert.ToBase64String(bytes).Replace("/", "_").Replace("+", "-").Replace("=", "");
    }

    /// <summary>
    /// 解码键名
    /// </summary>
    /// <param name="encodedKey">编码后的键名</param>
    /// <returns>原始键名</returns>
    private string DecodeKey(string encodedKey)
    {
        try
        {
            var base64 = encodedKey.Replace("_", "/").Replace("-", "+");
            // 补充可能缺失的填充字符
            while (base64.Length % 4 != 0)
            {
                base64 += "=";
            }
            
            var bytes = Convert.FromBase64String(base64);
            return Encoding.UTF8.GetString(bytes);
        }
        catch
        {
            // 如果解码失败，返回编码后的键名
            return encodedKey;
        }
    }
}