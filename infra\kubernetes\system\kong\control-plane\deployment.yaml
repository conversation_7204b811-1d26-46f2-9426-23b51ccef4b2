# CINA.CLUB Platform - Kong Ingress Controller (Control Plane)
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Service Account for Kong Ingress Controller
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kong-ingress-controller
  namespace: kong-system
  labels:
    app: kong-ingress-controller
    component: control-plane
    tier: platform-infrastructure
  annotations:
    description: "Service account for Kong Ingress Controller control plane"

---
# Cluster Role for Kong Ingress Controller
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kong-ingress-controller
  labels:
    app: kong-ingress-controller
    component: control-plane
rules:
  # Core Kubernetes resources
  - apiGroups: [""]
    resources: ["services", "endpoints", "secrets", "configmaps", "pods", "nodes"]
    verbs: ["get", "list", "watch"]
  
  - apiGroups: [""]
    resources: ["services/status", "endpoints/status"]
    verbs: ["get", "patch", "update"]
  
  # Events for logging
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]
  
  # Networking resources
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses", "ingressclasses"]
    verbs: ["get", "list", "watch"]
  
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses/status"]
    verbs: ["get", "patch", "update"]
  
  # Kong CRDs - full control over Kong configuration
  - apiGroups: ["configuration.konghq.com"]
    resources: ["*"]
    verbs: ["get", "list", "watch", "create", "patch", "update", "delete"]
  
  # Gateway API resources
  - apiGroups: ["gateway.networking.k8s.io"]
    resources: ["gateways", "httproutes", "tcproutes", "tlsroutes", "udproutes", "referencegrants"]
    verbs: ["get", "list", "watch"]
  
  - apiGroups: ["gateway.networking.k8s.io"]
    resources: ["gateways/status", "httproutes/status", "tcproutes/status", "tlsroutes/status", "udproutes/status"]
    verbs: ["get", "patch", "update"]
  
  # Discovery API
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]
  
  # Coordination for leader election
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "list", "watch", "create", "patch", "update"]

---
# Cluster Role Binding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kong-ingress-controller
  labels:
    app: kong-ingress-controller
    component: control-plane
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kong-ingress-controller
subjects:
  - kind: ServiceAccount
    name: kong-ingress-controller
    namespace: kong-system

---
# Kong Ingress Controller Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kong-ingress-controller
  namespace: kong-system
  labels:
    app: kong-ingress-controller
    component: control-plane
    tier: platform-infrastructure
    app.kubernetes.io/name: kong-ingress-controller
    app.kubernetes.io/component: control-plane
    app.kubernetes.io/part-of: kong-gateway
    app.kubernetes.io/managed-by: platform-engineering
  annotations:
    description: "Kong Ingress Controller - watches Kubernetes API and configures Kong proxy"
spec:
  replicas: 1  # Single replica for leader election (control plane doesn't need HA)
  strategy:
    type: Recreate  # Ensure only one controller is active at a time
  
  selector:
    matchLabels:
      app: kong-ingress-controller
      component: control-plane
  
  template:
    metadata:
      labels:
        app: kong-ingress-controller
        component: control-plane
        tier: platform-infrastructure
        version: "2.12"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    
    spec:
      # Security context for pod
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Service account
      serviceAccountName: kong-ingress-controller
      
      # Node selection for stable nodes
      nodeSelector:
        kubernetes.io/os: linux
      
      # Containers
      containers:
        - name: kong-ingress-controller
          image: kong/kubernetes-ingress-controller:2.12.0
          imagePullPolicy: IfNotPresent
          
          # Security context for container
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
          
          # Environment variables
          env:
            # Kong proxy configuration
            - name: CONTROLLER_KONG_ADMIN_URL
              value: "http://kong-proxy.kong-system.svc.cluster.local:8001"
            - name: CONTROLLER_KONG_ADMIN_TLS_SKIP_VERIFY
              value: "false"
            - name: CONTROLLER_PUBLISH_SERVICE
              value: "kong-system/kong-proxy"
            
            # Controller configuration
            - name: CONTROLLER_INGRESS_CLASS
              value: "kong"
            - name: CONTROLLER_ELECTION_ID
              value: "kong-ingress-controller-leader-kong-system"
            - name: CONTROLLER_ELECTION_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            
            # Feature gates
            - name: CONTROLLER_FEATURE_GATES
              value: "GatewayAlpha=true,CombinedServiceRoutes=true,ExpressionRoutes=true"
            
            # Logging configuration
            - name: CONTROLLER_LOG_LEVEL
              value: "info"
            - name: CONTROLLER_LOG_FORMAT
              value: "json"
            
            # Performance tuning
            - name: CONTROLLER_KUSTOMIZE
              value: "false"
            - name: CONTROLLER_UPDATE_STATUS
              value: "true"
            - name: CONTROLLER_UPDATE_STATUS_ON_SHUTDOWN
              value: "true"
            
            # Admission webhook configuration
            - name: CONTROLLER_ADMISSION_WEBHOOK_LISTEN
              value: "off"  # Disabled for simplicity
            
            # Metrics and debugging
            - name: CONTROLLER_ANONYMOUS_REPORTS
              value: "false"
            - name: CONTROLLER_DEBUG_LOG_REDUCE_REDUNDANCY
              value: "true"
          
          # Ports
          ports:
            - name: health
              containerPort: 10254
              protocol: TCP
            - name: metrics
              containerPort: 8080
              protocol: TCP
          
          # Volume mounts
          volumeMounts:
            - name: tmp
              mountPath: /tmp
          
          # Resource requirements
          resources:
            requests:
              cpu: "100m"
              memory: "256Mi"
            limits:
              cpu: "1"
              memory: "512Mi"
          
          # Health checks
          livenessProbe:
            httpGet:
              path: /healthz
              port: 10254
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /readyz
              port: 10254
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          
          # Startup probe
          startupProbe:
            httpGet:
              path: /healthz
              port: 10254
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 30
      
      # Volumes
      volumes:
        - name: tmp
          emptyDir:
            sizeLimit: 1Gi
      
      # Termination grace period
      terminationGracePeriodSeconds: 30
      
      # DNS configuration
      dnsPolicy: ClusterFirst
      dnsConfig:
        options:
          - name: ndots
            value: "2"
          - name: edns0

---
# Service for Kong Ingress Controller (for metrics)
apiVersion: v1
kind: Service
metadata:
  name: kong-ingress-controller
  namespace: kong-system
  labels:
    app: kong-ingress-controller
    component: control-plane
  annotations:
    description: "Kong Ingress Controller service for metrics and health checks"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  
  selector:
    app: kong-ingress-controller
    component: control-plane
  
  ports:
    # Health check port
    - name: health
      port: 10254
      targetPort: 10254
      protocol: TCP
    
    # Metrics port
    - name: metrics
      port: 8080
      targetPort: 8080
      protocol: TCP 