/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16
Modified: 2025-01-16
*/

// Package config provides a standardized configuration loading and validation system for CINA.CLUB backend services.
// It implements a layered configuration strategy with support for files, environment variables, and defaults,
// backed by Viper and go-playground/validator for robust configuration management.
package config

import (
	"fmt"
	"os"
	"reflect"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server struct {
		Port         int    `mapstructure:"port"`
		Mode         string `mapstructure:"mode"`
		ReadTimeout  int    `mapstructure:"read_timeout"`
		WriteTimeout int    `mapstructure:"write_timeout"`
	} `mapstructure:"server"`

	Database struct {
		Postgres struct {
			Host     string `mapstructure:"host"`
			Port     int    `mapstructure:"port"`
			User     string `mapstructure:"user"`
			Password string `mapstructure:"password"`
			Database string `mapstructure:"database"`
			SSLMode  string `mapstructure:"ssl_mode"`
		} `mapstructure:"postgres"`
	} `mapstructure:"database"`

	FileStorage struct {
		Provider string `mapstructure:"provider"`
		Endpoint string `mapstructure:"endpoint"`
		Bucket   string `mapstructure:"bucket"`
		Region   string `mapstructure:"region"`
	} `mapstructure:"file_storage"`

	Notification struct {
		Provider string `mapstructure:"provider"`
		Endpoint string `mapstructure:"endpoint"`
	} `mapstructure:"notification"`

	Log struct {
		Level  string `mapstructure:"level"`
		Format string `mapstructure:"format"`
	} `mapstructure:"log"`

	RateLimit struct {
		Enabled bool `mapstructure:"enabled"`
		RPS     int  `mapstructure:"rps"`
	} `mapstructure:"rate_limit"`
}

// Loader encapsulates all configuration loading logic and options
type Loader struct {
	// Internal state - each Load operation creates a new viper instance for isolation
}

// NewLoader creates a new configuration loader instance
func NewLoader() *Loader {
	return &Loader{}
}

// Load is the main function that loads and validates configuration from multiple sources.
// It implements the layered configuration strategy: defaults -> file -> environment variables.
//
// Parameters:
//   - configPath: path to the configuration file (YAML format)
//   - configStruct: pointer to the configuration struct to populate
//
// The function will:
//  1. Create a new Viper instance for isolation
//  2. Set up environment variable binding with CINA_ prefix
//  3. Load configuration file if it exists
//  4. Apply environment variable overrides
//  5. Unmarshal configuration into the provided struct
//  6. Validate the configuration using struct tags
//
// Returns an error if any step fails, ensuring fail-fast behavior during service startup.
func (l *Loader) Load(configPath string, configStruct interface{}) error {
	if configStruct == nil {
		return fmt.Errorf("configStruct cannot be nil")
	}

	// Ensure configStruct is a pointer to a struct
	v := reflect.ValueOf(configStruct)
	if v.Kind() != reflect.Ptr || v.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("configStruct must be a pointer to a struct, got %T", configStruct)
	}

	// Step 1: Initialize a new Viper instance for isolation
	vp := viper.New()

	// Step 2: Configure environment variable binding
	vp.SetEnvPrefix("CINA")                                       // All env vars start with CINA_
	vp.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "_")) // Convert dots and dashes to underscores
	vp.AutomaticEnv()                                             // Enable automatic environment variable lookup

	// Step 3: Load configuration file
	if configPath != "" {
		if err := l.loadConfigFile(vp, configPath); err != nil {
			return fmt.Errorf("failed to load config file '%s': %w", configPath, err)
		}
	}

	// Step 4: Apply defaults using struct tags (if present)
	if err := l.applyDefaults(vp, configStruct); err != nil {
		return fmt.Errorf("failed to apply defaults: %w", err)
	}

	// Step 5: Unmarshal configuration into struct
	if err := vp.Unmarshal(configStruct); err != nil {
		return fmt.Errorf("failed to unmarshal config into struct: %w", err)
	}

	// Step 6: Validate the configuration
	if err := validateStruct(configStruct); err != nil {
		formattedErr := FormatValidationError(err)
		return fmt.Errorf("config validation failed: %w", formattedErr)
	}

	return nil
}

// loadConfigFile loads the configuration file using Viper
func (l *Loader) loadConfigFile(vp *viper.Viper, configPath string) error {
	// Check if file exists
	if _, err := os.Stat(configPath); err != nil {
		if os.IsNotExist(err) {
			// File doesn't exist, which might be okay for some deployments
			// Log but don't fail - environment variables might provide all config
			return nil
		}
		return fmt.Errorf("cannot access config file: %w", err)
	}

	// Set the config file path
	vp.SetConfigFile(configPath)

	// Read the configuration file
	if err := vp.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	return nil
}

// applyDefaults applies default values from struct tags to Viper
// This function uses reflection to find 'default' tags and set them in Viper
func (l *Loader) applyDefaults(vp *viper.Viper, configStruct interface{}) error {
	return l.setDefaultsRecursive(vp, "", reflect.ValueOf(configStruct).Elem(), reflect.TypeOf(configStruct).Elem())
}

// setDefaultsRecursive recursively processes struct fields to find and apply default values
func (l *Loader) setDefaultsRecursive(vp *viper.Viper, prefix string, value reflect.Value, typ reflect.Type) error {
	for i := 0; i < value.NumField(); i++ {
		field := value.Field(i)
		fieldType := typ.Field(i)

		// Skip unexported fields
		if !field.CanSet() {
			continue
		}

		// Get the mapstructure tag to determine the config key
		mapstructureTag := fieldType.Tag.Get("mapstructure")
		if mapstructureTag == "" || mapstructureTag == "-" {
			// If no mapstructure tag, use the field name in lowercase
			mapstructureTag = strings.ToLower(fieldType.Name)
		}

		// Build the full key path
		var fullKey string
		if prefix != "" {
			fullKey = prefix + "." + mapstructureTag
		} else {
			fullKey = mapstructureTag
		}

		// Check if this field has a default value
		if defaultValue := fieldType.Tag.Get("default"); defaultValue != "" {
			vp.SetDefault(fullKey, defaultValue)
		}

		// If this field is a struct, recurse into it
		if field.Kind() == reflect.Struct {
			if err := l.setDefaultsRecursive(vp, fullKey, field, fieldType.Type); err != nil {
				return err
			}
		}
	}

	return nil
}

// LoadConfig is a convenience function that creates a new loader and loads configuration
// This is the most common usage pattern for services.
func LoadConfig(configPath string, configStruct interface{}) error {
	loader := NewLoader()
	return loader.Load(configPath, configStruct)
}

// MustLoadConfig is like LoadConfig but panics if loading fails
// This is useful for services that cannot continue without valid configuration
func MustLoadConfig(configPath string, configStruct interface{}) {
	if err := LoadConfig(configPath, configStruct); err != nil {
		panic(fmt.Sprintf("failed to load configuration: %v", err))
	}
}

// ConfigInfo provides information about the loaded configuration
type ConfigInfo struct {
	ConfigFile      string            `json:"config_file"`
	EnvVarsFound    []string          `json:"env_vars_found"`
	DefaultsApplied map[string]string `json:"defaults_applied"`
}

// LoadWithInfo loads configuration and returns additional information about the loading process
// This is useful for debugging configuration issues
func (l *Loader) LoadWithInfo(configPath string, configStruct interface{}) (*ConfigInfo, error) {
	// For now, just call the regular Load function
	// In the future, this could be enhanced to collect and return detailed loading information
	err := l.Load(configPath, configStruct)
	if err != nil {
		return nil, err
	}

	info := &ConfigInfo{
		ConfigFile:      configPath,
		EnvVarsFound:    l.getEnvironmentVariables(),
		DefaultsApplied: make(map[string]string),
	}

	return info, nil
}

// getEnvironmentVariables returns a list of CINA_* environment variables that were found
func (l *Loader) getEnvironmentVariables() []string {
	var envVars []string
	for _, env := range os.Environ() {
		if strings.HasPrefix(env, "CINA_") {
			envVars = append(envVars, env)
		}
	}
	return envVars
}

// Load loads configuration from file or environment variables
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// Set default values
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "development")
	viper.SetDefault("server.read_timeout", 10)
	viper.SetDefault("server.write_timeout", 10)
	viper.SetDefault("database.postgres.host", "localhost")
	viper.SetDefault("database.postgres.port", 5432)
	viper.SetDefault("database.postgres.user", "postgres")
	viper.SetDefault("database.postgres.password", "password")
	viper.SetDefault("database.postgres.database", "cloud_sync")
	viper.SetDefault("database.postgres.ssl_mode", "disable")
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "text")
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.rps", 100)

	// Environment variable support
	viper.AutomaticEnv()

	// Try to read config file, but don't fail if it doesn't exist
	viper.ReadInConfig()

	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}
