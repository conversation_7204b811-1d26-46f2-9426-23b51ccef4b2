/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

// Package jwks provides a high-performance JWKS (JSON Web Key Set) client with automatic refresh and caching.
package jwks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/patrickmn/go-cache"
	"golang.org/x/time/rate"
	"gopkg.in/square/go-jose.v2"
)

const (
	defaultCacheTTL        = 1 * time.Hour
	defaultRefreshInterval = 10 * time.Second
	defaultTimeout         = 30 * time.Second
	jwksCacheKey           = "jwks"
)

var (
	defaultRefreshLimit = rate.Every(defaultRefreshInterval) // Max one refresh per 10 seconds
)

var (
	ErrJWKSNotFound   = errors.New("JWKS not available")
	ErrKeyNotFound    = errors.New("key not found in JWKS")
	ErrInvalidKeyType = errors.New("invalid key type")
)

// Client provides high-performance JWKS retrieval with automatic caching and refresh
type Client struct {
	jwksURL    string
	cache      *cache.Cache
	refresher  *rate.Limiter
	httpClient *http.Client
	mu         sync.RWMutex
	staleJWKS  *jose.JSONWebKeySet // Fallback for when refresh fails
}

// NewClient creates a new JWKS client with the specified JWKS URL
func NewClient(jwksURL string) *Client {
	return &Client{
		jwksURL:    jwksURL,
		cache:      cache.New(defaultCacheTTL, 2*defaultCacheTTL),
		refresher:  rate.NewLimiter(defaultRefreshLimit, 1),
		httpClient: &http.Client{Timeout: defaultTimeout},
	}
}

// NewClientWithOptions creates a new JWKS client with custom options
func NewClientWithOptions(jwksURL string, cacheTTL time.Duration, refreshLimit rate.Limit, timeout time.Duration) *Client {
	return &Client{
		jwksURL:    jwksURL,
		cache:      cache.New(cacheTTL, 2*cacheTTL),
		refresher:  rate.NewLimiter(refreshLimit, 1),
		httpClient: &http.Client{Timeout: timeout},
	}
}

// GetKey retrieves the public key for JWT validation based on the token's "kid" claim
// This method is designed to be used with jwt.Parse as the key function
func (c *Client) GetKey(token *jwt.Token) (interface{}, error) {
	// Extract kid from token header
	kid, ok := token.Header["kid"].(string)
	if !ok || kid == "" {
		return nil, errors.New("token missing kid claim")
	}

	// Try to get JWKS from cache first
	jwks, err := c.getJWKS(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get JWKS: %w", err)
	}

	// Find the key by kid
	key := jwks.Key(kid)
	if len(key) == 0 {
		return nil, fmt.Errorf("%w: kid=%s", ErrKeyNotFound, kid)
	}

	// Return the public key
	return key[0].Key, nil
}

// getJWKS retrieves JWKS from cache or fetches from remote URL
func (c *Client) getJWKS(ctx context.Context) (*jose.JSONWebKeySet, error) {
	// Try cache first
	if cached, found := c.cache.Get(jwksCacheKey); found {
		if jwks, ok := cached.(*jose.JSONWebKeySet); ok {
			return jwks, nil
		}
	}

	// Cache miss - try to refresh
	return c.refreshJWKS(ctx)
}

// refreshJWKS fetches JWKS from the remote URL with rate limiting
func (c *Client) refreshJWKS(ctx context.Context) (*jose.JSONWebKeySet, error) {
	// Check rate limit
	if !c.refresher.Allow() {
		// Rate limited - try to return stale cache
		c.mu.RLock()
		stale := c.staleJWKS
		c.mu.RUnlock()

		if stale != nil {
			return stale, nil
		}
		return nil, errors.New("rate limited and no stale cache available")
	}

	// Fetch JWKS from remote URL
	req, err := http.NewRequestWithContext(ctx, "GET", c.jwksURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		// Network error - try stale cache
		return c.tryStaleCache(err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err := fmt.Errorf("JWKS endpoint returned status %d", resp.StatusCode)
		return c.tryStaleCache(err)
	}

	// Parse JWKS
	var jwks jose.JSONWebKeySet
	if err := json.NewDecoder(resp.Body).Decode(&jwks); err != nil {
		return c.tryStaleCache(fmt.Errorf("failed to parse JWKS: %w", err))
	}

	// Cache the fresh JWKS
	c.cache.Set(jwksCacheKey, &jwks, cache.DefaultExpiration)

	// Update stale cache
	c.mu.Lock()
	c.staleJWKS = &jwks
	c.mu.Unlock()

	return &jwks, nil
}

// tryStaleCache attempts to return stale cache when refresh fails
func (c *Client) tryStaleCache(refreshErr error) (*jose.JSONWebKeySet, error) {
	c.mu.RLock()
	stale := c.staleJWKS
	c.mu.RUnlock()

	if stale != nil {
		// Return stale cache but continue trying to refresh in background
		go func() {
			// Background refresh attempt (best effort)
			ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
			defer cancel()
			c.refreshJWKS(ctx)
		}()
		return stale, nil
	}

	return nil, fmt.Errorf("refresh failed and no stale cache: %w", refreshErr)
}

// Preload fetches and caches JWKS during service startup
func (c *Client) Preload(ctx context.Context) error {
	_, err := c.refreshJWKS(ctx)
	return err
}

// ClearCache removes all cached JWKS data
func (c *Client) ClearCache() {
	c.cache.Flush()
	c.mu.Lock()
	c.staleJWKS = nil
	c.mu.Unlock()
}

// KeyFunc returns a function compatible with jwt.Parse for key retrieval
func (c *Client) KeyFunc() jwt.Keyfunc {
	return c.GetKey
}
