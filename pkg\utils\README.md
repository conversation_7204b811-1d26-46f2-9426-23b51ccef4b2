# pkg/utils - Common Utility Functions

The `pkg/utils` package provides a collection of zero-dependency, high-performance utility functions for common programming tasks in the CINA.CLUB monorepo. These utilities are designed to be safe, efficient, and easy to use across all backend services.

## Design Principles

- **Zero Dependencies**: Only uses Go standard library
- **Pure Functions**: Most functions have no side effects
- **High Performance**: Optimized for speed and memory efficiency
- **Type Safety**: Leverages Go generics for better type safety
- **Comprehensive Testing**: 100% test coverage requirement

## Packages

### conv - Type Conversion
Provides safe and convenient type conversion utilities.

```go
import "cinaclub.com/pkg/utils/conv"

// Pointer conversion
ptr := conv.ToPointer(42)
value := conv.FromPointer(ptr, 0)

// String conversion with defaults
num := conv.Atoi("123", 0)         // Returns 123
num = conv.Atoi("invalid", 999)    // Returns 999 (default)

// Any type to string
str := conv.ToString(123)          // Returns "123"
```

### crypto - Cryptographic Functions
Provides common hashing and password handling functions.

```go
import "cinaclub.com/pkg/utils/crypto"

// Hashing
hash := crypto.SHA256String("hello world")
mac := crypto.HMACSHA256String("message", "secret-key")

// Password hashing (PBKDF2-SHA256)
hash, err := crypto.HashPassword("myPassword123!")
isValid := crypto.CheckPasswordHash("myPassword123!", hash)
```

### rand - Secure Random Generation
Provides cryptographically secure random number and string generation.

```go
import "cinaclub.com/pkg/utils/rand"

// Random strings
str, _ := rand.String(10)           // "aB3dE9fG2h"
digits, _ := rand.Digits(6)         // "123456"
hex, _ := rand.Hex(8)              // "1A2B3C4D"

// Random numbers
num, _ := rand.Int(100)            // 0-99
num, _ := rand.IntRange(10, 20)    // 10-20
```

### slice - Generic Slice Operations
Provides generic slice operations not available in the standard library.

```go
import "cinaclub.com/pkg/utils/slice"

// Contains and search
found := slice.Contains([]int{1, 2, 3}, 2)    // true
index := slice.Index([]string{"a", "b"}, "b") // 1

// Set operations
unique := slice.Unique([]int{1, 2, 2, 3})     // [1, 2, 3]
diff := slice.Difference([]int{1, 2, 3}, []int{2, 4}) // [1, 3]
union := slice.Union([]int{1, 2}, []int{2, 3}) // [1, 2, 3]

// Functional operations
found, exists := slice.Find([]int{1, 2, 3}, func(x int) bool { return x > 2 })
hasEven := slice.Any([]int{1, 3, 5, 8}, func(x int) bool { return x%2 == 0 })
```

### str - String Utilities
Provides string manipulation utilities that complement the standard strings package.

```go
import "cinaclub.com/pkg/utils/str"

// Case conversion
snake := str.ToSnakeCase("HelloWorld")     // "hello_world"
camel := str.ToCamelCase("hello_world")    // "helloWorld"
pascal := str.ToPascalCase("hello_world")  // "HelloWorld"

// String manipulation
truncated := str.Truncate("Hello, World!", 8, "...")  // "Hello..."
padded := str.PadLeft("hello", 10, "0")              // "00000hello"
reversed := str.Reverse("hello")                     // "olleh"

// Template rendering
template := "Hello {name}, you are {age} years old!"
data := map[string]interface{}{"name": "John", "age": 30}
result := str.RenderTemplate(template, data)
// "Hello John, you are 30 years old!"
```

### timeutil - Time Utilities
Provides time utilities that complement the standard time package.

```go
import "cinaclub.com/pkg/utils/timeutil"

// Timestamps
millis := timeutil.NowMillis()
seconds := timeutil.NowSeconds()

// Time calculations
start := timeutil.StartOfDay(time.Now())
end := timeutil.EndOfDay(time.Now())
age := timeutil.Age(birthDate)
days := timeutil.DaysBetween(start, end)

// Formatting
formatted := timeutil.FormatDuration(2*time.Hour + 30*time.Minute) // "2h30m"

// Parsing
t, format, err := timeutil.ParseAnyFormat("2023-01-15")
```

### validator - Data Validation
Provides common validation functions for data format checking.

```go
import "cinaclub.com/pkg/utils/validator"

// Format validation
isValid := validator.IsEmail("<EMAIL>")
isValid = validator.IsURL("https://example.com")
isValid = validator.IsPhoneNumber("+**********")

// Type validation
isValid = validator.IsNumeric("12345")
isValid = validator.IsAlpha("abcDEF")
isValid = validator.IsFloat("123.45")

// Password validation
policy := validator.DefaultPasswordPolicy()
isValid = validator.ValidatePassword("MyPassword123!", policy)
strength := validator.CheckPasswordStrength("MyPassword123!")
```

## License

Copyright (c) 2025 Cina.Club. All rights reserved. 