/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package toolkit

import (
	"context"

	"cina.club/services/ai-assistant-service/internal/application/port"
)

// QueryMemoryTool represents a tool for querying memory
type QueryMemoryTool struct {
	name        string
	description string
}

// NewQueryMemoryTool creates a new memory query tool
func NewQueryMemoryTool() port.Tool {
	return &QueryMemoryTool{
		name:        "query_memory",
		description: "Query user's memory information, including preferences, conversation history, important events, etc.",
	}
}

// Name returns the tool name
func (t *QueryMemoryTool) Name() string {
	return t.name
}

// Description returns the tool description
func (t *QueryMemoryTool) Description() string {
	return t.description
}

// Category returns the tool category
func (t *QueryMemoryTool) Category() port.ToolCategory {
	return port.ToolCategoryMemory
}

// RequiresAuth returns whether authentication is required
func (t *QueryMemoryTool) RequiresAuth() bool {
	return true // Memory queries require user authentication
}

// IsAsync returns whether this is an async tool
func (t *QueryMemoryTool) IsAsync() bool {
	return false
}

// InputSchema returns the input parameter schema
func (t *QueryMemoryTool) InputSchema() *port.JSONSchema {
	schema := port.NewObjectSchema(
		"Query memory parameters",
		map[string]*port.JSONSchema{},
		[]string{"query"},
	)

	schema.AddProperty("query", port.NewStringSchema("Query keywords", true))
	schema.AddProperty("memory_type", port.NewStringSchema("Memory type", false))

	limitMin := float64(1)
	limitMax := float64(20)
	schema.AddProperty("limit", port.NewIntegerSchema("Limit number of results returned", &limitMin, &limitMax))

	return schema
}

// OutputSchema returns the output result schema
func (t *QueryMemoryTool) OutputSchema() *port.JSONSchema {
	memorySchema := port.NewObjectSchema(
		"Memory item",
		map[string]*port.JSONSchema{},
		[]string{"id", "content", "type"},
	)

	memorySchema.AddProperty("id", port.NewStringSchema("Memory ID", true))
	memorySchema.AddProperty("content", port.NewStringSchema("Memory content", true))
	memorySchema.AddProperty("type", port.NewStringSchema("Memory type", true))
	memorySchema.AddProperty("importance", port.NewIntegerSchema("Importance level (1-10)", nil, nil))
	memorySchema.AddProperty("created_at", port.NewStringSchema("Creation time", false))
	memorySchema.AddProperty("tags", port.NewArraySchema("Tags", port.NewStringSchema("Tag", false)))

	return port.NewObjectSchema(
		"Query memory result",
		map[string]*port.JSONSchema{
			"memories":    port.NewArraySchema("Memory list", memorySchema),
			"total_count": port.NewIntegerSchema("Total count", nil, nil),
		},
		[]string{"memories", "total_count"},
	)
}

// Execute executes the tool logic
func (t *QueryMemoryTool) Execute(ctx context.Context, inputs map[string]interface{}) (*port.ToolResult, error) {
	// Extract parameters
	query, ok := inputs["query"].(string)
	if !ok || query == "" {
		return port.NewToolError("query is required and must be a string"), nil
	}

	memoryType := ""
	if mt, ok := inputs["memory_type"].(string); ok {
		memoryType = mt
	}

	limit := 10
	if l, ok := inputs["limit"].(float64); ok {
		limit = int(l)
	}

	// TODO: Actual memory query logic
	// This should call the memory-service gRPC interface
	result := t.queryMemory(ctx, query, memoryType, limit)

	return port.NewToolResult(map[string]interface{}{
		"memories":    result.Memories,
		"total_count": result.TotalCount,
	}), nil
}

// MemoryResult represents the result of a memory query
type MemoryResult struct {
	Memories   []MemoryItem `json:"memories"`
	TotalCount int          `json:"total_count"`
}

// MemoryItem represents a memory item
type MemoryItem struct {
	ID         string   `json:"id"`
	Content    string   `json:"content"`
	Type       string   `json:"type"`
	Importance int      `json:"importance"`
	CreatedAt  string   `json:"created_at"`
	Tags       []string `json:"tags"`
}

// queryMemory queries memory (simplified implementation)
func (t *QueryMemoryTool) queryMemory(_ context.Context, _ string, memoryType string, limit int) *MemoryResult {
	// This is a simplified implementation, should call memory-service gRPC interface in practice

	memories := []MemoryItem{
		{
			ID:         "memory-001",
			Content:    "User prefers arranging outdoor activities on weekends",
			Type:       "preference",
			Importance: 8,
			CreatedAt:  "2024-01-15T10:30:00Z",
			Tags:       []string{"outdoor", "weekend", "preference"},
		},
		{
			ID:         "memory-002",
			Content:    "User previously asked about photography service information",
			Type:       "conversation",
			Importance: 6,
			CreatedAt:  "2024-01-20T14:20:00Z",
			Tags:       []string{"photography", "service", "conversation"},
		},
		{
			ID:         "memory-003",
			Content:    "User's birthday is March 15th",
			Type:       "personal_info",
			Importance: 9,
			CreatedAt:  "2024-01-10T09:00:00Z",
			Tags:       []string{"birthday", "personal"},
		},
	}

	// Simple filtering logic
	filteredMemories := []MemoryItem{}
	for _, memory := range memories {
		if memoryType != "" && memory.Type != memoryType {
			continue
		}
		filteredMemories = append(filteredMemories, memory)
	}

	// Apply limit
	if len(filteredMemories) > limit {
		filteredMemories = filteredMemories[:limit]
	}

	return &MemoryResult{
		Memories:   filteredMemories,
		TotalCount: len(filteredMemories),
	}
}
