/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package conv provides safe and convenient type conversion utilities.
package conv

// ToPointer returns a pointer to the given value.
// This is useful when you need to take the address of a literal or value.
//
// Example:
//
//	stringPtr := conv.ToPointer("hello")
//	intPtr := conv.ToPointer(42)
func ToPointer[T any](v T) *T {
	return &v
}

// FromPointer returns the value pointed to by p, or defaultValue if p is nil.
// This is useful for safely dereferencing pointers with a fallback value.
//
// Example:
//
//	var ptr *string
//	value := conv.FromPointer(ptr, "default") // returns "default"
//
//	ptr = conv.ToPointer("hello")
//	value = conv.FromPointer(ptr, "default")  // returns "hello"
func FromPointer[T any](p *T, defaultValue T) T {
	if p == nil {
		return defaultValue
	}
	return *p
}

// ToOptionalPointer returns a pointer to the given value if the value is not
// the zero value of its type, otherwise returns nil.
// This is useful for converting optional values to pointers.
//
// Example:
//
//	ptr := conv.ToOptionalPointer("")        // returns nil
//	ptr = conv.ToOptionalPointer("hello")    // returns pointer to "hello"
//	ptr = conv.ToOptionalPointer(0)          // returns nil
//	ptr = conv.ToOptionalPointer(42)         // returns pointer to 42
func ToOptionalPointer[T comparable](v T) *T {
	var zero T
	if v == zero {
		return nil
	}
	return &v
}

// FromOptionalPointer returns the value pointed to by p, or the zero value
// of type T if p is nil.
//
// Example:
//
//	var ptr *string
//	value := conv.FromOptionalPointer(ptr)   // returns ""
//
//	ptr = conv.ToPointer("hello")
//	value = conv.FromOptionalPointer(ptr)    // returns "hello"
func FromOptionalPointer[T any](p *T) T {
	var zero T
	if p == nil {
		return zero
	}
	return *p
}
