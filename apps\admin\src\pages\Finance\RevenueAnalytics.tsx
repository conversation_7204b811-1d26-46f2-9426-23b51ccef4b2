/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:40:00
 * Modified: 2025-01-23 16:40:00
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Typography,
  Space,
  Button,
  Table,
  Tag,
  Progress,
  Alert,
  Tabs,
  List,
  Avatar,
  Tooltip,
} from 'antd';
import {
  DollarOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  CalendarOutlined,
  BarChartOutlined,
  PieChartOutlined,
  LineChartOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { Line, Column, Pie, Area } from '@ant-design/charts';
import { useQuery } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

interface RevenueData {
  date: string;
  revenue: number;
  subscriptions: number;
  oneTime: number;
  refunds: number;
  netRevenue: number;
}

interface RevenueForecast {
  date: string;
  predicted: number;
  confidence: {
    lower: number;
    upper: number;
  };
  actual?: number;
}

interface RevenueByPlan {
  plan: string;
  revenue: number;
  subscribers: number;
  growth: number;
  color: string;
}

interface TopCustomer {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  totalRevenue: number;
  lastPayment: string;
  plan: string;
}

const RevenueAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d');
  const [dateRange, setDateRange] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data queries
  const { data: revenueOverview } = useQuery({
    queryKey: ['revenue-overview', timeRange],
    queryFn: async () => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        totalRevenue: 156789.45,
        monthlyGrowth: 12.5,
        subscriptionRevenue: 134567.89,
        oneTimeRevenue: 22221.56,
        refunds: 3456.78,
        netRevenue: 153332.67,
        averageRevenuePerUser: 89.45,
        customerLifetimeValue: 1234.56,
        churnRate: 2.3,
        forecast: {
          nextMonth: 178934.56,
          confidence: 85
        }
      };
    }
  });

  const { data: revenueChart } = useQuery({
    queryKey: ['revenue-chart', timeRange],
    queryFn: async (): Promise<RevenueData[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      const data: RevenueData[] = [];
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      for (let i = 0; i < 30; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        data.push({
          date: date.toISOString().split('T')[0],
          revenue: Math.random() * 5000 + 3000,
          subscriptions: Math.random() * 4000 + 2500,
          oneTime: Math.random() * 1000 + 500,
          refunds: Math.random() * 200 + 50,
          netRevenue: Math.random() * 4800 + 2800
        });
      }
      return data;
    }
  });

  const { data: forecastData } = useQuery({
    queryKey: ['revenue-forecast'],
    queryFn: async (): Promise<RevenueForecast[]> => {
      await new Promise(resolve => setTimeout(resolve, 700));
      const data: RevenueForecast[] = [];
      const startDate = new Date();

      for (let i = 0; i < 30; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        const predicted = Math.random() * 6000 + 4000;
        data.push({
          date: date.toISOString().split('T')[0],
          predicted,
          confidence: {
            lower: predicted * 0.85,
            upper: predicted * 1.15
          },
          actual: i < 15 ? Math.random() * 5500 + 3500 : undefined
        });
      }
      return data;
    }
  });

  const { data: revenueByPlan } = useQuery({
    queryKey: ['revenue-by-plan'],
    queryFn: async (): Promise<RevenueByPlan[]> => {
      await new Promise(resolve => setTimeout(resolve, 500));
      return [
        { plan: 'Professional', revenue: 89456.78, subscribers: 1234, growth: 15.6, color: '#1890ff' },
        { plan: 'Enterprise', revenue: 67890.12, subscribers: 456, growth: 8.9, color: '#52c41a' },
        { plan: 'Basic', revenue: 34567.89, subscribers: 2345, growth: -2.1, color: '#faad14' },
        { plan: 'Premium', revenue: 23456.78, subscribers: 789, growth: 22.3, color: '#722ed1' }
      ];
    }
  });

  const { data: topCustomers } = useQuery({
    queryKey: ['top-customers'],
    queryFn: async (): Promise<TopCustomer[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return [
        {
          id: '1',
          name: 'TechCorp Inc.',
          email: '<EMAIL>',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=TechCorp',
          totalRevenue: 12345.67,
          lastPayment: '2025-01-20',
          plan: 'Enterprise'
        },
        {
          id: '2',
          name: 'StartupXYZ',
          email: '<EMAIL>',
          totalRevenue: 8901.23,
          lastPayment: '2025-01-19',
          plan: 'Professional'
        },
        {
          id: '3',
          name: 'Global Solutions',
          email: '<EMAIL>',
          totalRevenue: 7654.32,
          lastPayment: '2025-01-18',
          plan: 'Enterprise'
        }
      ];
    }
  });

  // Chart configurations
  const revenueLineConfig = {
    data: revenueChart || [],
    xField: 'date',
    yField: 'netRevenue',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 3,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Net Revenue',
        value: `$${datum.netRevenue.toFixed(2)}`
      })
    }
  };

  const revenueAreaConfig = {
    data: revenueChart || [],
    xField: 'date',
    yField: 'revenue',
    seriesField: 'type',
    color: ['#1890ff', '#52c41a', '#faad14'],
    areaStyle: { fillOpacity: 0.6 },
    isStack: true,
    tooltip: {
      formatter: (datum: any) => ({
        name: datum.type,
        value: `$${datum.revenue.toFixed(2)}`
      })
    }
  };

  const planPieConfig = {
    data: revenueByPlan || [],
    angleField: 'revenue',
    colorField: 'plan',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: ${value}'
    },
    interactions: [{ type: 'element-active' }]
  };

  const forecastConfig = {
    data: forecastData || [],
    xField: 'date',
    yField: 'predicted',
    smooth: true,
    color: '#722ed1',
    point: {
      size: 3,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Predicted Revenue',
        value: `$${datum.predicted.toFixed(2)}`
      })
    }
  };

  const topCustomersColumns: ColumnsType<TopCustomer> = [
    {
      title: 'Customer',
      key: 'customer',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.avatar} size="small" style={{ marginRight: '8px' }} />
          <div>
            <div><Text strong>{record.name}</Text></div>
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.email}</Text>
          </div>
        </div>
      )
    },
    {
      title: 'Plan',
      dataIndex: 'plan',
      key: 'plan',
      render: (plan: string) => <Tag color="blue">{plan}</Tag>
    },
    {
      title: 'Total Revenue',
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      render: (amount: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          ${amount.toFixed(2)}
        </Text>
      ),
      sorter: (a, b) => a.totalRevenue - b.totalRevenue
    },
    {
      title: 'Last Payment',
      dataIndex: 'lastPayment',
      key: 'lastPayment',
      render: (date: string) => new Date(date).toLocaleDateString()
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2}>
              <BarChartOutlined /> Revenue Analytics
            </Title>
            <Paragraph type="secondary">
              Comprehensive revenue analysis, forecasting, and insights.
            </Paragraph>
          </div>
          <Space>
            <Select
              value={timeRange}
              onChange={setTimeRange}
              style={{ width: 120 }}
            >
              <Option value="7d">Last 7 days</Option>
              <Option value="30d">Last 30 days</Option>
              <Option value="90d">Last 90 days</Option>
              <Option value="1y">Last year</Option>
            </Select>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
            />
            <Button icon={<DownloadOutlined />}>
              Export Report
            </Button>
          </Space>
        </div>
      </div>

      {/* Key Metrics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={revenueOverview?.totalRevenue || 0}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                <ArrowUpOutlined style={{ color: '#52c41a' }} /> 
                {revenueOverview?.monthlyGrowth || 0}% vs last month
              </Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Subscription Revenue"
              value={revenueOverview?.subscriptionRevenue || 0}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
            <Progress 
              percent={revenueOverview ? 
                (revenueOverview.subscriptionRevenue / revenueOverview.totalRevenue) * 100 : 0}
              showInfo={false}
              strokeColor="#1890ff"
              size="small"
              style={{ marginTop: '8px' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="ARPU"
              value={revenueOverview?.averageRevenuePerUser || 0}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">Average Revenue Per User</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Churn Rate"
              value={revenueOverview?.churnRate || 0}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#f5222d' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">Monthly churn rate</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Forecast Alert */}
      <Alert
        message="Revenue Forecast"
        description={
          <div>
            Next month's predicted revenue: <Text strong>${revenueOverview?.forecast.nextMonth.toFixed(2) || 0}</Text>
            {' '}with {revenueOverview?.forecast.confidence || 0}% confidence
            <Tooltip title="Based on historical data and current trends">
              <InfoCircleOutlined style={{ marginLeft: '8px', color: '#1890ff' }} />
            </Tooltip>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* Charts and Analytics */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Revenue Trends" key="overview">
          <Row gutter={16}>
            <Col span={16}>
              <Card title="Revenue Over Time" extra={<LineChartOutlined />}>
                <Line {...revenueLineConfig} height={300} />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Revenue by Plan" extra={<PieChartOutlined />}>
                <Pie {...planPieConfig} height={300} />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Forecasting" key="forecast">
          <Card title="Revenue Forecast" extra={<TrendingUpOutlined />}>
            <Line {...forecastConfig} height={400} />
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary">
                Forecast based on machine learning models analyzing historical patterns, 
                seasonal trends, and market conditions.
              </Text>
            </div>
          </Card>
        </TabPane>

        <TabPane tab="Plan Performance" key="plans">
          <Row gutter={16}>
            {revenueByPlan?.map((plan) => (
              <Col span={6} key={plan.plan}>
                <Card>
                  <div style={{ textAlign: 'center' }}>
                    <Title level={4} style={{ color: plan.color }}>
                      {plan.plan}
                    </Title>
                    <Statistic
                      value={plan.revenue}
                      prefix={<DollarOutlined />}
                      precision={2}
                      valueStyle={{ color: plan.color }}
                    />
                    <div style={{ marginTop: '12px' }}>
                      <Text type="secondary">{plan.subscribers} subscribers</Text>
                      <br />
                      <Text style={{ 
                        color: plan.growth > 0 ? '#52c41a' : '#f5222d',
                        fontWeight: 'bold'
                      }}>
                        {plan.growth > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                        {Math.abs(plan.growth)}%
                      </Text>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="Top Customers" key="customers">
          <Card title="Highest Revenue Customers">
            <Table
              columns={topCustomersColumns}
              dataSource={topCustomers || []}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default RevenueAnalytics; 