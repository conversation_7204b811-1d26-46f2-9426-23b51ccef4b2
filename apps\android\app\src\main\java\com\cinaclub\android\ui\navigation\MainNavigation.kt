/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.adaptive.navigationsuite.NavigationSuiteScaffold
import androidx.compose.material3.adaptive.navigationsuite.NavigationSuiteType
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.cinaclub.android.ui.calendar.CalendarScreen
import com.cinaclub.android.ui.chat.ChatListScreen
import com.cinaclub.android.ui.contacts.ContactsScreen
import com.cinaclub.android.ui.mail.MailScreen
import com.cinaclub.android.ui.profile.ProfileScreen
import com.cinaclub.android.ui.settings.SettingsScreen
import com.cinaclub.android.ui.settings.LanguageScreen
import com.cinaclub.android.ui.settings.PrivacyScreen
import com.cinaclub.android.ui.workbench.WorkbenchScreen

/**
 * Main navigation component that manages app navigation.
 * Uses adaptive navigation suite for responsive design across different screen sizes.
 * Implements CINA.CLUB's enterprise-style navigation following WeChat Work patterns.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainNavigation(
    modifier: Modifier = Modifier
) {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    // Determine navigation suite type based on screen size
    val navigationSuiteType = NavigationSuiteType.NavigationBar // TODO: Make adaptive

    NavigationSuiteScaffold(
        navigationSuiteItems = {
            bottomNavigationItems.forEach { item ->
                item(
                    icon = {
                        Icon(
                            imageVector = item.icon,
                            contentDescription = item.title
                        )
                    },
                    label = {
                        Text(
                            text = item.title,
                            style = MaterialTheme.typography.labelSmall
                        )
                    },
                    selected = currentRoute == item.route,
                    onClick = {
                        if (currentRoute != item.route) {
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.startDestinationId) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    },
                    colors = NavigationSuiteItemDefaults.colors(
                        selectedIconColor = Color(0xFF4A90E2),
                        selectedTextColor = Color(0xFF4A90E2),
                        unselectedIconColor = Color(0xFF888888),
                        unselectedTextColor = Color(0xFF888888)
                    )
                )
            }
        },
        modifier = modifier
    ) {
        NavHost(
            navController = navController,
            startDestination = NavigationDestination.Messages.route,
            modifier = Modifier.fillMaxSize()
        ) {
            // Main bottom navigation screens
            composable(NavigationDestination.Messages.route) {
                ChatListScreen(
                    onChatClick = { chatItem ->
                        // Navigate to individual chat
                        navController.navigate("chat/${chatItem.id}")
                    },
                    onSearchClick = {
                        navController.navigate("search")
                    },
                    onAddClick = {
                        navController.navigate("add_chat")
                    }
                )
            }

            composable(NavigationDestination.Mail.route) {
                MailScreen(
                    onSearchClick = {
                        navController.navigate("mail_search")
                    },
                    onComposeClick = {
                        navController.navigate("compose_mail")
                    },
                    onEmailClick = { email ->
                        navController.navigate("email/${email.id}")
                    }
                )
            }

            composable(NavigationDestination.Files.route) {
                // TODO: Implement files screen
                FilesPlaceholderScreen()
            }

            composable(NavigationDestination.Workbench.route) {
                WorkbenchScreen(
                    onToolClick = { tool ->
                        navController.navigate("tool/${tool.id}")
                    },
                    onManageEnterpriseClick = {
                        navController.navigate("enterprise_management")
                    },
                    onFindAppsClick = {
                        navController.navigate("app_store")
                    },
                    onSubmitRequestClick = {
                        navController.navigate("submit_request")
                    }
                )
            }

            composable(NavigationDestination.Contacts.route) {
                ContactsScreen(
                    onNavigateToSearch = {
                        navController.navigate("contact_search")
                    },
                    onDepartmentClick = { department ->
                        navController.navigate("department/${department.id}")
                    },
                    onContactClick = { contact ->
                        navController.navigate("contact/${contact.id}")
                    }
                )
            }

            // Profile and settings
            composable("profile") {
                ProfileScreen(
                    onNavigateToSettings = {
                        navController.navigate("settings")
                    },
                    onNavigateToAlbum = {
                        navController.navigate("album")
                    },
                    onNavigateToFavorites = {
                        navController.navigate("favorites")
                    },
                    onNavigateToFiles = {
                        navController.navigate("my_files")
                    },
                    onNavigateToWallet = {
                        navController.navigate("wallet")
                    },
                    onNavigateToMembership = {
                        navController.navigate("membership")
                    },
                    onNavigateToCustomization = {
                        navController.navigate("customization")
                    },
                    onNavigateToFloatWindow = {
                        navController.navigate("float_window")
                    }
                )
            }

            composable("settings") {
                SettingsScreen(
                    onNavigateToAccount = {
                        navController.navigate("account_security")
                    },
                    onNavigateToNotifications = {
                        navController.navigate("notifications")
                    },
                    onNavigateToGeneral = {
                        navController.navigate("general_settings")
                    },
                    onNavigateToLanguage = {
                        navController.navigate("language")
                    },
                    onNavigateToPrivacy = {
                        navController.navigate("privacy")
                    },
                    onNavigateToAbout = {
                        navController.navigate("about")
                    },
                    onLogout = {
                        // TODO: Implement logout flow
                        // Clear user session and navigate to login
                    }
                )
            }

            composable("language") {
                LanguageScreen(
                    onBackClick = { navController.popBackStack() },
                    onLanguageSelected = { language ->
                        // TODO: Apply language change
                        navController.popBackStack()
                    },
                    currentLanguage = "简体中文"
                )
            }

            composable("privacy") {
                PrivacyScreen(
                    onBackClick = { navController.popBackStack() }
                )
            }

            composable("calendar") {
                CalendarScreen(
                    onEventClick = { event ->
                        navController.navigate("event/${event.id}")
                    },
                    onDateClick = { date ->
                        navController.navigate("new_event?date=${date}")
                    },
                    onAddEventClick = {
                        navController.navigate("new_event")
                    }
                )
            }

            // Authentication screens (if not logged in)
            composable("login") {
                // TODO: Implement login screen
                LoginPlaceholderScreen(
                    onLoginSuccess = {
                        navController.navigate(NavigationDestination.Messages.route) {
                            popUpTo("login") { inclusive = true }
                        }
                    },
                    onNavigateToRegister = {
                        navController.navigate("register")
                    }
                )
            }

            composable("register") {
                // TODO: Implement register screen
                RegisterPlaceholderScreen(
                    onRegisterSuccess = {
                        navController.navigate(NavigationDestination.Messages.route) {
                            popUpTo("register") { inclusive = true }
                        }
                    },
                    onNavigateToLogin = {
                        navController.navigate("login")
                    }
                )
            }

            // Additional feature screens
            composable("chat/{chatId}") { backStackEntry ->
                val chatId = backStackEntry.arguments?.getString("chatId") ?: ""
                // TODO: Implement individual chat screen
                ChatDetailPlaceholderScreen(
                    chatId = chatId,
                    onBackClick = { navController.popBackStack() }
                )
            }

            composable("search") {
                // TODO: Implement global search screen
                SearchPlaceholderScreen(
                    onBackClick = { navController.popBackStack() }
                )
            }
        }
    }
}

/**
 * Bottom navigation destinations for main app features.
 */
private val bottomNavigationItems = listOf(
    NavigationDestination.Messages,
    NavigationDestination.Mail,
    NavigationDestination.Files,
    NavigationDestination.Workbench,
    NavigationDestination.Contacts
)

/**
 * Navigation destinations definition.
 */
sealed class NavigationDestination(
    val route: String,
    val title: String,
    val icon: ImageVector
) {
    object Messages : NavigationDestination("messages", "消息", Icons.Default.Chat)
    object Mail : NavigationDestination("mail", "邮件", Icons.Default.Email)
    object Files : NavigationDestination("files", "文档", Icons.Default.Description)
    object Workbench : NavigationDestination("workbench", "工作台", Icons.Default.Apps)
    object Contacts : NavigationDestination("contacts", "通讯录", Icons.Default.Contacts)
}

// Placeholder screens for features not yet implemented
@Composable
private fun FilesPlaceholderScreen() {
    Box(modifier = Modifier.fillMaxSize()) {
        Text(
            text = "文档功能正在开发中...",
            modifier = Modifier.align(androidx.compose.ui.Alignment.Center),
            style = MaterialTheme.typography.headlineSmall
        )
    }
}

@Composable
private fun LoginPlaceholderScreen(
    onLoginSuccess: () -> Unit,
    onNavigateToRegister: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "CINA.CLUB 登录",
            style = MaterialTheme.typography.headlineLarge
        )
        Spacer(modifier = Modifier.height(32.dp))
        Button(onClick = onLoginSuccess) {
            Text("模拟登录成功")
        }
        Spacer(modifier = Modifier.height(16.dp))
        TextButton(onClick = onNavigateToRegister) {
            Text("注册新账户")
        }
    }
}

@Composable
private fun RegisterPlaceholderScreen(
    onRegisterSuccess: () -> Unit,
    onNavigateToLogin: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "CINA.CLUB 注册",
            style = MaterialTheme.typography.headlineLarge
        )
        Spacer(modifier = Modifier.height(32.dp))
        Button(onClick = onRegisterSuccess) {
            Text("模拟注册成功")
        }
        Spacer(modifier = Modifier.height(16.dp))
        TextButton(onClick = onNavigateToLogin) {
            Text("已有账户？立即登录")
        }
    }
}

@Composable
private fun ChatDetailPlaceholderScreen(
    chatId: String,
    onBackClick: () -> Unit
) {
    Column(modifier = Modifier.fillMaxSize()) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(Icons.Default.ArrowBack, contentDescription = "返回")
            }
            Text(
                text = "聊天详情: $chatId",
                style = MaterialTheme.typography.headlineSmall
            )
        }
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = androidx.compose.ui.Alignment.Center
        ) {
            Text("聊天界面正在开发中...")
        }
    }
}

@Composable
private fun SearchPlaceholderScreen(
    onBackClick: () -> Unit
) {
    Column(modifier = Modifier.fillMaxSize()) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(Icons.Default.ArrowBack, contentDescription = "返回")
            }
            Text(
                text = "全局搜索",
                style = MaterialTheme.typography.headlineSmall
            )
        }
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = androidx.compose.ui.Alignment.Center
        ) {
            Text("搜索功能正在开发中...")
        }
    }
} 