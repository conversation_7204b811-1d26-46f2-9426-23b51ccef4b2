
---
### 文件2: `docs/architecture/L4_Service_Design_Patterns/02_repository_and_unit_of_work_pattern.md`

```markdown
# 微服务设计模式: 仓储与工作单元 (Repository & Unit of Work)

**文档状态**: 已采纳 (Accepted)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27

## 1. 概述

为了将业务逻辑与数据持久化逻辑彻底解耦，并有效地管理数据库事务，CINA.CLUB平台强制要求所有微服务在数据访问层采用**仓储模式 (Repository Pattern)** 和**工作单元模式 (Unit of Work Pattern)**。

---

## 2. 仓储模式 (Repository Pattern)

### 2.1 定义

仓储模式为领域模型提供了一个类似**内存中集合**的抽象。业务逻辑通过仓储接口来对领域对象进行CRUD操作，而无需关心这些对象是如何被存储和检索的（是SQL, NoSQL, 还是内存）。

### 2.2 职责

*   **封装数据查询**: 封装所有与数据库交互的细节。
*   **模型转换**: 负责在**领域模型 (`core/models`)** 和**持久化模型 (`adapter/repository/model.go`)** 之间进行转换。
*   **接口定义**: 仓储的接口在`application/port`中定义，实现则在`adapter/repository`中。

### 2.3 示例

```go
// application/port/user_repo.go
package port

type UserRepository interface {
    GetByID(ctx context.Context, tx pgx.Tx, id uuid.UUID) (*models.User, error)
    Create(ctx context.Context, tx pgx.Tx, user *models.User) error
}

// adapter/repository/user_repo.go
package repository

type userRepo struct {
    db *pgxpool.Pool
}

func (r *userRepo) GetByID(ctx context.Context, tx pgx.Tx, id uuid.UUID) (*models.User, error) {
    // 优先使用传入的事务tx，如果tx为nil，则使用r.db
    q := getQueryer(tx, r.db) 
    
    // ... 执行SQL查询，获取持久化模型 ...
    // ... 将持久化模型转换为领域模型并返回 ...
}
```
**关键点**: 所有仓储方法都应接收一个**事务对象 (`pgx.Tx`)** 作为参数。这使得多个仓储操作可以在同一个事务中被编排。

---

## 3. 工作单元模式 (Unit of Work Pattern)

### 3.1 定义

工作单元模式维护一个受业务事务影响的对象列表，并协调变化的写入和并发问题的解决。在我们的实践中，它主要体现为**管理数据库事务的生命周期**。

### 3.2 职责

*   **开启事务**: 在一个业务操作开始时，开启一个数据库事务。
*   **传递事务**: 将同一个事务对象传递给该业务操作中涉及的所有仓储方法。
*   **提交或回滚**: 根据业务操作的最终结果，提交或回滚该事务。

### 3.3 实现方式

推荐在**应用服务层 (`application/service`)** 实现工作单元逻辑。

```go
// application/service/user_service.go

type userService struct {
    db   *pgxpool.Pool
    repo port.UserRepository
    // ... 其他仓储
}

// DoComplexOperation 是一个需要事务的业务方法
func (s *userService) DoComplexOperation(ctx context.Context, userID, otherID uuid.UUID) error {
    // 1. 开启工作单元（事务）
    tx, err := s.db.Begin(ctx)
    if err != nil {
        return app_errors.Wrap(err, app_errors.Internal, "failed to begin transaction")
    }
    // 2. 保证事务最终被提交或回滚
    defer tx.Rollback(ctx) // 如果后面commit成功，rollback是无操作的

    // 3. 将事务(tx)传递给所有仓储操作
    user, err := s.repo.GetByID(ctx, tx, userID)
    if err != nil {
        return err // 错误发生，defer中的Rollback会执行
    }

    // ... 对user进行一些操作 ...
    err = s.repo.Update(ctx, tx, user)
    if err != nil {
        return err // 错误发生，defer中的Rollback会执行
    }
    
    // ... 其他仓储操作 ...

    // 4. 所有操作成功，提交事务
    return tx.Commit(ctx)
}
```

**结论**: 通过结合使用仓储和工作单元模式，我们实现了业务逻辑与数据访问的完全解耦，并确保了复杂业务操作的数据一致性。
```
