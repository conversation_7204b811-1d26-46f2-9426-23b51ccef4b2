/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package http

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/sirupsen/logrus"

	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// Middleware holds all HTTP middleware dependencies
type Middleware struct {
	bffService  port.BFFService
	auditLogger port.AuditLogger
	logger      *logrus.Logger
}

// NewMiddleware creates a new middleware instance
func NewMiddleware(bffService port.BFFService, auditLogger port.AuditLogger, logger *logrus.Logger) *Middleware {
	return &Middleware{
		bffService:  bffService,
		auditLogger: auditLogger,
		logger:      logger,
	}
}

// RequestID adds a unique request ID to each request
func (m *Middleware) RequestID(next http.Handler) http.Handler {
	return middleware.RequestID(next)
}

// Logging logs HTTP requests and responses
func (m *Middleware) Logging(next http.Handler) http.Handler {
	return middleware.RequestLogger(&middleware.DefaultLogFormatter{
		Logger:  m.logger,
		NoColor: true,
	})(next)
}

// Recovery recovers from panics and returns a proper error response
func (m *Middleware) Recovery(next http.Handler) http.Handler {
	return middleware.Recoverer(next)
}

// CORS handles Cross-Origin Resource Sharing
func (m *Middleware) CORS(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers for admin frontend
		w.Header().Set("Access-Control-Allow-Origin", "https://admin.cina.club")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Access-Control-Max-Age", "86400")

		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// RateLimit provides basic rate limiting
func (m *Middleware) RateLimit(next http.Handler) http.Handler {
	return middleware.Throttle(100)(next) // Allow 100 concurrent requests
}

// SessionAuth validates admin session from cookie
func (m *Middleware) SessionAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Extract session ID from cookie
		cookie, err := r.Cookie("admin_session_id")
		if err != nil {
			m.writeErrorResponse(w, http.StatusUnauthorized, "Missing session cookie")
			return
		}

		// Validate session
		session, err := m.bffService.ValidateSession(r.Context(), cookie.Value)
		if err != nil {
			m.logger.WithError(err).Debug("Session validation failed")
			m.writeErrorResponse(w, http.StatusUnauthorized, "Invalid session")
			return
		}

		// Add session to context
		ctx := context.WithValue(r.Context(), "session", session)
		ctx = context.WithValue(ctx, "actor_info", &port.ActorInfo{
			EmployeeID: session.EmployeeID,
			Email:      session.Email,
			Roles:      session.Roles,
			IPAddress:  getClientIP(r),
			UserAgent:  r.UserAgent(),
		})

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AuditLog logs all write operations for audit purposes
func (m *Middleware) AuditLog(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Only audit write operations
		if !isWriteOperation(r.Method) {
			next.ServeHTTP(w, r)
			return
		}

		// Get actor info from context
		actorInfo, ok := r.Context().Value("actor_info").(*port.ActorInfo)
		if !ok {
			m.logger.Error("Actor info not found in context for audit logging")
			next.ServeHTTP(w, r)
			return
		}

		// Read and store request body
		var requestBody map[string]interface{}
		if r.Body != nil {
			bodyBytes, err := io.ReadAll(r.Body)
			if err == nil && len(bodyBytes) > 0 {
				// Restore body for next handler
				r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				// Parse JSON body for audit log
				if err := json.Unmarshal(bodyBytes, &requestBody); err != nil {
					// If not JSON, store as raw string
					requestBody = map[string]interface{}{
						"raw_body": string(bodyBytes),
					}
				}

				// Sanitize sensitive data
				requestBody = sanitizeRequestBody(requestBody)
			}
		}

		// Create response writer wrapper to capture response details
		responseWrapper := &responseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
			startTime:      time.Now(),
		}

		// Process request
		next.ServeHTTP(responseWrapper, r)

		// Create audit log entry
		responseTime := time.Since(responseWrapper.startTime).Milliseconds()
		success := responseWrapper.statusCode >= 200 && responseWrapper.statusCode < 400

		// Create audit log entry using the LogEntry method
		auditEntry := model.NewAuditLogEntry(actorInfo.EmployeeID, actorInfo.Email, actorInfo.IPAddress)
		auditEntry.SetRequest(r.Method, r.URL.Path, requestBody)
		auditEntry.SetResponse(responseWrapper.statusCode, "", success)
		auditEntry.SetResponseTime(responseTime)
		auditEntry.SetUserAgent(actorInfo.UserAgent)
		auditEntry.AddTag("http_request")

		if err := m.auditLogger.LogEntry(r.Context(), auditEntry); err != nil {
			m.logger.WithError(err).Error("Failed to log audit entry")
		}
	})
}

// RequireRole creates a middleware that requires specific roles
func (m *Middleware) RequireRole(requiredRole string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			session, ok := r.Context().Value("session").(*model.AdminSession)
			if !ok {
				m.writeErrorResponse(w, http.StatusUnauthorized, "Session not found")
				return
			}

			// Check if user has required role
			if !hasRole(session.Roles, requiredRole) {
				m.logger.WithFields(logrus.Fields{
					"employee_id":   session.EmployeeID,
					"required_role": requiredRole,
					"user_roles":    session.Roles,
				}).Warn("Access denied due to insufficient permissions")

				m.writeErrorResponse(w, http.StatusForbidden, "Insufficient permissions")
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// RequireAnyRole creates a middleware that requires any of the specified roles
func (m *Middleware) RequireAnyRole(roles ...string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			session, ok := r.Context().Value("session").(*model.AdminSession)
			if !ok {
				m.writeErrorResponse(w, http.StatusUnauthorized, "Session not found")
				return
			}

			// Check if user has any of the required roles
			hasAnyRole := false
			for _, role := range roles {
				if hasRole(session.Roles, role) {
					hasAnyRole = true
					break
				}
			}

			if !hasAnyRole {
				m.logger.WithFields(logrus.Fields{
					"employee_id":    session.EmployeeID,
					"required_roles": roles,
					"user_roles":     session.Roles,
				}).Warn("Access denied due to insufficient permissions")

				m.writeErrorResponse(w, http.StatusForbidden, "Insufficient permissions")
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// responseWriter wraps http.ResponseWriter to capture response details
type responseWriter struct {
	http.ResponseWriter
	statusCode int
	startTime  time.Time
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// Helper functions

// isWriteOperation checks if the HTTP method is a write operation
func isWriteOperation(method string) bool {
	writeOps := []string{"POST", "PUT", "PATCH", "DELETE"}
	for _, op := range writeOps {
		if method == op {
			return true
		}
	}
	return false
}

// hasRole checks if the user has a specific role
func hasRole(userRoles []string, requiredRole string) bool {
	for _, role := range userRoles {
		if role == requiredRole {
			return true
		}
	}
	return false
}

// getClientIP extracts the client IP address from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header first
	forwarded := r.Header.Get("X-Forwarded-For")
	if forwarded != "" {
		// Take the first IP if multiple are present
		ips := strings.Split(forwarded, ",")
		return strings.TrimSpace(ips[0])
	}

	// Check X-Real-IP header
	realIP := r.Header.Get("X-Real-IP")
	if realIP != "" {
		return realIP
	}

	// Fall back to RemoteAddr
	ip := r.RemoteAddr
	if lastColon := strings.LastIndex(ip, ":"); lastColon != -1 {
		ip = ip[:lastColon]
	}
	return ip
}

// sanitizeRequestBody removes sensitive information from request body for audit logging
func sanitizeRequestBody(body map[string]interface{}) map[string]interface{} {
	sensitiveFields := []string{
		"password", "secret", "token", "key", "credential",
		"authorization", "auth", "private", "confidential",
	}

	sanitized := make(map[string]interface{})
	for k, v := range body {
		key := strings.ToLower(k)
		isSensitive := false

		for _, field := range sensitiveFields {
			if strings.Contains(key, field) {
				isSensitive = true
				break
			}
		}

		if isSensitive {
			sanitized[k] = "[REDACTED]"
		} else {
			sanitized[k] = v
		}
	}

	return sanitized
}

// writeErrorResponse writes a standardized error response
func (m *Middleware) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"error":     true,
		"message":   message,
		"status":    statusCode,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		m.logger.WithError(err).Error("Failed to write error response")
	}
}
