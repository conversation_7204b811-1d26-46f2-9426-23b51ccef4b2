// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-15 12:00:00
// Modified: 2025-01-15 12:00:00

package cache

import (
	"context"
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	"cina.club/services/analytics-service/internal/application/port"
)

// RedisCache Redis cache implementation
type RedisCache struct {
	client *redis.Client
}

// CacheInterface Cache interface
type CacheInterface interface {
	port.CacheRepository
	// Close Close connection
	Close() error
}

// Ensure RedisCache implements CacheInterface
var _ CacheInterface = (*RedisCache)(nil)

// NewRedisCache Create Redis cache
func NewRedisCache() (*RedisCache, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:         viper.GetString("redis.addr"),
		Password:     viper.GetString("redis.password"),
		DB:           viper.GetInt("redis.db"),
		PoolSize:     viper.GetInt("redis.pool_size"),
		MinIdleConns: viper.GetInt("redis.min_idle_conns"),
		DialTimeout:  viper.GetDuration("redis.dial_timeout"),
		ReadTimeout:  viper.GetDuration("redis.read_timeout"),
		WriteTimeout: viper.GetDuration("redis.write_timeout"),
		PoolTimeout:  viper.GetDuration("redis.pool_timeout"),
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, err
	}

	logrus.Info("Connected to Redis successfully")
	return &RedisCache{client: rdb}, nil
}

// Set Set cache
func (r *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	// Serialize value
	data, err := json.Marshal(value)
	if err != nil {
		logrus.WithError(err).WithField("key", key).Error("Failed to marshal cache value")
		return err
	}

	err = r.client.Set(ctx, key, data, expiration).Err()
	if err != nil {
		logrus.WithError(err).WithField("key", key).Error("Failed to set cache")
		return err
	}

	logrus.WithFields(logrus.Fields{
		"key":        key,
		"expiration": expiration.String(),
	}).Debug("Cache set successfully")
	return nil
}

// Get Get cache
func (r *RedisCache) Get(ctx context.Context, key string) (string, error) {
	val, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			logrus.WithField("key", key).Debug("Cache miss")
			return "", err
		}
		logrus.WithError(err).WithField("key", key).Error("Failed to get cache")
		return "", err
	}

	logrus.WithField("key", key).Debug("Cache hit")
	return val, nil
}

// GetObject Get object cache
func (r *RedisCache) GetObject(ctx context.Context, key string, dest interface{}) error {
	val, err := r.Get(ctx, key)
	if err != nil {
		return err
	}

	err = json.Unmarshal([]byte(val), dest)
	if err != nil {
		logrus.WithError(err).WithField("key", key).Error("Failed to unmarshal cache value")
		return err
	}

	return nil
}

// Delete Delete cache
func (r *RedisCache) Delete(ctx context.Context, key string) error {
	err := r.client.Del(ctx, key).Err()
	if err != nil {
		logrus.WithError(err).WithField("key", key).Error("Failed to delete cache")
		return err
	}

	logrus.WithField("key", key).Debug("Cache deleted successfully")
	return nil
}

// Exists Check if cache exists
func (r *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	count, err := r.client.Exists(ctx, key).Result()
	if err != nil {
		logrus.WithError(err).WithField("key", key).Error("Failed to check cache existence")
		return false, err
	}

	return count > 0, nil
}

// SetExpire Set cache expiration time
func (r *RedisCache) SetExpire(ctx context.Context, key string, expiration time.Duration) error {
	err := r.client.Expire(ctx, key, expiration).Err()
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"key":        key,
			"expiration": expiration.String(),
		}).Error("Failed to set cache expiration")
		return err
	}

	logrus.WithFields(logrus.Fields{
		"key":        key,
		"expiration": expiration.String(),
	}).Debug("Cache expiration set successfully")
	return nil
}

// GetKeys Get matching keys
func (r *RedisCache) GetKeys(ctx context.Context, pattern string) ([]string, error) {
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		logrus.WithError(err).WithField("pattern", pattern).Error("Failed to get cache keys")
		return nil, err
	}

	logrus.WithFields(logrus.Fields{
		"pattern": pattern,
		"count":   len(keys),
	}).Debug("Retrieved cache keys")
	return keys, nil
}

// Ping Check connection
func (r *RedisCache) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close Close connection
func (r *RedisCache) Close() error {
	err := r.client.Close()
	if err != nil {
		logrus.WithError(err).Error("Failed to close Redis connection")
		return err
	}

	logrus.Info("Redis connection closed")
	return nil
}

// FlushAll Clear all cache (for testing only)
func (r *RedisCache) FlushAll(ctx context.Context) error {
	err := r.client.FlushAll(ctx).Err()
	if err != nil {
		logrus.WithError(err).Error("Failed to flush all cache")
		return err
	}

	logrus.Warn("All cache flushed")
	return nil
}

// GetCacheStats Get cache statistics
func (r *RedisCache) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	info, err := r.client.Info(ctx, "memory", "stats").Result()
	if err != nil {
		logrus.WithError(err).Error("Failed to get cache stats")
		return nil, err
	}

	dbSize, err := r.client.DBSize(ctx).Result()
	if err != nil {
		logrus.WithError(err).Error("Failed to get DB size")
		return nil, err
	}

	stats := map[string]interface{}{
		"db_size": dbSize,
		"info":    info,
	}

	return stats, nil
}
