好的，遵照您的指示。我将为您生成一份专门针对 **`news-crawler-service`** 的、极致细化的、生产级**内部架构设计文档**，并特别强调**使用Go语言作为核心技术栈**的实现细节。

这份文档将基于我们刚生成的`news-crawler-service-srs.md` (Go版)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将特别关注**基于Go并发模型的分布式任务调度与执行、多信源适配器模式、以及作为一个高并发I/O密集型服务的可靠性与“礼貌性”设计**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `news-crawler-service` 内部架构设计 (Go版)

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `news-crawler-service-srs.md` (v1.1 Go-Centric)
**核心架构**: 分布式调度器 + Goroutine Worker池 + 适配器模式

## 1. 概述

`news-crawler-service` 是CINA.CLUB实时信息流的“**数据触角**”。它是一个**高并发、I/O密集型**的后台服务。其架构设计的核心目标是：
1.  **高性能与高并发**: 利用Go的并发原语（goroutine, channel）来同时处理数千个信源的采集任务。
2.  **分布式与可扩展性**: 服务必须能够以多个实例的形式部署，并通过协调机制（如分布式锁）来共同分担采集任务，实现水平扩展。
3.  **可维护性与可扩展性**: 添加对一种新类型信源（如未来支持FTP）的支持，应该是一个清晰、低耦合的过程。
4.  **可靠性与容错**: 任务执行必须能从瞬时的网络或目标服务器错误中恢复。
5.  **资源控制与礼貌性**: 必须能精确控制并发量和对同一主机的请求频率，避免被封禁或对目标服务器造成过大压力。

本架构设计通过采用**Go语言**，并构建一个**“分布式调度器(Scheduler) + 并发Worker池(Worker Pool)”**的模型，结合**适配器模式**来处理异构信源，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (分布式调度与并发执行)

```mermaid
graph TD
    subgraph "管理与配置"
        A[Admin API Server (gRPC)]
        DB[(PostgreSQL)]
    end

    subgraph "分布式协调"
        style "分布式协调" fill:#f3e5f5
        Lock[Redis (for Distributed Lock)]
    end

    subgraph "Crawler Service Cluster (Multiple Pods)"
        style "Crawler Service Cluster (Multiple Pods)" fill:#e0f7fa
        
        subgraph "Node 1"
            Scheduler1[Scheduler Goroutine]
            WorkerPool1[Goroutine Worker Pool]
        end
        
        subgraph "Node 2"
            Scheduler2[Scheduler Goroutine]
            WorkerPool2[Goroutine Worker Pool]
        end
        
        subgraph "Node N"
            SchedulerN[Scheduler Goroutine]
            WorkerPoolN[Goroutine Worker Pool]
        end
    end

    subgraph "输出"
        style "输出" fill:#e8f5e9
        Kafka[(Kafka<br/>raw-news-stream Topic)]
    end

    A -- "CRUD Source Configs" --> DB

    Scheduler1 & Scheduler2 & SchedulerN -- "1. Try to acquire global lock" --> Lock
    
    subgraph "只有一个节点能获取到锁"
        Scheduler1 -- "2. Lock Acquired!" --> Scheduler1
        Scheduler1 -- "3. Scan DB for due sources" --> DB
        Scheduler1 -- "4. Dispatch CrawlJobs to" --> WorkerPool1 & WorkerPool2 & WorkerPoolN
    end
    
    note over WorkerPool1 & WorkerPool2 & WorkerPoolN
        Workers in all nodes execute tasks concurrently.
    end
    
    WorkerPool1 -- "Fetch/Parse/Publish" --> Kafka
```

### 2.2 最终目录结构 (`services/news-crawler-service/`)

```
news-crawler-service/
├── cmd/server/
│   └── main.go                 # ✨ 统一入口: 启动API, 调度器, Worker池 ✨
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_lock.go   # 分布式锁实现
│   │   ├── event/
│   │   │   └── kafka_producer.go
│   │   ├── grpc/
│   │   │   └── handler.go      # Admin API Handler
│   │   ├── parser/             # ✨ 解析器适配器 ✨
│   │   │   ├── interface.go    # 定义Parser接口
│   │   │   ├── rss_parser.go
│   │   │   └── webpage_parser.go
│   │   ├── repository/
│   │   │   └── postgres_repo.go
│   │   └── scraper/              # ✨ 抓取器适配器 ✨
│   │       ├── interface.go    # 定义Scraper接口
│   │       └── http_scraper.go
│   ├── application/
│   │   ├── port/
│   │   │   └── ...
│   │   └── service/
│   │       └── crawl_service.go # 核心应用服务, 编排单次抓取流程
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── scheduler/
│           ├── scheduler.go      # ✨ 分布式调度器 ✨
│           └── worker_pool.go    # ✨ Goroutine Worker池 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/scheduler/` - 调度与执行核心

这是本服务并发模型和分布式协调的核心。

*   **`scheduler.go`**: **分布式调度器**。
    *   **`Scheduler` struct**: 注入分布式锁客户端(`DistributedLock`)和任务分发channel (`chan CrawlJob`)。
    *   **`Run()` method**:
        1.  在一个`time.Ticker`（如每分钟）驱动的循环中运行。
        2.  **核心逻辑**: 调用`lock.TryAcquire("crawler-scheduler-lock", ttl)`尝试获取全局调度锁。
        3.  **如果获取锁成功**:
            a. 从数据库中查询出所有在当前时间窗口内需要被调度的信源。
            b. 为每个信源创建一个`CrawlJob`对象。
            c. 将`CrawlJob`发送到任务channel中。
        4.  **如果获取锁失败**: 说明其他实例正在调度，本次循环直接跳过。
*   **`worker_pool.go`**: **并发Worker池**。
    *   **`WorkerPool` struct**: 注入任务channel (`<-chan CrawlJob`)和`CrawlService`。
    *   **`Start(numWorkers int)` method**:
        1.  启动`numWorkers`个**goroutine**。
        2.  每个goroutine都在一个循环中，从任务channel中接收`CrawlJob`。
        3.  接收到任务后，调用`crawlService.ProcessJob(ctx, job)`。

### 3.2 `application/` - 应用层 (单次抓取流程的编排)

*   **`application/service/crawl_service.go`**:
    *   **`CrawlService`**: 注入`Repository`, `Scraper`, `Parser`, `Producer`等所有依赖。
    *   **`ProcessJob(ctx, job)` method**: **这是单次抓取任务的完整流程**。
        1.  **获取抓取器(Scraper)**: 根据`job.SourceType`从工厂获取对应的`Scraper`适配器。
        2.  **抓取**: `rawData, err := scraper.Scrape(ctx, job.URL, job.AuthConfig)`。
        3.  **获取解析器(Parser)**: 根据`job.SourceType`从工厂获取对应的`Parser`适配器。
        4.  **解析**: `parsedItems, err := parser.Parse(ctx, rawData, job.ParserConfig)`。
        5.  **增量过滤**:
            a. 提取所有`parsedItems`的唯一ID（如URL）。
            b. 调用`repository.FilterNewItemIDs(ctx, itemIDs)`，返回一个只包含新ID的列表。
        6.  **标准化与发布**:
            a. 遍历新item。
            b. 将其转换为`RawNewsEvent` Protobuf消息。
            c. 调用`producer.Publish(ctx, event)`。
        7.  **更新已处理记录**: 调用`repository.MarkItemsAsProcessed(ctx, newItemIDs)`。

### 3.3 `adapter/` - 适配层 (The Bridge to the World)

*   **`adapter/scraper/`**: **抓取器适配器，负责“获取”数据**。
    *   `interface.go`: 定义`Scraper`接口，`Scrape(...) ([]byte, error)`。
    *   `http_scraper.go`:
        *   实现`Scraper`接口。
        *   内部使用一个经过配置的`http.Client`，该客户端**必须**包含：
            *   合理的超时设置。
            *   自定义的`User-Agent`。
            *   一个**基于域名的速率限制器**（使用`golang.org/x/time/rate`），防止对同一主机请求过快。
            *   (可选) 代理支持。
*   **`adapter/parser/`**: **解析器适配器，负责“理解”数据**。
    *   `interface.go`: 定义`Parser`接口，`Parse(...) ([]ParsedItem, error)`。
    *   `rss_parser.go`: 使用`mmcdole/gofeed`库实现。
    *   `webpage_parser.go`: 使用`goquery`库实现，根据传入的CSS选择器进行解析。
*   **`adapter/cache/redis_lock.go`**:
    *   实现`DistributedLock`接口，使用Redis的`SETNX`命令来实现分布式锁。
*   **`adapter/repository/postgres_repo.go`**:
    *   **`FilterNewItemIDs`**: 使用`WHERE hash NOT IN (...)`或更高效的`JOIN`来过滤已存在的ID。
    *   **`MarkItemsAsProcessed`**: 使用`INSERT ... ON CONFLICT DO NOTHING`来批量、幂等地插入已处理的ID哈希。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的、基于Go的`news-crawler-service`：
1.  **Go并发模型的充分利用**:
    *   通过**调度器+有界Worker池**的模型，将任务的“发现”和“执行”解耦，既能充分利用并发优势，又能有效控制对外部资源的请求压力。
2.  **分布式协调**: 采用**基于Redis的分布式锁**，确保了多个服务实例可以协同工作而不会产生任务冲突，实现了简单而可靠的水平扩展。
3.  **双重适配器模式**:
    *   **`Scraper`适配器**: 封装了获取原始数据的不同方式（HTTP, API）。
    *   **`Parser`适配器**: 封装了解析不同数据格式的逻辑（XML, HTML）。
    *   这种设计使得添加对新协议（如FTP）或新格式（如JSON Feed）的支持，只需要实现新的适配器即可，代码结构清晰，扩展性强。
4.  **可靠性与礼貌性内建**: 在HTTP抓取器中内置了超时、重试和基于域名的速率限制，确保了服务在面对不稳定的外部世界时依然健壮，并做一个“负责任的”网络公民。

这种架构确保了`news-crawler-service`能够以一种**高性能、高并发、高可靠且易于维护**的方式，为CINA.CLUB平台提供源源不断的数据血液。