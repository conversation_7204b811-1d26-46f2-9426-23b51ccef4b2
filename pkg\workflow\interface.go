/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package workflow

import (
	"context"
)

// NodeExecutor defines the interface that all workflow node implementations must satisfy.
// Each node type (e.g., "api_call", "send_notification", "llm_prompt") must implement this interface.
type NodeExecutor interface {
	// Execute performs the actual work of the node.
	// It receives a context for cancellation/timeout and a map of input parameters.
	// It returns a map of output values that can be used by subsequent nodes.
	//
	// Implementations should:
	// - Respect the context for cancellation and timeouts
	// - Return meaningful output data in the outputs map
	// - Return descriptive errors for debugging
	// - Be thread-safe if the executor will be used concurrently
	Execute(ctx context.Context, inputs map[string]interface{}) (outputs map[string]interface{}, err error)
}

// ExecutionListener provides hooks for monitoring workflow execution progress.
// This is useful for logging, metrics, debugging, and progress tracking.
type ExecutionListener interface {
	// OnWorkflowStart is called when workflow execution begins
	OnWorkflowStart(ctx context.Context, workflow *Workflow, state *ExecutionState)

	// OnWorkflowComplete is called when workflow execution completes successfully
	OnWorkflowComplete(ctx context.Context, workflow *Workflow, state *ExecutionState)

	// OnWorkflowError is called when workflow execution fails
	OnWorkflowError(ctx context.Context, workflow *Workflow, state *ExecutionState, err error)

	// OnNodeStart is called before a node begins execution
	OnNodeStart(ctx context.Context, node *Node, inputs map[string]interface{})

	// OnNodeComplete is called when a node completes successfully
	OnNodeComplete(ctx context.Context, node *Node, result *NodeResult)

	// OnNodeError is called when a node execution fails
	OnNodeError(ctx context.Context, node *Node, err error, attempt int)

	// OnNodeSkip is called when a node is skipped due to conditions
	OnNodeSkip(ctx context.Context, node *Node, reason string)
}

// NoOpListener is a default implementation of ExecutionListener that does nothing.
// This can be used as a base for custom listeners that only need to implement some methods.
type NoOpListener struct{}

func (n *NoOpListener) OnWorkflowStart(ctx context.Context, workflow *Workflow, state *ExecutionState) {
}
func (n *NoOpListener) OnWorkflowComplete(ctx context.Context, workflow *Workflow, state *ExecutionState) {
}
func (n *NoOpListener) OnWorkflowError(ctx context.Context, workflow *Workflow, state *ExecutionState, err error) {
}
func (n *NoOpListener) OnNodeStart(ctx context.Context, node *Node, inputs map[string]interface{}) {}
func (n *NoOpListener) OnNodeComplete(ctx context.Context, node *Node, result *NodeResult)         {}
func (n *NoOpListener) OnNodeError(ctx context.Context, node *Node, err error, attempt int)        {}
func (n *NoOpListener) OnNodeSkip(ctx context.Context, node *Node, reason string)                  {}

// ConditionEvaluator defines the interface for evaluating edge conditions.
// This allows for pluggable condition evaluation strategies.
type ConditionEvaluator interface {
	// Evaluate determines whether an edge condition is satisfied.
	// It receives the condition definition and the outputs from the source node.
	// Returns true if the condition is met and the edge should be followed.
	Evaluate(condition *EdgeCondition, nodeOutputs map[string]interface{}) (bool, error)
}

// ExpressionEvaluator defines the interface for evaluating template expressions.
// This allows for pluggable expression evaluation strategies.
type ExpressionEvaluator interface {
	// Evaluate processes a template expression and returns the resolved value.
	// It receives the expression string and the current execution state.
	// Returns the resolved value or an error if evaluation fails.
	Evaluate(expression string, state *ExecutionState) (interface{}, error)
}

// WorkflowEngine defines the high-level interface for workflow execution.
// This is the main interface that upper services will interact with.
type WorkflowEngine interface {
	// RegisterNode registers a node executor for a specific node type.
	// This allows the engine to know how to execute nodes of that type.
	RegisterNode(nodeType string, executor NodeExecutor)

	// RegisterListener adds an execution listener for monitoring workflow progress.
	RegisterListener(listener ExecutionListener)

	// Execute runs a workflow with the given initial state.
	// Returns the final execution state or an error if execution fails.
	Execute(ctx context.Context, workflow *Workflow, initialState *ExecutionState) (*ExecutionState, error)

	// Validate checks if a workflow is valid and can be executed.
	// This includes DAG validation, node type validation, etc.
	Validate(workflow *Workflow) error

	// GetExecutionOrder returns the order in which nodes would be executed.
	// This is useful for debugging and visualization.
	GetExecutionOrder(workflow *Workflow) ([]string, error)
}

// NodeRegistry manages the registration and lookup of node executors.
type NodeRegistry interface {
	// Register associates a node type with its executor implementation.
	Register(nodeType string, executor NodeExecutor)

	// Get retrieves the executor for a given node type.
	// Returns nil if no executor is registered for the type.
	Get(nodeType string) NodeExecutor

	// List returns all registered node types.
	List() []string

	// Unregister removes a node type registration.
	Unregister(nodeType string)
}

// ExecutionContext provides additional context and utilities during workflow execution.
// This can be used to pass additional data or services to node executors.
type ExecutionContext struct {
	// WorkflowID is the ID of the currently executing workflow
	WorkflowID string

	// ExecutionID is a unique identifier for this execution instance
	ExecutionID string

	// StartTime is when the workflow execution started
	StartTime string

	// Variables are global variables available to all nodes
	Variables map[string]interface{}

	// Services are injected services that nodes can use
	Services map[string]interface{}

	// Logger is a logger instance for this execution
	Logger interface{}

	// Metadata can store additional execution-specific information
	Metadata map[string]interface{}
}

// Resumable indicates that a workflow can be paused and resumed.
// This is useful for long-running workflows or workflows that wait for external events.
type Resumable interface {
	// Pause interrupts the workflow execution at a safe point.
	// Returns the current state that can be used to resume later.
	Pause() (*ExecutionState, error)

	// Resume continues workflow execution from a saved state.
	Resume(ctx context.Context, state *ExecutionState) (*ExecutionState, error)

	// CanResume checks if the workflow can be resumed from the given state.
	CanResume(state *ExecutionState) bool
}
