/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

/**
 * 聊天Tab内容组件
 */
@Component
export struct ChatTabContent {
  @State private conversations: ConversationItem[] = [];

  aboutToAppear(): void {
    this.loadConversations();
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Text('聊天')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
        
        Blank()
        
        Button('新建')
          .fontSize(14)
          .height(32)
          .backgroundColor('#007AFF')
          .borderRadius(16)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 10, bottom: 10 })
      
      // 聊天列表
      if (this.conversations.length > 0) {
        List() {
          ForEach(this.conversations, (item: ConversationItem) => {
            ListItem() {
              this.ConversationItemView(item)
            }
          })
        }
        .width('100%')
        .layoutWeight(1)
      } else {
        this.EmptyView()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
  }

  @Builder
  ConversationItemView(item: ConversationItem) {
    Row() {
      // 头像
      Row() {
        Text(item.name.charAt(0))
          .fontSize(16)
          .fontColor('#FFFFFF')
      }
      .width(48)
      .height(48)
      .backgroundColor(item.isAI ? '#FF6B35' : '#007AFF')
      .borderRadius(24)
      .justifyContent(FlexAlign.Center)
      .margin({ right: 12 })
      
      // 内容区域
      Column() {
        Row() {
          Text(item.name)
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor('#000000')
          
          Blank()
          
          Text(item.timeAgo)
            .fontSize(12)
            .fontColor('#999999')
        }
        .width('100%')
        .margin({ bottom: 4 })
        
        Text(item.lastMessage)
          .fontSize(14)
          .fontColor('#666666')
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .onClick(() => {
      // 处理点击事件
    })
  }

  @Builder
  EmptyView() {
    Column() {
      Text('💬')
        .fontSize(48)
        .margin({ bottom: 16 })
      
      Text('暂无聊天记录')
        .fontSize(16)
        .fontColor('#666666')
        .margin({ bottom: 8 })
      
      Text('点击"新建"开始对话')
        .fontSize(14)
        .fontColor('#999999')
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  private loadConversations(): void {
    // 模拟数据
    this.conversations = [
      {
        id: '1',
        name: 'AI助手',
        lastMessage: '有什么我可以帮助您的吗？',
        timeAgo: '刚刚',
        isAI: true
      },
      {
        id: '2',
        name: '张三',
        lastMessage: '明天的会议准备得怎么样了？',
        timeAgo: '10分钟前',
        isAI: false
      }
    ];
  }
}

interface ConversationItem {
  id: string;
  name: string;
  lastMessage: string;
  timeAgo: string;
  isAI: boolean;
} 