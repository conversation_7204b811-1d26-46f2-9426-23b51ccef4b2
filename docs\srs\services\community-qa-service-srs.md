﻿好的，遵照您的指示，我们来生成一份为 `community-qa-service` (社区问答服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **与AI助手的深度集成**: 明确AI助手如何查询、引用和（在用户授权下）甚至回答问题，将Q&A社区作为AI的核心知识来源。
2.  **悬赏与奖励机制**: 引入“灵境币悬赏”功能，允许提问者使用虚拟货币激励高质量的回答，并与`cina-coin-ledger-service`集成。
3.  **内容质量与声望系统**: 细化基于用户贡献（回答被采纳、被点赞）的声望(Reputation)系统，激励专家用户。
4.  **草稿与编辑历史**: 增加对问题和答案的草稿与编辑历史的支持。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和扩展性指标。

这份文档将描绘一个功能强大、激励有效、且能为人类和AI共同提供高质量知识的问答平台。

---

### CINA.CLUB - community-qa-service 需求规格说明书

**版本: 2.0 (生产级定义，集成AI与悬赏机制)**  
**发布日期: 2025-06-21**  
**最后修订日期: 2025-06-21**  
**文档负责人:** [社区产品经理/技术负责人名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 旨在为用户提供解决实际问题的有效途径。`community-qa-service` 的核心目的是构建一个**结构化的、可信的知识问答平台**。用户可以提出具体、明确的问题，社区中的其他用户或领域专家提供直接的解答，并通过投票和“最佳答案”采纳机制，筛选出最高质量的解决方案。此服务不仅为平台沉淀可复用的高质量知识资产，更将作为**AI助手的核心知识源**，并引入悬赏机制以激励高价值内容的产出。

#### 1.2. 服务范围
本服务 **负责**:
*   **问题(Question)管理**: 提出、编辑、草稿保存、版本历史、标记状态（已解决/未解决）。
*   **答案(Answer)管理**: 提交、编辑、草稿保存、版本历史，以及最重要的**“最佳答案”采纳**。
*   **评论(Comment)管理**: 对问题和答案的轻量级澄清评论。
*   **悬赏与奖励**:
    *   允许提问者为问题设置“灵境币”悬赏。
    *   与`cina-coin-ledger-service`协同，在最佳答案被采纳后，将悬赏金额发放给回答者。
*   **用户声望与互动**:
    *   处理对问题和答案的投票 (顶/踩)。
    *   根据用户的有效贡献（回答被采纳、被点赞），计算和维护其在本领域的**声望(Reputation)**。
*   **内容发现**: 支持按标签、状态、悬赏额、热度等维度进行搜索和筛选。
*   **与平台生态的深度集成**: 包括AI助手、内容审核、游戏化、通知、搜索等。

本服务 **不负责**:
*   开放式、无明确目的的讨论 (由 `community-forum-service` 负责)。
*   即时通讯/私信 (由 `chat-service` 体系负责)。
*   灵境币的底层账本管理 (由 `cina-coin-ledger-service` 负责)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 用户提问、回答、投票、设置悬赏。
*   **`ai-assistant-service` (主要)**: 查询已解决的问题库，作为回答用户查询的权威知识来源；在用户授权下，可帮助用户提问或整理答案。
*   **`cina-coin-ledger-service`**: (被本服务调用) 冻结和转移悬赏金。
*   **`search-indexer-service`**: (消费本服务事件) 索引Q&A内容以支持全局搜索。
*   其他内部服务: `content-moderation-service`, `gamification-service`, `notification-dispatch-service`.

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`community-qa-service` 是平台知识沉淀的“**精炼厂**”和“**智慧银行**”。它将社区的集体智慧结构化，为每一个问题寻求一个或多个高质量的答案，并最终由提问者和社区共同确认一个“最佳”解决方案。这与论坛的“发散性”讨论形成互补，成为**AI助手最信赖的UGC知识源**，并通过经济激励加速高质量内容的产生。

#### 2.2. 主要功能概述
*   以“采纳最佳答案”为核心的结构化问答流程。
*   集成了虚拟货币的悬赏激励机制。
*   基于贡献的声望系统。
*   与AI助手的深度查询与引用集成。

### 3. 核心流程图

#### 3.1. 带悬赏问题的提问与解决流程
```mermaid
sequenceDiagram
    participant UserA as "提问者"
    participant UserB as "回答者"
    participant QAService
    participant LedgerService as "cina-coin-ledger"
    participant NotificationService

    UserA->>QAService: 1. POST /questions (title, content, bountyAmount: 100)
    QAService->>LedgerService: 2. Request to freeze 100 coins from UserA's account
    LedgerService-->>QAService: (Freeze successful)
    QAService->>DB: 3. Create Question (status: OPEN, bounty: 100)
    QAService-->>UserA: 201 Created (questionId)
    
    UserB->>QAService: 4. POST /questions/{id}/answers (content: "My great answer")
    QAService->>NotificationService: 5. Notify UserA of new answer
    
    Note over UserA: UserA reviews and accepts UserB's answer
    
    UserA->>QAService: 6. POST /answers/{ansId}/accept
    QAService->>DB: 7. **Start Transaction**
    QAService->>DB: 8. Update Answer.is_accepted=true, Question.status=RESOLVED
    QAService->>DB: 9. Update UserB's reputation score
    QAService->>DB: 10. **Commit Transaction**
    
    QAService->>LedgerService: 11. **Asynchronously** commit frozen balance: transfer 100 coins to UserB
    QAService->>NotificationService: 12. Notify UserB of accepted answer and bounty reward
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 问题 (Question) 管理
*   **FR4.1.1 (悬赏提问)**: 用户在发布新问题时，可以选择性地附加一笔“灵境币”作为悬赏。
    *   提问时，系统必须调用`cina-coin-ledger-service`**冻结**用户相应数额的灵境币。冻结失败则提问失败。
*   **FR4.1.2 (内容)**: 问题包含标题和支持富文本的详细描述。支持草稿和编辑历史。
*   **FR4.1.3 (状态)**: 问题状态包括`DRAFT`, `OPEN`, `ANSWERED`, `RESOLVED`, `CLOSED`。

#### 4.2. 答案 (Answer) 与奖励
*   **FR4.2.1 (采纳最佳答案 - 核心)**: 只有问题的提问者能从所有答案中选择一个标记为“最佳答案”。
*   **FR4.2.2 (悬赏发放)**: 当悬赏问题的最佳答案被采纳时，系统必须**异步地**调用`cina-coin-ledger-service`，将之前冻结的悬赏金**转移**给最佳答案的作者。
*   **FR4.2.3 (自动采纳 - 可选)**: 如果悬赏问题在一定期限（如15天）后仍未被提问者手动采纳，系统可以根据投票数等指标自动采纳一个答案并发放悬赏。

#### 4.3. 用户声望 (Reputation) 系统
*   **FR4.3.1 (声望计算)**: 系统必须为每个用户维护一个声望分数。
    *   **加分项**: 回答被点赞、回答被采纳为最佳答案。
    *   **扣分项**: 回答被点踩、问题/回答被管理员删除。
*   **FR4.3.2 (权限关联)**: 用户的某些操作权限可以与其声望分数挂钩（如：声望达到一定值才能免审核、才能编辑他人问题等）。

#### 4.4. AI助手集成
*   **FR4.4.1 (AI知识源)**: `ai-assistant-service`在回答用户问题时，应优先查询本服务的已解决问题库(`status=RESOLVED`)，并可以将高质量的问答对作为其回答的直接依据或参考。
*   **FR4.4.2 (AI引用)**: 当AI的回答引用了本服务的某个问答时，必须在其响应中包含对源Q&A的引用链接。
*   **FR4.4.3 (AI辅助提问)**: AI助手可以帮助用户将其模糊的自然语言问题，整理成一个结构化的、带有合适标签的问题，并代为发布。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部RESTful API接口
*   **版本**: `/api/v1/community/qa`
*   **认证**: User JWT, Admin/Mod JWT。
*   **核心端点**:
    *   **Questions**:
        *   `GET /questions?tag=...&status=...&sort_by=hot|newest|bounty_desc`: 列表与搜索。
        *   `POST /questions`: 提问。Request: `AskQuestionRequest { title, contentJson, tags, bountyAmount? }`。
        *   `GET /questions/{questionId}`: 获取问题详情（含答案列表）。
    *   **Answers**:
        *   `POST /questions/{questionId}/answers`: 回答问题。
        *   `POST /answers/{answerId}/accept`: (提问者) 采纳此答案。
    *   **Users**:
        *   `GET /users/{userId}/reputation`: 获取用户的声望和贡献摘要。
*   **内部API (S2S)**:
    *   `GET /internal/search?query=...&top_k=3`: (供`ai-assistant-service`调用) 内部语义/关键词搜索接口，只返回最相关的已解决问答对。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`qa_questions`**: `id`, `author_user_id`, `title`, `content_json`, `status`, `vote_score`, `accepted_answer_id`, `bounty_amount`, `bounty_status` (`PENDING_FREEZE`, `FROZEN`, `AWARDED`, `REFUNDED`), `ledger_freeze_op_id`.
*   **`qa_answers`**: `id`, `question_id`, `author_user_id`, `content_json`, `vote_score`, `is_accepted_answer`.
*   **`qa_votes`**: `(target_type, target_id, user_id)` (PK), `direction`.
*   **`user_reputations`**: `user_id` (PK), `reputation_score`, `answers_provided`, `answers_accepted`.

#### 6.2. 数据一致性与并发
*   **悬赏冻结**: 提问操作必须与账本的冻结操作形成一个**Saga分布式事务**。先尝试冻结，成功后再创建问题。冻结失败则问题创建失败。
*   **采纳与声望**: 采纳答案和更新回答者声望必须在同一个本地数据库事务中完成。
*   **悬赏发放**: 发放是最终一致的。在本地事务成功后，通过异步任务调用账本服务，并有可靠的重试和幂等性保证。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: 问题列表和详情页P99 < 300ms。写操作P99 < 200ms (不含对账本服务的调用)。
*   **AI查询延迟**: `GET /internal/search` 接口P99 < 150ms。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **悬赏流程可靠性**: 必须保证悬赏金不会因系统故障而丢失或重复发放。与账本服务的交互必须是幂等的、可重试的。

#### 7.3. 可扩展性需求
*   服务可水平扩展。数据库可通过读写分离和分区进行扩展。

#### 7.4. 安全性需求
*   **经济安全**: 严格校验悬赏金额，防止负数或超额。与账本服务的交互必须安全可靠。
*   **内容安全**: 所有UGC内容必须送审。
*   **权限控制**: 严格的权限检查（只有提问者能采纳等）。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。
*   **分布式事务**: 对于悬赏提问流程，推荐使用**编排式Saga模式**，由本服务作为Saga协调者。
*   **搜索**: 内部AI查询可以依赖PostgreSQL的FTS和pgvector。对于全局搜索，将数据同步到`search-indexer-service`的ES集群。
*   **异步处理**: 悬赏发放、通知、积分、索引更新等所有非核心阻塞操作，都应通过后台任务或消息队列进行异步处理。

---
这份版本2.0的SRS文档为`community-qa-service`构建了一个现代化的、激励驱动的知识社区。它通过将传统的问答模式与AI和虚拟经济系统深度融合，旨在最大化地激发社区的知识生产力，并使其成果能高效地反哺给AI和所有用户。