# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB Go 服务标准化基础镜像
# 多阶段构建，优化镜像大小和安全性

# Build stage
FROM golang:1.22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装基本工具和依赖
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

# 设置 Go 环境
ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

# 首先复制 go.mod 和 go.sum 文件以利用 Docker 缓存
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
# 使用 ldflags 减小二进制文件大小
RUN go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main ./cmd/server

# Final stage
FROM gcr.io/distroless/static:nonroot

# 从 builder 阶段复制时区数据
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# 从 builder 阶段复制 CA 证书
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# 从 builder 阶段复制应用二进制文件
COPY --from=builder /app/main /app/main

# 创建非特权用户（distroless 自带 nonroot 用户）
USER nonroot:nonroot

# 设置工作目录
WORKDIR /app

# 暴露端口（可以在具体服务中覆盖）
EXPOSE 8080

# 健康检查（在具体服务中可以覆盖）
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/app/main", "health"] || exit 1

# 设置默认环境变量
ENV TZ=UTC \
    PORT=8080

# 启动应用
ENTRYPOINT ["/app/main"] 