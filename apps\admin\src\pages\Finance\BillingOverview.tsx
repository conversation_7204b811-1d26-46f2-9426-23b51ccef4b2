/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:20:00
 * Modified: 2025-01-23 16:20:00
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Button,
  Select,
  DatePicker,
  Table,
  Tag,
  Progress,
  Tooltip,
  Badge,
  Alert,
  Tabs,
  List,
  Avatar,
} from 'antd';
import {
  DollarOutlined,
  TrendingUpOutlined,
  CreditCardOutlined,
  UserOutlined,
  CalendarOutlined,
  DownloadOutlined,
  EyeOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  FallOutlined,
  LineChartOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Line, Column, Pie } from '@ant-design/plots';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface FinancialKPI {
  title: string;
  value: number;
  prefix?: React.ReactNode;
  suffix?: string;
  precision?: number;
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
  };
  color?: string;
}

interface RevenueData {
  date: string;
  revenue: number;
  transactions: number;
  subscriptions: number;
}

interface PaymentMethod {
  method: string;
  count: number;
  amount: number;
  percentage: number;
}

interface Subscription {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  plan: string;
  status: 'active' | 'cancelled' | 'expired' | 'pending';
  amount: number;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  nextBilling: string;
  createdAt: string;
}

interface RecentTransaction {
  id: string;
  type: 'payment' | 'refund' | 'subscription' | 'fee';
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed';
  description: string;
  customer: string;
  paymentMethod: string;
  createdAt: string;
}

const BillingOverview: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data queries
  const { data: financialKPIs = [] } = useQuery({
    queryKey: ['finance', 'kpis', timeRange],
    queryFn: async (): Promise<FinancialKPI[]> => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return [
        {
          title: 'Total Revenue',
          value: 245678.90,
          prefix: <DollarOutlined />,
          precision: 2,
          trend: { direction: 'up', percentage: 12.5 },
          color: '#52c41a'
        },
        {
          title: 'Monthly Recurring Revenue',
          value: 89456.78,
          prefix: <TrendingUpOutlined />,
          precision: 2,
          trend: { direction: 'up', percentage: 8.3 },
          color: '#1890ff'
        },
        {
          title: 'Active Subscriptions',
          value: 1247,
          prefix: <UserOutlined />,
          trend: { direction: 'up', percentage: 5.7 },
          color: '#722ed1'
        },
        {
          title: 'Average Order Value',
          value: 67.89,
          prefix: <CreditCardOutlined />,
          precision: 2,
          trend: { direction: 'down', percentage: 2.1 },
          color: '#faad14'
        },
        {
          title: 'Churn Rate',
          value: 3.2,
          suffix: '%',
          precision: 1,
          trend: { direction: 'down', percentage: 0.8 },
          color: '#f5222d'
        },
        {
          title: 'Customer Lifetime Value',
          value: 1234.56,
          prefix: <DollarOutlined />,
          precision: 2,
          trend: { direction: 'up', percentage: 15.2 },
          color: '#13c2c2'
        }
      ];
    }
  });

  const { data: revenueData = [] } = useQuery({
    queryKey: ['finance', 'revenue', timeRange],
    queryFn: async (): Promise<RevenueData[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        revenue: Math.random() * 10000 + 5000,
        transactions: Math.floor(Math.random() * 100) + 50,
        subscriptions: Math.floor(Math.random() * 20) + 10
      }));
    }
  });

  const { data: paymentMethods = [] } = useQuery({
    queryKey: ['finance', 'payment-methods'],
    queryFn: async (): Promise<PaymentMethod[]> => {
      await new Promise(resolve => setTimeout(resolve, 400));
      return [
        { method: 'Credit Card', count: 1456, amount: 189234.56, percentage: 65.2 },
        { method: 'PayPal', count: 567, amount: 67890.12, percentage: 23.4 },
        { method: 'Bank Transfer', count: 234, amount: 34567.89, percentage: 11.9 },
        { method: 'Cryptocurrency', count: 89, amount: 12345.67, percentage: 4.3 },
        { method: 'Other', count: 45, amount: 5678.90, percentage: 2.0 }
      ];
    }
  });

  const { data: subscriptions = [], isLoading: subscriptionsLoading } = useQuery({
    queryKey: ['finance', 'subscriptions'],
    queryFn: async (): Promise<Subscription[]> => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return [
        {
          id: 'sub_1',
          userId: 'user_123',
          userName: 'John Doe',
          userEmail: '<EMAIL>',
          plan: 'Professional',
          status: 'active',
          amount: 29.99,
          currency: 'USD',
          billingCycle: 'monthly',
          nextBilling: '2025-02-15T00:00:00Z',
          createdAt: '2024-11-15T10:00:00Z'
        },
        {
          id: 'sub_2',
          userId: 'user_456',
          userName: 'Jane Smith',
          userEmail: '<EMAIL>',
          plan: 'Enterprise',
          status: 'active',
          amount: 299.99,
          currency: 'USD',
          billingCycle: 'yearly',
          nextBilling: '2025-12-01T00:00:00Z',
          createdAt: '2024-12-01T14:30:00Z'
        },
        {
          id: 'sub_3',
          userId: 'user_789',
          userName: 'Bob Johnson',
          userEmail: '<EMAIL>',
          plan: 'Basic',
          status: 'cancelled',
          amount: 9.99,
          currency: 'USD',
          billingCycle: 'monthly',
          nextBilling: '2025-01-30T00:00:00Z',
          createdAt: '2024-10-15T09:15:00Z'
        }
      ];
    }
  });

  const { data: recentTransactions = [], isLoading: transactionsLoading } = useQuery({
    queryKey: ['finance', 'transactions', 'recent'],
    queryFn: async (): Promise<RecentTransaction[]> => {
      await new Promise(resolve => setTimeout(resolve, 700));
      return [
        {
          id: 'txn_1',
          type: 'payment',
          amount: 29.99,
          currency: 'USD',
          status: 'completed',
          description: 'Professional Plan - Monthly',
          customer: '<EMAIL>',
          paymentMethod: 'Credit Card',
          createdAt: '2025-01-23T15:30:00Z'
        },
        {
          id: 'txn_2',
          type: 'subscription',
          amount: 299.99,
          currency: 'USD',
          status: 'completed',
          description: 'Enterprise Plan - Yearly',
          customer: '<EMAIL>',
          paymentMethod: 'Bank Transfer',
          createdAt: '2025-01-23T14:45:00Z'
        },
        {
          id: 'txn_3',
          type: 'refund',
          amount: -19.99,
          currency: 'USD',
          status: 'completed',
          description: 'Refund for Basic Plan',
          customer: '<EMAIL>',
          paymentMethod: 'PayPal',
          createdAt: '2025-01-23T13:20:00Z'
        },
        {
          id: 'txn_4',
          type: 'payment',
          amount: 49.99,
          currency: 'USD',
          status: 'pending',
          description: 'Premium Plan - Monthly',
          customer: '<EMAIL>',
          paymentMethod: 'Credit Card',
          createdAt: '2025-01-23T12:15:00Z'
        }
      ];
    }
  });

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'down': return <FallOutlined style={{ color: '#f5222d' }} />;
      default: return <LineChartOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'completed': return 'green';
      case 'pending': return 'orange';
      case 'cancelled': case 'failed': return 'red';
      case 'expired': return 'default';
      default: return 'blue';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment': case 'subscription': return 'green';
      case 'refund': return 'red';
      case 'fee': return 'orange';
      default: return 'blue';
    }
  };

  const subscriptionColumns: ColumnsType<Subscription> = [
    {
      title: 'Customer',
      key: 'customer',
      render: (_, record) => (
        <div>
          <div><Text strong>{record.userName}</Text></div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.userEmail}
          </Text>
        </div>
      )
    },
    {
      title: 'Plan',
      dataIndex: 'plan',
      key: 'plan',
      render: (plan: string) => <Tag color="blue">{plan}</Tag>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge 
          status={status === 'active' ? 'success' : status === 'pending' ? 'processing' : 'error'}
          text={status.toUpperCase()}
        />
      )
    },
    {
      title: 'Amount',
      key: 'amount',
      render: (_, record) => (
        <div>
          <Text strong>${record.amount}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.billingCycle}
          </Text>
        </div>
      )
    },
    {
      title: 'Next Billing',
      dataIndex: 'nextBilling',
      key: 'nextBilling',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="link" icon={<EyeOutlined />} size="small">
            View
          </Button>
        </Space>
      )
    }
  ];

  const transactionColumns: ColumnsType<RecentTransaction> = [
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>
          {type.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      key: 'customer',
      render: (customer: string) => <Text code>{customer}</Text>
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record) => (
        <Text style={{ color: amount < 0 ? '#f5222d' : '#52c41a' }}>
          ${Math.abs(amount).toFixed(2)} {record.currency}
        </Text>
      )
    },
    {
      title: 'Payment Method',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString()
    }
  ];

  const revenueChartConfig = {
    data: revenueData,
    xField: 'date',
    yField: 'revenue',
    smooth: true,
    point: {
      size: 3,
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Revenue',
        value: `$${datum.revenue.toLocaleString()}`,
      }),
    },
  };

  const paymentMethodsChartConfig = {
    data: paymentMethods,
    angleField: 'percentage',
    colorField: 'method',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}%',
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2}>
              <DollarOutlined /> Billing Overview
            </Title>
            <Paragraph type="secondary">
              Monitor revenue, subscriptions, and financial performance.
            </Paragraph>
          </div>
          <Space>
            <Select
              value={timeRange}
              onChange={setTimeRange}
              style={{ width: 120 }}
            >
              <Option value="7d">Last 7 days</Option>
              <Option value="30d">Last 30 days</Option>
              <Option value="90d">Last 90 days</Option>
              <Option value="1y">Last year</Option>
            </Select>
            <Button icon={<DownloadOutlined />}>
              Export Report
            </Button>
          </Space>
        </div>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <LineChartOutlined />
              Overview
            </span>
          }
          key="overview"
        >
          {/* Financial KPIs */}
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            {financialKPIs.map((kpi, index) => (
              <Col span={8} key={index}>
                <Card>
                  <Statistic
                    title={
                      <Space>
                        <Text>{kpi.title}</Text>
                        <Tooltip title={`${kpi.trend.direction === 'up' ? '+' : kpi.trend.direction === 'down' ? '-' : ''}${kpi.trend.percentage}% from last period`}>
                          {getTrendIcon(kpi.trend.direction)}
                        </Tooltip>
                      </Space>
                    }
                    value={kpi.value}
                    prefix={kpi.prefix}
                    suffix={kpi.suffix}
                    precision={kpi.precision}
                    valueStyle={{ color: kpi.color }}
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text 
                      type="secondary" 
                      style={{ 
                        fontSize: '12px',
                        color: kpi.trend.direction === 'up' ? '#52c41a' : kpi.trend.direction === 'down' ? '#f5222d' : '#1890ff'
                      }}
                    >
                      {kpi.trend.direction === 'up' ? '+' : kpi.trend.direction === 'down' ? '-' : ''}
                      {kpi.trend.percentage}% from last period
                    </Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Revenue Chart */}
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={16}>
              <Card title="Revenue Trend">
                <Line {...revenueChartConfig} height={300} />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Payment Methods">
                <Pie {...paymentMethodsChartConfig} height={300} />
              </Card>
            </Col>
          </Row>

          {/* Payment Methods Breakdown */}
          <Card title="Payment Methods Breakdown" style={{ marginBottom: '24px' }}>
            <List
              dataSource={paymentMethods}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<CreditCardOutlined />} />}
                    title={item.method}
                    description={`${item.count} transactions`}
                  />
                  <div style={{ textAlign: 'right' }}>
                    <div><Text strong>${item.amount.toLocaleString()}</Text></div>
                    <div>
                      <Progress 
                        percent={item.percentage} 
                        size="small" 
                        showInfo={false}
                        style={{ width: '100px' }}
                      />
                      <Text type="secondary" style={{ marginLeft: '8px' }}>
                        {item.percentage}%
                      </Text>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <UserOutlined />
              Subscriptions
            </span>
          }
          key="subscriptions"
        >
          <Card>
            <div style={{ marginBottom: '16px' }}>
              <Title level={4}>Active Subscriptions</Title>
              <Paragraph type="secondary">
                Manage customer subscriptions and billing cycles.
              </Paragraph>
            </div>

            <Table
              columns={subscriptionColumns}
              dataSource={subscriptions}
              loading={subscriptionsLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <CreditCardOutlined />
              Transactions
            </span>
          }
          key="transactions"
        >
          <Card>
            <div style={{ marginBottom: '16px' }}>
              <Title level={4}>Recent Transactions</Title>
              <Paragraph type="secondary">
                View and manage payment transactions.
              </Paragraph>
            </div>

            <Table
              columns={transactionColumns}
              dataSource={recentTransactions}
              loading={transactionsLoading}
              rowKey="id"
              pagination={{ pageSize: 15 }}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default BillingOverview; 