/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package http

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/sirupsen/logrus"

	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// Handler holds all HTTP handler dependencies
type Handler struct {
	bffService port.BFFService
	logger     *logrus.Logger
}

// NewHandler creates a new handler instance
func NewHandler(bffService port.BFFService, logger *logrus.Logger) *Handler {
	return &Handler{
		bffService: bffService,
		logger:     logger,
	}
}

// Authentication handlers

// GetCurrentUser returns the current authenticated user information
func (h *Handler) GetCurrentUser(w http.ResponseWriter, r *http.Request) {
	session := r.Context().Value("session").(*model.AdminSession)

	response := map[string]interface{}{
		"id":           session.EmployeeID,
		"email":        session.Email,
		"name":         session.Name,
		"roles":        session.Roles,
		"created_at":   session.CreatedAt,
		"last_used_at": session.LastUsedAt,
	}

	h.writeJSONResponse(w, http.StatusOK, response)
}

// Logout invalidates the current session
func (h *Handler) Logout(w http.ResponseWriter, r *http.Request) {
	session := r.Context().Value("session").(*model.AdminSession)

	if err := h.bffService.DestroySession(r.Context(), session.ID); err != nil {
		h.logger.WithError(err).Error("Failed to destroy session during logout")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to logout")
		return
	}

	// Clear the session cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "admin_session_id",
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteStrictMode,
	})

	h.writeJSONResponse(w, http.StatusOK, map[string]string{"message": "Logged out successfully"})
}

// User management handlers

// GetUsers returns a paginated list of users with filtering options
func (h *Handler) GetUsers(w http.ResponseWriter, r *http.Request) {
	// Parse query parameters
	filter := h.parseUserFilter(r)

	users, err := h.bffService.GetUsers(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get users")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve users")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, users)
}

// GetUserProfile returns the full profile of a specific user
func (h *Handler) GetUserProfile(w http.ResponseWriter, r *http.Request) {
	userID := chi.URLParam(r, "userID")
	if userID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "User ID is required")
		return
	}

	profile, err := h.bffService.GetUserFullProfile(r.Context(), userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user profile")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve user profile")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, profile)
}

// SuspendUser suspends a user account
func (h *Handler) SuspendUser(w http.ResponseWriter, r *http.Request) {
	userID := chi.URLParam(r, "userID")
	if userID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "User ID is required")
		return
	}

	// Parse request body
	var req struct {
		Reason string `json:"reason"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	actorInfo := h.getActorInfo(r)
	if err := h.bffService.SuspendUser(r.Context(), actorInfo, userID, req.Reason); err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":  userID,
			"actor_id": actorInfo.EmployeeID,
		}).Error("Failed to suspend user")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to suspend user")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{"message": "User suspended successfully"})
}

// RestoreUser restores a suspended user account
func (h *Handler) RestoreUser(w http.ResponseWriter, r *http.Request) {
	userID := chi.URLParam(r, "userID")
	if userID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "User ID is required")
		return
	}

	// Parse request body
	var req struct {
		Reason string `json:"reason"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	actorInfo := h.getActorInfo(r)
	if err := h.bffService.RestoreUser(r.Context(), actorInfo, userID, req.Reason); err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":  userID,
			"actor_id": actorInfo.EmployeeID,
		}).Error("Failed to restore user")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to restore user")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{"message": "User restored successfully"})
}

// Content moderation handlers

// GetModerationQueue returns the content moderation queue
func (h *Handler) GetModerationQueue(w http.ResponseWriter, r *http.Request) {
	filter := h.parseModerationFilter(r)

	queue, err := h.bffService.GetModerationQueue(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get moderation queue")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve moderation queue")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, queue)
}

// ApproveContent approves a piece of content
func (h *Handler) ApproveContent(w http.ResponseWriter, r *http.Request) {
	taskID := chi.URLParam(r, "taskID")
	if taskID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Task ID is required")
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	actorInfo := h.getActorInfo(r)
	if err := h.bffService.ApproveContent(r.Context(), actorInfo, taskID, req.Reason); err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to approve content")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to approve content")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{"message": "Content approved successfully"})
}

// RejectContent rejects a piece of content
func (h *Handler) RejectContent(w http.ResponseWriter, r *http.Request) {
	taskID := chi.URLParam(r, "taskID")
	if taskID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Task ID is required")
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	actorInfo := h.getActorInfo(r)
	if err := h.bffService.RejectContent(r.Context(), actorInfo, taskID, req.Reason); err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to reject content")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to reject content")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{"message": "Content rejected successfully"})
}

// Order management handlers

// GetOrders returns a paginated list of orders
func (h *Handler) GetOrders(w http.ResponseWriter, r *http.Request) {
	filter := h.parseOrderFilter(r)

	orders, err := h.bffService.GetOrders(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get orders")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve orders")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, orders)
}

// GetOrderDetails returns detailed information about a specific order
func (h *Handler) GetOrderDetails(w http.ResponseWriter, r *http.Request) {
	orderID := chi.URLParam(r, "orderID")
	if orderID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Order ID is required")
		return
	}

	details, err := h.bffService.GetOrderDetails(r.Context(), orderID)
	if err != nil {
		h.logger.WithError(err).WithField("order_id", orderID).Error("Failed to get order details")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve order details")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, details)
}

// CancelOrder cancels an order
func (h *Handler) CancelOrder(w http.ResponseWriter, r *http.Request) {
	orderID := chi.URLParam(r, "orderID")
	if orderID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Order ID is required")
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	actorInfo := h.getActorInfo(r)
	if err := h.bffService.CancelOrder(r.Context(), actorInfo, orderID, req.Reason); err != nil {
		h.logger.WithError(err).WithField("order_id", orderID).Error("Failed to cancel order")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to cancel order")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{"message": "Order cancelled successfully"})
}

// RefundOrder processes a refund for an order
func (h *Handler) RefundOrder(w http.ResponseWriter, r *http.Request) {
	orderID := chi.URLParam(r, "orderID")
	if orderID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Order ID is required")
		return
	}

	var req struct {
		Amount float64 `json:"amount"`
		Reason string  `json:"reason"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	actorInfo := h.getActorInfo(r)
	if err := h.bffService.RefundOrder(r.Context(), actorInfo, orderID, req.Amount, req.Reason); err != nil {
		h.logger.WithError(err).WithField("order_id", orderID).Error("Failed to refund order")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to refund order")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{"message": "Order refunded successfully"})
}

// Analytics handlers

// GetDashboardSummary returns summary statistics for the admin dashboard
func (h *Handler) GetDashboardSummary(w http.ResponseWriter, r *http.Request) {
	summary, err := h.bffService.GetDashboardSummary(r.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get dashboard summary")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve dashboard summary")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, summary)
}

// GetUserAnalytics returns user analytics data
func (h *Handler) GetUserAnalytics(w http.ResponseWriter, r *http.Request) {
	timeRange := h.parseTimeRange(r)

	analytics, err := h.bffService.GetUserAnalytics(r.Context(), timeRange)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user analytics")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve user analytics")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, analytics)
}

// GetContentAnalytics returns content analytics data
func (h *Handler) GetContentAnalytics(w http.ResponseWriter, r *http.Request) {
	timeRange := h.parseTimeRange(r)

	analytics, err := h.bffService.GetContentAnalytics(r.Context(), timeRange)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get content analytics")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve content analytics")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, analytics)
}

// GetRevenueAnalytics returns revenue analytics data
func (h *Handler) GetRevenueAnalytics(w http.ResponseWriter, r *http.Request) {
	timeRange := h.parseTimeRange(r)

	analytics, err := h.bffService.GetRevenueAnalytics(r.Context(), timeRange)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get revenue analytics")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve revenue analytics")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, analytics)
}

// System management handlers

// GetSystemHealth returns system health status
func (h *Handler) GetSystemHealth(w http.ResponseWriter, r *http.Request) {
	health, err := h.bffService.GetSystemHealth(r.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get system health")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve system health")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, health)
}

// GetAuditLogs returns audit logs with filtering
func (h *Handler) GetAuditLogs(w http.ResponseWriter, r *http.Request) {
	filter := h.parseAuditLogFilter(r)

	logs, err := h.bffService.GetAuditLogs(r.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get audit logs")
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve audit logs")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, logs)
}

// Helper methods

// getActorInfo extracts actor information from the request context
func (h *Handler) getActorInfo(r *http.Request) *port.ActorInfo {
	if actorInfo, ok := r.Context().Value("actor_info").(*port.ActorInfo); ok {
		return actorInfo
	}
	// Fallback - this should not happen in normal flow
	session := r.Context().Value("session").(*model.AdminSession)
	return &port.ActorInfo{
		EmployeeID: session.EmployeeID,
		Email:      session.Email,
		Roles:      session.Roles,
		IPAddress:  r.RemoteAddr,
		UserAgent:  r.UserAgent(),
	}
}

// parseUserFilter parses user filter parameters from query string
func (h *Handler) parseUserFilter(r *http.Request) port.UserFilter {
	filter := port.UserFilter{
		Page:      1,
		PageSize:  20,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	if page := r.URL.Query().Get("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := r.URL.Query().Get("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	filter.Email = r.URL.Query().Get("email")
	filter.Status = r.URL.Query().Get("status")
	filter.Role = r.URL.Query().Get("role")
	filter.SearchTerm = r.URL.Query().Get("search")
	filter.SortBy = getOrDefault(r.URL.Query().Get("sort_by"), "created_at")
	filter.SortOrder = getOrDefault(r.URL.Query().Get("sort_order"), "desc")

	return filter
}

// parseModerationFilter parses moderation filter parameters from query string
func (h *Handler) parseModerationFilter(r *http.Request) port.ModerationFilter {
	filter := port.ModerationFilter{
		Page:     1,
		PageSize: 20,
	}

	if page := r.URL.Query().Get("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := r.URL.Query().Get("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	filter.Status = r.URL.Query().Get("status")
	filter.ContentType = r.URL.Query().Get("content_type")
	filter.Priority = r.URL.Query().Get("priority")

	return filter
}

// parseOrderFilter parses order filter parameters from query string
func (h *Handler) parseOrderFilter(r *http.Request) port.OrderFilter {
	filter := port.OrderFilter{
		Page:     1,
		PageSize: 20,
	}

	if page := r.URL.Query().Get("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := r.URL.Query().Get("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	filter.UserID = r.URL.Query().Get("user_id")
	filter.Status = r.URL.Query().Get("status")
	filter.ServiceID = r.URL.Query().Get("service_id")

	return filter
}

// parseTimeRange parses time range parameters from query string
func (h *Handler) parseTimeRange(r *http.Request) port.TimeRange {
	now := time.Now()
	defaultRange := port.TimeRange{
		Start: now.AddDate(0, 0, -30), // Last 30 days
		End:   now,
	}

	startStr := r.URL.Query().Get("start")
	endStr := r.URL.Query().Get("end")

	if startStr != "" {
		if start, err := time.Parse(time.RFC3339, startStr); err == nil {
			defaultRange.Start = start
		}
	}

	if endStr != "" {
		if end, err := time.Parse(time.RFC3339, endStr); err == nil {
			defaultRange.End = end
		}
	}

	return defaultRange
}

// parseAuditLogFilter parses audit log filter parameters from query string
func (h *Handler) parseAuditLogFilter(r *http.Request) model.AuditLogFilter {
	filter := model.AuditLogFilter{
		Page:      1,
		PageSize:  50,
		SortBy:    "timestamp",
		SortOrder: "desc",
	}

	if page := r.URL.Query().Get("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := r.URL.Query().Get("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 1000 {
			filter.PageSize = ps
		}
	}

	filter.ActorID = r.URL.Query().Get("actor_id")
	filter.Action = r.URL.Query().Get("action")
	filter.ResourceType = r.URL.Query().Get("resource_type")
	filter.ResourceID = r.URL.Query().Get("resource_id")

	return filter
}

// writeJSONResponse writes a JSON response with proper headers
func (h *Handler) writeJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		h.logger.WithError(err).Error("Failed to encode JSON response")
	}
}

// writeErrorResponse writes a standardized error response
func (h *Handler) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"error":     true,
		"message":   message,
		"status":    statusCode,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to write error response")
	}
}

// getOrDefault returns the value if not empty, otherwise returns the default
func getOrDefault(value, defaultValue string) string {
	if value == "" {
		return defaultValue
	}
	return value
}
