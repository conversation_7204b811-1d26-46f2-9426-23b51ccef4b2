# CINA.CLUB Platform - Activity Feed Service Base Kustomization
# Copyright (c) 2025 Cina.Club

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: activity-feed-service-base
  annotations:
    description: "Base configuration for Activity Feed Service Kong Ingress"
    service: "activity-feed-service"
    service-type: "activity-feeds"

resources:
  - ingress.yaml

namespace: activity

commonLabels:
  app: activity-feed-service
  component: api-gateway
  service: activity-feed
  tier: application
  platform: cina-club
  service-type: activity-feeds

commonAnnotations:
  platform: "cina-club"
  service-owner: "<EMAIL>"
  api-version: "v1"
  managed-by: "kong-ingress-controller" 