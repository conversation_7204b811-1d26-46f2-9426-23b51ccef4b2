/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { hilog } from '@kit.PerformanceAnalysisKit';
import { preferences } from '@kit.ArkData';
import { common } from '@kit.AbilityKit';

/**
 * 应用配置接口
 */
export interface AppConfig {
  // 服务器配置
  apiBaseUrl: string;
  wsBaseUrl: string;
  
  // 功能开关
  enableLocalAI: boolean;
  enableE2EE: boolean;
  enableDistributedSync: boolean;
  
  // 性能配置
  maxCacheSize: number;
  networkTimeout: number;
  
  // 用户界面配置
  defaultTheme: 'light' | 'dark' | 'auto';
  defaultLanguage: string;
  
  // 调试配置
  enableDebugMode: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * 应用配置管理器
 * 
 * 负责加载、保存和管理应用的各种配置
 * 支持默认配置、用户配置和运行时配置
 */
export class AppConfigManager {
  private static instance: AppConfigManager;
  private static readonly TAG = 'AppConfigManager';
  
  private config: AppConfig;
  private userPreferences: preferences.Preferences | null = null;
  private isInitialized: boolean = false;

  // 默认配置
  private static readonly DEFAULT_CONFIG: AppConfig = {
    // 服务器配置 - 生产环境
    apiBaseUrl: 'https://api.cina.club',
    wsBaseUrl: 'wss://ws.cina.club',
    
    // 功能开关
    enableLocalAI: true,
    enableE2EE: true,
    enableDistributedSync: true,
    
    // 性能配置
    maxCacheSize: 100 * 1024 * 1024, // 100MB
    networkTimeout: 30000, // 30秒
    
    // 用户界面配置
    defaultTheme: 'auto',
    defaultLanguage: 'zh-CN',
    
    // 调试配置
    enableDebugMode: false,
    logLevel: 'info'
  };

  /**
   * 获取单例实例
   */
  static getInstance(): AppConfigManager {
    if (!AppConfigManager.instance) {
      AppConfigManager.instance = new AppConfigManager();
    }
    return AppConfigManager.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this.config = { ...AppConfigManager.DEFAULT_CONFIG };
  }

  /**
   * 初始化配置管理器
   */
  async initialize(context?: common.UIAbilityContext): Promise<void> {
    if (this.isInitialized) {
      hilog.warn(0x0000, AppConfigManager.TAG, 'AppConfigManager already initialized');
      return;
    }

    try {
      // 初始化用户偏好设置
      if (context) {
        this.userPreferences = await preferences.getPreferences(context, 'app_config');
      }

      // 加载用户自定义配置
      await this.loadUserConfig();
      
      // 根据构建环境调整配置
      this.adjustConfigForEnvironment();
      
      this.isInitialized = true;
      hilog.info(0x0000, AppConfigManager.TAG, 'AppConfigManager initialized successfully');
    } catch (error) {
      hilog.error(0x0000, AppConfigManager.TAG, `Failed to initialize: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取完整配置
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * 获取API基础URL
   */
  getApiBaseUrl(): string {
    return this.config.apiBaseUrl;
  }

  /**
   * 获取WebSocket基础URL
   */
  getWsBaseUrl(): string {
    return this.config.wsBaseUrl;
  }

  /**
   * 检查是否启用本地AI
   */
  isLocalAIEnabled(): boolean {
    return this.config.enableLocalAI;
  }

  /**
   * 检查是否启用端到端加密
   */
  isE2EEEnabled(): boolean {
    return this.config.enableE2EE;
  }

  /**
   * 检查是否启用分布式同步
   */
  isDistributedSyncEnabled(): boolean {
    return this.config.enableDistributedSync;
  }

  /**
   * 获取最大缓存大小
   */
  getMaxCacheSize(): number {
    return this.config.maxCacheSize;
  }

  /**
   * 获取网络超时时间
   */
  getNetworkTimeout(): number {
    return this.config.networkTimeout;
  }

  /**
   * 获取默认主题
   */
  getDefaultTheme(): 'light' | 'dark' | 'auto' {
    return this.config.defaultTheme;
  }

  /**
   * 获取默认语言
   */
  getDefaultLanguage(): string {
    return this.config.defaultLanguage;
  }

  /**
   * 检查是否启用调试模式
   */
  isDebugModeEnabled(): boolean {
    return this.config.enableDebugMode;
  }

  /**
   * 获取日志级别
   */
  getLogLevel(): 'debug' | 'info' | 'warn' | 'error' {
    return this.config.logLevel;
  }

  /**
   * 更新配置项
   */
  async updateConfig(updates: Partial<AppConfig>): Promise<void> {
    this.config = { ...this.config, ...updates };
    
    // 保存到用户偏好设置
    await this.saveUserConfig();
    
    hilog.info(0x0000, AppConfigManager.TAG, 'Configuration updated');
  }

  /**
   * 重置为默认配置
   */
  async resetToDefault(): Promise<void> {
    this.config = { ...AppConfigManager.DEFAULT_CONFIG };
    await this.saveUserConfig();
    hilog.info(0x0000, AppConfigManager.TAG, 'Configuration reset to default');
  }

  /**
   * 加载用户自定义配置
   */
  private async loadUserConfig(): Promise<void> {
    if (!this.userPreferences) {
      return;
    }

    try {
      const userConfigJson = await this.userPreferences.get('user_config', '{}') as string;
      const userConfig = JSON.parse(userConfigJson) as Partial<AppConfig>;
      
      // 合并用户配置
      this.config = { ...this.config, ...userConfig };
      
      hilog.info(0x0000, AppConfigManager.TAG, 'User configuration loaded');
    } catch (error) {
      hilog.warn(0x0000, AppConfigManager.TAG, `Failed to load user config: ${error.message}`);
    }
  }

  /**
   * 保存用户配置
   */
  private async saveUserConfig(): Promise<void> {
    if (!this.userPreferences) {
      return;
    }

    try {
      const userConfigJson = JSON.stringify(this.config);
      await this.userPreferences.put('user_config', userConfigJson);
      await this.userPreferences.flush();
      
      hilog.info(0x0000, AppConfigManager.TAG, 'User configuration saved');
    } catch (error) {
      hilog.error(0x0000, AppConfigManager.TAG, `Failed to save user config: ${error.message}`);
    }
  }

  /**
   * 根据构建环境调整配置
   */
  private adjustConfigForEnvironment(): void {
    // 检查是否为调试构建
    const isDebugBuild = globalThis.process?.env?.NODE_ENV === 'development';
    
    if (isDebugBuild) {
      // 调试环境配置
      this.config.enableDebugMode = true;
      this.config.logLevel = 'debug';
      this.config.apiBaseUrl = 'https://dev-api.cina.club';
      this.config.wsBaseUrl = 'wss://dev-ws.cina.club';
      
      hilog.info(0x0000, AppConfigManager.TAG, 'Configuration adjusted for debug environment');
    } else {
      // 生产环境配置
      this.config.enableDebugMode = false;
      this.config.logLevel = 'warn';
      
      hilog.info(0x0000, AppConfigManager.TAG, 'Configuration adjusted for production environment');
    }
  }
} 