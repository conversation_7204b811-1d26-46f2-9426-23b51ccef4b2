好的，遵照您的指示。我将根据我们刚才讨论的、版本1.0的`blockchain-gateway-service` SRS，为您生成一份专门针对 **`blockchain-gateway-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将详细阐述其作为**平台区块链交互中枢**的技术实现细节，包括**多链适配器框架、RPC节点的高可用管理、以及高效的缓存策略**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `blockchain-gateway-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-28
**参考SRS**: `blockchain-gateway-service-srs.md` (v1.0)
**核心架构**: 多链适配器框架 + RPC节点高可用代理 + 强缓存层

## 1. 概述

`blockchain-gateway-service` 的架构核心是**抽象、代理和缓存**。它必须将不同区块链的RPC接口差异性完全封装，并为上层应用提供一个统一、稳定、高性能的访问入口，同时最大限度地降低对昂贵的RPC节点的直接请求。

**核心技术挑战**:
1.  **多链异构性**: 如何设计一个框架，能同时优雅地处理EVM链（如Ethereum, BSC）和非EVM链（如Solana, TRON）的巨大差异。
2.  **RPC节点的高可用性**: RPC节点是典型的外部依赖，可能变慢、出错或完全不可用。如何实现对节点的健康检查、负载均衡和无缝故障切换是关键。
3.  **缓存策略**: 链上数据查询通常很慢。如何设计一个智能的缓存策略，在保证数据相对新鲜度的同时，大幅提升查询性能并降低成本。
4.  **数据标准化**: 如何将不同链返回的、结构各异的数据（如交易、区块）标准化为平台统一的模型。

本架构通过**“链提供者(ChainProvider)”适配器模式**和**带熔断器的RPC客户端池**来应对这些挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (内部)

```mermaid
graph TD
    subgraph "Calling Services (Client, other MS)"
        A[gRPC/REST Request]
    end

    subgraph "Blockchain Gateway Service"
        B[API Layer (gRPC/REST)<br/>(adapter/transport)]
        
        subgraph "Application Layer (The Brains)"
            C[GatewayService<br/>(application/service)]
        end
        
        subgraph "Chain Provider Framework (The Adapter Layer)"
            D{ProviderFactory}
            E[ChainProvider Interface]
            F[EVMProvider<br/>(for ETH, BSC, ...)]
            G[SolanaProvider]
            H[TronProvider]
        end
        
        subgraph "RPC Client Layer (The Connection Pool)"
            I{RPCClientManager}
            J[RPC Client with<br/>Circuit Breaker]
        end
        
        subgraph "Cache Layer"
            K[Redis Cache<br/>(adapter/cache)]
        end
    end

    subgraph "External World"
        L[RPC Nodes (Infura, Alchemy, ...)]
        M[Blockchain Explorer APIs<br/>(Etherscan, BscScan, ...)]
    end

    A --> B
    B -- "calls" --> C
    C -- "uses" --> K
    C -- "uses factory to get" --> D
    D -- "returns instance of" --> E
    E -- "implemented by" --> F & G & H
    
    F & G & H -- "get client from" --> I
    I -- "manages pool of" --> J
    J -- "makes RPC call to" --> L
    
    F & G & H -- "can also call" --> M

```

### 2.2 最终目录结构 (`services/blockchain-gateway-service/`)

```
blockchain-gateway-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   └── handler.go      # gRPC Handler，调用application/service
│   │   ├── cache/
│   │   │   └── redis_cache.go  # 实现了CacheStore接口
│   │   └── rpc/                # ✨ RPC节点客户端封装 ✨
│   │       ├── client.go       # 定义RPCClient接口和带熔断器的实现
│   │       └── manager.go      # RPCClientManager，管理所有链的客户端池
│   ├── application/
│   │   ├── port/
│   │   │   ├── cache.go
│   │   │   └── provider.go     # 定义ChainProvider接口
│   │   └── service/
│   │       └── gateway_service.go # ✨ 核心业务逻辑实现 ✨
│   └── domain/
│       ├── provider/           # ✨ 所有ChainProvider的实现 ✨
│       │   ├── factory.go      # ProviderFactory
│       │   ├── evm.go          # 通用EVM Provider
│       │   ├── solana.go       # Solana Provider
│       │   └── tron.go         # TRON Provider
│       └── model/
│           └── alias.go        # 使用/core/models中的类型
└── ...
```

---

## 3. 各核心组件深度解析

### 3.1 `adapter/rpc/` - RPC客户端管理层 (The Connection)

这是与外部RPC节点交互的**最底层封装**，核心是**高可用性**。

*   **`manager.go`: `RPCClientManager`**
    *   **职责**: 在服务启动时，根据配置文件，为**每一条链**（如BSC）的**每一个RPC节点URL**（主节点、备用节点1、备用节点2...）创建并管理一个`RPCClient`实例池。
    *   提供`GetClient(chain string, primary bool)`方法，用于获取一个可用的客户端。
*   **`client.go`: `RPCClient`**
    *   **职责**: 封装一个到**单个**RPC端点的连接（如`go-ethereum`的`ethclient.Client`）。
    *   **核心功能: 熔断器 (Circuit Breaker)**。每个`RPCClient`实例都必须内嵌一个熔断器（如`sony/gobreaker`）。
        *   当对此客户端的调用连续失败达到阈值时，熔断器打开，在一段时间内，所有对它的调用都会立即失败（快速失败），而不会产生慢速的网络请求。
        *   熔断器会在半开放状态下，尝试性地放过一个请求，如果成功，则关闭熔断器恢复服务；如果失败，则继续保持打开。
    *   **健康检查**: `RPCClient`需要提供一个`Health()`方法，供`RPCClientManager`定期调用。

### 3.2 `domain/provider/` - 链提供者框架 (The Adapter)

这是**抹平多链差异**的核心。

*   **`application/port/provider.go`**: 定义`ChainProvider`接口。
    ```go
    type ChainProvider interface {
        GetBalance(ctx, address, tokenContracts) (*models.Balance, error)
        GetTransactions(ctx, address, page) ([]*models.Transaction, error)
        BroadcastTransaction(ctx, signedTxHex) (string, error)
        // ...
    }
    ```
*   **`factory.go`: `ProviderFactory`**
    *   一个单例工厂，在启动时被初始化。
    *   提供`GetProvider(chain string) (ChainProvider, error)`方法。
    *   内部有一个`map[string]ChainProvider`。根据`chain`字符串（如`"bsc"`），返回预先创建好的`EVMProvider`或`SolanaProvider`实例。
*   **`evm.go`: `EVMProvider`**
    *   实现了`ChainProvider`接口，用于所有EVM兼容链。
    *   它的构造函数接收一个`RPCClientManager`。
    *   **`GetBalance`实现**:
        1.  从`RPCClientManager`获取一个健康的EVM RPC客户端。
        2.  使用`go-ethereum`的`ethclient`调用`eth_getBalance`查询原生币余额。
        3.  **使用Multicall智能合约**，在一次RPC调用中批量查询所有token的`balanceOf`。
        4.  (可选) 调用区块链浏览器API作为备用数据源。
    *   其他方法的实现类似。
*   **`solana.go`: `SolanaProvider`**
    *   实现了`ChainProvider`接口。
    *   内部使用Solana的Go SDK（如`gagliardetto/solana-go`）来与Solana RPC节点交互。

### 3.3 `application/service/gateway_service.go` - 应用层 (The Brains)

这是gRPC Handler的直接调用对象，负责**编排、缓存和数据标准化**。

*   **`GatewayService` struct**: 依赖注入`ProviderFactory`和`CacheStore`。
*   **`GetBalance(ctx, chain, address, ...)` method**:
    1.  **构造缓存Key**: `key := fmt.Sprintf("bcs:balance:%s:%s", chain, address)`。
    2.  **查询缓存**: 调用`cacheStore.Get(key)`。
    3.  **如果命中**: 反序列化后直接返回。
    4.  **如果未命中**:
        a. 调用`providerFactory.GetProvider(chain)`获取对应的链适配器。
        b. 调用`provider.GetBalance(...)`从链上获取数据。
        c. **数据标准化**: 将`provider`返回的特定于链的数据结构，转换为`/core/models`中定义的、平台统一的`Balance`模型。
        d. **写入缓存**: 调用`cacheStore.Set(key, standardizedData, ttl)`。
        e. 返回标准化数据。
*   **`BroadcastTransaction` method**:
    1.  调用`providerFactory.GetProvider(chain)`。
    2.  调用`provider.BroadcastTransaction(...)`。
    3.  广播成功后，**立即发布`TransactionBroadcasted`事件到Kafka**。
    4.  返回`tx_hash`。

### 3.4 `adapter/cache/` - 缓存层

*   `redis_cache.go`: 实现了`port.CacheStore`接口。
*   **职责**: 封装所有与Redis的交互。使用`MGET`/`MSET`进行批量操作，使用`SETEX`设置TTL。

## 4. 总结

本架构设计通过以下关键点，构建了一个生产级的`blockchain-gateway-service`：
1.  **三层抽象**:
    *   **RPC客户端层 (`adapter/rpc`)**: 负责与单个节点的连接和高可用（熔断）。
    *   **链提供者层 (`domain/provider`)**: 负责抹平不同区块链的协议差异。
    *   **应用服务层 (`application/service`)**: 负责缓存、数据标准化和业务流程编排。
2.  **缓存优先**: 严格的读缓存策略，确保了API的极高性能和低成本。
3.  **高可用设计**: 通过多RPC节点配置和客户端侧的熔断、故障切换机制，保证了即使部分基础设施出现问题，服务依然能够稳定运行。
4.  **可扩展性**: 添加对一条新链的支持，只需要：
    *   在`domain/provider`下实现一个新的`ChainProvider`。
    *   在`ProviderFactory`中注册它。
    *   在配置文件中添加新链的RPC节点。
    *   核心的`GatewayService`代码无需任何改动。

这种架构使得`blockchain-gateway-service`能够作为一个健壮、高效、可平滑扩展的统一网关，为CINA.CLUB所有Web3功能提供坚实、可靠的链上数据支持。