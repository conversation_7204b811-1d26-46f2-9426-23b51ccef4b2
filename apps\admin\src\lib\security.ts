/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 15:50:00
 * Modified: 2025-01-23 15:50:00
 */

import { z } from 'zod';

/**
 * Security Configuration and Utilities
 * Provides CSRF protection, CSP implementation, and input sanitization
 */

// CSRF Token Management
export class CSRFProtection {
  private static readonly TOKEN_KEY = 'csrf-token';
  private static readonly HEADER_NAME = 'X-CSRF-Token';

  /**
   * Generate a new CSRF token
   */
  static generateToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Store CSRF token in sessionStorage
   */
  static setToken(token: string): void {
    sessionStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * Get current CSRF token
   */
  static getToken(): string | null {
    return sessionStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Get CSRF headers for API requests
   */
  static getHeaders(): Record<string, string> {
    const token = this.getToken();
    return token ? { [this.HEADER_NAME]: token } : {};
  }

  /**
   * Initialize CSRF protection
   */
  static async initialize(): Promise<void> {
    try {
      // Request CSRF token from server
      const response = await fetch('/api/v1/csrf-token', {
        method: 'POST',
        credentials: 'include'
      });
      
      if (response.ok) {
        const { token } = await response.json();
        this.setToken(token);
      }
    } catch (error) {
      console.error('Failed to initialize CSRF protection:', error);
    }
  }

  /**
   * Refresh CSRF token
   */
  static async refreshToken(): Promise<boolean> {
    try {
      await this.initialize();
      return true;
    } catch (error) {
      console.error('Failed to refresh CSRF token:', error);
      return false;
    }
  }
}

// Content Security Policy Configuration
export class ContentSecurityPolicy {
  private static readonly CSP_DIRECTIVES = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for Vite in development
      "'unsafe-eval'", // Required for development
      'https://cdn.jsdelivr.net',
      'https://unpkg.com'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for Ant Design
      'https://fonts.googleapis.com'
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
      'data:'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      'http:' // Remove in production
    ],
    'connect-src': [
      "'self'",
      'ws:',
      'wss:',
      process.env.NODE_ENV === 'development' ? 'http://localhost:*' : '',
      'https://api.cina.club'
    ].filter(Boolean),
    'frame-src': ["'none'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'upgrade-insecure-requests': process.env.NODE_ENV === 'production' ? [] : null,
  };

  /**
   * Generate CSP header value
   */
  static generateCSP(): string {
    return Object.entries(this.CSP_DIRECTIVES)
      .filter(([, value]) => value !== null)
      .map(([directive, sources]) => 
        Array.isArray(sources) && sources.length > 0 
          ? `${directive} ${sources.join(' ')}`
          : directive
      )
      .join('; ');
  }

  /**
   * Apply CSP to the document
   */
  static apply(): void {
    if (typeof document !== 'undefined') {
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = this.generateCSP();
      document.head.appendChild(meta);
    }
  }

  /**
   * Check if CSP is supported
   */
  static isSupported(): boolean {
    return typeof window !== 'undefined' && 'SecurityPolicyViolationEvent' in window;
  }

  /**
   * Add CSP violation event listener
   */
  static onViolation(callback: (event: SecurityPolicyViolationEvent) => void): void {
    if (this.isSupported()) {
      document.addEventListener('securitypolicyviolation', callback);
    }
  }
}

// Input Sanitization Utilities
export class InputSanitizer {
  /**
   * HTML sanitization patterns
   */
  private static readonly HTML_PATTERNS = {
    script: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    iframe: /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    object: /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    embed: /<embed\b[^<]*>/gi,
    form: /<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi,
    input: /<input\b[^<]*>/gi,
    button: /<button\b[^<]*(?:(?!<\/button>)<[^<]*)*<\/button>/gi,
    style: /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
    link: /<link\b[^<]*>/gi,
    meta: /<meta\b[^<]*>/gi,
  };

  /**
   * JavaScript event patterns
   */
  private static readonly JS_EVENT_PATTERNS = [
    /on\w+\s*=/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
  ];

  /**
   * Sanitize HTML content
   */
  static sanitizeHTML(input: string): string {
    if (typeof input !== 'string') return '';
    
    let sanitized = input;
    
    // Remove dangerous HTML tags
    Object.values(this.HTML_PATTERNS).forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });
    
    // Remove JavaScript event handlers
    this.JS_EVENT_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });
    
    return sanitized;
  }

  /**
   * Sanitize user input for display
   */
  static sanitizeText(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize URL input
   */
  static sanitizeURL(input: string): string {
    if (typeof input !== 'string') return '';
    
    try {
      const url = new URL(input);
      
      // Only allow HTTP and HTTPS protocols
      if (!['http:', 'https:'].includes(url.protocol)) {
        return '';
      }
      
      return url.toString();
    } catch {
      return '';
    }
  }

  /**
   * Sanitize filename
   */
  static sanitizeFilename(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .replace(/[<>:"/\\|?*]/g, '')
      .replace(/\.\./g, '')
      .replace(/^\./, '')
      .substring(0, 255);
  }
}

// XSS Prevention Utilities
export class XSSProtection {
  /**
   * Validate and sanitize user input using Zod schemas
   */
  static createSafeValidator<T>(schema: z.ZodType<T>) {
    return (input: unknown): { success: true; data: T } | { success: false; error: string } => {
      try {
        const result = schema.parse(input);
        return { success: true, data: result };
      } catch (error) {
        if (error instanceof z.ZodError) {
          return { 
            success: false, 
            error: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
          };
        }
        return { success: false, error: 'Validation failed' };
      }
    };
  }

  /**
   * Safe JSON parser
   */
  static safeJSONParse<T>(input: string): { success: true; data: T } | { success: false; error: string } {
    try {
      const parsed = JSON.parse(input);
      return { success: true, data: parsed };
    } catch (error) {
      return { success: false, error: 'Invalid JSON' };
    }
  }

  /**
   * Create safe innerHTML alternative
   */
  static safeSetInnerHTML(element: HTMLElement, content: string): void {
    const sanitized = InputSanitizer.sanitizeHTML(content);
    element.innerHTML = sanitized;
  }

  /**
   * Create safe click handler
   */
  static safeClickHandler(handler: (event: Event) => void) {
    return (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      
      // Verify the event is from a trusted source
      if (event.isTrusted) {
        handler(event);
      }
    };
  }
}

// Security Headers Helper
export class SecurityHeaders {
  /**
   * Get recommended security headers for API requests
   */
  static getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
      ...CSRFProtection.getHeaders(),
    };
  }

  /**
   * Apply security headers to fetch requests
   */
  static applyToFetch(options: RequestInit = {}): RequestInit {
    return {
      ...options,
      headers: {
        ...this.getSecurityHeaders(),
        ...options.headers,
      },
    };
  }
}

// Initialize security features
export function initializeSecurity(): void {
  // Initialize CSRF protection
  CSRFProtection.initialize();
  
  // Apply Content Security Policy
  ContentSecurityPolicy.apply();
  
  // Add CSP violation reporting
  if (process.env.NODE_ENV === 'production') {
    ContentSecurityPolicy.onViolation((event) => {
      console.warn('CSP Violation:', {
        violatedDirective: event.violatedDirective,
        blockedURI: event.blockedURI,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber,
      });
      
      // Send violation report to server
      fetch('/api/v1/security/csp-violation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          violatedDirective: event.violatedDirective,
          blockedURI: event.blockedURI,
          sourceFile: event.sourceFile,
          lineNumber: event.lineNumber,
          timestamp: new Date().toISOString(),
        }),
      }).catch(console.error);
    });
  }
}

export {
  CSRFProtection,
  ContentSecurityPolicy,
  InputSanitizer,
  XSSProtection,
  SecurityHeaders,
}; 