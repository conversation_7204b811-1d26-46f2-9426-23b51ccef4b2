# CINA.CLUB Platform - <PERSON>ae<PERSON> Distributed Tracing
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: v1
kind: Namespace
metadata:
  name: observability
  labels:
    app.kubernetes.io/name: observability
    app.kubernetes.io/component: system
    platform: cina-club

---
# Jaeger All-in-One deployment for development/staging
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
  namespace: observability
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: all-in-one
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: jaeger
      app.kubernetes.io/component: all-in-one
  template:
    metadata:
      labels:
        app.kubernetes.io/name: jaeger
        app.kubernetes.io/component: all-in-one
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "16686"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 10001
        fsGroup: 10001
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: jaeger
          image: jaegertracing/all-in-one:1.51.0
          args:
            - --memory.max-traces=50000
            - --query.base-path=/jaeger
            - --prometheus.server-url=http://prometheus.monitoring.svc.cluster.local:9090
            - --prometheus.query.support-spanmetrics-connector=true
          ports:
            - containerPort: 16686
              name: query
              protocol: TCP
            - containerPort: 14268
              name: jaeger-http
              protocol: TCP
            - containerPort: 6831
              name: jaeger-thrift
              protocol: UDP
            - containerPort: 6832
              name: jaeger-binary
              protocol: UDP
            - containerPort: 5778
              name: jaeger-configs
              protocol: TCP
            - containerPort: 5775
              name: zipkin-thrift
              protocol: UDP
            - containerPort: 9411
              name: zipkin-http
              protocol: TCP
            - containerPort: 14250
              name: grpc
              protocol: TCP
          env:
            - name: COLLECTOR_OTLP_ENABLED
              value: "true"
            - name: METRICS_STORAGE_TYPE
              value: prometheus
            - name: PROMETHEUS_SERVER_URL
              value: "http://prometheus.monitoring.svc.cluster.local:9090"
            - name: PROMETHEUS_QUERY_SUPPORT_SPANMETRICS_CONNECTOR
              value: "true"
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 1Gi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
          livenessProbe:
            httpGet:
              path: /
              port: 16686
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 16686
            initialDelaySeconds: 5
            periodSeconds: 5

---
# Jaeger Query Service
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query
  namespace: observability
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: query
spec:
  type: ClusterIP
  ports:
    - name: query
      port: 16686
      targetPort: 16686
      protocol: TCP
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: all-in-one

---
# Jaeger Collector Service
apiVersion: v1
kind: Service
metadata:
  name: jaeger-collector
  namespace: observability
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: collector
spec:
  type: ClusterIP
  ports:
    - name: jaeger-http
      port: 14268
      targetPort: 14268
      protocol: TCP
    - name: jaeger-grpc
      port: 14250
      targetPort: 14250
      protocol: TCP
    - name: zipkin-http
      port: 9411
      targetPort: 9411
      protocol: TCP
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: all-in-one

---
# Jaeger Agent Service (for UDP)
apiVersion: v1
kind: Service
metadata:
  name: jaeger-agent
  namespace: observability
  labels:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: agent
spec:
  type: ClusterIP
  ports:
    - name: jaeger-thrift
      port: 6831
      targetPort: 6831
      protocol: UDP
    - name: jaeger-binary
      port: 6832
      targetPort: 6832
      protocol: UDP
    - name: configs
      port: 5778
      targetPort: 5778
      protocol: TCP
    - name: zipkin-thrift
      port: 5775
      targetPort: 5775
      protocol: UDP
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: all-in-one

---
# OpenTelemetry Collector for Jaeger
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-collector
  namespace: observability
  labels:
    app.kubernetes.io/name: otel-collector
    app.kubernetes.io/component: collector
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: otel-collector
      app.kubernetes.io/component: collector
  template:
    metadata:
      labels:
        app.kubernetes.io/name: otel-collector
        app.kubernetes.io/component: collector
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 10001
        fsGroup: 10001
      containers:
        - name: otel-collector
          image: otel/opentelemetry-collector-contrib:0.88.0
          args:
            - --config=/etc/otel/config.yaml
          ports:
            - containerPort: 4317
              name: otlp-grpc
              protocol: TCP
            - containerPort: 4318
              name: otlp-http
              protocol: TCP
            - containerPort: 8888
              name: metrics
              protocol: TCP
          env:
            - name: GOGC
              value: "80"
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          volumeMounts:
            - name: config
              mountPath: /etc/otel
              readOnly: true
      volumes:
        - name: config
          configMap:
            name: otel-collector-config

---
# OpenTelemetry Collector Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector-config
  namespace: observability
  labels:
    app.kubernetes.io/name: otel-collector
    app.kubernetes.io/component: config
data:
  config.yaml: |
    receivers:
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
      
      # Collect metrics from Kong Gateway
      prometheus:
        config:
          scrape_configs:
            - job_name: 'kong-proxy'
              static_configs:
                - targets: ['kong-proxy.kong-system.svc.cluster.local:8100']
              scrape_interval: 15s
              metrics_path: /metrics

    processors:
      batch:
        timeout: 1s
        send_batch_size: 1024
      
      memory_limiter:
        limit_mib: 400
      
      # Add platform metadata
      resource:
        attributes:
          - key: platform
            value: cina-club
            action: upsert
          - key: environment
            from_attribute: deployment.environment
            action: upsert

    exporters:
      # Export traces to Jaeger
      jaeger:
        endpoint: jaeger-collector.observability.svc.cluster.local:14250
        tls:
          insecure: true
      
      # Export metrics to Prometheus
      prometheus:
        endpoint: "0.0.0.0:8888"
        
      # Export logs to Loki (if available)
      loki:
        endpoint: http://loki.logging.svc.cluster.local:3100/loki/api/v1/push

    service:
      pipelines:
        traces:
          receivers: [otlp]
          processors: [memory_limiter, batch, resource]
          exporters: [jaeger]
        
        metrics:
          receivers: [otlp, prometheus]
          processors: [memory_limiter, batch, resource]
          exporters: [prometheus]
        
        logs:
          receivers: [otlp]
          processors: [memory_limiter, batch, resource]
          exporters: [loki]

---
# OpenTelemetry Collector Service
apiVersion: v1
kind: Service
metadata:
  name: otel-collector
  namespace: observability
  labels:
    app.kubernetes.io/name: otel-collector
    app.kubernetes.io/component: collector
spec:
  type: ClusterIP
  ports:
    - name: otlp-grpc
      port: 4317
      targetPort: 4317
      protocol: TCP
    - name: otlp-http
      port: 4318
      targetPort: 4318
      protocol: TCP
    - name: metrics
      port: 8888
      targetPort: 8888
      protocol: TCP
  selector:
    app.kubernetes.io/name: otel-collector
    app.kubernetes.io/component: collector

---
# ServiceMonitor for Prometheus to scrape Jaeger and OTel Collector
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: observability-stack
  namespace: observability
  labels:
    team: platform
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: jaeger
  endpoints:
  - port: query
    path: /metrics
    interval: 30s
  namespaceSelector:
    matchNames:
    - observability 