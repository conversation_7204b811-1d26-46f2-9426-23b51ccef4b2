# pkg/auth - Authentication and Authorization Package

**Copyright (c) 2025 Cina.Club**  
**All rights reserved.**

## Overview

`pkg/auth` provides comprehensive authentication and authorization middleware for CINA.CLUB backend microservices. It implements JWT validation, service-to-service authentication, and role-based access control (RBAC) through composable gRPC interceptors with high performance and security.

## Features

- **JWT Validation**: Automatic JWKS refresh and caching for user tokens
- **Service-to-Service Authentication**: Secure S2S communication using JWT tokens
- **Role-Based Access Control (RBAC)**: High-performance in-memory permission checking
- **Composable Interceptors**: Mix and match authentication components as needed
- **High Performance**: Optimized for minimal latency overhead
- **Production Ready**: Comprehensive error handling and observability

## Quick Start

```go
package main

import (
    "pkg/auth"
    "google.golang.org/grpc"
)

func main() {
    // Configure authentication
    cfg := auth.Config{
        UserCoreJWKSUrl: "https://user-core-service/jwks",
        RBACPolicy: map[string][]string{
            "admin": {"user.create", "user.delete", "service.manage"},
            "user":  {"user.read", "profile.update"},
        },
        S2SPublicKeys: map[string]string{
            "user-service": "-----BEGIN PUBLIC KEY-----\n...",
        },
        AllowedIssuers:  []string{"user-core-service"},
        AllowedAudience: []string{"api-gateway", "current-service"},
        ClockSkew:       5, // seconds
    }

    // Define RPC permission mappings
    rpcPermissions := map[string]string{
        "/UserService/CreateUser": "user.create",
        "/UserService/GetUser":    "user.read",
        "/UserService/DeleteUser": "user.delete",
    }

    // Initialize auth suite
    authSuite, err := auth.NewAuthSuite(cfg, rpcPermissions)
    if err != nil {
        panic(err)
    }

    // Create gRPC server with auth interceptors
    server := grpc.NewServer(
        grpc.UnaryInterceptor(authSuite.UserJWTInterceptor),
        grpc.StreamInterceptor(authSuite.UserJWTStreamInterceptor),
        // Add RBAC if needed
        // grpc.UnaryInterceptor(authSuite.RBACInterceptor),
    )

    // Register your services...
}
```

## Architecture

### Directory Structure

```
pkg/auth/
├── auth.go              # Main entry point and configuration
├── context.go           # Context utilities for user/service injection
├── factory.go           # Factory functions for component creation
├── jwks/
│   └── client.go        # JWKS client with automatic refresh
├── rbac/
│   └── engine.go        # In-memory RBAC policy engine
├── s2s/
│   └── verifier.go      # Service-to-service token verification
├── interceptor/
│   ├── user_jwt.go      # User JWT validation interceptor
│   ├── s2s_jwt.go       # S2S JWT validation interceptor
│   ├── rbac.go          # RBAC authorization interceptor
│   └── chain.go         # Interceptor chain builder
└── README.md            # This file
```

### Component Overview

1. **JWKS Client** (`jwks/client.go`): High-performance JWKS retrieval with automatic caching and refresh
2. **RBAC Engine** (`rbac/engine.go`): Fast in-memory role-based access control
3. **S2S Verifier** (`s2s/verifier.go`): Service-to-service JWT token verification
4. **Interceptors** (`interceptor/`): gRPC interceptors for different authentication needs
5. **Context Utils** (`context.go`): Safe context injection and extraction utilities

## Usage Patterns

### API Gateway Pattern
For services that handle user requests:

```go
authSuite, _ := auth.NewAuthSuite(cfg, rpcPermissions)
server := grpc.NewServer(
    grpc.UnaryInterceptor(authSuite.UserJWTInterceptor),
    grpc.UnaryInterceptor(authSuite.RBACInterceptor),
)
```

### Internal Service Pattern
For services that only handle S2S requests:

```go
authSuite, _ := auth.NewAuthSuite(cfg, nil) // No RPC permissions needed
server := grpc.NewServer(
    grpc.UnaryInterceptor(authSuite.S2SInterceptor),
)
```

### Mixed Service Pattern
For services that handle both user and service requests:

```go
// Use interceptor chain builder for complex scenarios
builder := interceptor.NewChainBuilder().
    AddS2S(s2sInterceptor).
    AddUserJWT(userJWTInterceptor).
    AddRBAC(rbacInterceptor)

opts := builder.BuildServerOptions()
server := grpc.NewServer(opts...)
```

## Configuration

### Environment Variables
Typical configuration through environment variables:

```bash
AUTH_USER_CORE_JWKS_URL=https://user-core-service/jwks
AUTH_ALLOWED_ISSUERS=user-core-service
AUTH_ALLOWED_AUDIENCE=api-gateway,current-service
AUTH_CLOCK_SKEW_SECONDS=5
```

### RBAC Policy Format
Define roles and permissions in configuration:

```yaml
rbac_policy:
  admin:
    - user.create
    - user.delete
    - service.manage
  user:
    - user.read
    - profile.update
  viewer:
    - user.read
```

## Performance Characteristics

- **JWKS Refresh**: Cached with configurable TTL, rate-limited refresh
- **RBAC Lookup**: O(1) permission checking using in-memory hash maps
- **JWT Validation**: Optimized with reusable parsers and minimal allocations
- **Interceptor Overhead**: < 1ms P99 latency under normal conditions

## Security Considerations

- **Algorithm Restriction**: Only RSA and ECDSA algorithms allowed, no HMAC
- **Clock Skew Tolerance**: Configurable tolerance for time drift
- **Fail-Closed**: Authentication failures result in request rejection
- **Token Exposure**: No tokens logged in plaintext
- **Stale Cache**: Graceful degradation when JWKS endpoint is unavailable

## Testing

```go
// Mock implementations available for testing
func TestWithMockAuth(t *testing.T) {
    user := &auth.AuthenticatedUser{
        ID:    "user123",
        Roles: []string{"admin"},
    }
    
    ctx := auth.NewContextWithUser(context.Background(), user)
    
    // Test your business logic with authenticated context
    result, err := myService.DoSomething(ctx)
    assert.NoError(t, err)
}
```

## Migration Guide

When migrating existing services to use `pkg/auth`:

1. Add `pkg/auth` dependency to your service
2. Configure authentication in your service startup
3. Replace custom auth middleware with `pkg/auth` interceptors
4. Update your business logic to use context utilities
5. Test thoroughly with different authentication scenarios

## Troubleshooting

### Common Issues

1. **JWKS Fetch Failures**: Check network connectivity to user-core-service
2. **Token Validation Errors**: Verify issuer/audience configuration
3. **Permission Denied**: Check RBAC policy and RPC permission mappings
4. **Clock Skew Issues**: Increase clock skew tolerance or sync server clocks

### Debug Mode

Enable debug logging to troubleshoot authentication issues:

```go
// Enable detailed logging for debugging
cfg.DebugMode = true
```

## Contributing

Please refer to the main project's contributing guidelines. All changes to authentication and authorization logic require security team review.

---

For more information, see the architecture documentation in `docs/architecture/pkg/pkg-auth-arch.md`. 