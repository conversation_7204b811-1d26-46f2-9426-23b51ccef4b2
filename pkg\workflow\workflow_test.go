/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package workflow

import (
	"context"
	"fmt"
	"testing"
	"time"
)

// Mock node executor for testing
type MockNodeExecutor struct {
	ExecuteFunc func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error)
}

func (m *MockNodeExecutor) Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
	if m.ExecuteFunc != nil {
		return m.ExecuteFunc(ctx, inputs)
	}
	// Default behavior: just echo inputs with a mock output
	outputs := make(map[string]interface{})
	for k, v := range inputs {
		outputs[k] = v
	}
	outputs["mock_result"] = "success"
	return outputs, nil
}

// TestWorkflowValidation tests workflow validation functionality
func TestWorkflowValidation(t *testing.T) {
	tests := []struct {
		name      string
		workflow  *Workflow
		expectErr bool
	}{
		{
			name: "valid workflow",
			workflow: &Workflow{
				ID:   "test-workflow",
				Name: "Test Workflow",
				Nodes: []Node{
					{ID: "node1", Type: "test"},
					{ID: "node2", Type: "test"},
				},
				Edges: []Edge{
					{FromNode: "node1", ToNode: "node2"},
				},
			},
			expectErr: false,
		},
		{
			name: "empty workflow",
			workflow: &Workflow{
				ID:    "empty-workflow",
				Nodes: []Node{},
				Edges: []Edge{},
			},
			expectErr: true,
		},
		{
			name: "duplicate node IDs",
			workflow: &Workflow{
				ID: "duplicate-nodes",
				Nodes: []Node{
					{ID: "node1", Type: "test"},
					{ID: "node1", Type: "test"}, // duplicate
				},
			},
			expectErr: true,
		},
		{
			name: "edge references non-existent node",
			workflow: &Workflow{
				ID: "invalid-edge",
				Nodes: []Node{
					{ID: "node1", Type: "test"},
				},
				Edges: []Edge{
					{FromNode: "node1", ToNode: "nonexistent"},
				},
			},
			expectErr: true,
		},
		{
			name: "self-referencing edge",
			workflow: &Workflow{
				ID: "self-ref",
				Nodes: []Node{
					{ID: "node1", Type: "test"},
				},
				Edges: []Edge{
					{FromNode: "node1", ToNode: "node1"},
				},
			},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.workflow.Validate()
			if tt.expectErr && err == nil {
				t.Errorf("expected error but got none")
			}
			if !tt.expectErr && err != nil {
				t.Errorf("unexpected error: %v", err)
			}
		})
	}
}

// TestGraphConstruction tests DAG construction and validation
func TestGraphConstruction(t *testing.T) {
	t.Run("valid DAG construction", func(t *testing.T) {
		workflow := &Workflow{
			ID: "test-dag",
			Nodes: []Node{
				{ID: "start", Type: "start"},
				{ID: "middle", Type: "process"},
				{ID: "end", Type: "end"},
			},
			Edges: []Edge{
				{FromNode: "start", ToNode: "middle"},
				{FromNode: "middle", ToNode: "end"},
			},
		}

		graph, err := NewGraph(workflow)
		if err != nil {
			t.Fatalf("unexpected error creating graph: %v", err)
		}

		// Test topological sort
		order, err := graph.TopologicalSort()
		if err != nil {
			t.Fatalf("topological sort failed: %v", err)
		}

		expected := []string{"start", "middle", "end"}
		if len(order) != len(expected) {
			t.Fatalf("expected %d nodes, got %d", len(expected), len(order))
		}

		for i, nodeID := range order {
			if nodeID != expected[i] {
				t.Errorf("expected node %s at position %d, got %s", expected[i], i, nodeID)
			}
		}
	})

	t.Run("cycle detection", func(t *testing.T) {
		workflow := &Workflow{
			ID: "cyclic-workflow",
			Nodes: []Node{
				{ID: "node1", Type: "test"},
				{ID: "node2", Type: "test"},
				{ID: "node3", Type: "test"},
			},
			Edges: []Edge{
				{FromNode: "node1", ToNode: "node2"},
				{FromNode: "node2", ToNode: "node3"},
				{FromNode: "node3", ToNode: "node1"}, // creates cycle
			},
		}

		_, err := NewGraph(workflow)
		if err == nil {
			t.Fatal("expected error for cyclic graph but got none")
		}
	})
}

// TestExpressionInterpolation tests template expression evaluation
func TestExpressionInterpolation(t *testing.T) {
	interpolator := NewExpressionInterpolator()

	// Create test execution state
	state := NewExecutionState()
	state.NodeResults = map[string]*NodeResult{
		"node1": {
			NodeID: "node1",
			Status: NodeStatusCompleted,
			Outputs: map[string]interface{}{
				"result": "hello",
				"count":  42,
			},
		},
	}
	state.Variables = map[string]interface{}{
		"globalVar": "world",
	}

	tests := []struct {
		name     string
		inputs   map[string]interface{}
		expected map[string]interface{}
	}{
		{
			name: "simple template",
			inputs: map[string]interface{}{
				"message": "{{ .nodes.node1.outputs.result }}",
			},
			expected: map[string]interface{}{
				"message": "hello",
			},
		},
		{
			name: "complex template with variables",
			inputs: map[string]interface{}{
				"greeting": "{{ .nodes.node1.outputs.result }} {{ .variables.globalVar }}",
				"count":    "{{ .nodes.node1.outputs.count }}",
			},
			expected: map[string]interface{}{
				"greeting": "hello world",
				"count":    42,
			},
		},
		{
			name: "no template expressions",
			inputs: map[string]interface{}{
				"static": "value",
				"number": 123,
			},
			expected: map[string]interface{}{
				"static": "value",
				"number": 123,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := interpolator.InterpolateInputs(tt.inputs, state)
			if err != nil {
				t.Fatalf("interpolation failed: %v", err)
			}

			for key, expectedValue := range tt.expected {
				if result[key] != expectedValue {
					t.Errorf("key %s: expected %v, got %v", key, expectedValue, result[key])
				}
			}
		})
	}
}

// TestWorkflowExecution tests end-to-end workflow execution
func TestWorkflowExecution(t *testing.T) {
	// Create executor
	executor := NewExecutor()

	// Register mock node executors
	executor.RegisterNode("start", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{
				"initialized": true,
				"timestamp":   time.Now().Format(time.RFC3339),
			}, nil
		},
	})

	executor.RegisterNode("process", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{
				"processed": true,
				"input":     inputs["initialized"],
			}, nil
		},
	})

	executor.RegisterNode("end", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{
				"completed": true,
				"final":     "success",
			}, nil
		},
	})

	// Create test workflow
	workflow := &Workflow{
		ID:   "test-execution",
		Name: "Test Execution Workflow",
		Nodes: []Node{
			{
				ID:   "start",
				Type: "start",
				Inputs: map[string]interface{}{
					"initial": "value",
				},
			},
			{
				ID:   "process",
				Type: "process",
				Inputs: map[string]interface{}{
					"initialized": "{{ .nodes.start.outputs.initialized }}",
				},
			},
			{
				ID:   "end",
				Type: "end",
				Inputs: map[string]interface{}{
					"processed": "{{ .nodes.process.outputs.processed }}",
				},
			},
		},
		Edges: []Edge{
			{FromNode: "start", ToNode: "process"},
			{FromNode: "process", ToNode: "end"},
		},
	}

	// Execute workflow
	ctx := context.Background()
	initialState := NewExecutionState()

	finalState, err := executor.Execute(ctx, workflow, initialState)
	if err != nil {
		t.Fatalf("workflow execution failed: %v", err)
	}

	// Verify execution results
	if finalState.Status != ExecutionStatusCompleted {
		t.Errorf("expected status %s, got %s", ExecutionStatusCompleted, finalState.Status)
	}

	if len(finalState.NodeResults) != 3 {
		t.Errorf("expected 3 node results, got %d", len(finalState.NodeResults))
	}

	// Verify each node completed successfully
	for nodeID, result := range finalState.NodeResults {
		if result.Status != NodeStatusCompleted {
			t.Errorf("node %s: expected status %s, got %s", nodeID, NodeStatusCompleted, result.Status)
		}
	}
}

// TestWorkflowSerialization tests JSON serialization/deserialization
func TestWorkflowSerialization(t *testing.T) {
	original := &Workflow{
		ID:          "test-serialization",
		Name:        "Test Serialization",
		Description: "Test workflow for serialization",
		Version:     "1.0",
		Nodes: []Node{
			{
				ID:          "node1",
				Type:        "test",
				Name:        "Test Node",
				Description: "A test node",
				Inputs: map[string]interface{}{
					"param1": "value1",
					"param2": 42,
				},
			},
		},
		Edges: []Edge{},
		Metadata: map[string]interface{}{
			"author": "test",
		},
	}

	// Serialize to JSON
	jsonData, err := original.ToJSON()
	if err != nil {
		t.Fatalf("serialization failed: %v", err)
	}

	// Deserialize from JSON
	deserialized, err := FromJSON(jsonData)
	if err != nil {
		t.Fatalf("deserialization failed: %v", err)
	}

	// Verify basic properties
	if deserialized.ID != original.ID {
		t.Errorf("ID mismatch: expected %s, got %s", original.ID, deserialized.ID)
	}

	if deserialized.Name != original.Name {
		t.Errorf("Name mismatch: expected %s, got %s", original.Name, deserialized.Name)
	}

	if len(deserialized.Nodes) != len(original.Nodes) {
		t.Errorf("Nodes count mismatch: expected %d, got %d", len(original.Nodes), len(deserialized.Nodes))
	}

	// Verify node details
	if len(deserialized.Nodes) > 0 {
		node := deserialized.Nodes[0]
		originalNode := original.Nodes[0]

		if node.ID != originalNode.ID {
			t.Errorf("Node ID mismatch: expected %s, got %s", originalNode.ID, node.ID)
		}

		if node.Type != originalNode.Type {
			t.Errorf("Node Type mismatch: expected %s, got %s", originalNode.Type, node.Type)
		}
	}
}

// TestExecutionStateManagement tests execution state operations
func TestExecutionStateManagement(t *testing.T) {
	state := NewExecutionState()
	state.WorkflowID = "test-workflow"
	state.ExecutionID = "test-execution"

	// Test setting node results
	result1 := &NodeResult{
		NodeID:  "node1",
		Status:  NodeStatusCompleted,
		Outputs: map[string]interface{}{"result": "success"},
	}

	state.SetNodeResult("node1", result1)

	// Test getting node results
	retrieved := state.GetNodeResult("node1")
	if retrieved == nil {
		t.Fatal("expected node result but got nil")
	}

	if retrieved.NodeID != "node1" {
		t.Errorf("expected NodeID node1, got %s", retrieved.NodeID)
	}

	// Test node completion check
	if !state.IsNodeCompleted("node1") {
		t.Error("expected node1 to be completed")
	}

	if state.IsNodeCompleted("nonexistent") {
		t.Error("expected nonexistent node to not be completed")
	}

	// Test variables
	state.SetVariable("testVar", "testValue")
	if state.GetVariable("testVar") != "testValue" {
		t.Error("variable not stored correctly")
	}

	// Test state cloning
	cloned, err := state.Clone()
	if err != nil {
		t.Fatalf("cloning failed: %v", err)
	}

	if cloned.WorkflowID != state.WorkflowID {
		t.Error("cloned state has different WorkflowID")
	}

	if !cloned.IsNodeCompleted("node1") {
		t.Error("cloned state should have completed nodes")
	}
}

// TestRetryLogic tests node execution retry functionality
func TestRetryLogic(t *testing.T) {
	executor := NewExecutor()

	// Create a node executor that fails the first two times
	attemptCount := 0
	executor.RegisterNode("flaky", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			attemptCount++
			if attemptCount < 3 {
				return nil, fmt.Errorf("attempt %d failed", attemptCount)
			}
			return map[string]interface{}{"success": true}, nil
		},
	})

	// Create workflow with retry configuration
	workflow := &Workflow{
		ID: "retry-test",
		Nodes: []Node{
			{
				ID:   "flaky-node",
				Type: "flaky",
				Retry: &RetryConfig{
					MaxAttempts:    3,
					BackoffSeconds: 1,
				},
			},
		},
		Edges: []Edge{},
	}

	ctx := context.Background()
	finalState, err := executor.Execute(ctx, workflow, nil)

	if err != nil {
		t.Fatalf("expected workflow to succeed after retries, but got error: %v", err)
	}

	if finalState.Status != ExecutionStatusCompleted {
		t.Errorf("expected status %s, got %s", ExecutionStatusCompleted, finalState.Status)
	}

	result := finalState.GetNodeResult("flaky-node")
	if result == nil {
		t.Fatal("expected node result")
	}

	if result.Attempt != 3 {
		t.Errorf("expected 3 attempts, got %d", result.Attempt)
	}
}

// TestConditionalExecution tests conditional workflow branching
func TestConditionalExecution(t *testing.T) {
	executor := NewExecutor()

	// Register condition node
	executor.RegisterNode("condition", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			value, _ := inputs["value"].(int)
			return map[string]interface{}{
				"result": value > 10,
			}, nil
		},
	})

	executor.RegisterNode("branch_a", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{"branch": "A"}, nil
		},
	})

	executor.RegisterNode("branch_b", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{"branch": "B"}, nil
		},
	})

	// Create workflow with conditional logic
	workflow := &Workflow{
		ID: "conditional-test",
		Nodes: []Node{
			{
				ID:   "condition",
				Type: "condition",
				Inputs: map[string]interface{}{
					"value": 15, // > 10, should take branch A
				},
			},
			{
				ID:   "branch_a",
				Type: "branch_a",
			},
			{
				ID:   "branch_b",
				Type: "branch_b",
			},
		},
		Edges: []Edge{
			{
				FromNode: "condition",
				ToNode:   "branch_a",
				Condition: &EdgeCondition{
					Type:  "equals",
					Field: "result",
					Value: true,
				},
			},
			{
				FromNode: "condition",
				ToNode:   "branch_b",
				Condition: &EdgeCondition{
					Type:  "equals",
					Field: "result",
					Value: false,
				},
			},
		},
	}

	ctx := context.Background()
	finalState, err := executor.Execute(ctx, workflow, nil)

	if err != nil {
		t.Fatalf("workflow execution failed: %v", err)
	}

	// Verify that branch A was executed (since value > 10)
	if !finalState.IsNodeCompleted("branch_a") {
		t.Error("expected branch_a to be executed")
	}

	// Note: In this simple test, branch_b might also execute since we don't have
	// sophisticated conditional logic in the executor yet. This test demonstrates
	// the structure for conditional workflows.
}

// TestBuiltinNodes tests built-in node implementations using mock nodes
func TestBuiltinNodes(t *testing.T) {
	t.Run("StartNode simulation", func(t *testing.T) {
		// Simulate start node behavior with mock
		node := &MockNodeExecutor{
			ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
				outputs := make(map[string]interface{})
				// Copy all inputs to outputs (StartNode behavior)
				for key, value := range inputs {
					outputs[key] = value
				}
				// Add execution metadata
				outputs["_startTime"] = time.Now().Format(time.RFC3339)
				outputs["_nodeType"] = "start"
				return outputs, nil
			},
		}

		ctx := context.Background()
		inputs := map[string]interface{}{
			"param1": "value1",
			"param2": 42,
		}

		outputs, err := node.Execute(ctx, inputs)
		if err != nil {
			t.Fatalf("StartNode simulation failed: %v", err)
		}

		if outputs["param1"] != "value1" {
			t.Error("StartNode should pass through inputs")
		}

		if outputs["_nodeType"] != "start" {
			t.Error("StartNode should set _nodeType metadata")
		}
	})

	t.Run("ConditionNode simulation", func(t *testing.T) {
		// Simulate condition node behavior with mock
		node := &MockNodeExecutor{
			ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
				operator, ok := inputs["operator"].(string)
				if !ok {
					return nil, fmt.Errorf("operator input is required and must be a string")
				}

				left := inputs["left"]
				right := inputs["right"]

				var result bool
				switch operator {
				case "equals":
					result = fmt.Sprintf("%v", left) == fmt.Sprintf("%v", right)
				case "greater_than":
					if leftInt, ok := left.(int); ok {
						if rightInt, ok := right.(int); ok {
							result = leftInt > rightInt
						}
					}
				default:
					return nil, fmt.Errorf("unknown operator: %s", operator)
				}

				return map[string]interface{}{
					"result":    result,
					"operator":  operator,
					"left":      left,
					"right":     right,
					"_nodeType": "condition",
				}, nil
			},
		}

		ctx := context.Background()

		// Test equals condition
		inputs := map[string]interface{}{
			"operator": "equals",
			"left":     "hello",
			"right":    "hello",
		}

		outputs, err := node.Execute(ctx, inputs)
		if err != nil {
			t.Fatalf("ConditionNode simulation failed: %v", err)
		}

		if outputs["result"] != true {
			t.Error("equals condition should return true")
		}

		// Test greater_than condition
		inputs = map[string]interface{}{
			"operator": "greater_than",
			"left":     10,
			"right":    5,
		}

		outputs, err = node.Execute(ctx, inputs)
		if err != nil {
			t.Fatalf("ConditionNode simulation failed: %v", err)
		}

		if outputs["result"] != true {
			t.Error("greater_than condition should return true")
		}
	})
}

// BenchmarkWorkflowExecution benchmarks workflow execution performance
func BenchmarkWorkflowExecution(b *testing.B) {
	executor := NewExecutor()
	executor.RegisterNode("test", &MockNodeExecutor{})

	workflow := &Workflow{
		ID: "benchmark-workflow",
		Nodes: []Node{
			{ID: "node1", Type: "test"},
			{ID: "node2", Type: "test"},
			{ID: "node3", Type: "test"},
		},
		Edges: []Edge{
			{FromNode: "node1", ToNode: "node2"},
			{FromNode: "node2", ToNode: "node3"},
		},
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		state := NewExecutionState()
		_, err := executor.Execute(ctx, workflow, state)
		if err != nil {
			b.Fatalf("workflow execution failed: %v", err)
		}
	}
}

// Example usage demonstration
func ExampleExecutor_Execute() {
	// Create a new workflow executor
	executor := NewExecutor()

	// Register a simple test node
	executor.RegisterNode("greet", &MockNodeExecutor{
		ExecuteFunc: func(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error) {
			name := inputs["name"].(string)
			return map[string]interface{}{
				"greeting": fmt.Sprintf("Hello, %s!", name),
			}, nil
		},
	})

	// Define a simple workflow
	workflow := &Workflow{
		ID:   "greeting-workflow",
		Name: "Simple Greeting Workflow",
		Nodes: []Node{
			{
				ID:   "greet",
				Type: "greet",
				Inputs: map[string]interface{}{
					"name": "World",
				},
			},
		},
		Edges: []Edge{},
	}

	// Execute the workflow
	ctx := context.Background()
	finalState, err := executor.Execute(ctx, workflow, nil)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	// Get the result
	result := finalState.GetNodeResult("greet")
	fmt.Printf("Greeting: %s\n", result.Outputs["greeting"])

	// Output: Greeting: Hello, World!
}
