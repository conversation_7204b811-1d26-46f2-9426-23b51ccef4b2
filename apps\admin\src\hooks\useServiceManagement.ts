/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { Service, ServiceStatus, ServiceMetrics, ServiceLog, ServiceQueryParams } from '@/types/service'
import { apiClient } from '@/lib/api-client'

/**
 * 服务列表查询 Hook
 */
export const useServices = (params?: ServiceQueryParams) => {
  return useQuery({
    queryKey: ['services', params],
    queryFn: async () => {
      // Mock implementation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockServices: Service[] = [
        {
          id: '1',
          name: 'user-core-service',
          displayName: '用户核心服务',
          type: 'MICROSERVICE' as any,
          status: ServiceStatus.RUNNING,
          health: 'HEALTHY' as any,
          version: '1.2.3',
          endpoint: '/api/users',
          port: 8001,
          host: 'localhost',
          environment: 'production',
          tags: ['core', 'user-management'],
          dependencies: ['database', 'cache'],
          createdAt: '2025-01-01T00:00:00Z',
          updatedAt: '2025-01-23T10:00:00Z',
          lastHealthCheck: '2025-01-23T10:00:00Z',
        },
      ]
      
      return { data: mockServices, total: mockServices.length }
    },
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute auto-refresh
  })
}

/**
 * 服务详情查询 Hook
 */
export const useService = (serviceId: string) => {
  return useQuery({
    queryKey: ['service', serviceId],
    queryFn: async () => {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500))
      
      return {
        id: serviceId,
        name: 'user-core-service',
        displayName: '用户核心服务',
        type: 'MICROSERVICE' as any,
        status: ServiceStatus.RUNNING,
        health: 'HEALTHY' as any,
        version: '1.2.3',
        endpoint: '/api/users',
        port: 8001,
        host: 'localhost',
        environment: 'production',
        tags: ['core', 'user-management'],
        dependencies: ['database', 'cache'],
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-01-23T10:00:00Z',
        lastHealthCheck: '2025-01-23T10:00:00Z',
      } as Service
    },
    enabled: !!serviceId,
    staleTime: 30 * 1000,
    refetchInterval: 30 * 1000, // More frequent refresh for detail view
  })
}

/**
 * 服务指标查询 Hook
 */
export const useServiceMetrics = (serviceId: string) => {
  return useQuery({
    queryKey: ['service-metrics', serviceId],
    queryFn: async () => {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 300))
      
      return {
        serviceId,
        timestamp: new Date().toISOString(),
        cpu: { usage: Math.floor(Math.random() * 80), limit: 80 },
        memory: { 
          usage: **********, 
          limit: **********, 
          percentage: Math.floor(Math.random() * 85) 
        },
        network: { 
          inbound: Math.floor(Math.random() * 2000000), 
          outbound: Math.floor(Math.random() * 3000000) 
        },
        requests: { 
          total: 15000, 
          success: 14850, 
          errors: 150, 
          rate: Math.random() * 50 
        },
        responseTime: { 
          average: Math.floor(Math.random() * 200), 
          p50: 100, 
          p95: 200, 
          p99: 350 
        },
        uptime: 99.9,
      } as ServiceMetrics
    },
    enabled: !!serviceId,
    staleTime: 15 * 1000, // 15 seconds
    refetchInterval: 15 * 1000, // Real-time metrics
  })
}

/**
 * 服务日志查询 Hook
 */
export const useServiceLogs = (serviceId: string, params?: { level?: string; limit?: number }) => {
  return useQuery({
    queryKey: ['service-logs', serviceId, params],
    queryFn: async () => {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockLogs: ServiceLog[] = [
        {
          id: '1',
          serviceId,
          timestamp: new Date().toISOString(),
          level: 'INFO',
          message: 'Service health check passed',
          source: 'health-checker',
          metadata: { endpoint: '/health', responseTime: 45 },
        },
        {
          id: '2',
          serviceId,
          timestamp: new Date(Date.now() - 2 * 60000).toISOString(),
          level: 'WARN',
          message: 'High memory usage detected',
          source: 'monitor',
          metadata: { memoryUsage: 85, threshold: 80 },
        },
        {
          id: '3',
          serviceId,
          timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
          level: 'ERROR',
          message: 'Failed to connect to database',
          source: 'database-pool',
          metadata: { error: 'Connection timeout', retryCount: 3 },
        },
      ]
      
      return mockLogs
    },
    enabled: !!serviceId,
    staleTime: 10 * 1000,
    refetchInterval: 10 * 1000, // Real-time logs
  })
}

/**
 * 服务控制 Hook (启动/停止/重启)
 */
export const useServiceControl = () => {
  const queryClient = useQueryClient()

  const controlMutation = useMutation({
    mutationFn: async ({ serviceId, action }: { serviceId: string; action: 'start' | 'stop' | 'restart' }) => {
      // Mock implementation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      if (Math.random() > 0.9) {
        throw new Error(`服务${action}失败`)
      }
      
      return { success: true, action }
    },
    onSuccess: (data, variables) => {
      const actionTexts = {
        start: '启动',
        stop: '停止',
        restart: '重启',
      }
      
      message.success(`服务${actionTexts[variables.action]}成功`)
      
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['services'] })
      queryClient.invalidateQueries({ queryKey: ['service', variables.serviceId] })
      queryClient.invalidateQueries({ queryKey: ['service-metrics', variables.serviceId] })
    },
    onError: (error, variables) => {
      const actionTexts = {
        start: '启动',
        stop: '停止',
        restart: '重启',
      }
      
      message.error(`服务${actionTexts[variables.action]}失败: ${error.message}`)
    },
  })

  return {
    startService: (serviceId: string) => controlMutation.mutate({ serviceId, action: 'start' }),
    stopService: (serviceId: string) => controlMutation.mutate({ serviceId, action: 'stop' }),
    restartService: (serviceId: string) => controlMutation.mutate({ serviceId, action: 'restart' }),
    isLoading: controlMutation.isPending,
  }
}

/**
 * 批量服务控制 Hook
 */
export const useBulkServiceControl = () => {
  const queryClient = useQueryClient()

  const bulkControlMutation = useMutation({
    mutationFn: async ({ serviceIds, action }: { serviceIds: string[]; action: 'start' | 'stop' | 'restart' }) => {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      if (Math.random() > 0.8) {
        throw new Error(`批量${action}部分失败`)
      }
      
      return { success: true, action, count: serviceIds.length }
    },
    onSuccess: (data, variables) => {
      const actionTexts = {
        start: '启动',
        stop: '停止',
        restart: '重启',
      }
      
      message.success(`批量${actionTexts[variables.action]}成功，共处理 ${data.count} 个服务`)
      
      // 刷新服务列表
      queryClient.invalidateQueries({ queryKey: ['services'] })
    },
    onError: (error, variables) => {
      const actionTexts = {
        start: '启动',
        stop: '停止',
        restart: '重启',
      }
      
      message.error(`批量${actionTexts[variables.action]}失败: ${error.message}`)
    },
  })

  return {
    bulkStart: (serviceIds: string[]) => bulkControlMutation.mutate({ serviceIds, action: 'start' }),
    bulkStop: (serviceIds: string[]) => bulkControlMutation.mutate({ serviceIds, action: 'stop' }),
    bulkRestart: (serviceIds: string[]) => bulkControlMutation.mutate({ serviceIds, action: 'restart' }),
    isLoading: bulkControlMutation.isPending,
  }
}

/**
 * 服务健康检查 Hook
 */
export const useServiceHealthCheck = () => {
  const queryClient = useQueryClient()

  const healthCheckMutation = useMutation({
    mutationFn: async (serviceId: string) => {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const isHealthy = Math.random() > 0.2
      
      return {
        serviceId,
        healthy: isHealthy,
        timestamp: new Date().toISOString(),
        responseTime: Math.floor(Math.random() * 500),
        details: isHealthy ? 'Service is healthy' : 'Service is experiencing issues',
      }
    },
    onSuccess: (data) => {
      if (data.healthy) {
        message.success(`服务健康检查通过 (${data.responseTime}ms)`)
      } else {
        message.warning(`服务健康检查失败: ${data.details}`)
      }
      
      // 刷新服务数据
      queryClient.invalidateQueries({ queryKey: ['service', data.serviceId] })
      queryClient.invalidateQueries({ queryKey: ['service-metrics', data.serviceId] })
    },
    onError: (error) => {
      message.error(`健康检查失败: ${error.message}`)
    },
  })

  return {
    performHealthCheck: healthCheckMutation.mutate,
    isLoading: healthCheckMutation.isPending,
  }
}

/**
 * WebSocket 连接 Hook (用于实时更新)
 */
export const useServiceWebSocket = (serviceId?: string) => {
  const [connected, setConnected] = useState(false)
  const [lastMessage, setLastMessage] = useState<any>(null)
  const queryClient = useQueryClient()

  useEffect(() => {
    if (!serviceId) return

    // Mock WebSocket connection
    const connectWebSocket = () => {
      setConnected(true)
      
      // 模拟定期接收实时数据
      const interval = setInterval(() => {
        const mockUpdate = {
          type: 'service-update',
          serviceId,
          timestamp: new Date().toISOString(),
          data: {
            status: Math.random() > 0.95 ? ServiceStatus.ERROR : ServiceStatus.RUNNING,
            metrics: {
              cpu: Math.floor(Math.random() * 100),
              memory: Math.floor(Math.random() * 100),
            },
          },
        }
        
        setLastMessage(mockUpdate)
        
        // 更新缓存数据
        queryClient.invalidateQueries({ queryKey: ['service-metrics', serviceId] })
      }, 5000) // 5秒更新一次

      return () => {
        clearInterval(interval)
        setConnected(false)
      }
    }

    const cleanup = connectWebSocket()
    
    return cleanup
  }, [serviceId, queryClient])

  return {
    connected,
    lastMessage,
  }
}

/**
 * 服务配置管理 Hook
 */
export const useServiceConfig = (serviceId: string) => {
  const queryClient = useQueryClient()

  const configQuery = useQuery({
    queryKey: ['service-config', serviceId],
    queryFn: async () => {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500))
      
      return {
        serviceId,
        config: {
          port: 8001,
          database: {
            host: 'localhost',
            port: 5432,
            name: 'cina_club',
          },
          cache: {
            host: 'localhost',
            port: 6379,
          },
          logging: {
            level: 'INFO',
            format: 'json',
          },
        },
        lastModified: '2025-01-23T10:00:00Z',
      }
    },
    enabled: !!serviceId,
  })

  const updateConfigMutation = useMutation({
    mutationFn: async ({ serviceId, config }: { serviceId: string; config: any }) => {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return { success: true, config }
    },
    onSuccess: () => {
      message.success('服务配置更新成功')
      queryClient.invalidateQueries({ queryKey: ['service-config', serviceId] })
    },
    onError: (error) => {
      message.error(`配置更新失败: ${error.message}`)
    },
  })

  return {
    config: configQuery.data,
    isLoading: configQuery.isLoading,
    updateConfig: updateConfigMutation.mutate,
    isUpdating: updateConfigMutation.isPending,
  }
} 