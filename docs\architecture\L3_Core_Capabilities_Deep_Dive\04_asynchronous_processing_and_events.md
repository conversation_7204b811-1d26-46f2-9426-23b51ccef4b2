
---
### 文件4: `docs/architecture/L3_Core_Capabilities_Deep_Dive/04_asynchronous_processing_and_events.md`

(内容大纲)
*   **概述**: 阐述事件驱动架构在CINA.CLUB中的核心地位。
*   **领域事件设计**:
    *   **定义**: 什么是好的领域事件（描述过去发生的事实、不可变、包含足够上下文）。
    *   **命名规范**: `<Domain>.<Entity>.<PastTenseVerb>`, e.g., `user.UserRegistered`。
    *   **Schema**: 必须在`/core/api`中用Protobuf定义。
*   **Kafka使用规范**:
    *   **Topic策略**: 按领域或源服务划分Topic。
    *   **分区与Key**: 如何选择消息的Key以保证顺序性（如使用`user_id`作为Key，保证单个用户的所有事件在同一分区）。
*   **后台任务与Worker**:
    *   **技术选型**: Asynq (基于Redis)。
    *   **使用场景**: 需要重试、定时、延迟执行的任务（如视频转码、发送提醒）。
    *   **与事件驱动的区别**: 事件用于解耦，后台任务用于可靠地执行某个具体操作。
*   **死信队列(DLQ)处理流程**:
    *   **机制**: 消费失败的消息被推送到`<original_topic>.dlq`。
    *   **监控**: 对DLQ的队列长度设置告警。
    *   **处理**: SRE/开发人员通过后台工具查看DLQ消息，分析失败原因，并可选择重试或丢弃。
*   **相关包**: 链接到`pkg/messaging`的SRS。

---