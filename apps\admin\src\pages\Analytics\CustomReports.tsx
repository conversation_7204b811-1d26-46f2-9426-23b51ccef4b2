/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:45:00
Modified: 2025-01-23 10:45:00
*/

import React, { useState, useRef } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Switch, 
  Tag, 
  Space, 
  message, 
  Typography,
  Tooltip,
  Popconfirm,
  Badge,
  Steps,
  DatePicker,
  TimePicker
} from 'antd'
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-table'
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  ScheduleOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  SendOutlined,
  FileOutlined,
  DashboardOutlined,
  UserOutlined
} from '@ant-design/icons'

import { CustomReport, ReportTemplate, ReportStatus, ReportFrequency } from '@/types/analytics'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { Text, Title } = Typography
const { Option } = Select
const { Step } = Steps
const { RangePicker } = DatePicker

// Mock data for reports and templates
const mockReports: CustomReport[] = [
  {
    id: '1',
    name: '用户增长月报',
    description: '每月用户增长情况统计报告',
    template: 'user-growth',
    status: ReportStatus.ACTIVE,
    frequency: ReportFrequency.MONTHLY,
    lastRun: '2025-01-22T10:00:00Z',
    nextRun: '2025-02-01T10:00:00Z',
    recipients: ['<EMAIL>', '<EMAIL>'],
    parameters: {
      dateRange: 'last_month',
      metrics: ['total_users', 'new_users', 'active_users'],
      groupBy: 'day',
      includeCharts: true,
    },
    createdBy: 'admin',
    createdAt: '2025-01-15T10:00:00Z',
    updatedAt: '2025-01-20T10:00:00Z',
  },
  {
    id: '2',
    name: '内容表现周报',
    description: '每周内容表现分析报告',
    template: 'content-performance',
    status: ReportStatus.ACTIVE,
    frequency: ReportFrequency.WEEKLY,
    lastRun: '2025-01-22T09:00:00Z',
    nextRun: '2025-01-29T09:00:00Z',
    recipients: ['<EMAIL>'],
    parameters: {
      dateRange: 'last_week',
      metrics: ['views', 'engagement', 'shares'],
      contentTypes: ['POST', 'IMAGE', 'VIDEO'],
      includeCharts: true,
    },
    createdBy: 'content_manager',
    createdAt: '2025-01-10T09:00:00Z',
    updatedAt: '2025-01-22T09:00:00Z',
  }
]

const mockTemplates: ReportTemplate[] = [
  {
    id: 'user-growth',
    name: '用户增长报告',
    description: '分析用户注册、活跃度和留存情况',
    category: 'user',
    icon: 'user',
    fields: [
      { name: 'dateRange', label: '时间范围', type: 'dateRange', required: true },
      { name: 'metrics', label: '指标', type: 'multiselect', required: true },
      { name: 'groupBy', label: '分组', type: 'select', required: false },
      { name: 'includeCharts', label: '包含图表', type: 'boolean', required: false },
    ],
    defaultParameters: {
      dateRange: 'last_month',
      metrics: ['total_users', 'new_users', 'active_users'],
      groupBy: 'day',
      includeCharts: true,
    },
  },
  {
    id: 'content-performance',
    name: '内容表现报告',
    description: '分析内容浏览量、互动率和传播效果',
    category: 'content',
    icon: 'file',
    fields: [
      { name: 'dateRange', label: '时间范围', type: 'dateRange', required: true },
      { name: 'contentTypes', label: '内容类型', type: 'multiselect', required: false },
      { name: 'metrics', label: '指标', type: 'multiselect', required: true },
      { name: 'topN', label: '排行榜数量', type: 'number', required: false },
    ],
    defaultParameters: {
      dateRange: 'last_week',
      contentTypes: ['POST', 'IMAGE', 'VIDEO'],
      metrics: ['views', 'engagement', 'shares'],
      topN: 10,
    },
  }
]

/**
 * Custom Reports Management Component
 */
const CustomReports: React.FC = () => {
  const actionRef = useRef<ActionType>()
  const { hasPermission } = usePermission()
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [modalVisible, setModalVisible] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [editingReport, setEditingReport] = useState<CustomReport | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null)
  const [form] = Form.useForm()

  // Permission checks
  const canCreateReports = hasPermission(Permission.ANALYTICS_MANAGE)
  const canViewReports = hasPermission(Permission.ANALYTICS_VIEW)

  // Fetch reports data
  const fetchReports = async (params: any) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        data: mockReports,
        success: true,
        total: mockReports.length,
      }
    } catch (error) {
      message.error('获取报告列表失败')
      return {
        data: [],
        success: false,
        total: 0,
      }
    }
  }

  // Get status color
  const getStatusColor = (status: ReportStatus) => {
    switch (status) {
      case ReportStatus.ACTIVE: return 'green'
      case ReportStatus.PAUSED: return 'orange'
      case ReportStatus.ERROR: return 'red'
      case ReportStatus.DRAFT: return 'blue'
      default: return 'default'
    }
  }

  // Get status text
  const getStatusText = (status: ReportStatus) => {
    switch (status) {
      case ReportStatus.ACTIVE: return '运行中'
      case ReportStatus.PAUSED: return '已暂停'
      case ReportStatus.ERROR: return '错误'
      case ReportStatus.DRAFT: return '草稿'
      default: return '未知'
    }
  }

  // Get frequency text
  const getFrequencyText = (frequency: ReportFrequency) => {
    switch (frequency) {
      case ReportFrequency.DAILY: return '每日'
      case ReportFrequency.WEEKLY: return '每周'
      case ReportFrequency.MONTHLY: return '每月'
      case ReportFrequency.QUARTERLY: return '每季度'
      case ReportFrequency.MANUAL: return '手动'
      default: return '未知'
    }
  }

  // Open create/edit modal
  const handleEdit = (report?: CustomReport) => {
    setEditingReport(report || null)
    setCurrentStep(0)
    setModalVisible(true)
    
    if (report) {
      form.setFieldsValue({
        name: report.name,
        description: report.description,
        template: report.template,
        frequency: report.frequency,
        recipients: report.recipients,
        ...report.parameters,
      })
      const template = mockTemplates.find(t => t.id === report.template)
      setSelectedTemplate(template || null)
    } else {
      form.resetFields()
      setSelectedTemplate(null)
    }
  }

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    const template = mockTemplates.find(t => t.id === templateId)
    setSelectedTemplate(template || null)
    
    if (template) {
      form.setFieldsValue({
        template: templateId,
        ...template.defaultParameters,
      })
    }
  }

  // Submit form
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (editingReport) {
        message.success('报告更新成功')
      } else {
        message.success('报告创建成功')
      }
      
      setModalVisible(false)
      actionRef.current?.reload()
      form.resetFields()
      setSelectedTemplate(null)
    } catch (error) {
      console.error('Form validation failed:', error)
    }
  }

  // Run report manually
  const handleRunReport = async (reportId: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('报告生成中，完成后将发送到您的邮箱')
    } catch (error) {
      message.error('报告运行失败')
    }
  }

  // Toggle report status
  const handleToggleStatus = async (reportId: string, currentStatus: ReportStatus) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      const newStatus = currentStatus === ReportStatus.ACTIVE ? ReportStatus.PAUSED : ReportStatus.ACTIVE
      message.success(`报告已${getStatusText(newStatus)}`)
      actionRef.current?.reload()
    } catch (error) {
      message.error('状态切换失败')
    }
  }

  // Delete report
  const handleDelete = async (reportId: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      message.success('报告删除成功')
      actionRef.current?.reload()
    } catch (error) {
      message.error('删除失败')
    }
  }

  // Export report
  const handleExport = async (reportId: string, format: 'excel' | 'pdf') => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success(`${format.toUpperCase()} 报告下载中...`)
    } catch (error) {
      message.error('导出失败')
    }
  }

  // Table columns
  const columns: ProColumns<CustomReport>[] = [
    {
      title: '报告信息',
      key: 'info',
      width: 250,
      fixed: 'left',
      render: (_, record) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <Text strong>{record.name}</Text>
            <Tag color="blue" style={{ marginLeft: '8px', fontSize: '10px' }}>
              {record.template}
            </Tag>
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
            {record.description}
          </div>
          <div style={{ fontSize: '11px', color: '#999' }}>
            创建者: {record.createdBy}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ReportStatus, record) => (
        <div style={{ textAlign: 'center' }}>
          <Tag color={getStatusColor(status)}>
            {getStatusText(status)}
          </Tag>
          {canCreateReports && status !== ReportStatus.DRAFT && (
            <div style={{ marginTop: '4px' }}>
              <Switch
                size="small"
                checked={status === ReportStatus.ACTIVE}
                onChange={() => handleToggleStatus(record.id, status)}
              />
            </div>
          )}
        </div>
      ),
    },
    {
      title: '执行频率',
      dataIndex: 'frequency',
      key: 'frequency',
      width: 100,
      render: (frequency: ReportFrequency) => (
        <div style={{ textAlign: 'center' }}>
          <ScheduleOutlined style={{ marginRight: '4px' }} />
          {getFrequencyText(frequency)}
        </div>
      ),
    },
    {
      title: '上次运行',
      dataIndex: 'lastRun',
      key: 'lastRun',
      width: 150,
      render: (lastRun: string) => (
        <Text style={{ fontSize: '12px' }}>
          {lastRun ? new Date(lastRun).toLocaleString() : '未运行'}
        </Text>
      ),
    },
    {
      title: '收件人',
      dataIndex: 'recipients',
      key: 'recipients',
      width: 120,
      render: (recipients: string[]) => (
        <div style={{ textAlign: 'center' }}>
          <Badge count={recipients.length} style={{ backgroundColor: '#52c41a' }}>
            <SendOutlined />
          </Badge>
          <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
            {recipients.length} 位收件人
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <Space wrap>
          <Tooltip title="立即运行">
            <Button
              type="text"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleRunReport(record.id)}
            />
          </Tooltip>
          <Tooltip title="导出Excel">
            <Button
              type="text"
              size="small"
              icon={<FileExcelOutlined />}
              onClick={() => handleExport(record.id, 'excel')}
            />
          </Tooltip>
          <Tooltip title="导出PDF">
            <Button
              type="text"
              size="small"
              icon={<FilePdfOutlined />}
              onClick={() => handleExport(record.id, 'pdf')}
            />
          </Tooltip>
          {canCreateReports && (
            <>
              <Tooltip title="编辑">
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(record)}
                />
              </Tooltip>
              <Tooltip title="删除">
                <Popconfirm
                  title="确定要删除这个报告吗？"
                  onConfirm={() => handleDelete(record.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Popconfirm>
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ]

  if (!canViewReports) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限查看自定义报告</p>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <ProTable<CustomReport>
        headerTitle="自定义报告"
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={fetchReports}
        rowSelection={canCreateReports ? {
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        } : undefined}
        toolBarRender={() => [
          canCreateReports && (
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleEdit()}
            >
              新建报告
            </Button>
          ),
        ]}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        scroll={{ x: 1000 }}
      />

      {/* Create/Edit Report Modal */}
      <Modal
        title={editingReport ? '编辑报告' : '新建报告'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              {currentStep > 0 && (
                <Button onClick={() => setCurrentStep(currentStep - 1)}>
                  上一步
                </Button>
              )}
            </div>
            <div>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              {currentStep < 2 ? (
                <Button 
                  type="primary" 
                  onClick={() => setCurrentStep(currentStep + 1)}
                  disabled={currentStep === 0 && !selectedTemplate}
                >
                  下一步
                </Button>
              ) : (
                <Button type="primary" onClick={handleSubmit}>
                  {editingReport ? '更新' : '创建'}
                </Button>
              )}
            </div>
          </div>
        }
      >
        <Steps current={currentStep} style={{ marginBottom: '24px' }}>
          <Step title="选择模板" icon={<FileOutlined />} />
          <Step title="配置参数" icon={<SettingOutlined />} />
          <Step title="调度设置" icon={<ScheduleOutlined />} />
        </Steps>

        <Form form={form} layout="vertical">
          {/* Step 1: Template Selection */}
          {currentStep === 0 && (
            <div>
              <Title level={4}>选择报告模板</Title>
              <Row gutter={[16, 16]}>
                {mockTemplates.map((template) => (
                  <Col xs={24} sm={12} lg={8} key={template.id}>
                    <Card
                      size="small"
                      hoverable
                      style={{ 
                        cursor: 'pointer',
                        border: selectedTemplate?.id === template.id ? '2px solid #1890ff' : '1px solid #d9d9d9'
                      }}
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                          {template.icon === 'user' && <UserOutlined />}
                          {template.icon === 'file' && <FileOutlined />}
                          {template.icon === 'dashboard' && <DashboardOutlined />}
                        </div>
                        <Text strong>{template.name}</Text>
                        <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                          {template.description}
                        </div>
                        <Tag color="blue" style={{ marginTop: '8px' }}>
                          {template.category}
                        </Tag>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          )}

          {/* Step 2: Parameters Configuration */}
          {currentStep === 1 && selectedTemplate && (
            <div>
              <Title level={4}>配置报告参数</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="报告名称"
                    name="name"
                    rules={[{ required: true, message: '请输入报告名称' }]}
                  >
                    <Input placeholder="请输入报告名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="报告描述"
                    name="description"
                  >
                    <Input placeholder="请输入报告描述" />
                  </Form.Item>
                </Col>
              </Row>

              {/* Dynamic parameter fields based on template */}
              {selectedTemplate.fields.map((field) => (
                <Form.Item
                  key={field.name}
                  label={field.label}
                  name={field.name}
                  rules={field.required ? [{ required: true, message: `请选择${field.label}` }] : []}
                >
                  {field.type === 'dateRange' && <RangePicker />}
                  {field.type === 'select' && (
                    <Select placeholder={`请选择${field.label}`}>
                      <Option value="day">按天</Option>
                      <Option value="week">按周</Option>
                      <Option value="month">按月</Option>
                    </Select>
                  )}
                  {field.type === 'multiselect' && (
                    <Select mode="multiple" placeholder={`请选择${field.label}`}>
                      <Option value="total_users">总用户数</Option>
                      <Option value="new_users">新增用户</Option>
                      <Option value="active_users">活跃用户</Option>
                      <Option value="views">浏览量</Option>
                      <Option value="engagement">互动率</Option>
                      <Option value="shares">分享数</Option>
                    </Select>
                  )}
                  {field.type === 'boolean' && <Switch />}
                  {field.type === 'number' && <Input type="number" placeholder={`请输入${field.label}`} />}
                </Form.Item>
              ))}
            </div>
          )}

          {/* Step 3: Schedule Settings */}
          {currentStep === 2 && (
            <div>
              <Title level={4}>调度设置</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="执行频率"
                    name="frequency"
                    rules={[{ required: true, message: '请选择执行频率' }]}
                  >
                    <Select placeholder="请选择执行频率">
                      <Option value={ReportFrequency.MANUAL}>手动执行</Option>
                      <Option value={ReportFrequency.DAILY}>每日</Option>
                      <Option value={ReportFrequency.WEEKLY}>每周</Option>
                      <Option value={ReportFrequency.MONTHLY}>每月</Option>
                      <Option value={ReportFrequency.QUARTERLY}>每季度</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="执行时间"
                    name="executeTime"
                  >
                    <TimePicker format="HH:mm" placeholder="选择执行时间" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="收件人邮箱"
                name="recipients"
                rules={[{ required: true, message: '请输入收件人邮箱' }]}
              >
                <Select 
                  mode="tags" 
                  placeholder="请输入收件人邮箱地址"
                  tokenSeparators={[',']}
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="自动发送"
                    name="autoSend"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="包含图表"
                    name="includeCharts"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          )}
        </Form>
      </Modal>
    </div>
  )
}

export default CustomReports 