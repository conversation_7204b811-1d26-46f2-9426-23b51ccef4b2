/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"errors"
	"fmt"

	"golang.org/x/crypto/argon2"
	"golang.org/x/crypto/chacha20poly1305"
)

// E2EEEngine 端到端加密引擎
type E2EEEngine struct {
	keyDerivationSalt []byte
}

// NewE2EEEngine 创建新的E2EE引擎
func NewE2EEEngine() *E2EEEngine {
	salt := make([]byte, 32)
	rand.Read(salt)
	return &E2EEEngine{
		keyDerivationSalt: salt,
	}
}

// EncryptedData 加密数据结构
type EncryptedData struct {
	Algorithm  string            `json:"algorithm"`
	KeyID      string            `json:"key_id"`
	Nonce      []byte            `json:"nonce"`
	Ciphertext []byte            `json:"ciphertext"`
	AuthTag    []byte            `json:"auth_tag,omitempty"`
	Metadata   map[string]string `json:"metadata,omitempty"`
}

// KeyPair 密钥对
type KeyPair struct {
	PublicKey  []byte `json:"public_key"`
	PrivateKey []byte `json:"private_key"`
	KeyID      string `json:"key_id"`
}

// EncryptWithPassword 使用密码加密数据
func (e *E2EEEngine) EncryptWithPassword(data []byte, password string) (*EncryptedData, error) {
	// 使用Argon2id派生密钥
	key := argon2.IDKey([]byte(password), e.keyDerivationSalt, 1, 64*1024, 4, 32)

	// 使用ChaCha20-Poly1305加密
	aead, err := chacha20poly1305.New(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	nonce := make([]byte, aead.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := aead.Seal(nil, nonce, data, nil)

	return &EncryptedData{
		Algorithm:  "ChaCha20-Poly1305",
		KeyID:      e.deriveKeyID(password),
		Nonce:      nonce,
		Ciphertext: ciphertext,
		Metadata:   map[string]string{"kdf": "Argon2id"},
	}, nil
}

// DecryptWithPassword 使用密码解密数据
func (e *E2EEEngine) DecryptWithPassword(encData *EncryptedData, password string) ([]byte, error) {
	if encData.Algorithm != "ChaCha20-Poly1305" {
		return nil, errors.New("unsupported encryption algorithm")
	}

	// 验证密钥ID
	expectedKeyID := e.deriveKeyID(password)
	if encData.KeyID != expectedKeyID {
		return nil, errors.New("invalid password or corrupted data")
	}

	// 重新派生密钥
	key := argon2.IDKey([]byte(password), e.keyDerivationSalt, 1, 64*1024, 4, 32)

	aead, err := chacha20poly1305.New(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	plaintext, err := aead.Open(nil, encData.Nonce, encData.Ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}

	return plaintext, nil
}

// EncryptWithKey 使用对称密钥加密数据
func (e *E2EEEngine) EncryptWithKey(data []byte, key []byte) (*EncryptedData, error) {
	if len(key) != 32 {
		return nil, errors.New("key must be 32 bytes")
	}

	// 使用AES-256-GCM加密
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	nonce := make([]byte, aead.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := aead.Seal(nil, nonce, data, nil)

	return &EncryptedData{
		Algorithm:  "AES-256-GCM",
		KeyID:      e.deriveKeyIDFromBytes(key),
		Nonce:      nonce,
		Ciphertext: ciphertext,
	}, nil
}

// DecryptWithKey 使用对称密钥解密数据
func (e *E2EEEngine) DecryptWithKey(encData *EncryptedData, key []byte) ([]byte, error) {
	if len(key) != 32 {
		return nil, errors.New("key must be 32 bytes")
	}

	if encData.Algorithm != "AES-256-GCM" {
		return nil, errors.New("unsupported encryption algorithm")
	}

	// 验证密钥ID
	expectedKeyID := e.deriveKeyIDFromBytes(key)
	if encData.KeyID != expectedKeyID {
		return nil, errors.New("invalid key or corrupted data")
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	plaintext, err := aead.Open(nil, encData.Nonce, encData.Ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}

	return plaintext, nil
}

// GenerateKey 生成随机密钥
func (e *E2EEEngine) GenerateKey() ([]byte, error) {
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return nil, fmt.Errorf("failed to generate key: %w", err)
	}
	return key, nil
}

// deriveKeyID 从密码派生密钥ID
func (e *E2EEEngine) deriveKeyID(password string) string {
	h := sha256.New()
	h.Write([]byte(password))
	h.Write(e.keyDerivationSalt)
	return fmt.Sprintf("%x", h.Sum(nil))[:16]
}

// deriveKeyIDFromBytes 从字节密钥派生密钥ID
func (e *E2EEEngine) deriveKeyIDFromBytes(key []byte) string {
	h := sha256.New()
	h.Write(key)
	return fmt.Sprintf("%x", h.Sum(nil))[:16]
}

// SecureWipe 安全擦除内存中的敏感数据
func SecureWipe(data []byte) {
	for i := range data {
		data[i] = 0
	}
}
