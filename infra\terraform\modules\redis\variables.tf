# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

variable "cluster_id" {
  description = "Group identifier for the Redis cluster"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs"
  type        = list(string)
}

variable "node_type" {
  description = "The compute and memory capacity of the nodes"
  type        = string
  default     = "cache.t3.micro"
}

variable "num_cache_nodes" {
  description = "The initial number of cache nodes"
  type        = number
  default     = 1
}

variable "parameter_group_family" {
  description = "The name of the parameter group family"
  type        = string
  default     = "redis7"
}

variable "parameters" {
  description = "List of parameter group parameters to apply"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "replication_group_id" {
  description = "The ID of the replication group (for clustered Redis)"
  type        = string
  default     = null
}

variable "automatic_failover_enabled" {
  description = "Specifies whether automatic failover is enabled"
  type        = bool
  default     = false
}

variable "multi_az_enabled" {
  description = "Specifies whether to enable Multi-AZ Support"
  type        = bool
  default     = false
}

variable "at_rest_encryption_enabled" {
  description = "Whether to enable encryption at rest"
  type        = bool
  default     = true
}

variable "transit_encryption_enabled" {
  description = "Whether to enable encryption in transit"
  type        = bool
  default     = true
}

variable "auth_token" {
  description = "The password used to access a password protected server"
  type        = string
  default     = null
  sensitive   = true
}

variable "maintenance_window" {
  description = "Specifies the weekly time range for maintenance"
  type        = string
  default     = "sun:05:00-sun:06:00"
}

variable "snapshot_retention_limit" {
  description = "The number of days for which ElastiCache will retain automatic cache cluster snapshots"
  type        = number
  default     = 5
}

variable "snapshot_window" {
  description = "The daily time range for snapshots"
  type        = string
  default     = "03:00-05:00"
}

variable "allowed_cidr_blocks" {
  description = "List of CIDR blocks allowed to access the Redis cluster"
  type        = list(string)
  default     = ["10.0.0.0/16"]
}

variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
  default     = {}
} 