/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import (
	"context"

	"github.com/segmentio/kafka-go"
	"go.opentelemetry.io/otel/propagation"
)

const (
	// HeaderTraceParent W3C Trace Context 父追踪头
	HeaderTraceParent = "traceparent"
	// HeaderTraceState W3C Trace Context 状态头
	HeaderTraceState = "tracestate"
)

// Propagator 追踪上下文传播器
type Propagator struct {
	propagator propagation.TextMapPropagator
}

// NewPropagator 创建新的追踪上下文传播器
func NewPropagator(propagator propagation.TextMapPropagator) *Propagator {
	if propagator == nil {
		// 使用默认的 W3C Trace Context 传播器
		propagator = propagation.TraceContext{}
	}
	return &Propagator{
		propagator: propagator,
	}
}

// Inject 将追踪上下文注入到 Kafka 消息头中
func (p *Propagator) Inject(ctx context.Context, headers *[]kafka.Header) {
	carrier := &KafkaHeaderCarrier{headers: headers}
	p.propagator.Inject(ctx, carrier)
}

// Extract 从 Kafka 消息头中提取追踪上下文
func (p *Propagator) Extract(ctx context.Context, headers []kafka.Header) context.Context {
	carrier := &KafkaHeaderCarrier{headers: &headers}
	return p.propagator.Extract(ctx, carrier)
}

// KafkaHeaderCarrier 实现 propagation.TextMapCarrier 接口
// 用于在 Kafka 消息头中读写追踪上下文
type KafkaHeaderCarrier struct {
	headers *[]kafka.Header
}

// Get 获取指定键的值
func (c *KafkaHeaderCarrier) Get(key string) string {
	if c.headers == nil {
		return ""
	}

	for _, header := range *c.headers {
		if header.Key == key {
			return string(header.Value)
		}
	}
	return ""
}

// Set 设置指定键的值
func (c *KafkaHeaderCarrier) Set(key, value string) {
	if c.headers == nil {
		headers := make([]kafka.Header, 0)
		c.headers = &headers
	}

	// 检查是否已存在该键，如果存在则更新
	for i, header := range *c.headers {
		if header.Key == key {
			(*c.headers)[i].Value = []byte(value)
			return
		}
	}

	// 如果不存在则添加新的头
	*c.headers = append(*c.headers, kafka.Header{
		Key:   key,
		Value: []byte(value),
	})
}

// Keys 返回所有键的列表
func (c *KafkaHeaderCarrier) Keys() []string {
	if c.headers == nil {
		return nil
	}

	keys := make([]string, 0, len(*c.headers))
	for _, header := range *c.headers {
		keys = append(keys, header.Key)
	}
	return keys
}

// HeadersToMap 将 Kafka 消息头转换为 map
func HeadersToMap(headers []kafka.Header) map[string]string {
	headerMap := make(map[string]string, len(headers))
	for _, header := range headers {
		headerMap[header.Key] = string(header.Value)
	}
	return headerMap
}

// MapToHeaders 将 map 转换为 Kafka 消息头
func MapToHeaders(headerMap map[string]string) []kafka.Header {
	headers := make([]kafka.Header, 0, len(headerMap))
	for key, value := range headerMap {
		headers = append(headers, kafka.Header{
			Key:   key,
			Value: []byte(value),
		})
	}
	return headers
}

// AddStandardHeaders 添加标准的消息头
func AddStandardHeaders(headers *[]kafka.Header, eventType, eventID, sourceService string) {
	if headers == nil {
		newHeaders := make([]kafka.Header, 0)
		headers = &newHeaders
	}

	// 添加事件类型
	if eventType != "" {
		*headers = append(*headers, kafka.Header{
			Key:   HeaderEventType,
			Value: []byte(eventType),
		})
	}

	// 添加事件ID
	if eventID != "" {
		*headers = append(*headers, kafka.Header{
			Key:   HeaderEventID,
			Value: []byte(eventID),
		})
	}

	// 添加源服务
	if sourceService != "" {
		*headers = append(*headers, kafka.Header{
			Key:   HeaderSourceService,
			Value: []byte(sourceService),
		})
	}
}
