﻿好的，遵照您的指示，我们来生成一份为 `notification-dispatch-service` (通知分发服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多渠道与提供商抽象**: 详细定义如何支持多种通知渠道（Push, Email, SMS, In-App）和每个渠道下的多个提供商（如Push渠道下的FCM, APNS, HMS），并实现智能路由和故障降级。
2.  **高级模板与个性化**: 引入功能更强大的模板引擎，支持多语言、条件逻辑和复杂的个性化占位符。
3.  **用户偏好与防打扰策略**: 详细定义与`user-core-service`的集成，以获取和遵守用户的精细化通知偏好，并引入频率控制、静默时段等防打扰策略。
4.  **发送状态追踪与分析**: 建立一个闭环的发送状态追踪系统，记录每次发送的详细状态，并为分析和优化提供数据支持。
5.  **批量与广播的优化**: 详细描述如何高效地处理大规模的批量和广播通知，避免对系统造成冲击。
6.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义和事件契约，并对数据模型进行优化。
7.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、高吞吐、高送达率且尊重用户偏好的企业级通知网关。

---

### CINA.CLUB - notification-dispatch-service 需求规格说明书

**版本: 2.0 (生产级定义，集成多渠道路由与防打扰策略)**  
**发布日期: 2025-06-23**  
**最后修订日期: 2025-06-23**  
**文档负责人:** [平台工程/用户增长团队负责人名称]  
**审批人:** [CTO/架构师]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台需要一个强大、统一的系统来处理与用户的各种异步通信，以提升用户参与度和留存率。`notification-dispatch-service` 的目的在于构建一个**高性能、高可靠、多渠道的通知分发中枢**。它消费来自平台各业务服务的通知请求，根据用户的精细化偏好、设备上下文和平台的防打扰策略，智能地选择最优渠道，并将消息通过第三方服务商（如FCM, SendGrid）可靠地送达用户。

#### 1.2. 服务范围
本服务 **负责**:
*   **统一通知请求接收**: 通过消息队列（首选）或内部API，接收标准化的通知请求。
*   **用户上下文与渠道决策**:
    *   获取目标用户的通知偏好、联系方式和设备Token。
    *   根据用户偏好、通知优先级和平台的防打扰策略，智能决策最佳的通知分发渠道。
*   **高级模板渲染**: 使用支持多语言、条件逻辑的模板引擎，动态生成个性化的通知内容。
*   **多渠道/多提供商分发**:
    *   封装和抽象对多种第三方推送平台 (FCM, APNS, HMS Push)、邮件服务商 (SendGrid, AWS SES)、SMS网关 (Twilio) 的API调用。
    *   支持对同一渠道的多个提供商进行故障降级。
*   **发送状态追踪与闭环**: 记录每次分发的详细状态（已发送、已送达、失败），并处理来自服务商的异步回调（如送达回执、退信）。
*   **防打扰与频率控制**: 实施全局或按用户/通知类型的频率控制和静默时段策略。
*   **协同记录**: 在分发后，协调`activity-feed-service`记录通知历史。

本服务 **不负责**:
*   **产生业务通知的逻辑** (由各业务服务负责)。
*   **存储通知的完整历史记录供用户查阅** (由 `activity-feed-service` 负责)。
*   **管理用户偏好设置的UI**。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部所有需要触达用户的微服务**: (通过消息队列) 主要交互方式。
*   **`user-core-service`**: (被本服务调用) 获取用户偏好、联系方式、设备Token。
*   **`activity-feed-service`**: (被本服务调用) 记录已分发的通知。
*   **第三方服务商**: (通过Webhook) 向本服务发送异步的发送状态回调。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`notification-dispatch-service` 是CINA.CLUB平台用户触达的“**最后一公里执行者**”和“**智能通信网关**”。它是一个高度解耦的、专注于“**如何智能且合规地发送**”而非“为何发送”的后台服务。通过消费事件总线上的事件，它将平台内部的业务逻辑变化转化为对用户的实际、精准的通知，是维持用户活跃度和平台信誉度的重要工具。

#### 2.2. 主要功能概述
*   事件驱动的、高吞吐量的通知处理。
*   基于用户偏好和防打扰策略的智能渠道路由。
*   支持多语言和复杂逻辑的高级模板渲染。
*   对多渠道、多提供商的适配与故障降级。
*   闭环的发送状态追踪与分析。

### 3. 核心流程图

#### 3.1. 处理一个高优先级通知请求
```mermaid
sequenceDiagram
    participant SourceService
    participant MQ
    participant NotificationService as NS
    participant UserCoreService as UCS
    participant TemplateEngine
    participant ProviderRouter as "Channel/Provider Router"
    participant PushProvider as "e.g., FCM"
    participant EmailProvider as "e.g., SendGrid"

    SourceService->>MQ: 1. Publish DispatchNotificationCommand
    MQ-->>NS: 2. Consume command
    
    NS->>UCS: 3. Get user preferences & contact info for target user
    UCS-->>NS: (Preferences: Push=ON, Email=ON. Contact: fcm_token, email_addr)
    
    NS->>NS: 4. **[Anti-Disturbance Check]** <br/> - Check frequency control (Redis). <br/> - Check quiet hours.
    alt Check Passed
        NS->>TemplateEngine: 5. Render notification content for Push & Email
        TemplateEngine-->>NS: (Rendered title & body)
        
        NS->>ProviderRouter: 6. **[Channel Decision]** <br/> - Priority: Push > Email.
        ProviderRouter-->>NS: (Select Push channel first)
        
        NS->>PushProvider: 7. **[Dispatch]** Send Push notification
        PushProvider-->>NS: (Status: SENT, external_id: "...")
        
        NS->>DB: 8. Log dispatch attempt (channel: PUSH, status: SENT)
        
        Note over NS: If push fails with retryable error, it will retry. <br/> If it fails permanently, it will try the next channel (Email).
    else Check Failed (e.g., frequency limit)
        NS->>DB: 9. Log dispatch attempt (status: SKIPPED_BY_POLICY)
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 通知请求处理与上下文
*   **FR4.1.1 (统一请求)**: 系统必须能处理标准化的`DispatchNotificationCommand`，无论是来自MQ还是内部API。
*   **FR4.1.2 (用户上下文获取)**: 在处理每个通知前，系统必须调用`user-core-service`获取目标用户的**精细化通知偏好**（如“允许任务提醒的Push，但关闭营销邮件”）、语言偏好、时区、以及所有渠道的联系方式/Token。

#### 4.2. 防打扰与渠道决策
*   **FR4.2.1 (频率控制)**: 系统必须支持可配置的、按`userId`和`notificationType`的频率控制。例如，“同一种类型的通知，1小时内最多发送3次”。此逻辑推荐使用Redis实现。
*   **FR4.2.2 (静默时段)**: 系统必须遵守用户设置的静默时段（如晚22:00到早8:00）。对于非紧急通知，在此期间应被暂存或丢弃。
*   **FR4.2.3 (智能渠道决策)**:
    *   系统必须有一个决策引擎，根据**通知优先级、用户偏好、渠道成本、防打扰策略检查结果**，选择一个或多个分发渠道及其执行顺序。
    *   **示例**: 对于“密码重置”这类`URGENT`通知，可以绕过静默时段，并采用`Email -> SMS`的降级策略。

#### 4.3. 高级模板引擎
*   **FR4.3.1 (功能)**: 模板引擎必须支持：
    *   **多语言**: 根据用户的语言偏好选择模板。
    *   **占位符**: 使用`DispatchNotificationCommand`中的`templateContext`数据填充。
    *   **条件逻辑**: 如 `{{if .user.is_vip}} ... {{else}} ... {{end}}`。
    *   **格式化函数**: 如日期格式化、数字格式化。
*   **FR4.3.2 (管理)**: 模板必须可以通过后台或配置文件进行版本化管理。

#### 4.4. 多提供商适配与降级
*   **FR4.4.1 (适配器模式)**: 必须为每个渠道（Push, Email, SMS）定义一个统一的接口，并为每个具体的服务商实现该接口的适配器。
*   **FR4.4.2 (故障降级)**: 支持为同一渠道配置主备提供商。当主提供商的API调用连续失败触发熔断时，自动切换到备用提供商。

#### 4.5. 闭环状态追踪
*   **FR4.5.1 (发送日志)**: 系统必须为其数据库中的每一条分发记录一个详细的日志，包含请求ID、用户ID、渠道、提供商、状态（`PENDING`, `SENT`, `FAILED`, `SKIPPED`）和外部返回的ID。
*   **FR4.5.2 (异步回调处理)**: 系统必须提供Webhook端点，接收来自服务商的异步状态回调（如`delivered`, `opened`, `bounced`），并更新对应的日志记录。
*   **FR4.5.3 (失效Token处理)**: 当收到来自APNS/FCM的“无效Token”错误时，系统必须调用`user-core-service`的API，将该设备Token标记为失效，以避免无效推送。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 消息队列事件契约 (主要入站接口)
*   **Topic**: `hina_notification_requests`
*   **Event (Protobuf)**: `DispatchNotificationCommand`
    ```protobuf
    message DispatchNotificationCommand {
      string event_id = 1;
      Target target = 2; // Can be userIds or a broadcast segment
      string notification_type = 3; // e.g., "TASK_ACCEPTED"
      string template_key = 4;
      google.protobuf.Struct template_context = 5;
      string deep_link_url = 6;
      Priority priority = 7; // URGENT, HIGH, NORMAL, LOW
    }
    ```

#### 5.2. 内部gRPC API接口 (次要入站/管理接口)
*   **Package**: `hina.vip.notification.v1`
*   **认证**: 严格的S2S认证。
*   `rpc DispatchImmediate(DispatchNotificationCommand) returns (DispatchResult)`
*   `rpc GetDispatchLogs(GetLogsRequest) returns (GetLogsResponse)`

#### 5.3. Webhook API接口 (来自第三方服务商)
*   `POST /webhooks/{providerType}`: 需签名验证。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`notification_templates`**: `id`, `template_key (UNIQUE)`, `channel_type`, `language_code`, `title_template`, `body_template`, `version`.
*   **`notification_dispatch_logs`**: `id`, `request_id`, `user_id`, `channel`, `provider`, `status`, `attempt_count`, `external_message_id`, `error_message`, `dispatched_at`. (此表会非常大，必须按时间分区)。
*   **`provider_configs`**: `id`, `provider_name`, `channel`, `priority`, `encrypted_credentials`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **吞吐量**: 能够处理平台所有业务事件产生的高峰通知请求量（目标：**>10,000 通知/分钟**）。
*   **处理延迟**: 从接收到事件到调用外部提供商API的P99延迟应 `< 100ms`。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **消息不丢失**: 依赖MQ的持久性和至少一次消费语义。
*   **可靠发送**: 对可重试的发送失败必须有健壮的、带指数退避的重试机制。

#### 7.3. 可扩展性需求
*   服务应为无状态，事件消费者组可水平扩展以增加并行处理能力。

#### 7.4. 安全性需求
*   **API密钥安全**: 所有第三方服务商的API Key/凭证必须通过Vault或KMS进行安全存储和轮换。
*   **Webhook安全**: 严格验证所有入站Webhook的签名或来源IP。
*   **内容安全**: 模板中填充的数据应经过基本的清理，防止注入。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **模板引擎**: Go内置的`html/template`或`text/template`，或功能更强大的第三方库如`Pongo2`。
*   **核心架构**: 事件驱动，通过消费消息队列来与业务服务解耦。
*   **后台任务**: 使用`Asynq`或类似的任务队列来处理重试和需要延迟执行的任务。
*   **适配器模式**: 是实现多提供商支持和故障降级的关键。

---
这份版本2.0的SRS文档为`notification-dispatch-service`构建了一个企业级的、智能化的用户触达中心。它通过引入高级的路由、模板和防打扰策略，确保每一次通知都能在正确的时间、以正确的方式、送达给正确的用户，从而在提升用户体验和实现业务目标之间取得最佳平衡。