#!/bin/bash
# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

set -euo pipefail

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

ENVIRONMENT=""
FORCE=false

show_help() {
    cat << EOF
CINA.CLUB Infrastructure Cleanup Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV    Target environment (dev|staging|prod)
    -f, --force             Force cleanup without confirmation
    -h, --help              Show this help message

EXAMPLES:
    $0 -e dev              # Clean up dev environment
    $0 -e staging -f       # Force cleanup staging

EOF
}

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }

cleanup_kubernetes() {
    local namespace="cina-club-$ENVIRONMENT"
    
    log_info "Cleaning up Kubernetes resources in namespace: $namespace"
    
    if kubectl get namespace "$namespace" > /dev/null 2>&1; then
        kubectl delete namespace "$namespace" --timeout=300s || log_warning "Namespace deletion timeout"
        log_success "Kubernetes cleanup completed"
    else
        log_info "Namespace not found: $namespace"
    fi
}

cleanup_terraform() {
    log_info "Cleaning up Terraform resources for environment: $ENVIRONMENT"
    
    cd "infra/terraform/environments/$ENVIRONMENT"
    terraform init
    terraform destroy -auto-approve || log_error "Terraform destroy failed"
    cd - > /dev/null
    
    log_success "Terraform cleanup completed"
}

main() {
    if [[ "$ENVIRONMENT" == "" ]]; then
        log_error "Environment is required"
        show_help
        exit 1
    fi
    
    if [[ "$FORCE" == false ]]; then
        echo -e "${RED}WARNING: This will destroy all resources in environment: $ENVIRONMENT${NC}"
        read -p "Are you sure? (yes/no): " confirmation
        if [[ "$confirmation" != "yes" ]]; then
            log_info "Cleanup cancelled"
            exit 0
        fi
    fi
    
    cleanup_kubernetes
    cleanup_terraform
    
    log_success "Cleanup completed for environment: $ENVIRONMENT"
}

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment) ENVIRONMENT="$2"; shift 2 ;;
        -f|--force) FORCE=true; shift ;;
        -h|--help) show_help; exit 0 ;;
        *) log_error "Unknown option: $1"; show_help; exit 1 ;;
    esac
done

main 