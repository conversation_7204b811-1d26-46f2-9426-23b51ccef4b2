import csv
import os
import sys
import time
from tqdm import tqdm

# Check if the translators library is installed, and install it if not.
try:
    import translators as ts
except ImportError:
    print("The 'translators' library is not installed. Attempting to install...")
    import subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "translators[google,bing]"])
        print("Successfully installed the 'translators' library.")
        import translators as ts
    except Exception as e:
        print(f"Failed to install the 'translators' library. Please install it manually using: pip install 'translators[google,bing]'")
        print(f"Error: {e}")
        sys.exit(1)

def translate_csv_in_batches(file_path, batch_size=1000):
    """
    Translates a CSV file in batches, updating it in place.

    Args:
        file_path (str): The path to the CSV file.
        batch_size (int): The number of rows to translate in each run.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            data = list(reader)
        print(f"Successfully read {len(data)} rows from {file_path}")
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return
    except Exception as e:
        print(f"Error reading file: {e}")
        return

    rows_to_process_indices = [i for i, row in enumerate(data) if len(row) > 3 and row[3] in ('Translation Error', 'Translation Failed')]
    
    if not rows_to_process_indices:
        print("No more rows found needing translation.")
        return

    batch_indices = rows_to_process_indices[:batch_size]
    
    print(f"Found {len(rows_to_process_indices)} rows to translate. Processing a batch of {len(batch_indices)}.")

    for i in tqdm(batch_indices, desc="Translating rows"):
        row = data[i]
        original_text = row[2]
        
        if not original_text.strip():
            data[i][3] = '' # Handle empty text
            continue

        try:
            translated_text = ts.translate_text(original_text, translator='bing', to_language='zh-CN')
            data[i][3] = translated_text
            time.sleep(0.1) # Small delay to avoid getting blocked
        except Exception as e:
            print(f"\nWarning: Could not translate row {i+1}: '{original_text}'. Error: {e}")
            data[i][3] = 'Translation Failed'

    try:
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f, quoting=csv.QUOTE_MINIMAL)
            writer.writerows(data)
        print(f"\nSuccessfully updated {file_path} with {len(batch_indices)} translations.")
    except Exception as e:
        print(f"\nError writing to file: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_to_translate = sys.argv[1]
    else:
        # Default file path if not provided as an argument
        file_to_translate = os.path.join('docs', 'architecture', 'nice classification', 'trans.csv')

    if not os.path.exists(file_to_translate):
         print(f"Error: The file '{file_to_translate}' does not exist in the current directory.")
         sys.exit(1)
         
    translate_csv_in_batches(file_to_translate) 