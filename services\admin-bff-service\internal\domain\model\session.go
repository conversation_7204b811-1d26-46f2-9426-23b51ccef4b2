/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package model

import (
	"time"
)

// AdminSession represents an admin session model
type AdminSession struct {
	ID         string    `json:"id"`
	EmployeeID string    `json:"employee_id"`
	Email      string    `json:"email"`
	Name       string    `json:"name"`
	Roles      []string  `json:"roles"`
	CreatedAt  time.Time `json:"created_at"`
	ExpiresAt  time.Time `json:"expires_at"`
	LastUsedAt time.Time `json:"last_used_at"`
	IPAddress  string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
}

// IsValid checks if the session is valid
func (s *AdminSession) IsValid() bool {
	return time.Now().Before(s.ExpiresAt)
}

// HasRole checks if the session has the specified role
func (s *AdminSession) HasRole(role string) bool {
	for _, r := range s.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasAnyRole checks if the session has any of the specified roles
func (s *AdminSession) HasAnyRole(roles ...string) bool {
	for _, role := range roles {
		if s.<PERSON>(role) {
			return true
		}
	}
	return false
}

// UpdateLastUsed updates the last used time
func (s *AdminSession) UpdateLastUsed() {
	s.LastUsedAt = time.Now()
}

// Employee represents employee information
type Employee struct {
	ID       string   `json:"id"`
	Email    string   `json:"email"`
	Name     string   `json:"name"`
	Roles    []string `json:"roles"`
	IsActive bool     `json:"is_active"`
}

// SessionRole represents session role constants
type SessionRole string

const (
	// Basic roles
	RoleViewer SessionRole = "viewer" // Read-only permissions
	RoleEditor SessionRole = "editor" // Edit permissions
	RoleAdmin  SessionRole = "admin"  // Admin permissions

	// Professional roles
	RoleContentModerator SessionRole = "content_moderator" // Content moderator
	RoleUserManager      SessionRole = "user_manager"      // User manager
	RoleBillingManager   SessionRole = "billing_manager"   // Billing manager
	RoleAnalyst          SessionRole = "analyst"           // Data analyst
	RoleSecurityOfficer  SessionRole = "security_officer"  // Security officer
	RoleSystemAdmin      SessionRole = "system_admin"      // System administrator

	// Super admin
	RoleSuperAdmin SessionRole = "super_admin" // Super administrator
)

// RoleHierarchy defines the role hierarchy
var RoleHierarchy = map[SessionRole][]SessionRole{
	RoleSuperAdmin: {
		RoleSystemAdmin, RoleSecurityOfficer, RoleAnalyst,
		RoleBillingManager, RoleUserManager, RoleContentModerator,
		RoleAdmin, RoleEditor, RoleViewer,
	},
	RoleSystemAdmin: {
		RoleSecurityOfficer, RoleAnalyst, RoleBillingManager,
		RoleUserManager, RoleContentModerator, RoleAdmin,
		RoleEditor, RoleViewer,
	},
	RoleAdmin: {
		RoleEditor, RoleViewer,
	},
	RoleEditor: {
		RoleViewer,
	},
	RoleContentModerator: {
		RoleViewer,
	},
	RoleUserManager: {
		RoleViewer,
	},
	RoleBillingManager: {
		RoleViewer,
	},
	RoleAnalyst: {
		RoleViewer,
	},
	RoleSecurityOfficer: {
		RoleViewer,
	},
}

// HasPermission checks if the session has the specified permission (considering role hierarchy)
func (s *AdminSession) HasPermission(requiredRole SessionRole) bool {
	for _, role := range s.Roles {
		sessionRole := SessionRole(role)
		if sessionRole == requiredRole {
			return true
		}

		// Check role hierarchy
		if subordinates, exists := RoleHierarchy[sessionRole]; exists {
			for _, subordinate := range subordinates {
				if subordinate == requiredRole {
					return true
				}
			}
		}
	}
	return false
}
