好的，遵照您的指示。我将为您生成一份专门针对 **`ai-assistant-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`ai-assistant-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将特别关注**Agentic Workflow的实现、工作流引擎(`pkg/workflow`)的集成、工具箱(Toolkit)的管理，以及如何编排其他微服务**来完成复杂任务，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `ai-assistant-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `ai-assistant-service-srs.md` (v2.0)
**核心架构**: Agentic Workflow + In-Process Orchestration Engine

## 1. 概述

`ai-assistant-service` 是CINA.CLUB平台的“智能中枢”。其核心挑战在于：
1.  **意图理解与规划**: 如何将用户模糊的、多模态的自然语言指令，转化为一个结构化的、可执行的步骤计划。
2.  **动态工作流编排**: 如何高效、可靠地执行这个计划，按顺序或并行地调用多个内部和外部工具（API）。
3.  **工具的统一管理**: 如何以一种可扩展、可被LLM理解的方式，管理和封装平台所有的能力（API）。
4.  **上下文与状态管理**: 如何在多轮对话中，维护一个复杂的、长生命周期的执行状态。
5.  **结果合成与响应**: 如何将多个工具调用的结果，智能地合成为一段连贯、有用的自然语言回复。

本架构设计通过采用**智能代理(Agent) + 工作流引擎(Workflow Engine)**的模式，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (Agentic Workflow)

```mermaid
graph TD
    subgraph "AI Assistant Service"
        A[gRPC/HTTP API Layer<br/>(adapter/transport)]
        B[Orchestrator<br/>(application/orchestrator)]
        C{Planner<br/>(application/planner)}
        D[Workflow Engine<br/>(pkg/workflow)]
        E{Toolkit<br/>(application/toolkit)}
        F[Session Manager<br/>(domain/session)]
        G[Redis Cache<br/>(adapter/cache)]
    end

    subgraph "External World"
        H[LLM for Planning]
        I[LLM for Synthesis]
        J[Internal Microservices (MCPs)]
    end
    
    A -- "1. New Request" --> B
    B -- "2. Get/Create Session" --> F
    F -- "Uses" --> G
    B -- "3. Start Orchestration" --> C
    C -- "4. Needs Tools & History" --> E & F
    C -- "5. Call LLM to Generate Plan" --> H
    H -- "Returns JSON Plan" --> C
    C -- "6. Hands Plan to Engine" --> D
    
    D -- "7. Sequentially Executes Nodes" --> E
    E -- "8. Finds and Calls Specific Tool" --> J & I
    
    D -- "9. After Execution, Synthesize" --> C
    C -- "10. Call LLM to Generate Final Response" --> I
    I -- "Returns Final Text" --> C
    C -- "11. Final result" --> B
    B -- "12. Stream Response" --> A
```

### 2.2 最终目录结构 (`services/ai-assistant-service/`)

```
ai-assistant-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   ├── server.go
│   │   │   └── handler.go      # gRPC Handler, 调用application/orchestrator
│   │   ├── cache/
│   │   │   └── redis_session_store.go # 实现了SessionStore接口
│   │   └── clients/            # 封装对其他微服务和LLM API的调用
│   │       ├── user_core_client.go
│   │       └── openai_client.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── session_store.go # 定义SessionStore接口
│   │   │   └── toolkit.go      # 定义Toolkit和Tool接口
│   │   ├── orchestrator.go     # ✨ 总编排器, 整个流程的驱动者 ✨
│   │   ├── planner.go          # ✨ 规划器, 负责生成和解析计划 ✨
│   │   └── toolkit/            # ✨ 工具箱, 所有可用工具的实现 ✨
│   │       ├── factory.go      # 工具工厂, 用于创建工具实例
│   │       ├── tool_search_services.go
│   │       ├── tool_create_schedule.go
│   │       └── tool_llm_call.go
│   └── domain/
│       ├── model/              # 核心领域模型
│       │   ├── session.go      # DialogState, ExecutionState
│       │   └── workflow.go     # Plan, Node, Edge (可复用pkg/workflow的定义)
│       └── session/
│           └── session_manager.go # 封装对Session的CRUD和状态转换
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The State)

*   `domain/model/`:
    *   `session.go`: 定义核心的状态对象`DialogState`。它包含了`UserID`, `SessionID`, `MessageHistory`, 以及最重要的 `ExecutionState`。
    *   `ExecutionState`: 包含当前正在执行的`Plan` (工作流定义)，当前执行到第几步 (`currentStep`)，以及所有已执行节点的输出结果 `map[nodeID]nodeOutput`。
*   `domain/session/`:
    *   `SessionManager`: 负责`DialogState`的生命周期管理。它提供`GetOrCreate`, `Save`, `Delete`等方法，其实现由`adapter/cache`提供。

### 3.2 `application/` - 应用层 (The Brains)

这是本服务的核心业务逻辑。

*   **`application/toolkit/`**: **工具箱，平台能力的体现**。
    *   `port/toolkit.go`: 定义`Tool`接口。
        ```go
        type Tool interface {
            Name() string // 唯一的工具名，如 "search_services"
            Description() string // 给LLM看的、详细的工具功能描述
            InputSchema() jsonschema.Schema // 输入参数的JSON Schema
            OutputSchema() jsonschema.Schema // 输出参数的JSON Schema
            Execute(ctx context.Context, inputs map[string]interface{}) (map[string]interface{}, error)
        }
        ```
    *   **工具实现**: 每个`tool_*.go`文件实现一个具体的`Tool`。例如，`tool_search_services.go`会封装对`search-service` gRPC客户端的调用。
    *   `factory.go`: `Toolkit`结构体，它在启动时被初始化，**注册所有可用的`Tool`实现**。它提供`GetTool(name string)`和`GetAllToolSchemas()`方法。

*   **`application/planner.go`**: **规划器，Agent的大脑**。
    *   `Planner` struct: 接收`Toolkit`作为依赖。
    *   **`GeneratePlan(ctx, query, history)` method**:
        1.  调用`toolkit.GetAllToolSchemas()`获取所有可用工具的描述和Schema。
        2.  构造一个**特殊的Prompt**给“规划师LLM”（如GPT-4, Claude 3）。这个Prompt包含：
            *   用户当前的查询。
            *   最近的对话历史。
            *   **一个详细的、格式化的工具列表**和它们的使用说明。
            *   指示LLM**必须**以一个特定的JSON格式返回一个分步骤的计划。
        3.  调用LLM API。
        4.  接收LLM返回的JSON，并将其解析为一个`workflow.Workflow`对象。
*   **`application/orchestrator.go`**: **总编排器，流程的主引擎**。
    *   `Orchestrator` struct: 接收`Planner`, `SessionManager`, `pkg/workflow.Executor`作为依赖。
    *   **`Process(ctx, request)` method**:
        1.  调用`sessionManager.GetOrCreate(sessionID)`获取或创建会话。
        2.  检查会话中是否存在一个未完成的`Plan`。
            *   **如果不存在（新对话）**:
                a. 调用`planner.GeneratePlan()`来生成一个新的`Plan`。
                b. 将新`Plan`保存到会话的`ExecutionState`中。
            *   **如果存在（多轮对话的继续）**:
                a. 直接使用会话中已有的`Plan`。
        3.  **核心**: 创建一个`pkg/workflow.Executor`实例，并**将`application/toolkit`中的所有`Tool`实现，作为`NodeExecutor`注册进去**。
            ```go
            // 伪代码
            workflowExecutor := workflow.NewExecutor()
            for _, tool := range toolkit.GetAllTools() {
                // 将Tool的Execute方法适配为NodeExecutor接口
                workflowExecutor.RegisterNode(tool.Name(), tool) 
            }
            ```
        4.  调用`workflowExecutor.Run(ctx, plan, executionState)`来执行工作流。
        5.  `Run`方法可能是阻塞的（对于简单流程），也可能是非阻塞的（如果遇到需要用户输入的节点，则保存状态并立即返回）。
        6.  工作流执行完毕或暂停后，调用`planner`中的**结果合成**方法，将最终的`ExecutionState`和用户请求打包，再次调用LLM（可能是另一个更便宜的“合成师LLM”），生成最终的自然语言回复。
        7.  将回复以流式方式返回给`adapter/grpc`。
        8.  更新并保存最终的`DialogState`。

### 3.3 `adapter/` - 适配层 (The Bridge to I/O)

*   `adapter/transport/` (`grpc/`, `http/`):
    *   实现API接口，接收外部请求，将其转换为内部模型，并调用`application.Orchestrator`。
    *   负责处理流式响应的HTTP `text/event-stream`逻辑。
*   `adapter/cache/`:
    *   `redis_session_store.go`: 实现`port.SessionStore`接口，使用Redis来持久化`DialogState`的JSON序列化字符串。**这是保证服务无状态的关键**。
*   `adapter/clients/`:
    *   封装所有对外部API的调用，包括对其他微服务的gRPC客户端和对OpenAI等第三方LLM的HTTP客户端。这些客户端都应集成`pkg/tracing`和`pkg/metrics`。

## 4. `pkg/workflow`的集成方式

`pkg/workflow`在本服务中被用作一个**进程内(in-process)的库**，而不是一个独立的服务。
*   **注册**: `Orchestrator`在处理每个请求时，动态地将`Toolkit`中的工具注册为`pkg/workflow`引擎的节点执行器。
*   **执行**: `Orchestrator`调用`workflow.Executor.Run()`来同步（或异步地、分步地）执行工作流。
*   **状态**: 工作流的执行状态（`ExecutionState`）由`Orchestrator`管理，并存储在`DialogState`中，最终持久化到Redis。当一个需要用户输入的工作流暂停后，下次用户回复时，`Orchestrator`会从Redis加载状态，并从上次暂停的节点继续执行。

## 5. 总结

本架构设计通过以下关键点来构建一个强大的`ai-assistant-service`：
1.  **职责分离**:
    *   `Planner`负责“思考和计划”。
    *   `Toolkit`负责定义和实现“能力”。
    *   `pkg/workflow`负责“执行”计划。
    *   `Orchestrator`负责“串联”所有部分，驱动整个流程。
2.  **LLM作为规划引擎**: 利用LLM的强大推理能力，将非结构化语言转换为结构化工作流，实现了极高的灵活性。
3.  **无状态服务**: 通过将所有会话状态外置到Redis，服务本身可以水平扩展以应对高并发。
4.  **可扩展的工具箱**: 添加一个新的平台能力，只需要实现`Tool`接口并在`Toolkit`中注册即可，无需修改核心编排逻辑。

这种架构使得`ai-assistant-service`能够优雅地处理从简单的问答到需要多步、跨服务调用的复杂任务，成为名副其实的平台“智能中枢”。