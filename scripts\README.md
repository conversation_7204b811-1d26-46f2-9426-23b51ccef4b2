# CINA.CLUB 开发脚本

这个目录包含了 CINA.CLUB 平台开发所需的各种自动化脚本。

## 目录结构

```
scripts/
├── lib/                    # 共享函数库
│   └── helpers.sh         # 通用辅助函数
├── gen/                   # 代码生成脚本
│   ├── proto.sh          # Protobuf 代码生成
│   └── core.sh           # 核心库编译
├── db/                    # 数据库脚本
│   └── migrate.sh        # 数据库迁移
├── setup/                 # 环境设置脚本
│   └── dev-env.sh        # 开发环境设置
└── ci/                    # CI/CD 脚本
    └── lint.sh           # 代码检查
```

## 快速开始

### 1. 设置开发环境

```bash
# 完整设置
make setup

# 或者直接运行脚本
./scripts/setup/dev-env.sh

# 最小化设置
./scripts/setup/dev-env.sh --minimal
```

### 2. 生成代码

```bash
# 生成所有代码
make gen

# 单独生成 Protobuf 代码
make gen-proto

# 单独编译核心库
make gen-core

# 为特定平台编译核心库
make gen-core-android    # Android AAR
make gen-core-ios        # iOS xcframework
make gen-core-wasm       # WebAssembly
make gen-core-windows    # Windows DLL
```

### 3. 代码检查

```bash
# 运行所有检查
make lint

# 自动修复问题
make lint-fix

# 快速检查
make lint-fast
```

### 4. 数据库迁移

```bash
# 运行迁移
make db-migrate SERVICE=user-core-service

# 回滚迁移
make db-migrate-down SERVICE=user-core-service

# 查看状态
make db-status SERVICE=user-core-service
```

## 脚本详细说明

### 代码生成脚本

#### `gen/proto.sh` - Protobuf 代码生成

生成所有目标语言的 gRPC/Protobuf 客户端代码。

```bash
# 基本用法
./scripts/gen/proto.sh

# 指定目标语言
./scripts/gen/proto.sh --target go,typescript

# 清理后重新生成
./scripts/gen/proto.sh --clean

# 详细输出
./scripts/gen/proto.sh --verbose
```

**支持的目标语言：**
- `go` - Go 语言
- `typescript` - TypeScript
- `swift` - Swift
- `kotlin` - Kotlin
- `python` - Python
- `all` - 所有语言（默认）

#### `gen/core.sh` - 核心库编译

将 `/core` 目录编译为各平台所需的库文件。

```bash
# 编译所有平台
./scripts/gen/core.sh

# 指定平台
./scripts/gen/core.sh --target android,ios

# 优化编译
./scripts/gen/core.sh --optimize

# 清理后编译
./scripts/gen/core.sh --clean --target all
```

**支持的平台：**
- `android` - Android AAR 文件
- `ios` - iOS xcframework
- `wasm` - WebAssembly 模块
- `windows` - Windows DLL
- `all` - 所有平台（默认）

### 数据库脚本

#### `db/migrate.sh` - 数据库迁移

管理微服务的数据库迁移。

```bash
# 运行所有待执行的迁移
./scripts/db/migrate.sh user-core-service up

# 回滚一个迁移
./scripts/db/migrate.sh user-core-service down --steps 1

# 强制设置版本
./scripts/db/migrate.sh user-core-service force --version 5

# 创建新迁移
./scripts/db/migrate.sh user-core-service create --name add_users_table

# 查看迁移状态
./scripts/db/migrate.sh user-core-service status

# 指定环境
./scripts/db/migrate.sh user-core-service up --env staging
```

### 环境设置脚本

#### `setup/dev-env.sh` - 开发环境设置

一键配置完整的多平台开发环境。

```bash
# 完整设置
./scripts/setup/dev-env.sh

# 最小化设置
./scripts/setup/dev-env.sh --minimal

# 指定平台
./scripts/setup/dev-env.sh --platforms android,web

# 跳过某些步骤
./scripts/setup/dev-env.sh --skip-docker --skip-generation
```

### CI/CD 脚本

#### `ci/lint.sh` - 代码检查

运行所有支持语言的代码规范检查。

```bash
# 运行所有检查
./scripts/ci/lint.sh

# 自动修复问题
./scripts/ci/lint.sh --fix

# 快速检查
./scripts/ci/lint.sh --fast

# 详细输出
./scripts/ci/lint.sh --verbose
```

**支持的检查：**
- Go 代码（golangci-lint）
- TypeScript 代码（ESLint）
- Protobuf 文件（buf lint）
- Shell 脚本（ShellCheck）

## 环境要求

### Windows 用户

这些脚本需要在 Unix-like 环境中运行。Windows 用户可以使用：

1. **WSL2（推荐）**
   ```bash
   wsl --install Ubuntu
   ```

2. **Git Bash**
   - 随 Git for Windows 安装
   - 功能有限，建议使用 WSL2

3. **Docker 容器**
   ```bash
   docker run -it --rm -v ${PWD}:/workspace ubuntu:latest bash
   ```

### 必需工具

- **Go** 1.22+
- **Node.js** 18+
- **Docker** & Docker Compose
- **Git**

### 可选工具

这些工具会自动安装（如果缺失）：

- `buf` - Protobuf 工具链
- `golangci-lint` - Go 代码检查
- `migrate` - 数据库迁移工具
- `shellcheck` - Shell 脚本检查
- `pnpm` - 快速包管理器

## 环境变量

所有脚本都支持以下环境变量：

- `DEBUG=1` - 启用调试输出
- `DRY_RUN=1` - 显示命令但不执行
- `SKIP_INTERACTIVE=1` - 跳过交互式提示

## 故障排除

### 权限问题

```bash
# 给脚本添加执行权限
chmod +x scripts/**/*.sh
```

### 路径问题

所有脚本都应该从项目根目录执行：

```bash
# 正确
./scripts/gen/proto.sh

# 错误
cd scripts/gen && ./proto.sh
```

### 工具缺失

运行环境检查：

```bash
make check-tools
```

或者运行完整的环境设置：

```bash
make setup
```

## 贡献指南

### 添加新脚本

1. 在适当的子目录中创建脚本
2. 使用 `scripts/lib/helpers.sh` 中的函数
3. 添加版权声明
4. 提供详细的使用说明
5. 在 `Makefile` 中添加对应的命令

### 脚本规范

- 使用 `#!/usr/bin/env bash` shebang
- 设置 `set -euo pipefail`
- 导入并使用 `helpers.sh` 函数
- 提供 `--help` 选项
- 支持调试和干运行模式
- 添加适当的错误处理

### 测试

在提交前测试脚本：

```bash
# 语法检查
make lint

# 干运行测试
DRY_RUN=1 ./scripts/your-script.sh

# 调试模式
DEBUG=1 ./scripts/your-script.sh
```

## 许可证

Copyright (c) 2025 Cina.Club. All rights reserved. 