﻿好的，遵照您的指示，我们来生成一份为 `cloud-sync-service` (云同步服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **同步协议与算法**: 详细定义客户端与服务端之间的同步协议，引入更健壮的版本向量（Vector Clocks）或类似机制来处理并发修改和冲突检测，而不仅仅是时间戳。
2.  **数据分块与去重**: 明确对大型数据条目进行内容定义分块（Content-Defined Chunking）和去重的逻辑，以节省存储和带宽。
3.  **实时通知与轮询结合**: 明确如何通过WebSocket推送（来自`notification-dispatch-service`）实现近实时同步，并结合后台轮询作为容错。
4.  **端到端加密(E2EE)下的元数据处理**: 详细阐述哪些元数据可以明文存储以支持服务端逻辑，以及如何处理这些元数据可能带来的隐私泄露风险。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、高效、且能应对复杂并发同步场景的端到端加密数据同步后端。

---

### CINA.CLUB - cloud-sync-service 需求规格说明书

**版本: 2.0 (生产级定义，集成高级同步协议)**  
**发布日期: 2025-06-21**  
**最后修订日期: 2025-06-21**  
**文档负责人:** [数据同步/平台工程团队负责人名称]  
**审批人:** [CTO/首席隐私官(CPO)]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心同步协议与流程](#3-核心同步协议与流程)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台的核心功能，如个人知识库(PKB)和个人记忆(PM)，都基于**端到端加密(E2EE)**来保护用户隐私。为了在不牺牲隐私的前提下，提供无缝的跨设备体验和可靠的数据备份能力，`cloud-sync-service` 应运而生。其目的在于提供一个**安全的、服务器端零知识的同步协调服务**，让用户可以高效、可靠地备份、恢复和同步他们的加密数据。

#### 1.2. 服务范围
本服务 **负责**:
*   **加密数据块(Chunk)存储协调**: 接收来自客户端的加密数据块，并通过`file-storage-service`将其可靠地存储到对象存储中。
*   **元数据与版本管理**: 管理与这些加密数据块相关的元数据，包括**版本向量(Version Vector)**、来源设备、校验和、以及与业务条目(如PKB Item)的关联。
*   **增量变更同步**: 提供API，允许客户端查询自上次同步以来其他设备产生的变更，并协调下载。
*   **内容定义分块(CDC)与去重**: 支持对大型数据进行分块上传，并通过校验和在服务端实现数据块级别的去重，以节省存储和带宽。
*   **冲突检测**: 基于版本向量，精确检测并发修改导致的编辑冲突。
*   **实时同步通知**: 在数据发生变更时，触发通知，以便在线的客户端能近实时地拉取更新。
*   **数据清理与垃圾回收**: 管理已删除数据的清理（软删除和硬删除）。

本服务 **不负责**:
*   **加密或解密任何用户数据内容**: 加解密操作**完全在客户端**使用用户的DEK进行。
*   **管理用户的加密密钥(DEK)**或主密码。
*   **理解加密数据的具体内容或结构**: 对本服务而言，用户数据是不透明的二进制块。
*   **解决冲突的业务逻辑**: 本服务只负责**检测**冲突并向客户端提供冲突的双方版本信息，具体的合并逻辑由客户端实现。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (唯一)**: 负责本地数据的E2EE加密/解密，并调用本服务API进行上传/下载。
*   **`notification-dispatch-service`**: (被本服务调用) 发送“有新数据需要同步”的静默推送。
*   **`file-storage-service`**: (被本服务调用) 获取预签名URL以上传/下载加密数据块。
*   **CINA.CLUB平台管理员**: 查看服务状态和匿名的存储用量统计。

#### 1.4. 定义与缩略语
*   **E2EE**: End-to-End Encryption (端到端加密)。
*   **Chunk**: 加密后的数据块。
*   **Version Vector**: 版本向量。一个映射 `(deviceId -> versionCounter)`，用于追踪每个设备对一个数据条目的修改次数，是比时间戳更可靠的并发控制机制。
*   **CDC**: Content-Defined Chunking，内容定义分块，一种能稳定地将文件分割成块的算法。
*   **LWW**: Last Write Wins，一种简单的冲突解决策略，本服务应避免依赖它，而是将冲突交给客户端。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`cloud-sync-service` 是CINA.CLUB隐私保护架构的“**安全保险库**”和“**同步协调器**”。它作为一个“可信的盲人仓库管理员”，为用户的E2EE数据提供存储空间和元数据索引，但无法窥探其中内容。它将复杂的、有状态的同步逻辑从客户端中抽离出来，为平台所有需要E2EE数据同步的功能提供了统一、可靠的基础设施。

#### 2.2. 主要功能概述
*   基于版本向量的高级并发控制与冲突检测。
*   基于内容定义分块的数据去重，实现高效存储。
*   结合静默推送和后台轮询的混合式同步。
*   服务器端零知识，保证用户数据隐私。

### 3. 核心同步协议与流程

#### 3.1. 客户端A上传变更
```mermaid
sequenceDiagram
    participant ClientA
    participant CloudSyncService as CSS
    participant FileStorageService as FSS
    participant NotificationService as NS

    ClientA->>ClientA: 1. User edits a PKB item. <br/> - Encrypt data. <br/> - Split into chunks via CDC. <br/> - Increment own version in Vector.
    
    ClientA->>CSS: 2. POST /sync/push (items: [{itemId, newVersionVector, chunks:[{checksum, size}] }])
    
    CSS->>CSS: 3. **[Concurrency Check]** <br/> - Get current server VersionVector for itemId. <br/> - Compare with client's newVersionVector.
    
    alt No Conflict
        CSS-->>ClientA: 4a. 200 OK - { chunks_to_upload: [{checksum, upload_url}] } <br/> (CSS gets upload URLs from FSS)
        ClientA->>FSS: 5a. Uploads required chunks directly
        ClientA->>CSS: 6a. POST /sync/push/finalize (itemId, newVersionVector)
        CSS->>DB: 7a. **Atomically** update item's metadata (new vector, chunk list)
        CSS->>NS: 8a. Request to send silent push to User's other devices
    else Conflict Detected
        CSS-->>ClientA: 4b. **409 Conflict** - { server_version_vector, conflicting_chunks_download_urls }
        Note over ClientA: Client must now merge changes and re-push.
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 加密数据块(Chunk)管理
*   **FR4.1.1 (内容定义分块)**: 客户端在上传前，必须使用内容定义分块算法（如基于Rabin-Karp的滚动哈希）将加密后的数据分割成多个Chunk。
*   **FR4.1.2 (去重上传)**:
    *   客户端在发起`push`请求时，只提交每个Chunk的**校验和(checksum)**和大小。
    *   服务端检查这些checksum，如果某些Chunk已存在于对象存储中（通过查询`chunks`元数据表），则无需重复上传。
    *   服务端只为那些不存在的Chunk生成预签名上传URL。
*   **FR4.1.3 (事务性最终确认)**: 客户端在上传完所有必需的Chunk后，必须调用一个`finalize`接口。服务端在此调用中，才原子性地更新数据条目（如PKB Item）的元数据，将其指向新的Chunk列表和新的版本向量。

#### 4.2. 并发控制与冲突检测
*   **FR4.2.1 (版本向量)**:
    *   系统必须为每个同步的数据条目（Item）维护一个版本向量。
    *   版本向量是一个`map[deviceId]int64`，记录了每个设备对该条目的修改计数。
*   **FR4.2.2 (冲突检测逻辑)**:
    *   当客户端A推送一个变更时，它会带上它所知道的最新版本向量 `V_client`。
    *   服务端拥有权威的版本向量 `V_server`。
    *   **无冲突**: 如果对于所有设备D，`V_client[D] <= V_server[D]`，且至少有一个是严格小于。
    *   **冲突**: 如果存在某个设备D，`V_client[D] > V_server[D]`，同时又存在另一个设备E，`V_client[E] < V_server[E]`。这表示客户端A在修改时，并不知道设备E已经做出了更新。
*   **FR4.3.3 (冲突处理)**: 检测到冲突时，服务端必须拒绝本次`push`，并返回`409 Conflict`状态码，响应体中需包含服务端的权威版本向量和冲突版本的数据块下载信息，供客户端进行三方合并。

#### 4.3. 增量变更同步 (Pull)
*   **FR4.3.1 (Pull请求)**: 客户端定期或在收到静默推送后，调用`POST /sync/pull`接口，请求中包含其本地所有条目的版本向量快照。
*   **FR4.3.2 (变更计算)**: 服务端接收到请求后，遍历客户端提交的版本向量快照，与服务端的权威版本进行比较，计算出需要下发的变更列表（包括新建、更新和删除的条目）。
*   **FR4.3.3 (下载协调)**: 对于需要下载的条目，响应中包含其Chunk列表和每个Chunk的预签名下载URL。

#### 4.4. 实时同步通知
*   **FR4.4.1 (触发)**: 当一个`push/finalize`成功处理后，如果该变更是由设备A产生的，本服务必须调用`notification-dispatch-service`。
*   **FR4.4.2 (静默推送)**: 请求向该用户的**所有其他在线设备**（B, C...）发送一条高优先级的、**静默的**数据推送(Silent Push Notification)。
*   **FR4.4.3 (客户端响应)**: 客户端收到静默推送后，应立即在后台发起一次`pull`同步请求。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端RESTful API接口
*   **版本**: `/api/v1/sync`
*   **认证**: 用户JWT, `X-Device-ID`头。
*   **核心端点**:
    *   `POST /push`: 发起一次变更推送。
        *   Request: `PushRequest { items: [{ item_id, base_version_vector, new_version_vector, chunks: [{checksum, size}] }] }`
        *   Response: `PushResponse { results: [{ item_id, status, chunks_to_upload: [{checksum, upload_url}] }] }` or `409 Conflict`
    *   `POST /push/finalize`: 确认上传并最终提交变更。
        *   Request: `FinalizeRequest { items: [{ item_id, new_version_vector }] }`
    *   `POST /pull`: 拉取增量变更。
        *   Request: `PullRequest { since_sequence_point?, known_version_vectors: map<string, VersionVector> }`
        *   Response: `PullResponse { changes: [...], new_sequence_point }`
    *   `POST /bootstrap`: 新设备首次全量同步。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`sync_items` (数据条目元数据)**:
    *   `id (PK)`, `user_id (FK, INDEX)`, `item_id (VARCHAR, INDEX)`, `item_type`
    *   `version_vector (JSONB)`: 存储版本向量。
    *   `is_deleted (BOOLEAN)`
    *   `last_modified_at (TIMESTAMPTZ, INDEX)`
    *   `last_modified_by_device_id (VARCHAR)`
*   **`chunks` (数据块元数据，用于去重)**:
    *   `checksum (VARCHAR(64), PK)`: SHA256哈希值。
    *   `size_bytes (BIGINT)`
    *   `ref_count (INT)`: 被多少个Item引用。
    *   `storage_key (TEXT)`: 在对象存储中的位置。
*   **`item_chunks` (关联表)**:
    *   `item_id`, `chunk_checksum`, `order (INT)`。复合主键。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: `push`和`pull`接口的P99延迟应 `< 200ms` (不含对象存储操作)。
*   **吞吐量**: 能处理大量并发的同步请求，特别是当大量用户同时上线时。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **数据完整性**: 元数据更新必须是事务性的。使用Checksum保证数据块在传输和存储过程中的完整性。

#### 7.3. 可扩展性需求
*   服务可水平扩展。元数据DB和对象存储必须支持水平扩展。

#### 7.4. 安全性需求 (最高优先级)
*   **服务器零知识**: 本服务绝不能访问或存储用户DEK或明文数据。
*   **API安全**: 所有API必须通过用户JWT强认证。
*   **对象存储安全**: 使用预签名URL机制，存储桶私有，URL有效期短。
*   **元数据隐私**: 虽然元数据是明文，但设计上应尽量减少其中可能暴露的隐私信息。例如，不存储文件名。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。其JSONB类型非常适合存储版本向量。
*   **分块算法**: 推荐使用 [FastCDC](https://github.com/fastcdc/fastcdc) 算法的Go实现。
*   **并发控制**: `sync_items`表的更新必须使用乐观锁（比较`version_vector`）或悲观锁来处理高并发。
*   **实时通知**: 与`notification-dispatch-service`的紧密集成是实现近实时同步体验的关键。

---
这份版本2.0的SRS文档为`cloud-sync-service`构建了一个生产级的、高度安全和高效的E2EE数据同步后端。它通过引入版本向量和内容定义分块等高级同步协议，能够优雅地处理复杂的并发场景，为用户提供无缝、可靠的跨设备数据体验，同时将隐私保护置于最高优先级。