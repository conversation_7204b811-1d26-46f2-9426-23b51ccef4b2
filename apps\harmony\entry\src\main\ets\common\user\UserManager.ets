/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { hilog } from '@kit.PerformanceAnalysisKit';
import { StorageManager } from '../storage/StorageManager';
import { CryptoManager } from '../crypto/CryptoManager';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  nickname?: string;
  level: number;
  memberType: 'free' | 'premium' | 'enterprise';
  createdAt: string;
  lastLoginAt: string;
}

/**
 * 登录请求接口
 */
export interface LoginRequest {
  phone?: string;
  email?: string;
  password: string;
  captcha?: string;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  success: boolean;
  user?: UserInfo;
  accessToken?: string;
  refreshToken?: string;
  message?: string;
}

/**
 * 用户管理器
 * 
 * 负责用户认证、状态管理、用户信息缓存等功能
 */
export class UserManager {
  private static readonly TAG = 'UserManager';
  
  private currentUser: UserInfo | null = null;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private isLoggedIn: boolean = false;
  
  private readonly storageManager: StorageManager;
  private readonly cryptoManager: CryptoManager;
  private readonly networkManager: NetworkManager;

  constructor(
    storageManager: StorageManager,
    cryptoManager: CryptoManager,
    networkManager: NetworkManager
  ) {
    this.storageManager = storageManager;
    this.cryptoManager = cryptoManager;
    this.networkManager = networkManager;
  }

  /**
   * 检查登录状态
   */
  async checkLoginStatus(): Promise<boolean> {
    try {
      // 从安全存储中获取令牌
      const storedTokens = await this.loadStoredTokens();
      if (!storedTokens.accessToken) {
        return false;
      }

      // 验证令牌有效性
      const isValid = await this.validateToken(storedTokens.accessToken);
      if (!isValid) {
        // 尝试刷新令牌
        if (storedTokens.refreshToken) {
          return await this.refreshAccessToken(storedTokens.refreshToken);
        }
        return false;
      }

      // 恢复用户状态
      this.accessToken = storedTokens.accessToken;
      this.refreshToken = storedTokens.refreshToken;
      this.isLoggedIn = true;
      
      // 加载用户信息
      await this.loadUserInfo();
      
      hilog.info(0x0000, UserManager.TAG, 'User login status verified');
      return true;
    } catch (error) {
      hilog.error(0x0000, UserManager.TAG, `Check login status failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 用户登录
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      hilog.info(0x0000, UserManager.TAG, 'Starting user login');

      // 发送登录请求
      const response = await this.networkManager.post<LoginResponse>('/auth/login', request);
      
      if (response.success && response.user && response.accessToken) {
        // 保存用户信息和令牌
        this.currentUser = response.user;
        this.accessToken = response.accessToken;
        this.refreshToken = response.refreshToken || null;
        this.isLoggedIn = true;

        // 持久化存储
        await this.saveUserTokens();
        await this.saveUserInfo();

        hilog.info(0x0000, UserManager.TAG, `User logged in successfully: ${response.user.username}`);
        return response;
      } else {
        hilog.warn(0x0000, UserManager.TAG, `Login failed: ${response.message}`);
        return response;
      }
    } catch (error) {
      hilog.error(0x0000, UserManager.TAG, `Login error: ${error.message}`);
      return {
        success: false,
        message: `登录失败: ${error.message}`
      };
    }
  }

  /**
   * 用户注销
   */
  async logout(): Promise<void> {
    try {
      hilog.info(0x0000, UserManager.TAG, 'Starting user logout');

      // 通知服务器注销
      if (this.accessToken) {
        try {
          await this.networkManager.post('/auth/logout', {});
        } catch (error) {
          hilog.warn(0x0000, UserManager.TAG, `Server logout failed: ${error.message}`);
        }
      }

      // 清理本地状态
      await this.clearUserData();
      
      hilog.info(0x0000, UserManager.TAG, 'User logged out successfully');
    } catch (error) {
      hilog.error(0x0000, UserManager.TAG, `Logout error: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): UserInfo | null {
    return this.currentUser;
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId(): string | null {
    return this.currentUser?.id || null;
  }

  /**
   * 检查是否已登录
   */
  isUserLoggedIn(): boolean {
    return this.isLoggedIn && this.currentUser !== null;
  }

  /**
   * 获取访问令牌
   */
  getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * 获取刷新令牌
   */
  getRefreshToken(): string | null {
    return this.refreshToken;
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(updates: Partial<UserInfo>): Promise<void> {
    if (!this.currentUser) {
      throw new Error('No current user');
    }

    try {
      // 发送更新请求
      const response = await this.networkManager.put<UserInfo>('/user/profile', updates);
      
      // 更新本地用户信息
      this.currentUser = { ...this.currentUser, ...response };
      
      // 保存更新后的用户信息
      await this.saveUserInfo();
      
      hilog.info(0x0000, UserManager.TAG, 'User info updated successfully');
    } catch (error) {
      hilog.error(0x0000, UserManager.TAG, `Update user info failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.currentUser = null;
    this.accessToken = null;
    this.refreshToken = null;
    this.isLoggedIn = false;
    hilog.info(0x0000, UserManager.TAG, 'UserManager cleaned up');
  }

  // ===================== 私有方法 =====================

  /**
   * 加载存储的令牌
   */
  private async loadStoredTokens(): Promise<{ accessToken: string | null; refreshToken: string | null }> {
    try {
      const accessToken = await this.storageManager.getSecureString('access_token');
      const refreshToken = await this.storageManager.getSecureString('refresh_token');
      return { accessToken, refreshToken };
    } catch (error) {
      hilog.warn(0x0000, UserManager.TAG, `Load stored tokens failed: ${error.message}`);
      return { accessToken: null, refreshToken: null };
    }
  }

  /**
   * 保存用户令牌
   */
  private async saveUserTokens(): Promise<void> {
    if (this.accessToken) {
      await this.storageManager.setSecureString('access_token', this.accessToken);
    }
    if (this.refreshToken) {
      await this.storageManager.setSecureString('refresh_token', this.refreshToken);
    }
  }

  /**
   * 保存用户信息
   */
  private async saveUserInfo(): Promise<void> {
    if (this.currentUser) {
      const userInfoJson = JSON.stringify(this.currentUser);
      await this.storageManager.setString('user_info', userInfoJson);
    }
  }

  /**
   * 加载用户信息
   */
  private async loadUserInfo(): Promise<void> {
    try {
      const userInfoJson = await this.storageManager.getString('user_info');
      if (userInfoJson) {
        this.currentUser = JSON.parse(userInfoJson) as UserInfo;
      }
    } catch (error) {
      hilog.warn(0x0000, UserManager.TAG, `Load user info failed: ${error.message}`);
    }
  }

  /**
   * 验证令牌有效性
   */
  private async validateToken(token: string): Promise<boolean> {
    try {
      const response = await this.networkManager.get('/auth/validate', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      return response.valid === true;
    } catch (error) {
      hilog.warn(0x0000, UserManager.TAG, `Token validation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 刷新访问令牌
   */
  private async refreshAccessToken(refreshToken: string): Promise<boolean> {
    try {
      const response = await this.networkManager.post<LoginResponse>('/auth/refresh', {
        refreshToken: refreshToken
      });

      if (response.success && response.accessToken) {
        this.accessToken = response.accessToken;
        this.refreshToken = response.refreshToken || this.refreshToken;
        this.isLoggedIn = true;

        await this.saveUserTokens();
        hilog.info(0x0000, UserManager.TAG, 'Access token refreshed successfully');
        return true;
      }

      return false;
    } catch (error) {
      hilog.error(0x0000, UserManager.TAG, `Refresh token failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 清理用户数据
   */
  private async clearUserData(): Promise<void> {
    // 清理内存状态
    this.currentUser = null;
    this.accessToken = null;
    this.refreshToken = null;
    this.isLoggedIn = false;

    // 清理存储数据
    await this.storageManager.remove('access_token');
    await this.storageManager.remove('refresh_token');
    await this.storageManager.remove('user_info');
  }
} 