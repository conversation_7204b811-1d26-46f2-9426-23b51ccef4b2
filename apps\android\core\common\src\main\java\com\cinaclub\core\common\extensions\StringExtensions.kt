/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.core.common.extensions

import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern

/**
 * String extension functions for common operations.
 */

/**
 * Checks if string is a valid email address.
 */
fun String.isValidEmail(): <PERSON><PERSON>an {
    val emailPattern = Pattern.compile(
        "[a-zA-Z0-9\\+\\.\\_\\%\\-\\+]{1,256}" +
                "\\@" +
                "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
                "(" +
                "\\." +
                "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
                ")+"
    )
    return emailPattern.matcher(this).matches()
}

/**
 * Checks if string is a valid phone number.
 */
fun String.isValidPhoneNumber(): Bo<PERSON>an {
    val phonePattern = Pattern.compile("^[+]?[0-9]{10,15}$")
    return phonePattern.matcher(this.replace("\\s".toRegex(), "")).matches()
}

/**
 * Truncates string to specified length with ellipsis.
 */
fun String.truncate(maxLength: Int): String {
    return if (length <= maxLength) {
        this
    } else {
        "${take(maxLength - 3)}..."
    }
}

/**
 * Converts string to SHA-256 hash.
 */
fun String.toSHA256(): String {
    val digest = MessageDigest.getInstance("SHA-256")
    val hashBytes = digest.digest(toByteArray())
    return hashBytes.joinToString("") { "%02x".format(it) }
}

/**
 * Converts timestamp string to formatted date.
 */
fun String.toFormattedDate(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
    return try {
        val timestamp = toLongOrNull() ?: return this
        val date = Date(timestamp)
        val formatter = SimpleDateFormat(pattern, Locale.getDefault())
        formatter.format(date)
    } catch (e: Exception) {
        this
    }
}

/**
 * Masks sensitive information (like credit card numbers).
 */
fun String.maskSensitive(visibleChars: Int = 4): String {
    return if (length <= visibleChars) {
        "*".repeat(length)
    } else {
        "*".repeat(length - visibleChars) + takeLast(visibleChars)
    }
}

/**
 * Removes HTML tags from string.
 */
fun String.stripHtml(): String {
    return replace(Regex("<[^>]*>"), "")
}

/**
 * Capitalizes first letter of each word.
 */
fun String.toTitleCase(): String {
    return split(" ").joinToString(" ") { word ->
        word.lowercase().replaceFirstChar { 
            if (it.isLowerCase()) it.titlecase() else it.toString() 
        }
    }
}

/**
 * Checks if string contains only alphanumeric characters.
 */
fun String.isAlphanumeric(): Boolean {
    return matches(Regex("^[a-zA-Z0-9]+$"))
}

/**
 * Converts camelCase to snake_case.
 */
fun String.toSnakeCase(): String {
    return replace(Regex("([a-z])([A-Z])"), "$1_$2").lowercase()
}

/**
 * Converts snake_case to camelCase.
 */
fun String.toCamelCase(): String {
    return split("_").mapIndexed { index, word ->
        if (index == 0) word.lowercase()
        else word.lowercase().replaceFirstChar { it.uppercase() }
    }.joinToString("")
} 