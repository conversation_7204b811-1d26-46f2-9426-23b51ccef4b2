/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 11:00:00
Modified: 2025-01-21 11:00:00
*/

package instrument

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// RedisHook implements redis.Hook interface to provide OpenTelemetry tracing
// and structured logging for Redis operations
type RedisHook struct {
	tracer trace.Tracer
	logger *slog.Logger
}

// NewRedisHook creates a new Redis hook with observability integration
func NewRedisHook(tracer trace.Tracer, logger *slog.Logger) *RedisHook {
	return &RedisHook{
		tracer: tracer,
		logger: logger,
	}
}

// DialHook is called when a new connection is established
func (h *RedisHook) DialHook(next redis.DialHook) redis.DialHook {
	return func(ctx context.Context, network, addr string) (net.Conn, error) {
		if h.tracer == nil {
			return next(ctx, network, addr)
		}

		// Start a span for the connection attempt
		spanCtx, span := h.tracer.Start(ctx, "db.redis.dial",
			trace.WithSpanKind(trace.SpanKindClient),
			trace.WithAttributes(
				attribute.String("db.system", "redis"),
				attribute.String("net.transport", network),
				attribute.String("net.peer.name", parseHost(addr)),
				attribute.String("net.peer.port", parsePort(addr)),
			),
		)
		defer span.End()

		// Log connection attempt
		if h.logger != nil {
			h.logger.Debug("Redis connection attempt",
				"network", network,
				"addr", addr,
			)
		}

		start := time.Now()
		conn, err := next(spanCtx, network, addr)
		duration := time.Since(start)

		// Set span attributes
		span.SetAttributes(
			attribute.Int64("db.connection_duration_ms", duration.Milliseconds()),
		)

		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())

			if h.logger != nil {
				h.logger.Error("Redis connection failed",
					"error", err,
					"network", network,
					"addr", addr,
					"duration_ms", duration.Milliseconds(),
				)
			}
		} else {
			span.SetStatus(codes.Ok, "")

			if h.logger != nil {
				h.logger.Debug("Redis connection established",
					"network", network,
					"addr", addr,
					"duration_ms", duration.Milliseconds(),
				)
			}
		}

		return conn, err
	}
}

// ProcessHook is called for each Redis command
func (h *RedisHook) ProcessHook(next redis.ProcessHook) redis.ProcessHook {
	return func(ctx context.Context, cmd redis.Cmder) error {
		if h.tracer == nil {
			return next(ctx, cmd)
		}

		// Extract command information
		cmdName := cmd.Name()
		cmdArgs := cmd.Args()

		// Start a span for the Redis command
		spanCtx, span := h.tracer.Start(ctx, "db.redis.command",
			trace.WithSpanKind(trace.SpanKindClient),
			trace.WithAttributes(
				attribute.String("db.system", "redis"),
				attribute.String("db.operation", cmdName),
				attribute.String("db.statement", formatRedisCommand(cmdName, cmdArgs)),
			),
		)
		defer span.End()

		// Add command arguments count (but not values for security)
		if len(cmdArgs) > 1 { // First arg is always the command name
			span.SetAttributes(
				attribute.Int("db.args.count", len(cmdArgs)-1),
			)
		}

		// Log command start
		if h.logger != nil {
			h.logger.Debug("Redis command started",
				"command", cmdName,
				"args_count", len(cmdArgs)-1,
			)
		}

		start := time.Now()
		err := next(spanCtx, cmd)
		duration := time.Since(start)

		// Set span attributes
		span.SetAttributes(
			attribute.Int64("db.duration_ms", duration.Milliseconds()),
		)

		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
			span.SetAttributes(
				attribute.String("db.error", err.Error()),
			)

			// Log error
			if h.logger != nil {
				h.logger.Error("Redis command failed",
					"command", cmdName,
					"error", err,
					"duration_ms", duration.Milliseconds(),
				)
			}
		} else {
			span.SetStatus(codes.Ok, "")

			// Log successful completion
			if h.logger != nil {
				level := slog.LevelDebug
				// Log slow commands at info level
				if duration > 50*time.Millisecond {
					level = slog.LevelInfo
				}

				h.logger.Log(ctx, level, "Redis command completed",
					"command", cmdName,
					"duration_ms", duration.Milliseconds(),
				)
			}
		}

		return err
	}
}

// ProcessPipelineHook is called for pipelined Redis commands
func (h *RedisHook) ProcessPipelineHook(next redis.ProcessPipelineHook) redis.ProcessPipelineHook {
	return func(ctx context.Context, cmds []redis.Cmder) error {
		if h.tracer == nil {
			return next(ctx, cmds)
		}

		// Start a span for the pipeline
		spanCtx, span := h.tracer.Start(ctx, "db.redis.pipeline",
			trace.WithSpanKind(trace.SpanKindClient),
			trace.WithAttributes(
				attribute.String("db.system", "redis"),
				attribute.String("db.operation", "pipeline"),
				attribute.Int("db.pipeline.command_count", len(cmds)),
			),
		)
		defer span.End()

		// Log pipeline start
		if h.logger != nil {
			h.logger.Debug("Redis pipeline started",
				"command_count", len(cmds),
			)
		}

		start := time.Now()
		err := next(spanCtx, cmds)
		duration := time.Since(start)

		// Set span attributes
		span.SetAttributes(
			attribute.Int64("db.duration_ms", duration.Milliseconds()),
		)

		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())

			// Log error
			if h.logger != nil {
				h.logger.Error("Redis pipeline failed",
					"command_count", len(cmds),
					"error", err,
					"duration_ms", duration.Milliseconds(),
				)
			}
		} else {
			span.SetStatus(codes.Ok, "")

			// Count successful vs failed commands
			successCount := 0
			errorCount := 0
			for _, cmd := range cmds {
				if cmd.Err() != nil {
					errorCount++
				} else {
					successCount++
				}
			}

			span.SetAttributes(
				attribute.Int("db.pipeline.success_count", successCount),
				attribute.Int("db.pipeline.error_count", errorCount),
			)

			// Log successful completion
			if h.logger != nil {
				h.logger.Info("Redis pipeline completed",
					"command_count", len(cmds),
					"success_count", successCount,
					"error_count", errorCount,
					"duration_ms", duration.Milliseconds(),
				)
			}
		}

		return err
	}
}

// formatRedisCommand formats a Redis command for tracing (without sensitive data)
func formatRedisCommand(name string, args []interface{}) string {
	if len(args) <= 1 {
		return name
	}

	// For security, we only include the command name and sanitized args
	var sanitizedArgs []string
	for i, arg := range args {
		if i == 0 {
			continue // Skip command name
		}

		// Convert to string but limit length for readability
		argStr := fmt.Sprintf("%v", arg)
		if len(argStr) > 50 {
			argStr = argStr[:47] + "..."
		}

		// Sanitize potentially sensitive commands
		if isSensitiveCommand(name) {
			argStr = "[REDACTED]"
		}

		sanitizedArgs = append(sanitizedArgs, argStr)
	}

	return fmt.Sprintf("%s %s", name, strings.Join(sanitizedArgs, " "))
}

// isSensitiveCommand checks if a Redis command might contain sensitive data
func isSensitiveCommand(cmd string) bool {
	sensitiveCommands := map[string]bool{
		"AUTH":     true,
		"CONFIG":   true,
		"EVAL":     true,
		"EVALSHA":  true,
		"SCRIPT":   true,
		"CLIENT":   true,
		"SHUTDOWN": true,
		"DEBUG":    true,
	}

	return sensitiveCommands[strings.ToUpper(cmd)]
}

// parseHost extracts the host from an address string
func parseHost(addr string) string {
	host, _, err := net.SplitHostPort(addr)
	if err != nil {
		return addr
	}
	return host
}

// parsePort extracts the port from an address string
func parsePort(addr string) string {
	_, port, err := net.SplitHostPort(addr)
	if err != nil {
		return ""
	}
	return port
}
