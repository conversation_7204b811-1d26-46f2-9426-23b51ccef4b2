/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * Go核心库的ArkTS封装层
 * 
 * 提供类型安全的接口来调用底层的Go核心功能
 * 包括加密、AI推理、数据同步等核心能力
 */
export class CoreGoBridge {
  private static instance: CoreGoBridge;
  private static readonly TAG = 'CoreGoBridge';
  
  private isInitialized: boolean = false;
  private nativeModule: any;

  /**
   * 获取单例实例
   */
  static getInstance(): CoreGoBridge {
    if (!CoreGoBridge.instance) {
      CoreGoBridge.instance = new CoreGoBridge();
    }
    return CoreGoBridge.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    try {
      // 加载原生模块
      this.nativeModule = globalThis.requireNapi('goBridge');
      hilog.info(0x0000, CoreGoBridge.TAG, 'Native module loaded successfully');
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Failed to load native module: ${error.message}`);
      throw error;
    }
  }

  /**
   * 初始化Go核心库
   */
  initialize(): boolean {
    if (this.isInitialized) {
      hilog.warn(0x0000, CoreGoBridge.TAG, 'Go core already initialized');
      return true;
    }

    try {
      const result = this.nativeModule.initializeGoCore();
      if (result === 0) {
        this.isInitialized = true;
        hilog.info(0x0000, CoreGoBridge.TAG, 'Go core initialized successfully');
        return true;
      } else {
        hilog.error(0x0000, CoreGoBridge.TAG, `Go core initialization failed with code: ${result}`);
        return false;
      }
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Go core initialization error: ${error.message}`);
      return false;
    }
  }

  /**
   * 清理Go核心库
   */
  cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    try {
      this.nativeModule.cleanupGoCore();
      this.isInitialized = false;
      hilog.info(0x0000, CoreGoBridge.TAG, 'Go core cleaned up successfully');
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Go core cleanup error: ${error.message}`);
    }
  }

  /**
   * 获取最后的错误信息
   */
  getLastError(): string | null {
    try {
      return this.nativeModule.getLastError();
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Failed to get last error: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取模块版本
   */
  getVersion(): string {
    try {
      return this.nativeModule.getModuleVersion();
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Failed to get version: ${error.message}`);
      return 'unknown';
    }
  }

  // ===================== 加密功能 =====================

  /**
   * 对称加密
   * @param key 加密密钥
   * @param plaintext 明文数据
   * @returns 加密后的数据
   */
  async encryptSymmetric(key: ArrayBuffer, plaintext: ArrayBuffer): Promise<ArrayBuffer> {
    this.ensureInitialized();
    
    try {
      return await this.nativeModule.encryptSymmetric(key, plaintext);
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Encryption failed: ${error.message}`);
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * 对称解密
   * @param key 解密密钥
   * @param ciphertext 密文数据
   * @returns 解密后的数据
   */
  async decryptSymmetric(key: ArrayBuffer, ciphertext: ArrayBuffer): Promise<ArrayBuffer> {
    this.ensureInitialized();
    
    try {
      return await this.nativeModule.decryptSymmetric(key, ciphertext);
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Decryption failed: ${error.message}`);
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  /**
   * 派生密钥
   * @param password 主密码
   * @param salt 盐值
   * @returns 派生的密钥
   */
  deriveKey(password: string, salt: ArrayBuffer): ArrayBuffer {
    this.ensureInitialized();
    
    try {
      return this.nativeModule.deriveKey(password, salt);
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Key derivation failed: ${error.message}`);
      throw new Error(`Key derivation failed: ${error.message}`);
    }
  }

  // ===================== AI功能 =====================

  /**
   * 初始化AI引擎
   * @param modelPath 模型文件路径
   * @returns 是否成功
   */
  initializeAIEngine(modelPath: string): boolean {
    this.ensureInitialized();
    
    try {
      const result = this.nativeModule.initializeAIEngine(modelPath);
      return result === 0;
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `AI engine initialization failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 文本预测
   * @param prompt 输入提示
   * @param maxTokens 最大Token数量
   * @returns 预测结果
   */
  async predictText(prompt: string, maxTokens: number): Promise<ArrayBuffer> {
    this.ensureInitialized();
    
    try {
      return await this.nativeModule.predictText(prompt, maxTokens);
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Text prediction failed: ${error.message}`);
      throw new Error(`Text prediction failed: ${error.message}`);
    }
  }

  /**
   * 清理AI引擎
   */
  cleanupAIEngine(): void {
    try {
      this.nativeModule.cleanupAIEngine();
      hilog.info(0x0000, CoreGoBridge.TAG, 'AI engine cleaned up');
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `AI engine cleanup failed: ${error.message}`);
    }
  }

  // ===================== 数据同步功能 =====================

  /**
   * 创建同步块
   * @param data 数据
   * @param chunkId 块ID
   * @returns 同步块
   */
  createSyncChunk(data: ArrayBuffer, chunkId: string): ArrayBuffer {
    this.ensureInitialized();
    
    try {
      return this.nativeModule.createSyncChunk(data, chunkId);
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Create sync chunk failed: ${error.message}`);
      throw new Error(`Create sync chunk failed: ${error.message}`);
    }
  }

  /**
   * 合并同步块
   * @param chunks 同步块数组
   * @returns 合并后的数据
   */
  mergeSyncChunks(chunks: ArrayBuffer): ArrayBuffer {
    this.ensureInitialized();
    
    try {
      return this.nativeModule.mergeSyncChunks(chunks);
    } catch (error) {
      hilog.error(0x0000, CoreGoBridge.TAG, `Merge sync chunks failed: ${error.message}`);
      throw new Error(`Merge sync chunks failed: ${error.message}`);
    }
  }

  // ===================== 私有方法 =====================

  /**
   * 确保已初始化
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Go core not initialized. Call initialize() first.');
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }
} 