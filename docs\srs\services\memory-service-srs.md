﻿好的，遵照您的指示，我们来生成一份为 `memory-service` (个人记忆服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **记忆结构与类型**: 详细定义记忆的结构，引入“显式记忆”和“隐式/推断记忆”的概念，并支持更丰富的元数据。
2.  **记忆生命周期与权重**: 引入记忆的“重要性评分”和“衰减机制”，使其更接近人脑的记忆模式，能自动识别和管理核心记忆。
3.  **高级检索与RAG**: 明确与`ai-assistant-service`的RAG（检索增强生成）流程，支持更复杂的查询，如“我最近关于项目的想法是什么？”。
4.  **安全与隐私控制**: 强化ALE（应用层加密）模型的细节，并增加用户对AI访问其记忆的精细化控制。
5.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、极度注重隐私、且能为AI提供高级上下文支持的智能记忆系统。

---

### CINA.CLUB - memory-service 需求规格说明书

**版本: 2.0 (生产级定义，集成高级生命周期与RAG)**  
**发布日期: 2025-06-23**  
**最后修订日期: 2025-06-23**  
**文档负责人:** [AI平台/个性化团队负责人名称]  
**审批人:** [CTO/首席隐私官 (CPO)]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了使CINA.CLUB的AI助手能提供真正深度个性化、具备连续性上下文的对话体验，`memory-service` 旨在为每个用户创建一个**安全的、持久化的、可智能检索的长期记忆库 (Personal Memory, PM)**。AI助手可以将与用户交互中产生的关键事实、用户偏好、对话摘要等存入此记忆库，并在未来的交互中主动检索这些记忆，从而提供更连贯、更贴心、更了解用户的智能体验。本服务的核心是在云端以**服务器端零知识**的原则，安全地存储和检索高度私密的个人数据。

#### 1.2. 服务范围
本服务 **负责**:
*   **个人记忆(PM)条目的安全存储**: 接收来自AI助手或用户创建的记忆条目，并进行**应用层加密 (ALE)** 后持久化。
*   **记忆的结构化管理**: 支持多种记忆类型，如`EXPLICIT`（用户或AI明确存入的事实）、`IMPLICIT`（AI从对话中自动推断的偏好）。
*   **记忆生命周期管理**:
    *   为记忆计算和维护**重要性评分(Importance Score)**。
    *   实现记忆的**自然衰减(Decay)**和**回顾增强(Retrieval Strengthening)**机制。
*   **智能检索与RAG支持**:
    *   为记忆内容生成嵌入向量（通过`embedding-service`）。
    *   提供强大的检索API，支持基于**新近度(Recency)、重要性(Importance)和相关性(Relevance)**的加权混合搜索，为AI的RAG（检索增强生成）流程提供核心上下文。
*   **与加密密钥管理的集成**: 与`key-management-proxy-service`紧密协作，安全地获取和使用用户DEK。

本服务 **不负责**:
*   **管理用户主加密密钥(DEK)** (由`key-management-proxy-service`负责)。
*   **决定“什么”应该被记住**: 这个决策逻辑主要在`ai-assistant-service`中，本服务只负责存储被告知要记下的东西。
*   **用户主动管理的、结构化的个人知识库(PKB)** (由`personal-kb-service`负责，PM更侧重于AI驱动的、非结构化的事实片段)。

#### 1.3. 目标用户/调用方
*   **`ai-assistant-service` (主要)**: 创建新记忆，在生成回复前查询记忆以获取长期上下文。
*   **CINA.CLUB客户端应用**: (通过`user-core-service`或API Gateway代理) 允许用户浏览、搜索和（有限度地）管理自己的记忆。
*   **`digital-twin-service`**: (可选) 查询记忆摘要以构建数字孪生的“个性”。

#### 1.4. 定义与缩略语
*   **PM**: Personal Memory (个人记忆)。
*   **ALE**: Application-Level Encryption。
*   **DEK**: Data Encryption Key。
*   **KMSProxy**: `key-management-proxy-service`。
*   **RAG**: Retrieval-Augmented Generation (检索增强生成)。
*   **Recency, Importance, Relevance**: 记忆检索的三大核心要素：新近度、重要性、相关性。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`memory-service` 是CINA.CLUB平台实现深度个性化和情景感知AI体验的“**长期记忆海马体**”。它专注于存储由AI和用户共同塑造的、用于长期参考的非结构化事实和偏好。与`personal-kb-service`中用户主动构建的“知识”不同，`memory-service`中的“记忆”更像是AI的“笔记”，具有动态的生命周期和重要性。它为AI提供了超越短期对话窗口的、真正的长期上下文。

#### 2.2. 主要功能概述
*   基于用户DEK的应用层加密存储。
*   模拟人脑记忆的、基于权重和衰减的生命周期管理。
*   为RAG优化的、支持多维度加权的高级检索。
*   与AI助手和加密服务的深度、安全集成。

### 3. 核心流程图

#### 3.1. AI助手检索记忆以增强回复 (RAG流程)
```mermaid
sequenceDiagram
    participant User
    participant AIAssistantService as AIAS
    participant MemoryService as MS
    participant EmbeddingService as ES
    participant VectorDB

    User->>AIAS: "你还记得我最喜欢的咖啡店吗？"
    AIAS->>ES: 1. Get embedding for user's query
    ES-->>AIAS: (Query Vector)
    
    AIAS->>MS: 2. POST /search (query_vector, recency_w: 0.2, importance_w: 0.5, relevance_w: 1.0)
    MS->>VectorDB: 3a. Perform semantic search (k-NN) for Relevance
    MS->>DB: 3b. Get Recency and Importance scores for retrieved items
    MS->>MS: 4. **[In-Memory]** Calculate final score for each memory <br/> (weighted sum of 3 scores) and re-rank.
    MS-->>AIAS: 5. (Top N relevant memories, decrypted)

    Note over AIAS: **[Augmented Generation]**
    AIAS->>LLM: 6. Generate response based on: <br/> - User's original query <br/> - Retrieved memories (e.g., "我最喜欢的咖啡店是街角的星巴克")
    LLM-->>AIAS: ("记得，您最喜欢的咖啡店是街角的星巴克。")
    AIAS-->>User: (Final Response)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 记忆条目(PM Item)管理
*   **FR4.1.1 (创建)**: 系统必须提供API，允许创建新的记忆条目。请求中需包含：
    *   `content` (文本): 记忆的核心内容。
    *   `type`: `EXPLICIT` (用户或AI明确指令存入) 或 `IMPLICIT` (AI从对话中自动摘要和推断)。
    *   `importance_score` (1-10): 由创建者（AI）评估的初始重要性。
*   **FR4.1.2 (读取)**: 提供API，根据ID获取单个记忆条目的详细内容（解密后）。
*   **FR4.1.3 (更新)**: 支持更新记忆条目的内容或元数据。每次更新都应刷新其`last_accessed_at`时间戳。

#### 4.2. 记忆生命周期与权重
*   **FR4.2.1 (重要性评分)**: 每个记忆条目必须有一个`importance_score`。
*   **FR4.2.2 (新近度)**: `recency`是根据`last_accessed_at`时间戳动态计算的，越近的记忆得分越高。
*   **FR4.2.3 (记忆加强)**: **每次一个记忆被成功检索和使用时**，其`last_accessed_at`必须被更新，从而增强其`recency`得分。
*   **FR4.2.4 (记忆衰减 - 可选)**: （高级）可以有一个后台任务，对长时间未被访问且重要性低的记忆，逐渐降低其`importance_score`，模拟遗忘过程。

#### 4.3. 智能检索 (RAG核心)
*   **FR4.3.1 (高级搜索API)**: 系统必须提供一个`POST /search`接口，接收**查询向量**以及`recency`, `importance`, `relevance`的**权重参数**。
*   **FR4.3.2 (检索与重排)**:
    *   **步骤1 (相关性检索)**: 使用查询向量在VectorDB中进行k-NN搜索，召回一批语义相关的记忆。
    *   **步骤2 (分数计算)**: 对召回的每个记忆，从数据库中获取其`importance_score`和`last_accessed_at`，并计算出`recency_score`。
    *   **步骤3 (加权融合)**: 根据API传入的权重，计算每个记忆的最终得分：`FinalScore = relevance_w * relevance_score + importance_w * importance_score + recency_w * recency_score`。
    *   **步骤4 (重排)**: 按`FinalScore`对召回的记忆进行重排，并返回Top-N结果。

#### 4.4. 数据安全与隐私控制
*   **FR4.4.1 (应用层加密)**: 所有记忆条目的`content`字段在持久化前，必须使用该用户的DEK进行应用层加密。
*   **FR4.4.2 (密钥交互)**: 严格遵循与`key-management-proxy-service`的安全交互协议，明文DEK绝不在内存外存在。
*   **FR4.4.3 (用户控制)**: 用户必须能通过客户端UI：
    *   查看、搜索和删除自己的记忆。
    *   授权或撤销AI助手对记忆的读/写权限。
    *   一键清除所有记忆。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC API接口 (S2S)
*   **Package**: `hina.vip.memory.v1`
*   **认证**: 严格的S2S认证 + 用户会话凭证。
*   **核心RPC**:
    ```protobuf
    service MemoryService {
      // 写操作
      rpc CreateMemory(CreateMemoryRequest) returns (MemoryItem);
      // 读操作
      rpc GetMemory(GetMemoryRequest) returns (MemoryItem);
      // 核心检索接口
      rpc SearchMemories(SearchMemoriesRequest) returns (SearchMemoriesResponse);
    }

    message CreateMemoryRequest {
      AuthContext auth_context = 1;
      string content_text = 2;
      MemoryType type = 3;
      float importance_score = 4;
    }
    
    message SearchMemoriesRequest {
      AuthContext auth_context = 1;
      repeated float query_vector = 2;
      int32 top_k = 3;
      SearchWeights weights = 4; // 包含recency, importance, relevance的权重
    }
    
    message SearchMemoriesResponse {
      repeated MemorySearchResultItem results = 1;
    }
    ```

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`personal_memories`**:
    *   `memory_id (PK, UUID)`
    *   `user_id (FK, INDEX)`
    *   `type (VARCHAR)`: `EXPLICIT`, `IMPLICIT`.
    *   `encrypted_content (BYTEA)`
    *   `encryption_metadata (JSONB)`
    *   `importance_score (FLOAT, INDEX)`
    *   `embedding_vector_id (VARCHAR)`: (可选) 关联到VectorDB中的ID。
    *   `created_at (TIMESTAMPTZ)`
    *   `last_accessed_at (TIMESTAMPTZ, INDEX)`: **每次被成功检索时都必须更新**。

#### 6.2. 数据存储
*   **元数据与加密内容**: PostgreSQL。
*   **向量索引**: Pinecone, Weaviate, Milvus等专业向量数据库。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **写入延迟**: 创建一个记忆（包括加密、嵌入、存储），P99应 `< 500ms`。
*   **搜索延迟**: `SearchMemories` API的P99应 `< 300ms` (主要取决于VectorDB和DB性能)。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **容错**: 对`KMSProxy`, `EmbeddingService`, `VectorDB`的依赖必须有健壮的重试和熔断机制。

#### 7.3. 可扩展性需求
*   服务可水平扩展。元数据DB和向量DB都必须支持水平扩展。

#### 7.4. 安全性需求 (最高优先级)
*   **服务器端零知识**: 严格执行ALE模型。
*   **权限控制**: 严格的`userId`隔离。
*   **审计**: 所有记忆的创建、访问（特别是导致解密的操作）、修改、删除都必须有详细、不可篡改的审计日志。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **加密**: 使用Go标准库`crypto/aes`等，并遵循密码学最佳实践。
*   **RAG集成**: 与`ai-assistant-service`的接口契约是本服务的核心，`SearchMemories`接口的设计直接影响RAG流程的质量。
*   **数据库**: PostgreSQL的灵活性和成熟度足以应对此场景。

---
这份版本2.0的SRS文档为`memory-service`构建了一个先进的、注重隐私的、且能模拟人脑记忆机制的长期记忆系统。它通过为AI提供基于新近度、重要性和相关性的高质量上下文，将是CINA.CLUB平台实现真正智能化、人性化对话体验的关键。