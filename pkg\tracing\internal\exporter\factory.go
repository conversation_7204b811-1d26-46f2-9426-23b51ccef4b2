/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package exporter

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/sdk/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
)

// ExporterConfig 导出器配置（从主包复制，避免循环依赖）
type ExporterConfig struct {
	Type        string            `mapstructure:"type"`
	Endpoint    string            `mapstructure:"endpoint"`
	Headers     map[string]string `mapstructure:"headers"`
	Insecure    bool              `mapstructure:"insecure"`
	Compression string            `mapstructure:"compression"`
	Timeout     int               `mapstructure:"timeout"`
	Retry       RetryConfig       `mapstructure:"retry"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	Enabled         bool `mapstructure:"enabled"`
	MaxAttempts     int  `mapstructure:"max_attempts"`
	InitialInterval int  `mapstructure:"initial_interval"`
	MaxInterval     int  `mapstructure:"max_interval"`
}

// New 根据配置创建对应的 SpanExporter
func New(ctx context.Context, cfg ExporterConfig) (trace.SpanExporter, error) {
	switch cfg.Type {
	case "stdout":
		return createStdoutExporter(cfg)
	case "jaeger":
		return createJaegerExporter(cfg)
	case "otlp-grpc":
		return createOTLPGRPCExporter(ctx, cfg)
	case "otlp-http":
		return createOTLPHTTPExporter(ctx, cfg)
	default:
		return nil, fmt.Errorf("unsupported exporter type: %s", cfg.Type)
	}
}

// createStdoutExporter 创建标准输出导出器
func createStdoutExporter(cfg ExporterConfig) (trace.SpanExporter, error) {
	opts := []stdouttrace.Option{
		stdouttrace.WithPrettyPrint(),
	}

	return stdouttrace.New(opts...)
}

// createJaegerExporter 创建 Jaeger 导出器
func createJaegerExporter(cfg ExporterConfig) (trace.SpanExporter, error) {
	if cfg.Endpoint == "" {
		return nil, fmt.Errorf("jaeger endpoint is required")
	}

	return jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(cfg.Endpoint)))
}

// createOTLPGRPCExporter 创建 OTLP gRPC 导出器
func createOTLPGRPCExporter(ctx context.Context, cfg ExporterConfig) (trace.SpanExporter, error) {
	if cfg.Endpoint == "" {
		return nil, fmt.Errorf("otlp-grpc endpoint is required")
	}

	opts := []otlptracegrpc.Option{
		otlptracegrpc.WithEndpoint(cfg.Endpoint),
		otlptracegrpc.WithTimeout(time.Duration(cfg.Timeout) * time.Second),
	}

	// 配置安全连接
	if cfg.Insecure {
		opts = append(opts, otlptracegrpc.WithTLSCredentials(insecure.NewCredentials()))
	} else {
		opts = append(opts, otlptracegrpc.WithTLSCredentials(credentials.NewTLS(nil)))
	}

	// 配置压缩
	if cfg.Compression == "gzip" {
		opts = append(opts, otlptracegrpc.WithCompressor(cfg.Compression))
	}

	// 配置自定义头部
	if len(cfg.Headers) > 0 {
		opts = append(opts, otlptracegrpc.WithHeaders(cfg.Headers))
	}

	// 配置重试
	if cfg.Retry.Enabled {
		retryConfig := otlptracegrpc.RetryConfig{
			Enabled:         cfg.Retry.Enabled,
			InitialInterval: time.Duration(cfg.Retry.InitialInterval) * time.Millisecond,
			MaxInterval:     time.Duration(cfg.Retry.MaxInterval) * time.Millisecond,
			MaxElapsedTime:  time.Duration(cfg.Retry.MaxAttempts*cfg.Retry.MaxInterval) * time.Millisecond,
		}
		opts = append(opts, otlptracegrpc.WithRetry(retryConfig))
	}

	// 配置拨号选项
	dialOpts := []grpc.DialOption{}
	if cfg.Insecure {
		dialOpts = append(dialOpts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	}
	opts = append(opts, otlptracegrpc.WithDialOption(dialOpts...))

	return otlptracegrpc.New(ctx, opts...)
}

// createOTLPHTTPExporter 创建 OTLP HTTP 导出器
func createOTLPHTTPExporter(ctx context.Context, cfg ExporterConfig) (trace.SpanExporter, error) {
	if cfg.Endpoint == "" {
		return nil, fmt.Errorf("otlp-http endpoint is required")
	}

	opts := []otlptracehttp.Option{
		otlptracehttp.WithEndpoint(cfg.Endpoint),
		otlptracehttp.WithTimeout(time.Duration(cfg.Timeout) * time.Second),
	}

	// 配置安全连接
	if cfg.Insecure {
		opts = append(opts, otlptracehttp.WithInsecure())
	}

	// 配置压缩
	if cfg.Compression == "gzip" {
		opts = append(opts, otlptracehttp.WithCompression(otlptracehttp.GzipCompression))
	}

	// 配置自定义头部
	if len(cfg.Headers) > 0 {
		opts = append(opts, otlptracehttp.WithHeaders(cfg.Headers))
	}

	// 配置重试
	if cfg.Retry.Enabled {
		retryConfig := otlptracehttp.RetryConfig{
			Enabled:         cfg.Retry.Enabled,
			InitialInterval: time.Duration(cfg.Retry.InitialInterval) * time.Millisecond,
			MaxInterval:     time.Duration(cfg.Retry.MaxInterval) * time.Millisecond,
			MaxElapsedTime:  time.Duration(cfg.Retry.MaxAttempts*cfg.Retry.MaxInterval) * time.Millisecond,
		}
		opts = append(opts, otlptracehttp.WithRetry(retryConfig))
	}

	return otlptracehttp.New(ctx, opts...)
}

// ValidateConfig 验证导出器配置
func ValidateConfig(cfg ExporterConfig) error {
	switch cfg.Type {
	case "stdout":
		// stdout 不需要额外验证
		return nil
	case "jaeger", "otlp-grpc", "otlp-http":
		if cfg.Endpoint == "" {
			return fmt.Errorf("endpoint is required for exporter type: %s", cfg.Type)
		}
		return nil
	default:
		return fmt.Errorf("unsupported exporter type: %s", cfg.Type)
	}
}
