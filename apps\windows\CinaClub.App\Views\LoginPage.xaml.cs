/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using Microsoft.UI.Xaml.Controls;
using Microsoft.Extensions.DependencyInjection;
using CinaClub.App.ViewModels;

namespace CinaClub.App.Views;

/// <summary>
/// 登录页面
/// </summary>
public sealed partial class LoginPage : Page
{
    /// <summary>
    /// 登录页面的ViewModel
    /// </summary>
    public LoginViewModel ViewModel { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public LoginPage()
    {
        this.InitializeComponent();
        
        // 获取ViewModel实例
        ViewModel = App.Services.GetRequiredService<LoginViewModel>();
        
        // 设置数据上下文
        this.DataContext = ViewModel;
    }
} 