/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package logger

import (
	"log/slog"
	"strings"
)

// Config 定义日志配置
type Config struct {
	// Level 日志级别: debug, info, warn, error
	Level string `mapstructure:"level" validate:"required,oneof=debug info warn error" default:"info"`

	// Format 输出格式: json, text (生产环境强制使用json)
	Format string `mapstructure:"format" validate:"required,oneof=json text" default:"json"`

	// AddSource 是否在日志中包含源文件信息 (影响性能)
	AddSource bool `mapstructure:"add_source" default:"false"`

	// ServiceName 服务名称，将作为固定字段添加到每条日志中
	ServiceName string `mapstructure:"service_name" validate:"required"`

	// ServiceVersion 服务版本，将作为固定字段添加到每条日志中
	ServiceVersion string `mapstructure:"service_version" validate:"required"`
}

// ToSlogLevel 将字符串日志级别转换为slog.Level
func (c *Config) ToSlogLevel() slog.Level {
	switch strings.ToLower(c.Level) {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// IsJSONFormat 检查是否使用JSON格式
func (c *Config) IsJSONFormat() bool {
	return strings.ToLower(c.Format) == "json"
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	if c.ServiceName == "" {
		return ErrMissingServiceName
	}
	if c.ServiceVersion == "" {
		return ErrMissingServiceVersion
	}

	level := strings.ToLower(c.Level)
	if level != "debug" && level != "info" && level != "warn" && level != "error" {
		return ErrInvalidLogLevel
	}

	format := strings.ToLower(c.Format)
	if format != "json" && format != "text" {
		return ErrInvalidLogFormat
	}

	return nil
}
