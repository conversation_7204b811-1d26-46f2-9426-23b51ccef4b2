/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useEffect } from 'react'
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Progress, 
  Tag, 
  Badge, 
  Space, 
  Alert, 
  Button,
  Typography,
  Divider,
  Avatar,
  Tooltip,
  Timeline
} from 'antd'
import { 
  DashboardOutlined, 
  DatabaseOutlined, 
  CloudServerOutlined,
  BugOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { Line, Pie, Column } from '@ant-design/charts'

import { Service, ServiceStatus, HealthStatus, ServiceType, ServiceMetrics } from '@/types/service'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { Title, Text } = Typography

// Mock services data
const mockServices: Service[] = [
  {
    id: '1',
    name: 'user-core-service',
    displayName: '用户核心服务',
    type: ServiceType.MICROSERVICE,
    status: ServiceStatus.RUNNING,
    health: HealthStatus.HEALTHY,
    version: '1.2.3',
    endpoint: '/api/users',
    port: 8001,
    host: 'localhost',
    environment: 'production',
    tags: ['core', 'user-management'],
    dependencies: ['database', 'cache'],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-23T10:00:00Z',
    lastHealthCheck: '2025-01-23T10:00:00Z',
  },
  {
    id: '2',
    name: 'content-service',
    displayName: '内容管理服务',
    type: ServiceType.MICROSERVICE,
    status: ServiceStatus.RUNNING,
    health: HealthStatus.DEGRADED,
    version: '2.1.0',
    endpoint: '/api/content',
    port: 8002,
    environment: 'production',
    tags: ['content', 'cms'],
    dependencies: ['database', 'storage'],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-23T10:00:00Z',
    lastHealthCheck: '2025-01-23T10:00:00Z',
  },
  {
    id: '3',
    name: 'redis-cache',
    displayName: 'Redis 缓存',
    type: ServiceType.CACHE,
    status: ServiceStatus.RUNNING,
    health: HealthStatus.HEALTHY,
    version: '7.0.0',
    port: 6379,
    environment: 'production',
    tags: ['cache', 'redis'],
    dependencies: [],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-23T10:00:00Z',
    lastHealthCheck: '2025-01-23T10:00:00Z',
  },
]

// Mock metrics data
const mockMetrics: ServiceMetrics = {
  serviceId: '1',
  timestamp: '2025-01-23T10:00:00Z',
  cpu: { usage: 45, limit: 80 },
  memory: { usage: **********, limit: **********, percentage: 50 },
  network: { inbound: 1024000, outbound: 2048000 },
  requests: { total: 15000, success: 14850, errors: 150, rate: 25.5 },
  responseTime: { average: 120, p50: 100, p95: 200, p99: 350 },
  uptime: 99.9,
}

/**
 * 服务监控仪表板
 */
const ServiceDashboard: React.FC = () => {
  const { hasPermission } = usePermission()
  const [services, setServices] = useState<Service[]>(mockServices)
  const [loading, setLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  // 权限检查
  const canView = hasPermission(Permission.SERVICE_VIEW)
  const canControl = hasPermission(Permission.SERVICE_CONTROL)

  // 服务状态统计
  const serviceStats = {
    total: services.length,
    running: services.filter(s => s.status === ServiceStatus.RUNNING).length,
    healthy: services.filter(s => s.health === HealthStatus.HEALTHY).length,
    degraded: services.filter(s => s.health === HealthStatus.DEGRADED).length,
    critical: services.filter(s => s.health === HealthStatus.CRITICAL).length,
  }

  // 服务类型分布
  const serviceTypeData = Object.values(ServiceType).map(type => ({
    type,
    count: services.filter(s => s.type === type).length,
  })).filter(item => item.count > 0)

  // 响应时间趋势数据
  const responseTimeData = [
    { time: '10:00', value: 120 },
    { time: '10:15', value: 115 },
    { time: '10:30', value: 125 },
    { time: '10:45', value: 110 },
    { time: '11:00', value: 130 },
  ]

  // 刷新数据
  const handleRefresh = async () => {
    setLoading(true)
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setLastUpdate(new Date())
    } finally {
      setLoading(false)
    }
  }

  // 自动刷新
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(new Date())
    }, 30000) // 30秒刷新一次

    return () => clearInterval(interval)
  }, [])

  // 获取健康状态颜色
  const getHealthColor = (health: HealthStatus) => {
    switch (health) {
      case HealthStatus.HEALTHY: return 'success'
      case HealthStatus.DEGRADED: return 'warning'
      case HealthStatus.CRITICAL: return 'error'
      case HealthStatus.UNHEALTHY: return 'error'
      default: return 'default'
    }
  }

  // 获取服务状态颜色
  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.RUNNING: return 'green'
      case ServiceStatus.STARTING: return 'blue'
      case ServiceStatus.STOPPING: return 'orange'
      case ServiceStatus.STOPPED: return 'red'
      case ServiceStatus.ERROR: return 'red'
      default: return 'gray'
    }
  }

  if (!canView) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限查看服务监控</p>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={3} style={{ margin: 0 }}>
            <DashboardOutlined /> 服务监控仪表板
          </Title>
          <Text type="secondary">
            最后更新: {lastUpdate.toLocaleString()}
          </Text>
        </Col>
        <Col>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Col>
      </Row>

      {/* 整体状态告警 */}
      {serviceStats.critical > 0 && (
        <Alert
          message="服务健康告警"
          description={`检测到 ${serviceStats.critical} 个服务处于危险状态，请立即处理！`}
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
          action={
            <Button size="small" danger>
              查看详情
            </Button>
          }
        />
      )}

      {/* 服务统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总服务数"
              value={serviceStats.total}
              prefix={<CloudServerOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中"
              value={serviceStats.running}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={`/ ${serviceStats.total}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="健康服务"
              value={serviceStats.healthy}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="异常服务"
              value={serviceStats.degraded + serviceStats.critical}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细监控信息 */}
      <Row gutter={[16, 16]}>
        {/* 服务列表 */}
        <Col xs={24} lg={12}>
          <Card title="服务状态" extra={<Badge count={services.length} />}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {services.map(service => (
                <div key={service.id} style={{ 
                  padding: '12px', 
                  border: '1px solid #f0f0f0', 
                  borderRadius: '6px',
                  background: '#fafafa'
                }}>
                  <Row justify="space-between" align="middle">
                    <Col flex="auto">
                      <Space>
                        <Avatar 
                          icon={<DatabaseOutlined />} 
                          size="small"
                          style={{ 
                            backgroundColor: getStatusColor(service.status) 
                          }}
                        />
                        <div>
                          <div style={{ fontWeight: 500 }}>{service.displayName}</div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {service.name} v{service.version}
                          </Text>
                        </div>
                      </Space>
                    </Col>
                    <Col>
                      <Space>
                        <Tag color={getStatusColor(service.status)} size="small">
                          {service.status}
                        </Tag>
                        <Badge 
                          status={getHealthColor(service.health) as any} 
                          text={service.health}
                        />
                      </Space>
                    </Col>
                  </Row>
                </div>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 性能指标 */}
        <Col xs={24} lg={12}>
          <Card title="系统性能">
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>CPU 使用率</Text>
                  <Text>{mockMetrics.cpu.usage}%</Text>
                </div>
                <Progress 
                  percent={mockMetrics.cpu.usage} 
                  status={mockMetrics.cpu.usage > 80 ? 'exception' : 'normal'}
                  showInfo={false}
                />
              </Col>
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>内存使用率</Text>
                  <Text>{mockMetrics.memory.percentage}%</Text>
                </div>
                <Progress 
                  percent={mockMetrics.memory.percentage} 
                  status={mockMetrics.memory.percentage > 85 ? 'exception' : 'normal'}
                  showInfo={false}
                />
              </Col>
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>服务可用性</Text>
                  <Text>{mockMetrics.uptime}%</Text>
                </div>
                <Progress 
                  percent={mockMetrics.uptime} 
                  status={mockMetrics.uptime < 99 ? 'exception' : 'normal'}
                  showInfo={false}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 响应时间趋势 */}
        <Col xs={24} lg={12}>
          <Card title="响应时间趋势">
            <Line
              data={responseTimeData}
              xField="time"
              yField="value"
              height={200}
              smooth
              point={{
                size: 3,
                shape: 'diamond',
              }}
              lineStyle={{
                stroke: '#1890ff',
                lineWidth: 2,
              }}
            />
          </Card>
        </Col>

        {/* 服务类型分布 */}
        <Col xs={24} lg={12}>
          <Card title="服务类型分布">
            <Pie
              data={serviceTypeData}
              angleField="count"
              colorField="type"
              height={200}
              radius={0.8}
              label={{
                type: 'outer',
                content: '{name} {percentage}',
              }}
            />
          </Card>
        </Col>

        {/* 请求统计 */}
        <Col xs={24}>
          <Card title="请求统计">
            <Row gutter={16}>
              <Col xs={24} sm={6}>
                <Statistic
                  title="总请求数"
                  value={mockMetrics.requests.total}
                  precision={0}
                />
              </Col>
              <Col xs={24} sm={6}>
                <Statistic
                  title="成功请求"
                  value={mockMetrics.requests.success}
                  precision={0}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col xs={24} sm={6}>
                <Statistic
                  title="错误请求"
                  value={mockMetrics.requests.errors}
                  precision={0}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col xs={24} sm={6}>
                <Statistic
                  title="请求速率"
                  value={mockMetrics.requests.rate}
                  precision={1}
                  suffix="req/s"
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 最近事件 */}
        <Col xs={24}>
          <Card title="最近事件">
            <Timeline>
              <Timeline.Item color="green">
                <Text>user-core-service 健康检查通过</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  2 分钟前
                </Text>
              </Timeline.Item>
              <Timeline.Item color="orange">
                <Text>content-service 响应时间超过阈值</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  5 分钟前
                </Text>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <Text>redis-cache 重启完成</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  10 分钟前
                </Text>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ServiceDashboard 