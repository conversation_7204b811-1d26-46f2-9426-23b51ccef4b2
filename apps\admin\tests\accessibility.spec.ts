/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 19:10:00
 * Modified: 2025-01-23 19:10:00
 */

import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility Testing', () => {
  test('Dashboard should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/login');
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password');
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*dashboard/);

    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('User List page should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/login');
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password');
    await page.getByRole('button', { name: 'Login' }).click();
    
    await page.goto('/users');
    await expect(page.getByRole('heading', { name: /用户管理/ })).toBeVisible();

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });
}); 