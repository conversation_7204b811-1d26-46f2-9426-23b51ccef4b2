### 文件3: `docs/architecture/L2_System_Landscape/03_platform_data_flow.md`

```markdown
# CINA.CLUB - 平台核心数据流

**文档状态**: 动态更新 (Living Document)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27

## 1. 引言

本文档通过几个核心业务场景，以序列图的形式展示了数据在CINA.CLUB平台微服务之间的主要流动路径。这有助于理解服务间的依赖关系和信息传递的生命周期。

---

## 2. 关键数据流图

### 2.1 用户UGC发布与处理流程

**场景**: 用户在社区论坛发布一篇带图片的帖子。

```mermaid
sequenceDiagram
    participant Client
    participant ForumService as community-forum
    participant FileService as file-storage
    participant Kafka
    participant ModerationService as content-moderation
    participant IndexerService as search-indexer
    participant ActivityService as activity-feed
    
    Client->>FileService: 1. 请求图片上传URL
    FileService-->>Client: (Presigned URL)
    Client->>CloudStorage: 2. 上传图片
    
    Client->>ForumService: 3. POST /threads (含图片key)
    ForumService->>DB: 4. 创建帖子 (status: PENDING)
    
    ForumService->>Kafka: 5. Publish ThreadSubmittedEvent
    
    Kafka-->>ModerationService: 6a. 消费事件, 开始内容审核
    Kafka-->>IndexerService: 6b. 消费事件, 预索引(不可见)
    
    ModerationService-->>Kafka: 7. Publish ModerationCompletedEvent (APPROVED)
    
    Kafka-->>ForumService: 8. 消费审核结果
    ForumService->>DB: 9. 更新帖子status为PUBLISHED
    
    ForumService->>Kafka: 10. Publish ThreadPublishedEvent
    
    Kafka-->>IndexerService: 11a. 消费事件, 使索引可见
    Kafka-->>ActivityService: 11b. 消费事件, 为关注者生成Feed
```

**数据流说明**:
1.  **用户生成数据**: 帖子文本和图片数据由用户在客户端创建。
2.  **写操作**: 数据首先被写入`community-forum-service`的数据库。
3.  **事件广播**: `forum-service`通过Kafka将“帖子已提交”和“帖子已发布”两个关键**领域事件**广播出去。
4.  **异步消费**:
    *   `content-moderation-service`进行内容安全审核。
    *   `search-indexer-service`进行数据索引，以供搜索。
    *   `activity-feed-service`为粉丝生成动态。
5.  **数据最终态**: 数据最终以多种形式存在于：`forum-service`的DB、`file-storage`的对象存储、`search-indexer`的Elasticsearch、以及`activity-feed`的NoSQL数据库中。

### 2.2 AI助手执行复杂任务流程

**场景**: 用户向AI助手说：“帮我预约下周三下午和张三的会议”。

```mermaid
sequenceDiagram
    participant Client
    participant AIAssistant as ai-assistant
    participant UserCore as user-core
    participant Schedule as schedule-service
    participant Notification as notification-dispatch

    Client->>AIAssistant: 1. POST /chat (prompt)
    
    AIAssistant->>LLM: 2. [Planning] 生成工作流计划
    LLM-->>AIAssistant: Plan: [1. 查张三信息, 2. 查双方忙闲, 3. 创建日程, 4. 发通知]
    
    note over AIAssistant: 开始执行工作流 (pkg/workflow)
    
    AIAssistant->>UserCore: 3. (Step 1) 查询用户"张三"的ID
    UserCore-->>AIAssistant: (张三的userId)
    
    AIAssistant->>Schedule: 4. (Step 2) 查询双方下周三下午的忙闲
    Schedule-->>AIAssistant: (可用时间段)
    
    AIAssistant->>Schedule: 5. (Step 3) 创建日程，邀请双方
    Schedule-->>AIAssistant: (日程创建成功)

    AIAssistant->>Notification: 6. (Step 4) 请求向双方发送日程确认通知
    Notification-->>AIAssistant: (通知已分发)
    
    AIAssistant->>LLM: 7. [Synthesis] 生成最终回复
    LLM-->>AIAssistant: ("好的，已为您和张三创建了下周三下午的会议...")
    
    AIAssistant-->>Client: 8. 返回最终回复
```

**数据流说明**:
1.  **指令输入**: 用户自然语言指令是流程的起点。
2.  **动态编排**: `ai-assistant-service`作为**数据编排者**，不存储核心业务数据，而是按需从各个权威服务（`user-core`, `schedule`）**拉取(pull)**数据。
3.  **写操作分派**: 在完成计算和决策后，它将**写操作(command)**分派给相应的权威服务去执行。
4.  **响应合成**: 最后，它将所有操作的结果汇总，并生成最终的自然语言响应。
```