<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright (c) 2025 Cina.Club
  All rights reserved.
  Created: 2025-06-20 16:30:00
  Modified: 2025-06-20 16:30:00
-->
<resources>
    <!-- CINA.CLUB Theme -->
    <style name="Theme.CinaClub" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/cina_blue</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/cina_blue_light</item>
        <item name="colorOnPrimaryContainer">@color/cina_blue_dark</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/cina_green</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/cina_green_light</item>
        <item name="colorOnSecondaryContainer">@color/cina_green_dark</item>
        
        <!-- Tertiary brand color -->
        <item name="colorTertiary">@color/cina_purple</item>
        <item name="colorOnTertiary">@color/white</item>
        <item name="colorTertiaryContainer">@color/cina_purple_light</item>
        <item name="colorOnTertiaryContainer">@color/cina_purple_dark</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>
        <item name="colorErrorContainer">@color/error_light</item>
        <item name="colorOnErrorContainer">@color/error_dark</item>
        
        <!-- Surface colors -->
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/on_background</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">?attr/isLightTheme</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightNavigationBar">?attr/isLightTheme</item>
        
        <!-- Window insets -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
    </style>
    
    <!-- Splash Screen Theme -->
    <style name="Theme.CinaClub.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/cina_blue</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_cina_club_logo</item>
        <item name="windowSplashScreenAnimationDuration">500</item>
        <item name="postSplashScreenTheme">@style/Theme.CinaClub</item>
    </style>
</resources> 