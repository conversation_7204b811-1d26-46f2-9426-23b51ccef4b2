﻿好的，遵照您的指示，我们来生成一份为 `schedule-service` (日程管理服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多日历与视图**: 明确支持用户创建多个独立的日历（如“工作”、“个人”、“家庭”），并能将共享日历叠加显示。
2.  **重复日程(RRULE)的高级处理**: 详细定义对复杂重复日程（包括例外、覆盖、时区变更）的计算和存储逻辑，这是日程服务的核心难点。
3.  **与`calendar-sync-service`的协同**: 明确双向同步中的数据一致性、冲突处理和事件通知机制。
4.  **高级查询与忙闲状态**: 引入更灵活的查询API，并优化忙闲状态（Free/Busy）的计算，支持考虑“暂定(Tentative)”状态。
5.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义和事件契约，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标，特别是针对重复日程展开的计算密集型操作。

这份文档将描绘一个功能强大、精确可靠，且能作为整个平台时间管理与协调基础的日程中心。

---

### CINA.CLUB - schedule-service 需求规格说明书

**版本: 2.0 (生产级定义，支持多日历与高级重复日程)**  
**发布日期: 2025-06-24**  
**最后修订日期: 2025-06-24**  
**文档负责人:** [产品/技术负责人名称 - 负责日程模块]  
**审批人:** [CTO/架构师]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 旨在成为用户的核心生活与工作效率平台。`schedule-service` 是这一愿景的基础服务之一，其目的在于提供一个**强大、灵活、精确且可靠的个人和共享日程管理解决方案**。它帮助用户高效组织时间、协调活动，并与平台其他功能（如AI助手、任务、服务预订、外部日历同步）无缝集成，成为平台所有时间相关业务的数据与逻辑中枢。

#### 1.2. 服务范围
本服务 **负责**:
*   **多日历管理**: 支持用户创建、管理和自定义多个个人日历（如“工作”、“家庭”）。
*   **日程条目(Event)管理**:
    *   处理单次和**复杂重复性日程**的完整生命周期管理。
    *   基于iCalendar RFC 5545标准，精确处理RRULE、EXDATE和实例覆盖。
*   **提醒调度**: 管理日程的多个提醒设置，并在适当的时候触发通知。
*   **参与者与共享管理**:
    *   处理日程的参与者列表和RSVP状态（接受、拒绝、暂定）。
    *   管理用户间的日历级共享和访问权限控制。
*   **忙闲状态(Free/Busy)查询**: 提供API查询一个或多个用户在特定时间段的忙闲信息。
*   **与外部同步的协调**: 当日程发生变化时，发布精确的领域事件，通知`calendar-sync-service`进行同步。

本服务 **不负责**:
*   **直接发送Push/Email提醒** (由 `notification-dispatch-service` 负责)。
*   **管理外部日历的OAuth凭证** (由 `calendar-sync-service` 负责)。
*   **复杂的项目管理或待办列表(To-Do)** (本服务专注于基于时间的事件)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 通过API Gateway与本服务交互。
*   **`ai-assistant-service`**: 创建日程、查询日程、检查忙闲。
*   **`service-offering-service`**: 在预订服务时，查询忙闲状态，或直接创建相关日程。
*   **`calendar-sync-service`**: 调用本服务API以创建/更新从外部日历同步过来的日程。
*   **`routines-service`**: 通过本服务API自动创建或修改日程。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`schedule-service` 是平台的时间管理**核心引擎**。它不仅是用户个人的日历工具，更是平台内各种时间相关业务（服务预订、任务截止日期、活动安排）的**数据基础和交汇点**。其对重复日程和时区的计算准确性，以及查询效率，直接影响到平台众多功能的可靠性和用户体验。

#### 2.2. 主要功能概述
*   支持多日历与权限共享。
*   精确实现iCalendar RFC 5545标准的复杂重复日程。
*   高效的日程实例展开与忙闲状态计算。
*   可靠的、事件驱动的提醒与同步触发机制。

### 3. 核心流程图

#### 3.1. 查询一个月视图的日程（含重复日程展开）
```mermaid
sequenceDiagram
    participant Client
    participant ScheduleService as SS
    participant RRULE_Library as "iCal Library (in-process)"
    participant DB

    Client->>SS: 1. GET /me/events?start=...&end=...&calendar_ids=...
    
    SS->>DB: 2. Find all single events within [start, end]
    SS->>DB: 3. Find all recurring event MASTERS that could potentially have instances in [start, end]
    
    DB-->>SS: (Returns lists of single events and recurring masters)
    
    loop For each recurring master
        SS->>RRULE_Library: 4. Expand instances for this master within [start, end]
        RRULE_Library-->>SS: (List of calculated instances)
    end
    
    SS->>DB: 5. Find all OVERRIDES and EXCEPTIONS for these masters within [start, end]
    DB-->>SS: (List of overrides and exceptions)
    
    SS->>SS: 6. **[Merge & Reconcile]** <br/> - Combine single events and expanded instances. <br/> - Apply overrides to specific instances. <br/> - Remove excepted instances.
    
    SS-->>Client: 7. (Final, flattened list of event instances for the month)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 多日历与共享
*   **FR4.1.1 (多日历)**: 每个用户必须能创建、编辑、删除自己的多个日历，并为每个日历设置名称、颜色等属性。
*   **FR4.1.2 (共享)**: 用户可以将自己的某个日历共享给其他CINA.CLUB用户，并设置权限级别：
    *   `FREE_BUSY_ONLY`: 只能看到忙闲状态，看不到详情。
    *   `READ_ONLY`: 可以看到日程详情，但不能修改。
    *   `READ_WRITE`: 可以查看和修改日程。
*   **FR4.1.3 (视图叠加)**: `GET /me/events`接口必须能接收一个`calendar_ids`数组，同时查询用户自己的和被共享的多个日历，并返回一个合并后的视图。

#### 4.2. 高级重复日程处理
*   **FR4.2.1 (RRULE完全支持)**: 系统必须能存储、解析和生成符合**RFC 5545**标准的RRULE字符串，支持所有频率(`DAILY`, `WEEKLY`, `MONTHLY`, `YEARLY`)和参数(`BYDAY`, `BYMONTHDAY`, `INTERVAL`, `COUNT`, `UNTIL`等)。
*   **FR4.2.2 (实例展开引擎)**: 必须有一个高效的、经过充分测试的引擎，能在给定的时间范围内，准确地计算并返回一个重复日程系列的所有发生实例。
*   **FR4.2.3 (例外与覆盖)**:
    *   **例外(EXDATE)**: 用户必须能将重复系列中的一个或多个实例标记为“例外”（即删除该次发生）。
    *   **覆盖(Override)**: 用户必须能对重复系列中的某个特定实例进行修改。系统需要将被修改的实例从原系列中“脱离”出来，并创建一个独立的、关联到原主事件的覆盖事件。
*   **FR4.2.4 (时区处理)**:
    *   所有时间在后端必须以**UTC**存储。
    *   重复日程的RRULE必须与其定义的**时区(TZID)**绑定。所有实例的计算都必须在该时区下进行，然后再转换为UTC存储。
    *   API在返回数据时，应能根据请求头或用户偏好，将UTC时间转换为目标时区的时间。

#### 4.3. 提醒与通知
*   **FR4.3.1 (多提醒)**: 用户可以为一个日程设置多个提醒（如提前15分钟Push，提前1天Email）。
*   **FR4.3.2 (提醒调度)**: 系统必须有一个可靠的后台调度器，在提醒时间到达时，将`ReminderDueEvent`发布到消息队列。该事件需包含足够信息供`notification-dispatch-service`渲染和发送。

#### 4.4. 忙闲状态(Free/Busy)查询
*   **FR4.4.1 (内部API)**: 系统必须提供一个`POST /internal/free-busy`接口，接收一个或多个用户ID和时间范围。
*   **FR4.4.2 (精确计算)**:
    *   返回结果是这些用户在该时间范围内的所有忙碌时间段列表。
    *   查询必须尊重共享权限。如果A对B只有`FREE_BUSY_ONLY`权限，则只能看到B的忙碌时段，看不到日程详情。
    *   查询必须能处理“暂定(Tentative)”状态的日程，可以将其作为“可能忙碌”返回。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部/客户端gRPC API接口
*   **Package**: `hina.vip.schedule.v1`
*   **认证**: User JWT, S2S Auth, Admin Role。
*   **核心RPC**:
    ```protobuf
    service ScheduleService {
      // 日历管理
      rpc ListCalendars(ListCalendarsRequest) returns (ListCalendarsResponse);
      rpc CreateCalendar(CreateCalendarRequest) returns (Calendar);

      // 事件管理
      rpc ListEventInstances(ListEventInstancesRequest) returns (ListEventInstancesResponse);
      rpc CreateEvent(CreateEventRequest) returns (Event);
      rpc UpdateEvent(UpdateEventRequest) returns (Event);
      rpc DeleteEvent(DeleteEventRequest) returns (google.protobuf.Empty);

      // 忙闲查询 (内部)
      rpc GetFreeBusy(GetFreeBusyRequest) returns (GetFreeBusyResponse);
    }

    message ListEventInstancesRequest {
      repeated string calendar_ids = 1;
      google.protobuf.Timestamp start_time = 2;
      google.protobuf.Timestamp end_time = 3;
      string time_zone = 4;
    }
    ```

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`calendars`**: `id (PK)`, `user_id (FK)`, `name`, `color`, `is_primary`.
*   **`events` (主事件表)**:
    *   `id (PK)`, `calendar_id (FK)`, `creator_user_id (FK)`
    *   `title`, `description`
    *   `start_time_utc`, `end_time_utc`, `time_zone`
    *   `is_all_day`
    *   **`rrule_string (TEXT, nullable)`**: 存储重复规则。
*   **`event_overrides` (实例覆盖表)**:
    *   `id (PK)`, `recurring_master_event_id (FK)`
    *   `original_instance_start_time_utc`: 标识被覆盖的是哪个实例。
    *   `new_start_time_utc`, `new_end_time_utc`, `new_title`, ... (存储所有被修改的字段)
*   **`event_exceptions` (例外日期表)**:
    *   `recurring_master_event_id (FK)`
    *   `exception_start_time_utc`: 存储被删除的实例的原始开始时间。
*   **`event_attendees`**: `event_id`, `user_id`, `email`, `rsvp_status`.
*   **`calendar_shares`**: `calendar_id`, `grantee_user_id`, `permission_level`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **日程查询延迟**: `ListEventInstances`是性能关键。对于一个月视图，P99延迟应 `< 500ms`。
*   **优化策略**: 对重复日程的展开结果进行**缓存**是必要的。缓存的Key可以是`(calendar_ids, month, year, timezone)`。当该日历有任何变更时，必须使相关缓存失效。
*   **CRUD API延迟**: P99延迟应 `< 150ms`。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **数据准确性**: RRULE计算必须100%准确。时区处理绝不能出错。
*   **提醒可靠性**: 提醒调度必须可靠，不能丢失提醒。

#### 7.3. 可扩展性需求
*   服务可水平扩展。数据库可通过读写分离、分区进行扩展。
*   重复日程展开的计算密集型操作可以考虑用专门的只读Worker来异步计算和预热缓存。

#### 7.4. 安全性需求
*   用户只能访问和修改自己的日程，或被明确授权共享的日程。**权限检查是所有API的强制要求**。
*   防止通过构造恶意的、无限循环的RRULE导致服务资源耗尽(DoS)。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **RRULE库**: 这是核心技术难点。必须选择一个健壮的、经过充分测试的iCalendar RRULE解析和实例生成库（如Go的`teambition/rrule-go`），或投入资源对其进行扩展和加固。
*   **时区处理**: 必须使用Go标准库的`time.Location`和标准的IANA时区数据库。所有时间在后端存储和计算时，**强制使用UTC**。
*   **提醒调度**: 使用Kubernetes CronJob或Go的`gocron`等高精度调度器，定期扫描并发布`ReminderDueEvent`到消息队列。

---
这份版本2.0的SRS文档为`schedule-service`构建了一个功能强大、逻辑严谨、性能可靠的时间管理核心。它通过对复杂重复日程和多日历共享的精确支持，能够极大地赋能平台上的其他业务，并为用户提供世界一流的个人日程管理体验。