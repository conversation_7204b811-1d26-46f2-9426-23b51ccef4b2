/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:00:00
 * Modified: 2025-01-23 18:05:00
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { useAuthStore } from './auth';
import { User, UserRole, Permission, UserStatus } from '@/types/user';

const mockUser: User = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  avatar: '',
  status: UserStatus.ACTIVE,
  roles: [UserRole.ADMIN],
  permissions: [Permission.USER_VIEW, Permission.CONTENT_VIEW],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  lastLoginAt: new Date().toISOString(),
  emailVerified: true,
  phoneVerified: false,
  twoFactorEnabled: false,
};

const mockToken = 'mock-jwt-token';
const mockRefreshToken = 'mock-refresh-token';

describe('Auth Store (Zustand)', () => {
  // Reset store and localStorage before each test
  beforeEach(() => {
    useAuthStore.getState().logout();
    localStorage.clear();
  });

  it('should have the correct initial state', () => {
    const { isAuthenticated, user, token, refreshToken } = useAuthStore.getState();
    expect(isAuthenticated).toBe(false);
    expect(user).toBeNull();
    expect(token).toBeNull();
    expect(refreshToken).toBeNull();
  });

  it('should handle login correctly', () => {
    useAuthStore.getState().login(mockToken, mockRefreshToken, mockUser);
    
    const { isAuthenticated, user, token, refreshToken } = useAuthStore.getState();
    expect(isAuthenticated).toBe(true);
    expect(user).toEqual(mockUser);
    expect(token).toBe(mockToken);
    expect(refreshToken).toBe(mockRefreshToken);
  });

  it('should persist state to localStorage on login', () => {
    useAuthStore.getState().login(mockToken, mockRefreshToken, mockUser);
    
    const persistedState = JSON.parse(localStorage.getItem('auth-storage') || '{}');
    expect(persistedState.state.token).toBe(mockToken);
    expect(persistedState.state.user).toEqual(mockUser);
  });

  it('should handle logout correctly', () => {
    // First, log in
    useAuthStore.getState().login(mockToken, mockRefreshToken, mockUser);
    
    // Then, log out
    useAuthStore.getState().logout();

    const { isAuthenticated, user, token, refreshToken } = useAuthStore.getState();
    expect(isAuthenticated).toBe(false);
    expect(user).toBeNull();
    expect(token).toBeNull();
    expect(refreshToken).toBeNull();
  });

  it('should clear localStorage on logout', () => {
    useAuthStore.getState().login(mockToken, mockRefreshToken, mockUser);
    useAuthStore.getState().logout();
    
    const persistedState = localStorage.getItem('auth-storage');
    expect(persistedState).toBeNull();
  });

  describe('hasPermission', () => {
    it('should return true if user has the required permission', () => {
      useAuthStore.getState().login(mockToken, mockRefreshToken, mockUser);
      const { hasPermission } = useAuthStore.getState();
      
      expect(hasPermission(Permission.USER_VIEW)).toBe(true);
    });

    it('should return false if user does not have the required permission', () => {
      useAuthStore.getState().login(mockToken, mockRefreshToken, mockUser);
      const { hasPermission } = useAuthStore.getState();
      
      expect(hasPermission(Permission.SYSTEM_CONFIG)).toBe(false);
    });

    it('should return false if user is not authenticated', () => {
      const { hasPermission } = useAuthStore.getState();
      expect(hasPermission(Permission.USER_VIEW)).toBe(false);
    });
  });
}); 