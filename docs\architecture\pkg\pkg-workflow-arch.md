好的，遵照您的指示。我将根据我们刚才讨论的、版本2.0的`pkg/workflow` SRS，为您生成一份专门针对 **`pkg/workflow`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将详细阐述其作为**平台通用流程编排内核**的技术实现细节，包括**数据结构、执行器生命周期、并发模型、数据流表达式处理、以及错误和可观测性**等核心机制，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `pkg/workflow` (工作流引擎内核) 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-28
**参考SRS**: `pkg/workflow-srs.md` (v2.0)
**核心架构**: 并发DAG执行器 + 表达式驱动的数据/控制流

## 1. 概述

`pkg/workflow` 的架构核心是构建一个**高效、可扩展、且对上层服务友好的嵌入式库**。它必须在不引入过多复杂性的前提下，提供强大的流程编排能力。本架构设计的核心是将工作流的**静态定义(Definition)**、**运行时状态(State)**和**执行逻辑(Execution)**三者清晰地分离。

**核心组件**:
1.  **Definition Layer**: 定义工作流的结构（`Workflow`, `Node`, `Edge`）。
2.  **State Layer**: 定义运行时的数据上下文（`ExecutionState`）。
3.  **Execution Layer**: 包含`Executor`（执行器）、`NodeRegistry`（节点注册表）和`ExpressionEvaluator`（表达式求值器）。

---

## 2. 架构图与目录结构

### 2.1 核心架构与交互图

```mermaid
graph TD
    subgraph "Caller Service (e.g., routines-service)"
        A[Workflow Definition (JSON)]
        B[Initial State Data]
        C[Custom NodeExecutor Impls]
    end

    subgraph "pkg/workflow"
        subgraph "Definition Layer"
            D[Workflow Structs<br/>(Node, Edge)]
        end

        subgraph "State Layer"
            E[ExecutionState Struct]
        end

        subgraph "Execution Layer"
            F[Executor]
            G[Node Registry]
            H[Expression Evaluator<br/>(expr)]
            I[DAG Runner]
        end
        
        subgraph "Interface"
            J[NodeExecutor Interface]
        end
    end

    A -- "1. Unmarshal into" --> D
    B -- "2. Initialize" --> E
    C -- "3. Implements" --> J
    
    subgraph "Initialization Phase (at service start)"
        C -- "4. Register with typeName" --> G
    end

    subgraph "Execution Phase (per request)"
        D & E -- "5. Input to" --> F
        F -- "Gets registry" --> G
        F -- "Uses" --> I
        I -- "Resolves inputs using" --> H
        I -- "Gets NodeExecutor from" --> G
        I -- "Calls" --> J
        J -- "Modifies" --> E
        H -- "Reads from" --> E
        F -- "Returns final" --> E
    end
```

### 2.2 最终目录结构 (`pkg/workflow/`)

```
workflow/
├── definition.go           # 定义 Workflow, Node, Edge 结构体和解析逻辑
├── state.go                # 定义 ExecutionState 及其助手方法
├── executor.go             # 定义 Executor 结构体和其公共方法 (Run, Register)
├── executor_opts.go        # 定义 Executor 的配置选项 (e.g., WithConcurrency, WithHooks)
├── interface.go            # 定义 NodeExecutor 接口
├── dag_runner.go           # ✨ 核心: DAG的拓扑排序和并发执行逻辑 ✨
├── expression.go           # ✨ 核心: 表达式求值器的封装 (JSONPath/expr) ✨
├── nodes/                  # 内置的、与业务无关的节点实现
│   ├── core_if.go          # 'core.if' 条件节点
│   ├── core_switch.go      # 'core.switch' 分支节点
│   └── core_transform.go   # 'core.transform' 数据转换节点
├── errors.go               # 定义本包特定的错误类型 (e.g., CycleDetectedError)
└── workflow_test.go        # 核心集成测试
```
---

## 3. 各核心组件深度解析

### 3.1 `definition.go` - 定义层

*   **`Workflow`, `Node`, `Edge` structs**:
    *   使用`json`标签，使其能直接从上层服务（如`routines-service`）存储的JSON定义中反序列化。
    *   `Node.Inputs`被定义为`map[string]interface{}`，以支持任意复杂的输入结构。
    *   `Edge`结构将包含`FromNodeID`, `ToNodeID`, `FromOutputKey`, `ToInputKey`, 和可选的`Condition`字符串。

### 3.2 `state.go` - 状态层

*   **`ExecutionState` struct**:
    *   `data map[string]interface{}`: 这是核心。它是一个可被表达式引擎访问的动态数据容器。其顶层键包括`trigger`和`nodes`。
    *   `mu sync.RWMutex`: 由于节点会并发执行并写入状态，**对`data`的任何读写都必须由读写锁保护**。
    *   提供`GetValue(path string)`和`SetValue(path string, value interface{})`等线程安全的方法，这两个方法内部会使用`expression.go`的逻辑来解析路径。

### 3.3 `expression.go` - 表达式求值器

*   **职责**: 封装对`antonmedv/expr`库的调用。
*   **`Evaluator` struct**:
    *   在创建时，可以预先注册一些自定义的辅助函数，如`len()`, `upper()`, `contains()`等，供工作流定义中的表达式使用。
*   **`Eval(expression string, env map[string]interface{}) (interface{}, error)` method**:
    *   `env`就是`ExecutionState.data`。
    *   **安全**: `expr`库默认是内存安全的，但需要确保禁用了任何可能导致不安全操作的功能（如访问操作系统）。
    *   **性能**: `expr`会将表达式编译成字节码，对于会重复求值的表达式（如在循环中），可以缓存编译结果以提升性能。

### 3.4 `dag_runner.go` - DAG执行器

这是引擎的“心脏”，负责实际的执行流。

*   **`dagRunner` struct**: 包含`graph`（邻接表表示）, `inDegrees` (入度map), `nodeExecutors` (从`Executor`传入的注册表), `state`等。
*   **`Run()` method**:
    1.  **初始化**:
        *   调用`buildGraph()`，遍历`Workflow.Nodes`和`Workflow.Edges`，构建邻接表和计算每个节点的初始入度。
        *   在此过程中，调用`detectCycle()`进行**环路检测**，如果发现环，则立即返回`CycleDetectedError`。
        *   找到所有入度为0的节点，放入一个“就绪”channel (`readyQueue`)。
    2.  **并发执行循环**:
        *   启动一个固定数量的**worker goroutine**（由`Executor`的`concurrency`选项决定）。
        *   所有worker都从`readyQueue`中消费节点任务。
        *   主goroutine使用`sync.WaitGroup`来等待所有节点执行完成。
    3.  **worker goroutine的逻辑**:
        a. 从`readyQueue`取出一个`nodeID`。
        b. **解析输入**:
            *   获取`workflow.Nodes[nodeID].Inputs`。
            *   遍历`inputs` map，如果值是表达式，则调用`expression.Evaluator.Eval()`，使用当前的`ExecutionState`进行求值。
        c. **执行节点**:
            *   从`nodeExecutors`注册表中找到`node.Type`对应的`NodeExecutor`实例。
            *   调用`executor.Execute(ctx, state, resolvedInputs)`。
        d. **处理结果**:
            *   如果`Execute`返回错误：
                *   将错误信息写入`ExecutionState`的`nodes[nodeID].error`。
                *   如果错误策略是`stop_on_error`，则向一个`cancel` channel发送信号，中断整个工作流。
            *   如果成功：
                *   将`outputs`写入`ExecutionState`的`nodes[nodeID].outputs`。
                *   **更新依赖**:
                    *   获取该节点的所有出边(outgoing edges)。
                    *   对于每条边，评估其`condition`表达式。
                    *   如果条件满足，将其目标节点的入度原子性地减1。
                    *   如果目标节点的入度变为0，则将其`nodeID`推入`readyQueue`。
    4.  **结束**: 当`WaitGroup`完成且`readyQueue`为空时，主goroutine返回最终的`ExecutionState`。

### 3.5 `executor.go` - 公共入口

*   `Executor`结构体是面向用户的API。
*   它的`Run`方法主要是一个封装器，它创建`dagRunner`实例，然后调用`dagRunner.Run()`，并处理最终的返回结果和错误。
*   它还负责管理`NodeRegistry`，确保在多goroutine环境中对注册表的并发访问是安全的。

### 3.6 `nodes/` - 内置节点

*   `core_if.go`:
    *   `IfNodeExecutor`的`Execute`方法接收一个`condition`输入，求值后返回一个`{"branch": true/false}`的输出。
*   `core_transform.go`:
    *   `TransformNodeExecutor`接收一个`template` map作为输入，如`{"full_name": "{{ .nodes.n1.out.first + ' ' + .nodes.n1.out.last }}"}`。
    *   它遍历这个template，对每个值进行求值，并返回一个新的map作为输出。

## 4. 总结

本架构设计通过以下关键点，构建了一个生产级的`pkg/workflow`内核：
1.  **清晰分层**: 定义、状态、执行三层分离，职责明确。
2.  **并发执行**: 通过基于拓扑排序和并发worker的DAG执行模型，实现了高性能。
3.  **强大的数据与控制流**: 利用表达式引擎，将数据映射和条件分支的逻辑从执行器中解耦出来，使其完全由JSON定义驱动，极大地增强了灵活性。
4.  **线程安全**: 通过对`ExecutionState`的并发访问进行加锁，确保了在并发模型下的数据一致性。
5.  **插件式节点**: `NodeExecutor`接口和注册表机制，使得上层服务可以像搭积木一样，轻松地为引擎添加新的能力。

这个内核虽然是无状态的、嵌入式的，但其强大的编排能力足以支撑`ai-assistant-service`的动态Agent工作流和`routines-service`的用户自定义自动化，是CINA.CLUB平台自动化和智能化的核心驱动力。