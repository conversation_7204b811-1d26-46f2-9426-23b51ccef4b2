//go:build wasm

package crypto

import (
	"encoding/base64"
	"encoding/json"
	"syscall/js"
)

// 导出到JavaScript的全局函数

func init() {
	js.Global().Set("cinaE2EE", js.ValueOf(map[string]interface{}{
		"encryptWithPassword": js.<PERSON>(encryptWithPasswordJS),
		"decryptWithPassword": js.<PERSON>c<PERSON>(decryptWithPasswordJS),
		"encryptWithKey":      js.<PERSON>c<PERSON><PERSON>(encryptWithKeyJS),
		"decryptWithKey":      js.Func<PERSON>f(decryptWithKeyJS),
		"generateKey":         js.FuncOf(generateKeyJS),
	}))
}

var wasmEngine = NewE2EEEngine()

// encryptWithPasswordJS JavaScript包装器
func encryptWithPasswordJS(this js.Value, args []js.Value) interface{} {
	if len(args) != 2 {
		return js.ValueOf(map[string]interface{}{
			"error": "encryptWithPassword requires 2 arguments: data, password",
		})
	}

	// 从base64解码数据
	dataStr := args[0].String()
	data, err := base64.StdEncoding.DecodeString(dataStr)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to decode data: " + err.Error(),
		})
	}

	password := args[1].String()

	encData, err := wasmEngine.EncryptWithPassword(data, password)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 将加密数据转换为JSON
	jsonData, err := json.Marshal(encData)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to marshal result: " + err.Error(),
		})
	}

	return js.ValueOf(map[string]interface{}{
		"result": string(jsonData),
	})
}

// decryptWithPasswordJS JavaScript包装器
func decryptWithPasswordJS(this js.Value, args []js.Value) interface{} {
	if len(args) != 2 {
		return js.ValueOf(map[string]interface{}{
			"error": "decryptWithPassword requires 2 arguments: encryptedData, password",
		})
	}

	encDataJSON := args[0].String()
	password := args[1].String()

	var encData EncryptedData
	if err := json.Unmarshal([]byte(encDataJSON), &encData); err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to parse encrypted data: " + err.Error(),
		})
	}

	data, err := wasmEngine.DecryptWithPassword(&encData, password)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 将解密数据编码为base64
	result := base64.StdEncoding.EncodeToString(data)

	return js.ValueOf(map[string]interface{}{
		"result": result,
	})
}

// encryptWithKeyJS JavaScript包装器
func encryptWithKeyJS(this js.Value, args []js.Value) interface{} {
	if len(args) != 2 {
		return js.ValueOf(map[string]interface{}{
			"error": "encryptWithKey requires 2 arguments: data, key",
		})
	}

	// 从base64解码数据和密钥
	dataStr := args[0].String()
	data, err := base64.StdEncoding.DecodeString(dataStr)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to decode data: " + err.Error(),
		})
	}

	keyStr := args[1].String()
	key, err := base64.StdEncoding.DecodeString(keyStr)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to decode key: " + err.Error(),
		})
	}

	encData, err := wasmEngine.EncryptWithKey(data, key)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 将加密数据转换为JSON
	jsonData, err := json.Marshal(encData)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to marshal result: " + err.Error(),
		})
	}

	return js.ValueOf(map[string]interface{}{
		"result": string(jsonData),
	})
}

// decryptWithKeyJS JavaScript包装器
func decryptWithKeyJS(this js.Value, args []js.Value) interface{} {
	if len(args) != 2 {
		return js.ValueOf(map[string]interface{}{
			"error": "decryptWithKey requires 2 arguments: encryptedData, key",
		})
	}

	encDataJSON := args[0].String()
	keyStr := args[1].String()

	var encData EncryptedData
	if err := json.Unmarshal([]byte(encDataJSON), &encData); err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to parse encrypted data: " + err.Error(),
		})
	}

	key, err := base64.StdEncoding.DecodeString(keyStr)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": "failed to decode key: " + err.Error(),
		})
	}

	data, err := wasmEngine.DecryptWithKey(&encData, key)
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 将解密数据编码为base64
	result := base64.StdEncoding.EncodeToString(data)

	return js.ValueOf(map[string]interface{}{
		"result": result,
	})
}

// generateKeyJS JavaScript包装器
func generateKeyJS(this js.Value, args []js.Value) interface{} {
	key, err := wasmEngine.GenerateKey()
	if err != nil {
		return js.ValueOf(map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 将密钥编码为base64
	result := base64.StdEncoding.EncodeToString(key)

	return js.ValueOf(map[string]interface{}{
		"result": result,
	})
}
