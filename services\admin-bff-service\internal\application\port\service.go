/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package port

import (
	"context"
	"time"

	"cina.club/services/admin-bff-service/internal/domain/model"
)

// BFFService Admin BFF service core interface
type BFFService interface {
	// Authentication and session management
	CreateSession(ctx context.Context, employee *model.Employee, ipAddress, userAgent string) (*model.AdminSession, error)
	ValidateSession(ctx context.Context, sessionID string) (*model.AdminSession, error)
	RefreshSession(ctx context.Context, sessionID string) (*model.AdminSession, error)
	DestroySession(ctx context.Context, sessionID string) error
	DestroyAllUserSessions(ctx context.Context, employeeID string) error

	// User management aggregated API
	GetUsers(ctx context.Context, filter UserFilter) (*UsersResponse, error)
	GetUserFullProfile(ctx context.Context, userID string) (*UserFullProfileDTO, error)
	SuspendUser(ctx context.Context, actorInfo *ActorInfo, userID string, reason string) error
	RestoreUser(ctx context.Context, actorInfo *ActorInfo, userID string, reason string) error
	UpdateUserStatus(ctx context.Context, actorInfo *ActorInfo, userID string, status string) error

	// Content management aggregated API
	GetModerationQueue(ctx context.Context, filter ModerationFilter) (*ModerationQueueResponse, error)
	ApproveContent(ctx context.Context, actorInfo *ActorInfo, contentID string, reason string) error
	RejectContent(ctx context.Context, actorInfo *ActorInfo, contentID string, reason string) error

	// Order and payment management aggregated API
	GetOrders(ctx context.Context, filter OrderFilter) (*OrdersResponse, error)
	GetOrderDetails(ctx context.Context, orderID string) (*OrderDetailsDTO, error)
	CancelOrder(ctx context.Context, actorInfo *ActorInfo, orderID string, reason string) error
	RefundOrder(ctx context.Context, actorInfo *ActorInfo, orderID string, amount float64, reason string) error

	// Analytics and reporting aggregated API
	GetDashboardSummary(ctx context.Context) (*DashboardSummaryDTO, error)
	GetUserAnalytics(ctx context.Context, timeRange TimeRange) (*UserAnalyticsDTO, error)
	GetContentAnalytics(ctx context.Context, timeRange TimeRange) (*ContentAnalyticsDTO, error)
	GetRevenueAnalytics(ctx context.Context, timeRange TimeRange) (*RevenueAnalyticsDTO, error)

	// System management API
	GetSystemHealth(ctx context.Context) (*SystemHealthDTO, error)
	GetAuditLogs(ctx context.Context, filter model.AuditLogFilter) (*AuditLogsResponse, error)
}

// ActorInfo operator information
type ActorInfo struct {
	EmployeeID string
	Email      string
	Roles      []string
	IPAddress  string
	UserAgent  string
}

// UserFilter user filter
type UserFilter struct {
	Email       string
	Status      string
	Role        string
	Level       int
	CreatedFrom *time.Time
	CreatedTo   *time.Time
	SearchTerm  string
	SortBy      string
	SortOrder   string
	Page        int
	PageSize    int
}

// UsersResponse user list response
type UsersResponse struct {
	Users      []*UserSummaryDTO `json:"users"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// UserSummaryDTO user summary DTO
type UserSummaryDTO struct {
	ID             string     `json:"id"`
	Email          string     `json:"email"`
	Username       string     `json:"username"`
	DisplayName    string     `json:"display_name"`
	Status         string     `json:"status"`
	Level          int        `json:"level"`
	CreatedAt      time.Time  `json:"created_at"`
	LastLoginAt    *time.Time `json:"last_login_at"`
	FollowersCount int        `json:"followers_count"`
	IsVerified     bool       `json:"is_verified"`
	IsPremium      bool       `json:"is_premium"`
}

// UserFullProfileDTO user complete profile DTO
type UserFullProfileDTO struct {
	// Basic information
	ID          string     `json:"id"`
	Email       string     `json:"email"`
	Username    string     `json:"username"`
	DisplayName string     `json:"display_name"`
	Avatar      string     `json:"avatar"`
	Bio         string     `json:"bio"`
	Status      string     `json:"status"`
	Level       int        `json:"level"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	LastLoginAt *time.Time `json:"last_login_at"`

	// Verification information
	IsVerified     bool       `json:"is_verified"`
	VerifiedAt     *time.Time `json:"verified_at"`
	KYCStatus      string     `json:"kyc_status"`
	KYCCompletedAt *time.Time `json:"kyc_completed_at"`

	// Social statistics
	FollowersCount int `json:"followers_count"`
	FollowingCount int `json:"following_count"`
	PostsCount     int `json:"posts_count"`
	LikesCount     int `json:"likes_count"`

	// Subscription information
	IsPremium        bool       `json:"is_premium"`
	PremiumStartAt   *time.Time `json:"premium_start_at"`
	PremiumExpireAt  *time.Time `json:"premium_expire_at"`
	SubscriptionPlan string     `json:"subscription_plan"`

	// Financial information
	CoinBalance    float64 `json:"coin_balance"`
	TotalSpent     float64 `json:"total_spent"`
	TotalEarned    float64 `json:"total_earned"`
	UnpaidInvoices int     `json:"unpaid_invoices"`

	// Content statistics
	PublishedContent int `json:"published_content"`
	PendingContent   int `json:"pending_content"`
	RejectedContent  int `json:"rejected_content"`

	// Behavior statistics
	LoginCount   int        `json:"login_count"`
	LastActiveAt *time.Time `json:"last_active_at"`
	DeviceCount  int        `json:"device_count"`

	// Risk information
	WarningCount    int        `json:"warning_count"`
	SuspensionCount int        `json:"suspension_count"`
	LastSuspendedAt *time.Time `json:"last_suspended_at"`
	ReportedCount   int        `json:"reported_count"`
}

// ModerationFilter content moderation filter
type ModerationFilter struct {
	Status      string
	ContentType string
	ReporterID  string
	CreatedFrom *time.Time
	CreatedTo   *time.Time
	Priority    string
	Page        int
	PageSize    int
}

// ModerationQueueResponse content moderation queue response
type ModerationQueueResponse struct {
	Tasks      []*ModerationTaskDTO `json:"tasks"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	TotalPages int                  `json:"total_pages"`
}

// ModerationTaskDTO content moderation task DTO
type ModerationTaskDTO struct {
	ID          string     `json:"id"`
	ContentID   string     `json:"content_id"`
	ContentType string     `json:"content_type"`
	Title       string     `json:"title"`
	AuthorID    string     `json:"author_id"`
	AuthorName  string     `json:"author_name"`
	Status      string     `json:"status"`
	Priority    string     `json:"priority"`
	ReportCount int        `json:"report_count"`
	CreatedAt   time.Time  `json:"created_at"`
	AssignedTo  string     `json:"assigned_to"`
	ReviewedAt  *time.Time `json:"reviewed_at"`
	ReviewedBy  string     `json:"reviewed_by"`
}

// OrderFilter order filter
type OrderFilter struct {
	UserID      string
	Status      string
	ServiceID   string
	CreatedFrom *time.Time
	CreatedTo   *time.Time
	AmountFrom  *float64
	AmountTo    *float64
	Page        int
	PageSize    int
}

// OrdersResponse order list response
type OrdersResponse struct {
	Orders     []*OrderSummaryDTO `json:"orders"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

// OrderSummaryDTO order summary DTO
type OrderSummaryDTO struct {
	ID          string     `json:"id"`
	UserID      string     `json:"user_id"`
	UserName    string     `json:"user_name"`
	ServiceID   string     `json:"service_id"`
	ServiceName string     `json:"service_name"`
	Status      string     `json:"status"`
	Amount      float64    `json:"amount"`
	Currency    string     `json:"currency"`
	CreatedAt   time.Time  `json:"created_at"`
	PaidAt      *time.Time `json:"paid_at"`
}

// OrderDetailsDTO order details DTO
type OrderDetailsDTO struct {
	OrderSummaryDTO
	Description   string                 `json:"description"`
	Items         []OrderItemDTO         `json:"items"`
	PaymentMethod string                 `json:"payment_method"`
	PaymentStatus string                 `json:"payment_status"`
	InvoiceURL    string                 `json:"invoice_url"`
	RefundAmount  float64                `json:"refund_amount"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// OrderItemDTO order item DTO
type OrderItemDTO struct {
	ID       string  `json:"id"`
	Name     string  `json:"name"`
	Quantity int     `json:"quantity"`
	Price    float64 `json:"price"`
	Total    float64 `json:"total"`
}

// TimeRange time range
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// DashboardSummaryDTO dashboard summary DTO
type DashboardSummaryDTO struct {
	// User statistics
	TotalUsers    int64 `json:"total_users"`
	ActiveUsers   int64 `json:"active_users"`
	NewUsersToday int64 `json:"new_users_today"`
	PremiumUsers  int64 `json:"premium_users"`

	// Content statistics
	TotalContent      int64 `json:"total_content"`
	PendingModeration int64 `json:"pending_moderation"`
	PublishedToday    int64 `json:"published_today"`

	// Financial statistics
	TotalRevenue    float64 `json:"total_revenue"`
	RevenueToday    float64 `json:"revenue_today"`
	PendingPayments float64 `json:"pending_payments"`

	// System statistics
	SystemHealth   string  `json:"system_health"`
	ActiveSessions int64   `json:"active_sessions"`
	ErrorRate      float64 `json:"error_rate"`
}

// UserAnalyticsDTO user analytics DTO
type UserAnalyticsDTO struct {
	TimeRange    TimeRange       `json:"time_range"`
	NewUsers     []DailyMetric   `json:"new_users"`
	ActiveUsers  []DailyMetric   `json:"active_users"`
	UsersByLevel []LevelMetric   `json:"users_by_level"`
	Retention    RetentionMetric `json:"retention"`
}

// ContentAnalyticsDTO content analytics DTO
type ContentAnalyticsDTO struct {
	TimeRange         TimeRange         `json:"time_range"`
	NewContent        []DailyMetric     `json:"new_content"`
	ContentByType     []TypeMetric      `json:"content_by_type"`
	ModerationMetrics ModerationMetrics `json:"moderation_metrics"`
}

// RevenueAnalyticsDTO revenue analytics DTO
type RevenueAnalyticsDTO struct {
	TimeRange       TimeRange       `json:"time_range"`
	DailyRevenue    []DailyMetric   `json:"daily_revenue"`
	RevenueBySource []SourceMetric  `json:"revenue_by_source"`
	TopServices     []ServiceMetric `json:"top_services"`
}

// SystemHealthDTO system health status DTO
type SystemHealthDTO struct {
	OverallStatus string             `json:"overall_status"`
	Services      []ServiceHealthDTO `json:"services"`
	Metrics       SystemMetricsDTO   `json:"metrics"`
	LastChecked   time.Time          `json:"last_checked"`
}

// ServiceHealthDTO service health status DTO
type ServiceHealthDTO struct {
	Name         string    `json:"name"`
	Status       string    `json:"status"`
	ResponseTime int64     `json:"response_time_ms"`
	LastChecked  time.Time `json:"last_checked"`
	ErrorRate    float64   `json:"error_rate"`
}

// SystemMetricsDTO system metrics DTO
type SystemMetricsDTO struct {
	CPUUsage    float64         `json:"cpu_usage"`
	MemoryUsage float64         `json:"memory_usage"`
	DiskUsage   float64         `json:"disk_usage"`
	NetworkIO   NetworkIOMetric `json:"network_io"`
}

// AuditLogsResponse audit logs response
type AuditLogsResponse struct {
	Logs       []*model.AuditLogEntry `json:"logs"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// Helper type definitions
type DailyMetric struct {
	Date  time.Time `json:"date"`
	Value int64     `json:"value"`
}

type LevelMetric struct {
	Level int   `json:"level"`
	Count int64 `json:"count"`
}

type RetentionMetric struct {
	Day1  float64 `json:"day_1"`
	Day7  float64 `json:"day_7"`
	Day30 float64 `json:"day_30"`
}

type TypeMetric struct {
	Type  string `json:"type"`
	Count int64  `json:"count"`
}

type ModerationMetrics struct {
	Pending  int64 `json:"pending"`
	Approved int64 `json:"approved"`
	Rejected int64 `json:"rejected"`
}

type SourceMetric struct {
	Source string  `json:"source"`
	Amount float64 `json:"amount"`
}

type ServiceMetric struct {
	ServiceID   string  `json:"service_id"`
	ServiceName string  `json:"service_name"`
	Revenue     float64 `json:"revenue"`
	OrderCount  int64   `json:"order_count"`
}

type NetworkIOMetric struct {
	BytesIn  int64 `json:"bytes_in"`
	BytesOut int64 `json:"bytes_out"`
}
