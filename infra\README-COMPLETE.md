# CINA.CLUB Infrastructure as Code (IaC)

*Copyright (c) 2025 Cina.Club, All rights reserved*  
*Created: 2025-06-27, Modified: 2025-06-27*

## 概览

本目录包含CINA.CLUB平台的完整基础设施即代码(Infrastructure as Code, IaC)解决方案。我们采用**声明式**的方法，通过代码定义和管理所有基础设施资源，确保环境的**一致性、可重复性和可追溯性**。

## 架构原则

### 核心理念: "云原生 + 基础设施即代码"

```mermaid
graph TB
    A[Terraform] -->|创建云资源| B[AWS Infrastructure]
    B --> C[EKS Cluster]
    C --> D[Kubernetes Workloads]
    E[Docker Images] -->|部署到| D
    F[Kustomize] -->|管理配置| D
    
    subgraph "环境隔离"
        G[Dev Environment]
        H[Staging Environment] 
        I[Prod Environment]
    end
    
    D --> G
    D --> H
    D --> I
```

### 设计原则

1. **声明式 (Declarative)**: 描述"最终状态"而非"执行步骤"
2. **不可变基础设施 (Immutable Infrastructure)**: 通过替换而非修改实现变更
3. **环境一致性 (Environment Parity)**: dev/staging/prod环境配置高度相似
4. **最小权限原则 (Least Privilege)**: 严格的安全和权限控制
5. **成本优化 (Cost Optimization)**: 智能资源管理和自动伸缩

## 目录结构

```
infra/
├── docker/                    # 容器化基础
│   ├── base/                 # 基础镜像模板
│   │   ├── Dockerfile.go     # Go服务标准镜像
│   │   └── Dockerfile.python # Python服务标准镜像
│   ├── services/             # 微服务Dockerfile
│   │   ├── user-core-service.Dockerfile
│   │   ├── ai-assistant-service.Dockerfile
│   │   └── ... (35+ 微服务)
│   └── dev/                  # 本地开发环境
│       ├── docker-compose.yml
│       └── config/
├── kubernetes/               # Kubernetes部署配置
│   ├── base/                # 基础K8s资源模板
│   │   ├── user-core-service/
│   │   ├── ai-assistant-service/
│   │   └── ... (按服务组织)
│   ├── overlays/            # 环境特定配置
│   │   ├── dev/             # 开发环境
│   │   ├── staging/         # 预发布环境
│   │   └── prod/            # 生产环境
│   └── system/              # 集群级系统组件
│       ├── ingress-nginx.yaml
│       ├── cert-manager.yaml
│       ├── prometheus.yaml
│       └── fluentd-daemonset.yaml
├── terraform/               # 云资源编排
│   ├── modules/            # 可复用模块
│   │   ├── vpc/            # VPC网络模块
│   │   ├── eks/            # EKS集群模块
│   │   ├── rds/            # RDS数据库模块
│   │   ├── redis/          # Redis缓存模块
│   │   └── kafka/          # Kafka消息队列模块
│   └── environments/       # 环境配置
│       ├── dev/            # 开发环境
│       ├── staging/        # 预发布环境
│       └── prod/           # 生产环境
└── scripts/               # 自动化脚本
    ├── deploy.sh          # 部署脚本
    ├── health-check.sh    # 健康检查脚本
    └── cleanup.sh         # 资源清理脚本
```

## 技术栈

### 核心工具

| 工具 | 版本 | 用途 |
|------|------|------|
| **Terraform** | ≥1.5 | 云资源编排 |
| **Kubernetes** | ≥1.25 | 容器编排平台 |
| **Kustomize** | ≥5.0 | Kubernetes配置管理 |
| **Docker** | Latest | 容器化平台 |
| **AWS EKS** | 1.28 | 托管Kubernetes服务 |

### 云服务组件

| 服务 | 用途 | 高可用 |
|------|------|--------|
| **Amazon EKS** | Kubernetes托管服务 | ✅ Multi-AZ |
| **Amazon RDS PostgreSQL** | 主数据库 | ✅ Multi-AZ + 读副本 |
| **Amazon ElastiCache Redis** | 缓存层 | ✅ 集群模式 |
| **Amazon MSK** | Kafka消息队列 | ✅ Multi-AZ |
| **Amazon VPC** | 网络隔离 | ✅ 多可用区 |
| **Amazon S3** | 对象存储 | ✅ 跨区域复制 |
| **AWS Secrets Manager** | 密钥管理 | ✅ 自动轮换 |

## 环境架构

### 开发环境 (Dev)
- **目标**: 日常开发和集成测试
- **规模**: 小型 (t3.medium节点)
- **部署**: 自动部署 (每次PR合并)
- **数据**: 模拟数据

### 预发布环境 (Staging)
- **目标**: QA测试和UAT验收
- **规模**: 中型 (t3.large节点)
- **部署**: 手动触发 (发布前)
- **数据**: 生产镜像数据

### 生产环境 (Prod)
- **目标**: 面向用户的正式环境
- **规模**: 大型 (m5.xlarge + Spot实例)
- **部署**: 严格审批流程
- **数据**: 真实用户数据

## 快速开始

### 前置条件

```bash
# 安装必要工具
brew install terraform kubectl kustomize awscli

# 配置AWS凭证
aws configure

# 验证工具版本
terraform --version    # >= 1.5
kubectl version        # >= 1.25
kustomize version      # >= 5.0
```

### 部署完整环境

```bash
# 部署开发环境
./infra/scripts/deploy.sh -e dev

# 部署预发布环境
./infra/scripts/deploy.sh -e staging

# 部署生产环境 (需要额外审批)
./infra/scripts/deploy.sh -e prod
```

### 部署单个服务

```bash
# 部署特定服务到开发环境
./infra/scripts/deploy.sh -e dev -s user-core-service

# 干运行 (查看变更计划)
./infra/scripts/deploy.sh -e staging -d

# 查看部署计划
./infra/scripts/deploy.sh -e prod -a plan
```

### 健康检查

```bash
# 检查环境健康状态
./infra/scripts/health-check.sh -e dev

# 详细健康检查
./infra/scripts/health-check.sh -e prod -v
```

## 微服务清单

### 核心服务 (Core Services)
- **user-core-service**: 用户核心服务
- **ai-assistant-service**: AI助手服务
- **chat-api-service**: 聊天API服务
- **payment-service**: 支付服务

### 社交互动服务 (Social & Interactive)
- **social-service**: 社交关系服务
- **chat-websocket-server**: 实时聊天服务
- **live-api-service**: 直播API服务
- **community-forum-service**: 社区论坛服务

### AI与智能服务 (AI & Intelligence)
- **embedding-service**: 向量嵌入服务
- **content-moderation-service**: 内容审核服务
- **personal-kb-service**: 个人知识库服务
- **memory-service**: 记忆管理服务

### 市场交易服务 (Market & Trading)
- **service-offering-service**: 服务市场
- **review-service**: 评价系统
- **billing-service**: 计费服务
- **gamification-service**: 游戏化服务

### 平台基础设施服务 (Platform Infrastructure)
- **file-storage-service**: 文件存储服务
- **notification-dispatch-service**: 通知分发服务
- **search-service**: 搜索服务
- **analytics-service**: 分析服务

*总计: **39个微服务**, 支持完整的CINA.CLUB平台生态*

## 监控与运维

### 监控栈
- **Prometheus**: 指标收集和存储
- **Grafana**: 可视化仪表板
- **Fluentd**: 日志收集和转发
- **Elasticsearch**: 日志存储和搜索

### 关键指标
- **可用性**: 99.9% (dev), 99.95% (staging), 99.99% (prod)
- **响应时间**: <200ms (P95)
- **错误率**: <0.1%
- **资源利用率**: CPU <70%, Memory <80%

### 告警规则
- 服务不可用 > 1分钟
- 错误率 > 1%
- 响应时间 > 500ms
- 资源利用率 > 90%

## 安全性

### 网络安全
- **VPC隔离**: 每个环境独立的VPC
- **网络策略**: Pod间通信白名单
- **TLS加密**: 所有服务间通信加密
- **WAF防护**: 应用层攻击防护

### 访问控制
- **RBAC**: 基于角色的访问控制
- **IAM**: AWS资源精细化权限
- **服务账户**: K8s服务账户映射IAM角色
- **密钥管理**: AWS Secrets Manager自动轮换

### 数据保护
- **静态加密**: 数据库和存储加密
- **传输加密**: TLS 1.3
- **备份策略**: 自动备份和跨区域复制
- **审计日志**: 完整的操作审计

## 成本优化

### 资源策略
- **Spot实例**: 非生产环境使用Spot实例
- **自动伸缩**: HPA和CA自动调整资源
- **资源限制**: 合理的CPU和内存限制
- **定时缩容**: 非工作时间自动缩容

### 成本监控
- **标签策略**: 细粒度成本分配
- **预算告警**: 成本超额提醒
- **使用分析**: 定期资源使用分析
- **优化建议**: 自动化成本优化建议

## 灾难恢复

### 备份策略
- **数据库**: 每日全量备份 + 实时增量
- **配置**: Git版本控制
- **镜像**: 多地域镜像仓库
- **状态**: Terraform状态文件远程存储

### 恢复流程
1. **评估影响**: 确定故障范围和影响
2. **激活计划**: 启动灾难恢复流程
3. **恢复数据**: 从最近备份恢复数据
4. **重建环境**: 使用IaC快速重建
5. **验证功能**: 全面功能测试
6. **切换流量**: 逐步恢复用户访问

### RTO/RPO目标
- **RTO (恢复时间目标)**: <4小时
- **RPO (恢复点目标)**: <15分钟
- **数据一致性**: 强一致性保证

## 最佳实践

### 开发流程
1. **功能开发** → 本地Docker环境测试
2. **提交PR** → 自动部署到dev环境
3. **代码审查** → DevOps团队审查基础设施变更
4. **合并主分支** → 自动部署到staging环境
5. **QA测试** → 全面功能和性能测试
6. **生产发布** → 严格的发布流程和回滚计划

### 运维准则
- **变更前确认**: 所有变更都要经过plan阶段
- **渐进式发布**: 蓝绿部署或金丝雀发布
- **监控先行**: 部署后立即验证关键指标
- **及时回滚**: 发现问题立即回滚到已知良好状态

### 故障处理
1. **快速响应** (5分钟内)
2. **影响评估** (确定影响范围)
3. **临时缓解** (恢复服务可用性)
4. **根因分析** (找出问题根源)
5. **永久修复** (防止问题重复发生)
6. **文档总结** (更新运维手册)

## 版本历史

### v1.0.0 (2025-06-27)
- ✅ 完整的基础设施即代码体系
- ✅ 39个微服务的Docker镜像
- ✅ Kubernetes部署和配置管理
- ✅ 三环境 (dev/staging/prod) 支持
- ✅ 监控、日志和告警系统
- ✅ 自动化部署和健康检查脚本
- ✅ 完整的安全和合规措施

### 下一步规划 (v1.1.0)
- 🔄 服务网格 (Istio) 集成
- 🔄 GitOps工作流 (ArgoCD)
- 🔄 更多地域的多活架构
- 🔄 AI驱动的智能运维
- 🔄 成本优化自动化

## 联系我们

### 支持团队
- **DevOps团队**: <EMAIL>
- **平台工程**: <EMAIL>
- **安全团队**: <EMAIL>

### 紧急联系
- **故障响应**: +1-XXX-XXX-XXXX
- **安全事件**: <EMAIL>

---

**CINA.CLUB基础设施团队**  
*构建可靠、安全、高效的云原生平台* 