/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import { z } from 'zod'

// 通用验证规则
export const commonValidation = {
  email: z.string()
    .min(1, '邮箱不能为空')
    .email('请输入有效的邮箱地址'),
  
  password: z.string()
    .min(8, '密码长度至少8位')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  
  phone: z.string()
    .min(1, '手机号不能为空')
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号'),
  
  required: (field: string) => z.string().min(1, `${field}不能为空`),
  
  url: z.string().url('请输入有效的URL'),
  
  positiveNumber: z.number().positive('必须是正数'),
  
  date: z.string().datetime('请输入有效的日期时间'),
}

// 登录表单验证
export const loginSchema = z.object({
  email: commonValidation.email,
  password: z.string().min(1, '密码不能为空'),
  remember: z.boolean().optional(),
})

// 用户创建表单验证
export const createUserSchema = z.object({
  email: commonValidation.email,
  username: z.string()
    .min(3, '用户名长度至少3位')
    .max(20, '用户名长度不能超过20位')
    .regex(/^[a-zA-Z0-9_-]+$/, '用户名只能包含字母、数字、下划线和横线'),
  firstName: commonValidation.required('姓'),
  lastName: commonValidation.required('名'),
  phone: commonValidation.phone.optional(),
  roles: z.array(z.string()).min(1, '至少选择一个角色'),
  sendInvitation: z.boolean().optional(),
  temporaryPassword: z.string().optional(),
})

// 用户更新表单验证
export const updateUserSchema = z.object({
  username: z.string()
    .min(3, '用户名长度至少3位')
    .max(20, '用户名长度不能超过20位')
    .regex(/^[a-zA-Z0-9_-]+$/, '用户名只能包含字母、数字、下划线和横线')
    .optional(),
  firstName: z.string().min(1, '姓不能为空').optional(),
  lastName: z.string().min(1, '名不能为空').optional(),
  phone: commonValidation.phone.optional(),
  roles: z.array(z.string()).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING', 'DELETED']).optional(),
})

// 密码修改表单验证
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, '当前密码不能为空'),
  newPassword: commonValidation.password,
  confirmPassword: z.string().min(1, '确认密码不能为空'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: '新密码和确认密码不匹配',
  path: ['confirmPassword'],
})

// 系统配置表单验证
export const systemConfigSchema = z.object({
  key: z.string()
    .min(1, '配置键不能为空')
    .regex(/^[A-Z_][A-Z0-9_]*$/, '配置键必须使用大写字母和下划线'),
  value: z.any(),
  description: commonValidation.required('描述'),
  category: z.enum(['GENERAL', 'SECURITY', 'PERFORMANCE', 'FEATURES', 'INTEGRATIONS', 'NOTIFICATIONS', 'STORAGE', 'CACHE']),
  type: z.enum(['STRING', 'NUMBER', 'BOOLEAN', 'JSON', 'ARRAY', 'SECRET']),
  isRequired: z.boolean().optional(),
  environment: z.enum(['DEVELOPMENT', 'STAGING', 'PRODUCTION']),
})

// 服务配置表单验证
export const serviceConfigSchema = z.object({
  name: commonValidation.required('服务名称'),
  displayName: commonValidation.required('显示名称'),
  description: z.string().optional(),
  type: z.enum(['API', 'DATABASE', 'CACHE', 'QUEUE', 'STORAGE', 'EXTERNAL', 'MICROSERVICE']),
  endpoint: commonValidation.url.optional(),
  port: z.number().min(1).max(65535, '端口号必须在1-65535之间').optional(),
  environment: commonValidation.required('环境'),
  tags: z.array(z.string()).optional(),
})

// 内容创建表单验证
export const createContentSchema = z.object({
  title: z.string().max(200, '标题长度不能超过200字符').optional(),
  body: commonValidation.required('内容'),
  type: z.enum(['TEXT', 'IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT', 'POST', 'COMMENT', 'MESSAGE', 'PROFILE']),
  category: z.enum(['GENERAL', 'NEWS', 'ENTERTAINMENT', 'EDUCATION', 'TECHNOLOGY', 'BUSINESS', 'HEALTH', 'SPORTS', 'POLITICS', 'LIFESTYLE']),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
})

// 内容审核表单验证
export const moderationActionSchema = z.object({
  action: z.enum(['APPROVE', 'REJECT', 'FLAG', 'HIDE', 'DELETE', 'QUARANTINE', 'WARN_USER', 'SUSPEND_USER']),
  reason: commonValidation.required('操作原因'),
  notifyUser: z.boolean().optional(),
  details: z.record(z.any()).optional(),
})

// 通知创建表单验证
export const createNotificationSchema = z.object({
  title: commonValidation.required('标题'),
  message: commonValidation.required('消息内容'),
  type: z.enum(['INFO', 'WARNING', 'ERROR', 'SUCCESS']),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']),
  targetAudience: z.enum(['ALL_USERS', 'ADMINS', 'SPECIFIC_USERS', 'USER_ROLES']),
  targets: z.array(z.string()).optional(),
  channels: z.array(z.enum(['IN_APP', 'EMAIL', 'SMS', 'PUSH'])).min(1, '至少选择一个发送渠道'),
  scheduledAt: z.string().datetime().optional(),
  expiresAt: z.string().datetime().optional(),
})

// 财务配置表单验证
export const billingConfigSchema = z.object({
  currency: z.enum(['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'KRW', 'AUD', 'CAD', 'CHF', 'SEK']),
  taxRate: z.number().min(0).max(100, '税率必须在0-100之间'),
  paymentMethods: z.array(z.enum(['CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER', 'PAYPAL', 'STRIPE', 'WECHAT_PAY', 'ALIPAY', 'CRYPTOCURRENCY', 'WALLET'])).min(1, '至少选择一种支付方式'),
  invoiceSettings: z.object({
    prefix: commonValidation.required('发票前缀'),
    dueDays: z.number().min(1, '账单到期天数必须大于0'),
    reminderDays: z.array(z.number()).min(1, '至少设置一个提醒时间'),
  }),
})

// 退款申请表单验证
export const refundRequestSchema = z.object({
  transactionId: commonValidation.required('交易ID'),
  amount: commonValidation.positiveNumber,
  reason: commonValidation.required('退款原因'),
  notes: z.string().optional(),
})

// 表单验证错误处理
export const formatZodError = (error: z.ZodError): Record<string, string> => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    errors[path] = err.message
  })
  
  return errors
}

// 验证Hook
export const useFormValidation = <T>(schema: z.ZodSchema<T>) => {
  const validate = (data: any): { success: boolean; data?: T; errors?: Record<string, string> } => {
    try {
      const validatedData = schema.parse(data)
      return { success: true, data: validatedData }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { success: false, errors: formatZodError(error) }
      }
      return { success: false, errors: { general: '验证失败' } }
    }
  }

  const validateField = (field: string, value: any): string | undefined => {
    try {
      const fieldSchema = schema.shape[field as keyof typeof schema.shape]
      if (fieldSchema) {
        fieldSchema.parse(value)
      }
      return undefined
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message
      }
      return '验证失败'
    }
  }

  return { validate, validateField }
}

// 导出类型
export type LoginFormData = z.infer<typeof loginSchema>
export type CreateUserFormData = z.infer<typeof createUserSchema>
export type UpdateUserFormData = z.infer<typeof updateUserSchema>
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>
export type SystemConfigFormData = z.infer<typeof systemConfigSchema>
export type ServiceConfigFormData = z.infer<typeof serviceConfigSchema>
export type CreateContentFormData = z.infer<typeof createContentSchema>
export type ModerationActionFormData = z.infer<typeof moderationActionSchema>
export type CreateNotificationFormData = z.infer<typeof createNotificationSchema>
export type BillingConfigFormData = z.infer<typeof billingConfigSchema>
export type RefundRequestFormData = z.infer<typeof refundRequestSchema>