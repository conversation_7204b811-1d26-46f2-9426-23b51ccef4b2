/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Select, 
  DatePicker, 
  Space, 
  Table, 
  Tag, 
  Progress,
  Typography,
  Tooltip,
  List,
  Avatar,
  Badge,
  Empty
} from 'antd'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { 
  TrendingUpOutlined, 
  TrendingDownOutlined,
  EyeOutlined,
  LikeOutlined,
  ShareAltOutlined,
  MessageOutlined,
  FlagOutlined,
  UserOutlined,
  FireOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'

import { ContentType, Content, ContentAnalyticsData, TrendingContent } from '@/types/content'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { RangePicker } = DatePicker
const { Option } = Select
const { Text, Title } = Typography

// Mock analytics data with comprehensive content metrics
const mockAnalyticsData: ContentAnalyticsData = {
  overview: {
    totalContent: 15420,
    totalViews: 2846392,
    totalLikes: 184723,
    totalShares: 47203,
    totalComments: 89472,
    averageEngagement: 68.5,
    contentGrowthRate: 12.3,
    viewGrowthRate: 8.7,
  },
  dailyMetrics: [
    { date: '2025-01-17', views: 42000, likes: 3200, shares: 890, comments: 1560 },
    { date: '2025-01-18', views: 38000, likes: 2950, shares: 820, comments: 1420 },
    { date: '2025-01-19', views: 45000, likes: 3450, shares: 950, comments: 1680 },
    { date: '2025-01-20', views: 52000, likes: 3890, shares: 1120, comments: 1850 },
    { date: '2025-01-21', views: 48000, likes: 3650, shares: 1050, comments: 1720 },
    { date: '2025-01-22', views: 55000, likes: 4100, shares: 1250, comments: 1950 },
    { date: '2025-01-23', views: 58000, likes: 4380, shares: 1340, comments: 2080 },
  ],
  contentTypeDistribution: [
    { type: 'POST', count: 8542, percentage: 55.4 },
    { type: 'IMAGE', count: 4231, percentage: 27.4 },
    { type: 'VIDEO', count: 2647, percentage: 17.2 },
  ],
  topCategories: [
    { category: 'LIFESTYLE', content: 3820, engagement: 78.5 },
    { category: 'TECHNOLOGY', content: 2940, engagement: 72.3 },
    { category: 'FOOD', content: 2150, engagement: 65.8 },
    { category: 'TRAVEL', content: 1890, engagement: 61.2 },
    { category: 'ENTERTAINMENT', content: 1620, engagement: 59.4 },
  ],
  trendingContent: [
    {
      id: '1',
      title: '2025年最值得期待的科技产品',
      type: ContentType.POST,
      authorName: '科技达人',
      authorAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=tech',
      metrics: {
        views: 25480,
        likes: 2150,
        shares: 580,
        comments: 340,
        engagementRate: 12.2,
        viralityScore: 8.5,
      },
      growthRate: 145.6,
      createdAt: '2025-01-23T08:00:00Z',
    },
    {
      id: '2',
      title: '分享一些实用的生活小技巧',
      type: ContentType.POST,
      authorName: '生活达人',
      authorAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=life',
      metrics: {
        views: 18750,
        likes: 1680,
        shares: 420,
        comments: 280,
        engagementRate: 10.8,
        viralityScore: 6.8,
      },
      growthRate: 98.3,
      createdAt: '2025-01-22T15:30:00Z',
    },
  ],
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1']

/**
 * Content Analytics Dashboard Component
 */
const ContentAnalytics: React.FC = () => {
  const { hasPermission } = usePermission()
  const [dateRange, setDateRange] = useState<string>('7days')
  const [contentType, setContentType] = useState<string>('all')
  const [analyticsData, setAnalyticsData] = useState<ContentAnalyticsData>(mockAnalyticsData)

  // Permission check
  const canViewAnalytics = hasPermission(Permission.CONTENT_ANALYTICS)

  useEffect(() => {
    // Mock API call to fetch analytics data
    const fetchAnalytics = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        setAnalyticsData(mockAnalyticsData)
      } catch (error) {
        console.error('Failed to fetch analytics:', error)
      }
    }

    fetchAnalytics()
  }, [dateRange, contentType])

  // Get trend indicator based on value
  const getTrendIndicator = (value: number) => {
    if (value > 0) {
      return <TrendingUpOutlined style={{ color: '#52c41a' }} />
    } else if (value < 0) {
      return <TrendingDownOutlined style={{ color: '#ff4d4f' }} />
    }
    return null
  }

  // Get content type color
  const getContentTypeColor = (type: ContentType) => {
    switch (type) {
      case ContentType.POST: return 'blue'
      case ContentType.IMAGE: return 'green'
      case ContentType.VIDEO: return 'orange'
      default: return 'default'
    }
  }

  if (!canViewAnalytics) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Empty
          description="您没有权限查看内容分析数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* Page title and filters */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <Title level={2} style={{ margin: 0 }}>内容分析</Title>
          <Space>
            <Select
              value={contentType}
              onChange={setContentType}
              style={{ width: 120 }}
            >
              <Option value="all">全部类型</Option>
              <Option value="POST">文章</Option>
              <Option value="IMAGE">图片</Option>
              <Option value="VIDEO">视频</Option>
            </Select>
            <Select
              value={dateRange}
              onChange={setDateRange}
              style={{ width: 120 }}
            >
              <Option value="1days">今日</Option>
              <Option value="7days">近7天</Option>
              <Option value="30days">近30天</Option>
              <Option value="90days">近90天</Option>
            </Select>
          </Space>
        </div>
      </div>

      {/* Key metrics overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总内容数"
              value={analyticsData.overview.totalContent}
              prefix={<EyeOutlined />}
              suffix={getTrendIndicator(analyticsData.overview.contentGrowthRate)}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              较上期增长 {analyticsData.overview.contentGrowthRate}%
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={analyticsData.overview.totalViews}
              prefix={<EyeOutlined />}
              suffix={getTrendIndicator(analyticsData.overview.viewGrowthRate)}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              较上期增长 {analyticsData.overview.viewGrowthRate}%
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总点赞数"
              value={analyticsData.overview.totalLikes}
              prefix={<LikeOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均参与度"
              value={analyticsData.overview.averageEngagement}
              suffix="%"
              prefix={<FireOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {/* Trend charts */}
        <Col xs={24} lg={16}>
          <Card title="内容互动趋势" style={{ height: '400px' }}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.dailyMetrics}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="views" 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  name="浏览量"
                />
                <Line 
                  type="monotone" 
                  dataKey="likes" 
                  stroke="#82ca9d" 
                  strokeWidth={2}
                  name="点赞数"
                />
                <Line 
                  type="monotone" 
                  dataKey="comments" 
                  stroke="#ffc658" 
                  strokeWidth={2}
                  name="评论数"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Content type distribution */}
        <Col xs={24} lg={8}>
          <Card title="内容类型分布" style={{ height: '400px' }}>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={analyticsData.contentTypeDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ type, percentage }) => `${type} ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {analyticsData.contentTypeDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Top categories */}
        <Col xs={24} lg={12}>
          <Card title="热门内容分类">
            <div style={{ height: '300px', overflowY: 'auto' }}>
              {analyticsData.topCategories.map((category, index) => (
                <div key={category.category} style={{ 
                  padding: '12px 0', 
                  borderBottom: index < analyticsData.topCategories.length - 1 ? '1px solid #f0f0f0' : 'none' 
                }}>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '8px'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Badge count={index + 1} style={{ backgroundColor: '#52c41a', marginRight: '12px' }} />
                      <Text strong>{category.category}</Text>
                    </div>
                    <Text type="secondary">{category.content} 篇</Text>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Text style={{ marginRight: '12px', minWidth: '60px' }}>参与度</Text>
                    <Progress 
                      percent={category.engagement} 
                      size="small" 
                      style={{ flex: 1 }}
                      format={(percent) => `${percent}%`}
                    />
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        {/* Trending content */}
        <Col xs={24} lg={12}>
          <Card title="热门内容" extra={<FireOutlined style={{ color: '#ff4d4f' }} />}>
            <List
              itemLayout="horizontal"
              dataSource={analyticsData.trendingContent}
              style={{ height: '300px', overflowY: 'auto' }}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div style={{ position: 'relative' }}>
                        <Avatar src={item.authorAvatar} />
                        <Badge 
                          count={index + 1} 
                          style={{ 
                            position: 'absolute', 
                            top: -5, 
                            right: -5,
                            backgroundColor: '#ff4d4f'
                          }} 
                        />
                      </div>
                    }
                    title={
                      <div>
                        <Text strong ellipsis style={{ maxWidth: '200px' }}>
                          {item.title}
                        </Text>
                        <Tag 
                          color={getContentTypeColor(item.type)}
                          style={{ marginLeft: '8px' }}
                        >
                          {item.type}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: '4px' }}>
                          <Space>
                            <Tooltip title="浏览量">
                              <span><EyeOutlined /> {item.metrics.views.toLocaleString()}</span>
                            </Tooltip>
                            <Tooltip title="点赞数">
                              <span><LikeOutlined /> {item.metrics.likes.toLocaleString()}</span>
                            </Tooltip>
                            <Tooltip title="分享数">
                              <span><ShareAltOutlined /> {item.metrics.shares}</span>
                            </Tooltip>
                          </Space>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <span style={{ fontSize: '12px', color: '#666' }}>
                            增长率: 
                          </span>
                          <span style={{ 
                            color: item.growthRate > 0 ? '#52c41a' : '#ff4d4f',
                            fontWeight: 'bold',
                            marginLeft: '4px'
                          }}>
                            +{item.growthRate}%
                          </span>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ContentAnalytics 