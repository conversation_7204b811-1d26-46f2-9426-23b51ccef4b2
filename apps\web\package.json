{"name": "cina-club-web", "version": "1.0.0", "description": "CINA.CLUB Web Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "axios": "^1.6.0", "socket.io-client": "^4.7.0", "zustand": "^4.4.0", "react-query": "^3.39.0", "lucide-react": "^0.292.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "jest": "^29.0.0", "@testing-library/react": "^13.0.0", "@testing-library/jest-dom": "^6.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}