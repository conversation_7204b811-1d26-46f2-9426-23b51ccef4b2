#!/bin/bash

# CINA.CLUB Platform - Kong Gateway Validation Script
# Copyright (c) 2025 Cina.Club
# All rights reserved.

set -euo pipefail

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[Kong Test]${NC} $1"; }
log_success() { echo -e "${GREEN}[Kong Success]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[Kong Warning]${NC} $1"; }
log_error() { echo -e "${RED}[Kong Error]${NC} $1"; }

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
KONG_DIR="$(dirname "$SCRIPT_DIR")/kong"
NAMESPACE="kong-system"

# 验证Kong配置文件
validate_kong_configs() {
    log_info "验证Kong Gateway配置文件..."
    
    local config_files=(
        "$KONG_DIR/namespace.yaml"
        "$KONG_DIR/control-plane/deployment.yaml"
        "$KONG_DIR/data-plane/deployment.yaml"
        "$KONG_DIR/data-plane/service.yaml"
        "$KONG_DIR/base/kong-plugins/jwt-validator.yaml"
        "$KONG_DIR/base/kong-plugins/rate-limiting.yaml"
    )
    
    local issues=0
    for file in "${config_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "配置文件不存在: $file"
            ((issues++))
            continue
        fi
        
        # 验证YAML语法
        if ! kubectl apply --dry-run=client -f "$file" &>/dev/null; then
            log_error "配置文件语法错误: $file"
            ((issues++))
        else
            log_success "配置文件验证通过: $(basename "$file")"
        fi
    done
    
    if [ $issues -eq 0 ]; then
        log_success "所有Kong配置文件验证通过"
        return 0
    else
        log_error "发现 $issues 个配置问题"
        return 1
    fi
}

# 检查Kong配置的安全性
check_kong_security() {
    log_info "检查Kong Gateway安全配置..."
    
    local security_checks=0
    local security_issues=0
    
    # 检查是否使用非root用户
    if grep -q "runAsUser:" "$KONG_DIR"/*/deployment.yaml; then
        log_success "Kong使用非root用户运行"
        ((security_checks++))
    else
        log_warning "Kong可能使用root用户运行"
        ((security_issues++))
    fi
    
    # 检查资源限制
    if grep -q "resources:" "$KONG_DIR"/*/deployment.yaml; then
        log_success "Kong配置了资源限制"
        ((security_checks++))
    else
        log_warning "Kong未配置资源限制"
        ((security_issues++))
    fi
    
    # 检查安全上下文
    if grep -q "securityContext:" "$KONG_DIR"/*/deployment.yaml; then
        log_success "Kong配置了安全上下文"
        ((security_checks++))
    else
        log_warning "Kong未配置安全上下文"
        ((security_issues++))
    fi
    
    # 检查RBAC
    if grep -q "serviceAccountName:" "$KONG_DIR"/*/deployment.yaml; then
        log_success "Kong使用了专用服务账户"
        ((security_checks++))
    else
        log_warning "Kong未使用专用服务账户"
        ((security_issues++))
    fi
    
    log_info "安全检查完成: $security_checks/4 项通过"
    
    if [ $security_issues -eq 0 ]; then
        log_success "Kong安全配置验证通过"
        return 0
    else
        log_warning "发现 $security_issues 个安全配置问题"
        return 1
    fi
}

# 验证Kong插件配置
validate_kong_plugins() {
    log_info "验证Kong插件配置..."
    
    local plugin_dir="$KONG_DIR/base/kong-plugins"
    if [ ! -d "$plugin_dir" ]; then
        log_error "Kong插件目录不存在: $plugin_dir"
        return 1
    fi
    
    local plugin_checks=0
    local plugin_issues=0
    
    # 检查JWT验证插件
    if [ -f "$plugin_dir/jwt-validator.yaml" ]; then
        if kubectl apply --dry-run=client -f "$plugin_dir/jwt-validator.yaml" &>/dev/null; then
            log_success "JWT验证插件配置正确"
            ((plugin_checks++))
        else
            log_error "JWT验证插件配置错误"
            ((plugin_issues++))
        fi
    else
        log_warning "缺少JWT验证插件配置"
        ((plugin_issues++))
    fi
    
    # 检查限流插件
    if [ -f "$plugin_dir/rate-limiting.yaml" ]; then
        if kubectl apply --dry-run=client -f "$plugin_dir/rate-limiting.yaml" &>/dev/null; then
            log_success "限流插件配置正确"
            ((plugin_checks++))
        else
            log_error "限流插件配置错误"
            ((plugin_issues++))
        fi
    else
        log_warning "缺少限流插件配置"
        ((plugin_issues++))
    fi
    
    log_info "插件检查完成: $plugin_checks/2 项通过"
    
    if [ $plugin_issues -eq 0 ]; then
        log_success "Kong插件配置验证通过"
        return 0
    else
        log_error "发现 $plugin_issues 个插件配置问题"
        return 1
    fi
}

# 检查Kong Gateway可用性(如果部署到集群)
check_kong_availability() {
    if [ "${VALIDATION_MODE:-dry-run}" != "live" ]; then
        log_info "跳过Kong可用性检查(dry-run模式)"
        return 0
    fi
    
    log_info "检查Kong Gateway可用性..."
    
    # 检查Kong命名空间是否存在
    if ! kubectl get namespace "$NAMESPACE" &>/dev/null; then
        log_warning "Kong命名空间不存在，跳过可用性检查"
        return 0
    fi
    
    # 检查Kong控制面板
    if kubectl get deployment kong-controller -n "$NAMESPACE" &>/dev/null; then
        local controller_ready=$(kubectl get deployment kong-controller -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        local controller_desired=$(kubectl get deployment kong-controller -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "1")
        
        if [ "$controller_ready" = "$controller_desired" ] && [ "$controller_ready" != "0" ]; then
            log_success "Kong控制面板运行正常 ($controller_ready/$controller_desired)"
        else
            log_warning "Kong控制面板未就绪 ($controller_ready/$controller_desired)"
        fi
    else
        log_warning "Kong控制面板未部署"
    fi
    
    # 检查Kong数据面板
    if kubectl get deployment kong-gateway -n "$NAMESPACE" &>/dev/null; then
        local gateway_ready=$(kubectl get deployment kong-gateway -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        local gateway_desired=$(kubectl get deployment kong-gateway -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "2")
        
        if [ "$gateway_ready" = "$gateway_desired" ] && [ "$gateway_ready" != "0" ]; then
            log_success "Kong数据面板运行正常 ($gateway_ready/$gateway_desired)"
        else
            log_warning "Kong数据面板未就绪 ($gateway_ready/$gateway_desired)"
        fi
    else
        log_warning "Kong数据面板未部署"
    fi
    
    # 检查Kong服务
    if kubectl get service kong-gateway -n "$NAMESPACE" &>/dev/null; then
        local service_type=$(kubectl get service kong-gateway -n "$NAMESPACE" -o jsonpath='{.spec.type}')
        log_success "Kong服务已创建 (类型: $service_type)"
        
        # 如果是LoadBalancer，检查外部IP
        if [ "$service_type" = "LoadBalancer" ]; then
            local external_ip=$(kubectl get service kong-gateway -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
            if [ "$external_ip" != "pending" ] && [ -n "$external_ip" ]; then
                log_success "Kong外部访问地址: $external_ip"
            else
                log_info "Kong外部IP分配中..."
            fi
        fi
    else
        log_warning "Kong服务未创建"
    fi
}

# 主验证函数
main() {
    log_info "开始Kong Gateway验证..."
    
    local total_checks=0
    local passed_checks=0
    
    # 执行各项验证
    if validate_kong_configs; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    if check_kong_security; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    if validate_kong_plugins; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    if check_kong_availability; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    # 生成验证结果
    log_info "Kong Gateway验证完成: $passed_checks/$total_checks 项通过"
    
    if [ $passed_checks -eq $total_checks ]; then
        log_success "Kong Gateway验证全部通过！"
        return 0
    elif [ $passed_checks -gt $((total_checks / 2)) ]; then
        log_warning "Kong Gateway验证部分通过，有部分问题需要关注"
        return 1
    else
        log_error "Kong Gateway验证失败，存在严重问题"
        return 2
    fi
}

# 帮助信息
show_help() {
    cat << EOF
Kong Gateway 验证脚本

用法:
    $0 [选项]

选项:
    -h, --help    显示此帮助信息
    -v, --verbose 详细输出模式

环境变量:
    VALIDATION_MODE    验证模式 (dry-run|live)
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@" 