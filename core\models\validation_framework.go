// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 16:30:00
// Modified: 2025-01-23 16:30:00

package models

import (
	"encoding/json"
	"fmt"
	"net/mail"
	"net/url"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// ValidationFramework provides comprehensive validation capabilities
type ValidationFramework struct {
	validators       map[string]ValidatorFunc
	schemas          map[string]*ValidationSchema
	rulesets         map[string]*ValidationRuleset
	customValidators map[string]CustomValidatorFunc
	contextProviders map[string]ContextProviderFunc
}

// ValidatorFunc defines a basic validation function
type ValidatorFunc func(value interface{}, params map[string]interface{}) *ValidationError

// CustomValidatorFunc defines a custom validation function with context
type CustomValidatorFunc func(value interface{}, context ValidationContext, params map[string]interface{}) *ValidationError

// ContextProviderFunc provides validation context
type ContextProviderFunc func(data interface{}) ValidationContext

// ValidationSchema defines a complete validation schema for a data structure
type ValidationSchema struct {
	Name        string                  `json:"name"`
	Version     string                  `json:"version"`
	Description string                  `json:"description"`
	Fields      map[string]*FieldSchema `json:"fields"`
	Rules       []CrossFieldRule        `json:"rules"`
	Groups      map[string][]string     `json:"groups"`
	Metadata    map[string]interface{}  `json:"metadata"`
}

// FieldSchema defines validation rules for a single field
type FieldSchema struct {
	Type             string                 `json:"type"`
	Required         bool                   `json:"required"`
	Nullable         bool                   `json:"nullable"`
	Default          interface{}            `json:"default,omitempty"`
	Validators       []ValidatorConfig      `json:"validators"`
	Constraints      map[string]interface{} `json:"constraints"`
	Dependencies     []string               `json:"dependencies"`
	ConditionalRules []ConditionalRule      `json:"conditional_rules"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// ValidatorConfig configures a validator
type ValidatorConfig struct {
	Name       string                 `json:"name"`
	Parameters map[string]interface{} `json:"parameters"`
	Message    string                 `json:"message,omitempty"`
	Severity   ValidationSeverity     `json:"severity"`
	Groups     []string               `json:"groups,omitempty"`
	Conditions []string               `json:"conditions,omitempty"`
}

// ValidationRuleset defines a set of validation rules
type ValidationRuleset struct {
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Rules       []ValidationRule `json:"rules"`
	Priority    int              `json:"priority"`
	Enabled     bool             `json:"enabled"`
	Context     []string         `json:"context"`
}

// ValidationRule defines a single validation rule
type ValidationRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Field       string                 `json:"field"`
	Validator   string                 `json:"validator"`
	Parameters  map[string]interface{} `json:"parameters"`
	Message     string                 `json:"message"`
	Severity    ValidationSeverity     `json:"severity"`
	Enabled     bool                   `json:"enabled"`
	Conditions  []string               `json:"conditions"`
	Groups      []string               `json:"groups"`
}

// CrossFieldRule defines validation rules that span multiple fields
type CrossFieldRule struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Fields      []string               `json:"fields"`
	Validator   string                 `json:"validator"`
	Parameters  map[string]interface{} `json:"parameters"`
	Message     string                 `json:"message"`
	Severity    ValidationSeverity     `json:"severity"`
}

// ConditionalRule defines conditional validation logic
type ConditionalRule struct {
	Condition string            `json:"condition"`
	Rules     []ValidatorConfig `json:"rules"`
}

// ValidationContext provides context for validation
type ValidationContext struct {
	UserID      string                 `json:"user_id,omitempty"`
	TenantID    string                 `json:"tenant_id,omitempty"`
	Operation   string                 `json:"operation"` // "create", "update", "delete"
	Timestamp   time.Time              `json:"timestamp"`
	Source      string                 `json:"source"`
	RequestID   string                 `json:"request_id,omitempty"`
	SessionID   string                 `json:"session_id,omitempty"`
	ClientInfo  map[string]interface{} `json:"client_info,omitempty"`
	Environment string                 `json:"environment"`
	Locale      string                 `json:"locale,omitempty"`
	Timezone    string                 `json:"timezone,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ValidationResult represents the result of validation
type ValidationResult struct {
	Valid       bool                   `json:"valid"`
	Errors      []ValidationError      `json:"errors"`
	Warnings    []ValidationError      `json:"warnings"`
	Info        []ValidationError      `json:"info"`
	Summary     ValidationSummary      `json:"summary"`
	Context     ValidationContext      `json:"context"`
	Metadata    map[string]interface{} `json:"metadata"`
	ProcessedAt time.Time              `json:"processed_at"`
	Duration    time.Duration          `json:"duration"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field      string                 `json:"field"`
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	Severity   ValidationSeverity     `json:"severity"`
	Value      interface{}            `json:"value,omitempty"`
	Expected   interface{}            `json:"expected,omitempty"`
	Context    map[string]interface{} `json:"context,omitempty"`
	Path       string                 `json:"path,omitempty"`
	Rule       string                 `json:"rule,omitempty"`
	Group      string                 `json:"group,omitempty"`
	Suggestion string                 `json:"suggestion,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// ValidationSummary provides a summary of validation results
type ValidationSummary struct {
	TotalFields     int `json:"total_fields"`
	ValidatedFields int `json:"validated_fields"`
	ErrorCount      int `json:"error_count"`
	WarningCount    int `json:"warning_count"`
	InfoCount       int `json:"info_count"`
	SkippedFields   int `json:"skipped_fields"`
}

// ValidationSeverity defines the severity of validation issues
type ValidationSeverity string

const (
	SeverityError   ValidationSeverity = "error"
	SeverityWarning ValidationSeverity = "warning"
	SeverityInfo    ValidationSeverity = "info"
)

// NewValidationFramework creates a new validation framework
func NewValidationFramework() *ValidationFramework {
	vf := &ValidationFramework{
		validators:       make(map[string]ValidatorFunc),
		schemas:          make(map[string]*ValidationSchema),
		rulesets:         make(map[string]*ValidationRuleset),
		customValidators: make(map[string]CustomValidatorFunc),
		contextProviders: make(map[string]ContextProviderFunc),
	}

	// Register built-in validators
	vf.registerBuiltInValidators()
	vf.registerBuiltInContextProviders()

	return vf
}

// RegisterValidator registers a validator function
func (vf *ValidationFramework) RegisterValidator(name string, validator ValidatorFunc) {
	vf.validators[name] = validator
}

// RegisterCustomValidator registers a custom validator function
func (vf *ValidationFramework) RegisterCustomValidator(name string, validator CustomValidatorFunc) {
	vf.customValidators[name] = validator
}

// RegisterSchema registers a validation schema
func (vf *ValidationFramework) RegisterSchema(schema *ValidationSchema) {
	vf.schemas[schema.Name] = schema
}

// RegisterRuleset registers a validation ruleset
func (vf *ValidationFramework) RegisterRuleset(ruleset *ValidationRuleset) {
	vf.rulesets[ruleset.Name] = ruleset
}

// RegisterContextProvider registers a context provider
func (vf *ValidationFramework) RegisterContextProvider(name string, provider ContextProviderFunc) {
	vf.contextProviders[name] = provider
}

// ValidateWithSchema validates data against a registered schema
func (vf *ValidationFramework) ValidateWithSchema(schemaName string, data interface{}, context ValidationContext) *ValidationResult {
	startTime := time.Now()

	schema, exists := vf.schemas[schemaName]
	if !exists {
		return &ValidationResult{
			Valid: false,
			Errors: []ValidationError{
				{
					Code:     "SCHEMA_NOT_FOUND",
					Message:  fmt.Sprintf("Schema %s not found", schemaName),
					Severity: SeverityError,
				},
			},
			ProcessedAt: time.Now(),
			Duration:    time.Since(startTime),
		}
	}

	return vf.validateAgainstSchema(data, schema, context, startTime)
}

// ValidateWithRules validates data against a set of rules
func (vf *ValidationFramework) ValidateWithRules(data interface{}, rules []ValidationRule, context ValidationContext) *ValidationResult {
	startTime := time.Now()

	result := &ValidationResult{
		Valid:       true,
		Context:     context,
		Metadata:    make(map[string]interface{}),
		ProcessedAt: time.Now(),
	}

	dataMap := vf.toMap(data)

	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		// Check conditions
		if !vf.evaluateConditions(rule.Conditions, dataMap, context) {
			continue
		}

		// Get field value
		value, _ := vf.getFieldValue(dataMap, rule.Field)

		// Apply validator
		var validationError *ValidationError

		if customValidator, exists := vf.customValidators[rule.Validator]; exists {
			validationError = customValidator(value, context, rule.Parameters)
		} else if validator, exists := vf.validators[rule.Validator]; exists {
			validationError = validator(value, rule.Parameters)
		} else {
			validationError = &ValidationError{
				Field:    rule.Field,
				Code:     "VALIDATOR_NOT_FOUND",
				Message:  fmt.Sprintf("Validator %s not found", rule.Validator),
				Severity: SeverityError,
				Rule:     rule.Name,
			}
		}

		if validationError != nil {
			validationError.Field = rule.Field
			validationError.Rule = rule.Name

			if validationError.Message == "" {
				validationError.Message = rule.Message
			}

			if validationError.Severity == "" {
				validationError.Severity = rule.Severity
			}

			vf.addValidationError(result, *validationError)
		}

		result.Summary.ValidatedFields++
	}

	result.Summary.TotalFields = len(rules)
	result.Duration = time.Since(startTime)

	return result
}

// validateAgainstSchema validates data against a schema
func (vf *ValidationFramework) validateAgainstSchema(data interface{}, schema *ValidationSchema, context ValidationContext, startTime time.Time) *ValidationResult {
	result := &ValidationResult{
		Valid:       true,
		Context:     context,
		Metadata:    make(map[string]interface{}),
		ProcessedAt: time.Now(),
	}

	dataMap := vf.toMap(data)

	// Validate individual fields
	for fieldName, fieldSchema := range schema.Fields {
		result.Summary.TotalFields++

		value, exists := vf.getFieldValue(dataMap, fieldName)

		// Check required fields
		if fieldSchema.Required && !exists {
			vf.addValidationError(result, ValidationError{
				Field:    fieldName,
				Code:     "REQUIRED_FIELD_MISSING",
				Message:  fmt.Sprintf("Field %s is required", fieldName),
				Severity: SeverityError,
			})
			continue
		}

		// Skip validation for missing optional fields
		if !exists {
			result.Summary.SkippedFields++
			continue
		}

		// Check nullable fields
		if value == nil && !fieldSchema.Nullable {
			vf.addValidationError(result, ValidationError{
				Field:    fieldName,
				Code:     "NULL_NOT_ALLOWED",
				Message:  fmt.Sprintf("Field %s cannot be null", fieldName),
				Severity: SeverityError,
			})
			continue
		}

		// Validate field type
		if !vf.validateFieldType(value, fieldSchema.Type) {
			vf.addValidationError(result, ValidationError{
				Field:    fieldName,
				Code:     "INVALID_TYPE",
				Message:  fmt.Sprintf("Field %s must be of type %s", fieldName, fieldSchema.Type),
				Severity: SeverityError,
				Value:    value,
				Expected: fieldSchema.Type,
			})
			continue
		}

		// Apply field validators
		for _, validatorConfig := range fieldSchema.Validators {
			// Check conditions
			if !vf.evaluateConditions(validatorConfig.Conditions, dataMap, context) {
				continue
			}

			var validationError *ValidationError

			if customValidator, exists := vf.customValidators[validatorConfig.Name]; exists {
				validationError = customValidator(value, context, validatorConfig.Parameters)
			} else if validator, exists := vf.validators[validatorConfig.Name]; exists {
				validationError = validator(value, validatorConfig.Parameters)
			} else {
				validationError = &ValidationError{
					Field:    fieldName,
					Code:     "VALIDATOR_NOT_FOUND",
					Message:  fmt.Sprintf("Validator %s not found", validatorConfig.Name),
					Severity: SeverityError,
				}
			}

			if validationError != nil {
				validationError.Field = fieldName

				if validationError.Message == "" && validatorConfig.Message != "" {
					validationError.Message = validatorConfig.Message
				}

				if validationError.Severity == "" {
					validationError.Severity = validatorConfig.Severity
				}

				vf.addValidationError(result, *validationError)
			}
		}

		// Apply conditional rules
		for _, conditionalRule := range fieldSchema.ConditionalRules {
			if vf.evaluateCondition(conditionalRule.Condition, dataMap, context) {
				for _, rule := range conditionalRule.Rules {
					// Apply conditional validator
					var validationError *ValidationError

					if customValidator, exists := vf.customValidators[rule.Name]; exists {
						validationError = customValidator(value, context, rule.Parameters)
					} else if validator, exists := vf.validators[rule.Name]; exists {
						validationError = validator(value, rule.Parameters)
					}

					if validationError != nil {
						validationError.Field = fieldName
						vf.addValidationError(result, *validationError)
					}
				}
			}
		}

		result.Summary.ValidatedFields++
	}

	// Apply cross-field rules
	for _, crossFieldRule := range schema.Rules {
		validationError := vf.applyCrossFieldRule(dataMap, crossFieldRule, context)
		if validationError != nil {
			vf.addValidationError(result, *validationError)
		}
	}

	result.Duration = time.Since(startTime)
	result.Metadata["schema"] = schema.Name
	result.Metadata["version"] = schema.Version

	return result
}

// Helper methods
func (vf *ValidationFramework) addValidationError(result *ValidationResult, error ValidationError) {
	switch error.Severity {
	case SeverityError:
		result.Errors = append(result.Errors, error)
		result.Summary.ErrorCount++
		result.Valid = false
	case SeverityWarning:
		result.Warnings = append(result.Warnings, error)
		result.Summary.WarningCount++
	case SeverityInfo:
		result.Info = append(result.Info, error)
		result.Summary.InfoCount++
	}
}

func (vf *ValidationFramework) toMap(data interface{}) map[string]interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		return v
	case []byte:
		var result map[string]interface{}
		json.Unmarshal(v, &result)
		return result
	case string:
		var result map[string]interface{}
		json.Unmarshal([]byte(v), &result)
		return result
	default:
		return vf.structToMap(data)
	}
}

func (vf *ValidationFramework) structToMap(data interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	value := reflect.ValueOf(data)
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	if value.Kind() != reflect.Struct {
		return result
	}

	typ := value.Type()
	for i := 0; i < value.NumField(); i++ {
		field := typ.Field(i)
		fieldValue := value.Field(i)

		if !fieldValue.CanInterface() {
			continue
		}

		jsonTag := field.Tag.Get("json")
		if jsonTag == "-" {
			continue
		}

		fieldName := field.Name
		if jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			if parts[0] != "" {
				fieldName = parts[0]
			}
		}

		result[fieldName] = fieldValue.Interface()
	}

	return result
}

func (vf *ValidationFramework) getFieldValue(data map[string]interface{}, field string) (interface{}, bool) {
	parts := strings.Split(field, ".")
	current := data

	for i, part := range parts {
		value, exists := current[part]
		if !exists {
			return nil, false
		}

		if i == len(parts)-1 {
			return value, true
		}

		if nestedMap, ok := value.(map[string]interface{}); ok {
			current = nestedMap
		} else {
			return nil, false
		}
	}

	return nil, false
}

func (vf *ValidationFramework) validateFieldType(value interface{}, expectedType string) bool {
	if value == nil {
		return true // Null values are handled separately
	}

	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "number", "int", "integer":
		switch value.(type) {
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
			return true
		}
		return false
	case "boolean", "bool":
		_, ok := value.(bool)
		return ok
	case "array", "slice":
		v := reflect.ValueOf(value)
		return v.Kind() == reflect.Slice || v.Kind() == reflect.Array
	case "object", "map":
		_, ok := value.(map[string]interface{})
		return ok
	case "date", "datetime", "timestamp":
		switch value.(type) {
		case time.Time, string:
			return true
		}
		return false
	default:
		return true // Unknown types pass validation
	}
}

func (vf *ValidationFramework) evaluateConditions(conditions []string, data map[string]interface{}, context ValidationContext) bool {
	if len(conditions) == 0 {
		return true
	}

	for _, condition := range conditions {
		if !vf.evaluateCondition(condition, data, context) {
			return false
		}
	}

	return true
}

func (vf *ValidationFramework) evaluateCondition(condition string, data map[string]interface{}, context ValidationContext) bool {
	// Simple condition evaluation - in practice, you'd use a more sophisticated expression evaluator
	if condition == "true" {
		return true
	}
	if condition == "false" {
		return false
	}

	// Check for field existence
	if strings.HasPrefix(condition, "exists:") {
		field := strings.TrimPrefix(condition, "exists:")
		_, exists := vf.getFieldValue(data, field)
		return exists
	}

	// Check for field equality
	if strings.Contains(condition, "==") {
		parts := strings.Split(condition, "==")
		if len(parts) == 2 {
			field := strings.TrimSpace(parts[0])
			expectedValue := strings.TrimSpace(parts[1])

			value, exists := vf.getFieldValue(data, field)
			if !exists {
				return false
			}

			return fmt.Sprintf("%v", value) == expectedValue
		}
	}

	// Check context conditions
	if strings.HasPrefix(condition, "context.") {
		contextField := strings.TrimPrefix(condition, "context.")
		switch contextField {
		case "operation==create":
			return context.Operation == "create"
		case "operation==update":
			return context.Operation == "update"
		case "operation==delete":
			return context.Operation == "delete"
		}
	}

	return false
}

func (vf *ValidationFramework) applyCrossFieldRule(data map[string]interface{}, rule CrossFieldRule, context ValidationContext) *ValidationError {
	// Get values for all fields in the rule
	values := make(map[string]interface{})
	for _, field := range rule.Fields {
		if value, exists := vf.getFieldValue(data, field); exists {
			values[field] = value
		}
	}

	// Apply cross-field validator
	if customValidator, exists := vf.customValidators[rule.Validator]; exists {
		return customValidator(values, context, rule.Parameters)
	} else if validator, exists := vf.validators[rule.Validator]; exists {
		return validator(values, rule.Parameters)
	}

	return &ValidationError{
		Code:     "VALIDATOR_NOT_FOUND",
		Message:  fmt.Sprintf("Cross-field validator %s not found", rule.Validator),
		Severity: SeverityError,
	}
}

// Register built-in validators
func (vf *ValidationFramework) registerBuiltInValidators() {
	// Required validator
	vf.validators["required"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		if value == nil {
			return &ValidationError{
				Code:     "REQUIRED",
				Message:  "Field is required",
				Severity: SeverityError,
			}
		}

		// Check for zero values
		v := reflect.ValueOf(value)
		if v.IsZero() {
			return &ValidationError{
				Code:     "REQUIRED",
				Message:  "Field is required",
				Severity: SeverityError,
			}
		}

		return nil
	}

	// String length validators
	vf.validators["min_length"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		str, ok := value.(string)
		if !ok {
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a string",
				Severity: SeverityError,
			}
		}

		minLength, ok := params["min"].(int)
		if !ok {
			minLength = 1
		}

		if len(str) < minLength {
			return &ValidationError{
				Code:     "MIN_LENGTH",
				Message:  fmt.Sprintf("Value must be at least %d characters", minLength),
				Severity: SeverityError,
				Value:    len(str),
				Expected: minLength,
			}
		}

		return nil
	}

	vf.validators["max_length"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		str, ok := value.(string)
		if !ok {
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a string",
				Severity: SeverityError,
			}
		}

		maxLength, ok := params["max"].(int)
		if !ok {
			maxLength = 255
		}

		if len(str) > maxLength {
			return &ValidationError{
				Code:     "MAX_LENGTH",
				Message:  fmt.Sprintf("Value must not exceed %d characters", maxLength),
				Severity: SeverityError,
				Value:    len(str),
				Expected: maxLength,
			}
		}

		return nil
	}

	// Email validator
	vf.validators["email"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		str, ok := value.(string)
		if !ok {
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a string",
				Severity: SeverityError,
			}
		}

		if _, err := mail.ParseAddress(str); err != nil {
			return &ValidationError{
				Code:     "INVALID_EMAIL",
				Message:  "Invalid email format",
				Severity: SeverityError,
				Value:    str,
			}
		}

		return nil
	}

	// URL validator
	vf.validators["url"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		str, ok := value.(string)
		if !ok {
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a string",
				Severity: SeverityError,
			}
		}

		if _, err := url.ParseRequestURI(str); err != nil {
			return &ValidationError{
				Code:     "INVALID_URL",
				Message:  "Invalid URL format",
				Severity: SeverityError,
				Value:    str,
			}
		}

		return nil
	}

	// Regex validator
	vf.validators["regex"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		str, ok := value.(string)
		if !ok {
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a string",
				Severity: SeverityError,
			}
		}

		pattern, ok := params["pattern"].(string)
		if !ok {
			return &ValidationError{
				Code:     "INVALID_PARAMETER",
				Message:  "Pattern parameter is required",
				Severity: SeverityError,
			}
		}

		matched, err := regexp.MatchString(pattern, str)
		if err != nil {
			return &ValidationError{
				Code:     "REGEX_ERROR",
				Message:  "Invalid regex pattern",
				Severity: SeverityError,
			}
		}

		if !matched {
			return &ValidationError{
				Code:     "REGEX_MISMATCH",
				Message:  "Value does not match required pattern",
				Severity: SeverityError,
				Value:    str,
				Expected: pattern,
			}
		}

		return nil
	}

	// Number range validators
	vf.validators["min_value"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		var num float64
		var ok bool

		switch v := value.(type) {
		case int:
			num = float64(v)
			ok = true
		case int64:
			num = float64(v)
			ok = true
		case float64:
			num = v
			ok = true
		case string:
			var err error
			num, err = strconv.ParseFloat(v, 64)
			ok = err == nil
		}

		if !ok {
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a number",
				Severity: SeverityError,
			}
		}

		minValue, ok := params["min"].(float64)
		if !ok {
			if minInt, ok := params["min"].(int); ok {
				minValue = float64(minInt)
			} else {
				return &ValidationError{
					Code:     "INVALID_PARAMETER",
					Message:  "Min parameter is required",
					Severity: SeverityError,
				}
			}
		}

		if num < minValue {
			return &ValidationError{
				Code:     "MIN_VALUE",
				Message:  fmt.Sprintf("Value must be at least %g", minValue),
				Severity: SeverityError,
				Value:    num,
				Expected: minValue,
			}
		}

		return nil
	}

	vf.validators["max_value"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		var num float64
		var ok bool

		switch v := value.(type) {
		case int:
			num = float64(v)
			ok = true
		case int64:
			num = float64(v)
			ok = true
		case float64:
			num = v
			ok = true
		case string:
			var err error
			num, err = strconv.ParseFloat(v, 64)
			ok = err == nil
		}

		if !ok {
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a number",
				Severity: SeverityError,
			}
		}

		maxValue, ok := params["max"].(float64)
		if !ok {
			if maxInt, ok := params["max"].(int); ok {
				maxValue = float64(maxInt)
			} else {
				return &ValidationError{
					Code:     "INVALID_PARAMETER",
					Message:  "Max parameter is required",
					Severity: SeverityError,
				}
			}
		}

		if num > maxValue {
			return &ValidationError{
				Code:     "MAX_VALUE",
				Message:  fmt.Sprintf("Value must not exceed %g", maxValue),
				Severity: SeverityError,
				Value:    num,
				Expected: maxValue,
			}
		}

		return nil
	}

	// Enum validator
	vf.validators["enum"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		allowedValues, ok := params["values"].([]interface{})
		if !ok {
			return &ValidationError{
				Code:     "INVALID_PARAMETER",
				Message:  "Values parameter is required",
				Severity: SeverityError,
			}
		}

		valueStr := fmt.Sprintf("%v", value)
		for _, allowed := range allowedValues {
			if fmt.Sprintf("%v", allowed) == valueStr {
				return nil
			}
		}

		return &ValidationError{
			Code:     "INVALID_ENUM",
			Message:  "Value is not in allowed list",
			Severity: SeverityError,
			Value:    value,
			Expected: allowedValues,
		}
	}

	// Date validator
	vf.validators["date"] = func(value interface{}, params map[string]interface{}) *ValidationError {
		switch v := value.(type) {
		case time.Time:
			return nil
		case string:
			format, ok := params["format"].(string)
			if !ok {
				format = time.RFC3339
			}

			if _, err := time.Parse(format, v); err != nil {
				return &ValidationError{
					Code:     "INVALID_DATE",
					Message:  "Invalid date format",
					Severity: SeverityError,
					Value:    v,
					Expected: format,
				}
			}

			return nil
		default:
			return &ValidationError{
				Code:     "INVALID_TYPE",
				Message:  "Value must be a date or string",
				Severity: SeverityError,
			}
		}
	}
}

func (vf *ValidationFramework) registerBuiltInContextProviders() {
	vf.contextProviders["default"] = func(data interface{}) ValidationContext {
		return ValidationContext{
			Operation:   "validate",
			Timestamp:   time.Now(),
			Environment: "production",
			Metadata:    make(map[string]interface{}),
		}
	}
}

// Global validation framework instance
var DefaultValidationFramework = NewValidationFramework()
