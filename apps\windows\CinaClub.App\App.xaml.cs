/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.IO;
using CinaClub.Core.Interfaces;
using CinaClub.Infrastructure.Services;
using CinaClub.Infrastructure.GoBridge;
using CinaClub.Infrastructure.Network;
using CinaClub.Infrastructure.Repositories;
using CinaClub.Core.UseCases.Auth;
using CinaClub.Core.UseCases.Chat;
using CinaClub.Core.UseCases.PKB;
using CinaClub.App.ViewModels;
using CinaClub.App.Services;

namespace CinaClub.App;

/// <summary>
/// CINA.CLUB Windows应用程序主类
/// 负责应用程序的初始化、依赖注入配置和启动逻辑
/// </summary>
public partial class App : Application
{
    /// <summary>
    /// 全局服务提供者
    /// </summary>
    public static IServiceProvider Services { get; private set; } = null!;

    /// <summary>
    /// 主机配置
    /// </summary>
    private IHost _host = null!;

    /// <summary>
    /// 构造函数
    /// </summary>
    public App()
    {
        this.InitializeComponent();
        
        // 配置主机和依赖注入
        ConfigureHost();
    }

    /// <summary>
    /// 应用程序启动时的处理
    /// </summary>
    /// <param name="args">启动参数</param>
    protected override void OnLaunched(Microsoft.UI.Xaml.LaunchActivatedEventArgs args)
    {
        // 启动主机
        _host.Start();
        
        // 初始化主窗口
        var window = Services.GetRequiredService<MainWindow>();
        window.Activate();
    }

    /// <summary>
    /// 配置主机和依赖注入容器
    /// </summary>
    private void ConfigureHost()
    {
        var builder = Host.CreateDefaultBuilder();

        // 配置应用程序配置
        builder.ConfigureAppConfiguration((context, config) =>
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            var configPath = Path.Combine(appDataPath, "CinaClub", "config.json");
            
            config.AddJsonFile(configPath, optional: true, reloadOnChange: true);
            config.AddEnvironmentVariables("CINACLUB_");
        });

        // 配置服务
        builder.ConfigureServices((context, services) =>
        {
            ConfigureServices(services, context.Configuration);
        });

        _host = builder.Build();
        Services = _host.Services;
    }

    /// <summary>
    /// 配置依赖注入服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置
        services.AddSingleton(configuration);

        // 注册窗口和页面
        services.AddTransient<MainWindow>();
        services.AddTransient<Views.LoginPage>();
        services.AddTransient<Views.DashboardPage>();
        services.AddTransient<Views.ChatPage>();
        services.AddTransient<Views.PKBPage>();
        services.AddTransient<Views.SettingsPage>();
        services.AddTransient<Views.ShellPage>();

        // 注册ViewModels
        services.AddTransient<LoginViewModel>();
        services.AddTransient<DashboardViewModel>();
        services.AddTransient<ChatViewModel>();
        services.AddTransient<PKBViewModel>();
        services.AddTransient<SettingsViewModel>();
        services.AddTransient<ShellViewModel>();

        // 注册应用服务
        services.AddSingleton<INavigationService, NavigationService>();
        services.AddSingleton<IDialogService, DialogService>();
        services.AddSingleton<ISecureStorageService, WindowsSecureStorageService>();

        // 注册Go桥接服务
        services.AddSingleton<ICryptoVault, CryptoVaultService>();
        services.AddSingleton<IAICore, AICoreService>();
        services.AddSingleton<IDataSyncService, DataSyncService>();

        // 注册网络服务
        services.AddHttpClient();
        services.AddSingleton<IGrpcClientFactory, GrpcClientFactory>();
        services.AddSingleton<IWebSocketService, WebSocketService>();

        // 注册仓储
        services.AddSingleton<IUserRepository, UserRepository>();
        services.AddSingleton<IChatRepository, ChatRepository>();
        services.AddSingleton<IPKBRepository, PKBRepository>();

        // 注册用例
        services.AddTransient<LoginUseCase>();
        services.AddTransient<SendMessageUseCase>();
        services.AddTransient<SearchPKBUseCase>();
        services.AddTransient<CreatePKBEntryUseCase>();

        // 注册基础设施服务
        services.AddSingleton<IDatabaseService, SqliteDatabaseService>();
        services.AddSingleton<ICacheService, MemoryCacheService>();

        // 配置gRPC客户端
        var serverUrl = configuration.GetConnectionString("ApiServer") ?? "https://api.cinaclub.com";
        services.AddGrpcClient<UserCore.UserCoreClient>(o =>
        {
            o.Address = new Uri(serverUrl);
        });
        services.AddGrpcClient<Chat.ChatClient>(o =>
        {
            o.Address = new Uri(serverUrl);
        });
        services.AddGrpcClient<PersonalKB.PersonalKBClient>(o =>
        {
            o.Address = new Uri(serverUrl);
        });
    }
} 