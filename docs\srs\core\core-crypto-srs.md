好的，遵照您的指示。我们将对`core/crypto`的SRS进行一次**重大的、生产级的战略升级**，使其能够完全支撑您所描述的、复杂的Web3钱包和其他E2EE功能。

这份**版本 2.0**的SRS将引入**分层密钥体系**、**标准化的密文格式**和**混合加密方案**，使其设计对标行业领先的密码学应用（如1Password, Signal）的核心原则。

---
### CINA.CLUB - 共享核心加密库 (`core/crypto`) 需求规格说明书

**版本: 2.0 (生产级定义，集成三层密钥体系与混合加密)**  
**发布日期: 2025-06-28**  
**文档负责人:** [首席安全官 (CSO) / 安全架构师]  
**审批人:** [CTO]

#### 修订历史
| 版本 | 日期       | 修订人     | 修订描述                                                     |
|------|------------|------------|--------------------------------------------------------------|
| 1.0  | 2025-06-26 | Cina.Club  | 初始版本，定义了基础的加解密原语。                         |
| 2.0  | 2025-06-28 | Cina.Club  | **引入三层密钥体系(MEK/KEK/DEK)、标准化密文格式、混合加密(ECIES)，并细化了密钥派生和跨平台导出接口。** |

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心加密方案与模型 (深度解析)](#3-核心加密方案与模型-深度解析)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [跨平台导出与使用](#6-跨平台导出与使用)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了支撑CINA.CLUB平台先进的**端到端加密(E2EE)**功能，特别是**非托管Web3钱包**和**私密知识库(PKB)**，平台需要一个**绝对安全、经过严格设计、跨平台一致**的密码学核心库。`core/crypto` 包的目的就是提供这套标准化的、高性能的加密原语。它通过实现一个**分层密钥体系**和现代加密算法，将复杂的密码学操作封装成简单、安全的API，确保在所有客户端上，对用户数据的保护达到行业最高标准。

#### 1.2. 范围与边界 (保持不变)
*   **范围之内**: 提供密钥派生、对称/非对称加解密、数字签名、密钥生成等密码学原语。
*   **范围之外**: 密钥的持久化存储、应用层加密(ALE)的业务流程、任何业务逻辑或网络I/O。

#### 1.3. 目标用户 (保持不变)
*   **CINA.CLUB 前端应用** (通过Go Mobile/WASM)，是本包的主要消费者。
*   需要密码学计算的**后端服务**。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`core/crypto` 是`/core`目录下的**安全基石**，处于依赖链的绝对最底层。其实现的正确性、安全性和性能，直接决定了平台所有隐私保护承诺的有效性。

#### 2.2. 设计原则 (增强)
*   **安全第一**: 绝不自行“发明”加密算法。所有实现都必须基于经过公开审计和学术界认可的标准。
*   **分层防御 (Defense in Depth)**: 通过MEK/KEK/DEK三层密钥体系，增加攻击者获取有用数据的难度。
*   **密码学敏捷性 (Cryptographic Agility)**: 通过标准化的、带版本的密文格式，使得未来在不破坏旧数据的情况下，升级加密算法成为可能。
*   **简单接口，隐藏复杂性**: 提供极简的API，将nonce管理、密钥派生、密文格式化等所有细节封装在内。
*   **零依赖**: 严格控制外部依赖。

---

### 3. 核心加密方案与模型 (深度解析)

#### 3.1. 三层密钥体系 (MEK/KEK/DEK)

这是本架构的核心，用于隔离不同层次的风险。

1.  **主加密密钥 (MEK - Master Encryption Key)**
    *   **来源**: **必须**使用 **Argon2id** 算法，从用户的**主密码**和一个**服务端下发的、用户唯一的盐(Salt)**派生而来。这个Salt存储在`user-core-service`中。
    *   **生命周期**: 只在用户解锁钱包的会话期间，存在于**内存**中。绝不持久化。
    *   **用途**: **唯一用途**是加密和解密**密钥加密密钥(KEK)**。

2.  **密钥加密密钥 (KEK - Key Encryption Key)**
    *   **来源**: 使用密码学安全随机数生成器`crypto/rand`独立生成。每个用户可以有一个或多个KEK。
    *   **生命周期**: **加密后**（使用MEK）存储在设备的安全区域（Keychain/Keystore）。
    *   **用途**: **唯一用途**是加密和解密**数据加密密钥(DEK)**。
    *   **理由**: 引入KEK层，使得用户**修改主密码**时，**无需**重新加密所有数据。只需用旧MEK解密KEK，再用新MEK加密KEK即可，这是一个极快的操作。

3.  **数据加密密钥 (DEK - Data Encryption Key)**
    *   **来源**: `crypto/rand`独立生成。可以为每个数据项（如一篇PKB笔记、一个钱包私钥）都生成一个独立的DEK。
    *   **生命周期**: **加密后**（使用KEK）与加密数据本身存储在一起。
    *   **用途**: 用于直接加密和解密**用户数据**（如笔记内容、Web3私钥）。

#### 3.2. 标准化密文格式 (`SealedBox`)

所有对称加密的输出，都必须遵循一个统一的、带元数据的格式。

```
// SealedBox 代表一个标准的加密数据包
[ 1-byte version | 12-byte KEK_id | 32-byte encrypted_DEK | 24-byte nonce | N-byte ciphertext | 16-byte auth_tag ]
```
*   `version`: 密码学版本号（如`0x01`），用于未来的算法升级。
*   `KEK_id`: (可选) 用于标识使用了哪个KEK进行加密，支持KEK的轮换。
*   `encrypted_DEK`: 被KEK加密后的DEK。
*   `nonce`: XChaCha20的24字节随机数。
*   `ciphertext`: 真正的数据密文。
*   `auth_tag`: Poly1305的认证标签。

**流程**: 解密时，先用MEK解密KEK，再用KEK解密`encrypted_DEK`得到DEK，最后用DEK和`nonce`解密`ciphertext`并验证`auth_tag`。

#### 3.3. 混合加密方案 (ECIES)

*   **算法选择**: **必须**使用**ECIES (Elliptic Curve Integrated Encryption Scheme)**，基于`secp256k1`或`Curve25519`。
*   **工作原理**: 发送方用接收方的公钥，加密一个临时的对称密钥，然后用这个对称密钥加密实际数据。这结合了非对称加密的密钥分发便利性和对称加密的高性能。
*   **应用场景**: **安全共享**。例如，用户A想与用户B共享一篇加密笔记，A的客户端会：
    1.  获取B的公钥。
    2.  调用`EncryptHybrid(note_DEK, B_public_key)`，得到一个“加密后的笔记DEK”。
    3.  将这个“加密后的笔记DEK”通过平台发送给B。

#### 3.4. 数字签名与密钥派生 (Web3钱包)
*   **签名**: **必须**使用**ECDSA with secp256k1**，以兼容以太坊和所有EVM链。
*   **密钥派生**: **必须**遵循**BIP-39 (助记词), BIP-32 (分层确定性钱包), BIP-44 (多币种派生路径)**标准。
    *   本包需要提供从助记词派生出根种子，再根据派生路径（如`m/44'/60'/0'/0/0`）派生出具体链的私钥的函数。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 密钥管理
*   **FR4.1.1 (密钥派生)**: `DeriveKeyFromPassword` (使用Argon2id)。
*   **FR4.1.2 (BIP39/32/44)**:
    *   `GenerateMnemonic()`: 生成12或24个单词的助记词。
    *   `NewMasterKeyFromMnemonic(mnemonic)`: 从助记词生成BIP-32主密钥。
    *   `DerivePrivateKey(masterKey, path)`: 根据BIP-44路径派生私钥。
*   **FR4.1.3 (密钥生成)**: `GenerateSymmetricKey()`, `GenerateECIESKeyPair()`, `GenerateEd25519KeyPair()`。

#### 4.2. 数据加解密
*   **FR4.2.1 (对称加密)**:
    *   `Encrypt(plaintext, kek)`: 实现**FR3.2**定义的`SealedBox`格式的加密。内部会生成一个临时的DEK。
    *   `Decrypt(sealedBox, kek)`: 解密`SealedBox`。
*   **FR4.2.2 (混合加密)**:
    *   `EncryptHybrid(plaintext, recipientPublicKey)`。
    *   `DecryptHybrid(ciphertext, recipientPrivateKey)`。

#### 4.3. 签名与验证
*   **FR4.3.1 (ECDSA签名)**: `SignEthereumTx(unsignedTx, privateKey)`，`SignPersonalMessage(message, privateKey)`。
*   **FR4.3.2 (Ed25519签名)**: `SignEd25519(message, privateKey)`, `VerifyEd25519(...)`。

---

### 5. 接口定义 (API Specification)

```go
// core/crypto/e2ee_v2.go

// Encrypt 使用KEK加密数据，返回一个标准化的SealedBox。
func Encrypt(plaintext []byte, kek []byte) ([]byte, error)

// Decrypt 解密一个SealedBox。
func Decrypt(sealedBox []byte, kek []byte) ([]byte, error)


// core/crypto/bip39.go

// GenerateMnemonic 生成一个指定位数的BIP39助记词。
func GenerateMnemonic(bits int) (string, error)

// NewMasterKeyFromMnemonic 从助记词和密码(passphrase)创建BIP32主密钥。
func NewMasterKeyFromMnemonic(mnemonic, passphrase string) ([]byte, error)


// core/crypto/hdwallet.go

// DerivePrivateKeyForPath 从主密钥和BIP44路径派生出私钥。
func DerivePrivateKeyForPath(masterKey []byte, path string) (crypto.PrivateKey, error)
```

---

### 6. 跨平台导出与使用

*   **FR6.1 (Go Mobile/WASM导出)**:
    *   必须创建`exports_*.go`文件，将上述**所有公共API**导出。
    *   接口设计必须处理好`[]byte`与平台特定类型的转换。对于复杂的返回结果（如密钥对），应返回**序列化后的JSON字符串**。
    *   所有导出的函数**必须**返回一个`error`字符串，`""`表示成功。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**: 密码学操作必须尽可能快，不应成为UI交互的瓶颈。
*   **NFR7.2 (安全性 - 最高优先级)**:
    *   **零漏洞**: 代码必须经过严格的内部审查和**第三方专业安全公司的审计**。
    *   **抗侧信道攻击**: 关键操作（如密钥比较）必须使用常量时间算法 (`crypto/subtle`)。
    *   **内存安全**: 必须尽最大努力在敏感密钥使用完毕后，从内存中**显式地将其清零**。
*   **NFR7.3 (可测试性)**: 测试覆盖率目标为100%，并**必须**包含针对所有加密算法的已知答案测试向量(KAT)。

---

### 8. 技术约束与开发规范

*   **TC8.1 (核心依赖)**:
    *   **密码学**: **`golang.org/x/crypto`**。
    *   **BIP39/32**: `github.com/tyler-smith/go-bip39`, `github.com/btcsuite/btcd/btcutil/hdkeychain` 等经过社区广泛使用的库。
    *   **EVM相关**: `github.com/ethereum/go-ethereum/crypto`。
*   **TC8.2 (开发规范)**:
    *   **严格审查**: 对`core/crypto`的任何PR都必须经过**至少两名**核心安全负责人/架构师的批准。
    *   **禁止裸露原语**: 严禁直接暴露`crypto/aes`等底层原语给上层调用。所有功能都必须通过本包封装好的、安全的API来提供。

---
这份版本2.0的SRS为`core/crypto`库的设计和实现提供了坚实、安全、且面向未来的指导。通过引入分层密钥体系和标准化的加密方案，它能为CINA.CLUB的Web3钱包和其他E2EE功能提供金融级别的安全保障。