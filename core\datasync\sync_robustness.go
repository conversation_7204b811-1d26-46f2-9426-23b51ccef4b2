/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-01 11:00:00
Modified: 2025-01-01 11:00:00
*/

package datasync

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// SyncErrorType defines different types of sync errors
type SyncErrorType string

const (
	SyncErrorTypeNetwork    SyncErrorType = "NETWORK"
	SyncErrorTypeAuth       SyncErrorType = "AUTH"
	SyncErrorTypeConflict   SyncErrorType = "CONFLICT"
	SyncErrorTypeCorruption SyncErrorType = "CORRUPTION"
	SyncErrorTypeQuota      SyncErrorType = "QUOTA"
	SyncErrorTypeTimeout    SyncErrorType = "TIMEOUT"
	SyncErrorTypeValidation SyncErrorType = "VALIDATION"
	SyncErrorTypeInternal   SyncErrorType = "INTERNAL"
)

// SyncErrorSeverity defines error severity levels
type SyncErrorSeverity string

const (
	SyncErrorSeverityLow      SyncErrorSeverity = "LOW"
	SyncErrorSeverityMedium   SyncErrorSeverity = "MEDIUM"
	SyncErrorSeverityHigh     SyncErrorSeverity = "HIGH"
	SyncErrorSeverityCritical SyncErrorSeverity = "CRITICAL"
)

// SyncError represents a detailed sync error
type SyncError struct {
	ID         string                 `json:"id"`
	Type       SyncErrorType          `json:"type"`
	Severity   SyncErrorSeverity      `json:"severity"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details"`
	ItemPath   string                 `json:"item_path,omitempty"`
	ChunkID    string                 `json:"chunk_id,omitempty"`
	SessionID  string                 `json:"session_id,omitempty"`
	Timestamp  time.Time              `json:"timestamp"`
	Retryable  bool                   `json:"retryable"`
	RetryCount int                    `json:"retry_count"`
	Context    map[string]interface{} `json:"context"`
	StackTrace string                 `json:"stack_trace,omitempty"`
}

// RetryPolicy defines retry behavior for sync operations
type RetryPolicy struct {
	MaxRetries      int             `json:"max_retries"`
	InitialDelay    time.Duration   `json:"initial_delay"`
	MaxDelay        time.Duration   `json:"max_delay"`
	BackoffFactor   float64         `json:"backoff_factor"`
	Jitter          bool            `json:"jitter"`
	RetryableErrors []SyncErrorType `json:"retryable_errors"`
}

// DefaultRetryPolicy returns a sensible default retry policy
func DefaultRetryPolicy() *RetryPolicy {
	return &RetryPolicy{
		MaxRetries:    3,
		InitialDelay:  1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        true,
		RetryableErrors: []SyncErrorType{
			SyncErrorTypeNetwork,
			SyncErrorTypeTimeout,
			SyncErrorTypeInternal,
		},
	}
}

// ConflictResolver handles sync conflicts automatically where possible
type ConflictResolver interface {
	// Resolve attempts to automatically resolve a conflict
	Resolve(ctx context.Context, conflict *SyncConflict) (*ConflictResolutionResult, error)

	// CanResolve determines if a conflict can be automatically resolved
	CanResolve(conflict *SyncConflict) bool

	// GetStrategy returns the resolution strategy for a conflict type
	GetStrategy(conflictType ConflictType) ConflictResolution
}

// ConflictResolutionResult represents the result of conflict resolution
type ConflictResolutionResult struct {
	Resolved     bool                   `json:"resolved"`
	Resolution   ConflictResolution     `json:"resolution"`
	ResolvedItem *SyncItem              `json:"resolved_item,omitempty"`
	Reason       string                 `json:"reason"`
	Metadata     map[string]interface{} `json:"metadata"`
	Timestamp    time.Time              `json:"timestamp"`
}

// PerformanceMetrics tracks sync performance
type PerformanceMetrics struct {
	// Timing metrics
	TotalDuration   time.Duration `json:"total_duration"`
	NetworkTime     time.Duration `json:"network_time"`
	ProcessingTime  time.Duration `json:"processing_time"`
	EncryptionTime  time.Duration `json:"encryption_time"`
	CompressionTime time.Duration `json:"compression_time"`

	// Throughput metrics
	BytesPerSecond  float64 `json:"bytes_per_second"`
	ItemsPerSecond  float64 `json:"items_per_second"`
	ChunksPerSecond float64 `json:"chunks_per_second"`

	// Efficiency metrics
	CompressionRatio   float64 `json:"compression_ratio"`
	DeduplicationRatio float64 `json:"deduplication_ratio"`
	CacheHitRate       float64 `json:"cache_hit_rate"`

	// Error metrics
	ErrorRate    float64 `json:"error_rate"`
	RetryRate    float64 `json:"retry_rate"`
	ConflictRate float64 `json:"conflict_rate"`

	// Resource usage
	PeakMemoryUsage  int64 `json:"peak_memory_usage"`
	NetworkBytesUp   int64 `json:"network_bytes_up"`
	NetworkBytesDown int64 `json:"network_bytes_down"`

	// Quality metrics
	DataIntegrityScore float64 `json:"data_integrity_score"`
	ConsistencyScore   float64 `json:"consistency_score"`

	Timestamp time.Time `json:"timestamp"`
}

// SyncHealthStatus represents the health of the sync system
type SyncHealthStatus struct {
	IsHealthy        bool                   `json:"is_healthy"`
	Status           string                 `json:"status"`
	LastSync         time.Time              `json:"last_sync"`
	SyncFrequency    time.Duration          `json:"sync_frequency"`
	PendingItems     int                    `json:"pending_items"`
	ConflictCount    int                    `json:"conflict_count"`
	ErrorCount       int                    `json:"error_count"`
	PerformanceScore float64                `json:"performance_score"`
	Issues           []string               `json:"issues"`
	Recommendations  []string               `json:"recommendations"`
	Metrics          PerformanceMetrics     `json:"metrics"`
	ComponentStatus  map[string]interface{} `json:"component_status"`
}

// SyncMonitor monitors sync operations and collects metrics
type SyncMonitor interface {
	// Metrics collection
	RecordMetric(name string, value float64, tags map[string]string) error
	RecordDuration(name string, duration time.Duration, tags map[string]string) error
	RecordError(err *SyncError) error

	// Health monitoring
	GetHealthStatus() *SyncHealthStatus
	CheckHealth(ctx context.Context) error

	// Performance analysis
	GetPerformanceMetrics(timeRange TimeRange) (*PerformanceMetrics, error)
	GetPerformanceTrends(timeRange TimeRange) ([]PerformanceDataPoint, error)

	// Alerting
	SetThreshold(metric string, threshold float64) error
	GetAlerts(timeRange TimeRange) ([]SyncAlert, error)
}

// TimeRange represents a time period for queries
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// PerformanceDataPoint represents a performance measurement at a specific time
type PerformanceDataPoint struct {
	Timestamp time.Time              `json:"timestamp"`
	Metrics   map[string]float64     `json:"metrics"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// SyncAlert represents a sync-related alert
type SyncAlert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Message     string                 `json:"message"`
	Metric      string                 `json:"metric"`
	Threshold   float64                `json:"threshold"`
	ActualValue float64                `json:"actual_value"`
	Timestamp   time.Time              `json:"timestamp"`
	Resolved    bool                   `json:"resolved"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// RobustSyncEngine enhances the basic sync engine with robustness features
type RobustSyncEngine struct {
	*SyncEngine
	retryPolicy      *RetryPolicy
	conflictResolver ConflictResolver
	monitor          SyncMonitor
	errorHandler     ErrorHandler
	config           RobustSyncConfig
	mu               sync.RWMutex

	// State tracking
	activeOperations map[string]*SyncOperation
	errorHistory     []SyncError
	metrics          PerformanceMetrics
}

// RobustSyncConfig contains configuration for robust sync features
type RobustSyncConfig struct {
	// Error handling
	MaxErrorHistory       int  `json:"max_error_history"`
	ErrorReportingEnabled bool `json:"error_reporting_enabled"`

	// Performance monitoring
	MetricsEnabled        bool               `json:"metrics_enabled"`
	MetricsInterval       time.Duration      `json:"metrics_interval"`
	PerformanceThresholds map[string]float64 `json:"performance_thresholds"`

	// Reliability features
	IntegrityCheckEnabled bool `json:"integrity_check_enabled"`
	AutoRecoveryEnabled   bool `json:"auto_recovery_enabled"`
	BackupEnabled         bool `json:"backup_enabled"`

	// Optimization
	CompressionEnabled   bool `json:"compression_enabled"`
	DeduplicationEnabled bool `json:"deduplication_enabled"`
	PrefetchEnabled      bool `json:"prefetch_enabled"`

	// Timeouts
	OperationTimeout  time.Duration `json:"operation_timeout"`
	NetworkTimeout    time.Duration `json:"network_timeout"`
	ProcessingTimeout time.Duration `json:"processing_timeout"`
}

// DefaultRobustSyncConfig returns a default configuration
func DefaultRobustSyncConfig() *RobustSyncConfig {
	return &RobustSyncConfig{
		MaxErrorHistory:       1000,
		ErrorReportingEnabled: true,
		MetricsEnabled:        true,
		MetricsInterval:       30 * time.Second,
		PerformanceThresholds: map[string]float64{
			"error_rate":       0.05,        // 5%
			"bytes_per_second": 1024 * 1024, // 1MB/s
			"cache_hit_rate":   0.8,         // 80%
		},
		IntegrityCheckEnabled: true,
		AutoRecoveryEnabled:   true,
		BackupEnabled:         true,
		CompressionEnabled:    true,
		DeduplicationEnabled:  true,
		PrefetchEnabled:       true,
		OperationTimeout:      5 * time.Minute,
		NetworkTimeout:        30 * time.Second,
		ProcessingTimeout:     2 * time.Minute,
	}
}

// SyncOperation tracks an active sync operation
type SyncOperation struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	Status     SyncStatus             `json:"status"`
	StartTime  time.Time              `json:"start_time"`
	UpdateTime time.Time              `json:"update_time"`
	Progress   *SyncProgress          `json:"progress"`
	Errors     []SyncError            `json:"errors"`
	Metadata   map[string]interface{} `json:"metadata"`
	Context    context.Context        `json:"-"`
	CancelFunc context.CancelFunc     `json:"-"`
}

// ErrorHandler handles sync errors with various strategies
type ErrorHandler interface {
	// Handle processes an error and determines the appropriate action
	Handle(ctx context.Context, err *SyncError, operation *SyncOperation) (*ErrorHandlingResult, error)

	// ShouldRetry determines if an operation should be retried
	ShouldRetry(err *SyncError, retryCount int) bool

	// GetRecoveryStrategy returns a recovery strategy for an error
	GetRecoveryStrategy(err *SyncError) RecoveryStrategy
}

// ErrorHandlingResult represents the result of error handling
type ErrorHandlingResult struct {
	Action        ErrorAction            `json:"action"`
	RetryAfter    time.Duration          `json:"retry_after"`
	RecoverySteps []RecoveryStep         `json:"recovery_steps"`
	ShouldAlert   bool                   `json:"should_alert"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ErrorAction defines what action to take for an error
type ErrorAction string

const (
	ErrorActionRetry    ErrorAction = "RETRY"
	ErrorActionSkip     ErrorAction = "SKIP"
	ErrorActionFail     ErrorAction = "FAIL"
	ErrorActionRecover  ErrorAction = "RECOVER"
	ErrorActionEscalate ErrorAction = "ESCALATE"
)

// RecoveryStrategy defines how to recover from errors
type RecoveryStrategy string

const (
	RecoveryStrategyReset    RecoveryStrategy = "RESET"
	RecoveryStrategyRollback RecoveryStrategy = "ROLLBACK"
	RecoveryStrategyRepair   RecoveryStrategy = "REPAIR"
	RecoveryStrategyReplace  RecoveryStrategy = "REPLACE"
	RecoveryStrategyManual   RecoveryStrategy = "MANUAL"
)

// RecoveryStep represents a step in the recovery process
type RecoveryStep struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Action      func() error           `json:"-"`
	Parameters  map[string]interface{} `json:"parameters"`
	Required    bool                   `json:"required"`
	Completed   bool                   `json:"completed"`
	Error       string                 `json:"error,omitempty"`
}

// NewRobustSyncEngine creates a new robust sync engine
func NewRobustSyncEngine(
	baseEngine *SyncEngine,
	retryPolicy *RetryPolicy,
	resolver ConflictResolver,
	monitor SyncMonitor,
	handler ErrorHandler,
	config *RobustSyncConfig,
) *RobustSyncEngine {
	if retryPolicy == nil {
		retryPolicy = DefaultRetryPolicy()
	}
	if config == nil {
		config = DefaultRobustSyncConfig()
	}

	return &RobustSyncEngine{
		SyncEngine:       baseEngine,
		retryPolicy:      retryPolicy,
		conflictResolver: resolver,
		monitor:          monitor,
		errorHandler:     handler,
		config:           *config,
		activeOperations: make(map[string]*SyncOperation),
		errorHistory:     make([]SyncError, 0),
	}
}

// PushChangesRobust performs a robust push operation with error handling and retries
func (rse *RobustSyncEngine) PushChangesRobust(ctx context.Context, items []SyncItem) error {
	operation := rse.startOperation("push", ctx)
	defer rse.endOperation(operation.ID)

	startTime := time.Now()

	// Apply timeout
	ctx, cancel := context.WithTimeout(ctx, rse.config.OperationTimeout)
	defer cancel()

	// Perform pre-sync validation
	if err := rse.validateItems(items); err != nil {
		syncErr := &SyncError{
			ID:        generateErrorID(),
			Type:      SyncErrorTypeValidation,
			Severity:  SyncErrorSeverityHigh,
			Message:   "Item validation failed",
			Details:   err.Error(),
			Timestamp: time.Now(),
			Retryable: false,
		}
		rse.recordError(syncErr)
		return err
	}

	// Perform the push with retry logic
	var lastErr error
	for attempt := 0; attempt <= rse.retryPolicy.MaxRetries; attempt++ {
		if attempt > 0 {
			// Calculate backoff delay
			delay := rse.calculateBackoffDelay(attempt)
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		err := rse.SyncEngine.PushChanges(ctx, items)
		if err == nil {
			// Success - record metrics
			duration := time.Since(startTime)
			rse.recordSuccessMetrics("push", duration, len(items))
			return nil
		}

		// Handle the error
		syncErr := rse.convertToSyncError(err, "push")
		syncErr.RetryCount = attempt

		if rse.errorHandler != nil {
			result, handlerErr := rse.errorHandler.Handle(ctx, syncErr, operation)
			if handlerErr != nil {
				rse.recordError(syncErr)
				return handlerErr
			}

			switch result.Action {
			case ErrorActionRetry:
				if attempt < rse.retryPolicy.MaxRetries {
					lastErr = err
					continue
				}
			case ErrorActionSkip:
				return nil // Skip this operation
			case ErrorActionFail:
				rse.recordError(syncErr)
				return err
			case ErrorActionRecover:
				if recoveryErr := rse.executeRecovery(result.RecoverySteps); recoveryErr != nil {
					rse.recordError(syncErr)
					return recoveryErr
				}
				continue // Retry after recovery
			}
		}

		// Check if error is retryable
		if !rse.isRetryable(syncErr) {
			rse.recordError(syncErr)
			return err
		}

		lastErr = err
	}

	// All retries exhausted
	finalErr := &SyncError{
		ID:        generateErrorID(),
		Type:      SyncErrorTypeInternal,
		Severity:  SyncErrorSeverityCritical,
		Message:   "Push operation failed after all retries",
		Details:   lastErr.Error(),
		Timestamp: time.Now(),
		Retryable: false,
	}
	rse.recordError(finalErr)
	return lastErr
}

// PullChangesRobust performs a robust pull operation with error handling and retries
func (rse *RobustSyncEngine) PullChangesRobust(ctx context.Context) ([]SyncItem, error) {
	operation := rse.startOperation("pull", ctx)
	defer rse.endOperation(operation.ID)

	startTime := time.Now()

	// Apply timeout
	ctx, cancel := context.WithTimeout(ctx, rse.config.OperationTimeout)
	defer cancel()

	// Perform the pull with retry logic
	var lastErr error
	for attempt := 0; attempt <= rse.retryPolicy.MaxRetries; attempt++ {
		if attempt > 0 {
			delay := rse.calculateBackoffDelay(attempt)
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}

		items, err := rse.SyncEngine.PullChanges(ctx)
		if err == nil {
			// Success - handle conflicts if any
			resolvedItems, resolveErr := rse.resolveConflicts(ctx, items)
			if resolveErr != nil {
				syncErr := rse.convertToSyncError(resolveErr, "conflict_resolution")
				rse.recordError(syncErr)
				return items, resolveErr // Return original items even if conflict resolution failed
			}

			// Perform integrity check
			if rse.config.IntegrityCheckEnabled {
				if integrityErr := rse.verifyIntegrity(resolvedItems); integrityErr != nil {
					syncErr := rse.convertToSyncError(integrityErr, "integrity_check")
					rse.recordError(syncErr)
					// Continue with warning - don't fail the entire operation
				}
			}

			// Record success metrics
			duration := time.Since(startTime)
			rse.recordSuccessMetrics("pull", duration, len(resolvedItems))
			return resolvedItems, nil
		}

		// Handle the error
		syncErr := rse.convertToSyncError(err, "pull")
		syncErr.RetryCount = attempt

		if !rse.isRetryable(syncErr) {
			rse.recordError(syncErr)
			return nil, err
		}

		lastErr = err
	}

	// All retries exhausted
	finalErr := &SyncError{
		ID:        generateErrorID(),
		Type:      SyncErrorTypeInternal,
		Severity:  SyncErrorSeverityCritical,
		Message:   "Pull operation failed after all retries",
		Details:   lastErr.Error(),
		Timestamp: time.Now(),
		Retryable: false,
	}
	rse.recordError(finalErr)
	return nil, lastErr
}

// validateItems validates sync items before processing
func (rse *RobustSyncEngine) validateItems(items []SyncItem) error {
	for i, item := range items {
		if item.ID == "" {
			return fmt.Errorf("item %d has empty ID", i)
		}
		if item.Path == "" {
			return fmt.Errorf("item %d has empty path", i)
		}
		if item.Size < 0 {
			return fmt.Errorf("item %d has negative size", i)
		}
		// Add more validation rules as needed
	}
	return nil
}

// resolveConflicts attempts to resolve conflicts automatically
func (rse *RobustSyncEngine) resolveConflicts(ctx context.Context, items []SyncItem) ([]SyncItem, error) {
	if rse.conflictResolver == nil {
		return items, nil
	}

	resolvedItems := make([]SyncItem, 0, len(items))

	for _, item := range items {
		// Check if this item has conflicts (simplified logic)
		// In reality, you'd need to detect conflicts by comparing with local state

		resolvedItems = append(resolvedItems, item)
	}

	return resolvedItems, nil
}

// verifyIntegrity performs integrity checks on sync items
func (rse *RobustSyncEngine) verifyIntegrity(items []SyncItem) error {
	for _, item := range items {
		// Verify content hash matches actual content
		if !item.IsDeleted {
			// In a real implementation, you'd read the actual content and verify the hash
			// This is a placeholder for integrity verification logic
		}
	}
	return nil
}

// Helper methods

func (rse *RobustSyncEngine) startOperation(opType string, ctx context.Context) *SyncOperation {
	rse.mu.Lock()
	defer rse.mu.Unlock()

	ctx, cancel := context.WithCancel(ctx)

	operation := &SyncOperation{
		ID:         generateOperationID(),
		Type:       opType,
		Status:     SyncStatusPreparing,
		StartTime:  time.Now(),
		UpdateTime: time.Now(),
		Errors:     make([]SyncError, 0),
		Metadata:   make(map[string]interface{}),
		Context:    ctx,
		CancelFunc: cancel,
	}

	rse.activeOperations[operation.ID] = operation
	return operation
}

func (rse *RobustSyncEngine) endOperation(operationID string) {
	rse.mu.Lock()
	defer rse.mu.Unlock()

	if operation, exists := rse.activeOperations[operationID]; exists {
		operation.CancelFunc()
		delete(rse.activeOperations, operationID)
	}
}

func (rse *RobustSyncEngine) calculateBackoffDelay(attempt int) time.Duration {
	delay := time.Duration(float64(rse.retryPolicy.InitialDelay) *
		float64(attempt) * rse.retryPolicy.BackoffFactor)

	if delay > rse.retryPolicy.MaxDelay {
		delay = rse.retryPolicy.MaxDelay
	}

	if rse.retryPolicy.Jitter {
		// Add 20% jitter
		jitter := time.Duration(float64(delay) * 0.2 * (0.5 - float64(time.Now().UnixNano()%1000)/1000.0))
		delay += jitter
	}

	return delay
}

func (rse *RobustSyncEngine) isRetryable(err *SyncError) bool {
	for _, retryableType := range rse.retryPolicy.RetryableErrors {
		if err.Type == retryableType {
			return true
		}
	}
	return err.Retryable
}

func (rse *RobustSyncEngine) convertToSyncError(err error, operation string) *SyncError {
	return &SyncError{
		ID:        generateErrorID(),
		Type:      SyncErrorTypeInternal, // Would be determined based on error type
		Severity:  SyncErrorSeverityMedium,
		Message:   err.Error(),
		Timestamp: time.Now(),
		Retryable: true,
		Context: map[string]interface{}{
			"operation": operation,
		},
	}
}

func (rse *RobustSyncEngine) recordError(err *SyncError) {
	rse.mu.Lock()
	defer rse.mu.Unlock()

	rse.errorHistory = append(rse.errorHistory, *err)

	// Maintain error history size
	if len(rse.errorHistory) > rse.config.MaxErrorHistory {
		rse.errorHistory = rse.errorHistory[len(rse.errorHistory)-rse.config.MaxErrorHistory:]
	}

	// Report to monitor if available
	if rse.monitor != nil {
		rse.monitor.RecordError(err)
	}
}

func (rse *RobustSyncEngine) recordSuccessMetrics(operation string, duration time.Duration, itemCount int) {
	if rse.monitor != nil {
		tags := map[string]string{
			"operation": operation,
		}
		rse.monitor.RecordDuration("sync_duration", duration, tags)
		rse.monitor.RecordMetric("sync_items_processed", float64(itemCount), tags)
		rse.monitor.RecordMetric("sync_success", 1, tags)
	}
}

func (rse *RobustSyncEngine) executeRecovery(steps []RecoveryStep) error {
	for _, step := range steps {
		if step.Required && step.Action != nil {
			if err := step.Action(); err != nil {
				return fmt.Errorf("recovery step %s failed: %w", step.ID, err)
			}
		}
	}
	return nil
}

// GetHealthStatus returns the current health status of the sync system
func (rse *RobustSyncEngine) GetHealthStatus() *SyncHealthStatus {
	if rse.monitor != nil {
		return rse.monitor.GetHealthStatus()
	}

	// Fallback basic health status
	return &SyncHealthStatus{
		IsHealthy:        true,
		Status:           "healthy",
		LastSync:         time.Now(),
		PendingItems:     0,
		ConflictCount:    0,
		ErrorCount:       len(rse.errorHistory),
		PerformanceScore: 1.0,
	}
}

// GetErrorHistory returns recent sync errors
func (rse *RobustSyncEngine) GetErrorHistory() []SyncError {
	rse.mu.RLock()
	defer rse.mu.RUnlock()

	// Return a copy to prevent external modification
	history := make([]SyncError, len(rse.errorHistory))
	copy(history, rse.errorHistory)
	return history
}

// GetActiveOperations returns currently active sync operations
func (rse *RobustSyncEngine) GetActiveOperations() map[string]*SyncOperation {
	rse.mu.RLock()
	defer rse.mu.RUnlock()

	// Return a copy to prevent external modification
	operations := make(map[string]*SyncOperation)
	for id, op := range rse.activeOperations {
		operations[id] = op
	}
	return operations
}

// Utility functions for ID generation
func generateErrorID() string {
	return fmt.Sprintf("err_%d", time.Now().UnixNano())
}

func generateOperationID() string {
	return fmt.Sprintf("op_%d", time.Now().UnixNano())
}
