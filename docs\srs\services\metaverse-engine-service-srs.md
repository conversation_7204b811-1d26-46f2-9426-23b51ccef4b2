﻿好的，遵照您的指示，我们来生成一份为 `metaverse-engine-service` (元宇宙引擎/场景服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **世界/场景定义**: 引入更专业、更结构化的场景描述语言或格式，支持动态加载、物理属性和可交互对象脚本。
2.  **实例管理与伸缩**: 详细定义世界实例的生命周期、负载均衡策略和自动伸缩机制，以应对动态的用户流量。
3.  **状态同步协议**: 细化状态同步的协议，引入增量更新、兴趣管理（Interest Management）和客户端预测/服务端调和（CS/SR）等高级概念，以优化带宽和体验。
4.  **服务器端权威逻辑**: 强调服务器作为权威仲裁者的角色，包括服务器端物理校验和脚本执行，以防止作弊。
5.  **空间音频与通信**: 详细描述与WebRTC服务器（或第三方服务）的信令交互，以实现沉浸式空间音频。
6.  **细化API与数据模型**: 提供更具体的gRPC/WebSocket接口定义，并对数据模型进行优化。
7.  **强化非功能性需求**: 补充更具体、可量化的性能指标，如Tick Rate、延迟、抖动（Jitter）。

这份文档将描绘一个功能强大、性能卓越、可扩展，且能为大规模用户提供稳定、流畅沉浸式体验的元宇宙后端引擎。

---

### CINA.CLUB - metaverse-engine-service 需求规格说明书

**版本: 2.0 (生产级定义，集成高级同步协议与服务器权威)**  
**发布日期: 2025-06-23**  
**最后修订日期: 2025-06-23**  
**文档负责人:** [元宇宙产品经理/架构师名称]  
**审批人:** [CTO/项目总监名称]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 致力于打造一个全面的智能生活服务平台。`metaverse-engine-service` 作为其中的前瞻性核心组件，旨在为用户提供**高性能、可扩展、且具有服务器端权威逻辑**的沉浸式3D虚拟交互环境（“海内灵境数字世界”）。用户可以通过其个性化的数字孪生在这些虚拟场景中进行社交、体验服务、参与活动，从而极大地丰富用户体验，增强平台的用户粘性，并为未来的服务模式和商业化探索提供新的可能性。

#### 1.2. 服务范围
本服务 **负责**:
*   **世界与场景管理**: 加载和管理虚拟世界的静态场景描述，包括物理属性、导航网格和可交互对象定义。
*   **世界实例管理与负载均衡**:
    *   动态创建、管理和销毁可供多用户同时在线的虚拟世界实例（房间/场景）。
    *   根据负载情况，在多个服务器节点间智能地分配和调度实例。
*   **用户连接与状态同步**:
    *   处理用户与特定世界实例的WebSocket/WebRTC连接。
    *   以高频率、低延迟的方式，同步所有动态实体（Avatar、可移动对象）的状态。
    *   实现**兴趣管理(Interest Management)**，只向客户端发送其关心范围内的数据。
*   **服务器端权威逻辑**:
    *   作为所有物理交互和游戏规则的**最终仲裁者**，以防止作弊。
    *   执行服务器端脚本，驱动场景内动态对象的行为。
*   **场景内通信协调**: 为场景内的空间音频和文本聊天提供信令交换和消息路由。
*   **与平台生态的集成**: 与`digital-twin-service`, `chat-api-service`, `gamification-service`等服务深度协同。

本服务 **不负责**:
*   **客户端的3D渲染逻辑**。
*   **复杂的自定义游戏逻辑** (但提供脚本化基础)。
*   **虚拟分身的详细创建与资产管理** (由 `digital-twin-service` 负责)。
*   **WebRTC媒体服务器(SFU/MCU)**的实现 (但会与其进行信令交互)。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`metaverse-engine-service` 是连接用户数字身份 (`digital-twin-service`) 与沉浸式体验的**实时交互核心**。它是一个典型的、有状态的、对性能要求极高的游戏服务器后端，为平台上的社交、服务预览、虚拟活动等提供了一个动态的、可信的3D交互层。

#### 2.2. 主要功能概述
*   支持大规模并发用户的、可自动伸缩的世界实例管理。
*   基于兴趣管理和增量更新的高效状态同步协议。
*   服务器端权威的物理与逻辑校验，防止作弊。
*   可脚本化的、动态的场景交互。
*   沉浸式空间音频的信令支持。

### 3. 核心流程图

#### 3.1. 客户端状态同步与服务器端调和
```mermaid
sequenceDiagram
    participant ClientA
    participant MetaverseEngine as ME
    participant Redis
    participant OtherClients

    ClientA->>ClientA: 1. **[Prediction]** User moves. <br/> Locally render new position immediately.
    ClientA->>ME: 2. [C2S_AVATAR_UPDATE] Send input/new state (position, velocity)
    
    ME->>ME: 3. **[Server Reconciliation]** <br/> - Validate input (e.g., check speed). <br/> - Run server-side physics simulation step. <br/> - Determine authoritative new state.
    
    ME->>DB/Redis: 4. Update authoritative state
    
    Note over ME: **[Interest Management]**
    ME->>ME: 5. Determine which clients need this update (e.g., are nearby).
    
    ME->>OtherClients: 6a. [S2C_WORLD_STATE_UPDATE] Broadcast authoritative state of ClientA's avatar.
    ME->>ClientA: 6b. [S2C_WORLD_STATE_UPDATE] Send authoritative state back to ClientA.
    
    ClientA->>ClientA: 7. **[Correction]** <br/> - Compare server's authoritative state with its predicted state. <br/> - If different, smoothly correct local avatar's position.
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 世界与实例管理
*   **FR4.1.1 (场景定义)**: 系统应能加载和解析结构化的场景描述文件（如JSON, XML或自定义格式），其中包含实体列表、组件（如Transform, PhysicsBody, Script）、地形和导航网格数据。
*   **FR4.1.2 (实例生命周期)**:
    *   **创建**: 提供API，允许用户请求创建或加入一个世界实例。系统根据负载情况，在最合适的服务器节点上启动一个新的实例进程/goroutine。
    *   **销毁**: 实例在无用户后，经过一个可配置的超时时间，应能自动销毁并释放资源。
*   **FR4.1.3 (自动伸缩)**: (高级) 整个服务应能根据总在线人数或实例数量，自动增减服务器节点。

#### 4.2. 高效状态同步
*   **FR4.2.1 (兴趣管理 - Interest Management)**:
    *   服务器必须只向每个客户端发送其“感兴趣”的实体状态更新。
    *   兴趣范围可以基于空间邻近度（如客户端周围50米半径内）或明确的订阅关系。
*   **FR4.2.2 (增量更新与压缩)**:
    *   状态同步包必须是增量式的，只包含自上次更新以来发生变化的数据。
    *   必须采用高效的二进制序列化格式（Protobuf）和数据压缩算法（如Delta Compression, Bit-packing）以最大限度地减少带宽消耗。
*   **FR4.2.3 (客户端预测与服务端调和)**:
    *   客户端应实现**预测(Prediction)**机制，在收到服务器确认前，根据用户输入立即在本地渲染结果，以隐藏网络延迟。
    *   服务器作为权威，在收到客户端输入后，进行验证和模拟，然后将**权威状态**广播出去。
    *   客户端在收到服务器的权威状态后，与自己的预测状态进行比较，并平滑地**修正(Correction)**任何差异。

#### 4.3. 服务器端权威逻辑
*   **FR4.3.1 (物理校验)**: 服务器必须对客户端上传的位置和状态进行合法性校验，如移动速度不能超过上限、不能穿墙（基于服务器端碰撞检测）。
*   **FR4.3.2 (服务器端脚本)**:
    *   系统应能为场景中的实体附加服务器端脚本（推荐使用嵌入式语言如**Lua**或**JavaScript (via `goja`)**）。
    *   这些脚本可以响应事件（如`OnInteract`, `OnTimer`），并能修改实体状态或调用平台其他服务的API（通过一个受限的沙箱接口）。

#### 4.4. 场景内通信
*   **FR4.4.1 (空间音频)**:
    *   本服务负责**信令交换**。当用户加入实例时，本服务通知其需要与哪些其他用户建立WebRTC连接。
    *   在状态同步包中，包含每个Avatar的头部位置和朝向。客户端根据这些信息，在本地计算和渲染空间音频效果（音量、声道）。
    *   实际的音视频流通过独立的**WebRTC媒体服务器(SFU)**进行转发。
*   **FR4.4.2 (场景文本聊天)**: 接收客户端在场景内发送的文本聊天消息，并根据频道（公共/局部）进行广播，同时异步转发给`chat-api-service`进行持久化。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端实时通信接口 (WebSocket)
*   **协议**: WebSocket。
*   **消息格式**: **Protocol Buffers (Protobuf)**。
*   **核心C2S事件**: `AuthenticateRequest`, `JoinInstanceRequest`, `AvatarStateUpdate` (包含用户输入和预测状态), `ObjectInteractionRequest`.
*   **核心S2C事件**: `JoinInstanceResult`, `WorldStateUpdate` (包含权威状态和时间戳), `ObjectInteractionResult`.

#### 5.2. HTTP/gRPC管理API接口
*   **版本**: `/api/v1/metaverse`
*   **认证**: 用户JWT或S2S认证。
*   **核心端点**:
    *   `GET /worlds`: 列出可用的世界定义。
    *   `POST /worlds/{worldId}/instances/join`: **请求加入实例**。此接口是核心入口，它会进行负载均衡，返回一个具体的`metaverse-engine-service`实例的WebSocket地址和一次性的连接令牌。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型
*   **运行时 (内存 & Redis)**:
    *   `WorldInstance`: `ID`, `WorldDefinitionID`, `map<UserID, ClientConnection>`, `map<ObjectID, EntityState>`, 物理引擎世界实例。
    *   `EntityState`: `ID`, `Position`, `Rotation`, `Velocity`, `ScriptState (JSONB)`.
*   **持久化 (PostgreSQL/MongoDB)**:
    *   `world_definitions`: `id`, `name`, `scene_graph_asset_key`.
    *   `scene_scripts`: `id`, `name`, `script_code (LUA/JS)`.

#### 6.2. 数据持久化与存储
*   **世界静态定义**: PostgreSQL 或 MongoDB。
*   **活动实例的元数据**: Redis (如实例ID、在线人数、所在服务器节点)。
*   **高频变化状态**: **只在服务器内存中**。只在必要时（如用户下线时保存最后位置）才持久化到DB。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **Tick Rate**: 服务器逻辑更新和状态同步的目标频率 **>= 20Hz**。
*   **网络延迟 (Round-Trip Time)**: P95应 < 150ms。
*   **抖动 (Jitter)**: 状态更新包的到达时间间隔应尽可能稳定。
*   **并发连接数 (CCU)**: 单个服务器实例应能支持1000+并发用户。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。单个实例故障不应影响其他实例。
*   **优雅降级**: 在高负载时，服务器可以动态降低Tick Rate或减少非关键物理模拟的精度。

#### 7.3. 可扩展性需求
*   能够通过水平扩展服务器实例来增加总用户容量。
*   (未来) 支持将超大虚拟世界**分片(Sharding)**到多个服务器实例上，实现无缝大世界。

#### 7.4. 安全性需求
*   **防作弊**: **服务器端权威**是核心防作弊手段。所有客户端输入都必须经过服务器验证。
*   **通信加密**: 所有通信使用WSS (TLS)。
*   **脚本安全**: 服务器端脚本必须运行在安全的沙箱环境中，限制其对系统资源的访问。

### 8. 技术约束与选型建议
*   **语言**: Go。其并发模型和性能非常适合构建游戏服务器。
*   **物理引擎**: （可选）使用成熟的Go物理引擎库（如`engo.io/ecs`中的物理部分）或通过Cgo绑定C++的物理引擎（如Box2D, PhysX）。
*   **脚本语言**: **Lua** (`gopher-lua`) 是游戏领域最常用、最高效的嵌入式脚本语言。`goja` (JavaScript) 是另一个优秀的选择。
*   **网络协议**: 优先使用WebSocket。对于要求极低延迟的状态同步，可以评估在WebRTC Data Channels上实现自定义的、不可靠的UDP式协议。

---
这份版本2.0的SRS文档为`metaverse-engine-service`构建了一个生产级的、高性能的、安全的实时交互后端。它通过引入服务器端权威、兴趣管理和高级同步协议，为CINA.CLUB的沉浸式虚拟体验奠定了坚实的技术基础，能够支持一个稳定、流畅且可信的虚拟世界。