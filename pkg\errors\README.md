/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

# pkg/errors - 统一错误处理包

## 概述

`pkg/errors` 是 CINA.CLUB 平台的核心错误处理包，提供了统一、结构化、可追溯的错误处理解决方案。该包定义了标准的错误类型和错误码，并提供与 gRPC 状态码的无缝转换，确保错误信息在整个微服务架构中的一致性和可用性。

## 设计原则

- **标准化**: 所有服务使用统一的错误表示和传递方式
- **信息丰富**: 错误包含错误码、消息、元数据、堆栈跟踪和底层原因
- **gRPC 原生集成**: 与 gRPC 错误模型完美契合
- **可追溯性**: 支持 Go 1.13+ 的错误包装特性
- **开发者友好**: 提供简洁直观的 API

## 核心特性

### 1. 统一错误码

定义了与 HTTP 状态码和 gRPC 状态码对应的平台错误码：

```go
// 主要错误码
OK                // 200 - 成功
InvalidArgument   // 400 - 无效参数
Unauthenticated   // 401 - 未认证
PermissionDenied  // 403 - 权限不足
NotFound          // 404 - 资源不存在
AlreadyExists     // 409 - 资源已存在
Internal          // 500 - 内部错误
Unavailable       // 503 - 服务不可用
DeadlineExceeded  // 504 - 超时
```

### 2. 结构化错误

`AppError` 结构体包含丰富的上下文信息：

```go
type AppError struct {
    Code     ErrorCode         // 错误码
    Message  string           // 错误消息
    Cause    error           // 底层错误
    Metadata map[string]string // 元数据
    // stack (内部堆栈跟踪)
}
```

### 3. gRPC 集成

提供 `AppError` 与 gRPC Status 之间的双向转换：

- `ToGRPCStatus(err error) *status.Status`
- `FromGRPCError(err error) *AppError`
- `ToGRPCError(err error) error`

## 安装和导入

```go
import "pkg/errors"
```

## 基本使用

### 创建错误

```go
// 创建新错误
err := errors.New(errors.InvalidArgument, "email is required")

// 使用格式化字符串
err := errors.Newf(errors.InvalidArgument, "invalid email: %s", email)
```

### 包装错误

```go
// 包装现有错误
if err := db.Query(...); err != nil {
    return errors.Wrap(err, errors.Internal, "failed to query database")
}

// 使用格式化消息包装
if err := validateUser(user); err != nil {
    return errors.Wrapf(err, errors.InvalidArgument, "user validation failed for %s", user.ID)
}
```

### 添加元数据

```go
err := errors.New(errors.InvalidArgument, "validation failed")
err = errors.WithMeta(err, "field", "email")
err = errors.WithMeta(err, "value", user.Email)

// 或者一次添加多个元数据
metadata := map[string]string{
    "field": "email",
    "value": user.Email,
}
err = errors.WithMetadata(err, metadata)
```

### 检查错误

```go
// 检查错误码
if errors.IsCode(err, errors.NotFound) {
    // 处理资源不存在的情况
}

// 获取错误码
code := errors.GetCode(err)

// 获取元数据
metadata := errors.GetMetadata(err)
if field, exists := metadata["field"]; exists {
    log.Printf("validation failed for field: %s", field)
}
```

## 在微服务中的使用

### Repository 层

```go
func (r *userRepo) GetByID(ctx context.Context, id string) (*User, error) {
    user, err := r.db.QueryRow(ctx, "SELECT * FROM users WHERE id = $1", id)
    if err != nil {
        if errors.Is(err, pgx.ErrNoRows) {
            return nil, errors.Wrap(err, errors.NotFound, "user not found")
        }
        return nil, errors.Wrap(err, errors.Internal, "database query failed")
    }
    return user, nil
}
```

### Service 层

```go
func (s *userService) CreateUser(ctx context.Context, req *CreateUserRequest) error {
    // 参数验证
    if req.Email == "" {
        err := errors.New(errors.InvalidArgument, "email is required")
        return errors.WithMeta(err, "field", "email")
    }
    
    // 业务逻辑
    if err := s.repo.Create(ctx, user); err != nil {
        if errors.IsCode(err, errors.AlreadyExists) {
            return errors.Wrap(err, errors.AlreadyExists, "user already exists")
        }
        return errors.Wrap(err, errors.Internal, "failed to create user")
    }
    
    return nil
}
```

### gRPC Handler 层

```go
func (h *userHandler) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.GetUserResponse, error) {
    user, err := h.service.GetUser(ctx, req.Id)
    if err != nil {
        // 自动转换为 gRPC 错误
        return nil, errors.ToGRPCError(err)
    }
    
    return &pb.GetUserResponse{User: user}, nil
}
```

### gRPC 客户端

```go
func (c *userClient) GetUser(ctx context.Context, id string) (*User, error) {
    resp, err := c.grpcClient.GetUser(ctx, &pb.GetUserRequest{Id: id})
    if err != nil {
        // 从 gRPC 错误恢复 AppError
        appErr := errors.FromGRPCError(err)
        if errors.IsCode(appErr, errors.NotFound) {
            return nil, fmt.Errorf("user %s not found", id)
        }
        return nil, fmt.Errorf("failed to get user: %w", appErr)
    }
    
    return resp.User, nil
}
```

## gRPC 错误拦截器

建议在 gRPC 服务中使用统一的错误处理拦截器：

```go
func ErrorInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
    resp, err := handler(ctx, req)
    if err != nil {
        // 统一转换错误
        return nil, errors.ToGRPCError(err)
    }
    return resp, nil
}

// 在服务启动时注册
server := grpc.NewServer(
    grpc.UnaryInterceptor(ErrorInterceptor),
)
```

## 错误格式化

`AppError` 支持多种格式化选项：

```go
err := errors.New(errors.InvalidArgument, "validation failed")
err = errors.WithMeta(err, "field", "email")

// 简单格式
fmt.Printf("%s", err)  // code: InvalidArgument, msg: validation failed

// 详细格式 (包含堆栈跟踪)
fmt.Printf("%+v", err)
// Output:
// Error Code: InvalidArgument
// Message: validation failed
// Metadata:
//   field: email
// [Stack trace follows...]
```

## 性能考虑

### 堆栈跟踪开销

- 堆栈跟踪的捕获有一定性能开销
- 生产环境中可考虑只在 `Internal` 错误级别启用
- 堆栈跟踪主要用于调试，不应暴露给最终用户

### 错误创建性能

```go
// 基准测试结果 (示例)
BenchmarkNew-8          500000    2000 ns/op
BenchmarkWrap-8         300000    2500 ns/op
BenchmarkToGRPCStatus-8 200000    3000 ns/op
```

## 最佳实践

### 1. 错误码选择

- 使用最具体的错误码
- `InvalidArgument` 用于参数验证错误
- `NotFound` 用于资源不存在
- `Internal` 用于未知的系统错误

### 2. 错误消息

- 消息应面向开发者，包含足够的调试信息
- 不要在消息中包含敏感信息
- 用户友好的消息应由前端根据错误码生成

### 3. 元数据使用

- 使用元数据传递结构化的错误上下文
- 常用字段：`field`（验证失败的字段）、`resource_id`（相关资源ID）
- 避免在元数据中存储大量数据

### 4. 错误包装

- 在每个层级包装错误，添加相应的上下文
- 保持错误链的完整性
- 使用 `Wrap` 而不是创建新错误

### 5. gRPC 集成

- 在 gRPC Handler 中使用 `ToGRPCError` 转换错误
- 在客户端使用 `FromGRPCError` 恢复错误信息
- 使用统一的错误处理拦截器

## API 参考

### 错误创建函数

- `New(code ErrorCode, message string) *AppError`
- `Newf(code ErrorCode, format string, args ...interface{}) *AppError`
- `Wrap(cause error, code ErrorCode, message string) *AppError`
- `Wrapf(cause error, code ErrorCode, format string, args ...interface{}) *AppError`

### 元数据函数

- `WithMeta(err error, key, value string) error`
- `WithMetadata(err error, metadata map[string]string) error`

### 检查函数

- `IsCode(err error, code ErrorCode) bool`
- `GetCode(err error) ErrorCode`
- `GetMetadata(err error) map[string]string`
- `GetMessage(err error) string`
- `GetCause(err error) error`

### gRPC 转换函数

- `ToGRPCStatus(err error) *status.Status`
- `ToGRPCError(err error) error`
- `FromGRPCError(err error) *AppError`
- `IsGRPCError(err error) bool`
- `GetGRPCCode(err error) codes.Code`

### 错误码函数

- `(c ErrorCode) ToGRPCCode() codes.Code`
- `FromGRPCCode(code codes.Code) ErrorCode`
- `(c ErrorCode) String() string`
- `(c ErrorCode) IsValid() bool`

## 测试

运行单元测试：

```bash
cd pkg/errors
go test -v
```

运行基准测试：

```bash
go test -bench=.
```

## 依赖

- `google.golang.org/grpc`
- `google.golang.org/protobuf`
- `github.com/pkg/errors`

## 许可证

Copyright (c) 2025 Cina.Club, All rights reserved. 