/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.core.common

/**
 * Application-wide constants used across all modules.
 */
object Constants {
    
    // API Configuration
    const val BASE_URL = "https://api.cina.club"
    const val GRPC_HOST = "grpc.cina.club"
    const val GRPC_PORT = 443
    const val WS_URL = "wss://ws.cina.club"
    
    // Database
    const val DATABASE_NAME = "cina_club_db"
    const val DATABASE_VERSION = 1
    
    // Encryption
    const val KEY_ALIAS = "cina_club_master_key"
    const val KEYSTORE_PROVIDER = "AndroidKeyStore"
    const val ENCRYPTION_ALGORITHM = "AES/GCM/NoPadding"
    const val KEY_SIZE = 256
    
    // Data Preferences
    const val PREFERENCES_NAME = "cina_club_prefs"
    const val ENCRYPTED_PREFS_NAME = "cina_club_encrypted_prefs"
    
    // Network
    const val CONNECTION_TIMEOUT = 30_000L // 30 seconds
    const val READ_TIMEOUT = 30_000L // 30 seconds
    const val WRITE_TIMEOUT = 30_000L // 30 seconds
    
    // Auth
    const val TOKEN_REFRESH_THRESHOLD = 300_000L // 5 minutes before expiry
    const val MAX_RETRY_ATTEMPTS = 3
    
    // Sync
    const val SYNC_INTERVAL_MINUTES = 15L
    const val BACKGROUND_SYNC_WORK_NAME = "background_sync"
    
    // Media
    const val MAX_UPLOAD_SIZE_MB = 100L
    const val IMAGE_QUALITY = 85
    const val VIDEO_QUALITY_720P = 720
    const val VIDEO_QUALITY_1080P = 1080
    
    // Chat
    const val MESSAGE_PAGE_SIZE = 50
    const val MAX_MESSAGE_LENGTH = 2000
    
    // PKB (Personal Knowledge Base)
    const val PKB_ITEM_PAGE_SIZE = 20
    const val MAX_PKB_ITEM_SIZE_MB = 50L
    
    // Live Streaming
    const val RTMP_CONNECTION_TIMEOUT = 5000L
    const val MAX_VIEWERS_COUNT = 10000
    
    // AI Assistant
    const val MAX_PROMPT_LENGTH = 4000
    const val AI_RESPONSE_TIMEOUT = 60_000L // 60 seconds
    
    // Deep Links
    const val DEEP_LINK_SCHEME = "cinaclub"
    const val DEEP_LINK_HOST = "app.cina.club"
    
    // File Types
    val SUPPORTED_IMAGE_TYPES = listOf("jpg", "jpeg", "png", "gif", "webp")
    val SUPPORTED_VIDEO_TYPES = listOf("mp4", "avi", "mov", "mkv")
    val SUPPORTED_DOCUMENT_TYPES = listOf("pdf", "doc", "docx", "txt", "md")
} 