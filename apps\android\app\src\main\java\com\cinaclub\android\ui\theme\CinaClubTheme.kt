/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

/**
 * CINA.CLUB brand colors and theme configuration.
 * Provides consistent visual identity across the application.
 */
object CinaClubColors {
    // Primary brand colors
    val Primary = Color(0xFF4A90E2)        // CINA.CLUB blue
    val PrimaryVariant = Color(0xFF2171B5)  // Darker blue
    val Secondary = Color(0xFF07C160)       // WeChat green accent
    val SecondaryVariant = Color(0xFF059A52) // Darker green
    
    // Background colors
    val Background = Color(0xFFF5F5F5)      // Light gray background
    val Surface = Color(0xFFFFFFFF)         // White surface
    val SurfaceVariant = Color(0xFFF0F0F0)  // Light gray variant
    
    // Text colors
    val OnPrimary = Color(0xFFFFFFFF)       // White text on primary
    val OnSecondary = Color(0xFFFFFFFF)     // White text on secondary
    val OnBackground = Color(0xFF333333)    // Dark text on background
    val OnSurface = Color(0xFF333333)       // Dark text on surface
    val OnSurfaceVariant = Color(0xFF666666) // Medium gray text
    
    // Status colors
    val Error = Color(0xFFE53935)           // Red for errors
    val ErrorVariant = Color(0xFFD32F2F)    // Darker red
    val Warning = Color(0xFFFF9800)         // Orange for warnings
    val Success = Color(0xFF4CAF50)         // Green for success
    val Info = Color(0xFF2196F3)            // Blue for info
    
    // Additional UI colors
    val Divider = Color(0xFFE0E0E0)         // Light gray dividers
    val Outline = Color(0xFFBBBBBB)         // Medium gray outlines
    val Shadow = Color(0x1A000000)          // Semi-transparent black
    
    // WeChat-inspired colors
    val WeChatGreen = Color(0xFF07C160)     // WeChat brand green
    val WeChatText = Color(0xFF888888)      // WeChat secondary text
    val WeChatBorder = Color(0xFFE0E0E0)    // WeChat border color
}

private val LightColorScheme = lightColorScheme(
    primary = CinaClubColors.Primary,
    onPrimary = CinaClubColors.OnPrimary,
    primaryContainer = CinaClubColors.PrimaryVariant,
    onPrimaryContainer = CinaClubColors.OnPrimary,
    
    secondary = CinaClubColors.Secondary,
    onSecondary = CinaClubColors.OnSecondary,
    secondaryContainer = CinaClubColors.SecondaryVariant,
    onSecondaryContainer = CinaClubColors.OnSecondary,
    
    tertiary = CinaClubColors.Warning,
    onTertiary = Color.White,
    
    error = CinaClubColors.Error,
    onError = Color.White,
    
    background = CinaClubColors.Background,
    onBackground = CinaClubColors.OnBackground,
    
    surface = CinaClubColors.Surface,
    onSurface = CinaClubColors.OnSurface,
    surfaceVariant = CinaClubColors.SurfaceVariant,
    onSurfaceVariant = CinaClubColors.OnSurfaceVariant,
    
    outline = CinaClubColors.Outline,
    outlineVariant = CinaClubColors.Divider
)

private val DarkColorScheme = darkColorScheme(
    primary = CinaClubColors.Primary,
    onPrimary = CinaClubColors.OnPrimary,
    primaryContainer = CinaClubColors.PrimaryVariant,
    onPrimaryContainer = CinaClubColors.OnPrimary,
    
    secondary = CinaClubColors.Secondary,
    onSecondary = CinaClubColors.OnSecondary,
    secondaryContainer = CinaClubColors.SecondaryVariant,
    onSecondaryContainer = CinaClubColors.OnSecondary,
    
    tertiary = CinaClubColors.Warning,
    onTertiary = Color.White,
    
    error = CinaClubColors.Error,
    onError = Color.White,
    
    background = Color(0xFF1A1A1A),
    onBackground = Color(0xFFE0E0E0),
    
    surface = Color(0xFF2A2A2A),
    onSurface = Color(0xFFE0E0E0),
    surfaceVariant = Color(0xFF3A3A3A),
    onSurfaceVariant = Color(0xFFB0B0B0),
    
    outline = Color(0xFF555555),
    outlineVariant = Color(0xFF404040)
)

/**
 * CINA.CLUB Material 3 theme.
 * Applies brand colors and design system.
 */
@Composable
fun CinaClubTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = false, // Disabled to maintain brand consistency
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = CinaClubTypography,
        shapes = CinaClubShapes,
        content = content
    )
}

/**
 * Preview theme for development.
 */
@Composable
fun CinaClubThemePreview(
    content: @Composable () -> Unit
) {
    CinaClubTheme(
        darkTheme = false,
        content = content
    )
} 