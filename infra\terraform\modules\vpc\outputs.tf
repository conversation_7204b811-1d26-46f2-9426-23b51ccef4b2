# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB VPC 模块输出变量

# VPC 信息
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "The CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_arn" {
  description = "The ARN of the VPC"
  value       = aws_vpc.main.arn
}

# Internet Gateway
output "internet_gateway_id" {
  description = "The ID of the Internet Gateway"
  value       = aws_internet_gateway.main.id
}

# 子网信息
output "public_subnet_ids" {
  description = "List of IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "public_subnet_arns" {
  description = "List of ARNs of the public subnets"
  value       = aws_subnet.public[*].arn
}

output "public_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the public subnets"
  value       = aws_subnet.public[*].cidr_block
}

output "private_subnet_ids" {
  description = "List of IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "private_subnet_arns" {
  description = "List of ARNs of the private subnets"
  value       = aws_subnet.private[*].arn
}

output "private_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the private subnets"
  value       = aws_subnet.private[*].cidr_block
}

output "database_subnet_ids" {
  description = "List of IDs of the database subnets"
  value       = aws_subnet.database[*].id
}

output "database_subnet_arns" {
  description = "List of ARNs of the database subnets"
  value       = aws_subnet.database[*].arn
}

output "database_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the database subnets"
  value       = aws_subnet.database[*].cidr_block
}

# 可用区信息
output "availability_zones" {
  description = "List of availability zones"
  value       = data.aws_availability_zones.available.names
}

# NAT Gateway 信息
output "nat_gateway_ids" {
  description = "List of IDs of the NAT Gateways"
  value       = aws_nat_gateway.main[*].id
}

output "nat_gateway_public_ips" {
  description = "List of public Elastic IPs associated with the NAT Gateways"
  value       = aws_eip.nat[*].public_ip
}

# 路由表信息
output "public_route_table_id" {
  description = "ID of the public route table"
  value       = aws_route_table.public.id
}

output "private_route_table_ids" {
  description = "List of IDs of the private route tables"
  value       = aws_route_table.private[*].id
}

output "database_route_table_id" {
  description = "ID of the database route table"
  value       = aws_route_table.database.id
}

# 子网组信息
output "database_subnet_group_name" {
  description = "Name of the database subnet group"
  value       = try(aws_db_subnet_group.main[0].name, null)
}

output "database_subnet_group_id" {
  description = "ID of the database subnet group"
  value       = try(aws_db_subnet_group.main[0].id, null)
}

output "elasticache_subnet_group_name" {
  description = "Name of the ElastiCache subnet group"
  value       = try(aws_elasticache_subnet_group.main[0].name, null)
}

# VPC Flow Logs
output "vpc_flow_log_id" {
  description = "ID of the VPC Flow Log"
  value       = try(aws_flow_log.main[0].id, null)
}

output "vpc_flow_log_cloudwatch_log_group" {
  description = "CloudWatch Log Group name for VPC Flow Logs"
  value       = try(aws_cloudwatch_log_group.flow_log[0].name, null)
}

# 网络配置总结（便于其他模块使用）
output "network_config" {
  description = "Network configuration summary"
  value = {
    vpc_id                = aws_vpc.main.id
    vpc_cidr             = aws_vpc.main.cidr_block
    public_subnet_ids    = aws_subnet.public[*].id
    private_subnet_ids   = aws_subnet.private[*].id
    database_subnet_ids  = aws_subnet.database[*].id
    availability_zones   = data.aws_availability_zones.available.names
    nat_gateway_count    = length(aws_nat_gateway.main)
    internet_gateway_id  = aws_internet_gateway.main.id
  }
}

# 安全组默认 ID（用于后续安全组规则）
output "default_security_group_id" {
  description = "ID of the default security group"
  value       = aws_vpc.main.default_security_group_id
}

# DHCP 选项集
output "dhcp_options_id" {
  description = "The ID of the DHCP options"
  value       = aws_vpc.main.dhcp_options_id
}

# VPC 主路由表
output "main_route_table_id" {
  description = "The ID of the main route table"
  value       = aws_vpc.main.main_route_table_id
} 