/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package model

import "errors"

// Domain error definitions for admin BFF service

// Session errors
var (
	ErrSessionNotFound      = errors.New("session not found")
	ErrSessionExpired       = errors.New("session has expired")
	ErrSessionInvalid       = errors.New("session is invalid")
	ErrSessionAlreadyExists = errors.New("session already exists")
	ErrSessionCreateFailed  = errors.New("failed to create session")
	ErrSessionUpdateFailed  = errors.New("failed to update session")
	ErrSessionDeleteFailed  = errors.New("failed to delete session")
)

// Employee errors
var (
	ErrEmployeeNotFound     = errors.New("employee not found")
	ErrEmployeeInactive     = errors.New("employee is inactive")
	ErrEmployeeUnauthorized = errors.New("employee is unauthorized")
	ErrInvalidEmployeeID    = errors.New("invalid employee ID")
	ErrInvalidEmail         = errors.New("invalid email address")
)

// Authentication errors
var (
	ErrAuthenticationFailed = errors.New("authentication failed")
	ErrInvalidCredentials   = errors.New("invalid credentials")
	ErrAccountLocked        = errors.New("account is locked")
	ErrAccountSuspended     = errors.New("account is suspended")
	ErrInsufficientRoles    = errors.New("insufficient roles for operation")
)

// Audit log errors
var (
	ErrAuditLogInvalidEntry = errors.New("invalid audit log entry")
	ErrAuditLogStoreFailed  = errors.New("failed to store audit log")
)

// Validation errors
var (
	ErrInvalidUserID     = errors.New("invalid user ID")
	ErrInvalidOrderID    = errors.New("invalid order ID")
	ErrInvalidContentID  = errors.New("invalid content ID")
	ErrInvalidFilter     = errors.New("invalid filter parameters")
	ErrInvalidTimeRange  = errors.New("invalid time range")
	ErrInvalidPagination = errors.New("invalid pagination parameters")
	ErrMissingParameter  = errors.New("missing required parameter")
)

// Business logic errors
var (
	ErrUserAlreadySuspended   = errors.New("user is already suspended")
	ErrUserNotSuspended       = errors.New("user is not suspended")
	ErrContentAlreadyApproved = errors.New("content is already approved")
	ErrContentAlreadyRejected = errors.New("content is already rejected")
	ErrOrderCannotBeCancelled = errors.New("order cannot be cancelled")
	ErrOrderCannotBeRefunded  = errors.New("order cannot be refunded")
)

// External service errors
var (
	ErrUserServiceUnavailable         = errors.New("user service is unavailable")
	ErrBillingServiceUnavailable      = errors.New("billing service is unavailable")
	ErrSocialServiceUnavailable       = errors.New("social service is unavailable")
	ErrContentServiceUnavailable      = errors.New("content service is unavailable")
	ErrAnalyticsServiceUnavailable    = errors.New("analytics service is unavailable")
	ErrNotificationServiceUnavailable = errors.New("notification service is unavailable")
)

// System errors
var (
	ErrInternalServerError = errors.New("internal server error")
	ErrServiceUnavailable  = errors.New("service is unavailable")
	ErrRateLimitExceeded   = errors.New("rate limit exceeded")
	ErrRequestTimeout      = errors.New("request timeout")
	ErrDatabaseError       = errors.New("database error")
	ErrCacheError          = errors.New("cache error")
)

// Error types for structured error handling

// ValidationError represents a validation error with field information
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

func (e ValidationError) Error() string {
	return e.Message
}

// BusinessError represents a business logic error
type BusinessError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e BusinessError) Error() string {
	return e.Message
}

// ServiceError represents an external service error
type ServiceError struct {
	Service string `json:"service"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Retry   bool   `json:"retry"`
}

func (e ServiceError) Error() string {
	return e.Message
}

// Error code constants
const (
	// Validation error codes
	CodeInvalidInput    = "INVALID_INPUT"
	CodeMissingField    = "MISSING_FIELD"
	CodeInvalidFormat   = "INVALID_FORMAT"
	CodeValueOutOfRange = "VALUE_OUT_OF_RANGE"

	// Authentication error codes
	CodeUnauthorized   = "UNAUTHORIZED"
	CodeForbidden      = "FORBIDDEN"
	CodeSessionExpired = "SESSION_EXPIRED"
	CodeInvalidSession = "INVALID_SESSION"

	// Business logic error codes
	CodeResourceNotFound      = "RESOURCE_NOT_FOUND"
	CodeResourceConflict      = "RESOURCE_CONFLICT"
	CodeOperationNotAllowed   = "OPERATION_NOT_ALLOWED"
	CodeBusinessRuleViolation = "BUSINESS_RULE_VIOLATION"

	// System error codes
	CodeInternalError      = "INTERNAL_ERROR"
	CodeServiceUnavailable = "SERVICE_UNAVAILABLE"
	CodeTimeout            = "TIMEOUT"
	CodeRateLimited        = "RATE_LIMITED"
)

// NewValidationError creates a new validation error
func NewValidationError(field, message, code string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
		Code:    code,
	}
}

// NewBusinessError creates a new business error
func NewBusinessError(code, message, details string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewServiceError creates a new service error
func NewServiceError(service, code, message string, retry bool) *ServiceError {
	return &ServiceError{
		Service: service,
		Code:    code,
		Message: message,
		Retry:   retry,
	}
}

// IsRetryableError checks if an error is retryable
func IsRetryableError(err error) bool {
	switch e := err.(type) {
	case *ServiceError:
		return e.Retry
	case ServiceError:
		return e.Retry
	default:
		// Check for common retryable errors
		return err == ErrServiceUnavailable ||
			err == ErrRequestTimeout ||
			err == ErrDatabaseError ||
			err == ErrCacheError
	}
}

// GetErrorCode extracts error code from an error
func GetErrorCode(err error) string {
	switch e := err.(type) {
	case *ValidationError:
		return e.Code
	case ValidationError:
		return e.Code
	case *BusinessError:
		return e.Code
	case BusinessError:
		return e.Code
	case *ServiceError:
		return e.Code
	case ServiceError:
		return e.Code
	default:
		return CodeInternalError
	}
}
