/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 12:00:00
Modified: 2025-01-21 12:00:00
*/

package interceptor

import (
	"context"
	"testing"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Mock handler for testing
func mockHandler(ctx context.Context, req interface{}) (interface{}, error) {
	return "success", nil
}

func TestChainBuilder_Empty(t *testing.T) {
	builder := NewChainBuilder()

	unaryInterceptor := builder.BuildUnary()
	if unaryInterceptor != nil {
		t.Error("Expected nil unary interceptor for empty chain")
	}

	streamInterceptor := builder.BuildStream()
	if streamInterceptor != nil {
		t.Error("Expected nil stream interceptor for empty chain")
	}

	opts := builder.BuildServerOptions()
	if len(opts) != 0 {
		t.<PERSON><PERSON>("Expected empty server options, got %d options", len(opts))
	}
}

func TestChainBuilder_SingleInterceptor(t *testing.T) {
	// Create a simple test interceptor
	testInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		return handler(ctx, req)
	}

	builder := NewChainBuilder()
	builder.AddCustomUnary(testInterceptor)

	unaryInterceptor := builder.BuildUnary()
	if unaryInterceptor == nil {
		t.Error("Expected unary interceptor for single interceptor chain")
	}

	// Test the interceptor
	info := &grpc.UnaryServerInfo{FullMethod: "/test.Service/TestMethod"}
	result, err := unaryInterceptor(context.Background(), "test", info, mockHandler)
	if err != nil {
		t.Errorf("Interceptor failed: %v", err)
	}
	if result != "success" {
		t.Errorf("Expected 'success', got %v", result)
	}
}

func TestChainBuilder_MultipleInterceptors(t *testing.T) {
	order := []string{}

	// Create interceptors that record execution order
	interceptor1 := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		order = append(order, "interceptor1")
		return handler(ctx, req)
	}

	interceptor2 := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		order = append(order, "interceptor2")
		return handler(ctx, req)
	}

	builder := NewChainBuilder()
	builder.AddCustomUnary(interceptor1)
	builder.AddCustomUnary(interceptor2)

	unaryInterceptor := builder.BuildUnary()
	if unaryInterceptor == nil {
		t.Error("Expected unary interceptor for multi-interceptor chain")
	}

	// Test the interceptor chain
	info := &grpc.UnaryServerInfo{FullMethod: "/test.Service/TestMethod"}
	_, err := unaryInterceptor(context.Background(), "test", info, mockHandler)
	if err != nil {
		t.Errorf("Interceptor chain failed: %v", err)
	}

	// Verify execution order (should be first added, first executed)
	if len(order) != 2 {
		t.Errorf("Expected 2 interceptors executed, got %d", len(order))
	}
	if order[0] != "interceptor1" {
		t.Errorf("Expected first interceptor to be 'interceptor1', got %s", order[0])
	}
	if order[1] != "interceptor2" {
		t.Errorf("Expected second interceptor to be 'interceptor2', got %s", order[1])
	}
}

func TestChainBuilder_ErrorPropagation(t *testing.T) {
	// Create an interceptor that returns an error
	errorInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		return nil, status.Error(codes.PermissionDenied, "test error")
	}

	builder := NewChainBuilder()
	builder.AddCustomUnary(errorInterceptor)

	unaryInterceptor := builder.BuildUnary()

	// Test that error is propagated
	info := &grpc.UnaryServerInfo{FullMethod: "/test.Service/TestMethod"}
	_, err := unaryInterceptor(context.Background(), "test", info, mockHandler)

	if err == nil {
		t.Error("Expected error but got none")
	}

	if st, ok := status.FromError(err); ok {
		if st.Code() != codes.PermissionDenied {
			t.Errorf("Expected PermissionDenied, got %v", st.Code())
		}
		if st.Message() != "test error" {
			t.Errorf("Expected 'test error', got %s", st.Message())
		}
	} else {
		t.Error("Expected gRPC status error")
	}
}

func TestCommonChains_CreatesValidChains(t *testing.T) {
	// We can't test the actual functionality without real interceptors,
	// but we can test that the chain builders are created correctly

	commonChains := CommonChains{}

	// These will panic if the methods don't exist or have wrong signatures
	// We pass nil since we're just testing the chain structure
	apiChain := commonChains.APIGateway(nil, nil)
	if apiChain == nil {
		t.Error("APIGateway returned nil chain")
	}

	internalChain := commonChains.InternalService(nil)
	if internalChain == nil {
		t.Error("InternalService returned nil chain")
	}

	mixedChain := commonChains.MixedService(nil, nil, nil)
	if mixedChain == nil {
		t.Error("MixedService returned nil chain")
	}

	adminChain := commonChains.AdminService(nil, nil)
	if adminChain == nil {
		t.Error("AdminService returned nil chain")
	}
}

// Benchmark tests
func BenchmarkChainBuilder_SingleInterceptor(b *testing.B) {
	testInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		return handler(ctx, req)
	}

	info := &grpc.UnaryServerInfo{FullMethod: "/test.Service/TestMethod"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		builder := NewChainBuilder()
		builder.AddCustomUnary(testInterceptor)
		interceptor := builder.BuildUnary()
		_, _ = interceptor(context.Background(), "test", info, mockHandler)
	}
}

func BenchmarkChainBuilder_MultipleInterceptors(b *testing.B) {
	interceptor1 := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		return handler(ctx, req)
	}

	interceptor2 := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		return handler(ctx, req)
	}

	interceptor3 := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		return handler(ctx, req)
	}

	info := &grpc.UnaryServerInfo{FullMethod: "/test.Service/TestMethod"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		builder := NewChainBuilder()
		builder.AddCustomUnary(interceptor1)
		builder.AddCustomUnary(interceptor2)
		builder.AddCustomUnary(interceptor3)
		interceptor := builder.BuildUnary()
		_, _ = interceptor(context.Background(), "test", info, mockHandler)
	}
}
