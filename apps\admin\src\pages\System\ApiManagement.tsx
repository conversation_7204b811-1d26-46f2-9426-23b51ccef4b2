/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:10:00
 * Modified: 2025-01-23 16:10:00
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Progress,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Tooltip,
  Badge,
  Tabs,
  List,
  Alert,
  Switch,
  InputNumber,
  Descriptions,
} from 'antd';
import {
  ApiOutlined,
  KeyOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FireOutlined,
  ShieldOutlined,
  BugOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Line, Column } from '@ant-design/plots';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

interface ApiKey {
  id: string;
  name: string;
  key: string;
  description: string;
  status: 'active' | 'inactive' | 'revoked';
  permissions: string[];
  rateLimit: {
    requests: number;
    window: string;
  };
  usage: {
    totalRequests: number;
    todayRequests: number;
    lastUsed: string;
  };
  createdAt: string;
  expiresAt?: string;
  createdBy: string;
}

interface ApiEndpoint {
  path: string;
  method: string;
  description: string;
  status: 'healthy' | 'degraded' | 'down';
  responseTime: number;
  successRate: number;
  requestCount: number;
  errorCount: number;
  lastChecked: string;
}

interface ApiStats {
  totalRequests: number;
  todayRequests: number;
  averageResponseTime: number;
  errorRate: number;
  activeKeys: number;
  topEndpoints: Array<{ endpoint: string; count: number }>;
  requestsByHour: Array<{ hour: string; requests: number }>;
}

const ApiManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isApiKeyModalVisible, setIsApiKeyModalVisible] = useState(false);
  const [selectedApiKey, setSelectedApiKey] = useState<ApiKey | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // Mock data queries
  const { data: apiStats } = useQuery({
    queryKey: ['api', 'stats'],
    queryFn: async (): Promise<ApiStats> => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        totalRequests: 1234567,
        todayRequests: 8934,
        averageResponseTime: 245,
        errorRate: 2.3,
        activeKeys: 12,
        topEndpoints: [
          { endpoint: '/api/v1/users', count: 234567 },
          { endpoint: '/api/v1/content', count: 189432 },
          { endpoint: '/api/v1/analytics', count: 156789 },
          { endpoint: '/api/v1/auth', count: 98765 },
          { endpoint: '/api/v1/services', count: 67890 }
        ],
        requestsByHour: Array.from({ length: 24 }, (_, i) => ({
          hour: `${i}:00`,
          requests: Math.floor(Math.random() * 1000) + 200
        }))
      };
    }
  });

  const { data: apiKeys = [], isLoading: keysLoading } = useQuery({
    queryKey: ['api', 'keys'],
    queryFn: async (): Promise<ApiKey[]> => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return [
        {
          id: '1',
          name: 'Admin Dashboard',
          key: 'ak_live_1234567890abcdef',
          description: 'API key for the admin dashboard application',
          status: 'active',
          permissions: ['users:read', 'users:write', 'content:read', 'analytics:read'],
          rateLimit: { requests: 10000, window: 'hour' },
          usage: {
            totalRequests: 456789,
            todayRequests: 1234,
            lastUsed: '2025-01-23T15:30:00Z'
          },
          createdAt: '2025-01-20T10:00:00Z',
          createdBy: '<EMAIL>'
        },
        {
          id: '2',
          name: 'Mobile App',
          key: 'ak_live_abcdef1234567890',
          description: 'API key for mobile applications',
          status: 'active',
          permissions: ['users:read', 'content:read'],
          rateLimit: { requests: 5000, window: 'hour' },
          usage: {
            totalRequests: 234567,
            todayRequests: 567,
            lastUsed: '2025-01-23T15:25:00Z'
          },
          createdAt: '2025-01-18T14:00:00Z',
          createdBy: '<EMAIL>'
        },
        {
          id: '3',
          name: 'Analytics Service',
          key: 'ak_live_fedcba0987654321',
          description: 'API key for analytics and reporting services',
          status: 'inactive',
          permissions: ['analytics:read', 'analytics:write'],
          rateLimit: { requests: 2000, window: 'hour' },
          usage: {
            totalRequests: 123456,
            todayRequests: 0,
            lastUsed: '2025-01-22T09:15:00Z'
          },
          createdAt: '2025-01-15T08:00:00Z',
          expiresAt: '2025-02-15T08:00:00Z',
          createdBy: '<EMAIL>'
        }
      ];
    }
  });

  const { data: apiEndpoints = [], isLoading: endpointsLoading } = useQuery({
    queryKey: ['api', 'endpoints'],
    queryFn: async (): Promise<ApiEndpoint[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return [
        {
          path: '/api/v1/users',
          method: 'GET',
          description: 'Get users list',
          status: 'healthy',
          responseTime: 145,
          successRate: 99.8,
          requestCount: 23456,
          errorCount: 47,
          lastChecked: '2025-01-23T15:30:00Z'
        },
        {
          path: '/api/v1/auth/login',
          method: 'POST',
          description: 'User authentication',
          status: 'healthy',
          responseTime: 234,
          successRate: 98.5,
          requestCount: 12345,
          errorCount: 185,
          lastChecked: '2025-01-23T15:30:00Z'
        },
        {
          path: '/api/v1/content',
          method: 'GET',
          description: 'Get content list',
          status: 'degraded',
          responseTime: 567,
          successRate: 95.2,
          requestCount: 18765,
          errorCount: 899,
          lastChecked: '2025-01-23T15:29:00Z'
        },
        {
          path: '/api/v1/analytics/dashboard',
          method: 'GET',
          description: 'Dashboard analytics data',
          status: 'healthy',
          responseTime: 189,
          successRate: 99.9,
          requestCount: 8765,
          errorCount: 9,
          lastChecked: '2025-01-23T15:30:00Z'
        }
      ];
    }
  });

  // Mutations
  const createApiKeyMutation = useMutation({
    mutationFn: async (data: any) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { ...data, id: Date.now().toString(), key: `ak_live_${Math.random().toString(36).substr(2, 16)}` };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api', 'keys'] });
      setIsApiKeyModalVisible(false);
      form.resetFields();
    }
  });

  const updateApiKeyMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return { id, ...data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api', 'keys'] });
      setIsApiKeyModalVisible(false);
      setSelectedApiKey(null);
    }
  });

  const revokeApiKeyMutation = useMutation({
    mutationFn: async (id: string) => {
      await new Promise(resolve => setTimeout(resolve, 500));
      return { id, status: 'revoked' };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api', 'keys'] });
    }
  });

  const handleCreateApiKey = () => {
    setSelectedApiKey(null);
    form.resetFields();
    setIsApiKeyModalVisible(true);
  };

  const handleEditApiKey = (apiKey: ApiKey) => {
    setSelectedApiKey(apiKey);
    form.setFieldsValue(apiKey);
    setIsApiKeyModalVisible(true);
  };

  const handleRevokeApiKey = (apiKey: ApiKey) => {
    Modal.confirm({
      title: 'Revoke API Key',
      content: `Are you sure you want to revoke the API key "${apiKey.name}"? This action cannot be undone.`,
      onOk: () => revokeApiKeyMutation.mutate(apiKey.id)
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'healthy': return 'green';
      case 'inactive': case 'degraded': return 'orange';
      case 'revoked': case 'down': return 'red';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': case 'healthy': return <CheckCircleOutlined />;
      case 'inactive': case 'degraded': return <WarningOutlined />;
      case 'revoked': case 'down': return <BugOutlined />;
      default: return <ClockCircleOutlined />;
    }
  };

  const apiKeyColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: ApiKey) => (
        <div>
          <Text strong>{name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
      render: (key: string) => (
        <Text code>{key.slice(0, 20)}...</Text>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge 
          status={status === 'active' ? 'success' : status === 'inactive' ? 'warning' : 'error'}
          text={status.toUpperCase()}
        />
      )
    },
    {
      title: 'Usage Today',
      key: 'usage',
      render: (_, record: ApiKey) => (
        <div>
          <Text>{record.usage.todayRequests.toLocaleString()}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.rateLimit.requests.toLocaleString()}/{record.rateLimit.window} limit
          </Text>
        </div>
      )
    },
    {
      title: 'Last Used',
      dataIndex: ['usage', 'lastUsed'],
      key: 'lastUsed',
      render: (lastUsed: string) => (
        <div>
          <div>{new Date(lastUsed).toLocaleDateString()}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {new Date(lastUsed).toLocaleTimeString()}
          </Text>
        </div>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: ApiKey) => (
        <Space>
          <Tooltip title="Edit">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              onClick={() => handleEditApiKey(record)}
            />
          </Tooltip>
          <Tooltip title="Revoke">
            <Button 
              type="link" 
              danger
              icon={<DeleteOutlined />} 
              onClick={() => handleRevokeApiKey(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  const endpointColumns = [
    {
      title: 'Endpoint',
      key: 'endpoint',
      render: (_, record: ApiEndpoint) => (
        <div>
          <Space>
            <Tag color="blue">{record.method}</Tag>
            <Text code>{record.path}</Text>
          </Space>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Space>
          {getStatusIcon(status)}
          <Tag color={getStatusColor(status)}>
            {status.toUpperCase()}
          </Tag>
        </Space>
      )
    },
    {
      title: 'Response Time',
      dataIndex: 'responseTime',
      key: 'responseTime',
      render: (time: number) => (
        <Text>{time}ms</Text>
      )
    },
    {
      title: 'Success Rate',
      dataIndex: 'successRate',
      key: 'successRate',
      render: (rate: number) => (
        <div>
          <Progress 
            percent={rate} 
            size="small" 
            status={rate >= 99 ? 'success' : rate >= 95 ? 'normal' : 'exception'}
            format={() => `${rate}%`}
          />
        </div>
      )
    },
    {
      title: 'Requests',
      dataIndex: 'requestCount',
      key: 'requestCount',
      render: (count: number, record: ApiEndpoint) => (
        <div>
          <Text>{count.toLocaleString()}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.errorCount} errors
          </Text>
        </div>
      )
    }
  ];

  const requestsChartConfig = {
    data: apiStats?.requestsByHour || [],
    xField: 'hour',
    yField: 'requests',
    smooth: true,
    point: {
      size: 3,
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Requests',
        value: datum.requests.toLocaleString(),
      }),
    },
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <ApiOutlined /> API Management
        </Title>
        <Paragraph type="secondary">
          Monitor API usage, manage API keys, and configure rate limits.
        </Paragraph>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              Overview
            </span>
          }
          key="overview"
        >
          {/* Statistics Cards */}
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Total Requests"
                  value={apiStats?.totalRequests || 0}
                  prefix={<ApiOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Today's Requests"
                  value={apiStats?.todayRequests || 0}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Avg Response Time"
                  value={apiStats?.averageResponseTime || 0}
                  suffix="ms"
                  prefix={<FireOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Error Rate"
                  value={apiStats?.errorRate || 0}
                  suffix="%"
                  prefix={<WarningOutlined />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
          </Row>

          {/* Charts */}
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={16}>
              <Card title="Requests by Hour (Last 24h)">
                <Line {...requestsChartConfig} height={300} />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Top Endpoints">
                <List
                  dataSource={apiStats?.topEndpoints || []}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        title={<Text code>{item.endpoint}</Text>}
                        description={`${item.count.toLocaleString()} requests`}
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane
          tab={
            <span>
              <KeyOutlined />
              API Keys
            </span>
          }
          key="keys"
        >
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div>
                <Title level={4}>API Keys</Title>
                <Paragraph type="secondary">
                  Manage API keys and their permissions.
                </Paragraph>
              </div>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleCreateApiKey}
              >
                Create API Key
              </Button>
            </div>

            <Table
              columns={apiKeyColumns}
              dataSource={apiKeys}
              loading={keysLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <SettingOutlined />
              Endpoints
            </span>
          }
          key="endpoints"
        >
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div>
                <Title level={4}>API Endpoints</Title>
                <Paragraph type="secondary">
                  Monitor endpoint health and performance.
                </Paragraph>
              </div>
              <Button icon={<ReloadOutlined />}>
                Refresh Status
              </Button>
            </div>

            <Table
              columns={endpointColumns}
              dataSource={apiEndpoints}
              loading={endpointsLoading}
              rowKey="path"
              pagination={{ pageSize: 15 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* API Key Modal */}
      <Modal
        title={selectedApiKey ? 'Edit API Key' : 'Create API Key'}
        open={isApiKeyModalVisible}
        onCancel={() => {
          setIsApiKeyModalVisible(false);
          setSelectedApiKey(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => {
            if (selectedApiKey) {
              updateApiKeyMutation.mutate({ id: selectedApiKey.id, ...values });
            } else {
              createApiKeyMutation.mutate(values);
            }
          }}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please enter a name' }]}
          >
            <Input placeholder="Enter API key name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter a description' }]}
          >
            <TextArea 
              placeholder="Describe the purpose of this API key"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="permissions"
            label="Permissions"
            rules={[{ required: true, message: 'Please select permissions' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select permissions"
              options={[
                { label: 'Users: Read', value: 'users:read' },
                { label: 'Users: Write', value: 'users:write' },
                { label: 'Content: Read', value: 'content:read' },
                { label: 'Content: Write', value: 'content:write' },
                { label: 'Analytics: Read', value: 'analytics:read' },
                { label: 'Analytics: Write', value: 'analytics:write' },
                { label: 'System: Read', value: 'system:read' },
                { label: 'System: Write', value: 'system:write' },
              ]}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['rateLimit', 'requests']}
                label="Rate Limit (Requests)"
                rules={[{ required: true, message: 'Please set rate limit' }]}
              >
                <InputNumber 
                  placeholder="1000"
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rateLimit', 'window']}
                label="Time Window"
                rules={[{ required: true, message: 'Please select time window' }]}
              >
                <Select placeholder="Select window">
                  <Option value="minute">Per Minute</Option>
                  <Option value="hour">Per Hour</Option>
                  <Option value="day">Per Day</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="expiresAt" label="Expiration Date (Optional)">
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={createApiKeyMutation.isPending || updateApiKeyMutation.isPending}
              >
                {selectedApiKey ? 'Update' : 'Create'} API Key
              </Button>
              <Button onClick={() => setIsApiKeyModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ApiManagement; 