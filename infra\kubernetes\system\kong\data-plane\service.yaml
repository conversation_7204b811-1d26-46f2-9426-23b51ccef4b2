# CINA.CLUB Platform - Kong Proxy Service (Main Platform Entry Point)
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Kong Proxy LoadBalancer Service - Main Platform Entry Point
apiVersion: v1
kind: Service
metadata:
  name: kong-proxy
  namespace: kong-system
  labels:
    app: kong-proxy
    component: data-plane
    tier: platform-infrastructure
    app.kubernetes.io/name: kong-proxy
    app.kubernetes.io/component: data-plane
    app.kubernetes.io/part-of: kong-gateway
    app.kubernetes.io/managed-by: platform-engineering
  annotations:
    description: "Kong Proxy LoadBalancer - Main entry point for CINA.CLUB platform"
    # AWS Load Balancer Controller annotations (example for AWS)
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-path: "/status"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-port: "8100"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval: "10"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-timeout: "5"
    service.beta.kubernetes.io/aws-load-balancer-healthy-threshold: "2"
    service.beta.kubernetes.io/aws-load-balancer-unhealthy-threshold: "2"
    # SSL termination at load balancer level
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:region:account:certificate/cert-id"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    # Preserve client IP
    service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
    # Connection draining
    service.beta.kubernetes.io/aws-load-balancer-connection-draining-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-connection-draining-timeout: "60"
spec:
  type: LoadBalancer
  
  # Load balancer source ranges (adjust for your security requirements)
  loadBalancerSourceRanges:
    - "0.0.0.0/0"  # Allow from anywhere (public API gateway)
  
  # Session affinity for better caching
  sessionAffinity: None  # Let Kong handle load balancing internally
  
  # External traffic policy for preserving source IP
  externalTrafficPolicy: Local  # Preserve client IP addresses
  
  selector:
    app: kong-proxy
    component: data-plane
  
  ports:
    # HTTP port (will redirect to HTTPS in production)
    - name: http
      port: 80
      targetPort: 8000
      protocol: TCP
    
    # HTTPS port (main production traffic)
    - name: https
      port: 443
      targetPort: 8443
      protocol: TCP

---
# Kong Proxy ClusterIP Service for internal communication
apiVersion: v1
kind: Service
metadata:
  name: kong-proxy-internal
  namespace: kong-system
  labels:
    app: kong-proxy
    component: data-plane
    tier: platform-infrastructure
    scope: internal
  annotations:
    description: "Kong Proxy internal service for cluster communication"
spec:
  type: ClusterIP
  
  selector:
    app: kong-proxy
    component: data-plane
  
  ports:
    # HTTP port for internal communication
    - name: proxy-http
      port: 8000
      targetPort: 8000
      protocol: TCP
    
    # HTTPS port for internal communication
    - name: proxy-https
      port: 8443
      targetPort: 8443
      protocol: TCP
    
    # Admin API port (for Kong Ingress Controller communication)
    - name: admin
      port: 8001
      targetPort: 8001
      protocol: TCP
    
    # Status/metrics port for monitoring
    - name: status
      port: 8100
      targetPort: 8100
      protocol: TCP

---
# Service Monitor for Prometheus (if Prometheus Operator is used)
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: kong-proxy-metrics
  namespace: kong-system
  labels:
    app: kong-proxy
    component: data-plane
    monitoring: prometheus
spec:
  selector:
    matchLabels:
      app: kong-proxy
      component: data-plane
  
  endpoints:
    - port: status
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
      scheme: http
      # Metric relabeling rules
      metricRelabelings:
        - sourceLabels: [__name__]
          regex: "kong_.*"
          targetLabel: service
          replacement: "kong-gateway"
        - sourceLabels: [__name__]
          regex: "nginx_.*"
          targetLabel: service
          replacement: "kong-nginx"
  
  # Namespace selector
  namespaceSelector:
    matchNames:
      - kong-system

---
# Ingress Class for Kong (defining Kong as an Ingress Controller)
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: kong
  labels:
    app: kong-ingress-controller
    component: control-plane
  annotations:
    description: "Kong Ingress Class for CINA.CLUB platform"
    ingressclass.kubernetes.io/is-default-class: "true"  # Make Kong the default Ingress controller
spec:
  controller: ingress-controllers.konghq.com/kong
  parameters:
    apiGroup: configuration.konghq.com
    kind: KongIngressController
    name: kong-ingress-controller

---
# Network Policy for Kong Proxy data plane
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kong-proxy-network-policy
  namespace: kong-system
  labels:
    app: kong-proxy
    component: data-plane
spec:
  podSelector:
    matchLabels:
      app: kong-proxy
      component: data-plane
  
  policyTypes:
    - Ingress
    - Egress
  
  # Ingress rules for Kong Proxy
  ingress:
    # Allow traffic from internet (through load balancer)
    - from: []  # Allow from any source (internet traffic)
      ports:
        - protocol: TCP
          port: 8000  # HTTP proxy port
        - protocol: TCP
          port: 8443  # HTTPS proxy port
    
    # Allow traffic from Kong Ingress Controller
    - from:
        - podSelector:
            matchLabels:
              app: kong-ingress-controller
              component: control-plane
      ports:
        - protocol: TCP
          port: 8001  # Admin API port
    
    # Allow traffic from monitoring namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8100  # Metrics port

  # Egress rules for Kong Proxy
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow communication to all application namespaces
    - to:
        - namespaceSelector:
            matchLabels:
              cina-club.com/allow-kong: "true"
    
    # Allow HTTPS to external services (for JWKS, etc.)
    - to: []
      ports:
        - protocol: TCP
          port: 443
    
    # Allow HTTP to external services
    - to: []
      ports:
        - protocol: TCP
          port: 80
    
    # Allow communication to monitoring systems
    - to:
        - namespaceSelector:
            matchLabels:
              name: monitoring
    
    # Allow communication to logging systems
    - to:
        - namespaceSelector:
            matchLabels:
              name: logging 