/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using Microsoft.Extensions.DependencyInjection;
using System;
using CinaClub.App.Services;
using CinaClub.App.Views;
using WinUIEx;

namespace CinaClub.App;

/// <summary>
/// CINA.CLUB应用程序的主窗口
/// </summary>
public sealed partial class MainWindow : WindowEx
{
    private readonly INavigationService _navigationService;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MainWindow()
    {
        this.InitializeComponent();
        
        // 获取依赖服务
        _navigationService = App.Services.GetRequiredService<INavigationService>();
        
        // 初始化窗口
        InitializeWindow();
        
        // 初始化导航
        InitializeNavigation();
    }

    /// <summary>
    /// 初始化窗口属性
    /// </summary>
    private void InitializeWindow()
    {
        // 设置窗口图标
        this.SetIcon("Assets/icon.ico");
        
        // 设置最小尺寸
        this.MinWidth = 1200;
        this.MinHeight = 800;
        
        // 居中显示
        this.CenterOnScreen();
        
        // 设置标题栏
        this.SetTitleBar(null); // 使用默认标题栏
        this.Title = "CINA.CLUB - 您的智能生活助手";
    }

    /// <summary>
    /// 初始化导航系统
    /// </summary>
    private void InitializeNavigation()
    {
        // 设置导航框架
        _navigationService.Initialize(ContentFrame);
        
        // 导航到登录页面
        _navigationService.NavigateTo<LoginPage>();
    }

    /// <summary>
    /// 处理导航失败事件
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void ContentFrame_NavigationFailed(object sender, NavigationFailedEventArgs e)
    {
        ShowNotification("导航失败", $"无法导航到页面: {e.SourcePageType.Name}", InfoBarSeverity.Error);
    }

    /// <summary>
    /// 显示通知
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息</param>
    /// <param name="severity">严重级别</param>
    public void ShowNotification(string title, string message, InfoBarSeverity severity = InfoBarSeverity.Informational)
    {
        DispatcherQueue.TryEnqueue(() =>
        {
            NotificationBar.Title = title;
            NotificationBar.Message = message;
            NotificationBar.Severity = severity;
            NotificationBar.IsOpen = true;
        });
    }

    /// <summary>
    /// 显示加载状态
    /// </summary>
    /// <param name="isLoading">是否加载中</param>
    /// <param name="message">加载消息</param>
    public void ShowLoading(bool isLoading, string message = "正在加载...")
    {
        DispatcherQueue.TryEnqueue(() =>
        {
            LoadingText.Text = message;
            LoadingOverlay.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
        });
    }
} 