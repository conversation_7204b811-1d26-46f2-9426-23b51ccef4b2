server {
  listen 80;
  server_name localhost;

  root /usr/share/nginx/html;
  index index.html index.htm;

  # Handle client-side routing
  location / {
    try_files $uri $uri/ /index.html;
  }

  # Add headers to prevent caching of index.html
  location = /index.html {
    add_header 'Cache-Control' 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
    expires off;
  }

  # Cache static assets for a long time
  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public";
  }
} 