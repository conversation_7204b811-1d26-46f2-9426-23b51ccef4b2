好的，遵照您的指示。我将为您生成一份专门针对 **`personal-kb-service` (PKB)** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`personal-kb-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**应用层加密(ALE)的实现流程、混合搜索(Hybrid Search)的架构、与多个AI和安全服务的深度协同，以及作为一个隐私核心服务的数据隔离与安全保障**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `personal-kb-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `personal-kb-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 应用层加密(ALE) + 混合搜索(Hybrid Search)

## 1. 概述

`personal-kb-service` 是用户**个人数据主权的体现**，为用户提供一个安全的、可智能检索的私有知识库。其核心挑战在于：
1.  **绝对的隐私与安全**: 必须实现**服务器端零知识**存储。服务在持久化层只处理密文，但在业务逻辑层需要临时访问明文以进行索引和处理。
2.  **强大的检索能力**: 需要结合**关键词搜索**和**向量语义搜索**，提供精准、智能的混合搜索体验。
3.  **异步内容处理**: 从URL剪辑或上传文档中提取内容是耗时操作，必须异步化。
4.  **与加密和AI服务的紧密协同**: 作为一个隐私和AI功能的核心交汇点，需要与`key-management-proxy-service`和`embedding-service`进行高频、安全、可靠的交互。
5.  **数据隔离**: 必须在架构和代码层面，保证任何情况下一个用户的数据都不能被另一个用户访问到。

本架构设计通过采用**整洁架构**，并在应用层严格执行**加解密流程**，同时构建一个**多阶段的混合搜索管道**，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (创建与混合搜索流程)

```mermaid
graph TD
    subgraph "客户端/AI助手"
        Requester
    end

    subgraph "PersonalKBService"
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B_Write[CommandService<br/><em>application/command</em>]
        B_Read[QueryService<br/><em>application/query</em>]
        C[HybridSearchService<br/><em>domain/service</em>]
        D[Repository (Postgres)<br/><em>adapter/repository</em>]
        E[VectorDB Repo<br/><em>adapter/repository</em>]
        F[KMSProxy Client<br/><em>adapter/client</em>]
        G[Embedding Client<br/><em>adapter/client</em>]
        H[Kafka Producer<br/><em>adapter/event</em>]
    end

    subgraph "下游依赖"
        KMSProxy[key-management-proxy-service]
        Embedding[embedding-service]
        VectorDB[(Vector Database)]
        PostgreSQL[(PostgreSQL)]
        Kafka[(Kafka)]
    end
    
    %% Create Flow
    Requester -- "1. CreateItem(plaintext)" --> A
    A -- "调用" --> B_Write
    B_Write -- "2. Get DEK & Encrypt" --> F
    F -- "DecryptData RPC" --> KMSProxy
    
    B_Write -- "3. Get Embedding" --> G
    G -- "Embed RPC" --> Embedding
    
    B_Write -- "4. Save to DBs" --> D & E
    D --> PostgreSQL
    E --> VectorDB
    
    %% Search Flow
    Requester -- "5. Search(query)" --> A
    A -- "调用" --> B_Read
    B_Read -- "6. Use HybridSearch" --> C
    
    C -- "6a. Semantic Search" --> E
    C -- "6b. Keyword Search" --> D
    
    C -- "7. Fuse & Re-rank results" --> C
    C -- "Returns ranked IDs" --> B_Read
    
    B_Read -- "8. Get & Decrypt content for Top N" --> D & F
    B_Read -- "9. Return results" --> A
```

### 2.2 最终目录结构 (`services/personal-kb-service/`)

```
personal-kb-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # ✨ 异步内容处理(URL剪辑/文档提取)的Worker入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── kms_proxy_client.go
│   │   │   └── embedding_client.go
│   │   ├── event/
│   │   │   └── producer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       ├── postgres_repo.go
│   │       └── vectordb_repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── command/
│   │   │   └── pkb_command_service.go # ✨ 处理写操作和加密流程 ✨
│   │   └── query/
│   │       └── pkb_query_service.go   # ✨ 处理读操作和混合搜索 ✨
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── hybrid_search_service.go # ✨ 混合搜索与RRF融合算法 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Knowledge & Search Rules)

*   `domain/model/`: 使用`/core/models`中与PKB相关的`struct`，如`PKBItem`, `RichContent`。
*   **`domain/service/hybrid_search_service.go`**: **这是混合搜索的核心算法实现**。
    *   **`HybridSearcher`**: 一个无状态的领域服务。
    *   **`Search(ctx, query, semanticResults, keywordResults)` method**:
        *   接收一个原始查询和两路（语义、关键词）召回的结果列表。每个结果列表都包含ID和各自的分数。
        *   **规范化分数**: 将两路结果的分数分别进行规范化（如min-max scaling）。
        *   **融合与重排**:
            1.  使用**Reciprocal Rank Fusion (RRF)**算法来融合两个排序列表。RRF对排序位置敏感，对绝对分数不敏感，非常适合融合异构的搜索结果。
            2.  公式: `RRF_Score(doc) = 1 / (k + rank_semantic) + 1 / (k + rank_keyword)` (k是常数，如60)。
            3.  根据`RRF_Score`对所有文档进行最终排序。
        *   返回一个有序的文档ID列表。

### 3.2 `application/` - 应用层 (The Secure Workflows)

采用CQRS思想，将加密/解密的复杂性分别隔离在写/读路径中。

*   **`application/command/pkb_command_service.go`**: **处理所有写操作，是ALE模型的核心实现者**。
    *   **`CreateItem(ctx, userID, itemData)`**:
        1.  **获取明文**: `plaintext_content = itemData.Content`。
        2.  **并行调用**:
            a. **获取向量**: `vector, err := embeddingClient.Embed(plaintext_content)`。
            b. **获取密文**: `ciphertext, err := kmsProxyClient.EncryptData(userID, plaintext_content)`。
        3.  **开启数据库事务**:
            a. 调用`vectordbRepo.Upsert(itemID, vector)`。
            b. 调用`postgresRepo.CreateItem(itemID, userID, ciphertext, ...)`。
        4.  **提交事务**。
    *   **`ImportFromURL(ctx, userID, url)`**:
        *   在数据库中创建一个状态为`PROCESSING`的`PKBItem`记录。
        *   **发布`URLClipJobRequest`事件到Kafka**。

*   **`application/query/pkb_query_service.go`**: **处理所有读操作，执行混合搜索和解密**。
    *   **`SearchItems(ctx, userID, query, ...)`**:
        1.  **并行执行召回**:
            a. **语义召回**: 调用`embeddingClient.Embed(query)`获取查询向量，然后调用`vectordbRepo.Search(vector)`获取`semanticResults`。
            b. **关键词召回**: 调用`postgresRepo.SearchByKeyword(query)`（使用FTS）获取`keywordResults`。
        2.  **融合**: 调用`domain.HybridSearcher.Search()`获取最终排序的ID列表。
        3.  **获取密文**: 取Top-N的ID，调用`postgresRepo.GetItemsBatch(ids)`获取其**加密内容**。
        4.  **解密**: **并行地**为每个item调用`kmsProxyClient.DecryptData(userID, item.EncryptedContent)`。
        5.  返回解密后的、排序好的结果列表。
    *   **`GetItem(ctx, userID, itemID)`**:
        1.  从`postgresRepo`获取加密内容。
        2.  调用`kmsProxyClient.DecryptData()`解密。
        3.  返回明文内容。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   `postgres_repo.go`: 实现与PostgreSQL的交互。存储**加密后的**`PKBItem`。
        *   提供`SearchByKeyword`方法，使用PostgreSQL的**全文检索(FTS)**功能。
    *   `vectordb_repo.go`: 实现与**向量数据库**的交互。封装k-NN搜索和Upsert操作。
*   **`adapter/client/`**:
    *   封装对`key-management-proxy-service`和`embedding-service`的gRPC调用。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现gRPC接口，根据请求是读还是写，分别调用`QueryService`或`CommandService`。**Handler层是执行用户ID隔离检查的第一道防线**。

### 3.4 `cmd/worker/` - 异步内容处理器

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   消费来自Kafka的`URLClipJobRequest`和`DocumentProcessJobRequest`任务。
    *   **URL剪辑**: 使用`go-rod`或类似的headless browser库，抓取URL的正文内容。
    *   **文档提取**: 使用`unidoc`或外部Tika服务，从PDF/DOCX等文件中提取纯文本。
    *   **处理**: 提取出明文内容后，它会**像一个普通的客户端一样**，调用本服务的`pkb_command_service.CreateItem`流程（获取向量、加密、存储），来完成一个异步导入的条目创建。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`personal-kb-service`：
1.  **安全即核心 (Security as Core)**: **应用层加密(ALE)**流程被深度集成在`CommandService`中。服务在任何时候都不会将明文和用户身份一起持久化，确保了服务器端零知识。
2.  **先进的检索模型**: 通过在领域层实现`HybridSearchService`，将语义搜索和关键词搜索的优势结合起来，并通过RRF进行科学的融合，提供了远超单一搜索方式的精准度。
3.  **CQRS的实践**: 清晰地分离了**写路径（加密和索引）**和**读路径（搜索和解密）**，使得两条路径都可以被独立优化，代码职责分明。
4.  **异步化处理**: 将耗时的内容导入操作（网页抓取、文档解析）通过**事件驱动的Worker模式**进行异步处理，保证了API的快速响应和系统的弹性。
5.  **严格的依赖和交互**: 与`kms-proxy-service`和`embedding-service`的交互界限清晰，职责单一，共同构成了一个强大的、隐私优先的AI知识管理体系。

这种架构确保了`personal-kb-service`能够为用户提供一个**绝对安全、功能强大且智能易用**的个人知识大脑，成为AI助手最可信赖的私有知识来源。