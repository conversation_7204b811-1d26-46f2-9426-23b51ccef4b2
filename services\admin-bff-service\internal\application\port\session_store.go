/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package port

import (
	"context"
	"time"

	"cina.club/services/admin-bff-service/internal/domain/model"
)

// SessionStore session storage interface
type SessionStore interface {
	// CreateSession creates a new session
	CreateSession(ctx context.Context, session *model.AdminSession) error

	// GetSession gets session by session ID
	GetSession(ctx context.Context, sessionID string) (*model.AdminSession, error)

	// UpdateSession updates session information
	UpdateSession(ctx context.Context, session *model.AdminSession) error

	// DeleteSession deletes a session
	DeleteSession(ctx context.Context, sessionID string) error

	// RefreshSession refreshes session expiration time
	RefreshSession(ctx context.Context, sessionID string, duration time.Duration) error

	// GetActiveSessionsByEmployeeID gets all active sessions for an employee
	GetActiveSessionsByEmployeeID(ctx context.Context, employeeID string) ([]*model.AdminSession, error)

	// DeleteAllSessionsByEmployeeID deletes all sessions for an employee
	DeleteAllSessionsByEmployeeID(ctx context.Context, employeeID string) error

	// GetSessionCount gets total number of active sessions
	GetSessionCount(ctx context.Context) (int64, error)

	// CleanupExpiredSessions cleans up expired sessions
	CleanupExpiredSessions(ctx context.Context) (int64, error)
}

// AuditLogger audit log recorder interface
type AuditLogger interface {
	// LogEntry records an audit log entry
	LogEntry(ctx context.Context, entry *model.AuditLogEntry) error

	// LogBatch records audit logs in batch
	LogBatch(ctx context.Context, entries []*model.AuditLogEntry) error

	// GetLogs gets audit logs (for querying)
	GetLogs(ctx context.Context, filter model.AuditLogFilter) ([]*model.AuditLogEntry, error)

	// GetLogCount gets total number of audit logs
	GetLogCount(ctx context.Context, filter model.AuditLogFilter) (int64, error)
}

// AuditLogFilter audit log filter
type AuditLogFilter struct {
	ActorID      string
	ResourceType string
	ResourceID   string
	Action       string
	StartTime    *time.Time
	EndTime      *time.Time
	Success      *bool
	IPAddress    string
	Limit        int
	Offset       int
}

// EmployeeProvider employee information provider interface
type EmployeeProvider interface {
	// GetEmployeeByID gets employee information by ID
	GetEmployeeByID(ctx context.Context, employeeID string) (*model.Employee, error)

	// GetEmployeeByEmail gets employee information by email
	GetEmployeeByEmail(ctx context.Context, email string) (*model.Employee, error)

	// ValidateEmployee validates if an employee is valid
	ValidateEmployee(ctx context.Context, employeeID string) (bool, error)

	// GetEmployeeRoles gets employee roles
	GetEmployeeRoles(ctx context.Context, employeeID string) ([]string, error)

	// UpdateEmployeeLastLogin updates employee's last login time
	UpdateEmployeeLastLogin(ctx context.Context, employeeID string) error
}
