//go:build mobile

// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package aic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// MobileEngine provides a mobile-friendly interface to the AI engine
type MobileEngine struct {
	engine *Engine
}

// NewMobileEngine creates a new mobile AI engine
func NewMobileEngine() *MobileEngine {
	return &MobileEngine{
		engine: NewEngine(),
	}
}

// LoadModelMobile loads a model and returns the model info as JSON
func (me *MobileEngine) LoadModelMobile(modelPath string) (string, error) {
	modelInfo, err := me.engine.LoadModel(modelPath)
	if err != nil {
		return "", err
	}

	jsonData, err := json.Marshal(modelInfo)
	if err != nil {
		return "", fmt.Errorf("failed to marshal model info: %w", err)
	}

	return string(jsonData), nil
}

// UnloadModelMobile unloads a model
func (me *MobileEngine) UnloadModelMobile(modelID string) error {
	return me.engine.UnloadModel(modelID)
}

// GetLoadedModelsMobile returns loaded models as JSON
func (me *MobileEngine) GetLoadedModelsMobile() (string, error) {
	models := me.engine.GetLoadedModels()

	jsonData, err := json.Marshal(models)
	if err != nil {
		return "", fmt.Errorf("failed to marshal loaded models: %w", err)
	}

	return string(jsonData), nil
}

// CreateSessionMobile creates a new inference session and returns the session as JSON
func (me *MobileEngine) CreateSessionMobile(modelID string, configJSON string) (string, error) {
	var config *InferenceConfig

	if configJSON != "" {
		config = &InferenceConfig{}
		if err := json.Unmarshal([]byte(configJSON), config); err != nil {
			return "", fmt.Errorf("failed to unmarshal config: %w", err)
		}
	}

	session, err := me.engine.CreateSession(modelID, config)
	if err != nil {
		return "", err
	}

	jsonData, err := json.Marshal(session)
	if err != nil {
		return "", fmt.Errorf("failed to marshal session: %w", err)
	}

	return string(jsonData), nil
}

// CloseSessionMobile closes a session
func (me *MobileEngine) CloseSessionMobile(sessionID string) error {
	return me.engine.CloseSession(sessionID)
}

// PredictMobile performs synchronous inference and returns the result as JSON
func (me *MobileEngine) PredictMobile(sessionID string, prompt string) (string, error) {
	result, err := me.engine.Predict(sessionID, prompt)
	if err != nil {
		return "", err
	}

	jsonData, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal inference result: %w", err)
	}

	return string(jsonData), nil
}

// GetSessionContextMobile returns the session context as JSON
func (me *MobileEngine) GetSessionContextMobile(sessionID string) (string, error) {
	session, err := me.engine.GetSession(sessionID)
	if err != nil {
		return "", err
	}

	context := session.GetContext()

	jsonData, err := json.Marshal(context)
	if err != nil {
		return "", fmt.Errorf("failed to marshal session context: %w", err)
	}

	return string(jsonData), nil
}

// AddToSessionContextMobile adds a message to the session context
func (me *MobileEngine) AddToSessionContextMobile(sessionID string, message string) error {
	session, err := me.engine.GetSession(sessionID)
	if err != nil {
		return err
	}

	session.AddToContext(message)
	return nil
}

// ClearSessionContextMobile clears the session context
func (me *MobileEngine) ClearSessionContextMobile(sessionID string) error {
	session, err := me.engine.GetSession(sessionID)
	if err != nil {
		return err
	}

	session.ClearContext()
	return nil
}

// CloseMobile closes the engine
func (me *MobileEngine) CloseMobile() error {
	return me.engine.Close()
}

// MobileTokenCallback provides a mobile-friendly token callback interface
type MobileTokenCallback struct {
	OnTokenFunc    func(tokenJSON string) error
	OnCompleteFunc func(resultJSON string) error
	OnErrorFunc    func(errMsg string)
}

// OnToken is called for each token
func (mtc *MobileTokenCallback) OnToken(token *Token) error {
	if mtc.OnTokenFunc == nil {
		return nil
	}

	jsonData, err := json.Marshal(token)
	if err != nil {
		return fmt.Errorf("failed to marshal token: %w", err)
	}

	return mtc.OnTokenFunc(string(jsonData))
}

// OnComplete is called when inference is complete
func (mtc *MobileTokenCallback) OnComplete(result *InferenceResult) error {
	if mtc.OnCompleteFunc == nil {
		return nil
	}

	jsonData, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %w", err)
	}

	return mtc.OnCompleteFunc(string(jsonData))
}

// OnError is called when an error occurs
func (mtc *MobileTokenCallback) OnError(err error) {
	if mtc.OnErrorFunc != nil {
		mtc.OnErrorFunc(err.Error())
	}
}

// PredictStreamMobile performs streaming inference with mobile-friendly callbacks
func (me *MobileEngine) PredictStreamMobile(sessionID string, prompt string, timeoutSeconds int, callback *MobileTokenCallback) error {
	ctx := context.Background()
	if timeoutSeconds > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(timeoutSeconds)*time.Second)
		defer cancel()
	}

	return me.engine.PredictStream(ctx, sessionID, prompt, callback)
}

// DefaultInferenceConfigMobile returns a default inference config as JSON
func DefaultInferenceConfigMobile() (string, error) {
	config := DefaultInferenceConfig()

	jsonData, err := json.Marshal(config)
	if err != nil {
		return "", fmt.Errorf("failed to marshal default config: %w", err)
	}

	return string(jsonData), nil
}

// ValidateInferenceConfigMobile validates an inference config JSON
func ValidateInferenceConfigMobile(configJSON string) (string, error) {
	var config InferenceConfig
	if err := json.Unmarshal([]byte(configJSON), &config); err != nil {
		return err.Error(), nil
	}

	// Basic validation
	if config.MaxTokens <= 0 {
		return "MaxTokens must be greater than 0", nil
	}

	if config.Temperature < 0 || config.Temperature > 2 {
		return "Temperature must be between 0 and 2", nil
	}

	if config.TopP < 0 || config.TopP > 1 {
		return "TopP must be between 0 and 1", nil
	}

	return "", nil // Valid
}
