好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`core/aic` (AI Core)** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档是实现平台**端侧AI能力**的核心，将详细阐述`core/aic`的设计哲学、与底层C++推理引擎的交互方式、接口定义和安全约束，作为所有需要本地AI功能的前端应用的权威实现蓝图。

---
### CINA.CLUB - 共享核心本地AI库 (`core/aic`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [AI平台总监/首席工程师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计与架构](#3-核心设计与架构)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [跨平台导出与使用](#6-跨平台导出与使用)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台提供低延迟、可离线、且隐私安全的AI功能（如本地LLM聊天），需要一个能够在用户设备上高效执行AI模型推理的引擎。`core/aic` (AI Core) 包的目的在于提供一个**标准化的、高性能的、平台无关的本地AI推理引擎的Go语言封装**。它通过**CGO**调用底层的C++推理库（如`llama.cpp`），将复杂的模型加载、内存管理和推理执行逻辑封装起来，为上层应用提供简单、统一的AI能力调用接口。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   使用CGO定义并实现对`llama.cpp`等C++推理库核心API的Go语言绑定。
    *   管理本地AI**模型的加载、初始化和卸载**的生命周期。
    *   封装**推理会话(Session)**，处理输入的预处理和输出的后处理。
    *   实现对**流式生成(Streaming Generation)**的支持，以支持打字机效果。
    *   提供一套简洁、安全的Go语言API，供Go Mobile和WASM导出。
*   **范围之外 (Out-of-Scope)**:
    *   **模型的下载、更新和版本管理**: 这是前端`ai-engine` TypeScript模块的职责。本包只负责加载一个给定路径的模型文件。
    *   **具体的C++推理库的实现**: 本包是`llama.cpp`等库的**使用者和封装者**，而不是实现者。
    *   **任何业务逻辑**: 本包只负责纯粹的AI模型推理。
    *   **UI的实现**。

#### 1.3. 目标用户
*   **CINA.CLUB前端应用** (通过Go Mobile/WASM)，是本包的唯一消费者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`core/aic` 是位于`core/`目录下的一个高度专业化的库。它处于技术栈的底层，直接与C/C++世界交互。
*   **依赖关系**: 它只依赖Go标准库和CGO。它不依赖任何其他的`core/`或`pkg/`包。
*   **被依赖关系**: 它被前端（通过Go Mobile/WASM）间接调用。

#### 2.2. 设计原则
*   **性能是王道 (Performance is King)**: CGO的调用、内存的拷贝、数据的转换都必须被优化到极致，以最小化Go与C++之间的交互开销。
*   **资源安全 (Resource Safety)**: C++层的内存（如模型权重、上下文）必须由Go层进行精确的生命周期管理。**必须**通过`defer`和`runtime.SetFinalizer`等机制，确保C++分配的内存被正确释放，防止内存泄漏。
*   **薄封装 (Thin Wrapper)**: Go层的封装应尽可能地“薄”，主要负责类型安全、错误处理和生命周期管理，而将核心的计算密集型逻辑保留在C++层。
*   **异步与并发安全**: 推理过程通常是阻塞和耗时的，必须在独立的goroutine中执行，以避免阻塞调用方（特别是UI线程）。对单个推理会话的访问必须是线程安全的。

---

### 3. 核心设计与架构

#### 3.1. CGO绑定 (`cgo_bindings.go`)
这是连接Go和C++世界的桥梁。
*   使用`// #cgo CFLAGS: ...`和`// #cgo LDFLAGS: ...`指令来配置C++编译器的参数和需要链接的库。
*   使用`import "C"`来引入一个虚拟的C包。
*   所有需要从Go调用的C++函数，都在这个文件中通过`C.function_name()`的形式进行调用。
*   所有需要从C++回调到Go的函数，都通过`//export FunctionName`的方式导出，并传递函数指针给C++层。

#### 3.2. 推理会话 (`Session`)
`Session`是核心的抽象，代表一个已加载到内存中的模型实例。
*   **数据结构**: `Session` struct内部包含一个指向C++层模型上下文的指针（如`C.struct_llama_context*`）和一个`sync.Mutex`来保证并发安全。
*   **生命周期**:
    1.  `aic.NewSession(modelPath)`: 调用`C.llama_init_from_file()`加载模型，创建一个C++上下文，并将其指针存入`Session`结构体。使用`runtime.SetFinalizer`为`Session`对象注册一个终结器，确保在`Session`对象被GC回收时，对应的C++内存能被`C.llama_free()`释放。
    2.  `Session.Close()`: 提供一个手动释放资源的方法，主动调用`C.llama_free()`。

#### 3.3. 流式生成模型
1.  Go层调用C++层的推理函数，并传递一个Go的**回调函数指针**。
2.  C++层的推理循环每生成一个token，就通过函数指针调用这个Go回调函数。
3.  Go回调函数将接收到的C++字符串（`*C.char`）转换为Go字符串。
4.  为了将结果传递给上层（如React Native的JS层），这个Go回调函数会将token写入一个**Go channel**，或者调用一个预先注册的接口方法（对于Go Mobile）。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 模型管理
*   **FR4.1.1 (加载模型)**: 提供`NewSession(modelPath string, config SessionConfig)`函数。
    *   `modelPath`: 模型的本地文件路径。
    *   `SessionConfig`: 包含GPU层数、上下文长度等模型加载参数。
    *   函数必须返回一个`*Session`实例和一个`error`。加载失败（如文件不存在、格式错误）必须返回详细错误。
*   **FR4.1.2 (释放模型)**: `Session`对象必须实现一个`Close()`方法，用于显式释放所有C++层分配的内存和资源。

#### 4.2. 推理执行
*   **FR4.2.1 (流式预测)**: 提供`Session.PredictStream(ctx context.Context, prompt string, config PredictConfig) (<-chan string, error)`方法。
    *   `ctx`: 用于控制超时和取消。
    *   `PredictConfig`: 包含采样参数，如`temperature`, `top_p`, `top_k`。
    *   返回一个**只读的Go channel**和一个`error`。调用方可以从channel中持续读取生成的token流。当生成结束或发生错误时，channel会被关闭。
*   **FR4.2.2 (异步执行)**: `PredictStream`方法必须是**非阻塞的**。它应立即返回channel和error，并在一个新的goroutine中启动实际的推理循环。
*   **FR4.2.3 (取消支持)**: 推理循环必须能响应`ctx.Done()`。当上下文被取消时，应尽快中断C++层的推理并关闭结果channel。

---

### 5. 接口定义 (API Specification)

```go
// core/aic/session.go

// Session 代表一个加载到内存的AI模型推理会话。
// 它是并发安全的。
type Session struct {
    // ... private fields, including C pointer and mutex
}

type SessionConfig struct {
    ContextSize int
    GpuLayers   int
    // ...其他llama.cpp加载参数
}

type PredictConfig struct {
    Temperature float32
    TopP        float32
    TopK        int
    // ...其他采样参数
}

// NewSession 加载一个模型文件并创建一个新的推理会话。
func NewSession(modelPath string, cfg SessionConfig) (*Session, error)

// Close 显式释放与会话相关的C++内存。
func (s *Session) Close() error

// PredictStream 以流式方式执行推理。
// 返回一个用于接收token的channel。channel关闭表示生成结束或出错。
func (s *Session) PredictStream(
    ctx context.Context, 
    prompt string, 
    cfg PredictConfig,
) (<-chan string, error)
```

---

### 6. 跨平台导出与使用

#### 6.1. Go Mobile 导出 (`exports_mobile.go`)
由于Go Mobile不支持channel，我们需要使用**回调接口**的模式。
```go
// core/aic/exports_mobile.go
//go:build mobile

// TokenCallback 是一个由原生层(Java/Swift)实现的接口，用于接收token。
type TokenCallback interface {
    OnToken(token string)
    OnEnd()
    OnError(err string)
}

// MobileSession 是对Session的包装，以适应Go Mobile的接口限制。
type MobileSession struct {
    internalSession *Session
}

func NewMobileSession(modelPath string, /* ... */) (*MobileSession, error) { ... }

// Predict 方法是阻塞的，它会在另一个goroutine中运行推理，并通过回调返回结果。
func (ms *MobileSession) Predict(prompt string, /* ... */, callback TokenCallback) {
    go func() {
        // ... 调用 internalSession.PredictStream ...
        // 在循环中，从channel读取token，并调用 callback.OnToken(token)
        // 结束后调用 callback.OnEnd() 或 callback.OnError()
    }()
}
```

#### 6.2. 前端使用 (React Native)
*   前端的`ai-engine` TypeScript模块会调用`CoreGoBridgeModule.NewMobileSession()`来创建会话。
*   调用`session.Predict()`时，会传递一个JS函数作为回调，这个回调函数通过**事件发射器(EventEmitter)**将token发送给React Hooks，最终更新UI。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   **首Token延迟(TTFT)**: 对于一个简单的prompt，TTFT应在1-2秒内（取决于设备和模型加载状态）。
    *   **生成速度**: 在中端Android设备上，4-bit量化模型的生成速度目标为 >10 tokens/秒。
*   **NFR7.2 (内存管理)**:
    *   内存占用必须被严格控制。必须确保没有CGO导致的内存泄漏。
    *   应用应能处理模型加载失败（如内存不足）的错误，并优雅地降级。
*   **NFR7.3 (可靠性)**: CGO调用和内存操作必须极度健壮，防止任何可能导致应用崩溃的段错误(segmentation fault)。

---

### 8. 技术约束与开发规范

*   **TC8.1 (核心依赖)**:
    *   **C++库**: `llama.cpp`。其Git Submodule应被包含在`core/aic/third_party`目录下。
    *   **构建工具**: `CMake`用于编译C++代码。
*   **TC8.2 (开发规范)**:
    *   **CGO安全**: 所有在Go和C之间传递的指针都必须小心处理。严禁在Go中存储指向Go内存的C指针。
    *   **错误处理**: 所有C++函数返回的错误码都必须在Go层被检查，并转换为Go的`error`类型。
    *   **平台特定编译**: 必须使用构建标签来隔离与CGO相关的代码，确保`core/aic`在非移动端环境（如普通单元测试）下也能被编译（尽管功能受限）。
    *   **严格审查**: 对`core/aic`的任何PR都必须经过对CGO和并发编程有深入理解的核心工程师的审查。

---
这份SRS为`core/aic`库的设计和实现提供了坚实、安全、高性能的指导。通过将复杂的底层C++交互封装在一个干净、可靠的Go API后面，它为CINA.CLUB平台在所有客户端上实现强大的端侧AI能力奠定了核心基础。