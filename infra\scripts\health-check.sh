#!/bin/bash
# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=""
NAMESPACE=""
VERBOSE=false

# Help function
show_help() {
    cat << EOF
CINA.CLUB Infrastructure Health Check Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV    Target environment (dev|staging|prod)
    -n, --namespace NS       Kubernetes namespace [default: cina-club-ENV]
    -v, --verbose           Enable verbose output
    -h, --help              Show this help message

EXAMPLES:
    $0 -e dev              # Check health of dev environment
    $0 -e prod -v          # Verbose health check for prod
    $0 -n custom-namespace # Check specific namespace

EOF
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "curl is not installed"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Check cluster connectivity
check_cluster_connectivity() {
    log_info "Checking cluster connectivity..."
    
    if kubectl cluster-info > /dev/null 2>&1; then
        log_success "Connected to Kubernetes cluster"
        if [[ "$VERBOSE" == true ]]; then
            kubectl cluster-info
        fi
    else
        log_error "Failed to connect to Kubernetes cluster"
        exit 1
    fi
}

# Check namespace
check_namespace() {
    log_info "Checking namespace: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" > /dev/null 2>&1; then
        log_success "Namespace exists: $NAMESPACE"
    else
        log_error "Namespace not found: $NAMESPACE"
        exit 1
    fi
}

# Check deployments
check_deployments() {
    log_info "Checking deployments in namespace: $NAMESPACE"
    
    local deployments
    deployments=$(kubectl get deployments -n "$NAMESPACE" -o name 2>/dev/null || echo "")
    
    if [[ -z "$deployments" ]]; then
        log_warning "No deployments found in namespace: $NAMESPACE"
        return
    fi
    
    local total=0
    local ready=0
    local failed=0
    
    while IFS= read -r deployment; do
        if [[ -z "$deployment" ]]; then
            continue
        fi
        
        total=$((total + 1))
        deployment_name=$(echo "$deployment" | cut -d'/' -f2)
        
        log_info "Checking deployment: $deployment_name"
        
        # Get deployment status
        local replicas available ready_replicas
        replicas=$(kubectl get "$deployment" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
        available=$(kubectl get "$deployment" -n "$NAMESPACE" -o jsonpath='{.status.availableReplicas}' 2>/dev/null || echo "0")
        ready_replicas=$(kubectl get "$deployment" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        
        if [[ "$available" == "$replicas" ]] && [[ "$ready_replicas" == "$replicas" ]]; then
            log_success "  ✓ $deployment_name: $ready_replicas/$replicas replicas ready"
            ready=$((ready + 1))
        else
            log_error "  ✗ $deployment_name: $ready_replicas/$replicas replicas ready (Expected: $replicas)"
            failed=$((failed + 1))
            
            if [[ "$VERBOSE" == true ]]; then
                kubectl describe "$deployment" -n "$NAMESPACE"
            fi
        fi
    done <<< "$deployments"
    
    log_info "Deployment Summary: $ready ready, $failed failed, $total total"
    
    if [[ $failed -gt 0 ]]; then
        return 1
    fi
}

# Check services
check_services() {
    log_info "Checking services in namespace: $NAMESPACE"
    
    local services
    services=$(kubectl get services -n "$NAMESPACE" -o name 2>/dev/null || echo "")
    
    if [[ -z "$services" ]]; then
        log_warning "No services found in namespace: $NAMESPACE"
        return
    fi
    
    while IFS= read -r service; do
        if [[ -z "$service" ]]; then
            continue
        fi
        
        service_name=$(echo "$service" | cut -d'/' -f2)
        log_info "Checking service: $service_name"
        
        # Get service endpoints
        local endpoints
        endpoints=$(kubectl get endpoints "$service_name" -n "$NAMESPACE" -o jsonpath='{.subsets[*].addresses[*].ip}' 2>/dev/null || echo "")
        
        if [[ -n "$endpoints" ]]; then
            local endpoint_count
            endpoint_count=$(echo "$endpoints" | wc -w)
            log_success "  ✓ $service_name: $endpoint_count endpoints"
        else
            log_warning "  ⚠ $service_name: No endpoints available"
        fi
        
        if [[ "$VERBOSE" == true ]]; then
            kubectl get service "$service_name" -n "$NAMESPACE" -o wide
        fi
    done <<< "$services"
}

# Check pods
check_pods() {
    log_info "Checking pods in namespace: $NAMESPACE"
    
    local pods
    pods=$(kubectl get pods -n "$NAMESPACE" -o name 2>/dev/null || echo "")
    
    if [[ -z "$pods" ]]; then
        log_warning "No pods found in namespace: $NAMESPACE"
        return
    fi
    
    local total=0
    local running=0
    local failed=0
    
    while IFS= read -r pod; do
        if [[ -z "$pod" ]]; then
            continue
        fi
        
        total=$((total + 1))
        pod_name=$(echo "$pod" | cut -d'/' -f2)
        
        # Get pod status
        local status ready restarts
        status=$(kubectl get "$pod" -n "$NAMESPACE" -o jsonpath='{.status.phase}' 2>/dev/null || echo "Unknown")
        ready=$(kubectl get "$pod" -n "$NAMESPACE" -o jsonpath='{.status.containerStatuses[0].ready}' 2>/dev/null || echo "false")
        restarts=$(kubectl get "$pod" -n "$NAMESPACE" -o jsonpath='{.status.containerStatuses[0].restartCount}' 2>/dev/null || echo "0")
        
        if [[ "$status" == "Running" ]] && [[ "$ready" == "true" ]]; then
            log_success "  ✓ $pod_name: Running (restarts: $restarts)"
            running=$((running + 1))
        else
            log_error "  ✗ $pod_name: $status (ready: $ready, restarts: $restarts)"
            failed=$((failed + 1))
            
            if [[ "$VERBOSE" == true ]]; then
                kubectl describe "$pod" -n "$NAMESPACE"
                kubectl logs "$pod" -n "$NAMESPACE" --tail=10
            fi
        fi
    done <<< "$pods"
    
    log_info "Pod Summary: $running running, $failed failed, $total total"
    
    if [[ $failed -gt 0 ]]; then
        return 1
    fi
}

# Check ingresses
check_ingresses() {
    log_info "Checking ingresses in namespace: $NAMESPACE"
    
    local ingresses
    ingresses=$(kubectl get ingresses -n "$NAMESPACE" -o name 2>/dev/null || echo "")
    
    if [[ -z "$ingresses" ]]; then
        log_info "No ingresses found in namespace: $NAMESPACE"
        return
    fi
    
    while IFS= read -r ingress; do
        if [[ -z "$ingress" ]]; then
            continue
        fi
        
        ingress_name=$(echo "$ingress" | cut -d'/' -f2)
        log_info "Checking ingress: $ingress_name"
        
        # Get ingress status
        local hosts
        hosts=$(kubectl get "$ingress" -n "$NAMESPACE" -o jsonpath='{.spec.rules[*].host}' 2>/dev/null || echo "")
        
        if [[ -n "$hosts" ]]; then
            log_success "  ✓ $ingress_name: Hosts configured: $hosts"
        else
            log_warning "  ⚠ $ingress_name: No hosts configured"
        fi
        
        if [[ "$VERBOSE" == true ]]; then
            kubectl describe "$ingress" -n "$NAMESPACE"
        fi
    done <<< "$ingresses"
}

# Check resource usage
check_resource_usage() {
    log_info "Checking resource usage in namespace: $NAMESPACE"
    
    if ! kubectl top nodes > /dev/null 2>&1; then
        log_warning "Metrics server not available - skipping resource usage check"
        return
    fi
    
    log_info "Node resource usage:"
    kubectl top nodes
    
    log_info "Pod resource usage in namespace $NAMESPACE:"
    kubectl top pods -n "$NAMESPACE" 2>/dev/null || log_warning "No pods found or metrics not available"
}

# Perform health check on service endpoints
check_service_health() {
    log_info "Checking service health endpoints..."
    
    # Port-forward to each service and check health endpoint
    local services
    services=$(kubectl get services -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || echo "")
    
    for service in $services; do
        if [[ -z "$service" ]]; then
            continue
        fi
        
        log_info "Checking health endpoint for service: $service"
        
        # Get service port
        local port
        port=$(kubectl get service "$service" -n "$NAMESPACE" -o jsonpath='{.spec.ports[0].port}' 2>/dev/null || echo "")
        
        if [[ -z "$port" ]]; then
            log_warning "  ⚠ No port found for service: $service"
            continue
        fi
        
        # Start port-forward in background
        kubectl port-forward "service/$service" 8080:"$port" -n "$NAMESPACE" > /dev/null 2>&1 &
        local pf_pid=$!
        
        # Wait a moment for port-forward to establish
        sleep 2
        
        # Check health endpoint
        if curl -s -f http://localhost:8080/health > /dev/null 2>&1; then
            log_success "  ✓ $service: Health endpoint responding"
        else
            log_warning "  ⚠ $service: Health endpoint not responding or not available"
        fi
        
        # Kill port-forward
        kill $pf_pid > /dev/null 2>&1 || true
        sleep 1
    done
}

# Generate health report
generate_health_report() {
    log_info "Generating health report for environment: $ENVIRONMENT"
    
    local report_file="/tmp/cina-club-health-report-${ENVIRONMENT}-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "CINA.CLUB Health Report"
        echo "Environment: $ENVIRONMENT"
        echo "Namespace: $NAMESPACE"
        echo "Generated: $(date)"
        echo ""
        
        echo "=== Cluster Info ==="
        kubectl cluster-info
        echo ""
        
        echo "=== Nodes ==="
        kubectl get nodes -o wide
        echo ""
        
        echo "=== Deployments ==="
        kubectl get deployments -n "$NAMESPACE" -o wide
        echo ""
        
        echo "=== Services ==="
        kubectl get services -n "$NAMESPACE" -o wide
        echo ""
        
        echo "=== Pods ==="
        kubectl get pods -n "$NAMESPACE" -o wide
        echo ""
        
        echo "=== Events (Last 10) ==="
        kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -10
        echo ""
        
    } > "$report_file"
    
    log_success "Health report generated: $report_file"
}

# Main health check flow
main() {
    log_info "Starting CINA.CLUB infrastructure health check"
    log_info "Environment: $ENVIRONMENT"
    log_info "Namespace: $NAMESPACE"
    
    check_prerequisites
    check_cluster_connectivity
    check_namespace
    
    local exit_code=0
    
    check_deployments || exit_code=1
    check_services
    check_pods || exit_code=1
    check_ingresses
    check_resource_usage
    check_service_health
    
    generate_health_report
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "Health check completed successfully!"
    else
        log_error "Health check completed with issues!"
    fi
    
    exit $exit_code
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Set default namespace if not provided
if [[ "$NAMESPACE" == "" ]] && [[ "$ENVIRONMENT" != "" ]]; then
    NAMESPACE="cina-club-$ENVIRONMENT"
fi

# Validate required arguments
if [[ "$ENVIRONMENT" == "" ]] && [[ "$NAMESPACE" == "" ]]; then
    log_error "Either environment or namespace is required"
    show_help
    exit 1
fi

# Run main health check
main 