好的，遵照您的指示。我将为您生成一份专门针对 **`user-core-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将基于您提供的`user-core-service-srs.md` (v3.0/v4.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**、**仓储模式**和**工作单元模式**。它将特别关注**高可用性、数据一致性、安全性以及与多个其他服务的协同**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `user-core-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `user-core-service-srs.md` (v4.0)
**核心架构**: 整洁架构 (Clean Architecture)，为高可用和数据一致性优化

## 1. 概述

`user-core-service` 是CINA.CLUB平台**所有用户身份和元数据的唯一权威来源**。其核心挑战在于：
1.  **高可用性与低延迟**: 作为平台最核心、被调用最频繁的服务，任何抖动都会对整个平台产生巨大影响。
2.  **数据强一致性**: 账户状态、认证凭证、角色权限等核心数据必须保证强一致性。
3.  **安全性**: 必须保护用户的密码、PII和会话令牌，防止任何未授权访问。
4.  **复杂的业务逻辑**: 涉及注册、登录、等级计算、会员状态同步、认证审核等多个复杂的业务流程。
5.  **广泛的协同**: 需要与`billing-service`, `notification-dispatch-service`, `social-service`等多个服务进行事件或API交互。

本架构设计通过采用**整洁架构**，并结合**CQRS（命令查询责任分离）思想的轻度应用**和**事件驱动**模式，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 整洁架构分层图

```mermaid
graph TD
    subgraph "外部世界 (Frameworks & Drivers)"
        A[gRPC/REST Server<br/>(adapter/transport)]
        B[PostgreSQL<br/>(adapter/repository)]
        C[Redis Cache<br/>(adapter/cache)]
        D[Kafka Producer/Consumer<br/>(adapter/event)]
        E[3rd-party Clients<br/>(SMS, KYC)]
    end
    
    subgraph "应用层 (Application)"
        F[UserService (Commands)<br/>(application/command)]
        G[UserQueryService (Queries)<br/>(application/query)]
    end
    
    subgraph "领域层 (Domain)"
        H[User, Growth, Membership Aggregates<br/>(domain/model, domain/aggregate)]
        I[Repository & Service Interfaces<br/>(application/port)]
        J[Domain Services & Factories<br/>(domain/service)]
    end
    
    A -- "调用" --> F & G
    F -- "使用" --> I
    G -- "使用" --> I
    B -- "实现" --> I
    C -- "实现" --> I
    D -- "实现/调用" --> F
    E -- "被...调用" --> F

    F & G -- "操作" --> H
    H -- "包含业务规则" --> J
```
_注: 应用层被逻辑上拆分为处理写操作的`Command`服务和处理读操作的`Query`服务，这是CQRS思想的体现。_

### 2.2 最终目录结构 (`services/user-core-service/`)

```
user-core-service/
├── cmd/server/
│   └── main.go                 # 服务启动入口，依赖注入
├── cmd/worker/                 # ✨ 后台任务Worker的独立入口 ✨
│   └── main.go                 # (e.g., for level calculation cronjob)
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_user_cache.go # 实现了UserCache接口
│   │   ├── client/
│   │   │   ├── billing_client.go
│   │   │   └── kyc_client.go
│   │   ├── event/
│   │   │   ├── producer.go     # Kafka生产者
│   │   │   └── consumer.go     # 消费来自billing等服务的事件
│   │   ├── grpc/
│   │   │   ├── server.go
│   │   │   └── handler.go      # gRPC Handler实现
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型
│   │       ├── repo.go         # 实现了所有仓储接口的struct
│   │       └── user_repo.go    # UserRepository实现
│   ├── application/
│   │   ├── port/               # 端口 (Interfaces)
│   │   │   ├── cache.go
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── command/            # ✨ 写操作 (Use Cases) ✨
│   │   │   └── user_command_service.go
│   │   └── query/              # ✨ 读操作 (Use Cases) ✨
│   │       └── user_query_service.go
│   └── domain/
│       ├── aggregate/          # 聚合根
│       │   └── user_aggregate.go # 封装User及其关联对象的业务规则
│       ├── model/
│       │   └── alias.go
│       ├── service/            # 领域服务
│       │   └── password_service.go # 密码哈希与校验
│       └── factory/
│           └── user_factory.go # 创建新用户的工厂
└── ...
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Business Rules)

*   `domain/model/`: 使用`/core/models`中定义的`User`, `UserGrowth`等核心`struct`。
*   **`domain/service/`**:
    *   `password_service.go`: 封装所有与密码相关的逻辑。
        *   `HashPassword(password)`: **必须**使用`Argon2id`。
        *   `CheckPassword(password, hash)`。
        *   这个服务是纯粹的，无状态的。
*   **`domain/factory/`**:
    *   `user_factory.go`: `NewUser(phone, password)`工厂函数。
        *   负责创建符合业务不变量（invariant）的新用户对象。
        *   例如，它会调用`password_service`来哈希密码，并自动生成一个初始的随机`username`。
*   **`domain/aggregate/`**: **这是DDD的核心**。
    *   `user_aggregate.go`: `UserAggregate`结构体。它不只是一个数据容器，它**封装了`User`对象和其所有关联子对象（Profile, Growth, Membership）的状态以及只允许通过其方法进行的、符合业务规则的状态变更**。
        *   `func (a *UserAggregate) UpdateProfile(newProfile)`: 方法内部会检查昵称是否包含非法字符等业务规则。
        *   `func (a *UserAggregate) SuspendAccount(reason)`: 改变用户状态，并记录一个领域事件。
        *   `func (a *UserAggregate) CalculateLevelUp()`: 根据`TotalActiveDays`计算新等级，如果升级，则记录`UserLevelUpEvent`。

### 3.2 `application/` - 应用层 (The Use Cases Orchestrator)

这是对**CQRS(Command Query Responsibility Segregation)思想**的轻度应用，将改变系统状态的**写操作(Command)**和不改变状态的**读操作(Query)**分离开来。

*   **`application/command/`**: **处理所有写请求**。
    *   `user_command_service.go`: 实现了`UserService`接口中所有改变状态的方法，如`Register`, `UpdateProfile`, `ChangePassword`, `SuspendUser`。
    *   **工作流程 (以`UpdateProfile`为例)**:
        1.  开启工作单元（事务）。
        2.  从仓储中获取`UserAggregate`。
        3.  调用`userAggregate.UpdateProfile(newData)`。**业务规则在领域对象内部执行**。
        4.  将变更后的`UserAggregate`交由仓储持久化。
        5.  提交工作单元。
        6.  从`UserAggregate`中收集领域事件，并发布到事件总线。
*   **`application/query/`**: **处理所有读请求**。
    *   `user_query_service.go`: 实现了`UserService`接口中所有只读方法，如`GetUserDetails`, `ListDevices`。
    *   **工作流程 (以`GetUserDetails`为例)**:
        1.  **首先，尝试从缓存(`port.UserCache`)中获取数据**。
        2.  如果缓存未命中：
            a. 从仓储(`port.UserRepository`)中获取数据。
            b. 将数据写入缓存。
        3.  返回数据。
    *   **优点**: 读操作的逻辑非常简单，并且天然地集成了缓存策略，不与复杂的写事务逻辑耦合，性能极高。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   `model.go`: 定义与数据库表完全对应的`struct`，带ORM标签。
    *   `user_repo.go`: 实现了`port.UserRepository`接口。
        *   负责将`domain.UserAggregate`与持久化模型进行转换。
        *   使用`pgx`和工作单元模式执行SQL。
*   **`adapter/cache/`**:
    *   `redis_user_cache.go`: 实现了`port.UserCache`接口。
    *   使用Redis的`HGETALL`/`HSET`或`GET`/`SET`（配合JSON序列化）来缓存`UserDetails`对象。
*   **`adapter/grpc/`**:
    *   `handler.go`: gRPC Handler根据RPC是读操作还是写操作，分别调用`UserQueryService`或`UserCommandService`。
*   **`adapter/event/`**:
    *   `producer.go`: 提供一个`EventProducer`，`UserCommandService`在事务成功后调用它来发布领域事件。
    *   `consumer.go`: 消费来自`billing-service`的`MembershipPurchasedEvent`，并调用`UserCommandService`的`UpdateMembership`方法。

### 3.4 `cmd/worker/` - 后台任务

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**: 专门用于执行定时的后台批处理任务。
*   **核心任务: `UserLevelCalculator`**:
    1.  以**Kubernetes CronJob**的形式每日运行。
    2.  启动时，它会初始化`application`层的服务，但**不启动gRPC服务器**。
    3.  执行逻辑：
        a. 以流式方式从数据库中批量拉取需要更新成长信息的用户（如`last_active_date < TODAY`）。
        b. 对于每个用户，从Redis中获取其昨日的在线时长。
        c. 调用`UserAggregate.UpdateActiveDayAndOnlineBonus(...)`和`userAggregate.CalculateLevelUp()`。
        d. 批量将更新后的`UserAggregate`持久化到数据库。
        e. 批量发布所有产生的`UserLevelUpEvent`事件。
*   **设计决策**: 将批处理任务与在线API服务分离，确保了长时间运行的批处理不会影响在线服务的性能和稳定性。

## 4. 数据库与缓存设计

*   **数据库 (PostgreSQL)**:
    *   表结构如SRS v4.0所定义。
    *   对`users.status`, `user_contacts.phone_number`, `user_contacts.email`等高频查询字段建立索引。
*   **缓存 (Redis)**:
    *   **缓存内容**: 缓存`GetUserDetails`返回的**完整聚合对象**，而不是零散的字段。这减少了缓存的复杂性。
    *   **缓存策略**: **Cache-Aside**。
    *   **失效策略**:
        1.  **TTL**: 为缓存设置一个合理的过期时间（如1小时）。
        2.  **事件驱动失效**: 在`adapter/event`中创建一个**内部消费者**，监听本服务自己发布的`User...ChangedEvent`。当收到事件时，主动删除（invalidate）Redis中对应的缓存Key。这保证了数据在大多数情况下的强一致性。

## 5. 总结

本架构设计通过以下关键点来构建一个生产级的`user-core-service`：
1.  **整洁架构**: 保证了业务逻辑与技术实现的彻底解耦。
2.  **领域聚合**: 使用`UserAggregate`封装了复杂的状态变更和业务规则，保证了领域模型的内聚和一致性。
3.  **CQRS思想**: 将读写操作分离到不同的服务中，使得读路径可以被极限优化（缓存优先），写路径可以专注于事务和数据一致性。
4.  **后台任务分离**: 将资源密集型的批处理任务移至独立的Worker进程，保障了在线API的性能和稳定性。
5.  **事件驱动解耦**: 通过发布领域事件，与其他服务进行低耦合的、可靠的异步通信。

这种架构使得`user-core-service`在功能强大、业务复杂的同时，依然保持了**高度的可测试性、可维护性和可扩展性**，能够作为整个CINA.CLUB平台的坚实用户基石。