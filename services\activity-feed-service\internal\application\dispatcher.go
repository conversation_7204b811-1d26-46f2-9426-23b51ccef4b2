/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package application

import (
	"context"
	"encoding/json"
	"fmt"

	"cina.club/services/activity-feed-service/internal/application/handlers"
	"cina.club/services/activity-feed-service/internal/domain"
)

// Event represents a generic platform event
type Event interface {
	GetEventType() string
	GetEventID() string
	GetUserID() string
	GetPayload() []byte
	GetTimestamp() int64
}

// GenericEvent is a basic implementation of Event interface
type GenericEvent struct {
	EventType string `json:"event_type"`
	EventID   string `json:"event_id"`
	UserID    string `json:"user_id"`
	Payload   []byte `json:"payload"`
	Timestamp int64  `json:"timestamp"`
}

// GetEventType returns the event type
func (e *GenericEvent) GetEventType() string {
	return e.EventType
}

// GetEventID returns the event ID
func (e *GenericEvent) GetEventID() string {
	return e.EventID
}

// GetUserID returns the user ID
func (e *GenericEvent) GetUserID() string {
	return e.UserID
}

// GetPayload returns the event payload
func (e *GenericEvent) GetPayload() []byte {
	return e.Payload
}

// GetTimestamp returns the event timestamp
func (e *GenericEvent) GetTimestamp() int64 {
	return e.Timestamp
}

// Dispatcher handles routing of platform events to appropriate feed handlers
type Dispatcher struct {
	handlerFactory *handlers.Factory
	logger         domain.Logger
	eventHandlers  map[string]string // eventType -> handlerName mapping
}

// NewDispatcher creates a new event dispatcher
func NewDispatcher(handlerFactory *handlers.Factory, logger domain.Logger) *Dispatcher {
	dispatcher := &Dispatcher{
		handlerFactory: handlerFactory,
		logger:         logger,
		eventHandlers:  make(map[string]string),
	}

	// Initialize event-to-handler mappings
	dispatcher.initializeEventMappings()

	return dispatcher
}

// initializeEventMappings sets up the mappings between event types and handlers
func (d *Dispatcher) initializeEventMappings() {
	// Interaction events
	d.eventHandlers["forum.post.liked"] = "interaction"
	d.eventHandlers["forum.post.commented"] = "interaction"
	d.eventHandlers["forum.post.replied"] = "interaction"
	d.eventHandlers["user.followed"] = "interaction"
	d.eventHandlers["user.mentioned"] = "interaction"
	d.eventHandlers["content.shared"] = "interaction"
	d.eventHandlers["service.reviewed"] = "interaction"

	// Notification events
	d.eventHandlers["system.announcement"] = "notification"
	d.eventHandlers["account.security.alert"] = "notification"
	d.eventHandlers["task.status.changed"] = "notification"
	d.eventHandlers["service.booked"] = "notification"
	d.eventHandlers["payment.received"] = "notification"
	d.eventHandlers["payment.failed"] = "notification"
	d.eventHandlers["subscription.expired"] = "notification"
	d.eventHandlers["verification.completed"] = "notification"

	// Following events
	d.eventHandlers["content.published"] = "following"
	d.eventHandlers["user.status.updated"] = "following"
	d.eventHandlers["live.stream.started"] = "following"
	d.eventHandlers["course.released"] = "following"
	d.eventHandlers["product.launched"] = "following"
}

// HandleEvent processes an incoming platform event
func (d *Dispatcher) HandleEvent(ctx context.Context, event domain.Event) error {
	eventType := event.GetEventType()

	d.logger.Debug(ctx, "Dispatching event",
		"event_type", eventType,
		"event_id", event.GetEventID(),
		"user_id", event.GetUserID())

	// Find the appropriate handler for this event type
	handlerName, exists := d.eventHandlers[eventType]
	if !exists {
		d.logger.Warn(ctx, "No handler found for event type",
			"event_type", eventType,
			"event_id", event.GetEventID())
		return nil // Not an error, just skip unhandled events
	}

	// Get the handler from factory
	handler, err := d.handlerFactory.GetHandler(handlerName)
	if err != nil {
		d.logger.Error(ctx, "Failed to get handler",
			"handler_name", handlerName,
			"event_type", eventType,
			"error", err)
		return fmt.Errorf("failed to get handler %s: %w", handlerName, err)
	}

	// Handle the event
	if err := handler.Handle(ctx, event); err != nil {
		d.logger.Error(ctx, "Handler failed to process event",
			"handler_name", handlerName,
			"event_type", eventType,
			"event_id", event.GetEventID(),
			"error", err)
		return fmt.Errorf("handler %s failed to process event %s: %w", handlerName, eventType, err)
	}

	d.logger.Debug(ctx, "Event processed successfully",
		"event_type", eventType,
		"event_id", event.GetEventID(),
		"handler", handlerName)

	return nil
}

// HandleRawEvent processes raw event data (from Kafka consumer)
func (d *Dispatcher) HandleRawEvent(ctx context.Context, eventData []byte) error {
	// Parse the raw event data
	var genericEvent GenericEvent
	if err := json.Unmarshal(eventData, &genericEvent); err != nil {
		d.logger.Error(ctx, "Failed to parse event data", "error", err)
		return fmt.Errorf("failed to parse event data: %w", err)
	}

	// Handle the parsed event
	return d.HandleEvent(ctx, &genericEvent)
}

// AddEventMapping adds a new event type to handler mapping
func (d *Dispatcher) AddEventMapping(eventType, handlerName string) {
	d.eventHandlers[eventType] = handlerName
	d.logger.Info(context.Background(), "Added event mapping",
		"event_type", eventType,
		"handler", handlerName)
}

// RemoveEventMapping removes an event type mapping
func (d *Dispatcher) RemoveEventMapping(eventType string) {
	delete(d.eventHandlers, eventType)
	d.logger.Info(context.Background(), "Removed event mapping",
		"event_type", eventType)
}

// GetEventMappings returns all current event mappings
func (d *Dispatcher) GetEventMappings() map[string]string {
	// Return a copy to prevent external modification
	mappings := make(map[string]string)
	for k, v := range d.eventHandlers {
		mappings[k] = v
	}
	return mappings
}

// GetSupportedEventTypes returns all supported event types
func (d *Dispatcher) GetSupportedEventTypes() []string {
	eventTypes := make([]string, 0, len(d.eventHandlers))
	for eventType := range d.eventHandlers {
		eventTypes = append(eventTypes, eventType)
	}
	return eventTypes
}

// GetDispatcherStats returns dispatcher statistics
func (d *Dispatcher) GetDispatcherStats() map[string]interface{} {
	stats := make(map[string]interface{})
	stats["total_mappings"] = len(d.eventHandlers)

	handlerCount := make(map[string]int)
	for _, handler := range d.eventHandlers {
		handlerCount[handler]++
	}
	stats["events_per_handler"] = handlerCount

	return stats
}
