
### 文件2: `docs/architecture/L2_System_Landscape/02_service_communication_patterns.md`

```markdown
# CINA.CLUB - 服务通信模式

**文档状态**: 已采纳 (Accepted)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27

## 1. 引言

本文档定义了CINA.CLUB平台内部微服务之间，以及微服务与外部世界进行通信的标准模式和协议。遵循这些模式是保证系统**解耦、可靠和可维护**的关键。

---

## 2. 核心通信模式

平台主要采用两种通信模式：**同步请求-响应**和**异步事件驱动**。

### 2.1 同步通信 (Synchronous Communication)

*   **适用场景**:
    *   当调用方需要**立即**得到一个响应才能继续其工作流时。
    *   用于**查询**操作（Read-heavy）。
    *   客户端通过API Gateway对后端的请求。
*   **技术标准**:
    *   **服务间 (S2S)**: **必须**使用 **gRPC**。
    *   **客户端-服务器**: **必须**使用 **RESTful API** (JSON/HTTP)。这些API通过`grpc-gateway`从gRPC服务自动生成，以保证与内部接口的一致性。
*   **优点**: 简单直接，易于理解和调试。
*   **缺点/风险**:
    *   **紧耦合**: 调用方和服务方在时间上是耦合的。如果服务方变慢或不可用，会直接影响调用方。
    *   **降低可用性**: 过长的同步调用链会显著降低整个系统的可用性。
*   **架构原则**: **审慎使用同步调用**。对于任何非即时必要的跨服务操作，都应优先考虑异步模式。

### 2.2 异步通信 (Asynchronous Communication)

*   **适用场景**:
    *   当一个服务的状态变更需要**通知**其他一个或多个服务时。
    *   用于执行耗时、可延迟的后台任务（如视频转码、发送邮件）。
    *   用于Saga分布式事务的步骤协调。
*   **技术标准**:
    *   **事件总线**: **必须**使用 **Apache Kafka**。
    *   **事件格式**: 消息体**必须**是使用`/core/api`中定义的`.proto`文件序列化后的二进制数据。
*   **优点**:
    *   **解耦**: 服务间无直接依赖。
    *   **韧性**: 消费者服务宕机不影响生产者。
    *   **可扩展性**: 可通过增加消费者实例来并行处理事件。
*   **缺点**:
    *   **最终一致性**: 数据的最终一致性需要时间。
    *   **复杂性**: 需要处理消息重投、顺序性、死信队列等问题。

---

## 3. 通信协议与契约

*   **API契约**: 所有gRPC服务和Kafka事件的结构**必须**在`/core/api`目录中通过**Protocol Buffers v3**进行定义。这是平台通信的**唯一事实来源**。
*   **服务发现**: 在Kubernetes环境中，gRPC客户端通过标准的DNS解析`<service-name>.<namespace>.svc.cluster.local`来发现服务。
*   **网络安全**:
    *   所有gRPC S2S通信**必须**启用**mTLS**进行双向认证。
    *   所有对外的RESTful API**必须**通过`api-gateway`暴露，并强制使用**TLS 1.3**。
```
