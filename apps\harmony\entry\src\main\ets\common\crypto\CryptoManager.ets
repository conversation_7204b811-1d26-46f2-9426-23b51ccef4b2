/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { hilog } from '@kit.PerformanceAnalysisKit';
import { CoreGoBridge } from '../core/CoreGoBridge';

/**
 * 加密管理器
 * 
 * 提供高级的加密解密功能，是Go核心加密库的ArkTS封装
 */
export class CryptoManager {
  private static readonly TAG = 'CryptoManager';
  
  private goBridge: CoreGoBridge;

  constructor() {
    this.goBridge = CoreGoBridge.getInstance();
  }

  /**
   * 生成随机盐值
   */
  generateSalt(): ArrayBuffer {
    const salt = new Uint8Array(32); // 256位盐值
    for (let i = 0; i < salt.length; i++) {
      salt[i] = Math.floor(Math.random() * 256);
    }
    return salt.buffer;
  }

  /**
   * 从密码派生密钥
   */
  async deriveKeyFromPassword(password: string, salt?: ArrayBuffer): Promise<ArrayBuffer> {
    try {
      const saltBuffer = salt || this.generateSalt();
      const derivedKey = this.goBridge.deriveKey(password, saltBuffer);
      
      hilog.debug(0x0000, CryptoManager.TAG, 'Key derived from password successfully');
      return derivedKey;
    } catch (error) {
      hilog.error(0x0000, CryptoManager.TAG, `Derive key failed: ${error.message}`);
      throw new Error(`密钥派生失败: ${error.message}`);
    }
  }

  /**
   * 对称加密
   */
  async encryptData(key: ArrayBuffer, data: string | ArrayBuffer): Promise<ArrayBuffer> {
    try {
      let dataBuffer: ArrayBuffer;
      
      if (typeof data === 'string') {
        // 将字符串转换为ArrayBuffer
        const encoder = new TextEncoder();
        dataBuffer = encoder.encode(data).buffer;
      } else {
        dataBuffer = data;
      }

      const encrypted = await this.goBridge.encryptSymmetric(key, dataBuffer);
      
      hilog.debug(0x0000, CryptoManager.TAG, 'Data encrypted successfully');
      return encrypted;
    } catch (error) {
      hilog.error(0x0000, CryptoManager.TAG, `Encrypt data failed: ${error.message}`);
      throw new Error(`数据加密失败: ${error.message}`);
    }
  }

  /**
   * 对称解密
   */
  async decryptData(key: ArrayBuffer, encryptedData: ArrayBuffer): Promise<ArrayBuffer> {
    try {
      const decrypted = await this.goBridge.decryptSymmetric(key, encryptedData);
      
      hilog.debug(0x0000, CryptoManager.TAG, 'Data decrypted successfully');
      return decrypted;
    } catch (error) {
      hilog.error(0x0000, CryptoManager.TAG, `Decrypt data failed: ${error.message}`);
      throw new Error(`数据解密失败: ${error.message}`);
    }
  }

  /**
   * 加密字符串并返回Base64编码的结果
   */
  async encryptString(key: ArrayBuffer, plaintext: string): Promise<string> {
    try {
      const encrypted = await this.encryptData(key, plaintext);
      
      // 转换为Base64字符串
      const uint8Array = new Uint8Array(encrypted);
      let binaryString = '';
      for (let i = 0; i < uint8Array.length; i++) {
        binaryString += String.fromCharCode(uint8Array[i]);
      }
      
      const base64 = btoa(binaryString);
      hilog.debug(0x0000, CryptoManager.TAG, 'String encrypted and encoded to Base64');
      return base64;
    } catch (error) {
      hilog.error(0x0000, CryptoManager.TAG, `Encrypt string failed: ${error.message}`);
      throw new Error(`字符串加密失败: ${error.message}`);
    }
  }

  /**
   * 解密Base64编码的字符串
   */
  async decryptString(key: ArrayBuffer, encryptedBase64: string): Promise<string> {
    try {
      // 从Base64解码
      const binaryString = atob(encryptedBase64);
      const uint8Array = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
      }
      
      const decrypted = await this.decryptData(key, uint8Array.buffer);
      
      // 转换为字符串
      const decoder = new TextDecoder();
      const plaintext = decoder.decode(decrypted);
      
      hilog.debug(0x0000, CryptoManager.TAG, 'Base64 string decrypted successfully');
      return plaintext;
    } catch (error) {
      hilog.error(0x0000, CryptoManager.TAG, `Decrypt string failed: ${error.message}`);
      throw new Error(`字符串解密失败: ${error.message}`);
    }
  }

  /**
   * 生成加密的数据包（包含盐值和加密数据）
   */
  async createSecurePackage(password: string, data: string): Promise<string> {
    try {
      // 生成盐值
      const salt = this.generateSalt();
      
      // 从密码派生密钥
      const key = await this.deriveKeyFromPassword(password, salt);
      
      // 加密数据
      const encrypted = await this.encryptData(key, data);
      
      // 创建包含盐值和加密数据的包
      const saltArray = new Uint8Array(salt);
      const encryptedArray = new Uint8Array(encrypted);
      
      // 组合：盐值长度(4字节) + 盐值 + 加密数据
      const packageSize = 4 + saltArray.length + encryptedArray.length;
      const packageArray = new Uint8Array(packageSize);
      
      // 写入盐值长度
      const view = new DataView(packageArray.buffer);
      view.setUint32(0, saltArray.length, false);
      
      // 写入盐值
      packageArray.set(saltArray, 4);
      
      // 写入加密数据
      packageArray.set(encryptedArray, 4 + saltArray.length);
      
      // 转换为Base64
      let binaryString = '';
      for (let i = 0; i < packageArray.length; i++) {
        binaryString += String.fromCharCode(packageArray[i]);
      }
      
      const base64Package = btoa(binaryString);
      hilog.debug(0x0000, CryptoManager.TAG, 'Secure package created successfully');
      return base64Package;
    } catch (error) {
      hilog.error(0x0000, CryptoManager.TAG, `Create secure package failed: ${error.message}`);
      throw new Error(`创建安全包失败: ${error.message}`);
    }
  }

  /**
   * 解密安全数据包
   */
  async decryptSecurePackage(password: string, packageBase64: string): Promise<string> {
    try {
      // 从Base64解码
      const binaryString = atob(packageBase64);
      const packageArray = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        packageArray[i] = binaryString.charCodeAt(i);
      }
      
      // 读取盐值长度
      const view = new DataView(packageArray.buffer);
      const saltLength = view.getUint32(0, false);
      
      // 提取盐值
      const salt = packageArray.slice(4, 4 + saltLength).buffer;
      
      // 提取加密数据
      const encrypted = packageArray.slice(4 + saltLength).buffer;
      
      // 从密码派生密钥
      const key = await this.deriveKeyFromPassword(password, salt);
      
      // 解密数据
      const decrypted = await this.decryptData(key, encrypted);
      
      // 转换为字符串
      const decoder = new TextDecoder();
      const plaintext = decoder.decode(decrypted);
      
      hilog.debug(0x0000, CryptoManager.TAG, 'Secure package decrypted successfully');
      return plaintext;
    } catch (error) {
      hilog.error(0x0000, CryptoManager.TAG, `Decrypt secure package failed: ${error.message}`);
      throw new Error(`解密安全包失败: ${error.message}`);
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 清理敏感内存数据等
    hilog.info(0x0000, CryptoManager.TAG, 'CryptoManager cleaned up');
  }
} 