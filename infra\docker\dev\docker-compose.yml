# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB 本地开发环境
# 一键启动完整的微服务开发环境

version: '3.8'

# 共享网络
networks:
  cina-dev:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 共享卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  kafka_data:
    driver: local
  elasticsearch_data:
    driver: local
  minio_data:
    driver: local

services:
  # ===========================================
  # 基础设施服务
  # ===========================================
  
  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: cina-postgres
    hostname: postgres
    environment:
      POSTGRES_DB: cinaclub_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_MULTIPLE_DATABASES: >
        user_core_db,billing_db,memory_db,personal_kb_db,
        community_forum_db,community_qa_db,payment_db,
        service_offering_db,review_db,analytics_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/postgres-init.sh:/docker-entrypoint-initdb.d/postgres-init.sh:ro
    networks:
      - cina-dev
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d cinaclub_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis 缓存和会话存储
  redis:
    image: redis:7-alpine
    container_name: cina-redis
    hostname: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    networks:
      - cina-dev
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cina-zookeeper
    hostname: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cina-dev
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cina-kafka
    hostname: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - cina-dev
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server=localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # Kafka UI (管理界面)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cina-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: cina-dev
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - cina-dev
    restart: unless-stopped

  # Elasticsearch (搜索引擎)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: cina-elasticsearch
    hostname: elasticsearch
    environment:
      - node.name=elasticsearch
      - cluster.name=cina-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - cina-dev
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # MinIO (S3 兼容对象存储)
  minio:
    image: minio/minio:latest
    container_name: cina-minio
    hostname: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - cina-dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped

  # MinIO Client (用于初始化存储桶)
  minio-client:
    image: minio/mc:latest
    container_name: cina-minio-client
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      sleep 10;
      /usr/bin/mc alias set minio http://minio:9000 minioadmin minioadmin123;
      /usr/bin/mc mb minio/cina-files --ignore-existing;
      /usr/bin/mc mb minio/cina-avatars --ignore-existing;
      /usr/bin/mc mb minio/cina-videos --ignore-existing;
      /usr/bin/mc policy set public minio/cina-files;
      /usr/bin/mc policy set public minio/cina-avatars;
      /usr/bin/mc policy set public minio/cina-videos;
      exit 0;
      "
    networks:
      - cina-dev

  # ===========================================
  # 监控与可观测性
  # ===========================================

  # Prometheus (监控)
  prometheus:
    image: prom/prometheus:latest
    container_name: cina-prometheus
    hostname: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cina-dev
    restart: unless-stopped

  # Grafana (仪表板)
  grafana:
    image: grafana/grafana:latest
    container_name: cina-grafana
    hostname: grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    volumes:
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - cina-dev
    restart: unless-stopped

  # Jaeger (分布式追踪)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: cina-jaeger
    hostname: jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - cina-dev
    restart: unless-stopped

  # ===========================================
  # 开发工具
  # ===========================================

  # Adminer (数据库管理界面)
  adminer:
    image: adminer:latest
    container_name: cina-adminer
    hostname: adminer
    ports:
      - "8081:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    networks:
      - cina-dev
    restart: unless-stopped

  # MailHog (邮件测试工具)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: cina-mailhog
    hostname: mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - cina-dev
    restart: unless-stopped

  # ===========================================
  # CINA.CLUB 微服务
  # ===========================================

  # API Gateway (如果需要本地测试)
  # api-gateway:
  #   build:
  #     context: ../../
  #     dockerfile: services/api-gateway/Dockerfile
  #   container_name: cina-api-gateway
  #   hostname: api-gateway
  #   ports:
  #     - "8000:8000"
  #   environment:
  #     - DATABASE_URL=******************************************/cinaclub_dev?sslmode=disable
  #     - REDIS_URL=redis://redis:6379
  #     - KAFKA_BROKERS=kafka:29092
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #     kafka:
  #       condition: service_healthy
  #   networks:
  #     - cina-dev
  #   volumes:
  #     - ../../services/api-gateway:/app
  #   restart: unless-stopped

  # 用户核心服务示例
  # user-core-service:
  #   build:
  #     context: ../../
  #     dockerfile: services/user-core-service/Dockerfile
  #   container_name: cina-user-core
  #   hostname: user-core-service
  #   ports:
  #     - "8001:8080"
  #   environment:
  #     - DATABASE_URL=******************************************/user_core_db?sslmode=disable
  #     - REDIS_URL=redis://redis:6379
  #     - KAFKA_BROKERS=kafka:29092
  #     - LOG_LEVEL=debug
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #     kafka:
  #       condition: service_healthy
  #   networks:
  #     - cina-dev
  #   volumes:
  #     - ../../services/user-core-service:/app
  #   restart: unless-stopped 