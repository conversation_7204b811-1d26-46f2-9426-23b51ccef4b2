# Update go_package in proto files
$PROTO_DIR = "core/api/proto/v1"

Get-ChildItem -Path $PROTO_DIR -Filter *.proto | ForEach-Object {
    $content = Get-Content $_.FullName
    $updatedContent = $content -replace 'option go_package = ".*";', 'option go_package = "cina.club/core/api/proto/v1;pb";'
    Set-Content -Path $_.FullName -Value $updatedContent
}

Write-Host "Updated go_package for all proto files in $PROTO_DIR" 