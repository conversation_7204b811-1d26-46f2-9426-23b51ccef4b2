/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.core.gobridge

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * Main bridge between Kotlin and Go core logic.
 * Provides access to encryption, AI, and data synchronization functionality.
 */
object GoCore {
    
    private var isInitialized = false
    
    init {
        try {
            System.loadLibrary("core-go")
            Timber.d("Go core library loaded successfully")
        } catch (e: UnsatisfiedLinkError) {
            Timber.e(e, "Failed to load Go core library")
        }
    }
    
    /**
     * Initialize the Go core bridge.
     */
    fun initialize(): Boolean {
        return try {
            if (!isInitialized) {
                nativeInit()
                isInitialized = true
                Timber.d("Go core initialized successfully")
            }
            true
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize Go core")
            false
        }
    }
    
    /**
     * Cleanup Go core resources.
     */
    fun cleanup() {
        if (isInitialized) {
            try {
                nativeCleanup()
                isInitialized = false
                Timber.d("Go core cleaned up successfully")
            } catch (e: Exception) {
                Timber.e(e, "Failed to cleanup Go core")
            }
        }
    }
    
    // Crypto Operations
    
    /**
     * Derive master encryption key from password.
     */
    suspend fun deriveMasterKey(password: String, salt: ByteArray): ByteArray = 
        withContext(Dispatchers.Default) {
            nativeDeriveMasterKey(password, salt)
        }
    
    /**
     * Encrypt data using AES-GCM.
     */
    suspend fun encryptData(key: ByteArray, plaintext: ByteArray): ByteArray = 
        withContext(Dispatchers.Default) {
            nativeEncryptData(key, plaintext)
        }
    
    /**
     * Decrypt data using AES-GCM.
     */
    suspend fun decryptData(key: ByteArray, ciphertext: ByteArray): ByteArray = 
        withContext(Dispatchers.Default) {
            nativeDecryptData(key, ciphertext)
        }
    
    // AI Operations
    
    /**
     * Initialize AI model with given path.
     */
    suspend fun initAIModel(modelPath: String): Boolean = 
        withContext(Dispatchers.Default) {
            nativeInitAIModel(modelPath)
        }
    
    /**
     * Generate AI response stream.
     */
    fun generateAIResponse(prompt: String): Flow<String> = flow {
        val callback = object : AIResponseCallback {
            override fun onToken(token: String) {
                // This will be called from the Go side through JNI
                // The actual emission will be handled by the native implementation
            }
            
            override fun onComplete() {
                // Generation complete
            }
            
            override fun onError(error: String) {
                throw Exception(error)
            }
        }
        
        nativeGenerateAIResponse(prompt, callback)
    }.flowOn(Dispatchers.Default)
    
    // Data Sync Operations
    
    /**
     * Sync data with remote server.
     */
    suspend fun syncData(userId: String, authToken: String): Boolean = 
        withContext(Dispatchers.Default) {
            nativeSyncData(userId, authToken)
        }
    
    /**
     * Push local changes to server.
     */
    suspend fun pushChanges(changes: ByteArray): Boolean = 
        withContext(Dispatchers.Default) {
            nativePushChanges(changes)
        }
    
    /**
     * Pull remote changes from server.
     */
    suspend fun pullChanges(): ByteArray = 
        withContext(Dispatchers.Default) {
            nativePullChanges()
        }
    
    // Native method declarations
    
    private external fun nativeInit(): Boolean
    private external fun nativeCleanup()
    
    // Crypto native methods
    private external fun nativeDeriveMasterKey(password: String, salt: ByteArray): ByteArray
    private external fun nativeEncryptData(key: ByteArray, plaintext: ByteArray): ByteArray
    private external fun nativeDecryptData(key: ByteArray, ciphertext: ByteArray): ByteArray
    
    // AI native methods
    private external fun nativeInitAIModel(modelPath: String): Boolean
    private external fun nativeGenerateAIResponse(prompt: String, callback: AIResponseCallback)
    
    // Data sync native methods
    private external fun nativeSyncData(userId: String, authToken: String): Boolean
    private external fun nativePushChanges(changes: ByteArray): Boolean
    private external fun nativePullChanges(): ByteArray
}

/**
 * Callback interface for AI response streaming.
 */
interface AIResponseCallback {
    fun onToken(token: String)
    fun onComplete()
    fun onError(error: String)
} 