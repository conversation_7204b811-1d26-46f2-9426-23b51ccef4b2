好的，遵照您的指示。我将为您生成一份专门针对 **`embedding-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`embedding-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将特别关注**多模型/多提供商的适配与智能路由、故障降级与熔断机制、用量追踪与成本控制，以及作为一个无状态计算代理的高性能和高可用性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `embedding-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `embedding-service-srs.md` (v2.0)
**核心架构**: 策略模式(Strategy Pattern) + 适配器模式(Adapter Pattern) + 熔断器(Circuit Breaker)

## 1. 概述

`embedding-service` 是CINA.CLUB AI基础设施的“**通用语义翻译器**”。它是一个高度专业化的**计算代理服务**。其核心挑战在于：
1.  **异构模型抽象**: 需要将不同提供商（OpenAI, Google, ...）、不同模态（文本、图像）的嵌入模型API，抽象成一个统一的内部接口。
2.  **高可用性与容错**: 外部模型API可能会变慢或不可用。服务必须能够在这种情况下保持可用，并优雅地降级。
3.  **成本与性能的平衡**: 不同的嵌入模型在成本、性能和质量上各有优劣。服务需要能够根据请求的上下文，智能地选择最合适的模型。
4.  **高吞吐量**: 平台多个服务会产生大量的嵌入请求，本服务必须能高效地处理批量和并发请求。
5.  **无状态**: 作为一个纯粹的计算代理，服务本身不应存储任何持久化状态，以便于水平扩展。

本架构设计通过采用**策略模式**来封装不同模型的调用逻辑，并结合**熔断器模式**实现故障降级，构建一个高可用、经济高效的语义编码中心。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (智能路由与故障降级)

```mermaid
graph TD
    subgraph "调用方服务"
        style "调用方服务" fill:#eee
        Requester[e.g., search-service]
    end

    subgraph "EmbeddingService"
        style EmbeddingService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[EmbeddingService<br/><em>application/service</em>]
        C{ModelRouter<br/><em>application/router</em>}
        D{EmbeddingProviderStrategy<br/><em>(interface)</em>}
        E[Provider Implementations<br/>(OpenAIAdapter, GoogleAdapter, ...)<br/><em>adapter/provider</em>]
        F[Circuit Breaker<br/>(per provider)]
        G[Redis Cache<br/><em>adapter/cache</em>]
        H[UsageTracker<br/><em>domain/service</em>]
    end

    subgraph "外部模型API"
        style "外部模型API" fill:#f3e5f5
        P1[OpenAI API]
        P2[Google AI Platform]
        P3[Self-hosted Model API]
    end

    Requester -- "1. POST /embed" --> A
    A -- "调用" --> B
    
    B -- "2. Check Cache" --> G
    B -- "3. Select Model via Router" --> C
    C -- "Returns Provider Strategy" --> D
    
    B -- "4. Get Provider Impl" --> E
    E -- "Wraps call with" --> F
    F -- "5. Call External API" --> P1
    
    P1 -- "Result" --> F
    F --> E --> B
    
    B -- "6. Cache Result" --> G
    B -- "7. Track Usage" --> H
    
    B -- "8. Return Result" --> A
    
    subgraph "故障降级"
        F -- "If P1 fails" --> F
        note over F: "Circuit Breaker for P1 Opens!"
        F -- "Fallback Request" --> B
        B -- "Select Fallback via Router" --> C
        C -- "Returns P3 Strategy" --> D
        B -- "Call Fallback" --> E
        E -- "Call" --> P3
    end
```

### 2.2 最终目录结构 (`services/embedding-service/`)

```
embedding-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_cache.go  # 实现了Cache接口
│   │   ├── grpc/
│   │   │   └── handler.go      # gRPC Handler实现
│   │   └── provider/           # ✨ 模型提供商的适配器实现 ✨
│   │       ├── interface.go    # 定义EmbeddingProvider接口
│   │       ├── openai_adapter.go
│   │       ├── google_adapter.go
│   │       └── self_hosted_adapter.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── cache.go
│   │   │   └── provider.go     # (可选) 接口定义也可在此
│   │   ├── router/
│   │   │   └── model_router.go # ✨ 智能路由核心逻辑 ✨
│   │   └── service/
│   │       └── embedding_service.go # 核心应用服务实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── usage_tracker.go # 用量追踪服务
├── config/
│   └── models.yaml             # ✨ 模型目录与路由规则配置文件 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/models.yaml` - 模型与路由的“单一事实来源”

这是本服务灵活性的关键。所有模型信息和路由规则都通过配置文件定义，无需修改代码。
```yaml
models:
  - name: "openai-text-embedding-3-large"
    provider: "openai"
    modality: "text"
    dimension: 3072
    cost_per_1k_tokens: 0.00013
    tags: ["high_quality", "search_document"]
  - name: "google-text-embedding-004"
    provider: "google"
    modality: "text"
    dimension: 768
    cost_per_1k_tokens: 0.00002
    tags: ["low_cost", "classification"]
  # ...

routing_rules:
  - when:
      task_type: "search_document"
      quality_preference: "high_quality"
    use: "openai-text-embedding-3-large"
    fallback: "google-text-embedding-004"
  - when:
      task_type: "classification"
    use: "google-text-embedding-004"
    fallback: "self-hosted-bge-m3"
  - default:
      use: "google-text-embedding-004"
      fallback: "self-hosted-bge-m3"
```

### 3.2 `application/` - 应用层 (The Orchestrator & Router)

*   **`application/router/model_router.go`**:
    *   **`ModelRouter` struct**: 在启动时加载并解析`config/models.yaml`。
    *   **`SelectModel(taskType, qualityPref, ...)` method**:
        1.  遍历`routing_rules`。
        2.  找到第一条匹配当前请求上下文的规则。
        3.  返回规则中定义的`use`模型名称和`fallback`模型名称。
        4.  如果没有匹配的规则，则返回`default`规则。
*   **`application/service/embedding_service.go`**:
    *   **`EmbeddingService` struct**: 注入`ModelRouter`和所有`Provider`适配器的实例。
    *   **`Embed(ctx, request)` method**:
        1.  **缓存检查**: 为请求中的每个输入项，构造缓存key（如`hash(content)+model_name`），并尝试从`cache`获取。
        2.  对于所有缓存未命中的输入项：
            a. 调用`modelRouter.SelectModel()`获取主模型和备用模型。
            b. **核心调用**: 调用一个`callProviderWithFallback`的私有方法。
        3.  合并缓存结果和新生成的结果。
        4.  调用`usageTracker.Record(...)`记录本次API调用的token消耗。
        5.  将新生成的结果异步写入缓存。
        6.  返回最终结果。
    *   **`callProviderWithFallback(ctx, providerName, fallbackName, inputs)`**:
        a. 获取`providerName`对应的适配器实例。
        b. **使用熔断器(Circuit Breaker)**包装对该适配器`Embed`方法的调用。
        c. **如果调用成功**: 返回结果。
        d. **如果调用失败（且熔断器打开）**: 记录失败，然后递归或迭代地调用自身，但这次使用`fallbackName`作为`providerName`。如果备用模型也失败，则返回最终错误。

### 3.3 `adapter/` - 适配层 (The Bridge to External Models)

*   **`adapter/provider/`**: **策略模式+适配器模式的实现**。
    *   `interface.go`: 定义`EmbeddingProvider`接口。
        ```go
        type EmbeddingProvider interface {
            Embed(ctx context.Context, inputs []string, modelName string) ([][]float32, error)
            // ... for images, etc.
            GetTokenCount(texts []string) int
        }
        ```
    *   **`openai_adapter.go`, `google_adapter.go`, ...**:
        *   每个文件实现`EmbeddingProvider`接口。
        *   它们封装了对具体外部API的HTTP客户端调用、认证（API Key处理）、以及错误格式的转换。
        *   **熔断器集成**: 每个适配器实例在创建时，都应关联一个独立的熔断器实例（如使用`sony/gobreaker`）。
            ```go
            func (a *OpenAIAdapter) Embed(...) ([][]float32, error) {
                result, err := a.circuitBreaker.Execute(func() (interface{}, error) {
                    // ... 实际的HTTP API调用 ...
                    return response, err
                })
                // ...
            }
            ```
*   **`adapter/cache/`**:
    *   `redis_cache.go`: 实现`port.Cache`接口，使用Redis的`MGET`/`MSET`来高效地批量处理缓存读写。

### 3.4 `domain/` - 领域层 (The Business Primitives)

*   `domain/service/usage_tracker.go`:
    *   **`UsageTracker`**: 负责记录用量。
    *   **`Record(callingService, model, tokens)`**:
        *   可以将用量数据写入一个**Kafka Topic (`embedding-usage-events`)**，供`analytics-service`进行后续的成本分析和分摊。
        *   或者，对于简单的限额，可以直接更新Redis中的计数器。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`embedding-service`：
1.  **配置驱动的灵活性**: 通过外部化的YAML文件来管理模型目录和路由规则，使得添加新模型或调整策略无需修改代码，极大地提升了运营效率。
2.  **策略与适配器模式**: 清晰地将不同模型提供商的API调用逻辑封装在各自的适配器中，实现了与核心业务逻辑的完全解耦和高度的可扩展性。
3.  **熔断器实现高可用**: 为每个外部依赖都配备了独立的熔断器，确保在某个提供商故障时，服务能够自动、快速地降级到备用方案，保证了整个服务的高可用性。
4.  **全面的性能与成本优化**: 通过批量处理、结果缓存和智能路由，在保证服务质量的同时，最大限度地提升了性能并控制了API调用成本。
5.  **无状态设计**: 服务本身不存储任何状态（除了配置和缓存），可以无限水平扩展以应对高并发请求。

这种架构使得`embedding-service`成为了一个健壮、智能、经济高效的AI基础设施核心，能够可靠地支撑CINA.CLUB平台所有需要语义理解能力的业务场景。