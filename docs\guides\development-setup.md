# 开发环境设置指南

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

本文档提供CINA.CLUB项目完整的开发环境设置指南，包括所有必要的工具、依赖和配置步骤。

## 系统要求

### 硬件要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| **CPU** | 4核心 | 8核心以上 |
| **内存** | 8GB | 16GB以上 |
| **存储** | 50GB可用空间 | 100GB SSD |
| **网络** | 稳定的互联网连接 | 带宽不低于50Mbps |

### 操作系统支持

- **Windows**: Windows 10/11 (推荐WSL2)
- **macOS**: macOS 12.0 或更高版本
- **Linux**: Ubuntu 20.04+, CentOS 8+, 或其他现代发行版

## 核心工具安装

### 1. Git 版本控制

```bash
# Ubuntu/Debian
sudo apt update && sudo apt install git

# macOS (使用Homebrew)
brew install git

# Windows (推荐使用Git for Windows)
# 下载并安装: https://git-scm.com/download/win

# 验证安装
git --version
```

**配置Git：**
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main
```

### 2. Go 开发环境

**安装Go 1.22+：**
```bash
# 下载并安装Go (Linux/macOS)
wget https://go.dev/dl/go1.22.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.22.0.linux-amd64.tar.gz

# 配置环境变量 (添加到 ~/.bashrc 或 ~/.zshrc)
export PATH=$PATH:/usr/local/go/bin
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin

# Windows用户建议使用官方安装包
# 下载地址: https://go.dev/dl/

# 验证安装
go version
```

**安装Go工具：**
```bash
# 代码格式化工具
go install golang.org/x/tools/cmd/goimports@latest

# 代码检查工具
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 依赖管理工具
go install github.com/golang/dep/cmd/dep@latest

# 测试覆盖率工具
go install github.com/axw/gocov/gocov@latest
```

### 3. Node.js 和 npm

**安装Node.js 18+：**
```bash
# 使用NodeSource仓库 (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# macOS使用Homebrew
brew install node

# 或者使用NVM (推荐)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 验证安装
node --version
npm --version
```

**安装全局工具：**
```bash
# TypeScript编译器
npm install -g typescript

# 代码格式化工具
npm install -g prettier

# 代码检查工具
npm install -g eslint

# 包管理工具
npm install -g yarn pnpm
```

### 4. Docker 和 Docker Compose

**安装Docker：**
```bash
# Ubuntu
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# macOS
# 下载Docker Desktop: https://www.docker.com/products/docker-desktop/

# 验证安装
docker --version
docker-compose --version
```

### 5. Kubernetes 工具

**安装kubectl：**
```bash
# Linux
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# macOS
brew install kubectl

# Windows
choco install kubernetes-cli

# 验证安装
kubectl version --client
```

**安装开发工具：**
```bash
# Helm包管理器
curl https://get.helm.sh/helm-v3.12.0-linux-amd64.tar.gz | tar xz
sudo mv linux-amd64/helm /usr/local/bin/helm

# K9s集群管理工具
brew install k9s  # macOS
# 或从GitHub下载: https://github.com/derailed/k9s/releases

# Minikube本地开发
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
sudo install minikube-linux-amd64 /usr/local/bin/minikube
```

## 移动端开发环境

### Android 开发

**安装Android Studio：**
1. 下载Android Studio: https://developer.android.com/studio
2. 安装Android SDK Platform-Tools
3. 配置环境变量：

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

**安装必要的SDK：**
```bash
# 通过sdkmanager安装
sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
sdkmanager "system-images;android-34;google_apis;x86_64"
```

### iOS/macOS 开发 (仅限macOS)

**安装Xcode：**
```bash
# 从App Store安装Xcode
# 或下载Xcode Command Line Tools
xcode-select --install

# 验证安装
xcodebuild -version
```

### HarmonyOS 开发

**安装DevEco Studio：**
1. 下载地址: https://developer.harmonyos.com/cn/develop/deveco-studio
2. 安装HarmonyOS SDK
3. 配置模拟器或真机调试

### Windows 开发

**安装Visual Studio：**
1. 下载Visual Studio 2022: https://visualstudio.microsoft.com/
2. 安装工作负载：
   - .NET桌面开发
   - 通用Windows平台开发
   - C++桌面开发

## 项目设置

### 1. 克隆项目

```bash
# 克隆主仓库
git clone https://github.com/cina-club/monorepo.git
cd monorepo

# 初始化子模块
git submodule update --init --recursive
```

### 2. 环境配置

**复制环境配置文件：**
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑配置文件
nano .env.local
```

**环境变量配置示例：**
```bash
# 开发环境配置
ENVIRONMENT=development
DEBUG=true

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/cina_club_dev
REDIS_URL=redis://localhost:6379

# API配置
API_BASE_URL=http://localhost:8080
WS_BASE_URL=ws://localhost:8080

# 认证配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# 第三方服务
OPENAI_API_KEY=your-openai-api-key
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
```

### 3. 依赖安装

**后端依赖：**
```bash
# 安装Go依赖
cd core
go mod download
go mod tidy

# 安装各服务依赖
cd ../services/user-core-service
go mod download

# 安装共享包依赖
cd ../../pkg
go mod download
```

**前端依赖：**
```bash
# Web应用依赖
cd apps/admin
npm install

# 移动端依赖
cd ../android
./gradlew build

cd ../apple
swift package resolve

cd ../harmony
ohpm install
```

### 4. 数据库设置

**启动本地数据库：**
```bash
# 使用Docker Compose启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 等待服务启动
sleep 30

# 运行数据库迁移
make db-migrate

# 初始化测试数据
make db-seed
```

**手动数据库设置：**
```bash
# PostgreSQL
sudo apt install postgresql postgresql-contrib
sudo -u postgres createuser --interactive
sudo -u postgres createdb cina_club_dev

# Redis
sudo apt install redis-server
sudo systemctl start redis-server
```

## 开发工具配置

### 1. IDE配置

**VS Code推荐扩展：**
```json
{
  "recommendations": [
    "golang.Go",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "ms-kubernetes-tools.vscode-kubernetes-tools",
    "ms-vscode.vscode-docker",
    "GitHub.copilot",
    "ms-vscode.vscode-json"
  ]
}
```

**VS Code工作区配置：**
```json
{
  "folders": [
    {"name": "Root", "path": "."},
    {"name": "Core", "path": "./core"},
    {"name": "Services", "path": "./services"},
    {"name": "Packages", "path": "./pkg"},
    {"name": "Apps", "path": "./apps"}
  ],
  "settings": {
    "go.useLanguageServer": true,
    "go.lintTool": "golangci-lint",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": true
    }
  }
}
```

### 2. Git 配置

**设置Git钩子：**
```bash
# 安装pre-commit工具
pip install pre-commit

# 安装项目钩子
pre-commit install

# 手动运行检查
pre-commit run --all-files
```

**Git钩子配置 (.pre-commit-config.yaml)：**
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/golangci/golangci-lint
    rev: v1.54.0
    hooks:
      - id: golangci-lint

  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        files: \.(js|ts|tsx)$
```

## 验证安装

### 1. 运行测试

```bash
# 运行所有测试
make test

# 运行后端测试
make test-backend

# 运行前端测试
make test-frontend

# 运行集成测试
make test-integration
```

### 2. 启动开发服务器

```bash
# 启动后端服务
make dev-backend

# 启动前端服务
make dev-frontend

# 启动所有服务
make dev
```

### 3. 健康检查

**检查服务状态：**
```bash
# 检查API健康状态
curl http://localhost:8080/health

# 检查数据库连接
make db-ping

# 检查Redis连接
redis-cli ping
```

**访问开发服务：**
- **API文档**: http://localhost:8080/swagger
- **管理后台**: http://localhost:3000
- **监控面板**: http://localhost:3001

## 常见问题

### 端口冲突

如果遇到端口占用问题：
```bash
# 检查端口占用
lsof -i :8080
netstat -tulpn | grep :8080

# 杀死占用进程
kill -9 <PID>
```

### 权限问题

```bash
# Docker权限问题
sudo usermod -aG docker $USER
newgrp docker

# 文件权限问题
sudo chown -R $USER:$USER .
```

### 网络问题

```bash
# 清理Docker网络
docker system prune -f
docker network prune -f

# 重置DNS
sudo systemctl restart systemd-resolved
```

## 开发工作流

### 1. 日常开发流程

```bash
# 1. 更新代码
git pull origin main

# 2. 创建功能分支
git checkout -b feature/new-feature

# 3. 启动开发环境
make dev

# 4. 进行开发工作
# ...

# 5. 运行测试
make test

# 6. 提交代码
git add .
git commit -m "feat: add new feature"

# 7. 推送分支
git push origin feature/new-feature

# 8. 创建Pull Request
```

### 2. 代码质量检查

```bash
# 运行代码检查
make lint

# 自动修复格式问题
make fmt

# 安全检查
make security-scan

# 依赖检查
make deps-check
```

### 3. 性能分析

```bash
# Go性能分析
go tool pprof http://localhost:8080/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:8080/debug/pprof/heap

# 前端性能分析
npm run analyze
```

## 团队协作

### 1. 代码规范

- 遵循项目的[代码规范](../contributing/CONTRIBUTING.md#代码规范)
- 使用自动化工具保证代码质量
- 编写单元测试和文档

### 2. 分支策略

- `main`: 生产分支
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

### 3. 发布流程

```bash
# 创建发布分支
git checkout -b release/v1.2.0

# 更新版本号
make version-bump VERSION=1.2.0

# 运行完整测试
make test-full

# 合并到main分支
git checkout main
git merge release/v1.2.0
git tag v1.2.0
```

## 故障排除

### 常用调试命令

```bash
# 查看系统资源
htop
df -h
free -h

# 查看服务日志
make logs

# 检查服务状态
make status

# 重置开发环境
make clean && make dev
```

### 获取帮助

- **文档**: 查看项目[文档](../index.md)
- **社区**: 加入[开发者群组](mailto:<EMAIL>)
- **Issues**: 在GitHub上[创建Issue](https://github.com/cina-club/monorepo/issues)

---

**配置完成后，您就可以开始为CINA.CLUB项目做贡献了！** 🚀 