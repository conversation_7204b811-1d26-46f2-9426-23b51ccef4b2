/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package cache

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/admin-bff-service/internal/domain/model"
)

func TestNewRedisSessionStore(t *testing.T) {
	client := redis.NewClient(&redis.Options{})
	logger := logrus.New()

	store := NewRedisSessionStore(client, logger)

	assert.NotNil(t, store)
	assert.Implements(t, (*interface{})(nil), store)
}

func TestRedisSessionStore_CreateSession(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise

	store := NewRedisSessionStore(db, logger)

	session := &model.AdminSession{
		ID:         "session123",
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Name:       "Admin User",
		Roles:      []string{"admin"},
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		LastUsedAt: time.Now(),
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	// Expect Redis SET command for session (use regexp for flexible matching)
	mock.Regexp().ExpectSet("admin_session:session123", `.*`, 24*time.Hour).SetVal("OK")
	// Expect Redis SADD command for employee sessions
	mock.ExpectSAdd("admin_employee_sessions:emp123", "session123").SetVal(1)

	err := store.CreateSession(context.Background(), session)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRedisSessionStore_GetSession(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	t.Run("successful get", func(t *testing.T) {
		sessionData := `{
			"id": "session123",
			"employee_id": "emp123",
			"email": "<EMAIL>", 
			"name": "Admin User",
			"roles": ["admin"],
			"created_at": "2025-06-23T10:00:00Z",
			"expires_at": "2025-06-24T10:00:00Z",
			"last_used_at": "2025-06-23T10:00:00Z",
			"ip_address": "***********",
			"user_agent": "Mozilla/5.0"
		}`

		mock.ExpectGet("admin_session:session123").SetVal(sessionData)

		result, err := store.GetSession(context.Background(), "session123")

		require.NoError(t, err)
		assert.Equal(t, "session123", result.ID)
		assert.Equal(t, "emp123", result.EmployeeID)
		assert.Equal(t, "<EMAIL>", result.Email)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("session not found", func(t *testing.T) {
		mock.ExpectGet("admin_session:nonexistent").RedisNil()

		result, err := store.GetSession(context.Background(), "nonexistent")

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "session not found")
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisSessionStore_UpdateSession(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	session := &model.AdminSession{
		ID:         "session123",
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Name:       "Admin User",
		Roles:      []string{"admin"},
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		LastUsedAt: time.Now(),
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	// Expect EXISTS check
	mock.ExpectExists("admin_session:session123").SetVal(1)
	// Expect TTL check
	mock.ExpectTTL("admin_session:session123").SetVal(12 * time.Hour)
	// Expect SET command
	mock.Regexp().ExpectSet("admin_session:session123", `.*`, 12*time.Hour).SetVal("OK")

	err := store.UpdateSession(context.Background(), session)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRedisSessionStore_DeleteSession(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	t.Run("successful delete", func(t *testing.T) {
		sessionData := `{
			"id": "session123",
			"employee_id": "emp123",
			"email": "<EMAIL>",
			"name": "Admin User", 
			"roles": ["admin"],
			"created_at": "2025-06-23T10:00:00Z",
			"expires_at": "2025-06-24T10:00:00Z",
			"last_used_at": "2025-06-23T10:00:00Z",
			"ip_address": "***********",
			"user_agent": "Mozilla/5.0"
		}`

		// Expect GET for session info
		mock.ExpectGet("admin_session:session123").SetVal(sessionData)
		// Expect DEL command
		mock.ExpectDel("admin_session:session123").SetVal(1)
		// Expect SREM command
		mock.ExpectSRem("admin_employee_sessions:emp123", "session123").SetVal(1)

		err := store.DeleteSession(context.Background(), "session123")

		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("session not found", func(t *testing.T) {
		// Expect GET (not found)
		mock.ExpectGet("admin_session:nonexistent").RedisNil()
		// Expect DEL anyway
		mock.ExpectDel("admin_session:nonexistent").SetVal(0)

		err := store.DeleteSession(context.Background(), "nonexistent")

		assert.NoError(t, err) // Should not error even if session doesn't exist
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisSessionStore_RefreshSession(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	mock.ExpectExpire("admin_session:session123", 2*time.Hour).SetVal(true)

	err := store.RefreshSession(context.Background(), "session123", 2*time.Hour)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRedisSessionStore_GetActiveSessionsByEmployeeID(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	sessionData := `{
		"id": "session123",
		"employee_id": "emp123",
		"email": "<EMAIL>",
		"name": "Admin User",
		"roles": ["admin"],
		"created_at": "2025-06-23T10:00:00Z",
		"expires_at": "2025-06-24T10:00:00Z",
		"last_used_at": "2025-06-23T10:00:00Z",
		"ip_address": "***********",
		"user_agent": "Mozilla/5.0"
	}`

	// Expect SMEMBERS command
	mock.ExpectSMembers("admin_employee_sessions:emp123").SetVal([]string{"session123", "session456"})
	// Expect GET for first session
	mock.ExpectGet("admin_session:session123").SetVal(sessionData)
	// Expect GET for second session (expired/not found)
	mock.ExpectGet("admin_session:session456").RedisNil()
	// Expect SREM for expired session
	mock.ExpectSRem("admin_employee_sessions:emp123", "session456").SetVal(1)

	sessions, err := store.GetActiveSessionsByEmployeeID(context.Background(), "emp123")

	require.NoError(t, err)
	assert.Len(t, sessions, 1)
	assert.Equal(t, "session123", sessions[0].ID)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRedisSessionStore_DeleteAllSessionsByEmployeeID(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	sessionData := `{
		"id": "session123",
		"employee_id": "emp123",
		"email": "<EMAIL>",
		"name": "Admin User",
		"roles": ["admin"],
		"created_at": "2025-06-23T10:00:00Z",
		"expires_at": "2025-06-24T10:00:00Z",
		"last_used_at": "2025-06-23T10:00:00Z",
		"ip_address": "***********",
		"user_agent": "Mozilla/5.0"
	}`

	// Expect SMEMBERS for getting sessions
	mock.ExpectSMembers("admin_employee_sessions:emp123").SetVal([]string{"session123"})
	// Expect GET for session info
	mock.ExpectGet("admin_session:session123").SetVal(sessionData)
	// Expect GET again for deletion
	mock.ExpectGet("admin_session:session123").SetVal(sessionData)
	// Expect DEL for session
	mock.ExpectDel("admin_session:session123").SetVal(1)
	// Expect SREM for removing from employee sessions
	mock.ExpectSRem("admin_employee_sessions:emp123", "session123").SetVal(1)
	// Expect DEL for employee sessions set
	mock.ExpectDel("admin_employee_sessions:emp123").SetVal(1)

	err := store.DeleteAllSessionsByEmployeeID(context.Background(), "emp123")

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRedisSessionStore_GetSessionCount(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	// Mock SCAN command
	mock.ExpectScan(0, "admin_session:*", 0).SetVal([]string{"admin_session:session1", "admin_session:session2"}, 0)

	count, err := store.GetSessionCount(context.Background())

	assert.NoError(t, err)
	assert.Equal(t, int64(2), count)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRedisSessionStore_CleanupExpiredSessions(t *testing.T) {
	db, mock := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	// Mock SCAN command
	mock.ExpectScan(0, "admin_session:*", 0).SetVal([]string{"admin_session:session1", "admin_session:session2"}, 0)
	// Mock GET for first session (exists)
	mock.ExpectGet("admin_session:session1").SetVal(`{"id":"session1","expires_at":"2025-06-24T10:00:00Z"}`)
	// Mock GET for second session (expired/not found)
	mock.ExpectGet("admin_session:session2").RedisNil()

	count, err := store.CleanupExpiredSessions(context.Background())

	assert.NoError(t, err)
	assert.Equal(t, int64(1), count) // One expired session
	assert.NoError(t, mock.ExpectationsWereMet())
}

// Benchmark tests
func BenchmarkRedisSessionStore_CreateSession(b *testing.B) {
	db, _ := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	session := &model.AdminSession{
		ID:         "session123",
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Name:       "Admin User",
		Roles:      []string{"admin"},
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		LastUsedAt: time.Now(),
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Note: This will fail in benchmark due to mock expectations
		// but it tests the performance of the marshaling/preparation logic
		store.CreateSession(context.Background(), session)
	}
}

func BenchmarkRedisSessionStore_GetSession(b *testing.B) {
	db, _ := redismock.NewClientMock()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	store := NewRedisSessionStore(db, logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		store.GetSession(context.Background(), "session123")
	}
}
