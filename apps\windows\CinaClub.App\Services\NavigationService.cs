 /*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using Microsoft.UI.Xaml.Controls;
using Microsoft.Extensions.Logging;
using System;

namespace CinaClub.App.Services;

/// <summary>
/// 导航服务实现
/// </summary>
public class NavigationService : INavigationService
{
    private readonly ILogger<NavigationService> _logger;
    private Frame? _frame;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public NavigationService(ILogger<NavigationService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 是否可以后退
    /// </summary>
    public bool CanGoBack => _frame?.CanGoBack ?? false;

    /// <summary>
    /// 是否可以前进
    /// </summary>
    public bool CanGoForward => _frame?.CanGoForward ?? false;

    /// <summary>
    /// 当前页面类型
    /// </summary>
    public Type? CurrentPageType => _frame?.CurrentSourcePageType;

    /// <summary>
    /// 初始化导航服务
    /// </summary>
    /// <param name="frame">导航框架</param>
    public void Initialize(Frame frame)
    {
        _frame = frame ?? throw new ArgumentNullException(nameof(frame));
        _logger.LogInformation("导航服务已初始化");
    }

    /// <summary>
    /// 导航到指定页面类型
    /// </summary>
    /// <typeparam name="T">页面类型</typeparam>
    /// <param name="parameter">导航参数</param>
    /// <returns>是否导航成功</returns>
    public bool NavigateTo<T>(object? parameter = null) where T : class
    {
        return NavigateTo(typeof(T), parameter);
    }

    /// <summary>
    /// 导航到指定页面类型
    /// </summary>
    /// <param name="pageType">页面类型</param>
    /// <param name="parameter">导航参数</param>
    /// <returns>是否导航成功</returns>
    public bool NavigateTo(Type pageType, object? parameter = null)
    {
        if (_frame == null)
        {
            _logger.LogError("导航框架未初始化");
            return false;
        }

        if (pageType == null)
        {
            _logger.LogError("页面类型不能为空");
            return false;
        }

        try
        {
            _logger.LogInformation("导航到页面: {PageType}, 参数: {Parameter}", pageType.Name, parameter);
            
            var success = _frame.Navigate(pageType, parameter);
            
            if (success)
            {
                _logger.LogDebug("导航成功");
            }
            else
            {
                _logger.LogWarning("导航失败");
            }
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导航过程中发生异常");
            return false;
        }
    }

    /// <summary>
    /// 后退导航
    /// </summary>
    /// <returns>是否可以后退</returns>
    public bool GoBack()
    {
        if (_frame == null)
        {
            _logger.LogError("导航框架未初始化");
            return false;
        }

        if (!_frame.CanGoBack)
        {
            _logger.LogWarning("无法后退导航");
            return false;
        }

        try
        {
            _logger.LogInformation("执行后退导航");
            _frame.GoBack();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "后退导航过程中发生异常");
            return false;
        }
    }

    /// <summary>
    /// 前进导航
    /// </summary>
    /// <returns>是否可以前进</returns>
    public bool GoForward()
    {
        if (_frame == null)
        {
            _logger.LogError("导航框架未初始化");
            return false;
        }

        if (!_frame.CanGoForward)
        {
            _logger.LogWarning("无法前进导航");
            return false;
        }

        try
        {
            _logger.LogInformation("执行前进导航");
            _frame.GoForward();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "前进导航过程中发生异常");
            return false;
        }
    }

    /// <summary>
    /// 清空导航历史
    /// </summary>
    public void ClearHistory()
    {
        if (_frame == null)
        {
            _logger.LogError("导航框架未初始化");
            return;
        }

        try
        {
            _logger.LogInformation("清空导航历史");
            
            // 清空后退堆栈
            while (_frame.CanGoBack)
            {
                _frame.BackStack.RemoveAt(_frame.BackStack.Count - 1);
            }
            
            // 清空前进堆栈
            while (_frame.CanGoForward)
            {
                _frame.ForwardStack.RemoveAt(_frame.ForwardStack.Count - 1);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空导航历史过程中发生异常");
        }
    }
}