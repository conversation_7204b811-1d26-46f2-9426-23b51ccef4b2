好的，遵照您的指示。我将为您生成`L2_System_Landscape/`目录下的三份核心文档的、详细的、生产级的内容。

这组文档旨在为所有团队成员提供一个**自顶向下的、清晰的平台宏观视图**，解释清“我们有哪些部分”以及“它们是如何协同工作的”。这对于理解个体工作在整个平台中的位置至关重要。

---
### 文件1: `docs/architecture/L2_System_Landscape/01_system_context_and_domains.md`

```markdown
# CINA.CLUB - 系统上下文与业务领域

**文档状态**: 动态更新 (Living Document)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27

## 1. 引言

本文档提供了CINA.CLUB平台的最高层次视图。它首先定义了平台作为一个整体，与其外部环境的交互（**系统上下文**），然后深入平台内部，将其划分为多个高内聚的**业务领域（Domains）**。这为理解平台的业务边界和内部微服务的组织结构提供了“地图”。

---

## 2. 系统上下文图 (System Context Diagram)

系统上下文图将整个CINA.CLUB平台视为一个“黑箱”，展示其与主要外部参与者（用户）和外部系统的交互。

```mermaid
graph TD
    subgraph "外部参与者 (Actors)"
        A[最终用户<br/>(消费者, 服务提供者)]
        B[平台管理员/运营团队]
        C[AI/MLOps工程师]
    end
    
    subgraph "CINA.CLUB 平台 (System)"
        P((CINA.CLUB))
    end
    
    subgraph "外部系统 (External Systems)"
        S1[支付网关<br/>(Stripe, PayPal, ...)]
        S2[地图服务<br/>(Google Maps, 高德)]
        S3[云通信服务<br/>(SMS, Email, Push)]
        S4[第三方日历<br/>(Google Calendar, Outlook)]
        S5[云AI模型API<br/>(OpenAI, Google Gemini)]
        S6[KYC验证服务]
        S7[日志/监控/追踪后端<br/>(Grafana Cloud, Jaeger)]
    end

    A -- "通过客户端App/Web访问" --> P
    P -- "通过管理后台访问" --> B
    P -- "通过MLOps平台访问" --> C
    
    P <--> S1
    P <--> S2
    P <--> S3
    P <--> S4
    P <--> S5
    P <--> S6
    P -- "导出可观测性数据" --> S7
```

**交互说明**:
*   **最终用户**: 通过客户端应用（Web/Mobile）与平台进行所有核心交互。
*   **平台管理员**: 通过专门的管理后台API与平台交互，进行内容治理、用户管理等。
*   **AI/MLOps工程师**: 通过模型管理API和后台，管理AI模型的生命周期。
*   **外部系统**: 平台依赖一系列第三方SaaS/PaaS服务来提供支付、地图、通信等基础能力。

---

## 3. 业务领域划分图 (Business Domain Diagram)

此图将CINA.CLUB平台内部拆分为多个逻辑上高内聚的业务领域。每个领域都由一个或多个微服务负责实现。这种划分直接对应于Monorepo中`/services`目录下的组织结构。

```mermaid
graph TD
    subgraph User & Identity Domain
        direction LR
        U1(user-core-service)
        U2(social-service)
        U3(family-tree-service)
    end

    subgraph AI & Personalization Domain
        direction LR
        A1(ai-assistant-service)
        A2(routines-service)
        A3(memory-service)
        A4(personal-kb-service)
        A5(embedding-service)
        A6(model-management-service)
    end

    subgraph Content & Knowledge Domain
        direction LR
        C1(shared-kb-service)
        C2(community-forum-service)
        C3(community-qa-service)
        C4(short-video-service)
        C5(content-moderation-service)
    end

    subgraph Marketplace & Transaction Domain
        direction LR
        M1(service-offering-service)
        M2(review-service)
        M3(billing-service)
        M4(payment-service)
        M5(cina-coin-ledger-service)
    end

    subgraph Realtime & Notification Domain
        direction LR
        R1(chat-api-service)
        R2(chat-websocket-server)
        R3(notification-dispatch-service)
        R4(activity-feed-service)
    end

    subgraph Metaverse & Digital Twin Domain
        direction LR
        D1(digital-twin-service)
        D2(metaverse-engine-service)
    end
    
    subgraph Platform Infrastructure Domain
        direction LR
        I1(api-gateway)
        I2(search-service)
        I3(search-indexer-service)
        I4(file-storage-service)
        I5(key-management-proxy-service)
        I6(location-service)
        I7(calendar-sync-service)
        I8(analytics-service)
        I9(gamification-service)
    end

    %% 主要依赖关系 (示例)
    M1 --> M3 & M4
    A1 --> A3 & A4 & I2
    C2 --> C5
    R1 <--> R2
    R4 --> R3
    D2 --> D1
    U1 & A2 & C1 & M1 --> I9
```

---

## 4. 微服务清单与职责链接

下表列出了所有微服务及其核心职责，并链接到各自详细的SRS文档。

| 领域 (Domain)         | 微服务名称                      | 核心职责                                        | SRS文档链接                                   |
| --------------------- | ------------------------------- | ----------------------------------------------- | --------------------------------------------- |
| **用户与身份**        | `user-core-service`             | 认证, 账户, 等级, 会员, RBAC                    | `../../srs/services/user-core-service-srs.md` |
|                       | `social-service`                | 关注/好友, 社交图谱                             | `../../srs/services/social-service-srs.md`    |
|                       | `family-tree-service`           | 族谱, 亲缘关系                                  | `../../srs/services/family-tree-service-srs.md`|
| **AI与个性化**        | `ai-assistant-service`          | 对话编排, LLM代理, 工作流                       | `../../srs/services/ai-assistant-service-srs.md` |
|                       | ...                             | ...                                             | ...                                           |
| **(其他所有服务)**    | ...                             | ...                                             | ...                                           |

*(注: 此处为节省篇幅，省略了完整的表格。实际文档中应包含附录B中的完整表格)*

