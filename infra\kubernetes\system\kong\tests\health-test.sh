#!/bin/bash

# CINA.CLUB Kong Gateway - Health Test
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00
#
# Platform engineering health tests for Kong Gateway

set -euo pipefail

# Test configuration
KONG_STATUS_URL="${KONG_STATUS_URL:-http://kong-admin.kong-system.svc.cluster.local:8001}"
KONG_PROXY_URL="${KONG_PROXY_URL:-http://kong-proxy.kong-system.svc.cluster.local:8000}"

# Test Kong status endpoint
test_kong_status() {
    echo "Testing Kong status endpoint..."
    
    # Test status endpoint
    if curl -f "${KONG_STATUS_URL}/status" | grep -q "message"; then
        echo "✅ Kong status endpoint is healthy"
        return 0
    else
        echo "❌ Kong status endpoint failed"
        return 1
    fi
}

# Test Kong readiness
test_kong_readiness() {
    echo "Testing Kong readiness endpoint..."
    
    # Test readiness endpoint
    if curl -f "${KONG_STATUS_URL}/status/ready"; then
        echo "✅ Kong is ready"
        return 0
    else
        echo "❌ Kong is not ready"
        return 1
    fi
}

# Test metrics endpoint
test_metrics_endpoint() {
    echo "Testing metrics endpoint..."
    
    # Test metrics
    if curl -f "${KONG_STATUS_URL}/metrics" | grep -q "kong_"; then
        echo "✅ Metrics endpoint is working"
        return 0
    else
        echo "❌ Metrics endpoint failed"
        return 1
    fi
}

# Test proxy health
test_proxy_health() {
    echo "Testing proxy health..."
    
    # Test basic proxy functionality
    if curl -f "${KONG_PROXY_URL}/health" -H "User-Agent: health-test"; then
        echo "✅ Proxy is healthy"
        return 0
    else
        echo "❌ Proxy health check failed"
        return 1
    fi
}

# Test Kong configuration
test_kong_configuration() {
    echo "Testing Kong configuration..."
    
    # Test if Kong can access its configuration
    if curl -f "${KONG_STATUS_URL}/services" | grep -q "data"; then
        echo "✅ Kong configuration is accessible"
        return 0
    else
        echo "❌ Kong configuration is not accessible"
        return 1
    fi
}

# Test Kong plugins
test_kong_plugins() {
    echo "Testing Kong plugins..."
    
    # Test if Kong has plugins configured
    if curl -f "${KONG_STATUS_URL}/plugins" | grep -q "data"; then
        echo "✅ Kong plugins are configured"
        return 0
    else
        echo "❌ Kong plugins are not configured"
        return 1
    fi
}

# Test Kong ingress controller connectivity
test_ingress_controller() {
    echo "Testing Kong Ingress Controller connectivity..."
    
    # Test if Kong can list routes (indicating ingress controller is working)
    if curl -f "${KONG_STATUS_URL}/routes" | grep -q "data"; then
        echo "✅ Kong Ingress Controller is working"
        return 0
    else
        echo "❌ Kong Ingress Controller is not working"
        return 1
    fi
}

# Test Kong database connectivity (if using DB mode)
test_database_connectivity() {
    echo "Testing Kong database connectivity..."
    
    # For DB-less mode, this should return information about the configuration source
    local db_info=$(curl -s "${KONG_STATUS_URL}/status" | grep -o '"database":[^}]*}' || echo "")
    
    if [[ -n "$db_info" ]]; then
        echo "✅ Kong database/configuration is accessible"
        return 0
    else
        echo "❌ Kong database/configuration is not accessible"
        return 1
    fi
}

# Main test execution
main() {
    echo "Starting Kong Gateway health tests..."
    
    local failed=0
    
    test_kong_status || ((failed++))
    test_kong_readiness || ((failed++))
    test_metrics_endpoint || ((failed++))
    test_proxy_health || ((failed++))
    test_kong_configuration || ((failed++))
    test_kong_plugins || ((failed++))
    test_ingress_controller || ((failed++))
    test_database_connectivity || ((failed++))
    
    if [ $failed -eq 0 ]; then
        echo "🎉 All Kong Gateway health tests passed!"
        exit 0
    else
        echo "❌ $failed Kong Gateway health tests failed"
        exit 1
    fi
}

main "$@" 