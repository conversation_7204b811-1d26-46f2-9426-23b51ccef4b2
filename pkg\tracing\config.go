/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package tracing

import (
	"fmt"
)

// Config 定义分布式追踪的配置参数
type Config struct {
	// Enabled 是否启用追踪
	Enabled bool `mapstructure:"enabled" default:"true"`

	// ServiceName 服务名称，将作为 Resource 属性
	ServiceName string `mapstructure:"service_name" validate:"required"`

	// ServiceVersion 服务版本
	ServiceVersion string `mapstructure:"service_version"`

	// Environment 部署环境 (dev, staging, prod)
	Environment string `mapstructure:"environment"`

	// Exporter 配置追踪数据导出器
	Exporter ExporterConfig `mapstructure:"exporter" validate:"required"`

	// Sampler 配置采样策略
	Sampler SamplerConfig `mapstructure:"sampler" validate:"required"`

	// BatchTimeout 批处理超时时间（秒）
	BatchTimeout int `mapstructure:"batch_timeout" default:"10"`

	// MaxExportBatchSize 最大导出批处理大小
	MaxExportBatchSize int `mapstructure:"max_export_batch_size" default:"512"`

	// MaxQueueSize 最大队列大小
	MaxQueueSize int `mapstructure:"max_queue_size" default:"2048"`
}

// ExporterConfig 定义追踪数据导出器的配置
type ExporterConfig struct {
	// Type 导出器类型 (stdout, jaeger, otlp-grpc, otlp-http)
	Type string `mapstructure:"type" validate:"required,oneof=stdout jaeger otlp-grpc otlp-http"`

	// Endpoint 导出器端点地址 (对于 jaeger 和 otlp)
	Endpoint string `mapstructure:"endpoint"`

	// Headers 自定义 HTTP 头部 (对于 otlp-http)
	Headers map[string]string `mapstructure:"headers"`

	// Insecure 是否使用不安全连接 (对于 otlp-grpc)
	Insecure bool `mapstructure:"insecure" default:"false"`

	// Compression 压缩方式 (gzip, none)
	Compression string `mapstructure:"compression" default:"gzip"`

	// Timeout 导出超时时间（秒）
	Timeout int `mapstructure:"timeout" default:"30"`

	// RetryConfig 重试配置
	Retry RetryConfig `mapstructure:"retry"`
}

// RetryConfig 定义重试配置
type RetryConfig struct {
	// Enabled 是否启用重试
	Enabled bool `mapstructure:"enabled" default:"true"`

	// MaxAttempts 最大重试次数
	MaxAttempts int `mapstructure:"max_attempts" default:"3"`

	// InitialInterval 初始重试间隔（毫秒）
	InitialInterval int `mapstructure:"initial_interval" default:"1000"`

	// MaxInterval 最大重试间隔（毫秒）
	MaxInterval int `mapstructure:"max_interval" default:"30000"`
}

// SamplerConfig 定义采样策略的配置
type SamplerConfig struct {
	// Type 采样器类型 (always_on, always_off, parent_based_trace_id_ratio, trace_id_ratio)
	Type string `mapstructure:"type" validate:"required,oneof=always_on always_off parent_based_trace_id_ratio trace_id_ratio"`

	// Param 采样参数 (用于 trace_id_ratio 和 parent_based_trace_id_ratio)
	// 取值范围 [0.0, 1.0]，表示采样概率
	Param float64 `mapstructure:"param" validate:"gte=0,lte=1"`

	// ParentBased 是否基于父 span 的采样决策
	// 仅在 Type 为 parent_based_trace_id_ratio 时有效
	ParentBased bool `mapstructure:"parent_based" default:"true"`
}

// DefaultConfig 返回默认配置
func DefaultConfig(serviceName string) Config {
	return Config{
		Enabled:        true,
		ServiceName:    serviceName,
		ServiceVersion: "unknown",
		Environment:    "development",
		Exporter: ExporterConfig{
			Type:        "stdout",
			Insecure:    true,
			Compression: "gzip",
			Timeout:     30,
			Retry: RetryConfig{
				Enabled:         true,
				MaxAttempts:     3,
				InitialInterval: 1000,
				MaxInterval:     30000,
			},
		},
		Sampler: SamplerConfig{
			Type:        "parent_based_trace_id_ratio",
			Param:       1.0,
			ParentBased: true,
		},
		BatchTimeout:       10,
		MaxExportBatchSize: 512,
		MaxQueueSize:       2048,
	}
}

// ValidateConfig 验证配置的有效性
func (c *Config) Validate() error {
	if c.ServiceName == "" {
		return fmt.Errorf("service_name is required")
	}

	// 验证 Exporter 配置
	switch c.Exporter.Type {
	case "jaeger", "otlp-grpc", "otlp-http":
		if c.Exporter.Endpoint == "" {
			return fmt.Errorf("endpoint is required for exporter type: %s", c.Exporter.Type)
		}
	case "stdout":
		// stdout 不需要 endpoint
	default:
		return fmt.Errorf("unsupported exporter type: %s", c.Exporter.Type)
	}

	// 验证 Sampler 配置
	switch c.Sampler.Type {
	case "trace_id_ratio", "parent_based_trace_id_ratio":
		if c.Sampler.Param < 0.0 || c.Sampler.Param > 1.0 {
			return fmt.Errorf("sampler param must be between 0.0 and 1.0, got: %f", c.Sampler.Param)
		}
	case "always_on", "always_off":
		// 这些类型不需要参数
	default:
		return fmt.Errorf("unsupported sampler type: %s", c.Sampler.Type)
	}

	return nil
}

// DevelopmentConfig 返回开发环境的配置
func DevelopmentConfig(serviceName string) Config {
	cfg := DefaultConfig(serviceName)
	cfg.Environment = "development"
	cfg.Exporter.Type = "stdout"
	cfg.Sampler.Type = "always_on"
	cfg.Sampler.Param = 1.0
	return cfg
}

// ProductionConfig 返回生产环境的配置
func ProductionConfig(serviceName string, jaegerEndpoint string) Config {
	cfg := DefaultConfig(serviceName)
	cfg.Environment = "production"
	cfg.Exporter.Type = "jaeger"
	cfg.Exporter.Endpoint = jaegerEndpoint
	cfg.Exporter.Insecure = false
	cfg.Sampler.Type = "parent_based_trace_id_ratio"
	cfg.Sampler.Param = 0.1 // 10% 采样率
	return cfg
}

// TestConfig 返回测试环境的配置
func TestConfig(serviceName string) Config {
	cfg := DefaultConfig(serviceName)
	cfg.Environment = "test"
	cfg.Enabled = false // 测试时默认禁用追踪
	return cfg
}
