package concurrent

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/application/service"
	"cina.club/services/live-gateway-service/internal/domain/model"
	"cina.club/services/live-gateway-service/internal/testutil/generators"
)

// Mock implementations for missing dependencies
type mockCacheRepository struct {
	mock.Mock
}

func (m *mockCacheRepository) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *mockCacheRepository) DeleteNodeLoad(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) StoreStreamMapping(ctx context.Context, mapping *model.StreamMapping) error {
	args := m.Called(ctx, mapping)
	return args.Error(0)
}

func (m *mockCacheRepository) GetStreamMapping(ctx context.Context, streamKey string) (*model.StreamMapping, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamMapping), args.Error(1)
}

func (m *mockCacheRepository) DeleteStreamMapping(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) DeleteStreamStats(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNodeLoad(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) ListNodeLoads(ctx context.Context) (map[string]*model.NodeLoad, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) GetStreamStats(ctx context.Context, streamKey string) (*model.StreamStats, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamStats), args.Error(1)
}

func (m *mockCacheRepository) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *mockCacheRepository) StoreNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *mockCacheRepository) StoreStreamStats(ctx context.Context, streamKey string, stats *model.StreamStats) error {
	args := m.Called(ctx, streamKey, stats)
	return args.Error(0)
}

// Node operations
func (m *mockCacheRepository) StoreNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNode(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) DeleteNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error {
	args := m.Called(ctx, nodeID, status)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error {
	args := m.Called(ctx, nodeID, stats)
	return args.Error(0)
}

type mockMediaServerAdapter struct {
	mock.Mock
}

// Update mockMediaServerAdapter to match the latest MediaServerAdapter interface
func (m *mockMediaServerAdapter) GetType() model.MediaServerType {
	args := m.Called()
	return args.Get(0).(model.MediaServerType)
}

func (m *mockMediaServerAdapter) ParseWebhookRequest(req *port.WebhookParseRequest) (*port.WebhookRequest, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.WebhookRequest), args.Error(1)
}

func (m *mockMediaServerAdapter) ValidateWebhookRequest(req *port.WebhookValidateRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) RespondWebhookSuccess(req *port.WebhookResponseRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) RespondWebhookFailure(req *port.WebhookErrorRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) GeneratePushURL(ctx context.Context, req *port.PushURLRequest) (*model.PushURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PushURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GeneratePlayURLs(ctx context.Context, req *port.RequestPlayURLsRequest) ([]*model.PlayURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.PlayURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GetStreamInfo(ctx context.Context, streamKey string) (*port.StreamInfo, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.StreamInfo), args.Error(1)
}

func (m *mockMediaServerAdapter) KickStream(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) GetServerStats(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockMediaServerAdapter) HealthCheck(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

type mockLoadBalancer struct {
	mock.Mock
}

func (m *mockLoadBalancer) RegisterNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) UpdateNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) GetNodeByID(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) GetAvailableNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) SelectNode(ctx context.Context, eligibleNodes []*model.MediaNode) (*model.MediaNode, error) {
	args := m.Called(ctx, eligibleNodes)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) UpdateNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *mockLoadBalancer) AddNode(ctx context.Context, node *model.MediaServerNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) RemoveNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockLoadBalancer) SelectNodeByRequest(ctx context.Context, req *port.NodeSelectionRequest) (*model.MediaNode, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

type mockLiveAPIClient struct {
	mock.Mock
}

func (m *mockLiveAPIClient) SendEvent(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) CheckPushAuth(ctx context.Context, req *port.AuthRequest) (*port.AuthResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.AuthResponse), args.Error(1)
}

func (m *mockLiveAPIClient) NotifyRecordingCompleted(ctx context.Context, event *port.RecordingCompletedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Update NotifyStreamKicked method to match the expected signature
func (m *mockLiveAPIClient) NotifyStreamKicked(ctx context.Context, event *port.StreamKickedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add NotifyStreamPublished method to mockLiveAPIClient
func (m *mockLiveAPIClient) NotifyStreamPublished(ctx context.Context, event *port.StreamPublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add NotifyStreamUnpublished method to mockLiveAPIClient
func (m *mockLiveAPIClient) NotifyStreamUnpublished(ctx context.Context, event *port.StreamUnpublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add SubmitForModeration method to mockLiveAPIClient
func (m *mockLiveAPIClient) SubmitForModeration(ctx context.Context, event *port.ModerationSubmissionEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type mockEventPublisher struct {
	mock.Mock
}

func (m *mockEventPublisher) Publish(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishStreamEvent(ctx context.Context, event *model.WebhookEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishNodeEvent(ctx context.Context, nodeID string, eventType string, data interface{}) error {
	args := m.Called(ctx, nodeID, eventType, data)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishAlertEvent(ctx context.Context, alertType interface{}) error {
	args := m.Called(ctx, alertType)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type mockEventStore struct {
	mock.Mock
}

func (m *mockEventStore) StoreEvent(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventStore) QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error) {
	args := m.Called(ctx, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Event), args.Error(1)
}

func (m *mockEventStore) StoreBatch(ctx context.Context, events []*model.Event) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

func setupTestService(t *testing.T) port.GatewayService {
	// Setup mock dependencies
	cache := new(mockCacheRepository)
	mediaAdapter := new(mockMediaServerAdapter)
	loadBalancer := new(mockLoadBalancer)
	liveAPIClient := new(mockLiveAPIClient)
	eventPublisher := new(mockEventPublisher)
	eventStore := new(mockEventStore)
	logger := logrus.New()

	// Setup default config
	config := &service.GatewayConfig{
		DefaultTTL:               1 * time.Hour,
		MaxConcurrentStreams:     100,
		LoadBalanceStrategy:      "round_robin",
		EnableMetrics:            true,
		WebhookTimeout:           30 * time.Second,
		AuthTimeout:              10 * time.Second,
		AllowAuthFallbackOnError: false,
	}

	// Setup mock expectations
	mediaAdapter.On("GetType").Return(model.MediaServerTypeSRS)
	mediaAdapter.On("GeneratePushURL", mock.Anything, mock.AnythingOfType("*port.PushURLRequest")).Return(&model.PushURL{
		URLString: "rtmp://test-server/live/test-stream",
		StreamKey: "test-stream",
		TTL:       time.Hour,
	}, nil)
	mediaAdapter.On("GeneratePlayURLs", mock.Anything, mock.AnythingOfType("*port.RequestPlayURLsRequest")).Return([]*model.PlayURL{
		{
			URLString: "http://test-server/live/test-stream.flv",
			Protocol:  model.PlayProtocolFLV,
			StreamKey: "test-stream",
			TTL:       time.Hour,
		},
	}, nil)
	mediaAdapter.On("GetStreamInfo", mock.Anything, mock.AnythingOfType("string")).Return(&port.StreamInfo{
		StreamKey:  "test-stream",
		Status:     model.StreamStatusActive,
		Protocol:   model.StreamProtocolRTMP,
		Quality:    model.StreamQualityHigh,
		ServerNode: "test-node",
		Stats: &model.StreamStats{
			StreamKey:    "test-stream",
			IsActive:     true,
			VideoBitrate: 5000000,
			AudioBitrate: 128000,
			ViewerCount:  10,
		},
	}, nil)
	cache.On("GetNode", mock.Anything, mock.AnythingOfType("string")).Return(&model.MediaNode{
		ID:           "test-node",
		Address:      "127.0.0.1",
		Port:         1935,
		Region:       "test-region",
		ServerType:   model.MediaServerTypeSRS,
		Capabilities: model.NodeCapabilities{RTMP: true, HLS: true},
		Protocols:    []model.StreamProtocol{model.StreamProtocolRTMP},
		Status:       model.NodeStatusActive,
	}, nil)
	cache.On("StoreNode", mock.Anything, mock.AnythingOfType("*model.MediaNode")).Return(nil)

	// Setup load balancer expectations
	node := &model.MediaNode{
		ID:         "test-node",
		ServerType: model.MediaServerTypeSRS,
		Address:    "test-server",
		Port:       1935,
		Region:     "test-region",
		Status:     model.NodeStatusActive,
		Protocols:  []model.StreamProtocol{model.StreamProtocolRTMP},
	}
	loadBalancer.On("SelectNodeByRequest", mock.Anything, mock.AnythingOfType("*port.NodeSelectionRequest")).Return(node, nil)

	// Setup cache expectations
	cache.On("StoreStreamMapping", mock.Anything, mock.AnythingOfType("*model.StreamMapping")).Return(nil)
	cache.On("GetStreamMapping", mock.Anything, mock.AnythingOfType("string")).Return(&model.StreamMapping{
		StreamKey:  "test-stream",
		RoomID:     uuid.New(),
		UserID:     uuid.New(),
		AuthToken:  "test-token",
		ServerNode: "test-node",
		Protocol:   model.StreamProtocolRTMP,
		Status:     model.StreamStatusActive,
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}, nil)

	// Setup event publisher expectations
	eventPublisher.On("PublishStreamEvent", mock.Anything, mock.AnythingOfType("*model.WebhookEvent")).Return(nil)
	eventPublisher.On("PublishEvent", mock.Anything, mock.AnythingOfType("*model.Event")).Return(nil)

	// Setup event store expectations
	eventStore.On("StoreEvent", mock.Anything, mock.AnythingOfType("*model.Event")).Return(nil)
	eventStore.On("StoreBatch", mock.Anything, mock.AnythingOfType("[]*model.Event")).Return(nil)
	eventStore.On("QueryEvents", mock.Anything, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time")).Return([]*model.Event{}, nil)

	// Create service
	svc := service.NewGatewayService(
		cache,
		mediaAdapter,
		loadBalancer,
		liveAPIClient,
		eventPublisher,
		eventStore,
		config,
		logger,
	)

	// Add cleanup
	t.Cleanup(func() {
		// Clean up resources
	})

	return svc
}

func TestConcurrentStreamOperations(t *testing.T) {
	// 设置测试环境
	ctx := context.Background()
	svc := setupTestService(t)
	streamGen := generators.NewStreamGenerator(time.Now().UnixNano())

	// 测试参数
	const (
		numPublishers  = 50
		numViewers     = 200
		numOperations  = 100
		operationDelay = 10 * time.Millisecond
	)

	// 生成测试流
	streams := make([]string, numPublishers)
	for i := range streams {
		streams[i] = streamGen.GenerateStreamKey()
	}

	// 并发发布流
	t.Run("concurrent publish", func(t *testing.T) {
		var wg sync.WaitGroup
		errCh := make(chan error, numPublishers)
		resultCh := make(chan *model.PushURL, numPublishers)

		for i := 0; i < numPublishers; i++ {
			wg.Add(1)
			go func(idx int) {
				defer wg.Done()
				req := &port.CreateStreamRequest{
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
					Protocol:  model.StreamProtocolRTMP,
					Quality:   model.StreamQualityHigh,
					ClientIP:  "127.0.0.1",
					UserAgent: "test-agent",
				}
				url, err := svc.RequestPushURL(ctx, req)
				if err != nil {
					errCh <- err
					return
				}
				resultCh <- url
			}(i)
			time.Sleep(operationDelay)
		}

		wg.Wait()
		close(errCh)
		close(resultCh)

		// 检查错误
		for err := range errCh {
			assert.NoError(t, err)
		}

		// 验证结果
		urls := make(map[string]bool)
		for url := range resultCh {
			assert.NotEmpty(t, url.URLString)
			assert.NotEmpty(t, url.StreamKey)
			urls[url.StreamKey] = true
		}
		assert.Equal(t, numPublishers, len(urls))
	})

	// 并发拉流
	t.Run("concurrent pull", func(t *testing.T) {
		var wg sync.WaitGroup
		errCh := make(chan error, numViewers)
		resultCh := make(chan []*model.PlayURL, numViewers)

		for i := 0; i < numViewers; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				streamKey := streams[i%numPublishers]
				req := &port.RequestPlayURLsRequest{
					StreamKey: streamKey,
					Protocols: []model.PlayProtocol{
						model.PlayProtocolHLS,
						model.PlayProtocolFLV,
					},
					Quality:   model.StreamQualityHigh,
					EnableCDN: true,
					Region:    "test-region",
					ClientIP:  "127.0.0.1",
					UserAgent: "test-agent",
					TTL:       time.Hour,
				}
				urls, err := svc.RequestPlayURLs(ctx, req)
				if err != nil {
					errCh <- err
					return
				}
				resultCh <- urls
			}()
			time.Sleep(operationDelay)
		}

		wg.Wait()
		close(errCh)
		close(resultCh)

		// 检查错误
		for err := range errCh {
			assert.NoError(t, err)
		}

		// 验证结果
		playURLs := make(map[string]map[model.PlayProtocol]bool)
		for urls := range resultCh {
			for _, url := range urls {
				if _, exists := playURLs[url.StreamKey]; !exists {
					playURLs[url.StreamKey] = make(map[model.PlayProtocol]bool)
				}
				playURLs[url.StreamKey][url.Protocol] = true
			}
		}
		assert.NotEmpty(t, playURLs)
	})

	// 并发处理Webhook事件
	t.Run("concurrent webhook events", func(t *testing.T) {
		var wg sync.WaitGroup
		errCh := make(chan error, numOperations)
		resultCh := make(chan *model.WebhookResponse, numOperations)

		for i := 0; i < numOperations; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				streamKey := streams[i%numPublishers]
				req := &port.WebhookRequest{
					EventType:  model.WebhookEventTypePublish,
					StreamKey:  streamKey,
					AuthToken:  "test-token",
					ClientIP:   "127.0.0.1",
					UserAgent:  "test-agent",
					ServerNode: "test-node",
					Protocol:   model.StreamProtocolRTMP,
					Timestamp:  time.Now(),
				}
				resp, err := svc.HandleWebhook(ctx, req)
				if err != nil {
					errCh <- err
					return
				}
				resultCh <- &model.WebhookResponse{
					Success:   resp.Success,
					StreamKey: streamKey,
					Message:   resp.Message,
				}
			}()
			time.Sleep(operationDelay)
		}

		wg.Wait()
		close(errCh)
		close(resultCh)

		// 检查错误
		for err := range errCh {
			assert.NoError(t, err)
		}

		// 验证结果
		responses := make(map[string]bool)
		for resp := range resultCh {
			assert.NotNil(t, resp)
			assert.True(t, resp.Success)
			responses[resp.StreamKey] = true
		}
		assert.NotEmpty(t, responses)
	})
}

// Define PushAuthRequest at the top of the file
type PushAuthRequest struct {
	StreamKey string `json:"stream_key"`
	Token     string `json:"token"`
}
