// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package datasync

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"hash"
)

const (
	// FastCDC default parameters
	MinChunkSize     = 4 * 1024  // 4KB
	AverageChunkSize = 16 * 1024 // 16KB
	MaxChunkSize     = 64 * 1024 // 64KB
	MaskS            = 0x0000d90303530000
	MaskL            = 0x0000d90003530000
	Gear             = 0x9e3779b185ebca87
)

// Chunk represents a content-defined chunk
type Chunk struct {
	ID       string `json:"id"`       // SHA-256 hash of the content
	Data     []byte `json:"data"`     // The actual chunk data
	Size     int    `json:"size"`     // Size in bytes
	Offset   int64  `json:"offset"`   // Offset in the original data
	Sequence int    `json:"sequence"` // Sequence number in the original data
}

// ChunkResult represents the result of chunking operation
type ChunkResult struct {
	Chunks      []Chunk `json:"chunks"`
	TotalSize   int64   `json:"total_size"`
	ChunkCount  int     `json:"chunk_count"`
	Fingerprint string  `json:"fingerprint"` // Overall hash of the data
}

// FastCDC implements Fast Content-Defined Chunking algorithm
type FastCDC struct {
	minSize int
	avgSize int
	maxSize int
	maskS   uint64
	maskL   uint64
	gear    uint64
	hasher  hash.Hash
}

// NewFastCDC creates a new FastCDC chunker with default parameters
func NewFastCDC() *FastCDC {
	return &FastCDC{
		minSize: MinChunkSize,
		avgSize: AverageChunkSize,
		maxSize: MaxChunkSize,
		maskS:   MaskS,
		maskL:   MaskL,
		gear:    Gear,
		hasher:  sha256.New(),
	}
}

// NewFastCDCWithParams creates a new FastCDC chunker with custom parameters
func NewFastCDCWithParams(minSize, avgSize, maxSize int) *FastCDC {
	return &FastCDC{
		minSize: minSize,
		avgSize: avgSize,
		maxSize: maxSize,
		maskS:   MaskS,
		maskL:   MaskL,
		gear:    Gear,
		hasher:  sha256.New(),
	}
}

// ChunkData performs content-defined chunking on the input data
func (cdc *FastCDC) ChunkData(data []byte) *ChunkResult {
	if len(data) == 0 {
		return &ChunkResult{
			Chunks:      []Chunk{},
			TotalSize:   0,
			ChunkCount:  0,
			Fingerprint: cdc.hashData([]byte{}),
		}
	}

	chunks := make([]Chunk, 0)
	offset := int64(0)
	sequence := 0

	for len(data) > 0 {
		chunkSize := cdc.findChunkBoundary(data)
		chunkData := data[:chunkSize]

		chunk := Chunk{
			ID:       cdc.hashData(chunkData),
			Data:     make([]byte, len(chunkData)),
			Size:     len(chunkData),
			Offset:   offset,
			Sequence: sequence,
		}
		copy(chunk.Data, chunkData)

		chunks = append(chunks, chunk)

		data = data[chunkSize:]
		offset += int64(chunkSize)
		sequence++
	}

	// Calculate overall fingerprint
	fingerprint := cdc.calculateFingerprint(chunks)

	return &ChunkResult{
		Chunks:      chunks,
		TotalSize:   offset,
		ChunkCount:  len(chunks),
		Fingerprint: fingerprint,
	}
}

// findChunkBoundary finds the boundary for the next chunk using the FastCDC algorithm
func (cdc *FastCDC) findChunkBoundary(data []byte) int {
	if len(data) <= cdc.minSize {
		return len(data)
	}

	var hash uint64

	// Initialize rolling hash for the first window
	for i := 0; i < cdc.minSize && i < len(data); i++ {
		hash = (hash << 1) + uint64(data[i])
	}

	// Find chunk boundary starting from minimum size
	for i := cdc.minSize; i < len(data) && i < cdc.maxSize; i++ {
		hash = (hash << 1) + uint64(data[i])

		// Check for boundary condition
		if i < cdc.avgSize {
			// Use smaller mask for earlier boundary (more sensitive)
			if hash&cdc.maskS == 0 {
				return i + 1
			}
		} else {
			// Use larger mask for later boundary (less sensitive)
			if hash&cdc.maskL == 0 {
				return i + 1
			}
		}
	}

	// If we reach max size without finding a boundary, cut here
	if len(data) >= cdc.maxSize {
		return cdc.maxSize
	}

	return len(data)
}

// hashData calculates SHA-256 hash of the data
func (cdc *FastCDC) hashData(data []byte) string {
	cdc.hasher.Reset()
	cdc.hasher.Write(data)
	return hex.EncodeToString(cdc.hasher.Sum(nil))
}

// calculateFingerprint calculates the overall fingerprint of all chunks
func (cdc *FastCDC) calculateFingerprint(chunks []Chunk) string {
	cdc.hasher.Reset()
	for _, chunk := range chunks {
		cdc.hasher.Write([]byte(chunk.ID))
	}
	return hex.EncodeToString(cdc.hasher.Sum(nil))
}

// ChunkDiff represents the difference between two chunk sets
type ChunkDiff struct {
	Added    []Chunk  `json:"added"`    // Chunks that are new
	Removed  []string `json:"removed"`  // Chunk IDs that were removed
	Modified []Chunk  `json:"modified"` // Chunks that were modified
	Common   []string `json:"common"`   // Chunk IDs that are unchanged
}

// CompareChunks compares two chunk results and returns the differences
func CompareChunks(oldResult, newResult *ChunkResult) *ChunkDiff {
	if oldResult == nil && newResult == nil {
		return &ChunkDiff{}
	}

	if oldResult == nil {
		return &ChunkDiff{
			Added: newResult.Chunks,
		}
	}

	if newResult == nil {
		removed := make([]string, len(oldResult.Chunks))
		for i, chunk := range oldResult.Chunks {
			removed[i] = chunk.ID
		}
		return &ChunkDiff{
			Removed: removed,
		}
	}

	// Create maps for efficient lookup
	oldChunks := make(map[string]Chunk)
	for _, chunk := range oldResult.Chunks {
		oldChunks[chunk.ID] = chunk
	}

	newChunks := make(map[string]Chunk)
	for _, chunk := range newResult.Chunks {
		newChunks[chunk.ID] = chunk
	}

	diff := &ChunkDiff{
		Added:    []Chunk{},
		Removed:  []string{},
		Modified: []Chunk{},
		Common:   []string{},
	}

	// Find added and common chunks
	for id, newChunk := range newChunks {
		if oldChunk, exists := oldChunks[id]; exists {
			// Check if the chunk has been modified (different position/sequence)
			if oldChunk.Offset != newChunk.Offset || oldChunk.Sequence != newChunk.Sequence {
				diff.Modified = append(diff.Modified, newChunk)
			} else {
				diff.Common = append(diff.Common, id)
			}
		} else {
			diff.Added = append(diff.Added, newChunk)
		}
	}

	// Find removed chunks
	for id := range oldChunks {
		if _, exists := newChunks[id]; !exists {
			diff.Removed = append(diff.Removed, id)
		}
	}

	return diff
}

// ChunkDataSimple is a convenience function that chunks data with default settings
func ChunkDataSimple(data []byte) *ChunkResult {
	cdc := NewFastCDC()
	return cdc.ChunkData(data)
}

// ReassembleChunks reassembles chunks back into the original data
func ReassembleChunks(chunks []Chunk) []byte {
	if len(chunks) == 0 {
		return []byte{}
	}

	// Calculate total size
	totalSize := 0
	for _, chunk := range chunks {
		totalSize += chunk.Size
	}

	// Reassemble data
	result := make([]byte, 0, totalSize)
	for _, chunk := range chunks {
		result = append(result, chunk.Data...)
	}

	return result
}

// ValidateChunks validates that chunks are properly formed and in sequence
func ValidateChunks(chunks []Chunk) error {
	if len(chunks) == 0 {
		return nil
	}

	expectedOffset := int64(0)
	for i, chunk := range chunks {
		// Check sequence
		if chunk.Sequence != i {
			return fmt.Errorf("chunk %d has incorrect sequence %d, expected %d", i, chunk.Sequence, i)
		}

		// Check offset
		if chunk.Offset != expectedOffset {
			return fmt.Errorf("chunk %d has incorrect offset %d, expected %d", i, chunk.Offset, expectedOffset)
		}

		// Check size
		if chunk.Size != len(chunk.Data) {
			return fmt.Errorf("chunk %d size mismatch: declared %d, actual %d", i, chunk.Size, len(chunk.Data))
		}

		// Validate hash
		cdc := NewFastCDC()
		expectedID := cdc.hashData(chunk.Data)
		if chunk.ID != expectedID {
			return fmt.Errorf("chunk %d has incorrect hash: declared %s, calculated %s", i, chunk.ID, expectedID)
		}

		expectedOffset += int64(chunk.Size)
	}

	return nil
}
