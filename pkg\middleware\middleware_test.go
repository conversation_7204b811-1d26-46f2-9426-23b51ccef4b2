/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package middleware

import (
	"context"
	"errors"
	"log/slog"
	"os"
	"testing"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// mockRequest 模拟请求消息
type mockRequest struct {
	Message   string
	ShouldErr bool
}

// Validate 实现 Validator 接口
func (r *mockRequest) Validate() error {
	if r.ShouldErr {
		return errors.New("validation failed")
	}
	return nil
}

// mockResponse 模拟响应消息
type mockResponse struct {
	Message string
}

// mockHandler 模拟 gRPC 处理器
func mockHandler(shouldPanic bool, shouldError bool) grpc.UnaryHandler {
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		if shouldPanic {
			panic("test panic")
		}
		if shouldError {
			return nil, status.Error(codes.Internal, "test error")
		}
		return &mockResponse{Message: "success"}, nil
	}
}

// mockServerInfo 模拟服务器信息
var mockServerInfo = &grpc.UnaryServerInfo{
	FullMethod: "/test.service/TestMethod",
}

func TestRecoveryInterceptor(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	t.Run("handles panic", func(t *testing.T) {
		interceptor := RecoveryInterceptor(logger)
		ctx := context.Background()
		req := &mockRequest{Message: "test"}

		// 应该捕获 panic 并返回错误，而不是让程序崩溃
		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(true, false))

		assert.Nil(t, resp)
		assert.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
	})

	t.Run("normal execution", func(t *testing.T) {
		interceptor := RecoveryInterceptor(logger)
		ctx := context.Background()
		req := &mockRequest{Message: "test"}

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.NotNil(t, resp)
		assert.NoError(t, err)
		assert.Equal(t, "success", resp.(*mockResponse).Message)
	})
}

func TestRecoveryInterceptorWithOptions(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	t.Run("custom recovery handler", func(t *testing.T) {
		opts := &RecoveryOptions{
			LogStackTrace: false,
			CustomRecoveryHandler: func(ctx context.Context, r interface{}) error {
				return status.Error(codes.Unavailable, "custom recovery")
			},
		}

		interceptor := RecoveryInterceptorWithOptions(logger, opts)
		ctx := context.Background()
		req := &mockRequest{Message: "test"}

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(true, false))

		assert.Nil(t, resp)
		assert.Error(t, err)
		assert.Equal(t, codes.Unavailable, status.Code(err))
		assert.Contains(t, err.Error(), "custom recovery")
	})
}

func TestValidationInterceptor(t *testing.T) {
	t.Run("validation passes", func(t *testing.T) {
		interceptor := ValidationInterceptor()
		ctx := context.Background()
		req := &mockRequest{Message: "test", ShouldErr: false}

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.NotNil(t, resp)
		assert.NoError(t, err)
	})

	t.Run("validation fails", func(t *testing.T) {
		interceptor := ValidationInterceptor()
		ctx := context.Background()
		req := &mockRequest{Message: "test", ShouldErr: true}

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.Nil(t, resp)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("no validator interface", func(t *testing.T) {
		interceptor := ValidationInterceptor()
		ctx := context.Background()
		req := "not a validator" // 不实现 Validator 接口

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.NotNil(t, resp)
		assert.NoError(t, err)
	})
}

func TestValidationInterceptorWithOptions(t *testing.T) {
	t.Run("skip validation", func(t *testing.T) {
		opts := &ValidationOptions{
			SkipValidation: true,
		}
		interceptor := ValidationInterceptorWithOptions(opts)
		ctx := context.Background()
		req := &mockRequest{Message: "test", ShouldErr: true} // 应该失败但被跳过

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.NotNil(t, resp)
		assert.NoError(t, err) // 验证被跳过，所以不会出错
	})

	t.Run("exclude methods", func(t *testing.T) {
		opts := &ValidationOptions{
			ExcludeMethods: []string{"/test.service/TestMethod"},
		}
		interceptor := ValidationInterceptorWithOptions(opts)
		ctx := context.Background()
		req := &mockRequest{Message: "test", ShouldErr: true}

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.NotNil(t, resp)
		assert.NoError(t, err) // 方法被排除，所以不会验证
	})

	t.Run("custom validator", func(t *testing.T) {
		opts := &ValidationOptions{
			CustomValidator: func(req interface{}) error {
				return errors.New("custom validation error")
			},
		}
		interceptor := ValidationInterceptorWithOptions(opts)
		ctx := context.Background()
		req := &mockRequest{Message: "test", ShouldErr: false}

		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.Nil(t, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "custom validation")
	})
}

func TestServerMetrics(t *testing.T) {
	t.Run("create and register metrics", func(t *testing.T) {
		metrics := NewServerMetrics()
		reg := prometheus.NewRegistry()

		err := metrics.Register(reg)
		assert.NoError(t, err)

		// 尝试重复注册应该出错
		err = metrics.Register(reg)
		assert.Error(t, err)
	})

	t.Run("unary interceptor records metrics", func(t *testing.T) {
		metrics := NewServerMetrics()
		reg := prometheus.NewRegistry()
		metrics.Register(reg)

		interceptor := metrics.UnaryServerInterceptor()
		ctx := context.Background()
		req := &mockRequest{Message: "test"}

		// 调用拦截器
		resp, err := interceptor(ctx, req, mockServerInfo, mockHandler(false, false))

		assert.NotNil(t, resp)
		assert.NoError(t, err)

		// 检查指标是否被记录（这里只做基本检查）
		metricFamilies, err := reg.Gather()
		assert.NoError(t, err)
		assert.NotEmpty(t, metricFamilies)
	})
}

func TestParseFullMethodName(t *testing.T) {
	tests := []struct {
		input          string
		expectedSvc    string
		expectedMethod string
	}{
		{"/test.service/TestMethod", "test.service", "TestMethod"},
		{"/package.service/Method", "package.service", "Method"},
		{"/service/method", "service", "method"},
		{"", "unknown", "unknown"},
		{"/onlyservice", "unknown", "onlyservice"},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			service, method := parseFullMethodName(test.input)
			assert.Equal(t, test.expectedSvc, service)
			assert.Equal(t, test.expectedMethod, method)
		})
	}
}

func TestValidationResult(t *testing.T) {
	t.Run("new validation result is valid", func(t *testing.T) {
		result := NewValidationResult()
		assert.True(t, result.Valid)
		assert.False(t, result.HasErrors())
		assert.Empty(t, result.Error())
	})

	t.Run("add error makes result invalid", func(t *testing.T) {
		result := NewValidationResult()
		result.AddError(errors.New("test error"))

		assert.False(t, result.Valid)
		assert.True(t, result.HasErrors())
		assert.Contains(t, result.Error(), "test error")
	})

	t.Run("add field error", func(t *testing.T) {
		result := NewValidationResult()
		result.AddFieldError("field1", errors.New("field error"))

		assert.False(t, result.Valid)
		assert.True(t, result.HasErrors())
		assert.Contains(t, result.Error(), "field1: field error")
	})

	t.Run("multiple errors", func(t *testing.T) {
		result := NewValidationResult()
		result.AddError(errors.New("error1"))
		result.AddError(errors.New("error2"))
		result.AddFieldError("field1", errors.New("field error"))

		errorStr := result.Error()
		assert.Contains(t, errorStr, "error1")
		assert.Contains(t, errorStr, "error2")
		assert.Contains(t, errorStr, "field1: field error")
	})
}

func TestChainBuilder(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	t.Run("default chain config", func(t *testing.T) {
		config := DefaultChainConfig("test-service", logger)

		assert.Equal(t, "test-service", config.ServiceName)
		assert.Equal(t, logger, config.Logger)
		assert.True(t, config.EnableRecovery)
		assert.True(t, config.EnableTracing)
		assert.True(t, config.EnableLogging)
		assert.True(t, config.EnableMetrics)
		assert.True(t, config.EnableValidation)
	})

	t.Run("build unary chain", func(t *testing.T) {
		config := DefaultChainConfig("test-service", logger)
		interceptors := BuildUnaryChain(config)

		// 应该有5个拦截器（Recovery, Tracing, Logging, Metrics, Validation）
		assert.Len(t, interceptors, 5)
	})

	t.Run("build stream chain", func(t *testing.T) {
		config := DefaultChainConfig("test-service", logger)
		interceptors := BuildStreamChain(config)

		// 应该有5个拦截器
		assert.Len(t, interceptors, 5)
	})

	t.Run("selective enabling", func(t *testing.T) {
		config := DefaultChainConfig("test-service", logger)
		config.EnableMetrics = false
		config.EnableValidation = false

		interceptors := BuildUnaryChain(config)

		// 应该只有3个拦截器（Recovery, Tracing, Logging）
		assert.Len(t, interceptors, 3)
	})
}

func TestSimpleChain(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	t.Run("fluent interface", func(t *testing.T) {
		chain := NewSimpleChain("test-service", logger).
			WithRecovery(true).
			WithTracing(false).
			WithLogging(true).
			WithMetrics(false).
			WithValidation(true)

		unaryInterceptors := chain.BuildUnary()
		streamInterceptors := chain.BuildStream()

		// 应该有3个拦截器（Recovery, Logging, Validation）
		assert.Len(t, unaryInterceptors, 3)
		assert.Len(t, streamInterceptors, 3)
	})
}

func TestQuickStart(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	unaryInterceptors, streamInterceptors := QuickStart("test-service", logger)

	assert.Len(t, unaryInterceptors, 5)
	assert.Len(t, streamInterceptors, 5)
}

func TestPresets(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	t.Run("production chain", func(t *testing.T) {
		config := Presets.ProductionChain("test-service", logger)
		assert.True(t, config.EnableRecovery)
		assert.True(t, config.EnableTracing)
		assert.True(t, config.EnableLogging)
		assert.True(t, config.EnableMetrics)
		assert.True(t, config.EnableValidation)
	})

	t.Run("testing chain", func(t *testing.T) {
		config := Presets.TestingChain("test-service", logger)
		assert.True(t, config.EnableRecovery)
		assert.False(t, config.EnableTracing)
		assert.True(t, config.EnableLogging)
		assert.False(t, config.EnableMetrics)
		assert.True(t, config.EnableValidation)
	})

	t.Run("minimal chain", func(t *testing.T) {
		config := Presets.MinimalChain("test-service", logger)
		assert.True(t, config.EnableRecovery)
		assert.False(t, config.EnableTracing)
		assert.False(t, config.EnableLogging)
		assert.False(t, config.EnableMetrics)
		assert.True(t, config.EnableValidation)
	})
}

// 基准测试
func BenchmarkRecoveryInterceptor(b *testing.B) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
	interceptor := RecoveryInterceptor(logger)
	ctx := context.Background()
	req := &mockRequest{Message: "test"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = interceptor(ctx, req, mockServerInfo, mockHandler(false, false))
	}
}

func BenchmarkValidationInterceptor(b *testing.B) {
	interceptor := ValidationInterceptor()
	ctx := context.Background()
	req := &mockRequest{Message: "test"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = interceptor(ctx, req, mockServerInfo, mockHandler(false, false))
	}
}

func BenchmarkFullChain(b *testing.B) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
	unaryInterceptors, _ := QuickStart("benchmark-service", logger)

	// 创建一个链式拦截器（简化版本）
	chainedInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 依次调用所有拦截器
		currentHandler := handler
		for i := len(unaryInterceptors) - 1; i >= 0; i-- {
			interceptor := unaryInterceptors[i]
			prevHandler := currentHandler
			currentHandler = func(ctx context.Context, req interface{}) (interface{}, error) {
				return interceptor(ctx, req, info, prevHandler)
			}
		}
		return currentHandler(ctx, req)
	}

	ctx := context.Background()
	req := &mockRequest{Message: "test"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = chainedInterceptor(ctx, req, mockServerInfo, mockHandler(false, false))
	}
}

// 辅助函数测试
func TestValidateMessage(t *testing.T) {
	t.Run("valid message", func(t *testing.T) {
		msg := &mockRequest{Message: "test", ShouldErr: false}
		err := ValidateMessage(msg)
		assert.NoError(t, err)
	})

	t.Run("invalid message", func(t *testing.T) {
		msg := &mockRequest{Message: "test", ShouldErr: true}
		err := ValidateMessage(msg)
		assert.Error(t, err)
	})

	t.Run("non-validator message", func(t *testing.T) {
		msg := "not a validator"
		err := ValidateMessage(msg)
		assert.NoError(t, err) // 不是 validator，返回 nil
	})
}

func TestValidateMessageDetailed(t *testing.T) {
	t.Run("valid message", func(t *testing.T) {
		msg := &mockRequest{Message: "test", ShouldErr: false}
		result := ValidateMessageDetailed(msg)
		assert.True(t, result.Valid)
		assert.False(t, result.HasErrors())
	})

	t.Run("invalid message", func(t *testing.T) {
		msg := &mockRequest{Message: "test", ShouldErr: true}
		result := ValidateMessageDetailed(msg)
		assert.False(t, result.Valid)
		assert.True(t, result.HasErrors())
	})
}
