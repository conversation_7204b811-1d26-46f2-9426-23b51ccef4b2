好的，遵照您的指示。我将为您生成一份专门针对 **`blockchain-gateway-service`** 的、极致细化的、生产级的**软件需求规格说明书 (SRS)**。

这份文档将详细定义这个服务的核心职责、接口、多链适配策略、性能与安全要求，使其成为平台与所有区块链交互的**唯一、安全、高效的网关**。

---
### CINA.CLUB - `blockchain-gateway-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-28**  
**文档负责人:** [Web3技术负责人/架构师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心架构与流程](#3-核心架构与流程)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB平台需要与多个异构的区块链网络（如Ethereum, BSC, Polygon, Solana等）进行交互，以支持其Web3钱包功能。直接在客户端或多个后端服务中分别实现与各条链的RPC通信，会导致代码冗余、维护困难、安全风险分散和成本失控。`blockchain-gateway-service` 的目的在于构建一个**统一的、高可用的、与具体链无关的区块链交互网关**。它通过抽象和封装对不同链RPC节点的调用，为平台所有需要链上数据的服务和客户端提供一个**标准化的、可缓存的、高性能的**访问层。

#### 1.2. 服务范围
本服务 **负责**:
*   **多链RPC节点管理**:
    *   管理与多条链、多个RPC提供商（如Infura, Alchemy, 或自建节点）的连接。
    *   实现对RPC节点的**健康检查、负载均衡和故障切换**。
*   **统一的区块链数据查询**:
    *   提供统一的API，用于查询**账户余额**（原生币和Token）。
    *   提供统一的API，用于获取**交易历史**和**交易详情/回执**。
    *   提供统一的API，用于估算**网络费用 (Gas Fee)**。
    *   提供统一的API，用于调用智能合约的**只读方法**。
*   **交易广播**:
    *   提供一个统一、安全的端点，用于接收**客户端已签名**的交易，并将其广播到相应的区块链网络。
*   **结果缓存与标准化**:
    *   对高频的、非时变的链上查询结果进行**缓存**，以降低延迟和RPC调用成本。
    *   将不同链返回的异构数据，标准化为平台统一的`Transaction`, `TokenBalance`等模型。

本服务 **不负责**:
*   **交易签名或任何私钥操作**: 本服务**绝不**接触任何用户私钥。它只处理已签名的交易。
*   **钱包元数据管理**: 如钱包名称、自定义代币列表，由`wallet-meta-service`负责。
*   **市场数据**: 如代币价格，由`market-data-service`负责。
*   **复杂的链上数据索引**: 对于需要复杂查询的场景（如全量的NFT持有者），应由专门的索引服务或The Graph完成。本服务只负责基础的、点对点的RPC查询。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 通过API Gateway调用本服务，获取链上数据和广播交易。
*   **`notification-service`**: 调用本服务查询交易状态，以发送确认通知。
*   **`wallet-meta-service`**: (可选) 调用本服务获取代币元数据。
*   **其他内部微服务**: 任何需要与区块链交互的服务。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`blockchain-gateway-service` 是CINA.CLUB平台与**去中心化世界的唯一桥梁**。它是一个典型的**代理和适配器**模式的服务，将底层区块链的复杂性和异构性对上层业务完全屏蔽。其性能、可靠性和成本控制能力，直接决定了平台Web3功能的整体用户体验和运营效率。

#### 2.2. 主要功能概述
*   支持多链、多RPC提供商的适配器与智能路由。
*   统一的、标准化的区块链查询与交易广播API。
*   强大的、针对RPC调用的结果缓存机制。
*   为平台提供安全、可靠、经济的链上交互能力。

---

### 3. 核心架构与流程

#### 3.1 客户端查询BSC链上余额的流程

```mermaid
sequenceDiagram
    participant Client
    participant BlockchainGateway as BC_Gateway
    participant Redis
    participant BSC_Node_Provider as "BSC RPC Provider"

    Client->>BC_Gateway: 1. GET /balance?chain=bsc&address=0x...
    
    BC_Gateway->>Redis: 2. Check cache for key "balance:bsc:0x..."
    alt Cache Hit
        Redis-->>BC_Gateway: 3a. (Cached balance data)
        BC_Gateway-->>Client: 4a. Return cached data
    else Cache Miss
        BC_Gateway->>BC_Gateway: 3b. Find BSCChainProvider adapter
        BC_Gateway->>BSC_Node_Provider: 4b. [JSON-RPC] eth_getBalance & multicall for token balances
        BSC_Node_Provider-->>BC_Gateway: 5b. (Raw balance data)
        
        BC_Gateway->>BC_Gateway: 6b. Standardize data into internal model
        BC_Gateway->>Redis: 7b. Set cache for "balance:bsc:0x..." with a short TTL
        BC_Gateway-->>Client: 8b. Return standardized data
    end
```
---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 多链适配层
*   **FR4.1.1 (Provider接口)**: 必须定义一个统一的`ChainProvider`接口，包含`GetBalance`, `GetTransaction`, `EstimateGas`, `BroadcastTransaction`等标准方法。
*   **FR4.1.2 (具体实现)**:
    *   **EVM链**: 必须为EVM兼容链（Ethereum, BSC, Polygon等）提供一个通用的`EVMProvider`实现，它封装了对`go-ethereum`客户端的调用。
    *   **非EVM链**: 必须为每一条支持的非EVM链（如Solana, TRON）提供一个独立的`Provider`实现，封装其特定的SDK。
*   **FR4.1.3 (工厂模式)**: 系统必须有一个`ProviderFactory`，能根据请求中的`chain`参数，动态地返回对应的`ChainProvider`实例。

#### 4.2. RPC节点管理
*   **FR4.2.1 (多节点配置)**: 对于每条链，必须支持配置**一个主RPC节点**和**多个备用RPC节点**。
*   **FR4.2.2 (健康检查)**: 必须有后台任务，定期对所有配置的RPC节点进行健康检查（如查询最新块高）。
*   **FR4.2.3 (故障切换)**: 当对主节点的RPC调用连续失败或超时时，**熔断器(Circuit Breaker)**必须打开，并自动将后续请求路由到健康的备用节点。

#### 4.3. 统一查询功能
*   **FR4.3.1 (余额查询)**: `GetBalance`接口必须能同时返回原生币和请求的多个Token的余额。
    *   **优化**: 对于EVM链，必须使用**Multicall**智能合约来批量查询多个Token的余额，将多次RPC调用合并为一次。
*   **FR4.3.2 (交易历史)**: `GetTransactions`接口应优先从**区块链浏览器API**（如Etherscan, BscScan）获取交易历史，因为这比扫描区块更高效。RPC调用作为备用方案。
*   **FR4.3.3 (数据标准化)**: 所有从不同链或API源获取的数据，在返回给客户端前，都必须被转换为平台统一的Protobuf模型（如`common.Transaction`, `common.TokenBalance`）。

#### 4.4. 交易广播与事件
*   **FR4.4.1 (安全广播)**: `BroadcastTransaction`接口只接收**十六进制编码的、已签名的原始交易字符串**。它**绝不能**处理任何与私钥相关的逻辑。
*   **FR4.4.2 (事件发布)**: 交易成功广播到网络并获取到`tx_hash`后，系统必须立即向Kafka发布一个`TransactionBroadcasted`事件，供下游服务（如`notification-service`）进行链上状态的异步跟踪。

#### 4.5. 缓存策略
*   **FR4.5.1 (缓存内容)**: 必须对`GetBalance`, `GetTransactions`, `GetTransactionByHash`等高频只读API的结果进行缓存。
*   **FR4.5.2 (缓存TTL)**: 缓存的TTL（生存时间）必须是可配置的且相对较短（如15-60秒），以在性能和数据新鲜度之间取得平衡。
*   **FR4.5.3 (缓存Key)**: 缓存Key必须是标准化的，例如 `balance:{chain}:{address}`。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部/客户端 gRPC API接口
*   **Package**: `hina.v1.blockchain_gateway`
*   **认证**: 用户JWT (用于速率限制和审计) / S2S Auth。
*   **核心RPC**:
    ```protobuf
    service BlockchainGatewayService {
      // 查询类
      rpc GetBalance(GetBalanceRequest) returns (GetBalanceResponse);
      rpc GetTransactions(GetTransactionsRequest) returns (GetTransactionsResponse);
      rpc GetTransactionByHash(GetTransactionByHashRequest) returns (Transaction);
      rpc EstimateGas(EstimateGasRequest) returns (EstimateGasResponse);
      rpc CallContract(CallContractRequest) returns (CallContractResponse); // 只读调用

      // 写操作类
      rpc BroadcastTransaction(BroadcastTransactionRequest) returns (BroadcastTransactionResponse);
    }
    ```

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型
*   **无核心持久化数据库**: 本服务主要是一个**无状态的代理和缓存层**。
*   **配置**: RPC节点URL、API Key等配置，通过`pkg/config`从配置文件或环境变量加载。敏感的API Key必须加密存储或从Secrets Manager获取。
*   **缓存 (Redis)**:
    *   `Key`: `bcs_gateway:{function}:{chain}:{params_hash}`
    *   `Value`: 序列化后的Protobuf响应体。

---

### 7. 非功能性需求 (Non-Functional Requirements)

*   **NFR7.1 (性能)**:
    *   **API延迟 (缓存命中)**: P99 < 50ms。
    *   **API延迟 (缓存未命中)**: P99延迟主要取决于下游RPC节点的响应速度，本服务的额外开销应 < 30ms。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。
    *   **容错**: **必须**实现**FR4.2.3**中定义的RPC节点故障切换机制，这是保证高可用的关键。
*   **NFR7.3 (可扩展性)**:
    *   服务应为无状态，易于水平扩展以应对高并发查询。
*   **NFR7.4 (安全性)**:
    *   **防滥用**: 所有API都必须有严格的速率限制，以防止对RPC节点资源的滥用攻击。
    *   **凭证安全**: 所有RPC节点和区块链浏览器API的Key都必须安全存储。
    *   **无私钥**: 再次强调，本服务的设计原则是**绝不触碰私钥**。

---

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **核心库**:
    *   **EVM链**: `go-ethereum` (`ethclient`)。
    *   **Solana**: `gagliardetto/solana-go`。
    *   **TRON**: 需要找到或封装一个与TRON gRPC API交互的库。
*   **适配器模式**: 这是实现多链支持的核心架构模式。
*   **RPC节点服务**: 优先使用**Alchemy**或**Infura**等专业的节点服务商，因为它们提供了更高的稳定性、性能和开发者工具。自建节点作为备用或用于降低成本。

---
这份SRS为`blockchain-gateway-service`的设计和实现提供了坚实、全面的指导。通过将复杂的、异构的区块链交互封装在一个统一、高性能、高可用的网关后面，它极大地简化了平台其他部分的开发，并为CINA.CLUB的Web3功能提供了可靠的基础设施。