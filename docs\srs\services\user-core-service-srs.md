好的，遵照您的指示。我将仔细阅读您提供的所有附件，特别是 `user-core-service-srs.md` (v3.0) 和 `cina.club monorepo srs.md` (v6.0)，然后生成一份**最终的、集大成的、详细完整的生产级** `user-core-service` 需求规格说明书。

这份文档将：
*   **完全包含** `user-core-service-srs.md` v3.0 中定义的所有功能需求，确保无一遗漏。
*   **深度融合** `cina.club monorepo srs.md` v6.0 中定义的平台级架构原则、技术选型和微服务间的协同关系。
*   **极致细化** 服务在Monorepo中的定位、依赖关系和接口契约，使其成为一份可直接用于指导开发、测试和运维的权威蓝图。

---

### CINA.CLUB - user-core-service 需求规格说明书

**版本: 4.0 (生产级最终定义, 整合平台架构)**  
**发布日期: 2025-06-25**  
**文档负责人:** [平台总架构师/Cina.Club]  
**审批人:** [CTO/Cina.Club]

#### 修订历史
| 版本 | 日期       | 修订人     | 修订描述                                                     |
|------|------------|------------|--------------------------------------------------------------|
| 3.0  | 2025-06-16 | Cina.Club  | 引入用户等级系统、会员体系、设备管理和信誉分。                 |
| 4.0  | 2025-06-25 | Cina.Club  | **最终版。与平台总体架构(v6.0)对齐，细化了服务协同、技术约束和Monorepo集成细节，包含v3.0所有功能。** |

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束与选型建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
`user-core-service` 是 CINA.CLUB 平台所有用户数据、身份认证和授权策略的 **绝对单一事实来源 (Absolute Single Source of Truth)**。它通过整合认证、账户管理、角色权限和详细资料，旨在提供一个统一、高效、极度安全的用户核心能力中枢。本服务负责从用户注册、登录、会话管理，到账户状态、等级成长、会员权益、角色权限、以及个性化资料和偏好的完整生命周期管理，为整个平台提供坚如磐石的用户基础。

#### 1.2. 服务范围
本服务 **负责**:
*   **注册**: 支持手机号、邮箱、社交登录。
*   **认证**: 验证用户凭证（密码、SMS验证码、2FA码），实现防暴力破解。
*   **会话管理**: 签发、刷新和吊销JWT（Access & Refresh Token），提供JWKS端点。
*   **设备管理**: 追踪用户登录设备，支持用户管理和强制下线。
*   **账户管理**: 管理用户核心身份（`userId`, `username`）、账户状态（`ACTIVE`, `SUSPENDED`, `PENDING_DELETION`）。
*   **用户等级系统**: 基于在线时长和活跃天数，计算和管理用户等级。
*   **会员与靓号体系**: 管理用户的会员状态、等级和特权，以及靓号（Vanity Username）的分配与管理。
*   **资料管理**: 管理用户核心资料（昵称、头像、简介）和扩展资料（地址簿、技能、作品集）。
*   **偏好管理**: 管理用户的应用及通知偏好。
*   **角色与权限管理 (RBAC)**: 定义和管理平台的角色与权限，并将其分配给用户。
*   **实名认证 (KYC)**: 对接第三方服务，处理用户的实名认证流程。
*   **官方认证**: 管理个人和企业机构的官方认证申请与状态。
*   **信誉与安全分**: 初步记录和管理用户的信誉积分，为风控提供依据。
*   **事件发布**: 将关键的用户生命周期事件发布到平台事件总线（Kafka）。

本服务 **不负责**:
*   **业务数据**: 任何具体的业务数据，如订单(`service-offering-service`)、聊天记录(`chat-api-service`)、帖子(`community-forum-service`)等。
*   **社交关系**: 关注/粉丝、好友、拉黑等社交图谱，由 `social-service` 负责。
*   **家庭关系**: 族谱、亲缘关系，由 `family-tree-service` 负责。
*   **游戏化激励**: 任务成就、徽章、非货币积分，由 `gamification-service` 负责。
*   **虚拟货币账本**: 灵境币的交易与账本，由 `cina-coin-ledger-service` 负责。
*   **文件存储**: 用户头像、认证材料等文件的二进制存储，由 `file-storage-service` 负责（本服务只存URL或Key）。
*   **商业化逻辑**: 会员产品的详细计费与订阅生命周期，由 `billing-service` 负责。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用**: 通过API Gateway进行注册、登录、管理个人资料、查看等级、管理设备等。
*   **`api-gateway`**: 调用本服务的JWKS端点验证所有传入请求的JWT。
*   **所有其他CINA.CLUB内部微服务**: 调用本服务的内部gRPC API查询用户信息、状态、等级、会员、角色和权限。这是平台最被高频调用的内部服务。
*   **CINA.CLUB平台管理员**: 通过管理后台调用本服务API进行用户管理、认证审核、会员管理等。

#### 1.4. 定义与缩略语
*   **JWT / JWKS**: JSON Web Token / JSON Web Key Set.
*   **RBAC**: Role-Based Access Control.
*   **KYC**: Know Your Customer.
*   **PII**: Personally Identifiable Information.
*   **活跃天 (Active Day)**: 用户在一天内（自然日，UTC）有任何有效在线行为（如登录、刷新令牌）。
*   **靓号 (Vanity Username)**: 用户购买或获赠的、具有特殊含义或简短易记的自定义用户名。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`user-core-service` 是平台的“**户籍、身份认证、成长与信誉中心**”。所有对用户身份的确认、权限的校验、等级和会员状态的判定以及个性化信息的获取都源于此服务。其稳定性、安全性和性能直接决定了整个平台的用户体验和安全基石。

#### 2.2. 架构原则对齐
本服务严格遵循平台总体架构原则：
*   **Go-Centric**: 使用Go语言实现所有业务逻辑。
*   **API优先**: 所有功能通过`core/api/proto/v1/user_core.proto`中定义的gRPC接口暴露。
*   **事件驱动**: 通过向Kafka发布领域事件，与下游服务（如`activity-feed-service`, `analytics-service`）解耦。
*   **Monorepo集成**: 作为`services/user-core-service`存在，依赖`core/`和`pkg/`中的共享库。

#### 2.3. 主要功能概述
*   统一、安全的用户注册、登录、会话管理。
*   基于活跃度的用户等级成长体系。
*   支持会员和靓号的商业化能力。
*   全面的用户账户、资料、偏好、角色权限的CRUD管理。
*   支持实名认证 (KYC) 和个人/企业官方认证。
*   通过API和事件，为平台其他部分提供权威的用户数据。

### 3. 核心流程图

#### 3.1. 用户注册与引导
```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant UserCoreService as UCS
    participant Redis
    participant SMS Gateway
    participant DB

    Client->>API Gateway: POST /auth/register/request-sms-code (phoneNumber)
    API Gateway->>UCS: (forward request)
    UCS->>UCS: 1. 生成6位验证码，设置5分钟有效期
    UCS->>Redis: 2. SETEX `sms_code:138...` 300 `123456`
    UCS->>SMS Gateway: 3. 发送验证码 '123456' 到 '138...'
    SMS Gateway-->>UCS: 发送成功
    UCS-->>API Gateway: { "success": true }
    API Gateway-->>Client: { "success": true }

    Note over Client: 用户输入验证码和密码

    Client->>API Gateway: POST /auth/register/by-phone (phoneNumber, password, code)
    API Gateway->>UCS: (forward request)
    UCS->>Redis: 4. GET `sms_code:138...`
    alt 验证码正确且未过期
        Redis-->>UCS: `123456`
        UCS->>DB: 5. 检查手机号是否已存在
        DB-->>UCS: 不存在
        UCS->>UCS: 6. 生成唯一8位username, Hash密码(Argon2id)
        UCS->>DB: 7. 在事务中创建User, UserAuth, UserContact, UserProfile, UserGrowth等记录
        UCS->>Redis: 8. DEL `sms_code:138...`
        UCS->>UCS: 9. 发布 UserRegisteredEvent
        UCS-->>API Gateway: 注册成功
        API Gateway-->>Client: 201 Created
    else 验证码错误或过期
        Redis-->>UCS: nil or wrong value
        UCS-->>API Gateway: 错误: 验证码无效
        API Gateway-->>Client: 400 Bad Request
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 用户注册与引导
*   **FR4.1.1 (手机号注册)**: 核心流程，如流程图所示。系统必须对请求进行速率限制，并校验密码强度（如：长度>=12, 含大小写、数字、特殊符号）。
*   **FR4.1.2 (用户名生成)**: 注册成功时，系统必须为其自动生成一个唯一的、随机的8位纯数字 `username`。生成逻辑必须包含数据库唯一性约束和应用层重试机制。
*   **FR4.1.3 (账户初始化)**: 注册成功后，系统必须在单次数据库事务中，创建完整的用户记录，包括核心身份、认证凭证、初始状态(`ACTIVE`)、默认角色(`USER`)、空的Profile、空的偏好设置、以及**初始化的成长记录** (`UserGrowth` 表)。随后发布 `UserRegisteredEvent` 事件。
*   **FR4.1.4 (社交登录 - 可选)**: 支持通过第三方OAuth提供商进行登录或注册。
*   **FR4.1.5 (Email注册 - 可选)**: 提供类似的基于邮件验证码或验证链接的注册流程。

#### 4.2. 用户认证与会话管理
*   **FR4.2.1 (登录)**: 支持“手机号/邮箱/用户名+密码”登录。必须检查账户状态是否为 `ACTIVE`。必须实现防暴力破解机制（基于IP和用户ID的速率限制、失败N次后临时锁定或要求人机验证）。
*   **FR4.2.2 (JWT令牌管理)**:
    *   **Access Token**: 使用非对称加密 (RS256/ES256)，生命周期短（15分钟），Payload包含`userId`, `username`, `roles`, `deviceId`等。
    *   **Refresh Token**: 生命周期长（30天），必须是安全的、不透明的随机字符串。其哈希值存入DB，与用户、设备ID、User-Agent关联，以便实现单点/全部登出。
    *   **令牌刷新**: `/auth/token/refresh` 端点必须实现刷新令牌旋转 (Rotation)，即每次使用后都颁发一个新的Refresh Token，并使旧的失效。
    *   **JWKS端点**: `/.well-known/jwks.json` 必须公开，供其他服务和API Gateway验证Access Token签名。
*   **FR4.2.3 (密码管理)**: 支持忘记密码重置和修改密码，密码必须使用强哈希算法（**Argon2id**）加盐存储。
*   **FR4.2.4 (双因素认证 - 2FA, 可选)**: 支持基于TOTP的2FA。

#### 4.3. 设备管理
*   **FR4.3.1 (设备追踪)**: 每次成功登录或刷新令牌，都必须记录或更新设备信息（`deviceId`, `deviceType`, `osVersion`, `lastSeenIp`, `lastSeenAt`）。
*   **FR4.3.2 (设备列表)**: 提供API供用户查看其所有活跃设备列表。
*   **FR4.3.3 (远程登出)**: 提供API供用户远程登出（吊销）指定设备或除当前设备外的所有设备。

#### 4.4. 用户等级系统 (QQ-like)
*   **FR4.4.1 (等级计算)**:
    *   用户等级 (`Level`) 由累计活跃天数 (`TotalActiveDays`) 决定。
    *   等级公式：`Level = floor(sqrt(TotalActiveDays))` 或其他可配置的非线性函数。
*   **FR4.4.2 (活跃天数计算)**:
    *   一个后台批处理任务每日（UTC 00:05）运行。
    *   该任务扫描所有在前一日有在线记录的用户（通过检查心跳记录或`chat-websocket-server`的在线状态日志），并为其`TotalActiveDays`加1。
*   **FR4.4.3 (在线时长记录与奖励)**:
    *   客户端需定期（如每5分钟）向一个心跳API (`/me/heartbeat`) 发送请求，以证明其在线。
    *   `user-core-service` 记录每次心跳，并更新一个当日的在线时长累加器（存储在Redis中）。
    *   每日批处理任务会根据用户前一日的总在线时长，给予额外的活跃天数奖励。例如：
        *   在线满2小时，`TotalActiveDays`额外 +0.2天。
        *   在线满5小时，`TotalActiveDays`额外 +0.5天。
        *   在线时长奖励上限为+0.5天/日。
*   **FR4.4.4 (会员加速)**:
    *   如果用户是会员，每日活跃天数计算会有加速。例如，VIP1级用户，基础活跃天按1.2倍计算，在线时长奖励按1.5倍计算。
    *   加速倍率由 `billing-service` 提供的会员等级决定。

#### 4.5. 会员与靓号体系
*   **FR4.5.1 (会员状态)**: 系统需能存储和管理用户的会员状态（`VIPLevel`, `VIPExpiresAt`）。此状态通常由 `billing-service` 在用户购买/续费成功后，通过发布事件或调用内部API来更新。
*   **FR4.5.2 (靓号管理)**:
    *   提供内部API供管理员创建靓号（Vanity Username）池。
    *   用户（通常是会员）可以通过 `billing-service` 购买或通过活动获得靓号。
    *   提供API供用户将其 `username` 更换为已拥有的靓号。
    *   靓号可以有过期时间，过期后如果未续费，系统可将其回收。

#### 4.6. 账户、资料与偏好管理
*   **FR4.6.1 (核心资料)**: 用户可以更新 `displayName` (昵称), `avatarUrl`, `bio` (简介) 等。
*   **FR4.6.2 (账户状态管理)**: 管理员可更改用户状态，用户可发起账户删除流程。
*   **FR4.6.3 (扩展资料)**: 用户可管理地址簿、技能列表、作品集。
*   **FR4.6.4 (偏好设置)**: 用户可管理应用、通知、隐私等偏好。
*   **FR4.6.5 (公开资料)**: 提供API返回用户的公开资料，其内容由用户的隐私偏好设置决定。

#### 4.7. 角色与权限管理 (RBAC)
*   **FR4.7.1 (定义)**: 支持管理员定义系统范围的`Permission`和`Role`。
*   **FR4.7.2 (分配)**: 支持管理员为用户分配角色，为角色分配权限。
*   **FR4.7.3 (查询)**: 提供内部API，能根据`userId`高效查询其拥有的所有权限列表。

#### 4.8. 实名认证 (KYC) 与官方认证
*   **FR4.8.1 (KYC流程)**: 用户提交真实姓名和身份证号 -> 服务调用第三方接口验证 -> 根据结果更新用户KYC状态。用户的原始PII（身份证号、姓名）严禁明文存储。
*   **FR4.8.2 (官方认证)**: 用户提交申请材料（链接） -> 管理员通过后台审核 -> 系统更新认证状态并通知用户。

#### 4.9. 信誉与安全分
*   **FR4.9.1 (分数记录)**: 系统需维护一个`reputation_score`字段。
*   **FR4.9.2 (分数调整)**: 此分数由其他服务（如`review-service`, `content-moderation-service`）通过发布事件来触发调整。本服务消费事件并更新分数。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/外部RESTful API接口
*   **版本**: `/api/v1`
*   **Auth模块 (`/auth/*`)**: `POST /register/request-sms-code`, `POST /register/by-phone`, `POST /login`, `POST /token/refresh`, `POST /logout`, `GET /.well-known/jwks.json`
*   **Users模块 (`/users/me/*`)**: `GET /me`, `PATCH /me`, `DELETE /me`, `POST /me/heartbeat`, `GET /me/devices`, `DELETE /me/devices/{deviceId}`, `POST /me/kyc`, `POST /me/verifications`, `/me/addresses`, `/me/skills`等。

#### 5.2. 内部服务API接口 (S2S)
*   **契约定义**: `core/api/proto/v1/user_core.proto`
*   **认证**: 严格S2S认证 (mTLS + 服务级JWT)。
*   **核心RPC**:
    *   `rpc GetUserDetails(GetUserDetailsRequest) returns (UserDetailsResponse)`: (最高频) 获取用户完整核心信息。
    *   `rpc BatchGetUsersSnippet(BatchGetUsersSnippetRequest) returns (BatchGetUsersSnippetResponse)`: 批量获取用户摘要信息。
    *   `rpc UpdateMembership(UpdateMembershipRequest) returns (UserMembership)`: (由 `billing-service` 调用) 更新用户会员状态。
    *   `rpc CheckPermission(CheckPermissionRequest) returns (CheckPermissionResponse)`: 检查用户权限。

#### 5.3. 消息队列事件契约
*   **出站 (发布)**: `UserRegisteredEvent`, `UserLevelUpEvent`, `UserAccountStatusChangedEvent`, `UserProfileUpdatedEvent`, `UserAccountHardDeletedEvent`。
*   **入站 (消费)**: `MembershipPurchasedEvent` (from `billing-service`), `ReputationScoreChangeEvent` (from other services)。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`users`**: `id (PK, UUID)`, `username (UNIQUE)`, `status (INDEX)`.
*   **`user_auths`**: `user_id (PK)`, `password_hash`.
*   **`user_contacts`**: `user_id (PK)`, `email (UNIQUE)`, `phone_number (UNIQUE)`.
*   **`user_profiles`**: `user_id (PK)`, `display_name`, `avatar_url`, `bio`.
*   **`user_growths`**: `user_id (PK, FK)`, `level`, `total_active_days`, `last_active_date`, `today_online_duration_sec`.
*   **`user_memberships`**: `user_id (PK, FK)`, `vip_level`, `expires_at`.
*   **`refresh_tokens`**: `id (PK)`, `user_id (INDEX)`, `token_hash`, `device_id`, `expires_at`.
*   **`user_devices`**: `id (PK)`, `user_id (INDEX)`, `device_id`, `device_type`, `os_version`, `last_seen_ip`, `last_seen_at`.
*   **`roles`, `permissions`, `role_permissions`, `user_roles`**: RBAC模型表。
*   **`user_addresses`, `user_skills`, `user_portfolio_items`**: 扩展资料表。
*   **`user_kyc_infos`, `official_verifications`**: 认证模型表。

#### 6.2. 数据缓存 (Redis)
*   **缓存内容**: 内部查询API (`GetUserDetails`, `BatchGetUsersSnippet`) 的结果。
*   **Key Schema**: `user:details:{userId}`, `user:snippet:{userId}`.
*   **失效策略**: Cache-Aside + 事件驱动失效。当`user-core-service`发布`UserProfileUpdatedEvent`等事件时，一个专门的消费者负责清理相关的Redis缓存。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **认证API**: P99延迟 < 100ms。
*   **内部查询API**: P99延迟 < 30ms (缓存命中)。
*   **心跳API**: P99 < 20ms。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.99%。
*   **数据备份**: RPO < 5min, RTO < 1h。

#### 7.3. 可扩展性需求
*   服务实例无状态，可水平扩展。
*   数据库配置读写分离。

#### 7.4. 安全性需求 (最高优先级)
*   **密码安全**: 使用 Argon2id。
*   **令牌安全**: 严格的JWT和Refresh Token管理策略。
*   **PII保护**: 对敏感字段进行应用层加密。
*   **API安全**: 速率限制，防注入。

#### 7.5. 可观测性需求
*   **日志**: 结构化JSON日志，记录所有关键操作。
*   **指标**: API性能、认证成功/失败率、缓存命中率、等级计算任务状态。
*   **告警**: 对关键指标设置告警，如登录失败率激增、高延迟、任务失败。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **Monorepo集成**: 服务位于`services/user-core-service/`，依赖`core/`和`pkg/`。
*   **数据库**: PostgreSQL。
*   **缓存**: Redis。
*   **密码哈希**: 强制使用 Argon2id。
*   **后台任务**: 等级计算的每日批处理任务，使用Kubernetes CronJob。

---
这份最终的、版本4.0的SRS文档，为`user-core-service`提供了一个全面、详尽、且与CINA.CLUB平台整体架构深度融合的设计蓝图。它不仅包含了所有必要的功能，更在性能、安全、可靠性和可扩展性等方面提出了生产级的严格要求，能够支撑一个世界级应用的用户体系。