# CINA.CLUB Platform - System Base Kustomization
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: cina-club-system-base
  annotations:
    description: "Base configuration for CINA.CLUB platform system components"

# Common labels applied to all resources
commonLabels:
  platform: cina-club
  managed-by: platform-engineering
  environment: base

# Namespace for all resources
namespace: system

# Resource files
resources:
  # Kong Gateway Platform
  - kong/namespace.yaml
  - kong/control-plane/deployment.yaml
  - kong/data-plane/deployment.yaml
  - kong/data-plane/service.yaml
  
  # Monitoring Stack (using complete prometheus.yaml)
  - monitoring/prometheus.yaml
  
  # Logging Platform
  - logging/fluentd-daemonset.yaml
  - logging/secrets.yaml
  
  # Certificate Management
  - cert-manager/cert-manager.yaml
  - cert-manager/secure-config.yaml

# Configuration patches
patchesStrategicMerge:
  - patches/resource-limits.yaml
  - patches/security-context.yaml

# Config map and secret generators
configMapGenerator:
  - name: system-config
    literals:
      - platform.name=CINA.CLUB
      - platform.version=v1.0.0
      - environment=production
      - log.level=info

secretGenerator:
  - name: platform-secrets
    literals:
      - admin.password=changeme-in-production
    type: Opaque

# Images configuration
images:
  - name: kong/kubernetes-ingress-controller
    newTag: "2.12.0"
  - name: kong
    newTag: "3.4.2-alpine"
  - name: prom/prometheus
    newTag: "v2.47.2"
  - name: grafana/grafana
    newTag: "10.2.0"
  - name: fluent/fluentd-kubernetes-daemonset
    newTag: "v1.16-debian-elasticsearch7-1"
  - name: quay.io/jetstack/cert-manager-controller
    newTag: "v1.13.2"

# Resource transformations
replicas:
  - name: kong-proxy
    count: 3
  - name: prometheus
    count: 2

# Validation
buildMetadata:
  - buildDate
  - buildVersion
  - gitCommit 