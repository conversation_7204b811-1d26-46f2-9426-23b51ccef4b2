/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Privacy settings screen following WeChat design.
 * Manages user privacy preferences and permissions.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacyScreen(
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var allowFindByPhone by remember { mutableStateOf(true) }
    var allowFindByWeChatId by remember { mutableStateOf(true) }
    var allowFindByQrCode by remember { mutableStateOf(false) }
    var allowWeChatFriendRequests by remember { mutableStateOf(true) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "隐私",
                    fontWeight = FontWeight.Medium
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF4A90E2)
            )
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Add me as contact section
            item {
                Text(
                    text = "加我为联系人时",
                    color = Color(0xFF333333),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Column {
                        // Verification required
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "需要验证",
                                color = Color(0xFF333333),
                                fontSize = 16.sp
                            )
                        }
                    }
                }
            }
            
            // Find me by section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Column {
                        Text(
                            text = "允许别人找到当前企业身份",
                            color = Color(0xFF888888),
                            fontSize = 14.sp,
                            modifier = Modifier.padding(16.dp)
                        )
                        
                        Divider(color = Color(0xFFE0E0E0))
                        
                        // Allow find by group chat
                        PrivacyToggleItem(
                            title = "可通过群聊添加我",
                            isChecked = allowFindByPhone,
                            onCheckedChange = { allowFindByPhone = it }
                        )
                        
                        Divider(
                            modifier = Modifier.padding(start = 16.dp),
                            color = Color(0xFFE0E0E0)
                        )
                        
                        // Allow find by business card
                        PrivacyToggleItem(
                            title = "可通过个人名片添加我",
                            isChecked = allowFindByWeChatId,
                            onCheckedChange = { allowFindByWeChatId = it }
                        )
                        
                        Divider(
                            modifier = Modifier.padding(start = 16.dp),
                            color = Color(0xFFE0E0E0)
                        )
                        
                        // Block WeChat friends list
                        PrivacyToggleItem(
                            title = "获取我的微信好友关系",
                            isChecked = allowFindByQrCode,
                            onCheckedChange = { allowFindByQrCode = it }
                        )
                        
                        Divider(
                            modifier = Modifier.padding(start = 16.dp),
                            color = Color(0xFFE0E0E0)
                        )
                        
                        // Allow friend requests in WeChat
                        PrivacyToggleItem(
                            title = "接受我在微信收到的好友申请",
                            isChecked = allowWeChatFriendRequests,
                            onCheckedChange = { allowWeChatFriendRequests = it }
                        )
                        
                        // Description text
                        Text(
                            text = "开启后，在你绑定的微信账号中收到好友请求时，可选择中企业微信通过验证",
                            color = Color(0xFF888888),
                            fontSize = 12.sp,
                            modifier = Modifier.padding(16.dp)
                        )
                        
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "查看示例",
                                color = Color(0xFF4A90E2),
                                fontSize = 14.sp
                            )
                            
                            Spacer(modifier = Modifier.width(4.dp))
                            
                            Icon(
                                imageVector = Icons.Default.ChevronRight,
                                contentDescription = null,
                                tint = Color(0xFF4A90E2),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
            
            // Contact blacklist section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { /* Navigate to blacklist */ }
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "通讯录黑名单",
                            color = Color(0xFF333333),
                            fontSize = 16.sp,
                            modifier = Modifier.weight(1f)
                        )
                        
                        Icon(
                            imageVector = Icons.Default.ChevronRight,
                            contentDescription = null,
                            tint = Color(0xFFBBBBBB)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun PrivacyToggleItem(
    title: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            color = Color(0xFF333333),
            fontSize = 16.sp,
            modifier = Modifier.weight(1f)
        )
        
        Switch(
            checked = isChecked,
            onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = Color(0xFF4A90E2),
                uncheckedThumbColor = Color.White,
                uncheckedTrackColor = Color(0xFFBBBBBB)
            )
        )
    }
} 