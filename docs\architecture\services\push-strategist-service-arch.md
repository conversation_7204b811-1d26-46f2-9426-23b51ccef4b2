好的，遵照您的指示。我将为您生成一份专门针对 **`push-strategist-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`push-strategist-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**可配置的推送策略管道、与推荐服务的协同进行受众圈选、严格的防打扰过滤层，以及作为一个后台决策系统的高可靠性和可扩展性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `push-strategist-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `push-strategist-service-srs.md` (v1.0)
**核心架构**: 事件驱动 + 可配置的决策管道(Pipeline)

## 1. 概述

`push-strategist-service` 是平台主动用户触达的“**决策大脑**”。它是一个纯粹的**后台决策服务**，其架构设计的核心目标是构建一个**灵活、智能、可靠的推送决策管道**。

*   **灵活**: 运营人员应能通过配置，快速创建和调整推送策略，而无需开发人员介入。
*   **智能**: 决策过程应能充分利用平台的用户画像和推荐算法能力，实现精准的受众选择。
*   **可靠**: 整个决策和下发流程必须是可靠的，能处理各种异常，并对每一次决策都有据可查。

本架构设计通过将每个推送策略实现为一个**可配置的、多阶段的管道(Pipeline)**，并将每个阶段（如受众圈选、过滤）的逻辑封装为可复用的组件，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (决策管道执行流程)

```mermaid
graph TD
    subgraph "触发源 (Triggers)"
        A[Kafka Platform Events]
        B[Cron Job Scheduler]
    end

    subgraph "PushStrategistService"
        style PushStrategistService fill:#e0f7fa
        C[Trigger Listeners<br/><em>(adapter/trigger)</em>]
        D[Application Service<br/><em>(application/service)</em>]
        
        subgraph "Decision Pipeline"
            P1[Stage 1: Audience Selection]
            P2[Stage 2: Pre-send Filtering]
            P3[Stage 3: Command Generation]
        end

        E[Domain Services<br/><em>(e.g., AntiDisturbanceFilter)</em>]
        F[Downstream Clients<br/><em>(adapter/client)</em>]
        G[Kafka Producer<br/><em>(adapter/event)</em>]
    end

    subgraph "依赖服务"
        style "依赖服务" fill:#f3e5f5
        S1[recall-service]
        S2[user-core-service]
        S3[notification-dispatch-service (via Kafka)]
        S4[Redis (for Filtering Cache)]
    end

    A & B -- "1. Trigger with Event/Task" --> C
    C -- "调用" --> D
    
    D -- "2. Loads Strategy & Starts Pipeline" --> P1
    P1 -- "3a. Calls Recall Service" --> F
    F -- "gRPC to" --> S1
    S1 -- "Returns Candidate Users" --> F
    
    P1 -- "3b. (Optional) Rule-based filtering" --> F
    F -- "gRPC to" --> S2
    
    P1 -- "Candidate Users" --> P2
    
    P2 -- "4. Applies Anti-disturbance Rules" --> E
    E -- "4a. Checks User Preferences" --> F --> S2
    E -- "4b. Checks Rate Limits" --> F --> S4
    
    P2 -- "Final Target Users" --> P3
    
    P3 -- "5. Generates DispatchNotificationCommands" --> P3
    P3 -- "6. Publish to Kafka" --> G
    G --> Kafka_Out[(Kafka)]
    Kafka_Out --> S3
```

### 2.2 最终目录结构 (`services/push-strategist-service/`)

```
push-strategist-service/
├── cmd/server/
│   └── main.go                 # API(for admin), 事件消费者, Cron任务触发器
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_filter_store.go # 防打扰过滤器的缓存
│   │   ├── client/
│   │   │   ├── recall_client.go
│   │   │   └── user_core_client.go
│   │   ├── event/
│   │   │   └── consumer.go     # 消费平台事件
│   │   ├── grpc/
│   │   │   └── handler.go      # Admin后台管理接口
│   │   └── trigger/
│   │       └── cron_trigger.go # 定时任务触发器
│   ├── application/
│   │   ├── port/
│   │   │   └── ...
│   │   └── pipeline/             # ✨ 推送决策管道核心实现 ✨
│   │       ├── interface.go      # 定义Pipeline和Stage接口
│   │       ├── factory.go        # 根据策略配置创建Pipeline实例
│   │       ├── pipeline.go       # Pipeline的执行逻辑
│   │       └── stages/           # ✨ 各个管道阶段的具体实现 ✨
│   │           ├── audience_selection_stage.go
│   │           └── pre_send_filter_stage.go
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── anti_disturbance_filter.go # ✨ 防打扰规则领域服务 ✨
├── config/
│   └── push_strategies.yaml      # ✨ 推送策略与管道配置文件 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/push_strategies.yaml` - 策略的“单一事实来源”

这是实现运营灵活性的核心。
```yaml
strategies:
  - id: "hot_video_push_v1"
    name: "爆款视频推送策略"
    trigger:
      type: "event"
      event_name: "video.became_hot"
    
    pipeline: # 定义了管道的各个阶段
      - stage: "audience_selection"
        params:
          # 定义了此阶段使用的圈人方法
          methods:
            - type: "recall_service"
              strategy_name: "i2u_for_push" # Item-to-User协同过滤
              # 参数会传递给recall-service
              params: { seed_item_id: "{{ .event.payload.video_id }}" } 
              weight: 0.8
            - type: "rule_based"
              rules: # 圈出该视频作者的所有粉丝
                - field: "user_relation"
                  op: "is_follower_of"
                  value: "{{ .event.payload.author_id }}"
              weight: 0.2
          target_size: 10000 # 圈选目标人群大小

      - stage: "pre_send_filtering"
        params:
          # 定义了此阶段使用的过滤规则
          filters:
            - "user_preference" # 检查用户通知开关
            - "global_frequency_limit" # 检查全局推送频率
            - "quiet_hours" # 检查静默时段

      - stage: "command_generation"
        params:
          # 定义了最终下发指令的模板
          notification_type: "CONTENT_RECOMMENDATION"
          template_key: "hot_video_push_template"
          # template_context中的值可以使用事件payload中的字段
          template_context:
            video_title: "{{ .event.payload.title }}"
            author_name: "{{ .event.payload.author_name }}"
```

### 3.2 `domain/` - 领域层 (The Decision Rules)

*   `domain/model/`: 定义`PushStrategy`, `PushTask`等核心领域对象。
*   **`domain/service/anti_disturbance_filter.go`**:
    *   **`AntiDisturbanceFilter`**: 一个无状态的领域服务，封装了所有防打扰规则的校验逻辑。
    *   **`Filter(ctx, userIDs, rules)`**:
        1.  接收一批用户ID和要应用的过滤规则列表。
        2.  **并行地**为这批用户执行所有过滤检查：
            a. 批量调用`user-core-service`获取通知偏好和时区。
            b. 批量从Redis中获取用户的推送频率计数。
        3.  返回一个通过了所有检查的、干净的用户ID列表。

### 3.3 `application/` - 应用层 (The Pipeline Engine)

*   **`application/pipeline/`**: **这是整个决策流程的编排引擎**。
    *   `interface.go`: 定义`Pipeline`和`Stage`接口。
    *   `factory.go`: **`PipelineFactory`**在启动时加载并解析`push_strategies.yaml`。它能根据触发的事件或任务，返回一个预先配置好的`Pipeline`实例。
    *   `pipeline.go`: **`Pipeline.Execute()`**方法按顺序执行其包含的所有`Stage`。它负责在`Stage`之间传递上下文（如候选用户列表）。
    *   **`stages/`**: **每个阶段的具体实现**。
        *   **`audience_selection_stage.go`**:
            *   `Execute`方法解析`params.methods`。
            *   **并行地**执行所有圈人方法：
                *   对于`recall_service`类型，调用`recall_client`。
                *   对于`rule_based`类型，调用`user-core-client`或`social-client`。
            *   将所有通路返回的用户ID进行加权合并和去重，得到最终的候选用户列表，并存入管道上下文中。
        *   **`pre_send_filter_stage.go`**:
            *   `Execute`方法从管道上下文中获取候选用户列表。
            *   调用`domain.AntiDisturbanceFilter.Filter()`进行过滤。
            *   将过滤后的用户列表写回管道上下文。
        *   **(隐式) `command_generation_stage`**:
            *   管道的最后一个阶段。
            *   获取最终的目标用户列表。
            *   为每个用户，根据策略配置生成一个`DispatchNotificationCommand`。
            *   调用`adapter.event.Producer`批量发布这些指令到Kafka。

### 3.4 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/trigger/`**:
    *   **`kafka_consumer.go`**: 消费平台事件，并根据事件类型查找对应的策略ID，然后调用`PipelineFactory`获取并执行管道。
    *   **`cron_trigger.go`**: 由一个独立的CronJob进程运行，定期触发配置为`time.cron`的策略。
*   **`adapter/client/`**: 封装所有对下游服务（`recall`, `user-core`）的gRPC调用。
*   **`adapter/cache/redis_filter_store.go`**: 实现对用户推送频率的Redis计数器的读写。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`push-strategist-service`：
1.  **可配置的决策管道**: 将复杂的推送决策流程，抽象为由多个**阶段(Stage)**组成的**管道(Pipeline)**。运营人员通过修改YAML配置文件，即可组合和调整这些阶段的行为，实现了极高的业务灵活性。
2.  **智能受众圈选**: 通过与`recall-service`的深度集成，将强大的推荐算法能力（如协同过滤）引入到受众选择中，实现了从“按标签圈人”到“按兴趣圈人”的智能化升级。
3.  **用户体验为中心的过滤层**: 设计了一个强制的、多维度的**防打扰过滤阶段**，将用户偏好和平台频率控制置于高优先级，确保了主动触达不会损害用户体验。
4.  **彻底的职责分离**:
    *   **本服务**: 只负责**“决策”**——决定给谁、发什么。
    *   **`notification-dispatch-service`**: 只负责**“执行”**——如何将消息可靠地发送出去。
    *   这种分离使得整个推送系统清晰、健壮且可扩展。

这种架构确保了`push-strategist-service`能够作为一个**智能、精准、可靠且负责任**的决策中枢，在实现平台增长目标的同时，最大化地保护和提升用户体验。