/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package errors

import (
	"errors"
	"fmt"
	"strings"

	pkgerrors "github.com/pkg/errors"
)

// AppError 定义了应用程序的标准错误类型
// 它包含了丰富的上下文信息，支持错误链和堆栈跟踪
type AppError struct {
	// Code 错误码，用于分类错误类型
	Code ErrorCode

	// Message 错误消息，面向开发者的详细描述
	Message string

	// Cause 被包装的底层错误，遵循Go 1.13+的错误包装规范
	Cause error

	// Metadata 附加元数据，提供额外的上下文信息
	Metadata map[string]string

	// stack 堆栈跟踪信息，用于调试
	stack StackTracer
}

// Error 实现标准error接口
func (e *AppError) Error() string {
	var parts []string

	// 添加错误码
	if e.Code != "" {
		parts = append(parts, fmt.Sprintf("code: %s", e.Code))
	}

	// 添加消息
	if e.Message != "" {
		parts = append(parts, fmt.Sprintf("msg: %s", e.Message))
	}

	// 添加底层错误
	if e.Cause != nil {
		parts = append(parts, fmt.Sprintf("cause: %v", e.Cause))
	}

	return strings.Join(parts, ", ")
}

// Unwrap 实现Go 1.13+的错误解包接口，支持errors.Is和errors.As
func (e *AppError) Unwrap() error {
	return e.Cause
}

// StackTrace 实现StackTracer接口
func (e *AppError) StackTrace() pkgerrors.StackTrace {
	if e.stack != nil {
		return e.stack.StackTrace()
	}
	return nil
}

// Format 实现fmt.Formatter接口，支持不同的格式化选项
func (e *AppError) Format(s fmt.State, verb rune) {
	switch verb {
	case 'v':
		if s.Flag('+') {
			// 详细格式：包含所有信息和堆栈跟踪
			fmt.Fprintf(s, "Error Code: %s\n", e.Code)
			fmt.Fprintf(s, "Message: %s\n", e.Message)

			if len(e.Metadata) > 0 {
				fmt.Fprintf(s, "Metadata:\n")
				for k, v := range e.Metadata {
					fmt.Fprintf(s, "  %s: %s\n", k, v)
				}
			}

			if e.Cause != nil {
				fmt.Fprintf(s, "Cause: %+v\n", e.Cause)
			}

			if e.stack != nil {
				stack := e.stack.StackTrace()
				for _, f := range stack {
					fmt.Fprintf(s, "%+v\n", f)
				}
			}
		} else {
			// 简单格式
			fmt.Fprintf(s, "%s", e.Error())
		}
	case 's':
		fmt.Fprintf(s, "%s", e.Error())
	case 'q':
		fmt.Fprintf(s, "%q", e.Error())
	}
}

// New 创建一个新的AppError
func New(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:     code,
		Message:  message,
		Metadata: make(map[string]string),
		stack:    newStack(1),
	}
}

// Newf 使用格式化字符串创建一个新的AppError
func Newf(code ErrorCode, format string, args ...interface{}) *AppError {
	return &AppError{
		Code:     code,
		Message:  fmt.Sprintf(format, args...),
		Metadata: make(map[string]string),
		stack:    newStack(1),
	}
}

// Wrap 包装一个现有的错误，创建一个新的AppError
func Wrap(cause error, code ErrorCode, message string) *AppError {
	if cause == nil {
		return New(code, message)
	}

	return &AppError{
		Code:     code,
		Message:  message,
		Cause:    cause,
		Metadata: make(map[string]string),
		stack:    newStack(1),
	}
}

// Wrapf 使用格式化字符串包装一个现有的错误
func Wrapf(cause error, code ErrorCode, format string, args ...interface{}) *AppError {
	if cause == nil {
		return Newf(code, format, args...)
	}

	return &AppError{
		Code:     code,
		Message:  fmt.Sprintf(format, args...),
		Cause:    cause,
		Metadata: make(map[string]string),
		stack:    newStack(1),
	}
}

// WithMeta 为错误添加元数据
func WithMeta(err error, key, value string) error {
	if err == nil {
		return nil
	}

	// 如果是AppError，直接添加元数据
	if appErr, ok := err.(*AppError); ok {
		if appErr.Metadata == nil {
			appErr.Metadata = make(map[string]string)
		}
		appErr.Metadata[key] = value
		return appErr
	}

	// 如果不是AppError，包装后添加元数据
	wrappedErr := Wrap(err, Internal, "wrapped error with metadata")
	wrappedErr.Metadata[key] = value
	return wrappedErr
}

// WithMetadata 为错误添加多个元数据
func WithMetadata(err error, metadata map[string]string) error {
	if err == nil {
		return nil
	}

	// 如果是AppError，直接添加元数据
	if appErr, ok := err.(*AppError); ok {
		if appErr.Metadata == nil {
			appErr.Metadata = make(map[string]string)
		}
		for k, v := range metadata {
			appErr.Metadata[k] = v
		}
		return appErr
	}

	// 如果不是AppError，包装后添加元数据
	wrappedErr := Wrap(err, Internal, "wrapped error with metadata")
	for k, v := range metadata {
		wrappedErr.Metadata[k] = v
	}
	return wrappedErr
}

// IsCode 检查错误链中是否包含指定的错误码
func IsCode(err error, code ErrorCode) bool {
	if err == nil {
		return false
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Code == code
	}

	return false
}

// GetCode 获取错误链中第一个AppError的错误码
func GetCode(err error) ErrorCode {
	if err == nil {
		return OK
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Code
	}

	return Internal
}

// GetMetadata 获取错误的元数据
func GetMetadata(err error) map[string]string {
	if err == nil {
		return nil
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		if appErr.Metadata == nil {
			return make(map[string]string)
		}

		// 返回副本，避免外部修改
		result := make(map[string]string)
		for k, v := range appErr.Metadata {
			result[k] = v
		}
		return result
	}

	return make(map[string]string)
}

// GetMessage 获取错误消息
func GetMessage(err error) string {
	if err == nil {
		return ""
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Message
	}

	return err.Error()
}

// GetCause 获取底层错误原因
func GetCause(err error) error {
	if err == nil {
		return nil
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Cause
	}

	return err
}
