﻿好的，遵照您的指示，我们来生成一份为 `review-service` (评价与反馈服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多维度评价体系**: 引入更丰富的评价体系，除了总分，还支持对多个子维度（如“沟通”、“专业度”、“准时性”）进行打分。
2.  **评价标签与摘要**: 支持用户为评价打标签，并引入AI能力，自动为评价内容生成摘要和提取关键词，以供快速浏览。
3.  **防作弊与信誉模型**: 详细定义反刷单、反恶意差评的机制，并与用户的信誉体系联动，增加高质量评价的权重。
4.  **申诉与仲裁流程**: 增加对评价的申诉(Appeal)和平台仲裁(Arbitration)流程的支持，以保证公平性。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和扩展性指标。

这份文档将描绘一个功能强大、公正透明、且能有效构建平台信任体系的评价中心。

---

### CINA.CLUB - review-service 需求规格说明书

**版本: 2.0 (生产级定义，集成多维评价与申诉流程)**  
**发布日期: 2025-06-24**  
**最后修订日期: 2025-06-24**  
**文档负责人:** [社区/信任与安全团队负责人/架构师名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台的核心价值在于连接人与服务，而**信任**是这种连接的基石。`review-service` 的目的在于构建一个**公正、透明、数据驱动且可信**的评价与反馈系统。通过收集和展示用户对服务、任务及提供者的**多维度**真实评价，并引入**智能摘要**和**公平的申诉机制**，帮助用户做出更明智的消费决策，激励服务提供者提升服务质量，从而建立和维护平台生态的整体信誉。

#### 1.2. 服务范围
本服务 **负责**:
*   **评价生命周期管理**: 接收、存储、更新、删除（软删除）用户对特定目标的评价。
*   **多维度评价体系**:
    *   支持对总体进行1-5星评分。
    *   支持对多个可配置的子维度（如“沟通”、“专业度”）进行评分。
*   **评价内容与标签**:
    *   处理文本评论和图片/视频附件链接。
    *   支持用户为评价选择预定义的正面/负面标签。
*   **AI内容增强**:
    *   （异步）调用`ai-assistant-service`为长篇评价**自动生成摘要**和**提取关键词**。
*   **评价聚合**: 实时或准实时地计算和更新被评价对象的**总体平均分、各子维度平均分、评分分布**等统计数据。
*   **互动与回复**: 处理对评价的“有用”反馈，并允许被评价方进行公开回复。
*   **申诉与仲裁**: 管理被评价方对不实评价的申诉流程，并记录平台仲裁结果。
*   **评价资格与反作弊**: 严格校验评价资格，并集成反刷单/恶意差评的检测逻辑。

本服务 **不负责**:
*   **决定用户何时有资格评价**: 由源业务服务（如`service-offering-service`）触发。
*   **内容审核的核心逻辑**: 由`content-moderation-service`负责。
*   **文件存储**: 由`file-storage-service`负责。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`review-service` 是CINA.CLUB平台的**信任基础设施**和**信誉计算中心**。它是一个高写入和高读取量的服务，其数据的公正性和丰富性直接影响用户决策、搜索排序和平台信誉。它通过与业务核心服务解耦，为平台所有需要信誉评估的场景提供了通用的、可扩展的评价能力。

#### 2.2. 主要功能概述
*   支持对多维度、多实体的评价。
*   实时的、多维度的评分聚合。
*   AI驱动的评价内容摘要与标签提取。
*   公正的评价申诉与仲裁流程。
*   与审核、通知、游戏化系统的事件驱动集成。

### 3. 核心流程图

#### 3.1. 用户提交评价并触发AI摘要流程
```mermaid
sequenceDiagram
    participant Client
    participant ReviewService as RS
    participant MQ
    participant ContentModeration as CMS
    participant AIAssistantService as AIAS
    
    Client->>RS: 1. POST /reviews (target, ratings: {overall: 5, communication: 4}, comment, ...)
    RS->>RS: 2. Validate review eligibility
    RS->>DB: 3. Create Review record (status: PENDING_MODERATION)
    RS-->>Client: 202 Accepted
    
    RS->>MQ: 4. Publish ReviewSubmittedEvent
    
    MQ-->>CMS: 5a. **[Moderation]** Consume & start content moderation
    MQ-->>AIAS: 5b. **[AI Enhancement]** Consume & start summary/keyword generation
    
    CMS-->>MQ: 6a. Publish ModerationResultEvent (APPROVED)
    AIAS-->>MQ: 6b. Publish ReviewEnhancementCompletedEvent (summary, keywords)
    
    MQ-->>RS: 7a. Consume ModerationResult
    RS->>DB: 8a. Update Review status to PUBLISHED. Atomically update RatingAggregate.
    
    MQ-->>RS: 7b. Consume EnhancementResult
    RS->>DB: 8b. Update Review record with AI-generated summary and keywords.
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 多维度评价体系
*   **FR4.1.1 (评价模板)**: 系统必须支持管理员为不同的评价目标类型（`targetEntityType`）配置不同的**评价模板**。
*   **FR4.1.2 (模板内容)**: 评价模板定义了：
    *   一个必须的总体评分（1-5星）。
    *   一个或多个可选的子维度评分（如服务类的“沟通”、“专业度”、“准时性”）。
    *   一组可选的、供用户选择的正面/负面标签（如“服务超预期”、“态度恶劣”）。

#### 4.2. AI内容增强
*   **FR4.2.1 (异步处理)**: 当一条包含较长文本评论的评价提交后，系统必须异步触发一个任务。
*   **FR4.2.2 (调用AI服务)**: 该任务调用`ai-assistant-service`，请求其：
    *   为评价文本生成一个简短的**摘要(Summary)**。
    *   从评价文本中提取**关键词(Keywords)**。
*   **FR4.2.3 (结果存储)**: AI生成的结果将被存储在对应的`Review`记录中，用于在评价列表页进行快速展示。

#### 4.3. 评价聚合
*   **FR4.3.1 (多维度聚合)**: `RatingAggregate`记录必须能存储和更新被评价目标的**总体平均分**和**每个子维度的平均分**。
*   **FR4.3.2 (权重与信誉)**: （高级）在计算平均分时，可以引入评价者的**信誉分**作为权重。来自高信誉度用户的评价，对平均分的影响更大。

#### 4.4. 申诉与仲裁
*   **FR4.4.1 (发起申诉)**: 被评价方可以对收到的评价发起申诉（`Appeal`），需提供申诉理由和证据链接。
*   **FR4.4.2 (申诉处理)**: 申诉将进入一个专门的人工审核队列。平台仲裁员介入调查。
*   **FR4.4.3 (仲裁结果)**: 仲裁结果可以是“维持原判”、“隐藏评价”或“删除评价”。系统必须记录仲裁结果，并更新评价状态。在仲裁期间，该评价可以被临时标记为“争议中”。

#### 4.5. 评价资格与反作弊
*   **FR4.5.1 (严格资格)**: 在接受评价提交前，必须通过内部API，严格验证用户是否完成了与评价目标相关的、合格的交易。
*   **FR4.5.2 (反作弊模型)**: 系统应记录评价行为模式（如评价频率、IP地址、设备指纹），并可以（可选）将这些数据输入一个异常检测模型，以识别和标记可疑的刷单或恶意差评行为。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部/客户端RESTful API接口
*   **版本**: `/api/v1/reviews`
*   **核心端点**:
    *   `POST /`: 提交新评价。Request: `SubmitReviewRequest { targetEntityType, targetEntityId, ..., ratings: { overall: 5, ... } }`
    *   `GET /target/{type}/{id}`: 获取评价列表。
    *   `GET /target/{type}/{id}/summary`: 获取评分摘要（包含各维度）。
    *   `POST /{reviewId}/appeal`: 发起申诉。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`review_templates`**: `id`, `target_entity_type`, `dimensions_config (JSONB)`, `tags_config (JSONB)`.
*   **`reviews`**:
    *   `id (PK)`, `reviewer_user_id`, `target_entity_type`, `target_entity_id`.
    *   `ratings (JSONB)`: 存储 `{ "overall": 5, "communication": 4, ... }`。
    *   `comment_text`, `selected_tags (TEXT[])`.
    *   `ai_summary (TEXT)`, `ai_keywords (TEXT[])`.
    *   `status (VARCHAR, INDEX)`: `PENDING_MODERATION`, `PUBLISHED`, `HIDDEN_BY_APPEAL`, `DELETED`.
*   **`rating_aggregates`**:
    *   `(target_entity_type, target_entity_id)` (PK).
    *   `overall_average_rating`, `total_ratings_count`.
    *   `dimensional_ratings_summary (JSONB)`: 存储各子维度的平均分和计数。
*   **`review_appeals`**: `id`, `review_id`, `appellant_user_id`, `reason`, `status` (`PENDING`, `RESOLVED`), `arbitration_result (JSONB)`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: 评价提交API P99 < 200ms。评价列表和摘要查询API P99 < 150ms。
*   **聚合更新**: 必须在评价发布后**秒级内**完成（通过异步事件）。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **数据准确性**: 评分聚合计算必须绝对准确。

#### 7.3. 可扩展性需求
*   服务可水平扩展。`reviews`表会非常庞大，必须考虑基于`target_entity_id`或`created_at`进行分区。

#### 7.4. 安全性与公正性
*   **防作弊**: 严格的评价资格校验和异常行为检测是核心。
*   **公正性**: 申诉和仲裁流程的设计必须公正、透明，并有据可查。
*   **内容安全**: 所有UGC内容必须送审。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。其强大的事务能力和JSONB字段非常适合处理多维度评价数据。
*   **异步处理**: AI摘要生成、聚合更新、通知、积分等所有非核心阻塞操作，都应通过后台任务或消息队列进行异步处理。
*   **数据冗余**: `rating_aggregates`表是对高频读的查询性能的关键优化，必须保证其与`reviews`主表的数据最终一致性。

---
这份版本2.0的SRS文档为`review-service`构建了一个现代化、公正且智能的信任与信誉系统。通过引入多维度评价、AI增强和申诉仲裁等机制，它能更全面、更客观地反映交易和互动的质量，为CINA.CLUB平台生态的健康发展提供坚实的保障。