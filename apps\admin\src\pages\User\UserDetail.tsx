/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Avatar, 
  Tag, 
  Space, 
  Button, 
  Descriptions, 
  Timeline, 
  Statistic,
  Modal,
  message,
  Tabs,
  Table,
  Badge
} from 'antd'
import { 
  EditOutlined, 
  DeleteOutlined, 
  UserOutlined, 
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  LockOutlined,
  UnlockOutlined
} from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'

import { User, UserStatus, UserRole, UserActivity } from '@/types/user'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'
import { Loading } from '@/components/Loading'

const { TabPane } = Tabs

// Mock user activity data
const mockActivity: UserActivity[] = [
  {
    id: '1',
    userId: '1',
    action: 'LOGIN',
    description: '用户登录系统',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    metadata: { device: 'Desktop', browser: 'Chrome' },
    createdAt: '2025-01-23T10:00:00Z',
  },
  {
    id: '2',
    userId: '1',
    action: 'UPDATE_PROFILE',
    description: '更新了个人资料',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    metadata: { fields: ['firstName', 'lastName'] },
    createdAt: '2025-01-22T15:30:00Z',
  },
]

/**
 * 用户详情页面
 */
const UserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { hasPermission } = usePermission()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('profile')

  // 权限检查
  const canEdit = hasPermission(Permission.USER_EDIT)
  const canDelete = hasPermission(Permission.USER_DELETE)
  const canSuspend = hasPermission(Permission.USER_SUSPEND)

  // 获取用户详情
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true)
        // Mock API call - replace with actual implementation
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock user data
        const mockUser: User = {
          id: id || '1',
          email: '<EMAIL>',
          username: 'admin',
          firstName: 'Admin',
          lastName: 'User',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
          phone: '+86 138 0000 0000',
          status: UserStatus.ACTIVE,
          roles: [UserRole.SUPER_ADMIN],
          permissions: [Permission.USER_VIEW, Permission.USER_CREATE, Permission.USER_EDIT],
          createdAt: '2025-01-01T00:00:00Z',
          updatedAt: '2025-01-23T00:00:00Z',
          lastLoginAt: '2025-01-23T10:00:00Z',
          emailVerified: true,
          phoneVerified: true,
          twoFactorEnabled: true,
        }
        
        setUser(mockUser)
      } catch (error) {
        message.error('获取用户信息失败')
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchUser()
    }
  }, [id])

  // 状态操作
  const handleStatusAction = async (action: 'suspend' | 'activate' | 'delete') => {
    if (!user) return

    const actionTexts = {
      suspend: '暂停',
      activate: '激活',
      delete: '删除',
    }

    const actionColors = {
      suspend: 'warning',
      activate: 'success',
      delete: 'danger',
    }

    Modal.confirm({
      title: `${actionTexts[action]}用户`,
      content: `确定要${actionTexts[action]}用户 "${user.firstName} ${user.lastName}" 吗？`,
      okText: '确认',
      okType: actionColors[action] as any,
      cancelText: '取消',
      onOk: async () => {
        try {
          // Mock API call
          await new Promise(resolve => setTimeout(resolve, 500))
          message.success(`用户${actionTexts[action]}成功`)
          
          if (action === 'delete') {
            navigate('/users')
          } else {
            // Update user status
            setUser(prev => prev ? {
              ...prev,
              status: action === 'suspend' ? UserStatus.SUSPENDED : UserStatus.ACTIVE
            } : null)
          }
        } catch (error) {
          message.error(`${actionTexts[action]}失败`)
        }
      },
    })
  }

  // 重置密码
  const handleResetPassword = () => {
    Modal.confirm({
      title: '重置密码',
      content: '确定要为该用户重置密码吗？新密码将通过邮件发送给用户。',
      okText: '确认重置',
      cancelText: '取消',
      onOk: async () => {
        try {
          // Mock API call
          await new Promise(resolve => setTimeout(resolve, 500))
          message.success('密码重置成功，新密码已发送到用户邮箱')
        } catch (error) {
          message.error('密码重置失败')
        }
      },
    })
  }

  // 活动记录列表列配置
  const activityColumns = [
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (action: string) => <Tag>{action}</Tag>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time: string) => new Date(time).toLocaleString(),
    },
  ]

  if (loading) {
    return <Loading type="page" tip="加载用户信息中..." />
  }

  if (!user) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>用户不存在</h3>
        <Button onClick={() => navigate('/users')}>返回用户列表</Button>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[24, 24]}>
        {/* 用户基本信息卡片 */}
        <Col span={24}>
          <Card>
            <Row align="middle" justify="space-between">
              <Col>
                <Space size="large">
                  <Avatar 
                    size={80} 
                    src={user.avatar}
                    icon={<UserOutlined />}
                  />
                  <div>
                    <h2 style={{ margin: 0 }}>{user.firstName} {user.lastName}</h2>
                    <p style={{ margin: '4px 0', color: '#666' }}>@{user.username}</p>
                    <Space>
                      <Tag color={user.status === UserStatus.ACTIVE ? 'green' : 'red'}>
                        {user.status}
                      </Tag>
                      {user.roles.map(role => (
                        <Tag key={role} color="blue">{role}</Tag>
                      ))}
                    </Space>
                  </div>
                </Space>
              </Col>
              <Col>
                <Space>
                  {canEdit && (
                    <Button 
                      type="primary"
                      icon={<EditOutlined />}
                      onClick={() => navigate(`/users/${user.id}/edit`)}
                    >
                      编辑
                    </Button>
                  )}
                  {canSuspend && user.status === UserStatus.ACTIVE && (
                    <Button 
                      danger
                      icon={<LockOutlined />}
                      onClick={() => handleStatusAction('suspend')}
                    >
                      暂停
                    </Button>
                  )}
                  {canSuspend && user.status === UserStatus.SUSPENDED && (
                    <Button 
                      type="primary"
                      icon={<UnlockOutlined />}
                      onClick={() => handleStatusAction('activate')}
                    >
                      激活
                    </Button>
                  )}
                  <Button onClick={handleResetPassword}>
                    重置密码
                  </Button>
                  {canDelete && (
                    <Button 
                      danger
                      type="text"
                      icon={<DeleteOutlined />}
                      onClick={() => handleStatusAction('delete')}
                    >
                      删除
                    </Button>
                  )}
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 统计信息 */}
        <Col span={24}>
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic 
                  title="注册天数" 
                  value={Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))} 
                  suffix="天"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic 
                  title="最后登录" 
                  value={user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : '从未登录'} 
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic 
                  title="权限数量" 
                  value={user.permissions.length} 
                  suffix="个"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic 
                  title="安全等级" 
                  value={user.twoFactorEnabled ? '高' : '中'} 
                  valueStyle={{ color: user.twoFactorEnabled ? '#3f8600' : '#cf1322' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* 详细信息标签页 */}
        <Col span={24}>
          <Card>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab="基本信息" key="profile">
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="用户ID">{user.id}</Descriptions.Item>
                  <Descriptions.Item label="用户名">{user.username}</Descriptions.Item>
                  <Descriptions.Item label="邮箱">
                    <Space>
                      <MailOutlined />
                      {user.email}
                      {user.emailVerified && <Badge status="success" text="已验证" />}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="手机号">
                    <Space>
                      <PhoneOutlined />
                      {user.phone || '未设置'}
                      {user.phone && user.phoneVerified && <Badge status="success" text="已验证" />}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="注册时间">
                    <Space>
                      <CalendarOutlined />
                      {new Date(user.createdAt).toLocaleString()}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="更新时间">
                    <Space>
                      <CalendarOutlined />
                      {new Date(user.updatedAt).toLocaleString()}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="两步验证">
                    <Badge 
                      status={user.twoFactorEnabled ? "success" : "default"} 
                      text={user.twoFactorEnabled ? "已启用" : "未启用"} 
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="账户状态">
                    <Badge 
                      status={user.status === UserStatus.ACTIVE ? "success" : "error"} 
                      text={user.status} 
                    />
                  </Descriptions.Item>
                </Descriptions>
              </TabPane>

              <TabPane tab="角色权限" key="permissions">
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <h4>用户角色</h4>
                    <Space wrap>
                      {user.roles.map(role => (
                        <Tag key={role} color="blue" style={{ padding: '4px 8px' }}>
                          {role}
                        </Tag>
                      ))}
                    </Space>
                  </Col>
                  <Col span={12}>
                    <h4>权限列表</h4>
                    <Space wrap>
                      {user.permissions.map(permission => (
                        <Tag key={permission} color="green" style={{ fontSize: '12px' }}>
                          {permission}
                        </Tag>
                      ))}
                    </Space>
                  </Col>
                </Row>
              </TabPane>

              <TabPane tab="活动记录" key="activity">
                <Table 
                  columns={activityColumns}
                  dataSource={mockActivity}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
              </TabPane>

              <TabPane tab="操作历史" key="history">
                <Timeline>
                  <Timeline.Item color="green">
                    <p>账户创建</p>
                    <p style={{ color: '#666', fontSize: '12px' }}>
                      {new Date(user.createdAt).toLocaleString()}
                    </p>
                  </Timeline.Item>
                  <Timeline.Item color="blue">
                    <p>最后登录</p>
                    <p style={{ color: '#666', fontSize: '12px' }}>
                      {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : '从未登录'}
                    </p>
                  </Timeline.Item>
                  <Timeline.Item color="orange">
                    <p>资料更新</p>
                    <p style={{ color: '#666', fontSize: '12px' }}>
                      {new Date(user.updatedAt).toLocaleString()}
                    </p>
                  </Timeline.Item>
                </Timeline>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default UserDetail 