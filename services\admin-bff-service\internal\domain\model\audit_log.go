/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package model

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
)

// Error constants for audit log validation
var (
	ErrAuditLogMissingActor  = errors.New("audit log entry missing actor ID")
	ErrAuditLogMissingAction = errors.New("audit log entry missing action")
)

// AuditLogEntry represents a single audit log entry with comprehensive tracking information
type AuditLogEntry struct {
	// Basic identifiers
	ID          string    `json:"id"`
	Timestamp   time.Time `json:"timestamp"`
	TraceID     string    `json:"trace_id"`
	ServiceName string    `json:"service_name"`

	// Actor information (who performed the action)
	ActorID    string   `json:"actor_id"`
	ActorEmail string   `json:"actor_email"`
	ActorRoles []string `json:"actor_roles,omitempty"`
	ActorIP    string   `json:"actor_ip"`
	UserAgent  string   `json:"user_agent,omitempty"`

	// Action details (what was done)
	Action       string `json:"action"`
	Resource     string `json:"resource"`
	ResourceID   string `json:"resource_id"`
	ResourceType string `json:"resource_type"`

	// Request/Response details
	RequestMethod  string                 `json:"request_method,omitempty"`
	RequestPath    string                 `json:"request_path,omitempty"`
	RequestBody    map[string]interface{} `json:"request_body,omitempty"`
	ResponseStatus int                    `json:"response_status,omitempty"`
	ResponseTime   int64                  `json:"response_time_ms,omitempty"`
	Success        bool                   `json:"success"`

	// Additional context
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	Tags     []string               `json:"tags,omitempty"`
}

// NewAuditLogEntry creates a new audit log entry with basic information
func NewAuditLogEntry(actorID, actorEmail, actorIP string) *AuditLogEntry {
	return &AuditLogEntry{
		ID:          uuid.New().String(),
		Timestamp:   time.Now().UTC(),
		ServiceName: "admin-bff-service",
		ActorID:     actorID,
		ActorEmail:  actorEmail,
		ActorIP:     actorIP,
		Success:     true,
		Metadata:    make(map[string]interface{}),
		Tags:        make([]string, 0),
	}
}

// SetTraceID sets the distributed tracing ID
func (e *AuditLogEntry) SetTraceID(traceID string) {
	e.TraceID = traceID
}

// SetActorRoles sets the roles of the actor performing the action
func (e *AuditLogEntry) SetActorRoles(roles []string) {
	e.ActorRoles = roles
}

// SetUserAgent sets the user agent string from the request
func (e *AuditLogEntry) SetUserAgent(userAgent string) {
	e.UserAgent = userAgent
}

// SetResource sets the resource information that was acted upon
func (e *AuditLogEntry) SetResource(resourceType, resourceID, action string) {
	e.ResourceType = resourceType
	e.ResourceID = resourceID
	e.Action = action
}

// SetRequest sets the HTTP request information
func (e *AuditLogEntry) SetRequest(method, path string, body map[string]interface{}) {
	e.RequestMethod = method
	e.RequestPath = path
	e.RequestBody = body
}

// SetResponse sets the HTTP response information
func (e *AuditLogEntry) SetResponse(status int, message string, success bool) {
	e.ResponseStatus = status
	e.Success = success
	if message != "" {
		e.AddMetadata("response_message", message)
	}
}

// SetResponseTime sets the response time in milliseconds
func (e *AuditLogEntry) SetResponseTime(ms int64) {
	e.ResponseTime = ms
}

// AddMetadata adds additional metadata to the audit log entry
func (e *AuditLogEntry) AddMetadata(key string, value interface{}) {
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	e.Metadata[key] = value
}

// AddTag adds a tag for categorization and filtering
func (e *AuditLogEntry) AddTag(tag string) {
	e.Tags = append(e.Tags, tag)
}

// ToJSON converts the audit log entry to JSON format
func (e *AuditLogEntry) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// Validate ensures the audit log entry has all required fields
func (e *AuditLogEntry) Validate() error {
	if e.ActorID == "" {
		return ErrAuditLogMissingActor
	}
	if e.Action == "" {
		return ErrAuditLogMissingAction
	}
	return nil
}

// GetSeverity returns the severity level based on the action and success status
func (e *AuditLogEntry) GetSeverity() string {
	if !e.Success {
		return "ERROR"
	}

	switch e.Action {
	case ActionUserSuspend, ActionUserDelete, ActionContentReject:
		return "HIGH"
	case ActionUserCreate, ActionUserRestore, ActionContentApprove:
		return "MEDIUM"
	case ActionLogin, ActionLogout:
		return "LOW"
	default:
		return "MEDIUM"
	}
}

// Resource types
const (
	ResourceUser    = "user"
	ResourceContent = "content"
	ResourceOrder   = "order"
	ResourceSession = "session"
	ResourceSystem  = "system"
)

// Actions
const (
	ActionLogin          = "login"
	ActionLogout         = "logout"
	ActionUserCreate     = "user_create"
	ActionUserUpdate     = "user_update"
	ActionUserSuspend    = "user_suspend"
	ActionUserRestore    = "user_restore"
	ActionUserDelete     = "user_delete"
	ActionContentApprove = "content_approve"
	ActionContentReject  = "content_reject"
	ActionOrderCancel    = "order_cancel"
	ActionOrderRefund    = "order_refund"
)

// AuditLogFilter represents filtering criteria for audit log queries
type AuditLogFilter struct {
	ActorID      string     `json:"actor_id,omitempty"`
	ActorEmail   string     `json:"actor_email,omitempty"`
	Action       string     `json:"action,omitempty"`
	ResourceType string     `json:"resource_type,omitempty"`
	ResourceID   string     `json:"resource_id,omitempty"`
	Success      *bool      `json:"success,omitempty"`
	StartTime    *time.Time `json:"start_time,omitempty"`
	EndTime      *time.Time `json:"end_time,omitempty"`
	Page         int        `json:"page"`
	PageSize     int        `json:"page_size"`
	SortBy       string     `json:"sort_by"`
	SortOrder    string     `json:"sort_order"`
}

// Default values for audit log filter
const (
	DefaultAuditLogPageSize = 50
	MaxAuditLogPageSize     = 1000
	DefaultAuditLogSortBy   = "timestamp"
)
