/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package application

import (
	"context"
	"fmt"
	"time"

	"cina.club/services/activity-feed-service/internal/domain"
)

// QueryService handles all read operations for the activity feed
type QueryService struct {
	feedRepo        domain.FeedRepository
	unreadCountRepo domain.UnreadCountRepository
	logger          domain.Logger
}

// NewQueryService creates a new query service instance
func NewQueryService(
	feedRepo domain.FeedRepository,
	unreadCountRepo domain.UnreadCountRepository,
	logger domain.Logger,
) *QueryService {
	return &QueryService{
		feedRepo:        feedRepo,
		unreadCountRepo: unreadCountRepo,
		logger:          logger,
	}
}

// GetUserFeedItems retrieves feed items for a user with pagination
func (s *QueryService) GetUserFeedItems(
	ctx context.Context,
	userID string,
	feedType domain.FeedType,
	limit, offset int,
) (*FeedItemsResponse, error) {
	s.logger.Debug(ctx, "Getting user feed items",
		"user_id", userID,
		"feed_type", feedType,
		"limit", limit,
		"offset", offset)

	// Validate parameters
	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}
	if limit <= 0 || limit > 100 {
		limit = 20 // Default limit
	}
	if offset < 0 {
		offset = 0
	}

	// Get feed items
	items, err := s.feedRepo.GetUserFeedItems(ctx, userID, feedType, limit+1, offset)
	if err != nil {
		s.logger.Error(ctx, "Failed to get user feed items",
			"user_id", userID,
			"feed_type", feedType,
			"error", err)
		return nil, fmt.Errorf("failed to get user feed items: %w", err)
	}

	// Get total count
	totalCount, err := s.feedRepo.GetUserFeedCount(ctx, userID, feedType)
	if err != nil {
		s.logger.Warn(ctx, "Failed to get total count",
			"user_id", userID,
			"feed_type", feedType,
			"error", err)
		totalCount = int64(len(items)) // Fallback to current batch size
	}

	// Check if there are more items
	hasMore := len(items) > limit
	if hasMore {
		items = items[:limit] // Remove the extra item
	}

	nextOffset := offset + limit
	if !hasMore {
		nextOffset = 0
	}

	response := &FeedItemsResponse{
		Items:      items,
		TotalCount: totalCount,
		HasMore:    hasMore,
		NextOffset: nextOffset,
	}

	s.logger.Debug(ctx, "Retrieved user feed items",
		"user_id", userID,
		"feed_type", feedType,
		"count", len(items),
		"has_more", hasMore)

	return response, nil
}

// GetUserFeedSummary retrieves a summary of all feed types for a user
func (s *QueryService) GetUserFeedSummary(ctx context.Context, userID string) (*domain.FeedSummary, error) {
	s.logger.Debug(ctx, "Getting user feed summary", "user_id", userID)

	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}

	// Get all unread counts
	unreadCounts, err := s.unreadCountRepo.GetAllUnreadCounts(ctx, userID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get unread counts",
			"user_id", userID,
			"error", err)
		return nil, fmt.Errorf("failed to get unread counts: %w", err)
	}

	// Create feed summary
	summary := domain.NewFeedSummary(userID)
	summary.UnreadCounts = unreadCounts

	// Calculate total unread
	for _, count := range unreadCounts {
		summary.TotalUnread += count
	}

	// Get recent items from each feed type (optional)
	for feedType := range unreadCounts {
		recentItems, err := s.feedRepo.GetUnreadFeedItems(ctx, userID, feedType, 3)
		if err != nil {
			s.logger.Warn(ctx, "Failed to get recent items",
				"user_id", userID,
				"feed_type", feedType,
				"error", err)
			continue
		}
		summary.RecentItems = append(summary.RecentItems, recentItems...)

		if len(recentItems) > 0 {
			summary.LastActivityAt[feedType] = recentItems[0].CreatedAt
		}
	}

	s.logger.Debug(ctx, "Retrieved user feed summary",
		"user_id", userID,
		"total_unread", summary.TotalUnread)

	return summary, nil
}

// GetUnreadFeedItems retrieves unread items for a specific feed type
func (s *QueryService) GetUnreadFeedItems(
	ctx context.Context,
	userID string,
	feedType domain.FeedType,
	limit int,
) ([]*domain.ActivityFeedItem, error) {
	s.logger.Debug(ctx, "Getting unread feed items",
		"user_id", userID,
		"feed_type", feedType,
		"limit", limit)

	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	items, err := s.feedRepo.GetUnreadFeedItems(ctx, userID, feedType, limit)
	if err != nil {
		s.logger.Error(ctx, "Failed to get unread feed items",
			"user_id", userID,
			"feed_type", feedType,
			"error", err)
		return nil, fmt.Errorf("failed to get unread feed items: %w", err)
	}

	s.logger.Debug(ctx, "Retrieved unread feed items",
		"user_id", userID,
		"feed_type", feedType,
		"count", len(items))

	return items, nil
}

// MarkItemsAsRead marks specific feed items as read
func (s *QueryService) MarkItemsAsRead(ctx context.Context, userID string, itemIDs []string) error {
	s.logger.Debug(ctx, "Marking items as read",
		"user_id", userID,
		"count", len(itemIDs))

	if userID == "" {
		return fmt.Errorf("user ID is required")
	}
	if len(itemIDs) == 0 {
		return fmt.Errorf("item IDs are required")
	}

	// Mark items as read in repository
	if err := s.feedRepo.MarkItemsAsRead(ctx, userID, itemIDs); err != nil {
		s.logger.Error(ctx, "Failed to mark items as read",
			"user_id", userID,
			"count", len(itemIDs),
			"error", err)
		return fmt.Errorf("failed to mark items as read: %w", err)
	}

	// Update unread counts - we need to get the feed types of marked items
	// For simplicity, we'll decrement all feed types by 1
	// In a real implementation, we'd group by feed type and decrement accordingly
	for _, feedType := range []domain.FeedType{domain.FeedTypeNotifications, domain.FeedTypeInteractions, domain.FeedTypeFollowing} {
		if err := s.unreadCountRepo.DecrementUnreadCount(ctx, userID, feedType, 1); err != nil {
			s.logger.Warn(ctx, "Failed to decrement unread count",
				"user_id", userID,
				"feed_type", feedType,
				"error", err)
		}
	}

	s.logger.Info(ctx, "Marked items as read",
		"user_id", userID,
		"count", len(itemIDs))

	return nil
}

// MarkAllFeedAsRead marks all items in a feed type as read
func (s *QueryService) MarkAllFeedAsRead(ctx context.Context, userID string, feedType domain.FeedType) error {
	s.logger.Debug(ctx, "Marking all feed as read",
		"user_id", userID,
		"feed_type", feedType)

	if userID == "" {
		return fmt.Errorf("user ID is required")
	}

	// Mark all items as read in repository
	if err := s.feedRepo.MarkAllFeedAsRead(ctx, userID, feedType); err != nil {
		s.logger.Error(ctx, "Failed to mark all feed as read",
			"user_id", userID,
			"feed_type", feedType,
			"error", err)
		return fmt.Errorf("failed to mark all feed as read: %w", err)
	}

	// Reset unread count
	if err := s.unreadCountRepo.ResetUnreadCount(ctx, userID, feedType); err != nil {
		s.logger.Error(ctx, "Failed to reset unread count",
			"user_id", userID,
			"feed_type", feedType,
			"error", err)
		return fmt.Errorf("failed to reset unread count: %w", err)
	}

	s.logger.Info(ctx, "Marked all feed as read",
		"user_id", userID,
		"feed_type", feedType)

	return nil
}

// GetUserActivityStats retrieves activity statistics for a user
func (s *QueryService) GetUserActivityStats(ctx context.Context, userID string, since time.Time) (*domain.UserActivityStats, error) {
	s.logger.Debug(ctx, "Getting user activity stats",
		"user_id", userID,
		"since", since)

	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}

	stats, err := s.feedRepo.GetUserActivityStats(ctx, userID, since)
	if err != nil {
		s.logger.Error(ctx, "Failed to get user activity stats",
			"user_id", userID,
			"error", err)
		return nil, fmt.Errorf("failed to get user activity stats: %w", err)
	}

	s.logger.Debug(ctx, "Retrieved user activity stats",
		"user_id", userID,
		"total_items", stats.TotalItems)

	return stats, nil
}

// CreateSystemAnnouncement creates a system announcement for specified users
func (s *QueryService) CreateSystemAnnouncement(ctx context.Context, req *SystemAnnouncementRequest) error {
	s.logger.Debug(ctx, "Creating system announcement",
		"title", req.Title,
		"target_users", len(req.TargetUserIDs))

	if req.Title == "" || req.Message == "" {
		return fmt.Errorf("title and message are required")
	}

	// If no target users specified, this would typically get all active users
	// For now, we'll just log and return
	if len(req.TargetUserIDs) == 0 {
		s.logger.Info(ctx, "System announcement created for all users", "title", req.Title)
		return nil
	}

	// Create feed items for each target user
	items := make([]*domain.ActivityFeedItem, 0, len(req.TargetUserIDs))
	for _, userID := range req.TargetUserIDs {
		item := domain.NewActivityFeedItem(userID, domain.FeedTypeNotifications, domain.ActivityTypeSystemAnnouncement)
		item.SetDisplay(domain.DisplayData{
			Title:   req.Title,
			Message: req.Message,
			IconURL: req.IconURL,
		})
		if req.DeepLinkURL != "" {
			item.DeepLinkURL = req.DeepLinkURL
		}
		items = append(items, item)
	}

	// Batch create feed items
	if err := s.feedRepo.BatchCreateFeedItems(ctx, items); err != nil {
		s.logger.Error(ctx, "Failed to create system announcement items",
			"title", req.Title,
			"error", err)
		return fmt.Errorf("failed to create system announcement items: %w", err)
	}

	// Increment unread counts
	for _, userID := range req.TargetUserIDs {
		if err := s.unreadCountRepo.IncrementUnreadCount(ctx, userID, domain.FeedTypeNotifications); err != nil {
			s.logger.Warn(ctx, "Failed to increment unread count for system announcement",
				"user_id", userID,
				"error", err)
		}
	}

	s.logger.Info(ctx, "System announcement created",
		"title", req.Title,
		"target_users", len(req.TargetUserIDs))

	return nil
}
