好的，遵照您的指示，我们将生成一份**专门针对Cosmos SDK技术栈**的、生产级的软件需求规格说明书（SRS）。

这份SRS将更加具体，它会使用Cosmos生态系统中的特定术语（如`Module`, `x/`, `Tendermint`, `IBC`），并对核心模块的功能需求进行更精确的定义。这使得文档可以直接交付给一个熟悉Cosgmos SDK的开发团队，作为他们进行模块设计和编码的直接依据。

---

## **CINA CHAIN (海内链) - Software Requirements Specification (SRS) for Cosmos SDK Implementation**
## **CINA CHAIN (海内链) - 基于Cosmos SDK实现的软件需求规格说明书**

**Document ID:** CINA-SRS-COSMOS-V1.0
**Version:** 1.0
**Date:** 2023-10-27
**Status:** DRAFT

---

### **1. Introduction (引言)**

#### **1.1 Purpose (目的)**
This document specifies the software requirements for the **CINA CHAIN**, an application-specific blockchain built using the **Cosmos SDK framework**. It serves as the definitive guide for the development, testing, and deployment of the CINA CHAIN's on-chain logic and its associated off-chain components.
本文档详述了**CINA CHAIN**的软件需求，这是一条使用**Cosmos SDK框架**构建的应用专用区块链。它将作为CINA CHAIN链上逻辑及其相关链下组件的开发、测试和部署的最终指南。

#### **1.2 Scope (范围)**
The scope covers all custom-built Cosmos SDK modules (`x/` modules), modifications to standard modules, and the required specifications for interacting off-chain services such as the CINA SDK, Node Clients, and Oracles.
本文档的范围涵盖了所有定制化的Cosmos SDK模块（`x/`模块）、对标准模块的修改，以及与之交互的链下服务（如CINA SDK、节点客户端、预言机）所需的需求规格。

#### **1.3 Definitions, Acronyms, and Abbreviations (定义、缩写与简称)**
*   **AppChain:** Application-Specific Blockchain.
*   **SDK:** Cosmos SDK.
*   **`x/`:** Prefix for a custom Cosmos SDK module (e.g., `x/marketplace`).
*   **IBC:** Inter-Blockchain Communication Protocol.
*   **Tendermint:** The default BFT consensus engine used by the Cosmos SDK.
*   **Keeper:** The controller logic within a Cosmos SDK module that manages state.
*   **Msg:** A transaction type that triggers a state change in a module.
*   **Query:** A request to read data from a module's state.
*   ... (A comprehensive list of all terms will be included here)

---

### **2. Overall Description (总体描述)**

#### **2.1 Product Perspective (产品愿景)**
CINA CHAIN is a sovereign, Proof-of-Stake blockchain built on the Cosmos SDK. It leverages Tendermint for fast finality and BFT consensus. Its application layer is designed to facilitate a global, decentralized marketplace for AI computation, featuring a unique green-energy incentive system.
CINA CHAIN是一条基于Cosmos SDK构建的主权权益证明（Proof-of-Stake）区块链。它利用Tendermint实现快速最终性和BFT共识。其应用层旨在促成一个全球性的、去中心化的AI计算市场，并以独特的绿色能源激励系统为特色。

#### **2.5 Design and Implementation Constraints (设计与实现约束)**
*   **Programming Language:** All on-chain modules must be implemented in **Go**.
*   **Consensus Engine:** The chain will use **Tendermint Core** as its consensus and networking layer.
*   **IBC Compatibility:** All custom token types ($C, GEC) must be compatible with the IBC protocol for cross-chain transfers. The chain must enable the `x/ibc` module.
*   **Dependency Management:** Go modules shall be used for managing all software dependencies.

---

### **3. System Features (系统特性)**

*(This section details the requirements for each custom Cosmos SDK module.)*

#### **3.1 `x/marketplace` Module**
*   **REQ-MKT-001:** The module's state must store a `Provider` object, indexed by the provider's CINA address (`sdk.AccAddress`). The `Provider` object shall contain fields for `RpcEndpoint`, `GpuClassId`, `IsGreenCertified`, and a `ReputationScore`.
*   **REQ-MKT-002:** The module must expose a `MsgRegisterProvider` transaction type. This message must require the sender to have a minimum amount of staked `$C` (a queryable parameter `MinProviderStake`) before successful execution.
*   **REQ-MKT-003:** The module must expose a `MsgUpdatePrice` transaction. The keeper for this message must prevent a provider from updating their price more than once per `N` blocks, where `N` is a governance-controlled parameter.
*   **REQ-MKT-004:** The module shall expose a `QueryProviders` gRPC endpoint that allows filtering by `GpuClassId` and `IsGreenCertified`, and supports pagination.
*   **REQ-MKT-005:** The reputation score of a provider shall be updated by the `x/escrow` module upon successful or failed task completion via an internal keeper-to-keeper function call.

#### **3.2 `x/escrow` Module**
*   **REQ-ESC-001:** The module must expose a `MsgCreateTask` transaction. This message shall require the user (`buyer`) to send a sufficient `$C` fee to the module's account, which acts as the escrow. An immutable `Task` object is created in the state.
*   **REQ-ESC-002:** The keeper for `MsgCreateTask` must call the `x/deflation` module's keeper to verify that the provider's quoted price for the task is `>= Minted_Subsidy * MQM`.
*   **REQ-ESC-003:** The module must expose a `MsgSubmitVerification` transaction, which can only be called by a whitelisted set of oracle addresses.
*   **REQ-ESC-004:** Upon receiving a successful verification via `MsgSubmitVerification`, the settlement keeper function must be triggered. This function shall:
    1.  Transfer the escrowed `$C` from the module account to the provider's address.
    2.  Invoke the `x/minter` module's `MintRewards` function with the task details.
    3.  Emit a `TaskSettled` event.
*   **REQ-ESC-005:** A "slashing" hook must be implemented. If verification fails due to provider fraud, the keeper will call the `x/slashing` module to penalize the provider's staked `$C`.

#### **3.3 `x/ucpo` and `x/minter` Modules**
*   **REQ-UCM-001:** The `x/ucpo` module will primarily manage state. Its state will contain the `PowerParameterRegistry`, a key-value store mapping `(ModelID, GpuClassID)` to an `EptmEntry` struct.
*   **REQ-UCM-002:** The `PowerParameterRegistry` must only be modifiable via a governance proposal that passes through the standard `x/gov` module.
*   **REQ-UCM-003:** The `x/minter` module's `MintRewards` function shall be private and only callable by the `x/escrow` module's keeper.
*   **REQ-UCM-004:** The `MintRewards` function must perform the following logic:
    1.  Read the task's token count and provider's `GpuClassID`.
    2.  Query the `x/ucpo` keeper to get the correct `EPT_M` value.
    3.  Calculate the amount of `$C` to be minted.
    4.  Query the `x/poge` keeper to check if the provider is green-certified.
    5.  If certified, calculate the number of GECs to be minted.
    6.  Call the `x/bank` module's `MintCoins` function to create the new `$C`.
    7.  Call the `x/poge` module's `MintGEC` function to create the new GEC NFT.

#### **3.4 `x/poge` Module**
*   **REQ-PGE-001:** This module will be an implementation of the CW721 (or equivalent NFT) standard, customized for GECs. The module will manage all GEC tokens.
*   **REQ-PGE-002:** The `MintGEC` function must only be callable by the `x/minter` module's keeper.
*   **REQ-PGE-003:** The module must expose a `MsgBurnGEC` transaction for users to "retire" their certificates for the "Green Priority Queue" access.
*   **REQ-PGE-004:** The module's state must store mappings for `GEC Staking`, allowing GEC NFTs to be locked and associated with a provider's or governor's address. The staking logic must be integrated with the `x/staking` and `x/gov` modules to apply reward/voting power multipliers.

---

### **4. External Interface Requirements (外部接口需求)**

#### **4.1 CINA CHAIN RPC & gRPC API**
*   **REQ-EIR-001:** All custom module queries must be exposed via both gRPC and a gRPC-gateway (for RESTful JSON support).
*   **REQ-EIR-002:** The API must be documented using Protocol Buffers (v3) and OpenAPI specifications.

#### **4.2 IBC Integration**
*   **REQ-EIR-003:** The native `$C` denomination (`ucina`) must be registered as a valid IBC asset.
*   **REQ-EIR-004:** The chain must support IBC transfers with at least one major public Cosmos hub (e.g., the Cosmos Hub) at launch to facilitate DEX listings.

---

### **5. Non-Functional Requirements (非功能性需求)**

#### **5.1 Performance Requirements**
*   **REQ-NFR-001:** Block time shall be configured in Tendermint's `config.toml` to a target of 5 seconds.
*   **REQ-NFR-002:** The application state machine (the Go modules) must be optimized to ensure that the median block processing time remains under 50% of the target block time under heavy load.

#### **5.2 Security Requirements**
*   **REQ-NFR-003:** The `BeginBlock` and `EndBlock` logic in `app.go` must be minimal to prevent non-deterministic behavior or performance degradation.
*   **REQ-NFR-004:** All state-mutating keeper functions must implement robust validation logic to prevent invalid state transitions, following the "object-capability model" promoted by the Cosmos SDK.

#### **5.3 Scalability**
*   **REQ-NFR-005:** The application must be built using the latest stable version of the Cosmos SDK to leverage performance improvements and new features like state pruning and optimized data stores (e.g., IAVL v1).

---

### **Appendix A: Transaction Flow (Msg) Examples (附录A：交易流示例)**

*   **User Pays for Service:**
    1.  User's client signs and broadcasts a `MsgCreateTask` to the CINA CHAIN.
    2.  A validator node includes the `Msg` in a block proposal.
    3.  The block is committed via Tendermint consensus.
    4.  The `x/escrow` keeper processes the `Msg`, verifies funds, and creates the `Task` object in its state.
    5.  The provider's node client sees the new `Task` via a query or event.

*   **Provider Receives Reward:**
    1.  An Oracle node signs and broadcasts a `MsgSubmitVerification`.
    2.  The block is committed.
    3.  The `x/escrow` keeper processes the `Msg`.
    4.  It calls the `x/bank` keeper to transfer `$C`.
    5.  It calls the `x/minter` keeper, which in turn calls the `x/bank` and `x/poge` keepers to mint and distribute new assets.
    6.  Relevant events are emitted.