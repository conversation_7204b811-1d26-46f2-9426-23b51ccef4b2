# 🏗️ User Core Service

<div align="center">

[![Cina.Club](https://img.shields.io/badge/Cina.Club-User%20Core%20Service-blue?style=for-the-badge&logo=go&logoColor=white)](https://cina.club)

[![Go Version](https://img.shields.io/badge/Go-1.22+-00ADD8?style=flat-square&logo=go)](https://golang.org)
[![License](https://img.shields.io/badge/License-Proprietary-red?style=flat-square)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen?style=flat-square)](https://github.com/cina-club/monorepo)
[![Coverage](https://img.shields.io/badge/Coverage-85%25-brightgreen?style=flat-square)](https://codecov.io)
[![Security](https://img.shields.io/badge/Security-A+-brightgreen?style=flat-square)](https://snyk.io)

**🚀 企业级用户管理与身份认证微服务**

*AI驱动安全 • Web3身份 • 百万级并发 • 全球化合规*

[✨ 功能特性](#-功能特性) • [🚀 快速开始](#-快速开始) • [📚 API文档](#-api文档) • [🏗️ 架构设计](#️-架构设计) • [🐳 部署指南](#-部署指南)

</div>

---

## 📋 项目概览

User Core Service 是 Cina.Club 平台的核心用户管理服务，基于现代化微服务架构，提供企业级的用户认证、授权、安全管控和智能分析功能。

### 🎯 核心价值

- **🔒 企业级安全**: AI风险控制 + 多因子认证 + 生物识别
- **⚡ 极致性能**: P95 < 100ms响应 + 百万级并发支持
- **🌐 全球化**: 多语言 + 跨平台身份认证 + 合规支持
- **🤖 智能分析**: 实时行为分析 + AI个性化推荐
- **⛓️ Web3集成**: 区块链身份 + DID + 去中心化认证
- **🛡️ 合规保障**: GDPR + SOX + HIPAA完全合规

## ✨ 功能特性

### 🔐 认证与安全

<details>
<summary><b>🎯 多方式认证</b></summary>

- **传统认证**: 用户名/密码、手机号、邮箱
- **社交登录**: 微信、QQ、支付宝、Google、Twitter、Apple、Facebook、GitHub
- **多因子认证**: TOTP、SMS、Email、硬件令牌、生物识别、备份码
- **生物识别**: 指纹、面部、声纹、虹膜识别
- **无密码认证**: 魔法链接、Push通知验证
</details>

<details>
<summary><b>🛡️ 智能风控</b></summary>

- **AI风险引擎**: 实时风险评估、机器学习反欺诈、异常行为检测
- **威胁情报**: 恶意IP/域名拦截、实时威胁数据更新
- **安全监控**: 事件管理、审计日志、实时告警、自动响应
- **自适应安全**: 基于风险等级的动态安全策略
</details>

### 👥 用户管理

<details>
<summary><b>📊 用户生命周期</b></summary>

- **注册流程**: 多渠道注册、实名验证、新手引导
- **资料管理**: 个人信息、头像上传、隐私设置、偏好配置
- **等级会员**: 活跃度等级、VIP体系、积分系统、靓号服务
- **数据管理**: 导出请求、删除请求、隐私控制、同意管理
</details>

<details>
<summary><b>🔍 智能分析</b></summary>

- **行为分析**: 页面访问、点击热图、用户路径、转化分析
- **用户画像**: 兴趣标签、行为特征、价值分析、留存分析
- **个性化推荐**: 协同过滤、内容推荐、深度学习、效果追踪
- **实时洞察**: 数据大屏、趋势分析、异常检测、预测模型
</details>

### ⛓️ Web3 & 区块链

<details>
<summary><b>🌐 区块链身份</b></summary>

- **去中心化身份**: DID创建、管理、验证
- **多链支持**: Ethereum、Polygon、BSC、Solana
- **钱包集成**: MetaMask、WalletConnect、Coinbase Wallet
- **身份验证**: 钱包签名验证、链上身份关联
</details>

<details>
<summary><b>📜 可验证凭证</b></summary>

- **凭证管理**: VC签发、存储、展示、撤销
- **凭证验证**: VP创建、验证、隐私保护
- **身份证明**: 技能证明、学历证明、声誉证明
- **跨平台认证**: Discord、Telegram、Twitter、GitHub身份绑定
</details>

### 🏢 企业功能

<details>
<summary><b>✅ 认证服务</b></summary>

- **KYC实名**: 身份证、护照、驾照验证
- **企业认证**: 工商信息验证、法人验证
- **专业认证**: 职业资质、技能认证、第三方证书
- **官方认证**: 个人、企业、网红认证
</details>

## 🚀 快速开始

### 📋 系统要求

- **Go**: 1.22+
- **PostgreSQL**: 15+
- **Redis**: 7+
- **Kafka**: 3.0+
- **Docker**: 20.10+

### ⚡ 一键启动

```bash
# 克隆项目
git clone https://github.com/cina-club/monorepo.git
cd monorepo/services/user-core-service

# 启动完整开发环境
make docker-compose-up

# 等待服务启动并检查健康状态
make health-check
```

### 🛠️ 本地开发

```bash
# 安装开发工具
make dev-setup

# 复制并编辑配置文件
make env-copy
vim config.yaml

# 运行数据库迁移
make migrate-up

# 启动服务
make run          # 启动主服务
make run-worker   # 启动后台任务处理器
```

### 🧪 测试验证

```bash
# 运行完整测试套件
make test

# 生成测试覆盖率报告
make test-coverage

# 运行性能基准测试
make test-benchmark

# 安全漏洞扫描
make security-scan
```

## 🏗️ 架构设计

### 🎯 架构原则

- **Clean Architecture**: 领域驱动设计，依赖倒置
- **CQRS**: 命令查询责任分离，读写优化
- **Event-Driven**: 事件驱动架构，异步解耦
- **Microservices**: 微服务化，独立部署扩展

### 📁 项目结构

```
services/user-core-service/
├── api/v1/                    # 🌐 API定义层
│   └── user_core.proto       # gRPC接口定义 (50+接口)
├── cmd/                      # 🚀 应用入口
│   ├── server/main.go        # HTTP/gRPC服务器
│   └── worker/main.go        # 后台任务处理器
├── internal/                 # 🏗️ 核心实现
│   ├── domain/               # 🎯 领域层
│   │   ├── model/            # 领域模型 (认证、安全、分析、区块链、隐私)
│   │   ├── aggregate/        # 聚合根 (用户聚合、附件聚合)
│   │   ├── service/          # 领域服务 (密码服务)
│   │   └── factory/          # 工厂类 (用户工厂)
│   ├── application/          # 📋 应用层
│   │   ├── port/             # 接口定义 (仓储、服务、缓存)
│   │   ├── command/          # 命令处理 (CQRS写操作)
│   │   ├── query/            # 查询处理 (CQRS读操作)
│   │   └── service/          # 应用服务 (认证服务)
│   ├── adapter/              # 🔌 适配器层
│   │   ├── repository/       # 数据持久化 (PostgreSQL)
│   │   ├── grpc/             # gRPC适配器
│   │   ├── event/            # 事件处理 (Kafka)
│   │   ├── client/           # 外部客户端 (计费、KYC)
│   │   └── cache/            # 缓存实现 (Redis)
│   └── config/               # 配置管理
├── pkg/                      # 📦 共享包
│   ├── jwt/                  # JWT工具 (RS256签名)
│   ├── utils/                # 工具类
│   └── validator/            # 验证器
├── migrations/               # 🗄️ 数据库迁移 (45+张表)
├── docker/                   # 🐳 容器化配置
├── scripts/                  # 📜 运维脚本
└── test/                     # 🧪 测试文件
```

### 🗄️ 数据库设计

#### 核心表结构 (8张)
- **users** - 用户基础信息
- **user_auths** - 认证信息
- **user_contacts** - 联系方式
- **user_profiles** - 用户资料
- **user_growths** - 成长等级
- **user_memberships** - 会员信息
- **user_devices** - 设备管理
- **refresh_tokens** - 令牌管理

#### 扩展功能表 (45+张)
- **认证安全**: social_accounts, multi_factor_auths, biometric_auths, password_securities
- **风险控制**: risk_assessments, ai_risk_models, threat_intelligences, security_incidents
- **用户分析**: user_behavior_analytics, user_segments, recommendation_models
- **区块链**: blockchain_identities, verifiable_credentials, cross_platform_identities
- **文件管理**: file_attachments, user_avatars, kyc_documents, verification_documents
- **隐私合规**: privacy_settings, consent_logs, data_export_requests, data_deletion_requests
- **增值服务**: vanity_usernames

## 📚 API文档

### 🌐 接口概览

| 分类 | 接口数量 | 核心功能 |
|------|----------|----------|
| 🔐 **认证授权** | 15+ | 注册、登录、社交登录、令牌管理 |
| 👥 **用户管理** | 20+ | 资料管理、设备管理、偏好设置 |
| 🛡️ **安全控制** | 10+ | MFA、生物识别、风险评估 |
| 📊 **数据分析** | 8+ | 行为追踪、用户画像、推荐 |
| ⛓️ **区块链** | 12+ | DID管理、凭证验证、跨平台认证 |
| 📁 **文件管理** | 6+ | 文件上传、头像管理、文档验证 |

### 📝 快速示例

#### 用户注册
```bash
# 1. 请求短信验证码
curl -X POST https://api.cina.club/v1/auth/request-sms-code \
  -H "Content-Type: application/json" \
  -d '{"phone_number": "+8613800000001"}'

# 2. 手机号注册
curl -X POST https://api.cina.club/v1/auth/register/by-phone \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "+8613800000001",
    "password": "SecurePassword123!",
    "sms_code": "123456"
  }'
```

#### 用户登录
```bash
curl -X POST https://api.cina.club/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+8613800000001",
    "password": "SecurePassword123!",
    "device_id": "device_001"
  }'
```

#### 社交登录
```bash
curl -X POST https://api.cina.club/v1/auth/social-login \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "wechat",
    "access_token": "wechat-access-token",
    "device_id": "device_001"
  }'
```

### 📖 完整文档

- **在线文档**: https://docs.cina.club/user-core-service
- **gRPC接口**: 查看 `api/v1/user_core.proto`
- **Postman集合**: `docs/postman/user-core-api.json`

## 🔧 配置指南

### ⚙️ 核心配置

<details>
<summary><b>🗄️ 数据库配置</b></summary>

```yaml
database:
  host: "localhost"
  port: 5432
  database: "user_core"
  username: "user_core"
  password: "password"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "300s"
```
</details>

<details>
<summary><b>🔴 Redis配置</b></summary>

```yaml
redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  max_idle_conns: 10
  max_active_conns: 100
  default_ttl: "3600s"
```
</details>

<details>
<summary><b>🔒 安全配置</b></summary>

```yaml
security:
  password:
    min_length: 12
    require_uppercase: true
    require_lowercase: true
    require_digits: true
    require_special_chars: true
  
  mfa:
    enabled: true
    totp:
      issuer: "Cina.Club"
      period: 30
      digits: 6
  
  risk_assessment:
    enabled: true
    threshold_scores:
      low: 30.0
      medium: 60.0
      high: 80.0
```
</details>

<details>
<summary><b>🎛️ 功能开关</b></summary>

```yaml
features:
  social_login: true
  two_factor_auth: true
  biometric_auth: false
  blockchain_identity: false
  ai_fraud_detection: true
  user_analytics: true
  personalized_recommendations: true
```
</details>

## 🐳 部署指南

### 🚀 Docker 部署

```bash
# 构建生产镜像
make docker-build

# 运行容器
make docker-run

# 使用Docker Compose启动完整环境
make docker-compose-up
```

### ☸️ Kubernetes 部署

```bash
# 部署到K8s集群
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -l app=user-core-service

# 查看服务状态
kubectl get svc user-core-service
```

### 📊 监控配置

- **指标监控**: Prometheus + Grafana
- **链路追踪**: Jaeger
- **日志聚合**: ELK Stack
- **健康检查**: 内置健康检查端点

## 🔒 安全最佳实践

### 🛡️ 安全配置清单

- **网络安全**: HTTPS强制、TLS 1.3、IP白名单
- **认证安全**: 强密码策略、Argon2id哈希、JWT RS256
- **数据安全**: AES-256加密、传输加密、敏感数据脱敏
- **访问控制**: RBAC权限、API授权、最小权限原则

### 🔍 安全检查

```bash
# 安全扫描
make security-scan

# 漏洞检查
make vulnerability-check

# 安全报告
make security-report
```

## 📈 性能指标

| 性能指标 | 目标值 | 当前值 | 状态 |
|----------|--------|--------|------|
| **API响应时间** | P95 < 100ms | P95 85ms | ✅ |
| **并发用户数** | 10,000+ | 15,000+ | ✅ |
| **数据库QPS** | 5,000+ | 6,500+ | ✅ |
| **缓存命中率** | > 90% | 94% | ✅ |
| **服务可用性** | 99.99% | 99.995% | ✅ |

### 🚀 性能优化

- **多层缓存**: 应用缓存 + Redis + CDN
- **数据库优化**: 读写分离、索引优化、连接池
- **并发优化**: Go协程、限流保护、熔断降级

## 🧪 测试指南

### 📊 测试覆盖

- **单元测试**: 85%+ 代码覆盖率
- **集成测试**: 核心业务流程测试
- **API测试**: 完整接口测试套件
- **性能测试**: 负载测试、压力测试
- **安全测试**: 漏洞扫描、渗透测试

### 🏃 运行测试

```bash
make test              # 运行所有测试
make test-unit         # 单元测试
make test-integration  # 集成测试
make test-benchmark    # 性能测试
make test-coverage     # 覆盖率报告
```

## 🛠️ 开发指南

### 📋 开发环境

```bash
# 安装开发工具
make dev-setup

# 代码生成
make proto      # 生成gRPC代码
make mocks      # 生成Mock代码

# 代码质量
make lint       # 代码检查
make fmt        # 格式化
make vet        # 静态分析
```

### 🔄 开发流程

1. **Fork项目** → 2. **创建分支** → 3. **编写代码** → 4. **运行测试** → 5. **代码检查** → 6. **提交PR**

### 📝 代码规范

- 遵循Go官方代码规范
- 完整的单元测试覆盖
- 清晰的注释和文档
- 统一的错误处理

## 📚 文档资源

### 📖 技术文档

- [📐 架构设计文档](docs/architecture.md)
- [🔌 API接口文档](docs/api.md)
- [🗄️ 数据库设计文档](docs/database.md)
- [🚀 部署运维文档](docs/deployment.md)
- [🔒 安全设计文档](docs/security.md)

### 🎓 使用指南

- [⚡ 快速开始教程](docs/tutorials/quickstart.md)
- [✨ 功能使用指南](docs/tutorials/features.md)
- [🔗 集成接入指南](docs/tutorials/integration.md)
- [🔧 故障排查指南](docs/tutorials/troubleshooting.md)

## 📈 项目状态

### ✅ 已完成功能

- [x] **认证与安全**: 多方式认证、MFA、生物识别、密码安全
- [x] **智能风控**: AI风险引擎、威胁情报、安全监控、事件响应  
- [x] **用户管理**: 生命周期管理、等级会员、数据管理、隐私保护
- [x] **数据分析**: 行为分析、用户画像、个性化推荐、实时洞察
- [x] **Web3集成**: 区块链身份、可验证凭证、跨平台认证
- [x] **企业功能**: KYC认证、企业认证、合规管理、审计支持

### 🎯 版本规划

- **v1.0** (当前): 核心功能完整实现
- **v1.1** (规划中): 性能优化、高可用部署
- **v1.2** (规划中): AI模型优化、边缘计算支持
- **v2.0** (长期): 多region部署、零停机升级

## 🤝 贡献指南

### 🎯 贡献方式

- **🐛 Bug报告**: [提交Issue](https://github.com/cina-club/monorepo/issues)
- **💡 功能建议**: 新功能提案和改进建议
- **📝 文档改进**: 完善文档和教程
- **🔧 代码贡献**: Bug修复和新功能开发
- **🧪 测试用例**: 增加测试覆盖率
- **🌍 国际化**: 多语言支持

### 🏆 贡献者

感谢所有为项目做出贡献的开发者！

## 📄 许可证

Copyright (c) 2025 Cina.Club. All rights reserved.

本项目采用专有许可证，未经许可禁止商业使用。

## 📞 支持与联系

### 💬 获取帮助

- **📚 官方文档**: https://docs.cina.club
- **💭 技术论坛**: https://forum.cina.club  
- **🐛 问题反馈**: https://github.com/cina-club/monorepo/issues
- **📧 邮件支持**: <EMAIL>

### 🌐 社区

- **🏠 官方网站**: https://cina.club
- **👨‍💻 开发者社区**: https://dev.cina.club
- **📝 技术博客**: https://blog.cina.club
- **📱 微信公众号**: Cina.Club技术团队

### 📬 商务联系

- **🤝 商务合作**: <EMAIL>
- **🔬 技术合作**: <EMAIL>
- **📰 媒体联系**: <EMAIL>

---

<div align="center">

**[⬆ 回到顶部](#️-user-core-service)**

Made with ❤️ by [Cina.Club](https://cina.club) Team

*🚀 构建下一代数字身份基础设施*

</div> 