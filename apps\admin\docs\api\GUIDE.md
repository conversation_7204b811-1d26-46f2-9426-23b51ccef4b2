# Admin Dashboard - API Integration Guide

This guide explains the frontend patterns for interacting with the backend API. It covers the API client, service classes, and integration with React Query.

## 1. The API Client

The core API client is located at `src/lib/api-client.ts`. It is a pre-configured `axios` instance with the following features:
-   **Base URL**: Automatically prepends the backend URL.
-   **Authentication**: Automatically attaches the user's JWT to the `Authorization` header of every request.
-   **Token Refresh**: Automatically attempts to refresh an expired JWT using the refresh token. If it fails, it logs the user out.
-   **Error Handling**: Centralized handling for common HTTP error codes (e.g., 401, 403, 404, 500).
-   **Request Retries**: Automatically retries failed requests (e.g., network errors, 5xx server errors) with exponential backoff.

You should rarely need to use `apiClient` directly. Instead, you should use the abstraction provided by the API Service Classes.

## 2. API Service Classes

All API endpoints are organized into **Service Classes** located in `src/services/api/`. Each class corresponds to a major backend resource (e.g., `userApi.ts`, `contentApi.ts`).

These classes use a static-only pattern, meaning you do not need to instantiate them.

**Example: `UserApiService`**
```typescript
import { UserApiService } from '@/services/api/userApi';

// Get a list of users
const users = await UserApiService.getUsers({ page: 1, pageSize: 20 });

// Delete a user
await UserApiService.deleteUser('user-id-123');
```

This pattern provides a clean, organized, and type-safe way to interact with the API. When adding a new API endpoint, you should add a corresponding static method to the appropriate service class.

## 3. Server State Management with React Query

We use **React Query** (`@tanstack/react-query`) to manage all server state. This is the **required** pattern for fetching and updating data. It handles caching, background refetching, and loading/error states for us.

### 3.1. Fetching Data (`useQuery`)

To fetch data, use the `useQuery` hook. The `queryKey` should be a descriptive array, and the `queryFn` should call a method from an API service class.

**Example: Fetching the user list in a component**
```tsx
import { useQuery } from '@tanstack/react-query';
import { UserApiService } from '@/services/api/userApi';

const UserListComponent = () => {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['users', { page: 1, status: 'active' }], // Descriptive key
    queryFn: () => UserApiService.getUsers({ page: 1, status: 'active' }),
  });

  if (isLoading) return <p>Loading...</p>;
  if (isError) return <p>Error fetching users!</p>;

  return (
    <ul>
      {data.data.map(user => <li key={user.id}>{user.username}</li>)}
    </ul>
  );
};
```

### 3.2. Modifying Data (`useMutation`)

To create, update, or delete data, use the `useMutation` hook. This hook provides functions to handle success, error, and settlement states.

A key pattern we use is **optimistic updates**, which makes the UI feel faster.

**Example: Implementing a delete user button**
```tsx
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UserApiService } from '@/services/api/userApi';

const DeleteUserButton = ({ userId }) => {
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: (id: string) => UserApiService.deleteUser(id),
    
    // Optimistic Update Logic
    onMutate: async (deletedUserId) => {
      await queryClient.cancelQueries({ queryKey: ['users'] });
      const previousUsers = queryClient.getQueryData(['users']);
      queryClient.setQueryData(['users'], (old: any) => 
        old.filter(user => user.id !== deletedUserId)
      );
      return { previousUsers };
    },
    
    // Rollback on error
    onError: (err, variables, context) => {
      queryClient.setQueryData(['users'], context.previousUsers);
    },

    // Refetch after success/error to ensure data consistency
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  return (
    <button onClick={() => deleteMutation.mutate(userId)}>
      Delete User
    </button>
  );
};
```

By following these patterns, we ensure our application's data layer is consistent, robust, and performant. 