# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-20 HH:MM:SS
# Modified: 2025-06-20 HH:MM:SS

# Xcode
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcodeproj/project.xcworkspace/
*.xcworkspace/
.DS_Store
.swiftpm/

# Build Products
/build/
/DerivedData/

# Generated files
*.generated.swift
*.pb.swift

# Swift Package Manager
/.build/
/Packages/*/Package.resolved
/.swiftpm/xcode/package.xcworkspace/

# CocoaPods (if used)
/Pods/
*.podspec
Manifest.lock

# Carthage (if used)
/Carthage/Build/

# Accio dependency management
Dependencies/
.accio/

# fastlane
/fastlane/report.xml
/fastlane/Preview.html
/fastlane/screenshots/**/*.png
/fastlane/test_output

# Code Injection
iOSInjectionProject/

# Archives
*.xcarchive

# Provisioning profiles
*.mobileprovision
*.provisionprofile

# Certificate files
*.p12
*.cer

# Local development
.env
config.local.yaml

# IDE files
.vscode/
.idea/

# Temporary files
*.tmp
*.temp
*~.nib
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Testing
xctest.xctestrun
*.gcda
*.gcno

# Simulator logs
simulator_*.log

# App specific
UserInterfaceState.xcuserstate 