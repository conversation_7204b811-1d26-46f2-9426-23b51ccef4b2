package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"cina.club/services/ai-assistant-service/internal/application/service"
)

// AssistantHandler AI鍔╂墜HTTP澶勭悊鍣?
type AssistantHandler struct {
	assistantService *service.AIAssistantService
}

// NewAssistantHandler 鍒涘缓鏂扮殑AI鍔╂墜澶勭悊鍣?
func NewAssistantHandler(assistantService *service.AIAssistantService) *AssistantHandler {
	return &AssistantHandler{
		assistantService: assistantService,
	}
}

// RegisterRoutes 娉ㄥ唽璺敱
func (h *AssistantHandler) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api/v1")
	{
		// 瀵硅瘽鐩稿叧鎺ュ彛
		api.POST("/chat", h.ProcessMessage)
		api.GET("/chat/:session_id/history", h.GetSessionHistory)
		api.DELETE("/chat/:session_id", h.ClearSession)

		// 宸ュ叿鐩稿叧鎺ュ彛
		api.GET("/tools", h.GetAvailableTools)

		// 鍋ュ悍妫€鏌?
		api.GET("/health", h.HealthCheck)
	}
}

// ProcessMessage 澶勭悊鐢ㄦ埛娑堟伅
func (h *AssistantHandler) ProcessMessage(c *gin.Context) {
	var req service.ProcessMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 楠岃瘉蹇呴渶瀛楁
	if req.UserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "user_id is required",
		})
		return
	}

	if req.Message == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "message is required",
		})
		return
	}

	// 澶勭悊娑堟伅
	response, err := h.assistantService.ProcessMessage(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process message",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetSessionHistory 鑾峰彇浼氳瘽鍘嗗彶
func (h *AssistantHandler) GetSessionHistory(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "session_id is required",
		})
		return
	}

	// 鑾峰彇闄愬埗鍙傛暟
	limit := 50 // 榛樿闄愬埗
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	// 鑾峰彇浼氳瘽鍘嗗彶
	response, err := h.assistantService.GetSessionHistory(c.Request.Context(), sessionID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get session history",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ClearSession 娓呴櫎浼氳瘽
func (h *AssistantHandler) ClearSession(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "session_id is required",
		})
		return
	}

	// 娓呴櫎浼氳瘽
	err := h.assistantService.ClearSession(c.Request.Context(), sessionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to clear session",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Session cleared successfully",
	})
}

// GetAvailableTools 鑾峰彇鍙敤宸ュ叿鍒楄〃
func (h *AssistantHandler) GetAvailableTools(c *gin.Context) {
	tools, err := h.assistantService.GetAvailableTools(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get available tools",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"tools": tools,
		"count": len(tools),
	})
}

// HealthCheck 鍋ュ悍妫€鏌?
func (h *AssistantHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "healthy",
		"service": "ai-assistant-service",
		"version": "1.0.0",
	})
}
