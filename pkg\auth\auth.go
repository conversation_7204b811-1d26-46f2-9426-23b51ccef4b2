/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

// Package auth provides comprehensive authentication and authorization middleware for CINA.CLUB backend services.
// It implements JWT validation, service-to-service authentication, and role-based access control (RBAC) through
// composable gRPC interceptors with high performance and security.
package auth

import (
	"fmt"
	"time"

	"google.golang.org/grpc"

	"cina.club/pkg/auth/interceptor"
	"cina.club/pkg/auth/jwks"
	"cina.club/pkg/auth/rbac"
	"cina.club/pkg/auth/s2s"
)

// Note: AuthenticatedUser and ServiceIdentity types are defined in types.go

// Note: PublicKeyProvider is defined in the s2s package

// Config contains all configuration needed to initialize authentication and authorization components
type Config struct {
	UserCoreJWKSUrl string              `mapstructure:"user_core_jwks_url" validate:"required,url"`
	RBACPolicy      map[string][]string `mapstructure:"rbac_policy"`
	S2SPublicKeys   map[string]string   `mapstructure:"s2s_public_keys"` // map[service_name] -> public_key_pem

	// JWT Validation settings
	AllowedIssuers  []string `mapstructure:"allowed_issuers"`
	AllowedAudience []string `mapstructure:"allowed_audience"`
	ClockSkew       int      `mapstructure:"clock_skew_seconds" default:"5"` // Allow clock drift in seconds
}

// AuthSuite provides a convenient wrapper around all authentication and authorization components
type AuthSuite struct {
	// Unary interceptors
	UserJWTInterceptor grpc.UnaryServerInterceptor
	S2SInterceptor     grpc.UnaryServerInterceptor
	RBACInterceptor    grpc.UnaryServerInterceptor

	// Stream interceptors
	UserJWTStreamInterceptor grpc.StreamServerInterceptor
	S2SStreamInterceptor     grpc.StreamServerInterceptor
	RBACStreamInterceptor    grpc.StreamServerInterceptor
}

// NewAuthSuite initializes all authentication and authorization components based on the provided configuration
// and RPC permission mappings. It returns a ready-to-use AuthSuite instance.
func NewAuthSuite(cfg Config, rpcPermissions map[string]string) (*AuthSuite, error) {
	// Create a static key provider for S2S authentication
	s2sKeyProvider, err := s2s.NewStaticKeyProvider(cfg.S2SPublicKeys)
	if err != nil {
		return nil, fmt.Errorf("failed to create S2S key provider: %w", err)
	}

	// Initialize JWKS client for user JWT validation
	jwksClient := jwks.NewClient(cfg.UserCoreJWKSUrl)

	// Initialize RBAC engine
	rbacEngine := rbac.NewEngine(cfg.RBACPolicy)

	// Initialize S2S verifier
	s2sVerifier := s2s.NewVerifier(s2sKeyProvider)

	// Create interceptors with proper configuration
	clockSkew := time.Duration(cfg.ClockSkew) * time.Second

	userJWTInterceptor := interceptor.NewUserJWTInterceptor(jwksClient, cfg.AllowedIssuers, cfg.AllowedAudience, clockSkew)
	s2sInterceptor := interceptor.NewS2SJWTInterceptor(s2sVerifier, "current-service") // TODO: Make service name configurable
	rbacInterceptor := interceptor.NewRBACInterceptor(rbacEngine, rpcPermissions)

	return &AuthSuite{
		UserJWTInterceptor:       userJWTInterceptor.UnaryInterceptor(),
		S2SInterceptor:           s2sInterceptor.UnaryInterceptor(),
		RBACInterceptor:          rbacInterceptor.UnaryInterceptor(),
		UserJWTStreamInterceptor: userJWTInterceptor.StreamInterceptor(),
		S2SStreamInterceptor:     s2sInterceptor.StreamInterceptor(),
		RBACStreamInterceptor:    rbacInterceptor.StreamInterceptor(),
	}, nil
}
