好的，遵照您的指示。我将为您生成一份专门针对 **`model-management-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`model-management-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**模型处理的异步工作流、与Python Worker的解耦、基于设备能力的智能分发策略，以及作为一个MLOps核心平台的版本控制与部署管理**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `model-management-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `model-management-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 事件驱动的异步工作流 + 策略模式(用于部署)

## 1. 概述

`model-management-service` 是CINA.CLUB端侧AI能力的“**兵工厂**”和“**后勤分发中心**”。它是一个典型的**MLOps (Machine Learning Operations)**平台。其核心挑战在于：
1.  **异构工具链集成**: 模型优化（量化、转换）依赖于多种Python工具链（TensorFlow, PyTorch, ONNX Runtime等），需要将这些工具链的执行与主服务（Go语言）进行解耦。
2.  **长耗时异步任务**: 模型转换和量化是计算密集型、长耗时的任务，必须通过异步工作流来处理，不能阻塞API。
3.  **复杂的元数据与版本管理**: 需要精确地追踪每个模型的每个版本、其来源、优化参数、性能指标和目标设备能力。
4.  **智能与灵活的分发**: 需要根据客户端上报的设备画像，动态地、策略性地（如金丝雀发布）分发最合适的模型版本。
5.  **可靠性与可追溯性**: 模型处理的每一步都必须有日志、可追溯，并能从失败中恢复或报告。

本架构设计通过采用**事件驱动的异步工作流**，将Go编写的**编排服务**与**Python编写的独立处理Worker**彻底解耦，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (模型处理与分发流程)

```mermaid
graph TD
    subgraph "用户/系统"
        Admin[MLOps工程师]
        ClientApp
    end

    subgraph "ModelManagementService (Go)"
        style ModelManagementService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[Application Service<br/><em>application/service</em>]
        C[Domain Logic<br/><em>(domain/aggregate, domain/service)</em>]
        D[Repository<br/><em>adapter/repository</em>]
        E[Kafka Producer/Consumer<br/><em>adapter/event</em>]
    end

    subgraph "ModelProcessingWorker (Python)"
        style "ModelProcessingWorker (Python)" fill:#e8f5e9
        F[Kafka Consumer]
        G[Processing Pipeline Executor]
        H[Model Optimization Tools<br/>(TF, PyTorch, ONNX)]
        I[Kafka Producer]
    end
    
    subgraph "下游依赖"
        style "下游依赖" fill:#f3e5f5
        S1[Hugging Face Hub]
        S2[file-storage-service]
        Kafka[(Kafka)]
    end

    Admin -- "1. Import/Process Model" --> A
    A -- "调用" --> B
    B -- "2. Create Job, Publish to Kafka" --> E
    E -- "ModelProcessingJobRequest" --> Kafka
    
    Kafka -- "3. Consume Job" --> F
    F -- "调用" --> G
    G -- "4. Download Raw Model from" --> S2
    G -- "使用" --> H
    G -- "5. Upload Optimized Model to" --> S2
    G -- "6. Publish Result" --> I
    I -- "ModelProcessingResultEvent" --> Kafka
    
    Kafka -- "7. Consume Result" --> E
    E -- "调用" --> B
    B -- "8. Update ModelVersion Status" --> D
    
    ClientApp -- "9. GET /available" --> A
```

### 2.2 最终目录结构 (`services/model-management-service/`)

```
model-management-service/
├── cmd/server/
│   └── main.go                 # Go API服务启动入口
├── cmd/worker/                 # ✨ Python Worker的目录 ✨
│   ├── main.py                 # Worker启动入口, 消费Kafka
│   ├── pipeline/
│   │   ├── executor.py
│   │   └── steps/              # 每个优化步骤的Python实现
│   │       ├── convert_tflite.py
│   │       └── quantize.py
│   └── requirements.txt
├── internal/ (Go服务部分)
│   ├── adapter/
│   │   ├── client/
│   │   │   └── file_storage_client.go
│   │   ├── event/
│   │   │   ├── producer.go
│   │   │   └── result_consumer.go # 消费来自Python Worker的结果事件
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── mms_service.go  # 核心应用服务实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── deployment_strategy_service.go # ✨ 部署策略服务 ✨
├── go.mod
└── Dockerfile.server
└── Dockerfile.worker           # 分别为Go服务和Python Worker创建Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 Go服务 (`internal/`) - 编排与管理层

这是整个MLOps流程的“大脑”和“交通警察”。

*   **`domain/service/deployment_strategy_service.go`**:
    *   **`DeploymentStrategyService`**: 一个无状态的领域服务，封装了模型分发的决策逻辑。
    *   **`SelectVersionForDevice(deviceProfile, allAvailableVersions)`**:
        1.  **能力匹配**: 过滤出所有`targetDeviceCapabilities`与`deviceProfile`匹配的版本。
        2.  **策略应用**:
            a. 查找`CANARY`部署策略。如果存在，则根据`canary_percentage`和用户/设备ID的哈希值，决定是返回金丝雀版本还是稳定版本。
            b. 如果不命中金丝雀策略，则返回`STABLE`版本。

*   **`application/service/mms_service.go`**:
    *   **`ImportModel(...)`**:
        1.  从外部源（HF Hub）下载原始模型。
        2.  调用`file-storage-service`上传原始模型。
        3.  在数据库中创建`Model`和`ModelVersion`（状态为`RAW`）记录。
    *   **`CreateModelProcessingJob(ctx, versionID, pipelineName)`**:
        1.  在数据库中创建一个`ModelProcessingJob`记录（状态为`PENDING`）。
        2.  从DB中获取`pipelineDefinition`。
        3.  **发布`ModelProcessingJobRequestEvent`到Kafka**。事件内容包含`jobId`, `sourceModelKey`, `pipelineDefinition`。
    *   **`HandleProcessingResult(ctx, resultEvent)` (由事件消费者调用)**:
        1.  根据`jobId`找到对应的Job和源`ModelVersion`。
        2.  如果处理成功，则在数据库中创建一个新的`ModelVersion`，状态为`READY`，并保存所有产物信息和性能指标。
        3.  更新Job状态为`COMPLETED`或`FAILED`。
    *   **`GetAvailableModels(ctx, deviceProfile)`**:
        1.  从数据库中查询出所有状态为`DEPLOYED`的模型版本。
        2.  调用`domain.DeploymentStrategyService.SelectVersionForDevice()`为每个模型选择最合适的版本。
        3.  返回最终的模型列表。

*   **`adapter/`**:
    *   `event/result_consumer.go`: 消费来自Python Worker的`ModelProcessingResultEvent`。
    *   `event/producer.go`: 发布`ModelProcessingJobRequestEvent`。
    *   其他`adapter`负责gRPC、DB等。

### 3.2 Python Worker (`cmd/worker/`) - 模型处理层

这是一个独立的、可水平扩展的**计算密集型**服务。

*   **`main.py`**:
    *   一个长时间运行的Python进程。
    *   初始化一个Kafka消费者，订阅`model-processing-jobs` Topic。
*   **`pipeline/executor.py`**:
    *   **`PipelineExecutor`**: 接收到一个Job请求后，负责执行。
    *   **`run()` method**:
        1.  根据`sourceModelKey`，调用`file-storage-service`（通过一个简单的HTTP客户端）下载原始模型文件到本地。
        2.  解析`pipelineDefinition`，得到一个步骤列表。
        3.  **按顺序执行每个步骤**:
            a. 调用`steps`目录中对应的Python函数（如`convert_tflite.run(...)`）。
            b. 将上一步的输出作为下一步的输入。
        4.  所有步骤成功后，将最终产物（优化后的模型、日志、性能报告）上传回`file-storage-service`。
        5.  构造一个`ModelProcessingResultEvent`。
        6.  使用Kafka生产者，将结果事件发布出去。
        7.  如果任何步骤失败，则发布一个包含错误信息的失败结果事件。
*   **`pipeline/steps/`**:
    *   每个文件都是一个独立的、可测试的模型处理脚本。
    *   例如，`quantize.py`会使用`tensorflow.lite.TFLiteConverter`或`onnxruntime.quantization`来执行量化操作。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`model-management-service`：
1.  **语言分离，职责清晰**:
    *   **Go服务**: 负责**编排、管理、分发和API服务**。它逻辑清晰、性能高、可靠性强。
    *   **Python Worker**: 负责**计算密集型的模型优化**。它能充分利用Python丰富的AI/ML生态系统。
    *   两者通过**Kafka事件总线**彻底解耦，可以独立部署、扩展和演进。
2.  **异步工作流**: 将长耗时的模型处理流程完全异步化，确保了API服务的低延迟和高可用性。
3.  **配置驱动的灵活性**: 将模型处理的**流水线(Pipeline)**设计为可配置的，使得MLOps工程师可以灵活地组合不同的优化步骤，而无需修改Worker代码。
4.  **智能分发**: 通过**设备能力匹配**和**部署策略（金丝雀）**，实现了对端侧模型分发的精细化控制，便于进行A/B测试和灰度发布。
5.  **完整的MLOps闭环**: 覆盖了从模型导入、处理、版本化、部署到最终分发的整个生命周期，并提供了可追溯性。

这种架构确保了`model-management-service`能够以一种**自动化、可扩展、可靠且灵活**的方式，为CINA.CLUB的端侧AI战略提供强大的技术支持和管理能力。