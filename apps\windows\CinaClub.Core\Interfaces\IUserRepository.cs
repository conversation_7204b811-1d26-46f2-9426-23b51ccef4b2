/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System.Threading.Tasks;
using CinaClub.Core.Models;
using CinaClub.Core.UseCases.Auth;

namespace CinaClub.Core.Interfaces;

/// <summary>
/// 用户仓储接口
/// 定义用户数据访问的抽象
/// </summary>
public interface IUserRepository
{
    /// <summary>
    /// 用户认证
    /// </summary>
    /// <param name="username">用户名/邮箱/手机号</param>
    /// <param name="password">密码</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> AuthenticateAsync(string username, string password);

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    Task<User?> GetByIdAsync(string userId);

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户信息</returns>
    Task<User?> GetByUsernameAsync(string username);

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>用户信息</returns>
    Task<User?> GetByEmailAsync(string email);

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateAsync(User user);

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateAsync(User user);

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteAsync(string userId);

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    /// <param name="refreshToken">刷新令牌</param>
    /// <returns>新的令牌信息</returns>
    Task<TokenRefreshResult?> RefreshTokenAsync(string refreshToken);

    /// <summary>
    /// 注销用户（撤销令牌）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="accessToken">访问令牌</param>
    /// <returns>注销结果</returns>
    Task<bool> LogoutAsync(string userId, string accessToken);
}

/// <summary>
/// 令牌刷新结果
/// </summary>
public class TokenRefreshResult
{
    /// <summary>
    /// 新的访问令牌
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;

    /// <summary>
    /// 新的刷新令牌（可选）
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// 令牌过期时间
    /// </summary>
    public DateTime ExpiresAt { get; set; }
} 