/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

// User status enumeration
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING = 'PENDING',
  DELETED = 'DELETED',
}

// User role enumeration
export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  OPERATIONS = 'OPERATIONS',
  CUSTOMER_SERVICE = 'CUSTOMER_SERVICE',
  CONTENT_MODERATOR = 'CONTENT_MODERATOR',
  ANALYST = 'ANALYST',
  VIEWER = 'VIEWER',
}

// User permission enumeration
export enum Permission {
  // User management
  USER_VIEW = 'USER_VIEW',
  USER_CREATE = 'USER_CREATE',
  USER_EDIT = 'USER_EDIT',
  USER_DELETE = 'USER_DELETE',
  USER_SUSPEND = 'USER_SUSPEND',
  
  // Service management
  SERVICE_VIEW = 'SERVICE_VIEW',
  SERVICE_CONTROL = 'SERVICE_CONTROL',
  SERVICE_CONFIG = 'SERVICE_CONFIG',
  
  // Content management
  CONTENT_VIEW = 'CONTENT_VIEW',
  CONTENT_MODERATE = 'CONTENT_MODERATE',
  CONTENT_DELETE = 'CONTENT_DELETE',
  
  // Analytics
  ANALYTICS_VIEW = 'ANALYTICS_VIEW',
  ANALYTICS_EXPORT = 'ANALYTICS_EXPORT',
  
  // System
  SYSTEM_CONFIG = 'SYSTEM_CONFIG',
  SYSTEM_LOGS = 'SYSTEM_LOGS',
  
  // Financial
  FINANCE_VIEW = 'FINANCE_VIEW',
  FINANCE_MANAGE = 'FINANCE_MANAGE',
}

// Base user interface
export interface User {
  id: string
  email: string
  username: string
  firstName: string
  lastName: string
  avatar?: string
  phone?: string
  status: UserStatus
  roles: UserRole[]
  permissions: Permission[]
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  emailVerified: boolean
  phoneVerified: boolean
  twoFactorEnabled: boolean
}

// User creation request
export interface CreateUserRequest {
  email: string
  username: string
  firstName: string
  lastName: string
  phone?: string
  roles: UserRole[]
  sendInvitation?: boolean
  temporaryPassword?: string
}

// User update request
export interface UpdateUserRequest {
  username?: string
  firstName?: string
  lastName?: string
  phone?: string
  avatar?: string
  roles?: UserRole[]
  status?: UserStatus
}

// User list response
export interface UserListResponse {
  users: User[]
  total: number
  page: number
  pageSize: number
}

// User query parameters
export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: UserStatus
  role?: UserRole
  sortBy?: 'createdAt' | 'lastLoginAt' | 'email' | 'username'
  sortOrder?: 'ASC' | 'DESC'
  dateFrom?: string
  dateTo?: string
}

// User activity log
export interface UserActivity {
  id: string
  userId: string
  action: string
  description: string
  ip: string
  userAgent: string
  metadata?: Record<string, any>
  createdAt: string
}

// User statistics
export interface UserStatistics {
  totalUsers: number
  activeUsers: number
  newUsersThisMonth: number
  userGrowthRate: number
  topRoles: Array<{
    role: UserRole
    count: number
  }>
  topCountries: Array<{
    country: string
    count: number
  }>
}

// Bulk user operations
export interface BulkUserOperation {
  userIds: string[]
  operation: 'suspend' | 'activate' | 'delete' | 'updateRole'
  params?: {
    status?: UserStatus
    roles?: UserRole[]
  }
}

// User invitation
export interface UserInvitation {
  id: string
  email: string
  roles: UserRole[]
  invitedBy: string
  invitedAt: string
  expiresAt: string
  acceptedAt?: string
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'CANCELLED'
}

// Additional types for API service compatibility
export interface UserListParams {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: UserStatus;
  role?: UserRole;
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLogin';
  sortOrder?: 'asc' | 'desc';
  createdAfter?: string;
  createdBefore?: string;
}

export interface BulkOperationResult {
  success: boolean;
  processed: number;
  failed: number;
  errors?: Array<{
    userId: string;
    error: string;
  }>;
} 