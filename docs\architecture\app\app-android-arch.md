好的，遵照您的指示。我将为您生成一份专门针对 **CINA.CLUB Android App** 的、极致细化的、生产级**前端架构设计文档**。

这份文档将基于我们已经确立的**Go-Centric全栈架构**和**Monorepo**模式，深入到Android应用的内部，详细阐述其模块划分、技术实现、与Go核心逻辑的交互方式，以及如何应对性能、安全和离线等挑战。这份文档将是Android团队的**核心开发手册和架构蓝图**。

---
### CINA.CLUB - Android App 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [Android架构师/前端负责人]
**参考平台SRS**: 平台总体SRS v7.0

## 1. 概述与设计哲学

CINA.CLUB Android App是平台触达安卓用户的核心载体。它旨在提供一个**性能媲美原生、功能强大、体验流畅、且绝对安全**的移动应用。本架构的核心目标是在享受React Native跨平台开发效率的同时，通过与Go Mobile的深度集成，将性能和安全敏感的计算密集型任务下沉到原生层，实现两全其美。

### 1.1 核心设计哲学
*   **原生体验优先 (Native-First Experience)**: 尽管使用React Native，但所有UI交互、动画和关键流程都必须达到或超越原生应用的性能和流畅度标准。
*   **逻辑下沉 (Logic Offloading)**: 将复杂的、计算密集型的、安全关键的逻辑（加密、同步、AI推理）从JavaScript线程下沉到由Go编写的、运行在后台线程的原生代码中。
*   **状态驱动与响应式 (State-Driven & Reactive)**: UI是应用状态的确定性函数。所有数据流都是单向的，状态的变更会自动、高效地驱动UI的更新。
*   **离线优先 (Offline-First)**: 核心功能（如查看已同步的PKB/聊天记录）必须在无网络连接时可用。所有写操作都应先在本地完成，再异步同步到云端。
*   **分层与解耦**: 严格遵循分层架构，将UI、业务逻辑、状态管理、核心能力和原生通信清晰地分离开来。

---

## 2. 技术栈选型

| 领域             | 技术选型                                         | 理由                                                                 |
|------------------|--------------------------------------------------|----------------------------------------------------------------------|
| **UI框架**         | **React Native (New Architecture: Fabric & TurboModules)** | 启用新架构以获得更接近原生的性能和更高效的JS-Native通信。            |
| **编程语言**     | **TypeScript** (JS/TS层), **Kotlin** (原生层)        | Kotlin是Android官方推荐语言，用于编写桥接模块。                        |
| **核心逻辑引擎** | **Go Mobile**                                    | 将`/core`中的Go代码编译为`.aar`库，供原生层调用。                      |
| **状态管理**     | **Zustand**                                      | 轻量、高效、易于按功能域拆分Store。                                    |
| **数据请求与缓存**| **TanStack Query**                               | 管理所有与后端API的异步数据交互，内置强大的缓存和同步机制。            |
| **UI组件库**     | **Tamagui**                                      | 用于构建平台自有设计系统，高性能且跨平台（与Web共享）。                |
| **导航与路由**   | **React Navigation**                             | React Native社区的事实标准。                                         |
| **本地数据库**   | **SQLite** (通过**Go Mobile的CGO绑定**进行访问)      | Go层可以直接、高性能地读写本地数据库，为JS层提供简单的异步接口。         |
| **本地AI推理**   | **Go + CGO + llama.cpp**                         | 高性能的本地LLM推理方案。                                            |
| **E2EE加密**     | **Go + golang.org/x/crypto**                     | 保证加密算法的平台一致性和最高安全性。                                 |
| **安全存储**     | **Android Keystore** (通过Go Mobile -> JNI调用)      | 用于安全地存储加密后的DEK（数据加密密钥）。                            |

---

## 3. Monorepo中的应用架构 (`apps/mobile/`)

```
apps/mobile/
├── android/                    # Android原生项目
│   ├── app/
│   │   ├── build.gradle        # <-- 1. 依赖 'core-go.aar'
│   │   └── src/main/
│   │       ├── java/com/cinaclub/
│   │       │   ├── MainActivity.java
│   │       │   └── nativemodules/  # <-- 2. 原生桥接模块
│   │       │       ├── CoreGoBridgePackage.java
│   │       │       ├── CryptoModule.kt
│   │       │       ├── DataSyncModule.kt
│   │       │       └── AICoreModule.kt
│   │       └── jniLibs/          # <-- 3. Go Mobile & llama.cpp 的 .so 文件
│   │           ├── arm64-v8a/
│   │           └── ...
│   └── libs/
│       └── core-go.aar           # <-- 4. Go核心逻辑库
├── src/
│   ├── api/                    # 5. API层 (由go-core-wrapper和TanStack Query组合)
│   ├── assets/                 # 6. 静态资源
│   ├── components/             # 7. 共享的业务组件
│   ├── core/                   # 8. ✨ 核心能力封装层 (JS/TS) ✨
│   │   ├── aic.ts
│   │   ├── crypto.ts
│   │   └── datasync.ts
│   ├── navigation/             # 9. 导航与路由
│   ├── screens/                # 10. 页面/屏幕
│   ├── state/                  # 11. 状态管理 (Zustand Stores)
│   ├── theme/                  # 12. 主题与样式
│   ├── utils/                  # 13. JS/TS工具函数
│   └── App.tsx                 # 应用根组件
└── package.json
```

### 3.1 核心模块深度解析 (`apps/mobile/src/`)

#### `src/core/` - Go核心逻辑的TypeScript封装层

这是连接JS世界和原生Go世界的**关键桥梁**。它封装了对原生模块的调用，为上层提供了干净、类型安全、基于Promise和事件的API。这个目录的设计直接对应于`packages/go-core-wrapper`。

*   **`core/crypto.ts`**:
    *   `async function deriveKey(password: string): Promise<void>`: 调用`CryptoModule.deriveKey`。
    *   `async function encrypt(data: Uint8Array): Promise<Uint8Array>`: 调用`CryptoModule.encrypt`。
    *   `async function decrypt(ciphertext: Uint8Array): Promise<Uint8Array>`: 调用`CryptoModule.decrypt`。
*   **`core/datasync.ts`**:
    *   `class SyncEngine`: 封装了`DataSyncModule`的API，提供`pushChanges()`, `pullChanges()`等方法。
*   **`core/aic.ts`**:
    *   `class LLM`: 封装了`AICoreModule`。
    *   `predictStream(prompt: string, onToken: (token: string) => void): void`: 调用`AICoreModule.predictStream`，并在内部设置一个`DeviceEventEmitter`监听器来接收来自原生层的token流，然后通过`onToken`回调函数传递出去。

#### `src/state/` - 状态管理中心

*   使用Zustand创建多个独立的、按业务域划分的Store。
*   **`authStore.ts`**: 管理`accessToken`, `currentUser`等。提供`login`, `logout`, `refreshToken`等异步action。
*   **`pkbStore.ts`**: 管理PKB的本地数据视图。包含`items`, `folders`, `tags`等状态，以及`addItem`, `updateItem`等action。这些action会调用`core/datasync`来处理实际的数据操作和同步。
*   **`chatStore.ts`**: 管理聊天消息、房间列表、在线状态等。

#### `src/api/` - 数据请求层

*   **职责**: 封装所有与后端API的交互。
*   **实现**: 使用**TanStack Query**。
*   **`queries/`**:
    *   `useUserProfile.ts`: `useQuery({ queryKey: ['user', id], queryFn: () => apiClient.users.get(...) })`。
    *   `useServiceList.ts`: `useInfiniteQuery(...)`，用于实现服务列表的无限滚动。
*   **`mutations/`**:
    *   `useUpdateProfile.ts`: `useMutation({ mutationFn: (data) => apiClient.users.update(...), onSuccess: () => queryClient.invalidateQueries(['user']) })`。
    *   **乐观更新**: 对于点赞等操作，使用乐观更新来提供即时UI反馈。

---

## 4. 关键功能实现架构

### 4.1 应用启动与E2EE解锁流程

1.  **`App.tsx`渲染**: 应用启动。
2.  **检查登录状态**: `authStore`检查`accessToken`是否存在且有效。
3.  **如果已登录**:
    *   **检查会话是否解锁**: `cryptoStore`（一个专门管理加密会话的Zustand store）检查内存中的DEK是否存在。
    *   **如果未解锁**: 导航到“主密码/生物识别”解锁页面。
    *   **用户输入密码/指纹**:
        a. 调用`core/crypto.ts`的`deriveKey`或类似方法。
        b. **Go层 (`core/crypto`)**: 调用JNI，使用Android Keystore中存储的加密盐，从用户密码派生出MEK。
        c. **Go层**: 使用MEK解密存储在Keystore中的加密DEK。
        d. **Go层**: 将解密后的DEK明文安全地保存在Go的内存中（绝不返回给JS层）。
        e. 返回`true`给JS层，表示解锁成功。
    *   `cryptoStore`更新状态为`unlocked`。
    *   导航到应用主界面。
4.  **后台同步**: 解锁成功后，`data-sync`模块在后台自动触发一次`pullChanges`，拉取最新的E2EE数据。

### 4.2 离线优先与本地数据库 (PKB示例)

1.  **数据源**: `pkbStore`的数据**不直接来自API**，而是来自**本地SQLite数据库**。UI组件订阅`pkbStore`的状态。
2.  **本地数据库访问**: 所有对SQLite的读写操作都由**Go核心逻辑 (`core/datasync`的一部分)完成**。
3.  **读操作**:
    *   `pkbStore`初始化时，调用`core/datasync`的`getAllPKBItems()`。
    *   Go层查询本地SQLite，解密（调用`core/crypto`），然后将结果（JSON字符串）返回给JS层。
    *   `pkbStore`解析JSON并更新其状态。UI响应式地显示数据。
4.  **写操作 (离线)**:
    *   用户离线时创建了一篇笔记。
    *   UI调用`pkbStore.createItem(content)`。
    *   `pkbStore`调用`core/datasync`的`createLocalItem(content)`。
    *   Go层加密内容，并将其写入本地SQLite，同时在另一张“待同步队列”表中记录一条“创建”操作。
    *   Go层返回成功，`pkbStore`从本地重新加载数据，UI立即更新。
5.  **同步**:
    *   网络恢复后，`data-sync`的同步引擎被触发。
    *   它读取“待同步队列”表，并调用`pushChanges`将本地的变更同步到云端。

---

## 5. 总结

本Android应用架构是一个**高度分层、职责清晰、性能与安全并重**的生产级方案。

*   **UI层 (React Native)**: 专注于**表现**，享受现代前端框架带来的开发效率和丰富的生态。
*   **逻辑封装层 (TypeScript - `src/core`, `src/api`, `src/state`)**: 负责**业务逻辑编排**，作为UI与核心能力之间的“翻译官”和“调度员”。
*   **核心引擎层 (Go)**: 负责所有**脏活累活**——计算密集（AI）、安全敏感（加密）、性能关键（DB访问）的操作。它为上层提供了稳定、可靠、高性能的能力基石。

通过这种**“JS/TS负责貌美如花，Go负责赚钱养家”**的模式，我们可以在保证跨平台开发效率的同时，构建出一个不亚于纯原生应用的、功能强大且绝对安全的Android应用。


好的，遵照您的指示。考虑到在某些场景下，特别是对性能、系统深度集成和UI自定义要求达到极致时，纯原生开发可能是更优选择。我将为您生成一份专门针对 **纯原生Android前端** 的、极致细化的、生产级**架构设计文档**。

这份架构将：
1.  **完全对齐**我们之前定义的**Go-Centric后端和Monorepo架构**。
2.  采用业界领先的**现代Android开发（Modern Android Development）**最佳实践。
3.  深度整合**Go Mobile**，将在Go中实现的核心逻辑（加密、同步、AI）无缝集成到Android原生应用中。
4.  采用**模块化**和**分层**的设计，以应对CINA.CLUB平台的复杂性。

---
### CINA.CLUB - 原生Android应用 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [Android架构师]

## 1. 概述与设计哲学

### 1.1 项目愿景与目的
CINA.CLUB Android应用旨在为用户提供一个性能卓越、体验流畅、安全可靠的原生移动端入口。它不仅是后端强大功能的最终呈现者，更是平台**端侧智能(On-Device Intelligence)**和**端到端加密(E2EE)**战略的关键执行者。本架构的目标是构建一个**现代化的、模块化的、可维护、可测试、且与平台Go-Centric理念深度融合**的Android应用。

### 1.2 核心设计哲学
1.  **UI由状态驱动 (UI is a function of State)**: 遵循声明式UI范式，UI根据应用状态自动更新，而不是手动操作View。
2.  **分层架构 (Layered Architecture)**: 严格分离UI层、领域/业务逻辑层和数据层，实现高内聚、低耦合。
3.  **依赖注入 (Dependency Injection)**: 所有组件都通过构造函数或DI框架获取其依赖，便于测试和替换。
4.  **单一数据源 (Single Source of Truth)**: 对于特定数据，应用中应有唯一的、权威的数据来源（通常是Repository层）。
5.  **Go核心，Kotlin胶水，Compose皮肤**:
    *   **Go (via Go Mobile)**: 执行最核心、最复杂的计算和协议（加密、同步、AI）。
    *   **Kotlin**: 作为主要的Android开发语言，负责业务逻辑编排、Android SDK交互和作为连接UI与Go核心的“胶水”。
    *   **Jetpack Compose**: 作为唯一的UI工具包，构建整个应用的界面。

---

## 2. 核心技术选型 (Modern Android Development)

| 领域             | 技术选型                                         | 理由                                                                 |
|------------------|--------------------------------------------------|----------------------------------------------------------------------|
| **编程语言**     | **Kotlin** (100%)                                | Google官方首选，语言特性现代、安全，与Java完全兼容。                   |
| **UI工具包**     | **Jetpack Compose**                              | 现代声明式UI框架，极大提升开发效率和UI表现力。                         |
| **架构模式**     | **MVVM (Model-View-ViewModel) + Clean Architecture** | 业界标准，清晰地分离了职责，提高了可测试性。                         |
| **异步处理**     | **Kotlin Coroutines & Flow**                     | 官方推荐的异步解决方案，简化了线程管理和响应式数据流。                 |
| **依赖注入**     | **Hilt (Dagger)**                                | Google官方推荐，简化了依赖注入的配置，与Jetpack组件无缝集成。          |
| **核心逻辑集成** | **Go Mobile**                                    | 将`/core`中的Go库编译为AAR文件，通过JNI在Kotlin中调用。                |
| **网络请求**     | **gRPC-Kotlin/Java + OkHttp**                      | 与后端gRPC API直接通信，类型安全，性能高。OkHttp作为底层HTTP客户端。   |
| **本地数据库**   | **Room (on top of SQLite)**                      | Jetpack官方推荐的持久化库，提供了编译时SQL校验和对Coroutines/Flow的良好支持。|
| **导航**         | **Jetpack Navigation Compose**                   | 官方的、专为Compose设计的导航组件。                                  |

---

## 3. Monorepo模块化架构

Android项目本身也将采用**多模块(Multi-module)**架构，这与Monorepo的理念保持一致。它能提升构建速度、强制逻辑分离、并促进团队协作。

### 3.1 项目模块结构 (`apps/mobile/android/`)

```
android/
├── app/                      # ✨ 主应用模块 (Application Shell) ✨
│   ├── build.gradle.kts      # <-- 依赖所有:feature和:core模块
│   └── src/main/java/com/cinaclub/
│       ├── di/               # Hilt的顶层AppModule
│       ├── navigation/       # 应用的主导航图
│       └── CinaClubApplication.java
├── build-logic/              # Gradle构建逻辑 (Convention Plugins)
├── core/                     # ✨ Android核心模块 ✨
│   ├── build-logic/
│   ├── go-bridge/            # 1. Go Mobile桥接层
│   ├── network/              # 2. 网络层 (gRPC, OkHttp)
│   ├── database/             # 3. 本地数据库 (Room)
│   ├── security/             # 4. 安全存储 (EncryptedSharedPreferences)
│   └── common/               # 5. 通用工具、Base类
├── feature/                  # ✨ 功能模块 ✨
│   ├── build-logic/
│   ├── chat/                 # 聊天功能
│   ├── pkb/                  # 个人知识库功能
│   ├── auth/                 # 认证功能
│   ├── profile/              # 个人资料功能
│   └── ...
└── libs/
    └── core-go.aar           # Go Mobile编译产物
```

### 3.2 各模块深度解析

#### `:app` - 主应用模块 (Application Shell)

*   **职责**:
    *   一个非常“瘦”的模块。
    *   作为所有`feature`模块和`core`模块的**组装者**。
    *   包含应用的入口`MainActivity`。
    *   定义顶层的Hilt依赖注入容器 (`@HiltAndroidApp`)。
    *   定义应用的主导航图 (`NavHost`)，将所有功能的导航子图连接起来。

#### `:core:go-bridge` - Go Mobile桥接层

*   **职责**: **作为Kotlin世界和Go世界的唯一通道**。
*   **内容**:
    *   **JNI封装类 (`GoCore.kt`)**: 一个Kotlin `object` (单例)，内部通过`external`关键字声明对Go Mobile导出函数的调用。
    *   **数据转换**: 包含将Kotlin/Java数据类型（`String`, `ByteArray`）与Go兼容类型进行转换的逻辑。
    *   **异步封装**: 将Go中可能阻塞的或基于回调的函数，封装成Kotlin的`suspend`函数或`Flow`。
        ```kotlin
        // GoCore.kt
        object GoCore {
            // 加载Go编译的.so库
            init { System.loadLibrary("core-go") }
        
            // 声明JNI函数
            private external fun encrypt(key: ByteArray, plaintext: ByteArray): ByteArray
        
            // 封装为suspend函数
            suspend fun encryptData(key: ByteArray, plaintext: ByteArray): ByteArray =
                withContext(Dispatchers.Default) { // 在后台线程执行
                    encrypt(key, plaintext)
                }
        }
        ```

#### `:core:network` - 网络层

*   **职责**: 封装所有与后端的网络通信。
*   **内容**:
    *   **gRPC客户端**: 使用`grpc-kotlin`和`/core/api`生成的代码，创建所有微服务的gRPC客户端实例。
    *   **AuthInterceptor**: 一个OkHttp/gRPC拦截器，负责从`:core:security`中获取Access Token，并自动附加到每个出站请求头中。同时处理401错误，触发令牌刷新逻辑。
    *   **Hilt Module**: 提供`@Provides`函数，向整个应用注入配置好的gRPC客户端。

#### `:core:database` - 本地数据库层

*   **职责**: 提供统一的、类型安全的本地数据访问。
*   **内容**:
    *   **RoomDatabase定义**: 定义应用的`AppDatabase`。
    *   **DAO (Data Access Objects)**: 为每个需要本地缓存的实体（如`PKBItemEntity`, `UserCacheEntity`）定义DAO接口。
    *   **Type Converters**: 定义Room的类型转换器，用于处理复杂类型。

#### `feature/*` - 功能模块

*   **职责**: 每个模块都是一个高内聚的、垂直的功能切片（如聊天、认证）。
*   **内部架构**: 每个`feature`模块内部，都严格遵循**MVVM + Clean Architecture**。
    *   **`data/`**:
        *   `repository/`: **仓储实现**。是该功能的数据来源。它会调用`:core:network`的API客户端（远程数据）和`:core:database`的DAO（本地数据），并协调两者。对于E2EE功能，它还会调用`:core:go-bridge`进行加解密。
    *   **`domain/`**:
        *   `repository/`: **仓储接口**。
        *   `usecase/`: **用例**。封装了该功能的单一业务逻辑，如`GetChatHistoryUseCase`。它调用仓储接口。
    *   **`presentation/`**:
        *   `viewmodel/`: **ViewModel**。为UI准备状态，并处理UI事件。它调用`usecase`。
        *   `ui/`: **Jetpack Compose UI**。`@Composable`函数，只负责渲染`ViewModel`提供的状态，并将用户交互事件通知给`ViewModel`。

---

## 4. 核心功能实现策略 (原生Android)

### 4.1 E2EE功能 (PKB, Memory)

1.  **密钥管理**:
    *   用户输入主密码后，`:feature:auth`模块调用`:core:go-bridge` -> `core/crypto`的`DeriveMasterKey`派生出**MEK**。
    *   MEK用于解密存储在Android **Keystore**中的**DEK**（通过`EncryptedSharedPreferences`）。
    *   解密后的DEK明文由一个单例的`SessionKeyProvider`在内存中持有，并有生命周期管理。
2.  **数据读写**:
    *   `:feature:pkb`的`Repository`在写入数据前，从`SessionKeyProvider`获取DEK，调用`:core:go-bridge`的`encryptData`加密内容，然后将**密文**存入Room数据库。
    *   读取时反之。

### 4.2 本地LLM聊天

1.  **模型管理**:
    *   `:feature:chat`的`ViewModel`在初始化时，触发一个`ModelSyncUseCase`。
    *   该UseCase调用`:core:go-bridge`，Go层再调用`model-management-service` API，负责模型的下载、缓存和版本管理。
2.  **推理流程**:
    *   用户发送消息，`ChatViewModel`调用`SendMessageUseCase`。
    *   UseCase调用`:core:go-bridge` -> `core/aic`的`llm.predictStream`方法。
    *   Go层通过JNI调用`llama.cpp`，并通过**JNI回调**将生成的token流式返回给Kotlin。
    *   Kotlin的回调将token包装成一个`Flow<String>`。
    *   `ChatViewModel`收集(collect)这个`Flow`，并实时更新UI状态`StateFlow<String>`。
    *   Compose UI订阅这个`StateFlow`，实现打字机效果。

## 5. 总结

本原生Android架构是一个**高度现代化、模块化和分层**的解决方案，完美地融入了CINA.CLUB的Go-Centric全栈体系。

*   **清晰的职责**:
    *   **Go (`core/`)**: 负责“计算”，执行密码学、同步协议、AI推理等硬核任务。
    *   **Kotlin (`feature/domain`, `feature/data`)**: 负责“编排”，连接Android世界和Go世界，管理数据流和业务逻辑。
    *   **Compose (`feature/presentation`)**: 负责“呈现”，构建美观、响应式的用户界面。
*   **高可维护性与测试性**: 通过多模块划分和依赖注入，每个功能、每个层次都可以被独立开发、修改和单元测试。
*   **性能与体验**: 充分利用了Kotlin Coroutines的异步能力、Jetpack Compose的声明式UI优势，以及Go和C++的原生执行性能，能够为用户提供极致的移动端体验。

这份架构为CINA.CLUB Android应用的长期、健康发展提供了坚实的基础。

好的，遵照您的指示。我将为您生成一份专门针对 **CINA.CLUB 原生Android全平台** 的、极致细化的、生产级**架构设计文档**。

这份架构将覆盖从**手机、平板，到未来的可穿戴设备、车机、大屏设备**的统一解决方案。其核心理念是：**“一套核心逻辑，多设备UI自适应与能力适配”**。

这份架构将：
1.  **完全对齐**我们之前定义的**Go-Centric全栈架构**和**Monorepo**模式。
2.  采用Google官方推荐的**现代Android开发（Modern Android Development）**全家桶。
3.  通过**Go Mobile**和**JNI**，将在Go中实现的核心逻辑（加密、同步、AI）无缝集成到Android原生应用中。
4.  采用**多模块(Multi-module)**和**分层**设计，并为不同设备形态（手机、平板、大屏）的UI适配提供明确的策略。

---
### CINA.CLUB - 原生Android全平台应用 架构设计文档

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [Android架构师/移动端负责人]

## 1. 概述与设计哲学

### 1.1 项目愿景与目的
CINA.CLUB Android应用旨在为广大的Android生态用户提供一个**性能卓越、体验一致、安全可靠**的原生移动端入口。本架构的目标是构建一个能够**自适应多种屏幕尺寸和设备形态**（手机、折叠屏、平板、电视、车机）的、现代化的、可维护、可测试的Android应用，同时深度集成平台的Go核心能力。

### 1.2 核心设计哲学
1.  **声明式UI (Declarative UI)**: **Jetpack Compose是构建UI的唯一标准**。UI是应用状态的函数，实现UI与逻辑的彻底解耦。
2.  **分层与模块化 (Layered & Modular)**: 采用Google推荐的**多模块架构**，并遵循**MVVM + Clean Architecture**，严格分离UI、业务逻辑和数据层。
3.  **单一数据源 (Single Source of Truth)**: 通过Repository模式，确保应用状态的一致性和可靠性。
4.  **响应式与异步优先 (Reactive & Async First)**: 全面拥抱**Kotlin Coroutines & Flow**，构建健壮、高效的异步数据流。
5.  **依赖注入 (Dependency Injection)**: **Hilt**作为官方DI框架，贯穿整个应用，管理所有组件的生命周期和依赖关系。
6.  **Go核心，Kotlin胶水，Compose皮肤**:
    *   **Go (via Go Mobile)**: 执行平台最核心的计算和协议。
    *   **Kotlin**: 作为主要的Android开发语言，负责业务逻辑编排、Android SDK交互和作为连接UI与Go核心的“胶水”。
    *   **Jetpack Compose**: 构建所有自适应的UI界面。

---

## 2. 核心技术选型 (Modern Android Development)

| 领域             | 技术选型                                         | 理由                                                                 |
|------------------|--------------------------------------------------|----------------------------------------------------------------------|
| **UI框架**         | **Jetpack Compose**                              | 官方现代UI工具包，声明式，与Jetpack生态无缝集成，天然支持自适应布局。  |
| **编程语言**     | **Kotlin** (100%)                                | 官方首选，安全、简洁、现代。                                         |
| **核心逻辑集成** | **Go Mobile** (编译为AAR) -> **JNI** -> **Kotlin** | 复用`/core`的Go代码，保证平台一致性。                                 |
| **异步处理**     | **Kotlin Coroutines & Flow**                     | 官方推荐的异步解决方案。                                             |
| **架构模式**     | **MVVM + Clean Architecture**                    | 业界标准，分层清晰，高可测试性。                                     |
| **依赖注入**     | **Hilt**                                         | 官方DI框架，简化了Dagger的配置。                                     |
| **数据请求**     | **gRPC-Kotlin/Java + OkHttp**                      | 类型安全、高性能的API客户端。                                        |
| **本地数据库**   | **Room** (on top of SQLite)                      | 官方推荐的持久化库，编译时SQL校验，与Flow集成良好。                    |
| **导航**         | **Jetpack Navigation Compose**                   | 官方的、专为Compose设计的类型安全导航组件。                          |
| **安全存储**     | **Jetpack Security (EncryptedSharedPreferences)**  | 用于安全地存储加密密钥、认证令牌等。                                   |

---

## 3. Monorepo模块化架构

Android项目采用Gradle的多模块(Multi-module)项目结构，这是大型Android项目的最佳实践。

### 3.1 项目模块结构 (`apps/android/`)

```
android/
├── app/                      # ✨ 1. 主应用模块 (Application Shell) ✨
│   ├── build.gradle.kts      # <-- 依赖所有:feature和:core模块
│   └── src/main/java/com/cinaclub/
│       ├── di/
│       ├── navigation/
│       └── CinaClubApplication.java
├── build-logic/              # 2. Gradle构建逻辑 (Convention Plugins)
├── core/                     # 3. ✨ Android核心库模块 ✨
│   ├── common/               #    通用工具、Base类、资源
│   ├── data/                 #    核心数据层 (Repository接口, Network, DB, DataStore)
│   ├── domain/               #    核心领域层 (UseCase接口, Domain Models)
│   └── go-bridge/            #    ✨ Go Mobile桥接层 (JNI & Kotlin封装)
├── feature/                  # 4. ✨ 功能模块 ✨
│   ├── chat/                 #    聊天功能
│   ├── pkb/                  #    个人知识库功能
│   ├── auth/                 #    认证功能
│   └── ...
└── libs/
    └── core-go.aar           # 5. Go Mobile编译产物
```

### 3.2 各模块深度解析

#### `:app` - 主应用模块 (Application Shell)

*   **职责**:
    *   应用的“组装车间”和入口。
    *   定义顶层`@HiltAndroidApp`。
    *   初始化应用级服务（如日志、崩溃上报）。
    *   构建主`NavHost`，将所有`feature`模块的导航图连接起来。

#### `:core` - 核心库模块 (Core Library)

这是所有`feature`模块都依赖的、平台通用的基础能力层。

*   **`:core:common`**: 存放整个App可复用的工具类（如日期格式化）、扩展函数、`BaseViewModel`、以及共享的String/Color/Drawable资源。
*   **`:core:data`**: **数据层核心**。
    *   定义所有`Repository`的**接口**。
    *   封装**Room Database**的实例创建和DAO的提供（通过Hilt）。
    *   封装**gRPC客户端**的创建和拦截器（如AuthInterceptor）的配置。
    *   封装对**DataStore**或`EncryptedSharedPreferences`的访问。
*   **`:core:domain`**: **领域层核心**。
    *   定义所有`UseCase`的**基类或接口**。
    *   定义应用中流转的纯粹的、与UI和数据源无关的**领域模型**（Kotlin `data class`）。
*   **`:core:go-bridge`**: **连接Kotlin与Go的关键桥梁**。
    *   **JNI封装**: 包含一个Kotlin `object` (e.g., `GoCore.kt`)，通过`external fun`声明对Go导出函数的调用。
    *   **异步适配**: 将Go的阻塞或回调式函数，封装成Kotlin的`suspend`函数或`Flow`。
    *   **数据转换**: 处理`ByteArray`, `String`等在Kotlin和JNI之间的转换。
    *   **错误处理**: 将Go返回的`error`字符串，转换为Kotlin的`Exception`。

#### `:feature` - 功能模块

*   **职责**: 每个模块都是一个高内聚、可独立开发和测试的**垂直功能切片**。
*   **内部架构 (MVVM + Clean Architecture)**:
    *   **`data`**:
        *   **`repository`**: 实现`:core:data`中定义的`Repository`接口。它负责决策是从网络拉取数据，还是从本地数据库读取，以及何时调用`go-bridge`。
        *   **`remote/dto`**: 定义与网络API对应的DTO (Data Transfer Object)。
        *   **`local/entity`**: 定义Room数据库的`@Entity`。
    *   **`domain`**:
        *   `usecase`: 实现具体的业务逻辑，如`LoginUseCase`。它依赖`Repository`接口。
    *   **`presentation`**:
        *   `viewmodel`: `HiltViewModel`，持有UI状态（`StateFlow`），处理UI事件，并调用`UseCase`。
        *   `ui`: Jetpack Compose `@Composable`函数，构建UI。
        *   `navigation`: 定义该功能内部的导航路径，并提供一个函数将其集成到主导航图中。

---

## 4. 跨设备UI适配策略

这是实现“全平台”目标的关键。

1.  **自适应布局组件**:
    *   **`BoxWithConstraints`**: 获取可用空间，动态选择不同布局。
    *   **`LazyVerticalGrid`**: 在手机上显示单列，在平板上显示多列。
    *   **`WindowSizeClass`**: 使用官方库根据屏幕宽度（Compact, Medium, Expanded）来选择不同的顶层布局。
2.  **为大屏设计的导航**:
    *   使用**`NavigationSuite`**或类似的模式，在小屏上显示底部导航栏(`NavigationBar`)，在大屏上自动切换为侧边导航栏(`NavigationRail`)或永久抽屉(`NavigationDrawer`)。
3.  **创建可复用的`AdaptiveScaffold`**:
    *   构建一个自定义的`@Composable`函数`AdaptiveScaffold`，它内部封装了对`WindowSizeClass`的判断，并自动渲染出适合当前设备的顶层UI骨架（如`TopAppBar` + `BottomBar` vs. `NavigationRail` + `DetailView`）。
4.  **功能模块的自适应UI**:
    *   每个`feature`模块的UI，特别是列表-详情页，都必须设计为可自适应的。
    *   例如，`:feature:chat`在手机上是两个独立的页面（会话列表 -> 聊天页），在平板上则可以是一个双栏布局（左侧列表，右侧聊天窗口）。

---

## 5. 核心功能实现架构

### 5.1 E2EE与本地LLM
实现路径与iOS版本高度一致，只是技术栈换成了Android对应的部分：
*   **密钥管理**: MEK由主密码通过`:core:go-bridge`派生。加密后的DEK存储在**Android Keystore**中，通过`EncryptedSharedPreferences`进行访问。
*   **加解密**: `Repository`层调用`:core:go-bridge`中的Kotlin封装函数，该函数再通过JNI调用Go的加密库。
*   **本地LLM**: `ViewModel`调用UseCase，UseCase调用Repository，Repository再调用`:core:go-bridge`的`predictStream`。Go层通过JNI回调将token流式返回给Kotlin，Kotlin层将其转换为`Flow<String>`，ViewModel收集这个Flow并更新UI状态。

---

## 6. 总结

本原生Android应用架构是一个**遵循Google官方最佳实践、高度模块化、且与平台Go核心深度融合**的生产级解决方案。

*   **现代化与高效**: 全面采用Kotlin + Coroutines + Flow + Compose + Hilt + Jetpack，代表了Android开发的最高水平，极大地提升了开发效率和应用质量。
*   **可维护与可扩展**: 通过Gradle的多模块架构和Clean Architecture，将复杂的应用拆分为独立的、可独立编译和测试的功能单元，非常适合大规模团队协作和长期迭代。
*   **Go-Centric协同**: 通过**`:core:go-bridge`**这个关键的“翻译层”，将平台最核心的Go逻辑安全、高效地引入到Android原生应用中，实现了逻辑复用和平台一致性。
*   **全平台自适应**: 从一开始就将多设备形态纳入设计考量，通过`WindowSizeClass`和自适应组件，使得一套代码库能够优雅地适配手机、平板、折叠屏等多种设备。

这份架构为CINA.CLUB在整个Android生态的成功奠定了坚实的技术基础，确保了它能为数以十亿计的Android用户提供一致、流畅、安全的顶级原生体验。

