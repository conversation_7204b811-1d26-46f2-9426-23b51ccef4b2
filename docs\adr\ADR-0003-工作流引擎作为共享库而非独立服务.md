### ADR-003: 工作流引擎作为共享库而非独立服务

**标题**: 将工作流引擎实现为共享库(`pkg/workflow`)，而非独立的微服务
**状态**: 已接受
**日期**: 2025-06-18

#### 背景 (Context)

平台内的`ai-assistant-service`和`routines-service`都需要一个工作流引擎来编排一系列动作。`ai-assistant-service`需要它来执行由AI动态规划的、实时的、一次性的任务。`routines-service`需要它来执行用户预定义的、由事件触发的、持久化的自动化规则。我们需要决定这个引擎的实现形式。

#### 决策驱动因素 (Decision Drivers)

*   **性能与延迟**: AI助手的交互对延迟极其敏感。工作流的编排和执行过程不应引入显著的网络开销。
*   **代码复用**: 工作流的图执行逻辑（拓扑排序、上下文传递、错误处理）是通用的，应该被复用。
*   **业务隔离**: 引擎本身不应包含任何具体的业务逻辑（如“如何调用搜索服务”）。它应该只负责执行一个抽象的、由节点和边组成的图。
*   **状态管理**: AI助手的动态工作流状态是临时的、会话级别的（适合Redis）。用户自定义的自动化规则的执行日志是需要审计的、持久化的（适合PostgreSQL）。引擎的实现方式应能支持这两种不同的状态管理需求。
*   **架构简洁性**: 避免引入不必要的、增加系统整体复杂度的微服务。

#### 备选方案 (Considered Options)

1.  **独立的 `workflow-engine-service`**:
    *   创建一个新的微服务，专门负责接收工作流定义（JSON/YAML格式）并执行它。当需要执行具体动作时，它通过网络回调（RPC/HTTP）其他服务。
    *   *优点*: 职责单一，可以独立部署和扩展。
    *   *缺点*:
        *   **性能灾难**: 每个节点的执行都变成了多次网络往返调用，极大地增加了延迟。
        *   **架构复杂**: 引入了复杂的回调机制和双向依赖，状态同步困难。
        *   **过度设计**: 对于本平台当前的实时和事件驱动场景，这是一个过于沉重的解决方案。

2.  **作为共享库 `pkg/workflow`**:
    *   在Monorepo的`pkg/`目录下创建一个通用的、无状态的工作流引擎库。
    *   `ai-assistant-service`和`routines-service`各自导入这个库。
    *   每个服务在自己的业务逻辑中，使用这个库来构建和执行工作流图。执行过程是进程内的函数调用。
    *   *优点*:
        *   **极致性能**: 零网络开销的编排执行，延迟最低。
        *   **代码复用最大化**: 核心引擎逻辑被复用。
        *   **业务逻辑完全解耦**: 每个服务只实现自己需要的“工作流节点”，引擎不关心节点内容。
        *   **状态管理灵活**: 每个服务可以根据自己的需求选择最合适的方式来持久化工作流的状态。
    *   *缺点*: 引擎的更新需要重新编译和部署使用它的服务。但在Monorepo和自动化CI/CD下，这个缺点的影响很小。

#### 决策结果 (Decision Outcome)

**选择方案2：作为共享库 `pkg/workflow`**。

**理由**:
共享库方案完美地满足了**高性能**和**业务隔离**的核心需求。对于CINA.CLUB的AI助手和自动化流程这类对延迟敏感的场景，进程内执行的性能优势是压倒性的。独立服务方案引入的巨大网络开销和架构复杂性，与我们追求的“智能、快速响应”的用户体验背道而驰。因此，将工作流引擎的核心逻辑抽象为共享库，由需要它的服务在本地进程中实例化和执行，是最高效、最简洁、最符合平台利益的架构选择。