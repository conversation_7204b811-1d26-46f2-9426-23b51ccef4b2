/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url = uri("https://jitpack.io") }
    }
}

rootProject.name = "CinaClub"

include(":app")

// Core modules
include(":core:common")
include(":core:data")
include(":core:domain")
include(":core:go-bridge")

// Feature modules
include(":feature:auth")
include(":feature:chat")
include(":feature:pkb")
include(":feature:profile")
include(":feature:home")
include(":feature:ai-assistant")
include(":feature:live")
include(":feature:knowledge")
include(":feature:social")
include(":feature:payment") 