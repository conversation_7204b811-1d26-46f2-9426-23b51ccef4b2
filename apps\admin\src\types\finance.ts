/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

// Payment status enumeration
export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',
}

// Payment method types
export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  PAYPAL = 'PAYPAL',
  STRIPE = 'STRIPE',
  WECHAT_PAY = 'WECHAT_PAY',
  ALIPAY = 'ALIPAY',
  CRYPTOCURRENCY = 'CRYPTOCURRENCY',
  WALLET = 'WALLET',
}

// Transaction types
export enum TransactionType {
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  CHARGEBACK = 'CHARGEBACK',
  SUBSCRIPTION = 'SUBSCRIPTION',
  ONE_TIME = 'ONE_TIME',
  BONUS = 'BONUS',
  PENALTY = 'PENALTY',
  ADJUSTMENT = 'ADJUSTMENT',
}

// Subscription status
export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  SUSPENDED = 'SUSPENDED',
  PENDING = 'PENDING',
  TRIAL = 'TRIAL',
}

// Currency codes
export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  JPY = 'JPY',
  CNY = 'CNY',
  KRW = 'KRW',
  AUD = 'AUD',
  CAD = 'CAD',
  CHF = 'CHF',
  SEK = 'SEK',
}

// Base transaction interface
export interface Transaction {
  id: string
  userId: string
  userName: string
  userEmail: string
  type: TransactionType
  status: PaymentStatus
  amount: number
  currency: Currency
  fee: number
  netAmount: number
  paymentMethod: PaymentMethod
  paymentMethodDetails?: {
    cardLast4?: string
    brand?: string
    expiryMonth?: number
    expiryYear?: number
    bankName?: string
    accountLast4?: string
  }
  description: string
  reference: string
  externalId?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
  processedAt?: string
  refundedAt?: string
  refundAmount?: number
  failureReason?: string
}

// Subscription plan
export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  price: number
  currency: Currency
  interval: 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'LIFETIME'
  intervalCount: number
  trialDays: number
  features: string[]
  limits: Record<string, number>
  isActive: boolean
  isPopular: boolean
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// User subscription
export interface Subscription {
  id: string
  userId: string
  planId: string
  planName: string
  status: SubscriptionStatus
  currentPeriodStart: string
  currentPeriodEnd: string
  trialStart?: string
  trialEnd?: string
  cancelledAt?: string
  cancelReason?: string
  pausedAt?: string
  resumedAt?: string
  price: number
  currency: Currency
  paymentMethod: PaymentMethod
  nextBillingDate?: string
  lastPaymentDate?: string
  failedPaymentAttempts: number
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// Invoice
export interface Invoice {
  id: string
  number: string
  userId: string
  subscriptionId?: string
  status: 'DRAFT' | 'PENDING' | 'PAID' | 'OVERDUE' | 'CANCELLED' | 'VOID'
  subtotal: number
  taxAmount: number
  discountAmount: number
  total: number
  currency: Currency
  dueDate: string
  paidAt?: string
  items: InvoiceItem[]
  paymentAttempts: number
  nextPaymentAttempt?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// Invoice item
export interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  amount: number
  taxRate: number
  taxAmount: number
  metadata?: Record<string, any>
}

// Refund request
export interface RefundRequest {
  id: string
  transactionId: string
  userId: string
  amount: number
  currency: Currency
  reason: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PROCESSED' | 'FAILED'
  requestedBy: string
  requestedAt: string
  reviewedBy?: string
  reviewedAt?: string
  processedAt?: string
  notes?: string
  metadata?: Record<string, any>
}

// Financial statistics
export interface FinancialStatistics {
  revenue: {
    total: number
    recurring: number
    oneTime: number
    thisMonth: number
    lastMonth: number
    growth: number
  }
  transactions: {
    total: number
    successful: number
    failed: number
    refunded: number
    averageAmount: number
  }
  subscriptions: {
    active: number
    cancelled: number
    trial: number
    churnRate: number
    mrr: number // Monthly Recurring Revenue
    arr: number // Annual Recurring Revenue
  }
  customers: {
    total: number
    paying: number
    averageLifetimeValue: number
    averageRevenuePerUser: number
  }
  topPlans: Array<{
    planId: string
    planName: string
    subscribers: number
    revenue: number
  }>
  revenueByCountry: Array<{
    country: string
    revenue: number
    percentage: number
  }>
}

// Revenue report
export interface RevenueReport {
  period: {
    start: string
    end: string
    type: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
  }
  summary: {
    totalRevenue: number
    recurringRevenue: number
    oneTimeRevenue: number
    refunds: number
    netRevenue: number
    growth: number
  }
  breakdown: Array<{
    date: string
    revenue: number
    transactions: number
    newSubscriptions: number
    cancellations: number
  }>
  topProducts: Array<{
    productId: string
    productName: string
    revenue: number
    quantity: number
  }>
  paymentMethods: Array<{
    method: PaymentMethod
    revenue: number
    percentage: number
  }>
}

// Billing configuration
export interface BillingConfig {
  currency: Currency
  taxRate: number
  paymentMethods: PaymentMethod[]
  invoiceSettings: {
    prefix: string
    nextNumber: number
    dueDays: number
    reminderDays: number[]
  }
  subscriptionSettings: {
    trialDays: number
    gracePeriodDays: number
    maxFailedAttempts: number
    retryIntervalDays: number[]
  }
  refundPolicy: {
    autoApproveLimit: number
    requireApprovalAbove: number
    maxRefundDays: number
  }
}

// Payment gateway configuration
export interface PaymentGateway {
  id: string
  name: string
  type: 'STRIPE' | 'PAYPAL' | 'SQUARE' | 'BRAINTREE' | 'ADYEN' | 'CUSTOM'
  isActive: boolean
  isDefault: boolean
  supportedMethods: PaymentMethod[]
  supportedCurrencies: Currency[]
  configuration: Record<string, any>
  fees: {
    percentage: number
    fixed: number
    currency: Currency
  }
  credentials: {
    publicKey?: string
    secretKey: string // encrypted
    webhookSecret?: string
  }
  webhookUrl: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

// Financial alert
export interface FinancialAlert {
  id: string
  type: 'REVENUE_DROP' | 'FAILED_PAYMENTS' | 'CHURN_SPIKE' | 'REFUND_SPIKE' | 'CUSTOM'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  description: string
  threshold: number
  currentValue: number
  triggeredAt: string
  isActive: boolean
  recipients: string[]
  metadata?: Record<string, any>
}

// Tax configuration
export interface TaxConfig {
  id: string
  country: string
  region?: string
  taxType: 'VAT' | 'SALES_TAX' | 'GST' | 'OTHER'
  rate: number
  isDefault: boolean
  applicableProducts: string[]
  exemptions: Array<{
    type: 'CUSTOMER_TYPE' | 'PRODUCT_TYPE' | 'REGION'
    value: string
  }>
  effectiveFrom: string
  effectiveTo?: string
}

// Discount/Coupon
export interface Discount {
  id: string
  code: string
  name: string
  description: string
  type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_TRIAL'
  value: number
  currency?: Currency
  isActive: boolean
  maxUses?: number
  usedCount: number
  validFrom: string
  validTo?: string
  applicablePlans: string[]
  firstTimeOnly: boolean
  metadata?: Record<string, any>
  createdBy: string
  createdAt: string
}

// Payment export request
export interface PaymentExportRequest {
  dateFrom: string
  dateTo: string
  status?: PaymentStatus[]
  paymentMethods?: PaymentMethod[]
  currencies?: Currency[]
  format: 'CSV' | 'EXCEL' | 'PDF'
  includeRefunds: boolean
  includeMetadata: boolean
}

// Financial forecast
export interface FinancialForecast {
  period: {
    start: string
    end: string
  }
  model: 'LINEAR' | 'EXPONENTIAL' | 'SEASONAL'
  confidence: number
  predictions: Array<{
    date: string
    revenue: number
    lowerBound: number
    upperBound: number
  }>
  assumptions: Record<string, any>
  accuracy?: number
  generatedAt: string
} 