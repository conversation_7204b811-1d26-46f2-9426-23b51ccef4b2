/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage

/**
 * Profile screen showing user information and menu items.
 * Follows WeChat profile design patterns.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    onNavigateToSettings: () -> Unit,
    onNavigateToAlbum: () -> Unit,
    onNavigateToFavorites: () -> Unit,
    onNavigateToFiles: () -> Unit,
    onNavigateToWallet: () -> Unit,
    onNavigateToMembership: () -> Unit,
    onNavigateToCustomization: () -> Unit,
    onNavigateToFloatWindow: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFFF6B9D), // Pink gradient top
                        Color(0xFFFFE8F0), // Light pink gradient bottom
                        Color(0xFFF5F5F5)  // Light gray background
                    )
                )
            )
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Top header with status bar controls
            item {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 32.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Time and status indicators (placeholder)
                    Text(
                        text = "今天还没打卡呢",
                        color = Color.White,
                        fontSize = 14.sp
                    )
                    
                    Row {
                        Icon(
                            imageVector = Icons.Default.LocationOn,
                            contentDescription = "Location",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Icon(
                            imageVector = Icons.Default.QrCode,
                            contentDescription = "QR Code",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Add",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
            
            // Profile info section
            item {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Avatar
                    AsyncImage(
                        model = "https://via.placeholder.com/80x80", // Placeholder avatar
                        contentDescription = "Avatar",
                        modifier = Modifier
                            .size(80.dp)
                            .clip(CircleShape)
                            .background(Color.Gray),
                        contentScale = ContentScale.Crop
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    // User info
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "luckyluke",
                            color = Color.White,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "🍖 SVIP10 🔥 👑👑👑👑⭐ ...",
                                color = Color.White,
                                fontSize = 12.sp
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            // Member badge
                            Surface(
                                modifier = Modifier.size(20.dp),
                                color = Color(0xFFFFD700),
                                shape = CircleShape
                            ) {
                                Box(
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "会",
                                        color = Color.Black,
                                        fontSize = 10.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        Text(
                            text = "顺势而为，踏浪前行！",
                            color = Color.White,
                            fontSize = 14.sp
                        )
                    }
                    
                    // Arrow
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = "View Profile",
                        tint = Color.White
                    )
                }
            }
            
            // Menu items
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column {
                        ProfileMenuItem(
                            icon = Icons.Default.Photo,
                            title = "相册",
                            onClick = onNavigateToAlbum
                        )
                        
                        Divider(color = Color(0xFFE0E0E0))
                        
                        ProfileMenuItem(
                            icon = Icons.Default.Star,
                            title = "收藏",
                            onClick = onNavigateToFavorites
                        )
                        
                        Divider(color = Color(0xFFE0E0E0))
                        
                        ProfileMenuItem(
                            icon = Icons.Default.Folder,
                            title = "文件",
                            onClick = onNavigateToFiles
                        )
                        
                        Divider(color = Color(0xFFE0E0E0))
                        
                        ProfileMenuItem(
                            icon = Icons.Default.AccountBalance,
                            title = "钱包",
                            onClick = onNavigateToWallet
                        )
                    }
                }
            }
            
            // Membership section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    ProfileMenuItem(
                        icon = Icons.Default.Diamond,
                        title = "会员中心",
                        subtitle = "音乐会员免费领",
                        onClick = onNavigateToMembership,
                        hasSubtitle = true
                    )
                }
            }
            
            // Customization section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    ProfileMenuItem(
                        icon = Icons.Default.Palette,
                        title = "个性装扮",
                        onClick = onNavigateToCustomization
                    )
                }
            }
            
            // Float window section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    ProfileMenuItem(
                        icon = Icons.Default.OpenInNew,
                        title = "免流量",
                        subtitle = "跨时推广",
                        onClick = onNavigateToFloatWindow,
                        hasSubtitle = true
                    )
                }
            }
        }
        
        // Bottom navigation bar (Settings, Night mode, etc.)
        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateToSettings) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = "Settings",
                    tint = Color.Black
                )
            }
            
            IconButton(onClick = { /* Toggle night mode */ }) {
                Icon(
                    imageVector = Icons.Default.DarkMode,
                    contentDescription = "Night mode",
                    tint = Color.Black
                )
            }
            
            IconButton(onClick = { /* Open Jingzhou function */ }) {
                Text(
                    text = "荆州",
                    color = Color.Black,
                    fontSize = 12.sp
                )
            }
        }
    }
}

@Composable
private fun ProfileMenuItem(
    icon: ImageVector,
    title: String,
    subtitle: String? = null,
    onClick: () -> Unit,
    hasSubtitle: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color(0xFF4A90E2),
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                color = Color(0xFF333333),
                fontSize = 16.sp
            )
            
            if (hasSubtitle && subtitle != null) {
                Text(
                    text = subtitle,
                    color = Color(0xFF888888),
                    fontSize = 12.sp
                )
            }
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = null,
            tint = Color(0xFFBBBBBB),
            modifier = Modifier.size(20.dp)
        )
    }
} 