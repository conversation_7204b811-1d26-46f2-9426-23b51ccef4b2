// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package repository

import (
	"context"
	"encoding/json"
	"time"

	"github.com/sirupsen/logrus"

	"cina.club/services/analytics-service/internal/application/port"
	"cina.club/services/analytics-service/internal/domain/model"
)

// UserAnalyticsRepository 鐢ㄦ埛鍒嗘瀽浠撳偍瀹炵幇
type UserAnalyticsRepository struct {
	dw *DataWarehouse
	ch *ClickHouseRepository
}

// NewUserAnalyticsRepository 鍒涘缓鐢ㄦ埛鍒嗘瀽浠撳偍
func NewUserAnalyticsRepository(dw *DataWarehouse, ch *ClickHouseRepository) port.UserAnalyticsRepository {
	return &UserAnalyticsRepository{
		dw: dw,
		ch: ch,
	}
}

// GetUserProfileFeatures 鑾峰彇鐢ㄦ埛鐢诲儚鐗瑰緛
func (r *UserAnalyticsRepository) GetUserProfileFeatures(ctx context.Context, userID string) (*model.UserProfileFeatures, error) {
	query := `
		SELECT 
			user_id,
			registration_date,
			last_active_date,
			total_orders,
			total_spent,
			average_order_value,
			preferred_categories,
			activity_score,
			lifetime_value,
			churn_probability,
			recommendation_score,
			social_influence_score
		FROM ads_user_profile_features 
		WHERE user_id = ?
	`

	var features model.UserProfileFeatures
	var preferredCategoriesJSON string

	err := r.dw.db.WithContext(ctx).Raw(query, userID).Row().Scan(
		&features.UserID,
		&features.RegistrationDate,
		&features.LastActiveDate,
		&features.TotalOrders,
		&features.TotalSpent,
		&features.AverageOrderValue,
		&preferredCategoriesJSON,
		&features.ActivityScore,
		&features.LifetimeValue,
		&features.ChurnProbability,
		&features.RecommendationScore,
		&features.SocialInfluenceScore,
	)

	if err != nil {
		logrus.WithError(err).WithField("user_id", userID).Error("Failed to get user profile features")
		return nil, err
	}

	// 瑙ｆ瀽preferred_categories JSON
	if preferredCategoriesJSON != "" {
		err = json.Unmarshal([]byte(preferredCategoriesJSON), &features.PreferredCategories)
		if err != nil {
			logrus.WithError(err).Warn("Failed to parse preferred categories JSON")
		}
	}

	return &features, nil
}

// GetUserBehaviorAnalysis 鑾峰彇鐢ㄦ埛琛屼负鍒嗘瀽
func (r *UserAnalyticsRepository) GetUserBehaviorAnalysis(ctx context.Context, timeRange model.TimeRange, filters map[string]interface{}) (*model.UserBehaviorAnalysis, error) {
	analysis := &model.UserBehaviorAnalysis{
		TimeRange: timeRange,
	}

	// 鑾峰彇浼氳瘽鍒嗘瀽
	sessionQuery := `
		SELECT 
			COUNT(*) as total_sessions,
			AVG(session_duration) as avg_session_duration,
			AVG(bounce_rate) as bounce_rate,
			AVG(pages_per_session) as pages_per_session
		FROM ads_session_analytics 
		WHERE date >= ? AND date <= ?
	`

	var avgDurationSeconds int64
	err := r.dw.db.WithContext(ctx).Raw(sessionQuery,
		timeRange.StartDate.Format("2006-01-02"),
		timeRange.EndDate.Format("2006-01-02")).Row().Scan(
		&analysis.SessionAnalytics.TotalSessions,
		&avgDurationSeconds,
		&analysis.SessionAnalytics.BounceRate,
		&analysis.SessionAnalytics.PagesPerSession,
	)
	if err != nil {
		logrus.WithError(err).Error("Failed to get session analytics")
		return nil, err
	}
	analysis.SessionAnalytics.AverageSessionDuration = time.Duration(avgDurationSeconds) * time.Second

	// 鑾峰彇椤甸潰娴忚鍒嗘瀽
	pageViewQuery := `
		SELECT 
			SUM(total_page_views) as total_page_views,
			SUM(unique_page_views) as unique_page_views
		FROM ads_page_view_analytics 
		WHERE date >= ? AND date <= ?
	`
	err = r.dw.db.WithContext(ctx).Raw(pageViewQuery,
		timeRange.StartDate.Format("2006-01-02"),
		timeRange.EndDate.Format("2006-01-02")).Row().Scan(
		&analysis.PageViewAnalytics.TotalPageViews,
		&analysis.PageViewAnalytics.UniquePageViews,
	)
	if err != nil {
		logrus.WithError(err).Error("Failed to get page view analytics")
	}

	// 鑾峰彇鐑棬椤甸潰
	topPagesQuery := `
		SELECT 
			page_path,
			page_title,
			view_count,
			unique_views,
			average_time_seconds
		FROM ads_top_pages 
		WHERE date >= ? AND date <= ?
		ORDER BY view_count DESC 
		LIMIT 10
	`
	var topPages []struct {
		PagePath           string `json:"page_path"`
		PageTitle          string `json:"page_title"`
		ViewCount          int64  `json:"view_count"`
		UniqueViews        int64  `json:"unique_views"`
		AverageTimeSeconds int64  `json:"average_time_seconds"`
	}
	err = r.dw.db.WithContext(ctx).Raw(topPagesQuery,
		timeRange.StartDate.Format("2006-01-02"),
		timeRange.EndDate.Format("2006-01-02")).Scan(&topPages).Error

	if err != nil {
		logrus.WithError(err).Error("Failed to get top pages")
	} else {
		for _, page := range topPages {
			analysis.PageViewAnalytics.TopPages = append(analysis.PageViewAnalytics.TopPages, model.PageStats{
				PagePath:    page.PagePath,
				PageTitle:   page.PageTitle,
				ViewCount:   page.ViewCount,
				UniqueViews: page.UniqueViews,
				AverageTime: time.Duration(page.AverageTimeSeconds) * time.Second,
			})
		}
	}

	return analysis, nil
}

// GetCohortAnalysis 鑾峰彇闃熷垪鍒嗘瀽
func (r *UserAnalyticsRepository) GetCohortAnalysis(ctx context.Context, cohortType string, timeRange model.TimeRange) (*model.CohortAnalysis, error) {
	query := `
		SELECT 
			cohort_period,
			start_date,
			end_date,
			user_count,
			retention_rates
		FROM ads_cohort_analysis 
		WHERE cohort_type = ? 
		AND start_date >= ? AND end_date <= ?
		ORDER BY start_date
	`

	rows, err := r.dw.db.WithContext(ctx).Raw(query, cohortType,
		timeRange.StartDate.Format("2006-01-02"),
		timeRange.EndDate.Format("2006-01-02")).Rows()
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"cohort_type": cohortType,
			"time_range":  timeRange,
		}).Error("Failed to get cohort analysis")
		return nil, err
	}
	defer rows.Close()

	analysis := &model.CohortAnalysis{
		CohortType: cohortType,
	}

	for rows.Next() {
		var cohort model.CohortData
		var retentionRatesJSON string

		err := rows.Scan(
			&cohort.CohortPeriod,
			&cohort.StartDate,
			&cohort.EndDate,
			&cohort.UserCount,
			&retentionRatesJSON,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan cohort row")
			continue
		}

		// 瑙ｆ瀽retention_rates JSON
		if retentionRatesJSON != "" {
			err = json.Unmarshal([]byte(retentionRatesJSON), &cohort.RetentionRates)
			if err != nil {
				logrus.WithError(err).Warn("Failed to parse retention rates JSON")
			}
		}

		analysis.Cohorts = append(analysis.Cohorts, cohort)
	}

	return analysis, nil
}

// GetFunnelAnalysis 鑾峰彇婕忔枟鍒嗘瀽
func (r *UserAnalyticsRepository) GetFunnelAnalysis(ctx context.Context, funnelName string, timeRange model.TimeRange) (*model.FunnelAnalysis, error) {
	query := `
		SELECT 
			funnel_name,
			conversion_rate,
			step_name,
			step_order,
			user_count,
			dropoff_rate,
			conversion_to_next
		FROM ads_funnel_analysis 
		WHERE funnel_name = ? 
		AND date >= ? AND date <= ?
		ORDER BY step_order
	`

	rows, err := r.dw.db.WithContext(ctx).Raw(query, funnelName,
		timeRange.StartDate.Format("2006-01-02"),
		timeRange.EndDate.Format("2006-01-02")).Rows()
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"funnel_name": funnelName,
			"time_range":  timeRange,
		}).Error("Failed to get funnel analysis")
		return nil, err
	}
	defer rows.Close()

	analysis := &model.FunnelAnalysis{
		FunnelName: funnelName,
		TimeRange:  timeRange,
	}

	for rows.Next() {
		var step model.FunnelStep
		var actualFunnelName string

		err := rows.Scan(
			&actualFunnelName,
			&analysis.ConversionRate,
			&step.StepName,
			&step.StepOrder,
			&step.UserCount,
			&step.DropoffRate,
			&step.ConversionToNext,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan funnel step row")
			continue
		}

		analysis.Steps = append(analysis.Steps, step)
	}

	return analysis, nil
}

// GetUserSegmentation 鑾峰彇鐢ㄦ埛鍒嗙兢
func (r *UserAnalyticsRepository) GetUserSegmentation(ctx context.Context, segmentCriteria map[string]interface{}) ([]model.UserProfileFeatures, error) {
	// 鏋勫缓鍔ㄦ€佹煡璇紙绠€鍖栫増鏈紝瀹為檯闇€瑕佹洿澶嶆潅鐨勬煡璇㈡瀯寤哄櫒锛?
	baseQuery := `
		SELECT 
			user_id,
			registration_date,
			last_active_date,
			total_orders,
			total_spent,
			average_order_value,
			preferred_categories,
			activity_score,
			lifetime_value,
			churn_probability,
			recommendation_score,
			social_influence_score
		FROM ads_user_profile_features 
		WHERE 1=1
	`

	// 娣诲姞鍒嗙兢鏉′欢
	var conditions []string
	var args []interface{}

	if minOrders, ok := segmentCriteria["min_orders"]; ok {
		conditions = append(conditions, "total_orders >= ?")
		args = append(args, minOrders)
	}

	if minSpent, ok := segmentCriteria["min_spent"]; ok {
		conditions = append(conditions, "total_spent >= ?")
		args = append(args, minSpent)
	}

	if activityScore, ok := segmentCriteria["min_activity_score"]; ok {
		conditions = append(conditions, "activity_score >= ?")
		args = append(args, activityScore)
	}

	// 鏋勫缓瀹屾暣鏌ヨ
	query := baseQuery
	for _, condition := range conditions {
		query += " AND " + condition
	}
	query += " LIMIT 1000"

	rows, err := r.dw.db.WithContext(ctx).Raw(query, args...).Rows()
	if err != nil {
		logrus.WithError(err).WithField("criteria", segmentCriteria).Error("Failed to get user segmentation")
		return nil, err
	}
	defer rows.Close()

	var segments []model.UserProfileFeatures
	for rows.Next() {
		var features model.UserProfileFeatures
		var preferredCategoriesJSON string

		err := rows.Scan(
			&features.UserID,
			&features.RegistrationDate,
			&features.LastActiveDate,
			&features.TotalOrders,
			&features.TotalSpent,
			&features.AverageOrderValue,
			&preferredCategoriesJSON,
			&features.ActivityScore,
			&features.LifetimeValue,
			&features.ChurnProbability,
			&features.RecommendationScore,
			&features.SocialInfluenceScore,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to scan user segmentation row")
			continue
		}

		// 瑙ｆ瀽preferred_categories JSON
		if preferredCategoriesJSON != "" {
			err = json.Unmarshal([]byte(preferredCategoriesJSON), &features.PreferredCategories)
			if err != nil {
				logrus.WithError(err).Warn("Failed to parse preferred categories JSON")
			}
		}

		segments = append(segments, features)
	}

	return segments, nil
}
