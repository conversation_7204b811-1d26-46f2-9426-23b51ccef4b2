/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package port

import (
	"testing"
)

func TestErrors(t *testing.T) {
	// Test that errors are properly defined
	testCases := []struct {
		name string
		err  error
	}{
		{"ErrToolNotFound", ErrToolNotFound},
		{"ErrInvalidInput", ErrInvalidInput},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.err == nil {
				t.Errorf("Error %s should not be nil", tc.name)
			}

			if tc.err.Error() == "" {
				t.<PERSON><PERSON>rf("Error %s should have a message", tc.name)
			}
		})
	}
}

func TestErrorComparison(t *testing.T) {
	// Test that errors can be compared correctly
	if ErrToolNotFound == ErrInvalidInput {
		t.Error("Different errors should not be equal")
	}

	// Test error messages
	if ErrToolNotFound.Error() != "tool not found" {
		t.<PERSON><PERSON><PERSON>("Expected 'tool not found', got '%s'", ErrToolNotFound.Error())
	}

	if ErrInvalidInput.Error() != "invalid input" {
		t.Errorf("Expected 'invalid input', got '%s'", ErrInvalidInput.Error())
	}
}
