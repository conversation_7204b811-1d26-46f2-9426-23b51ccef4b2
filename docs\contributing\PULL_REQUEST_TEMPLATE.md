# Pull Request 模板

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

## 变更描述

### 变更类型
<!-- 在适当的选项前打勾 [x] -->
- [ ] 🐛 Bug修复
- [ ] ✨ 新功能
- [ ] 📝 文档更新
- [ ] 🎨 代码风格/格式调整
- [ ] ♻️ 代码重构
- [ ] ⚡ 性能优化
- [ ] ✅ 测试相关
- [ ] 🔧 构建工具或辅助工具变动
- [ ] 🔒 安全性改进

### 变更详情
<!-- 详细描述您的变更内容 -->


### 相关Issue
<!-- 如果此PR解决了某个Issue，请在下方提及 -->
Closes #(issue number)

## 测试说明

### 测试范围
<!-- 描述您如何测试了这些变更 -->
- [ ] 单元测试
- [ ] 集成测试
- [ ] 手动测试
- [ ] 端到端测试

### 测试步骤
<!-- 详细描述测试步骤，便于审查者验证 -->
1. 
2. 
3. 

### 测试结果
<!-- 描述测试结果和任何相关的输出 -->


## 检查清单

### 代码质量
- [ ] 代码遵循项目的代码规范
- [ ] 代码已通过所有测试
- [ ] 代码已通过lint检查
- [ ] 没有编译警告或错误
- [ ] 代码具有适当的注释和文档

### 功能完整性
- [ ] 新功能已添加相应的测试
- [ ] Bug修复已添加回归测试
- [ ] 所有边界情况都已考虑
- [ ] 错误处理已正确实现

### 安全性
- [ ] 代码没有引入安全漏洞
- [ ] 敏感信息没有暴露在代码中
- [ ] 输入验证已正确实现
- [ ] 认证和授权逻辑正确

### 性能
- [ ] 变更不会显著影响性能
- [ ] 数据库查询已优化
- [ ] 内存使用合理
- [ ] 并发安全性已考虑

### 文档
- [ ] README已更新（如需要）
- [ ] API文档已更新（如需要）
- [ ] 变更日志已更新
- [ ] 内联代码注释充足

### 向后兼容性
- [ ] 变更保持向后兼容
- [ ] 如有破坏性变更，已在描述中说明
- [ ] 迁移指南已提供（如需要）

### 数据库
- [ ] 数据库迁移已测试
- [ ] 迁移可以安全回滚
- [ ] 大表变更已考虑影响

## 部署注意事项

### 部署前准备
<!-- 列出部署此PR前需要完成的任何步骤 -->
- [ ] 无特殊要求
- [ ] 需要数据库迁移
- [ ] 需要环境变量更新
- [ ] 需要外部服务配置
- [ ] 其他：

### 部署顺序
<!-- 如果有特定的部署顺序要求，请说明 -->


### 回滚计划
<!-- 如果需要回滚，应该如何操作 -->


## 截图/录屏
<!-- 如果涉及UI变更，请提供相关截图或录屏 -->


## 额外信息

### 相关链接
<!-- 提供任何相关的设计文档、讨论链接等 -->
- 设计文档：
- 相关讨论：
- 参考资料：

### 后续工作
<!-- 列出此PR之后需要完成的相关工作 -->
- [ ] 
- [ ] 

### 风险评估
<!-- 评估此变更可能带来的风险 -->
- **风险等级**: 🟢 低 / 🟡 中 / 🔴 高
- **风险描述**: 

## 审查者注意事项
<!-- 为审查者提供特别需要关注的点 -->


## 自我审查
<!-- 在提交PR前，请确认您已经完成以下自我审查 -->
- [ ] 我已经仔细审查了自己的代码
- [ ] 我已经测试了所有的变更
- [ ] 我已经检查了代码风格和格式
- [ ] 我已经更新了相关文档
- [ ] 我已经考虑了这些变更对其他部分的影响

---

**提交此PR即表示您同意[贡献协议](CONTRIBUTING.md)和[行为准则](CODE_OF_CONDUCT.md)** 