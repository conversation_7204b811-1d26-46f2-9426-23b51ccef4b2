{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/api/*": ["src/api/*"], "@/components/*": ["src/components/*"], "@/hooks/*": ["src/hooks/*"], "@/lib/*": ["src/lib/*"], "@/pages/*": ["src/pages/*"], "@/services/*": ["src/services/*"], "@/store/*": ["src/store/*"], "@/types/*": ["src/types/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}