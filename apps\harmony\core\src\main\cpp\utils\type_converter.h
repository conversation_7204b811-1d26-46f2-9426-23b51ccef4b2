/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

#ifndef TYPE_CONVERTER_H
#define TYPE_CONVERTER_H

#include <napi/native_api.h>
#include "../include/core_go.h"
#include <string>

/**
 * NAPI与Go类型之间的转换工具类
 */
class TypeConverter {
public:
    /**
     * 将NAPI ArrayBuffer转换为Go Slice
     * @param env NAPI环境
     * @param napiValue NAPI ArrayBuffer值
     * @param goSlice 输出的Go Slice
     * @return 转换是否成功
     */
    static bool ArrayBufferToGoSlice(napi_env env, napi_value napiValue, GoSlice* goSlice);

    /**
     * 将Go Slice转换为NAPI ArrayBuffer
     * @param env NAPI环境
     * @param goSlice Go Slice
     * @param napiValue 输出的NAPI ArrayBuffer值
     * @return 转换是否成功
     */
    static bool GoSliceToArrayBuffer(napi_env env, const GoSlice& goSlice, napi_value* napiValue);

    /**
     * 将NAPI String转换为Go String
     * @param env NAPI环境
     * @param napiValue NAPI String值
     * @param goString 输出的Go String
     * @return 转换是否成功
     */
    static bool StringToGoString(napi_env env, napi_value napiValue, GoString* goString);

    /**
     * 将Go String转换为NAPI String
     * @param env NAPI环境
     * @param goString Go String
     * @param napiValue 输出的NAPI String值
     * @return 转换是否成功
     */
    static bool GoStringToString(napi_env env, const GoString& goString, napi_value* napiValue);

    /**
     * 将NAPI Number转换为C++ int
     * @param env NAPI环境
     * @param napiValue NAPI Number值
     * @param result 输出的int值
     * @return 转换是否成功
     */
    static bool NumberToInt(napi_env env, napi_value napiValue, int* result);

    /**
     * 将C++ int转换为NAPI Number
     * @param env NAPI环境
     * @param value int值
     * @param napiValue 输出的NAPI Number值
     * @return 转换是否成功
     */
    static bool IntToNumber(napi_env env, int value, napi_value* napiValue);

    /**
     * 检查NAPI值的类型
     * @param env NAPI环境
     * @param napiValue NAPI值
     * @param expectedType 期望的类型
     * @return 类型是否匹配
     */
    static bool CheckType(napi_env env, napi_value napiValue, napi_valuetype expectedType);

    /**
     * 创建错误对象
     * @param env NAPI环境
     * @param message 错误消息
     * @param error 输出的错误对象
     * @return 创建是否成功
     */
    static bool CreateError(napi_env env, const std::string& message, napi_value* error);

private:
    // 私有构造函数，工具类不需要实例化
    TypeConverter() = default;
    
    // 静态缓冲区，用于存储临时字符串数据
    static thread_local std::string tempStringBuffer;
};

#endif // TYPE_CONVERTER_H 