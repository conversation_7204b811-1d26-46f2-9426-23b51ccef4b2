/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 17:35:00
 * Modified: 2025-01-23 17:35:00
 */

import React from 'react';
import { Tag } from 'antd';
import { UserStatus, UserRole } from '@/types/user';

// Status color mapping
const statusColors: Record<UserStatus, string> = {
  [UserStatus.ACTIVE]: 'green',
  [UserStatus.INACTIVE]: 'orange',
  [UserStatus.SUSPENDED]: 'red',
  [UserStatus.PENDING]: 'blue',
  [UserStatus.DELETED]: 'gray',
};

// Role color mapping
const roleColors: Record<UserRole, string> = {
  [UserRole.SUPER_ADMIN]: 'purple',
  [UserRole.ADMIN]: 'blue',
  [UserRole.OPERATIONS]: 'cyan',
  [UserRole.CUSTOMER_SERVICE]: 'green',
  [UserRole.CONTENT_MODERATOR]: 'orange',
  [UserRole.ANALYST]: 'geekblue',
  [UserRole.VIEWER]: 'gray',
};

interface StatusTagProps {
  status: UserStatus;
}

export const StatusTag: React.FC<StatusTagProps> = React.memo(({ status }) => {
  return <Tag color={statusColors[status]}>{status}</Tag>;
});

interface RoleTagProps {
  role: UserRole;
}

export const RoleTag: React.FC<RoleTagProps> = React.memo(({ role }) => {
  return <Tag color={roleColors[role]} style={{ fontSize: 11 }}>{role}</Tag>;
});

StatusTag.displayName = 'StatusTag';
RoleTag.displayName = 'RoleTag'; 