好的，遵照您的指示。我将为您生成一份专门针对 **`news-crawler-service` (新闻采集服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**，并特别强调**使用Go语言作为主要实现语言**的技术选型和考量。

虽然Python在Web抓取生态上更传统，但使用Go完全可以构建一个高性能、高并发、易于维护的生产级爬虫系统，并且这更符合我们平台的“Go-Centric”原则。这份SRS将围绕Go的技术特性进行设计。

---
### CINA.CLUB - `news-crawler-service` 需求规格说明书 (Go版)

**版本: 1.1 (Go-Centric生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [数据平台/内容采集团队负责人]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议 (Go-Centric)](#8-技术约束与选型建议-go-centric)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了支撑CINA.CLUB的“7x24实时快讯”功能，平台需要一个强大、稳定、可扩展的**数据采集系统**，能从全球范围内多样化的信息源中抓取原始新闻数据。`news-crawler-service` 的目的在于构建这样一个**专业的、分布式的、高并发的**数据采集服务。它负责处理与所有外部信源的交互，并将抓取到的异构数据，标准化后注入到平台的内部事件总线中，为下游的`fast-news-service`提供源源不断的原始数据。

#### 1.2. 服务范围
本服务 **负责**:
*   **信源管理**: 动态地管理和配置需要采集的目标信源列表及其参数。
*   **多协议采集**:
    *   **轮询(Polling)**: 定期抓取**RSS/Atom Feeds**和**静态网页(HTML)**。
    *   **API对接**: 调用外部新闻提供商的**RESTful API**。
*   **内容解析**: 解析不同格式（XML, HTML, JSON）的原始数据，提取核心信息（标题、正文、发布时间、来源等）。
*   **数据标准化**: 将提取出的异构数据，转换为平台统一的`RawNewsEvent` Protobuf格式。
*   **事件发布**: 将标准化的`RawNewsEvent`可靠地发布到Kafka的`raw-news-stream` Topic。
*   **调度与执行**: 管理和调度所有采集任务的执行（如抓取频率）。
*   **监控与告警**: 监控每个信源的抓取成功率和数据质量，并在出现异常时发送告警。

本服务 **不负责**:
*   **新闻内容的深度处理**: 如去重、评估、翻译等，这是`fast-news-service`的职责。
*   **JavaScript渲染页面的抓取**: 对于需要JS渲染的复杂网页，本服务会通过**调用外部的、专门的Headless Browser服务**（如Browserless.io,或自建的`go-rod`服务）来获取渲染后的HTML，而不是在自身进程中运行浏览器。
*   **存储新闻内容**: 本服务是一个无状态的管道，抓取到的数据处理后立即发布，不进行持久化。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内容运营/数据团队**: (主要管理用户) 通过管理后台或API配置和管理信源。
*   **Kafka**: (被本服务写入) 是本服务的主要输出目标。
*   **`fast-news-service` (通过Kafka)**: 本服务的唯一数据消费者。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`news-crawler-service` 是平台实时信息流的“**数据触角**”和“**第一道关口**”。它位于整个平台的边缘，专门负责与外部的、不可靠的、格式各异的数据源打交道。通过将数据采集的复杂性和“脏活累活”集中在这个服务中，它极大地简化了下游核心业务服务（如`fast-news-service`）的逻辑，使其可以专注于处理干净、标准化的内部数据流。

#### 2.2. 主要功能概述
*   可配置的、支持多信源、多协议的分布式采集框架。
*   将异构数据源标准化为统一的内部事件。
*   基于Go并发模型的高性能、高可靠的任务调度与执行。
*   全面的信源健康状况与数据质量监控。

---

### 3. 核心流程图

#### 3.1. 一个RSS信源的定时抓取流程 (Go Concurrency Model)

```mermaid
sequenceDiagram
    participant Scheduler as "Cron Scheduler (in main process)"
    participant WorkerPool as "Goroutine Worker Pool"
    participant DB as "PostgreSQL (Source Config)"
    participant ExternalRSS as "External RSS Feed"
    participant Kafka
    
    Scheduler->>Scheduler: 1. Ticker fires (e.g., every minute)
    Scheduler->>DB: 2. Get all sources scheduled for this minute
    
    loop For each source
        Scheduler->>WorkerPool: 3. Dispatch CrawlJob to a free goroutine
    end

    WorkerPool->>ExternalRSS: 4. HTTP GET request to RSS feed URL
    ExternalRSS-->>WorkerPool: (XML content)
    
    WorkerPool->>WorkerPool: 5. Parse XML, extract items
    
    loop For each new item
        WorkerPool->>DB: 6. Check if this item's GUID has been processed
        alt Is a new item
            WorkerPool->>WorkerPool: 7a. Normalize to RawNews protobuf message
            WorkerPool->>Kafka: 8a. Publish RawNewsEvent
            WorkerPool->>DB: 9a. Record item's GUID as processed
        else Is an old item
            WorkerPool->>WorkerPool: 7b. Skip
        end
    end
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 信源管理
*   **FR4.1.1 (动态配置)**: 系统必须支持管理员通过API或后台界面，动态地CRUD**信源(Source)**配置。
*   **FR4.1.2 (信源参数)**: 每个信源配置必须包含：
    *   `name`, `type` (`RSS`, `API`, `WEBPAGE`), `url`.
    *   `crawl_interval_seconds`: 轮询间隔。
    *   `status`: `ACTIVE`, `INACTIVE`.
    *   `parser_config`: 一个JSONB字段，用于存储特定于该信源的解析规则（如HTML的CSS选择器、API的JSON路径）。
    *   `auth_config`: 存储访问该信源所需的认证信息（如API Key），**必须加密存储**。

#### 4.2. 采集与解析
*   **FR4.2.1 (RSS/Atom采集器)**: 必须能处理标准的RSS 2.0和Atom格式。
*   **FR4.2.2 (API采集器)**: 必须能根据`parser_config`，向指定的API端点发起HTTP请求，并处理JSON响应。
*   **FR4.2.3 (网页采集器)**:
    *   **静态HTML**: 必须能抓取静态HTML页面，并根据`parser_config`中定义的CSS选择器，提取出标题和正文。
    *   **动态JS页面**: 对于需要JS渲染的页面，**必须**调用一个外部的、专门的Headless Browser服务（如Browserless.io），获取其渲染后的HTML，然后再进行解析。
*   **FR4.2.4 (内容标准化)**: 所有从不同来源采集的数据，在发布前都必须被转换为统一的`RawNewsEvent` Protobuf消息。

#### 4.3. 任务调度与执行
*   **FR4.3.1 (内部调度器)**: 服务内部必须有一个基于时间轮或高精度Ticker的调度器，能根据每个信源的`crawl_interval_seconds`，在准确的时间点触发抓取任务。
*   **FR4.3.2 (并发Worker池)**:
    *   必须实现一个**固定大小的goroutine池**来执行实际的抓取任务。
    *   调度器将到期的任务发送到一个带缓冲的channel中，由Worker goroutine消费。这可以控制对外部世界的并发请求数，避免因任务过多而耗尽系统资源。
*   **FR4.3.3 (增量采集)**: 必须记录每个条目的唯一标识符（如URL或GUID的哈希值），以避免将同一条新闻重复发布到Kafka。此检查应在数据库层面完成，以保证分布式环境下的正确性。

#### 4.4. 可靠性与监控
*   **FR4.4.1 (错误处理)**: 对外部信源的任何网络或解析错误，都必须被捕获、记录，并触发告警。
*   **FR4.4.2 (重试机制)**: 对可恢复的网络错误，必须有带指数退避的重试策略。
*   **FR4.4.3 (信源健康监控)**: 系统必须持续监控每个信源的抓取成功率。如果一个信源连续多次抓取失败，应自动将其状态降级为`UNHEALTHY`，并通知运营人员。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 输出接口: 消息队列事件契约
*   **出站Topic**: `raw-news-stream`
*   **Event**: `RawNewsEvent` (`core/api/proto/v1/news_events.proto`)

#### 5.2. 管理后台API接口
*   **版本**: `/api/v1/crawlers`
*   **核心端点**:
    *   `GET /sources`: 获取所有信源配置列表。
    *   `POST /sources`: 创建新的信源配置。
    *   `PUT /sources/{id}`: 更新信源配置。
    *   `GET /sources/{id}/status`: 查看单个信源的健康状况和最新抓取日志。

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`news_sources`**:
    *   `id (PK)`, `name`, `type`, `url`, `crawl_interval_seconds`, `status`, `last_crawl_at`, `health_status`.
    *   `parser_config (JSONB)`
    *   `encrypted_auth_config (BYTEA)`
*   **`processed_item_hashes`**:
    *   `item_hash (VARCHAR(64), PK)`: 文章URL或GUID的SHA256哈希值。
    *   `source_id (FK)`
    *   `created_at (TIMESTAMPTZ, INDEX)`: 用于定期清理。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与吞吐量)**:
    *   系统必须能支持管理数千个信源配置。
    *   Worker池必须能支持每秒数百次的并发抓取（取决于网络和目标服务器响应）。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.9%。本服务的故障将导致快讯信息流中断。
    *   **数据不错漏**: 必须保证每个信源的每条新内容都被抓取到一次且仅一次。
*   **NFR7.3 (可扩展性)**: 服务可以水平扩展。多个实例通过**数据库或分布式锁**来协调任务分配，避免重复调度。
*   **NFR7.4 (礼貌性)**:
    *   必须遵守目标网站的`robots.txt`协议。
    *   必须在HTTP请求头中设置一个明确的`User-Agent`。
    *   **必须**实现一个**基于域名的全局速率限制器**（如使用`golang.org/x/time/rate`），防止对同一主机名的请求过于频繁。

---

### 8. 技术约束与选型建议 (Go-Centric)
*   **语言**: **Go**。其强大的并发模型（goroutine, channel）和高效的网络库非常适合构建高并发的I/O密集型采集服务。
*   **HTTP客户端**: Go标准库`net/http`，或功能更强大的`fasthttp`。
*   **HTML解析**: `goquery` (类似jQuery的API) 或 `golang.org/x/net/html` (更底层)。
*   **RSS/Atom解析**: `github.com/mmcdole/gofeed`。
*   **调度器**: 可以使用`github.com/robfig/cron`或`gocron`库来实现服务内的定时调度。
*   **并发控制**: 使用带缓冲的channel作为任务队列，结合`sync.WaitGroup`来管理goroutine池。

---
这份SRS为`news-crawler-service`的设计和实现提供了坚实、全面的指导。通过采用**Go语言和其强大的并发模型**，结合**清晰的、可配置的信源管理**，CINA.CLUB平台能够构建一个高性能、高可靠、且与整体技术栈保持一致的数据采集前端。