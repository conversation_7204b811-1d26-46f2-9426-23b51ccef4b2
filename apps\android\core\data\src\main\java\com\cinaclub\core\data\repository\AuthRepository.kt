/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.core.data.repository

import com.cinaclub.core.common.result.Result
import com.cinaclub.core.domain.model.User
import com.cinaclub.core.domain.repository.IAuthRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository implementation for authentication operations.
 * Manages user authentication, token storage, and session management.
 */
@Singleton
class AuthRepository @Inject constructor(
    private val authApiService: AuthApiService,
    private val tokenStorage: TokenStorage,
    private val userPreferences: UserPreferences,
    private val goBridge: GoBridge
) : IAuthRepository {

    override suspend fun login(email: String, password: String): Result<User> {
        return try {
            // Hash password using Go bridge for consistent hashing
            val hashedPassword = goBridge.hashPassword(password)
            
            // Call authentication API
            val response = authApiService.login(email, hashedPassword)
            
            if (response.isSuccessful) {
                val authResponse = response.body()!!
                
                // Store tokens securely
                tokenStorage.saveAccessToken(authResponse.accessToken)
                tokenStorage.saveRefreshToken(authResponse.refreshToken)
                
                // Store user information
                userPreferences.saveUser(authResponse.user)
                
                Result.Success(authResponse.user)
            } else {
                Result.Error(Exception("Login failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun register(
        email: String,
        password: String,
        firstName: String,
        lastName: String
    ): Result<User> {
        return try {
            // Validate password strength
            if (!isPasswordStrong(password)) {
                return Result.Error(Exception("Password does not meet security requirements"))
            }
            
            // Hash password using Go bridge
            val hashedPassword = goBridge.hashPassword(password)
            
            // Generate encryption keys for the user
            val keyPair = goBridge.generateUserKeyPair()
            
            // Call registration API
            val response = authApiService.register(
                email = email,
                hashedPassword = hashedPassword,
                firstName = firstName,
                lastName = lastName,
                publicKey = keyPair.publicKey
            )
            
            if (response.isSuccessful) {
                val authResponse = response.body()!!
                
                // Store tokens and keys securely
                tokenStorage.saveAccessToken(authResponse.accessToken)
                tokenStorage.saveRefreshToken(authResponse.refreshToken)
                goBridge.storePrivateKey(keyPair.privateKey, password)
                
                // Store user information
                userPreferences.saveUser(authResponse.user)
                
                Result.Success(authResponse.user)
            } else {
                Result.Error(Exception("Registration failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun logout(): Result<Unit> {
        return try {
            // Call logout API to invalidate tokens
            val accessToken = tokenStorage.getAccessToken()
            if (!accessToken.isNullOrEmpty()) {
                authApiService.logout(accessToken)
            }
            
            // Clear all stored data
            tokenStorage.clearTokens()
            userPreferences.clearUser()
            goBridge.clearSession()
            
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun refreshToken(): Result<String> {
        return try {
            val refreshToken = tokenStorage.getRefreshToken()
                ?: return Result.Error(Exception("No refresh token available"))
            
            val response = authApiService.refreshToken(refreshToken)
            
            if (response.isSuccessful) {
                val tokenResponse = response.body()!!
                tokenStorage.saveAccessToken(tokenResponse.accessToken)
                
                // Update refresh token if provided
                tokenResponse.refreshToken?.let { newRefreshToken ->
                    tokenStorage.saveRefreshToken(newRefreshToken)
                }
                
                Result.Success(tokenResponse.accessToken)
            } else {
                // Refresh failed - user needs to login again
                logout()
                Result.Error(Exception("Token refresh failed"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override fun getCurrentUser(): Flow<User?> = flow {
        emit(userPreferences.getUser())
    }

    override suspend fun updateProfile(
        firstName: String?,
        lastName: String?,
        avatarUrl: String?
    ): Result<User> {
        return try {
            val response = authApiService.updateProfile(
                firstName = firstName,
                lastName = lastName,
                avatarUrl = avatarUrl
            )
            
            if (response.isSuccessful) {
                val updatedUser = response.body()!!
                userPreferences.saveUser(updatedUser)
                Result.Success(updatedUser)
            } else {
                Result.Error(Exception("Profile update failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun changePassword(
        currentPassword: String,
        newPassword: String
    ): Result<Unit> {
        return try {
            // Validate new password strength
            if (!isPasswordStrong(newPassword)) {
                return Result.Error(Exception("New password does not meet security requirements"))
            }
            
            // Hash passwords using Go bridge
            val currentHashedPassword = goBridge.hashPassword(currentPassword)
            val newHashedPassword = goBridge.hashPassword(newPassword)
            
            val response = authApiService.changePassword(
                currentPassword = currentHashedPassword,
                newPassword = newHashedPassword
            )
            
            if (response.isSuccessful) {
                // Re-encrypt private key with new password
                goBridge.reencryptPrivateKey(currentPassword, newPassword)
                Result.Success(Unit)
            } else {
                Result.Error(Exception("Password change failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun forgotPassword(email: String): Result<Unit> {
        return try {
            val response = authApiService.forgotPassword(email)
            
            if (response.isSuccessful) {
                Result.Success(Unit)
            } else {
                Result.Error(Exception("Password reset request failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun resetPassword(
        token: String,
        newPassword: String
    ): Result<Unit> {
        return try {
            // Validate password strength
            if (!isPasswordStrong(newPassword)) {
                return Result.Error(Exception("Password does not meet security requirements"))
            }
            
            // Hash password using Go bridge
            val hashedPassword = goBridge.hashPassword(newPassword)
            
            val response = authApiService.resetPassword(
                token = token,
                newPassword = hashedPassword
            )
            
            if (response.isSuccessful) {
                Result.Success(Unit)
            } else {
                Result.Error(Exception("Password reset failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun verifyEmail(token: String): Result<Unit> {
        return try {
            val response = authApiService.verifyEmail(token)
            
            if (response.isSuccessful) {
                // Update user verification status
                val currentUser = userPreferences.getUser()
                currentUser?.let { user ->
                    val updatedUser = user.copy(isEmailVerified = true)
                    userPreferences.saveUser(updatedUser)
                }
                Result.Success(Unit)
            } else {
                Result.Error(Exception("Email verification failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    override suspend fun resendVerificationEmail(): Result<Unit> {
        return try {
            val response = authApiService.resendVerificationEmail()
            
            if (response.isSuccessful) {
                Result.Success(Unit)
            } else {
                Result.Error(Exception("Failed to resend verification email: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.Error(e)
        }
    }

    private fun isPasswordStrong(password: String): Boolean {
        // Password must be at least 8 characters long
        if (password.length < 8) return false
        
        // Must contain at least one uppercase letter
        if (!password.any { it.isUpperCase() }) return false
        
        // Must contain at least one lowercase letter
        if (!password.any { it.isLowerCase() }) return false
        
        // Must contain at least one digit
        if (!password.any { it.isDigit() }) return false
        
        // Must contain at least one special character
        val specialChars = "!@#$%^&*(),.?\":{}|<>"
        if (!password.any { it in specialChars }) return false
        
        return true
    }
}

// Data classes and interfaces would be defined in separate files
data class AuthResponse(
    val user: User,
    val accessToken: String,
    val refreshToken: String
)

data class TokenResponse(
    val accessToken: String,
    val refreshToken: String?
)

data class KeyPair(
    val publicKey: String,
    val privateKey: String
)

interface AuthApiService {
    suspend fun login(email: String, password: String): Response<AuthResponse>
    suspend fun register(email: String, hashedPassword: String, firstName: String, lastName: String, publicKey: String): Response<AuthResponse>
    suspend fun logout(accessToken: String): Response<Unit>
    suspend fun refreshToken(refreshToken: String): Response<TokenResponse>
    suspend fun updateProfile(firstName: String?, lastName: String?, avatarUrl: String?): Response<User>
    suspend fun changePassword(currentPassword: String, newPassword: String): Response<Unit>
    suspend fun forgotPassword(email: String): Response<Unit>
    suspend fun resetPassword(token: String, newPassword: String): Response<Unit>
    suspend fun verifyEmail(token: String): Response<Unit>
    suspend fun resendVerificationEmail(): Response<Unit>
}

interface TokenStorage {
    suspend fun saveAccessToken(token: String)
    suspend fun saveRefreshToken(token: String)
    suspend fun getAccessToken(): String?
    suspend fun getRefreshToken(): String?
    suspend fun clearTokens()
}

interface UserPreferences {
    suspend fun saveUser(user: User)
    suspend fun getUser(): User?
    suspend fun clearUser()
}

interface GoBridge {
    suspend fun hashPassword(password: String): String
    suspend fun generateUserKeyPair(): KeyPair
    suspend fun storePrivateKey(privateKey: String, password: String)
    suspend fun reencryptPrivateKey(oldPassword: String, newPassword: String)
    suspend fun clearSession()
}

// Placeholder for HTTP Response
interface Response<T> {
    val body: () -> T?
    val isSuccessful: Boolean
    val message: () -> String
} 