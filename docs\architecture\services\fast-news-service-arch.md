好的，遵照您的指示。我将为您生成一份专门针对 **`fast-news-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`fast-news-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**事件驱动管道的实现、智能去重与重要性评估的领域逻辑、与AI服务的异步协同，以及作为一个高吞吐量实时处理系统的性能和可靠性保障**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `fast-news-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `fast-news-service-srs.md` (v1.0)
**核心架构**: 事件驱动的ETL管道 (Extract-Transform-Load)

## 1. 概述

`fast-news-service` 是CINA.CLUB实时信息流的“**中央厨房**”和“**质量控制中心**”。它是一个纯粹的**后台数据处理服务**，其架构设计的核心目标是构建一个低延迟、高吞吐、高可靠的ETL（提取-转换-加载）管道。

*   **Extract**: 从Kafka的`raw-news-stream` Topic中提取原始新闻事件。
*   **Transform**: 执行一系列的转换操作，包括清洗、去重、重要性评估和AI内容增强。
*   **Load**: 将处理好的快讯持久化到数据库，并发布一个最终的、可供全平台消费的`ProcessedNewsEvent`。

本架构设计的核心在于将复杂的处理流程拆分为一系列**独立的、可测试的、可组合的步骤**，并通过事件驱动和异步化来保证系统的整体性能和韧性。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (内部处理管道)

```mermaid
graph TD
    subgraph "输入 (Kafka)"
        A[raw-news-stream Topic]
    end

    subgraph "FastNewsService"
        style FastNewsService fill:#e0f7fa
        B[Kafka Consumer<br/><em>adapter/event</em>]
        C[Application Service<br/><em>application/service</em>]
        
        subgraph "Domain Services (The 'Transform' Steps)"
            D1[CleanerService]
            D2[DeduplicationService]
            D3[ScoringService]
            D4[AIEnrichmentService]
        end

        E[Repository<br/><em>adapter/repository</em>]
        F[Kafka Producer<br/><em>adapter/event</em>]
    end

    subgraph "输出 (Kafka)"
        G[fast-news-events Topic]
    end
    
    subgraph "外部依赖"
        Redis[(Redis for Dedupe)]
        AI[ai-assistant-service]
        DB[(PostgreSQL)]
    end

    A --> B
    B -- "调用" --> C
    
    C -- "Step 1: Clean" --> D1
    C -- "Step 2: Deduplicate" --> D2
    D2 -- "Uses" --> Redis
    
    C -- "Step 3: Score" --> D3
    
    C -- "Step 4: Enrich (Async)" --> D4
    D4 -- "Calls" --> AI
    
    C -- "Step 5: Persist" --> E
    E --> DB
    
    C -- "Step 6: Publish" --> F
    F --> G
```

### 2.2 最终目录结构 (`services/fast-news-service/`)

```
fast-news-service/
├── cmd/server/
│   └── main.go                 # API服务(用于Admin)和事件消费者的统一入口
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_dedupe_store.go # ✨ SimHash去重存储实现 ✨
│   │   ├── client/
│   │   │   └── ai_assistant_client.go
│   │   ├── event/
│   │   │   ├── consumer.go
│   │   │   └── producer.go
│   │   ├── grpc/
│   │   │   └── handler.go      # Admin后台管理接口
│   │   └── repository/
│   │       ├── model.go
│   │       └── postgres_repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── cache.go
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── news_processing_service.go # ✨ 核心应用服务, 编排处理管道 ✨
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/            # ✨ 领域服务, 封装独立的业务规则/算法 ✨
│           ├── cleaner_service.go
│           ├── deduplication_service.go
│           ├── importance_scoring_service.go
│           └── ai_enrichment_service.go
├── config/
│   ├── importance_rules.yaml   # ✨ 重要性评估规则配置文件 ✨
│   └── deduplication_config.yaml
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/` - 业务规则的配置化

*   **`importance_rules.yaml`**:
    ```yaml
    source_weights:
      "Bloomberg": 10
      "Reuters": 9
      "LocalNews": 3
    
    major_keywords:
      - keyword: "美联储"
        score_boost: 50
      - keyword: "加息"
        score_boost: 40
      - keyword: "突发"
        score_boost: 30
    ```
*   **`deduplication_config.yaml`**:
    ```yaml
    simhash:
      shingle_size: 3
      hamming_distance_threshold: 3
    ttl_seconds: 86400 # 24 hours
    ```

### 3.2 `domain/` - 领域层 (The Processing Rules & Algorithms)

这是封装所有独立、无状态、可测试的处理逻辑的地方。

*   `domain/model/`: 定义`FastNewsItem`等核心领域对象。
*   **`domain/service/cleaner_service.go`**:
    *   **`CleanerService`**: 使用`bluemonday`等库，提供`CleanHTML(html)`方法，返回纯文本。
*   **`domain/service/deduplication_service.go`**:
    *   **`DeduplicationService`**:
    *   **`CalculateSimHash(text)`**: 一个纯函数，接收文本，返回`uint64`的SimHash值。
    *   **`IsDuplicate(ctx, newHash, cache)`**: 调用`cache`接口，获取近期所有的哈希值，计算汉明距离，并根据配置的阈值返回`true`或`false`。
*   **`domain/service/importance_scoring_service.go`**:
    *   **`ScoringService`**: 在启动时加载并解析`importance_rules.yaml`。
    *   **`Score(newsItem)`**: 一个纯函数，根据规则为`newsItem`计算一个总分。
*   **`domain/service/ai_enrichment_service.go`**:
    *   **`AIEnrichmentService`**: 注入`ai_assistant_client`。
    *   **`Enrich(ctx, content)`**: 调用AI服务，请求摘要、关键词等，并返回一个包含增强结果的`struct`。

### 3.3 `application/` - 应用层 (The ETL Pipeline Orchestrator)

*   **`application/port/`**: 定义`Repository`, `Cache`, `NewsProcessingService`等接口。
*   **`application/service/news_processing_service.go`**: **这是所有处理步骤的编排者**。
    *   **`NewsProcessingService`**: 注入所有`domain/service`和`adapter`的实例。
    *   **`ProcessRawNews(ctx, rawNews)`**:
        1.  **清洗**: `cleanedContent := s.cleaner.CleanHTML(rawNews.Content)`。
        2.  **去重检查**:
            a. `hash := s.deduplicator.CalculateSimHash(cleanedContent)`。
            b. `isDup, err := s.deduplicator.IsDuplicate(ctx, hash, s.dedupeCache)`。
            c. 如果是重复，则记录日志并**立即返回**，中止流程。
        3.  **创建领域对象**: `newsItem := domain.NewFastNewsItem(...)`。
        4.  **重要性评分**: `score := s.scorer.Score(newsItem)`，并设置到`newsItem`上。
        5.  **AI增强 (异步)**:
            *   如果`score`高于阈值，则启动一个新的goroutine（或发送到内部的Asynq任务队列）来执行AI增强。
            *   `go s.enrichAndSave(bgCtx, newsItem)`。
            *   AI增强是一个**“尽力而为(best-effort)”**的操作，它的失败**不应**阻塞主流程的持久化和发布。
        6.  **持久化**: 调用`repository.Save(ctx, newsItem)`。
        7.  **发布事件**: 调用`producer.PublishProcessedNews(ctx, newsItem)`。
        8.  **更新去重库**: `s.dedupeCache.AddHash(ctx, hash)`。

### 3.4 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/cache/redis_dedupe_store.go`**:
    *   实现`port.DedupeCache`接口。
    *   使用**Redis Sorted Set** (`ZADD`, `ZREMRANGEBYSCORE`, `ZRANGE`) 来存储SimHash值，`score`为时间戳，便于按时间清理。
*   **`adapter/repository/postgres_repo.go`**:
    *   实现`port.Repository`接口，负责将`FastNewsItem`持久化到PostgreSQL。
*   **`adapter/event/consumer.go`**:
    *   封装`pkg/messaging`，消费`raw-news-stream` Topic，并将每条消息传递给`application.NewsProcessingService.ProcessRawNews`。
*   **`adapter/event/producer.go`**:
    *   封装`pkg/messaging`，用于发布`ProcessedNewsEvent`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`fast-news-service`：
1.  **管道化处理 (Pipeline Processing)**: 将复杂的处理流程拆分为一系列独立的、单一职责的领域服务（Cleaner, Deduplicator, Scorer），由应用服务进行编排。这使得每个步骤都清晰、可测试、可独立优化。
2.  **配置驱动的业务规则**: 将易变的重要性评估规则从代码中分离到YAML配置文件中，使得运营可以灵活调整，而无需重新部署服务。
3.  **高性能去重**: 采用**SimHash + Redis Sorted Set**的方案，提供了一个高效、可扩展、且能自动清理过期的实时去重机制。
4.  **异步与容错**: 将耗时的AI增强操作异步化，确保其不影响核心新闻流的处理延迟。整个系统基于Kafka的“至少一次”消费语义和DLQ策略，保证了数据处理的可靠性。
5.  **清晰的数据流**: 通过使用两个独立的Kafka Topic（`raw-news`和`processed-news`），清晰地划分了数据的处理阶段，使得数据治理和系统监控变得简单。

这种架构确保了`fast-news-service`能够作为一个**高效、智能、可靠**的实时信息加工厂，为CINA.CLUB平台的所有下游应用持续不断地提供高质量的快讯内容。