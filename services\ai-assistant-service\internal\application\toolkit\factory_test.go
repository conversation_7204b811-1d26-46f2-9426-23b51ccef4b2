/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package toolkit

import (
	"context"
	"testing"

	"cina.club/services/ai-assistant-service/internal/application/port"
)

func TestNewToolkitFactory(t *testing.T) {
	factory := NewToolkitFactory()

	if factory == nil {
		t.Fatal("NewToolkitFactory() returned nil")
	}

	if factory.tools == nil {
		t.Error("Factory tools map should be initialized")
	}

	if len(factory.tools) != 0 {
		t.Error("Factory should start with empty tools map")
	}
}

func TestToolkitFactory_RegisterTool(t *testing.T) {
	factory := NewToolkitFactory()
	mockTool := &MockTool{name: "test_tool"}

	err := factory.RegisterTool(mockTool)
	if err != nil {
		t.Fatalf("Failed to register tool: %v", err)
	}

	if len(factory.tools) != 1 {
		t.<PERSON>("Expected 1 tool, got %d", len(factory.tools))
	}

	if factory.tools["test_tool"] != mockTool {
		t.Error("Tool not stored correctly")
	}
}

func TestToolkitFactory_RegisterTool_DuplicateName(t *testing.T) {
	factory := NewToolkitFactory()
	mockTool1 := &MockTool{name: "duplicate_tool"}
	mockTool2 := &MockTool{name: "duplicate_tool"}

	err := factory.RegisterTool(mockTool1)
	if err != nil {
		t.Fatalf("Failed to register first tool: %v", err)
	}

	err = factory.RegisterTool(mockTool2)
	if err == nil {
		t.Error("Expected error when registering duplicate tool name")
	}
}

func TestToolkitFactory_RegisterDefaultTools(t *testing.T) {
	factory := NewToolkitFactory()

	err := factory.RegisterDefaultTools()
	if err != nil {
		t.Fatalf("Failed to register default tools: %v", err)
	}

	expectedTools := []string{
		"search_services",
		"create_schedule",
		"query_memory",
		"query_knowledge",
		"get_user_info",
		"llm_call",
	}

	for _, toolName := range expectedTools {
		if _, exists := factory.tools[toolName]; !exists {
			t.Errorf("Expected tool %s not found", toolName)
		}
	}
}

func TestToolkitFactory_GetTool(t *testing.T) {
	factory := NewToolkitFactory()
	mockTool := &MockTool{name: "test_tool"}

	_ = factory.RegisterTool(mockTool)

	// Test existing tool
	tool, err := factory.GetTool("test_tool")
	if err != nil {
		t.Fatalf("Failed to get tool: %v", err)
	}

	if tool != mockTool {
		t.Error("Got wrong tool")
	}

	// Test non-existing tool
	_, err = factory.GetTool("non_existing_tool")
	if err == nil {
		t.Error("Expected error for non-existing tool")
	}
}

func TestToolkitFactory_ExecuteTool(t *testing.T) {
	factory := NewToolkitFactory()
	mockResult := port.NewToolResult(map[string]interface{}{"result": "success"})
	mockTool := &MockTool{
		name:   "test_tool",
		result: mockResult,
	}

	_ = factory.RegisterTool(mockTool)

	inputs := map[string]interface{}{"input": "test"}
	result, err := factory.ExecuteTool(context.Background(), "test_tool", inputs)

	if err != nil {
		t.Fatalf("Failed to execute tool: %v", err)
	}

	if result != mockResult {
		t.Error("Got wrong result")
	}
}

func TestToolkitFactory_ExecuteTool_NonExisting(t *testing.T) {
	factory := NewToolkitFactory()

	inputs := map[string]interface{}{"input": "test"}
	_, err := factory.ExecuteTool(context.Background(), "non_existing_tool", inputs)

	if err == nil {
		t.Error("Expected error for non-existing tool")
	}
}

func TestToolkitFactory_GetToolSchemas(t *testing.T) {
	factory := NewToolkitFactory()
	mockTool := &MockTool{name: "test_tool"}

	_ = factory.RegisterTool(mockTool)

	schemas := factory.GetToolSchemas()

	if len(schemas) != 1 {
		t.Errorf("Expected 1 schema, got %d", len(schemas))
	}

	if schemas[0].Name != "test_tool" {
		t.Errorf("Expected schema name 'test_tool', got '%s'", schemas[0].Name)
	}
}

func TestToolkitFactory_GetRegisteredTools(t *testing.T) {
	factory := NewToolkitFactory()
	mockTool1 := &MockTool{name: "tool1"}
	mockTool2 := &MockTool{name: "tool2"}

	_ = factory.RegisterTool(mockTool1)
	_ = factory.RegisterTool(mockTool2)

	// Test that tools are registered
	tool1, err := factory.GetTool("tool1")
	if err != nil {
		t.Errorf("Failed to get tool1: %v", err)
	}
	if tool1 != mockTool1 {
		t.Error("Got wrong tool1")
	}

	tool2, err := factory.GetTool("tool2")
	if err != nil {
		t.Errorf("Failed to get tool2: %v", err)
	}
	if tool2 != mockTool2 {
		t.Error("Got wrong tool2")
	}
}

func TestToolkitFactory_GetToolsByCategory(t *testing.T) {
	factory := NewToolkitFactory()
	mockTool1 := &MockTool{name: "tool1", category: port.ToolCategorySearch}
	mockTool2 := &MockTool{name: "tool2", category: port.ToolCategoryUser}
	mockTool3 := &MockTool{name: "tool3", category: port.ToolCategorySearch}

	_ = factory.RegisterTool(mockTool1)
	_ = factory.RegisterTool(mockTool2)
	_ = factory.RegisterTool(mockTool3)

	searchTools := factory.GetToolsByCategory(port.ToolCategorySearch)

	if len(searchTools) != 2 {
		t.Errorf("Expected 2 search tools, got %d", len(searchTools))
	}

	userTools := factory.GetToolsByCategory(port.ToolCategoryUser)

	if len(userTools) != 1 {
		t.Errorf("Expected 1 user tool, got %d", len(userTools))
	}
}

// MockTool for testing
type MockTool struct {
	name     string
	category port.ToolCategory
	result   *port.ToolResult
	err      error
}

func (m *MockTool) Name() string                   { return m.name }
func (m *MockTool) Description() string            { return "Mock tool" }
func (m *MockTool) Category() port.ToolCategory    { return m.category }
func (m *MockTool) RequiresAuth() bool             { return false }
func (m *MockTool) IsAsync() bool                  { return false }
func (m *MockTool) InputSchema() *port.JSONSchema  { return nil }
func (m *MockTool) OutputSchema() *port.JSONSchema { return nil }
func (m *MockTool) Execute(ctx context.Context, inputs map[string]interface{}) (*port.ToolResult, error) {
	return m.result, m.err
}
