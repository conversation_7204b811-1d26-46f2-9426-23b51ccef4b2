# CINA.CLUB Apple Applications

/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

本目录包含 CINA.CLUB 平台的所有 Apple 生态应用，支持 iOS、iPadOS、macOS、watchOS 和 visionOS。

## 项目结构

```
apple/
├── Package.swift                    # Swift Package Manager 配置
├── Targets/                         # 应用目标
│   ├── iOS_iPadOS/                 # iOS 和 iPadOS 应用
│   ├── macOS/                      # macOS 应用
│   ├── watchOS/                    # Apple Watch 应用
│   └── visionOS/                   # Apple Vision Pro 应用
├── Packages/                       # 共享 Swift 包
│   ├── AppCore/                    # 应用核心业务逻辑
│   ├── DataLayer/                  # 数据访问层
│   ├── DesignSystem/               # UI 组件和设计系统
│   ├── GoBridge/                   # Go 核心逻辑桥接
│   └── Feature/                    # 功能模块
│       ├── FeatureAuth/            # 认证功能
│       ├── FeatureChat/            # 聊天功能
│       ├── FeaturePKB/             # 个人知识库
│       ├── FeatureLive/            # 直播功能
│       └── FeatureMarketplace/     # 服务市场
└── Frameworks/
    └── CoreGo.xcframework          # Go Mobile 编译产物
```

## 核心特性

### 🎯 统一架构
- **共享核心逻辑**: 通过 Go Mobile 编译的 CoreGo.xcframework 实现
- **平台特定 UI**: 每个平台使用最适合的原生 UI 框架
- **模块化设计**: 使用 Swift Package Manager 进行模块化管理

### 🔐 端到端加密 (E2EE)
- 所有敏感数据在本地加密
- 使用 Go 核心库进行加密操作
- 安全的密钥管理和存储

### 🤖 本地 AI 推理
- 端侧 AI 模型管理
- 隐私保护的本地推理
- AI 助手和个人知识库集成

### 🔄 数据同步
- 端到端加密的云同步
- 跨设备数据一致性
- 版本控制和冲突解决

## 技术栈

- **语言**: Swift 5.9+
- **UI 框架**: SwiftUI
- **架构模式**: MVVM + Coordinator Pattern
- **异步编程**: Swift Concurrency (async/await) + Combine
- **网络通信**: gRPC-Swift
- **本地存储**: SwiftData/Core Data
- **依赖管理**: Swift Package Manager
- **核心逻辑**: Go Mobile (CoreGo.xcframework)

## 开发要求

### 系统要求
- macOS 14.0+
- Xcode 15.0+
- Swift 5.9+

### 支持平台
- iOS 17.0+
- iPadOS 17.0+
- macOS 14.0+
- watchOS 10.0+
- visionOS 1.0+

## 开发指南

### 项目设置
1. 确保安装了 Xcode 15.0+
2. 克隆项目到本地
3. 使用 Swift Package Manager 解析依赖
4. 构建 Go Mobile 框架 (详见 `/core` 目录说明)

### 代码规范
- 所有文件必须包含版权声明
- 遵循 Swift API 设计准则
- 使用 SwiftLint 进行代码风格检查
- 异步操作使用 async/await 模式

### 架构原则
1. **单一职责**: 每个模块只负责一个特定功能
2. **依赖倒置**: 高层模块不依赖低层模块
3. **接口隔离**: 客户端不应被迫依赖不使用的接口
4. **开闭原则**: 对扩展开放，对修改关闭

## 功能模块

### AppCore
应用的核心业务逻辑，包括：
- 用户状态管理
- 应用生命周期管理
- 全局配置和协调

### DataLayer
统一的数据访问层，包括：
- Repository 模式实现
- 网络请求封装
- 本地数据持久化

### DesignSystem
统一的设计系统，包括：
- 颜色和字体定义
- 可复用 UI 组件
- 视图修饰符

### GoBridge
与 Go 核心的桥接层，包括：
- 加密操作封装
- 数据同步接口
- AI 推理接口

## 版权声明

Copyright (c) 2025 Cina.Club  
All rights reserved.

本项目的所有源代码均受版权保护，未经授权不得复制、修改或分发。 