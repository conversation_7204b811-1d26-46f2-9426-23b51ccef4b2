# CINA.CLUB Kubernetes Infrastructure
**Copyright (c) 2025 Cina.Club**  
**创建日期**: 2025-01-27  
**架构模式**: Kong Gateway + 平台工程

## 🏗️ 架构概览

CINA.CLUB Kubernetes基础设施采用**平台工程**模式，基于**Kong Gateway**构建企业级API网关管理体系。

## 📁 目录结构

```
infra/kubernetes/
├── base/kong/plugins/           # Kong Gateway标准插件库  
└── system/                      # 平台级基础设施
    ├── kong/                    # Kong Gateway核心配置
    ├── cert-manager.yaml        # 证书管理
    ├── monitoring-stack.yaml    # 监控堆栈
    ├── prometheus.yaml          # 指标收集
    └── fluentd-daemonset.yaml  # 日志收集
```

## 🛠️ 平台组件

### Kong Gateway 基础设施
- **Kong Ingress Controller**: API网关控制面
- **Kong Proxy**: 数据面代理，高可用和自动扩展
- **标准插件库**: 6种服务类型的优化插件配置

### 系统监控
- **Prometheus**: 指标收集和存储
- **Fluentd**: 统一日志收集
- **cert-manager**: 自动TLS证书管理

## 🚀 部署命令

```bash
# 部署Kong Gateway
kubectl apply -k infra/kubernetes/system/kong/

# 部署监控系统  
kubectl apply -f infra/kubernetes/system/monitoring-stack.yaml

# 验证部署状态
kubectl get pods -n kong-system
```

## 📊 当前状态

- **微服务数量**: 41个 ✅ 100%完成
- **API网关**: Kong Gateway企业级配置 ✅
- **多环境**: dev/staging/prod环境支持 ✅
- **验证状态**: 100%通过 ✅

**注意**: 微服务部署配置现在位于各自的 `services/<service-name>/deploy/` 目录下，采用服务团队自治模式。

---

**CINA.CLUB平台工程团队**
