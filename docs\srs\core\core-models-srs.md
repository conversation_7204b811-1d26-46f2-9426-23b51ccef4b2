好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`core/models`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`core/models`的设计哲学、结构定义、使用约束和最佳实践，作为所有后端服务和核心前端逻辑共享业务模型的权威设计蓝图。

---
### CINA.CLUB - 共享核心数据模型 (`core/models`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师]  
**审批人:** [CTO, 各技术团队负责人]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计原则与约束](#3-核心设计原则与约束)
4.  [功能需求 (核心模型定义)](#4-功能需求-核心模型定义)
    *   [4.1 `user.go`: 用户核心模型](#41-usergo-用户核心模型)
    *   [4.2 `service.go`: 服务与交易模型](#42-servicego-服务与交易模型)
    *   [4.3 `content.go`: 通用内容模型](#43-contentgo-通用内容模型)
    *   [4.4 `social.go`: 社交关系模型](#44-socialgo-社交关系模型)
    *   [4.5 `asset.go`: 通用资产模型](#45-assetgo-通用资产模型)
5.  [接口定义 (作为数据载体)](#5-接口定义-作为数据载体)
6.  [模型演进与版本控制](#6-模型演进与版本控制)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的Go-Centric架构中，后端微服务之间以及核心前端逻辑（Go Mobile/WASM）内部，需要对核心业务对象（如用户、服务、订单）有一套**统一、权威、强类型**的内存表示。`core/models` 包的目的就是提供这套**共享的核心业务对象数据结构 (Go structs)**。它作为平台业务领域的“通用语言”，确保了数据在不同组件之间传递时结构的一致性和正确性。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   定义平台核心的、跨多个服务和客户端使用的业务对象的Go `struct`。
    *   为这些`struct`定义基础的、无副作用的辅助方法（如`IsEmpty()`, `GetFullName()`）。
    *   定义与业务领域相关的枚举类型（如`OrderStatus`, `UserRole`）。
*   **范围之外 (Out-of-Scope)**:
    *   **任何特定于数据库的实现细节**: `core/models` 中的`struct` **严禁**包含任何ORM或数据库驱动的标签（如`gorm:"..."`, `db:"..."`）。
    *   **任何业务逻辑或流程控制**: 模型本身只包含数据，不包含行为。
    *   **API或事件的特定消息结构**: `core/api`中的Protobuf `message`是通信契约，而`core/models`中的`struct`是业务领域的内存表示。它们之间通常需要一层转换。
    *   任何I/O操作或外部依赖。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。
*   **`core/`包内其他模块**的开发者（如`crypto`, `datasync`）。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`core/models` 是位于`core/`目录下的一个基础定义库。它处于依赖链的底层，通常只依赖Go标准库。它被几乎所有的后端服务和`core/`中的其他模块所依赖。

#### 2.2. 设计原则
*   **领域驱动设计 (DDD) 核心**: `core/models`是平台核心领域模型的代码体现，代表了对业务最纯粹的抽象。
*   **贫血模型 (Anemic Domain Model)**: 本包中的`struct`是“贫血”的，即主要作为数据的容器(Data Transfer Objects, DTOs)，其业务逻辑和行为由各个服务的应用层/领域服务来处理。
*   **稳定性与不变性**: 核心模型的结构应保持高度稳定。任何修改都需要经过严格的跨团队评审。
*   **清晰与自解释**: 结构体和字段的命名必须清晰、无歧义，并附有详细的GoDoc注释。

---

### 3. 核心设计原则与约束

#### 3.1. 模型与数据库实体分离
这是一个**强制性**的核心原则。
*   **`core/models` (领域模型)**: 平台通用的、纯粹的业务对象表示，用于业务逻辑层之间的数据传递。
*   **`services/*/internal/adapter/repository` (持久化模型)**: 每个服务在自己的`repository`层定义与数据库表结构一一对应的`struct`（可以带GORM等标签）。
*   **转换层**: `repository`的实现负责在这两种模型之间进行转换。

**理由**:
1.  **解耦**: 核心业务逻辑不应与数据库的物理实现绑定。更换数据库或表结构不应影响到`core/models`。
2.  **职责单一**: `core/models`只关心业务概念，`repository`模型只关心数据存储。
3.  **灵活性**: 不同的服务可能需要将同一个领域模型持久化为不同的表结构。

#### 3.2. 文件组织
*   按业务领域将相关的模型组织在同一个文件中。例如，所有与用户相关的模型在`user.go`中，所有与交易相关的在`service.go`中。

---

### 4. 功能需求 (核心模型定义)

本节定义了`core/models`中必须包含的核心数据结构。这是一个非详尽的列表，会随业务发展而演进。

#### 4.1. `user.go`: 用户核心模型
*   **职责**: 定义用户、认证、权限、成长等核心身份概念。
*   **核心结构体**:
    ```go
    type User struct {
        ID                      uuid.UUID
        Username                string
        Status                  UserStatus
        Profile                 UserProfile
        Growth                  UserGrowth
        Membership              UserMembership
        KYCInfo                 UserKYCInfo
        OfficialVerification    OfficialVerification
        Roles                   []UserRole
    }
    
    type UserProfile struct {
        DisplayName string
        AvatarURL   string
        Bio         string
    }
    
    type UserGrowth struct {
        Level           int
        TotalActiveDays float64
    }
    
    type UserMembership struct {
        VIPLevel  int
        ExpiresAt time.Time
    }
    
    // ... 其他相关struct和enum
    ```

#### 4.2. `service.go`: 服务与交易模型
*   **职责**: 定义服务市场、订单、预订等交易相关的核心概念。
*   **核心结构体**:
    ```go
    type ServiceProduct struct {
        ID          uuid.UUID
        Name        string
        CategoryID  uuid.UUID
    }

    type ServiceOffering struct {
        ID              uuid.UUID
        ProductID       uuid.UUID
        ProviderUserID  uuid.UUID
        Title           string
        PricingDetails  map[string]interface{} // 使用map表示灵活的JSONB
    }

    type Order struct {
        ID              uuid.UUID
        CustomerUserID  uuid.UUID
        ProviderUserID  uuid.UUID
        OfferingID      uuid.UUID
        Status          OrderStatus
        TotalAmount     int64 // 使用最小货币单位，如分
        Currency        string
        LineItems       []OrderLineItem
        Booking         Booking
    }

    type Booking struct {
        StartTimeUTC time.Time
        EndTimeUTC   time.Time
        Status       BookingStatus
    }
    
    // ... 其他相关struct和enum
    ```

#### 4.3. `content.go`: 通用内容模型
*   **职责**: 定义被多个内容服务（论坛、问答、知识库）共享的通用内容结构。
*   **核心结构体**:
    ```go
    // Block-based content, similar to Notion
    type ContentBlock struct {
        ID      string
        Type    BlockType // e.g., PARAGRAPH, HEADING, IMAGE
        Content map[string]interface{}
    }
    
    type RichContent struct {
        Blocks []ContentBlock
    }

    type VoteSummary struct {
        Upvotes   int
        Downvotes int
        Score     int
    }
    ```

#### 4.4. `social.go`: 社交关系模型
*   **职责**: 定义关注、好友等社交关系。
*   **核心结构体**:
    ```go
    type Relationship struct {
        SourceUserID uuid.UUID
        TargetUserID uuid.UUID
        Type         RelationshipType // FOLLOWS, FRIEND, BLOCKED
    }

    type SocialStats struct {
        FollowersCount int
        FollowingCount int
        FriendsCount   int
    }
    ```

#### 4.5. `asset.go`: 通用资产模型
*   **职责**: 定义被多个服务使用的通用资产或物品概念，如Avatar资产、积分商城物品。
*   **核心结构体**:
    ```go
    type Asset struct {
        ID            uuid.UUID
        Type          AssetType
        Name          string
        Description   string
        ThumbnailURL  string
        Metadata      map[string]interface{}
    }
    ```

---

### 5. 接口定义 (作为数据载体)

`core/models`本身不提供可执行的API，但它定义的`struct`是所有API（`core/api`）和业务逻辑层之间传递数据的**载体**。

**示例：从Protobuf到领域模型的转换**
```go
// services/user-core-service/internal/adapter/grpc/converter.go

// toUserModel 将gRPC的User消息转换为核心领域模型User
func toUserModel(pbUser *user_v1.User) *models.User {
    if pbUser == nil {
        return nil
    }
    return &models.User{
        ID:       uuid.FromString(pbUser.Id),
        Username: pbUser.Username,
        // ... 字段转换 ...
    }
}
```

---

### 6. 模型演进与版本控制

*   **FR6.1 (向后兼容)**: 对已有的`struct`字段的修改必须是向后兼容的。
    *   **允许**: 新增字段。
    *   **禁止**: 删除字段、修改字段类型。
*   **FR6.2 (废弃)**: 需要废弃的字段，应保留但添加`// Deprecated: ...`注释，并在下一个大版本中移除。
*   **FR6.3 (评审)**: 任何对`core/models`的修改都必须通过一个涉及多个团队（特别是依赖该模型的服务团队）的PR评审流程。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**: `struct`的定义应考虑内存对齐和效率。避免不必要的指针，除非字段是可选的。
*   **NFR7.2 (可靠性)**: 模型的定义必须精确，避免模糊不清的字段（如使用强类型的枚举而不是`string`）。
*   **NFR7.3 (可测试性)**: 提供简单的工厂函数（如`NewTestUser()`）来创建测试用的模型实例，会极大地便利下游服务的单元测试。

---

### 8. 技术约束与开发规范

*   **TC8.1 (依赖)**: **严格禁止任何非Go标准库的依赖**。这是为了确保`core/models`的最大可移植性和最小耦合。
*   **TC8.2 (开发规范)**:
    *   **强制使用**: 所有跨服务传递的核心业务对象，**必须**使用`core/models`中定义的类型。
    *   **清晰的注释**: 每个`struct`和`field`都必须有清晰的GoDoc注释，解释其业务含义。
    *   **贫血原则**: **严禁**在模型`struct`上添加任何包含业务逻辑、I/O操作或依赖外部服务的方法。

---
这份SRS为`core/models`的设计和管理提供了坚实、全面的指导。通过维护这样一个稳定、权威、纯粹的核心领域模型库，CINA.CLUB平台可以确保其分布式系统在数据层面上的**一致性、正确性和长期可维护性**，是实现高效协同开发的关键。