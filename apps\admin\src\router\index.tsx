/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Spin } from 'antd'

import { ProtectedRoute } from './ProtectedRoute'
import { useAuthStore } from '@/store/auth'
import { Permission } from '@/types/auth'

// 懒加载页面组件
const LoginPage = React.lazy(() => import('@/pages/Login'))
const DashboardPage = React.lazy(() => import('@/pages/Dashboard'))
const UserListPage = React.lazy(() => import('@/pages/User/UserList'))
const UserDetailPage = React.lazy(() => import('@/pages/User/UserDetail'))
const ServiceListPage = React.lazy(() => import('@/pages/Service/ServiceList'))
const ServiceDetailPage = React.lazy(() => import('@/pages/Service/ServiceDetail'))
const OrderListPage = React.lazy(() => import('@/pages/Order/OrderList'))
const OrderDetailPage = React.lazy(() => import('@/pages/Order/OrderDetail'))
const ContentListPage = React.lazy(() => import('@/pages/Content/ContentList'))
const FinanceOverviewPage = React.lazy(() => import('@/pages/Finance/FinanceOverview'))
const AnalyticsPage = React.lazy(() => import('@/pages/Analytics'))
const SystemConfigPage = React.lazy(() => import('@/pages/System/SystemConfig'))
const NotFoundPage = React.lazy(() => import('@/pages/NotFound'))
const HealthCheckPage = React.lazy(() => import('@/pages/HealthCheck'))

// 布局组件
const BasicLayout = React.lazy(() => import('@/layouts/BasicLayout'))
const UserLayout = React.lazy(() => import('@/layouts/UserLayout'))

/**
 * 页面加载中组件
 */
const PageLoading: React.FC = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh' 
  }}>
    <Spin size="large" tip="加载中..." />
  </div>
)

/**
 * 主路由组件
 */
const Router: React.FC = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)

  return (
    <Suspense fallback={<PageLoading />}>
      <Routes>
        {/* Health Check Route */}
        <Route path="/health" element={<HealthCheckPage />} />
        
        {/* 用户认证相关路由 */}
        <Route
          path="/login"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <UserLayout>
                <LoginPage />
              </UserLayout>
            )
          }
        />

        {/* 受保护的管理后台路由 */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <BasicLayout>
                <Routes>
                  {/* 首页重定向 */}
                  <Route 
                    path="/" 
                    element={<Navigate to="/dashboard" replace />} 
                  />

                  {/* 仪表板 */}
                  <Route 
                    path="/dashboard" 
                    element={<DashboardPage />} 
                  />

                  {/* 用户管理 */}
                  <Route
                    path="/users"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.USER_VIEW]}>
                        <UserListPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/users/:id"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.USER_VIEW]}>
                        <UserDetailPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* 服务管理 */}
                  <Route
                    path="/services"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.SERVICE_VIEW]}>
                        <ServiceListPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/services/:id"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.SERVICE_VIEW]}>
                        <ServiceDetailPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* 订单管理 */}
                  <Route
                    path="/orders"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.ORDER_VIEW]}>
                        <OrderListPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/orders/:id"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.ORDER_VIEW]}>
                        <OrderDetailPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* 内容管理 */}
                  <Route
                    path="/content"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.CONTENT_VIEW]}>
                        <ContentListPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* 财务管理 */}
                  <Route
                    path="/finance"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.FINANCE_VIEW]}>
                        <FinanceOverviewPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* 数据分析 */}
                  <Route
                    path="/analytics"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.ANALYTICS_VIEW]}>
                        <AnalyticsPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* 系统配置 */}
                  <Route
                    path="/system"
                    element={
                      <ProtectedRoute requiredPermissions={[Permission.SYSTEM_CONFIG]}>
                        <SystemConfigPage />
                      </ProtectedRoute>
                    }
                  />

                  {/* 404页面 */}
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </BasicLayout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </Suspense>
  )
}

export default Router 