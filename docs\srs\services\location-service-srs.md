﻿好的，遵照您的指示，我们来生成一份为 `location-service` (位置与地理服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多提供商与智能路由**: 详细定义如何支持多个地图服务提供商（如Google Maps, 高德地图），并引入基于成本、地区和性能的智能路由与故障降级策略。
2.  **地理空间数据存储与查询**: 增加对平台自定义地理空间数据（如服务区域多边形）的存储和查询能力，并明确使用PostGIS。
3.  **地理围栏(Geofencing)服务**: 将地理围栏功能从可选升级为核心，详细定义其管理和事件触发流程。
4.  **成本控制与用量追踪**: 强调成本控制，增加对内外部API调用的用量追踪和预算告警机制。
5.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、经济高效、高可用，且能作为整个平台所有地理位置能力统一出口的地理中台服务。

---

### CINA.CLUB - location-service 需求规格说明书

**版本: 2.0 (生产级定义，集成多提供商与地理空间能力)**  
**发布日期: 2025-06-22**  
**最后修订日期: 2025-06-22**  
**文档负责人:** [平台工程/地图服务团队负责人名称]  
**审批人:** [CTO/架构师]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台的大量核心功能，如服务市场、任务广场、AI助手情景感知等，都深度依赖于地理位置信息。`location-service` 的目的在于构建一个**统一、可靠、经济高效、且与具体提供商解耦**的地理空间数据处理和位置相关功能的服务中枢。它通过封装和智能路由对多个第三方地图服务提供商（如Google Maps Platform, 高德地图）的API调用，并管理平台自身的地理空间数据，为所有其他微服务提供标准化的地理位置能力。

#### 1.2. 服务范围
本服务 **负责**:
*   **API抽象与代理**:
    *   **地理编码/逆地理编码**: 地址与经纬度的相互转换。
    *   **兴趣点(POI)搜索**: 根据关键词、类别、位置搜索POI。
    *   **路线与距离计算**: 计算两点或多点间的距离和预估时长。
*   **多提供商智能路由**:
    *   支持同时集成多个地图服务提供商。
    *   根据请求的地区、成本、性能要求动态选择最优的提供商。
    *   实现对主提供商故障时的自动降级。
*   **平台地理空间数据管理**:
    *   存储和管理平台自定义的地理空间数据，如服务提供者的**服务区域（多边形）**。
    *   提供高效的地理空间查询，如“判断一个点是否在某个区域内”。
*   **地理围栏(Geofencing)管理**: 提供API供其他服务创建/删除地理围栏，并处理来自客户端或第三方服务的进入/离开事件。
*   **结果缓存与成本控制**: 缓存外部API调用结果，并追踪用量以控制成本。

本服务 **不负责**:
*   **地图瓦片服务 (Map Tile Serving)**: 客户端应直接从地图提供商获取地图瓦片。
*   **用户实时GPS位置追踪**: 主要由客户端App负责，但客户端可将位置上报给本服务以触发地理围栏。
*   **复杂的GIS分析**: 本服务提供基础地理计算和API代理。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部所有需要地理位置能力的微服务 (主要)**: `user-core-service`, `service-offering-service`, `ai-assistant-service`, `search-service`, `routines-service`等。
*   **CINA.CLUB客户端应用**: （可选）上报位置以触发地理围行事件。
*   **CINA.CLUB平台管理员**: 管理自定义地理数据和提供商配置。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`location-service` 是平台的**地理信息中台**和**位置能力网关**。它通过强大的API抽象层，将平台业务逻辑与具体的地图服务提供商完全解耦，极大地提高了系统的灵活性、可用性和成本控制能力。同时，它也是平台自有地理空间数据的“单一事实来源”。

#### 2.2. 主要功能概述
*   支持多地图服务提供商的适配器与智能路由。
*   统一的地理编码、POI搜索、路线规划API。
*   基于PostGIS的自定义地理空间数据存储与查询。
*   完整的地理围栏管理与事件协调。
*   高效的结果缓存与成本追踪。

### 3. 核心流程图

#### 3.1. 处理一个带智能路由的地理编码请求
```mermaid
sequenceDiagram
    participant Requester as "Calling Service"
    participant LocationService as LS
    participant Redis
    participant ProviderRouter as "Provider Router"
    participant ProviderA as "Provider A (e.g., Google)"
    participant ProviderB as "Provider B (e.g., 高德)"

    Requester->>LS: 1. GeocodeRequest (address: "北京市海淀区...", country_hint: "CN")
    
    LS->>Redis: 2. Check cache for this request
    alt Cache Hit
        Redis-->>LS: (Cached GeocodeResponse)
        LS-->>Requester: (Return cached response)
    else Cache Miss
        LS->>ProviderRouter: 3. Select provider for country="CN"
        ProviderRouter-->>LS: (Select ProviderB based on rules)
        
        LS->>ProviderB: 4. **[Attempt 1]** Call 高德 Geocoding API
        
        alt ProviderB is healthy
            ProviderB-->>LS: 5a. (GeocodeResponse)
        else ProviderB fails or times out
            Note over LS: Circuit Breaker for ProviderB opens!
            LS->>ProviderRouter: 5b. Request a fallback provider
            ProviderRouter-->>LS: (Select ProviderA)
            LS->>ProviderA: 6b. **[Attempt 2]** Call Google Geocoding API
            ProviderA-->>LS: (GeocodeResponse from fallback)
        end
        
        LS->>Redis: 7. Cache the new response with TTL
        LS-->>Requester: 8. Return the response
    end
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. API抽象与多提供商支持
*   **FR4.1.1 (适配器模式)**: 系统的核心逻辑必须通过一个统一的`MapProvider`接口与具体的地图服务提供商客户端进行交互。必须为每个集成的提供商（Google, 高德等）实现该接口。
*   **FR4.1.2 (智能路由)**: 系统必须有一个可配置的路由引擎。
    *   **规则**: 能根据请求参数（如`country_code`, `language`）和业务场景（`task_type`）选择主提供商。
    *   **示例**: 在中国大陆的请求优先使用高德地图，其他地区优先使用Google Maps。
*   **FR4.1.3 (故障降级)**: 当对主提供商的调用连续失败或超时达到阈值时，**熔断器(Circuit Breaker)**必须打开，并自动将请求路由到预定义的备用提供商。

#### 4.2. 平台地理空间数据管理
*   **FR4.2.1 (数据存储)**: 系统必须提供API，允许授权服务（如`service-offering-service`）创建和管理自定义的地理空间数据，特别是**多边形(Polygon)**区域。
*   **FR4.2.2 (空间查询)**: 必须提供高效的地理空间查询API，核心包括：
    *   **`ST_Contains`**: 判断一个点是否在一个或多个多边形区域内（如“判断用户当前位置是否在某服务商的服务范围内”）。
    *   **`ST_Intersects`**: 判断两个区域是否有交集。
    *   **`ST_DWithin`**: 查找某个点附近一定距离内的所有自定义地理对象。

#### 4.3. 地理围栏 (Geofencing)
*   **FR4.3.1 (围栏创建)**: 提供API供其他服务创建一个与业务对象关联的地理围栏（通常是圆形或多边形）。
*   **FR4.3.2 (事件处理)**:
    *   **客户端上报**: 提供API供客户端上报其当前位置。本服务检查该位置是否触发了任何围栏的进入/离开事件。
    *   **第三方回调**: (可选) 提供Webhook端点接收来自第三方地理围栏服务（如`Geofency`）的事件。
*   **FR4.3.3 (事件发布)**: 一旦检测到进入/离开事件，系统必须发布一个标准化的`GeofenceTransitionEvent`到消息队列，供`routines-service`等消费。

#### 4.4. 成本控制与用量追踪
*   **FR4.4.1 (结果缓存)**: 必须对来自外部地图API的、可重复查询的结果进行缓存（Redis）。缓存Key必须标准化且唯一。
*   **FR4.4.2 (用量记录)**: 系统必须记录对每个外部地图API的调用次数，并能按提供商、API类型进行聚合。
*   **FR4.4.3 (预算告警)**: 管理员可以为每个提供商设置月度调用预算。当用量接近或超过预算时，系统必须发送告警。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC API接口 (S2S)
*   **Package**: `hina.vip.location.v1`
*   **认证**: 严格的S2S认证 (mTLS + 服务级JWT)。
*   **核心RPC**:
    ```protobuf
    service LocationService {
      // Map Provider Proxy APIs
      rpc Geocode(GeocodeRequest) returns (GeocodeResponse);
      rpc ReverseGeocode(ReverseGeocodeRequest) returns (ReverseGeocodeResponse);
      rpc SearchPOI(SearchPOIRequest) returns (SearchPOIResponse);
      rpc GetRoute(RouteRequest) returns (RouteResponse);
      
      // Custom Geospatial Data APIs
      rpc CreateGeoShape(CreateGeoShapeRequest) returns (GeoShapeResponse);
      rpc GetGeoShapes(GetGeoShapesRequest) returns (GetGeoShapesResponse);
      rpc CheckPointInShapes(CheckPointInShapesRequest) returns (CheckPointInShapesResponse);
      
      // Geofencing APIs
      rpc CreateGeofence(CreateGeofenceRequest) returns (GeofenceResponse);
      rpc UpdateUserLocation(UpdateUserLocationRequest) returns (google.protobuf.Empty); // For client-side geofencing
    }
    ```

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL + PostGIS)
*   **`custom_geo_shapes`**:
    *   `id (PK)`, `owner_id` (关联的业务实体ID), `owner_type`
    *   `shape_type` (`POLYGON`, `CIRCLE`)
    *   **`geom (GEOMETRY)`**: 存储地理空间对象的核心字段。**必须在此列上创建GIST索引**。
    *   `metadata (JSONB)`
*   **`geofences`**:
    *   `id (PK)`, `shape_id (FK)`, `associated_business_id`, `is_active`.
*   **`provider_configs`**: `id`, `provider_name`, `api_endpoint`, `encrypted_api_key`.

#### 6.2. 数据存储
*   **数据库**: **必须使用 PostgreSQL 并启用 PostGIS 扩展**。PostGIS提供了强大的、符合OGC标准的地理空间数据类型和函数，是实现本服务核心功能的关键。
*   **缓存**: Redis。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**:
    *   内部查询（如`CheckPointInShapes`）P99延迟应 `< 50ms`。
    *   代理API（如`Geocode`）的P99延迟（不含外部API调用时间）应 `< 30ms`。
*   **缓存命中率**: 目标 > 80% for common queries。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **容错**: 必须实现**FR4.1.3**中定义的、基于熔断器的故障降级机制，这是保证高可用的关键。

#### 7.3. 可扩展性需求
*   服务应为无状态，易于水平扩展。
*   PostgreSQL数据库可通过读写分离和分区进行扩展。

#### 7.4. 安全性需求
*   **API Key安全**: 严格保护所有地图服务提供商的API Key，必须加密存储。
*   **S2S认证**: 保护内部API。
*   **隐私**: 不应无故存储用户的精确位置轨迹。对外部API的调用，应尽量避免传递用户PII。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: **PostgreSQL + PostGIS** 是强制性选择。
*   **核心架构**:
    *   采用“**适配器(Adapter)**”模式，为每个地图服务提供商实现统一的`MapProvider`接口。
    *   “**路由(Router)**”和“**熔断器(Circuit Breaker)**”是保证服务灵活性和可靠性的核心组件。
*   **地理空间库**: 使用成熟的Go地理空间库（如`twpayne/go-geom`）来处理GeoJSON和几何对象。

---
这份版本2.0的SRS文档为`location-service`构建了一个现代化、高可用、功能丰富的地理信息中台。它通过多提供商抽象、自定义地理数据管理和地理围栏能力，为CINA.CLUB平台所有LBS功能提供了坚实、经济且灵活的基础设施。