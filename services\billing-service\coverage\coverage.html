
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>client: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">cina.club/services/billing-service/internal/adapter/client/payment_client.go (0.0%)</option>
				
				<option value="file1">cina.club/services/billing-service/internal/adapter/client/user_core_client.go (0.0%)</option>
				
				<option value="file2">cina.club/services/billing-service/internal/adapter/event/payment_consumer.go (0.0%)</option>
				
				<option value="file3">cina.club/services/billing-service/internal/adapter/grpc/handler.go (0.0%)</option>
				
				<option value="file4">cina.club/services/billing-service/internal/adapter/http/handler.go (1.1%)</option>
				
				<option value="file5">cina.club/services/billing-service/internal/adapter/repository/repository.go (0.0%)</option>
				
				<option value="file6">cina.club/services/billing-service/internal/application/command/billing_command_service.go (42.4%)</option>
				
				<option value="file7">cina.club/services/billing-service/internal/application/query/billing_query_service.go (45.9%)</option>
				
				<option value="file8">cina.club/services/billing-service/internal/domain/aggregate/invoice_aggregate.go (88.2%)</option>
				
				<option value="file9">cina.club/services/billing-service/internal/domain/aggregate/subscription_aggregate.go (18.9%)</option>
				
				<option value="file10">cina.club/services/billing-service/internal/domain/service/pricing_strategy.go (89.3%)</option>
				
				<option value="file11">cina.club/services/billing-service/internal/domain/service/proration_service.go (48.5%)</option>
				
				<option value="file12">cina.club/services/billing-service/pkg/config/config.go (97.3%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">no coverage</span>
				<span class="cov1">low coverage</span>
				<span class="cov2">*</span>
				<span class="cov3">*</span>
				<span class="cov4">*</span>
				<span class="cov5">*</span>
				<span class="cov6">*</span>
				<span class="cov7">*</span>
				<span class="cov8">*</span>
				<span class="cov9">*</span>
				<span class="cov10">high coverage</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package client

import (
        "context"
        "time"

        "github.com/google/uuid"
        "github.com/shopspring/decimal"
)

// PaymentClient defines the interface for payment service communication
type PaymentClient interface {
        InitiatePayment(ctx context.Context, req *InitiatePaymentRequest) (*InitiatePaymentResponse, error)
        ProcessRefund(ctx context.Context, req *ProcessRefundRequest) (*ProcessRefundResponse, error)
        GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatusResponse, error)
}

// InitiatePaymentRequest represents a payment initiation request
type InitiatePaymentRequest struct {
        InvoiceID     uuid.UUID              `json:"invoice_id"`
        UserID        uuid.UUID              `json:"user_id"`
        Amount        decimal.Decimal        `json:"amount"`
        Currency      string                 `json:"currency"`
        Description   string                 `json:"description"`
        PaymentMethod string                 `json:"payment_method"`
        Metadata      map[string]interface{} `json:"metadata"`
}

// InitiatePaymentResponse represents a payment initiation response
type InitiatePaymentResponse struct {
        PaymentID     string                 `json:"payment_id"`
        Status        string                 `json:"status"`
        PaymentURL    string                 `json:"payment_url,omitempty"`
        PaymentParams map[string]interface{} `json:"payment_params,omitempty"`
        ExpiresAt     time.Time              `json:"expires_at"`
}

// ProcessRefundRequest represents a refund request
type ProcessRefundRequest struct {
        PaymentID string                 `json:"payment_id"`
        InvoiceID uuid.UUID              `json:"invoice_id"`
        Amount    decimal.Decimal        `json:"amount"`
        Reason    string                 `json:"reason"`
        Metadata  map[string]interface{} `json:"metadata"`
}

// ProcessRefundResponse represents a refund response
type ProcessRefundResponse struct {
        RefundID    string    `json:"refund_id"`
        Status      string    `json:"status"`
        ProcessedAt time.Time `json:"processed_at"`
}

// PaymentStatusResponse represents payment status information
type PaymentStatusResponse struct {
        PaymentID     string          `json:"payment_id"`
        Status        string          `json:"status"`
        Amount        decimal.Decimal `json:"amount"`
        Currency      string          `json:"currency"`
        ProcessedAt   *time.Time      `json:"processed_at,omitempty"`
        FailureReason *string         `json:"failure_reason,omitempty"`
}

// paymentClient implements PaymentClient
type paymentClient struct {
        address string
        timeout time.Duration
}

// NewPaymentClient creates a new payment client
func NewPaymentClient(address string) PaymentClient <span class="cov0" title="0">{
        return &amp;paymentClient{
                address: address,
                timeout: 30 * time.Second,
        }
}</span>

// InitiatePayment initiates a payment through the payment service
func (c *paymentClient) InitiatePayment(ctx context.Context, req *InitiatePaymentRequest) (*InitiatePaymentResponse, error) <span class="cov0" title="0">{
        // TODO: Implement actual gRPC call to payment service
        // For now, return a mock response
        return &amp;InitiatePaymentResponse{
                PaymentID:  uuid.New().String(),
                Status:     "PENDING",
                PaymentURL: "https://payment.example.com/pay/mock-id",
                ExpiresAt:  time.Now().Add(30 * time.Minute),
        }, nil
}</span>

// ProcessRefund processes a refund through the payment service
func (c *paymentClient) ProcessRefund(ctx context.Context, req *ProcessRefundRequest) (*ProcessRefundResponse, error) <span class="cov0" title="0">{
        // TODO: Implement actual gRPC call to payment service
        // For now, return a mock response
        return &amp;ProcessRefundResponse{
                RefundID:    uuid.New().String(),
                Status:      "PROCESSED",
                ProcessedAt: time.Now(),
        }, nil
}</span>

// GetPaymentStatus retrieves payment status from the payment service
func (c *paymentClient) GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatusResponse, error) <span class="cov0" title="0">{
        // TODO: Implement actual gRPC call to payment service
        // For now, return a mock response
        return &amp;PaymentStatusResponse{
                PaymentID:   paymentID,
                Status:      "COMPLETED",
                Amount:      decimal.NewFromFloat(9.99),
                Currency:    "USD",
                ProcessedAt: func() *time.Time </span><span class="cov0" title="0">{ t := time.Now(); return &amp;t }</span>(),
        }, nil
}
</pre>
		
		<pre class="file" id="file1" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package client

import (
        "context"
        "time"

        "github.com/google/uuid"
)

// UserCoreClient defines the interface for user core service communication
type UserCoreClient interface {
        UpdateUserMembershipStatus(ctx context.Context, req *UpdateMembershipStatusRequest) error
        GetUserInfo(ctx context.Context, userID uuid.UUID) (*UserInfo, error)
        NotifySubscriptionChange(ctx context.Context, req *SubscriptionChangeNotification) error
}

// UpdateMembershipStatusRequest represents a request to update user membership status
type UpdateMembershipStatusRequest struct {
        UserID         uuid.UUID              `json:"user_id"`
        MembershipType string                 `json:"membership_type"`
        Status         string                 `json:"status"`
        ExpiresAt      *time.Time             `json:"expires_at,omitempty"`
        Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// UserInfo represents user information
type UserInfo struct {
        UserID    uuid.UUID `json:"user_id"`
        Email     string    `json:"email"`
        Name      string    `json:"name"`
        Timezone  string    `json:"timezone"`
        Country   string    `json:"country"`
        CreatedAt time.Time `json:"created_at"`
}

// SubscriptionChangeNotification represents a subscription change notification
type SubscriptionChangeNotification struct {
        UserID         uuid.UUID              `json:"user_id"`
        SubscriptionID uuid.UUID              `json:"subscription_id"`
        ChangeType     string                 `json:"change_type"` // "CREATED", "UPDATED", "CANCELLED"
        OldStatus      string                 `json:"old_status,omitempty"`
        NewStatus      string                 `json:"new_status"`
        EffectiveDate  time.Time              `json:"effective_date"`
        Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// userCoreClient implements UserCoreClient
type userCoreClient struct {
        address string
        timeout time.Duration
}

// NewUserCoreClient creates a new user core client
func NewUserCoreClient(address string) UserCoreClient <span class="cov0" title="0">{
        return &amp;userCoreClient{
                address: address,
                timeout: 30 * time.Second,
        }
}</span>

// UpdateUserMembershipStatus updates the user's membership status
func (c *userCoreClient) UpdateUserMembershipStatus(ctx context.Context, req *UpdateMembershipStatusRequest) error <span class="cov0" title="0">{
        // TODO: Implement actual gRPC call to user core service
        // For now, this is a stub implementation
        return nil
}</span>

// GetUserInfo retrieves user information from the user core service
func (c *userCoreClient) GetUserInfo(ctx context.Context, userID uuid.UUID) (*UserInfo, error) <span class="cov0" title="0">{
        // TODO: Implement actual gRPC call to user core service
        // For now, return a mock response
        return &amp;UserInfo{
                UserID:    userID,
                Email:     "<EMAIL>",
                Name:      "Mock User",
                Timezone:  "UTC",
                Country:   "US",
                CreatedAt: time.Now().AddDate(-1, 0, 0), // 1 year ago
        }, nil
}</span>

// NotifySubscriptionChange sends a subscription change notification to user core service
func (c *userCoreClient) NotifySubscriptionChange(ctx context.Context, req *SubscriptionChangeNotification) error <span class="cov0" title="0">{
        // TODO: Implement actual gRPC call to user core service
        // For now, this is a stub implementation
        return nil
}</span>
</pre>
		
		<pre class="file" id="file2" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package event

import (
        "context"
        "encoding/json"
        "fmt"
        "time"

        "cina.club/pkg/logger"
        "github.com/google/uuid"
        "github.com/segmentio/kafka-go"
        "github.com/shopspring/decimal"
)

// PaymentEvent represents a payment-related event
type PaymentEvent struct {
        EventID   string                 `json:"event_id"`
        EventType string                 `json:"event_type"`
        PaymentID string                 `json:"payment_id"`
        InvoiceID uuid.UUID              `json:"invoice_id"`
        UserID    uuid.UUID              `json:"user_id"`
        Amount    decimal.Decimal        `json:"amount"`
        Currency  string                 `json:"currency"`
        Status    string                 `json:"status"`
        Timestamp time.Time              `json:"timestamp"`
        Metadata  map[string]interface{} `json:"metadata"`
}

// PaymentConsumer consumes payment events from Kafka
type PaymentConsumer struct {
        brokers       []string
        topic         string
        consumerGroup string
        reader        *kafka.Reader
        handler       PaymentEventHandler
        logger        logger.Logger
        running       bool
}

// PaymentEventHandler defines the interface for handling payment events
type PaymentEventHandler interface {
        HandlePaymentSucceeded(ctx context.Context, event PaymentEvent) error
        HandlePaymentFailed(ctx context.Context, event PaymentEvent) error
        HandlePaymentCancelled(ctx context.Context, event PaymentEvent) error
}

// NewPaymentConsumer creates a new payment event consumer
func NewPaymentConsumer(brokers []string, topic, consumerGroup string, handler PaymentEventHandler, log logger.Logger) *PaymentConsumer <span class="cov0" title="0">{
        return &amp;PaymentConsumer{
                brokers:       brokers,
                topic:         topic,
                consumerGroup: consumerGroup,
                handler:       handler,
                logger:        log,
                running:       false,
        }
}</span>

// Start starts the payment event consumer
func (c *PaymentConsumer) Start(ctx context.Context) error <span class="cov0" title="0">{
        if c.running </span><span class="cov0" title="0">{
                return fmt.Errorf("consumer is already running")
        }</span>

        <span class="cov0" title="0">c.reader = kafka.NewReader(kafka.ReaderConfig{
                Brokers:     c.brokers,
                Topic:       c.topic,
                GroupID:     c.consumerGroup,
                StartOffset: kafka.LastOffset,
                MinBytes:    10e3, // 10KB
                MaxBytes:    10e6, // 10MB
        })

        c.running = true
        c.logger.Info(ctx, "Starting payment event consumer",
                "topic", c.topic,
                "consumer_group", c.consumerGroup,
                "brokers", c.brokers,
        )

        for c.running </span><span class="cov0" title="0">{
                message, err := c.reader.ReadMessage(ctx)
                if err != nil </span><span class="cov0" title="0">{
                        if !c.running </span><span class="cov0" title="0">{
                                break</span>
                        }
                        <span class="cov0" title="0">c.logger.Error(ctx, "Failed to read message", "error", err)
                        continue</span>
                }

                <span class="cov0" title="0">if err := c.processMessage(ctx, message); err != nil </span><span class="cov0" title="0">{
                        c.logger.Error(ctx, "Failed to process message", "error", err, "offset", message.Offset)
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// Stop stops the payment event consumer
func (c *PaymentConsumer) Stop() error <span class="cov0" title="0">{
        c.running = false
        if c.reader != nil </span><span class="cov0" title="0">{
                return c.reader.Close()
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// processMessage processes a single Kafka message
func (c *PaymentConsumer) processMessage(ctx context.Context, message kafka.Message) error <span class="cov0" title="0">{
        var event PaymentEvent
        if err := json.Unmarshal(message.Value, &amp;event); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to unmarshal payment event: %w", err)
        }</span>

        <span class="cov0" title="0">c.logger.Debug(ctx, "Processing payment event",
                "event_id", event.EventID,
                "event_type", event.EventType,
                "payment_id", event.PaymentID,
                "invoice_id", event.InvoiceID,
        )

        switch event.EventType </span>{
        case "payment.succeeded":<span class="cov0" title="0">
                return c.handler.HandlePaymentSucceeded(ctx, event)</span>
        case "payment.failed":<span class="cov0" title="0">
                return c.handler.HandlePaymentFailed(ctx, event)</span>
        case "payment.cancelled":<span class="cov0" title="0">
                return c.handler.HandlePaymentCancelled(ctx, event)</span>
        default:<span class="cov0" title="0">
                c.logger.Warn(ctx, "Unknown payment event type", "event_type", event.EventType)
                return nil</span> // Don't return error for unknown events
        }
}
</pre>
		
		<pre class="file" id="file3" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package grpc

import (
        "cina.club/pkg/logger"
)

// Handler handles gRPC requests for the billing service
type Handler struct {
        commandService interface{} // TODO: Replace with actual command service interface
        queryService   interface{} // TODO: Replace with actual query service interface
        logger         logger.Logger
}

// NewHandler creates a new gRPC handler
func NewHandler(commandService, queryService interface{}, log logger.Logger) *Handler <span class="cov0" title="0">{
        return &amp;Handler{
                commandService: commandService,
                queryService:   queryService,
                logger:         log,
        }
}</span>

// TODO: Implement gRPC service methods based on protobuf definitions
// This is a placeholder until the protobuf definitions are created
</pre>
		
		<pre class="file" id="file4" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package http

import (
        "net/http"
        "strconv"
        "time"

        "cina.club/pkg/logger"
        "cina.club/services/billing-service/internal/application/command"
        "cina.club/services/billing-service/internal/application/query"
        "github.com/gin-gonic/gin"
        "github.com/google/uuid"
)

// Handler handles HTTP requests for the billing service
type Handler struct {
        commandService *command.BillingCommandService
        queryService   *query.BillingQueryService
        logger         logger.Logger
}

// NewHandler creates a new HTTP handler
func NewHandler(commandService *command.BillingCommandService, queryService *query.BillingQueryService, log logger.Logger) *Handler <span class="cov8" title="1">{
        return &amp;Handler{
                commandService: commandService,
                queryService:   queryService,
                logger:         log,
        }
}</span>

// ListProducts handles GET /api/v1/billing/products
func (h *Handler) ListProducts(c *gin.Context) <span class="cov0" title="0">{
        var isActive *bool
        if activeStr := c.Query("active"); activeStr != "" </span><span class="cov0" title="0">{
                active := activeStr == "true"
                isActive = &amp;active
        }</span>

        <span class="cov0" title="0">limit := 50 // default
        if limitStr := c.Query("limit"); limitStr != "" </span><span class="cov0" title="0">{
                if l, err := strconv.Atoi(limitStr); err == nil &amp;&amp; l &gt; 0 &amp;&amp; l &lt;= 100 </span><span class="cov0" title="0">{
                        limit = l
                }</span>
        }

        <span class="cov0" title="0">offset := 0
        if offsetStr := c.Query("offset"); offsetStr != "" </span><span class="cov0" title="0">{
                if o, err := strconv.Atoi(offsetStr); err == nil &amp;&amp; o &gt;= 0 </span><span class="cov0" title="0">{
                        offset = o
                }</span>
        }

        <span class="cov0" title="0">products, err := h.queryService.GetProducts(c.Request.Context(), isActive, limit, offset)
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Error(c.Request.Context(), "Failed to list products", "error", err)
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve products"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "products": products,
                "count":    len(products),
        })</span>
}

// CreateSubscription handles POST /api/v1/billing/subscriptions
func (h *Handler) CreateSubscription(c *gin.Context) <span class="cov0" title="0">{
        var req struct {
                UserID   uuid.UUID              `json:"user_id" binding:"required"`
                PriceID  uuid.UUID              `json:"price_id" binding:"required"`
                TrialEnd *time.Time             `json:"trial_end,omitempty"`
                Metadata map[string]interface{} `json:"metadata,omitempty"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // TODO: Implement subscription creation in command service
        <span class="cov0" title="0">h.logger.Info(c.Request.Context(), "Creating subscription",
                "user_id", req.UserID,
                "price_id", req.PriceID,
        )

        // For now, return a mock response
        c.JSON(http.StatusCreated, gin.H{
                "message":         "Subscription creation initiated",
                "subscription_id": uuid.New(),
                "status":          "TRIALING",
        })</span>
}

// GetUserSubscriptions handles GET /api/v1/billing/me/subscriptions
func (h *Handler) GetUserSubscriptions(c *gin.Context) <span class="cov0" title="0">{
        userIDHeader := c.GetHeader("X-User-ID")
        if userIDHeader == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID required"})
                return
        }</span>

        <span class="cov0" title="0">userID, err := uuid.Parse(userIDHeader)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
                return
        }</span>

        <span class="cov0" title="0">var status []string
        if statusParam := c.QueryArray("status"); len(statusParam) &gt; 0 </span><span class="cov0" title="0">{
                status = statusParam
        }</span>

        <span class="cov0" title="0">subscriptions, err := h.queryService.GetUserSubscriptions(c.Request.Context(), userID, status)
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Error(c.Request.Context(), "Failed to get user subscriptions", "error", err)
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscriptions"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "subscriptions": subscriptions,
                "count":         len(subscriptions),
        })</span>
}

// GetUserInvoices handles GET /api/v1/billing/me/invoices
func (h *Handler) GetUserInvoices(c *gin.Context) <span class="cov0" title="0">{
        userIDHeader := c.GetHeader("X-User-ID")
        if userIDHeader == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID required"})
                return
        }</span>

        <span class="cov0" title="0">userID, err := uuid.Parse(userIDHeader)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
                return
        }</span>

        <span class="cov0" title="0">var status []string
        if statusParam := c.QueryArray("status"); len(statusParam) &gt; 0 </span><span class="cov0" title="0">{
                status = statusParam
        }</span>

        <span class="cov0" title="0">limit := 20 // default
        if limitStr := c.Query("limit"); limitStr != "" </span><span class="cov0" title="0">{
                if l, err := strconv.Atoi(limitStr); err == nil &amp;&amp; l &gt; 0 &amp;&amp; l &lt;= 100 </span><span class="cov0" title="0">{
                        limit = l
                }</span>
        }

        <span class="cov0" title="0">offset := 0
        if offsetStr := c.Query("offset"); offsetStr != "" </span><span class="cov0" title="0">{
                if o, err := strconv.Atoi(offsetStr); err == nil &amp;&amp; o &gt;= 0 </span><span class="cov0" title="0">{
                        offset = o
                }</span>
        }

        <span class="cov0" title="0">invoices, err := h.queryService.GetUserInvoices(c.Request.Context(), userID, status, limit, offset)
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Error(c.Request.Context(), "Failed to get user invoices", "error", err)
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve invoices"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "invoices": invoices,
                "count":    len(invoices),
        })</span>
}

// RecordUsage handles POST /internal/usage/record
func (h *Handler) RecordUsage(c *gin.Context) <span class="cov0" title="0">{
        var req struct {
                UserID         uuid.UUID              `json:"user_id" binding:"required"`
                FeatureKey     string                 `json:"feature_key" binding:"required"`
                Quantity       int64                  `json:"quantity" binding:"required,min=1"`
                IdempotencyKey string                 `json:"idempotency_key" binding:"required"`
                Metadata       map[string]interface{} `json:"metadata,omitempty"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // TODO: Implement usage recording in command service
        <span class="cov0" title="0">h.logger.Info(c.Request.Context(), "Recording usage",
                "user_id", req.UserID,
                "feature_key", req.FeatureKey,
                "quantity", req.Quantity,
                "idempotency_key", req.IdempotencyKey,
        )

        // For now, return a mock response
        c.JSON(http.StatusCreated, gin.H{
                "message":     "Usage recorded successfully",
                "usage_id":    uuid.New(),
                "recorded_at": time.Now(),
                "feature_key": req.FeatureKey,
                "quantity":    req.Quantity,
        })</span>
}

// CheckAccess handles POST /internal/access-check
func (h *Handler) CheckAccess(c *gin.Context) <span class="cov0" title="0">{
        var req struct {
                UserID     uuid.UUID `json:"user_id" binding:"required"`
                FeatureKey string    `json:"feature_key" binding:"required"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // TODO: Implement access check logic with Redis caching
        <span class="cov0" title="0">h.logger.Debug(c.Request.Context(), "Checking access",
                "user_id", req.UserID,
                "feature_key", req.FeatureKey,
        )

        // For now, return a mock response (would normally check subscriptions and quotas)
        c.JSON(http.StatusOK, gin.H{
                "has_access":      true,
                "subscription_id": uuid.New(),
                "remaining_quota": 1000,
                "quota_reset_at":  time.Now().Add(24 * time.Hour),
                "access_level":    "FULL",
        })</span>
}

// HealthCheck handles GET /health
func (h *Handler) HealthCheck(c *gin.Context) <span class="cov0" title="0">{
        c.JSON(http.StatusOK, gin.H{
                "status":    "healthy",
                "timestamp": time.Now(),
                "service":   "billing-service",
        })
}</span>

// RegisterRoutes registers all HTTP routes
func (h *Handler) RegisterRoutes(router *gin.Engine) <span class="cov0" title="0">{
        // Health check
        router.GET("/health", h.HealthCheck)

        // Public billing API
        v1 := router.Group("/api/v1/billing")
        </span><span class="cov0" title="0">{
                v1.GET("/products", h.ListProducts)
                v1.POST("/subscriptions", h.CreateSubscription)
                v1.GET("/me/subscriptions", h.GetUserSubscriptions)
                v1.GET("/me/invoices", h.GetUserInvoices)
        }</span>

        // Internal API
        <span class="cov0" title="0">internal := router.Group("/internal")
        </span><span class="cov0" title="0">{
                internal.POST("/usage/record", h.RecordUsage)
                internal.POST("/access-check", h.CheckAccess)
        }</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package repository

import (
        "context"
        "time"

        "cina.club/services/billing-service/internal/application/port"
        "cina.club/services/billing-service/internal/domain/model"
        "github.com/google/uuid"
        "github.com/jackc/pgx/v5/pgxpool"
)

// repository implements the Repository interface
type repository struct {
        db *pgxpool.Pool
}

// NewRepository creates a new repository instance
func NewRepository(db *pgxpool.Pool) port.Repository <span class="cov0" title="0">{
        return &amp;repository{
                db: db,
        }
}</span>

// Product repository methods
func (r *repository) CreateProduct(ctx context.Context, product *model.Product) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

func (r *repository) GetProduct(ctx context.Context, id uuid.UUID) (*model.Product, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) GetProductByName(ctx context.Context, name string) (*model.Product, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) ListProducts(ctx context.Context, isActive *bool, limit, offset int) ([]model.Product, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) UpdateProduct(ctx context.Context, product *model.Product) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

func (r *repository) DeleteProduct(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

// Price repository methods
func (r *repository) CreatePrice(ctx context.Context, price *model.Price) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

func (r *repository) GetPrice(ctx context.Context, id uuid.UUID) (*model.Price, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) GetPricesByProduct(ctx context.Context, productID uuid.UUID, isActive *bool) ([]model.Price, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) ListPrices(ctx context.Context, isActive *bool, limit, offset int) ([]model.Price, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) UpdatePrice(ctx context.Context, price *model.Price) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

func (r *repository) DeletePrice(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

// Feature repository methods
func (r *repository) CreateFeature(ctx context.Context, feature *model.Feature) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

func (r *repository) GetFeature(ctx context.Context, id uuid.UUID) (*model.Feature, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) GetFeatureByKey(ctx context.Context, key string) (*model.Feature, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) ListFeatures(ctx context.Context, limit, offset int) ([]model.Feature, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

func (r *repository) UpdateFeature(ctx context.Context, feature *model.Feature) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

func (r *repository) DeleteFeature(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil
}</span>

func (r *repository) GetFeaturesByProduct(ctx context.Context, productID uuid.UUID) ([]model.Feature, error) <span class="cov0" title="0">{
        // TODO: Implement actual database operations
        return nil, nil
}</span>

// Subscription repository methods - placeholder implementations
func (r *repository) CreateSubscription(ctx context.Context, subscription *model.Subscription) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetSubscription(ctx context.Context, id uuid.UUID) (*model.Subscription, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetSubscriptionsByUser(ctx context.Context, userID uuid.UUID, status []string) ([]model.Subscription, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) UpdateSubscription(ctx context.Context, subscription *model.Subscription) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) UpdateSubscriptionWithOptimisticLock(ctx context.Context, subscription *model.Subscription, expectedVersion int) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) DeleteSubscription(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetSubscriptionsDueForBilling(ctx context.Context, dueDate time.Time) ([]model.Subscription, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetUsersDueForBilling(ctx context.Context, dueDate time.Time) ([]uuid.UUID, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetActiveSubscriptionsByFeature(ctx context.Context, userID uuid.UUID, featureKey string) ([]model.Subscription, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

// Invoice repository methods - placeholder implementations
func (r *repository) CreateInvoice(ctx context.Context, invoice *model.Invoice) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetInvoice(ctx context.Context, id uuid.UUID) (*model.Invoice, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetInvoiceByNumber(ctx context.Context, invoiceNumber string) (*model.Invoice, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetInvoicesByUser(ctx context.Context, userID uuid.UUID, status []string, limit, offset int) ([]model.Invoice, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetInvoicesBySubscription(ctx context.Context, subscriptionID uuid.UUID, limit, offset int) ([]model.Invoice, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) UpdateInvoice(ctx context.Context, invoice *model.Invoice) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) DeleteInvoice(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetPendingInvoices(ctx context.Context, limit, offset int) ([]model.Invoice, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetOverdueInvoices(ctx context.Context, asOfDate time.Time, limit, offset int) ([]model.Invoice, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) CreateInvoiceLineItem(ctx context.Context, lineItem *model.InvoiceLineItem) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetInvoiceLineItems(ctx context.Context, invoiceID uuid.UUID) ([]model.InvoiceLineItem, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) UpdateInvoiceLineItem(ctx context.Context, lineItem *model.InvoiceLineItem) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) DeleteInvoiceLineItem(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

// Usage repository methods - placeholder implementations
func (r *repository) CreateUsageRecord(ctx context.Context, record *model.UsageRecord) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetUsageRecord(ctx context.Context, id uuid.UUID) (*model.UsageRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetUsageRecordsByUser(ctx context.Context, userID uuid.UUID, featureID *uuid.UUID, from, to time.Time) ([]model.UsageRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetUsageRecordsByFeature(ctx context.Context, featureID uuid.UUID, from, to time.Time) ([]model.UsageRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetUnbilledUsageRecords(ctx context.Context, userID uuid.UUID, from, to time.Time) ([]model.UsageRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) UpdateUsageRecord(ctx context.Context, record *model.UsageRecord) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) DeleteUsageRecord(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) MarkUsageRecordsAsBilled(ctx context.Context, recordIDs []uuid.UUID, invoiceID uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetUsageRecordByIdempotencyKey(ctx context.Context, key string) (*model.UsageRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

// Coupon repository methods - placeholder implementations
func (r *repository) CreateCoupon(ctx context.Context, coupon *model.Coupon) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetCoupon(ctx context.Context, id uuid.UUID) (*model.Coupon, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetCouponByCode(ctx context.Context, code string) (*model.Coupon, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) ListCoupons(ctx context.Context, isActive *bool, limit, offset int) ([]model.Coupon, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) UpdateCoupon(ctx context.Context, coupon *model.Coupon) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) DeleteCoupon(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) IncrementCouponUsage(ctx context.Context, couponID uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

// Revenue share repository methods - placeholder implementations
func (r *repository) CreateRevenueShareRecord(ctx context.Context, record *model.RevenueSharePendingRecord) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) GetRevenueShareRecord(ctx context.Context, id uuid.UUID) (*model.RevenueSharePendingRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetPendingRevenueShares(ctx context.Context, limit, offset int) ([]model.RevenueSharePendingRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetRevenueSharesByInvoice(ctx context.Context, invoiceID uuid.UUID) ([]model.RevenueSharePendingRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) GetRevenueSharesByCreator(ctx context.Context, creatorID uuid.UUID, status []string, limit, offset int) ([]model.RevenueSharePendingRecord, error) <span class="cov0" title="0">{
        return nil, nil
}</span>

func (r *repository) UpdateRevenueShareRecord(ctx context.Context, record *model.RevenueSharePendingRecord) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) DeleteRevenueShareRecord(ctx context.Context, id uuid.UUID) error <span class="cov0" title="0">{
        return nil
}</span>

func (r *repository) MarkRevenueShareAsProcessed(ctx context.Context, recordID uuid.UUID, processedAt time.Time) error <span class="cov0" title="0">{
        return nil
}</span>
</pre>
		
		<pre class="file" id="file6" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 15:30:00
// Modified: 2025-01-23 15:30:00

package command

import (
        "context"
        "encoding/json"
        "fmt"
        "time"

        "cina.club/pkg/logger"
        "cina.club/services/billing-service/internal/adapter/client"
        "cina.club/services/billing-service/internal/adapter/event"
        "cina.club/services/billing-service/internal/application/port"
        "cina.club/services/billing-service/internal/domain/aggregate"
        "cina.club/services/billing-service/internal/domain/model"
        "cina.club/services/billing-service/internal/domain/service"
        "github.com/google/uuid"
        "github.com/segmentio/kafka-go"
        "github.com/shopspring/decimal"
)

// BillingCommandService handles all write operations for billing
type BillingCommandService struct {
        repo             port.Repository
        paymentClient    client.PaymentClient
        userCoreClient   client.UserCoreClient
        pricingService   service.PricingService
        prorationService service.ProrationService
        kafkaWriter      *kafka.Writer
        logger           logger.Logger
}

// CreateSubscriptionRequest represents a subscription creation request
type CreateSubscriptionRequest struct {
        UserID     uuid.UUID `json:"user_id"`
        PriceID    uuid.UUID `json:"price_id"`
        TrialDays  int       `json:"trial_days"`
        CouponCode string    `json:"coupon_code,omitempty"`
}

// UpgradeSubscriptionRequest represents a subscription upgrade request
type UpgradeSubscriptionRequest struct {
        SubscriptionID uuid.UUID `json:"subscription_id"`
        NewPriceID     uuid.UUID `json:"new_price_id"`
}

// CancelSubscriptionRequest represents a subscription cancellation request
type CancelSubscriptionRequest struct {
        SubscriptionID    uuid.UUID `json:"subscription_id"`
        CancelAtPeriodEnd bool      `json:"cancel_at_period_end"`
        Reason            string    `json:"reason,omitempty"`
}

// RecordUsageRequest represents a usage recording request
type RecordUsageRequest struct {
        UserID         uuid.UUID `json:"user_id"`
        FeatureKey     string    `json:"feature_key"`
        Quantity       int64     `json:"quantity"`
        IdempotencyKey string    `json:"idempotency_key"`
        Timestamp      time.Time `json:"timestamp,omitempty"`
}

// NewBillingCommandService creates a new billing command service
func NewBillingCommandService(
        repo port.Repository,
        paymentClient client.PaymentClient,
        userCoreClient client.UserCoreClient,
        pricingService service.PricingService,
        prorationService service.ProrationService,
        kafkaWriter *kafka.Writer,
        log logger.Logger,
) *BillingCommandService <span class="cov10" title="9">{
        return &amp;BillingCommandService{
                repo:             repo,
                paymentClient:    paymentClient,
                userCoreClient:   userCoreClient,
                pricingService:   pricingService,
                prorationService: prorationService,
                kafkaWriter:      kafkaWriter,
                logger:           log,
        }
}</span>

// CreateSubscription creates a new subscription for a user
func (s *BillingCommandService) CreateSubscription(ctx context.Context, req CreateSubscriptionRequest) (*model.Subscription, error) <span class="cov5" title="3">{
        s.logger.Info(ctx, "Creating subscription", "user_id", req.UserID, "price_id", req.PriceID)

        // Get price information
        price, err := s.repo.GetPrice(ctx, req.PriceID)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get price: %w", err)
        }</span>

        <span class="cov5" title="3">if !price.IsActive </span><span class="cov1" title="1">{
                return nil, fmt.Errorf("price is not active")
        }</span>

        // Create subscription aggregate
        <span class="cov3" title="2">subscriptionAgg, err := aggregate.NewSubscriptionAggregate(req.UserID, req.PriceID, price, req.TrialDays)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to create subscription aggregate: %w", err)
        }</span>

        <span class="cov3" title="2">subscription := subscriptionAgg.GetSubscription()

        // Save subscription
        if err := s.repo.CreateSubscription(ctx, subscription); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to save subscription: %w", err)
        }</span>

        // Create initial invoice if not in trial
        <span class="cov3" title="2">if subscription.Status == model.SubscriptionStatusActive </span><span class="cov1" title="1">{
                invoiceAgg := aggregate.NewInvoiceAggregate(
                        subscription.UserID,
                        &amp;subscription.ID,
                        subscription.CurrentPeriodStart,
                        subscription.CurrentPeriodEnd,
                )

                // Add subscription line item
                description := fmt.Sprintf("%s - %s", price.Product.Name, price.Interval)
                if err := invoiceAgg.AddSubscriptionLineItem(price, description); err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to add subscription line item: %w", err)
                }</span>

                // Apply coupon if provided
                <span class="cov1" title="1">if req.CouponCode != "" </span><span class="cov0" title="0">{
                        coupon, err := s.repo.GetCouponByCode(ctx, req.CouponCode)
                        if err == nil &amp;&amp; coupon != nil </span><span class="cov0" title="0">{
                                if err := invoiceAgg.ApplyCoupon(coupon); err != nil </span><span class="cov0" title="0">{
                                        s.logger.Warn(ctx, "Failed to apply coupon", "error", err)
                                }</span>
                        }
                }

                // Finalize invoice
                <span class="cov1" title="1">if err := invoiceAgg.Finalize(); err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to finalize invoice: %w", err)
                }</span>

                <span class="cov1" title="1">invoice := invoiceAgg.GetInvoice()
                lineItems := invoiceAgg.GetLineItems()

                // Save invoice
                if err := s.repo.CreateInvoice(ctx, invoice); err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to save invoice: %w", err)
                }</span>

                // Save line items
                <span class="cov1" title="1">for _, item := range lineItems </span><span class="cov1" title="1">{
                        if err := s.repo.CreateInvoiceLineItem(ctx, &amp;item); err != nil </span><span class="cov0" title="0">{
                                return nil, fmt.Errorf("failed to save line item: %w", err)
                        }</span>
                }

                // Initiate payment if amount &gt; 0
                <span class="cov1" title="1">if invoice.TotalAmount.GreaterThan(decimal.Zero) </span><span class="cov1" title="1">{
                        // TODO: Implement payment initiation
                        s.logger.Info(ctx, "Payment initiation needed", "invoice_id", invoice.ID, "amount", invoice.TotalAmount)
                }</span>
        }

        // Publish events
        <span class="cov3" title="2">events := subscriptionAgg.GetEvents()
        for _, domainEvent := range events </span><span class="cov3" title="2">{
                if err := s.publishEvent(ctx, domainEvent); err != nil </span><span class="cov3" title="2">{
                        s.logger.Error(ctx, "Failed to publish event", "error", err)
                }</span>
        }

        <span class="cov3" title="2">return subscription, nil</span>
}

// UpgradeSubscription upgrades a subscription to a new price
func (s *BillingCommandService) UpgradeSubscription(ctx context.Context, req UpgradeSubscriptionRequest) error <span class="cov0" title="0">{
        s.logger.Info(ctx, "Upgrading subscription", "subscription_id", req.SubscriptionID, "new_price_id", req.NewPriceID)

        // Get current subscription
        subscription, err := s.repo.GetSubscription(ctx, req.SubscriptionID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get subscription: %w", err)
        }</span>

        // Get current and new prices
        <span class="cov0" title="0">currentPrice, err := s.repo.GetPrice(ctx, subscription.PriceID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get current price: %w", err)
        }</span>

        <span class="cov0" title="0">newPrice, err := s.repo.GetPrice(ctx, req.NewPriceID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get new price: %w", err)
        }</span>

        // Create a new subscription for the new price to calculate proration
        <span class="cov0" title="0">newSubscription := *subscription
        newSubscription.PriceID = req.NewPriceID

        // Calculate proration
        prorationResult, err := s.prorationService.CalculateProration(ctx, subscription, &amp;newSubscription, time.Now())
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to calculate proration: %w", err)
        }</span>

        // Load subscription aggregate
        <span class="cov0" title="0">subscriptionAgg := aggregate.LoadSubscriptionAggregate(subscription, currentPrice)

        // Upgrade subscription
        if err := subscriptionAgg.Upgrade(req.NewPriceID, newPrice, prorationResult.NetAmount); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to upgrade subscription: %w", err)
        }</span>

        // Update subscription with optimistic locking
        <span class="cov0" title="0">updatedSubscription := subscriptionAgg.GetSubscription()
        if err := s.repo.UpdateSubscriptionWithOptimisticLock(ctx, updatedSubscription, subscription.Version); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to update subscription: %w", err)
        }</span>

        // Create proration invoice if needed
        <span class="cov0" title="0">if prorationResult.NetAmount.GreaterThan(decimal.Zero) </span><span class="cov0" title="0">{
                invoiceAgg := aggregate.NewInvoiceAggregate(
                        subscription.UserID,
                        &amp;subscription.ID,
                        time.Now(),
                        time.Now(),
                )

                description := fmt.Sprintf("Upgrade proration: %s to %s", currentPrice.Product.Name, newPrice.Product.Name)
                if err := invoiceAgg.AddUsageLineItem(description, prorationResult.NetAmount, 1, prorationResult.NetAmount, time.Now(), time.Now()); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to add proration line item: %w", err)
                }</span>

                <span class="cov0" title="0">if err := invoiceAgg.Finalize(); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to finalize proration invoice: %w", err)
                }</span>

                <span class="cov0" title="0">invoice := invoiceAgg.GetInvoice()
                if err := s.repo.CreateInvoice(ctx, invoice); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to save proration invoice: %w", err)
                }</span>
        }

        // Publish events
        <span class="cov0" title="0">events := subscriptionAgg.GetEvents()
        for _, domainEvent := range events </span><span class="cov0" title="0">{
                if err := s.publishEvent(ctx, domainEvent); err != nil </span><span class="cov0" title="0">{
                        s.logger.Error(ctx, "Failed to publish event", "error", err)
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// CancelSubscription cancels a subscription
func (s *BillingCommandService) CancelSubscription(ctx context.Context, req CancelSubscriptionRequest) error <span class="cov0" title="0">{
        s.logger.Info(ctx, "Canceling subscription", "subscription_id", req.SubscriptionID, "cancel_at_period_end", req.CancelAtPeriodEnd)

        // Get subscription
        subscription, err := s.repo.GetSubscription(ctx, req.SubscriptionID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get subscription: %w", err)
        }</span>

        // Get price
        <span class="cov0" title="0">price, err := s.repo.GetPrice(ctx, subscription.PriceID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get price: %w", err)
        }</span>

        // Load subscription aggregate
        <span class="cov0" title="0">subscriptionAgg := aggregate.LoadSubscriptionAggregate(subscription, price)

        // Cancel subscription
        if err := subscriptionAgg.Cancel(req.CancelAtPeriodEnd, req.Reason); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to cancel subscription: %w", err)
        }</span>

        // Update subscription
        <span class="cov0" title="0">updatedSubscription := subscriptionAgg.GetSubscription()
        if err := s.repo.UpdateSubscriptionWithOptimisticLock(ctx, updatedSubscription, subscription.Version); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to update subscription: %w", err)
        }</span>

        // Publish events
        <span class="cov0" title="0">events := subscriptionAgg.GetEvents()
        for _, domainEvent := range events </span><span class="cov0" title="0">{
                if err := s.publishEvent(ctx, domainEvent); err != nil </span><span class="cov0" title="0">{
                        s.logger.Error(ctx, "Failed to publish event", "error", err)
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// RecordUsage records usage for a user
func (s *BillingCommandService) RecordUsage(ctx context.Context, req RecordUsageRequest) error <span class="cov3" title="2">{
        // Check for duplicate using idempotency key
        existing, err := s.repo.GetUsageRecordByIdempotencyKey(ctx, req.IdempotencyKey)
        if err == nil &amp;&amp; existing != nil </span><span class="cov1" title="1">{
                return nil // Duplicate request, ignore
        }</span>

        // Get feature
        <span class="cov1" title="1">feature, err := s.repo.GetFeatureByKey(ctx, req.FeatureKey)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get feature: %w", err)
        }</span>

        // Create usage record
        <span class="cov1" title="1">timestamp := req.Timestamp
        if timestamp.IsZero() </span><span class="cov1" title="1">{
                timestamp = time.Now()
        }</span>

        <span class="cov1" title="1">usageRecord := &amp;model.UsageRecord{
                ID:             uuid.New(),
                UserID:         req.UserID,
                FeatureID:      feature.ID,
                Quantity:       req.Quantity,
                Timestamp:      timestamp,
                IdempotencyKey: req.IdempotencyKey,
                CreatedAt:      time.Now(),
        }

        // Save usage record
        if err := s.repo.CreateUsageRecord(ctx, usageRecord); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to save usage record: %w", err)
        }</span>

        <span class="cov1" title="1">s.logger.Info(ctx, "Usage recorded", "user_id", req.UserID, "feature_key", req.FeatureKey, "quantity", req.Quantity)

        return nil</span>
}

// RunBillingCycleForUser runs the billing cycle for a specific user
func (s *BillingCommandService) RunBillingCycleForUser(ctx context.Context, userID string) error <span class="cov3" title="2">{
        s.logger.Info(ctx, "Running billing cycle for user", "user_id", userID)

        userUUID, err := uuid.Parse(userID)
        if err != nil </span><span class="cov1" title="1">{
                return fmt.Errorf("invalid user ID: %w", err)
        }</span>

        // Get subscriptions due for billing
        <span class="cov1" title="1">subscriptions, err := s.repo.GetSubscriptionsDueForBilling(ctx, time.Now())
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get subscriptions due for billing: %w", err)
        }</span>

        // Filter by user
        <span class="cov1" title="1">var userSubscriptions []model.Subscription
        for _, sub := range subscriptions </span><span class="cov1" title="1">{
                if sub.UserID == userUUID </span><span class="cov1" title="1">{
                        userSubscriptions = append(userSubscriptions, sub)
                }</span>
        }

        // Process each subscription
        <span class="cov1" title="1">for _, subscription := range userSubscriptions </span><span class="cov1" title="1">{
                if err := s.processBillingForSubscription(ctx, &amp;subscription); err != nil </span><span class="cov0" title="0">{
                        s.logger.Error(ctx, "Failed to process billing for subscription", "error", err, "subscription_id", subscription.ID)
                        continue</span>
                }
        }

        <span class="cov1" title="1">return nil</span>
}

// processBillingForSubscription processes billing for a single subscription
func (s *BillingCommandService) processBillingForSubscription(ctx context.Context, subscription *model.Subscription) error <span class="cov1" title="1">{
        // Get price
        price, err := s.repo.GetPrice(ctx, subscription.PriceID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get price: %w", err)
        }</span>

        // Create invoice
        <span class="cov1" title="1">invoiceAgg := aggregate.NewInvoiceAggregate(
                subscription.UserID,
                &amp;subscription.ID,
                subscription.CurrentPeriodStart,
                subscription.CurrentPeriodEnd,
        )

        // Add subscription line item
        description := fmt.Sprintf("%s - %s", price.Product.Name, price.Interval)
        if err := invoiceAgg.AddSubscriptionLineItem(price, description); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to add subscription line item: %w", err)
        }</span>

        // Add usage charges if applicable
        <span class="cov1" title="1">if price.PricingModel == model.PricingModelPerUnit || price.PricingModel == model.PricingModelTiered </span><span class="cov0" title="0">{
                usageRecords, err := s.repo.GetUnbilledUsageRecords(ctx, subscription.UserID, subscription.CurrentPeriodStart, subscription.CurrentPeriodEnd)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to get usage records: %w", err)
                }</span>

                <span class="cov0" title="0">if len(usageRecords) &gt; 0 </span><span class="cov0" title="0">{
                        usageAmount, err := s.pricingService.CalculateUsageAmount(ctx, usageRecords, *price, subscription.CurrentPeriodStart, subscription.CurrentPeriodEnd)
                        if err != nil </span><span class="cov0" title="0">{
                                return fmt.Errorf("failed to calculate usage amount: %w", err)
                        }</span>

                        <span class="cov0" title="0">if usageAmount.GreaterThan(decimal.Zero) </span><span class="cov0" title="0">{
                                usageDescription := fmt.Sprintf("Usage charges for %s", price.Product.Name)
                                if err := invoiceAgg.AddUsageLineItem(usageDescription, usageAmount, len(usageRecords), usageAmount.Div(decimal.NewFromInt(int64(len(usageRecords)))), subscription.CurrentPeriodStart, subscription.CurrentPeriodEnd); err != nil </span><span class="cov0" title="0">{
                                        return fmt.Errorf("failed to add usage line item: %w", err)
                                }</span>
                        }
                }
        }

        // Finalize invoice
        <span class="cov1" title="1">if err := invoiceAgg.Finalize(); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to finalize invoice: %w", err)
        }</span>

        <span class="cov1" title="1">invoice := invoiceAgg.GetInvoice()
        lineItems := invoiceAgg.GetLineItems()

        // Save invoice and line items
        if err := s.repo.CreateInvoice(ctx, invoice); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to save invoice: %w", err)
        }</span>

        <span class="cov1" title="1">for _, item := range lineItems </span><span class="cov1" title="1">{
                if err := s.repo.CreateInvoiceLineItem(ctx, &amp;item); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to save line item: %w", err)
                }</span>
        }

        // Initiate payment
        <span class="cov1" title="1">if invoice.TotalAmount.GreaterThan(decimal.Zero) </span><span class="cov1" title="1">{
                // TODO: Implement actual payment initiation
                s.logger.Info(ctx, "Payment initiation needed",
                        "invoice_id", invoice.ID,
                        "amount", invoice.TotalAmount,
                )
        }</span>

        <span class="cov1" title="1">return nil</span>
}

// HandlePaymentSucceeded handles successful payment events
func (s *BillingCommandService) HandlePaymentSucceeded(ctx context.Context, event event.PaymentEvent) error <span class="cov1" title="1">{
        s.logger.Info(ctx, "Handling payment success",
                "payment_id", event.PaymentID,
                "invoice_id", event.InvoiceID,
                "amount", event.Amount,
        )

        // Get invoice
        invoice, err := s.repo.GetInvoice(ctx, event.InvoiceID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get invoice: %w", err)
        }</span>

        // Mark invoice as paid
        <span class="cov1" title="1">lineItems, err := s.repo.GetInvoiceLineItems(ctx, invoice.ID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get line items: %w", err)
        }</span>

        <span class="cov1" title="1">invoiceAgg := aggregate.LoadInvoiceAggregate(invoice, lineItems)
        if err := invoiceAgg.MarkAsPaid(); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to mark invoice as paid: %w", err)
        }</span>

        // Update invoice
        <span class="cov1" title="1">updatedInvoice := invoiceAgg.GetInvoice()
        if err := s.repo.UpdateInvoice(ctx, updatedInvoice); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to update invoice: %w", err)
        }</span>

        // Update subscription if applicable
        <span class="cov1" title="1">if invoice.SubscriptionID != nil </span><span class="cov1" title="1">{
                subscription, err := s.repo.GetSubscription(ctx, *invoice.SubscriptionID)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to get subscription: %w", err)
                }</span>

                <span class="cov1" title="1">price, err := s.repo.GetPrice(ctx, subscription.PriceID)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to get price: %w", err)
                }</span>

                <span class="cov1" title="1">subscriptionAgg := aggregate.LoadSubscriptionAggregate(subscription, price)
                if err := subscriptionAgg.HandlePaymentSuccess(); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to handle payment success: %w", err)
                }</span>

                <span class="cov1" title="1">updatedSubscription := subscriptionAgg.GetSubscription()
                if err := s.repo.UpdateSubscriptionWithOptimisticLock(ctx, updatedSubscription, subscription.Version); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to update subscription: %w", err)
                }</span>
        }

        <span class="cov1" title="1">return nil</span>
}

// HandlePaymentFailed handles failed payment events
func (s *BillingCommandService) HandlePaymentFailed(ctx context.Context, event event.PaymentEvent) error <span class="cov0" title="0">{
        s.logger.Warn(ctx, "Handling payment failure",
                "payment_id", event.PaymentID,
                "invoice_id", event.InvoiceID,
                "amount", event.Amount,
        )

        // Get invoice
        invoice, err := s.repo.GetInvoice(ctx, event.InvoiceID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get invoice: %w", err)
        }</span>

        // Mark invoice as failed
        <span class="cov0" title="0">lineItems, err := s.repo.GetInvoiceLineItems(ctx, invoice.ID)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to get line items: %w", err)
        }</span>

        <span class="cov0" title="0">invoiceAgg := aggregate.LoadInvoiceAggregate(invoice, lineItems)
        if err := invoiceAgg.MarkAsFailed(); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to mark invoice as failed: %w", err)
        }</span>

        // Update invoice
        <span class="cov0" title="0">updatedInvoice := invoiceAgg.GetInvoice()
        if err := s.repo.UpdateInvoice(ctx, updatedInvoice); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to update invoice: %w", err)
        }</span>

        // Update subscription if applicable
        <span class="cov0" title="0">if invoice.SubscriptionID != nil </span><span class="cov0" title="0">{
                subscription, err := s.repo.GetSubscription(ctx, *invoice.SubscriptionID)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to get subscription: %w", err)
                }</span>

                <span class="cov0" title="0">price, err := s.repo.GetPrice(ctx, subscription.PriceID)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to get price: %w", err)
                }</span>

                <span class="cov0" title="0">subscriptionAgg := aggregate.LoadSubscriptionAggregate(subscription, price)
                if err := subscriptionAgg.HandlePaymentFailure(); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to handle payment failure: %w", err)
                }</span>

                <span class="cov0" title="0">updatedSubscription := subscriptionAgg.GetSubscription()
                if err := s.repo.UpdateSubscriptionWithOptimisticLock(ctx, updatedSubscription, subscription.Version); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to update subscription: %w", err)
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// HandlePaymentCancelled handles cancelled payment events
func (s *BillingCommandService) HandlePaymentCancelled(ctx context.Context, event event.PaymentEvent) error <span class="cov0" title="0">{
        s.logger.Info(ctx, "Handling payment cancellation",
                "payment_id", event.PaymentID,
                "invoice_id", event.InvoiceID,
                "amount", event.Amount,
        )

        // Similar to payment failure but with different status
        return s.HandlePaymentFailed(ctx, event)
}</span>

// publishEvent publishes a domain event to Kafka
func (s *BillingCommandService) publishEvent(ctx context.Context, domainEvent aggregate.DomainEvent) error <span class="cov3" title="2">{
        if s.kafkaWriter == nil </span><span class="cov0" title="0">{
                return nil // No Kafka writer configured
        }</span>

        <span class="cov3" title="2">eventBytes, err := json.Marshal(domainEvent)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to marshal event: %w", err)
        }</span>

        <span class="cov3" title="2">message := kafka.Message{
                Key:   []byte(domainEvent.AggregateID().String()),
                Value: eventBytes,
                Headers: []kafka.Header{
                        {Key: "event_type", Value: []byte(domainEvent.EventType())},
                        {Key: "aggregate_id", Value: []byte(domainEvent.AggregateID().String())},
                },
        }

        return s.kafkaWriter.WriteMessages(ctx, message)</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package query

import (
        "context"
        "fmt"
        "time"

        "cina.club/pkg/logger"
        "cina.club/services/billing-service/internal/application/port"
        "cina.club/services/billing-service/internal/domain/model"
        "github.com/google/uuid"
        "github.com/redis/go-redis/v9"
)

// BillingQueryService handles all read operations for billing
type BillingQueryService struct {
        repo   port.Repository
        redis  *redis.Client
        logger logger.Logger
}

// NewBillingQueryService creates a new billing query service
func NewBillingQueryService(
        repo port.Repository,
        redis *redis.Client,
        log logger.Logger,
) *BillingQueryService <span class="cov10" title="13">{
        return &amp;BillingQueryService{
                repo:   repo,
                redis:  redis,
                logger: log,
        }
}</span>

// CheckAccess checks if a user has access to a specific feature
// This is a high-performance, cached operation
func (s *BillingQueryService) CheckAccess(ctx context.Context, userID uuid.UUID, featureKey string) (bool, error) <span class="cov3" title="2">{
        // Try to get from cache first (only if Redis is available)
        if s.redis != nil </span><span class="cov0" title="0">{
                cacheKey := fmt.Sprintf("access:%s:%s", userID.String(), featureKey)
                cached, err := s.redis.Get(ctx, cacheKey).Result()
                if err == nil </span><span class="cov0" title="0">{
                        return cached == "true", nil
                }</span>
        }

        // Cache miss or no Redis, query database
        <span class="cov3" title="2">subscriptions, err := s.repo.GetActiveSubscriptionsByFeature(ctx, userID, featureKey)
        if err != nil </span><span class="cov0" title="0">{
                s.logger.Error(ctx, "Failed to check access",
                        "error", err,
                        "user_id", userID,
                        "feature_key", featureKey,
                )
                return false, err
        }</span>

        <span class="cov3" title="2">hasAccess := len(subscriptions) &gt; 0

        // Cache the result for 5 minutes (only if Redis is available)
        if s.redis != nil </span><span class="cov0" title="0">{
                cacheKey := fmt.Sprintf("access:%s:%s", userID.String(), featureKey)
                cacheValue := "false"
                if hasAccess </span><span class="cov0" title="0">{
                        cacheValue = "true"
                }</span>

                <span class="cov0" title="0">err = s.redis.Set(ctx, cacheKey, cacheValue, 5*time.Minute).Err()
                if err != nil </span><span class="cov0" title="0">{
                        s.logger.Warn(ctx, "Failed to cache access check result", "error", err)
                }</span>
        }

        <span class="cov3" title="2">return hasAccess, nil</span>
}

// GetProducts retrieves all available products
func (s *BillingQueryService) GetProducts(ctx context.Context, isActive *bool, limit, offset int) ([]model.Product, error) <span class="cov1" title="1">{
        return s.repo.ListProducts(ctx, isActive, limit, offset)
}</span>

// GetUserSubscriptions retrieves all subscriptions for a user
func (s *BillingQueryService) GetUserSubscriptions(ctx context.Context, userID uuid.UUID, status []string) ([]model.Subscription, error) <span class="cov1" title="1">{
        return s.repo.GetSubscriptionsByUser(ctx, userID, status)
}</span>

// GetUserInvoices retrieves all invoices for a user
func (s *BillingQueryService) GetUserInvoices(ctx context.Context, userID uuid.UUID, status []string, limit, offset int) ([]model.Invoice, error) <span class="cov1" title="1">{
        return s.repo.GetInvoicesByUser(ctx, userID, status, limit, offset)
}</span>

// GetSubscription retrieves a specific subscription
func (s *BillingQueryService) GetSubscription(ctx context.Context, subscriptionID uuid.UUID) (*model.Subscription, error) <span class="cov1" title="1">{
        return s.repo.GetSubscription(ctx, subscriptionID)
}</span>

// GetInvoice retrieves a specific invoice
func (s *BillingQueryService) GetInvoice(ctx context.Context, invoiceID uuid.UUID) (*model.Invoice, error) <span class="cov1" title="1">{
        return s.repo.GetInvoice(ctx, invoiceID)
}</span>

// GetProductPrices retrieves all prices for a product
func (s *BillingQueryService) GetProductPrices(ctx context.Context, productID uuid.UUID, isActive *bool) ([]model.Price, error) <span class="cov1" title="1">{
        return s.repo.GetPricesByProduct(ctx, productID, isActive)
}</span>

// GetFeatures retrieves all features
func (s *BillingQueryService) GetFeatures(ctx context.Context, limit, offset int) ([]model.Feature, error) <span class="cov1" title="1">{
        return s.repo.ListFeatures(ctx, limit, offset)
}</span>

// GetUsageRecords retrieves usage records for a user
func (s *BillingQueryService) GetUsageRecords(ctx context.Context, userID uuid.UUID, featureID *uuid.UUID, from, to time.Time) ([]model.UsageRecord, error) <span class="cov1" title="1">{
        return s.repo.GetUsageRecordsByUser(ctx, userID, featureID, from, to)
}</span>

// InvalidateAccessCache invalidates the access cache for a user
func (s *BillingQueryService) InvalidateAccessCache(ctx context.Context, userID uuid.UUID) error <span class="cov3" title="2">{
        // If Redis is not available, nothing to invalidate
        if s.redis == nil </span><span class="cov3" title="2">{
                return nil
        }</span>

        <span class="cov0" title="0">pattern := fmt.Sprintf("access:%s:*", userID.String())

        keys, err := s.redis.Keys(ctx, pattern).Result()
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">if len(keys) &gt; 0 </span><span class="cov0" title="0">{
                return s.redis.Del(ctx, keys...).Err()
        }</span>

        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file8" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 15:30:00
// Modified: 2025-01-23 15:30:00

package aggregate

import (
        "errors"
        "fmt"
        "time"

        "cina.club/services/billing-service/internal/domain/model"
        "github.com/google/uuid"
        "github.com/shopspring/decimal"
)

// InvoiceAggregate encapsulates invoice business logic
type InvoiceAggregate struct {
        invoice   *model.Invoice
        lineItems []model.InvoiceLineItem
        events    []DomainEvent
}

// InvoiceCreatedEvent is emitted when an invoice is created
type InvoiceCreatedEvent struct {
        InvoiceID      uuid.UUID
        UserID         uuid.UUID
        SubscriptionID *uuid.UUID
        TotalAmount    decimal.Decimal
        DueDate        time.Time
        OccurredTime   time.Time
}

func (e InvoiceCreatedEvent) EventType() string      <span class="cov0" title="0">{ return "invoice.created" }</span>
func (e InvoiceCreatedEvent) AggregateID() uuid.UUID <span class="cov0" title="0">{ return e.InvoiceID }</span>
func (e InvoiceCreatedEvent) OccurredAt() time.Time  <span class="cov0" title="0">{ return e.OccurredTime }</span>

// InvoiceFinalizedEvent is emitted when an invoice is finalized
type InvoiceFinalizedEvent struct {
        InvoiceID    uuid.UUID
        TotalAmount  decimal.Decimal
        OccurredTime time.Time
}

func (e InvoiceFinalizedEvent) EventType() string      <span class="cov3" title="3">{ return "invoice.finalized" }</span>
func (e InvoiceFinalizedEvent) AggregateID() uuid.UUID <span class="cov0" title="0">{ return e.InvoiceID }</span>
func (e InvoiceFinalizedEvent) OccurredAt() time.Time  <span class="cov0" title="0">{ return e.OccurredTime }</span>

// InvoicePaidEvent is emitted when an invoice is paid
type InvoicePaidEvent struct {
        InvoiceID    uuid.UUID
        PaidAmount   decimal.Decimal
        PaidAt       time.Time
        OccurredTime time.Time
}

func (e InvoicePaidEvent) EventType() string      <span class="cov2" title="2">{ return "invoice.paid" }</span>
func (e InvoicePaidEvent) AggregateID() uuid.UUID <span class="cov0" title="0">{ return e.InvoiceID }</span>
func (e InvoicePaidEvent) OccurredAt() time.Time  <span class="cov0" title="0">{ return e.OccurredTime }</span>

// NewInvoiceAggregate creates a new invoice aggregate
func NewInvoiceAggregate(userID uuid.UUID, subscriptionID *uuid.UUID, periodStart, periodEnd time.Time) *InvoiceAggregate <span class="cov9" title="28">{
        now := time.Now()
        invoiceID := uuid.New()

        // Generate invoice number (simple sequential format - in production use a more sophisticated approach)
        invoiceNumber := fmt.Sprintf("INV-%d-%s", now.Unix(), invoiceID.String()[:8])

        invoice := &amp;model.Invoice{
                ID:             invoiceID,
                UserID:         userID,
                SubscriptionID: subscriptionID,
                InvoiceNumber:  invoiceNumber,
                Status:         model.InvoiceStatusDraft,
                Subtotal:       decimal.Zero,
                TaxAmount:      decimal.Zero,
                DiscountAmount: decimal.Zero,
                TotalAmount:    decimal.Zero,
                Currency:       "USD",
                DueDate:        now.AddDate(0, 0, 7), // Due in 7 days
                PeriodStart:    periodStart,
                PeriodEnd:      periodEnd,
                AttemptCount:   0,
                CreatedAt:      now,
                UpdatedAt:      now,
        }

        return &amp;InvoiceAggregate{
                invoice:   invoice,
                lineItems: []model.InvoiceLineItem{},
                events:    []DomainEvent{},
        }
}</span>

// LoadInvoiceAggregate loads an existing invoice aggregate
func LoadInvoiceAggregate(invoice *model.Invoice, lineItems []model.InvoiceLineItem) *InvoiceAggregate <span class="cov1" title="1">{
        return &amp;InvoiceAggregate{
                invoice:   invoice,
                lineItems: lineItems,
                events:    []DomainEvent{},
        }
}</span>

// GetInvoice returns the invoice model
func (a *InvoiceAggregate) GetInvoice() *model.Invoice <span class="cov8" title="19">{
        return a.invoice
}</span>

// GetLineItems returns the line items
func (a *InvoiceAggregate) GetLineItems() []model.InvoiceLineItem <span class="cov5" title="5">{
        return a.lineItems
}</span>

// GetEvents returns and clears the domain events
func (a *InvoiceAggregate) GetEvents() []DomainEvent <span class="cov4" title="4">{
        events := a.events
        a.events = []DomainEvent{}
        return events
}</span>

// AddSubscriptionLineItem adds a subscription line item to the invoice
func (a *InvoiceAggregate) AddSubscriptionLineItem(price *model.Price, description string) error <span class="cov7" title="14">{
        if a.invoice.Status != model.InvoiceStatusDraft </span><span class="cov1" title="1">{
                return errors.New("cannot add line items to non-draft invoice")
        }</span>

        <span class="cov7" title="13">lineItem := model.InvoiceLineItem{
                ID:          uuid.New(),
                InvoiceID:   a.invoice.ID,
                Description: description,
                Amount:      price.Amount,
                Quantity:    1,
                UnitAmount:  price.Amount,
                PriceID:     &amp;price.ID,
                ItemType:    "SUBSCRIPTION",
                CreatedAt:   time.Now(),
                UpdatedAt:   time.Now(),
        }

        a.lineItems = append(a.lineItems, lineItem)
        a.recalculateSubtotal()

        return nil</span>
}

// AddUsageLineItem adds a usage-based line item to the invoice
func (a *InvoiceAggregate) AddUsageLineItem(description string, amount decimal.Decimal, quantity int, unitAmount decimal.Decimal, usageStart, usageEnd time.Time) error <span class="cov2" title="2">{
        if a.invoice.Status != model.InvoiceStatusDraft </span><span class="cov1" title="1">{
                return errors.New("cannot add line items to non-draft invoice")
        }</span>

        <span class="cov1" title="1">lineItem := model.InvoiceLineItem{
                ID:          uuid.New(),
                InvoiceID:   a.invoice.ID,
                Description: description,
                Amount:      amount,
                Quantity:    quantity,
                UnitAmount:  unitAmount,
                UsageStart:  &amp;usageStart,
                UsageEnd:    &amp;usageEnd,
                ItemType:    "USAGE",
                CreatedAt:   time.Now(),
                UpdatedAt:   time.Now(),
        }

        a.lineItems = append(a.lineItems, lineItem)
        a.recalculateSubtotal()

        return nil</span>
}

// AddDiscountLineItem adds a discount line item to the invoice
func (a *InvoiceAggregate) AddDiscountLineItem(description string, discountAmount decimal.Decimal) error <span class="cov5" title="5">{
        if a.invoice.Status != model.InvoiceStatusDraft </span><span class="cov1" title="1">{
                return errors.New("cannot add line items to non-draft invoice")
        }</span>

        // Discount amounts should be negative
        <span class="cov4" title="4">if discountAmount.GreaterThan(decimal.Zero) </span><span class="cov4" title="4">{
                discountAmount = discountAmount.Neg()
        }</span>

        <span class="cov4" title="4">lineItem := model.InvoiceLineItem{
                ID:          uuid.New(),
                InvoiceID:   a.invoice.ID,
                Description: description,
                Amount:      discountAmount,
                Quantity:    1,
                UnitAmount:  discountAmount,
                ItemType:    "DISCOUNT",
                CreatedAt:   time.Now(),
                UpdatedAt:   time.Now(),
        }

        a.lineItems = append(a.lineItems, lineItem)
        a.recalculateSubtotal()

        return nil</span>
}

// ApplyCoupon applies a coupon discount to the invoice
func (a *InvoiceAggregate) ApplyCoupon(coupon *model.Coupon) error <span class="cov5" title="5">{
        if a.invoice.Status != model.InvoiceStatusDraft </span><span class="cov0" title="0">{
                return errors.New("cannot apply coupon to non-draft invoice")
        }</span>

        // Validate coupon
        <span class="cov5" title="5">if !a.isValidCoupon(coupon) </span><span class="cov2" title="2">{
                return errors.New("invalid or expired coupon")
        }</span>

        // Calculate discount amount
        <span class="cov3" title="3">discountAmount := a.calculateCouponDiscount(coupon)
        if discountAmount.LessThanOrEqual(decimal.Zero) </span><span class="cov1" title="1">{
                return errors.New("no discount applicable")
        }</span>

        // Add discount line item
        <span class="cov2" title="2">description := fmt.Sprintf("Coupon: %s", coupon.Code)
        return a.AddDiscountLineItem(description, discountAmount)</span>
}

// Finalize finalizes the invoice and makes it ready for payment
func (a *InvoiceAggregate) Finalize() error <span class="cov6" title="8">{
        if a.invoice.Status != model.InvoiceStatusDraft </span><span class="cov1" title="1">{
                return errors.New("can only finalize draft invoices")
        }</span>

        // Calculate totals
        <span class="cov6" title="7">a.recalculateSubtotal()
        a.calculateTax()
        a.calculateTotal()

        // Update status
        a.invoice.Status = model.InvoiceStatusOpen
        a.invoice.UpdatedAt = time.Now()

        // Add finalized event
        a.addEvent(InvoiceFinalizedEvent{
                InvoiceID:    a.invoice.ID,
                TotalAmount:  a.invoice.TotalAmount,
                OccurredTime: time.Now(),
        })

        return nil</span>
}

// MarkAsPaid marks the invoice as paid
func (a *InvoiceAggregate) MarkAsPaid() error <span class="cov4" title="4">{
        if a.invoice.Status != model.InvoiceStatusOpen &amp;&amp; a.invoice.Status != model.InvoiceStatusFailed </span><span class="cov1" title="1">{
                return errors.New("can only mark open or failed invoices as paid")
        }</span>

        <span class="cov3" title="3">now := time.Now()
        a.invoice.Status = model.InvoiceStatusPaid
        a.invoice.PaidAt = &amp;now
        a.invoice.UpdatedAt = now

        // Add paid event
        a.addEvent(InvoicePaidEvent{
                InvoiceID:    a.invoice.ID,
                PaidAmount:   a.invoice.TotalAmount,
                PaidAt:       now,
                OccurredTime: now,
        })

        return nil</span>
}

// MarkAsFailed marks the invoice as failed
func (a *InvoiceAggregate) MarkAsFailed() error <span class="cov2" title="2">{
        if a.invoice.Status != model.InvoiceStatusOpen </span><span class="cov1" title="1">{
                return errors.New("can only mark open invoices as failed")
        }</span>

        <span class="cov1" title="1">a.invoice.Status = model.InvoiceStatusFailed
        a.invoice.AttemptCount++
        a.invoice.UpdatedAt = time.Now()

        // Schedule next payment attempt
        nextAttempt := time.Now().AddDate(0, 0, a.invoice.AttemptCount) // Exponential backoff in days
        a.invoice.NextPaymentAttempt = &amp;nextAttempt

        return nil</span>
}

// Void voids the invoice
func (a *InvoiceAggregate) Void() error <span class="cov2" title="2">{
        if a.invoice.Status == model.InvoiceStatusPaid </span><span class="cov1" title="1">{
                return errors.New("cannot void paid invoices")
        }</span>

        <span class="cov1" title="1">now := time.Now()
        a.invoice.Status = model.InvoiceStatusVoid
        a.invoice.VoidedAt = &amp;now
        a.invoice.UpdatedAt = now

        return nil</span>
}

// IsPayable returns true if the invoice can be paid
func (a *InvoiceAggregate) IsPayable() bool <span class="cov6" title="8">{
        return a.invoice.Status == model.InvoiceStatusOpen || a.invoice.Status == model.InvoiceStatusFailed
}</span>

// IsOverdue returns true if the invoice is overdue
func (a *InvoiceAggregate) IsOverdue(asOfDate time.Time) bool <span class="cov3" title="3">{
        return a.IsPayable() &amp;&amp; a.invoice.DueDate.Before(asOfDate)
}</span>

// recalculateSubtotal recalculates the subtotal from line items
func (a *InvoiceAggregate) recalculateSubtotal() <span class="cov9" title="25">{
        subtotal := decimal.Zero
        discount := decimal.Zero

        for _, item := range a.lineItems </span><span class="cov10" title="30">{
                if item.ItemType == "DISCOUNT" </span><span class="cov5" title="5">{
                        discount = discount.Add(item.Amount.Abs())
                }</span> else<span class="cov9" title="25"> {
                        subtotal = subtotal.Add(item.Amount)
                }</span>
        }

        <span class="cov9" title="25">a.invoice.Subtotal = subtotal
        a.invoice.DiscountAmount = discount</span>
}

// calculateTax calculates tax (simplified - in production use tax service)
func (a *InvoiceAggregate) calculateTax() <span class="cov6" title="7">{
        // Simple 8% tax calculation for demo purposes
        taxRate := decimal.NewFromFloat(0.08)
        taxableAmount := a.invoice.Subtotal.Sub(a.invoice.DiscountAmount)

        if taxableAmount.LessThan(decimal.Zero) </span><span class="cov0" title="0">{
                taxableAmount = decimal.Zero
        }</span>

        <span class="cov6" title="7">a.invoice.TaxAmount = taxableAmount.Mul(taxRate).Round(2)</span>
}

// calculateTotal calculates the total amount
func (a *InvoiceAggregate) calculateTotal() <span class="cov6" title="7">{
        total := a.invoice.Subtotal.Sub(a.invoice.DiscountAmount).Add(a.invoice.TaxAmount)

        if total.LessThan(decimal.Zero) </span><span class="cov0" title="0">{
                total = decimal.Zero
        }</span>

        <span class="cov6" title="7">a.invoice.TotalAmount = total</span>
}

// isValidCoupon checks if a coupon is valid
func (a *InvoiceAggregate) isValidCoupon(coupon *model.Coupon) bool <span class="cov5" title="5">{
        now := time.Now()

        // Check if coupon is active
        if !coupon.IsActive </span><span class="cov1" title="1">{
                return false
        }</span>

        // Check validity period
        <span class="cov4" title="4">if now.Before(coupon.ValidFrom) </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov4" title="4">if coupon.ValidUntil != nil &amp;&amp; now.After(*coupon.ValidUntil) </span><span class="cov1" title="1">{
                return false
        }</span>

        // Check redemption limits
        <span class="cov3" title="3">if coupon.MaxRedemptions != nil &amp;&amp; coupon.TimesRedeemed &gt;= *coupon.MaxRedemptions </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov3" title="3">return true</span>
}

// calculateCouponDiscount calculates the discount amount for a coupon
func (a *InvoiceAggregate) calculateCouponDiscount(coupon *model.Coupon) decimal.Decimal <span class="cov3" title="3">{
        discountableAmount := a.invoice.Subtotal

        switch coupon.DiscountType </span>{
        case model.DiscountTypePercentage:<span class="cov2" title="2">
                percentage := coupon.DiscountValue.Div(decimal.NewFromInt(100))
                return discountableAmount.Mul(percentage).Round(2)</span>

        case model.DiscountTypeFixedAmount:<span class="cov1" title="1">
                // Don't give more discount than the subtotal
                if coupon.DiscountValue.GreaterThan(discountableAmount) </span><span class="cov0" title="0">{
                        return discountableAmount
                }</span>
                <span class="cov1" title="1">return coupon.DiscountValue</span>

        default:<span class="cov0" title="0">
                return decimal.Zero</span>
        }
}

// addEvent adds a domain event
func (a *InvoiceAggregate) addEvent(event DomainEvent) <span class="cov7" title="10">{
        a.events = append(a.events, event)
}</span>
</pre>
		
		<pre class="file" id="file9" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 15:30:00
// Modified: 2025-01-23 15:30:00

package aggregate

import (
        "errors"
        "time"

        "cina.club/services/billing-service/internal/domain/model"
        "github.com/google/uuid"
        "github.com/shopspring/decimal"
)

// SubscriptionAggregate encapsulates subscription business logic and state machine
type SubscriptionAggregate struct {
        subscription *model.Subscription
        price        *model.Price
        events       []DomainEvent
}

// DomainEvent represents a domain event
type DomainEvent interface {
        EventType() string
        AggregateID() uuid.UUID
        OccurredAt() time.Time
}

// SubscriptionCreatedEvent is emitted when a subscription is created
type SubscriptionCreatedEvent struct {
        SubscriptionID uuid.UUID
        UserID         uuid.UUID
        PriceID        uuid.UUID
        Status         string
        OccurredTime   time.Time
}

func (e SubscriptionCreatedEvent) EventType() string      <span class="cov0" title="0">{ return "subscription.created" }</span>
func (e SubscriptionCreatedEvent) AggregateID() uuid.UUID <span class="cov0" title="0">{ return e.SubscriptionID }</span>
func (e SubscriptionCreatedEvent) OccurredAt() time.Time  <span class="cov0" title="0">{ return e.OccurredTime }</span>

// SubscriptionUpgradedEvent is emitted when a subscription is upgraded
type SubscriptionUpgradedEvent struct {
        SubscriptionID  uuid.UUID
        FromPriceID     uuid.UUID
        ToPriceID       uuid.UUID
        ProrationAmount decimal.Decimal
        OccurredTime    time.Time
}

func (e SubscriptionUpgradedEvent) EventType() string      <span class="cov0" title="0">{ return "subscription.upgraded" }</span>
func (e SubscriptionUpgradedEvent) AggregateID() uuid.UUID <span class="cov0" title="0">{ return e.SubscriptionID }</span>
func (e SubscriptionUpgradedEvent) OccurredAt() time.Time  <span class="cov0" title="0">{ return e.OccurredTime }</span>

// SubscriptionCanceledEvent is emitted when a subscription is canceled
type SubscriptionCanceledEvent struct {
        SubscriptionID     uuid.UUID
        CancelAtPeriodEnd  bool
        CancellationReason string
        OccurredTime       time.Time
}

func (e SubscriptionCanceledEvent) EventType() string      <span class="cov0" title="0">{ return "subscription.canceled" }</span>
func (e SubscriptionCanceledEvent) AggregateID() uuid.UUID <span class="cov0" title="0">{ return e.SubscriptionID }</span>
func (e SubscriptionCanceledEvent) OccurredAt() time.Time  <span class="cov0" title="0">{ return e.OccurredTime }</span>

// NewSubscriptionAggregate creates a new subscription aggregate
func NewSubscriptionAggregate(userID, priceID uuid.UUID, price *model.Price, trialDays int) (*SubscriptionAggregate, error) <span class="cov8" title="1">{
        if price == nil </span><span class="cov0" title="0">{
                return nil, errors.New("price cannot be nil")
        }</span>

        <span class="cov8" title="1">now := time.Now()
        subscriptionID := uuid.New()

        var status string
        var trialStart, trialEnd *time.Time
        var periodStart, periodEnd time.Time

        // Determine if this is a trial subscription
        if trialDays &gt; 0 </span><span class="cov0" title="0">{
                status = model.SubscriptionStatusTrialing
                trialStart = &amp;now
                endTime := now.AddDate(0, 0, trialDays)
                trialEnd = &amp;endTime
                periodStart = now
                periodEnd = endTime
        }</span> else<span class="cov8" title="1"> {
                status = model.SubscriptionStatusActive
                periodStart = now
                // Calculate period end based on interval
                periodEnd = calculatePeriodEnd(now, price.Interval)
        }</span>

        <span class="cov8" title="1">subscription := &amp;model.Subscription{
                ID:                 subscriptionID,
                UserID:             userID,
                PriceID:            priceID,
                Status:             status,
                CurrentPeriodStart: periodStart,
                CurrentPeriodEnd:   periodEnd,
                TrialStart:         trialStart,
                TrialEnd:           trialEnd,
                CancelAtPeriodEnd:  false,
                Version:            1,
                CreatedAt:          now,
                UpdatedAt:          now,
        }

        aggregate := &amp;SubscriptionAggregate{
                subscription: subscription,
                price:        price,
                events:       []DomainEvent{},
        }

        // Add creation event
        aggregate.addEvent(SubscriptionCreatedEvent{
                SubscriptionID: subscriptionID,
                UserID:         userID,
                PriceID:        priceID,
                Status:         status,
                OccurredTime:   now,
        })

        return aggregate, nil</span>
}

// LoadSubscriptionAggregate loads an existing subscription aggregate
func LoadSubscriptionAggregate(subscription *model.Subscription, price *model.Price) *SubscriptionAggregate <span class="cov0" title="0">{
        return &amp;SubscriptionAggregate{
                subscription: subscription,
                price:        price,
                events:       []DomainEvent{},
        }
}</span>

// GetSubscription returns the subscription model
func (a *SubscriptionAggregate) GetSubscription() *model.Subscription <span class="cov8" title="1">{
        return a.subscription
}</span>

// GetEvents returns and clears the domain events
func (a *SubscriptionAggregate) GetEvents() []DomainEvent <span class="cov0" title="0">{
        events := a.events
        a.events = []DomainEvent{}
        return events
}</span>

// Upgrade upgrades the subscription to a new price
func (a *SubscriptionAggregate) Upgrade(newPriceID uuid.UUID, newPrice *model.Price, prorationAmount decimal.Decimal) error <span class="cov0" title="0">{
        if !a.canUpgrade() </span><span class="cov0" title="0">{
                return errors.New("subscription cannot be upgraded in current state")
        }</span>

        <span class="cov0" title="0">oldPriceID := a.subscription.PriceID
        a.subscription.PriceID = newPriceID
        a.price = newPrice
        a.subscription.UpdatedAt = time.Now()
        a.subscription.Version++

        // Add upgrade event
        a.addEvent(SubscriptionUpgradedEvent{
                SubscriptionID:  a.subscription.ID,
                FromPriceID:     oldPriceID,
                ToPriceID:       newPriceID,
                ProrationAmount: prorationAmount,
                OccurredTime:    time.Now(),
        })

        return nil</span>
}

// Cancel cancels the subscription
func (a *SubscriptionAggregate) Cancel(cancelAtPeriodEnd bool, reason string) error <span class="cov0" title="0">{
        if !a.canCancel() </span><span class="cov0" title="0">{
                return errors.New("subscription cannot be canceled in current state")
        }</span>

        <span class="cov0" title="0">now := time.Now()
        a.subscription.CancelAtPeriodEnd = cancelAtPeriodEnd

        if !cancelAtPeriodEnd </span><span class="cov0" title="0">{
                // Cancel immediately
                a.subscription.Status = model.SubscriptionStatusCanceled
                a.subscription.CanceledAt = &amp;now
                a.subscription.EndedAt = &amp;now
        }</span> else<span class="cov0" title="0"> {
                // Cancel at period end
                a.subscription.CanceledAt = &amp;now
        }</span>

        <span class="cov0" title="0">a.subscription.UpdatedAt = now
        a.subscription.Version++

        // Add cancellation event
        a.addEvent(SubscriptionCanceledEvent{
                SubscriptionID:     a.subscription.ID,
                CancelAtPeriodEnd:  cancelAtPeriodEnd,
                CancellationReason: reason,
                OccurredTime:       now,
        })

        return nil</span>
}

// HandlePaymentFailure handles payment failure for the subscription
func (a *SubscriptionAggregate) HandlePaymentFailure() error <span class="cov0" title="0">{
        if a.subscription.Status != model.SubscriptionStatusActive </span><span class="cov0" title="0">{
                return errors.New("can only handle payment failure for active subscriptions")
        }</span>

        // Move to past due status (grace period)
        <span class="cov0" title="0">a.subscription.Status = model.SubscriptionStatusPastDue
        a.subscription.UpdatedAt = time.Now()
        a.subscription.Version++

        return nil</span>
}

// HandlePaymentSuccess handles successful payment for the subscription
func (a *SubscriptionAggregate) HandlePaymentSuccess() error <span class="cov0" title="0">{
        now := time.Now()

        switch a.subscription.Status </span>{
        case model.SubscriptionStatusTrialing:<span class="cov0" title="0">
                // Trial ended with successful payment, move to active
                a.subscription.Status = model.SubscriptionStatusActive
                // Extend period based on subscription interval
                a.subscription.CurrentPeriodEnd = calculatePeriodEnd(now, a.price.Interval)</span>

        case model.SubscriptionStatusPastDue:<span class="cov0" title="0">
                // Payment recovered, move back to active
                a.subscription.Status = model.SubscriptionStatusActive</span>

        case model.SubscriptionStatusActive:<span class="cov0" title="0">
                // Renewal payment, extend period
                a.subscription.CurrentPeriodStart = a.subscription.CurrentPeriodEnd
                a.subscription.CurrentPeriodEnd = calculatePeriodEnd(a.subscription.CurrentPeriodEnd, a.price.Interval)</span>

        default:<span class="cov0" title="0">
                return errors.New("invalid subscription status for payment success")</span>
        }

        <span class="cov0" title="0">a.subscription.UpdatedAt = now
        a.subscription.Version++

        return nil</span>
}

// HandlePeriodEnd handles the end of the current billing period
func (a *SubscriptionAggregate) HandlePeriodEnd() error <span class="cov0" title="0">{
        now := time.Now()

        if a.subscription.CancelAtPeriodEnd </span><span class="cov0" title="0">{
                // Cancel the subscription
                a.subscription.Status = model.SubscriptionStatusInactive
                a.subscription.EndedAt = &amp;now
        }</span> else<span class="cov0" title="0"> if a.subscription.Status == model.SubscriptionStatusPastDue </span><span class="cov0" title="0">{
                // Grace period ended, deactivate subscription
                a.subscription.Status = model.SubscriptionStatusInactive
                a.subscription.EndedAt = &amp;now
        }</span>

        <span class="cov0" title="0">a.subscription.UpdatedAt = now
        a.subscription.Version++

        return nil</span>
}

// IsActive returns true if the subscription is active or trialing
func (a *SubscriptionAggregate) IsActive() bool <span class="cov0" title="0">{
        return a.subscription.Status == model.SubscriptionStatusActive ||
                a.subscription.Status == model.SubscriptionStatusTrialing
}</span>

// IsDueForBilling returns true if the subscription is due for billing
func (a *SubscriptionAggregate) IsDueForBilling(asOfDate time.Time) bool <span class="cov0" title="0">{
        return a.IsActive() &amp;&amp; a.subscription.CurrentPeriodEnd.Before(asOfDate)
}</span>

// canUpgrade checks if the subscription can be upgraded
func (a *SubscriptionAggregate) canUpgrade() bool <span class="cov0" title="0">{
        return a.subscription.Status == model.SubscriptionStatusActive ||
                a.subscription.Status == model.SubscriptionStatusTrialing
}</span>

// canCancel checks if the subscription can be canceled
func (a *SubscriptionAggregate) canCancel() bool <span class="cov0" title="0">{
        return a.subscription.Status == model.SubscriptionStatusActive ||
                a.subscription.Status == model.SubscriptionStatusTrialing ||
                a.subscription.Status == model.SubscriptionStatusPastDue
}</span>

// addEvent adds a domain event
func (a *SubscriptionAggregate) addEvent(event DomainEvent) <span class="cov8" title="1">{
        a.events = append(a.events, event)
}</span>

// calculatePeriodEnd calculates the end of the billing period
func calculatePeriodEnd(start time.Time, interval string) time.Time <span class="cov8" title="1">{
        switch interval </span>{
        case "month":<span class="cov8" title="1">
                return start.AddDate(0, 1, 0)</span>
        case "year":<span class="cov0" title="0">
                return start.AddDate(1, 0, 0)</span>
        case "week":<span class="cov0" title="0">
                return start.AddDate(0, 0, 7)</span>
        case "day":<span class="cov0" title="0">
                return start.AddDate(0, 0, 1)</span>
        default:<span class="cov0" title="0">
                // Default to monthly
                return start.AddDate(0, 1, 0)</span>
        }
}
</pre>
		
		<pre class="file" id="file10" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package service

import (
        "context"
        "fmt"
        "time"

        "cina.club/services/billing-service/internal/domain/model"
        "github.com/shopspring/decimal"
)

// pricingService implements PricingService
type pricingService struct {
        strategies map[string]PricingStrategy
}

// NewPricingService creates a new pricing service with all strategies
func NewPricingService() PricingService <span class="cov9" title="9">{
        strategies := map[string]PricingStrategy{
                model.PricingModelFlat:    &amp;FlatFeeStrategy{},
                model.PricingModelPerUnit: &amp;PerUnitStrategy{},
                model.PricingModelTiered:  &amp;TieredStrategy{},
        }

        return &amp;pricingService{
                strategies: strategies,
        }
}</span>

// CalculateUsageAmount calculates the total amount for usage records using the appropriate strategy
func (s *pricingService) CalculateUsageAmount(ctx context.Context, usageRecords []model.UsageRecord, price model.Price, periodStart, periodEnd time.Time) (decimal.Decimal, error) <span class="cov8" title="7">{
        strategy, err := s.GetStrategy(price.PricingModel)
        if err != nil </span><span class="cov1" title="1">{
                return decimal.Zero, err
        }</span>

        <span class="cov7" title="6">return strategy.Calculate(ctx, usageRecords, price, periodStart, periodEnd)</span>
}

// GetStrategy returns the pricing strategy for the given model
func (s *pricingService) GetStrategy(pricingModel string) (PricingStrategy, error) <span class="cov10" title="11">{
        strategy, exists := s.strategies[pricingModel]
        if !exists </span><span class="cov3" title="2">{
                return nil, fmt.Errorf("unsupported pricing model: %s", pricingModel)
        }</span>
        <span class="cov9" title="9">return strategy, nil</span>
}

// FlatFeeStrategy implements flat fee pricing (fixed amount regardless of usage)
type FlatFeeStrategy struct{}

func (s *FlatFeeStrategy) GetName() string <span class="cov1" title="1">{
        return "Flat Fee"
}</span>

func (s *FlatFeeStrategy) Calculate(ctx context.Context, usageRecords []model.UsageRecord, price model.Price, periodStart, periodEnd time.Time) (decimal.Decimal, error) <span class="cov1" title="1">{
        // For flat fee, return the fixed price amount
        // Usage records are ignored as it's a fixed fee
        return price.Amount, nil
}</span>

// PerUnitStrategy implements per-unit pricing (price * quantity used)
type PerUnitStrategy struct{}

func (s *PerUnitStrategy) GetName() string <span class="cov1" title="1">{
        return "Per Unit"
}</span>

func (s *PerUnitStrategy) Calculate(ctx context.Context, usageRecords []model.UsageRecord, price model.Price, periodStart, periodEnd time.Time) (decimal.Decimal, error) <span class="cov5" title="3">{
        if len(usageRecords) == 0 </span><span class="cov1" title="1">{
                return decimal.Zero, nil
        }</span>

        // Sum up all usage quantities in the period
        <span class="cov3" title="2">totalQuantity := int64(0)
        for _, record := range usageRecords </span><span class="cov6" title="4">{
                if record.Timestamp.After(periodStart) &amp;&amp; record.Timestamp.Before(periodEnd) </span><span class="cov3" title="2">{
                        totalQuantity += record.Quantity
                }</span>
        }

        // Calculate total amount: unit price * total quantity
        <span class="cov3" title="2">quantityDecimal := decimal.NewFromInt(totalQuantity)
        return price.Amount.Mul(quantityDecimal), nil</span>
}

// TieredStrategy implements tiered pricing (different rates for different usage tiers)
type TieredStrategy struct{}

func (s *TieredStrategy) GetName() string <span class="cov1" title="1">{
        return "Tiered"
}</span>

func (s *TieredStrategy) Calculate(ctx context.Context, usageRecords []model.UsageRecord, price model.Price, periodStart, periodEnd time.Time) (decimal.Decimal, error) <span class="cov3" title="2">{
        if len(usageRecords) == 0 </span><span class="cov0" title="0">{
                return decimal.Zero, nil
        }</span>

        // Sum up all usage quantities in the period
        <span class="cov3" title="2">totalQuantity := int64(0)
        for _, record := range usageRecords </span><span class="cov3" title="2">{
                if record.Timestamp.After(periodStart) &amp;&amp; record.Timestamp.Before(periodEnd) </span><span class="cov3" title="2">{
                        totalQuantity += record.Quantity
                }</span>
        }

        // Get tiered pricing configuration from metadata
        <span class="cov3" title="2">tiers, err := s.parseTieredConfig(price.Metadata)
        if err != nil </span><span class="cov0" title="0">{
                return decimal.Zero, fmt.Errorf("failed to parse tiered pricing config: %w", err)
        }</span>

        // Calculate amount based on tiers
        <span class="cov3" title="2">return s.calculateTieredAmount(totalQuantity, tiers), nil</span>
}

// TierConfig represents a pricing tier
type TierConfig struct {
        UpTo  int64           `json:"up_to"` // Maximum quantity for this tier (0 means unlimited)
        Price decimal.Decimal `json:"price"` // Price per unit in this tier
}

// parseTieredConfig extracts tier configuration from price metadata
func (s *TieredStrategy) parseTieredConfig(metadata map[string]interface{}) ([]TierConfig, error) <span class="cov3" title="2">{
        tiersInterface, exists := metadata["tiers"]
        if !exists </span><span class="cov1" title="1">{
                // Default tier configuration if not specified
                return []TierConfig{
                        {UpTo: 1000, Price: decimal.NewFromFloat(0.0)},   // First 1000 free
                        {UpTo: 10000, Price: decimal.NewFromFloat(0.01)}, // Next 9000 at $0.01 each
                        {UpTo: 0, Price: decimal.NewFromFloat(0.005)},    // Everything above at $0.005 each
                }, nil
        }</span>

        // Parse tiers from metadata
        <span class="cov1" title="1">tiersList, ok := tiersInterface.([]interface{})
        if !ok </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("tiers must be an array")
        }</span>

        <span class="cov1" title="1">var tiers []TierConfig
        for _, tierInterface := range tiersList </span><span class="cov3" title="2">{
                tierMap, ok := tierInterface.(map[string]interface{})
                if !ok </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("each tier must be an object")
                }</span>

                // Parse up_to
                <span class="cov3" title="2">upToInterface, exists := tierMap["up_to"]
                if !exists </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("tier missing 'up_to' field")
                }</span>
                <span class="cov3" title="2">upTo, ok := upToInterface.(float64)
                if !ok </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("'up_to' must be a number")
                }</span>

                // Parse price
                <span class="cov3" title="2">priceInterface, exists := tierMap["price"]
                if !exists </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("tier missing 'price' field")
                }</span>
                <span class="cov3" title="2">priceFloat, ok := priceInterface.(float64)
                if !ok </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("'price' must be a number")
                }</span>

                <span class="cov3" title="2">tiers = append(tiers, TierConfig{
                        UpTo:  int64(upTo),
                        Price: decimal.NewFromFloat(priceFloat),
                })</span>
        }

        <span class="cov1" title="1">return tiers, nil</span>
}

// calculateTieredAmount calculates the total amount based on tiered pricing
func (s *TieredStrategy) calculateTieredAmount(totalQuantity int64, tiers []TierConfig) decimal.Decimal <span class="cov3" title="2">{
        totalAmount := decimal.Zero
        remainingQuantity := totalQuantity

        for _, tier := range tiers </span><span class="cov6" title="4">{
                if remainingQuantity &lt;= 0 </span><span class="cov1" title="1">{
                        break</span>
                }

                <span class="cov5" title="3">var tierQuantity int64
                if tier.UpTo == 0 </span><span class="cov1" title="1">{ // Unlimited tier
                        tierQuantity = remainingQuantity
                }</span> else<span class="cov3" title="2"> {
                        tierQuantity = min(remainingQuantity, tier.UpTo)
                }</span>

                <span class="cov5" title="3">tierAmount := decimal.NewFromInt(tierQuantity).Mul(tier.Price)
                totalAmount = totalAmount.Add(tierAmount)

                remainingQuantity -= tierQuantity

                if tier.UpTo == 0 </span><span class="cov1" title="1">{ // Unlimited tier, we're done
                        break</span>
                }
        }

        <span class="cov3" title="2">return totalAmount</span>
}

// min returns the minimum of two int64 values
func min(a, b int64) int64 <span class="cov3" title="2">{
        if a &lt; b </span><span class="cov1" title="1">{
                return a
        }</span>
        <span class="cov1" title="1">return b</span>
}
</pre>
		
		<pre class="file" id="file11" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package service

import (
        "context"
        "fmt"
        "time"

        "cina.club/services/billing-service/internal/domain/model"
        "github.com/shopspring/decimal"
)

// prorationService implements ProrationService
type prorationService struct{}

// NewProrationService creates a new proration service
func NewProrationService() ProrationService <span class="cov10" title="9">{
        return &amp;prorationService{}
}</span>

// CalculateProration calculates the proration for subscription changes
func (s *prorationService) CalculateProration(ctx context.Context, fromSubscription, toSubscription *model.Subscription, changeDate time.Time) (*ProrationResult, error) <span class="cov8" title="6">{
        if fromSubscription == nil || toSubscription == nil </span><span class="cov1" title="1">{
                return nil, fmt.Errorf("both subscriptions must be provided")
        }</span>

        // Calculate remaining time in current period
        <span class="cov7" title="5">totalPeriodDuration := fromSubscription.CurrentPeriodEnd.Sub(fromSubscription.CurrentPeriodStart)
        remainingDuration := fromSubscription.CurrentPeriodEnd.Sub(changeDate)

        if remainingDuration &lt; 0 </span><span class="cov1" title="1">{
                return nil, fmt.Errorf("change date cannot be after current period end")
        }</span>

        // Calculate proration ratio
        <span class="cov6" title="4">prorationRatio := decimal.NewFromFloat(remainingDuration.Seconds()).Div(decimal.NewFromFloat(totalPeriodDuration.Seconds()))

        // Get the prices for both subscriptions
        fromPrice := fromSubscription.Price.Amount
        toPrice := toSubscription.Price.Amount

        // Calculate credit for unused portion of current subscription
        creditAmount := fromPrice.Mul(prorationRatio)

        // Calculate charge for new subscription (prorated for remaining period)
        chargeAmount := toPrice.Mul(prorationRatio)

        // Net amount is the difference
        netAmount := chargeAmount.Sub(creditAmount)

        var description string
        if netAmount.IsPositive() </span><span class="cov3" title="2">{
                description = fmt.Sprintf("Upgrade charge: $%s (prorated for %d days)",
                        netAmount.String(), int(remainingDuration.Hours()/24))
        }</span> else<span class="cov3" title="2"> if netAmount.IsNegative() </span><span class="cov1" title="1">{
                description = fmt.Sprintf("Downgrade credit: $%s (prorated for %d days)",
                        netAmount.Abs().String(), int(remainingDuration.Hours()/24))
        }</span> else<span class="cov1" title="1"> {
                description = "No charge for subscription change"
        }</span>

        <span class="cov6" title="4">return &amp;ProrationResult{
                CreditAmount:  creditAmount,
                ChargeAmount:  chargeAmount,
                NetAmount:     netAmount,
                EffectiveDate: changeDate,
                Description:   description,
        }, nil</span>
}

// CalculateRefund calculates refund amount for subscription cancellation
func (s *prorationService) CalculateRefund(ctx context.Context, subscription *model.Subscription, cancelDate time.Time) (*RefundResult, error) <span class="cov5" title="3">{
        if subscription == nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("subscription must be provided")
        }</span>

        // Only calculate refund if subscription is active and paid
        <span class="cov5" title="3">if subscription.Status != model.SubscriptionStatusActive </span><span class="cov1" title="1">{
                return &amp;RefundResult{
                        RefundAmount:  decimal.Zero,
                        RefundReason:  "No refund for inactive subscription",
                        EffectiveDate: cancelDate,
                        Description:   "Subscription is not active, no refund applicable",
                }, nil
        }</span>

        // Calculate remaining time in current period
        <span class="cov3" title="2">totalPeriodDuration := subscription.CurrentPeriodEnd.Sub(subscription.CurrentPeriodStart)
        remainingDuration := subscription.CurrentPeriodEnd.Sub(cancelDate)

        if remainingDuration &lt;= 0 </span><span class="cov1" title="1">{
                return &amp;RefundResult{
                        RefundAmount:  decimal.Zero,
                        RefundReason:  "Cancellation at end of period",
                        EffectiveDate: cancelDate,
                        Description:   "No refund for cancellation at end of billing period",
                }, nil
        }</span>

        // Calculate refund ratio
        <span class="cov1" title="1">refundRatio := decimal.NewFromFloat(remainingDuration.Seconds()).Div(decimal.NewFromFloat(totalPeriodDuration.Seconds()))

        // Calculate refund amount (prorated amount of the subscription price)
        subscriptionPrice := subscription.Price.Amount
        refundAmount := subscriptionPrice.Mul(refundRatio)

        description := fmt.Sprintf("Refund for unused %d days of subscription",
                int(remainingDuration.Hours()/24))

        return &amp;RefundResult{
                RefundAmount:  refundAmount,
                RefundReason:  "Prorated refund for early cancellation",
                EffectiveDate: cancelDate,
                Description:   description,
        }, nil</span>
}

// CalculateNextBillingAmount calculates the amount for the next billing cycle
func (s *prorationService) CalculateNextBillingAmount(ctx context.Context, subscription *model.Subscription, usageRecords []model.UsageRecord) (decimal.Decimal, error) <span class="cov0" title="0">{
        if subscription == nil </span><span class="cov0" title="0">{
                return decimal.Zero, fmt.Errorf("subscription must be provided")
        }</span>

        // Base subscription amount
        <span class="cov0" title="0">baseAmount := subscription.Price.Amount

        // If it's a usage-based pricing model, calculate usage charges
        if subscription.Price.PricingModel == model.PricingModelPerUnit || subscription.Price.PricingModel == model.PricingModelTiered </span>{<span class="cov0" title="0">
                // This would typically use the pricing service to calculate usage charges
                // For now, we'll return just the base amount
                // In a real implementation, this would integrate with the pricing service
        }</span>

        <span class="cov0" title="0">return baseAmount, nil</span>
}

// ValidateSubscriptionChange validates if a subscription change is allowed
func (s *prorationService) ValidateSubscriptionChange(ctx context.Context, fromSubscription *model.Subscription, toPriceID string, changeDate time.Time) error <span class="cov0" title="0">{
        if fromSubscription == nil </span><span class="cov0" title="0">{
                return fmt.Errorf("current subscription must be provided")
        }</span>

        // Check if subscription is in a valid state for changes
        <span class="cov0" title="0">validStates := []string{
                model.SubscriptionStatusActive,
                model.SubscriptionStatusTrialing,
        }

        isValidState := false
        for _, state := range validStates </span><span class="cov0" title="0">{
                if fromSubscription.Status == state </span><span class="cov0" title="0">{
                        isValidState = true
                        break</span>
                }
        }

        <span class="cov0" title="0">if !isValidState </span><span class="cov0" title="0">{
                return fmt.Errorf("subscription in state %s cannot be changed", fromSubscription.Status)
        }</span>

        // Check if change date is valid
        <span class="cov0" title="0">if changeDate.Before(time.Now()) </span><span class="cov0" title="0">{
                return fmt.Errorf("change date cannot be in the past")
        }</span>

        <span class="cov0" title="0">if changeDate.After(fromSubscription.CurrentPeriodEnd) </span><span class="cov0" title="0">{
                return fmt.Errorf("change date cannot be after current period end")
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// CalculateUpgradeImmediate calculates immediate upgrade charges
func (s *prorationService) CalculateUpgradeImmediate(ctx context.Context, fromSubscription *model.Subscription, toPrice decimal.Decimal, changeDate time.Time) (*ProrationResult, error) <span class="cov0" title="0">{
        if fromSubscription == nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("subscription must be provided")
        }</span>

        // Calculate remaining time in current period
        <span class="cov0" title="0">totalPeriodDuration := fromSubscription.CurrentPeriodEnd.Sub(fromSubscription.CurrentPeriodStart)
        remainingDuration := fromSubscription.CurrentPeriodEnd.Sub(changeDate)

        if remainingDuration &lt; 0 </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("change date cannot be after current period end")
        }</span>

        // Calculate proration ratio
        <span class="cov0" title="0">prorationRatio := decimal.NewFromFloat(remainingDuration.Seconds()).Div(decimal.NewFromFloat(totalPeriodDuration.Seconds()))

        // Calculate credit for unused portion of current subscription
        fromPrice := fromSubscription.Price.Amount
        creditAmount := fromPrice.Mul(prorationRatio)

        // Calculate charge for new price level (prorated for remaining period)
        chargeAmount := toPrice.Mul(prorationRatio)

        // Net amount (should be positive for upgrade)
        netAmount := chargeAmount.Sub(creditAmount)

        description := fmt.Sprintf("Immediate upgrade charge: $%s (prorated for %d days)",
                netAmount.String(), int(remainingDuration.Hours()/24))

        return &amp;ProrationResult{
                CreditAmount:  creditAmount,
                ChargeAmount:  chargeAmount,
                NetAmount:     netAmount,
                EffectiveDate: changeDate,
                Description:   description,
        }, nil</span>
}
</pre>
		
		<pre class="file" id="file12" style="display: none">// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package config

import (
        "fmt"
        "strings"

        "github.com/spf13/viper"
)

// Config holds all configuration for the billing service
type Config struct {
        Server           ServerConfig           `mapstructure:"server"`
        Database         DatabaseConfig         `mapstructure:"database"`
        Redis            RedisConfig            `mapstructure:"redis"`
        Kafka            KafkaConfig            `mapstructure:"kafka"`
        Logger           LoggerConfig           `mapstructure:"logger"`
        Worker           WorkerConfig           `mapstructure:"worker"`
        ExternalServices ExternalServicesConfig `mapstructure:"external_services"`
}

// ServerConfig contains server-related configuration
type ServerConfig struct {
        HTTPPort int    `mapstructure:"http_port"`
        GRPCPort int    `mapstructure:"grpc_port"`
        Host     string `mapstructure:"host"`
}

// DatabaseConfig contains database connection configuration
type DatabaseConfig struct {
        URL             string `mapstructure:"url"`
        MaxOpenConns    int    `mapstructure:"max_open_conns"`
        MaxIdleConns    int    `mapstructure:"max_idle_conns"`
        ConnMaxLifetime string `mapstructure:"conn_max_lifetime"`
}

// RedisConfig contains Redis connection configuration
type RedisConfig struct {
        Address  string `mapstructure:"address"`
        Password string `mapstructure:"password"`
        DB       int    `mapstructure:"db"`
}

// KafkaConfig contains Kafka configuration
type KafkaConfig struct {
        Brokers []string    `mapstructure:"brokers"`
        Topics  TopicConfig `mapstructure:"topics"`
}

// TopicConfig contains Kafka topic names
type TopicConfig struct {
        BillingEvents string `mapstructure:"billing_events"`
        PaymentEvents string `mapstructure:"payment_events"`
}

// LoggerConfig contains logging configuration
type LoggerConfig struct {
        Level  string `mapstructure:"level"`
        Format string `mapstructure:"format"`
}

// WorkerConfig contains worker-specific configuration
type WorkerConfig struct {
        Concurrency int            `mapstructure:"concurrency"`
        Queues      map[string]int `mapstructure:"queues"`
}

// ExternalServicesConfig contains configuration for external services
type ExternalServicesConfig struct {
        PaymentService  ExternalServiceConfig `mapstructure:"payment_service"`
        UserCoreService ExternalServiceConfig `mapstructure:"user_core_service"`
}

// ExternalServiceConfig contains configuration for an external service
type ExternalServiceConfig struct {
        Address string `mapstructure:"address"`
        Timeout string `mapstructure:"timeout"`
}

// Load loads configuration from various sources
func Load() *Config <span class="cov10" title="15">{
        viper.SetConfigName("config")
        viper.SetConfigType("yaml")
        viper.AddConfigPath("./configs")
        viper.AddConfigPath("../configs")
        viper.AddConfigPath(".")

        // Set defaults
        setDefaults()

        // Enable environment variable override
        viper.AutomaticEnv()
        viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

        // Read config file
        if err := viper.ReadInConfig(); err != nil </span><span class="cov10" title="15">{
                fmt.Printf("Warning: Could not read config file: %v\n", err)
        }</span>

        <span class="cov10" title="15">var config Config
        if err := viper.Unmarshal(&amp;config); err != nil </span><span class="cov0" title="0">{
                panic(fmt.Sprintf("Failed to unmarshal config: %v", err))</span>
        }

        <span class="cov10" title="15">return &amp;config</span>
}

// setDefaults sets default configuration values
func setDefaults() <span class="cov10" title="15">{
        // Server defaults
        viper.SetDefault("server.http_port", 8080)
        viper.SetDefault("server.grpc_port", 9090)
        viper.SetDefault("server.host", "0.0.0.0")

        // Database defaults
        viper.SetDefault("database.url", "postgres://localhost:5432/billing?sslmode=disable")
        viper.SetDefault("database.max_open_conns", 25)
        viper.SetDefault("database.max_idle_conns", 5)
        viper.SetDefault("database.conn_max_lifetime", "5m")

        // Redis defaults
        viper.SetDefault("redis.address", "localhost:6379")
        viper.SetDefault("redis.password", "")
        viper.SetDefault("redis.db", 0)

        // Kafka defaults
        viper.SetDefault("kafka.brokers", []string{"localhost:9092"})
        viper.SetDefault("kafka.topics.billing_events", "billing-events")
        viper.SetDefault("kafka.topics.payment_events", "payment-events")

        // Logger defaults
        viper.SetDefault("logger.level", "info")
        viper.SetDefault("logger.format", "json")

        // Worker defaults
        viper.SetDefault("worker.concurrency", 10)
        viper.SetDefault("worker.queues.billing", 6)
        viper.SetDefault("worker.queues.critical", 3)
        viper.SetDefault("worker.queues.default", 1)

        // External services defaults
        viper.SetDefault("external_services.payment_service.address", "localhost:9091")
        viper.SetDefault("external_services.payment_service.timeout", "30s")
        viper.SetDefault("external_services.user_core_service.address", "localhost:9092")
        viper.SetDefault("external_services.user_core_service.timeout", "30s")
}</span>
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
