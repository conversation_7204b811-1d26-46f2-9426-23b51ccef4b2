/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 17:55:00
 * Modified: 2025-01-23 17:55:00
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import PasswordReset from './PasswordReset';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock the API service
vi.mock('@/services/api/authApi', () => ({
  requestPasswordReset: vi.fn().mockResolvedValue({ success: true }),
  verifyResetCode: vi.fn().mockResolvedValue({ success: true }),
  resetPassword: vi.fn().mockResolvedValue({ success: true }),
}));

const queryClient = new QueryClient();

const renderComponent = () =>
  render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        <PasswordReset />
      </MemoryRouter>
    </QueryClientProvider>
  );

describe('PasswordReset Component', () => {
  it('renders the initial step (enter email)', () => {
    renderComponent();
    expect(screen.getByText('Forgot Your Password?')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
  });

  it('allows user to enter an email and request a reset', async () => {
    renderComponent();
    const emailInput = screen.getByLabelText('Email Address');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: 'Send Reset Link' }));

    // Wait for the step to change
    expect(await screen.findByText('Enter Verification Code')).toBeInTheDocument();
  });

  it('allows user to enter a verification code', async () => {
    renderComponent();
    // Go to step 1
    fireEvent.change(screen.getByLabelText('Email Address'), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: 'Send Reset Link' }));
    
    const codeInput = await screen.findByLabelText('Verification Code');
    fireEvent.change(codeInput, { target: { value: '123456' } });
    fireEvent.click(screen.getByRole('button', { name: 'Verify Code' }));
    
    // Wait for the step to change
    expect(await screen.findByText('Set New Password')).toBeInTheDocument();
  });

  it('allows user to set a new password', async () => {
    renderComponent();
    // Go through steps 0 and 1
    fireEvent.change(screen.getByLabelText('Email Address'), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: 'Send Reset Link' }));
    const codeInput = await screen.findByLabelText('Verification Code');
    fireEvent.change(codeInput, { target: { value: '123456' } });
    fireEvent.click(screen.getByRole('button', { name: 'Verify Code' }));

    const passwordInput = await screen.findByLabelText('New Password');
    const confirmInput = screen.getByLabelText('Confirm New Password');

    fireEvent.change(passwordInput, { target: { value: 'new-password-123' } });
    fireEvent.change(confirmInput, { target: { value: 'new-password-123' } });
    fireEvent.click(screen.getByRole('button', { name: 'Reset Password' }));

    // Wait for the success step
    expect(await screen.findByText('Password Reset Successfully!')).toBeInTheDocument();
  });
}); 