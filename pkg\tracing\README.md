# Tracing Package

分布式追踪包，为 CINA.CLUB 平台提供统一的 OpenTelemetry 追踪能力。

## 概述

`pkg/tracing` 是一个标准化的分布式追踪初始化和配置库，负责在每个微服务启动时以统一的方式配置 OpenTelemetry 追踪能力。它封装了复杂的 OpenTelemetry SDK 初始化流程，提供简单易用的一键启用接口。

## 特性

- **标准化配置**: 通过配置文件统一管理所有追踪参数
- **多种导出器支持**: 支持 stdout、Jaeger、OTLP (gRPC/HTTP) 等导出器
- **灵活采样策略**: 支持多种采样器类型，适应不同环境需求
- **优雅关闭**: 确保追踪数据在服务关闭时被完整导出
- **配置验证**: 内置配置验证，防止错误配置
- **预设配置**: 提供开发、生产、测试等环境的预设配置

## 快速开始

### 基本使用

```go
package main

import (
    "context"
    "log"
    
    "pkg/config"
    "pkg/tracing"
)

func main() {
    // 1. 加载配置
    var cfg struct {
        Tracing tracing.Config `mapstructure:"tracing"`
    }
    config.Load("./config.yaml", &cfg)

    // 2. 初始化分布式追踪
    shutdown, err := tracing.Init(cfg.Tracing)
    if err != nil {
        log.Fatalf("failed to initialize tracer: %v", err)
    }
    defer func() {
        if err := shutdown(context.Background()); err != nil {
            log.Printf("failed to shutdown tracer: %v", err)
        }
    }()

    // 3. 启动服务...
    // 其他组件会自动使用全局 TracerProvider
}
```

### 配置文件示例

```yaml
tracing:
  enabled: true
  service_name: "my-service"
  service_version: "v1.0.0"
  environment: "production"
  
  exporter:
    type: "jaeger"
    endpoint: "http://jaeger:14268/api/traces"
    timeout: 30
    retry:
      enabled: true
      max_attempts: 3
  
  sampler:
    type: "parent_based_trace_id_ratio"
    param: 0.1  # 10% 采样率
```

## 配置详解

### 主要配置

| 字段 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | bool | true | 是否启用追踪 |
| `service_name` | string | - | 服务名称（必填） |
| `service_version` | string | "unknown" | 服务版本 |
| `environment` | string | "development" | 部署环境 |

### Exporter 配置

支持的导出器类型：

#### stdout
```yaml
exporter:
  type: "stdout"
```

#### Jaeger
```yaml
exporter:
  type: "jaeger"
  endpoint: "http://jaeger:14268/api/traces"
```

#### OTLP gRPC
```yaml
exporter:
  type: "otlp-grpc"
  endpoint: "http://otel-collector:4317"
  insecure: true
  compression: "gzip"
  headers:
    x-api-key: "your-api-key"
```

#### OTLP HTTP
```yaml
exporter:
  type: "otlp-http"
  endpoint: "http://otel-collector:4318/v1/traces"
  insecure: true
  compression: "gzip"
```

### Sampler 配置

支持的采样器类型：

#### Always On (总是采样)
```yaml
sampler:
  type: "always_on"
```

#### Always Off (从不采样)
```yaml
sampler:
  type: "always_off"
```

#### Trace ID Ratio (比例采样)
```yaml
sampler:
  type: "trace_id_ratio"
  param: 0.1  # 10% 采样率
```

#### Parent Based (基于父 span 采样)
```yaml
sampler:
  type: "parent_based_trace_id_ratio"
  param: 0.1  # 根 span 10% 采样率
```

## 预设配置

### 开发环境
```go
cfg := tracing.DevelopmentConfig("my-service")
// - stdout 导出
// - 100% 采样
// - 开发环境标签
```

### 生产环境
```go
cfg := tracing.ProductionConfig("my-service", "http://jaeger:14268/api/traces")
// - Jaeger 导出
// - 10% 采样
// - 生产环境标签
// - 安全连接
```

### 测试环境
```go
cfg := tracing.TestConfig("my-service")
// - 禁用追踪
// - 测试环境标签
```

## 高级用法

### 手动创建 Span

```go
func businessLogic(ctx context.Context) error {
    // 创建子 span
    ctx, span := tracing.StartSpan(ctx, "business-operation")
    defer span.End() // 确保 span 被关闭

    // 添加属性
    tracing.AddAttributes(ctx, map[string]interface{}{
        "user_id": "12345",
        "operation": "create_order",
    })

    // 执行业务逻辑
    if err := someOperation(ctx); err != nil {
        tracing.RecordError(ctx, err)
        tracing.SetStatus(ctx, "ERROR", err.Error())
        return err
    }

    tracing.SetStatus(ctx, "OK", "success")
    return nil
}
```

### 获取 Tracer

```go
tracer := tracing.GetTracer("my-component")
ctx, span := tracer.Start(ctx, "operation")
defer span.End()
```

### 检查追踪状态

```go
if tracing.IsEnabled() {
    // 追踪已启用，可以进行额外的追踪操作
}
```

## 最佳实践

### 1. 服务命名
- 使用有意义的服务名称
- 遵循命名约定：`{domain}-{service}-service`
- 例如：`user-core-service`、`payment-service`

### 2. 环境配置
- 开发环境：使用 stdout 导出，100% 采样
- 测试环境：禁用追踪或使用内存导出
- 生产环境：使用 Jaeger/OTLP，低采样率（1-10%）

### 3. 采样策略
- 高流量服务：使用较低采样率（1-5%）
- 关键服务：使用较高采样率（10-50%）
- 调试时：临时使用 100% 采样

### 4. 错误处理
```go
// 正确的错误处理方式
ctx, span := tracing.StartSpan(ctx, "operation")
defer func() {
    if r := recover(); r != nil {
        tracing.RecordError(ctx, fmt.Errorf("panic: %v", r))
        tracing.SetStatus(ctx, "ERROR", "panic occurred")
        span.End()
        panic(r) // 重新抛出 panic
    }
    span.End()
}()

if err := operation(ctx); err != nil {
    tracing.RecordError(ctx, err)
    tracing.SetStatus(ctx, "ERROR", err.Error())
    return err
}

tracing.SetStatus(ctx, "OK", "success")
```

## 性能考虑

### 采样率建议
- **开发环境**: 100% (调试需要)
- **测试环境**: 0% 或 100% (根据需要)
- **生产环境**: 1-10% (平衡数据量和可观测性)

### 内存使用
- 批处理大小: 512 spans (默认)
- 队列大小: 2048 spans (默认)
- 超时时间: 10 秒 (默认)

### 网络开销
- 使用 gzip 压缩减少网络流量
- 配置合适的导出超时时间
- 启用重试机制确保数据可靠传输

## 故障排除

### 常见问题

#### 1. 初始化失败
```
Error: failed to create exporter: endpoint is required
```
**解决方案**: 检查导出器配置，确保 endpoint 字段已设置

#### 2. 采样配置错误
```
Error: sampler param must be between 0.0 and 1.0
```
**解决方案**: 检查采样参数，确保在有效范围内

#### 3. 网络连接问题
```
Error: failed to export spans: connection refused
```
**解决方案**: 检查追踪后端是否运行，网络连接是否正常

### 调试技巧

#### 启用详细日志
```go
// 在开发环境中启用 stdout 导出查看追踪数据
cfg := tracing.DevelopmentConfig("debug-service")
```

#### 验证配置
```go
if err := cfg.Validate(); err != nil {
    log.Printf("Invalid tracing config: %v", err)
}
```

#### 检查追踪状态
```go
log.Printf("Tracing enabled: %v", tracing.IsEnabled())
```

## 开发状态

当前版本是基础实现，包含：
- ✅ 配置管理和验证
- ✅ 基本 API 接口
- ✅ 测试框架
- ⏳ OpenTelemetry 集成 (待网络依赖解决)
- ⏳ 完整的导出器实现
- ⏳ 实际的 span 操作

## 依赖项

- `context` (标准库)
- `fmt` (标准库)
- OpenTelemetry Go SDK (计划中)
- 各种导出器包 (计划中)

## 许可证

Copyright (c) 2025 Cina.Club  
All rights reserved. 