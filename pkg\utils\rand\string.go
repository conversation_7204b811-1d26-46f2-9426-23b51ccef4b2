/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

// Package rand provides cryptographically secure random number and string generation.
// All functions use crypto/rand as the randomness source for security.
package rand

import (
	"crypto/rand"
	"fmt"
	"math/big"
)

const (
	// DefaultCharset contains alphanumeric characters (A-Z, a-z, 0-9)
	DefaultCharset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"

	// AlphaCharset contains only alphabetic characters (A-Z, a-z)
	AlphaCharset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

	// DigitCharset contains only numeric characters (0-9)
	DigitCharset = "0123456789"

	// UpperCharset contains only uppercase letters (A-Z)
	UpperCharset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

	// LowerCharset contains only lowercase letters (a-z)
	LowerCharset = "abcdefghijklmnopqrstuvwxyz"

	// HexCharset contains hexadecimal characters (0-9, A-F)
	HexCharset = "0123456789ABCDEF"

	// URLSafeCharset contains URL-safe characters
	URLSafeCharset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
)

// String generates a cryptographically secure random string of the specified length
// using the default charset (alphanumeric characters).
//
// Example:
//
//	str, err := rand.String(10)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// str might be "aB3dE9fG2h"
func String(length int) (string, error) {
	return StringWithCharset(length, DefaultCharset)
}

// StringWithCharset generates a cryptographically secure random string of the
// specified length using the provided character set.
//
// Example:
//
//	// Generate a 8-character string with only digits
//	str, err := rand.StringWithCharset(8, rand.DigitCharset)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// str might be "12345678"
func StringWithCharset(length int, charset string) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("length must be positive, got %d", length)
	}

	if len(charset) == 0 {
		return "", fmt.Errorf("charset cannot be empty")
	}

	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))

	for i := range result {
		// Generate a random index within the charset range
		// Using crypto/rand.Int to avoid modulo bias
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", fmt.Errorf("failed to generate random index: %w", err)
		}

		result[i] = charset[randomIndex.Int64()]
	}

	return string(result), nil
}

// Digits generates a cryptographically secure random string of digits.
//
// Example:
//
//	digits, err := rand.Digits(6)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// digits might be "123456"
func Digits(length int) (string, error) {
	return StringWithCharset(length, DigitCharset)
}

// Alpha generates a cryptographically secure random string of alphabetic characters.
//
// Example:
//
//	alpha, err := rand.Alpha(8)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// alpha might be "AbCdEfGh"
func Alpha(length int) (string, error) {
	return StringWithCharset(length, AlphaCharset)
}

// Upper generates a cryptographically secure random string of uppercase letters.
//
// Example:
//
//	upper, err := rand.Upper(5)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// upper might be "ABCDE"
func Upper(length int) (string, error) {
	return StringWithCharset(length, UpperCharset)
}

// Lower generates a cryptographically secure random string of lowercase letters.
//
// Example:
//
//	lower, err := rand.Lower(5)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// lower might be "abcde"
func Lower(length int) (string, error) {
	return StringWithCharset(length, LowerCharset)
}

// Hex generates a cryptographically secure random hexadecimal string.
//
// Example:
//
//	hex, err := rand.Hex(8)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// hex might be "1A2B3C4D"
func Hex(length int) (string, error) {
	return StringWithCharset(length, HexCharset)
}

// URLSafe generates a cryptographically secure random URL-safe string.
//
// Example:
//
//	urlSafe, err := rand.URLSafe(12)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// urlSafe might be "aB3-E9_G2h1k"
func URLSafe(length int) (string, error) {
	return StringWithCharset(length, URLSafeCharset)
}

// Bytes generates cryptographically secure random bytes.
//
// Example:
//
//	bytes, err := rand.Bytes(16)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// bytes contains 16 random bytes
func Bytes(length int) ([]byte, error) {
	if length <= 0 {
		return nil, fmt.Errorf("length must be positive, got %d", length)
	}

	result := make([]byte, length)
	if _, err := rand.Read(result); err != nil {
		return nil, fmt.Errorf("failed to generate random bytes: %w", err)
	}

	return result, nil
}

// Int generates a cryptographically secure random integer in the range [0, max).
//
// Example:
//
//	num, err := rand.Int(100)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// num is between 0 and 99
func Int(max int) (int, error) {
	if max <= 0 {
		return 0, fmt.Errorf("max must be positive, got %d", max)
	}

	randomBig, err := rand.Int(rand.Reader, big.NewInt(int64(max)))
	if err != nil {
		return 0, fmt.Errorf("failed to generate random integer: %w", err)
	}

	return int(randomBig.Int64()), nil
}

// IntRange generates a cryptographically secure random integer in the range [min, max].
//
// Example:
//
//	num, err := rand.IntRange(10, 20)
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// num is between 10 and 20 (inclusive)
func IntRange(min, max int) (int, error) {
	if min > max {
		return 0, fmt.Errorf("min (%d) cannot be greater than max (%d)", min, max)
	}

	if min == max {
		return min, nil
	}

	// Generate random number in range [0, max-min]
	rangeSize := max - min + 1
	randomNum, err := Int(rangeSize)
	if err != nil {
		return 0, err
	}

	return min + randomNum, nil
}
