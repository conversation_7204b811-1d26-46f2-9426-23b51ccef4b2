/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { hilog } from '@kit.PerformanceAnalysisKit';
import { http } from '@kit.NetworkKit';
import { webSocket } from '@kit.NetworkKit';
import { ConfigManager } from '../config/ConfigManager';

/**
 * HTTP请求选项
 */
export interface HttpRequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  expectDataType?: http.HttpDataType;
}

/**
 * WebSocket消息接口
 */
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

/**
 * WebSocket事件监听器
 */
export interface WebSocketEventListener {
  onOpen?: () => void;
  onMessage?: (message: WebSocketMessage) => void;
  onError?: (error: Error) => void;
  onClose?: (code: number, reason: string) => void;
}

/**
 * 网络管理器
 * 
 * 负责HTTP请求、WebSocket连接管理和网络状态监控
 */
export class NetworkManager {
  private static readonly TAG = 'NetworkManager';
  
  private readonly configManager: ConfigManager;
  private httpRequest: http.HttpRequest;
  
  // WebSocket连接管理
  private webSocketConnections: Map<string, webSocket.WebSocket> = new Map();
  private webSocketListeners: Map<string, WebSocketEventListener> = new Map();
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000; // 1秒

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.httpRequest = http.createHttp();
  }

  // ===================== HTTP请求方法 =====================

  /**
   * GET请求
   */
  async get<T = any>(url: string, options?: HttpRequestOptions): Promise<T> {
    return this.request<T>('GET', url, undefined, options);
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, options?: HttpRequestOptions): Promise<T> {
    return this.request<T>('POST', url, data, options);
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, options?: HttpRequestOptions): Promise<T> {
    return this.request<T>('PUT', url, data, options);
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, options?: HttpRequestOptions): Promise<T> {
    return this.request<T>('DELETE', url, undefined, options);
  }

  /**
   * 通用HTTP请求方法
   */
  private async request<T = any>(
    method: http.RequestMethod,
    url: string,
    data?: any,
    options?: HttpRequestOptions
  ): Promise<T> {
    const fullUrl = this.buildFullUrl(url);
    const requestOptions = this.buildRequestOptions(method, data, options);

    try {
      hilog.debug(0x0000, NetworkManager.TAG, `${method} ${fullUrl}`);

      const response = await this.httpRequest.request(fullUrl, requestOptions);
      
      if (response.responseCode === 200) {
        const result = this.parseResponse<T>(response.result);
        hilog.debug(0x0000, NetworkManager.TAG, `${method} ${fullUrl} - Success`);
        return result;
      } else {
        const error = new Error(`HTTP ${response.responseCode}: ${response.result}`);
        hilog.error(0x0000, NetworkManager.TAG, `${method} ${fullUrl} - Error: ${error.message}`);
        throw error;
      }
    } catch (error) {
      hilog.error(0x0000, NetworkManager.TAG, `${method} ${fullUrl} - Exception: ${error.message}`);
      throw error;
    }
  }

  // ===================== WebSocket方法 =====================

  /**
   * 创建WebSocket连接
   */
  async createWebSocketConnection(
    endpoint: string,
    listener: WebSocketEventListener,
    connectionId?: string
  ): Promise<string> {
    const id = connectionId || `ws_${Date.now()}`;
    const url = this.buildWebSocketUrl(endpoint);

    try {
      const ws = webSocket.createWebSocket();
      
      // 设置事件监听器
      ws.on('open', () => {
        hilog.info(0x0000, NetworkManager.TAG, `WebSocket connected: ${id}`);
        this.reconnectAttempts.set(id, 0); // 重置重连次数
        listener.onOpen?.();
      });

      ws.on('message', (err, value) => {
        if (err) {
          hilog.error(0x0000, NetworkManager.TAG, `WebSocket message error: ${err.message}`);
          listener.onError?.(new Error(err.message));
          return;
        }

        try {
          const message: WebSocketMessage = JSON.parse(value as string);
          listener.onMessage?.(message);
        } catch (parseError) {
          hilog.error(0x0000, NetworkManager.TAG, `WebSocket message parse error: ${parseError.message}`);
        }
      });

      ws.on('error', (err) => {
        hilog.error(0x0000, NetworkManager.TAG, `WebSocket error: ${err.message}`);
        listener.onError?.(new Error(err.message));
        
        // 自动重连
        this.scheduleReconnect(id, endpoint, listener);
      });

      ws.on('close', (err, value) => {
        const code = value?.code || 0;
        const reason = value?.reason || '';
        hilog.info(0x0000, NetworkManager.TAG, `WebSocket closed: ${id}, code: ${code}, reason: ${reason}`);
        
        this.webSocketConnections.delete(id);
        listener.onClose?.(code, reason);
        
        // 如果不是主动关闭，尝试重连
        if (code !== 1000) {
          this.scheduleReconnect(id, endpoint, listener);
        }
      });

      // 建立连接
      await ws.connect(url);
      
      // 保存连接和监听器
      this.webSocketConnections.set(id, ws);
      this.webSocketListeners.set(id, listener);
      
      return id;
    } catch (error) {
      hilog.error(0x0000, NetworkManager.TAG, `Create WebSocket connection failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送WebSocket消息
   */
  async sendWebSocketMessage(connectionId: string, message: WebSocketMessage): Promise<void> {
    const ws = this.webSocketConnections.get(connectionId);
    if (!ws) {
      throw new Error(`WebSocket connection not found: ${connectionId}`);
    }

    try {
      const messageStr = JSON.stringify(message);
      await ws.send(messageStr);
      hilog.debug(0x0000, NetworkManager.TAG, `WebSocket message sent: ${connectionId}`);
    } catch (error) {
      hilog.error(0x0000, NetworkManager.TAG, `Send WebSocket message failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 关闭WebSocket连接
   */
  async closeWebSocketConnection(connectionId: string): Promise<void> {
    const ws = this.webSocketConnections.get(connectionId);
    if (!ws) {
      hilog.warn(0x0000, NetworkManager.TAG, `WebSocket connection not found: ${connectionId}`);
      return;
    }

    try {
      await ws.close();
      this.webSocketConnections.delete(connectionId);
      this.webSocketListeners.delete(connectionId);
      this.reconnectAttempts.delete(connectionId);
      hilog.info(0x0000, NetworkManager.TAG, `WebSocket connection closed: ${connectionId}`);
    } catch (error) {
      hilog.error(0x0000, NetworkManager.TAG, `Close WebSocket connection failed: ${error.message}`);
    }
  }

  // ===================== 连接管理方法 =====================

  /**
   * 重新连接所有WebSocket（应用前台时调用）
   */
  async reconnectIfNeeded(): Promise<void> {
    hilog.info(0x0000, NetworkManager.TAG, 'Checking WebSocket connections for reconnection');
    
    // 这里可以实现重连逻辑
    // 例如检查连接状态，对断开的连接进行重连
  }

  /**
   * 处理应用后台事件
   */
  handleBackground(): void {
    hilog.info(0x0000, NetworkManager.TAG, 'Handling background state');
    
    // 可以选择性关闭一些非关键的WebSocket连接以节省资源
    // 或者降低心跳频率等
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 关闭所有WebSocket连接
    for (const [id, ws] of this.webSocketConnections) {
      try {
        ws.close();
      } catch (error) {
        hilog.warn(0x0000, NetworkManager.TAG, `Close WebSocket ${id} failed: ${error.message}`);
      }
    }

    this.webSocketConnections.clear();
    this.webSocketListeners.clear();
    this.reconnectAttempts.clear();

    // 清理HTTP请求
    this.httpRequest.destroy();

    hilog.info(0x0000, NetworkManager.TAG, 'NetworkManager cleaned up');
  }

  // ===================== 私有方法 =====================

  /**
   * 构建完整的URL
   */
  private buildFullUrl(path: string): string {
    const baseUrl = this.configManager.getApiBaseUrl();
    return path.startsWith('http') ? path : `${baseUrl}${path}`;
  }

  /**
   * 构建WebSocket URL
   */
  private buildWebSocketUrl(endpoint: string): string {
    const baseUrl = this.configManager.getWsBaseUrl();
    return endpoint.startsWith('ws') ? endpoint : `${baseUrl}${endpoint}`;
  }

  /**
   * 构建请求选项
   */
  private buildRequestOptions(
    method: http.RequestMethod,
    data?: any,
    options?: HttpRequestOptions
  ): http.HttpRequestOptions {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'CINA.CLUB-HarmonyOS/1.0.0',
      ...options?.headers
    };

    return {
      method: method,
      header: headers,
      extraData: data ? JSON.stringify(data) : undefined,
      expectDataType: options?.expectDataType || http.HttpDataType.STRING,
      connectTimeout: options?.timeout || this.configManager.getNetworkTimeout(),
      readTimeout: options?.timeout || this.configManager.getNetworkTimeout()
    };
  }

  /**
   * 解析响应数据
   */
  private parseResponse<T>(result: string | Object | ArrayBuffer): T {
    if (typeof result === 'string') {
      try {
        return JSON.parse(result) as T;
      } catch {
        return result as T;
      }
    }
    return result as T;
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(
    connectionId: string,
    endpoint: string,
    listener: WebSocketEventListener
  ): void {
    const attempts = this.reconnectAttempts.get(connectionId) || 0;
    
    if (attempts >= this.maxReconnectAttempts) {
      hilog.warn(0x0000, NetworkManager.TAG, `Max reconnect attempts reached for: ${connectionId}`);
      return;
    }

    const delay = this.reconnectDelay * Math.pow(2, attempts); // 指数退避
    this.reconnectAttempts.set(connectionId, attempts + 1);

    setTimeout(async () => {
      try {
        hilog.info(0x0000, NetworkManager.TAG, `Attempting to reconnect: ${connectionId}, attempt: ${attempts + 1}`);
        await this.createWebSocketConnection(endpoint, listener, connectionId);
      } catch (error) {
        hilog.error(0x0000, NetworkManager.TAG, `Reconnect failed: ${error.message}`);
      }
    }, delay);
  }
} 