# pkg/messaging

统一消息处理包，为 CINA.CLUB 平台所有微服务提供标准化的 Kafka 生产者和消费者封装。

## 功能特性

### 核心功能
- **生产者 (Producer)**: 异步、高性能的消息发布
- **消费者组 (Consumer Group)**: 可靠的消息消费与处理
- **Protobuf 序列化**: 类型安全的消息序列化/反序列化
- **分布式追踪**: 自动注入和提取 OpenTelemetry 追踪上下文
- **重试机制**: 带指数退避的智能重试策略
- **死信队列 (DLQ)**: 失败消息的可靠处理

### 可靠性保证
- **至少一次投递**: 确保消息不丢失
- **幂等消费**: 支持消费者幂等性设计
- **优雅关闭**: 完整的生命周期管理
- **错误恢复**: 自动处理各种异常情况

### 可观测性
- **结构化日志**: 详细的操作日志记录
- **性能指标**: 内置性能监控
- **追踪集成**: 完整的分布式追踪支持

## 快速开始

### 1. 生产者使用

```go
package main

import (
    "context"
    "log/slog"
    
    "pkg/messaging"
    "go.opentelemetry.io/otel/trace"
)

func main() {
    // 配置生产者
    config := messaging.KafkaProducerConfig{
        Brokers: []string{"localhost:9092"},
        WriterConfig: messaging.KafkaWriterConfig{
            BatchSize:    100,
            BatchTimeout: time.Second,
            Async:        true,
            Compression:  "snappy",
        },
    }
    
    // 创建生产者
    producer, err := messaging.NewProducer(config, slog.Default(), trace.NewNoopTracerProvider().Tracer(""))
    if err != nil {
        panic(err)
    }
    defer producer.Close()
    
    // 发布消息
    ctx := context.Background()
    message := &YourProtobufMessage{
        Field1: "value1",
        Field2: "value2",
    }
    
    err = producer.Publish(ctx, "your-topic", "message-key", message)
    if err != nil {
        log.Printf("Failed to publish: %v", err)
    }
}
```

### 2. 消费者使用

```go
package main

import (
    "context"
    "log/slog"
    
    "pkg/messaging"
    "go.opentelemetry.io/otel/trace"
    "google.golang.org/protobuf/proto"
)

// 实现消费者处理器
type MyEventHandler struct {
    // 你的业务逻辑依赖
}

func (h *MyEventHandler) Handle(ctx context.Context, msg proto.Message) error {
    switch event := msg.(type) {
    case *YourProtobufMessage:
        // 处理具体的消息类型
        return h.handleYourMessage(ctx, event)
    default:
        // 忽略未知消息类型
        return nil
    }
}

func main() {
    // 配置消费者
    config := messaging.KafkaConsumerConfig{
        Brokers: []string{"localhost:9092"},
        GroupID: "your-service-group",
        Topics:  []string{"your-topic"},
        RetryConfig: messaging.RetryConfig{
            Enabled:           true,
            MaxAttempts:       3,
            InitialInterval:   time.Second,
            BackoffMultiplier: 2.0,
        },
        DLQConfig: messaging.DLQConfig{
            Enabled:     true,
            TopicSuffix: "dlq",
        },
    }
    
    // 注册事件类型
    eventTypes := map[string]func() proto.Message{
        "your.protobuf.Message": func() proto.Message { 
            return &YourProtobufMessage{} 
        },
    }
    
    // 创建消费者组
    handler := &MyEventHandler{}
    consumer, err := messaging.NewConsumerGroup(
        config,
        handler,
        eventTypes,
        slog.Default(),
        trace.NewNoopTracerProvider().Tracer(""),
    )
    if err != nil {
        panic(err)
    }
    defer consumer.Close()
    
    // 启动消费者（阻塞操作）
    ctx := context.Background()
    if err := consumer.Run(ctx); err != nil {
        log.Printf("Consumer error: %v", err)
    }
}
```

## 配置选项

### 生产者配置

```go
type KafkaProducerConfig struct {
    Brokers  []string              // Kafka 代理地址
    Security KafkaSecurityConfig   // 安全配置 (SASL/TLS)
    WriterConfig KafkaWriterConfig // 写入器配置
}

type KafkaWriterConfig struct {
    BatchSize     int           // 批量大小 (默认: 100)
    BatchTimeout  time.Duration // 批量超时 (默认: 1s)
    Async         bool          // 异步模式 (默认: true)
    Compression   string        // 压缩算法 (默认: "snappy")
    MaxAttempts   int           // 最大重试次数 (默认: 3)
    WriteTimeout  time.Duration // 写入超时 (默认: 10s)
    ReadTimeout   time.Duration // 读取超时 (默认: 10s)
}
```

### 消费者配置

```go
type KafkaConsumerConfig struct {
    Brokers      []string             // Kafka 代理地址
    GroupID      string               // 消费者组ID
    Topics       []string             // 订阅主题列表
    Security     KafkaSecurityConfig  // 安全配置
    ReaderConfig KafkaReaderConfig    // 读取器配置
    RetryConfig  RetryConfig          // 重试配置
    DLQConfig    DLQConfig            // 死信队列配置
}

type RetryConfig struct {
    Enabled           bool          // 是否启用重试 (默认: true)
    MaxAttempts       int           // 最大重试次数 (默认: 3)
    InitialInterval   time.Duration // 初始重试间隔 (默认: 1s)
    MaxInterval       time.Duration // 最大重试间隔 (默认: 30s)
    BackoffMultiplier float64       // 退避倍数 (默认: 2.0)
    Jitter            float64       // 抖动因子 (默认: 0.1)
}
```

### 安全配置

```go
type KafkaSecurityConfig struct {
    SASL SASLConfig // SASL 认证
    TLS  TLSConfig  // TLS 加密
}

type SASLConfig struct {
    Enabled   bool   // 是否启用 SASL
    Mechanism string // 认证机制 (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512)
    Username  string // 用户名
    Password  string // 密码
}

type TLSConfig struct {
    Enabled            bool   // 是否启用 TLS
    InsecureSkipVerify bool   // 跳过证书验证
    CertFile           string // 证书文件路径
    KeyFile            string // 私钥文件路径
    CAFile             string // CA 证书文件路径
}
```

## 最佳实践

### 1. 消息设计

- **使用 Protocol Buffers**: 确保类型安全和向后兼容性
- **事件命名**: 使用清晰的事件类型命名 (例如: `user.registered`, `order.created`)
- **版本管理**: 为消息添加版本字段，支持平滑升级

### 2. 生产者最佳实践

- **批量发送**: 启用批量模式提高吞吐量
- **异步模式**: 使用异步发送避免阻塞业务逻辑
- **错误处理**: 实现适当的错误处理和监控
- **幂等性**: 设计幂等的消息发送逻辑

### 3. 消费者最佳实践

- **幂等处理**: 确保消息处理逻辑是幂等的
- **快速失败**: 对于不可重试的错误快速失败
- **资源管理**: 正确管理数据库连接等资源
- **监控告警**: 监控消费延迟和错误率

### 4. 运维最佳实践

- **主题规划**: 合理规划主题和分区数量
- **监控指标**: 监控生产和消费的关键指标
- **日志收集**: 收集和分析应用日志
- **容量规划**: 根据业务增长规划集群容量

## 错误处理

### 重试策略

包提供了灵活的重试机制：

```go
// 标记错误为可重试
err := messaging.WrapRetryableError(someError)

// 标记错误为不可重试
err := messaging.WrapNonRetryableError(someError)

// 检查错误是否可重试
if messaging.IsRetryableError(err) {
    // 处理可重试错误
}
```

### 死信队列

当消息处理失败且超过最大重试次数时，消息会被发送到死信队列：

- DLQ 主题格式: `{原始主题}.{后缀}` (例如: `user-events.dlq`)
- 包含完整的原始消息和错误信息
- 支持后续的手动处理和重新投递

## 性能调优

### 生产者调优

- **批量大小**: 根据消息大小和延迟要求调整
- **压缩算法**: 选择合适的压缩算法 (推荐 snappy)
- **异步模式**: 启用异步模式提高吞吐量

### 消费者调优

- **并发处理**: 消费者自动为每条消息创建 goroutine
- **批量提交**: 调整提交间隔平衡性能和可靠性
- **内存管理**: 控制最大字节数避免内存溢出

## 监控指标

包自动记录以下关键指标：

- 消息发送成功/失败数
- 消息处理延迟
- 重试次数统计
- DLQ 消息数量
- 连接状态

## 测试支持

包提供了完整的测试支持：

```go
// 模拟生产者
producer := messaging.NewMockProducer()

// 模拟消费者处理器
handler := messaging.NewMockConsumerHandler()

// 在测试中验证消息
messages := producer.GetMessages()
assert.Equal(t, 1, len(messages))
assert.Equal(t, "test-topic", messages[0].Topic)
```

## 版本兼容性

- **Go 版本**: 需要 Go 1.22+
- **Kafka 版本**: 兼容 Kafka 2.0+
- **依赖管理**: 使用 Go Modules

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 编写测试
4. 提交代码
5. 创建 Pull Request

## 许可证

Copyright (c) 2025 Cina.Club  
All rights reserved. 