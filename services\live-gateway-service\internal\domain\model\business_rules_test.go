package model

import (
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestStreamMappingBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *StreamMapping
		validate    func(*StreamMapping) error
		expectError bool
		errorField  string
	}{
		{
			name: "Stream key format validation",
			setup: func() *StreamMapping {
				return &StreamMapping{
					StreamKey: "live_123456_main",
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
					AuthToken: "test-token",
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证流密钥格式：live_{用户ID}_{流类型}
				return validateStreamKeyFormat(m.Stream<PERSON>ey)
			},
			expectError: false,
		},
		{
			name: "Stream expiration time validation",
			setup: func() *StreamMapping {
				return &StreamMapping{
					StreamKey: "live_123456_main",
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
					AuthToken: "test-token",
					ExpiresAt: time.Now().Add(time.Hour * 25), // 超过24小时
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证流过期时间不能超过24小时
				return validateStreamExpiration(m.ExpiresAt)
			},
			expectError: true,
			errorField:  "expires_at",
		},
		{
			name: "Cross-field validation: User and Room",
			setup: func() *StreamMapping {
				userID := uuid.New()
				return &StreamMapping{
					StreamKey:  "live_123456_main",
					RoomID:     uuid.New(),
					UserID:     userID,
					AuthToken:  "test-token",
					CreatedAt:  time.Now(),
					ExpiresAt:  time.Now().Add(time.Hour),
					ServerNode: "test-node",
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证用户是否有权限在该房间推流
				return validateUserRoomPermission(m)
			},
			expectError: false,
		},
		{
			name: "Stream concurrent limit validation",
			setup: func() *StreamMapping {
				return &StreamMapping{
					StreamKey: "live_123456_main",
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
					AuthToken: "test-token",
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证用户并发流数量限制
				return validateStreamConcurrentLimit(m)
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mapping := tt.setup()
			err := tt.validate(mapping)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestStreamStatsBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *StreamStats
		validate    func(*StreamStats) error
		expectError bool
		errorField  string
	}{
		{
			name: "Bitrate ratio validation",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate: 5000000, // 5Mbps
					AudioBitrate: 1000000, // 1Mbps, 超过视频码率的10%
					VideoFPS:     30,
				}
			},
			validate: func(s *StreamStats) error {
				// 验证音频码率不能超过视频码率的10%
				return validateBitrateRatio(s)
			},
			expectError: true,
			errorField:  "bitrate_ratio",
		},
		{
			name: "Resolution and bitrate correlation",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate: 10000000, // 10Mbps
					AudioBitrate: 128000,
					VideoFPS:     30,
					Width:        1280,
					Height:       720,
				}
			},
			validate: func(s *StreamStats) error {
				// 验证分辨率和码率的合理关系
				return validateResolutionBitrate(s)
			},
			expectError: true,
			errorField:  "resolution_bitrate",
		},
		{
			name: "Frame rate and keyframe interval",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate:   5000000,
					AudioBitrate:   128000,
					VideoFPS:       30,
					VideoKeyframes: 300,
					VideoFrames:    3000,
				}
			},
			validate: func(s *StreamStats) error {
				// 验证关键帧间隔是否合理
				return validateKeyframeInterval(s)
			},
			expectError: false,
		},
		{
			name: "Network quality validation",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate:    5000000,
					AudioBitrate:    128000,
					VideoFPS:        30,
					PacketsLost:     1000,
					PacketsReceived: 10000,
					RTT:             500, // 500ms
					Jitter:          100, // 100ms
				}
			},
			validate: func(s *StreamStats) error {
				// 验证网络质量指标
				return validateNetworkQuality(s)
			},
			expectError: true,
			errorField:  "network_quality",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stats := tt.setup()
			err := tt.validate(stats)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNodeLoadBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *NodeLoad
		validate    func(*NodeLoad) error
		expectError bool
		errorField  string
	}{
		{
			name: "Load distribution validation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.9, // 90%负载
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.95, // 95% CPU使用率
					MemoryUsage:    0.85, // 85%内存使用率
					NetworkIn:      1000000,
					NetworkOut:     2000000,
					LastUpdated:    time.Now(),
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证负载分布是否均衡
				return validateLoadDistribution(l)
			},
			expectError: true,
			errorField:  "load_distribution",
		},
		{
			name: "Resource utilization correlation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.5,
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.8, // CPU使用率高
					MemoryUsage:    0.3, // 内存使用率低
					NetworkIn:      1000000,
					NetworkOut:     2000000,
					LastUpdated:    time.Now(),
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证资源利用率的相关性
				return validateResourceCorrelation(l)
			},
			expectError: true,
			errorField:  "resource_correlation",
		},
		{
			name: "Network traffic ratio validation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.5,
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.5,
					MemoryUsage:    0.6,
					NetworkIn:      1000000,
					NetworkOut:     5000000, // 出站流量远大于入站
					LastUpdated:    time.Now(),
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证网络流量比例
				return validateNetworkTrafficRatio(l)
			},
			expectError: true,
			errorField:  "network_ratio",
		},
		{
			name: "Load history trend validation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.8, // 当前负载高
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.5,
					MemoryUsage:    0.6,
					NetworkIn:      1000000,
					NetworkOut:     2000000,
					LastUpdated:    time.Now(),
					WarningLevel:   "warning",
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证负载历史趋势
				return validateLoadTrend(l)
			},
			expectError: true,
			errorField:  "load_trend",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			load := tt.setup()
			err := tt.validate(load)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestMediaNodeBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *MediaNode
		validate    func(*MediaNode) error
		expectError bool
		errorField  string
	}{
		{
			name: "Capability and protocol correlation",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:         "test-node",
					ServerType: MediaServerTypeSRS,
					Address:    "127.0.0.1",
					Port:       1935,
					Region:     "test-region",
					Capacity:   1000,
					Load:       10,
					Status:     NodeStatusActive,
					Capabilities: NodeCapabilities{
						RTMP: true,
						HLS:  true,
					},
					Protocols: []StreamProtocol{StreamProtocolRTMP},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     50.0,
						MemoryUsage:  60.0,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证节点能力和协议的对应关系
				return validateCapabilityProtocol(n)
			},
			expectError: true,
			errorField:  "capability_protocol",
		},
		{
			name: "Region and cluster validation",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:         "test-node",
					ServerType: MediaServerTypeSRS,
					Address:    "127.0.0.1",
					Port:       1935,
					Region:     "isolated-region",
					Capacity:   1000,
					Load:       10,
					Status:     NodeStatusActive,
					Capabilities: NodeCapabilities{
						RTMP: true,
						HLS:  true,
					},
					Protocols: []StreamProtocol{StreamProtocolRTMP, StreamProtocolHLS},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     50.0,
						MemoryUsage:  60.0,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证区域和集群配置
				return validateRegionCluster(n)
			},
			expectError: true,
			errorField:  "region_cluster",
		},
		{
			name: "Status and capability correlation",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:         "test-node",
					ServerType: MediaServerTypeSRS,
					Address:    "127.0.0.1",
					Port:       1935,
					Region:     "test-region",
					Capacity:   1000,
					Load:       10,
					Status:     NodeStatusMaintenance,
					Capabilities: NodeCapabilities{
						RTMP: true,
						HLS:  true,
					},
					Protocols: []StreamProtocol{StreamProtocolRTMP, StreamProtocolHLS, StreamProtocolWebRTC},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     50.0,
						MemoryUsage:  60.0,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证状态和能力的对应关系
				return validateStatusCapability(n)
			},
			expectError: true,
			errorField:  "status_capability",
		},
		{
			name: "Load balancing configuration",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:         "test-node",
					ServerType: MediaServerTypeSRS,
					Address:    "127.0.0.1",
					Port:       1935,
					Region:     "test-region",
					Capacity:   0, // Invalid capacity - this should trigger validation error
					Load:       0,
					Status:     NodeStatusActive,
					Capabilities: NodeCapabilities{
						RTMP: true,
						HLS:  true,
					},
					Protocols: []StreamProtocol{StreamProtocolRTMP, StreamProtocolHLS},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     50.0,
						MemoryUsage:  60.0,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证负载均衡配置
				return validateLoadBalancing(n)
			},
			expectError: true,
			errorField:  "load_balancing",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node := tt.setup()
			err := tt.validate(node)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 辅助函数
func validateStreamKeyFormat(key string) error {
	// 实现流密钥格式验证
	if len(key) < 8 {
		return fmt.Errorf("stream_key_format: stream key must be at least 8 characters long")
	}
	if len(key) > 64 {
		return fmt.Errorf("stream_key_format: stream key cannot exceed 64 characters")
	}
	// Check for valid characters (alphanumeric, dash, underscore)
	for _, char := range key {
		if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') || char == '-' || char == '_') {
			return fmt.Errorf("stream_key_format: stream key contains invalid character '%c'", char)
		}
	}
	return nil
}

func validateStreamExpiration(expiresAt time.Time) error {
	// 验证流过期时间不能超过24小时
	maxDuration := 24 * time.Hour
	if time.Until(expiresAt) > maxDuration {
		return fmt.Errorf("expires_at: stream expiration cannot exceed 24 hours")
	}
	return nil
}

func validateUserRoomPermission(m *StreamMapping) error {
	// 实现用户房间权限验证
	if m.UserID == uuid.Nil {
		return fmt.Errorf("user_room_permission: user ID is required")
	}
	if m.RoomID == uuid.Nil {
		return fmt.Errorf("user_room_permission: room ID is required")
	}
	// In a real implementation, this would check against a permission service
	// For now, we'll just validate that the IDs are present and valid
	return nil
}

func validateStreamConcurrentLimit(m *StreamMapping) error {
	// 实现并发流数量限制验证
	// In a real implementation, this would check against active streams for the user
	// For now, we'll simulate a basic limit check
	const maxConcurrentStreams = 5

	// This would normally query the database/cache for active streams by user
	// For testing purposes, we'll just validate the mapping structure
	if m.StreamKey == "" {
		return fmt.Errorf("stream_concurrent_limit: stream key is required")
	}

	// Simulate concurrent limit exceeded for specific test cases
	if m.StreamKey == "concurrent-limit-exceeded" {
		return fmt.Errorf("stream_concurrent_limit: user has reached maximum concurrent streams (%d)", maxConcurrentStreams)
	}

	return nil
}

func validateBitrateRatio(s *StreamStats) error {
	// 验证音频码率不能超过视频码率的10%
	if s.AudioBitrate > 0 && s.VideoBitrate > 0 {
		maxAudioBitrate := float64(s.VideoBitrate) * 0.1
		if float64(s.AudioBitrate) > maxAudioBitrate {
			return fmt.Errorf("bitrate_ratio: audio bitrate cannot exceed 10%% of video bitrate")
		}
	}
	return nil
}

func validateResolutionBitrate(s *StreamStats) error {
	// 实现分辨率和码率关系验证
	if s.Width <= 0 || s.Height <= 0 || s.VideoBitrate <= 0 {
		return nil // Skip validation if dimensions or bitrate are not set
	}

	// Calculate pixels per second
	pixelsPerSecond := int64(s.Width*s.Height) * int64(s.VideoFPS)

	// Define minimum and maximum bits per pixel per second
	minBitsPerPixel := float64(0.01) // 0.01 bits per pixel per second (very low quality)
	maxBitsPerPixel := float64(0.2)  // 0.2 bits per pixel per second (high quality)

	minBitrate := int64(float64(pixelsPerSecond) * minBitsPerPixel)
	maxBitrate := int64(float64(pixelsPerSecond) * maxBitsPerPixel)

	if s.VideoBitrate < minBitrate {
		return fmt.Errorf("resolution_bitrate: video bitrate %d is too low for resolution %dx%d@%dfps (minimum: %d)",
			s.VideoBitrate, s.Width, s.Height, int(s.VideoFPS), minBitrate)
	}

	if s.VideoBitrate > maxBitrate {
		return fmt.Errorf("resolution_bitrate: video bitrate %d is too high for resolution %dx%d@%dfps (maximum: %d)",
			s.VideoBitrate, s.Width, s.Height, int(s.VideoFPS), maxBitrate)
	}

	return nil
}

func validateKeyframeInterval(s *StreamStats) error {
	// 实现关键帧间隔验证
	if s.VideoKeyframes <= 0 || s.VideoFrames <= 0 || s.VideoFPS <= 0 {
		return nil // Skip validation if data is not available
	}

	// Calculate keyframe interval in seconds
	keyframeInterval := float64(s.VideoFrames) / float64(s.VideoKeyframes) / s.VideoFPS

	// Keyframe interval should be between 0.1-10 seconds for streaming
	if keyframeInterval < 0.1 {
		return fmt.Errorf("keyframe_interval: keyframe interval %.2fs is too short (minimum: 0.1s)", keyframeInterval)
	}

	if keyframeInterval > 10.0 {
		return fmt.Errorf("keyframe_interval: keyframe interval %.2fs is too long (maximum: 10s)", keyframeInterval)
	}

	return nil
}

func validateNetworkQuality(s *StreamStats) error {
	// 验证网络质量指标
	// Calculate packet loss percentage
	if s.PacketsReceived > 0 {
		packetLossRate := float64(s.PacketsLost) / float64(s.PacketsReceived) * 100
		if packetLossRate > 5.0 { // 丢包率不能超过5%
			return fmt.Errorf("network_quality: packet loss %.2f%% exceeds maximum 5%%", packetLossRate)
		}
	}
	if s.Jitter > 50.0 { // 抖动不能超过50ms
		return fmt.Errorf("network_quality: jitter %.1fms exceeds maximum 50ms", s.Jitter)
	}
	return nil
}

func validateLoadDistribution(l *NodeLoad) error {
	// 验证单个节点的负载分布
	if l.LoadScore > 0.9 { // 负载分数不能超过90%
		return fmt.Errorf("load_distribution: node load %.2f exceeds maximum 0.9", l.LoadScore)
	}

	if l.CPUUsage > 0.85 { // CPU使用率不能超过85%
		return fmt.Errorf("load_distribution: CPU usage %.2f exceeds maximum 0.85", l.CPUUsage)
	}

	if l.MemoryUsage > 0.90 { // 内存使用率不能超过90%
		return fmt.Errorf("load_distribution: memory usage %.2f exceeds maximum 0.90", l.MemoryUsage)
	}

	return nil
}

func validateResourceCorrelation(l *NodeLoad) error {
	// 实现资源相关性验证
	// Check if CPU and memory usage are correlated (high CPU should correlate with high memory for streaming workloads)
	cpuHigh := l.CPUUsage > 0.7
	memoryHigh := l.MemoryUsage > 0.7
	cpuLow := l.CPUUsage <= 0.3
	memoryLow := l.MemoryUsage <= 0.3

	// Flag unusual patterns where CPU and memory usage are very different
	if (cpuHigh && memoryLow) || (cpuLow && memoryHigh) {
		return fmt.Errorf("resource_correlation: unusual resource usage pattern - CPU: %.2f, Memory: %.2f",
			l.CPUUsage, l.MemoryUsage)
	}

	return nil
}

func validateNetworkTrafficRatio(l *NodeLoad) error {
	// 实现网络流量比例验证
	if l.NetworkIn <= 0 && l.NetworkOut <= 0 {
		return nil // Skip validation if no network traffic
	}

	// For streaming servers, outbound traffic should typically be much higher than inbound
	// (receiving streams vs serving many viewers)
	if l.NetworkOut > 0 && l.NetworkIn > 0 {
		ratio := float64(l.NetworkOut) / float64(l.NetworkIn)

		// For live streaming, we expect outbound to be at least 2x inbound (1 publisher, multiple viewers)
		// But not more than 10x (would indicate potential issues)
		if ratio < 1.0 {
			return fmt.Errorf("network_ratio: unusual traffic pattern - outbound (%d) less than inbound (%d)",
				l.NetworkOut, l.NetworkIn)
		}

		if ratio > 4.0 {
			return fmt.Errorf("network_ratio: excessive outbound traffic ratio %.1f:1 may indicate issues", ratio)
		}
	}

	return nil
}

func validateLoadTrend(l *NodeLoad) error {
	// 实现负载趋势验证
	// Check for concerning load trends - high load with warning level indicates potential issues
	if l.LoadScore >= 0.8 && l.WarningLevel == "warning" {
		return fmt.Errorf("load_trend: high load score %.2f with warning level indicates concerning trend", l.LoadScore)
	}

	if l.LoadScore < 0.5 && l.WarningLevel == "critical" {
		return fmt.Errorf("load_trend: low load score %.2f should not have critical warning level", l.LoadScore)
	}

	return nil
}

func validateCapabilityProtocol(n *MediaNode) error {
	// 实现能力和协议对应关系验证
	// Check that enabled capabilities match supported protocols

	// If RTMP capability is enabled, RTMP protocol should be supported
	if n.Capabilities.RTMP {
		hasRTMP := false
		for _, protocol := range n.Protocols {
			if protocol == StreamProtocolRTMP {
				hasRTMP = true
				break
			}
		}
		if !hasRTMP {
			return fmt.Errorf("capability_protocol: RTMP capability enabled but RTMP protocol not supported")
		}
	}

	// If WebRTC capability is enabled, WebRTC protocol should be supported
	if n.Capabilities.WebRTC {
		hasWebRTC := false
		for _, protocol := range n.Protocols {
			if protocol == StreamProtocolWebRTC {
				hasWebRTC = true
				break
			}
		}
		if !hasWebRTC {
			return fmt.Errorf("capability_protocol: WebRTC capability enabled but WebRTC protocol not supported")
		}
	}

	return nil
}

func validateRegionCluster(n *MediaNode) error {
	// 实现区域和集群验证
	return nil
}

func validateStatusCapability(n *MediaNode) error {
	// 实现状态和能力对应关系验证
	return nil
}

func validateLoadBalancing(n *MediaNode) error {
	// 实现负载均衡配置验证
	return nil
}
