package model

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestStreamMappingBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *StreamMapping
		validate    func(*StreamMapping) error
		expectError bool
		errorField  string
	}{
		{
			name: "Stream key format validation",
			setup: func() *StreamMapping {
				return &StreamMapping{
					StreamKey: "live_123456_main",
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
					AuthToken: "test-token",
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证流密钥格式：live_{用户ID}_{流类型}
				return validateStreamKeyFormat(m.Stream<PERSON>ey)
			},
			expectError: false,
		},
		{
			name: "Stream expiration time validation",
			setup: func() *StreamMapping {
				return &StreamMapping{
					StreamKey: "live_123456_main",
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
					AuthToken: "test-token",
					ExpiresAt: time.Now().Add(time.Hour * 25), // 超过24小时
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证流过期时间不能超过24小时
				return validateStreamExpiration(m.ExpiresAt)
			},
			expectError: true,
			errorField:  "expires_at",
		},
		{
			name: "Cross-field validation: User and Room",
			setup: func() *StreamMapping {
				userID := uuid.New()
				return &StreamMapping{
					StreamKey:  "live_123456_main",
					RoomID:     uuid.New(),
					UserID:     userID,
					AuthToken:  "test-token",
					CreatedAt:  time.Now(),
					ExpiresAt:  time.Now().Add(time.Hour),
					ServerNode: "test-node",
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证用户是否有权限在该房间推流
				return validateUserRoomPermission(m)
			},
			expectError: false,
		},
		{
			name: "Stream concurrent limit validation",
			setup: func() *StreamMapping {
				return &StreamMapping{
					StreamKey: "live_123456_main",
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
					AuthToken: "test-token",
				}
			},
			validate: func(m *StreamMapping) error {
				// 验证用户并发流数量限制
				return validateStreamConcurrentLimit(m)
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mapping := tt.setup()
			err := tt.validate(mapping)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestStreamStatsBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *StreamStats
		validate    func(*StreamStats) error
		expectError bool
		errorField  string
	}{
		{
			name: "Bitrate ratio validation",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate: 5000000, // 5Mbps
					AudioBitrate: 1000000, // 1Mbps, 超过视频码率的10%
					VideoFPS:     30,
				}
			},
			validate: func(s *StreamStats) error {
				// 验证音频码率不能超过视频码率的10%
				return validateBitrateRatio(s)
			},
			expectError: true,
			errorField:  "bitrate_ratio",
		},
		{
			name: "Resolution and bitrate correlation",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate: 10000000, // 10Mbps
					AudioBitrate: 128000,
					VideoFPS:     30,
					Width:        1280,
					Height:       720,
				}
			},
			validate: func(s *StreamStats) error {
				// 验证分辨率和码率的合理关系
				return validateResolutionBitrate(s)
			},
			expectError: true,
			errorField:  "resolution_bitrate",
		},
		{
			name: "Frame rate and keyframe interval",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate:   5000000,
					AudioBitrate:   128000,
					VideoFPS:       30,
					VideoKeyframes: 300,
					VideoFrames:    3000,
				}
			},
			validate: func(s *StreamStats) error {
				// 验证关键帧间隔是否合理
				return validateKeyframeInterval(s)
			},
			expectError: false,
		},
		{
			name: "Network quality validation",
			setup: func() *StreamStats {
				return &StreamStats{
					VideoBitrate:    5000000,
					AudioBitrate:    128000,
					VideoFPS:        30,
					PacketsLost:     1000,
					PacketsReceived: 10000,
					RTT:             500, // 500ms
					Jitter:          100, // 100ms
				}
			},
			validate: func(s *StreamStats) error {
				// 验证网络质量指标
				return validateNetworkQuality(s)
			},
			expectError: true,
			errorField:  "network_quality",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stats := tt.setup()
			err := tt.validate(stats)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNodeLoadBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *NodeLoad
		validate    func(*NodeLoad) error
		expectError bool
		errorField  string
	}{
		{
			name: "Load distribution validation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.9, // 90%负载
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.95, // 95% CPU使用率
					MemoryUsage:    0.85, // 85%内存使用率
					NetworkIn:      1000000,
					NetworkOut:     2000000,
					LastUpdated:    time.Now(),
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证负载分布是否均衡
				return validateLoadDistribution(l)
			},
			expectError: true,
			errorField:  "load_distribution",
		},
		{
			name: "Resource utilization correlation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.5,
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.8, // CPU使用率高
					MemoryUsage:    0.3, // 内存使用率低
					NetworkIn:      1000000,
					NetworkOut:     2000000,
					LastUpdated:    time.Now(),
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证资源利用率的相关性
				return validateResourceCorrelation(l)
			},
			expectError: true,
			errorField:  "resource_correlation",
		},
		{
			name: "Network traffic ratio validation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.5,
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.5,
					MemoryUsage:    0.6,
					NetworkIn:      1000000,
					NetworkOut:     5000000, // 出站流量远大于入站
					LastUpdated:    time.Now(),
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证网络流量比例
				return validateNetworkTrafficRatio(l)
			},
			expectError: true,
			errorField:  "network_ratio",
		},
		{
			name: "Load history trend validation",
			setup: func() *NodeLoad {
				return &NodeLoad{
					NodeID:         "test-node",
					LoadScore:      0.8, // 当前负载高
					CurrentStreams: 10,
					CurrentBitrate: 50000000,
					CPUUsage:       0.5,
					MemoryUsage:    0.6,
					NetworkIn:      1000000,
					NetworkOut:     2000000,
					LastUpdated:    time.Now(),
					WarningLevel:   "warning",
				}
			},
			validate: func(l *NodeLoad) error {
				// 验证负载历史趋势
				return validateLoadTrend(l)
			},
			expectError: true,
			errorField:  "load_trend",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			load := tt.setup()
			err := tt.validate(load)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestMediaNodeBusinessRules(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *MediaNode
		validate    func(*MediaNode) error
		expectError bool
		errorField  string
	}{
		{
			name: "Capability and protocol correlation",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:        "test-node",
					Region:    "test-region",
					Capacity:  1000,
					Load:      10,
					Status:    NodeStatusActive,
					Protocols: []StreamProtocol{StreamProtocolRTMP},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     0.5,
						MemoryUsage:  0.6,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证节点能力和协议的对应关系
				return validateCapabilityProtocol(n)
			},
			expectError: true,
			errorField:  "capability_protocol",
		},
		{
			name: "Region and cluster validation",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:        "test-node",
					Region:    "isolated-region",
					Capacity:  1000,
					Load:      10,
					Status:    NodeStatusActive,
					Protocols: []StreamProtocol{StreamProtocolRTMP, StreamProtocolHLS},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     0.5,
						MemoryUsage:  0.6,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证区域和集群配置
				return validateRegionCluster(n)
			},
			expectError: true,
			errorField:  "region_cluster",
		},
		{
			name: "Status and capability correlation",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:        "test-node",
					Region:    "test-region",
					Capacity:  1000,
					Load:      10,
					Status:    NodeStatusMaintenance,
					Protocols: []StreamProtocol{StreamProtocolRTMP, StreamProtocolHLS, StreamProtocolWebRTC},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     0.5,
						MemoryUsage:  0.6,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证状态和能力的对应关系
				return validateStatusCapability(n)
			},
			expectError: true,
			errorField:  "status_capability",
		},
		{
			name: "Load balancing configuration",
			setup: func() *MediaNode {
				return &MediaNode{
					ID:        "test-node",
					Region:    "test-region",
					Capacity:  0, // Invalid capacity
					Load:      0,
					Status:    NodeStatusActive,
					Protocols: []StreamProtocol{StreamProtocolRTMP, StreamProtocolHLS},
					Stats: &NodeStats{
						NodeID:       "test-node",
						CPUUsage:     0.5,
						MemoryUsage:  0.6,
						NetworkIn:    1000000,
						NetworkOut:   2000000,
						StreamCount:  10,
						ClientCount:  100,
						IsAvailable:  true,
						LastSeen:     time.Now().Format(time.RFC3339),
						ErrorCount:   0,
						WarningCount: 0,
					},
				}
			},
			validate: func(n *MediaNode) error {
				// 验证负载均衡配置
				return validateLoadBalancing(n)
			},
			expectError: true,
			errorField:  "load_balancing",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node := tt.setup()
			err := tt.validate(node)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errorField)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 辅助函数
func validateStreamKeyFormat(key string) error {
	// 实现流密钥格式验证
	return nil
}

func validateStreamExpiration(expiresAt time.Time) error {
	// 实现流过期时间验证
	return nil
}

func validateUserRoomPermission(m *StreamMapping) error {
	// 实现用户房间权限验证
	return nil
}

func validateStreamConcurrentLimit(m *StreamMapping) error {
	// 实现并发流数量限制验证
	return nil
}

func validateBitrateRatio(s *StreamStats) error {
	// 实现码率比例验证
	return nil
}

func validateResolutionBitrate(s *StreamStats) error {
	// 实现分辨率和码率关系验证
	return nil
}

func validateKeyframeInterval(s *StreamStats) error {
	// 实现关键帧间隔验证
	return nil
}

func validateNetworkQuality(s *StreamStats) error {
	// 实现网络质量验证
	return nil
}

func validateLoadDistribution(l *NodeLoad) error {
	// 实现负载分布验证
	return nil
}

func validateResourceCorrelation(l *NodeLoad) error {
	// 实现资源相关性验证
	return nil
}

func validateNetworkTrafficRatio(l *NodeLoad) error {
	// 实现网络流量比例验证
	return nil
}

func validateLoadTrend(l *NodeLoad) error {
	// 实现负载趋势验证
	return nil
}

func validateCapabilityProtocol(n *MediaNode) error {
	// 实现能力和协议对应关系验证
	return nil
}

func validateRegionCluster(n *MediaNode) error {
	// 实现区域和集群验证
	return nil
}

func validateStatusCapability(n *MediaNode) error {
	// 实现状态和能力对应关系验证
	return nil
}

func validateLoadBalancing(n *MediaNode) error {
	// 实现负载均衡配置验证
	return nil
}
