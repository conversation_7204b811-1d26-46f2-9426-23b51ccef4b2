好的，遵照您的指示。我将为您生成一份专门针对 **`wallet-meta-service`** 的、极致细化的、生产级的**内部架构设计文档**。

这份文档将基于您提供的`wallet-meta-service-srs.md` (v1.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将阐明服务如何管理钱包的个性化数据，并与其他服务协同，为用户提供丰富的钱包体验。

---
### CINA.CLUB - `wallet-meta-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-28
**参考SRS**: `wallet-meta-service-srs.md` (v1.0)
**核心架构**: 整洁架构 (Clean Architecture) - CRUD密集型服务

## 1. 概述

`wallet-meta-service` 是一个典型的**业务逻辑相对简单，但对数据一致性和CRUD性能要求较高**的服务。其核心是为用户的每个区块链地址，提供一个可供个性化配置的“元数据档案”。

**核心技术挑战**:
1.  **数据建模**: 如何设计清晰、可扩展的数据库表来存储钱包、自定义代币和地址簿等一对多的关系。
2.  **数据隔离**: 如何在代码和数据库层面，都严格保证用户只能访问和修改自己的元数据。
3.  **API设计**: 如何提供简洁、易用的API，供客户端进行所有元数据的管理。
4.  **服务协同**: 如何优雅地与`blockchain-gateway-service`等其他服务进行可选的交互（如获取代币信息）。

本架构通过标准的**整洁架构**模式，构建一个健壮、可维护的CRUD服务。

---

## 2. 架构图与目录结构

### 2.1 整洁架构分层图

```mermaid
graph TD
    subgraph "外部世界 (Frameworks & Drivers)"
        A[gRPC/REST Server<br/>(adapter/transport)]
        B[PostgreSQL<br/>(adapter/repository)]
        C[Blockchain Gateway Client<br/>(adapter/client)]
    end
    
    subgraph "应用层 (Application)"
        E[WalletMetaService<br/>(application/service)]
    end
    
    subgraph "领域层 (Domain)"
        F[Wallet, CustomToken, AddressBookContact<br/>(domain/model)]
        G[Repository & Service Interfaces<br/>(application/port)]
    end
    
    A -- "调用" --> E
    E -- "使用" --> G
    B -- "实现" --> G
    E -- "调用(可选)" --> C
    E -- "操作" --> F
```

### 2.2 最终目录结构 (`services/wallet-meta-service/`)

```
wallet-meta-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   ├── server.go
│   │   │   └── handler.go          # gRPC Handler实现
│   │   ├── repository/
│   │   │   ├── model.go            # 数据库实体模型
│   │   │   └── postgres_repo.go    # 实现了Repository接口
│   │   └── client/
│   │       └── bc_gateway_client.go # 与blockchain-gateway-service交互的客户端
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── service.go          # WalletMetaService接口的实现
│   └── domain/
│       ├── model/
│       │   └── alias.go            # 使用/core/models中的类型
│       └── factory/
│           └── wallet_factory.go   # 创建新Wallet对象的工厂
└── ...
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Data & Rules)

由于本服务业务逻辑相对简单，领域层主要负责定义核心的数据结构和创建这些结构的工厂。

*   **`domain/model/`**:
    *   直接使用或别名化`/core/models`中定义的`Wallet`, `CustomToken`, `AddressBookContact`等`struct`。这些是业务的语言。
*   **`domain/factory/`**:
    *   `wallet_factory.go`: 提供`NewWallet`, `NewCustomToken`等工厂函数。
    *   **职责**: 封装创建新领域对象的逻辑，并执行最基础的业务不变量校验。例如，`NewCustomToken`会校验传入的合约地址是否是合法的地址格式。

### 3.2 `application/` - 应用层 (The Use Cases)

这是所有业务流程的编排者。

*   **`application/port/`**: 定义接口。
    *   `repository.go`: 定义`WalletMetaRepository`接口，包含所有数据库操作，如`CreateWallet`, `GetWalletsByUserID`, `AddCustomToken`, `DeleteContact`等。
    *   `service.go`: 定义`WalletMetaService`接口，对应所有暴露给客户端的业务用例。
*   **`application/service/service.go`**:
    *   `WalletMetaService`的实现。它依赖注入`Repository`和可选的`BlockchainGatewayClient`。
    *   **工作流程 (以`AddCustomToken`为例)**:
        1.  **权限校验**: 从`ctx`中获取`userID`，并调用`repo.GetWalletByID(ctx, walletID)`。检查返回的钱包是否属于该用户。**这是保证数据隔离的核心**。
        2.  **输入验证**: 调用`domain`工厂或验证函数，检查`contractAddress`的格式是否正确。
        3.  **持久化**: 调用`repo.AddCustomToken(ctx, walletID, tokenData)`。
        4.  **异步数据丰富 (Optional Enhancement)**:
            a. 在成功持久化后，**异步地**（通过启动一个新的goroutine或发送到内部任务队列）调用`bcGatewayClient.GetTokenInfo(chain, contractAddress)`。
            b. 如果成功获取到代币的`symbol`和`decimals`，则再次调用`repo.UpdateCustomTokenMetadata(...)`来更新记录。
            c. **设计决策**: 这个丰富操作必须是异步且可失败的，不能阻塞主`AddCustomToken`流程的响应。

### 3.3 `adapter/` - 适配层 (The Bridge)

*   **`adapter/repository/`**:
    *   `model.go`: 定义与`user_wallets`, `wallet_custom_tokens`等数据库表完全匹配的`struct`，带ORM或`db`标签。
    *   `postgres_repo.go`: `WalletMetaRepository`接口的具体PostgreSQL实现。它负责执行所有SQL查询，并在领域模型和持久化模型之间进行转换。
*   **`adapter/client/`**:
    *   `bc_gateway_client.go`: 封装对`blockchain-gateway-service`的gRPC客户端调用。包含错误处理和重试逻辑。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现`wallet-meta-service.proto`中定义的gRPC服务。它接收请求，从`ctx`中提取认证信息，然后调用`application.WalletMetaService`中对应的方法。

## 4. 数据库设计 (PostgreSQL)

数据库表结构基本与SRS中定义的一致。

*   **`user_wallets`**:
    *   `id (PK, UUID)`
    *   `user_id (FK, INDEX)`
    *   `address (VARCHAR(255))`
    *   `chain (VARCHAR(50))`
    *   `name (VARCHAR(100))`
    *   **复合唯一索引**: `UNIQUE (user_id, address, chain)`，确保一个用户的一个地址在一条链上只有一个档案。
*   **`wallet_custom_tokens`**:
    *   `id (PK, UUID)`
    *   `wallet_id (FK, INDEX)`
    *   `contract_address (VARCHAR(255))`
    *   `chain` (VARCHAR(50)) - *冗余字段，便于查询*
    *   `symbol`, `decimals` (nullable)
    *   **复合唯一索引**: `UNIQUE (wallet_id, contract_address)`。
*   **`user_address_book`**:
    *   `id (PK, UUID)`
    *   `user_id (FK, INDEX)`
    *   `address`, `chain`, `name`, `notes`
    *   **复合唯一索引**: `UNIQUE (user_id, address, chain)`。

**索引策略**:
*   所有`user_id`和`wallet_id`外键都必须建立索引，因为绝大多数查询都是以用户或钱包为起点的。
*   复合唯一索引用于保证数据的完整性。

## 5. 总结

本架构设计通过以下关键点，构建了一个生产级的`wallet-meta-service`：
1.  **清晰的职责**: 严格遵循整洁架构，将业务逻辑（`application`）、数据模型（`domain`）和基础设施交互（`adapter`）完全分离。
2.  **安全第一**: **权限校验是每个业务用例的第一步**，通过在`application`层强制检查`userID`与所操作资源的归属关系，来保证严格的数据隔离。
3.  **性能与体验的平衡**:
    *   核心的CRUD操作是同步的、快速响应的。
    *   可选的、可能耗时的数据丰富操作（如从链上获取token信息）被设计为**异步执行**，不影响主流程的用户体验。
4.  **解耦的服务协同**: 与`blockchain-gateway-service`的交互是可选的、容错的，即使`blockchain-gateway-service`暂时不可用，本服务的核心元数据管理功能依然可用。

这种架构使得`wallet-meta-service`成为一个健壮、安全、可维护的服务。它虽然业务逻辑不复杂，但作为承载用户大量个性化配置的中心，其稳定性和数据的准确性对提升钱包的整体用户体验至关重要。