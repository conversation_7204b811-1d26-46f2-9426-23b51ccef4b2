#!/bin/bash

# CINA.CLUB Kong Gateway - Test Runner
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00
#
# Platform engineering test runner for Kong Gateway

set -euo pipefail

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
KONG_NAMESPACE="${KONG_NAMESPACE:-kong-system}"
KUBECTL_CONTEXT="${KUBECTL_CONTEXT:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if k6 is available for load tests
    if ! command -v k6 &> /dev/null; then
        log_warning "k6 is not installed - load tests will be skipped"
        SKIP_LOAD_TESTS=true
    else
        SKIP_LOAD_TESTS=false
    fi
    
    # Check if Kong namespace exists
    if kubectl get namespace "$KONG_NAMESPACE" &> /dev/null; then
        log_success "Kong namespace '$KONG_NAMESPACE' exists"
    else
        log_error "Kong namespace '$KONG_NAMESPACE' does not exist"
        exit 1
    fi
    
    # Check if Kong pods are running
    local running_pods=$(kubectl get pods -n "$KONG_NAMESPACE" --field-selector=status.phase=Running --no-headers | wc -l)
    if [ "$running_pods" -gt 0 ]; then
        log_success "Kong pods are running ($running_pods pods)"
    else
        log_error "No Kong pods are running in namespace '$KONG_NAMESPACE'"
        exit 1
    fi
}

# Set up port forwarding for tests
setup_port_forwarding() {
    log_info "Setting up port forwarding for Kong services..."
    
    # Kill any existing port forwards
    pkill -f "kubectl.*port-forward.*kong" || true
    sleep 2
    
    # Port forward Kong proxy
    kubectl port-forward -n "$KONG_NAMESPACE" service/kong-proxy 8000:80 &
    PROXY_PF_PID=$!
    
    # Port forward Kong admin
    kubectl port-forward -n "$KONG_NAMESPACE" service/kong-admin 8001:8001 &
    ADMIN_PF_PID=$!
    
    # Wait for port forwards to be ready
    sleep 5
    
    log_success "Port forwarding established"
}

# Clean up port forwarding
cleanup_port_forwarding() {
    log_info "Cleaning up port forwarding..."
    
    if [ -n "${PROXY_PF_PID:-}" ]; then
        kill "$PROXY_PF_PID" 2>/dev/null || true
    fi
    
    if [ -n "${ADMIN_PF_PID:-}" ]; then
        kill "$ADMIN_PF_PID" 2>/dev/null || true
    fi
    
    # Kill any remaining port forwards
    pkill -f "kubectl.*port-forward.*kong" || true
}

# Run health tests
run_health_tests() {
    log_info "Running Kong Gateway health tests..."
    
    export KONG_STATUS_URL="http://localhost:8001"
    export KONG_PROXY_URL="http://localhost:8000"
    
    if bash "$SCRIPT_DIR/health-test.sh"; then
        log_success "Health tests passed"
        return 0
    else
        log_error "Health tests failed"
        return 1
    fi
}

# Run security tests
run_security_tests() {
    log_info "Running Kong Gateway security tests..."
    
    export KONG_PROXY_URL="http://localhost:8000"
    
    if bash "$SCRIPT_DIR/security-test.sh"; then
        log_success "Security tests passed"
        return 0
    else
        log_error "Security tests failed"
        return 1
    fi
}

# Run integration tests
run_integration_tests() {
    log_info "Running Kong Gateway integration tests..."
    
    if bash "$SCRIPT_DIR/integration-test.sh"; then
        log_success "Integration tests passed"
        return 0
    else
        log_error "Integration tests failed"
        return 1
    fi
}

# Run load tests
run_load_tests() {
    if [ "$SKIP_LOAD_TESTS" = true ]; then
        log_warning "Skipping load tests (k6 not available)"
        return 0
    fi
    
    log_info "Running Kong Gateway load tests..."
    
    export KONG_PROXY_URL="http://localhost:8000"
    
    if k6 run "$SCRIPT_DIR/load-test.js"; then
        log_success "Load tests passed"
        return 0
    else
        log_error "Load tests failed"
        return 1
    fi
}

# Main test execution
run_all_tests() {
    local failed=0
    local total=0
    
    log_info "Starting Kong Gateway test suite..."
    
    # Run health tests
    ((total++))
    run_health_tests || ((failed++))
    
    # Run security tests
    ((total++))
    run_security_tests || ((failed++))
    
    # Run integration tests
    ((total++))
    run_integration_tests || ((failed++))
    
    # Run load tests
    ((total++))
    run_load_tests || ((failed++))
    
    # Report results
    local passed=$((total - failed))
    
    echo
    log_info "Test Results Summary:"
    log_info "Total test suites: $total"
    log_success "Passed: $passed"
    
    if [ $failed -gt 0 ]; then
        log_error "Failed: $failed"
        return 1
    else
        log_success "All test suites passed! 🎉"
        return 0
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    cleanup_port_forwarding
    exit_code=$?
    exit $exit_code
}

# Usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Run Kong Gateway test suite

Options:
    -n, --namespace NAMESPACE    Kong namespace (default: kong-system)
    -c, --context CONTEXT       kubectl context to use
    -h, --help                   Show this help message

Test Types:
    health      Health and readiness tests
    security    Security and authentication tests
    integration Platform integration tests
    load        Performance and load tests (requires k6)

Examples:
    $0                          # Run all tests
    $0 -n production-kong       # Run with custom namespace
    $0 -c prod-cluster          # Run with specific kubectl context

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--namespace)
            KONG_NAMESPACE="$2"
            shift 2
            ;;
        -c|--context)
            KUBECTL_CONTEXT="$2"
            export KUBECONFIG_CONTEXT="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Set kubectl context if specified
if [ -n "$KUBECTL_CONTEXT" ]; then
    kubectl config use-context "$KUBECTL_CONTEXT"
fi

# Set up trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    log_info "CINA.CLUB Kong Gateway Test Suite"
    log_info "Namespace: $KONG_NAMESPACE"
    
    check_prerequisites
    setup_port_forwarding
    
    if run_all_tests; then
        log_success "All Kong Gateway tests completed successfully!"
        exit 0
    else
        log_error "Some Kong Gateway tests failed"
        exit 1
    fi
}

main "$@" 