/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

// Package rbac provides high-performance in-memory Role-Based Access Control (RBAC) engine.
package rbac

import (
	"fmt"
	"sync"
)

// Engine provides fast in-memory RBAC policy evaluation
type Engine struct {
	// policy stores role -> set of permissions for O(1) lookup
	policy map[string]map[string]struct{}
	mu     sync.RWMutex
}

// NewEngine creates a new RBAC engine with the given policy configuration
// policyData is a map of role -> list of permissions
func NewEngine(policyData map[string][]string) *Engine {
	engine := &Engine{
		policy: make(map[string]map[string]struct{}),
	}

	engine.loadPolicy(policyData)
	return engine
}

// loadPolicy converts the policy data into an optimized internal representation
func (e *Engine) loadPolicy(policyData map[string][]string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	// Clear existing policy
	e.policy = make(map[string]map[string]struct{})

	// Convert []string permissions to map[string]struct{} for O(1) lookup
	for role, permissions := range policyData {
		permissionSet := make(map[string]struct{})
		for _, permission := range permissions {
			permissionSet[permission] = struct{}{}
		}
		e.policy[role] = permissionSet
	}
}

// Can checks if any of the given roles has the required permission
// This is a high-performance O(1) operation for each role checked
func (e *Engine) Can(roles []string, requiredPermission string) bool {
	if len(roles) == 0 || requiredPermission == "" {
		return false
	}

	e.mu.RLock()
	defer e.mu.RUnlock()

	// Check each role to see if it has the required permission
	for _, role := range roles {
		if permissions, exists := e.policy[role]; exists {
			if _, hasPermission := permissions[requiredPermission]; hasPermission {
				return true
			}
		}
	}

	return false
}

// HasRole checks if the role exists in the policy
func (e *Engine) HasRole(role string) bool {
	e.mu.RLock()
	defer e.mu.RUnlock()

	_, exists := e.policy[role]
	return exists
}

// GetRolePermissions returns all permissions for a given role
func (e *Engine) GetRolePermissions(role string) []string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	permissions, exists := e.policy[role]
	if !exists {
		return nil
	}

	result := make([]string, 0, len(permissions))
	for permission := range permissions {
		result = append(result, permission)
	}

	return result
}

// GetAllRoles returns all roles defined in the policy
func (e *Engine) GetAllRoles() []string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	roles := make([]string, 0, len(e.policy))
	for role := range e.policy {
		roles = append(roles, role)
	}

	return roles
}

// UpdatePolicy updates the RBAC policy at runtime
// This is useful for dynamic policy updates without service restart
func (e *Engine) UpdatePolicy(policyData map[string][]string) {
	e.loadPolicy(policyData)
}

// AddRolePermissions adds permissions to an existing role
func (e *Engine) AddRolePermissions(role string, permissions []string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.policy[role] == nil {
		e.policy[role] = make(map[string]struct{})
	}

	for _, permission := range permissions {
		e.policy[role][permission] = struct{}{}
	}
}

// RemoveRolePermissions removes permissions from a role
func (e *Engine) RemoveRolePermissions(role string, permissions []string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	rolePermissions, exists := e.policy[role]
	if !exists {
		return
	}

	for _, permission := range permissions {
		delete(rolePermissions, permission)
	}
}

// RemoveRole removes a role and all its permissions
func (e *Engine) RemoveRole(role string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	delete(e.policy, role)
}

// Stats returns statistics about the RBAC policy
func (e *Engine) Stats() map[string]interface{} {
	e.mu.RLock()
	defer e.mu.RUnlock()

	totalPermissions := 0
	for _, permissions := range e.policy {
		totalPermissions += len(permissions)
	}

	return map[string]interface{}{
		"total_roles":       len(e.policy),
		"total_permissions": totalPermissions,
	}
}

// Validate checks the policy for common issues
func (e *Engine) Validate() []string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	var issues []string

	// Check for empty roles
	for role, permissions := range e.policy {
		if len(permissions) == 0 {
			issues = append(issues, fmt.Sprintf("role '%s' has no permissions", role))
		}
	}

	return issues
}
