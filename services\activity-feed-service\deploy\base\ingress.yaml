# CINA.CLUB Platform - Activity Feed Service Ingress Configuration
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Activity Feed Service Ingress - User Activity Streams and Feeds
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: activity-feed-ingress
  namespace: activity
  labels:
    app: activity-feed-service
    component: ingress
    tier: application
    service: activity-feed
  annotations:
    # Kong Ingress Controller configuration
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Activity feeds require authentication + analytics-level processing
    konghq.com/plugins: "jwt-validator-user-service, rate-limit-user, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
    
    # Protocol configuration for gRPC backend
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Activity-optimized timeouts (moderate for feed processing)
    konghq.com/read-timeout: "60000"        # 1 minute for activity feed generation
    konghq.com/write-timeout: "60000"       # 1 minute
    konghq.com/connect-timeout: "3000"      # 3 seconds
    
    # Upstream configuration
    konghq.com/host-header: "activity-feed-service.activity.svc.cluster.local"
    
    # Description and metadata
    description: "Activity Feed Service for user activity streams and personalized feeds"
    service-owner: "<EMAIL>"
    api-version: "v1"

spec:
  # TLS configuration
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  
  rules:
    - host: "api.cina.club"
      http:
        paths:
          # Personal activity feeds (JWT required)
          - path: /api/v1/activity/feeds
            pathType: Prefix
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080
          
          # Activity creation and updates
          - path: /api/v1/activity/create
            pathType: Prefix
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080
          
          # Activity timeline
          - path: /api/v1/activity/timeline
            pathType: Prefix
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080
          
          # Activity aggregation
          - path: /api/v1/activity/aggregation
            pathType: Prefix
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080
          
          # Activity notifications
          - path: /api/v1/activity/notifications
            pathType: Prefix
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080

---
# Public Activity Discovery
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: activity-feed-public-ingress
  namespace: activity
  labels:
    app: activity-feed-service
    component: ingress
    tier: public
    security-level: public
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Public activity discovery with optional authentication
    konghq.com/plugins: "jwt-validator-optional, rate-limit-ip, cors-global, prometheus-metrics, request-id"
    
    # Protocol configuration
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Description
    description: "Public activity discovery and trending feeds"

spec:
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  
  rules:
    - host: "api.cina.club"
      http:
        paths:
          # Public trending activities
          - path: /api/v1/activity/public/trending
            pathType: Prefix
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080
          
          # Public activity discovery
          - path: /api/v1/activity/public/discover
            pathType: Prefix
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080
          
          # Service health check
          - path: /api/v1/activity/health
            pathType: Exact
            backend:
              service:
                name: activity-feed-service
                port:
                  number: 8080

---
# Kong Service for Activity Feed Service
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: activity-feed-kong-service
  namespace: activity
  labels:
    app: activity-feed-service
    component: kong-service
  annotations:
    description: "Kong service configuration for Activity Feed Service"

spec:
  # Backend service configuration
  host: "activity-feed-service.activity.svc.cluster.local"
  port: 8080
  protocol: "grpc"                         # gRPC protocol for backend
  
  # Activity-optimized connection settings
  connect_timeout: 3000                    # 3 seconds
  read_timeout: 60000                      # 1 minute for feed generation
  write_timeout: 60000                     # 1 minute
  
  # Multiple retries for activity reliability
  retries: 2                               # 2 retries for activity operations
  
  # Load balancing
  path: "/" 