
---
### 文件3: `docs/architecture/L4_Service_Design_Patterns/03_error_handling_best_practices.md`

```markdown
# 微服务设计模式: 错误处理最佳实践

**文档状态**: 已采纳 (Accepted)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**相关包**: `pkg/errors`

## 1. 概述

统一、规范的错误处理是构建健壮、可维护的分布式系统的关键。本文档定义了CINA.CLUB后端服务在错误处理方面的最佳实践，所有开发人员都必须遵循。

---

## 2. 核心原则

1.  **错误是值 (Errors are values)**: 遵循Go语言的哲学，明确地处理每个可能返回`error`的函数调用。禁止使用`_`忽略错误，除非你100%确定该错误可以被安全地忽略。
2.  **只处理一次 (Handle once)**: 一个错误应该只被处理一次。通常，这意味着在错误产生的最底层进行**包装(wrapping)**，只在请求处理的最顶层（如gRPC handler）或需要根据错误类型改变控制流的地方进行**处理(handling)**（如记录日志、转换HTTP状态码）。
3.  **包装以添加上下文 (Wrap for context)**: 当你从一个下游函数接收到一个错误时，不要直接返回它。应该使用`pkg/errors.Wrap()`来包装它，并添加当前操作的上下文。这能构建一个清晰的错误链，极大地帮助调试。
4.  **对用户隐藏内部细节**: 返回给最终用户的错误信息**绝不能**包含任何内部实现细节，如数据库错误、堆栈跟踪或文件名。应返回一个通用的、友好的错误消息。

---

## 3. 实践指南

### 3.1 何时以及如何包装错误

**场景**: 在`application/service`层调用`repository`层。

```go
// 错误示例 👎: 丢失了原始错误和上下文
user, err := s.repo.GetByID(ctx, id)
if err != nil {
    return nil, fmt.Errorf("user not found") 
}

// 正确示例 👍: 保留了原始错误，并添加了业务上下文
user, err := s.repo.GetByID(ctx, id)
if err != nil {
    // repo层返回的可能是带NotFound码的AppError，也可能是Internal的DB错误
    // 无论如何，我们都添加一层业务上下文
    return nil, app_errors.Wrapf(err, app_errors.GetCode(err), "failed to get user for profile page, id: %s", id)
}
```

### 3.2 何时创建新错误

**场景**: 在业务逻辑的入口处，验证输入参数。

```go
// 正确示例 👍: 在逻辑的起点创建新的、明确的错误
func (s *userService) UpdateEmail(ctx context.Context, newEmail string) error {
    if !validator.IsEmail(newEmail) {
        err := app_errors.New(app_errors.InvalidArgument, "invalid email format")
        err = app_errors.WithMeta(err, "field", "email") // 添加结构化元数据
        return err
    }
    // ...
}
```

### 3.3 在顶层处理错误

**场景**: 在`adapter/grpc`的handler中。

```go
func (h *UserHandler) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.User, error) {
    user, err := h.userService.GetByID(ctx, req.GetUserId())
    if err != nil {
        // 这是处理错误的唯一地方
        // 1. 记录详细的日志
        logger.Error(ctx, err, "GetUser request failed")
        
        // 2. 将内部AppError转换为gRPC Status返回给调用方
        return nil, app_errors.ToGRPCStatus(err).Err()
    }

    return toProtoUser(user), nil
}
```

### 3.4 日志记录

*   **只在顶层记录**: 通常只在请求处理的最顶层（gRPC handler）记录一次错误日志。在调用链的中间层重复记录日志会产生大量噪音。
*   **记录结构化错误**: 使用`logger.Error(ctx, err, "message")`。`pkg/logger`会自动将`AppError`的结构化信息（code, metadata, stack trace）解析并记录下来。

**结论**: 遵循这些简单的错误处理模式，可以使我们的系统在出现问题时，能够快速、精确地定位到根本原因，同时保证对外的错误响应是安全和一致的。
```