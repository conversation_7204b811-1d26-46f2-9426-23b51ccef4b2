/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

#root {
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-1 { margin: 8px; }
.mt-1 { margin-top: 8px; }
.mr-1 { margin-right: 8px; }
.mb-1 { margin-bottom: 8px; }
.ml-1 { margin-left: 8px; }

.m-2 { margin: 16px; }
.mt-2 { margin-top: 16px; }
.mr-2 { margin-right: 16px; }
.mb-2 { margin-bottom: 16px; }
.ml-2 { margin-left: 16px; }

.m-3 { margin: 24px; }
.mt-3 { margin-top: 24px; }
.mr-3 { margin-right: 24px; }
.mb-3 { margin-bottom: 24px; }
.ml-3 { margin-left: 24px; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-1 { padding: 8px; }
.pt-1 { padding-top: 8px; }
.pr-1 { padding-right: 8px; }
.pb-1 { padding-bottom: 8px; }
.pl-1 { padding-left: 8px; }

.p-2 { padding: 16px; }
.pt-2 { padding-top: 16px; }
.pr-2 { padding-right: 16px; }
.pb-2 { padding-bottom: 16px; }
.pl-2 { padding-left: 16px; }

.p-3 { padding: 24px; }
.pt-3 { padding-top: 24px; }
.pr-3 { padding-right: 24px; }
.pb-3 { padding-bottom: 24px; }
.pl-3 { padding-left: 24px; }

/* Ant Design 自定义样式 */
.ant-layout {
  min-height: 100vh;
}

.ant-pro-layout .ant-pro-layout-content {
  margin: 0;
}

/* 响应式隐藏类 */
@media (max-width: 576px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .hidden-md {
    display: none !important;
  }
}

@media (max-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 自定义表格样式 */
.ant-table-wrapper .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 自定义卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-hoverable:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* 自定义按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 状态颜色 */
.status-success {
  color: #52c41a;
}

.status-warning {
  color: #faad14;
}

.status-error {
  color: #f5222d;
}

.status-info {
  color: #1890ff;
}

/* 徽章样式 */
.badge-success {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.badge-warning {
  background-color: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.badge-error {
  background-color: #fff2f0;
  color: #f5222d;
  border: 1px solid #ffccc7;
}

.badge-info {
  background-color: #f0f9ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
} 