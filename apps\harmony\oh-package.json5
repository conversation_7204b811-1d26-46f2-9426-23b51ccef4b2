/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

{
  "name": "cina-club-harmony",
  "version": "1.0.0",
  "description": "CINA.CLUB HarmonyOS Native Application - 一次开发，多端部署的超级应用",
  "main": "index.ets",
  "author": "CINA.CLUB Development Team",
  "license": "Proprietary",
  "dependencies": {
    "@ohos/hypium": "1.0.16"
  },
  "devDependencies": {
    "@ohos/hvigor-ohos-plugin": "5.0.0",
    "@ohos/hvigor": "5.0.0"
  },
  "scripts": {
    "build": "hvigor assembleHap --mode module -p module=entry@default -p product=default",
    "build:release": "hvigor assembleHap --mode module -p module=entry@default -p product=default --release",
    "clean": "hvigor clean",
    "test": "hvigor test",
    "lint": "ohpm run lint:arkts"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/cina-club/monorepo.git"
  },
  "keywords": [
    "harmonyos",
    "arkts",
    "arkui",
    "native",
    "cina-club",
    "go-mobile",
    "napi"
  ]
} 