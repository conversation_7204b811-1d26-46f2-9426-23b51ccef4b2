好的，遵照您的指示。我将为您生成一份全新的、专门针对 **`fast-news-service` (7x24快讯服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将基于我们之前讨论过的架构设计，详细定义这个新增的、高实时性信息管道的功能、接口、数据模型、性能和可靠性需求，使其可以直接作为开发、测试和运维的依据。

---
### CINA.CLUB - `fast-news-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [内容平台/实时系统负责人]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了向CINA.CLUB用户提供高时效性、高价值的全球财经、科技等领域的动态信息，平台需要建立一个“7x24小时实时快讯”功能。`fast-news-service` 的目的在于构建一个**低延迟、高可靠、多源聚合**的信息处理与分发核心。它负责消费由`news-crawler-service`采集的原始信息流，进行一系列的**清洗、去重、内容增强和重要性评估**，然后将高质量的、标准化的快讯内容，通过事件总线实时分发给平台所有相关的下游系统（如实时推送、搜索、通知等）。

#### 1.2. 服务范围
本服务 **负责**:
*   **消费原始新闻流**: 消费来自`news-crawler-service`发布的`RawNews`事件。
*   **信息处理与清洗**:
    *   **去重 (Deduplication)**: 基于语义或文本哈希，过滤掉重复或高度相似的新闻。
    *   **内容清洗**: 清除HTML标签、广告等无关内容。
*   **内容增强与分析**:
    *   **重要性评估**: 基于规则（信源、关键词）为每条快讯评估一个重要性等级。
    *   **AI增强**: (可选) 调用`ai-assistant-service`为重要或长篇快讯生成摘要、提取关键词、进行多语言翻译。
*   **内容审核协调**: (可选) 将人工编辑或高风险信源的内容提交给`content-moderation-service`。
*   **持久化**: 将处理好的快讯内容和元数据存储到数据库，以供历史回顾。
*   **事件发布**: 将最终处理完成的、标准化的`ProcessedNewsEvent`发布到平台事件总线。

本服务 **不负责**:
*   **数据采集与抓取**: 由`news-crawler-service`负责。
*   **实时消息广播**: 由`chat-websocket-server`负责消费本服务发布的事件。
*   **移动端推送**: 由`notification-dispatch-service`负责消费本服务发布的事件。
*   **搜索索引**: 由`search-indexer-service`负责消费本服务发布的事件。
*   **面向最终用户的API**: 本服务主要是一个后台处理管道，但可以提供内部查询API。

#### 1.3. 目标用户/调用方
*   **`news-crawler-service` (通过Kafka)**: 上游数据源。
*   **下游事件消费者 (通过Kafka)**: `chat-websocket-server`, `notification-dispatch-service`, `search-indexer-service`等。
*   **CINA.CLUB内容运营/编辑团队**: 通过（未来的）管理后台与本服务交互，进行内容干预、重要性调整。
*   **`ai-assistant-service`**: (被本服务调用) 提供AI增强能力。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`fast-news-service` 是平台**实时信息流的“中央厨房”和“质量控制中心”**。它位于原始数据采集和最终用户分发之间，扮演着关键的**加工、筛选和决策**角色。它确保了所有推送到用户面前的快讯都是经过标准化处理、评估过重要性且高质量的，是整个7x24快讯功能可靠性和内容质量的核心保障。

#### 2.2. 主要功能概述
*   事件驱动的、高吞吐量的实时信息处理管道。
*   基于哈希和语义的智能去重。
*   基于规则和AI的内容评估与增强。
*   为历史回顾提供持久化存储和查询API。
*   将高质量的、标准化的快讯事件分发给整个平台。

---

### 3. 核心流程图

#### 3.1. 一条快讯的处理与分发全流程

```mermaid
sequenceDiagram
    participant Crawler as news-crawler-service
    participant Kafka
    participant FastNewsService as FNS
    participant Redis (for Dedupe)
    participant AIAssistant as ai-assistant-service
    participant DB as PostgreSQL
    participant WebsocketSvc as chat-websocket-server
    
    Crawler->>Kafka: 1. Publish RawNewsEvent
    Kafka-->>FNS: 2. Consume event
    
    FNS->>Redis: 3. Check SimHash for deduplication
    alt Not a duplicate
        FNS->>FNS: 4. Clean & score importance
        alt High importance
            FNS->>AIAssistant: 5. Request AI Summary
            AIAssistant-->>FNS: (Summary text)
        end
        
        FNS->>DB: 6. Persist processed news item
        FNS->>Kafka: 7. ✨ Publish ProcessedNewsEvent ✨
        FNS->>Redis: 8. Add SimHash to Redis with TTL
    else Is a duplicate
        FNS->>FNS: 9. Discard the event
    end

    Kafka-->>WebsocketSvc: 10. Consume ProcessedNewsEvent
    WebsocketSvc->>Clients: 11. Broadcast to all online users
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 数据消费与预处理
*   **FR4.1.1 (事件消费)**: 系统必须能消费来自`raw-news-stream` Kafka Topic的`RawNews`事件。
*   **FR4.1.2 (智能去重)**:
    *   **必须**为每条新闻的正文内容计算一个**SimHash**值。
    *   **必须**在Redis中维护一个近期（如24小时）SimHash值的集合。
    *   对于新来的新闻，计算其SimHash与集合中已有哈希的**汉明距离(Hamming Distance)**。如果距离小于一个可配置的阈值（如3），则视为重复并丢弃。
*   **FR4.1.3 (内容清洗)**: 必须有一个可配置的清洗器，能移除HTML标签、JavaScript代码、以及信源特有的广告或免责声明。

#### 4.2. 内容评估与增强
*   **FR4.2.1 (重要性评分引擎)**:
    *   必须实现一个基于规则的评分引擎。
    *   **规则输入**: 信源的权重、标题/正文中的关键词（如“突发”、“央行”）、实体提及（公司、人物）。
    *   **输出**: 一个数值分数（如1-100）。
    *   **决策**: 根据分数，为最终的`ProcessedNewsEvent`设置`is_major`和`priority`字段。
*   **FR4.2.2 (AI增强工作流)**:
    *   对于重要性分数超过阈值的快讯，系统**必须异步地**调用`ai-assistant-service`。
    *   **请求能力**: 摘要、关键词提取、分类、情感分析、多语言翻译。
    *   AI返回的结果将被附加到快讯的元数据中。

#### 4.3. 持久化与查询
*   **FR4.3.1 (持久化)**: 所有被成功处理并发布的快讯，都必须被持久化到数据库中。
*   **FR4.3.2 (历史API)**: 必须提供一个内部的、支持分页的RESTful/gRPC API，允许客户端（如`admin-frontend`或用户快讯回顾页面）按时间倒序拉取历史快讯。

#### 4.4. 事件发布
*   **FR4.4.1 (标准化事件)**: 在完成所有处理后，系统**必须**发布一个`ProcessedNewsEvent`到`fast-news-events` Topic。
*   **FR4.4.2 (事件内容)**: 事件`payload`必须是一个标准化的、包含了所有必要信息（包括AI增强结果）的Protobuf消息，以便所有下游消费者都能轻松使用。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 消息队列事件契约
*   **入站Topic**: `raw-news-stream`
    *   **Event**: `RawNewsEvent` (`core/api/proto/v1/news_events.proto`)
*   **出站Topic**: `fast-news-events`
    *   **Event**: `ProcessedNewsEvent` (`core/api/proto/v1/news_events.proto`)
        ```protobuf
        message ProcessedNewsEvent {
          string id = 1;
          string title = 2;
          string content_summary = 3;
          string source = 4;
          google.protobuf.Timestamp published_at = 5;
          bool is_major = 6;
          int32 priority = 7;
          repeated string tags = 8;
          string full_content_link = 9; // 链接到完整新闻页
        }
        ```

#### 5.2. 内部/管理后台API接口
*   **版本**: `/api/v1/fast-news`
*   **核心端点**:
    *   `GET /items?page_token=...&limit=...`: 分页获取历史快讯。
    *   `POST /items`: (Admin Only) 允许编辑手动发布一条快讯。
    *   `PUT /items/{id}`: (Admin Only) 修改一条已发布快讯的重要性或内容。

---

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`fast_news_items`**:
    *   `id (PK, UUID)`
    *   `title (TEXT)`
    *   `content (TEXT)`
    *   `source (VARCHAR)`
    *   `source_url (TEXT, UNIQUE)`
    *   `published_at (TIMESTAMPTZ, INDEX)`
    *   `simhash_value (BIGINT, INDEX)`
    *   `importance_score (INT)`
    *   `is_major (BOOLEAN)`
    *   `metadata (JSONB)`: 存储AI生成的摘要、关键词、翻译等。

#### 6.2. 缓存 (Redis)
*   **SimHash去重集**: 使用Redis的**Sorted Set**或**Bloom Filter**。
    *   **Sorted Set**: `Key: news_simhash:{YYYY-MM-DD}`, `Score: timestamp`, `Member: simhash_value`。可以方便地清理过期数据。
    *   **Bloom Filter**: 空间效率更高，但有极小误判率。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与延迟)**:
    *   **端到端延迟**: 从`news-crawler-service`发布`RawNewsEvent`到本服务发布`ProcessedNewsEvent`的P99延迟**必须 `< 2秒**（不含可选的AI增强）。
    *   **吞吐量**: 必须能处理每秒数百条的原始新闻事件流入。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。
    *   **数据不丢失**: 必须保证“至少一次”的消费语义，不能丢失任何一条原始新闻。对处理失败的事件，要有重试和死信队列(DLQ)机制。
*   **NFR7.3 (可扩展性)**: 服务作为Kafka消费者组，可以通过增加实例数量来线性提升事件处理能力。

---

### 8. 技术约束与选型建议
*   **语言**: Go。其高并发模型非常适合构建高性能的事件驱动管道。
*   **去重算法库**: 使用成熟的Go SimHash库，如`github.com/mfonda/simhash`。
*   **核心架构**: 事件驱动的消费者/处理器模式。
*   **异步处理**: AI增强等耗时操作，必须通过独立的任务队列（如Asynq）或在独立的goroutine中进行，不能阻塞主处理流程。

---
这份SRS为`fast-news-service`的设计和实现提供了坚实、全面的指导。通过构建这样一个高效、智能、可靠的实时信息处理中枢，CINA.CLUB平台能够为其用户提供真正有价值的、高时效性的7x24快讯服务。