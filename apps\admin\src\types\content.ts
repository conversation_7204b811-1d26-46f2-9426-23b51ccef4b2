/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

// Content status enumeration
export enum ContentStatus {
  DRAFT = 'DRAFT',
  PENDING_REVIEW = 'PENDING_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED',
}

// Content types
export enum ContentType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  DOCUMENT = 'DOCUMENT',
  POST = 'POST',
  COMMENT = 'COMMENT',
  MESSAGE = 'MESSAGE',
  PROFILE = 'PROFILE',
}

// Content categories
export enum ContentCategory {
  GENERAL = 'GENERAL',
  NEWS = 'NEWS',
  ENTERTAINMENT = 'ENTERTAINMENT',
  EDUCATION = 'EDUCATION',
  TECHNOLOGY = 'TECHNOLOGY',
  BUSINESS = 'BUSINESS',
  HEALTH = 'HEALTH',
  SPORTS = 'SPORTS',
  POLITICS = 'POLITICS',
  LIFESTYLE = 'LIFESTYLE',
}

// Moderation action types
export enum ModerationAction {
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  FLAG = 'FLAG',
  HIDE = 'HIDE',
  DELETE = 'DELETE',
  QUARANTINE = 'QUARANTINE',
  WARN_USER = 'WARN_USER',
  SUSPEND_USER = 'SUSPEND_USER',
}

// Flag reasons
export enum FlagReason {
  SPAM = 'SPAM',
  INAPPROPRIATE = 'INAPPROPRIATE',
  HARASSMENT = 'HARASSMENT',
  HATE_SPEECH = 'HATE_SPEECH',
  VIOLENCE = 'VIOLENCE',
  COPYRIGHT = 'COPYRIGHT',
  MISINFORMATION = 'MISINFORMATION',
  ADULT_CONTENT = 'ADULT_CONTENT',
  ILLEGAL_CONTENT = 'ILLEGAL_CONTENT',
  OTHER = 'OTHER',
}

// Base content interface
export interface Content {
  id: string
  title?: string
  body: string
  type: ContentType
  category: ContentCategory
  status: ContentStatus
  authorId: string
  authorName: string
  authorAvatar?: string
  createdAt: string
  updatedAt: string
  publishedAt?: string
  moderatedAt?: string
  moderatedBy?: string
  moderationNote?: string
  tags: string[]
  metadata?: Record<string, any>
  attachments: ContentAttachment[]
  metrics: ContentMetrics
  flags: ContentFlag[]
  aiModerationScore?: number
  aiModerationFlags?: string[]
}

// Content attachment
export interface ContentAttachment {
  id: string
  name: string
  type: 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT'
  url: string
  thumbnailUrl?: string
  size: number
  mimeType: string
  duration?: number // for video/audio
  dimensions?: {
    width: number
    height: number
  }
}

// Content metrics
export interface ContentMetrics {
  views: number
  likes: number
  dislikes: number
  shares: number
  comments: number
  reports: number
  engagementRate: number
  viralityScore: number
}

// Content flag
export interface ContentFlag {
  id: string
  contentId: string
  reason: FlagReason
  description?: string
  reportedBy: string
  reportedAt: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  status: 'PENDING' | 'REVIEWED' | 'RESOLVED' | 'DISMISSED'
  reviewedBy?: string
  reviewedAt?: string
  reviewNote?: string
}

// Moderation queue item
export interface ModerationQueueItem {
  content: Content
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  assignedTo?: string
  flagCount: number
  aiRiskScore: number
  estimatedReviewTime: number
  similarContent: string[]
}

// Moderation action
export interface ModerationActionRecord {
  id: string
  contentId: string
  action: ModerationAction
  reason: string
  moderatorId: string
  moderatorName: string
  actionedAt: string
  details?: Record<string, any>
  userNotified: boolean
  appealable: boolean
}

// Content creation request
export interface CreateContentRequest {
  title?: string
  body: string
  type: ContentType
  category: ContentCategory
  tags?: string[]
  attachments?: Array<{
    file: File
    type: string
  }>
  metadata?: Record<string, any>
}

// Content update request
export interface UpdateContentRequest {
  title?: string
  body?: string
  category?: ContentCategory
  tags?: string[]
  status?: ContentStatus
  metadata?: Record<string, any>
}

// Content list response
export interface ContentListResponse {
  content: Content[]
  total: number
  page: number
  pageSize: number
}

// Content query parameters
export interface ContentQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: ContentStatus
  type?: ContentType
  category?: ContentCategory
  authorId?: string
  flagged?: boolean
  aiRiskScore?: number
  dateFrom?: string
  dateTo?: string
  sortBy?: 'createdAt' | 'publishedAt' | 'views' | 'flags' | 'aiRiskScore'
  sortOrder?: 'ASC' | 'DESC'
  tags?: string[]
}

// Content statistics
export interface ContentStatistics {
  totalContent: number
  publishedContent: number
  pendingReview: number
  flaggedContent: number
  moderatedToday: number
  averageReviewTime: number
  topCategories: Array<{
    category: ContentCategory
    count: number
  }>
  topFlags: Array<{
    reason: FlagReason
    count: number
  }>
  moderationStats: Array<{
    action: ModerationAction
    count: number
  }>
}

// Bulk content operations
export interface BulkContentOperation {
  contentIds: string[]
  action: ModerationAction
  reason: string
  notifyUsers?: boolean
  params?: Record<string, any>
}

// Content analytics
export interface ContentAnalytics {
  contentId: string
  period: 'DAY' | 'WEEK' | 'MONTH' | 'YEAR'
  views: Array<{
    date: string
    count: number
  }>
  engagement: Array<{
    date: string
    likes: number
    comments: number
    shares: number
  }>
  demographics: {
    ageGroups: Array<{
      range: string
      percentage: number
    }>
    countries: Array<{
      country: string
      percentage: number
    }>
    platforms: Array<{
      platform: string
      percentage: number
    }>
  }
}

// AI moderation result
export interface AIModerationResult {
  contentId: string
  riskScore: number // 0-100
  flags: Array<{
    type: FlagReason
    confidence: number
    details: string
  }>
  recommendation: 'APPROVE' | 'REVIEW' | 'REJECT'
  processedAt: string
  modelVersion: string
} 