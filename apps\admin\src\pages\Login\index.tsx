/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useEffect } from 'react'
import { Form, Input, Button, Card, Typography, Space, Alert } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'

import { useLogin } from '@/services/auth'
import { useAuthStore } from '@/store/auth'
import type { LoginRequest } from '@/types/auth'

const { Title, Text } = Typography

/**
 * 登录页面
 */
const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [form] = Form.useForm()

  const { mutate: login, isLoading } = useLogin()
  const { isAuthenticated, error, clearError } = useAuthStore()

  // 获取重定向地址
  const from = (location.state as any)?.from?.pathname || '/dashboard'

  // 如果已登录，重定向
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true })
    }
  }, [isAuthenticated, navigate, from])

  // 清除错误
  useEffect(() => {
    return () => {
      clearError()
    }
  }, [clearError])

  // 处理登录提交
  const handleSubmit = (values: LoginRequest) => {
    login(values)
  }

  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '20px',
      }}
    >
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: 12,
        }}
        bodyStyle={{ padding: '40px' }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 标题 */}
          <div style={{ textAlign: 'center' }}>
            <img
              src="https://gw.alipayobjects.com/zos/antfincdn/PmY%24TNNDBI/logo.svg"
              alt="CINA.CLUB"
              style={{ height: 48, marginBottom: 16 }}
            />
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              CINA.CLUB
            </Title>
            <Text type="secondary">后台管理系统</Text>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert
              message="登录失败"
              description={error}
              type="error"
              showIcon
              closable
              onClose={clearError}
            />
          )}

          {/* 登录表单 */}
          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
            initialValues={{
              username: 'admin',
              password: 'admin123',
            }}
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                block
                style={{ height: 48 }}
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          {/* 提示信息 */}
          <div style={{ textAlign: 'center' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              演示账号: admin / admin123
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  )
}

export default LoginPage 