/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-01 11:05:00
Modified: 2025-01-01 11:05:00
*/

package datasync

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"hash"
	"sync"
	"time"
)

// ChunkingStrategy defines different chunking approaches
type ChunkingStrategy string

const (
	ChunkingStrategyFixed    ChunkingStrategy = "FIXED"
	ChunkingStrategyFastCDC  ChunkingStrategy = "FAST_CDC"
	ChunkingStrategyAdaptive ChunkingStrategy = "ADAPTIVE"
	ChunkingStrategyHybrid   ChunkingStrategy = "HYBRID"
)

// ChunkingMetrics tracks chunking performance
type ChunkingMetrics struct {
	TotalBytes        int64         `json:"total_bytes"`
	TotalChunks       int           `json:"total_chunks"`
	AverageChunkSize  float64       `json:"average_chunk_size"`
	MinChunkSize      int           `json:"min_chunk_size"`
	MaxChunkSize      int           `json:"max_chunk_size"`
	ChunkingTime      time.Duration `json:"chunking_time"`
	HashingTime       time.Duration `json:"hashing_time"`
	DeduplicationRate float64       `json:"deduplication_rate"`
	CompressionRatio  float64       `json:"compression_ratio"`
	Timestamp         time.Time     `json:"timestamp"`
}

// AdaptiveChunkingConfig contains configuration for adaptive chunking
type AdaptiveChunkingConfig struct {
	// Size parameters
	MinSize    int `json:"min_size"`
	MaxSize    int `json:"max_size"`
	TargetSize int `json:"target_size"`

	// Performance thresholds
	MaxChunkingTime  time.Duration `json:"max_chunking_time"`
	TargetThroughput float64       `json:"target_throughput_mbps"`

	// Adaptation parameters
	SizeAdjustmentRate float64 `json:"size_adjustment_rate"`
	PerformanceWindow  int     `json:"performance_window"`

	// Quality parameters
	MinDeduplicationRate float64 `json:"min_deduplication_rate"`
	MaxCompressionRatio  float64 `json:"max_compression_ratio"`
}

// DefaultAdaptiveChunkingConfig returns default configuration
func DefaultAdaptiveChunkingConfig() *AdaptiveChunkingConfig {
	return &AdaptiveChunkingConfig{
		MinSize:              2 * 1024,   // 2KB
		MaxSize:              128 * 1024, // 128KB
		TargetSize:           32 * 1024,  // 32KB
		MaxChunkingTime:      100 * time.Millisecond,
		TargetThroughput:     100.0, // 100 MB/s
		SizeAdjustmentRate:   0.1,   // 10% adjustment
		PerformanceWindow:    10,    // Last 10 operations
		MinDeduplicationRate: 0.2,   // 20%
		MaxCompressionRatio:  0.8,   // 80%
	}
}

// EnhancedChunker provides advanced chunking capabilities
type EnhancedChunker struct {
	strategy ChunkingStrategy
	config   *AdaptiveChunkingConfig
	fastCDC  *FastCDC
	hasher   hash.Hash
	mu       sync.RWMutex

	// Performance tracking
	metrics            []ChunkingMetrics
	chunkCache         map[string]*Chunk
	performanceHistory []PerformanceDataPoint

	// Adaptive parameters
	currentMinSize int
	currentMaxSize int
	currentAvgSize int
}

// NewEnhancedChunker creates a new enhanced chunker
func NewEnhancedChunker(strategy ChunkingStrategy, config *AdaptiveChunkingConfig) *EnhancedChunker {
	if config == nil {
		config = DefaultAdaptiveChunkingConfig()
	}

	chunker := &EnhancedChunker{
		strategy:           strategy,
		config:             config,
		fastCDC:            NewFastCDC(),
		hasher:             sha256.New(),
		metrics:            make([]ChunkingMetrics, 0),
		chunkCache:         make(map[string]*Chunk),
		performanceHistory: make([]PerformanceDataPoint, 0),
		currentMinSize:     config.MinSize,
		currentMaxSize:     config.MaxSize,
		currentAvgSize:     config.TargetSize,
	}

	return chunker
}

// ChunkDataEnhanced performs enhanced chunking with monitoring and adaptation
func (ec *EnhancedChunker) ChunkDataEnhanced(ctx context.Context, data []byte, options *ChunkingOptions) (*EnhancedChunkResult, error) {
	startTime := time.Now()

	if options == nil {
		options = DefaultChunkingOptions()
	}

	// Pre-chunking validation
	if len(data) == 0 {
		return &EnhancedChunkResult{
			ChunkResult: &ChunkResult{
				Chunks:      []Chunk{},
				TotalSize:   0,
				ChunkCount:  0,
				Fingerprint: ec.hashData([]byte{}),
			},
			Metrics:  ChunkingMetrics{Timestamp: time.Now()},
			Strategy: ec.strategy,
			Adaptive: false,
		}, nil
	}

	// Apply adaptive parameters if using adaptive strategy
	if ec.strategy == ChunkingStrategyAdaptive || ec.strategy == ChunkingStrategyHybrid {
		ec.adaptParameters()
	}

	// Perform chunking based on strategy
	var result *ChunkResult
	var err error

	switch ec.strategy {
	case ChunkingStrategyFixed:
		result = ec.chunkFixed(data, options)
	case ChunkingStrategyFastCDC:
		result = ec.chunkFastCDC(data, options)
	case ChunkingStrategyAdaptive:
		result = ec.chunkAdaptive(data, options)
	case ChunkingStrategyHybrid:
		result = ec.chunkHybrid(data, options)
	default:
		return nil, fmt.Errorf("unsupported chunking strategy: %s", ec.strategy)
	}

	if err != nil {
		return nil, err
	}

	chunkingTime := time.Since(startTime)

	// Calculate metrics
	metrics := ec.calculateMetrics(result, chunkingTime, startTime)

	// Post-processing
	if options.EnableDeduplication {
		result = ec.deduplicateChunks(result)
	}

	if options.EnableCompression {
		result, err = ec.compressChunks(result)
		if err != nil {
			return nil, fmt.Errorf("compression failed: %w", err)
		}
	}

	// Cache chunks if enabled
	if options.EnableCaching {
		ec.cacheChunks(result.Chunks)
	}

	// Record metrics
	ec.recordMetrics(metrics)

	// Create enhanced result
	enhancedResult := &EnhancedChunkResult{
		ChunkResult:    result,
		Metrics:        metrics,
		Strategy:       ec.strategy,
		Adaptive:       ec.strategy == ChunkingStrategyAdaptive || ec.strategy == ChunkingStrategyHybrid,
		Options:        *options,
		ProcessingTime: time.Since(startTime),
	}

	return enhancedResult, nil
}

// ChunkingOptions provides options for chunking operations
type ChunkingOptions struct {
	EnableDeduplication bool                   `json:"enable_deduplication"`
	EnableCompression   bool                   `json:"enable_compression"`
	EnableCaching       bool                   `json:"enable_caching"`
	EnableValidation    bool                   `json:"enable_validation"`
	MaxConcurrency      int                    `json:"max_concurrency"`
	Timeout             time.Duration          `json:"timeout"`
	CustomParameters    map[string]interface{} `json:"custom_parameters"`
}

// DefaultChunkingOptions returns default chunking options
func DefaultChunkingOptions() *ChunkingOptions {
	return &ChunkingOptions{
		EnableDeduplication: true,
		EnableCompression:   false,
		EnableCaching:       true,
		EnableValidation:    true,
		MaxConcurrency:      4,
		Timeout:             30 * time.Second,
		CustomParameters:    make(map[string]interface{}),
	}
}

// EnhancedChunkResult contains the result of enhanced chunking
type EnhancedChunkResult struct {
	*ChunkResult
	Metrics        ChunkingMetrics  `json:"metrics"`
	Strategy       ChunkingStrategy `json:"strategy"`
	Adaptive       bool             `json:"adaptive"`
	Options        ChunkingOptions  `json:"options"`
	ProcessingTime time.Duration    `json:"processing_time"`
	Warnings       []string         `json:"warnings,omitempty"`
}

// chunkFixed performs fixed-size chunking
func (ec *EnhancedChunker) chunkFixed(data []byte, options *ChunkingOptions) *ChunkResult {
	chunkSize := ec.currentAvgSize
	chunks := make([]Chunk, 0)
	offset := int64(0)
	sequence := 0

	for len(data) > 0 {
		size := chunkSize
		if len(data) < size {
			size = len(data)
		}

		chunkData := data[:size]
		chunk := Chunk{
			ID:       ec.hashData(chunkData),
			Data:     make([]byte, len(chunkData)),
			Size:     len(chunkData),
			Offset:   offset,
			Sequence: sequence,
		}
		copy(chunk.Data, chunkData)

		chunks = append(chunks, chunk)

		data = data[size:]
		offset += int64(size)
		sequence++
	}

	return &ChunkResult{
		Chunks:      chunks,
		TotalSize:   offset,
		ChunkCount:  len(chunks),
		Fingerprint: ec.calculateFingerprint(chunks),
	}
}

// chunkFastCDC performs FastCDC chunking with current parameters
func (ec *EnhancedChunker) chunkFastCDC(data []byte, options *ChunkingOptions) *ChunkResult {
	// Update FastCDC parameters
	ec.fastCDC.minSize = ec.currentMinSize
	ec.fastCDC.maxSize = ec.currentMaxSize
	ec.fastCDC.avgSize = ec.currentAvgSize

	return ec.fastCDC.ChunkData(data)
}

// chunkAdaptive performs adaptive chunking based on content and performance
func (ec *EnhancedChunker) chunkAdaptive(data []byte, options *ChunkingOptions) *ChunkResult {
	// Analyze data characteristics
	dataAnalysis := ec.analyzeData(data)

	// Adjust chunking parameters based on analysis
	ec.adjustParametersForData(dataAnalysis)

	// Use FastCDC with adapted parameters
	return ec.chunkFastCDC(data, options)
}

// chunkHybrid combines multiple strategies for optimal results
func (ec *EnhancedChunker) chunkHybrid(data []byte, options *ChunkingOptions) *ChunkResult {
	// Try multiple strategies and pick the best result
	fixedResult := ec.chunkFixed(data, options)
	cdcResult := ec.chunkFastCDC(data, options)

	// Compare results and return the better one
	if ec.compareResults(fixedResult, cdcResult) {
		return fixedResult
	}
	return cdcResult
}

// DataAnalysis contains analysis of data characteristics
type DataAnalysis struct {
	Entropy         float64 `json:"entropy"`
	Compressibility float64 `json:"compressibility"`
	Repetition      float64 `json:"repetition"`
	RandomnessScore float64 `json:"randomness_score"`
}

// analyzeData analyzes data characteristics to inform chunking decisions
func (ec *EnhancedChunker) analyzeData(data []byte) DataAnalysis {
	// Simplified analysis - in reality this would be more sophisticated
	analysis := DataAnalysis{}

	if len(data) == 0 {
		return analysis
	}

	// Calculate entropy (simplified)
	byteFreq := make(map[byte]int)
	for _, b := range data {
		byteFreq[b]++
	}

	entropy := 0.0
	dataLen := float64(len(data))
	for _, freq := range byteFreq {
		if freq > 0 {
			p := float64(freq) / dataLen
			entropy -= p * (float64(freq) / dataLen)
		}
	}
	analysis.Entropy = entropy

	// Estimate compressibility (very simplified)
	uniqueBytes := len(byteFreq)
	analysis.Compressibility = 1.0 - (float64(uniqueBytes) / 256.0)

	// Calculate repetition score
	analysis.Repetition = ec.calculateRepetition(data)

	// Randomness score
	analysis.RandomnessScore = entropy / 8.0 // Normalize to 0-1

	return analysis
}

// calculateRepetition calculates how repetitive the data is
func (ec *EnhancedChunker) calculateRepetition(data []byte) float64 {
	if len(data) < 2 {
		return 0.0
	}

	repetitions := 0
	for i := 1; i < len(data); i++ {
		if data[i] == data[i-1] {
			repetitions++
		}
	}

	return float64(repetitions) / float64(len(data)-1)
}

// adjustParametersForData adjusts chunking parameters based on data analysis
func (ec *EnhancedChunker) adjustParametersForData(analysis DataAnalysis) {
	// High entropy data benefits from larger chunks
	if analysis.Entropy > 6.0 {
		ec.currentAvgSize = int(float64(ec.currentAvgSize) * 1.2)
		ec.currentMaxSize = int(float64(ec.currentMaxSize) * 1.1)
	}

	// High repetition benefits from smaller chunks for better deduplication
	if analysis.Repetition > 0.3 {
		ec.currentAvgSize = int(float64(ec.currentAvgSize) * 0.8)
		ec.currentMinSize = int(float64(ec.currentMinSize) * 0.9)
	}

	// Ensure bounds
	if ec.currentMinSize < ec.config.MinSize {
		ec.currentMinSize = ec.config.MinSize
	}
	if ec.currentMaxSize > ec.config.MaxSize {
		ec.currentMaxSize = ec.config.MaxSize
	}
	if ec.currentAvgSize < ec.currentMinSize {
		ec.currentAvgSize = ec.currentMinSize
	}
	if ec.currentAvgSize > ec.currentMaxSize {
		ec.currentAvgSize = ec.currentMaxSize
	}
}

// compareResults compares two chunk results and returns true if first is better
func (ec *EnhancedChunker) compareResults(result1, result2 *ChunkResult) bool {
	// Simple comparison - in reality this would be more sophisticated
	// Prefer fewer chunks (better for deduplication)
	if result1.ChunkCount != result2.ChunkCount {
		return result1.ChunkCount < result2.ChunkCount
	}

	// Prefer more uniform chunk sizes
	variance1 := ec.calculateChunkSizeVariance(result1.Chunks)
	variance2 := ec.calculateChunkSizeVariance(result2.Chunks)
	return variance1 < variance2
}

// calculateChunkSizeVariance calculates the variance in chunk sizes
func (ec *EnhancedChunker) calculateChunkSizeVariance(chunks []Chunk) float64 {
	if len(chunks) == 0 {
		return 0
	}

	// Calculate mean
	sum := 0
	for _, chunk := range chunks {
		sum += chunk.Size
	}
	mean := float64(sum) / float64(len(chunks))

	// Calculate variance
	variance := 0.0
	for _, chunk := range chunks {
		diff := float64(chunk.Size) - mean
		variance += diff * diff
	}

	return variance / float64(len(chunks))
}

// adaptParameters adapts chunking parameters based on performance history
func (ec *EnhancedChunker) adaptParameters() {
	ec.mu.Lock()
	defer ec.mu.Unlock()

	if len(ec.performanceHistory) < 2 {
		return
	}

	// Analyze recent performance
	recentPerformance := ec.performanceHistory
	if len(recentPerformance) > ec.config.PerformanceWindow {
		recentPerformance = recentPerformance[len(recentPerformance)-ec.config.PerformanceWindow:]
	}

	// Calculate average throughput
	avgThroughput := 0.0
	for _, perf := range recentPerformance {
		if throughput, exists := perf.Metrics["throughput_mbps"]; exists {
			avgThroughput += throughput
		}
	}
	avgThroughput /= float64(len(recentPerformance))

	// Adjust parameters based on performance
	if avgThroughput < ec.config.TargetThroughput {
		// Increase chunk size to improve throughput
		adjustment := ec.config.SizeAdjustmentRate
		ec.currentAvgSize = int(float64(ec.currentAvgSize) * (1.0 + adjustment))
		ec.currentMaxSize = int(float64(ec.currentMaxSize) * (1.0 + adjustment/2))
	} else if avgThroughput > ec.config.TargetThroughput*1.2 {
		// Decrease chunk size for better granularity
		adjustment := ec.config.SizeAdjustmentRate
		ec.currentAvgSize = int(float64(ec.currentAvgSize) * (1.0 - adjustment))
		ec.currentMinSize = int(float64(ec.currentMinSize) * (1.0 - adjustment/2))
	}

	// Ensure bounds
	if ec.currentMinSize < ec.config.MinSize {
		ec.currentMinSize = ec.config.MinSize
	}
	if ec.currentMaxSize > ec.config.MaxSize {
		ec.currentMaxSize = ec.config.MaxSize
	}
	if ec.currentAvgSize < ec.currentMinSize {
		ec.currentAvgSize = ec.currentMinSize
	}
	if ec.currentAvgSize > ec.currentMaxSize {
		ec.currentAvgSize = ec.currentMaxSize
	}
}

// calculateMetrics calculates chunking metrics
func (ec *EnhancedChunker) calculateMetrics(result *ChunkResult, chunkingTime time.Duration, startTime time.Time) ChunkingMetrics {
	metrics := ChunkingMetrics{
		TotalBytes:   result.TotalSize,
		TotalChunks:  result.ChunkCount,
		ChunkingTime: chunkingTime,
		Timestamp:    startTime,
	}

	if result.ChunkCount > 0 {
		metrics.AverageChunkSize = float64(result.TotalSize) / float64(result.ChunkCount)

		// Find min/max chunk sizes
		minSize := result.Chunks[0].Size
		maxSize := result.Chunks[0].Size
		for _, chunk := range result.Chunks {
			if chunk.Size < minSize {
				minSize = chunk.Size
			}
			if chunk.Size > maxSize {
				maxSize = chunk.Size
			}
		}
		metrics.MinChunkSize = minSize
		metrics.MaxChunkSize = maxSize
	}

	return metrics
}

// deduplicateChunks removes duplicate chunks
func (ec *EnhancedChunker) deduplicateChunks(result *ChunkResult) *ChunkResult {
	seen := make(map[string]bool)
	deduped := make([]Chunk, 0)

	for _, chunk := range result.Chunks {
		if !seen[chunk.ID] {
			seen[chunk.ID] = true
			deduped = append(deduped, chunk)
		}
	}

	return &ChunkResult{
		Chunks:      deduped,
		TotalSize:   result.TotalSize, // Keep original total size
		ChunkCount:  len(deduped),
		Fingerprint: result.Fingerprint,
	}
}

// compressChunks compresses chunk data
func (ec *EnhancedChunker) compressChunks(result *ChunkResult) (*ChunkResult, error) {
	// Placeholder for compression logic
	// In reality, you'd implement actual compression here
	return result, nil
}

// cacheChunks caches chunks for future use
func (ec *EnhancedChunker) cacheChunks(chunks []Chunk) {
	ec.mu.Lock()
	defer ec.mu.Unlock()

	for _, chunk := range chunks {
		ec.chunkCache[chunk.ID] = &chunk
	}

	// Maintain cache size (simple LRU would be better)
	if len(ec.chunkCache) > 10000 {
		// Remove some old entries (simplified)
		count := 0
		for id := range ec.chunkCache {
			delete(ec.chunkCache, id)
			count++
			if count > 1000 {
				break
			}
		}
	}
}

// recordMetrics records chunking metrics
func (ec *EnhancedChunker) recordMetrics(metrics ChunkingMetrics) {
	ec.mu.Lock()
	defer ec.mu.Unlock()

	ec.metrics = append(ec.metrics, metrics)

	// Maintain metrics history
	if len(ec.metrics) > 1000 {
		ec.metrics = ec.metrics[len(ec.metrics)-1000:]
	}

	// Add to performance history
	perfPoint := PerformanceDataPoint{
		Timestamp: metrics.Timestamp,
		Metrics: map[string]float64{
			"total_bytes":        float64(metrics.TotalBytes),
			"total_chunks":       float64(metrics.TotalChunks),
			"average_chunk_size": metrics.AverageChunkSize,
			"chunking_time_ms":   float64(metrics.ChunkingTime.Milliseconds()),
			"throughput_mbps":    float64(metrics.TotalBytes) / (1024 * 1024) / metrics.ChunkingTime.Seconds(),
		},
	}

	ec.performanceHistory = append(ec.performanceHistory, perfPoint)
	if len(ec.performanceHistory) > ec.config.PerformanceWindow*2 {
		ec.performanceHistory = ec.performanceHistory[len(ec.performanceHistory)-ec.config.PerformanceWindow*2:]
	}
}

// hashData calculates hash of data
func (ec *EnhancedChunker) hashData(data []byte) string {
	ec.hasher.Reset()
	ec.hasher.Write(data)
	return hex.EncodeToString(ec.hasher.Sum(nil))
}

// calculateFingerprint calculates fingerprint of chunks
func (ec *EnhancedChunker) calculateFingerprint(chunks []Chunk) string {
	ec.hasher.Reset()
	for _, chunk := range chunks {
		ec.hasher.Write([]byte(chunk.ID))
	}
	return hex.EncodeToString(ec.hasher.Sum(nil))
}

// GetMetrics returns chunking metrics
func (ec *EnhancedChunker) GetMetrics() []ChunkingMetrics {
	ec.mu.RLock()
	defer ec.mu.RUnlock()

	// Return a copy
	metrics := make([]ChunkingMetrics, len(ec.metrics))
	copy(metrics, ec.metrics)
	return metrics
}

// GetPerformanceHistory returns performance history
func (ec *EnhancedChunker) GetPerformanceHistory() []PerformanceDataPoint {
	ec.mu.RLock()
	defer ec.mu.RUnlock()

	// Return a copy
	history := make([]PerformanceDataPoint, len(ec.performanceHistory))
	copy(history, ec.performanceHistory)
	return history
}

// GetCurrentParameters returns current chunking parameters
func (ec *EnhancedChunker) GetCurrentParameters() map[string]interface{} {
	ec.mu.RLock()
	defer ec.mu.RUnlock()

	return map[string]interface{}{
		"strategy":         ec.strategy,
		"current_min_size": ec.currentMinSize,
		"current_max_size": ec.currentMaxSize,
		"current_avg_size": ec.currentAvgSize,
		"cache_size":       len(ec.chunkCache),
		"metrics_count":    len(ec.metrics),
	}
}
