# Security Policy

The security of the Cina.Club platform and its administrative tools is a top priority. We appreciate the efforts of security researchers and the community to help us maintain a secure environment.

## Supported Versions

We provide security updates for the most recent `major` version of the Admin Dashboard. Please ensure you are running the latest version before reporting a vulnerability.

| Version | Supported          |
| ------- | ------------------ |
| 1.x.x   | :white_check_mark: |
| < 1.0   | :x:                |

## Reporting a Vulnerability

We take all security reports seriously. If you believe you have found a security vulnerability in the Admin Dashboard or any related services, please report it to us responsibly.

**Please do not report security vulnerabilities through public GitHub issues.**

Instead, please email us directly at **`<EMAIL>`**.

Please include the following details in your report:
- A clear description of the vulnerability, including its potential impact.
- The version of the application you were testing against.
- Detailed steps to reproduce the vulnerability, including any URLs, request/response captures, or sample code.
- Your name or alias for recognition in our security acknowledgments, if desired.

We will make our best effort to respond to your report within 48 hours and will keep you informed of our progress as we investigate and resolve the issue.

## Security Measures in Place

- **Dependabot**: We use Dependabot to automatically scan our dependencies for known vulnerabilities and create pull requests to update them.
- **Sentry**: Production errors and exceptions are captured in Sentry, allowing for rapid detection of unexpected behavior that could be security-related.
- **Code Reviews**: All pull requests require at least one review from a core team member to ensure code quality and security best practices are followed.
- **E2E Testing**: Our test suite includes checks for authentication and authorization to prevent regressions in access control.

Thank you for helping keep Cina.Club secure. 