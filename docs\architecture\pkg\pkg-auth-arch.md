好的，遵照您的指示。我将为您生成一份专门针对 **`pkg/auth`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`pkg/auth` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到代码实现的层面，详细阐述**JWT验证流程、JWKS自动刷新机制、RBAC策略的加载与执行，以及如何作为可组合的gRPC中间件**来为所有后端服务提供统一、坚固的安全屏障。

---
### CINA.CLUB - `pkg/auth` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `pkg/auth-srs.md` (v1.0)
**核心架构**: 可组合的gRPC拦截器 + JWKS自动刷新客户端 + 内存RBAC策略引擎

## 1. 概述

`pkg/auth` 是CINA.CLUB后端微服务安全体系的**第一道防线**。它提供了一系列标准化的gRPC拦截器，用于在请求到达业务逻辑之前，强制执行认证（Authentication）和授权（Authorization）策略。其核心挑战在于：
1.  **高性能**: 作为每个受保护API请求的必经之路，其性能开销必须被控制在微秒级别。
2.  **高可用性**: 不能因为其依赖（如`user-core-service`的JWKS端点）的瞬时故障而导致整个服务不可用。
3.  **安全性**: 必须能正确、安全地解析和验证JWT，防止各类令牌攻击。
4.  **灵活性与可组合性**: 不同的API可能需要不同级别的认证（如仅S2S，或S2S+用户，或S2S+用户+RBAC），架构必须支持灵活组合。
5.  **解耦**: 必须与`user-core-service`（签发者）和所有业务服务（使用者）完全解耦，只依赖于开放标准（JWT, JWKS）。

本架构设计通过构建一个**高性能的JWKS客户端**和**可组合的gRPC拦截器链**来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (拦截器工作流)

```mermaid
graph TD
    subgraph "gRPC Request"
        IncomingRequest[gRPC Request + Metadata]
    end

    subgraph "pkg/auth Interceptors Chain"
        style "pkg/auth Interceptors Chain" fill:#e0f7fa
        
        A[S2S Interceptor]
        B[User JWT Interceptor]
        C[RBAC Interceptor]
        
        subgraph "Internal Components"
            JWKSClient[JWKS Client (with cache)]
            S2SKeyProvider[S2S Public Key Provider]
            RBACEngine[In-Memory RBAC Engine]
        end
    end

    subgraph "Business Logic"
        Handler[gRPC Business Handler]
    end
    
    subgraph "External Dependencies"
        UserCore[user-core-service]
        ConfigCenter[Config Center / Files]
    end

    IncomingRequest --> A
    A -- "Validate S2S Token" --> S2SKeyProvider
    A -- "Pass to" --> B
    
    B -- "Validate User JWT" --> JWKSClient
    JWKSClient -- "Fetch/Cache JWKS" --> UserCore
    B -- "Inject UserInfo to Context, Pass to" --> C
    
    C -- "Check Permission" --> RBACEngine
    RBACEngine -- "Load Policy from" --> ConfigCenter
    C -- "Authorized, Pass to" --> Handler
    
    Handler -- "Returns" --> C
    C --> B --> A --> IncomingRequest
```

### 2.2 最终目录结构 (`pkg/auth/`)

```
pkg/auth/
├── jwks/
│   └── client.go               # ✨ JWKS自动刷新客户端实现 ✨
├── rbac/
│   └── engine.go               # ✨ 内存RBAC策略引擎实现 ✨
├── s2s/
│   └── verifier.go             # S2S Token验证器
├── interceptor/
│   ├── user_jwt.go             # 用户JWT拦截器
│   ├── s2s_jwt.go              # S2S JWT拦截器
│   ├── rbac.go                 # RBAC拦截器
│   └── chain.go                # (可选) 拦截器链式组合的便捷函数
├── context.go                  # 上下文注入与提取工具
└── auth.go                     # 主入口和配置
```

---

## 3. 各层职责深度解析

### 3.1 `jwks/client.go` - JWKS自动刷新客户端

这是保证用户JWT验证高性能和高可用的核心。

*   **`JWKSClient` struct**:
    *   `jwksURL string`: `user-core-service`的JWKS端点地址。
    *   `cache *gocache.Cache`: 一个带TTL的内存缓存（如使用`patrickmn/go-cache`），用于存储获取到的`jose.JSONWebKeySet`。
    *   `refresher *rate.Limiter`: 一个速率限制器（`golang.org/x/time/rate`），防止在JWKS端点故障时发生“惊群效应”(thundering herd)。
*   **`GetKey(token *jwt.Token)` method**:
    1.  首先尝试从**内存缓存**中获取JWKS。
    2.  **缓存命中**: 从JWKS中根据`kid`（Key ID）查找公钥并返回。
    3.  **缓存未命中**:
        a. **通过速率限制器**检查是否允许发起新的HTTP请求。
        b. 如果允许，则发起HTTP请求获取最新的JWKS。
        c. 将获取到的JWKS存入缓存，并设置一个合理的TTL（如1小时）。
        d. 从新的JWKS中查找公钥并返回。
        e. 如果HTTP请求失败，则尝试返回**上一次成功获取的、可能已过期的缓存（stale cache）**，同时在后台goroutine中继续尝试刷新。这是**容错关键**。
        f. 如果连stale cache都没有，则返回错误。

### 3.2 `rbac/engine.go` - 内存RBAC策略引擎

*   **`RBACEngine` struct**:
    *   `policy map[string]map[string]struct{}`: 一个高效的、用于快速查找的权限映射。`map[role] -> map[permission] -> {}`。
*   **`NewRBACEngine(policyData)` method**:
    *   在服务启动时调用，接收一个从配置文件加载的策略数据。
    *   将策略数据转换为上述的`policy` map，存储在内存中以备快速查询。
*   **`Can(roles []string, requiredPermission string)` method**:
    *   一个极快的内存查找操作。
    *   遍历用户的所有`roles`，检查是否有任何一个role的权限map中包含`requiredPermission`。

### 3.3 `interceptor/` - gRPC拦截器实现

*   **`user_jwt.go`**: `UserJWTInterceptor`
    *   从gRPC元数据中提取`Bearer` token。
    *   使用`jwt.Parse`和`jwksClient.GetKey`来验证token。
    *   验证所有标准claims（`exp`, `iss`, `aud`等）。
    *   验证通过后，将解析出的`AuthenticatedUser`（包含`ID`, `Roles`等）通过`context.go`中的工具函数注入到`ctx`中。
*   **`s2s_jwt.go`**: `S2SJWTInterceptor`
    *   类似`UserJWTInterceptor`，但使用`s2s.Verifier`来验证。
    *   `s2s.Verifier`会从一个可配置的公钥提供者（如一个map或服务发现）获取调用方服务的公钥。
*   **`rbac.go`**: `RBACInterceptor`
    *   **构造函数**: `NewRBACInterceptor(engine *rbac.RBACEngine, rpcPermissions map[string]string)`。`rpcPermissions`是一个从gRPC服务器反射或手动配置的、从`fullMethodName`到所需`permission`的映射。
    *   **拦截逻辑**:
        1.  从`ctx`中提取`AuthenticatedUser`。如果不存在，返回`Unauthenticated`错误。
        2.  根据当前RPC的`fullMethodName`，从`rpcPermissions`映射中找到所需的权限。如果找不到，默认放行或拒绝（取决于安全策略）。
        3.  调用`rbacEngine.Can(user.Roles, requiredPermission)`进行检查。
        4.  如果无权，返回`PermissionDenied`错误。

### 3.4 `auth.go` - 主入口与配置

```go
package auth

// Config 是初始化所有认证授权组件所需的配置
type Config struct {
    UserCoreJWKSUrl string                      `mapstructure:"user_core_jwks_url"`
    RBACPolicy      map[string][]string           `mapstructure:"rbac_policy"`
    S2SPublicKeys   map[string]string           `mapstructure:"s2s_public_keys"` // map[service_name] -> public_key_pem
}

// AuthSuite 是一个便捷的结构体，封装了所有初始化好的组件
type AuthSuite struct {
    UserJWTInterceptor grpc.UnaryServerInterceptor
    S2SInterceptor     grpc.UnaryServerInterceptor
    RBACInterceptor    grpc.UnaryServerInterceptor
    // ...
}

// NewAuthSuite 是唯一的构造函数，它会初始化所有内部组件
func NewAuthSuite(cfg Config, rpcPermissions map[string]string) (*AuthSuite, error) {
    // 1. 初始化JWKS Client
    jwksClient := jwks.NewClient(cfg.UserCoreJWKSUrl, ...)

    // 2. 初始化RBAC Engine
    rbacEngine := rbac.NewRBACEngine(cfg.RBACPolicy)
    
    // 3. 初始化S2S Verifier
    s2sVerifier := s2s.NewVerifier(cfg.S2SPublicKeys)
    
    // 4. 创建各个拦截器实例
    suite := &AuthSuite{
        UserJWTInterceptor: interceptor.NewUserJWTInterceptor(jwksClient),
        S2SInterceptor:     interceptor.NewS2SInterceptor(s2sVerifier),
        RBACInterceptor:    interceptor.NewRBACInterceptor(rbacEngine, rpcPermissions),
    }

    return suite, nil
}
```

### 3.5 `context.go`
```go
package auth

type contextKey string

const (
    userContextKey contextKey = "auth_user"
)

func NewContextWithUser(ctx context.Context, user *AuthenticatedUser) context.Context {
    return context.WithValue(ctx, userContextKey, user)
}

func UserFromContext(ctx context.Context) (*AuthenticatedUser, bool) {
    user, ok := ctx.Value(userContextKey).(*AuthenticatedUser)
    return user, ok
}
```

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`pkg/auth`：
1.  **分层与解耦**:
    *   `jwks/`, `rbac/`, `s2s/`等子包实现了核心的、独立的验证逻辑。
    *   `interceptor/`层负责将这些逻辑适配到gRPC的拦截器模式中。
    *   顶层的`AuthSuite`和`Config`提供了统一、简单的入口，对使用者屏蔽了内部复杂性。
2.  **高性能与高可用设计**: `JWKSClient`中内置的**内存缓存 + stale cache + 速率限制**机制，是保证服务在`user-core-service`抖动或高负载时依然可用的关键。
3.  **内存优先的授权**: RBAC策略在服务启动时加载到内存中，使得每次授权检查都是**纳秒级**的内存操作，性能极高。
4.  **清晰的职责边界**: 严格遵守`pkg`库的原则，不处理任何与JWT签发相关的逻辑，只做验证。
5.  **可组合性**: 提供了独立的、可按需组合的拦截器，使得服务可以根据自己的安全需求（如某些内部服务可能只需要S2S认证）来构建拦截器链。

这种架构确保了`pkg/auth`能够以一种**高性能、高可用、安全可靠**的方式，为整个CINA.CLUB后端微服务生态系统提供统一的、坚固的认证授权保护。