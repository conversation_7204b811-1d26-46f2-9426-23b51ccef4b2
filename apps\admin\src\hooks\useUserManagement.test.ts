/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:20:00
 * Modified: 2025-01-23 18:20:00
 */

import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useUserManagement } from './useUserManagement'; // Assuming this hook exists
import { server } from '../mocks/server';
import { http, HttpResponse } from 'msw';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false, // Disable retries for testing
    },
  },
});

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('useUserManagement Hook', () => {
  it('should fetch users successfully', async () => {
    const { result } = renderHook(() => useUserManagement(), { wrapper });

    await waitFor(() => expect(result.current.usersQuery.isSuccess).toBe(true));

    expect(result.current.usersQuery.data).toBeDefined();
    expect(result.current.usersQuery.data?.length).toBeGreaterThan(0);
  });

  it('should handle server error when fetching users', async () => {
    // Override the default handler to return an error
    server.use(
      http.get('http://localhost:8080/api/users', () => {
        return new HttpResponse(null, { status: 500 });
      })
    );

    const { result } = renderHook(() => useUserManagement(), { wrapper });

    await waitFor(() => expect(result.current.usersQuery.isError).toBe(true));
    expect(result.current.usersQuery.error).toBeDefined();
  });

  it('should create a user successfully', async () => {
    const { result } = renderHook(() => useUserManagement(), { wrapper });
    
    const newUser = { email: '<EMAIL>', username: 'newuser' };
    result.current.createUserMutation.mutate(newUser);

    await waitFor(() => expect(result.current.createUserMutation.isSuccess).toBe(true));
    expect(result.current.createUserMutation.data).toBeDefined();
    expect(result.current.createUserMutation.data.id).toBe('new-user-id');
  });
}); 