/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package domain

import (
	"context"
	"time"
)

// Event represents a generic platform event
type Event interface {
	GetEventType() string
	GetEventID() string
	GetUserID() string
	GetPayload() []byte
	GetTimestamp() int64
}

// FeedRepository defines the interface for feed item persistence
type FeedRepository interface {
	// Create a new feed item
	CreateFeedItem(ctx context.Context, item *ActivityFeedItem) error

	// Update an existing feed item (for aggregation)
	UpdateFeedItem(ctx context.Context, item *ActivityFeedItem) error

	// Get a specific feed item by ID
	GetFeedItem(ctx context.Context, itemID string) (*ActivityFeedItem, error)

	// Get feed items for a user with pagination
	GetUserFeedItems(ctx context.Context, userID string, feedType FeedType, limit, offset int) ([]*ActivityFeedItem, error)

	// Get unread feed items for a user
	GetUnreadFeedItems(ctx context.Context, userID string, feedType FeedType, limit int) ([]*ActivityFeedItem, error)

	// Mark items as read
	MarkItemsAsRead(ctx context.Context, userID string, itemIDs []string) error

	// Mark all items of a feed type as read
	MarkAllFeedAsRead(ctx context.Context, userID string, feedType FeedType) error

	// Get total count of feed items for a user
	GetUserFeedCount(ctx context.Context, userID string, feedType FeedType) (int64, error)

	// Delete old feed items (cleanup)
	DeleteOldFeedItems(ctx context.Context, olderThan time.Time) (int64, error)

	// Get user activity statistics
	GetUserActivityStats(ctx context.Context, userID string, since time.Time) (*UserActivityStats, error)

	// Batch operations for performance
	BatchCreateFeedItems(ctx context.Context, items []*ActivityFeedItem) error
	BatchUpdateFeedItems(ctx context.Context, items []*ActivityFeedItem) error
}

// UnreadCountRepository defines the interface for managing unread counts
type UnreadCountRepository interface {
	// Get unread count for a specific feed type
	GetUnreadCount(ctx context.Context, userID string, feedType FeedType) (int, error)

	// Get all unread counts for a user
	GetAllUnreadCounts(ctx context.Context, userID string) (map[FeedType]int, error)

	// Increment unread count atomically
	IncrementUnreadCount(ctx context.Context, userID string, feedType FeedType) error

	// Decrement unread count atomically
	DecrementUnreadCount(ctx context.Context, userID string, feedType FeedType, count int) error

	// Set unread count to a specific value
	SetUnreadCount(ctx context.Context, userID string, feedType FeedType, count int) error

	// Reset all unread counts for a user
	ResetAllUnreadCounts(ctx context.Context, userID string) error

	// Reset unread count for a specific feed type
	ResetUnreadCount(ctx context.Context, userID string, feedType FeedType) error
}

// Logger interface defines the logging interface used by services
type Logger interface {
	Debug(ctx context.Context, msg string, args ...interface{})
	Info(ctx context.Context, msg string, args ...interface{})
	Warn(ctx context.Context, msg string, args ...interface{})
	Error(ctx context.Context, msg string, args ...interface{})
}

// UserActivityStats represents user activity statistics
type UserActivityStats struct {
	UserID             string                 `json:"user_id"`
	TotalItems         int64                  `json:"total_items"`
	UnreadItems        int64                  `json:"unread_items"`
	ItemsByFeedType    map[FeedType]int64     `json:"items_by_feed_type"`
	ItemsByActivity    map[ActivityType]int64 `json:"items_by_activity"`
	LastActivityAt     time.Time              `json:"last_activity_at"`
	MostActiveHour     int                    `json:"most_active_hour"` // 0-23
	AverageItemsPerDay float64                `json:"average_items_per_day"`
}
