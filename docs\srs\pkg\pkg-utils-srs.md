好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/utils`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`pkg/utils`的设计哲学、功能分类、代码规范和使用约束，作为所有后端服务通用工具函数的权威设计蓝图。

---
### CINA.CLUB - 共享后端库 `pkg/utils` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师/首席工程师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计原则与约束](#3-核心设计原则与约束)
4.  [功能需求 (按子包拆分)](#4-功能需求-按子包拆分)
    *   [4.1 `conv`: 类型转换](#41-conv-类型转换)
    *   [4.2 `crypto`: 加密与哈希](#42-crypto-加密与哈希)
    *   [4.3 `rand`: 随机数与字符串生成](#43-rand-随机数与字符串生成)
    *   [4.4 `slice`: 切片操作](#44-slice-切片操作)
    *   [4.5 `str`: 字符串操作](#45-str-字符串操作)
    *   [4.6 `timeutil`: 时间工具](#46-timeutil-时间工具)
    *   [4.7 `validator`: 通用验证器](#47-validator-通用验证器)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [非功能性需求](#6-非功能性需求)
7.  [技术约束与开发规范](#7-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的后端开发中，经常会遇到需要重复编写一些基础、通用的辅助函数的情况，例如类型转换、随机字符串生成、切片去重等。`pkg/utils` 包的目的在于提供一个**经过充分测试、高质量、零外部依赖**的通用工具函数库。通过将这些常用功能集中化，可以避免代码冗余，提高开发效率，并确保在整个平台中对这些基础操作的处理方式是一致和正确的。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   提供纯粹的、无状态的、与任何业务领域都无关的辅助函数。
    *   涵盖字符串处理、类型转换、随机数生成、切片操作、时间工具、通用加密/哈希、基础验证等领域。
*   **范围之外 (Out-of-Scope)**:
    *   任何包含业务逻辑的函数。
    *   任何需要依赖其他`pkg/`或`core/`包的函数。
    *   任何需要进行I/O操作（如数据库、网络）的函数。
    *   任何有副作用的函数（除非其功能明确如此，如修改传入的指针）。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/utils` 是位于`pkg/`目录下的**最基础、最底层的共享库**。它处于依赖链的最末端，**不依赖于任何其他自定义的`pkg/`或`core/`包**。它的设计目标是成为一个像Go标准库一样稳定、可靠的内部工具集。

#### 2.2. 设计原则
*   **零依赖 (Zero-Dependency)**: `pkg/utils`及其所有子包，除了Go标准库外，不应有任何第三方依赖。这保证了其通用性和稳定性，避免了因第三方库版本冲突带来的问题。
*   **纯函数 (Pure Functions)**: 绝大多数函数都应是纯函数，即对于相同的输入，总是返回相同的输出，并且没有可观察的副作用。
*   **高内聚 (High Cohesion)**: 功能相似的函数应被组织在同一个子包中（如所有字符串相关的函数在`str`子包）。
*   **明确性与简洁性 (Clarity & Simplicity)**: 函数名应清晰地表明其作用。实现应尽可能简单、高效。
*   **全面的测试 (Comprehensive Testing)**: 每个工具函数都必须有覆盖所有边界情况的单元测试。

---

### 3. 核心设计原则与约束

`pkg/utils` 必须被划分为多个具有明确职责的子包，以避免成为一个包罗万象的“垃圾桶”。开发者在导入时，应导入具体的子包，如`import "cinaclub.com/pkg/utils/str"`，而不是`import "cinaclub.com/pkg/utils"`。

---

### 4. 功能需求 (按子包拆分)

#### 4.1 `conv`: 类型转换
*   **职责**: 提供安全、便捷的基本类型和常用类型之间的转换。
*   **功能需求**:
    *   **FR4.1.1 (指针转换)**: 提供`ToPointer[T any](v T) *T`和`FromPointer[T any](p *T) T`泛型函数，用于基本类型与其指针之间的转换。
    *   **FR4.1.2 (字符串与数字)**: 提供`Atoi(s string, defaultValue int)`等函数，封装`strconv`，但在转换失败时返回一个默认值而不是`error`。
    *   **FR4.1.3 (任意类型到字符串)**: 提供`ToString(v interface{}) string`，使用`fmt.Sprintf("%v", ...)`进行转换。

#### 4.2 `crypto`: 加密与哈希
*   **职责**: 提供通用的、与业务无关的加密和哈希算法封装。**注意：这不处理E2EE或ALE的密钥管理，只提供算法本身。**
*   **功能需求**:
    *   **FR4.2.1 (MD5/SHA)**: 提供`MD5(s string)`和`SHA256(s string)`函数，返回十六进制编码的哈希值。
    *   **FR4.2.2 (HMAC)**: 提供`HMAC_SHA256(data, key []byte)`函数。
    *   **FR4.2.3 (密码哈希)**: 提供`HashPassword(password string)`和`CheckPasswordHash(password, hash string)`函数，内部**必须使用Argon2id**。

#### 4.3 `rand`: 随机数与字符串生成
*   **职责**: 提供密码学安全的随机数和随机字符串生成器。
*   **功能需求**:
    *   **FR4.3.1 (随机字符串)**: 提供`String(n int)`函数，生成指定长度的、由大小写字母和数字组成的随机字符串。
    *   **FR4.3.2 (随机数字串)**: 提供`Digits(n int)`函数，生成指定长度的纯数字字符串。
    *   **FR4.3.3 (底层实现)**: 所有随机生成器**必须**使用`crypto/rand`作为随机源，而不是不安全的`math/rand`。

#### 4.4 `slice`: 切片操作
*   **职责**: 提供Go标准库中没有的、但常用的泛型切片操作。
*   **功能需求**:
    *   **FR4.4.1 (去重)**: 提供`Unique[T comparable](s []T) []T`函数，移除切片中的重复元素。
    *   **FR4.4.2 (包含检查)**: 提供`Contains[T comparable](s []T, v T) bool`函数。
    *   **FR4.4.3 (差集/交集/并集)**: 提供`Difference`, `Intersection`, `Union`等集合操作函数。
    *   **FR4.4.4 (查找)**: 提供`Find[T any](s []T, predicate func(T) bool) (T, bool)`函数。

#### 4.5 `str`: 字符串操作
*   **职责**: 提供Go标准库`strings`包的补充功能。
*   **功能需求**:
    *   **FR4.5.1 (截断)**: 提供`Truncate(s string, length int, ellipsis string)`函数，按指定长度截断字符串并添加省略号。
    *   **FR4.5.2 (蛇形/驼峰转换)**: 提供`ToSnakeCase(s string)`和`ToCamelCase(s string)`函数。
    *   **FR4.5.3 (模板替换)**: 提供一个简单的`RenderTemplate(template string, data map[string]interface{}) string`函数，用于替换`{key}`形式的占位符。

#### 4.6 `timeutil`: 时间工具
*   **职责**: 提供对标准库`time`的常用封装。
*   **功能需求**:
    *   **FR4.6.1 (时间戳)**: 提供`NowMillis()`和`NowSeconds()`函数，获取当前的Unix毫秒/秒时间戳。
    *   **FR4.6.2 (格式化)**: 定义一组标准的时间格式化常量，如`ISO8601`, `DateOnly`。
    *   **FR4.6.3 (时间计算)**: 提供`StartOfDay(t time.Time)`和`EndOfDay(t time.Time)`等函数。

#### 4.7 `validator`: 通用验证器
*   **职责**: 提供一些通用的、可复用的数据验证函数。
*   **功能需求**:
    *   **FR4.7.1 (格式验证)**: 提供`IsEmail(s string)`, `IsPhoneNumber(s string, countryCode string)`, `IsURL(s string)`等基于正则表达式的验证函数。
    *   **FR4.7.2 (密码强度)**: 提供`CheckPasswordStrength(password string)`函数，检查密码是否满足长度、复杂度的要求。

---

### 5. 接口定义 (API Specification)

由于`pkg/utils`主要由独立的函数构成，本节以代码签名的形式列出关键接口。

```go
// pkg/utils/conv/pointer.go
func ToPointer[T any](v T) *T
func FromPointer[T any](p *T, defaultValue T) T

// pkg/utils/crypto/password.go
func HashPassword(password string) (string, error)
func CheckPasswordHash(password, hash string) bool

// pkg/utils/rand/string.go
func String(n int) (string, error)

// pkg/utils/slice/unique.go
func Unique[T comparable](s []T) []T

// pkg/utils/str/case.go
func ToSnakeCase(s string) string

// pkg/utils/timeutil/format.go
const (
    ISO8601 = "2006-01-02T15:04:05Z07:00"
)

// pkg/utils/validator/format.go
func IsEmail(s string) bool
```

---

### 6. 非功能性需求

*   **NFR6.1 (性能)**: 所有工具函数的实现都必须是高性能的。特别是在字符串操作和切片操作中，应注意避免不必要的内存分配。
*   **NFR6.2 (可靠性)**: 函数行为必须与文档和函数名完全一致。边界情况（如nil输入、空字符串、空切片）必须被正确处理。
*   **NFR6.3 (可测试性)**: **测试覆盖率目标为100%**。对于工具函数库，这是可以且必须达到的目标。

---

### 7. 技术约束与开发规范

*   **TC7.1 (依赖)**: **严格禁止任何非Go标准库的依赖**。
*   **TC7.2 (Go版本)**: 必须使用支持泛型的Go版本（Go 1.18+）。
*   **TC7.3 (代码风格)**: 遵循平台统一的`golangci-lint`规范。
*   **TC7.4 (文档)**:
    *   每个公共函数都必须有清晰的GoDoc注释，解释其功能、参数、返回值和任何特殊行为。
    *   必须提供可直接运行的GoDoc示例。
*   **TC7.5 (代码审查)**:
    *   向`pkg/utils`添加任何新函数或修改现有函数，都必须通过Pull Request进行。
    *   审查的核心标准是：**“这个函数是否足够通用？它是否真的不属于任何一个具体的业务领域？它的实现是否最高效和最安全？”**
*   **TC7.6 (组织结构)**: 任何新的功能类别都应创建新的子包，而不是将所有函数都放在根`utils`包下，以保持代码的组织性和可维护性。

---
这份SRS为`pkg/utils`库的设计和实现提供了坚实、全面的指导。通过建立这样一个高质量、零依赖的通用工具集，CINA.CLUB平台可以确保基础操作的一致性和可靠性，同时显著提升后端开发的效率和代码质量。