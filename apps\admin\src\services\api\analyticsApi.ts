/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 15:45:00
 * Modified: 2025-01-23 15:45:00
 */

import { apiClient } from '@/lib/api-client';
import type { 
  KPI, 
  Dashboard, 
  Widget,
  TimePeriod,
  ChartData,
  AnalyticsMetrics,
  ReportTemplate,
  CustomReport,
  UserAnalytics,
  ContentAnalytics,
  FinancialAnalytics,
  SystemAnalytics
} from '@/types/analytics';

/**
 * Analytics API Service
 * Provides comprehensive analytics and reporting functionality
 */
export class AnalyticsApiService {
  private static readonly BASE_PATH = '/api/v1/analytics';

  /**
   * Get main dashboard data with KPIs
   */
  static async getDashboardData(timeRange: TimePeriod = '30d'): Promise<Dashboard> {
    const response = await apiClient.get(`${this.BASE_PATH}/dashboard`, {
      params: { timeRange }
    });
    return response.data;
  }

  /**
   * Get specific KPI data
   */
  static async getKPI(kpiId: string, timeRange: TimePeriod = '30d'): Promise<KPI> {
    const response = await apiClient.get(`${this.BASE_PATH}/kpi/${kpiId}`, {
      params: { timeRange }
    });
    return response.data;
  }

  /**
   * Get multiple KPIs at once
   */
  static async getKPIs(kpiIds: string[], timeRange: TimePeriod = '30d'): Promise<KPI[]> {
    const response = await apiClient.post(`${this.BASE_PATH}/kpi/batch`, {
      kpiIds,
      timeRange
    });
    return response.data;
  }

  /**
   * Get user analytics data
   */
  static async getUserAnalytics(params?: {
    timeRange?: TimePeriod;
    segment?: 'all' | 'new' | 'returning' | 'active' | 'inactive';
    cohort?: string;
  }): Promise<UserAnalytics> {
    const response = await apiClient.get(`${this.BASE_PATH}/users`, { params });
    return response.data;
  }

  /**
   * Get content analytics data
   */
  static async getContentAnalytics(params?: {
    timeRange?: TimePeriod;
    contentType?: string;
    category?: string;
  }): Promise<ContentAnalytics> {
    const response = await apiClient.get(`${this.BASE_PATH}/content`, { params });
    return response.data;
  }

  /**
   * Get financial analytics data
   */
  static async getFinancialAnalytics(params?: {
    timeRange?: TimePeriod;
    currency?: string;
  }): Promise<FinancialAnalytics> {
    const response = await apiClient.get(`${this.BASE_PATH}/financial`, { params });
    return response.data;
  }

  /**
   * Get system analytics data
   */
  static async getSystemAnalytics(params?: {
    timeRange?: TimePeriod;
    serviceId?: string;
  }): Promise<SystemAnalytics> {
    const response = await apiClient.get(`${this.BASE_PATH}/system`, { params });
    return response.data;
  }

  /**
   * Get chart data for specific metric
   */
  static async getChartData(
    metric: string,
    timeRange: TimePeriod = '30d',
    groupBy: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<ChartData> {
    const response = await apiClient.get(`${this.BASE_PATH}/chart/${metric}`, {
      params: { timeRange, groupBy }
    });
    return response.data;
  }

  /**
   * Get real-time metrics
   */
  static async getRealTimeMetrics(): Promise<{
    activeUsers: number;
    pageViews: number;
    newRegistrations: number;
    revenue: number;
    systemHealth: number;
    lastUpdated: string;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/realtime`);
    return response.data;
  }

  /**
   * Get available report templates
   */
  static async getReportTemplates(): Promise<ReportTemplate[]> {
    const response = await apiClient.get(`${this.BASE_PATH}/reports/templates`);
    return response.data;
  }

  /**
   * Create custom report
   */
  static async createCustomReport(report: Omit<CustomReport, 'id' | 'createdAt' | 'updatedAt'>): Promise<CustomReport> {
    const response = await apiClient.post(`${this.BASE_PATH}/reports`, report);
    return response.data;
  }

  /**
   * Update custom report
   */
  static async updateCustomReport(id: string, report: Partial<CustomReport>): Promise<CustomReport> {
    const response = await apiClient.put(`${this.BASE_PATH}/reports/${id}`, report);
    return response.data;
  }

  /**
   * Get custom reports list
   */
  static async getCustomReports(params?: {
    status?: 'active' | 'paused' | 'draft';
    createdBy?: string;
  }): Promise<CustomReport[]> {
    const response = await apiClient.get(`${this.BASE_PATH}/reports`, { params });
    return response.data;
  }

  /**
   * Get custom report by ID
   */
  static async getCustomReport(id: string): Promise<CustomReport> {
    const response = await apiClient.get(`${this.BASE_PATH}/reports/${id}`);
    return response.data;
  }

  /**
   * Delete custom report
   */
  static async deleteCustomReport(id: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete(`${this.BASE_PATH}/reports/${id}`);
    return response.data;
  }

  /**
   * Execute report and get data
   */
  static async executeReport(id: string): Promise<{
    data: any[];
    metadata: {
      generatedAt: string;
      rowCount: number;
      executionTime: number;
    };
  }> {
    const response = await apiClient.post(`${this.BASE_PATH}/reports/${id}/execute`);
    return response.data;
  }

  /**
   * Export report data
   */
  static async exportReport(
    id: string,
    format: 'csv' | 'excel' | 'pdf' = 'csv',
    options?: {
      includeCharts?: boolean;
      dateRange?: { start: string; end: string };
    }
  ): Promise<Blob> {
    const response = await apiClient.post(`${this.BASE_PATH}/reports/${id}/export`, {
      format,
      options
    }, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Schedule report execution
   */
  static async scheduleReport(
    id: string,
    schedule: {
      frequency: 'daily' | 'weekly' | 'monthly';
      time: string; // HH:MM format
      timezone: string;
      recipients: string[];
      format: 'csv' | 'excel' | 'pdf';
    }
  ): Promise<{ success: boolean; scheduleId: string }> {
    const response = await apiClient.post(`${this.BASE_PATH}/reports/${id}/schedule`, schedule);
    return response.data;
  }

  /**
   * Get widget data
   */
  static async getWidgetData(widgetId: string, params?: Record<string, any>): Promise<Widget> {
    const response = await apiClient.get(`${this.BASE_PATH}/widgets/${widgetId}`, { params });
    return response.data;
  }

  /**
   * Update widget configuration
   */
  static async updateWidget(widgetId: string, config: Partial<Widget>): Promise<Widget> {
    const response = await apiClient.put(`${this.BASE_PATH}/widgets/${widgetId}`, config);
    return response.data;
  }

  /**
   * Get cohort analysis data
   */
  static async getCohortAnalysis(params?: {
    metric: 'retention' | 'revenue' | 'engagement';
    period: 'week' | 'month';
    cohortSize?: number;
  }): Promise<{
    cohorts: Array<{
      cohortLabel: string;
      totalUsers: number;
      periods: Array<{
        period: number;
        value: number;
        percentage: number;
      }>;
    }>;
    averageRetention: number[];
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/cohort`, { params });
    return response.data;
  }

  /**
   * Get funnel analysis data
   */
  static async getFunnelAnalysis(
    funnelSteps: string[],
    timeRange: TimePeriod = '30d'
  ): Promise<{
    steps: Array<{
      name: string;
      users: number;
      conversionRate: number;
      dropOffRate: number;
    }>;
    overallConversion: number;
  }> {
    const response = await apiClient.post(`${this.BASE_PATH}/funnel`, {
      steps: funnelSteps,
      timeRange
    });
    return response.data;
  }

  /**
   * Get A/B test results
   */
  static async getABTestResults(testId: string): Promise<{
    testName: string;
    status: 'running' | 'completed' | 'paused';
    variants: Array<{
      name: string;
      users: number;
      conversions: number;
      conversionRate: number;
      confidence: number;
    }>;
    winner?: string;
    significance: number;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/ab-tests/${testId}`);
    return response.data;
  }

  /**
   * Get available metrics list
   */
  static async getAvailableMetrics(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    dataType: 'number' | 'percentage' | 'currency' | 'duration';
    aggregationType: 'sum' | 'avg' | 'count' | 'max' | 'min';
  }>> {
    const response = await apiClient.get(`${this.BASE_PATH}/metrics`);
    return response.data;
  }

  /**
   * Stream real-time analytics data
   */
  static streamRealTimeData(onData: (data: AnalyticsMetrics) => void): WebSocket {
    const wsUrl = `ws://${window.location.host}/api/v1/analytics/stream`;
    const ws = new WebSocket(wsUrl);
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data) as AnalyticsMetrics;
      onData(data);
    };
    
    return ws;
  }
}

export default AnalyticsApiService; 