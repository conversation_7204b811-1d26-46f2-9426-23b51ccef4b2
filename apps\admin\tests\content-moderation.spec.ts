/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:50:00
 * Modified: 2025-01-23 18:50:00
 */

import { test, expect } from '@playwright/test';

test.describe('Content Moderation Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Log in as a content moderator before each test
    await page.goto('/login');
    await page.getByLabel('Email Address').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password'); // Use a mock password
    await page.getByRole('button', { name: 'Login' }).click();
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('should allow a moderator to approve and reject content', async ({ page }) => {
    // **1. Navigate to the Content Moderation Queue**
    await page.goto('/content/moderation');
    await expect(page.getByRole('heading', { name: 'Content Moderation Queue' })).toBeVisible();

    // **2. Approve a piece of content**
    // Find the first content item in the queue (assuming it has a test id or unique text)
    const firstContentItem = page.locator('[data-testid="content-item-1"]');
    await expect(firstContentItem).toBeVisible();
    await firstContentItem.getByRole('button', { name: 'Approve' }).click();
    
    // The modal for confirmation might not exist for a simple approve, but if it does:
    // await page.getByRole('button', { name: 'Confirm Approve' }).click();
    
    await expect(page.locator('.ant-message-success')).toHaveText(/Content approved/);
    await expect(firstContentItem).not.toBeVisible(); // Verify it's removed from the queue

    // **3. Reject a piece of content**
    const secondContentItem = page.locator('[data-testid="content-item-2"]');
    await expect(secondContentItem).toBeVisible();
    await secondContentItem.getByRole('button', { name: 'Reject' }).click();

    // A modal should appear to select a rejection reason
    await expect(page.getByRole('dialog')).toBeVisible();
    await page.getByLabel('Rejection Reason').selectOption('INAPPROPRIATE_CONTENT');
    await page.getByLabel('Moderator Notes').fill('This content violates our community guidelines.');
    
    await page.getByRole('button', { name: 'Confirm Rejection' }).click();
    
    await expect(page.locator('.ant-message-success')).toHaveText(/Content rejected/);
    await expect(secondContentItem).not.toBeVisible(); // Verify it's removed
  });
}); 