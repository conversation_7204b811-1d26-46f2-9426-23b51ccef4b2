/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package domain

import (
	"context"
	"fmt"
	"time"

	"cina.club/pkg/logger"
)

// AggregationAction represents the action to take after aggregation processing
type AggregationAction string

const (
	AggregationActionCreate AggregationAction = "create" // Create a new item
	AggregationActionUpdate AggregationAction = "update" // Update existing aggregated item
)

// AggregationResult represents the result of aggregation processing
type AggregationResult struct {
	Action         AggregationAction `json:"action"`
	Item           *ActivityFeedItem `json:"item"`
	ExistingItemID string            `json:"existing_item_id,omitempty"`
	UpdatedActors  []Actor           `json:"updated_actors,omitempty"`
	AggregationKey string            `json:"aggregation_key,omitempty"`
}

// Cache interface for aggregation tracking
type Cache interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value string, expiration time.Duration) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// Aggregator handles intelligent aggregation and noise reduction
type Aggregator struct {
	cache  Cache
	logger logger.Logger
	rules  map[string]*AggregationRule
}

// NewAggregator creates a new aggregator instance
func NewAggregator(cache Cache, logger logger.Logger) *Aggregator {
	return &Aggregator{
		cache:  cache,
		logger: logger,
		rules:  DefaultAggregationRules(),
	}
}

// AggregateOrNew processes an activity item and returns aggregation result
func (a *Aggregator) AggregateOrNew(ctx context.Context, item *ActivityFeedItem) (*AggregationResult, error) {
	// Find matching aggregation rule
	rule := a.findAggregationRule(item.FeedType, item.ActivityType)
	if rule == nil || !rule.Enabled {
		// No aggregation rule found or disabled, create new item
		return &AggregationResult{
			Action: AggregationActionCreate,
			Item:   item,
		}, nil
	}

	// Generate aggregation key
	aggregationKey := rule.GetAggregationKey(item)
	item.AggregationKey = aggregationKey

	// Check if there's an existing aggregation within the time window
	existingItemID, err := a.findExistingAggregation(ctx, aggregationKey)
	if err != nil {
		a.logger.Error(ctx, "Failed to check existing aggregation",
			"error", err,
			"aggregation_key", aggregationKey)
		// On error, default to creating new item
		return &AggregationResult{
			Action: AggregationActionCreate,
			Item:   item,
		}, nil
	}

	if existingItemID == "" {
		// No existing aggregation found, create new aggregated item
		item.IsAggregated = true
		item.AggregationCount = len(item.Actors)
		item.AggregationWindow = int(rule.TimeWindow.Minutes())

		// Track this aggregation
		if err := a.trackAggregation(ctx, aggregationKey, item.ID, rule.TimeWindow); err != nil {
			a.logger.Warn(ctx, "Failed to track aggregation",
				"error", err,
				"aggregation_key", aggregationKey,
				"item_id", item.ID)
		}

		return &AggregationResult{
			Action:         AggregationActionCreate,
			Item:           item,
			AggregationKey: aggregationKey,
		}, nil
	}

	// Existing aggregation found, update it
	updatedActors := a.mergeActors(item.Actors, rule.MaxActors)

	return &AggregationResult{
		Action:         AggregationActionUpdate,
		ExistingItemID: existingItemID,
		UpdatedActors:  updatedActors,
		AggregationKey: aggregationKey,
	}, nil
}

// findAggregationRule finds the matching aggregation rule
func (a *Aggregator) findAggregationRule(feedType FeedType, activityType ActivityType) *AggregationRule {
	key := string(feedType) + ":" + string(activityType)
	return a.rules[key]
}

// findExistingAggregation checks if there's an existing aggregation within time window
func (a *Aggregator) findExistingAggregation(ctx context.Context, aggregationKey string) (string, error) {
	cacheKey := "aggregation_tracker:" + aggregationKey

	exists, err := a.cache.Exists(ctx, cacheKey)
	if err != nil {
		return "", fmt.Errorf("failed to check cache existence: %w", err)
	}

	if !exists {
		return "", nil
	}

	itemID, err := a.cache.Get(ctx, cacheKey)
	if err != nil {
		return "", fmt.Errorf("failed to get cached item ID: %w", err)
	}

	return itemID, nil
}

// trackAggregation tracks an aggregation in cache with TTL
func (a *Aggregator) trackAggregation(ctx context.Context, aggregationKey, itemID string, timeWindow time.Duration) error {
	cacheKey := "aggregation_tracker:" + aggregationKey
	return a.cache.Set(ctx, cacheKey, itemID, timeWindow)
}

// mergeActors merges new actors with existing ones, respecting max actors limit
func (a *Aggregator) mergeActors(newActors []Actor, maxActors int) []Actor {
	// For simplicity, we just return the new actors here
	// In a real implementation, this would merge with existing actors from the database
	if len(newActors) > maxActors {
		return newActors[:maxActors]
	}
	return newActors
}

// UpdateAggregationRules updates the aggregation rules
func (a *Aggregator) UpdateAggregationRules(rules map[string]*AggregationRule) {
	a.rules = rules
}

// GetAggregationRules returns the current aggregation rules
func (a *Aggregator) GetAggregationRules() map[string]*AggregationRule {
	return a.rules
}

// AddAggregationRule adds or updates a single aggregation rule
func (a *Aggregator) AddAggregationRule(key string, rule *AggregationRule) {
	a.rules[key] = rule
}

// RemoveAggregationRule removes an aggregation rule
func (a *Aggregator) RemoveAggregationRule(key string) {
	delete(a.rules, key)
}

// IsAggregatable checks if an activity type can be aggregated
func (a *Aggregator) IsAggregatable(feedType FeedType, activityType ActivityType) bool {
	rule := a.findAggregationRule(feedType, activityType)
	return rule != nil && rule.Enabled
}

// CleanupExpiredAggregations removes expired aggregation trackers from cache
func (a *Aggregator) CleanupExpiredAggregations(ctx context.Context) error {
	// This is handled automatically by Redis TTL, but this method can be used
	// for additional cleanup logic if needed
	a.logger.Debug(ctx, "Cleanup expired aggregations called")
	return nil
}

// GetAggregationStats returns statistics about aggregations
func (a *Aggregator) GetAggregationStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})
	stats["total_rules"] = len(a.rules)

	enabledRules := 0
	for _, rule := range a.rules {
		if rule.Enabled {
			enabledRules++
		}
	}
	stats["enabled_rules"] = enabledRules

	return stats
}
