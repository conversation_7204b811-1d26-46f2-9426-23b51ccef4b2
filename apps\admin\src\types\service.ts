/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

// Service status enumeration
export enum ServiceStatus {
  RUNNING = 'RUNNING',
  STOPPED = 'STOPPED',
  STARTING = 'STARTING',
  STOPPING = 'STOPPING',
  ERROR = 'ERROR',
  UNKNOWN = 'UNKNOWN',
}

// Service health status
export enum HealthStatus {
  HEALTHY = 'HEALTHY',
  UNHEALTHY = 'UNHEALTHY',
  DEGRADED = 'DEGRADED',
  CRITICAL = 'CRITICAL',
  UNKNOWN = 'UNKNOWN',
}

// Service types
export enum ServiceType {
  API = 'API',
  DATABASE = 'DATABASE',
  CACHE = 'CACHE',
  QUEUE = 'QUEUE',
  STORAGE = 'STORAGE',
  EXTERNAL = 'EXTERNAL',
  MICROSERVICE = 'MICROSERVICE',
}

// Base service interface
export interface Service {
  id: string
  name: string
  displayName: string
  description?: string
  type: ServiceType
  status: ServiceStatus
  health: HealthStatus
  version: string
  endpoint?: string
  port?: number
  host?: string
  environment: string
  tags: string[]
  dependencies: string[]
  createdAt: string
  updatedAt: string
  lastHealthCheck: string
}

// Service metrics
export interface ServiceMetrics {
  serviceId: string
  timestamp: string
  cpu: {
    usage: number // percentage
    limit?: number
  }
  memory: {
    usage: number // bytes
    limit?: number
    percentage: number
  }
  network: {
    inbound: number // bytes
    outbound: number // bytes
  }
  requests: {
    total: number
    success: number
    errors: number
    rate: number // requests per second
  }
  responseTime: {
    average: number // milliseconds
    p50: number
    p95: number
    p99: number
  }
  uptime: number // percentage
}

// Service configuration
export interface ServiceConfig {
  serviceId: string
  config: Record<string, any>
  environment: Record<string, string>
  secrets: string[] // secret keys (values hidden)
  resources: {
    cpu?: string
    memory?: string
    storage?: string
  }
  scaling: {
    minReplicas: number
    maxReplicas: number
    targetCpu?: number
    targetMemory?: number
  }
}

// Service log entry
export interface ServiceLog {
  id: string
  serviceId: string
  timestamp: string
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL'
  message: string
  source?: string
  metadata?: Record<string, any>
  traceId?: string
  spanId?: string
}

// Service health check
export interface HealthCheck {
  serviceId: string
  endpoint: string
  method: 'GET' | 'POST' | 'HEAD'
  expectedStatus: number
  timeout: number // milliseconds
  interval: number // seconds
  retries: number
  headers?: Record<string, string>
  body?: string
}

// Service alert
export interface ServiceAlert {
  id: string
  serviceId: string
  type: 'CPU' | 'MEMORY' | 'DISK' | 'RESPONSE_TIME' | 'ERROR_RATE' | 'CUSTOM'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  message: string
  threshold: number
  currentValue: number
  status: 'ACTIVE' | 'RESOLVED' | 'ACKNOWLEDGED'
  createdAt: string
  resolvedAt?: string
  acknowledgedAt?: string
  acknowledgedBy?: string
}

// Service deployment
export interface ServiceDeployment {
  id: string
  serviceId: string
  version: string
  status: 'PENDING' | 'IN_PROGRESS' | 'SUCCESS' | 'FAILED' | 'ROLLED_BACK'
  deployedBy: string
  deployedAt: string
  completedAt?: string
  rollbackAt?: string
  changelog?: string
  artifacts: Array<{
    name: string
    url: string
    size: number
  }>
}

// Service operation request
export interface ServiceOperation {
  action: 'START' | 'STOP' | 'RESTART' | 'SCALE' | 'DEPLOY' | 'ROLLBACK'
  serviceId: string
  params?: {
    replicas?: number
    version?: string
    config?: Record<string, any>
  }
  reason?: string
}

// Service list response
export interface ServiceListResponse {
  services: Service[]
  total: number
  page: number
  pageSize: number
}

// Service query parameters
export interface ServiceQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: ServiceStatus
  health?: HealthStatus
  type?: ServiceType
  environment?: string
  tags?: string[]
  sortBy?: 'name' | 'status' | 'health' | 'lastHealthCheck'
  sortOrder?: 'ASC' | 'DESC'
}

// Service statistics
export interface ServiceStatistics {
  totalServices: number
  runningServices: number
  healthyServices: number
  criticalAlerts: number
  averageResponseTime: number
  totalRequests: number
  errorRate: number
  uptimePercentage: number
  statusDistribution: Array<{
    status: ServiceStatus
    count: number
  }>
  typeDistribution: Array<{
    type: ServiceType
    count: number
  }>
}

// Service dependency graph
export interface ServiceDependency {
  source: string
  target: string
  type: 'API_CALL' | 'DATABASE' | 'QUEUE' | 'CACHE' | 'FILE_SYSTEM'
  critical: boolean
}

// Service topology
export interface ServiceTopology {
  services: Service[]
  dependencies: ServiceDependency[]
  clusters: Array<{
    name: string
    services: string[]
  }>
} 