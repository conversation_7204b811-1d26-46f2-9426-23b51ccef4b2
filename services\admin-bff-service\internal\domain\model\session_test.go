/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestAdminSession_IsValid(t *testing.T) {
	tests := []struct {
		name     string
		session  *AdminSession
		expected bool
	}{
		{
			name: "valid session",
			session: &AdminSession{
				ExpiresAt: time.Now().Add(1 * time.Hour),
			},
			expected: true,
		},
		{
			name: "expired session",
			session: &AdminSession{
				ExpiresAt: time.Now().Add(-1 * time.Hour),
			},
			expected: false,
		},
		{
			name: "session expiring now",
			session: &AdminSession{
				ExpiresAt: time.Now(),
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.session.IsValid()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdminSession_HasRole(t *testing.T) {
	session := &AdminSession{
		Roles: []string{"admin", "user_manager", "viewer"},
	}

	tests := []struct {
		name     string
		role     string
		expected bool
	}{
		{"has admin role", "admin", true},
		{"has user_manager role", "user_manager", true},
		{"has viewer role", "viewer", true},
		{"does not have editor role", "editor", false},
		{"empty role", "", false},
		{"case sensitive", "Admin", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := session.HasRole(tt.role)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdminSession_HasAnyRole(t *testing.T) {
	session := &AdminSession{
		Roles: []string{"admin", "user_manager"},
	}

	tests := []struct {
		name     string
		roles    []string
		expected bool
	}{
		{"has one of the roles", []string{"admin", "editor"}, true},
		{"has all roles", []string{"admin", "user_manager"}, true},
		{"has none of the roles", []string{"editor", "viewer"}, false},
		{"empty roles list", []string{}, false},
		{"nil roles", nil, false},
		{"partial match", []string{"admin"}, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := session.HasAnyRole(tt.roles...)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdminSession_HasPermission(t *testing.T) {
	tests := []struct {
		name       string
		session    *AdminSession
		permission SessionRole
		expected   bool
	}{
		{
			name: "super admin has all permissions",
			session: &AdminSession{
				Roles: []string{string(RoleSuperAdmin)},
			},
			permission: RoleAdmin, // any permission works for super admin
			expected:   true,
		},
		{
			name: "system admin has admin permissions",
			session: &AdminSession{
				Roles: []string{string(RoleSystemAdmin)},
			},
			permission: RoleAdmin,
			expected:   true,
		},
		{
			name: "admin has editor permissions",
			session: &AdminSession{
				Roles: []string{string(RoleAdmin)},
			},
			permission: RoleEditor,
			expected:   true,
		},
		{
			name: "editor has viewer permissions",
			session: &AdminSession{
				Roles: []string{string(RoleEditor)},
			},
			permission: RoleViewer,
			expected:   true,
		},
		{
			name: "viewer cannot access editor permissions",
			session: &AdminSession{
				Roles: []string{string(RoleViewer)},
			},
			permission: RoleEditor,
			expected:   false,
		},
		{
			name: "specialist role has own permissions",
			session: &AdminSession{
				Roles: []string{string(RoleContentModerator)},
			},
			permission: RoleContentModerator,
			expected:   true,
		},
		{
			name: "specialist role cannot access higher permissions",
			session: &AdminSession{
				Roles: []string{string(RoleContentModerator)},
			},
			permission: RoleAdmin,
			expected:   false,
		},
		{
			name: "multiple roles - has permission",
			session: &AdminSession{
				Roles: []string{string(RoleViewer), string(RoleEditor)},
			},
			permission: RoleEditor,
			expected:   true,
		},
		{
			name: "no roles",
			session: &AdminSession{
				Roles: []string{},
			},
			permission: RoleViewer,
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.session.HasPermission(tt.permission)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdminSession_UpdateLastUsed(t *testing.T) {
	session := &AdminSession{
		LastUsedAt: time.Now().Add(-1 * time.Hour),
	}

	oldTime := session.LastUsedAt
	session.UpdateLastUsed()

	assert.True(t, session.LastUsedAt.After(oldTime))
	assert.True(t, time.Since(session.LastUsedAt) < time.Second)
}

func TestSessionRole_Constants(t *testing.T) {
	// Test all role constants are defined correctly
	assert.Equal(t, "viewer", string(RoleViewer))
	assert.Equal(t, "editor", string(RoleEditor))
	assert.Equal(t, "admin", string(RoleAdmin))
	assert.Equal(t, "content_moderator", string(RoleContentModerator))
	assert.Equal(t, "user_manager", string(RoleUserManager))
	assert.Equal(t, "billing_manager", string(RoleBillingManager))
	assert.Equal(t, "analyst", string(RoleAnalyst))
	assert.Equal(t, "security_officer", string(RoleSecurityOfficer))
	assert.Equal(t, "system_admin", string(RoleSystemAdmin))
	assert.Equal(t, "super_admin", string(RoleSuperAdmin))
}

func TestRoleHierarchy(t *testing.T) {
	// Test role hierarchy logic
	tests := []struct {
		role         string
		canAccess    []string
		cannotAccess []string
	}{
		{
			role:         string(RoleSuperAdmin),
			canAccess:    []string{string(RoleSystemAdmin), string(RoleAdmin), string(RoleEditor), string(RoleViewer)},
			cannotAccess: []string{},
		},
		{
			role:         string(RoleSystemAdmin),
			canAccess:    []string{string(RoleAdmin), string(RoleEditor), string(RoleViewer)},
			cannotAccess: []string{string(RoleSuperAdmin)},
		},
		{
			role:         string(RoleAdmin),
			canAccess:    []string{string(RoleEditor), string(RoleViewer)},
			cannotAccess: []string{string(RoleSystemAdmin), string(RoleSuperAdmin)},
		},
		{
			role:         string(RoleEditor),
			canAccess:    []string{string(RoleViewer)},
			cannotAccess: []string{string(RoleAdmin), string(RoleSystemAdmin)},
		},
		{
			role:         string(RoleViewer),
			canAccess:    []string{},
			cannotAccess: []string{string(RoleEditor), string(RoleAdmin)},
		},
	}

	for _, tt := range tests {
		t.Run("role_"+tt.role, func(t *testing.T) {
			session := &AdminSession{Roles: []string{tt.role}}

			for _, permission := range tt.canAccess {
				assert.True(t, session.HasPermission(SessionRole(permission)),
					"Role %s should have permission %s", tt.role, permission)
			}

			for _, permission := range tt.cannotAccess {
				assert.False(t, session.HasPermission(SessionRole(permission)),
					"Role %s should not have permission %s", tt.role, permission)
			}
		})
	}
}

func TestEmployee_Struct(t *testing.T) {
	employee := &Employee{
		ID:       "emp123",
		Email:    "<EMAIL>",
		Name:     "Admin User",
		Roles:    []string{"admin", "user_manager"},
		IsActive: true,
	}

	assert.Equal(t, "emp123", employee.ID)
	assert.Equal(t, "<EMAIL>", employee.Email)
	assert.Equal(t, "Admin User", employee.Name)
	assert.Equal(t, []string{"admin", "user_manager"}, employee.Roles)
	assert.True(t, employee.IsActive)
}

func TestAdminSession_EdgeCases(t *testing.T) {
	t.Run("nil session", func(t *testing.T) {
		var session *AdminSession
		// Note: These will panic for nil receiver, which is expected behavior
		// In real code, we should check for nil before calling these methods

		// Test with non-nil but empty session instead
		session = &AdminSession{}
		assert.False(t, session.HasRole("admin"))
		assert.False(t, session.HasAnyRole("admin", "viewer"))
		assert.False(t, session.HasPermission(RoleAdmin))
	})

	t.Run("session with nil roles", func(t *testing.T) {
		session := &AdminSession{Roles: nil}
		assert.False(t, session.HasRole("admin"))
		assert.False(t, session.HasAnyRole("admin"))
		assert.False(t, session.HasPermission(RoleAdmin))
	})

	t.Run("session with empty roles", func(t *testing.T) {
		session := &AdminSession{Roles: []string{}}
		assert.False(t, session.HasRole("admin"))
		assert.False(t, session.HasAnyRole("admin"))
		assert.False(t, session.HasPermission(RoleAdmin))
	})
}

func TestSpecialRolePermissions(t *testing.T) {
	// Test that specialist roles have their own permissions but not hierarchical ones
	specialRoles := []SessionRole{
		RoleContentModerator,
		RoleUserManager,
		RoleBillingManager,
		RoleAnalyst,
		RoleSecurityOfficer,
	}

	for _, role := range specialRoles {
		t.Run("specialist_role_"+string(role), func(t *testing.T) {
			session := &AdminSession{Roles: []string{string(role)}}

			// Should have own permission
			assert.True(t, session.HasPermission(role))

			// Should not have hierarchical admin permissions
			assert.False(t, session.HasPermission(RoleAdmin))
			assert.False(t, session.HasPermission(RoleSystemAdmin))
			assert.False(t, session.HasPermission(RoleSuperAdmin))
		})
	}
}

func TestConcurrentAccess(t *testing.T) {
	// Test concurrent access to session methods
	session := &AdminSession{
		Roles:     []string{"admin", "viewer"},
		ExpiresAt: time.Now().Add(1 * time.Hour),
	}

	done := make(chan bool, 100)

	// Run 100 concurrent goroutines
	for i := 0; i < 100; i++ {
		go func() {
			defer func() { done <- true }()

			// These operations should be safe for concurrent access
			session.IsValid()
			session.HasRole("admin")
			session.HasAnyRole("admin", "viewer")
			session.HasPermission(RoleAdmin)
			session.UpdateLastUsed() // This modifies state but should be safe
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 100; i++ {
		<-done
	}

	// Verify session is still valid
	assert.True(t, session.IsValid())
	assert.True(t, session.HasRole("admin"))
}

// Benchmark tests
func BenchmarkAdminSession_HasRole(b *testing.B) {
	session := &AdminSession{
		Roles: []string{"admin", "user_manager", "viewer", "editor", "analyst"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		session.HasRole("analyst")
	}
}

func BenchmarkAdminSession_HasPermission(b *testing.B) {
	session := &AdminSession{
		Roles: []string{"admin"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		session.HasPermission(RoleViewer)
	}
}
