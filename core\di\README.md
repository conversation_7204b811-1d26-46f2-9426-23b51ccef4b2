# Dependency Injection Module

## Overview

The Dependency Injection (DI) module provides a flexible and type-safe mechanism for managing service dependencies across the Cina.Club core module. It enables loose coupling, improved testability, and centralized service management.

## Key Features

- **Type-Safe Service Registration**: Ensures compile-time type checking for registered services
- **Thread-Safe Operations**: Supports concurrent access to the service container
- **Global and Scoped Containers**: Provides both global and local dependency management
- **Flexible Service Interfaces**: Supports multiple service registration strategies

## Core Components

### `Container`
A thread-safe container for managing service dependencies.

#### Methods
- `Register(service interface{})`: Add a service to the container
- `Resolve(serviceType reflect.Type)`: Retrieve a service by type
- `MustResolve(serviceType reflect.Type)`: Retrieve a service, panicking if not found
- `Clear()`: Remove all registered services

### Service Registrar Interfaces
Specialized interfaces for different module types:
- `APIServiceRegistrar`
- `ModelServiceRegistrar`
- `AICoreServiceRegistrar`
- `DataSyncServiceRegistrar`
- `CryptoServiceRegistrar`

## Usage Example

```go
// Create a new container
container := di.NewContainer()

// Create and register services
aiEngine := aic.NewEngine()
models := []aic.AIModel{
    &aic.BaseAIModel{name: "TextGenerationModel"},
}

// Create a service registrar
registrar := aic.NewServiceRegistrar(aiEngine, models)

// Register services
err := registrar.Register(container)
if err != nil {
    log.Fatal(err)
}

// Initialize services
err = registrar.InitializeAIModels()
if err != nil {
    log.Fatal(err)
}

// Resolve a service
resolvedEngine, err := container.Resolve(reflect.TypeOf(&aic.Engine{}))
```

## Best Practices

1. Always register services as pointers
2. Use specific interfaces for service registration
3. Initialize services before registering
4. Use `GlobalContainer` sparingly
5. Prefer local containers for module-specific dependencies

## Future Improvements

- Add support for service lifecycle management
- Implement lazy loading of services
- Create more advanced dependency resolution strategies

## Contributing

Please follow the core module's contribution guidelines when extending the dependency injection module. 