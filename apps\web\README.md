# CINA.CLUB Web Application

CINA.CLUB 平台的现代化Web前端应用，基于Next.js 14构建。

## 🚀 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **HTTP客户端**: Axios
- **WebSocket**: Socket.io Client
- **UI组件**: Lucide Icons
- **测试**: Jest + Testing Library

## 📁 项目结构

```
apps/web/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── globals.css     # 全局样式
│   │   ├── layout.tsx      # 根布局
│   │   └── page.tsx        # 主页
│   ├── components/         # 可复用组件
│   ├── lib/               # 工具库
│   ├── hooks/             # 自定义Hook
│   ├── types/             # TypeScript类型定义
│   └── utils/             # 工具函数
├── public/                # 静态文件
├── package.json          # 依赖配置
├── next.config.js        # Next.js配置
├── tailwind.config.js    # Tailwind配置
├── tsconfig.json         # TypeScript配置
├── Dockerfile            # Docker构建文件
└── README.md             # 项目文档
```

## 🛠️ 开发指南

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 运行测试
npm test
```

### Docker开发

```bash
# 构建开发镜像
docker build --target development -t cina-web:dev .

# 运行开发容器
docker run -p 3000:3000 -v $(pwd):/app cina-web:dev

# 或使用docker-compose
docker-compose up web
```

## 🎨 功能特性

### 已实现功能

- ✅ **响应式设计**: 支持桌面、平板、移动设备
- ✅ **现代化UI**: 基于Tailwind CSS的美观界面
- ✅ **TypeScript支持**: 完整的类型安全
- ✅ **SEO优化**: 内置元数据和SEO支持
- ✅ **性能优化**: Next.js优化和图片懒加载

### 主要页面

- **首页**: 平台介绍和功能展示
- **功能展示**: 核心功能介绍
- **服务状态**: 实时系统状态展示
- **平台概览**: 可用服务列表

### 计划功能

- 🔄 **用户认证**: 登录、注册、个人资料
- 🔄 **AI助手**: 智能对话界面
- 🔄 **实时聊天**: WebSocket聊天功能
- 🔄 **文件管理**: 云文件上传下载
- 🔄 **搜索功能**: 全站内容搜索
- 🔄 **数据面板**: 个人数据统计

## 🔧 配置说明

### 环境变量

```env
# API配置
API_BASE_URL=http://localhost:8080
WS_BASE_URL=ws://localhost:8080

# Next.js配置
NODE_ENV=development
PORT=3000
NEXT_TELEMETRY_DISABLED=1
```

### API集成

应用通过以下方式与后端API通信：

```typescript
// API基础配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8080';

// HTTP请求
import axios from 'axios';
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// WebSocket连接
import io from 'socket.io-client';
const socket = io(process.env.WS_BASE_URL || 'ws://localhost:8080');
```

## 📱 界面截图

### 首页
- 现代化hero区域
- 功能特性展示
- 服务状态概览
- 响应式设计

### 特性
- 清晰的信息架构
- 直观的用户界面
- 流畅的动画效果
- 一致的设计语言

## 🚀 部署

### Docker部署

```bash
# 构建生产镜像
docker build --target production -t cina-web:latest .

# 运行生产容器
docker run -p 3000:3000 -e NODE_ENV=production cina-web:latest
```

### 生产环境

```bash
# 安装依赖
npm ci --only=production

# 构建应用
npm run build

# 启动应用
npm start
```

## 🧪 测试

### 单元测试

```bash
# 运行所有测试
npm test

# 监听模式
npm test -- --watch

# 生成覆盖率报告
npm test -- --coverage
```

### 测试结构

```
src/
├── components/
│   └── __tests__/      # 组件测试
├── hooks/
│   └── __tests__/      # Hook测试
└── utils/
    └── __tests__/      # 工具函数测试
```

## 🔍 开发工具

### 代码质量

- **ESLint**: 代码规范检查
- **TypeScript**: 类型检查
- **Prettier**: 代码格式化（推荐）

### 开发体验

- **热重载**: 开发时自动刷新
- **TypeScript**: 智能提示和错误检查
- **Tailwind**: 快速样式开发

## 📚 API文档

### 主要端点

```
GET /api/v1/health        # 健康检查
GET /api/v1/users         # 用户列表
POST /api/v1/auth/login   # 用户登录
GET /api/v1/search        # 搜索接口
POST /api/v1/ai/chat      # AI对话
```

### WebSocket事件

```
connect                   # 连接建立
message                   # 消息接收
user_online              # 用户上线
user_offline             # 用户下线
```

## 🤝 贡献指南

### 开发流程

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -m 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

### 代码规范

- 使用TypeScript进行开发
- 遵循ESLint规则
- 编写单元测试
- 添加适当的注释

### 组件开发

```typescript
// 组件模板
interface ComponentProps {
  title: string;
  children?: React.ReactNode;
}

export default function Component({ title, children }: ComponentProps) {
  return (
    <div className="component">
      <h2>{title}</h2>
      {children}
    </div>
  );
}
```

## 📄 许可证

Copyright (c) 2025 Cina.Club. All rights reserved.

## 🔗 相关链接

- [CINA.CLUB 主项目](../../README.md)
- [API文档](../../docs/api/)
- [架构设计](../../docs/architecture/)
- [部署指南](../../docs/deployment/) 