﻿好的，遵照您的指示，我们来生成一份为 `gamification-service` (游戏化服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **规则引擎与条件逻辑**: 引入更强大、更灵活的规则引擎，支持复杂的、可嵌套的条件逻辑（AND/OR/NOT）和更丰富的操作符。
2.  **多维度的激励体系**: 将激励体系拆分为**经验值与等级(XP & Level)**、**成就与徽章(Badges)**和**积分与商城(Points & Store)**三个并行系统，以满足不同运营需求。
3.  **排行榜系统**: 详细定义周期性、累计性和多维度的排行榜，并说明其计算和防作弊机制。
4.  **防作弊与滥用**: 增加专门章节，强调对异常行为的检测和处理，这是生产级游戏化系统的关键。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和扩展性指标。

这份文档将描绘一个功能强大、激励有效、治理完善且能深度驱动用户行为的平台级游戏化引擎。

---

### CINA.CLUB - gamification-service 需求规格说明书

**版本: 2.0 (生产级定义，集成多维激励与高级规则引擎)**  
**发布日期: 2025-06-22**  
**最后修订日期: 2025-06-22**  
**文档负责人:** [用户增长/社区运营团队负责人/架构师名称]  
**审批人:** [产品总监/CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了提升CINA.CLUB用户的参与度、活跃度、忠诚度和贡献度，`gamification-service` 旨在将游戏设计元素和机制应用于非游戏场景。通过建立一套**多维度、可配置、且防滥用**的激励体系，包括**经验值与等级**、**成就与徽章**、**积分与兑换**和**排行榜**，来正向引导用户完成对平台生态有价值的行为，并从中获得成就感、荣誉感和实际回报，从而促进平台生态的健康和繁荣。

#### 1.2. 服务范围
本服务 **负责**:
*   **游戏化规则引擎**:
    *   定义和管理“当用户行为X满足条件Y时，触发奖励Z”的规则。
    *   支持复杂的、可嵌套的条件逻辑。
*   **经验值(XP)与等级系统**:
    *   追踪用户经验值(XP)的获取。**注意**: 平台的主等级系统（基于在线时长）由`user-core-service`管理，本服务可以作为其XP的来源之一，或管理独立的“贡献等级”。本SRS将假设其管理独立的“贡献等级”。
*   **成就与徽章(Badge)系统**:
    *   定义各种一次性或多等级的徽章及其获取条件。
    *   在用户达成条件时自动授予徽章。
*   **积分(Points)系统**:
    *   管理一种可用于在“积分商城”中兑换虚拟或实体商品的可消耗积分。
    *   处理积分的获取和消耗。
*   **排行榜(Leaderboards)**: 计算和维护基于特定指标的周期性或累计性排行榜。
*   **奖励发放协调**: 当规则触发奖励时，调用相关服务（如`cina-coin-ledger-service`, `digital-twin-service`）发放奖励。
*   **防作弊与滥用检测**: 监控异常行为，防止刷分。

本服务 **不负责**:
*   管理**平台主等级**（QQ式等级，由`user-core-service`负责）。
*   管理**灵境币(CINA Coin)**的底层账本 (由`cina-coin-ledger-service`负责，本服务仅作为其调用方)。
*   产生源业务事件 (本服务是业务事件的消费者)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部微服务 (通过消息队列)**: 主要的事件来源方。
*   **CINA.CLUB客户端应用**: 通过API查询用户的游戏化档案（等级、徽章、积分、排名）。
*   **`user-core-service`**: (被本服务调用) 查询用户信息；本服务也可能为其主等级系统提供XP输入。
*   **CINA.CLUB平台管理员/运营团队**: 通过管理后台配置游戏化规则、徽章、排行榜和积分商城。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`gamification-service` 是CINA.CLUB平台的**用户激励层**和**行为引导引擎**。它通过即时、明确的正向反馈机制，将用户的日常操作转化为可量化的成就和荣誉，与平台的业务核心（服务、任务、社区）和经济系统（灵境币）紧密结合，形成一个驱动用户持续、深度参与的强大闭环。

#### 2.2. 主要功能概述
*   支持复杂条件的、事件驱动的规则引擎。
*   XP、徽章、积分三位一体的多维激励体系。
*   可配置的、支持防作弊的排行榜系统。
*   与平台奖励发放服务的解耦与协同。

### 3. 核心流程图

#### 3.1. 用户完成任务，触发多重奖励
```mermaid
sequenceDiagram
    participant SourceService as "service-offering-service"
    participant MQ as "Message Queue"
    participant GamificationService as GS
    participant RuleEngine as "Rules Engine"
    participant DB
    participant LedgerService as "cina-coin-ledger"
    participant NotificationService as NS

    SourceService->>MQ: 1. Publish TaskCompletedEvent {userId, taskValue, ...}
    
    MQ-->>GS: 2. Consume event
    GS->>RuleEngine: 3. Evaluate event against all active rules
    
    Note right of RuleEngine: Found 3 matching rules for this event
    
    RuleEngine-->>GS: 4. **Actions:** <br/> - Rule A: AWARD_XP (50) <br/> - Rule B: AWARD_POINTS (100) <br/> - Rule C: GRANT_BADGE ("first_task")
    
    GS->>DB: 5. **Start Transaction**
    GS->>DB: 6. Atomically update UserGamificationState (XP += 50, Points += 100)
    GS->>DB: 7. Check if user levels up. If so, update level.
    GS->>DB: 8. Grant badge "first_task" to user in UserEarnedBadges
    GS->>DB: 9. **Commit Transaction**
    
    GS->>GS: 10. **Asynchronously** process any rewards for level up/badge
    GS->>LedgerService: 11a. Request to credit 20 CINA Coins for level up
    GS->>NotificationService: 11b. Request to send "Level Up!" and "New Badge!" notifications
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 游戏化规则引擎
*   **FR4.1.1 (规则定义)**: 系统必须支持管理员通过API或后台界面创建和管理游戏化规则。
*   **FR4.1.2 (复杂条件)**: 规则的`Conditions`部分必须支持可嵌套的逻辑树（`AND`/`OR`/`NOT`），并支持丰富的操作符（`EQUALS`, `GREATER_THAN`, `CONTAINS`等）来对事件payload和用户上下文进行判断。
*   **FR4.1.3 (多重动作)**: 规则的`Actions`部分必须支持一个动作数组，允许一个事件触发多个奖励。
*   **FR4.1.4 (速率限制)**: 规则可以配置一个速率限制（如“每天最多触发3次”），以防止对同一行为的无限奖励。

#### 4.2. 多维激励体系
*   **FR4.2.1 (贡献等级)**:
    *   系统为每个用户维护一个独立的**贡献经验值(XP)**和**贡献等级(Level)**。
    *   等级提升可以解锁平台内的某些象征性或功能性权限（如自定义主页颜色）。
*   **FR4.2.2 (成就徽章)**:
    *   支持一次性获取的徽章和可升级的多等级徽章（如“回答被采纳1次/10次/100次”）。
    *   用户可以在其个人资料页选择并展示已获得的徽章。
*   **FR4.2.3 (可消耗积分)**:
    *   积分(`Points`)是一种非货币性的代币，用户可以通过完成任务获取。
    *   系统需提供API支持一个“积分商城”，允许用户消耗积分兑换**虚拟物品**（如Avatar资产、主页主题）或**实体优惠券**。
    *   积分消耗操作必须是原子的。

#### 4.3. 排行榜 (Leaderboards)
*   **FR4.3.1 (可配置)**: 支持管理员创建多种类型的排行榜，可定义其指标、周期（日/周/月/总）和榜单长度。
*   **FR4.3.2 (周期性重置)**: 周期性排行榜必须有后台任务在周期结束时自动进行结算、存档，并开始新的周期。
*   **FR4.3.3 (结算奖励)**: 可以在排行榜结算时，为排名前N的用户自动发放奖励。

#### 4.4. 防作弊与滥用
*   **FR4.4.1 (行为频率监控)**: 系统应监控来自同一用户或同一IP的事件频率，对于在短时间内触发大量奖励事件的行为进行标记或暂时阻止。
*   **FR4.4.2 (规则反向操作)**: 如果一个奖励行为被撤销（如：一个被点赞的回答被删除），系统必须能触发一个“反向”操作，扣除之前授予的XP和积分。这要求源事件（如`AnswerDeletedEvent`）包含足够的信息。
*   **FR44.3 (人工干预)**: 管理员必须能查看可疑行为日志，并手动撤销或调整用户的积分/徽章。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端RESTful API接口
*   **版本**: `/api/v1/gamification`
*   **认证**: User JWT。
*   **核心端点**:
    *   `GET /me/profile`: 获取当前用户的完整游戏化档案（贡献等级、XP、积分、最近获得的徽章）。
    *   `GET /me/badges`: 获取我已获得的徽章列表。
    *   `GET /me/points/history`: 获取我的积分收支历史。
    *   `GET /leaderboards/{leaderboardId}`: 获取排行榜。
    *   `POST /store/redeem`: 在积分商城兑换商品。Request: `{ "item_id": "...", "quantity": 1 }`

#### 5.2. 管理后台API接口
*   `GET/POST/PUT/DELETE /admin/rules/{id}`
*   `GET/POST/PUT/DELETE /admin/badges/{id}`
*   `GET/POST/PUT/DELETE /admin/leaderboards/{id}`
*   `POST /admin/users/{userId}/manual-award`: 手动奖励。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`gamification_rules`**: `id`, `name`, `trigger_event_type`, `conditions_logic_tree (JSONB)`, `actions_config (JSONB)`, `rate_limit_config (JSONB)`, `is_active`.
*   **`badge_definitions`**: `id`, `name`, `description`, `icon_url`, `rarity`, `levels_config (JSONB)`.
*   **`user_gamification_profiles`**: `user_id (PK)`, `contribution_level`, `contribution_xp`, `points_balance`, `version (for optimistic locking)`.
*   **`user_earned_badges`**: `(user_id, badge_id)` (PK), `current_level`, `earned_at`.
*   **`points_transactions`**: `id`, `user_id`, `amount` (+/-), `type` (`AWARD`, `REDEEM`), `reference_id`, `timestamp`.

#### 6.2. 缓存数据 (Redis)
*   `leaderboard:{type}:{period}` (Sorted Set): `member=userId, score=points`.
*   `user_action_throttle:{ruleId}:{userId}` (String with TTL): 用于规则的速率限制。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **事件处理延迟**: P99应在100ms内完成规则评估和本地DB更新。对下游服务的调用应异步化。
*   **API延迟**: 用户查询API的P99 < 150ms。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **事件处理**: 必须可靠，使用MQ的确认和重试机制，确保事件不丢失。
*   **奖励发放**: 对下游服务（如`cina-coin-ledger-service`）的调用失败必须有健壮的重试、幂等性和错误记录机制。

#### 7.3. 可扩展性需求
*   服务可水平扩展。规则引擎的评估是CPU密集型，事件消费者组需能随事件量伸缩。

#### 7.4. 安全性需求
*   **防作弊**: 如**FR4.4**所述，这是核心安全要求。
*   **Admin API**: 必须有严格的权限控制和操作审计。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **规则引擎**: 初期可使用简单的代码逻辑和JSONB配置实现。如果规则变得极其复杂，可考虑引入专用的Go规则引擎库（如`grule`）或基于DMN的引擎。
*   **排行榜**: 使用Redis的Sorted Set是最高效和最常见的实现方式。
*   **异步处理**: 所有非核心阻塞操作（特别是对外部服务的调用）都应通过`Asynq`等任务队列进行异步处理。

---
这份版本2.0的SRS文档为`gamification-service`构建了一个多维度、功能强大且治理完善的用户激励平台。它通过将复杂的行为规则与多样的激励手段相结合，能够有效地引导用户行为，提升平台活跃度和用户粘性，是CINA.CLUB运营策略的技术核心。