// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-24 10:19:52
// Modified: 2025-06-24 10:19:52

package models

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// ServiceOffering represents a service that can be offered on the platform
type ServiceOffering struct {
	ID              uuid.UUID              `json:"id"`
	ProviderID      uuid.UUID              `json:"provider_id"`
	Title           string                 `json:"title"`
	Description     string                 `json:"description"`
	Category        ServiceCategory        `json:"category"`
	Subcategory     string                 `json:"subcategory"`
	Tags            []string               `json:"tags"`
	Status          ServiceStatus          `json:"status"`
	PriceType       PriceType              `json:"price_type"`
	BasePrice       int64                  `json:"base_price"` // in cents
	Currency        string                 `json:"currency"`
	Duration        time.Duration          `json:"duration"`
	Location        *ServiceLocation       `json:"location,omitempty"`
	Requirements    []string               `json:"requirements"`
	Deliverables    []string               `json:"deliverables"`
	Images          []string               `json:"images"`
	Rating          float64                `json:"rating"`
	ReviewCount     int64                  `json:"review_count"`
	CompletedOrders int64                  `json:"completed_orders"`
	Metadata        map[string]interface{} `json:"metadata"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// ServiceCategory represents the main category of a service
type ServiceCategory string

const (
	ServiceCategoryConsulting   ServiceCategory = "CONSULTING"
	ServiceCategoryDesign       ServiceCategory = "DESIGN"
	ServiceCategoryDevelopment  ServiceCategory = "DEVELOPMENT"
	ServiceCategoryMarketing    ServiceCategory = "MARKETING"
	ServiceCategoryWriting      ServiceCategory = "WRITING"
	ServiceCategoryEducation    ServiceCategory = "EDUCATION"
	ServiceCategoryHealthcare   ServiceCategory = "HEALTHCARE"
	ServiceCategoryLifestyle    ServiceCategory = "LIFESTYLE"
	ServiceCategoryBusiness     ServiceCategory = "BUSINESS"
	ServiceCategoryTechnology   ServiceCategory = "TECHNOLOGY"
	ServiceCategoryCreative     ServiceCategory = "CREATIVE"
	ServiceCategoryPersonalCare ServiceCategory = "PERSONAL_CARE"
	ServiceCategoryOther        ServiceCategory = "OTHER"
)

// ServiceStatus represents the current status of a service offering
type ServiceStatus string

const (
	ServiceStatusDraft     ServiceStatus = "DRAFT"
	ServiceStatusActive    ServiceStatus = "ACTIVE"
	ServiceStatusPaused    ServiceStatus = "PAUSED"
	ServiceStatusSuspended ServiceStatus = "SUSPENDED"
	ServiceStatusArchived  ServiceStatus = "ARCHIVED"
)

// PriceType represents how the service is priced
type PriceType string

const (
	PriceTypeFixed  PriceType = "FIXED"
	PriceTypeHourly PriceType = "HOURLY"
	PriceTypeDaily  PriceType = "DAILY"
	PriceTypeCustom PriceType = "CUSTOM"
)

// ServiceLocation represents the location requirements for a service
type ServiceLocation struct {
	Type      LocationType `json:"type"`
	Address   string       `json:"address,omitempty"`
	City      string       `json:"city,omitempty"`
	Country   string       `json:"country,omitempty"`
	Latitude  float64      `json:"latitude,omitempty"`
	Longitude float64      `json:"longitude,omitempty"`
	Radius    int32        `json:"radius,omitempty"` // in kilometers
}

// LocationType represents the type of location requirement
type LocationType string

const (
	LocationTypeOnline   LocationType = "ONLINE"
	LocationTypeInPerson LocationType = "IN_PERSON"
	LocationTypeFlexible LocationType = "FLEXIBLE"
)

// IsActive returns true if the service is currently active and bookable
func (s *ServiceOffering) IsActive() bool {
	return s.Status == ServiceStatusActive
}

// GetFormattedPrice returns the price formatted for display
func (s *ServiceOffering) GetFormattedPrice() string {
	price := float64(s.BasePrice) / 100.0
	switch s.PriceType {
	case PriceTypeFixed:
		return fmt.Sprintf("%.2f %s", price, s.Currency)
	case PriceTypeHourly:
		return fmt.Sprintf("%.2f %s/hour", price, s.Currency)
	case PriceTypeDaily:
		return fmt.Sprintf("%.2f %s/day", price, s.Currency)
	default:
		return fmt.Sprintf("%.2f %s", price, s.Currency)
	}
}

// GetRatingDisplay returns the rating formatted for display
func (s *ServiceOffering) GetRatingDisplay() string {
	if s.ReviewCount == 0 {
		return "No reviews yet"
	}
	return fmt.Sprintf("%.1f (%d reviews)", s.Rating, s.ReviewCount)
}

// Order represents a booking/order for a service
type Order struct {
	ID                 uuid.UUID              `json:"id"`
	ServiceID          uuid.UUID              `json:"service_id"`
	CustomerID         uuid.UUID              `json:"customer_id"`
	ProviderID         uuid.UUID              `json:"provider_id"`
	Status             OrderStatus            `json:"status"`
	PaymentStatus      PaymentStatus          `json:"payment_status"`
	TotalAmount        int64                  `json:"total_amount"` // in cents
	Currency           string                 `json:"currency"`
	ScheduledAt        *time.Time             `json:"scheduled_at,omitempty"`
	StartedAt          *time.Time             `json:"started_at,omitempty"`
	CompletedAt        *time.Time             `json:"completed_at,omitempty"`
	CancelledAt        *time.Time             `json:"cancelled_at,omitempty"`
	CancellationReason string                 `json:"cancellation_reason,omitempty"`
	Notes              string                 `json:"notes"`
	Requirements       map[string]interface{} `json:"requirements"`
	Deliverables       []OrderDeliverable     `json:"deliverables"`
	Messages           []OrderMessage         `json:"messages"`
	Metadata           map[string]interface{} `json:"metadata"`
	CreatedAt          time.Time              `json:"created_at"`
	UpdatedAt          time.Time              `json:"updated_at"`
}

// OrderStatus represents the current status of an order
type OrderStatus string

const (
	OrderStatusPending    OrderStatus = "PENDING"
	OrderStatusConfirmed  OrderStatus = "CONFIRMED"
	OrderStatusInProgress OrderStatus = "IN_PROGRESS"
	OrderStatusCompleted  OrderStatus = "COMPLETED"
	OrderStatusCancelled  OrderStatus = "CANCELLED"
	OrderStatusRefunded   OrderStatus = "REFUNDED"
	OrderStatusDisputed   OrderStatus = "DISPUTED"
)

// PaymentStatus represents the payment status of an order
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "PENDING"
	PaymentStatusPaid      PaymentStatus = "PAID"
	PaymentStatusFailed    PaymentStatus = "FAILED"
	PaymentStatusRefunded  PaymentStatus = "REFUNDED"
	PaymentStatusCancelled PaymentStatus = "CANCELLED"
)

// OrderDeliverable represents a deliverable item for an order
type OrderDeliverable struct {
	ID          uuid.UUID              `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        DeliverableType        `json:"type"`
	URL         string                 `json:"url,omitempty"`
	FileSize    int64                  `json:"file_size,omitempty"`
	Status      DeliverableStatus      `json:"status"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// DeliverableType represents the type of deliverable
type DeliverableType string

const (
	DeliverableTypeFile     DeliverableType = "FILE"
	DeliverableTypeLink     DeliverableType = "LINK"
	DeliverableTypeText     DeliverableType = "TEXT"
	DeliverableTypeVideo    DeliverableType = "VIDEO"
	DeliverableTypeAudio    DeliverableType = "AUDIO"
	DeliverableTypeImage    DeliverableType = "IMAGE"
	DeliverableTypeDocument DeliverableType = "DOCUMENT"
)

// DeliverableStatus represents the status of a deliverable
type DeliverableStatus string

const (
	DeliverableStatusPending   DeliverableStatus = "PENDING"
	DeliverableStatusDelivered DeliverableStatus = "DELIVERED"
	DeliverableStatusAccepted  DeliverableStatus = "ACCEPTED"
	DeliverableStatusRejected  DeliverableStatus = "REJECTED"
	DeliverableStatusRevision  DeliverableStatus = "REVISION"
)

// OrderMessage represents a message in the order communication thread
type OrderMessage struct {
	ID          uuid.UUID              `json:"id"`
	SenderID    uuid.UUID              `json:"sender_id"`
	Content     string                 `json:"content"`
	Type        OrderMessageType       `json:"type"`
	Attachments []string               `json:"attachments"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
}

// OrderMessageType represents the type of order message
type OrderMessageType string

const (
	OrderMessageTypeText        OrderMessageType = "TEXT"
	OrderMessageTypeFile        OrderMessageType = "FILE"
	OrderMessageTypeImage       OrderMessageType = "IMAGE"
	OrderMessageTypeSystem      OrderMessageType = "SYSTEM"
	OrderMessageTypeDeliverable OrderMessageType = "DELIVERABLE"
)

// IsActive returns true if the order is in an active state
func (o *Order) IsActive() bool {
	return o.Status == OrderStatusConfirmed || o.Status == OrderStatusInProgress
}

// IsCompleted returns true if the order has been completed
func (o *Order) IsCompleted() bool {
	return o.Status == OrderStatusCompleted
}

// IsCancelled returns true if the order has been cancelled
func (o *Order) IsCancelled() bool {
	return o.Status == OrderStatusCancelled
}

// CanBeCancelled returns true if the order can still be cancelled
func (o *Order) CanBeCancelled() bool {
	return o.Status == OrderStatusPending || o.Status == OrderStatusConfirmed
}

// GetFormattedAmount returns the total amount formatted for display
func (o *Order) GetFormattedAmount() string {
	amount := float64(o.TotalAmount) / 100.0
	return fmt.Sprintf("%.2f %s", amount, o.Currency)
}

// Review represents a review and rating for a service or provider
type Review struct {
	ID           uuid.UUID              `json:"id"`
	OrderID      uuid.UUID              `json:"order_id"`
	ServiceID    uuid.UUID              `json:"service_id"`
	CustomerID   uuid.UUID              `json:"customer_id"`
	ProviderID   uuid.UUID              `json:"provider_id"`
	Rating       int32                  `json:"rating"` // 1-5 stars
	Title        string                 `json:"title"`
	Content      string                 `json:"content"`
	Pros         []string               `json:"pros"`
	Cons         []string               `json:"cons"`
	Images       []string               `json:"images"`
	Status       ReviewStatus           `json:"status"`
	Response     *ReviewResponse        `json:"response,omitempty"`
	HelpfulCount int32                  `json:"helpful_count"`
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// ReviewStatus represents the status of a review
type ReviewStatus string

const (
	ReviewStatusDraft     ReviewStatus = "DRAFT"
	ReviewStatusPublished ReviewStatus = "PUBLISHED"
	ReviewStatusHidden    ReviewStatus = "HIDDEN"
	ReviewStatusFlagged   ReviewStatus = "FLAGGED"
	ReviewStatusDeleted   ReviewStatus = "DELETED"
)

// ReviewResponse represents a response to a review from the provider
type ReviewResponse struct {
	Content   string    `json:"content"`
	CreatedAt time.Time `json:"created_at"`
}

// IsVisible returns true if the review is visible to the public
func (r *Review) IsVisible() bool {
	return r.Status == ReviewStatusPublished
}

// GetStarDisplay returns a string representation of the star rating
func (r *Review) GetStarDisplay() string {
	stars := ""
	for i := int32(1); i <= 5; i++ {
		if i <= r.Rating {
			stars += "★"
		} else {
			stars += "☆"
		}
	}
	return stars
}
