#!/usr/bin/env bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB 数据库迁移脚本
# 为指定服务运行数据库迁移

set -euo pipefail

# 导入共享函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# shellcheck source=../lib/helpers.sh
source "$SCRIPT_DIR/../lib/helpers.sh"

# 脚本配置
readonly SCRIPT_NAME="db-migrator"
readonly SERVICES_DIR="services"
readonly MIGRATIONS_SUBDIR="migrations"

# 支持的迁移操作
readonly SUPPORTED_ACTIONS=(
    "up"
    "down"
    "force"
    "version"
    "status"
    "create"
)

# 默认配置
DEFAULT_ACTION="up"
DEFAULT_ENVIRONMENT="dev"

# 显示使用说明
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] SERVICE_NAME [ACTION]

Run database migrations for the specified service.

Arguments:
    SERVICE_NAME            Name of the service (e.g., user-core-service)
    ACTION                  Migration action to perform
                           Supported: ${SUPPORTED_ACTIONS[*]}
                           Default: $DEFAULT_ACTION

Options:
    -e, --env ENV          Environment (dev, staging, prod)
                          Default: $DEFAULT_ENVIRONMENT
    -s, --steps N          Number of migration steps (for up/down)
    -v, --version N        Target version (for force)
    -n, --name NAME        Migration name (for create)
    -d, --database URL     Override database URL
    -t, --timeout SECONDS  Migration timeout (default: 300)
    --dry-run              Show what would be done without executing
    -h, --help             Show this help message

Examples:
    $0 user-core-service up                    # Run all pending migrations
    $0 user-core-service down --steps 1        # Rollback one migration
    $0 billing-service up --env staging        # Run migrations for staging
    $0 user-core-service create --name add_users_table  # Create new migration
    $0 user-core-service status                # Show migration status
    $0 user-core-service force --version 5     # Force to specific version

Environment Variables:
    DEBUG=1                Enable debug output
    DRY_RUN=1             Show commands without executing
    DB_TIMEOUT            Default migration timeout
EOF
}

# 解析命令行参数
parse_args() {
    local service_name=""
    local action="$DEFAULT_ACTION"
    local environment="$DEFAULT_ENVIRONMENT"
    local steps=""
    local version=""
    local migration_name=""
    local database_url=""
    local timeout="${DB_TIMEOUT:-300}"
    local dry_run=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                environment="$2"
                shift 2
                ;;
            -s|--steps)
                steps="$2"
                shift 2
                ;;
            -v|--version)
                version="$2"
                shift 2
                ;;
            -n|--name)
                migration_name="$2"
                shift 2
                ;;
            -d|--database)
                database_url="$2"
                shift 2
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$service_name" ]]; then
                    service_name="$1"
                elif [[ -z "$action" ]] || [[ "$action" == "$DEFAULT_ACTION" ]]; then
                    action="$1"
                else
                    error "Unexpected argument: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 验证必需参数
    if [[ -z "$service_name" ]]; then
        error "Service name is required"
        show_usage
        exit 1
    fi
    
    # 设置全局变量
    SERVICE_NAME="$service_name"
    ACTION="$action"
    ENVIRONMENT="$environment"
    MIGRATION_STEPS="$steps"
    MIGRATION_VERSION="$version"
    MIGRATION_NAME="$migration_name"
    DATABASE_URL="$database_url"
    MIGRATION_TIMEOUT="$timeout"
    
    if [[ "$dry_run" == "true" ]]; then
        export DRY_RUN=1
    fi
}

# 验证参数
validate_args() {
    # 验证 action
    if [[ ! " ${SUPPORTED_ACTIONS[*]} " =~ " $ACTION " ]]; then
        error "Unsupported action: $ACTION"
        error "Supported actions: ${SUPPORTED_ACTIONS[*]}"
        exit 1
    fi
    
    # 验证特定 action 的参数
    case "$ACTION" in
        "create")
            if [[ -z "$MIGRATION_NAME" ]]; then
                error "Migration name is required for 'create' action"
                error "Use --name option to specify migration name"
                exit 1
            fi
            ;;
        "force")
            if [[ -z "$MIGRATION_VERSION" ]]; then
                error "Version is required for 'force' action"
                error "Use --version option to specify target version"
                exit 1
            fi
            ;;
    esac
    
    debug "Validated arguments:"
    debug "  Service: $SERVICE_NAME"
    debug "  Action: $ACTION"
    debug "  Environment: $ENVIRONMENT"
    debug "  Steps: ${MIGRATION_STEPS:-none}"
    debug "  Version: ${MIGRATION_VERSION:-none}"
    debug "  Migration name: ${MIGRATION_NAME:-none}"
}

# 检查必需的工具
check_tools() {
    step "Checking required tools"
    
    if ! check_command "migrate" "Install golang-migrate: https://github.com/golang-migrate/migrate/tree/master/cmd/migrate"; then
        error "Migration tool not found"
        exit 1
    fi
    
    # 检查 migrate 版本
    local migrate_version
    migrate_version=$(migrate -version 2>/dev/null | awk '{print $NF}' || echo "unknown")
    info "Using golang-migrate version: $migrate_version"
    
    success "All required tools are available"
}

# 检查服务和迁移目录
check_service_structure() {
    step "Checking service structure"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 检查服务目录
    local service_dir="$project_root/$SERVICES_DIR/$SERVICE_NAME"
    if ! check_dir "$service_dir"; then
        error "Service directory not found: $service_dir"
        exit 1
    fi
    
    # 检查迁移目录
    local migrations_dir="$service_dir/$MIGRATIONS_SUBDIR"
    
    if [[ "$ACTION" == "create" ]]; then
        # 对于 create 操作，确保迁移目录存在
        ensure_dir "$migrations_dir"
    else
        # 对于其他操作，迁移目录必须存在且包含迁移文件
        if ! check_dir "$migrations_dir"; then
            error "Migrations directory not found: $migrations_dir"
            exit 1
        fi
        
        local migration_files
        migration_files=$(find "$migrations_dir" -name "*.sql" | wc -l)
        if [[ $migration_files -eq 0 ]]; then
            warn "No migration files found in: $migrations_dir"
        else
            debug "Found $migration_files migration files"
        fi
    fi
    
    success "Service structure is valid"
    
    # 设置全局路径变量
    SERVICE_DIR="$service_dir"
    MIGRATIONS_DIR="$migrations_dir"
}

# 加载数据库配置
load_database_config() {
    step "Loading database configuration"
    
    # 如果指定了数据库 URL，直接使用
    if [[ -n "$DATABASE_URL" ]]; then
        info "Using provided database URL"
        return 0
    fi
    
    # 查找配置文件
    local config_files=(
        "$SERVICE_DIR/configs/config.$ENVIRONMENT.yaml"
        "$SERVICE_DIR/configs/config.yaml"
        "$SERVICE_DIR/config/config.$ENVIRONMENT.yaml"
        "$SERVICE_DIR/config/config.yaml"
    )
    
    local config_file=""
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            config_file="$file"
            break
        fi
    done
    
    if [[ -z "$config_file" ]]; then
        error "No configuration file found for service: $SERVICE_NAME"
        error "Tried: ${config_files[*]}"
        error "Please provide database URL with --database option"
        exit 1
    fi
    
    debug "Using config file: $config_file"
    
    # 尝试从配置文件提取数据库 URL
    # 这里简化处理，实际项目中可能需要更复杂的解析
    local db_url
    if command -v yq &> /dev/null; then
        # 使用 yq 解析 YAML
        db_url=$(yq eval '.database.url // .database.dsn // ""' "$config_file" 2>/dev/null || echo "")
    elif command -v python3 &> /dev/null; then
        # 使用 Python 解析 YAML
        db_url=$(python3 -c "
import yaml, sys
try:
    with open('$config_file', 'r') as f:
        config = yaml.safe_load(f)
    db = config.get('database', {})
    print(db.get('url', db.get('dsn', '')))
except:
    print('')
" 2>/dev/null || echo "")
    else
        # 简单的文本搜索（不够健壮，但在没有其他工具时可用）
        db_url=$(grep -E '^\s*(url|dsn):' "$config_file" | head -1 | sed 's/.*:\s*["\x27]*//' | sed 's/["\x27]*\s*$//' || echo "")
    fi
    
    if [[ -z "$db_url" ]]; then
        error "Could not extract database URL from config file: $config_file"
        error "Please ensure the config file has 'database.url' or 'database.dsn' field"
        error "Or provide database URL with --database option"
        exit 1
    fi
    
    DATABASE_URL="$db_url"
    info "Loaded database configuration from: $config_file"
    debug "Database URL pattern: ${db_url%%\?*}..."  # 隐藏查询参数以保护敏感信息
}

# 执行迁移操作
execute_migration() {
    step "Executing migration: $ACTION"
    
    local cmd="migrate"
    cmd="$cmd -path $MIGRATIONS_DIR"
    cmd="$cmd -database '$DATABASE_URL'"
    
    # 添加操作特定的参数
    case "$ACTION" in
        "up")
            if [[ -n "$MIGRATION_STEPS" ]]; then
                cmd="$cmd up $MIGRATION_STEPS"
            else
                cmd="$cmd up"
            fi
            ;;
        "down")
            if [[ -n "$MIGRATION_STEPS" ]]; then
                cmd="$cmd down $MIGRATION_STEPS"
            else
                cmd="$cmd down 1"  # 默认回滚一个迁移
            fi
            ;;
        "force")
            cmd="$cmd force $MIGRATION_VERSION"
            ;;
        "version")
            cmd="$cmd version"
            ;;
        "status")
            # migrate 没有 status 命令，我们自定义实现
            show_migration_status
            return 0
            ;;
        "create")
            cmd="migrate create -ext sql -dir $MIGRATIONS_DIR -seq $MIGRATION_NAME"
            ;;
    esac
    
    # 设置超时
    if command -v timeout &> /dev/null; then
        cmd="timeout $MIGRATION_TIMEOUT $cmd"
    fi
    
    info "Running migration command..."
    debug "Command: $cmd"
    
    if [[ "${DRY_RUN:-0}" == "1" ]]; then
        info "[DRY RUN] Would execute: $cmd"
        return 0
    fi
    
    # 执行命令
    if eval "$cmd"; then
        success "Migration completed successfully"
    else
        local exit_code=$?
        error "Migration failed with exit code: $exit_code"
        return $exit_code
    fi
}

# 显示迁移状态
show_migration_status() {
    info "Migration status for $SERVICE_NAME:"
    
    # 显示当前版本
    local current_version
    current_version=$(migrate -path "$MIGRATIONS_DIR" -database "$DATABASE_URL" version 2>/dev/null | awk '{print $NF}' || echo "unknown")
    info "Current version: $current_version"
    
    # 列出所有迁移文件
    local migration_files
    migration_files=$(find "$MIGRATIONS_DIR" -name "*.up.sql" | sort)
    
    if [[ -n "$migration_files" ]]; then
        info "Available migrations:"
        echo "$migration_files" | while read -r file; do
            local basename
            basename=$(basename "$file" .up.sql)
            local version="${basename%%_*}"
            local name="${basename#*_}"
            
            if [[ "$version" -le "$current_version" ]] 2>/dev/null; then
                info "  ✓ $version: $name (applied)"
            else
                info "  ○ $version: $name (pending)"
            fi
        done
    else
        warn "No migration files found"
    fi
}

# 验证迁移结果
validate_migration() {
    if [[ "$ACTION" == "create" ]]; then
        # 验证创建的迁移文件
        local created_files
        created_files=$(find "$MIGRATIONS_DIR" -name "*$MIGRATION_NAME*" -type f | wc -l)
        if [[ $created_files -gt 0 ]]; then
            success "Migration files created:"
            find "$MIGRATIONS_DIR" -name "*$MIGRATION_NAME*" -type f | while read -r file; do
                info "  $(basename "$file")"
            done
        else
            error "Migration files were not created"
            return 1
        fi
    else
        # 验证迁移状态
        info "Verifying migration status..."
        local new_version
        new_version=$(migrate -path "$MIGRATIONS_DIR" -database "$DATABASE_URL" version 2>/dev/null | awk '{print $NF}' || echo "unknown")
        info "Current database version: $new_version"
    fi
}

# 主函数
main() {
    # 初始化脚本
    init_script "$SCRIPT_NAME"
    
    # 解析命令行参数
    parse_args "$@"
    
    # 验证参数
    validate_args
    
    # 检查工具
    check_tools
    
    # 检查服务结构
    check_service_structure
    
    # 加载数据库配置
    load_database_config
    
    # 执行迁移
    execute_migration
    
    # 验证结果
    validate_migration
    
    # 完成
    finish_script "$SCRIPT_NAME"
}

# 只有在直接执行时才运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 