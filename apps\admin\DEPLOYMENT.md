# Deployment Guide: Admin Dashboard

This document provides instructions for building and deploying the Admin Dashboard application using Docker.

## Prerequisites

- Docker installed and running on your local machine.
- Node.js and pnpm installed for local development and testing.
- Access to the project's repository.

## Build and Run Locally with Docker

These steps allow you to build a production-ready Docker image and run it on your local machine to simulate a production environment.

1.  **Navigate to the admin app directory:**
    ```sh
    cd apps/admin
    ```

2.  **Build the Docker image:**
    This command uses the `Dockerfile` in the current directory to build an image and tags it as `cina-club-admin`.

    ```sh
    pnpm docker:build
    ```
    *or*
    ```sh
    docker build -t cina-club-admin .
    ```

3.  **Run the Docker container:**
    This command starts a container from the image you just built. It maps port `3001` on your host machine to port `80` inside the container.

    ```sh
    pnpm docker:run
    ```
    *or*
    ```sh
    docker run -d -p 3001:80 --name cina-admin-container cina-club-admin
    ```
    You can now access the application at [http://localhost:3001](http://localhost:3001).

## Production Deployment (CI/CD)

The deployment process should be automated through a CI/CD pipeline (e.g., GitHub Actions, Jenkins, GitLab CI).

### Pipeline Steps:

1.  **Checkout Code:** Pull the latest code from the `main` or `release` branch.

2.  **Run Tests:** Execute all tests to ensure code quality and prevent regressions.
    ```sh
    pnpm install
    pnpm test:coverage
    pnpm test:e2e
    ```

3.  **Build Docker Image:** Build the image, tagging it with the Git commit hash or a version number.
    ```sh
    docker build -t your-registry/cina-club-admin:$CI_COMMIT_SHA .
    ```

4.  **Push to Container Registry:** Push the built image to a container registry (e.g., Docker Hub, AWS ECR, Google GCR).
    ```sh
    docker push your-registry/cina-club-admin:$CI_COMMIT_SHA
    ```

5.  **Deploy to Environment:** Update the running service in your production environment (e.g., Kubernetes, AWS ECS, a VM) to use the new Docker image. This step is highly dependent on your infrastructure. For Kubernetes, it might involve a command like:
    ```sh
    kubectl set image deployment/admin-dashboard-deployment admin-dashboard=your-registry/cina-club-admin:$CI_COMMIT_SHA
    ```

## Environment Variables

The Vite application can be configured with environment variables. The Sentry DSN, for example, should be set during the build step in the CI/CD pipeline.

Example:
```sh
VITE_SENTRY_DSN="your-production-dsn" pnpm build
```

This ensures that the correct, production-ready variables are baked into the static files. 