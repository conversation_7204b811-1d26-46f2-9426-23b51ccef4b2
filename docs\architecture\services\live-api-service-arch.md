好的，遵照您的指示。我将为您生成一份专门针对 **`live-api-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`live-api-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**直播状态机的实现、作为Saga协调者与多个下游服务（媒体网关、计费、通知）的交互、以及如何处理来自媒体服务器的实时回调**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `live-api-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `live-api-service-srs.md` (v1.0)
**核心架构**: 整洁架构 + 状态机模式(State Machine) + Saga编排模式

## 1. 概述

`live-api-service` 是CINA.CLUB直播生态的**业务逻辑核心**和**状态管理器**。它是一个**有状态**（相对于直播间生命周期而言）的服务，负责编排整个直播流程。其核心架构挑战在于：
1.  **健壮的状态机**: 必须精确、可靠地管理直播间从`IDLE`到`ARCHIVED`的复杂状态转换，并处理所有边缘情况（如推流中断、恢复）。
2.  **分布式事务协调 (Saga)**: 开播、加入付费房间等流程涉及对多个服务的调用，必须保证这些操作的最终一致性。
3.  **实时回调处理**: 需要一个可靠、幂等的机制来处理来自底层媒体服务器（通过`live-gateway-service`）的实时状态回调。
4.  **清晰的职责边界**: 必须将自身的业务逻辑与`live-gateway-service`（媒体流控制）、`live-im-service`（互动消息）的职责清晰划分。

本架构设计通过采用**整洁架构**，并在领域层实现一个**严格的`LiveRoom`状态机聚合根**，同时在应用层作为**Saga协调者**来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (状态机与Saga协调)

```mermaid
graph TD
    subgraph "外部调用方"
        ClientApp[客户端App]
        LGS[live-gateway-service<br/>(via gRPC/Event)]
    end

    subgraph "LiveAPIService"
        style LiveAPIService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[Application Service<br/><em>application/service</em>]
        C[LiveRoomAggregate<br/><em>domain/aggregate</em>]
        D[PermissionService<br/><em>domain/service</em>]
        E[Repository<br/><em>adapter/repository</em>]
        F[Downstream Clients<br/><em>adapter/client</em>]
        G[Kafka Producer<br/><em>adapter/event</em>]
    end

    subgraph "下游参与者服务"
        style "下游参与者服务" fill:#f3e5f5
        S1[live-gateway-service]
        S2[billing-service]
        S3[notification-dispatch-service]
    end

    ClientApp -- "1. StartLive" --> A
    A -- "调用" --> B
    
    B -- "2. 加载聚合根" --> E
    E -- "返回LiveRoom对象" --> B
    
    B -- "3. 调用领域方法" --> C
    C -- "4. 内部执行状态转换<br/>(IDLE -> PREPARING)<br/>并检查权限" --> D & C
    
    B -- "5. ✨ 启动开播Saga ✨" --> B
    subgraph "StartLive Saga"
        B -- "Step1: RequestPushURL" --> F
        F -- "gRPC Call" --> S1
        S1 -- "PushURL" --> F
        
        B -- "Step2: Update Room Status to LIVE" --> E
        
        B -- "Step3: Publish LiveStartedEvent" --> G
    end

    LGS -- "6. [回调] StreamInterrupted" --> A
    A -- "调用" --> B
    B -- "调用领域方法" --> C
    C -- "更新状态为INTERRUPTED" --> C
    B -- "持久化" --> E
```

### 2.2 最终目录结构 (`services/live-api-service/`)

```
live-api-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # (可选) 用于录制后处理等后台任务
├── internal/
│   ├── adapter/
│   │   ├── client/             # 所有下游服务的gRPC客户端
│   │   ├── event/
│   │   │   └── producer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── postgres_repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── live_service.go # 核心应用服务实现
│   └── domain/
│       ├── aggregate/
│       │   └── live_room_aggregate.go # ✨ 直播间状态机聚合根 ✨
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── permission_service.go # ✨ 直播间访问权限检查服务 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Live Business Rules)

*   `domain/model/`: 使用`/core/models`中与直播相关的`struct`。
*   **`domain/service/permission_service.go`**:
    *   **`PermissionService`**: 一个无状态的领域服务。
    *   **`CanStartLive(actor)`**, **`CanJoinLive(actor, room)`**等方法。
    *   **逻辑**: `CanJoinLive`会检查`room.AccessControlConfig`，如果房间是付费的，它会返回一个需要进一步检查计费的信号；如果是密码房，它会校验密码。
*   **`domain/aggregate/live_room_aggregate.go`**: **这是管理直播生命周期的核心**。
    *   **`LiveRoom`聚合根**: 封装了`LiveRoom`实体和其当前会话`LiveSession`。
    *   **状态机方法**: **所有状态变更都必须通过聚合根的方法进行**，以保证业务不变量。
        *   `PrepareToStart(actor)`:
            1.  调用`permissionService.CanStartLive(actor)`。
            2.  检查当前状态是否为`IDLE`。
            3.  如果通过，将状态更新为`PREPARING`，并记录`LivePreparingEvent`。
        *   `GoLive(pushURL)`:
            1.  检查状态是否为`PREPARING`。
            2.  更新状态为`LIVE`，记录`startTime`，并记录`LiveStartedEvent`。
        *   `Interrupt()`:
            1.  检查状态是否为`LIVE`。
            2.  更新状态为`INTERRUPTED`。
        *   `Resume()`:
            1.  检查状态是否为`INTERRUPTED`。
            2.  更新状态为`LIVE`。
        *   `End()`:
            1.  检查状态是否为`LIVE`或`INTERRUPTED`。
            2.  更新状态为`ENDED`，记录`endTime`，并记录`LiveEndedEvent`。

### 3.2 `application/` - 应用层 (The Saga Coordinator & Use Cases)

*   **`application/service/live_service.go`**: 实现`LiveService`接口，是所有业务流程的编排者。
    *   **`StartLive(ctx, actorID, roomID)`**: **这是一个Saga事务**。
        1.  **加载聚合根**: `room, err := repo.GetLiveRoom(ctx, roomID)`。
        2.  **调用领域方法**: `err := room.PrepareToStart(actor)`。
        3.  **持久化初始状态**: `repo.UpdateLiveRoom(ctx, room)`。
        4.  **执行Saga步骤**:
            a. **[Execute]** 调用`liveGatewayClient.RequestPushURL(roomID)`。
            b. **如果成功**:
                i.  加载`room`聚合根。
                ii. 调用`room.GoLive(pushURL)`。
                iii. 持久化`LIVE`状态。
                iv. **发布`LiveStartedEvent`到Kafka**。
                v.  返回推流地址给handler。
            c. **如果失败**:
                i.  **[Compensate]** 加载`room`聚合根，调用其`FailToStart()`方法将状态回滚到`IDLE`，并持久化。
    *   **`JoinLiveRoom(ctx, userID, roomID, password)`**:
        1.  加载`room`聚合根。
        2.  调用`permissionService.CanJoinLive(user, room, password)`。
        3.  **如果需要付费**:
            *   调用`billingClient.CheckAccess(...)`。
            *   如果无权，返回一个包含支付信息的错误，由客户端引导支付。
        4.  **权限检查通过**:
            *   调用`liveGatewayClient.RequestPlayURL(roomID)`获取拉流地址。
            *   调用`liveIMClient.GetIMServerAddress(roomID)`获取IM服务器地址。
            *   返回聚合后的信息。
    *   **`HandleStreamInterrupted(ctx, roomID)` (由gRPC/回调调用)**:
        1.  加载`room`聚合根。
        2.  调用`room.Interrupt()`。
        3.  持久化。
        4.  **启动一个Asynq延迟任务**，在5分钟后检查房间状态，如果仍是`INTERRUPTED`，则调用`EndLive`。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**: 使用**PostgreSQL**存储直播间元数据和场次数据。
*   **`adapter/client/`**: 封装对所有下游服务（`live-gateway`, `billing`, `notification`等）的gRPC客户端调用。
*   **`adapter/grpc/handler.go`**:
    *   实现`live-api-service.proto`中定义的gRPC服务。
    *   它也需要实现一个**内部的gRPC服务**，用于接收来自`live-gateway-service`的状态回调（如`NotifyStreamInterrupted`）。此内部服务端口不应对外暴露。
*   **`adapter/event/producer.go`**: 封装`pkg/messaging`，用于在关键状态变更时（开播、下播）发布领域事件。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`live-api-service`：
1.  **状态机聚合根**: 将复杂、易错的直播生命周期管理，严格封装在`LiveRoom`聚合根中。所有状态变更都必须通过其方法进行，保证了业务规则的强制执行和数据一致性。
2.  **Saga模式协调**: 对于开播等涉及多个分布式服务的流程，明确使用**编排式Saga**进行协调，并通过补偿操作来保证系统的最终一致性，提升了系统的可靠性。
3.  **清晰的职责划分**:
    *   **本服务 (live-api)**: 负责**“业务是什么”**（直播的状态、权限、元数据）。
    *   **`live-gateway-service`**: 负责**“媒体如何流”**（推拉流地址、鉴权）。
    *   **`live-im-service`**: 负责**“人如何互动”**（弹幕、礼物）。
    *   这种分离使得每个服务都可以独立演进和扩展。
4.  **可靠的异步处理**: 通过处理来自媒体网关的回调，并使用**延迟任务队列(Asynq)**来处理超时逻辑（如推流中断后自动下播），构建了一个健壮的、能自动响应物理世界变化的系统。

这种架构确保了`live-api-service`能够作为一个**稳定、可靠、逻辑严谨**的业务中枢， orchestrating 整个CINA.CLUB直播生态的复杂流程。