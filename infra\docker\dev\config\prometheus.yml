# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB Prometheus 监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Kafka 监控
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9101']

  # Redis 监控（需要 redis_exporter）
  # - job_name: 'redis'
  #   static_configs:
  #     - targets: ['redis_exporter:9121']

  # PostgreSQL 监控（需要 postgres_exporter）
  # - job_name: 'postgres'
  #   static_configs:
  #     - targets: ['postgres_exporter:9187']

  # 微服务监控（通过 /metrics 端点）
  # - job_name: 'user-core-service'
  #   static_configs:
  #     - targets: ['user-core-service:8080']
  #   metrics_path: '/metrics'

  # - job_name: 'billing-service'
  #   static_configs:
  #     - targets: ['billing-service:8080']
  #   metrics_path: '/metrics'

  # Node Exporter（如果添加了系统监控）
  # - job_name: 'node'
  #   static_configs:
  #     - targets: ['node_exporter:9100']

# 告警配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093 