#!/bin/bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-12-28 10:00:00
# Modified: 2025-12-28 10:00:00

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
MIN_COVERAGE=90
COVERAGE_DIR="coverage"
TEST_TIMEOUT="10m"

echo -e "${BLUE}=== CINA.CLUB PKG Test Coverage Analysis ===${NC}"
echo "Minimum required coverage: ${MIN_COVERAGE}%"
echo "Test timeout: ${TEST_TIMEOUT}"
echo

# 创建覆盖率目录
mkdir -p "${COVERAGE_DIR}"

# 获取所有pkg子包
PKG_DIRS=$(find pkg -name "*.go" -not -name "*_test.go" -exec dirname {} \; | sort -u)

# 统计变量
TOTAL_PACKAGES=0
PASSED_PACKAGES=0
FAILED_PACKAGES=0

# 存储结果
declare -a RESULTS

echo -e "${BLUE}Running tests with coverage for all packages...${NC}"
echo

# 遍历每个包
for pkg_dir in $PKG_DIRS; do
    # 跳过examples目录
    if [[ "$pkg_dir" == *"examples"* ]]; then
        continue
    fi
    
    TOTAL_PACKAGES=$((TOTAL_PACKAGES + 1))
    pkg_name=$(basename "$pkg_dir")
    
    echo -e "${YELLOW}Testing package: ${pkg_dir}${NC}"
    
    # 检查是否有测试文件
    if ! find "$pkg_dir" -name "*_test.go" | grep -q .; then
        echo -e "${RED}  ❌ No test files found${NC}"
        RESULTS+=("$pkg_dir|0.0|NO_TESTS")
        FAILED_PACKAGES=$((FAILED_PACKAGES + 1))
        echo
        continue
    fi
    
    # 运行测试并生成覆盖率报告
    coverage_file="${COVERAGE_DIR}/${pkg_name}.out"
    
    if go test -timeout="$TEST_TIMEOUT" \
        -coverprofile="$coverage_file" \
        -covermode=atomic \
        "./$pkg_dir" > /dev/null 2>&1; then
        
        # 计算覆盖率
        if [[ -f "$coverage_file" ]]; then
            coverage=$(go tool cover -func="$coverage_file" | grep "total:" | awk '{print $3}' | sed 's/%//')
            
            if [[ -z "$coverage" ]]; then
                coverage="0.0"
            fi
            
            # 检查是否达到最低要求
            if (( $(echo "$coverage >= $MIN_COVERAGE" | bc -l) )); then
                echo -e "${GREEN}  ✅ Coverage: ${coverage}%${NC}"
                RESULTS+=("$pkg_dir|$coverage|PASS")
                PASSED_PACKAGES=$((PASSED_PACKAGES + 1))
            else
                echo -e "${RED}  ❌ Coverage: ${coverage}% (below ${MIN_COVERAGE}%)${NC}"
                RESULTS+=("$pkg_dir|$coverage|FAIL")
                FAILED_PACKAGES=$((FAILED_PACKAGES + 1))
            fi
        else
            echo -e "${RED}  ❌ Failed to generate coverage report${NC}"
            RESULTS+=("$pkg_dir|0.0|NO_COVERAGE")
            FAILED_PACKAGES=$((FAILED_PACKAGES + 1))
        fi
    else
        echo -e "${RED}  ❌ Tests failed${NC}"
        RESULTS+=("$pkg_dir|0.0|TEST_FAILED")
        FAILED_PACKAGES=$((FAILED_PACKAGES + 1))
    fi
    
    echo
done

# 生成合并的覆盖率报告
echo -e "${BLUE}Generating combined coverage report...${NC}"

# 合并所有覆盖率文件
echo "mode: atomic" > "${COVERAGE_DIR}/combined.out"
for coverage_file in "${COVERAGE_DIR}"/*.out; do
    if [[ "$coverage_file" != *"combined.out" ]]; then
        tail -n +2 "$coverage_file" >> "${COVERAGE_DIR}/combined.out" 2>/dev/null || true
    fi
done

# 计算总体覆盖率
if [[ -f "${COVERAGE_DIR}/combined.out" ]]; then
    total_coverage=$(go tool cover -func="${COVERAGE_DIR}/combined.out" | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    if [[ -z "$total_coverage" ]]; then
        total_coverage="0.0"
    fi
else
    total_coverage="0.0"
fi

# 生成HTML报告
if [[ -f "${COVERAGE_DIR}/combined.out" ]]; then
    go tool cover -html="${COVERAGE_DIR}/combined.out" -o="${COVERAGE_DIR}/coverage.html"
    echo "HTML coverage report generated: ${COVERAGE_DIR}/coverage.html"
fi

echo
echo -e "${BLUE}=== Coverage Report Summary ===${NC}"
echo
printf "%-30s %-10s %-10s\n" "Package" "Coverage" "Status"
echo "--------------------------------------------------------"

for result in "${RESULTS[@]}"; do
    IFS='|' read -r pkg coverage status <<< "$result"
    pkg_name=$(basename "$pkg")
    
    case $status in
        "PASS")
            printf "%-30s ${GREEN}%-10s ✅${NC}\n" "$pkg_name" "${coverage}%"
            ;;
        "FAIL")
            printf "%-30s ${RED}%-10s ❌${NC}\n" "$pkg_name" "${coverage}%"
            ;;
        "NO_TESTS")
            printf "%-30s ${YELLOW}%-10s ⚠️${NC}\n" "$pkg_name" "NO TESTS"
            ;;
        "NO_COVERAGE")
            printf "%-30s ${RED}%-10s ❌${NC}\n" "$pkg_name" "NO COV"
            ;;
        "TEST_FAILED")
            printf "%-30s ${RED}%-10s ❌${NC}\n" "$pkg_name" "FAILED"
            ;;
    esac
done

echo "--------------------------------------------------------"
printf "%-30s ${BLUE}%-10s${NC}\n" "TOTAL" "${total_coverage}%"
echo

# 统计信息
echo -e "${BLUE}=== Statistics ===${NC}"
echo "Total packages: $TOTAL_PACKAGES"
echo -e "Passed: ${GREEN}$PASSED_PACKAGES${NC}"
echo -e "Failed: ${RED}$FAILED_PACKAGES${NC}"
echo "Overall coverage: ${total_coverage}%"

# 检查总体覆盖率
if (( $(echo "$total_coverage >= $MIN_COVERAGE" | bc -l) )); then
    echo -e "\n${GREEN}🎉 Overall coverage meets requirement (${total_coverage}% >= ${MIN_COVERAGE}%)${NC}"
    overall_status=0
else
    echo -e "\n${RED}❌ Overall coverage below requirement (${total_coverage}% < ${MIN_COVERAGE}%)${NC}"
    overall_status=1
fi

# 生成详细报告
echo
echo -e "${BLUE}=== Detailed Analysis ===${NC}"

# 找出覆盖率最低的包
if [[ ${#RESULTS[@]} -gt 0 ]]; then
    echo
    echo "Packages needing attention:"
    for result in "${RESULTS[@]}"; do
        IFS='|' read -r pkg coverage status <<< "$result"
        if [[ "$status" == "FAIL" ]] || [[ "$status" == "NO_TESTS" ]] || [[ "$status" == "NO_COVERAGE" ]] || [[ "$status" == "TEST_FAILED" ]]; then
            echo "  - $(basename "$pkg"): $status"
        fi
    done
fi

# 生成改进建议
echo
echo -e "${BLUE}=== Improvement Suggestions ===${NC}"

for result in "${RESULTS[@]}"; do
    IFS='|' read -r pkg coverage status <<< "$result"
    pkg_name=$(basename "$pkg")
    
    case $status in
        "NO_TESTS")
            echo "  📝 $pkg_name: Add test files (*_test.go)"
            ;;
        "FAIL")
            if (( $(echo "$coverage < 50" | bc -l) )); then
                echo "  📝 $pkg_name: Coverage too low (${coverage}%), add more unit tests"
            elif (( $(echo "$coverage < 75" | bc -l) )); then
                echo "  📝 $pkg_name: Add integration tests and edge case tests"
            else
                echo "  📝 $pkg_name: Add a few more test cases to reach ${MIN_COVERAGE}%"
            fi
            ;;
        "TEST_FAILED")
            echo "  🔧 $pkg_name: Fix failing tests before measuring coverage"
            ;;
        "NO_COVERAGE")
            echo "  🔧 $pkg_name: Fix coverage report generation"
            ;;
    esac
done

# 生成性能基准测试报告
echo
echo -e "${BLUE}=== Running Benchmark Tests ===${NC}"

for pkg_dir in $PKG_DIRS; do
    if [[ "$pkg_dir" == *"examples"* ]]; then
        continue
    fi
    
    # 检查是否有基准测试
    if find "$pkg_dir" -name "*_test.go" -exec grep -l "func Benchmark" {} \; | grep -q .; then
        pkg_name=$(basename "$pkg_dir")
        echo "Running benchmarks for $pkg_name..."
        
        # 运行基准测试
        go test -bench=. -benchmem "./$pkg_dir" > "${COVERAGE_DIR}/${pkg_name}_bench.txt" 2>&1 || true
    fi
done

# 清理临时文件（保留主要报告）
find "${COVERAGE_DIR}" -name "*.out" ! -name "combined.out" -delete

echo
if [[ $overall_status -eq 0 && $FAILED_PACKAGES -eq 0 ]]; then
    echo -e "${GREEN}✅ All tests passed with sufficient coverage!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some packages failed coverage requirements.${NC}"
    echo "Run 'go tool cover -html=${COVERAGE_DIR}/combined.out' to view detailed coverage report."
    exit 1
fi 