# CI/CD Pipeline for Admin Dashboard

name: Admin Dashboard CI/CD

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run linter
        run: pnpm lint

      - name: Run unit tests
        run: pnpm test:coverage

      # Note: E2E tests require a running backend. 
      # This step might need to be run against a staging environment.
      # - name: Run E2E tests
      #   run: pnpm test:e2e

      - name: Build the application
        run: pnpm build

      - name: Build Docker image
        run: docker build . --file Dockerfile --tag cina-club-admin:${{ github.sha }}

      # - name: Log in to Docker Hub
      #   uses: docker/login-action@v3
      #   with:
      #     username: ${{ secrets.DOCKERHUB_USERNAME }}
      #     password: ${{ secrets.DOCKERHUB_TOKEN }}

      # - name: Push Docker image to registry
      #   run: |
      #     docker tag cina-club-admin:${{ github.sha }} your-registry/cina-club-admin:${{ github.sha }}
      #     docker push your-registry/cina-club-admin:${{ github.sha }} 