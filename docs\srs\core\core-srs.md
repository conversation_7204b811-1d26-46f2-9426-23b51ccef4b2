好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`core/` 目录** 的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档是整个平台技术体系中**最重要**的文档之一，因为它定义了连接所有后端、前端（iOS, Android, Web）的“通用语言”和“核心能力”。它将作为所有开发团队（后端、移动端、Web端）共同遵守的“宪法”。

---
### CINA.CLUB - 共享核心库 (`core/`) 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计原则与约束](#3-核心设计原则与约束)
4.  [功能需求 (按子包拆分)](#4-功能需求-按子包拆分)
    *   [4.1 `core/api`: API契约中心](#41-coreapi-api契约中心)
    *   [4.2 `core/models`: 共享数据模型](#42-coremodels-共享数据模型)
    *   [4.3 `core/crypto`: 端到端加密(E2EE)核心](#43-corecrypto-端到端加密e2ee核心)
    *   [4.4 `core/datasync`: 加密数据同步协议](#44-coredatasync-加密数据同步协议)
    *   [4.5 `core/aic`: 本地AI核心](#45-coreaic-本地ai核心)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [跨平台编译与集成策略](#6-跨平台编译与集成策略)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的“Go-Centric”全栈架构中，存在大量需要被后端微服务、iOS应用、Android应用和Web应用共同遵守的定义和共享的复杂逻辑。`core/` 包的目的在于提供这样一个**跨平台的、单一事实来源的共享核心库**。它封装了平台的API契约、核心数据模型、端到端加密算法、数据同步协议和本地AI能力，旨在：
*   **保证一致性**: 确保所有端都使用相同的API定义和数据结构。
*   **最大化代码复用**: 将最复杂、最关键的逻辑（如加密）用Go编写一次，多端复用。
*   **提升性能与安全**: 利用Go的性能优势和内存安全特性，在前端执行计算密集型和安全敏感型任务。
*   **简化开发**: 为各端提供统一、稳定的接口，屏蔽底层实现的复杂性。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   定义所有服务间和客户端-服务器间的gRPC/Protobuf **API契约**。
    *   定义平台核心业务对象的Go **数据结构 (`struct`)**。
    *   实现与平台无关的、纯粹的**端到端加密(E2EE)算法**和密钥管理逻辑。
    *   实现与`cloud-sync-service`交互的**加密数据同步协议**。
    *   提供调用本地AI推理引擎（如`llama.cpp`）的**CGO绑定和接口**。
*   **范围之外 (Out-of-Scope)**:
    *   **任何特定于某个微服务或前端应用的业务逻辑**。
    *   任何**后端专用**的共享逻辑（这属于`/pkg`）。
    *   任何**UI组件或前端框架相关的代码**。
    *   任何直接的I/O操作（如网络、数据库），除非是通过CGO与底层库交互。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。
*   **CINA.CLUB所有前端（iOS, Android, Web）应用**的开发者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`core/` 是Monorepo的**基石和枢纽**，处于依赖链的绝对最底层。
*   **依赖关系**:
    *   `services/*` (所有后端服务) -> **`core/`**
    *   `apps/*` (所有前端应用) -> **`core/`** (通过Go Mobile/WASM编译产物)
    *   **`core/`** -> (仅) Go标准库、Protobuf/gRPC库、CGO。
*   **重要性**: 对`core/`的任何修改都具有**全局影响**，可能需要所有后端和前端应用的重新编译、测试和发布。

#### 2.2. 设计原则
*   **平台无关性 (Platform-Agnostic)**: `core/`中的Go代码必须是平台无关的纯逻辑，不能包含任何特定于操作系统或前端框架的API调用（除非在带构建标签的`_mobile.go`或`_wasm.go`文件中）。
*   **稳定性优先 (Stability First)**: `core/`的接口一旦确定，应尽可能保持稳定。所有变更必须遵循严格的向后兼容策略和版本控制。
*   **极简依赖 (Minimal Dependencies)**: 严格控制第三方依赖，以减小编译后库的体积和潜在的安全风险。
*   **可测试性**: 所有逻辑必须设计为易于单元测试，不依赖外部状态。
*   **清晰的导出边界**: 必须通过专门的`exports_*.go`文件和构建标签，清晰地定义哪些函数和类型被导出到Go Mobile和WASM。

---

### 3. 核心设计与集成策略

`core/`通过Go强大的跨平台编译能力，实现“一次编写，多端运行”。
*   **后端**: 后端微服务直接通过Go Modules导入并使用`core/`中的包。
*   **移动端 (iOS/Android)**: 使用`gomobile bind`命令将`core/`编译成一个原生的静态/动态库（`.framework`/`.aar`），然后通过原生桥接（JSI/TurboModules/MethodChannel）暴露给React Native/Flutter等前端框架。
*   **Web端**: 使用`GOOS=js GOARCH=wasm go build`将`core/`编译成一个`.wasm`文件，在浏览器中通过JavaScript的“胶水代码”进行调用。

---

### 4. 功能需求 (按子包拆分)

#### 4.1. `core/api`: API契约中心
*   **职责**: 定义整个CINA.CLUB平台的**通信语言**。
*   **功能需求**:
    *   **FR4.1.1 (Protobuf定义)**: 所有微服务对外（包括S2S和对客户端）的gRPC接口，以及所有消息队列中的事件结构，都**必须**在此目录下的`.proto`文件中进行定义。
    *   **FR4.1.2 (统一规范)**: 所有`.proto`文件必须遵循`buf.yaml`中定义的Lint和代码风格规范。
    *   **FR4.1.3 (版本管理)**: 必须遵循`buf`的破坏性变更检测规则，确保API的向后兼容性。
    *   **FR4.1.4 (错误详情)**: 必须包含一个`errors.proto`文件，定义用于gRPC `details`字段传递的标准化`AppErrorDetail`消息。

#### 4.2. `core/models`: 共享数据模型
*   **职责**: 定义平台核心业务对象的**权威Go结构体**。
*   **功能需求**:
    *   **FR4.2.1 (核心对象)**: 包含`User`, `ServiceOffering`, `PKBItem`等在多个服务和客户端之间传递的核心业务对象的`struct`定义。
    *   **FR4.2.2 (单一来源)**: 这些`struct`是业务模型的“单一事实来源”。后端服务在处理业务逻辑时，应直接使用这些类型。
    *   **FR4.2.3 (无数据库标签)**: `core/models`中的`struct`**严禁**包含任何特定于数据库的ORM标签（如`gorm:"..."`）。数据库实体模型应在各个服务的`repository`层单独定义，并与核心模型进行转换。

#### 4.3. `core/crypto`: 端到端加密(E2EE)核心
*   **职责**: 提供平台无关的、高性能的、安全的E2EE加解密原语。
*   **功能需求**:
    *   **FR4.3.1 (密钥派生)**: 提供`DeriveMasterKey(password, salt)`函数，**必须**使用**Argon2id**算法从用户密码安全地派生出主加密密钥(MEK)。
    *   **FR4.3.2 (对称加解密)**: 提供`Encrypt(plaintext, key)`和`Decrypt(ciphertext, key)`函数。
        *   **必须**使用现代的AEAD加密算法，如**XChaCha20-Poly1305**，以提供高安全性和对大文件的良好支持。
        *   函数应自动处理随机数(nonce)的生成和附加。
    *   **FR4.3.3 (导出)**: 必须通过带构建标签的`exports_*.go`文件，将上述函数安全地导出到Go Mobile和WASM。导出的函数接口必须处理好`[]byte`和平台特定类型（如`jbyteArray`, `NSData`）之间的转换。

#### 4.4. `core/datasync`: 加密数据同步协议
*   **职责**: 实现与`cloud-sync-service`交互的客户端逻辑。
*   **功能需求**:
    *   **FR4.4.1 (版本向量)**: 提供`VersionVector`数据结构及其比较和合并的逻辑。
    *   **FR4.4.2 (内容定义分块 - CDC)**: 提供`ChunkData(data []byte)`函数，使用**FastCDC**算法将数据分割成内容定义的块。
    *   **FR4.4.3 (同步状态机)**: 实现一个客户端同步状态机，能够处理`push` -> `upload` -> `finalize`和`pull` -> `download`的完整流程。
    *   **FR4.4.4 (导出)**: 将同步状态机的高层接口（如`SyncEngine.PushChanges()`, `SyncEngine.PullChanges()`）导出到Go Mobile和WASM。

#### 4.5. `core/aic`: 本地AI核心
*   **职责**: 封装与本地LLM推理引擎（C++库）的交互。
*   **功能需求**:
    *   **FR4.5.1 (CGO绑定)**: 使用CGO定义对`llama.cpp`核心函数的Go调用接口，如`llama_init_from_file`, `llama_eval`, `llama_sample_token`。
    *   **FR4.5.2 (推理会话管理)**: 提供`NewSession(modelPath)`函数，用于加载模型并返回一个推理会话。
    *   **FR4.5.3 (流式生成)**: 提供`Session.PredictStream(prompt, tokenCallback)`方法，该方法在后台线程中进行推理，并通过一个Go channel或回调函数，实时返回生成的token。
    *   **FR4.5.4 (导出)**: 将`NewSession`和`PredictStream`等高层接口导出到Go Mobile，供前端调用。**注意：WASM端通常通过JS互操作调用Web版的LLM库，而不是通过CGO。**

---

### 5. 接口定义 (API Specification)

本包主要提供Go语言内部的API，其对外的接口是编译后的库。关键的Go接口示例如下：

```go
// core/crypto/exports_mobile.go
//go:build mobile
package crypto

// EncryptData a mobile-friendly wrapper for e2ee.Encrypt
func EncryptData(key, plaintext []byte) ([]byte, error) { ... }


// core/aic/exports_mobile.go
//go:build mobile
package aic

type Session struct { ... }
type TokenCallback interface { OnToken(token string) }

func NewLLMSession(modelPath string) (*Session, error)
func (s *Session) PredictStream(prompt string, callback TokenCallback) error
```

---

### 6. 跨平台编译与集成策略

*   **编译脚本**: `scripts/gen-gomobile.sh`和`scripts/gen-wasm.sh`脚本负责自动化编译流程。
*   **版本对齐**: `core/`库的版本必须与依赖它的前端和后端应用版本严格对齐。Monorepo的单一版本特性天然地保证了这一点。
*   **桥接层**:
    *   **移动端**: 在`apps/mobile/src/native-modules/`中编写非常薄的桥接代码，将Go Mobile库的API暴露为JS的`Promise`和事件。
    *   **Web端**: 在`apps/web/src/lib/client/wasm_loader.ts`中编写胶水代码，将WASM导出的Go函数包装成易于使用的JS函数。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   `crypto`模块的加解密性能必须接近原生C库的性能。
    *   `aic`模块的CGO调用开销必须极低。
    *   编译后的库文件体积应在可接受范围内。WASM文件需经过`wasm-opt`优化。
*   **NFR7.2 (可靠性)**:
    *   所有可能失败的操作（特别是CGO调用和内存操作）都必须有健壮的错误处理和资源管理（如`defer C.free(...)`）。
    *   库本身绝不能产生panic。
*   **NFR7.3 (可测试性)**: 所有纯Go逻辑都必须有100%的单元测试覆盖率。对于依赖CGO的模块，需要编写集成测试。

---

### 8. 技术约束与开发规范

*   **TC8.1 (依赖)**: **严格控制第三方Go依赖**。任何新依赖的引入都必须经过严格评估，考虑其对最终二进制文件体积、安全性和许可证的影响。
*   **TC8.2 (平台特定代码)**: 所有平台相关的代码（CGO、Go Mobile/WASM导出）都**必须**使用构建标签（`//go:build`）进行隔离。
*   **TC8.3 (内存管理)**: 在CGO和WASM交互中，必须特别注意手动内存管理，避免内存泄漏。
*   **TC8.4 (代码审查)**: 对`core/`的任何PR都必须经过**至少两名**来自不同团队（如一名后端，一名移动端）的核心工程师的审查和批准。

---
这份SRS为`core/`共享库的设计和实现提供了坚实、全面的指导。它定义了CINA.CLUB平台的技术“DNA”，是实现全栈Go-Centric架构、保证多端一致性、并交付高性能、高安全性应用的关键。