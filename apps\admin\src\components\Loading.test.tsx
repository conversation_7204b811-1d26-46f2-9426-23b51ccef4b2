/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 17:50:00
 * Modified: 2025-01-23 17:50:00
 */

import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Loading } from './Loading';

describe('Loading Component', () => {
  it('renders the loading spinner', () => {
    render(<Loading />);
    const spinner = screen.getByRole('alert');
    expect(spinner).toBeInTheDocument();
  });

  it('displays the correct tip when provided', () => {
    const tipMessage = 'Loading data...';
    render(<Loading tip={tipMessage} />);
    const tip = screen.getByText(tipMessage);
    expect(tip).toBeInTheDocument();
  });

  it('renders a full-page loader when variant is "page"', () => {
    const { container } = render(<Loading variant="page" />);
    const pageLoader = container.firstChild;
    expect(pageLoader).toHaveStyle('height: 100vh');
  });

  it('matches snapshot', () => {
    const { asFragment } = render(<Loading />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('matches snapshot with tip', () => {
    const { asFragment } = render(<Loading tip="Please wait..." />);
    expect(asFragment()).toMatchSnapshot();
  });
}); 