/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import SwiftUI

// MARK: - Color Tokens

public extension Color {
    /// CINA.CLUB brand colors
    static let cinaPrimary = Color(red: 0.2, green: 0.4, blue: 0.8)
    static let cinaSecondary = Color(red: 0.1, green: 0.7, blue: 0.3)
    static let cinaAccent = Color(red: 0.9, green: 0.5, blue: 0.1)
    
    /// Semantic colors
    static let cinaBackground = Color(UIColor.systemBackground)
    static let cinaSecondaryBackground = Color(UIColor.secondarySystemBackground)
    static let cinaTertiaryBackground = Color(UIColor.tertiarySystemBackground)
    
    static let cinaLabel = Color(UIColor.label)
    static let cinaSecondaryLabel = Color(UIColor.secondaryLabel)
    static let cinaTertiaryLabel = Color(UIColor.tertiaryLabel)
    
    /// Status colors
    static let cinaSuccess = Color(red: 0.0, green: 0.7, blue: 0.0)
    static let cinaWarning = Color(red: 0.9, green: 0.6, blue: 0.0)
    static let cinaError = Color(red: 0.9, green: 0.0, blue: 0.0)
    static let cinaInfo = Color(red: 0.0, green: 0.5, blue: 0.9)
}

// MARK: - Typography

public extension Font {
    /// CINA.CLUB typography scale
    static let cinaLargeTitle = Font.largeTitle.weight(.bold)
    static let cinaTitle1 = Font.title.weight(.semibold)
    static let cinaTitle2 = Font.title2.weight(.semibold)
    static let cinaTitle3 = Font.title3.weight(.medium)
    static let cinaHeadline = Font.headline.weight(.semibold)
    static let cinaSubheadline = Font.subheadline.weight(.medium)
    static let cinaBody = Font.body
    static let cinaCallout = Font.callout
    static let cinaFootnote = Font.footnote
    static let cinaCaption1 = Font.caption
    static let cinaCaption2 = Font.caption2
}

// MARK: - Spacing

public struct Spacing {
    public static let xs: CGFloat = 4
    public static let sm: CGFloat = 8
    public static let md: CGFloat = 16
    public static let lg: CGFloat = 24
    public static let xl: CGFloat = 32
    public static let xxl: CGFloat = 48
}

// MARK: - Corner Radius

public struct CornerRadius {
    public static let xs: CGFloat = 4
    public static let sm: CGFloat = 8
    public static let md: CGFloat = 12
    public static let lg: CGFloat = 16
    public static let xl: CGFloat = 24
    public static let circle: CGFloat = 1000
}

// MARK: - Components

/// Primary button component
public struct PrimaryButton: View {
    let title: String
    let action: () -> Void
    let isLoading: Bool
    let isDisabled: Bool
    
    public init(
        title: String,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isLoading = isLoading
        self.isDisabled = isDisabled
        self.action = action
    }
    
    public var body: some View {
        Button(action: action) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                
                Text(title)
                    .font(.cinaHeadline)
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(isDisabled ? Color.gray : Color.cinaPrimary)
            )
        }
        .disabled(isDisabled || isLoading)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
    }
}

/// Secondary button component
public struct SecondaryButton: View {
    let title: String
    let action: () -> Void
    let isLoading: Bool
    let isDisabled: Bool
    
    public init(
        title: String,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isLoading = isLoading
        self.isDisabled = isDisabled
        self.action = action
    }
    
    public var body: some View {
        Button(action: action) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .cinaPrimary))
                        .scaleEffect(0.8)
                }
                
                Text(title)
                    .font(.cinaHeadline)
                    .foregroundColor(.cinaPrimary)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .stroke(isDisabled ? Color.gray : Color.cinaPrimary, lineWidth: 2)
            )
        }
        .disabled(isDisabled || isLoading)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
    }
}

/// Card view component
public struct CardView<Content: View>: View {
    let content: Content
    let padding: CGFloat
    
    public init(padding: CGFloat = Spacing.md, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.padding = padding
    }
    
    public var body: some View {
        content
            .padding(padding)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(Color.cinaSecondaryBackground)
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
    }
}

/// Avatar view component
public struct AvatarView: View {
    let imageURL: URL?
    let size: CGFloat
    let fallbackText: String
    
    public init(imageURL: URL?, size: CGFloat = 40, fallbackText: String = "?") {
        self.imageURL = imageURL
        self.size = size
        self.fallbackText = fallbackText
    }
    
    public var body: some View {
        Group {
            if let imageURL = imageURL {
                AsyncImage(url: imageURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    ProgressView()
                }
            } else {
                Text(fallbackText)
                    .font(.cinaHeadline)
                    .foregroundColor(.white)
                    .background(Color.cinaPrimary)
            }
        }
        .frame(width: size, height: size)
        .clipShape(Circle())
    }
}

/// Input field component
public struct InputField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let isSecure: Bool
    let errorMessage: String?
    
    public init(
        title: String,
        placeholder: String = "",
        text: Binding<String>,
        isSecure: Bool = false,
        errorMessage: String? = nil
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.isSecure = isSecure
        self.errorMessage = errorMessage
    }
    
    public var body: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text(title)
                .font(.cinaCallout)
                .foregroundColor(.cinaLabel)
            
            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                } else {
                    TextField(placeholder, text: $text)
                }
            }
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .frame(height: 44)
            
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.cinaCaption1)
                    .foregroundColor(.cinaError)
            }
        }
    }
}

/// Tag view component
public struct TagView: View {
    let text: String
    let color: Color
    
    public init(text: String, color: Color = .cinaPrimary) {
        self.text = text
        self.color = color
    }
    
    public var body: some View {
        Text(text)
            .font(.cinaCaption1)
            .foregroundColor(.white)
            .padding(.horizontal, Spacing.sm)
            .padding(.vertical, Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.sm)
                    .fill(color)
            )
    }
}

/// Empty state view component
public struct EmptyStateView: View {
    let image: String
    let title: String
    let message: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    public init(
        image: String,
        title: String,
        message: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.image = image
        self.title = title
        self.message = message
        self.actionTitle = actionTitle
        self.action = action
    }
    
    public var body: some View {
        VStack(spacing: Spacing.lg) {
            Image(systemName: image)
                .font(.system(size: 60))
                .foregroundColor(.cinaSecondaryLabel)
            
            VStack(spacing: Spacing.sm) {
                Text(title)
                    .font(.cinaTitle3)
                    .foregroundColor(.cinaLabel)
                    .multilineTextAlignment(.center)
                
                Text(message)
                    .font(.cinaBody)
                    .foregroundColor(.cinaSecondaryLabel)
                    .multilineTextAlignment(.center)
            }
            
            if let actionTitle = actionTitle, let action = action {
                PrimaryButton(title: actionTitle, action: action)
                    .frame(maxWidth: 200)
            }
        }
        .padding(Spacing.xl)
    }
}

/// Loading view component
public struct LoadingView: View {
    let message: String
    
    public init(message: String = "Loading...") {
        self.message = message
    }
    
    public var body: some View {
        VStack(spacing: Spacing.md) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .cinaPrimary))
                .scaleEffect(1.2)
            
            Text(message)
                .font(.cinaCallout)
                .foregroundColor(.cinaSecondaryLabel)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.cinaBackground)
    }
}

// MARK: - View Modifiers

public extension View {
    /// Apply card style to any view
    func cardStyle(padding: CGFloat = Spacing.md) -> some View {
        self.padding(padding)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(Color.cinaSecondaryBackground)
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
    }
    
    /// Apply section header style
    func sectionHeaderStyle() -> some View {
        self
            .font(.cinaHeadline)
            .foregroundColor(.cinaLabel)
            .padding(.horizontal, Spacing.md)
            .padding(.vertical, Spacing.sm)
    }
    
    /// Apply navigation bar style
    func cinaNavigationBarStyle() -> some View {
        self
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(Color.cinaBackground, for: .navigationBar)
            .toolbarBackground(.visible, for: .navigationBar)
    }
} 