﻿这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **深化工作流**: 更详细地定义模型处理工作流，引入“管道(Pipeline)”和“步骤(Step)”的概念，使其更具灵活性和可配置性。
2.  **增强客户端能力匹配**: 细化客户端能力描述文件（Device Profile）和模型目标能力（Target Capabilities）的匹配逻辑。
3.  **强化版本与部署策略**: 引入更复杂的部署策略，如分阶段发布（Canary Release）和A/B测试支持。
4.  **细化API与事件**: 提供更具体的请求/响应体示例和事件契约。
5.  **补充非功能性需求**: 增加更具体、可量化的性能和安全指标。

这份文档将描绘一个功能强大、灵活、且符合现代MLOps实践的模型管理与分发平台。

---

### CINA.CLUB - model-management-service 需求规格说明书

**版本: 2.0 (生产级定义，集成高级工作流与部署策略)**  
**发布日期: 2025-06-19**  
**最后修订日期: 2025-06-19**  
**文档负责人:** [AI平台/MLOps团队负责人名称]  
**审批人:** [CTO/AI总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台内实现强大的、低延迟的、可离线的端侧AI能力，需要一个健壮的系统来管理和分发经过优化的AI模型。`model-management-service` 的目的在于构建一个集中化、自动化、可追溯的**端侧AI模型生命周期管理平台**，负责从模型获取、处理优化、版本控制、部署管理到安全分发的整个流程，赋能客户端应用实现高效的设备端推理。

#### 1.2. 服务范围
本服务 **负责**:
*   **模型获取**: 从Hugging Face Hub、公共URL或管理员上传，获取预训练模型。
*   **模型处理工作流**: 编排一个可配置的、多步骤的异步工作流（Pipeline），对原始模型进行格式转换（如转为TFLite, CoreML）、量化（FP16, INT8）、剪枝等优化。
*   **模型注册与元数据管理**: 为每个模型及其版本维护一个中央注册表，记录其来源、格式、性能、签名等详尽元数据。
*   **模型版本控制**: 管理模型的多个版本和其独立的生命周期状态（RAW, READY, DEPLOYED, DEPRECATED, ARCHIVED）。
*   **模型部署与分发**:
    *   管理模型的部署策略，支持全量发布、分阶段发布（Canary）。
    *   提供基于客户端能力（设备型号、OS版本、硬件加速器）的智能模型发现API。
    *   提供安全的、有时效性的模型文件下载URL。

本服务 **不负责**:
*   模型训练或微调 (由上游ML平台负责)。
*   端侧模型的实际加载与推理执行 (由客户端SDK负责)。
*   云端AI模型的服务化部署（如API a la `ai-assistant-service`）。
*   文件二进制数据的持久化存储 (由 `file-storage-service` 负责)。

#### 1.3. 目标用户/调用方
*   **MLOps工程师/AI平台管理员**: (主要管理用户) 通过管理后台或API与本服务交互，管理模型的整个生命周期。
*   **CINA.CLUB客户端应用**: (主要消费用户) 调用本服务的API进行模型发现和下载。
*   **`file-storage-service`**: 本服务调用它来存储和获取模型文件。
*   **模型处理Worker**: 独立的、可伸缩的后台计算节点，实际执行模型优化任务。

#### 1.4. 定义与缩略语
*   **On-Device Model**: 端侧AI模型。
*   **Pipeline**: 模型处理流水线，由一个或多个有序的Step组成。
*   **Step**: 流水线中的一个具体处理步骤，如`convert_to_tflite`, `quantize_int8`。
*   **Device Profile**: 描述客户端设备能力（硬件、OS等）的结构化数据。
*   **HF Hub**: Hugging Face Hub.
*   **MLOps**: Machine Learning Operations.

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`model-management-service` 是CINA.CLUB端侧AI能力的“兵工厂”和“后勤分发中心”。它连接了模型研发（AI科学家）和模型消费（客户端工程师），通过标准化的流程和严格的版本控制，确保所有部署到客户端的AI模型都是经过充分优化、质量可控、安全可靠的。

#### 2.2. 主要功能概述
*   可配置的、多步骤的模型处理流水线。
*   全面的模型元数据与版本管理。
*   基于客户端能力的智能模型发现与匹配。
*   支持分阶段发布的部署策略。
*   安全的模型文件分发。

### 3. 核心流程图

#### 3.1. 模型导入与处理流程
```mermaid
sequenceDiagram
    participant Admin
    participant ModelMgmtService as MMS
    participant HF_Hub as "Hugging Face Hub"
    participant FileStorageService as FSS
    participant MQ as "Message Queue"
    participant Worker as "Model Processing Worker"

    Admin->>MMS: 1. POST /admin/import/huggingface (modelId: "org/model-name")
    MMS->>HF_Hub: 2. Download model files
    HF_Hub-->>MMS: (Model files)
    MMS->>FSS: 3. Upload raw model to object storage
    FSS-->>MMS: (raw_object_key)
    MMS->>MMS: 4. Create ModelMetadata record (status: RAW)
    
    Admin->>MMS: 5. POST /admin/models/{id}/process-jobs (pipeline: "tflite_fp16_quantization")
    MMS->>MMS: 6. Create ModelProcessingJob record (status: PENDING)
    MMS->>MQ: 7. Publish ModelProcessingJobRequest event
    
    MQ-->>Worker: (Consume event)
    Worker->>FSS: 8. Download raw model
    Worker->>Worker: 9. **Execute Pipeline:** <br/> - Step 1: Convert to TFLite <br/> - Step 2: Quantize to FP16
    Worker->>FSS: 10. Upload optimized model & artifacts
    Worker->>MQ: 11. Publish ModelProcessingResultEvent (success, optimized_key, metrics)
    
    MQ-->>MMS: (Consume event)
    MMS->>MMS: 12. Create new ModelMetadata version (status: READY)
    MMS->>MMS: 13. Update job status to COMPLETED
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 模型获取与导入
*   **FR4.1.1 (多源导入)**: 系统必须支持从HF Hub、公共URL和管理员手动上传三种方式获取原始模型文件。
*   **FR4.1.2 (初始注册)**: 导入后，系统应在数据库中创建初始`Model`实体和其第一个`ModelVersion`记录，状态为`RAW`，并将原始模型文件存储到`file-storage-service`。

#### 4.2. 模型处理与优化工作流
*   **FR4.2.1 (流水线定义)**: 系统必须支持管理员通过API或配置文件，定义多个模型处理流水线（`ProcessingPipeline`）。每个流水线由一个或多个有序的步骤（`Step`）组成。
*   **FR4.2.2 (处理作业触发)**: 管理员必须能为指定的`ModelVersion`（`RAW`状态）触发一个处理作业，并指定使用哪个`ProcessingPipeline`。
*   **FR4.3.3 (异步编排)**: 系统必须将处理作业作为一个异步任务发送到消息队列。任务内容应包含源模型的位置、要执行的流水线定义。
*   **FR4.3.4 (状态追踪)**: 系统必须能跟踪每个处理作业的状态 (`PENDING`, `RUNNING`, `COMPLETED`, `FAILED`) 并记录详细的日志（包括每个步骤的日志）。
*   **FR4.3.5 (结果处理)**: 作业成功完成后，系统必须根据Worker返回的结果（优化后模型文件的key、性能指标、校验和等），创建一个新的`ModelVersion`记录，状态为`READY`。

#### 4.4. 模型注册与元数据管理
*   **FR4.4.1 (详尽元数据)**: `ModelVersion`记录必须包含详尽的元数据，包括：`modelId`, `versionString`, `description`, `modelType` (e.g., NLU, ASR), `targetFramework` (e.g., TFLITE, CORE_ML), `tags`, `inputOutputSignature`, `optimizedSizeMb`, `checksum`, `downloadObjectKey`, `status`, `license`, `performanceMetrics` (e.g., latency, accuracy), 和**`targetDeviceCapabilities`**。
*   **FR4.4.2 (`targetDeviceCapabilities`)**: 这是一个关键字段，以键值对数组形式定义了该模型版本所适配的最低设备能力。例如: `[{"key": "soc", "op": "in", "value": ["snapdragon_8_gen2", "tensor_g3"]}, {"key": "ram_gb", "op": "gte", "value": 8}]`。

#### 4.5. 模型分发与部署管理
*   **FR4.5.1 (智能模型发现)**: 系统必须提供客户端API (`GET /available`)，接收客户端上报的`DeviceProfile`。
    *   **`DeviceProfile`**: 客户端上报的自身设备信息，如 `{ "soc": "snapdragon_8_gen2", "os_version": "14.0", "ram_gb": 12, "app_version": "1.2.0" }`。
    *   **匹配逻辑**: 服务端根据`DeviceProfile`，筛选出所有`targetDeviceCapabilities`与之匹配的、且状态为`DEPLOYED`的模型版本。
*   **FR4.5.2 (部署策略管理)**:
    *   一个模型在任何时候，对于一种目标框架，只能有一个版本被标记为`STABLE`。
    *   支持**分阶段发布 (Canary)**: 管理员可以将一个新版本部署为`CANARY`，并配置一个流量比例（如`10%`）。客户端在请求时，本服务根据此比例，决定是返回`STABLE`版本还是`CANARY`版本。
*   **FR4.5.3 (安全下载)**: 系统必须提供API，为指定的`ModelVersion`生成一个有时效性、安全的预签名下载URL（通过调用`file-storage-service`）。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端RESTful API接口
*   **版本**: `/api/v1/on-device-models`
*   **认证**: 用户JWT。
*   **核心端点**:
    *   `POST /available`: 列出当前设备可用的模型。
        *   **Request Body**: `DeviceProfile { soc, os_version, ram_gb, ... }`
        *   **Response (200 OK)**: `AvailableModelsResponse { models: [ModelInfo] }`
    *   `GET /download-info/{modelId}/{versionString}`: 获取下载URL和校验信息。
        *   **Response (200 OK)**: `ModelDownloadInfo { downloadUrl, checksum, sizeMb }`

#### 5.2. 管理后台API接口
*   **路径前缀**: `/api/v1/admin/models`
*   **认证**: Admin/MLOps角色JWT。
*   **核心端点**:
    *   **Import**: `POST /import/huggingface`, `POST /import/url`, `POST /import/upload`
    *   **Processing**: `POST /{modelId}/versions/{versionId}/process-jobs`
    *   **Jobs**: `GET /process-jobs?status=RUNNING`
    *   **Deployment**: `PUT /{modelId}/versions/{versionId}/deploy`
        *   **Request Body**: `DeployRequest { strategy: "STABLE" | "CANARY", canary_percentage?: number }`

#### 5.3. 消息队列事件契约
*   **出站 (发布到MQ)**: `ModelProcessingJobRequestEvent { jobId, sourceModelKey, pipelineDefinition }`
*   **入站 (消费自MQ)**: `ModelProcessingResultEvent { jobId, success, artifacts: [{type, key, metrics}] }`

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (Go Structs for GORM/sqlx)
*   **`Model`**: 模型的顶层实体，`model_id`, `name`, `description`, `model_type`。
*   **`ModelVersion`**: 版本的详细元数据，如FR4.4.1所述。
*   **`ProcessingPipeline`**: 流水线定义，`id`, `name`, `steps_jsonb`。
*   **`ModelProcessingJob`**: 处理作业记录，`id`, `source_version_id`, `pipeline_id`, `status`, `logs`。
*   **`Deployment`**: 部署策略记录，`id`, `model_id`, `version_id`, `strategy`, `canary_percentage`。

#### 6.2. 数据持久化与存储
*   **元数据数据库 (PostgreSQL)**: 使用PostgreSQL存储所有元数据表。利用JSONB字段存储灵活的配置，如`targetDeviceCapabilities`和`performanceMetrics`。
*   **模型文件 (Object Storage)**: 所有模型文件（原始、优化后）通过`file-storage-service`存储在对象存储中。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: `/available` 模型发现API P99延迟应 `< 150ms`。下载信息获取 P99 < 100ms。
*   **模型处理时间**: 可以很长（分钟到小时级），必须是异步的，不影响API服务。

#### 7.2. 可靠性与可用性需求
*   **API服务可用性**: > 99.9%。
*   **模型处理工作流**: 必须可靠，支持失败重试，并有死信队列处理无法完成的作业。

#### 7.3. 可扩展性需求
*   API服务可水平扩展。
*   模型处理Worker集群必须是可根据任务队列长度自动伸缩的。

#### 7.4. 安全性需求
*   **Admin API**: 必须有严格的角色权限保护。
*   **模型下载**: 必须通过安全的、有时效性的预签名URL。
*   **模型安全**: 对上传的原始模型进行安全扫描（如检查是否有恶意代码）。
*   **凭证安全**: 保护Hugging Face Token和云服务API Key。

#### 7.5. 可观测性需求
*   **日志**: 详细记录模型导入、处理作业生命周期（包括每个步骤）、部署变更、API调用、错误。
*   **指标**: API性能、模型处理作业队列长度、平均处理时间、成功/失败率、各模型版本下载次数。
*   **追踪**: 分布式追踪覆盖API请求、MQ消息发送、Worker处理、结果回调的完整链路。

### 8. 技术约束与选型建议
*   **API与编排层**: Go语言。
*   **模型处理Worker**: 强烈推荐使用 **Python** 环境，因为绝大部分模型优化工具（TensorFlow, PyTorch, ONNX Runtime）都以Python为主要接口。服务间通过MQ解耦。
*   **集成**: 与`file-storage-service`的紧密集成是存储和分发模型文件的关键。
*   **配置化**: 流水线、部署策略等都应是可配置的，而不是硬编码。

---
这份版本2.0的SRS文档为`model-management-service`提供了一个更加成熟和灵活的设计蓝图，使其能够支持复杂的MLOps流程，满足生产环境中对端侧AI模型管理的严格要求。