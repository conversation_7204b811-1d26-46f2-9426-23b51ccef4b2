package di

import (
	"fmt"
	"reflect"
	"sync"
)

// Container represents a dependency injection container
type Container struct {
	services map[reflect.Type]interface{}
	mu       sync.RWMutex
}

// NewContainer creates a new dependency injection container
func NewContainer() *Container {
	return &Container{
		services: make(map[reflect.Type]interface{}),
	}
}

// Register adds a service to the container
func (c *Container) Register(service interface{}) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	t := reflect.TypeOf(service)
	if t.Kind() != reflect.Ptr {
		return fmt.Errorf("service must be a pointer, got %v", t)
	}

	c.services[t] = service
	return nil
}

// Resolve retrieves a service from the container
func (c *Container) Resolve(serviceType reflect.Type) (interface{}, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	service, exists := c.services[serviceType]
	if !exists {
		return nil, fmt.Errorf("service of type %v not found", serviceType)
	}

	return service, nil
}

// MustResolve retrieves a service and panics if not found
func (c *Container) MustResolve(serviceType reflect.Type) interface{} {
	service, err := c.Resolve(serviceType)
	if err != nil {
		panic(err)
	}
	return service
}

// Clear removes all registered services
func (c *Container) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.services = make(map[reflect.Type]interface{})
}

// ServiceRegistrar defines an interface for registering services
type ServiceRegistrar interface {
	Register(container *Container) error
}

// Interfaces for core module services
type (
	// APIServiceRegistrar defines how API services are registered
	APIServiceRegistrar interface {
		ServiceRegistrar
		InitializeAPIServices() error
	}

	// ModelServiceRegistrar defines how model services are registered
	ModelServiceRegistrar interface {
		ServiceRegistrar
		ValidateModelServices() error
	}

	// AICoreServiceRegistrar defines how AI core services are registered
	AICoreServiceRegistrar interface {
		ServiceRegistrar
		InitializeAIModels() error
	}

	// DataSyncServiceRegistrar defines how data sync services are registered
	DataSyncServiceRegistrar interface {
		ServiceRegistrar
		InitializeSyncMechanisms() error
	}

	// CryptoServiceRegistrar defines how crypto services are registered
	CryptoServiceRegistrar interface {
		ServiceRegistrar
		InitializeCryptoProviders() error
	}
)

// GlobalContainer is a package-level container for global services
var GlobalContainer = NewContainer()
