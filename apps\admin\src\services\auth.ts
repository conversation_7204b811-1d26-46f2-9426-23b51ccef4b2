/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'

import { get, post } from '@/lib/api-client'
import { useAuthStore } from '@/store/auth'
import type { 
  LoginRequest, 
  LoginResponse, 
  AuthUser, 
  RefreshTokenRequest 
} from '@/types/auth'

/**
 * 登录
 */
export function useLogin() {
  const login = useAuthStore((state) => state.login)
  const setLoading = useAuthStore((state) => state.setLoading)
  const setError = useAuthStore((state) => state.setError)

  return useMutation({
    mutationFn: (credentials: LoginRequest): Promise<LoginResponse> => {
      return post('/auth/login', credentials)
    },
    onMutate: () => {
      setLoading(true)
      setError(null)
    },
    onSuccess: (data) => {
      login(data.token, data.refreshToken, data.user)
      message.success('登录成功')
    },
    onError: (error: any) => {
      setError(error.message || '登录失败')
      message.error(error.message || '登录失败')
    },
    onSettled: () => {
      setLoading(false)
    },
  })
}

/**
 * 登出
 */
export function useLogout() {
  const logout = useAuthStore((state) => state.logout)
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (): Promise<void> => {
      return post('/auth/logout')
    },
    onSuccess: () => {
      logout()
      queryClient.clear()
      message.success('已安全退出')
    },
    onError: () => {
      // 即使服务端登出失败，也要清除本地状态
      logout()
      queryClient.clear()
    },
  })
}

/**
 * 获取当前用户信息
 */
export function useCurrentUser() {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const updateUser = useAuthStore((state) => state.updateUser)

  return useQuery({
    queryKey: ['currentUser'],
    queryFn: (): Promise<AuthUser> => get('/auth/me'),
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5分钟
    onSuccess: (data) => {
      updateUser(data)
    },
  })
}

/**
 * 刷新Token
 */
export function useRefreshToken() {
  const login = useAuthStore((state) => state.login)
  const logout = useAuthStore((state) => state.logout)

  return useMutation({
    mutationFn: (request: RefreshTokenRequest): Promise<LoginResponse> => {
      return post('/auth/refresh', request)
    },
    onSuccess: (data) => {
      login(data.token, data.refreshToken, data.user)
    },
    onError: () => {
      logout()
      window.location.href = '/login'
    },
  })
}

/**
 * 修改密码
 */
export function useChangePassword() {
  return useMutation({
    mutationFn: (data: { 
      currentPassword: string
      newPassword: string
      confirmPassword: string
    }): Promise<void> => {
      return post('/auth/change-password', data)
    },
    onSuccess: () => {
      message.success('密码修改成功')
    },
    onError: (error: any) => {
      message.error(error.message || '密码修改失败')
    },
  })
}

/**
 * 更新用户资料
 */
export function useUpdateProfile() {
  const updateUser = useAuthStore((state) => state.updateUser)
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: Partial<AuthUser>): Promise<AuthUser> => {
      return post('/auth/profile', data)
    },
    onSuccess: (data) => {
      updateUser(data)
      queryClient.invalidateQueries(['currentUser'])
      message.success('资料更新成功')
    },
    onError: (error: any) => {
      message.error(error.message || '资料更新失败')
    },
  })
}

/**
 * 获取用户权限列表
 */
export function useUserPermissions() {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const user = useAuthStore((state) => state.user)

  return useQuery({
    queryKey: ['userPermissions', user?.id],
    queryFn: (): Promise<string[]> => get('/auth/permissions'),
    enabled: isAuthenticated && !!user,
    staleTime: 10 * 60 * 1000, // 10分钟
  })
}

/**
 * 验证Token有效性
 */
export function useValidateToken() {
  return useMutation({
    mutationFn: (token: string): Promise<boolean> => {
      return post('/auth/validate', { token })
    },
  })
}

/**
 * 获取登录历史
 */
export function useLoginHistory() {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)

  return useQuery({
    queryKey: ['loginHistory'],
    queryFn: (): Promise<Array<{
      id: string
      ip: string
      userAgent: string
      location: string
      loginAt: string
      success: boolean
    }>> => get('/auth/login-history'),
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000,
  })
}

/**
 * 强制退出其他设备
 */
export function useLogoutOtherDevices() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (): Promise<void> => {
      return post('/auth/logout-other-devices')
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['loginHistory'])
      message.success('已强制退出其他设备')
    },
    onError: (error: any) => {
      message.error(error.message || '操作失败')
    },
  })
} 