好的，遵照您的指示。我将为您生成一份专门针对 **`content-moderation-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`content-moderation-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**多模态审核工作流、可配置的规则引擎、与AI分析及人工审核平台的协同，以及作为一个横向能力中心如何与平台所有UGC服务解耦**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `content-moderation-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `content-moderation-service-srs.md` (v2.0)
**核心架构**: 事件驱动 + 工作流引擎(Pipeline) + 规则引擎(Rules Engine)

## 1. 概述

`content-moderation-service` 是CINA.CLUB平台的“**内容安全防火墙**”和“**治理中枢**”。它是一个**横向的基础设施服务**，为平台所有UGC服务提供统一的内容安全保障。其核心挑战在于：
1.  **高吞吐与低延迟**: 需要能处理来自平台所有服务海量的、多模态的内容审核请求，并尽快给出机审结果。
2.  **准确性与召回率的平衡**: 既要准确识别违规内容，又要避免误伤正常内容。
3.  **灵活性与可配置性**: 审核策略和规则需要能根据运营需求和外部环境变化，进行快速、动态的调整，而无需重新部署服务。
4.  **多阶段工作流**: 需要编排复杂的“机审 -> 人审 -> 复审”流程，并与多种AI分析能力和人工审核平台进行集成。
5.  **解耦与通用性**: 必须作为一个通用的、与具体业务场景无关的服务，为所有UGC源提供一致的审核能力。

本架构设计通过采用**事件驱动**的异步处理模型，并内置一个**可配置的工作流引擎**和**规则引擎**来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (审核工作流管道)

```mermaid
graph TD
    subgraph "UGC 源服务"
        style "UGC 源服务" fill:#eee
        SourceService[e.g., community-forum]
    end

    subgraph "ContentModerationService"
        style ContentModerationService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[WorkflowEngine<br/><em>application/workflow</em>]
        C{Pipeline: Machine Review}
        D{Pipeline: Human Review}
        E[RuleEngine<br/><em>domain/service</em>]
        F[AI Analyzer Adapters<br/><em>adapter/analyzers</em>]
        G[Human Review Platform Client<br/><em>adapter/client</em>]
        H[Repository<br/><em>adapter/repository</em>]
    end

    subgraph "外部依赖"
        style "外部依赖" fill:#f3e5f5
        AI1[Text AI API]
        AI2[Image AI API]
        HR[Human Review Platform]
        Kafka[(Kafka)]
    end
    
    SourceService -- "1. POST /tasks (content)" --> A
    A -- "调用" --> B
    B -- "2. Create Task, Select & Start Pipeline" --> H & C
    
    C -- "3. Call AI Analyzers" --> F
    F -- "Adapts and calls" --> AI1 & AI2
    
    AI1 & AI2 -- "AI Results" --> F
    F -- "Normalized Results" --> C
    
    C -- "4. Evaluate with Rule Engine" --> E
    E -- "Decision: PASS/REJECT/REVIEW" --> C
    
    subgraph "Decision Path"
        C -- "PASS/REJECT" --> B
        B -- "5a. Publish ModerationCompletedEvent" --> Kafka
        
        C -- "REVIEW" --> D
        D -- "Push to Human Review" --> G
        G -- "Calls API" --> HR
        HR -- "Webhook Callback" --> A
        A -- "Update Human Decision" --> B
        B -- "5b. Publish ModerationCompletedEvent" --> Kafka
    end

    Kafka --> SourceService
```

### 2.2 最终目录结构 (`services/content-moderation-service/`)

```
content-moderation-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # ✨ 异步AI分析任务的独立Worker入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── analyzers/          # ✨ AI分析引擎的适配器 ✨
│   │   │   ├── interface.go
│   │   │   ├── text_aliyun.go
│   │   │   └── image_google.go
│   │   ├── client/
│   │   │   └── human_review_platform_client.go
│   │   ├── event/
│   │   │   └── producer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── workflow/           # ✨ 审核工作流引擎 ✨
│   │       ├── engine.go
│   │       ├── pipeline.go
│   │       └── step_machine_review.go # 各个工作流步骤的实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── rule_engine.go  # ✨ 规则引擎核心 ✨
│           └── user_profile_service.go # 用户风险画像服务
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Governance Rules)

*   `domain/model/`: 定义`ModerationTask`, `Rule`, `UserProfile`等核心领域对象。
*   **`domain/service/rule_engine.go`**: **这是决策的核心**。
    *   **`RuleEngine` struct**: 一个无状态的领域服务。
    *   **`Evaluate(context)` method**:
        *   接收一个包含所有决策所需信息的`EvaluationContext`（如AI分析结果、用户信息、内容元数据）。
        *   从仓储中加载与当前场景匹配的、所有激活的`Rule`。
        *   按优先级顺序，逐条执行规则。规则的`conditions`部分是一个可被解析的逻辑表达式树（如存储为JSON）。
        *   一旦有规则匹配，立即返回其定义的`action`（`PASS`, `REJECT`, `REVIEW`）和`labels`。
        *   如果所有规则都不匹配，则返回一个默认的`action`（如`REVIEW`）。
*   **`domain/service/user_profile_service.go`**:
    *   `UserProfileService`: 负责管理用户的历史违规记录和风险等级。
    *   `UpdateOnViolation(userID, task)`: 当一个任务被最终判定为违规时，调用此方法更新用户的违规记录，并可能重新计算其风险等级。

### 3.2 `application/` - 应用层 (The Workflow Engine)

*   `application/port/`: 定义`Repository`, `WorkflowEngineService`等接口。
*   **`application/workflow/`**: **这是整个审核流程的编排者**。
    *   **`engine.go`**: `WorkflowEngine`是主入口。
        *   **`SubmitTask(ctx, taskData)`**:
            1.  调用`repository.CreateTask()`创建一条新的审核任务记录。
            2.  根据`taskData`的`contentType`和`businessScenario`，从配置中选择一个合适的`Pipeline`。
            3.  **将一个`ExecutePipelineJob`任务推送到Asynq队列中**，而不是同步执行。
    *   **`pipeline.go`**:
        *   **`Pipeline` interface**: 定义`Run(ctx, task)`接口。
        *   **`Step` interface**: 定义`Execute(ctx, task)`接口。
        *   一个`Pipeline`由一个或多个有序的`Step`组成。
    *   **`step_machine_review.go`**:
        *   **`MachineReviewStep`**: 实现了`Step`接口。
        *   **`Execute` method**:
            a. 确定需要哪些类型的AI分析（文本、图像...）。
            b. **并行地**调用`adapter/analyzers`中对应的适配器，获取所有AI分析结果。
            c. 将归一化后的结果和任务上下文，传递给`domain.RuleEngine.Evaluate()`。
            d. 根据`RuleEngine`返回的决策，决定是进入下一个`Step`（如`HumanReviewStep`），还是直接结束工作流并更新任务最终状态。

### 3.3 `adapter/` - 适配层 (The Bridge to External Systems)

*   **`adapter/analyzers/`**: **AI能力适配器层**。
    *   **`interface.go`**: 定义`TextAnalyzer`, `ImageAnalyzer`等统一接口。
    *   **`text_aliyun.go`, `image_google.go`**: 实现了具体的接口，封装了对阿里云文本审核、Google Vision API等第三方服务的HTTP客户端调用。这使得更换或增加AI提供商变得非常容易。
*   **`adapter/repository/`**:
    *   使用**PostgreSQL**。其JSONB类型非常适合存储灵活的规则定义和审核结果。
*   **`adapter/client/`**:
    *   封装对外部人工审核平台API的调用。
*   **`adapter/transport/` (`grpc/`, `http/`)**:
    *   `grpc/handler.go`: 实现`content-moderation-service.proto`中定义的`SubmitTask`等接口。
    *   `http/webhook_handler.go`: 接收来自人工审核平台的结果回调Webhook。

### 3.4 `cmd/worker/` - 异步任务处理器

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   它是一个**Asynq Worker**，消费在**2.2**中`WorkflowEngine`推送到队列中的`ExecutePipelineJob`任务。
    *   AI分析等耗时操作都在这个Worker中执行，与API服务完全隔离。
*   **设计决策**: 将审核工作流的实际执行与任务提交完全异步化和解耦。
    1.  **保证API低延迟**: `SubmitTask` API可以秒级响应。
    2.  **提高吞吐量与韧性**: 可以通过水平扩展Worker的数量来处理审核任务的洪峰。单个AI分析引擎的超时或失败不会阻塞整个API服务。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`content-moderation-service`：
1.  **异步工作流引擎**: 将复杂的、多步骤的审核流程模型化为可配置的**管道(Pipeline)**，并通过后台Worker异步执行，保证了API的性能和系统的弹性。
2.  **可配置的规则引擎**: 将易变的审核决策逻辑从代码中剥离，沉淀为可在数据库中动态管理的**规则(Rules)**，极大地提升了运营的灵活性。
3.  **适配器模式**: 通过为AI分析能力和人审平台创建**适配器(Adapter)**，将服务与具体的第三方提供商解耦，便于未来更换或增加新的技术供应商。
4.  **数据驱动的风险评估**: 引入**用户风险画像**作为决策的一个重要维度，实现了从“对事”到“对人+对事”的智能化审核升级。

这种架构确保了`content-moderation-service`能够以一种**灵活、可扩展、准确且高效**的方式，为CINA.CLUB平台的所有UGC内容保驾护航，是构建一个健康、安全社区环境的核心技术保障。