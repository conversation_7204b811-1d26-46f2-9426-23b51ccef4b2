好的，遵照您的指示。我将为您生成一份专门针对 **`calendar-sync-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`calendar-sync-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**多提供商适配、双向同步引擎、冲突解决策略，以及与多个服务的复杂协同**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `calendar-sync-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `calendar-sync-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 策略模式(Strategy Pattern) + 适配器模式(Adapter Pattern)

## 1. 概述

`calendar-sync-service` 是CINA.CLUB平台与外部主流日历生态系统之间的“数据同步总线”。其核心挑战在于：
1.  **异构API适配**: Google Calendar, Microsoft Graph Calendar等每个提供商的API、数据模型、认证方式都不同。
2.  **双向同步的复杂性**: 需要精确追踪每个事件在两个系统中的状态，以处理创建、更新和删除操作。
3.  **冲突检测与解决**: 当同一个事件在CINA.CLUB和外部日历都被修改时，必须有一套明确的机制来检测和解决冲突。
4.  **状态管理与可靠性**: 同步过程可能是长耗时的，并且依赖于不稳定的外部网络。必须保证同步任务的可靠执行和状态的精确管理。
5.  **安全性**: 需要安全地管理和使用用户的OAuth凭证。

本架构设计通过采用**整洁架构**，并结合**策略模式**来处理不同提供商的逻辑，构建一个可扩展、健壮、可靠的日历同步引擎。

---

## 2. 架构图与目录结构

### 2.1 核心架构图

```mermaid
graph TD
    subgraph "外部世界"
        A[External Calendars<br/>(Google, Microsoft)]
        B[Client App (for OAuth)]
        C[schedule-service]
    end

    subgraph "CalendarSyncService"
        D[API Layer (gRPC/REST)<br/><em>adapter/transport</em>]
        E[Webhook Endpoint<br/><em>adapter/transport</em>]
        F[Kafka Consumer<br/><em>(for CINA -> External sync)</em><br/><em>adapter/event</em>]
        G[SyncEngine<br/><em>application/sync</em>]
        H{ProviderStrategyFactory<br/><em>application/provider</em>}
        I[Provider Strategies<br/>(GoogleStrategy, MSGraphStrategy)<br/><em>application/provider</em>]
        J[ConflictResolver<br/><em>domain/service</em>]
        K[Data Mapper<br/><em>domain/service</em>]
        L[Repository<br/><em>adapter/repository</em>]
        M[KMSProxy Client<br/><em>adapter/client</em>]
    end
    
    B -- "OAuth & Config" --> D
    A -- "Webhook Notification" --> E
    C -- "Publish ScheduleChangedEvent" --> F
    
    D & E & F -- "Trigger Sync" --> G
    
    G -- "1. Get Provider Strategy" --> H
    H -- "Returns" --> I
    G -- "2. Use Strategy to fetch data" --> I
    I -- "Call External API" --> A
    
    G -- "3. Detect Conflict" --> J
    G -- "4. Map Data" --> K
    G -- "5. Persist mapping" --> L
    L -- "Store encrypted tokens" --> M
    
    I -- "Update CINA schedule" --> C
```
### 2.2 最终目录结构 (`services/calendar-sync-service/`)

```
calendar-sync-service/
├── cmd/server/
│   └── main.go                 # API服务和Webhook服务器启动入口
├── cmd/worker/
│   └── main.go                 # ✨ 后台轮询和事件消费的独立入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── google_calendar_client.go
│   │   │   ├── msgraph_calendar_client.go
│   │   │   └── kms_proxy_client.go
│   │   ├── event/
│   │   │   └── schedule_consumer.go # 消费schedule-service的事件
│   │   ├── grpc/
│   │   │   └── handler.go      # 处理用户连接和映射配置的gRPC Handler
│   │   ├── http/
│   │   │   └── webhook_handler.go # 处理外部日历的Webhook回调
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   ├── provider.go     # ✨ 定义ProviderStrategy接口 ✨
│   │   │   └── service.go
│   │   ├── provider/           # ✨ Provider策略模式实现 ✨
│   │   │   ├── factory.go      # ProviderStrategyFactory
│   │   │   ├── google_strategy.go
│   │   │   └── msgraph_strategy.go
│   │   └── sync/               # ✨ 同步引擎核心 ✨
│   │       └── sync_engine.go
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── conflict_resolver.go # 冲突解决服务
│           └── data_mapper.go       # 数据映射服务
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Core Rules & Models)

*   `domain/model/`: 使用`/core/models`中与日历、连接、同步映射相关的`struct`。
*   **`domain/service/`**: **这是封装核心、无状态业务规则的地方**。
    *   `data_mapper.go`:
        *   **`DataMapper` service**: 提供纯函数，负责在CINA.CLUB的`Event`模型和各个外部提供商的事件模型（如`google.Event`, `msgraph.Event`）之间进行双向转换。
        *   **`ToCinaEvent(externalEvent interface{})`**
        *   **`FromCinaEvent(cinaEvent, providerType)`**
        *   它会处理RRULE、参与者、时区等复杂字段的精确映射。
    *   `conflict_resolver.go`:
        *   **`ConflictResolver` service**:
        *   **`Resolve(localVersion, remoteVersion, policy)` method**: 接收本地和远程两个版本的事件，以及预设的解决策略（`EXTERNAL_WINS`, `LOCAL_WINS`），返回最终应该被保存的版本。这是一个纯粹的决策逻辑。

### 3.2 `application/` - 应用层 (The Engine & Strategies)

这是本服务最核心的逻辑层。

*   **`application/provider/`**: **策略模式的实现，用于适配不同提供商**。
    *   `port/provider.go`: 定义`ProviderStrategy`接口。
        ```go
        type ProviderStrategy interface {
            GetOAuthURL(...) (string, error)
            HandleOAuthCallback(...) (*Connection, error)
            GetCalendars(...) ([]Calendar, error)
            // 核心: 获取增量变更
            GetIncrementalChanges(syncToken string) ([]ExternalEvent, newSyncToken, error)
            CreateEvent(event *models.Event) (externalID, etag string, error)
            UpdateEvent(...)
            DeleteEvent(...)
        }
        ```
    *   `google_strategy.go`, `msgraph_strategy.go`: 分别实现`ProviderStrategy`接口，封装对Google Calendar API和Microsoft Graph API的调用。
    *   `factory.go`: `ProviderStrategyFactory`根据`providerType`（如"GOOGLE"）返回一个具体的策略实例。

*   **`application/sync/`**: **同步引擎，流程的编排者**。
    *   `sync_engine.go`: `SyncEngine` struct。
    *   **`SyncFromExternal(connectionID)` method**:
        1.  从DB获取`Connection`和`SyncMapping`信息。
        2.  使用`ProviderStrategyFactory`获取对应的`ProviderStrategy`。
        3.  调用`strategy.GetIncrementalChanges()`获取外部变更列表。
        4.  **循环处理每个变更**:
            a. 从DB查找该外部事件是否已有映射。
            b. **如果是新建**: 调用`DataMapper`转换 -> 调用`schedule-service`创建新日程 -> 在DB中创建新的`synced_item_references`记录。
            c. **如果是更新**:
                i.  获取CINA.CLUB中的当前版本和`etag`。
                ii. **调用`ConflictResolver`判断并解决冲突**。
                iii. 调用`schedule-service`更新日程 -> 更新DB中的`etag`。
            d. **如果是删除**: 调用`schedule-service`删除日程 -> 删除DB中的映射记录。
        5.  更新`SyncMapping`中的`last_sync_token`。
    *   **`SyncToExternal(cinaScheduleChangeEvent)` method**:
        1.  从事件中获取`cina_schedule_id`。
        2.  从DB找到所有与此ID相关的同步映射。
        3.  对于每个映射，获取其`ProviderStrategy`。
        4.  调用`strategy.CreateEvent`/`UpdateEvent`/`DeleteEvent`将变更推送到外部日历。

### 3.3 `adapter/` - 适配层 (The Bridge to the World)

*   **`adapter/transport/` (`grpc/`, `http/`)**:
    *   `grpc/handler.go`: 实现用户管理OAuth连接和同步映射的API。
    *   `http/webhook_handler.go`:
        *   接收来自Google/Microsoft的Webhook推送。
        *   **只做最轻量的事**: 验证请求合法性，然后将一个`ExternalUpdateDetected`任务（包含`connectionID`）**推送到一个内部的Asynq任务队列**中。**绝不**在Webhook处理器中执行耗时的同步逻辑。
*   **`adapter/event/`**:
    *   `schedule_consumer.go`: 消费来自`schedule-service`的`ScheduleChangedEvent`，并调用`SyncEngine.SyncToExternal`。
*   **`adapter/client/`**:
    *   封装对Google、Microsoft等外部API的HTTP客户端。
    *   **必须**处理好OAuth `access_token`的刷新逻辑：在API调用返回401时，使用`refresh_token`（从`kms-proxy-service`解密后）获取新的`access_token`，然后重试原始请求。
*   **`adapter/repository/`**:
    *   实现`port.Repository`接口，与PostgreSQL交互。
    *   `GetConnection`方法在返回`Connection`对象前，需要调用`kms-proxy-service`客户端**解密`refresh_token`**。
    *   `CreateConnection`方法在持久化前，需要调用`kms-proxy-service`客户端**加密`refresh_token`**。

### 3.4 `cmd/worker/` - 后台任务

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    1.  **Webhook任务处理器**: 消费由Webhook处理器推送到Asynq队列中的`ExternalUpdateDetected`任务，并调用`SyncEngine.SyncFromExternal`。
    2.  **定时轮询器 (Fallback Poller)**:
        *   以**Kubernetes CronJob**的形式每小时运行。
        *   查询出所有`Connection`，并为每个连接都触发一个`SyncFromExternal`任务。这是对Webhook丢失的补充和容错。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`calendar-sync-service`：
1.  **策略模式**: 使用`ProviderStrategy`接口和工厂，优雅地将不同日历提供商的实现细节隔离开来，使得未来添加新的提供商（如Apple Calendar）变得非常简单。
2.  **职责清晰的领域服务**: 将复杂的映射逻辑(`DataMapper`)和冲突解决逻辑(`ConflictResolver`)封装在无状态的领域服务中，易于独立测试和维护。
3.  **异步化与解耦**:
    *   通过Webhook和后台Worker，将耗时的外部同步操作与即时响应解耦。
    *   通过消费Kafka事件，实现了与`schedule-service`的单向解耦。
4.  **安全核心**: 将敏感的`refresh_token`的加解密职责委托给`kms-proxy-service`，本服务不接触明文的长期凭证。
5.  **健壮的同步引擎**: `SyncEngine`作为核心编排者，清晰地处理了双向同步中的各种情况，并集成了冲突解决机制。

这种架构确保了`calendar-sync-service`能够可靠、高效、安全地在CINA.CLUB与外部世界之间建立起时间的桥梁，同时具备了应对未来需求变化的良好扩展性。