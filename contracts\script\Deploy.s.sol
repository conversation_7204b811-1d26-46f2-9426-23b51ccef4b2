// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Script.sol";
import {FamilyTree} from "../src/FamilyTree.sol";
import {UUPSProxy} from "solmate/proxy/UUPSProxy.sol";

contract Deploy is Script {
    
    function run() public returns (FamilyTree) {
        // Get the deployer's private key from the .env file
        uint256 deployerPrivateKey = vm.envUint("DEPLOYER_PRIVATE_KEY");
        
        // Start a broadcast; all subsequent transactions will be recorded and sent
        vm.startBroadcast(deployerPrivateKey);

        // 1. Deploy the implementation contract
        FamilyTree implementation = new FamilyTree();
        console.log("Implementation deployed at:", address(implementation));

        // 2. Deploy the proxy contract without initializing it.
        // An empty bytes array is passed for the initialization data.
        UUPSProxy proxy = new UUPSProxy(address(implementation), "");
        console.log("Proxy deployed at:", address(proxy));
        
        // Stop the broadcast
        vm.stopBroadcast();
        
        // Return a FamilyTree instance pointing to the proxy address for subsequent interactions
        return FamilyTree(payable(address(proxy)));
    }
} 