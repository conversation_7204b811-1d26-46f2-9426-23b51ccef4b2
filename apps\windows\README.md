# CINA.CLUB Windows 原生应用

## 概述

CINA.CLUB Windows 应用是基于 WinUI 3 和 .NET 8 构建的现代桌面应用程序，为 Windows 10/11 用户提供完整的 CINA.CLUB 平台功能体验。

## 技术栈

- **UI 框架**: WinUI 3 (Windows App SDK)
- **编程语言**: C# 12
- **目标框架**: .NET 8 + net8.0-windows10.0.19041.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **MVVM 框架**: CommunityToolkit.Mvvm
- **Go 集成**: P/Invoke 调用 core_go.dll

## 项目结构

```
apps/windows/
├── CinaClub.sln                    # Visual Studio 解决方案文件
├── CinaClub.App/                   # 主应用项目 (WinUI 3)
│   ├── Views/                      # XAML 页面
│   ├── ViewModels/                 # 视图模型
│   ├── Services/                   # 应用服务
│   └── Assets/                     # 应用资源
├── CinaClub.Core/                  # 核心业务逻辑 (.NET Standard)
│   ├── Models/                     # 领域模型
│   ├── Interfaces/                 # 接口定义
│   ├── UseCases/                   # 业务用例
│   └── Services/                   # 业务服务
├── CinaClub.Infrastructure/        # 基础设施层 (.NET Standard)
│   ├── GoBridge/                   # Go 核心库桥接
│   ├── Network/                    # 网络通信
│   ├── Database/                   # 数据存储
│   ├── Security/                   # 安全服务
│   └── Repositories/               # 数据仓储
└── libs/                           # 外部库
    └── x64/
        ├── core_go.dll             # Go 核心库
        └── core_go.h               # C 头文件
```

## 核心特性

### 🔐 端到端加密 (E2EE)
- 通过 Go 核心库实现的强加密算法
- 使用 Windows DPAPI 安全存储密钥
- 完整的密钥派生和管理系统

### 🤖 本地 AI 助手
- 端侧 LLM 模型推理
- 流式对话体验
- 个人知识库 (PKB) 集成

### 🔄 数据同步
- 跨设备加密数据同步
- 版本冲突处理
- 离线优先设计

### 💬 实时通信
- WebSocket 长连接
- 多设备消息同步
- 富文本消息支持

### 🎨 现代 UI
- Fluent Design System
- 深色/浅色主题
- 响应式布局

## 系统要求

- **操作系统**: Windows 10 1809 (Build 17763) 或更高版本
- **架构**: x86, x64, ARM64
- **运行时**: .NET 8 Runtime
- **内存**: 最低 4GB RAM (推荐 8GB)
- **存储**: 最低 500MB 可用空间

## 开发环境设置

### 前置要求

1. **Visual Studio 2022** (版本 17.8 或更高)
   - 安装 ".NET 桌面开发" 工作负载
   - 安装 "Windows 应用开发" 工作负载

2. **Windows App SDK** (最新版本)

3. **Git** (用于版本控制)

### 构建和运行

1. **克隆仓库**
   ```bash
   git clone <repository-url>
   cd 13_CINA.CLUB-Monorepo/apps/windows
   ```

2. **打开解决方案**
   ```bash
   start CinaClub.sln
   ```

3. **还原 NuGet 包**
   ```bash
   dotnet restore
   ```

4. **构建项目**
   ```bash
   dotnet build --configuration Debug
   ```

5. **运行应用**
   - 在 Visual Studio 中按 F5
   - 或者使用命令行: `dotnet run --project CinaClub.App`

## Go 核心库集成

Windows 应用通过 P/Invoke 技术调用 Go 编译的 DLL 库：

### 主要功能
- **加密/解密**: `DeriveKeyFromPassword`, `EncryptSymmetric`, `DecryptSymmetric`
- **AI 推理**: `CreateAISession`, `PredictStream`, `StopPrediction`
- **数据同步**: `SyncToCloud`, `PullFromCloud`

### 使用示例
```csharp
// 加密数据
var cryptoVault = serviceProvider.GetService<ICryptoVault>();
var key = await cryptoVault.DeriveKeyFromPasswordAsync("password");
var encrypted = await cryptoVault.EncryptAsync(key, data);
```

## 配置

应用配置文件位于 `%LOCALAPPDATA%/CinaClub/config.json`：

```json
{
  "ConnectionStrings": {
    "ApiServer": "https://api.cinaclub.com"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  },
  "Features": {
    "AIAssistantEnabled": true,
    "DataSyncEnabled": true
  }
}
```

## 部署

### Debug 部署
```bash
dotnet publish --configuration Debug --output ./publish
```

### Release 部署
```bash
dotnet publish --configuration Release --output ./publish --self-contained true
```

### MSIX 打包
1. 在 Visual Studio 中右键点击 `CinaClub.App` 项目
2. 选择 "打包和发布" > "创建应用包"
3. 按照向导完成打包

## 测试

### 单元测试
```bash
dotnet test
```

### UI 测试
使用 Windows Application Driver (WinAppDriver) 进行 UI 自动化测试。

## 贡献指南

1. **代码规范**
   - 遵循 C# 编码规范
   - 使用 EditorConfig 配置
   - 运行代码分析器

2. **提交流程**
   - 创建功能分支
   - 编写单元测试
   - 提交 Pull Request

3. **文档**
   - 更新相关文档
   - 添加 XML 注释

## 故障排除

### 常见问题

**Q: 应用启动时提示 "Go核心库未就绪"**
A: 确保 `core_go.dll` 文件存在于应用目录下，并且具有执行权限。

**Q: 登录失败**
A: 检查网络连接和服务器配置，确认 API 端点可访问。

**Q: UI 显示异常**
A: 确保系统支持 WinUI 3，并安装了最新的 Windows App SDK。

### 日志
应用日志保存在 `%LOCALAPPDATA%/CinaClub/Logs/` 目录下。

## 许可证

Copyright (c) 2025 Cina.Club. All rights reserved.

## 联系方式

- **技术支持**: <EMAIL>
- **开发团队**: <EMAIL>
- **官方网站**: https://cinaclub.com 