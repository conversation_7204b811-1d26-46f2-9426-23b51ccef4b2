# Admin Dashboard - Cina.Club

This is the central administration dashboard for the Cina.Club platform. It provides a comprehensive suite of tools for managing users, services, content, finances, and system configurations.

## Features

- **User Management**: Full CRUD operations, role and permission management.
- **Service Monitoring**: Real-time health checks and metrics for all backend services.
- **Content Moderation**: A dedicated queue for reviewing and managing user-generated content.
- **Advanced Analytics**: Dashboards for user engagement, revenue, chat, and live streams.
- **Financial Management**: Tools for handling transactions, subscriptions, and revenue forecasting.
- **System Administration**: Configure feature flags, monitor system health, and view audit logs.

## Tech Stack

- **Framework**: React 18 with Vite
- **UI**: Ant Design & Ant Design ProComponents
- **State Management**: Zustand (global) & React Query (server state)
- **Styling**: CSS-in-JS (via Ant Design)
- **Testing**: Vitest, React Testing Library, Playwright for E2E
- **Linting**: ESLint & Prettier

## Prerequisites

- Node.js (v18 or higher)
- pnpm (or npm/yarn)
- Docker (for running a production-like container)

## Getting Started

1.  **Clone the repository:**
    ```sh
    git clone <repository-url>
    ```

2.  **Navigate to the admin app directory:**
    ```sh
    cd apps/admin
    ```

3.  **Install dependencies:**
    ```sh
    pnpm install
    ```

4.  **Set up environment variables:**
    Create a `.env.local` file in this directory by copying the `.env.example` file (if one exists). At a minimum, you will need to specify the backend API URL:
    ```
    VITE_API_BASE_URL=http://localhost:8080
    ```

5.  **Run the development server:**
    ```sh
    pnpm dev
    ```
    The application will be available at `http://localhost:3001`.

## Available Scripts

- `pnpm dev`: Starts the development server.
- `pnpm build`: Builds the application for production.
- `pnpm preview`: Serves the production build locally.
- `pnpm test`: Runs unit and integration tests with Vitest.
- `pnpm test:coverage`: Runs tests and generates a coverage report.
- `pnpm test:e2e`: Runs end-to-end tests with Playwright.
- `pnpm storybook`: Starts the Storybook component explorer.
- `pnpm build-storybook`: Builds a static version of the Storybook.
- `pnpm lint`: Lints the codebase.
- `pnpm docker:build`: Builds a production Docker image.
- `pnpm docker:run`: Runs the application inside a Docker container.

## Project Structure

```
apps/admin/
├── public/           # Static assets
├── src/
│   ├── components/   # Reusable React components
│   ├── hooks/        # Custom React hooks
│   ├── layouts/      # Page layout components (e.g., BasicLayout)
│   ├── lib/          # Core libraries (API client, logger, validation)
│   ├── mocks/        # MSW mock server setup for testing
│   ├── pages/        # Page components, organized by feature
│   ├── router/       # Routing configuration
│   ├── services/     # API service classes
│   ├── store/        # Zustand global state stores
│   ├── styles/       # Global styles
│   └── types/        # TypeScript type definitions
├── tests/            # Playwright E2E tests
├── .dockerignore
├── .eslintrc.cjs
├── Dockerfile
├── nginx.conf
├── package.json
├── playwright.config.ts
├── tsconfig.json
└── vite.config.ts
```

## Architectural Notes

- **State Management**: We use a hybrid approach.
    - **Zustand** is used for simple, global UI state like authentication status.
    - **React Query** is used for all server state management, including caching, background refetching, and optimistic updates. This is the preferred way to handle data from the API.
- **Styling**: We leverage the powerful component library of Ant Design and its theming capabilities. Custom styles are minimal.
- **Testing**: The project has a multi-layered testing strategy. Please add tests for any new features you build.
    - **Unit/Integration**: Vitest + React Testing Library + MSW
    - **E2E**: Playwright
    - **Component Docs**: Storybook

---
*This README was last updated on 2025-01-23.* 