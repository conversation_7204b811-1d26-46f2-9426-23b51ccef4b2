﻿好的，遵照您的指示，我们来生成一份为 `digital-twin-service` (数字孪生/虚拟分身服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **资产与库存系统**: 详细定义平台资产目录（Catalog）和用户库存（Inventory）的分离，支持UGC资产和与`billing-service`集成的商业化资产。
2.  **Avatar外观数据结构**: 引入更专业、更灵活的数据结构来描述Avatar外观，使其能支持模块化换装和与多种3D渲染引擎（如Unity, Unreal）的兼容性。
3.  **Persona（个性）的演进**: 明确Persona数据的来源（用户输入、AI分析）、结构，以及其如何驱动数字孪生的行为。
4.  **与`metaverse-engine-service`的深度协同**: 详细描述两者在Avatar数据同步和状态管理上的交互协议。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、可扩展、且能为沉浸式体验和高级AI交互提供坚实数据基础的数字孪生系统。

---

### CINA.CLUB - digital-twin-service 需求规格说明书

**版本: 2.0 (生产级定义，集成高级资产系统与Persona演进)**  
**发布日期: 2025-06-22**  
**最后修订日期: 2025-06-22**  
**文档负责人:** [元宇宙/个性化团队负责人/产品经理]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台内提供更具沉浸感、个性化和未来感的交互体验，`digital-twin-service` 应运而生。其目的在于为每一位用户创建一个独特的、可高度定制的**数字孪生（Digital Twin）**。这个数字孪生不仅是用户在“海内灵境”虚拟世界中的视觉代表（**Avatar**），更是其**个性（Persona）**、偏好和成就的延伸，并为未来AI驱动的自主行为和高级社交互动奠定坚实的数据基础。

#### 1.2. 服务范围
本服务 **负责**:
*   **虚拟形象 (Avatar) 外观管理**:
    *   管理用户Avatar的**基础模型**和**捏脸参数(Blend Shapes)**。
    *   管理用户当前**装备(Equipped)**的虚拟资产配置。
*   **虚拟形象资产(Asset)系统**:
    *   维护一个平台级的**资产目录(Asset Catalog)**，包含所有可用的Avatar资产（服装、配饰、发型等）。
    *   管理每个用户的**个人库存(User Inventory)**，记录其已解锁或购买的资产。
    *   处理资产的解锁逻辑，与`gamification-service`和`billing-service`集成。
*   **个性特征 (Persona) 管理**:
    *   存储和管理反映用户特质的结构化数据，包括用户自述、AI从交互中学习到的特质、以及情绪状态指示器。
    *   提供API供AI服务查询和（经授权）更新Persona。
*   **与外部生态的集成**: （可选）支持与`Ready Player Me`等第三方Avatar平台的账户关联和数据同步。

本服务 **不负责**:
*   实时3D渲染 (由客户端负责)。
*   实时多人状态同步（位置、动作） (由 `metaverse-engine-service` 负责)。
*   虚拟物品的支付和账本 (由 `billing-service`和`cina-coin-ledger-service`负责)。
*   存储3D模型或贴图等二进制文件 (由 `file-storage-service` 负责)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 通过API定制Avatar，设置Persona。
*   **`metaverse-engine-service` (主要)**: 获取Avatar外观数据用于在虚拟世界中渲染。
*   **`ai-assistant-service`**: 查询Persona数据以提供个性化对话。
*   **`gamification-service`, `billing-service`**: (调用本服务) 通知本服务用户解锁/购买了新的Avatar资产。
*   **`user-core-service`**: (被本服务调用) 获取用户基础信息；本服务也可能被其调用以在用户资料页展示Avatar缩略图。

#### 1.4. 定义与缩略语
*   **Digital Twin**: 数字孪生，包含Avatar和Persona的完整用户虚拟化身。
*   **Avatar**: 用户的虚拟形象外观。
*   **Persona**: 数字孪生的“个性”和行为特质。
*   **Asset**: 构成Avatar的独立可穿戴/可替换部分，如一件T恤、一顶帽子。
*   **Slot**: Avatar身上可以装备Asset的位置，如`headwear`, `top`, `bottom`, `footwear`。
*   **Blend Shapes**: 用于控制3D模型面部表情或身体形态的参数。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`digital-twin-service`是用户数字身份的**可视化和个性化层**。它作为“**数字衣橱**”和“**个性档案室**”，连接用户的核心资料、成就和记忆，并将这些内在特质外化为一个可视、可交互的虚拟分身。它为`metaverse-engine-service`提供渲染数据，为`ai-assistant-service`提供个性化依据，是平台实现深度沉浸式体验和高级AI交互的基础数据服务。

#### 2.2. 主要功能概述
*   支持模块化换装和精细捏脸的Avatar定制系统。
*   分离的平台资产目录与用户个人库存。
*   可由用户和AI共同塑造和演进的Persona模型。
*   为元宇宙和AI提供高性能、标准化的数据接口。

### 3. 核心流程图

#### 3.1. Metaverse引擎获取并渲染用户Avatar流程
```mermaid
sequenceDiagram
    participant MetaverseEngine as ME
    participant DigitalTwinService as DTS
    participant UserCoreService as UCS
    participant FileStorageService as FSS
    
    Note over ME: User joins a virtual world instance.
    ME->>DTS: 1. GET /internal/users/{userId}/avatar-render-data
    
    DTS->>DB: 2. Get user's equipped asset list & blend shapes
    DTS->>UCS: 3. (Optional) Get latest user display name
    
    DTS-->>ME: 4. (Avatar Render Data: {base_model_key, blend_shapes, equipped_assets: [{slot, asset_key}]})
    
    loop For each asset_key
        ME->>FSS: 5. Request download URL for asset's 3D model file
        FSS-->>ME: (Presigned URL)
        ME->>ObjectStorage: 6. Download 3D model file (.glb/.fbx)
    end
    
    Note over ME: Client-side renderer assembles the avatar based on all data.
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 虚拟形象 (Avatar) 管理
*   **FR4.1.1 (基础模型)**: 系统支持多种基础男女体型模型。用户注册时自动分配一个默认模型。
*   **FR4.1.2 (捏脸系统)**: 用户必须能够通过调整一系列标准化的**Blend Shapes**参数（如眼睛大小、鼻子高度、嘴巴宽度）来精细化定制其面部特征。
*   **FR4.1.3 (模块化换装)**:
    *   Avatar被划分为多个**装备槽(Slot)**，如`hair`, `headwear`, `glasses`, `top`, `bottom`等。
    *   用户可以从其**个人库存**中，为每个Slot选择一个Asset进行装备。
*   **FR4.1.4 (配置快照)**: 用户的完整Avatar配置（基础模型、Blend Shapes、装备的Asset列表）必须被持久化存储。

#### 4.2. 虚拟形象资产 (Asset) 系统
*   **FR4.2.1 (资产目录)**: 系统必须维护一个平台级的`Asset Catalog`，包含所有平台官方提供的、或UGC创作者发布的资产。
    *   每个资产必须包含名称、描述、适用的Slot、3D模型文件的`storageKey`、缩略图等元数据。
*   **FR4.2.2 (资产商业化)**: 资产可以关联一个`UnlockCondition`，如：
    *   `FREE`:所有人可用。
    *   `GAMIFICATION`: 需要`gamification-service`授予的特定徽章。
    *   `PREMIUM`: 需要通过`billing-service`购买。
*   **FR4.2.3 (用户库存)**: 系统必须为每个用户维护一个`User Inventory`，记录其已拥有的所有资产ID。
*   **FR4.2.4 (资产解锁流程)**:
    *   本服务需提供内部API `POST /internal/users/{userId}/inventory/add-asset`。
    *   当`gamification-service`或`billing-service`的奖励/购买流程完成后，它们会调用此API，将新资产添加到用户库存中。

#### 4.3. 个性特征 (Persona) 管理
*   **FR4.3.1 (数据结构)**: `Persona`数据应为结构化JSON，包含多个维度：
    *   `self_description`: 用户自己填写的关键词或描述。
    *   `communication_style`: AI分析得出的沟通风格（如`formal`, `humorous`, `concise`）。
    *   `inferred_traits`: AI从用户行为（如PKB、聊天记录）中推断出的特质（如`tech_savvy`, `loves_pets`）。
    *   `emotional_state`: 一个动态的、表示当前情绪状态的指示器（如`happy`, `curious`）。
*   **FR4.3.2 (数据来源)**:
    *   用户可以通过设置页直接编辑`self_description`。
    *   `ai-assistant-service`在与用户交互后，可以调用本服务的内部API来更新`inferred_traits`和`communication_style`。
*   **FR4.3.3 (Persona驱动行为)**: `ai-assistant-service`和`metaverse-engine-service`（用于NPC互动）在与用户交互前，应查询其Persona数据，以调整自身的回复风格和行为。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部gRPC/RESTful API接口
*   **版本**: `/api/v1/digital-twin`
*   **认证**: User JWT, S2S Auth, Admin Role。
*   **核心端点**:
    *   **Avatar Management**:
        *   `GET /me/avatar`: 获取当前用户的完整Avatar配置。
        *   `PUT /me/avatar/base`: 更新基础模型或捏脸参数。Request: `{ base_model_id?, blend_shapes: {...} }`
        *   `POST /me/avatar/equip`: 装备资产。Request: `{ slot_id: "top", asset_id: "uuid" }`
    *   **Asset & Inventory**:
        *   `GET /assets/catalog?category=...&slot=...`: 查询平台资产目录。
        *   `GET /me/inventory`: 获取我的个人资产库存。
    *   **Persona**:
        *   `GET /me/persona`: 获取我的Persona数据。
        *   `PUT /me/persona/self-description`: 用户更新自我描述。
    *   **Internal S2S**:
        *   `GET /internal/users/{userId}/avatar-render-data`: (供`metaverse-engine-service`调用) 获取渲染所需的核心数据。
        *   `GET /internal/users/{userId}/persona`: (供`ai-assistant-service`调用) 获取Persona。
        *   `POST /internal/users/{userId}/inventory/add-asset`: (供`gamification`/`billing`调用) 为用户解锁资产。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL + JSONB or MongoDB)
*   **`avatars`**: `user_id (PK)`, `base_model_key`, `blend_shapes (JSONB)`.
*   **`equipped_assets`**: `user_id (PK)`, `slot_id (PK)`, `asset_id (FK)`.
*   **`asset_catalog`**: `id (PK)`, `name`, `description`, `slot_id`, `storage_key`, `thumbnail_url`, `unlock_condition (JSONB)`.
*   **`user_inventory`**: `(user_id, asset_id)` (PK).
*   **`personas`**: `user_id (PK)`, `persona_data (JSONB)`.

#### 6.2. 数据存储
*   **元数据**: **MongoDB** 或 **PostgreSQL + JSONB** 是理想选择，因为Avatar和Persona的数据结构非常灵活，可能会频繁变更。
*   **3D资产文件**: `file-storage-service` (对象存储)。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **`avatar-render-data` API**: 必须是极低延迟的，P99 < 50ms。因为它直接影响元宇宙场景的加载速度。
*   **其他CRUD API**: P99延迟 < 150ms。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。本服务的故障将导致元宇宙和部分AI个性化功能不可用。
*   **数据一致性**: 用户库存和装备状态必须保持强一致性。

#### 7.3. 可扩展性需求
*   服务可水平扩展。数据库需能应对大量用户的读写请求。

#### 7.4. 安全性需求
*   用户只能管理自己的数字孪生数据。
*   对用户上传的UGC资产（如果支持）需与`content-moderation-service`集成进行安全扫描和内容审核。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: MongoDB或PostgreSQL+JSONB，以应对灵活多变的数据结构。
*   **3D资产格式**: 推荐使用开放标准格式如**glTF/GLB**，它为网络传输和现代渲染引擎做了优化。
*   **集成**: 与`metaverse-engine-service`和`ai-assistant-service`的接口契约是本服务的核心，必须稳定且高效。

---
这份版本2.0的SRS文档为`digital-twin-service`构建了一个功能强大、可扩展、且面向未来的数字身份系统。它通过对Avatar和Persona的精细化管理，为CINA.CLUB平台提供沉浸式体验和深度个性化AI交互奠定了坚实的数据基础。