package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// MockBFFService implements port.BFFService for testing
type MockBFFService struct {
	mock.Mock
}

// Authentication and session management
func (m *MockBFFService) CreateSession(ctx context.Context, employee *model.Employee, ipAddress, userAgent string) (*model.AdminSession, error) {
	args := m.Called(ctx, employee, ipAddress, userAgent)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.AdminSession), args.Error(1)
}

func (m *MockBFFService) ValidateSession(ctx context.Context, sessionID string) (*model.AdminSession, error) {
	args := m.Called(ctx, sessionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.AdminSession), args.Error(1)
}

func (m *MockBFFService) RefreshSession(ctx context.Context, sessionID string) (*model.AdminSession, error) {
	args := m.Called(ctx, sessionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.AdminSession), args.Error(1)
}

func (m *MockBFFService) DestroySession(ctx context.Context, sessionID string) error {
	args := m.Called(ctx, sessionID)
	return args.Error(0)
}

func (m *MockBFFService) DestroyAllUserSessions(ctx context.Context, employeeID string) error {
	args := m.Called(ctx, employeeID)
	return args.Error(0)
}

// User management aggregated API
func (m *MockBFFService) GetUsers(ctx context.Context, filter port.UserFilter) (*port.UsersResponse, error) {
	args := m.Called(ctx, filter)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.UsersResponse), args.Error(1)
}

func (m *MockBFFService) GetUserFullProfile(ctx context.Context, userID string) (*port.UserFullProfileDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.UserFullProfileDTO), args.Error(1)
}

func (m *MockBFFService) SuspendUser(ctx context.Context, actorInfo *port.ActorInfo, userID string, reason string) error {
	args := m.Called(ctx, actorInfo, userID, reason)
	return args.Error(0)
}

func (m *MockBFFService) RestoreUser(ctx context.Context, actorInfo *port.ActorInfo, userID string, reason string) error {
	args := m.Called(ctx, actorInfo, userID, reason)
	return args.Error(0)
}

func (m *MockBFFService) UpdateUserStatus(ctx context.Context, actorInfo *port.ActorInfo, userID string, status string) error {
	args := m.Called(ctx, actorInfo, userID, status)
	return args.Error(0)
}

// Content management aggregated API
func (m *MockBFFService) GetModerationQueue(ctx context.Context, filter port.ModerationFilter) (*port.ModerationQueueResponse, error) {
	args := m.Called(ctx, filter)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.ModerationQueueResponse), args.Error(1)
}

func (m *MockBFFService) ApproveContent(ctx context.Context, actorInfo *port.ActorInfo, contentID string, reason string) error {
	args := m.Called(ctx, actorInfo, contentID, reason)
	return args.Error(0)
}

func (m *MockBFFService) RejectContent(ctx context.Context, actorInfo *port.ActorInfo, contentID string, reason string) error {
	args := m.Called(ctx, actorInfo, contentID, reason)
	return args.Error(0)
}

// Order and payment management aggregated API
func (m *MockBFFService) GetOrders(ctx context.Context, filter port.OrderFilter) (*port.OrdersResponse, error) {
	args := m.Called(ctx, filter)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.OrdersResponse), args.Error(1)
}

func (m *MockBFFService) GetOrderDetails(ctx context.Context, orderID string) (*port.OrderDetailsDTO, error) {
	args := m.Called(ctx, orderID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.OrderDetailsDTO), args.Error(1)
}

func (m *MockBFFService) CancelOrder(ctx context.Context, actorInfo *port.ActorInfo, orderID string, reason string) error {
	args := m.Called(ctx, actorInfo, orderID, reason)
	return args.Error(0)
}

func (m *MockBFFService) RefundOrder(ctx context.Context, actorInfo *port.ActorInfo, orderID string, amount float64, reason string) error {
	args := m.Called(ctx, actorInfo, orderID, amount, reason)
	return args.Error(0)
}

// Analytics and reporting aggregated API
func (m *MockBFFService) GetDashboardSummary(ctx context.Context) (*port.DashboardSummaryDTO, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.DashboardSummaryDTO), args.Error(1)
}

func (m *MockBFFService) GetUserAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.UserAnalyticsDTO, error) {
	args := m.Called(ctx, timeRange)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.UserAnalyticsDTO), args.Error(1)
}

func (m *MockBFFService) GetContentAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.ContentAnalyticsDTO, error) {
	args := m.Called(ctx, timeRange)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.ContentAnalyticsDTO), args.Error(1)
}

func (m *MockBFFService) GetRevenueAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.RevenueAnalyticsDTO, error) {
	args := m.Called(ctx, timeRange)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.RevenueAnalyticsDTO), args.Error(1)
}

// System management API
func (m *MockBFFService) GetSystemHealth(ctx context.Context) (*port.SystemHealthDTO, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.SystemHealthDTO), args.Error(1)
}

func (m *MockBFFService) GetAuditLogs(ctx context.Context, filter model.AuditLogFilter) (*port.AuditLogsResponse, error) {
	args := m.Called(ctx, filter)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.AuditLogsResponse), args.Error(1)
}

// Test functions

func TestBFFService_CreateSession(t *testing.T) {
	employee := &model.Employee{
		ID:       "emp123",
		Email:    "<EMAIL>",
		Name:     "Admin User",
		Roles:    []string{"admin"},
		IsActive: true,
	}

	expectedSession := &model.AdminSession{
		ID:         "session123",
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Name:       "Admin User",
		Roles:      []string{"admin"},
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		LastUsedAt: time.Now(),
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	t.Run("successful session creation", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("CreateSession", mock.Anything, employee, "***********", "Mozilla/5.0").
			Return(expectedSession, nil)

		session, err := service.CreateSession(context.Background(), employee, "***********", "Mozilla/5.0")

		require.NoError(t, err)
		assert.Equal(t, expectedSession.ID, session.ID)
		assert.Equal(t, expectedSession.EmployeeID, session.EmployeeID)
		assert.Equal(t, expectedSession.Email, session.Email)
	})

	t.Run("session creation error", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("CreateSession", mock.Anything, employee, "***********", "Mozilla/5.0").
			Return(nil, assert.AnError)

		session, err := service.CreateSession(context.Background(), employee, "***********", "Mozilla/5.0")

		assert.Error(t, err)
		assert.Nil(t, session)
	})
}

func TestBFFService_ValidateSession(t *testing.T) {
	service := &MockBFFService{}
	defer service.AssertExpectations(t)

	validSession := &model.AdminSession{
		ID:         "session123",
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Name:       "Admin User",
		Roles:      []string{"admin"},
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		LastUsedAt: time.Now(),
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	t.Run("valid session", func(t *testing.T) {
		service.On("ValidateSession", mock.Anything, "session123").
			Return(validSession, nil)

		session, err := service.ValidateSession(context.Background(), "session123")

		require.NoError(t, err)
		assert.Equal(t, "session123", session.ID)
		assert.Equal(t, "<EMAIL>", session.Email)
	})

	t.Run("invalid session", func(t *testing.T) {
		service.On("ValidateSession", mock.Anything, "invalid").
			Return(nil, assert.AnError)

		session, err := service.ValidateSession(context.Background(), "invalid")

		assert.Error(t, err)
		assert.Nil(t, session)
	})
}

func TestBFFService_GetUserFullProfile(t *testing.T) {
	service := &MockBFFService{}
	defer service.AssertExpectations(t)

	expectedProfile := &port.UserFullProfileDTO{
		ID:          "user123",
		Email:       "<EMAIL>",
		Username:    "testuser",
		DisplayName: "Test User",
		Status:      "active",
		Level:       5,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		IsVerified:  true,
		IsPremium:   false,
	}

	t.Run("successful profile retrieval", func(t *testing.T) {
		service.On("GetUserFullProfile", mock.Anything, "user123").
			Return(expectedProfile, nil)

		profile, err := service.GetUserFullProfile(context.Background(), "user123")

		require.NoError(t, err)
		assert.Equal(t, "user123", profile.ID)
		assert.Equal(t, "<EMAIL>", profile.Email)
		assert.Equal(t, "testuser", profile.Username)
	})

	t.Run("user not found", func(t *testing.T) {
		service.On("GetUserFullProfile", mock.Anything, "nonexistent").
			Return(nil, assert.AnError)

		profile, err := service.GetUserFullProfile(context.Background(), "nonexistent")

		assert.Error(t, err)
		assert.Nil(t, profile)
	})
}

func TestBFFService_SuspendUser(t *testing.T) {
	actorInfo := &port.ActorInfo{
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Roles:      []string{"admin"},
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	t.Run("successful user suspension", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("SuspendUser", mock.Anything, actorInfo, "user456", "Policy violation").
			Return(nil)

		err := service.SuspendUser(context.Background(), actorInfo, "user456", "Policy violation")

		assert.NoError(t, err)
	})

	t.Run("suspension error", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("SuspendUser", mock.Anything, actorInfo, "user456", "Policy violation").
			Return(assert.AnError)

		err := service.SuspendUser(context.Background(), actorInfo, "user456", "Policy violation")

		assert.Error(t, err)
	})
}

func TestBFFService_ApproveContent(t *testing.T) {
	actorInfo := &port.ActorInfo{
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Roles:      []string{"content_moderator"},
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	t.Run("successful content approval", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("ApproveContent", mock.Anything, actorInfo, "content789", "Content is appropriate").
			Return(nil)

		err := service.ApproveContent(context.Background(), actorInfo, "content789", "Content is appropriate")

		assert.NoError(t, err)
	})

	t.Run("approval error", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("ApproveContent", mock.Anything, actorInfo, "content789", "Content is appropriate").
			Return(assert.AnError)

		err := service.ApproveContent(context.Background(), actorInfo, "content789", "Content is appropriate")

		assert.Error(t, err)
	})
}

func TestBFFService_GetDashboardSummary(t *testing.T) {
	expectedSummary := &port.DashboardSummaryDTO{
		TotalUsers:        10000,
		ActiveUsers:       8500,
		NewUsersToday:     150,
		PremiumUsers:      1200,
		TotalContent:      50000,
		PendingModeration: 25,
		PublishedToday:    200,
		TotalRevenue:      150000.50,
		RevenueToday:      2500.75,
		SystemHealth:      "healthy",
		ActiveSessions:    850,
		ErrorRate:         0.02,
	}

	t.Run("successful dashboard summary retrieval", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("GetDashboardSummary", mock.Anything).
			Return(expectedSummary, nil)

		summary, err := service.GetDashboardSummary(context.Background())

		require.NoError(t, err)
		assert.Equal(t, int64(10000), summary.TotalUsers)
		assert.Equal(t, int64(8500), summary.ActiveUsers)
		assert.Equal(t, float64(150000.50), summary.TotalRevenue)
		assert.Equal(t, "healthy", summary.SystemHealth)
	})

	t.Run("dashboard summary error", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("GetDashboardSummary", mock.Anything).
			Return(nil, assert.AnError)

		summary, err := service.GetDashboardSummary(context.Background())

		assert.Error(t, err)
		assert.Nil(t, summary)
	})
}

func TestBFFService_GetUserAnalytics(t *testing.T) {
	timeRange := port.TimeRange{
		Start: time.Date(2025, 6, 16, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2025, 6, 23, 0, 0, 0, 0, time.UTC),
	}

	expectedAnalytics := &port.UserAnalyticsDTO{
		TimeRange: timeRange,
		NewUsers: []port.DailyMetric{
			{Date: time.Date(2025, 6, 16, 0, 0, 0, 0, time.UTC), Value: 50},
			{Date: time.Date(2025, 6, 17, 0, 0, 0, 0, time.UTC), Value: 45},
		},
		ActiveUsers: []port.DailyMetric{
			{Date: time.Date(2025, 6, 16, 0, 0, 0, 0, time.UTC), Value: 8500},
			{Date: time.Date(2025, 6, 17, 0, 0, 0, 0, time.UTC), Value: 8600},
		},
	}

	t.Run("successful user analytics retrieval", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("GetUserAnalytics", mock.Anything, timeRange).
			Return(expectedAnalytics, nil)

		analytics, err := service.GetUserAnalytics(context.Background(), timeRange)

		require.NoError(t, err)
		assert.Equal(t, timeRange, analytics.TimeRange)
		assert.Len(t, analytics.NewUsers, 2)
		assert.Equal(t, int64(50), analytics.NewUsers[0].Value)
	})

	t.Run("analytics retrieval error", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("GetUserAnalytics", mock.Anything, timeRange).
			Return(nil, assert.AnError)

		analytics, err := service.GetUserAnalytics(context.Background(), timeRange)

		assert.Error(t, err)
		assert.Nil(t, analytics)
	})
}

func TestBFFService_GetAuditLogs(t *testing.T) {
	startTime := time.Date(2025, 6, 16, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2025, 6, 23, 0, 0, 0, 0, time.UTC)

	filter := model.AuditLogFilter{
		ActorID:    "emp123",
		ResourceID: "user456",
		Action:     "login",
		StartTime:  &startTime,
		EndTime:    &endTime,
		Page:       1,
		PageSize:   20,
	}

	expectedLogs := &port.AuditLogsResponse{
		Logs: []*model.AuditLogEntry{
			{
				ID:         "log123",
				ActorID:    "emp123",
				ActorEmail: "<EMAIL>",
				Action:     "login",
				ResourceID: "user456",
				Timestamp:  time.Now(),
				ActorIP:    "***********",
				Success:    true,
			},
		},
		Total:      1,
		Page:       1,
		PageSize:   20,
		TotalPages: 1,
	}

	t.Run("successful audit logs retrieval", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("GetAuditLogs", mock.Anything, filter).
			Return(expectedLogs, nil)

		logs, err := service.GetAuditLogs(context.Background(), filter)

		require.NoError(t, err)
		assert.Len(t, logs.Logs, 1)
		assert.Equal(t, int64(1), logs.Total)
		assert.Equal(t, "log123", logs.Logs[0].ID)
	})

	t.Run("audit logs retrieval error", func(t *testing.T) {
		service := &MockBFFService{}
		defer service.AssertExpectations(t)

		service.On("GetAuditLogs", mock.Anything, filter).
			Return(nil, assert.AnError)

		logs, err := service.GetAuditLogs(context.Background(), filter)

		assert.Error(t, err)
		assert.Nil(t, logs)
	})
}

func TestBFFService_Integration(t *testing.T) {
	service := &MockBFFService{}
	defer service.AssertExpectations(t)

	// Test a complete workflow: create session -> validate -> suspend user -> get audit logs

	employee := &model.Employee{
		ID:       "emp123",
		Email:    "<EMAIL>",
		Name:     "Admin User",
		Roles:    []string{"admin"},
		IsActive: true,
	}

	session := &model.AdminSession{
		ID:         "session123",
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Name:       "Admin User",
		Roles:      []string{"admin"},
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		LastUsedAt: time.Now(),
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	actorInfo := &port.ActorInfo{
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Roles:      []string{"admin"},
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
	}

	// Mock expectations
	service.On("CreateSession", mock.Anything, employee, "***********", "Mozilla/5.0").
		Return(session, nil)
	service.On("ValidateSession", mock.Anything, "session123").
		Return(session, nil)
	service.On("SuspendUser", mock.Anything, actorInfo, "user456", "Policy violation").
		Return(nil)

	// Execute workflow
	ctx := context.Background()

	// Step 1: Create session
	createdSession, err := service.CreateSession(ctx, employee, "***********", "Mozilla/5.0")
	require.NoError(t, err)
	assert.Equal(t, "session123", createdSession.ID)

	// Step 2: Validate session
	validatedSession, err := service.ValidateSession(ctx, "session123")
	require.NoError(t, err)
	assert.Equal(t, "session123", validatedSession.ID)

	// Step 3: Suspend user
	err = service.SuspendUser(ctx, actorInfo, "user456", "Policy violation")
	require.NoError(t, err)
}

// Benchmark tests
func BenchmarkBFFService_ValidateSession(b *testing.B) {
	service := &MockBFFService{}

	session := &model.AdminSession{
		ID:         "session123",
		EmployeeID: "emp123",
		Email:      "<EMAIL>",
		Roles:      []string{"admin"},
		ExpiresAt:  time.Now().Add(24 * time.Hour),
	}

	service.On("ValidateSession", mock.Anything, "session123").
		Return(session, nil).Times(b.N)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service.ValidateSession(context.Background(), "session123")
	}
}
