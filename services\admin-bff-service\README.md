# Admin BFF Service

**Copyright (c) 2025 Cina.Club. All rights reserved.**

A Backend-for-Frontend (BFF) service that provides a unified, secure, and optimized API layer for the CINA.CLUB administration frontend. This service aggregates data from multiple microservices and implements comprehensive audit logging, session management, and role-based access control.

## 🏗️ Architecture

The service follows Clean Architecture principles with the following layers:

```
cmd/server/          # Application entry point
internal/
├── adapter/         # External interfaces layer
│   ├── cache/       # Redis session storage
│   ├── client/      # gRPC client adapters  
│   ├── logger/      # Audit logging implementation
│   └── transport/   # HTTP transport layer
│       └── http/    # Chi router, middleware, handlers
├── application/     # Application business logic
│   ├── port/        # Interface definitions
│   └── service/     # BFF orchestration service
└── domain/          # Core business entities
    └── model/       # Domain models and errors
```

## 🚀 Features

### Core Capabilities
- **API Aggregation**: Parallel calls to downstream services with data composition
- **Session Management**: Redis-based secure session storage with expiration
- **Audit Logging**: Comprehensive audit trail for all write operations
- **Role-Based Access Control**: Multi-level permission system
- **Request/Response Transformation**: gRPC to REST conversion with data filtering

### Security Features
- **Session Authentication**: HttpOnly cookie-based sessions
- **CORS Protection**: Configured for admin frontend domain
- **Request Sanitization**: Sensitive data redaction in audit logs
- **Rate Limiting**: Configurable request throttling
- **Input Validation**: Comprehensive request validation

### Operational Features
- **Health Checks**: Service health monitoring endpoints
- **Graceful Shutdown**: Clean resource cleanup
- **Structured Logging**: JSON format with configurable levels
- **Performance Monitoring**: Request timing and error tracking

## 📋 API Endpoints

### Authentication
- `GET /api/v1/admin/auth/me` - Get current user information
- `POST /api/v1/admin/auth/logout` - Destroy current session

### User Management (Requires UserManager role)
- `GET /api/v1/admin/users` - List users with filtering and pagination
- `GET /api/v1/admin/users/{id}/profile` - Get detailed user profile
- `POST /api/v1/admin/users/{id}/suspend` - Suspend user account
- `POST /api/v1/admin/users/{id}/restore` - Restore suspended user

### Content Moderation (Requires ContentModerator role)
- `GET /api/v1/admin/content/moderation-queue` - Get moderation queue
- `POST /api/v1/admin/content/tasks/{id}/approve` - Approve content
- `POST /api/v1/admin/content/tasks/{id}/reject` - Reject content

### Order Management (Requires BillingManager role)
- `GET /api/v1/admin/orders` - List orders with filtering
- `GET /api/v1/admin/orders/{id}` - Get order details
- `POST /api/v1/admin/orders/{id}/cancel` - Cancel order
- `POST /api/v1/admin/orders/{id}/refund` - Process refund

### Analytics (Requires Analyst role)
- `GET /api/v1/admin/analytics/dashboard` - Dashboard summary
- `GET /api/v1/admin/analytics/users` - User analytics
- `GET /api/v1/admin/analytics/content` - Content analytics
- `GET /api/v1/admin/analytics/revenue` - Revenue analytics

### System Management (Requires SystemAdmin role)
- `GET /api/v1/admin/system/health` - System health status
- `GET /api/v1/admin/system/audit-logs` - Audit log retrieval

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | HTTP server port | `8080` |
| `ENVIRONMENT` | Runtime environment | `development` |
| `LOG_LEVEL` | Logging level | `info` |
| `LOG_FORMAT` | Log format (json/text) | `json` |
| `REDIS_ADDRESS` | Redis server address | `localhost:6379` |
| `REDIS_PASSWORD` | Redis password | `` |
| `REDIS_DB` | Redis database number | `0` |

### Service Endpoints
- `USER_CORE_SERVICE_ADDRESS`
- `BILLING_SERVICE_ADDRESS`
- `SOCIAL_SERVICE_ADDRESS`
- `CONTENT_MODERATION_SERVICE_ADDRESS`
- `SERVICE_OFFERING_SERVICE_ADDRESS`
- `ANALYTICS_SERVICE_ADDRESS`
- `NOTIFICATION_DISPATCH_SERVICE_ADDRESS`

## 🏃‍♂️ Running the Service

### Prerequisites
- Go 1.21+
- Redis server
- Access to downstream microservices

### Development
```bash
# Install dependencies
go mod tidy

# Run tests
go test -v ./...

# Build and run
go build -o bin/admin-bff-service ./cmd/server
./bin/admin-bff-service
```

### Docker
```bash
# Build image
docker build -t admin-bff-service .

# Run container
docker run -p 8080:8080 \
  -e REDIS_ADDRESS=redis:6379 \
  -e USER_CORE_SERVICE_ADDRESS=user-core:50051 \
  admin-bff-service
```

## 🧪 Testing

### Unit Tests
```bash
# Run all tests
go test -v ./...

# Run with coverage
go test -v -cover ./...

# Run specific package tests
go test -v ./internal/application/service
```

### Integration Tests
```bash
# Run with test environment
ENVIRONMENT=test go test -v ./...
```

### Load Testing
```bash
# Use the provided benchmark tests
go test -bench=. -benchmem ./internal/adapter/transport/http
```

## 📊 Monitoring

### Health Endpoints
- `GET /health` - Basic service health check
- `GET /api/v1/admin/system/health` - Detailed system health

### Metrics
- Request latency (P50, P95, P99)
- Error rates by endpoint
- Session creation/validation rates
- Downstream service response times

### Audit Logging
All write operations are automatically logged with:
- Actor information (employee ID, email, roles)
- Request details (method, path, body)
- Response information (status, timing)
- Resource identification
- IP address and user agent

## 🔒 Security Considerations

### Session Security
- Sessions are stored in Redis with TTL
- HttpOnly cookies prevent XSS attacks
- Secure flag ensures HTTPS-only transmission
- SameSite protection against CSRF

### Input Validation
- All request parameters are validated
- JSON payloads are sanitized
- SQL injection protection via parameterized queries
- File upload restrictions (if applicable)

### Audit Trail
- Immutable audit logs
- Sensitive data redaction
- Distributed tracing support
- Compliance-ready formatting

## 🚀 Deployment

### Production Checklist
- [ ] Configure proper SSL certificates
- [ ] Set up Redis clustering for HA
- [ ] Configure log aggregation
- [ ] Set up monitoring and alerting
- [ ] Configure backup procedures
- [ ] Test disaster recovery procedures

### Scaling Considerations
- Service is stateless and horizontally scalable
- Redis session store can be clustered
- gRPC connections are pooled and reused
- Consider API gateway for load balancing

## 🤝 Contributing

### Code Style
- Follow Go standard formatting (`go fmt`)
- Use meaningful variable and function names
- Add comprehensive unit tests for new features
- Update documentation for API changes

### Pull Request Process
1. Create feature branch from main
2. Implement changes with tests
3. Update documentation
4. Submit pull request with description
5. Ensure CI/CD pipeline passes

## 📄 License

Copyright (c) 2025 Cina.Club. All rights reserved.

This software is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited. 