/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package crypto

import (
	"crypto/rand"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"

	"golang.org/x/crypto/pbkdf2"
)

const (
	// DefaultSaltSize is the default salt size in bytes
	DefaultSaltSize = 32
	// DefaultIterations is the default number of PBKDF2 iterations
	DefaultIterations = 100000
	// DefaultKeyLength is the default derived key length in bytes
	DefaultKeyLength = 32
)

// HashPassword generates a secure hash of the given password using PBKDF2-SHA256.
// It automatically generates a random salt and returns the hash in a format that
// includes all necessary parameters for verification.
//
// The returned hash format is: pbkdf2_sha256$iterations$salt$hash
// All components are base64 encoded for safe storage.
//
// Example:
//
//	hash, err := crypto.HashPassword("my-secret-password")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	// hash is now safe to store in database
func HashPassword(password string) (string, error) {
	// Generate random salt
	salt := make([]byte, DefaultSaltSize)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %w", err)
	}

	// Derive key using PBKDF2
	key := pbkdf2.Key([]byte(password), salt, DefaultIterations, DefaultKeyLength, sha256.New)

	// Encode components to base64
	saltB64 := base64.StdEncoding.EncodeToString(salt)
	keyB64 := base64.StdEncoding.EncodeToString(key)

	// Format: algorithm$iterations$salt$hash
	hash := fmt.Sprintf("pbkdf2_sha256$%d$%s$%s", DefaultIterations, saltB64, keyB64)
	return hash, nil
}

// CheckPasswordHash verifies that the given password matches the stored hash.
// Uses constant-time comparison to prevent timing attacks.
//
// Example:
//
//	isValid := crypto.CheckPasswordHash("my-secret-password", storedHash)
//	if isValid {
//	    // Password is correct
//	}
func CheckPasswordHash(password, hash string) bool {
	// Parse the hash format: algorithm$iterations$salt$hash
	parts := strings.Split(hash, "$")
	if len(parts) != 4 {
		return false
	}

	algorithm := parts[0]
	if algorithm != "pbkdf2_sha256" {
		return false
	}

	// Parse iterations
	iterations, err := strconv.Atoi(parts[1])
	if err != nil {
		return false
	}

	// Decode salt
	salt, err := base64.StdEncoding.DecodeString(parts[2])
	if err != nil {
		return false
	}

	// Decode stored hash
	storedKey, err := base64.StdEncoding.DecodeString(parts[3])
	if err != nil {
		return false
	}

	// Derive key from provided password using same parameters
	derivedKey := pbkdf2.Key([]byte(password), salt, iterations, len(storedKey), sha256.New)

	// Use constant-time comparison to prevent timing attacks
	return subtle.ConstantTimeCompare(derivedKey, storedKey) == 1
}

// HashPasswordWithParams allows customizing the PBKDF2 parameters.
// Use this when you need different security parameters than the defaults.
//
// Example:
//
//	hash, err := crypto.HashPasswordWithParams("password", 32, 150000, 64)
func HashPasswordWithParams(password string, saltSize, iterations, keyLength int) (string, error) {
	// Generate random salt
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %w", err)
	}

	// Derive key using PBKDF2
	key := pbkdf2.Key([]byte(password), salt, iterations, keyLength, sha256.New)

	// Encode components to base64
	saltB64 := base64.StdEncoding.EncodeToString(salt)
	keyB64 := base64.StdEncoding.EncodeToString(key)

	// Format: algorithm$iterations$salt$hash
	hash := fmt.Sprintf("pbkdf2_sha256$%d$%s$%s", iterations, saltB64, keyB64)
	return hash, nil
}

// IsValidPasswordHash checks if the given string is a valid password hash format.
// This is useful for validating stored hashes before attempting verification.
//
// Example:
//
//	if crypto.IsValidPasswordHash(storedHash) {
//	    isValid := crypto.CheckPasswordHash(password, storedHash)
//	}
func IsValidPasswordHash(hash string) bool {
	parts := strings.Split(hash, "$")
	if len(parts) != 4 {
		return false
	}

	algorithm := parts[0]
	if algorithm != "pbkdf2_sha256" {
		return false
	}

	// Check if iterations is a valid number
	if _, err := strconv.Atoi(parts[1]); err != nil {
		return false
	}

	// Check if salt is valid base64
	if _, err := base64.StdEncoding.DecodeString(parts[2]); err != nil {
		return false
	}

	// Check if hash is valid base64
	if _, err := base64.StdEncoding.DecodeString(parts[3]); err != nil {
		return false
	}

	return true
}
