/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package tracing

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultConfig(t *testing.T) {
	cfg := DefaultConfig("test-service")

	assert.Equal(t, "test-service", cfg.ServiceName)
	assert.True(t, cfg.Enabled)
	assert.Equal(t, "development", cfg.Environment)
	assert.Equal(t, "stdout", cfg.Exporter.Type)
	assert.Equal(t, "parent_based_trace_id_ratio", cfg.Sampler.Type)
	assert.Equal(t, 1.0, cfg.Sampler.Param)
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name    string
		config  Config
		wantErr bool
	}{
		{
			name:    "valid config",
			config:  DefaultConfig("test-service"),
			wantErr: false,
		},
		{
			name: "missing service name",
			config: Config{
				Enabled: true,
			},
			wantErr: true,
		},
		{
			name: "invalid sampler param",
			config: Config{
				ServiceName: "test",
				Enabled:     true,
				Exporter: ExporterConfig{
					Type: "stdout",
				},
				Sampler: SamplerConfig{
					Type:  "trace_id_ratio",
					Param: 1.5, // invalid: > 1.0
				},
			},
			wantErr: true,
		},
		{
			name: "invalid exporter type",
			config: Config{
				ServiceName: "test",
				Enabled:     true,
				Exporter: ExporterConfig{
					Type: "invalid",
				},
				Sampler: SamplerConfig{
					Type:  "always_on",
					Param: 0.5,
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestInit(t *testing.T) {
	tests := []struct {
		name    string
		config  Config
		wantErr bool
	}{
		{
			name:    "disabled tracing",
			config:  TestConfig("test-service"),
			wantErr: false,
		},
		{
			name:    "enabled tracing",
			config:  DevelopmentConfig("test-service"),
			wantErr: false,
		},
		{
			name: "invalid config",
			config: Config{
				Enabled: true,
				// missing required fields
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			shutdown, err := Init(tt.config)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, shutdown)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, shutdown)

				// Test shutdown function
				err = shutdown(context.Background())
				assert.NoError(t, err)
			}
		})
	}
}

func TestGetTracer(t *testing.T) {
	tracer := GetTracer("test-tracer")
	// Currently returns nil, but should not panic
	assert.Nil(t, tracer)
}

func TestIsEnabled(t *testing.T) {
	// Currently always returns false
	assert.False(t, IsEnabled())
}

func TestStartSpan(t *testing.T) {
	ctx := context.Background()
	newCtx, span := StartSpan(ctx, "test-span")

	assert.NotNil(t, newCtx)
	assert.Nil(t, span) // Currently returns nil
}

func TestAddAttributes(t *testing.T) {
	ctx := context.Background()
	attrs := map[string]interface{}{
		"key1": "value1",
		"key2": 42,
	}

	// Should not panic
	AddAttributes(ctx, attrs)
}

func TestSetStatus(t *testing.T) {
	ctx := context.Background()

	// Should not panic
	SetStatus(ctx, "OK", "success")
	SetStatus(ctx, "ERROR", "something went wrong")
}

func TestRecordError(t *testing.T) {
	ctx := context.Background()
	err := assert.AnError

	// Should not panic
	RecordError(ctx, err)
}

func TestPresetConfigs(t *testing.T) {
	serviceName := "test-service"

	t.Run("DevelopmentConfig", func(t *testing.T) {
		cfg := DevelopmentConfig(serviceName)
		assert.Equal(t, serviceName, cfg.ServiceName)
		assert.Equal(t, "development", cfg.Environment)
		assert.Equal(t, "stdout", cfg.Exporter.Type)
		assert.Equal(t, "always_on", cfg.Sampler.Type)
		assert.Equal(t, 1.0, cfg.Sampler.Param)
	})

	t.Run("ProductionConfig", func(t *testing.T) {
		endpoint := "http://jaeger:14268/api/traces"
		cfg := ProductionConfig(serviceName, endpoint)
		assert.Equal(t, serviceName, cfg.ServiceName)
		assert.Equal(t, "production", cfg.Environment)
		assert.Equal(t, "jaeger", cfg.Exporter.Type)
		assert.Equal(t, endpoint, cfg.Exporter.Endpoint)
		assert.Equal(t, "parent_based_trace_id_ratio", cfg.Sampler.Type)
		assert.Equal(t, 0.1, cfg.Sampler.Param)
		assert.False(t, cfg.Exporter.Insecure)
	})

	t.Run("TestConfig", func(t *testing.T) {
		cfg := TestConfig(serviceName)
		assert.Equal(t, serviceName, cfg.ServiceName)
		assert.Equal(t, "test", cfg.Environment)
		assert.False(t, cfg.Enabled)
	})
}

// BenchmarkInit 测试初始化性能
func BenchmarkInit(b *testing.B) {
	cfg := DevelopmentConfig("benchmark-service")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		shutdown, err := Init(cfg)
		require.NoError(b, err)
		require.NotNil(b, shutdown)
		_ = shutdown(context.Background())
	}
}

// BenchmarkStartSpan 测试 span 创建性能
func BenchmarkStartSpan(b *testing.B) {
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, span := StartSpan(ctx, "benchmark-span")
		_ = span
	}
}

// TestConfigYAMLCompatibility 测试配置与 YAML 的兼容性
func TestConfigYAMLCompatibility(t *testing.T) {
	// 验证所有配置字段都有正确的 mapstructure 标签
	cfg := DefaultConfig("test")

	// 这些字段应该有 mapstructure 标签
	assert.NotEmpty(t, cfg.ServiceName)
	assert.NotEmpty(t, cfg.Exporter.Type)
	assert.NotEmpty(t, cfg.Sampler.Type)
}

// TestThreadSafety 测试线程安全性
func TestThreadSafety(t *testing.T) {
	cfg := DevelopmentConfig("concurrent-test")

	// 启动多个 goroutine 同时初始化
	const numGoroutines = 10
	results := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			shutdown, err := Init(cfg)
			if err != nil {
				results <- err
				return
			}

			// 测试其他并发操作
			_ = GetTracer("concurrent-tracer")
			_ = IsEnabled()

			_, _ = StartSpan(context.Background(), "concurrent-span")
			AddAttributes(context.Background(), map[string]interface{}{"test": "value"})

			if shutdown != nil {
				_ = shutdown(context.Background())
			}

			results <- nil
		}()
	}

	// 等待所有 goroutine 完成
	for i := 0; i < numGoroutines; i++ {
		err := <-results
		assert.NoError(t, err)
	}
}
