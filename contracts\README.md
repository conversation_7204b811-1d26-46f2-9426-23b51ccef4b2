This directory contains the smart contracts for the CINA.CLUB platform, developed using the Foundry framework.

## Project Structure

- `src/`: Contains the Solidity source code for the smart contracts.
- `test/`: Contains the tests for the smart contracts, written in Solidity.
- `script/`: Contains Solidity scripts for deployment and interaction.
- `lib/`: Contains dependencies (git submodules).
- `foundry.toml`: Foundry configuration file.
- `.env.example`: Example environment variables file.
- `remappings.txt`: Import remappings for Solidity.

## Getting Started

### Prerequisites

- [Foundry](https://getfoundry.sh/)

### Installation

1.  Install the dependencies:
    ```bash
    forge install
    ```

### Compilation

```bash
forge build
```

### Testing

```bash
forge test
```

### Deployment

1.  Copy `.env.example` to `.env` and fill in the required values.
2.  Run the deployment script. Replace `sepolia` with your target network if needed.

    ```bash
    source .env
    forge script script/Deploy.s.sol --rpc-url $SEPOLIA_RPC_URL --broadcast --verify
    ``` 