# CINA.CLUB - 技术雷达

**文档状态**: 动态更新 (Living Document)  
**版本**: 2025-Q2  
**最后修订日期**: 2025-06-27

## 1. 引言

本文档使用ThoughtWorks技术雷达的形式，展示CINA.CLUB平台当前的技术选型状态和未来方向。它为技术决策提供了一个宏观框架，并指导团队在技术探索和应用上的投入。雷达分为四个环：

*   **ADOPT (采纳)**: 这是我们强烈推荐并已标准化的技术。所有新项目都应优先考虑使用它们。
*   **TRIAL (试验)**: 这些技术已在生产环境的某个非关键项目中得到验证，具有成为核心技术的潜力。团队应积极尝试并分享经验。
*   **ASSESS (评估)**: 这些是值得关注的新兴技术。我们鼓励团队进行技术预研（PoC）和学习，以评估其是否适合我们的平台。
*   **HOLD (暂缓)**: 这些技术因存在问题（如性能、安全、社区活跃度）或已有更好的替代品，团队应避免在新项目中使用，并考虑从现有项目中逐步移除。

---

## 2. 技术雷达详情

### 2.1 语言与框架 (Languages & Frameworks)

*   **ADOPT**:
    *   **Go (1.22+)**: 后端和核心逻辑的唯一主力语言。
    *   **React Native**: 移动端UI开发的核心框架。
    *   **TypeScript**: 前端开发和后端配置脚本的强制语言。
    *   **Go Mobile**: 连接Go核心逻辑与原生移动端的关键技术。
    *   **WebAssembly (WASM)**: 连接Go核心逻辑与Web端的关键技术。
    *   **Svelte/SvelteKit**: Web端UI开发的首选框架，因其性能和与WASM的良好集成。

*   **TRIAL**:
    *   **Flutter**: (用于特定高性能UI场景) 作为React Native的备选方案，在需要极致自定义渲染和动画的场景下进行试验。

*   **ASSESS**:
    *   **Rust**: 评估其在极度性能敏感或安全要求极高的领域（如密码学库）作为Go的补充。

*   **HOLD**:
    *   **Angular / AngularJS**: 技术栈过于庞大，与我们轻量、灵活的原则不符。
    *   **Cordova / Ionic**: 性能无法满足我们对原生体验的要求。

### 2.2 基础设施与平台 (Infrastructure & Platforms)

*   **ADOPT**:
    *   **Kubernetes (EKS/GKE)**: 应用部署和编排的基石。
    *   **Docker**: 容器化的标准。
    *   **Terraform**: 基础设施即代码(IaC)的标准工具。
    *   **Kafka (AWS MSK)**: 平台事件总线。
    *   **PostgreSQL (AWS RDS)**: 关系型数据库的首选。
    *   **Redis (AWS ElastiCache)**: 缓存和运行时状态存储。
    *   **Elasticsearch / OpenSearch**: 统一搜索引擎。
    *   **GitHub Actions**: CI/CD的标准平台。

*   **TRIAL**:
    *   **Temporal / Cadence**: 用于编排极其复杂的、有状态的Saga事务（如退款、多年订阅）。
    *   **Vector Databases (e.g., Pinecone, Weaviate)**: 用于AI和语义搜索的专用向量存储。

*   **ASSESS**:
    *   **Service Mesh (e.g., Istio, Linkerd)**: 评估其在服务治理、mTLS和可观测性上的价值，作为当前手动实现S2S认证和追踪的潜在替代。

*   **HOLD**:
    *   **Docker Swarm / Mesos**: 已被Kubernetes完全取代。
    *   **RabbitMQ**: 对于平台级的事件总线，Kafka的高吞吐量和持久性更具优势。

### 2.3 数据管理 (Data Management)

*   **ADOPT**:
    *   **Protobuf**: API和事件契约的唯一标准。
    *   **gRPC**: 服务间同步通信的标准。
    *   **pgx**: Go与PostgreSQL交互的高性能首选库。
    *   **GORM**: (有条件采纳) 仅在简单的CRUD服务中使用，以提高开发效率。复杂查询仍推荐使用pgx。
    *   **dbt**: 数据仓库中数据转换和建模的标准工具。
    *   **Airflow**: 数据ETL工作流的编排工具。

*   **TRIAL**:
    *   **TiDB / CockroachDB**: 用于需要水平扩展的关系型数据场景（如`social-service`的未来演进）。
    *   **Graph Databases (e.g., Neo4j)**: 用于`family-tree-service`或复杂社交推荐的图谱查询。

*   **ASSESS**:
    *   **Apache Flink / Spark Streaming**: 评估其在实时数据分析和反欺诈场景中的应用。

*   **HOLD**:
    *   **XML / SOAP**: 完全由Protobuf/gRPC/REST替代。

### 2.4 工具与库 (Tools & Libraries)

*   **ADOPT**:
    *   **Buf**: Protobuf的Lint、代码生成和版本管理的标准工具。
    *   **Turborepo**: 前端Monorepo管理的标准工具。
    *   **Go Workspaces**: 后端Monorepo中多模块管理的方式。
    *   **OpenTelemetry**: 分布式追踪和指标的开放标准。
    *   **Prometheus & Grafana**: 监控和告警的标准组合。
    *   **`spf13/cobra`**: 构建CLI工具的标准库。
    *   **`golang-jwt/jwt`**: JWT处理。
    *   **`golang.org/x/crypto`**: 密码学操作。

*   **TRIAL**:
    *   **`go-playground/validator`**: 在`pkg/config`之外，探索其在API输入验证中的更广泛应用。

*   **ASSESS**:
    *   **eBPF (e.g., Cilium, Pixie)**: 评估其在网络层面和服务间提供更深层次、无侵入的可观测性的能力。

*   **HOLD**:
    *   **dep / godep**: 已被Go Modules完全取代。
    *   **jQuery**: 不适用于我们的现代前端架构。