# CINA.CLUB Platform - Automated Backup and Restore System
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: v1
kind: Namespace
metadata:
  name: backup-system
  labels:
    app.kubernetes.io/name: backup-system
    app.kubernetes.io/component: automation
    platform: cina-club

---
# Velero Backup System
apiVersion: apps/v1
kind: Deployment
metadata:
  name: velero
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: velero
      app.kubernetes.io/component: backup
  template:
    metadata:
      labels:
        app.kubernetes.io/name: velero
        app.kubernetes.io/component: backup
    spec:
      serviceAccountName: velero
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
        - name: velero
          image: velero/velero:v1.12.1
          command:
            - /velero
          args:
            - server
            - --log-level=info
            - --log-format=json
            - --default-backup-storage-location=default
            - --default-volume-snapshot-locations=default
            - --backup-sync-period=1h
            - --restore-resource-priorities=namespace,storageclass,pv,pvc,secrets,configmaps,serviceaccounts,limitranges,resourcequotas
          env:
            - name: VELERO_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: LD_LIBRARY_PATH
              value: /plugins
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
          volumeMounts:
            - name: plugins
              mountPath: /plugins
            - name: scratch
              mountPath: /scratch
      volumes:
        - name: plugins
          emptyDir: {}
        - name: scratch
          emptyDir: {}

---
# Velero Service Account and RBAC
apiVersion: v1
kind: ServiceAccount
metadata:
  name: velero
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup
rules:
  # Core resources for backup
  - apiGroups: [""]
    resources: ["*"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  
  # All API groups for comprehensive backup
  - apiGroups: ["*"]
    resources: ["*"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: velero
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: velero
subjects:
  - kind: ServiceAccount
    name: velero
    namespace: backup-system

---
# Backup Storage Location
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: default
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-storage
spec:
  provider: aws  # Change based on your cloud provider
  storageType:
    objectStorage:
      bucket: cina-club-backups
      prefix: kubernetes
  config:
    region: us-west-2
    # s3ForcePathStyle: "false"
    # s3Url: https://s3.amazonaws.com

---
# Volume Snapshot Location
apiVersion: velero.io/v1
kind: VolumeSnapshotLocation
metadata:
  name: default
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: volume-snapshots
spec:
  provider: aws  # Change based on your cloud provider
  config:
    region: us-west-2

---
# Daily Backup Schedule for System Components
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-system-backup
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-schedule
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  template:
    metadata:
      labels:
        backup-type: system
        schedule: daily
    spec:
      includedNamespaces:
        - kong-system
        - monitoring
        - logging
        - cert-manager
        - observability
      excludedResources:
        - pods
        - events
      storageLocation: default
      volumeSnapshotLocations:
        - default
      ttl: 720h  # 30 days retention

---
# Weekly Full Backup Schedule
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: weekly-full-backup
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: backup-schedule
spec:
  schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM
  template:
    metadata:
      labels:
        backup-type: full
        schedule: weekly
    spec:
      storageLocation: default
      volumeSnapshotLocations:
        - default
      ttl: 2160h  # 90 days retention

---
# Backup Monitoring and Alerting
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: velero-backup-monitor
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: monitoring
    team: platform
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: velero
  endpoints:
  - port: monitoring
    path: /metrics
    interval: 30s

---
# Prometheus Rules for Backup Alerts
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: backup-alerts
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: alerts
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: backup.rules
    rules:
    - alert: BackupJobFailed
      expr: |
        velero_backup_failure_total > 0
      for: 5m
      labels:
        severity: critical
        component: backup
      annotations:
        summary: "Backup job failed"
        description: "Velero backup job has failed. Check backup system immediately."
    
    - alert: BackupJobMissing
      expr: |
        time() - velero_backup_last_successful_timestamp > 86400
      for: 10m
      labels:
        severity: warning
        component: backup
      annotations:
        summary: "No successful backup in 24 hours"
        description: "No successful backup has been completed in the last 24 hours."
    
    - alert: BackupStorageSpaceHigh
      expr: |
        velero_backup_size_bytes / velero_backup_storage_capacity_bytes > 0.8
      for: 15m
      labels:
        severity: warning
        component: backup
      annotations:
        summary: "Backup storage space running low"
        description: "Backup storage is over 80% full. Consider cleanup or expansion."

---
# Backup Restoration Job Template
apiVersion: batch/v1
kind: Job
metadata:
  name: restore-job-template
  namespace: backup-system
  labels:
    app.kubernetes.io/name: velero
    app.kubernetes.io/component: restore
  annotations:
    description: "Template for restoration jobs - modify as needed"
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/name: velero
        app.kubernetes.io/component: restore
    spec:
      restartPolicy: OnFailure
      serviceAccountName: velero
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
        - name: velero-restore
          image: velero/velero:v1.12.1
          command:
            - /velero
          args:
            - restore
            - create
            - --from-backup=BACKUP_NAME_HERE
            - --wait
          env:
            - name: VELERO_NAMESPACE
              value: backup-system
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi

---
# Config Drift Detection CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: config-drift-detector
  namespace: backup-system
  labels:
    app.kubernetes.io/name: config-drift-detector
    app.kubernetes.io/component: automation
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/name: config-drift-detector
            app.kubernetes.io/component: automation
        spec:
          restartPolicy: OnFailure
          serviceAccountName: config-drift-detector
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            fsGroup: 1000
          containers:
            - name: drift-detector
              image: bitnami/kubectl:1.28
              command:
                - /bin/bash
                - -c
                - |
                  echo "Starting configuration drift detection..."
                  
                  # Check Kong Gateway configuration
                  KONG_DESIRED_REPLICAS=3
                  KONG_ACTUAL_REPLICAS=$(kubectl get deployment kong-proxy -n kong-system -o jsonpath='{.spec.replicas}')
                  
                  if [ "$KONG_ACTUAL_REPLICAS" != "$KONG_DESIRED_REPLICAS" ]; then
                    echo "DRIFT DETECTED: Kong proxy replicas: expected=$KONG_DESIRED_REPLICAS, actual=$KONG_ACTUAL_REPLICAS"
                    kubectl annotate deployment kong-proxy -n kong-system config-drift="replicas-mismatch-$(date -u +%Y%m%d%H%M%S)" --overwrite
                  fi
                  
                  # Check if all required namespaces exist
                  REQUIRED_NAMESPACES="kong-system monitoring logging cert-manager observability"
                  for ns in $REQUIRED_NAMESPACES; do
                    if ! kubectl get namespace "$ns" >/dev/null 2>&1; then
                      echo "DRIFT DETECTED: Missing required namespace: $ns"
                    fi
                  done
                  
                  # Check resource quotas and limits
                  echo "Checking resource quotas and limits..."
                  kubectl get pods --all-namespaces -o jsonpath='{range .items[*]}{.metadata.namespace}{"\t"}{.metadata.name}{"\t"}{.spec.containers[*].resources}{"\n"}{end}' | grep -v "requests\|limits" || echo "DRIFT DETECTED: Pods without resource limits found"
                  
                  # Check security policies
                  echo "Checking security policies..."
                  kubectl get pods --all-namespaces -o jsonpath='{range .items[*]}{.metadata.namespace}{"\t"}{.metadata.name}{"\t"}{.spec.securityContext.runAsRoot}{"\n"}{end}' | grep "true" && echo "DRIFT DETECTED: Pods running as root found"
                  
                  echo "Configuration drift detection completed."
              env:
                - name: KUBECONFIG
                  value: /var/run/secrets/kubernetes.io/serviceaccount
              resources:
                requests:
                  cpu: 50m
                  memory: 128Mi
                limits:
                  cpu: 200m
                  memory: 256Mi

---
# Service Account for Config Drift Detector
apiVersion: v1
kind: ServiceAccount
metadata:
  name: config-drift-detector
  namespace: backup-system
  labels:
    app.kubernetes.io/name: config-drift-detector
    app.kubernetes.io/component: automation

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: config-drift-detector
  labels:
    app.kubernetes.io/name: config-drift-detector
    app.kubernetes.io/component: automation
rules:
  # Read-only access for drift detection
  - apiGroups: [""]
    resources: ["namespaces", "pods", "services", "configmaps", "secrets"]
    verbs: ["get", "list", "watch"]
  
  - apiGroups: ["apps"]
    resources: ["deployments", "daemonsets", "statefulsets"]
    verbs: ["get", "list", "watch"]
  
  # Annotation updates for drift marking
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: ["patch", "update"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: config-drift-detector
  labels:
    app.kubernetes.io/name: config-drift-detector
    app.kubernetes.io/component: automation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: config-drift-detector
subjects:
  - kind: ServiceAccount
    name: config-drift-detector
    namespace: backup-system 