# CINA.CLUB Android Application

*Copyright (c) 2025 Cina.Club*  
*All rights reserved.*

## 项目概述

CINA.CLUB Android应用是基于CINA.CLUB平台总体SRS 8.0架构设计的企业级移动应用。它采用Go-Centric全栈架构理念，结合现代Android开发最佳实践，为用户提供安全、高效、功能丰富的移动端体验。

### 核心特性

- 🔐 **端到端加密 (E2EE)**: 基于Go核心的加密算法，确保数据安全
- 🤖 **本地AI推理**: 集成llama.cpp，支持端侧LLM推理
- 💬 **实时通信**: 企业级聊天和协作功能
- 📚 **智能知识库**: AI增强的个人知识管理系统
- 👥 **企业协作**: 完整的组织架构和工作流支持
- 🌐 **离线优先**: 核心功能支持离线使用
- 🎨 **现代UI**: 基于Jetpack Compose的声明式界面

## 技术架构

### 架构原则
- **Go-Centric核心**: 复用平台Go语言核心逻辑
- **Clean Architecture**: 清晰的分层架构设计
- **MVVM模式**: 响应式UI状态管理
- **模块化设计**: 功能模块独立开发和测试
- **依赖注入**: Hilt框架管理组件依赖

### 技术栈
```
┌─────────────────┬────────────────────────────────┐
│ 层级            │ 技术选型                        │
├─────────────────┼────────────────────────────────┤
│ UI层            │ Jetpack Compose                │
│ 表现层          │ MVVM + StateFlow + Hilt        │
│ 领域层          │ Use Cases + Repository Pattern │
│ 数据层          │ Room + gRPC + EncryptedPrefs   │
│ 核心逻辑        │ Go Mobile (JNI Bridge)         │
│ 网络通信        │ gRPC-Kotlin + OkHttp          │
│ 异步处理        │ Kotlin Coroutines + Flow      │
│ 安全存储        │ Android Keystore + E2EE        │
└─────────────────┴────────────────────────────────┘
```

## 项目结构

```
apps/android/
├── app/                          # 主应用模块
│   ├── src/main/java/com/cinaclub/android/
│   │   ├── ui/
│   │   │   ├── theme/           # 主题和样式
│   │   │   ├── navigation/      # 应用导航
│   │   │   ├── chat/           # 聊天界面
│   │   │   ├── contacts/       # 通讯录界面
│   │   │   ├── profile/        # 个人资料界面
│   │   │   ├── settings/       # 设置界面
│   │   │   ├── workbench/      # 工作台界面
│   │   │   └── ...
│   │   ├── MainActivity.kt      # 主活动
│   │   └── CinaClubApplication.kt
│   └── src/main/res/            # 资源文件
│       ├── values/strings.xml   # 字符串资源
│       ├── values/colors.xml    # 颜色定义
│       └── ...
├── core/                        # 核心库模块
│   ├── common/                  # 通用工具
│   │   └── src/main/java/com/cinaclub/core/common/
│   │       ├── Result.kt        # 统一结果包装
│   │       └── ...
│   ├── data/                    # 数据层核心
│   ├── domain/                  # 领域层核心
│   └── go-bridge/               # Go Mobile桥接
│       └── src/main/java/com/cinaclub/core/go_bridge/
│           ├── CryptoBridge.kt  # 加密桥接
│           ├── AICoreModule.kt  # AI核心模块
│           └── ...
├── feature/                     # 功能模块
│   ├── auth/                    # 认证功能
│   │   └── src/main/java/com/cinaclub/feature/auth/
│   │       ├── data/repository/ # 数据仓库
│   │       ├── domain/         # 领域模型
│   │       └── presentation/   # UI组件
│   ├── chat/                    # 聊天功能
│   ├── pkb/                     # 个人知识库
│   ├── ai-assistant/            # AI助手
│   └── ...
├── build-logic/                 # 构建逻辑
├── libs/                        # 外部库
│   └── core-go.aar             # Go Mobile编译产物
├── gradle/                      # Gradle配置
├── build.gradle.kts             # 项目构建配置
├── settings.gradle.kts          # 模块设置
├── ARCHITECTURE.md              # 架构文档
├── IMPLEMENTATION_SUMMARY.md    # 实现总结
└── README.md                    # 项目文档
```

## 快速开始

### 环境要求

- **Android Studio**: Arctic Fox (2020.3.1) 或更高版本
- **JDK**: 11 或更高版本
- **Android SDK**: API Level 24 (Android 7.0) 最低，API Level 34 (Android 14) 目标
- **Kotlin**: 1.9.0 或更高版本
- **Go**: 1.22+ (用于Go Mobile编译)

### 依赖工具

```bash
# Android Studio包含的工具
- Android SDK Platform Tools
- Android SDK Build Tools
- Android Emulator

# 外部工具
- Go Mobile (gomobile)
- Protocol Buffer Compiler (protoc)
- gRPC代码生成器
```

### 构建步骤

1. **克隆仓库**
```bash
git clone <repository-url>
cd 13_CINA.CLUB-Monorepo/apps/android
```

2. **配置Go Mobile** (如果需要重新编译Go库)
```bash
# 安装Go Mobile
go install golang.org/x/mobile/cmd/gomobile@latest
gomobile init

# 编译Go库
cd ../../core
gomobile bind -target=android -o ../apps/android/libs/core-go.aar .
```

3. **打开Android Studio**
```bash
# 打开Android项目
android-studio apps/android
```

4. **同步项目**
- 等待Gradle同步完成
- 下载所需依赖

5. **运行应用**
- 选择设备或模拟器
- 点击运行按钮或使用快捷键 `Shift + F10`

### 配置说明

#### 本地配置文件

创建 `local.properties` 文件：
```properties
# Android SDK路径
sdk.dir=/Users/<USER>/Library/Android/sdk

# 可选：NDK路径（如果需要原生开发）
ndk.dir=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393

# 调试配置
debug.keystore.password=android
debug.key.password=android
```

#### 环境变量

```bash
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/tools
```

## 开发指南

### 代码规范

#### Kotlin编码标准
- 使用4空格缩进
- 行长度限制为120字符
- 使用驼峰命名法
- 类名使用PascalCase
- 函数和变量名使用camelCase

#### 文件头版权信息
```kotlin
/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */
```

#### 架构模式

**MVVM + Clean Architecture**
```kotlin
// ViewModel - 管理UI状态
class FeatureViewModel @Inject constructor(
    private val useCase: FeatureUseCase
) : ViewModel() {
    private val _uiState = MutableStateFlow(FeatureUiState())
    val uiState: StateFlow<FeatureUiState> = _uiState.asStateFlow()
}

// UseCase - 业务逻辑
class FeatureUseCase @Inject constructor(
    private val repository: FeatureRepository
) {
    suspend operator fun invoke(): Result<Data> = repository.getData()
}

// Repository - 数据访问
interface FeatureRepository {
    suspend fun getData(): Result<Data>
}
```

### 依赖注入

使用Hilt进行依赖注入：
```kotlin
// 模块定义
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    fun provideApiClient(): ApiClient = ApiClient()
}

// 注入使用
@HiltViewModel
class ViewModel @Inject constructor(
    private val apiClient: ApiClient
) : ViewModel()
```

### 异步处理

使用Kotlin Coroutines和Flow：
```kotlin
// Repository中的异步操作
class RepositoryImpl : Repository {
    override fun getData(): Flow<Result<Data>> = flow {
        emit(Result.Loading)
        try {
            val data = apiClient.fetchData()
            emit(Result.Success(data))
        } catch (e: Exception) {
            emit(Result.Error(e))
        }
    }
}

// ViewModel中收集数据
class ViewModel : ViewModel() {
    init {
        viewModelScope.launch {
            repository.getData().collect { result ->
                _uiState.value = when (result) {
                    is Result.Loading -> uiState.value.copy(isLoading = true)
                    is Result.Success -> uiState.value.copy(
                        isLoading = false,
                        data = result.data
                    )
                    is Result.Error -> uiState.value.copy(
                        isLoading = false,
                        error = result.exception.message
                    )
                }
            }
        }
    }
}
```

### UI开发

使用Jetpack Compose构建界面：
```kotlin
@Composable
fun FeatureScreen(
    viewModel: FeatureViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    LazyColumn {
        items(uiState.items) { item ->
            ItemCard(
                item = item,
                onClick = { viewModel.onItemClick(item.id) }
            )
        }
    }
}
```

## 测试

### 测试结构
```
src/test/                    # 单元测试
├── java/com/cinaclub/
│   ├── repository/         # Repository测试
│   ├── usecase/           # UseCase测试
│   └── viewmodel/         # ViewModel测试
└── resources/             # 测试资源

src/androidTest/            # 集成测试
├── java/com/cinaclub/
│   ├── ui/               # UI测试
│   ├── database/         # 数据库测试
│   └── integration/      # 集成测试
└── assets/               # 测试资产
```

### 运行测试

```bash
# 运行单元测试
./gradlew test

# 运行Android测试
./gradlew connectedAndroidTest

# 运行特定模块测试
./gradlew :feature:auth:test

# 测试覆盖率报告
./gradlew jacocoTestReport
```

### 测试示例

```kotlin
// 单元测试
@Test
fun `when login with valid credentials, should return success`() = runTest {
    // Given
    val credentials = AuthCredentials("<EMAIL>", "password")
    val expectedToken = AuthToken(...)
    coEvery { apiClient.login(any()) } returns expectedToken
    
    // When
    val result = repository.login(credentials)
    
    // Then
    assertThat(result).isInstanceOf(Result.Success::class.java)
    assertThat((result as Result.Success).data).isEqualTo(expectedToken)
}

// UI测试
@Test
fun loginScreen_displayedCorrectly() {
    composeTestRule.setContent {
        LoginScreen()
    }
    
    composeTestRule
        .onNodeWithText("邮箱")
        .assertIsDisplayed()
    
    composeTestRule
        .onNodeWithText("密码")
        .assertIsDisplayed()
}
```

## 部署

### 构建配置

#### Debug构建
```bash
./gradlew assembleDebug
```

#### Release构建
```bash
./gradlew assembleRelease
```

#### 签名配置
在 `app/build.gradle.kts` 中配置：
```kotlin
android {
    signingConfigs {
        create("release") {
            storeFile = file("keystore/release.keystore")
            storePassword = "store_password"
            keyAlias = "key_alias"
            keyPassword = "key_password"
        }
    }
    
    buildTypes {
        release {
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
    }
}
```

### CI/CD

GitHub Actions配置示例：
```yaml
name: Android CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    - name: Run tests
      run: ./gradlew test
    - name: Run Android tests
      run: ./gradlew connectedAndroidTest
```

## 贡献指南

### 开发流程

1. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

2. **进行开发**
- 遵循代码规范
- 添加必要的测试
- 更新文档

3. **提交代码**
```bash
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
```

4. **创建Pull Request**
- 详细描述更改内容
- 确保所有测试通过
- 请求代码审查

### 代码审查

#### 审查清单
- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 性能影响评估
- [ ] 安全性考虑

#### 自动化检查
- Lint检查
- 单元测试
- 集成测试
- 安全扫描

## 故障排除

### 常见问题

#### 构建失败
```bash
# 清理项目
./gradlew clean

# 重新构建
./gradlew build
```

#### 依赖冲突
```bash
# 查看依赖树
./gradlew :app:dependencies

# 强制使用特定版本
implementation("library:1.0.0") {
    force = true
}
```

#### Go Mobile问题
```bash
# 重新安装Go Mobile
go install golang.org/x/mobile/cmd/gomobile@latest
gomobile init

# 重新编译Go库
cd ../../core
gomobile bind -target=android -o ../apps/android/libs/core-go.aar .
```

### 调试技巧

#### 日志
```kotlin
// 使用结构化日志
Log.d("FeatureName", "Operation completed: $result")

// 条件日志
if (BuildConfig.DEBUG) {
    Log.v("Debug", "Detailed debug information")
}
```

#### 性能分析
- 使用Android Studio Profiler
- 监控内存使用
- 分析CPU性能
- 检查网络请求

## 许可证

Copyright (c) 2025 Cina.Club. All rights reserved.

本项目受版权保护，未经明确授权不得复制、分发或修改。 