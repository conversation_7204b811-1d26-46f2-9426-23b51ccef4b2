/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.core.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

/**
 * User domain model representing a CINA.CLUB user.
 */
@Parcelize
data class User(
    val id: String,
    val email: String,
    val firstName: String,
    val lastName: String,
    val username: String? = null,
    val avatarUrl: String? = null,
    val bio: String? = null,
    val location: String? = null,
    val website: String? = null,
    val phoneNumber: String? = null,
    val isEmailVerified: Boolean = false,
    val isPhoneVerified: Boolean = false,
    val membershipLevel: MembershipLevel = MembershipLevel.FREE,
    val userLevel: UserLevel = UserLevel.BEGINNER,
    val experiencePoints: Long = 0,
    val coinsBalance: Long = 0,
    val followersCount: Long = 0,
    val followingCount: Long = 0,
    val postsCount: Long = 0,
    val isOnline: Boolean = false,
    val lastSeenAt: LocalDateTime? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val preferences: UserPreferences = UserPreferences(),
    val permissions: Set<UserPermission> = emptySet(),
    val badges: List<UserBadge> = emptyList(),
    val subscriptions: List<UserSubscription> = emptyList()
) : Parcelable {
    
    /**
     * Returns the full name of the user.
     */
    val fullName: String
        get() = "$firstName $lastName".trim()
    
    /**
     * Returns the display name (username or full name).
     */
    val displayName: String
        get() = username?.takeIf { it.isNotBlank() } ?: fullName
    
    /**
     * Checks if the user has a specific permission.
     */
    fun hasPermission(permission: UserPermission): Boolean {
        return permissions.contains(permission) || 
               membershipLevel.permissions.contains(permission)
    }
    
    /**
     * Checks if the user has a premium membership.
     */
    val isPremium: Boolean
        get() = membershipLevel != MembershipLevel.FREE
    
    /**
     * Checks if user profile is complete.
     */
    val isProfileComplete: Boolean
        get() = username != null && 
                bio != null && 
                avatarUrl != null && 
                isEmailVerified
}

/**
 * User membership levels with associated benefits and permissions.
 */
@Parcelize
enum class MembershipLevel(
    val displayName: String,
    val maxUploadSizeMB: Long,
    val aiRequestsPerDay: Int,
    val permissions: Set<UserPermission>
) : Parcelable {
    FREE(
        displayName = "Free",
        maxUploadSizeMB = 10,
        aiRequestsPerDay = 20,
        permissions = setOf(
            UserPermission.READ_PUBLIC_CONTENT,
            UserPermission.CREATE_BASIC_CONTENT,
            UserPermission.BASIC_CHAT
        )
    ),
    BASIC(
        displayName = "Basic",
        maxUploadSizeMB = 50,
        aiRequestsPerDay = 100,
        permissions = setOf(
            UserPermission.READ_PUBLIC_CONTENT,
            UserPermission.CREATE_BASIC_CONTENT,
            UserPermission.CREATE_PREMIUM_CONTENT,
            UserPermission.BASIC_CHAT,
            UserPermission.GROUP_CHAT,
            UserPermission.BASIC_AI_FEATURES
        )
    ),
    PREMIUM(
        displayName = "Premium",
        maxUploadSizeMB = 200,
        aiRequestsPerDay = 500,
        permissions = setOf(
            UserPermission.READ_PUBLIC_CONTENT,
            UserPermission.READ_PREMIUM_CONTENT,
            UserPermission.CREATE_BASIC_CONTENT,
            UserPermission.CREATE_PREMIUM_CONTENT,
            UserPermission.BASIC_CHAT,
            UserPermission.GROUP_CHAT,
            UserPermission.VOICE_CHAT,
            UserPermission.VIDEO_CHAT,
            UserPermission.BASIC_AI_FEATURES,
            UserPermission.ADVANCED_AI_FEATURES
        )
    ),
    PRO(
        displayName = "Pro",
        maxUploadSizeMB = 1000,
        aiRequestsPerDay = 2000,
        permissions = setOf(
            UserPermission.READ_PUBLIC_CONTENT,
            UserPermission.READ_PREMIUM_CONTENT,
            UserPermission.CREATE_BASIC_CONTENT,
            UserPermission.CREATE_PREMIUM_CONTENT,
            UserPermission.LIVE_STREAMING,
            UserPermission.BASIC_CHAT,
            UserPermission.GROUP_CHAT,
            UserPermission.VOICE_CHAT,
            UserPermission.VIDEO_CHAT,
            UserPermission.BASIC_AI_FEATURES,
            UserPermission.ADVANCED_AI_FEATURES,
            UserPermission.API_ACCESS
        )
    )
}

/**
 * User activity levels based on experience points.
 */
@Parcelize
enum class UserLevel(
    val displayName: String,
    val minExperiencePoints: Long,
    val maxExperiencePoints: Long
) : Parcelable {
    BEGINNER("Beginner", 0, 999),
    INTERMEDIATE("Intermediate", 1000, 4999),
    ADVANCED("Advanced", 5000, 19999),
    EXPERT("Expert", 20000, 99999),
    MASTER("Master", 100000, Long.MAX_VALUE);
    
    companion object {
        fun fromExperiencePoints(points: Long): UserLevel {
            return values().first { points in it.minExperiencePoints..it.maxExperiencePoints }
        }
    }
}

/**
 * User permissions for fine-grained access control.
 */
@Parcelize
enum class UserPermission : Parcelable {
    // Content permissions
    READ_PUBLIC_CONTENT,
    READ_PREMIUM_CONTENT,
    CREATE_BASIC_CONTENT,
    CREATE_PREMIUM_CONTENT,
    MODERATE_CONTENT,
    
    // Communication permissions
    BASIC_CHAT,
    GROUP_CHAT,
    VOICE_CHAT,
    VIDEO_CHAT,
    LIVE_STREAMING,
    
    // AI permissions
    BASIC_AI_FEATURES,
    ADVANCED_AI_FEATURES,
    
    // System permissions
    API_ACCESS,
    ADMIN_ACCESS,
    SUPER_ADMIN_ACCESS
}

/**
 * User preferences for customizing the app experience.
 */
@Parcelize
data class UserPreferences(
    val theme: Theme = Theme.SYSTEM,
    val language: String = "en",
    val notifications: NotificationPreferences = NotificationPreferences(),
    val privacy: PrivacyPreferences = PrivacyPreferences(),
    val ai: AIPreferences = AIPreferences()
) : Parcelable

@Parcelize
enum class Theme : Parcelable {
    LIGHT, DARK, SYSTEM
}

@Parcelize
data class NotificationPreferences(
    val pushNotifications: Boolean = true,
    val emailNotifications: Boolean = true,
    val chatNotifications: Boolean = true,
    val liveNotifications: Boolean = true,
    val newsNotifications: Boolean = false,
    val quietHoursEnabled: Boolean = false,
    val quietHoursStart: String = "22:00",
    val quietHoursEnd: String = "08:00"
) : Parcelable

@Parcelize
data class PrivacyPreferences(
    val profileVisibility: ProfileVisibility = ProfileVisibility.PUBLIC,
    val showOnlineStatus: Boolean = true,
    val allowDirectMessages: Boolean = true,
    val allowFriendRequests: Boolean = true,
    val shareUsageData: Boolean = false
) : Parcelable

@Parcelize
enum class ProfileVisibility : Parcelable {
    PRIVATE, FRIENDS_ONLY, PUBLIC
}

@Parcelize
data class AIPreferences(
    val enableLocalAI: Boolean = true,
    val aiPersonality: AIPersonality = AIPersonality.BALANCED,
    val enableContextualSuggestions: Boolean = true,
    val enableSmartReply: Boolean = true
) : Parcelable

@Parcelize
enum class AIPersonality : Parcelable {
    CASUAL, PROFESSIONAL, BALANCED, CREATIVE
}

/**
 * User badge representing achievements or special status.
 */
@Parcelize
data class UserBadge(
    val id: String,
    val name: String,
    val description: String,
    val iconUrl: String,
    val category: BadgeCategory,
    val rarity: BadgeRarity,
    val earnedAt: LocalDateTime
) : Parcelable

@Parcelize
enum class BadgeCategory : Parcelable {
    ACHIEVEMENT, MEMBERSHIP, EVENT, COMMUNITY, SPECIAL
}

@Parcelize
enum class BadgeRarity : Parcelable {
    COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
}

/**
 * User subscription to premium services or content.
 */
@Parcelize
data class UserSubscription(
    val id: String,
    val serviceId: String,
    val serviceName: String,
    val type: SubscriptionType,
    val status: SubscriptionStatus,
    val startDate: LocalDateTime,
    val endDate: LocalDateTime?,
    val autoRenew: Boolean,
    val price: Double,
    val currency: String,
    val billingPeriod: BillingPeriod
) : Parcelable

@Parcelize
enum class SubscriptionType : Parcelable {
    MEMBERSHIP, SERVICE, CONTENT, AI_FEATURES
}

@Parcelize
enum class SubscriptionStatus : Parcelable {
    ACTIVE, EXPIRED, CANCELLED, SUSPENDED
}

@Parcelize
enum class BillingPeriod : Parcelable {
    MONTHLY, QUARTERLY, YEARLY, LIFETIME
} 