/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'

import { 
  Content, 
  ContentCategory, 
  ContentAnalyticsData,
  ModerationQueueItem,
  ModerationAction,
  ContentStatus,
  ContentType
} from '@/types/content'
import { apiClient } from '@/lib/api-client'

// Query keys for content management
export const CONTENT_QUERY_KEYS = {
  // Content moderation
  moderationQueue: 'moderation-queue',
  moderationQueueById: (id: string) => ['moderation-queue', id],
  
  // Content analytics
  analytics: 'content-analytics',
  analyticsOverview: 'content-analytics-overview',
  trendingContent: 'trending-content',
  
  // Content categories
  categories: 'content-categories',
  categoryById: (id: string) => ['content-categories', id],
  categoryTree: 'content-category-tree',
} as const

// Content moderation hooks
export const useContentModeration = () => {
  const queryClient = useQueryClient()

  // Get moderation queue
  const useModerationQueue = (params?: any) => {
    return useQuery({
      queryKey: [CONTENT_QUERY_KEYS.moderationQueue, params],
      queryFn: async () => {
        const response = await apiClient.get('/admin/content/moderation/queue', { params })
        return response.data
      },
      staleTime: 30 * 1000, // 30 seconds
      refetchInterval: 60 * 1000, // Refresh every minute
    })
  }

  // Get specific content for review
  const useContentForReview = (contentId: string) => {
    return useQuery({
      queryKey: CONTENT_QUERY_KEYS.moderationQueueById(contentId),
      queryFn: async () => {
        const response = await apiClient.get(`/admin/content/moderation/${contentId}`)
        return response.data
      },
      enabled: !!contentId,
    })
  }

  // Moderate content action
  const useModerationAction = () => {
    return useMutation({
      mutationFn: async ({ 
        contentId, 
        action, 
        reason, 
        metadata 
      }: {
        contentId: string
        action: ModerationAction
        reason: string
        metadata?: any
      }) => {
        const response = await apiClient.post(`/admin/content/moderation/${contentId}/action`, {
          action,
          reason,
          metadata,
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success(`内容${variables.action}成功`)
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.moderationQueue] })
        queryClient.invalidateQueries({ 
          queryKey: CONTENT_QUERY_KEYS.moderationQueueById(variables.contentId) 
        })
      },
      onError: (error) => {
        console.error('Moderation action failed:', error)
        message.error('审核操作失败')
      },
    })
  }

  // Bulk moderation action
  const useBulkModerationAction = () => {
    return useMutation({
      mutationFn: async ({ 
        contentIds, 
        action, 
        reason 
      }: {
        contentIds: string[]
        action: ModerationAction
        reason: string
      }) => {
        const response = await apiClient.post('/admin/content/moderation/bulk-action', {
          contentIds,
          action,
          reason,
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success(`批量${variables.action}成功`)
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.moderationQueue] })
      },
      onError: (error) => {
        console.error('Bulk moderation failed:', error)
        message.error('批量审核失败')
      },
    })
  }

  return {
    useModerationQueue,
    useContentForReview,
    useModerationAction,
    useBulkModerationAction,
  }
}

// Content analytics hooks
export const useContentAnalytics = () => {
  // Get analytics overview
  const useAnalyticsOverview = (dateRange: string, contentType?: ContentType) => {
    return useQuery({
      queryKey: [CONTENT_QUERY_KEYS.analyticsOverview, dateRange, contentType],
      queryFn: async () => {
        const response = await apiClient.get('/admin/content/analytics/overview', {
          params: { dateRange, contentType }
        })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  // Get detailed analytics
  const useDetailedAnalytics = (params: any) => {
    return useQuery({
      queryKey: [CONTENT_QUERY_KEYS.analytics, params],
      queryFn: async () => {
        const response = await apiClient.get('/admin/content/analytics', { params })
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  // Get trending content
  const useTrendingContent = (limit: number = 10) => {
    return useQuery({
      queryKey: [CONTENT_QUERY_KEYS.trendingContent, limit],
      queryFn: async () => {
        const response = await apiClient.get('/admin/content/analytics/trending', {
          params: { limit }
        })
        return response.data
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    })
  }

  // Export analytics report
  const useExportAnalytics = () => {
    return useMutation({
      mutationFn: async (params: any) => {
        const response = await apiClient.post('/admin/content/analytics/export', params, {
          responseType: 'blob'
        })
        return response.data
      },
      onSuccess: (data) => {
        // Create download link for the exported file
        const url = window.URL.createObjectURL(new Blob([data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `content-analytics-${Date.now()}.xlsx`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
        message.success('分析报告导出成功')
      },
      onError: (error) => {
        console.error('Export failed:', error)
        message.error('导出失败')
      },
    })
  }

  return {
    useAnalyticsOverview,
    useDetailedAnalytics,
    useTrendingContent,
    useExportAnalytics,
  }
}

// Content categories hooks
export const useContentCategories = () => {
  const queryClient = useQueryClient()

  // Get all categories
  const useCategories = (params?: any) => {
    return useQuery({
      queryKey: [CONTENT_QUERY_KEYS.categories, params],
      queryFn: async () => {
        const response = await apiClient.get('/admin/content/categories', { params })
        return response.data
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    })
  }

  // Get category tree structure
  const useCategoryTree = () => {
    return useQuery({
      queryKey: [CONTENT_QUERY_KEYS.categoryTree],
      queryFn: async () => {
        const response = await apiClient.get('/admin/content/categories/tree')
        return response.data
      },
      staleTime: 15 * 60 * 1000, // 15 minutes
    })
  }

  // Get category by ID
  const useCategory = (categoryId: string) => {
    return useQuery({
      queryKey: CONTENT_QUERY_KEYS.categoryById(categoryId),
      queryFn: async () => {
        const response = await apiClient.get(`/admin/content/categories/${categoryId}`)
        return response.data
      },
      enabled: !!categoryId,
    })
  }

  // Create category
  const useCreateCategory = () => {
    return useMutation({
      mutationFn: async (categoryData: Partial<ContentCategory>) => {
        const response = await apiClient.post('/admin/content/categories', categoryData)
        return response.data
      },
      onSuccess: () => {
        message.success('分类创建成功')
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categories] })
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categoryTree] })
      },
      onError: (error) => {
        console.error('Create category failed:', error)
        message.error('分类创建失败')
      },
    })
  }

  // Update category
  const useUpdateCategory = () => {
    return useMutation({
      mutationFn: async ({ 
        categoryId, 
        categoryData 
      }: {
        categoryId: string
        categoryData: Partial<ContentCategory>
      }) => {
        const response = await apiClient.put(`/admin/content/categories/${categoryId}`, categoryData)
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success('分类更新成功')
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categories] })
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categoryTree] })
        queryClient.invalidateQueries({ 
          queryKey: CONTENT_QUERY_KEYS.categoryById(variables.categoryId) 
        })
      },
      onError: (error) => {
        console.error('Update category failed:', error)
        message.error('分类更新失败')
      },
    })
  }

  // Delete category
  const useDeleteCategory = () => {
    return useMutation({
      mutationFn: async (categoryId: string) => {
        const response = await apiClient.delete(`/admin/content/categories/${categoryId}`)
        return response.data
      },
      onSuccess: () => {
        message.success('分类删除成功')
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categories] })
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categoryTree] })
      },
      onError: (error) => {
        console.error('Delete category failed:', error)
        message.error('分类删除失败')
      },
    })
  }

  // Reorder categories
  const useReorderCategories = () => {
    return useMutation({
      mutationFn: async (categoryOrders: Array<{ id: string; order: number }>) => {
        const response = await apiClient.put('/admin/content/categories/reorder', {
          orders: categoryOrders
        })
        return response.data
      },
      onSuccess: () => {
        message.success('分类排序更新成功')
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categories] })
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.categoryTree] })
      },
      onError: (error) => {
        console.error('Reorder categories failed:', error)
        message.error('排序更新失败')
      },
    })
  }

  return {
    useCategories,
    useCategoryTree,
    useCategory,
    useCreateCategory,
    useUpdateCategory,
    useDeleteCategory,
    useReorderCategories,
  }
}

// General content management hooks
export const useContentManagement = () => {
  const queryClient = useQueryClient()

  // Get content list with filters
  const useContentList = (params?: any) => {
    return useQuery({
      queryKey: ['content-list', params],
      queryFn: async () => {
        const response = await apiClient.get('/admin/content', { params })
        return response.data
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
    })
  }

  // Update content status
  const useUpdateContentStatus = () => {
    return useMutation({
      mutationFn: async ({ 
        contentId, 
        status, 
        reason 
      }: {
        contentId: string
        status: ContentStatus
        reason?: string
      }) => {
        const response = await apiClient.put(`/admin/content/${contentId}/status`, {
          status,
          reason,
        })
        return response.data
      },
      onSuccess: () => {
        message.success('内容状态更新成功')
        queryClient.invalidateQueries({ queryKey: ['content-list'] })
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.moderationQueue] })
      },
      onError: (error) => {
        console.error('Update content status failed:', error)
        message.error('状态更新失败')
      },
    })
  }

  // Bulk update content status
  const useBulkUpdateContentStatus = () => {
    return useMutation({
      mutationFn: async ({ 
        contentIds, 
        status, 
        reason 
      }: {
        contentIds: string[]
        status: ContentStatus
        reason?: string
      }) => {
        const response = await apiClient.put('/admin/content/bulk/status', {
          contentIds,
          status,
          reason,
        })
        return response.data
      },
      onSuccess: () => {
        message.success('批量状态更新成功')
        queryClient.invalidateQueries({ queryKey: ['content-list'] })
        queryClient.invalidateQueries({ queryKey: [CONTENT_QUERY_KEYS.moderationQueue] })
      },
      onError: (error) => {
        console.error('Bulk update status failed:', error)
        message.error('批量状态更新失败')
      },
    })
  }

  return {
    useContentList,
    useUpdateContentStatus,
    useBulkUpdateContentStatus,
  }
}

// Combine all content management hooks
export const useAllContentHooks = () => {
  const moderation = useContentModeration()
  const analytics = useContentAnalytics()
  const categories = useContentCategories()
  const management = useContentManagement()

  return {
    ...moderation,
    ...analytics,
    ...categories,
    ...management,
  }
}

// Export all hooks
export { useContentModeration, useContentAnalytics, useContentCategories, useContentManagement } 