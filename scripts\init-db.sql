-- Copyright (c) 2025 Cina.Club
-- All rights reserved.
-- Created: 2025-06-18
-- Modified: 2025-06-18

-- CINA.CLUB 数据库初始化脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 创建用户数据库
CREATE DATABASE cina_users;
CREATE DATABASE cina_billing;
CREATE DATABASE cina_analytics;
CREATE DATABASE cina_content;

-- 创建只读用户
CREATE USER cina_readonly WITH PASSWORD 'cina_readonly_password';
GRANT CONNECT ON DATABASE cina_club TO cina_readonly;
GRANT CONNECT ON DATABASE cina_users TO cina_readonly;
GRANT CONNECT ON DATABASE cina_billing TO cina_readonly;
GRANT CONNECT ON DATABASE cina_analytics TO cina_readonly;
GRANT CONNECT ON DATABASE cina_content TO cina_readonly;

-- 切换到主数据库
\c cina_club;

-- 授予只读权限
GRANT USAGE ON SCHEMA public TO cina_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO cina_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO cina_readonly;

-- 创建基础表结构（示例）
CREATE TABLE IF NOT EXISTS service_health (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    last_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入初始数据
INSERT INTO service_health (service_name, status) VALUES
    ('user-core-service', 'healthy'),
    ('api-gateway-service', 'healthy'),
    ('billing-service', 'healthy'),
    ('ai-assistant-service', 'healthy')
ON CONFLICT DO NOTHING; 