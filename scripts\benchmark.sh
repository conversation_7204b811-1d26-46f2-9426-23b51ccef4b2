#!/bin/bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-12-28 10:00:00
# Modified: 2025-12-28 10:00:00

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BENCHMARK_DIR="benchmarks"
BENCHMARK_TIME="10s"
BENCHMARK_COUNT=3

echo -e "${BLUE}=== CINA.CLUB PKG Performance Benchmark ===${NC}"
echo "Benchmark duration: ${BENCHMARK_TIME}"
echo "Benchmark iterations: ${BENCHMARK_COUNT}"
echo

# 创建基准测试目录
mkdir -p "${BENCHMARK_DIR}"

# 获取所有pkg子包
PKG_DIRS=$(find pkg -name "*.go" -not -name "*_test.go" -exec dirname {} \; | sort -u)

# 生成基准测试报告头
REPORT_FILE="${BENCHMARK_DIR}/benchmark_report.md"
cat > "$REPORT_FILE" << EOF
# CINA.CLUB PKG Performance Benchmark Report

Generated at: $(date)
Benchmark duration: ${BENCHMARK_TIME}
Benchmark iterations: ${BENCHMARK_COUNT}

## Summary

| Package | Benchmarks | Critical Path Performance |
|---------|------------|---------------------------|
EOF

echo -e "${BLUE}Running benchmark tests for all packages...${NC}"
echo

total_benchmarks=0
packages_with_benchmarks=0

# 遍历每个包
for pkg_dir in $PKG_DIRS; do
    # 跳过examples目录
    if [[ "$pkg_dir" == *"examples"* ]]; then
        continue
    fi
    
    pkg_name=$(basename "$pkg_dir")
    echo -e "${YELLOW}Checking package: ${pkg_dir}${NC}"
    
    # 检查是否有基准测试文件
    benchmark_files=$(find "$pkg_dir" -name "*_test.go" -exec grep -l "func Benchmark" {} \; 2>/dev/null || true)
    
    if [[ -z "$benchmark_files" ]]; then
        echo -e "${YELLOW}  ⚠️  No benchmark tests found${NC}"
        echo "| $pkg_name | No benchmarks | - |" >> "$REPORT_FILE"
        echo
        continue
    fi
    
    packages_with_benchmarks=$((packages_with_benchmarks + 1))
    echo -e "${GREEN}  ✅ Found benchmark tests${NC}"
    
    # 运行基准测试
    benchmark_output="${BENCHMARK_DIR}/${pkg_name}_benchmark.txt"
    echo "  Running benchmarks..."
    
    if go test -bench=. -benchmem -benchtime="$BENCHMARK_TIME" -count="$BENCHMARK_COUNT" \
        "./$pkg_dir" > "$benchmark_output" 2>&1; then
        
        # 统计基准测试数量
        benchmark_count=$(grep -c "^Benchmark" "$benchmark_output" || echo "0")
        total_benchmarks=$((total_benchmarks + benchmark_count))
        
        echo -e "${GREEN}  ✅ Completed $benchmark_count benchmarks${NC}"
        
        # 分析关键性能指标
        critical_path_analysis=$(analyze_critical_path "$benchmark_output" "$pkg_name")
        echo "| $pkg_name | $benchmark_count | $critical_path_analysis |" >> "$REPORT_FILE"
        
        # 生成详细报告
        generate_detailed_report "$pkg_dir" "$benchmark_output"
        
    else
        echo -e "${RED}  ❌ Benchmark tests failed${NC}"
        echo "| $pkg_name | Failed | - |" >> "$REPORT_FILE"
    fi
    
    echo
done

# 分析关键路径性能的函数
analyze_critical_path() {
    local output_file="$1"
    local pkg_name="$2"
    
    case "$pkg_name" in
        "auth")
            # JWT验证和RBAC检查性能
            jwt_perf=$(grep "BenchmarkJWTValidator_ValidateUserToken" "$output_file" | awk '{print $3}' | head -1 || echo "N/A")
            rbac_perf=$(grep "BenchmarkEngine_Can" "$output_file" | awk '{print $3}' | head -1 || echo "N/A")
            echo "JWT: $jwt_perf ns/op, RBAC: $rbac_perf ns/op"
            ;;
        "database")
            # 数据库连接和查询性能
            conn_perf=$(grep "BenchmarkPostgresClient_Query" "$output_file" | awk '{print $3}' | head -1 || echo "N/A")
            redis_perf=$(grep "BenchmarkRedisClient_Get" "$output_file" | awk '{print $3}' | head -1 || echo "N/A")
            echo "PG Query: $conn_perf ns/op, Redis Get: $redis_perf ns/op"
            ;;
        "logger")
            # 日志性能
            log_perf=$(grep "BenchmarkLogger_Info" "$output_file" | awk '{print $3}' | head -1 || echo "N/A")
            echo "Logging: $log_perf ns/op"
            ;;
        "config")
            # 配置加载性能
            config_perf=$(grep "BenchmarkConfig_Load" "$output_file" | awk '{print $3}' | head -1 || echo "N/A")
            echo "Config Load: $config_perf ns/op"
            ;;
        "tracing")
            # 追踪性能
            trace_perf=$(grep "BenchmarkTracer_Start" "$output_file" | awk '{print $3}' | head -1 || echo "N/A")
            echo "Trace Start: $trace_perf ns/op"
            ;;
        *)
            # 通用分析：找最慢的操作
            slowest=$(grep "^Benchmark" "$output_file" | sort -k3 -nr | head -1 | awk '{print $1 ": " $3 " ns/op"}' || echo "N/A")
            echo "$slowest"
            ;;
    esac
}

# 生成详细报告的函数
generate_detailed_report() {
    local pkg_dir="$1"
    local output_file="$2"
    local pkg_name=$(basename "$pkg_dir")
    
    local detail_file="${BENCHMARK_DIR}/${pkg_name}_detailed.md"
    
    cat > "$detail_file" << EOF
# $pkg_name Package Benchmark Details

## Test Results

\`\`\`
EOF
    
    cat "$output_file" >> "$detail_file"
    
    cat >> "$detail_file" << EOF
\`\`\`

## Performance Analysis

EOF
    
    # 分析性能数据
    if grep -q "^Benchmark" "$output_file"; then
        echo "### Top 5 Fastest Operations" >> "$detail_file"
        echo "" >> "$detail_file"
        grep "^Benchmark" "$output_file" | sort -k3 -n | head -5 | while read line; do
            echo "- $line" >> "$detail_file"
        done
        
        echo "" >> "$detail_file"
        echo "### Top 5 Slowest Operations" >> "$detail_file"
        echo "" >> "$detail_file"
        grep "^Benchmark" "$output_file" | sort -k3 -nr | head -5 | while read line; do
            echo "- $line" >> "$detail_file"
        done
        
        echo "" >> "$detail_file"
        echo "### Memory Usage Analysis" >> "$detail_file"
        echo "" >> "$detail_file"
        if grep -q "B/op" "$output_file"; then
            echo "Memory allocations per operation:" >> "$detail_file"
            grep "B/op" "$output_file" | sort -k5 -nr | head -5 | while read line; do
                echo "- $line" >> "$detail_file"
            done
        else
            echo "No memory allocation data available." >> "$detail_file"
        fi
    else
        echo "No benchmark results found." >> "$detail_file"
    fi
    
    cat >> "$detail_file" << EOF

## Performance Recommendations

EOF
    
    # 基于包类型生成性能建议
    case "$pkg_name" in
        "auth")
            cat >> "$detail_file" << EOF
- JWT validation should be < 100μs per operation
- RBAC checks should be < 1μs per operation  
- Consider caching JWKS public keys
- Monitor memory allocations in token parsing
EOF
            ;;
        "database")
            cat >> "$detail_file" << EOF
- Connection pool operations should be < 10μs
- Query execution depends on complexity
- Monitor connection leak and pool exhaustion
- Redis operations should be < 1ms
EOF
            ;;
        "logger")
            cat >> "$detail_file" << EOF
- Structured logging should be < 10μs per log entry
- Avoid excessive memory allocations
- Consider async logging for high-throughput scenarios
EOF
            ;;
        *)
            cat >> "$detail_file" << EOF
- Monitor memory allocations and garbage collection
- Optimize hot paths with high call frequency
- Consider caching for expensive operations
EOF
            ;;
    esac
}

# 完成主报告
cat >> "$REPORT_FILE" << EOF

## Overall Statistics

- Total packages scanned: $(echo "$PKG_DIRS" | wc -l)
- Packages with benchmarks: $packages_with_benchmarks
- Total benchmark tests: $total_benchmarks

## Performance Targets

| Component | Target Performance | Critical Path |
|-----------|-------------------|---------------|
| JWT Validation | < 100μs | User authentication |
| RBAC Check | < 1μs | Permission validation |
| DB Connection | < 10μs | Connection pool |
| Redis Get | < 1ms | Cache operations |
| Logging | < 10μs | Structured logging |
| Config Load | < 1ms | Service startup |

## Key Findings

### High Performance Areas ✅
EOF

# 分析高性能区域
for output_file in "${BENCHMARK_DIR}"/*_benchmark.txt; do
    if [[ -f "$output_file" ]]; then
        pkg_name=$(basename "$output_file" "_benchmark.txt")
        fast_ops=$(grep "^Benchmark" "$output_file" 2>/dev/null | awk '$3 < 1000 {print $1}' | head -3)
        if [[ -n "$fast_ops" ]]; then
            echo "" >> "$REPORT_FILE"
            echo "**$pkg_name:**" >> "$REPORT_FILE"
            echo "$fast_ops" | while read op; do
                echo "- $op" >> "$REPORT_FILE"
            done
        fi
    fi
done

cat >> "$REPORT_FILE" << EOF

### Areas for Optimization ⚠️
EOF

# 分析需要优化的区域
for output_file in "${BENCHMARK_DIR}"/*_benchmark.txt; do
    if [[ -f "$output_file" ]]; then
        pkg_name=$(basename "$output_file" "_benchmark.txt")
        slow_ops=$(grep "^Benchmark" "$output_file" 2>/dev/null | awk '$3 > 100000 {print $1 ": " $3 " ns/op"}' | head -3)
        if [[ -n "$slow_ops" ]]; then
            echo "" >> "$REPORT_FILE"
            echo "**$pkg_name:**" >> "$REPORT_FILE"
            echo "$slow_ops" | while read op; do
                echo "- $op" >> "$REPORT_FILE"
            done
        fi
    fi
done

cat >> "$REPORT_FILE" << EOF

## Recommendations

1. **Critical Path Optimization**: Focus on JWT validation and RBAC checks
2. **Memory Optimization**: Reduce allocations in hot paths
3. **Caching Strategy**: Implement intelligent caching for expensive operations
4. **Monitoring**: Set up performance alerts for degradation
5. **Regular Testing**: Run benchmarks in CI/CD pipeline

## Detailed Reports

EOF

# 添加详细报告链接
for detail_file in "${BENCHMARK_DIR}"/*_detailed.md; do
    if [[ -f "$detail_file" ]]; then
        pkg_name=$(basename "$detail_file" "_detailed.md")
        echo "- [$pkg_name Package Details](./${pkg_name}_detailed.md)" >> "$REPORT_FILE"
    fi
done

# 生成性能趋势脚本
cat > "${BENCHMARK_DIR}/track_performance.sh" << 'EOF'
#!/bin/bash

# 性能趋势跟踪脚本
TREND_FILE="performance_trend.csv"

if [[ ! -f "$TREND_FILE" ]]; then
    echo "date,package,benchmark,ns_per_op,bytes_per_op,allocs_per_op" > "$TREND_FILE"
fi

# 解析当前基准测试结果并添加到趋势文件
for benchmark_file in *_benchmark.txt; do
    if [[ -f "$benchmark_file" ]]; then
        pkg_name=$(basename "$benchmark_file" "_benchmark.txt")
        
        grep "^Benchmark" "$benchmark_file" | while read line; do
            benchmark_name=$(echo "$line" | awk '{print $1}')
            ns_per_op=$(echo "$line" | awk '{print $3}')
            bytes_per_op=$(echo "$line" | awk '{print $5}' | sed 's/B\/op//')
            allocs_per_op=$(echo "$line" | awk '{print $7}' | sed 's/allocs\/op//')
            
            echo "$(date -I),$pkg_name,$benchmark_name,$ns_per_op,$bytes_per_op,$allocs_per_op" >> "$TREND_FILE"
        done
    fi
done

echo "Performance data added to $TREND_FILE"
EOF

chmod +x "${BENCHMARK_DIR}/track_performance.sh"

echo
echo -e "${BLUE}=== Benchmark Summary ===${NC}"
echo "Total packages scanned: $(echo "$PKG_DIRS" | wc -l)"
echo "Packages with benchmarks: $packages_with_benchmarks"
echo "Total benchmark tests: $total_benchmarks"
echo
echo "Reports generated:"
echo "  📊 Main report: ${BENCHMARK_DIR}/benchmark_report.md"
echo "  📈 Performance tracking: ${BENCHMARK_DIR}/track_performance.sh"

for detail_file in "${BENCHMARK_DIR}"/*_detailed.md; do
    if [[ -f "$detail_file" ]]; then
        pkg_name=$(basename "$detail_file" "_detailed.md")
        echo "  📝 $pkg_name details: ${detail_file}"
    fi
done

echo
if [[ $packages_with_benchmarks -gt 0 ]]; then
    echo -e "${GREEN}✅ Benchmark analysis completed successfully!${NC}"
    echo "Run 'cd ${BENCHMARK_DIR} && ./track_performance.sh' to update performance trends."
else
    echo -e "${YELLOW}⚠️  No benchmark tests found. Consider adding benchmark tests to critical packages.${NC}"
fi

echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Review the benchmark report for performance insights"
echo "2. Add benchmark tests to packages without them"
echo "3. Set performance regression alerts"
echo "4. Run benchmarks regularly in CI/CD" 