﻿好的，遵照您的指示，我们来生成一份为 `file-storage-service` (文件存储服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **多存储后端与策略**: 明确支持连接多个不同的对象存储后端（如公有云S3、私有云MinIO），并引入基于文件类型或业务场景的存储策略。
2.  **图片处理与转换**: 将图片处理作为核心功能之一，支持动态、实时的图片缩放、裁剪、格式转换（如WebP），并提供CDN集成。
3.  **文件生命周期管理**: 详细定义文件的生命周期策略，包括冷热数据分离和自动清理。
4.  **访问控制与权限**: 引入更精细的访问控制模型，支持私有、公开、限时访问、以及（未来）基于角色的访问。
5.  **安全扫描与合规**: 明确与`content-moderation-service`的集成，对上传的文件进行异步的安全扫描。
6.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
7.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、安全可靠、经济高效，且能作为整个平台统一文件管理基石的服务。

---

### CINA.CLUB - file-storage-service 需求规格说明书

**版本: 2.0 (生产级定义，集成多后端、图片处理与高级权限)**  
**发布日期: 2025-06-22**  
**最后修订日期: 2025-06-22**  
**文档负责人:** [基础设施/平台工程团队负责人名称]  
**审批人:** [CTO/架构师]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台需要支持海量的、多样的用户生成内容 (UGC) 和系统文件。`file-storage-service` 的目的在于提供一个**统一、安全、可靠、且与底层对象存储解耦的文件管理与分发代理层**。它旨在简化其他微服务处理文件存储的复杂性，集中实施安全、权限和成本优化策略，并提供动态的、高性能的图片处理能力。

#### 1.2. 服务范围
本服务 **负责**:
*   **文件上传协调**: 处理来自其他内部微服务的文件上传请求，根据**存储策略**返回安全的**预签名上传URL**。
*   **文件访问与分发**:
    *   处理文件访问请求，根据**访问控制策略**返回安全的**预签名下载URL**或**CDN加速的URL**。
*   **动态图片处理**: 支持对图片进行实时的缩放、裁剪、格式转换（如WebP）和质量压缩。
*   **文件元数据管理**: 存储和管理与文件相关的核心元数据（上传者、文件类型、大小、存储位置、权限策略等）。
*   **文件生命周期管理**:
    *   处理文件删除请求。
    *   （通过后台任务或配置）实施冷热数据分离和过期文件自动清理。
*   **多存储后端支持**: 抽象底层存储，支持同时使用多个对象存储提供商（如AWS S3, MinIO）。
*   **安全扫描协调**: 在文件上传后，异步触发对文件的安全扫描（如病毒扫描）和内容审核。

本服务 **不负责**:
*   **直接存储文件二进制数据**: 实际文件存储在底层对象存储服务中。
*   **管理与业务逻辑强相关的元数据**: 由各自的业务服务自己管理。
*   **复杂的视频处理和转码**: 由`short-video-service`负责。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部所有需要文件存储的微服务 (主要)**: `user-core-service`, `short-video-service`, `community-forum-service`, `chat-api-service` 等。
*   **CINA.CLUB客户端应用 (间接)**: 从业务服务获取预签名URL后，直接与对象存储或CDN进行交互。
*   **`content-moderation-service`**: (被本服务调用或消费事件) 对上传的文件进行审核。

#### 1.4. 定义与缩略语
*   **Object Storage**: 对象存储 (S3, GCS, MinIO等)。
*   **Storage Backend**: 本服务配置的一个具体的对象存储实例。
*   **Storage Policy**: 一组规则，决定新文件应存储在哪个`Storage Backend`以及其生命周期。
*   **Presigned URL**: 预签名URL，一个有时效性的、带访问凭证的URL。
*   **CDN**: Content Delivery Network (内容分发网络)。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`file-storage-service` 是平台的基础设施层服务，作为所有需要文件存储的业务服务的**统一文件网关**和**图片处理引擎**。它将具体的对象存储实现与上层业务逻辑解耦，使得平台可以在未来轻松更换或使用多个存储后端，并集中实施文件相关的安全、成本和性能优化策略。

#### 2.2. 主要功能概述
*   基于预签名URL的安全、解耦的上传/下载机制。
*   支持多存储后端的、基于策略的存储。
*   高性能的、CDN友好的动态图片处理。
*   精细化的文件访问控制与生命周期管理。

### 3. 核心流程图

#### 3.1. 上传图片并请求一个实时缩略图
```mermaid
sequenceDiagram
    participant Client
    participant BusinessService as "e.g., user-core-service"
    participant FileStorageService as FSS
    participant ObjectStorage as "S3/MinIO"
    participant CDN

    Client->>BusinessService: 1. "我要更换头像"
    BusinessService->>FSS: 2. POST /request-upload-url (context: "user_avatar")
    
    FSS->>FSS: 3. Apply Storage Policy for "user_avatar"
    FSS->>ObjectStorage: 4. Generate Presigned PUT URL
    FSS->>DB: 5. Create FileMetadata record (status: PENDING)
    FSS-->>BusinessService: 6. (fileKey, presignedUrl)
    BusinessService-->>Client: (presignedUrl)
    
    Client->>ObjectStorage: 7. PUT image data to presignedUrl
    
    Client->>BusinessService: 8. "头像更换完成" (with fileKey)
    BusinessService->>FSS: 9. POST /finalize-upload (fileKey)
    FSS->>DB: 10. Update FileMetadata status to COMPLETED
    
    Note over Client: Later, requesting a thumbnail
    
    Client->>CDN: 11. GET /images/{fileKey}?w=200&h=200&format=webp
    
    alt CDN Cache Miss
        CDN->>FSS: 12. Forward request to FSS Image Processor endpoint
        FSS->>ObjectStorage: 13. Download original image
        FSS->>FSS: 14. **[In-Memory]** Resize & convert to WebP
        FSS-->>CDN: 15. (Processed image data)
    end
    
    CDN-->>Client: (Return cached or newly processed thumbnail)
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 文件上传
*   **FR4.1.1 (请求上传)**: 系统必须提供`POST /request-upload-url`接口，供内部服务请求上传预签名URL。
    *   请求中必须包含`contextType` (e.g., "USER_AVATAR", "CHAT_IMAGE")，本服务据此选择**存储策略**。
*   **FR4.1.2 (存储策略)**: 管理员必须能配置存储策略，定义不同`contextType`的文件应存储在哪个`Storage Backend`、哪个`Bucket`，并应用何种默认权限。
*   **FR4.1.3 (上传确认)**: 提供`POST /finalize-upload`接口。在调用此接口后，文件才被视为上传成功。此步骤可以触发后续的异步任务。

#### 4.2. 动态图片处理
*   **FR4.2.1 (实时处理API)**: 系统必须提供一个面向CDN的、可公开访问的图片处理端点。
*   **FR4.2.2 (功能)**: API必须支持通过Query参数进行以下操作：
    *   **缩放/裁剪**: `w` (宽度), `h` (高度), `fit` (`cover`, `contain`, `fill`)。
    *   **格式转换**: `format` (`jpeg`, `png`, `webp`, `avif`)。
    *   **质量压缩**: `q` (1-100)。
*   **FR4.2.3 (CDN集成)**: 处理后的图片应返回正确的HTTP缓存头（`Cache-Control`, `ETag`），以便CDN进行高效缓存。

#### 4.3. 文件访问与权限控制
*   **FR4.3.1 (访问控制策略)**: 每个文件元数据中必须包含一个`accessControl`字段，支持以下级别：
    *   `PRIVATE`: 默认。只有上传者或授权用户能访问。
    *   `PUBLIC_READ`: 所有人可读（通过CDN URL）。
    *   `SIGNED_URL_ONLY`: 必须通过请求预签名URL才能访问。
*   **FR4.3.2 (下载请求)**: `GET /request-download-url/{fileKey}`接口在生成URL前，必须严格执行权限检查。

#### 4.4. 文件生命周期与安全
*   **FR4.4.1 (删除)**: 提供`DELETE /{fileKey}`接口。支持软删除（标记`is_deleted`）和硬删除（立即从对象存储删除）。
*   **FR4.4.2 (自动清理)**: 必须有后台任务，定期硬删除那些已被软删除超过特定期限（如30天）的文件。
*   **FR4.4.3 (冷数据归档)**: （可选）通过配置底层对象存储的生命周期策略，将长时间未访问的文件自动转移到低成本的归档存储层。
*   **FR4.4.4 (安全扫描)**: 在`finalize-upload`后，系统必须异步地将文件（特别是用户上传的文档、压缩包）提交给一个安全扫描服务（如ClamAV），并将扫描结果记录在元数据中。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部S2S RESTful API接口
*   **版本**: `/api/v1/files`
*   **认证**: 严格的S2S认证。
*   **核心端点**:
    *   `POST /upload/request-url`: 请求上传。Request: `{ contextType, fileName, fileSize, uploaderId }`
    *   `POST /upload/finalize`: 确认上传。Request: `{ fileKey, checksum }`
    *   `GET /download-url/{fileKey}`: 请求私有文件下载URL。
    *   `GET /{fileKey}/metadata`: 获取文件元数据。
    *   `DELETE /{fileKey}`: 删除文件。

#### 5.2. 公开图片处理接口 (供CDN回源)
*   **路径**: `/images/{fileKey}`
*   **Query Params**: `w`, `h`, `fit`, `format`, `q`.
*   **安全**: 此端点需要防止被滥用。可以采用基于HMAC签名的URL（即除了CDN，无人能构造有效的处理请求）或速率限制。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`file_metadata`**:
    *   `file_key (PK, VARCHAR)`: 唯一的、随机生成的文件标识符。
    *   `uploader_user_id (UUID, INDEX)`
    *   `original_file_name (TEXT)`
    *   `storage_backend_id (VARCHAR, INDEX)`
    *   `storage_path (TEXT)`
    *   `content_type (VARCHAR)`
    *   `size_bytes (BIGINT)`
    *   `checksum_sha256 (VARCHAR)`
    *   `status (VARCHAR)`: `PENDING`, `COMPLETED`, `SCANNING`, `QUARANTINED`, `DELETED`.
    *   `access_control_type (VARCHAR)`: `PRIVATE`, `PUBLIC_READ`.
    *   `custom_metadata (JSONB)`: 存储如图片尺寸、视频时长等。
*   **`storage_backends`**: `id`, `provider_type` (`S3`, `MINIO`), `endpoint`, `bucket`, `encrypted_credentials`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: 元数据操作API P99延迟应 `< 50ms`。
*   **图片处理延迟**: P95处理时间（对于中等尺寸图片）应 `< 200ms`。
*   **吞吐量**: 能支持高并发的上传请求和图片处理请求。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。本服务的故障将影响平台所有涉及文件上传下载的功能。
*   **数据持久性**: 依赖底层对象存储，应达到11个9或更高的数据持久性。

#### 7.3. 可扩展性需求
*   服务应为无状态，易于水平扩展。
*   图片处理是CPU密集型操作，应考虑将其部署为独立的、可自动伸缩的Worker组。

#### 7.4. 安全性需求 (最高优先级)
*   **预签名URL安全**:
    *   最短合理有效期。
    *   精确的权限（`PUT` for upload, `GET` for download）。
    *   对上传URL强制`Content-Type`和`Content-Length`，防止被滥用。
*   **存储桶安全**: 默认私有，严格的IAM/Bucket策略。
*   **访问控制**: 确保下载和删除操作的权限检查逻辑无懈可击。
*   **安全扫描**: 所有上传的文件必须经过异步的病毒和恶意内容扫描。

### 8. 技术约束与选型建议
*   **语言**: Go。其高效的I/O和图像处理库（如`imaging`）使其非常适合此任务。
*   **图片处理**:
    *   推荐使用成熟的Go图像处理库，如`imaging`。
    *   对于超高性能需求，可以考虑使用C语言库（如`libvips`）并通过Cgo调用，或者部署一个专门的图片处理服务（如`Thumbor`, `imgproxy`）。
*   **适配器模式**: 通过统一的`ObjectStorageProvider`接口，清晰地隔离不同存储后端的实现。
*   **CDN**: 必须与一个功能强大的CDN（如Cloudflare, AWS CloudFront）紧密集成，以实现高效缓存和全球分发。

---
这份版本2.0的SRS文档为`file-storage-service`构建了一个现代化、多功能、安全可靠的文件管理中枢。它通过将存储、处理和分发解耦，并引入动态图片处理和多后端支持，为CINA.CLUB平台的所有富媒体功能提供了坚实的基础设施。