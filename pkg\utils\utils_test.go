/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package utils_test

import (
	"testing"
	"time"

	"cina.club/pkg/utils/conv"
	"cina.club/pkg/utils/crypto"
	"cina.club/pkg/utils/rand"
	"cina.club/pkg/utils/slice"
	"cina.club/pkg/utils/str"
	"cina.club/pkg/utils/timeutil"
	"cina.club/pkg/utils/validator"
)

func TestConvPackage(t *testing.T) {
	// Test pointer conversion
	value := 42
	ptr := conv.ToPointer(value)
	if *ptr != value {
		t.<PERSON><PERSON><PERSON>("ToPointer failed: expected %d, got %d", value, *ptr)
	}

	back := conv.FromPointer(ptr, 0)
	if back != value {
		t.<PERSON>rrorf("FromPointer failed: expected %d, got %d", value, back)
	}

	// Test string conversion
	result := conv.Atoi("123", 0)
	if result != 123 {
		t.<PERSON>rrorf("At<PERSON> failed: expected 123, got %d", result)
	}

	result = conv.Atoi("invalid", 999)
	if result != 999 {
		t.<PERSON><PERSON><PERSON>("Atoi default failed: expected 999, got %d", result)
	}

	str := conv.ToString(123)
	if str != "123" {
		t.Errorf("ToString failed: expected '123', got '%s'", str)
	}
}

func TestCryptoPackage(t *testing.T) {
	// Test hashing
	data := "hello world"
	hash := crypto.SHA256String(data)
	if len(hash) != 64 { // SHA256 hex string length
		t.Errorf("SHA256 failed: expected 64 characters, got %d", len(hash))
	}

	// Test password hashing
	password := "mySecretPassword123!"
	hash, err := crypto.HashPassword(password)
	if err != nil {
		t.Errorf("HashPassword failed: %v", err)
	}

	if !crypto.CheckPasswordHash(password, hash) {
		t.Error("CheckPasswordHash failed: password should match")
	}

	if crypto.CheckPasswordHash("wrongPassword", hash) {
		t.Error("CheckPasswordHash failed: wrong password should not match")
	}
}

func TestRandPackage(t *testing.T) {
	// Test random string generation
	str, err := rand.String(10)
	if err != nil {
		t.Errorf("String generation failed: %v", err)
	}
	if len(str) != 10 {
		t.Errorf("String length failed: expected 10, got %d", len(str))
	}

	// Test digits
	digits, err := rand.Digits(5)
	if err != nil {
		t.Errorf("Digits generation failed: %v", err)
	}
	if len(digits) != 5 {
		t.Errorf("Digits length failed: expected 5, got %d", len(digits))
	}

	// Check that digits are actually digits
	for _, r := range digits {
		if r < '0' || r > '9' {
			t.Errorf("Invalid digit found: %c", r)
		}
	}
}

func TestSlicePackage(t *testing.T) {
	// Test Contains
	numbers := []int{1, 2, 3, 4, 5}
	if !slice.Contains(numbers, 3) {
		t.Error("Contains failed: should find 3 in slice")
	}
	if slice.Contains(numbers, 6) {
		t.Error("Contains failed: should not find 6 in slice")
	}

	// Test Unique
	duplicates := []int{1, 2, 2, 3, 3, 3, 4}
	unique := slice.Unique(duplicates)
	expected := []int{1, 2, 3, 4}
	if len(unique) != len(expected) {
		t.Errorf("Unique failed: expected length %d, got %d", len(expected), len(unique))
	}

	// Test Find
	found, exists := slice.Find(numbers, func(x int) bool { return x > 3 })
	if !exists || found != 4 {
		t.Errorf("Find failed: expected 4, got %d (exists: %v)", found, exists)
	}
}

func TestStrPackage(t *testing.T) {
	// Test case conversion
	snake := str.ToSnakeCase("HelloWorld")
	if snake != "hello_world" {
		t.Errorf("ToSnakeCase failed: expected 'hello_world', got '%s'", snake)
	}

	camel := str.ToCamelCase("hello_world")
	if camel != "helloWorld" {
		t.Errorf("ToCamelCase failed: expected 'helloWorld', got '%s'", camel)
	}

	// Test truncate
	text := "Hello, World!"
	truncated := str.Truncate(text, 8, "...")
	if truncated != "Hello..." {
		t.Errorf("Truncate failed: expected 'Hello...', got '%s'", truncated)
	}

	// Test template rendering
	template := "Hello {name}, you are {age} years old!"
	data := map[string]interface{}{
		"name": "John",
		"age":  30,
	}
	result := str.RenderTemplate(template, data)
	expected := "Hello John, you are 30 years old!"
	if result != expected {
		t.Errorf("RenderTemplate failed: expected '%s', got '%s'", expected, result)
	}
}

func TestTimeUtilPackage(t *testing.T) {
	// Test timestamp functions
	now := time.Now()
	millis := timeutil.NowMillis()
	if millis <= 0 {
		t.Error("NowMillis failed: should return positive value")
	}

	seconds := timeutil.NowSeconds()
	if seconds <= 0 {
		t.Error("NowSeconds failed: should return positive value")
	}

	// Test start/end of day
	start := timeutil.StartOfDay(now)
	if start.Hour() != 0 || start.Minute() != 0 || start.Second() != 0 {
		t.Error("StartOfDay failed: should return midnight")
	}

	end := timeutil.EndOfDay(now)
	if end.Hour() != 23 || end.Minute() != 59 || end.Second() != 59 {
		t.Error("EndOfDay failed: should return end of day")
	}

	// Test age calculation
	birthDate := time.Date(1990, 5, 15, 0, 0, 0, 0, time.UTC)
	atDate := time.Date(2020, 5, 15, 0, 0, 0, 0, time.UTC)
	age := timeutil.AgeAt(birthDate, atDate)
	if age != 30 {
		t.Errorf("AgeAt failed: expected 30, got %d", age)
	}
}

func TestValidatorPackage(t *testing.T) {
	// Test email validation
	if !validator.IsEmail("<EMAIL>") {
		t.Error("IsEmail failed: should validate correct email")
	}
	if validator.IsEmail("invalid.email") {
		t.Error("IsEmail failed: should reject invalid email")
	}

	// Test URL validation
	if !validator.IsURL("https://example.com") {
		t.Error("IsURL failed: should validate correct URL")
	}
	if validator.IsURL("invalid-url") {
		t.Error("IsURL failed: should reject invalid URL")
	}

	// Test numeric validation
	if !validator.IsNumeric("12345") {
		t.Error("IsNumeric failed: should validate numeric string")
	}
	if validator.IsNumeric("123abc") {
		t.Error("IsNumeric failed: should reject non-numeric string")
	}

	// Test password strength
	strength := validator.CheckPasswordStrength("MyPassword123!")
	if strength < validator.PasswordGood {
		t.Errorf("CheckPasswordStrength failed: expected at least Good, got %s", strength.String())
	}

	weak := validator.CheckPasswordStrength("weak")
	if weak != validator.PasswordVeryWeak {
		t.Errorf("CheckPasswordStrength failed: expected VeryWeak, got %s", weak.String())
	}
}
