/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package middleware

import (
	"log/slog"

	"github.com/prometheus/client_golang/prometheus"
	"google.golang.org/grpc"
)

// ChainConfig 拦截器链配置
type ChainConfig struct {
	// ServiceName 服务名称
	ServiceName string
	// Logger 日志器
	Logger *slog.Logger
	// MetricsRegisterer Prometheus 指标注册器
	MetricsRegisterer prometheus.Registerer
	// EnableRecovery 是否启用 panic 恢复
	EnableRecovery bool
	// EnableTracing 是否启用分布式追踪
	EnableTracing bool
	// EnableLogging 是否启用请求日志
	EnableLogging bool
	// EnableMetrics 是否启用指标收集
	EnableMetrics bool
	// EnableValidation 是否启用请求验证
	EnableValidation bool
	// RecoveryOptions 恢复选项
	RecoveryOptions *RecoveryOptions
	// TracingOptions 追踪选项
	TracingOptions *TracingOptions
	// LoggingOptions 日志选项
	LoggingOptions *LoggingOptions
	// ValidationOptions 验证选项
	ValidationOptions *ValidationOptions
}

// DefaultChainConfig 返回默认的链配置
func DefaultChainConfig(serviceName string, logger *slog.Logger) *ChainConfig {
	return &ChainConfig{
		ServiceName:       serviceName,
		Logger:            logger,
		MetricsRegisterer: prometheus.DefaultRegisterer,
		EnableRecovery:    true,
		EnableTracing:     true,
		EnableLogging:     true,
		EnableMetrics:     true,
		EnableValidation:  true,
		RecoveryOptions:   DefaultRecoveryOptions(),
		TracingOptions:    DefaultTracingOptions(serviceName),
		LoggingOptions:    DefaultLoggingOptions(),
		ValidationOptions: DefaultValidationOptions(),
	}
}

// BuildUnaryChain 构建一元拦截器链
// 按照推荐的顺序组合拦截器：Recovery -> Tracing -> Logging -> Metrics -> Validation
func BuildUnaryChain(config *ChainConfig) []grpc.UnaryServerInterceptor {
	var interceptors []grpc.UnaryServerInterceptor

	// 1. Recovery (最外层) - 捕获所有 panic
	if config.EnableRecovery {
		if config.RecoveryOptions != nil {
			interceptors = append(interceptors, RecoveryInterceptorWithOptions(config.Logger, config.RecoveryOptions))
		} else {
			interceptors = append(interceptors, RecoveryInterceptor(config.Logger))
		}
	}

	// 2. Tracing - 创建或延续追踪 span
	if config.EnableTracing {
		if config.TracingOptions != nil {
			interceptors = append(interceptors, TracingInterceptorWithOptions(config.TracingOptions))
		} else {
			interceptors = append(interceptors, TracingInterceptor(config.ServiceName))
		}
	}

	// 3. Logging - 记录请求日志并注入上下文 logger
	if config.EnableLogging {
		if config.LoggingOptions != nil {
			interceptors = append(interceptors, LoggingInterceptorWithOptions(config.Logger, config.LoggingOptions))
		} else {
			interceptors = append(interceptors, LoggingInterceptor(config.Logger))
		}
	}

	// 4. Metrics - 收集 Prometheus 指标
	if config.EnableMetrics {
		serverMetrics := NewServerMetrics()
		if config.MetricsRegisterer != nil {
			serverMetrics.Register(config.MetricsRegisterer)
		}
		interceptors = append(interceptors, serverMetrics.UnaryServerInterceptor())
	}

	// 5. Validation (最内层) - 验证请求体
	if config.EnableValidation {
		if config.ValidationOptions != nil {
			interceptors = append(interceptors, ValidationInterceptorWithOptions(config.ValidationOptions))
		} else {
			interceptors = append(interceptors, ValidationInterceptor())
		}
	}

	return interceptors
}

// BuildStreamChain 构建流拦截器链
func BuildStreamChain(config *ChainConfig) []grpc.StreamServerInterceptor {
	var interceptors []grpc.StreamServerInterceptor

	// 1. Recovery (最外层)
	if config.EnableRecovery {
		interceptors = append(interceptors, RecoveryStreamInterceptor(config.Logger))
	}

	// 2. Tracing
	if config.EnableTracing {
		interceptors = append(interceptors, TracingStreamInterceptor(config.ServiceName))
	}

	// 3. Logging
	if config.EnableLogging {
		interceptors = append(interceptors, LoggingStreamInterceptor(config.Logger))
	}

	// 4. Metrics
	if config.EnableMetrics {
		serverMetrics := NewServerMetrics()
		if config.MetricsRegisterer != nil {
			serverMetrics.Register(config.MetricsRegisterer)
		}
		interceptors = append(interceptors, serverMetrics.StreamServerInterceptor())
	}

	// 5. Validation (最内层)
	if config.EnableValidation {
		interceptors = append(interceptors, ValidationStreamInterceptor())
	}

	return interceptors
}

// SimpleChain 简单链构造器
type SimpleChain struct {
	serviceName string
	logger      *slog.Logger
	config      *ChainConfig
}

// NewSimpleChain 创建简单链构造器
func NewSimpleChain(serviceName string, logger *slog.Logger) *SimpleChain {
	return &SimpleChain{
		serviceName: serviceName,
		logger:      logger,
		config:      DefaultChainConfig(serviceName, logger),
	}
}

// WithRecovery 启用/禁用 Recovery
func (c *SimpleChain) WithRecovery(enable bool) *SimpleChain {
	c.config.EnableRecovery = enable
	return c
}

// WithTracing 启用/禁用 Tracing
func (c *SimpleChain) WithTracing(enable bool) *SimpleChain {
	c.config.EnableTracing = enable
	return c
}

// WithLogging 启用/禁用 Logging
func (c *SimpleChain) WithLogging(enable bool) *SimpleChain {
	c.config.EnableLogging = enable
	return c
}

// WithMetrics 启用/禁用 Metrics
func (c *SimpleChain) WithMetrics(enable bool) *SimpleChain {
	c.config.EnableMetrics = enable
	return c
}

// WithValidation 启用/禁用 Validation
func (c *SimpleChain) WithValidation(enable bool) *SimpleChain {
	c.config.EnableValidation = enable
	return c
}

// WithMetricsRegisterer 设置指标注册器
func (c *SimpleChain) WithMetricsRegisterer(reg prometheus.Registerer) *SimpleChain {
	c.config.MetricsRegisterer = reg
	return c
}

// WithRecoveryOptions 设置恢复选项
func (c *SimpleChain) WithRecoveryOptions(opts *RecoveryOptions) *SimpleChain {
	c.config.RecoveryOptions = opts
	return c
}

// WithTracingOptions 设置追踪选项
func (c *SimpleChain) WithTracingOptions(opts *TracingOptions) *SimpleChain {
	c.config.TracingOptions = opts
	return c
}

// WithLoggingOptions 设置日志选项
func (c *SimpleChain) WithLoggingOptions(opts *LoggingOptions) *SimpleChain {
	c.config.LoggingOptions = opts
	return c
}

// WithValidationOptions 设置验证选项
func (c *SimpleChain) WithValidationOptions(opts *ValidationOptions) *SimpleChain {
	c.config.ValidationOptions = opts
	return c
}

// BuildUnary 构建一元拦截器链
func (c *SimpleChain) BuildUnary() []grpc.UnaryServerInterceptor {
	return BuildUnaryChain(c.config)
}

// BuildStream 构建流拦截器链
func (c *SimpleChain) BuildStream() []grpc.StreamServerInterceptor {
	return BuildStreamChain(c.config)
}

// PresetChains 预设链
type PresetChains struct{}

// ProductionChain 生产环境链（全功能）
func (PresetChains) ProductionChain(serviceName string, logger *slog.Logger) *ChainConfig {
	return DefaultChainConfig(serviceName, logger)
}

// DevelopmentChain 开发环境链（排除某些健康检查方法的日志）
func (PresetChains) DevelopmentChain(serviceName string, logger *slog.Logger) *ChainConfig {
	config := DefaultChainConfig(serviceName, logger)

	// 开发环境可能需要更多的日志详细信息
	config.LoggingOptions.EnableRequestLogging = true
	config.LoggingOptions.EnableResponseLogging = false // 出于性能考虑

	return config
}

// TestingChain 测试环境链（最小化功能）
func (PresetChains) TestingChain(serviceName string, logger *slog.Logger) *ChainConfig {
	config := DefaultChainConfig(serviceName, logger)

	// 测试环境可能不需要指标和复杂的追踪
	config.EnableMetrics = false
	config.EnableTracing = false
	config.ValidationOptions.SkipValidation = false // 仍然需要验证以确保测试数据正确

	return config
}

// MinimalChain 最小链（只有 Recovery 和 Validation）
func (PresetChains) MinimalChain(serviceName string, logger *slog.Logger) *ChainConfig {
	config := DefaultChainConfig(serviceName, logger)

	config.EnableTracing = false
	config.EnableLogging = false
	config.EnableMetrics = false
	// 保留 Recovery 和 Validation 以确保基本的稳定性和正确性

	return config
}

// Presets 预设实例
var Presets = PresetChains{}

// QuickStart 快速开始函数，返回标准的拦截器组合
func QuickStart(serviceName string, logger *slog.Logger) ([]grpc.UnaryServerInterceptor, []grpc.StreamServerInterceptor) {
	config := DefaultChainConfig(serviceName, logger)
	return BuildUnaryChain(config), BuildStreamChain(config)
}
