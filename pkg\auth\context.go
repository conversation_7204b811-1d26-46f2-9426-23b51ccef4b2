/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 10:00:00
Modified: 2025-01-21 10:00:00
*/

package auth

import (
	"context"
	"errors"
)

// Private context keys to prevent external package conflicts
type contextKey string

const (
	userContextKey    contextKey = "auth_user"
	serviceContextKey contextKey = "auth_service"
)

var (
	ErrUserNotFound    = errors.New("authenticated user not found in context")
	ErrServiceNotFound = errors.New("service identity not found in context")
)

// NewContextWithUser injects authenticated user information into the context
func NewContextWithUser(ctx context.Context, user *AuthenticatedUser) context.Context {
	return context.WithValue(ctx, userContextKey, user)
}

// UserFromContext safely extracts authenticated user information from the context
// Returns an error if user information is not present
func UserFromContext(ctx context.Context) (*AuthenticatedUser, error) {
	user, ok := ctx.Value(userContextKey).(*AuthenticatedUser)
	if !ok || user == nil {
		return nil, ErrUserNotFound
	}
	return user, nil
}

// MustUserFromContext extracts user from context and panics if not found
// Use only when you're certain the user context exists (after successful user JWT interceptor)
func MustUserFromContext(ctx context.Context) *AuthenticatedUser {
	user, err := UserFromContext(ctx)
	if err != nil {
		panic("expected authenticated user in context but found none")
	}
	return user
}

// NewContextWithService injects service identity information into the context
func NewContextWithService(ctx context.Context, service *ServiceIdentity) context.Context {
	return context.WithValue(ctx, serviceContextKey, service)
}

// ServiceFromContext safely extracts service identity information from the context
// Returns an error if service information is not present
func ServiceFromContext(ctx context.Context) (*ServiceIdentity, error) {
	service, ok := ctx.Value(serviceContextKey).(*ServiceIdentity)
	if !ok || service == nil {
		return nil, ErrServiceNotFound
	}
	return service, nil
}

// MustServiceFromContext extracts service from context and panics if not found
// Use only when you're certain the service context exists (after successful S2S JWT interceptor)
func MustServiceFromContext(ctx context.Context) *ServiceIdentity {
	service, err := ServiceFromContext(ctx)
	if err != nil {
		panic("expected service identity in context but found none")
	}
	return service
}

// HasUser checks if the context contains an authenticated user
func HasUser(ctx context.Context) bool {
	_, err := UserFromContext(ctx)
	return err == nil
}

// HasService checks if the context contains a service identity
func HasService(ctx context.Context) bool {
	_, err := ServiceFromContext(ctx)
	return err == nil
}
