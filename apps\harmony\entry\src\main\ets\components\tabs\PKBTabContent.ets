/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

/**
 * 个人知识库Tab内容组件
 */
@Component
export struct PKBTabContent {
  @State private notes: string[] = ['工作计划', '学习笔记', '读书心得'];

  build() {
    Column() {
      // 标题栏
      Row() {
        Text('个人知识库')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
        
        Blank()
        
        Button('新建')
          .fontSize(14)
          .height(32)
          .backgroundColor('#007AFF')
          .borderRadius(16)
      }
      .width('100%')
      .padding(16)
      
      // 搜索框
      TextInput({ placeholder: '搜索笔记...' })
        .backgroundColor('#F5F5F5')
        .borderRadius(20)
        .margin({ left: 16, right: 16, bottom: 16 })
      
      // 笔记列表
      if (this.notes.length > 0) {
        List() {
          ForEach(this.notes, (note: string) => {
            ListItem() {
              Row() {
                Text('📝')
                  .fontSize(20)
                  .margin({ right: 12 })
                
                Column() {
                  Text(note)
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                  
                  Text('这是笔记预览内容...')
                    .fontSize(14)
                    .fontColor('#666666')
                    .margin({ top: 4 })
                }
                .alignItems(HorizontalAlign.Start)
                
                Blank()
                
                Text('>')
                  .fontSize(16)
                  .fontColor('#999999')
              }
              .width('100%')
              .padding(16)
            }
          })
        }
        .layoutWeight(1)
        .divider({ strokeWidth: 1, color: '#F0F0F0' })
      } else {
        Column() {
          Text('📚')
            .fontSize(48)
            .margin({ bottom: 16 })
          
          Text('暂无笔记')
            .fontSize(16)
            .fontColor('#666666')
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
  }
} 