好的，遵照您的指示。我将为您生成一份专门针对 **`schedule-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`schedule-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**复杂重复日程(RRULE)的计算与存储优化、多日历与共享权限的实现、与`calendar-sync-service`的高效协同，以及作为平台时间管理中枢的性能与准确性**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `schedule-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `schedule-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 为时间序列查询优化的数据模型 + 缓存与预计算

## 1. 概述

`schedule-service` 是CINA.CLUB平台的时间管理**核心引擎**。它为所有需要处理时间、日程、预订的功能提供基础数据和逻辑。其核心挑战在于：
1.  **重复日程的复杂性**: 精确、高效地处理iCalendar RFC 5545标准下的复杂重复规则（RRULE），包括例外(EXDATE)和实例覆盖(Override)，是日程服务的核心技术难点。
2.  **高性能查询**: 日历视图的查询（如获取一个月的所有日程实例）是一个计算密集型操作，需要高效的展开(expansion)和查询算法。
3.  **忙闲状态(Free/Busy)计算**: 需要能快速地为多个用户计算出其在指定时间段内的忙闲状态，以支持预订和协调。
4.  **权限与共享**: 需要实现一个支持多日历和不同共享权限级别（只读、读写、只看忙闲）的访问控制模型。
5.  **与同步服务的协同**: 当日程变更时，需要可靠地通知`calendar-sync-service`，以保证与外部日历的数据一致性。

本架构设计通过采用**整洁架构**，在领域层封装复杂的**RRULE计算引擎**，并结合**查询时展开(Read-time Expansion)与缓存**的策略来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (日程查询与展开流程)

```mermaid
graph TD
    subgraph "客户端/调用方"
        style "客户端/调用方" fill:#eee
        ClientApp
        AIAssistant[ai-assistant-service]
    end

    subgraph "ScheduleService"
        style ScheduleService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[QueryService<br/><em>application/query</em>]
        C[RRULEExpansionEngine<br/><em>domain/service</em>]
        D[PermissionService<br/><em>domain/service</em>]
        E[Repository (Postgres)<br/><em>adapter/repository</em>]
        F[Redis Cache<br/><em>adapter/cache</em>]
    end

    subgraph "下游依赖"
        style "下游依赖" fill:#f3e5f5
        Kafka[(Kafka)]
        NotificationSvc[notification-dispatch-service]
    end
    
    ClientApp -- "1. ListEventInstances(start, end)" --> A
    A -- "调用" --> B
    
    B -- "2. Check Cache" --> F
    subgraph "Cache Miss Path"
        direction LR
        B -- "3. Get Raw Events from DB" --> E
        E -- "Returns Master Events, Overrides, Exceptions" --> B
        B -- "4. Expand Recurring Events" --> C
        C -- "Uses iCal library" --> C
        C -- "Returns expanded instances" --> B
        B -- "5. Merge & Reconcile" --> B
        B -- "6. Write to Cache" --> F
    end

    B -- "7. Check Permissions" --> D
    D -- "Filters results" --> B
    B -- "8. Return Final List" --> A
    
    subgraph "提醒流程"
        Scheduler[(Cron Job)] -- "Triggers" --> B
        B -- "Find due reminders" --> E
        B -- "Publish ReminderDueEvent" --> Kafka
        Kafka --> NotificationSvc
    end
```

### 2.2 最终目录结构 (`services/schedule-service/`)

```
schedule-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # ✨ 提醒调度等后台任务的独立入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_cache.go  # 缓存展开后的日程实例
│   │   ├── event/
│   │   │   └── producer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── postgres_repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── command/
│   │   │   └── schedule_command_service.go # 处理写操作
│   │   └── query/
│   │       └── schedule_query_service.go   # ✨ 处理读操作和日程展开 ✨
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── rrule_expansion_engine.go # ✨ RRULE计算引擎 ✨
│           └── permission_service.go # 共享权限检查服务
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Rules of Time)

*   `domain/model/`: 使用`/core/models`中与日程相关的`struct`，如`Event`, `Calendar`, `Attendee`。
*   **`domain/service/rrule_expansion_engine.go`**: **这是本服务技术上最核心、最复杂的部分**。
    *   **`RRULEExpansionEngine`**: 一个无状态的领域服务。它**必须**依赖一个经过充分测试的、健壮的iCalendar库（如`teambition/rrule-go`）。
    *   **`Expand(masterEvent, overrides, exceptions, start, end)` method**:
        1.  **纯函数**: 接收一个主事件（包含RRULE字符串和时区）、一个该事件的所有覆盖实例列表、一个例外日期列表，以及一个查询时间窗口。
        2.  **步骤1 (生成基础实例)**: 调用底层iCal库，根据`masterEvent.RRULE`和`masterEvent.TimeZone`，生成在`[start, end]`时间窗口内的所有理论发生实例。
        3.  **步骤2 (应用例外)**: 从生成的基础实例中，移除所有`start_time`在`exceptions`列表中的实例。
        4.  **步骤3 (应用覆盖)**:
            a. 遍历`overrides`列表。
            b. 对于每个覆盖实例，在基础实例列表中找到其对应的原始实例（通过`original_instance_start_time`匹配）。
            c. 将找到的原始实例替换为覆盖实例的内容。
        5.  返回一个最终的、准确的、在该时间窗口内发生的所有事件实例的列表。
*   **`domain/service/permission_service.go`**:
    *   **`PermissionService`**: 封装了复杂的日历共享权限检查逻辑。
    *   **`FilterEvents(user, events, calendarPermissions)`**: 接收一个用户、一个事件列表和该用户对相关日历的权限映射。
        *   **逻辑**: 遍历事件列表，根据权限映射，决定每个事件对该用户是否可见，以及可见的详细程度（是只显示"忙碌"，还是显示完整详情）。
        *   返回一个过滤后的事件列表。

### 3.2 `application/` - 应用层 (The Use Cases)

采用CQRS思想，分离读写路径。

*   **`application/command/schedule_command_service.go`**: **处理所有写操作**。
    *   **`CreateEvent(ctx, eventData)`**:
        1.  权限检查。
        2.  开启数据库事务。
        3.  调用`repository.CreateEvent()`持久化。
        4.  提交事务。
        5.  **发布`ScheduleChangedEvent`到Kafka**，通知`calendar-sync-service`等下游。
    *   **`UpdateEvent(ctx, updateData)`**:
        *   **复杂性**: 更新一个重复性日程的实例，是最复杂的操作。
        *   **逻辑**:
            *   **更新整个系列**: 修改主事件的`RRULE`。
            *   **只更新此实例**: 在`event_overrides`表中创建一个新的覆盖记录。
            *   **只更新此实例及后续**: 复杂操作，需要结束旧的重复系列（修改`UNTIL`），并创建一个新的、从当前实例开始的重复系列。
        *   操作完成后，同样发布`ScheduleChangedEvent`。

*   **`application/query/schedule_query_service.go`**: **处理所有读操作，是性能优化的关键**。
    *   **`ListEventInstances(ctx, userID, start, end, calendarIDs)`**:
        1.  **缓存检查**: 构造缓存Key（如`events:{userID}:{YYYY-MM}`），尝试从`port.Cache`获取结果。
        2.  **缓存未命中**:
            a. 调用`repository.GetCalendarsAndPermissions(userID, calendarIDs)`获取用户有权访问的日历和权限。
            b. **调用`repository.GetRawDataInWindow(...)`**，从数据库中一次性获取时间窗口内所有的单次事件、重复主事件、覆盖实例和例外日期。
            c. **调用`domain.RRULEExpansionEngine.Expand(...)`**对每个重复主事件进行展开。
            d. 合并所有单次事件和展开后的实例。
            e. **调用`domain.PermissionService.FilterEvents(...)`**根据权限过滤结果。
            f. 将最终结果写入缓存。
        3.  返回最终的事件实例列表。
    *   **`GetFreeBusy(ctx, userIDs, start, end)`**:
        *   为每个`userID`调用`ListEventInstances`获取其所有忙碌时段。
        *   合并所有用户的忙碌时段并返回。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库**: **PostgreSQL**。
    *   `postgres_repo.go`:
        *   **`GetRawDataInWindow`**: **这是一个经过高度优化的查询**。它需要能高效地找出所有**可能**在时间窗口内有发生实例的重复主事件。这通常通过检查`rrule`中是否有`UNTIL`或`COUNT`限制，或者对于没有限制的事件，全部取出。
*   **`adapter/cache/`**:
    *   `redis_cache.go`:
        *   **缓存内容**: 缓存`ListEventInstances`的**最终结果**（展开和过滤后的事件列表），序列化为JSON。
        *   **失效策略**: 任何写操作（Create/Update/Delete Event）都**必须**主动、精确地使其相关的缓存失效。例如，修改了`user123`在2025年7月的日程，则必须删除`events:user123:2025-07`这个Key。
*   **`adapter/event/`**:
    *   `producer.go`: 在所有写操作成功后，发布`ScheduleChangedEvent`。

### 3.4 `cmd/worker/` - 后台提醒调度器

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   以**Kubernetes CronJob**的形式**每分钟**运行一次。
    *   **工作**:
        a. 查询`event_reminders`表中所有需要在**接下来的一分钟内**触发的提醒。
        b. 对于每个需要触发的提醒，发布一个`ReminderDueEvent`到Kafka。
        c. `notification-dispatch-service`消费此事件并发送实际的通知。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`schedule-service`：
1.  **领域驱动的计算引擎**: 将极其复杂的**RRULE展开逻辑**封装在无状态的`RRULEExpansionEngine`领域服务中，使其与数据持久化和API请求解耦，并易于进行严格的单元测试。
2.  **查询时展开与缓存 (Read-time Expansion with Caching)**:
    *   **写操作简单化**: 写入时只存储“规则”（Master Event），不预先生成所有实例，这使得写操作非常快。
    *   **读操作智能化**: 在查询时，动态地、按需地展开日程实例。并通过对查询结果的积极缓存，来平衡计算开销和查询性能。
3.  **CQRS思想**: 将高计算复杂度的读操作(`QueryService`)与事务性的写操作(`CommandService`)分离，使得各自的实现可以被独立优化。
4.  **精细化权限模型**: 通过`PermissionService`在数据返回前的最后一刻进行过滤，确保了共享日历的隐私和权限控制是可靠的。
5.  **可靠的异步通知**: 将提醒调度和与外部系统的同步，通过Kafka事件进行解耦，保证了核心服务的性能和可靠性。

这种架构确保了`schedule-service`在处理时间管理的内在复杂性的同时，能够提供**准确、高性能、且安全**的服务，成为CINA.CLUB平台所有时间相关业务的坚实基础。