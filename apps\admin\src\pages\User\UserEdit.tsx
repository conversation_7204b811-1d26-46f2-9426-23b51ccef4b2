/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 11:15:00
Modified: 2025-01-23 11:15:00
*/

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Form, 
  Input, 
  Select, 
  Switch, 
  DatePicker, 
  Upload, 
  message, 
  Typography,
  Space,
  Avatar,
  Tag,
  Tabs,
  Table,
  Modal,
  Alert,
  Progress,
  Badge,
  Descriptions,
  Popconfirm
} from 'antd'
import { 
  UserOutlined, 
  SaveOutlined,
  KeyOutlined,
  MailOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UploadOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'

import { User, UserRole, UserStatus, Permission } from '@/types/user'
import { usePermission } from '@/store/auth'
import { Loading } from '@/components/Loading'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs

// Mock user data
const mockUserData: User = {
  id: 'user123',
  username: 'john_doe',
  email: '<EMAIL>',
  firstName: '约翰',
  lastName: '多伊',
  displayName: '约翰·多伊',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
  role: UserRole.USER,
  status: UserStatus.ACTIVE,
  emailVerified: true,
  phoneVerified: false,
  phone: '******-0123',
  dateOfBirth: '1990-05-15',
  bio: '资深软件开发工程师，专注于前端技术和用户体验设计。',
  location: '纽约, 美国',
  website: 'https://johndoe.dev',
  company: 'Tech Solutions Inc.',
  position: '高级前端开发工程师',
  timezone: 'America/New_York',
  language: 'zh-CN',
  metadata: {
    preferences: {
      theme: 'light',
      notifications: true,
      newsletter: true,
    },
  },
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2025-01-20T14:30:00Z',
  lastLoginAt: '2025-01-23T09:15:00Z',
  permissions: [Permission.USER_READ, Permission.CONTENT_READ],
}

// Mock activity data
const mockUserActivity = [
  {
    id: '1',
    action: '登录系统',
    timestamp: '2025-01-23T09:15:00Z',
    ip: '*************',
    userAgent: 'Chrome 120.0.0.0',
    location: '纽约, 美国',
    status: 'success',
  },
  {
    id: '2',
    action: '更新个人资料',
    timestamp: '2025-01-22T16:30:00Z',
    ip: '*************',
    userAgent: 'Chrome 120.0.0.0',
    location: '纽约, 美国',
    status: 'success',
  },
  {
    id: '3',
    action: '登录失败',
    timestamp: '2025-01-22T08:45:00Z',
    ip: '***********',
    userAgent: 'Firefox 121.0',
    location: '未知位置',
    status: 'failed',
  },
]

/**
 * User Edit Page Component
 */
const UserEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { hasPermission } = usePermission()
  const [form] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [userData, setUserData] = useState<User | null>(null)
  const [activeTab, setActiveTab] = useState('profile')
  const [passwordModalVisible, setPasswordModalVisible] = useState(false)
  const [avatarUploading, setAvatarUploading] = useState(false)

  // Permission checks
  const canEditUser = hasPermission(Permission.USER_UPDATE)
  const canDeleteUser = hasPermission(Permission.USER_DELETE)
  const canManageRoles = hasPermission(Permission.USER_ROLE_MANAGE)
  const canViewActivity = hasPermission(Permission.USER_ACTIVITY_VIEW)

  // Load user data
  useEffect(() => {
    const fetchUser = async () => {
      if (!id) return
      
      setLoading(true)
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        setUserData(mockUserData)
        
        form.setFieldsValue({
          ...mockUserData,
          dateOfBirth: mockUserData.dateOfBirth ? dayjs(mockUserData.dateOfBirth) : null,
        })
      } catch (error) {
        message.error('加载用户数据失败')
        navigate('/users')
      } finally {
        setLoading(false)
      }
    }

    fetchUser()
  }, [id, form, navigate])

  // Handle form submission
  const handleSubmit = async (values: any) => {
    if (!canEditUser) {
      message.error('您没有权限编辑用户')
      return
    }

    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('用户信息更新成功')
      setUserData(prev => ({ ...prev!, ...values }))
    } catch (error) {
      message.error('更新失败')
    } finally {
      setLoading(false)
    }
  }

  // Handle password reset
  const handlePasswordReset = async (values: any) => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('密码重置成功，新密码已发送到用户邮箱')
      setPasswordModalVisible(false)
      passwordForm.resetFields()
    } catch (error) {
      message.error('密码重置失败')
    } finally {
      setLoading(false)
    }
  }

  // Handle status change
  const handleStatusChange = async (status: UserStatus) => {
    if (!canEditUser) {
      message.error('您没有权限修改用户状态')
      return
    }

    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      setUserData(prev => ({ ...prev!, status }))
      message.success(`用户状态已更新为${status}`)
    } catch (error) {
      message.error('状态更新失败')
    }
  }

  // Handle role change
  const handleRoleChange = async (role: UserRole) => {
    if (!canManageRoles) {
      message.error('您没有权限修改用户角色')
      return
    }

    Modal.confirm({
      title: '确认角色变更',
      content: `确定要将用户角色更改为 ${role} 吗？这将影响用户的权限。`,
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 500))
          setUserData(prev => ({ ...prev!, role }))
          message.success('用户角色更新成功')
        } catch (error) {
          message.error('角色更新失败')
        }
      },
    })
  }

  // Handle user deletion
  const handleDeleteUser = async () => {
    if (!canDeleteUser) {
      message.error('您没有权限删除用户')
      return
    }

    Modal.confirm({
      title: '确认删除用户',
      content: '删除用户后无法恢复，确定要删除这个用户吗？',
      okText: '确定删除',
      okType: 'danger',
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 1000))
          message.success('用户删除成功')
          navigate('/users')
        } catch (error) {
          message.error('删除失败')
        }
      },
    })
  }

  // Handle avatar upload
  const handleAvatarUpload = async (file: any) => {
    setAvatarUploading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      const newAvatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`
      setUserData(prev => ({ ...prev!, avatar: newAvatarUrl }))
      message.success('头像上传成功')
    } catch (error) {
      message.error('头像上传失败')
    } finally {
      setAvatarUploading(false)
    }
    return false
  }

  // Get status/role text functions
  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE: return 'green'
      case UserStatus.INACTIVE: return 'orange'
      case UserStatus.SUSPENDED: return 'red'
      case UserStatus.PENDING: return 'blue'
      default: return 'default'
    }
  }

  const getStatusText = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE: return '活跃'
      case UserStatus.INACTIVE: return '不活跃'
      case UserStatus.SUSPENDED: return '已暂停'
      case UserStatus.PENDING: return '待激活'
      default: return '未知'
    }
  }

  const getRoleText = (role: UserRole) => {
    switch (role) {
      case UserRole.SUPER_ADMIN: return '超级管理员'
      case UserRole.ADMIN: return '管理员'
      case UserRole.MODERATOR: return '版主'
      case UserRole.EDITOR: return '编辑'
      case UserRole.CONTRIBUTOR: return '贡献者'
      case UserRole.USER: return '普通用户'
      case UserRole.VIEWER: return '访客'
      default: return '未知'
    }
  }

  if (loading && !userData) {
    return <Loading variant="page" />
  }

  if (!userData) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>用户不存在</h3>
        <Button onClick={() => navigate('/users')}>返回用户列表</Button>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'flex-start',
          marginBottom: '16px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Avatar 
              size={64} 
              src={userData.avatar} 
              icon={<UserOutlined />}
              style={{ marginRight: '16px' }}
            />
            <div>
              <Title level={2} style={{ margin: 0, marginBottom: '4px' }}>
                {userData.displayName}
              </Title>
              <div style={{ marginBottom: '8px' }}>
                <Tag color={getStatusColor(userData.status)}>
                  {getStatusText(userData.status)}
                </Tag>
                <Tag color="blue">{getRoleText(userData.role)}</Tag>
                {userData.emailVerified && (
                  <Tag color="green" icon={<CheckCircleOutlined />}>
                    邮箱已验证
                  </Tag>
                )}
              </div>
              <Text type="secondary">
                用户ID: {userData.id} • 最后登录: {dayjs(userData.lastLoginAt).format('YYYY-MM-DD HH:mm')}
              </Text>
            </div>
          </div>
          
          <Space>
            {canEditUser && (
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={() => form.submit()}
                loading={loading}
              >
                保存更改
              </Button>
            )}
            <Button 
              icon={<EyeOutlined />}
              onClick={() => navigate(`/users/${userData.id}`)}
            >
              查看详情
            </Button>
            {canDeleteUser && (
              <Popconfirm
                title="确定要删除这个用户吗？"
                onConfirm={handleDeleteUser}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  删除用户
                </Button>
              </Popconfirm>
            )}
          </Space>
        </div>

        {/* Quick Actions */}
        <div style={{ marginBottom: '16px' }}>
          <Space wrap>
            <Button
              size="small"
              onClick={() => setPasswordModalVisible(true)}
              icon={<KeyOutlined />}
            >
              重置密码
            </Button>
            <Select
              size="small"
              value={userData.status}
              onChange={handleStatusChange}
              style={{ width: 120 }}
              disabled={!canEditUser}
            >
              <Option value={UserStatus.ACTIVE}>激活</Option>
              <Option value={UserStatus.INACTIVE}>禁用</Option>
              <Option value={UserStatus.SUSPENDED}>暂停</Option>
            </Select>
            <Select
              size="small"
              value={userData.role}
              onChange={handleRoleChange}
              style={{ width: 140 }}
              disabled={!canManageRoles}
            >
              <Option value={UserRole.SUPER_ADMIN}>超级管理员</Option>
              <Option value={UserRole.ADMIN}>管理员</Option>
              <Option value={UserRole.MODERATOR}>版主</Option>
              <Option value={UserRole.EDITOR}>编辑</Option>
              <Option value={UserRole.CONTRIBUTOR}>贡献者</Option>
              <Option value={UserRole.USER}>普通用户</Option>
              <Option value={UserRole.VIEWER}>访客</Option>
            </Select>
          </Space>
        </div>
      </div>

      {/* Main Content */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* Profile Tab */}
        <TabPane tab="基本信息" key="profile">
          <Row gutter={24}>
            <Col xs={24} lg={16}>
              <Card title="个人信息" style={{ marginBottom: '24px' }}>
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSubmit}
                  disabled={!canEditUser}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="用户名"
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="邮箱"
                        name="email"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="姓"
                        name="firstName"
                        rules={[{ required: true, message: '请输入姓' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="名"
                        name="lastName"
                        rules={[{ required: true, message: '请输入名' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    label="显示名称"
                    name="displayName"
                    rules={[{ required: true, message: '请输入显示名称' }]}
                  >
                    <Input />
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="手机号码"
                        name="phone"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="出生日期"
                        name="dateOfBirth"
                      >
                        <DatePicker style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    label="个人简介"
                    name="bio"
                  >
                    <TextArea rows={4} />
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="所在地"
                        name="location"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="个人网站"
                        name="website"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="公司"
                        name="company"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="职位"
                        name="position"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="时区"
                        name="timezone"
                      >
                        <Select>
                          <Option value="Asia/Shanghai">Asia/Shanghai</Option>
                          <Option value="America/New_York">America/New_York</Option>
                          <Option value="Europe/London">Europe/London</Option>
                          <Option value="Asia/Tokyo">Asia/Tokyo</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="语言"
                        name="language"
                      >
                        <Select>
                          <Option value="zh-CN">简体中文</Option>
                          <Option value="en-US">English</Option>
                          <Option value="ja-JP">日本語</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </Card>
            </Col>

            <Col xs={24} lg={8}>
              <Card title="头像" style={{ marginBottom: '24px' }}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar 
                    size={120} 
                    src={userData.avatar} 
                    icon={<UserOutlined />}
                    style={{ marginBottom: '16px' }}
                  />
                  <div>
                    <Upload
                      showUploadList={false}
                      beforeUpload={handleAvatarUpload}
                      disabled={!canEditUser}
                    >
                      <Button 
                        icon={<UploadOutlined />}
                        loading={avatarUploading}
                        disabled={!canEditUser}
                      >
                        上传头像
                      </Button>
                    </Upload>
                  </div>
                </div>
              </Card>

              <Card title="账户状态">
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="状态">
                    <Tag color={getStatusColor(userData.status)}>
                      {getStatusText(userData.status)}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="角色">
                    <Tag color="blue">{getRoleText(userData.role)}</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="邮箱验证">
                    {userData.emailVerified ? (
                      <Tag color="green" icon={<CheckCircleOutlined />}>已验证</Tag>
                    ) : (
                      <Tag color="red" icon={<CloseCircleOutlined />}>未验证</Tag>
                    )}
                  </Descriptions.Item>
                  <Descriptions.Item label="手机验证">
                    {userData.phoneVerified ? (
                      <Tag color="green" icon={<CheckCircleOutlined />}>已验证</Tag>
                    ) : (
                      <Tag color="red" icon={<CloseCircleOutlined />}>未验证</Tag>
                    )}
                  </Descriptions.Item>
                  <Descriptions.Item label="创建时间">
                    {dayjs(userData.createdAt).format('YYYY-MM-DD HH:mm')}
                  </Descriptions.Item>
                  <Descriptions.Item label="最后更新">
                    {dayjs(userData.updatedAt).format('YYYY-MM-DD HH:mm')}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Activity Tab */}
        {canViewActivity && (
          <TabPane tab="活动记录" key="activity">
            <Card title="最近活动">
              <Table
                dataSource={mockUserActivity}
                columns={[
                  {
                    title: '操作',
                    dataIndex: 'action',
                    key: 'action',
                  },
                  {
                    title: '时间',
                    dataIndex: 'timestamp',
                    key: 'timestamp',
                    render: (timestamp) => dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss'),
                  },
                  {
                    title: 'IP地址',
                    dataIndex: 'ip',
                    key: 'ip',
                  },
                  {
                    title: '设备',
                    dataIndex: 'userAgent',
                    key: 'userAgent',
                    render: (userAgent) => userAgent.split(' ')[0],
                  },
                  {
                    title: '位置',
                    dataIndex: 'location',
                    key: 'location',
                  },
                  {
                    title: '状态',
                    dataIndex: 'status',
                    key: 'status',
                    render: (status) => (
                      <Tag color={status === 'success' ? 'green' : 'red'}>
                        {status === 'success' ? '成功' : '失败'}
                      </Tag>
                    ),
                  },
                ]}
                pagination={{ pageSize: 10 }}
              />
            </Card>
          </TabPane>
        )}

        {/* Security Tab */}
        <TabPane tab="安全设置" key="security">
          <Row gutter={24}>
            <Col xs={24} lg={12}>
              <Card title="密码管理" style={{ marginBottom: '24px' }}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>密码强度</Text>
                  <Progress 
                    percent={85} 
                    strokeColor="#52c41a"
                    style={{ marginTop: '8px' }}
                  />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    上次更改: 2025-01-15
                  </Text>
                </div>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button 
                    icon={<KeyOutlined />}
                    onClick={() => setPasswordModalVisible(true)}
                    disabled={!canEditUser}
                  >
                    重置密码
                  </Button>
                  <Button 
                    icon={<MailOutlined />}
                    disabled={!canEditUser}
                  >
                    发送密码重置邮件
                  </Button>
                </Space>
              </Card>

              <Card title="登录安全">
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text strong>两步验证</Text>
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          增强账户安全性
                        </Text>
                      </div>
                    </div>
                    <Switch checked={false} disabled={!canEditUser} />
                  </div>
                </div>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text strong>登录通知</Text>
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          新设备登录时发送邮件通知
                        </Text>
                      </div>
                    </div>
                    <Switch checked={true} disabled={!canEditUser} />
                  </div>
                </div>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="会话管理">
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>当前会话</Text>
                  <div style={{ marginTop: '8px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                      <div>
                        <Text>当前设备 (Chrome)</Text>
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            ************* • 纽约, 美国
                          </Text>
                        </div>
                      </div>
                      <Tag color="green">当前</Tag>
                    </div>
                  </div>
                </div>
                
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>其他会话</Text>
                  <div style={{ marginTop: '8px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                      <div>
                        <Text>iPhone Safari</Text>
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            ************ • 2小时前
                          </Text>
                        </div>
                      </div>
                      <Button size="small" danger disabled={!canEditUser}>
                        终止
                      </Button>
                    </div>
                  </div>
                </div>

                <Button 
                  block 
                  danger 
                  icon={<CloseCircleOutlined />}
                  disabled={!canEditUser}
                >
                  终止所有其他会话
                </Button>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* Password Reset Modal */}
      <Modal
        title="重置用户密码"
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        onOk={() => passwordForm.submit()}
        confirmLoading={loading}
      >
        <Form form={passwordForm} onFinish={handlePasswordReset} layout="vertical">
          <Alert
            message="密码重置说明"
            description="重置后，新的临时密码将发送到用户的注册邮箱，用户首次登录时需要修改密码。"
            type="info"
            style={{ marginBottom: '16px' }}
          />
          
          <Form.Item
            label="重置原因"
            name="reason"
            rules={[{ required: true, message: '请输入重置原因' }]}
          >
            <TextArea rows={3} placeholder="请说明密码重置的原因..." />
          </Form.Item>
          
          <Form.Item
            label="通知用户"
            name="notifyUser"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UserEdit 