/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 15:45:00
 * Modified: 2025-01-23 15:45:00
 */

import { apiClient } from '@/lib/api-client';
import type { 
  Service, 
  ServiceStatus, 
  ServiceMetrics, 
  ServiceConfig,
  ServiceLog,
  ServiceAlert,
  HealthCheckResult,
  ServiceListParams,
  ServiceListResponse,
  BulkServiceOperation,
  ServiceActionResult
} from '@/types/service';

/**
 * Service Monitoring API Service
 * Provides comprehensive service management and monitoring functionality
 */
export class ServiceApiService {
  private static readonly BASE_PATH = '/api/v1/services';

  /**
   * Get all services with their current status
   */
  static async getServices(params?: ServiceListParams): Promise<ServiceListResponse> {
    const response = await apiClient.get(this.BASE_PATH, { params });
    return response.data;
  }

  /**
   * Get service by ID with detailed information
   */
  static async getServiceById(id: string): Promise<Service> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}`);
    return response.data;
  }

  /**
   * Get service metrics for a specific time range
   */
  static async getServiceMetrics(
    id: string, 
    timeRange: '1h' | '6h' | '24h' | '7d' | '30d' = '24h'
  ): Promise<ServiceMetrics> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/metrics`, {
      params: { timeRange }
    });
    return response.data;
  }

  /**
   * Get service configuration
   */
  static async getServiceConfig(id: string): Promise<ServiceConfig> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/config`);
    return response.data;
  }

  /**
   * Update service configuration
   */
  static async updateServiceConfig(id: string, config: Partial<ServiceConfig>): Promise<ServiceConfig> {
    const response = await apiClient.put(`${this.BASE_PATH}/${id}/config`, config);
    return response.data;
  }

  /**
   * Start a service
   */
  static async startService(id: string): Promise<ServiceActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/start`);
    return response.data;
  }

  /**
   * Stop a service
   */
  static async stopService(id: string): Promise<ServiceActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/stop`);
    return response.data;
  }

  /**
   * Restart a service
   */
  static async restartService(id: string): Promise<ServiceActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/restart`);
    return response.data;
  }

  /**
   * Get service logs with pagination
   */
  static async getServiceLogs(
    id: string, 
    params?: {
      level?: 'debug' | 'info' | 'warn' | 'error';
      limit?: number;
      offset?: number;
      startTime?: string;
      endTime?: string;
    }
  ): Promise<{
    logs: ServiceLog[];
    total: number;
    hasMore: boolean;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/logs`, { params });
    return response.data;
  }

  /**
   * Stream service logs (WebSocket connection)
   */
  static streamServiceLogs(id: string, onLog: (log: ServiceLog) => void): WebSocket {
    const wsUrl = `ws://${window.location.host}/api/v1/services/${id}/logs/stream`;
    const ws = new WebSocket(wsUrl);
    
    ws.onmessage = (event) => {
      const log = JSON.parse(event.data) as ServiceLog;
      onLog(log);
    };
    
    return ws;
  }

  /**
   * Get service health check
   */
  static async getServiceHealth(id: string): Promise<HealthCheckResult> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/health`);
    return response.data;
  }

  /**
   * Perform health check on service
   */
  static async performHealthCheck(id: string): Promise<HealthCheckResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/health/check`);
    return response.data;
  }

  /**
   * Get service alerts
   */
  static async getServiceAlerts(
    id?: string,
    params?: {
      status?: 'active' | 'resolved' | 'acknowledged';
      severity?: 'low' | 'medium' | 'high' | 'critical';
      limit?: number;
    }
  ): Promise<ServiceAlert[]> {
    const endpoint = id ? `${this.BASE_PATH}/${id}/alerts` : `${this.BASE_PATH}/alerts`;
    const response = await apiClient.get(endpoint, { params });
    return response.data;
  }

  /**
   * Acknowledge service alert
   */
  static async acknowledgeAlert(alertId: string, message?: string): Promise<ServiceAlert> {
    const response = await apiClient.patch(`${this.BASE_PATH}/alerts/${alertId}/acknowledge`, { message });
    return response.data;
  }

  /**
   * Resolve service alert
   */
  static async resolveAlert(alertId: string, message?: string): Promise<ServiceAlert> {
    const response = await apiClient.patch(`${this.BASE_PATH}/alerts/${alertId}/resolve`, { message });
    return response.data;
  }

  /**
   * Get service dependencies
   */
  static async getServiceDependencies(id: string): Promise<{
    dependencies: Service[];
    dependents: Service[];
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/dependencies`);
    return response.data;
  }

  /**
   * Scale service (if supported)
   */
  static async scaleService(id: string, replicas: number): Promise<ServiceActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/scale`, { replicas });
    return response.data;
  }

  /**
   * Deploy service update
   */
  static async deployService(id: string, version?: string): Promise<ServiceActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/deploy`, { version });
    return response.data;
  }

  /**
   * Rollback service to previous version
   */
  static async rollbackService(id: string, version?: string): Promise<ServiceActionResult> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/rollback`, { version });
    return response.data;
  }

  /**
   * Get service versions history
   */
  static async getServiceVersions(id: string): Promise<Array<{
    version: string;
    deployedAt: string;
    deployedBy: string;
    status: 'active' | 'inactive' | 'rolled_back';
    changelog?: string;
  }>> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/versions`);
    return response.data;
  }

  /**
   * Bulk service operations
   */
  static async bulkServiceOperation(operation: BulkServiceOperation): Promise<{
    success: boolean;
    results: Array<{
      serviceId: string;
      success: boolean;
      error?: string;
    }>;
  }> {
    const response = await apiClient.post(`${this.BASE_PATH}/bulk`, operation);
    return response.data;
  }

  /**
   * Export service metrics
   */
  static async exportMetrics(
    serviceIds: string[],
    timeRange: string,
    format: 'csv' | 'json' = 'csv'
  ): Promise<Blob> {
    const response = await apiClient.post(`${this.BASE_PATH}/export/metrics`, {
      serviceIds,
      timeRange,
      format
    }, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Get system overview (all services summary)
   */
  static async getSystemOverview(): Promise<{
    totalServices: number;
    healthyServices: number;
    unhealthyServices: number;
    criticalAlerts: number;
    averageResponseTime: number;
    systemLoad: number;
    uptime: string;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/overview`);
    return response.data;
  }
}

export default ServiceApiService; 