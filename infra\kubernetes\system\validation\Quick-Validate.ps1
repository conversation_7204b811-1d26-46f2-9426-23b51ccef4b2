# CINA.CLUB Platform - Quick Validation Script
# Copyright (c) 2025 Cina.Club

Write-Host "=== CINA.CLUB Platform Validation ===" -ForegroundColor Green
Write-Host "Validation Time: $(Get-Date)" -ForegroundColor Cyan

$ErrorCount = 0
$SuccessCount = 0

# Define files to check
$FilesToCheck = @(
    "kong\namespace.yaml",
    "kong\control-plane\deployment.yaml", 
    "kong\data-plane\deployment.yaml",
    "kong\data-plane\service.yaml",
    "prometheus.yaml",
    "fluentd-daemonset.yaml",
    "cert-manager.yaml",
    "observability\jaeger.yaml",
    "automation\backup-restore.yaml",
    "secrets\elasticsearch-credentials.yaml",
    "secrets\cert-manager-secure.yaml"
)

Write-Host "`n1. Configuration File Validation" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

foreach ($file in $FilesToCheck) {
    $fullPath = Join-Path ".." $file
    if (Test-Path $fullPath) {
        try {
            $content = Get-Content $fullPath -Raw -ErrorAction Stop
            if ([string]::IsNullOrWhiteSpace($content)) {
                Write-Host "  [ERROR] Empty file: $file" -ForegroundColor Red
                $ErrorCount++
            } else {
                Write-Host "  [OK] $file" -ForegroundColor Green
                $SuccessCount++
            }
        }
        catch {
            Write-Host "  [ERROR] Cannot read: $file" -ForegroundColor Red
            $ErrorCount++
        }
    } else {
        Write-Host "  [MISSING] $file" -ForegroundColor Red
        $ErrorCount++
    }
}

Write-Host "`n2. Directory Structure Check" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

$DirsToCheck = @(
    "base",
    "overlays\production", 
    "overlays\development",
    "kong",
    "observability",
    "automation",
    "secrets"
)

foreach ($dir in $DirsToCheck) {
    $fullPath = Join-Path ".." $dir
    if (Test-Path $fullPath -PathType Container) {
        Write-Host "  [OK] Directory exists: $dir" -ForegroundColor Green
        $SuccessCount++
    } else {
        Write-Host "  [MISSING] Directory: $dir" -ForegroundColor Red
        $ErrorCount++
    }
}

Write-Host "`n3. Kustomize Structure Check" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

$KustomizeDirs = @("base", "overlays\production", "overlays\development")
foreach ($dir in $KustomizeDirs) {
    $kustomizeFile = Join-Path ".." "$dir\kustomization.yaml"
    if (Test-Path $kustomizeFile) {
        Write-Host "  [OK] $dir\kustomization.yaml" -ForegroundColor Green
        $SuccessCount++
    } else {
        Write-Host "  [MISSING] $dir\kustomization.yaml" -ForegroundColor Red
        $ErrorCount++
    }
}

Write-Host "`n4. Security Configuration Check" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

$secretsDir = Join-Path ".." "secrets"
if (Test-Path $secretsDir) {
    $secretFiles = Get-ChildItem $secretsDir -Filter "*.yaml" 
    if ($secretFiles.Count -gt 0) {
        Write-Host "  [OK] Found $($secretFiles.Count) secret files" -ForegroundColor Green
        $SuccessCount++
    } else {
        Write-Host "  [WARNING] No secret files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "  [ERROR] Secrets directory missing" -ForegroundColor Red
    $ErrorCount++
}

# Summary
Write-Host "`n=== VALIDATION SUMMARY ===" -ForegroundColor Green
Write-Host "Success: $SuccessCount" -ForegroundColor Green
Write-Host "Errors: $ErrorCount" -ForegroundColor Red
$Total = $SuccessCount + $ErrorCount
if ($Total -gt 0) {
    $PassRate = [math]::Round(($SuccessCount / $Total) * 100, 1)
    Write-Host "Pass Rate: $PassRate%" -ForegroundColor Cyan
}

if ($ErrorCount -eq 0) {
    Write-Host "`nResult: VALIDATION PASSED" -ForegroundColor Green
    exit 0
} elseif ($ErrorCount -lt 5) {
    Write-Host "`nResult: VALIDATION PASSED WITH WARNINGS" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "`nResult: VALIDATION FAILED" -ForegroundColor Red
    exit 2
} 