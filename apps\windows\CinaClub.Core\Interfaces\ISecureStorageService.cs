 /*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System.Threading.Tasks;

namespace CinaClub.Core.Interfaces;

/// <summary>
/// 安全存储服务接口
/// 提供敏感数据的安全存储和检索功能
/// </summary>
public interface ISecureStorageService
{
    /// <summary>
    /// 安全存储字符串值
    /// </summary>
    /// <param name="key">存储键</param>
    /// <param name="value">存储值</param>
    /// <returns>存储结果</returns>
    Task<bool> SetAsync(string key, string value);

    /// <summary>
    /// 获取安全存储的字符串值
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>存储值，如果不存在则返回null</returns>
    Task<string?> GetAsync(string key);

    /// <summary>
    /// 安全存储字节数组
    /// </summary>
    /// <param name="key">存储键</param>
    /// <param name="value">存储值</param>
    /// <returns>存储结果</returns>
    Task<bool> SetBytesAsync(string key, byte[] value);

    /// <summary>
    /// 获取安全存储的字节数组
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>存储值，如果不存在则返回null</returns>
    Task<byte[]?> GetBytesAsync(string key);

    /// <summary>
    /// 检查指定键是否存在
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>是否存在</returns>
    Task<bool> ContainsKeyAsync(string key);

    /// <summary>
    /// 删除指定键的存储值
    /// </summary>
    /// <param name="key">存储键</param>
    /// <returns>删除结果</returns>
    Task<bool> RemoveAsync(string key);

    /// <summary>
    /// 清空所有存储的值
    /// </summary>
    /// <returns>清空结果</returns>
    Task<bool> ClearAsync();

    /// <summary>
    /// 获取所有存储的键
    /// </summary>
    /// <returns>键的集合</returns>
    Task<IEnumerable<string>> GetAllKeysAsync();
}