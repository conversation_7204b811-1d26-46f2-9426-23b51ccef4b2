# CINA.CLUB Activity Feed Service Configuration

# Basic service configuration
service:
  name: "activity-feed-service"
  version: "1.0.0"
  environment: "development" # development, staging, production
  port: 8080
  debug: true

# Database configuration
database:
  # MongoDB configuration (primary storage)
  mongodb:
    uri: "mongodb://localhost:27017"
    database: "cina_club_activity_feed"
    collections:
      activity_feed_items: "activity_feed_items"
      feed_summaries: "feed_summaries"
    connection_pool:
      min_pool_size: 5
      max_pool_size: 50
      max_idle_time: "30m"
    timeout:
      connect: "10s"
      read: "30s"
      write: "30s"

  # Redis configuration (cache and unread counts)
  redis:
    addresses:
      - "localhost:6379"
    password: ""
    database: 0
    cluster_mode: false
    connection_pool:
      max_active: 100
      max_idle: 10
      idle_timeout: "300s"
    timeout:
      connect: "5s"
      read: "3s"
      write: "3s"

# Message queue configuration
messaging:
  # Kafka configuration
  kafka:
    brokers:
      - "localhost:9092"
    # Consumer configuration
    consumer:
      group_id: "activity-feed-service"
      topics:
        - "platform.events.post.liked"
        - "platform.events.post.commented"
        - "platform.events.user.followed"
        - "platform.events.task.status.changed"
        - "platform.events.user.mentioned"
        - "platform.events.system.announcement"
        - "platform.events.content.published"
      auto_offset_reset: "latest"
      session_timeout: "30s"
      heartbeat_interval: "3s"
    # Producer configuration
    producer:
      topics:
        unread_count_changed: "platform.events.feed.unread.count.changed"
        item_created: "platform.events.feed.item.created"
        item_updated: "platform.events.feed.item.updated"
        items_marked_read: "platform.events.feed.items.marked.read"
      acks: "all"
      retries: 3
      batch_size: 16384
      linger_ms: 10

# Feed service configuration
feed_service:
  # Pagination configuration
  pagination:
    default_page_size: 20
    max_page_size: 100
  
  # Aggregation configuration
  aggregation:
    default_time_window: "5m"
    max_aggregation_size: 50
    rules:
      # Like aggregation rules
      post_like:
        enabled: true
        time_window: "5m"
        max_participants: 20
        title_template: "{actors} and {others_count} others liked your post {target}"
      # Comment aggregation rules
      post_comment:
        enabled: true
        time_window: "10m"
        max_participants: 10
        title_template: "{actors} and {others_count} others commented on your post {target}"
      # Follow aggregation rules
      user_follow:
        enabled: true
        time_window: "1h"
        max_participants: 30
        title_template: "{actors} and {others_count} others followed you"
  
  # Cleanup configuration
  cleanup:
    retention_period: "90d" # Keep for 90 days
    batch_size: 1000
    schedule: "0 2 * * *" # Execute cleanup daily at 2 AM
  
  # Cache configuration
  cache:
    ttl: "5m"
    feed_summary_ttl: "10m"
    aggregation_lock_ttl: "30s"
  
  # Concurrency configuration
  concurrency:
    max_concurrent_processors: 10
    event_processing_timeout: "30s"

# API configuration
api:
  # HTTP configuration
  http:
    host: "0.0.0.0"
    port: 8080
    read_timeout: "15s"
    write_timeout: "15s"
    idle_timeout: "60s"
    max_header_bytes: 1048576 # 1MB
  
  # Authentication configuration
  auth:
    jwt:
      secret: "your-jwt-secret-here"
      issuer: "cina.club"
      audience: "cina.club.users"
      expiration: "24h"
  
  # CORS configuration
  cors:
    enabled: true
    allowed_origins:
      - "https://cina.club"
      - "https://app.cina.club"
      - "https://admin.cina.club"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:
      - "Content-Type"
      - "Authorization"
      - "X-User-ID"
      - "X-Request-ID"
    allow_credentials: true
    max_age: "12h"

# Logging configuration
logging:
  level: "info" # debug, info, warn, error
  format: "json" # json, text
  output: "stdout" # stdout, stderr, file
  file:
    path: "/var/log/activity-feed-service.log"
    max_size: "100MB"
    max_age: "30d"
    max_backups: 10
    compress: true
  fields:
    service: "activity-feed-service"
    version: "1.0.0"

# Monitoring configuration
monitoring:
  # Metrics configuration
  metrics:
    enabled: true
    path: "/metrics"
    namespace: "cina_club"
    subsystem: "activity_feed"
  
  # Health check configuration
  health:
    path: "/health"
    timeout: "10s"
    checks:
      - "database"
      - "redis"
      - "kafka"
  
  # Tracing configuration
  tracing:
    enabled: true
    service_name: "activity-feed-service"
    jaeger:
      endpoint: "http://localhost:14268/api/traces"
      sampler_type: "const"
      sampler_param: 1

# Security configuration
security:
  # Rate limiting configuration
  rate_limiting:
    enabled: true
    requests_per_minute: 1000
    burst_size: 100
  
  # Request validation
  validation:
    max_request_size: "10MB"
    timeout: "30s"

# Feature flags
feature_flags:
  # Aggregation feature
  aggregation_enabled: true
  
  # Real-time push
  real_time_push_enabled: true
  
  # Analytics feature
  analytics_enabled: true
  
  # Cleanup task
  cleanup_task_enabled: true
  
  # Batch processing
  batch_processing_enabled: true

# External services configuration
external_services:
  # User service
  user_service:
    base_url: "http://user-core-service:8080"
    timeout: "5s"
    retry_attempts: 3
  
  # Social service
  social_service:
    base_url: "http://social-service:8080"
    timeout: "5s"
    retry_attempts: 3
  
  # Notification dispatch service
  notification_service:
    base_url: "http://notification-dispatch-service:8080"
    timeout: "10s"
    retry_attempts: 2

# Development environment specific configuration
development:
  # Database
  database:
    mongodb:
      uri: "mongodb://localhost:27017"
    redis:
      addresses: ["localhost:6379"]
  
  # Message queue
  messaging:
    kafka:
      brokers: ["localhost:9092"]
  
  # Logging
  logging:
    level: "debug"
    format: "text"

# Production environment specific configuration
production:
  # Database
  database:
    mongodb:
      uri: "${MONGODB_URI}"
      connection_pool:
        min_pool_size: 10
        max_pool_size: 100
    redis:
      addresses: 
        - "${REDIS_HOST_1}:6379"
        - "${REDIS_HOST_2}:6379"
        - "${REDIS_HOST_3}:6379"
      password: "${REDIS_PASSWORD}"
      cluster_mode: true
  
  # Message queue
  messaging:
    kafka:
      brokers:
        - "${KAFKA_BROKER_1}:9092"
        - "${KAFKA_BROKER_2}:9092"
        - "${KAFKA_BROKER_3}:9092"
  
  # API
  api:
    auth:
      jwt:
        secret: "${JWT_SECRET}"
  
  # Logging
  logging:
    level: "info"
    format: "json"
    output: "file"
  
  # Monitoring
  monitoring:
    tracing:
      jaeger:
        endpoint: "${JAEGER_ENDPOINT}"
  
  # Security
  security:
    rate_limiting:
      requests_per_minute: 5000
      burst_size: 500 