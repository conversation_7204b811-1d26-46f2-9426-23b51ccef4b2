/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

// Time period enumeration
export enum TimePeriod {
  HOUR = 'HOUR',
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  QUARTER = 'QUARTER',
  YEAR = 'YEAR',
}

// Metric types
export enum MetricType {
  COUNT = 'COUNT',
  SUM = 'SUM',
  AVERAGE = 'AVERAGE',
  PERCENTAGE = 'PERCENTAGE',
  RATE = 'RATE',
  DISTRIBUTION = 'DISTRIBUTION',
}

// Chart types
export enum ChartType {
  LINE = 'LINE',
  BAR = 'BAR',
  PIE = 'PIE',
  DONUT = 'DONUT',
  AREA = 'AREA',
  SCATTER = 'SCATTER',
  HEATMAP = 'HEATMAP',
  GAUGE = 'GAUGE',
  NUMBER = 'NUMBER',
}

// KPI (Key Performance Indicator)
export interface KPI {
  id: string
  name: string
  description: string
  value: number
  unit?: string
  change: number // percentage change
  changeType: 'INCREASE' | 'DECREASE' | 'NEUTRAL'
  target?: number
  status: 'GOOD' | 'WARNING' | 'CRITICAL'
  trend: Array<{
    timestamp: string
    value: number
  }>
}

// Dashboard widget
export interface Widget {
  id: string
  title: string
  description?: string
  type: ChartType
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  config: WidgetConfig
  data: WidgetData
  refreshInterval?: number // seconds
  lastUpdated: string
}

// Widget configuration
export interface WidgetConfig {
  dataSource: string
  metrics: string[]
  dimensions: string[]
  filters: Record<string, any>
  timePeriod: TimePeriod
  chartOptions: {
    colors?: string[]
    showLegend?: boolean
    showGrid?: boolean
    animation?: boolean
    [key: string]: any
  }
}

// Widget data
export interface WidgetData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    color?: string
    type?: string
  }>
  total?: number
  metadata?: Record<string, any>
}

// Analytics dashboard
export interface Dashboard {
  id: string
  name: string
  description?: string
  isDefault: boolean
  isPublic: boolean
  ownerId: string
  widgets: Widget[]
  layout: DashboardLayout
  filters: DashboardFilter[]
  createdAt: string
  updatedAt: string
  lastViewedAt: string
  viewCount: number
}

// Dashboard layout
export interface DashboardLayout {
  columns: number
  rowHeight: number
  margin: [number, number]
  compactType: 'vertical' | 'horizontal' | null
}

// Dashboard filter
export interface DashboardFilter {
  id: string
  name: string
  type: 'SELECT' | 'MULTI_SELECT' | 'DATE_RANGE' | 'TEXT' | 'NUMBER_RANGE'
  options?: Array<{
    label: string
    value: string
  }>
  defaultValue?: any
  required: boolean
}

// Analytics query
export interface AnalyticsQuery {
  dataSource: string
  metrics: string[]
  dimensions: string[]
  filters: Array<{
    field: string
    operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'CONTAINS' | 'IN'
    value: any
  }>
  timeRange: {
    start: string
    end: string
  }
  granularity: TimePeriod
  orderBy?: Array<{
    field: string
    direction: 'ASC' | 'DESC'
  }>
  limit?: number
  offset?: number
}

// Analytics result
export interface AnalyticsResult {
  query: AnalyticsQuery
  data: Array<Record<string, any>>
  total: number
  executionTime: number
  cachedAt?: string
  metadata: {
    columns: Array<{
      name: string
      type: string
      description?: string
    }>
    totals?: Record<string, number>
    aggregations?: Record<string, any>
  }
}

// User analytics
export interface UserAnalytics {
  totalUsers: number
  activeUsers: {
    daily: number
    weekly: number
    monthly: number
  }
  newUsers: {
    today: number
    thisWeek: number
    thisMonth: number
  }
  userRetention: Array<{
    cohort: string
    day0: number
    day1: number
    day7: number
    day30: number
  }>
  userEngagement: {
    averageSessionDuration: number
    averagePageViews: number
    bounceRate: number
  }
  topCountries: Array<{
    country: string
    users: number
    percentage: number
  }>
  topDevices: Array<{
    device: string
    users: number
    percentage: number
  }>
}

// Content analytics
export interface ContentAnalytics {
  totalContent: number
  publishedContent: number
  contentViews: number
  contentEngagement: {
    likes: number
    comments: number
    shares: number
  }
  topContent: Array<{
    id: string
    title: string
    views: number
    engagement: number
  }>
  contentByCategory: Array<{
    category: string
    count: number
    views: number
  }>
  viralContent: Array<{
    id: string
    title: string
    viralityScore: number
    growthRate: number
  }>
}

// Revenue analytics
export interface RevenueAnalytics {
  totalRevenue: number
  recurringRevenue: number
  oneTimeRevenue: number
  averageRevenuePerUser: number
  monthlyRecurringRevenue: number
  churnRate: number
  customerLifetimeValue: number
  revenueByPlan: Array<{
    plan: string
    revenue: number
    users: number
  }>
  revenueGrowth: Array<{
    period: string
    revenue: number
    growth: number
  }>
}

// Performance analytics
export interface PerformanceAnalytics {
  averageResponseTime: number
  errorRate: number
  uptime: number
  throughput: number
  slowestEndpoints: Array<{
    endpoint: string
    averageResponseTime: number
    requestCount: number
  }>
  errorsByType: Array<{
    type: string
    count: number
    percentage: number
  }>
  performanceTrends: Array<{
    timestamp: string
    responseTime: number
    throughput: number
    errorRate: number
  }>
}

// Custom report
export interface CustomReport {
  id: string
  name: string
  description?: string
  query: AnalyticsQuery
  schedule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    time: string
    timezone: string
    recipients: string[]
  }
  format: 'PDF' | 'EXCEL' | 'CSV' | 'JSON'
  createdBy: string
  createdAt: string
  lastGenerated?: string
  isActive: boolean
}

// Report export request
export interface ReportExportRequest {
  reportId?: string
  query?: AnalyticsQuery
  format: 'PDF' | 'EXCEL' | 'CSV' | 'JSON'
  name: string
  includeCharts?: boolean
  template?: string
}

// Analytics configuration
export interface AnalyticsConfig {
  dataSources: Array<{
    id: string
    name: string
    description: string
    type: 'DATABASE' | 'API' | 'FILE' | 'STREAM'
    connection: Record<string, any>
    refreshInterval: number
    isActive: boolean
  }>
  metrics: Array<{
    id: string
    name: string
    description: string
    type: MetricType
    dataSource: string
    calculation: string
    unit?: string
  }>
  dimensions: Array<{
    id: string
    name: string
    description: string
    dataSource: string
    type: 'STRING' | 'NUMBER' | 'DATE' | 'BOOLEAN'
  }>
} 