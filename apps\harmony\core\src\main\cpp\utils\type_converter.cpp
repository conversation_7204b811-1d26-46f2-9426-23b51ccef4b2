/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

#include "type_converter.h"
#include <hilog/log.h>
#include <cstring>
#include <vector>

constexpr unsigned int LOG_DOMAIN = 0x3900;
constexpr char LOG_TAG[] = "TypeConverter";

// 线程局部存储的临时字符串缓冲区
thread_local std::string TypeConverter::tempStringBuffer;

bool TypeConverter::ArrayBufferToGoSlice(napi_env env, napi_value napiValue, GoSlice* goSlice) {
    if (!CheckType(env, napiValue, napi_object)) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Expected ArrayBuffer, got different type");
        return false;
    }

    bool isArrayBuffer;
    napi_status status = napi_is_arraybuffer(env, napiValue, &isArrayBuffer);
    if (status != napi_ok || !isArrayBuffer) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Value is not an ArrayBuffer");
        return false;
    }

    void* data;
    size_t byteLength;
    status = napi_get_arraybuffer_info(env, napiValue, &data, &byteLength);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to get ArrayBuffer info");
        return false;
    }

    // 为Go Slice分配内存并复制数据
    void* goData = malloc(byteLength);
    if (!goData) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to allocate memory for Go slice");
        return false;
    }
    
    memcpy(goData, data, byteLength);
    
    goSlice->data = goData;
    goSlice->len = static_cast<int64_t>(byteLength);
    goSlice->cap = static_cast<int64_t>(byteLength);

    return true;
}

bool TypeConverter::GoSliceToArrayBuffer(napi_env env, const GoSlice& goSlice, napi_value* napiValue) {
    if (goSlice.len <= 0 || !goSlice.data) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Invalid Go slice");
        return false;
    }

    void* data;
    napi_status status = napi_create_arraybuffer(env, goSlice.len, &data, napiValue);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to create ArrayBuffer");
        return false;
    }

    // 复制数据
    memcpy(data, goSlice.data, goSlice.len);
    return true;
}

bool TypeConverter::StringToGoString(napi_env env, napi_value napiValue, GoString* goString) {
    if (!CheckType(env, napiValue, napi_string)) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Expected string, got different type");
        return false;
    }

    size_t length;
    napi_status status = napi_get_value_string_utf8(env, napiValue, nullptr, 0, &length);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to get string length");
        return false;
    }

    // 使用线程局部缓冲区存储字符串
    tempStringBuffer.resize(length + 1);
    status = napi_get_value_string_utf8(env, napiValue, &tempStringBuffer[0], length + 1, &length);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to get string content");
        return false;
    }

    goString->p = tempStringBuffer.c_str();
    goString->n = static_cast<int64_t>(length);
    return true;
}

bool TypeConverter::GoStringToString(napi_env env, const GoString& goString, napi_value* napiValue) {
    if (!goString.p || goString.n <= 0) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Invalid Go string");
        return false;
    }

    napi_status status = napi_create_string_utf8(env, goString.p, goString.n, napiValue);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to create NAPI string");
        return false;
    }

    return true;
}

bool TypeConverter::NumberToInt(napi_env env, napi_value napiValue, int* result) {
    if (!CheckType(env, napiValue, napi_number)) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Expected number, got different type");
        return false;
    }

    int32_t value;
    napi_status status = napi_get_value_int32(env, napiValue, &value);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to get int32 value");
        return false;
    }

    *result = value;
    return true;
}

bool TypeConverter::IntToNumber(napi_env env, int value, napi_value* napiValue) {
    napi_status status = napi_create_int32(env, value, napiValue);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to create int32 value");
        return false;
    }

    return true;
}

bool TypeConverter::CheckType(napi_env env, napi_value napiValue, napi_valuetype expectedType) {
    napi_valuetype valueType;
    napi_status status = napi_typeof(env, napiValue, &valueType);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to get value type");
        return false;
    }

    return valueType == expectedType;
}

bool TypeConverter::CreateError(napi_env env, const std::string& message, napi_value* error) {
    napi_value messageValue;
    napi_status status = napi_create_string_utf8(env, message.c_str(), NAPI_AUTO_LENGTH, &messageValue);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to create error message");
        return false;
    }

    status = napi_create_error(env, nullptr, messageValue, error);
    if (status != napi_ok) {
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Failed to create error object");
        return false;
    }

    return true;
} 