好的，遵照您的指示。我将为您生成一份专门针对 **`admin-bff-service`** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将基于我们刚讨论过的平台架构，详细定义这个新增的、至关重要的BFF服务的功能、接口、安全和非功能性需求，使其可以直接作为开发、测试和运维的依据。

---
### CINA.CLUB - `admin-bff-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [平台工程/后台产品负责人]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了对CINA.CLUB平台进行高效、安全的运营和管理，需要一个统一的后台管理界面。`admin-bff-service` (Admin Backend for Frontend) 的目的在于作为这个**后台管理前端 (`admin-frontend`) 的专用后端**。它通过提供一个**安全的、面向UI的、聚合的API层**，将后台前端与复杂的后端微服务集群解耦，旨在：
*   **简化前端开发**: 为前端提供聚合好的、符合页面展示需求的数据，避免前端进行多次API调用。
*   **建立安全屏障**: 作为内部员工访问核心业务服务的唯一网关，集中实施认证、授权和审计。
*   **协议转换**: 将内部的gRPC协议转换为前端友好的RESTful/JSON协议。
*   **提升性能**: 通过后端聚合和缓存，减少网络往返，提升后台页面的加载速度。

#### 1.2. 服务范围
本服务 **负责**:
*   **内部员工认证**: 与公司SSO系统（如Okta, LDAP）或`user-core-service`的特殊角色集成，管理后台用户的登录和会话。
*   **API聚合与裁剪**: 提供面向后台UI的RESTful API，内部并行调用多个下游微服务的gRPC接口，并对返回结果进行组装和裁剪。
*   **权限透传与校验**: 在调用下游服务时，安全地传递操作员工的身份信息，并对自身API进行粗粒度的权限校验。
*   **操作审计**: **作为中央审计点**，记录所有通过后台执行的、改变系统状态的操作。
*   **配置与字典服务**: 为前端提供动态的枚举值、配置项等字典数据。

本服务 **不负责**:
*   **任何核心业务逻辑的实现**: 所有业务逻辑都在下游的微服务中。本服务是**纯粹的编排和代理层**。
*   **面向最终用户的API** (由`api-gateway`负责)。
*   **提供后台UI** (由`admin-frontend`负责)。

#### 1.3. 目标用户/调用方
*   **`admin-frontend` (唯一)**: 后台管理前端是本服务的唯一直接调用方。
*   **CINA.CLUB内部微服务**: (被本服务调用) 是本服务的数据来源。
*   **公司SSO/身份提供商**: (与本服务集成) 用于员工认证。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`admin-bff-service` 是**平台管理域的网关和协调者**。它位于内部员工使用的前端和庞大的后端微服务集群之间，扮演着“**安全管家**”、“**数据管家**”和“**翻译官**”的多重角色。其设计的好坏直接决定了后台管理系统的性能、安全性和可扩展性。

#### 2.2. 主要功能概述
*   安全的、基于SSO的内部员工认证。
*   面向UI的、聚合的RESTful API。
*   基于角色的API访问控制。
*   集中的、不可篡改的操作审计日志。
*   与下游微服务的高性能gRPC交互。

---

### 3. 核心流程图

#### 3.1. 管理员查询用户完整信息流程

```mermaid
sequenceDiagram
    participant AdminUser as "管理员"
    participant AdminFrontend
    participant AdminBFF as "admin-bff-service"
    participant UserCore as "user-core-service"
    participant Billing as "billing-service"
    participant Social as "social-service"

    AdminUser->>AdminFrontend: 1. 打开用户详情页 (for user_id: 'xyz')
    AdminFrontend->>AdminBFF: 2. GET /api/v1/admin/users/xyz/full-profile
    
    AdminBFF->>AdminBFF: 3. **权限校验**: 检查操作员角色是否有权查看用户详情
    
    par [并行gRPC调用]
        AdminBFF->>UserCore: 4a. GetUserForAdmin(id: 'xyz')
        UserCore-->>AdminBFF: (用户核心信息, 状态, 等级, ...)
    and
        AdminBFF->>Billing: 4b. GetUserSubscriptions(id: 'xyz')
        Billing-->>AdminBFF: (用户订阅列表)
    and
        AdminBFF->>Social: 4c. GetUserSocialStats(id: 'xyz')
        Social-->>AdminBFF: (粉丝数, 关注数)
    end
    
    AdminBFF->>AdminBFF: 5. **数据聚合与裁剪**: 将3个响应组装成一个前端需要的JSON对象
    
    AdminBFF-->>AdminFrontend: 6. 200 OK (返回聚合后的JSON数据)
    AdminFrontend->>AdminUser: 7. 渲染完整的用户详情页
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 认证与会话管理
*   **FR4.1.1 (SSO集成)**: **必须**支持通过SAML或OIDC协议与公司的身份提供商(IdP)进行单点登录集成。
*   **FR4.1.2 (会话管理)**: 员工通过SSO登录成功后，本服务为其创建一个内部会话（如基于Redis的Session），并向前端返回一个HttpOnly的Session Cookie。
*   **FR4.1.3 (角色同步)**: 登录时，本服务需要从IdP返回的断言(assertion)中获取员工的角色组信息，或调用`user-core-service`查询该员工账号被授予的内部角色。

#### 4.2. API聚合层
*   **FR4.2.1 (面向UI设计API)**: API的设计**不应**是下游gRPC接口的简单1:1代理。而应根据前端页面的数据需求来设计，一个API对应一个或一组UI组件所需的数据。
*   **FR4.2.2 (并行调用)**: 对于需要从多个服务获取数据的聚合API，**必须**使用Go的并发原语（如`errgroup`）来并行地发起gRPC调用，以最小化总响应时间。
*   **FR4.2.3 (数据裁剪与转换)**: 返回给前端的JSON数据**必须**是经过裁剪的，只包含UI需要展示的字段。同时，需要将内部的枚举值等转换为前端易于展示的字符串。

#### 4.3. 授权与权限透传
*   **FR4.3.1 (BFF层授权)**: 本服务需要实现一个API中间件，根据员工会话中的角色，检查其是否有权访问请求的RESTful API端点。这是一种**粗粒度**的权限检查。
*   **FR4.3.2 (权限透传)**: 在调用下游微服务的**Admin gRPC API**时，**必须**在出站gRPC请求的元数据中，附加以下信息：
    *   `x-actor-user-id`: 操作员工的用户ID。
    *   `x-actor-roles`: 操作员工的角色列表（逗号分隔）。
*   **FR4.3.3 (下游授权)**: 下游微服务的Admin API会利用这些透传的信息，进行**细粒度**的权限检查（由`pkg/auth`的RBAC拦截器实现）。

#### 4.4. 操作审计
*   **FR4.4.1 (审计中间件)**: 必须实现一个HTTP中间件，拦截所有**非GET**的API请求（即`POST`, `PUT`, `PATCH`, `DELETE`）。
*   **FR4.4.2 (日志内容)**: 对于每个被拦截的请求，**必须**记录一条结构化的审计日志。日志内容包括：
    *   `timestamp`
    *   `actor_id` (操作员工ID)
    *   `actor_ip`
    *   `target_api_method` (e.g., `POST`)
    *   `target_api_path` (e.g., `/api/v1/admin/users/xyz/suspend`)
    *   `request_body` (敏感信息如密码必须脱敏)
    *   `response_status_code`
    *   `trace_id`
*   **FR4.4.3 (日志存储)**: 审计日志**必须**被发送到一个独立的、安全的、防篡改的存储系统中（如专门的Kafka Topic -> Elasticsearch/Splunk）。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 对后台前端的RESTful API接口
*   **版本**: `/api/v1/admin`
*   **认证**: Session Cookie。
*   **API设计风格**: 面向资源的、聚合的API。
*   **示例端点**:
    *   `POST /auth/sso/login`: 发起SSO登录。
    *   `GET /auth/me`: 获取当前登录员工的信息和权限。
    *   `GET /dashboard/summary`: 获取后台首页所需的统计数据摘要。
    *   `GET /users`: 查询用户列表（支持分页、筛选、排序）。
    *   `GET /users/{id}/full-profile`: 获取单个用户的完整聚合信息。
    *   `POST /users/{id}/suspend`: 挂起一个用户账户。
    *   `GET /content/moderation-queue`: 获取内容审核队列。
    *   `POST /content/tasks/{id}/approve`: 批准一条内容。
    *   `GET /billing/invoices`: 查询发票列表。

### 6. 数据需求 (Data Requirements)

*   **无核心持久化数据库**: 本服务是**无状态**的。
*   **会话存储 (Redis)**: 使用Redis来存储内部员工的会话信息。Key: `admin_session:{session_id}`, Value: 包含员工ID、角色、过期时间等信息的JSON。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   **API延迟**: 聚合查询API的P95延迟应 `< 800ms`。简单代理API P95 < 200ms。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.9%。本服务的故障将导致整个后台管理系统不可用。
    *   **容错**: 对所有下游微服务的gRPC调用**必须**有合理的超时、重试和熔断机制。如果某个非核心服务调用失败，API应能返回部分数据，而不是整个请求失败。
*   **NFR7.3 (可扩展性)**: 服务应为无状态，易于水平扩展。
*   **NFR7.4 (安全性 - 最高优先级)**:
    *   严格执行SSO和会话管理。
    *   严格的输入验证，防止注入等攻击。
    *   严格的权限校验和透传。
    *   完善的、不可否认的审计日志。

---

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **HTTP框架**: **`chi`** 或 **`gin`**。因其轻量、高性能，且拥有丰富的中间件生态。
*   **gRPC客户端**: 使用由`/core/api`自动生成的客户端代码。
*   **前端集成**: 提供**OpenAPI v3**规范文档，供`admin-frontend`自动生成类型安全的API客户端。

---
这份SRS为`admin-bff-service`的设计和实现提供了坚实、全面的指导。通过构建这样一个强大的、安全的、面向UI的BFF层，CINA.CLUB平台可以确保其后台管理系统在功能、性能和安全上都达到企业级标准，为平台的精细化运营和管理提供有力支撑。