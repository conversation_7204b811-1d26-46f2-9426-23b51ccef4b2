/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package toolkit

import (
	"context"

	"cina.club/services/ai-assistant-service/internal/application/port"
)

// QueryKnowledgeTool represents a tool for querying knowledge base
type QueryKnowledgeTool struct {
	name        string
	description string
}

// NewQueryKnowledgeTool creates a new knowledge base query tool
func NewQueryKnowledgeTool() port.Tool {
	return &QueryKnowledgeTool{
		name:        "query_knowledge",
		description: "Query information from knowledge base, including FAQ, product information, user guides, etc.",
	}
}

// Name returns the tool name
func (t *QueryKnowledgeTool) Name() string {
	return t.name
}

// Description returns the tool description
func (t *QueryKnowledgeTool) Description() string {
	return t.description
}

// Category returns the tool category
func (t *QueryKnowledgeTool) Category() port.ToolCategory {
	return port.ToolCategoryKnowledge
}

// RequiresAuth returns whether authentication is required
func (t *QueryKnowledgeTool) RequiresAuth() bool {
	return false // Knowledge base queries don't require authentication
}

// IsAsync returns whether this is an async tool
func (t *QueryKnowledgeTool) IsAsync() bool {
	return false
}

// InputSchema returns the input parameter schema
func (t *QueryKnowledgeTool) InputSchema() *port.JSONSchema {
	schema := port.NewObjectSchema(
		"Query knowledge base parameters",
		map[string]*port.JSONSchema{},
		[]string{"query"},
	)

	schema.AddProperty("query", port.NewStringSchema("Query keywords", true))
	schema.AddProperty("category", port.NewStringSchema("Knowledge category", false))

	limitMin := float64(1)
	limitMax := float64(20)
	schema.AddProperty("limit", port.NewIntegerSchema("Limit number of results returned", &limitMin, &limitMax))

	return schema
}

// OutputSchema returns the output result schema
func (t *QueryKnowledgeTool) OutputSchema() *port.JSONSchema {
	knowledgeSchema := port.NewObjectSchema(
		"Knowledge item",
		map[string]*port.JSONSchema{},
		[]string{"id", "title", "content"},
	)

	knowledgeSchema.AddProperty("id", port.NewStringSchema("Knowledge ID", true))
	knowledgeSchema.AddProperty("title", port.NewStringSchema("Title", true))
	knowledgeSchema.AddProperty("content", port.NewStringSchema("Content", true))
	knowledgeSchema.AddProperty("category", port.NewStringSchema("Category", false))
	knowledgeSchema.AddProperty("tags", port.NewArraySchema("Tags", port.NewStringSchema("Tag", false)))
	knowledgeSchema.AddProperty("relevance_score", port.NewIntegerSchema("Relevance score", nil, nil))
	knowledgeSchema.AddProperty("updated_at", port.NewStringSchema("Update time", false))

	return port.NewObjectSchema(
		"Query knowledge base result",
		map[string]*port.JSONSchema{
			"knowledge_items": port.NewArraySchema("Knowledge list", knowledgeSchema),
			"total_count":     port.NewIntegerSchema("Total count", nil, nil),
		},
		[]string{"knowledge_items", "total_count"},
	)
}

// Execute executes the tool logic
func (t *QueryKnowledgeTool) Execute(ctx context.Context, inputs map[string]interface{}) (*port.ToolResult, error) {
	// Extract parameters
	query, ok := inputs["query"].(string)
	if !ok || query == "" {
		return port.NewToolError("query is required and must be a string"), nil
	}

	category := ""
	if c, ok := inputs["category"].(string); ok {
		category = c
	}

	limit := 10
	if l, ok := inputs["limit"].(float64); ok {
		limit = int(l)
	}

	// TODO: Actual knowledge base query logic
	// This should call the knowledge-service gRPC interface
	result := t.queryKnowledge(ctx, query, category, limit)

	return port.NewToolResult(map[string]interface{}{
		"knowledge_items": result.KnowledgeItems,
		"total_count":     result.TotalCount,
	}), nil
}

// KnowledgeResult represents the result of a knowledge base query
type KnowledgeResult struct {
	KnowledgeItems []KnowledgeItem `json:"knowledge_items"`
	TotalCount     int             `json:"total_count"`
}

// KnowledgeItem represents a knowledge item
type KnowledgeItem struct {
	ID             string   `json:"id"`
	Title          string   `json:"title"`
	Content        string   `json:"content"`
	Category       string   `json:"category"`
	Tags           []string `json:"tags"`
	RelevanceScore int      `json:"relevance_score"`
	UpdatedAt      string   `json:"updated_at"`
}

// queryKnowledge queries the knowledge base (simplified implementation)
func (t *QueryKnowledgeTool) queryKnowledge(_ context.Context, _ string, category string, limit int) *KnowledgeResult {
	// This is a simplified implementation, should call knowledge-service gRPC interface in practice

	knowledgeItems := []KnowledgeItem{
		{
			ID:             "kb-001",
			Title:          "How to choose the right service provider",
			Content:        "When choosing a service provider, consider the following factors: 1. Service quality and reputation 2. Reasonable pricing 3. Service scope 4. Response speed...",
			Category:       "User Guide",
			Tags:           []string{"service selection", "guide", "recommendation"},
			RelevanceScore: 95,
			UpdatedAt:      "2024-01-20T10:00:00Z",
		},
		{
			ID:             "kb-002",
			Title:          "Platform service categories description",
			Content:        "The platform provides multiple service categories: 1. Life services (housekeeping, maintenance, etc.) 2. Business services (consulting, design, etc.) 3. Education and training 4. Health and medical...",
			Category:       "Product Information",
			Tags:           []string{"categories", "services", "description"},
			RelevanceScore: 88,
			UpdatedAt:      "2024-01-18T15:30:00Z",
		},
		{
			ID:             "kb-003",
			Title:          "Frequently Asked Questions",
			Content:        "Q: How to cancel a booking? A: You can click the cancel button on the order details page... Q: How are service fees calculated? A: Fees are calculated based on service type, duration, and other factors...",
			Category:       "FAQ",
			Tags:           []string{"FAQ", "Q&A", "help"},
			RelevanceScore: 82,
			UpdatedAt:      "2024-01-22T12:00:00Z",
		},
	}

	// Simple filtering logic
	filteredItems := []KnowledgeItem{}
	for _, item := range knowledgeItems {
		if category != "" && item.Category != category {
			continue
		}
		filteredItems = append(filteredItems, item)
	}

	// Apply limit
	if len(filteredItems) > limit {
		filteredItems = filteredItems[:limit]
	}

	return &KnowledgeResult{
		KnowledgeItems: filteredItems,
		TotalCount:     len(filteredItems),
	}
}
