//go:build mobile

package crypto

import (
	"encoding/json"
	"fmt"
)

// MobileE2EEEngine 移动端E2EE引擎包装器
type MobileE2EEEngine struct {
	engine *E2EEEngine
}

// NewMobileE2EEEngine 创建移动端E2EE引擎
func NewMobileE2EEEngine() *MobileE2EEEngine {
	return &MobileE2EEEngine{
		engine: NewE2EEEngine(),
	}
}

// EncryptWithPasswordJSON 使用密码加密数据，返回JSON字符串
func (m *MobileE2EEEngine) EncryptWithPasswordJSON(data []byte, password string) (string, error) {
	encData, err := m.engine.EncryptWithPassword(data, password)
	if err != nil {
		return "", err
	}

	jsonData, err := json.Marshal(encData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal encrypted data: %w", err)
	}

	return string(jsonData), nil
}

// DecryptWithPasswordJSON 使用密码解密数据，从JSON字符串解析
func (m *MobileE2EEEngine) DecryptWithPasswordJSON(encDataJSON string, password string) ([]byte, error) {
	var encData EncryptedData
	if err := json.Unmarshal([]byte(encDataJSON), &encData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal encrypted data: %w", err)
	}

	return m.engine.DecryptWithPassword(&encData, password)
}

// EncryptWithKeyJSON 使用密钥加密数据，返回JSON字符串
func (m *MobileE2EEEngine) EncryptWithKeyJSON(data []byte, key []byte) (string, error) {
	encData, err := m.engine.EncryptWithKey(data, key)
	if err != nil {
		return "", err
	}

	jsonData, err := json.Marshal(encData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal encrypted data: %w", err)
	}

	return string(jsonData), nil
}

// DecryptWithKeyJSON 使用密钥解密数据，从JSON字符串解析
func (m *MobileE2EEEngine) DecryptWithKeyJSON(encDataJSON string, key []byte) ([]byte, error) {
	var encData EncryptedData
	if err := json.Unmarshal([]byte(encDataJSON), &encData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal encrypted data: %w", err)
	}

	return m.engine.DecryptWithKey(&encData, key)
}

// GenerateKey 生成随机密钥
func (m *MobileE2EEEngine) GenerateKey() ([]byte, error) {
	return m.engine.GenerateKey()
}
