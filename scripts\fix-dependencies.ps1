# CINA.CLUB 依赖修复脚本
# 为所有服务添加必要的cina.club/pkg和cina.club/core依赖

Write-Host "Starting dependency fixes..." -ForegroundColor Green

$fixedServices = 0
$services = Get-ChildItem -Path "services" -Directory

foreach ($service in $services) {
    $serviceName = $service.Name
    $servicePath = $service.FullName
    $goModPath = Join-Path $servicePath "go.mod"
    
    Write-Host "Processing: $serviceName" -ForegroundColor Yellow
    
    if (Test-Path $goModPath) {
        try {
            $content = Get-Content $goModPath -Raw
            $modified = $false
            
            # Check if we need to add require section for cina.club packages
            if ($content -match "cina\.club/pkg" -and $content -notmatch "require \(" -and $content -notmatch "cina\.club/pkg.*v") {
                Write-Host "  Adding cina.club/pkg dependency..." -ForegroundColor Cyan
                
                # Add require section if not exists
                if ($content -notmatch "require \(") {
                    $content += "`n`nrequire (`n    cina.club/pkg v0.0.0`n    cina.club/core v0.0.0`n)`n"
                    $modified = $true
                } else {
                    # Add to existing require section
                    $content = $content -replace "(require \([^)]*)", "`$1`n    cina.club/pkg v0.0.0`n    cina.club/core v0.0.0"
                    $modified = $true
                }
            }
            
            # Update replace section to include proper versions
            if ($content -match "replace \(" -and $content -notmatch "cina\.club/pkg.*=> \./\.\./\.\./pkg") {
                Write-Host "  Updating replace section..." -ForegroundColor Cyan
                
                # Update replace section
                $replaceSection = @"
replace (
    cina.club/core => ../../core
    cina.club/pkg => ../../pkg
    cina.club/services/$serviceName => ./
)
"@
                
                $content = $content -replace "replace \([^)]*\)", $replaceSection
                $modified = $true
            }
            
            if ($modified) {
                # Write without BOM
                $utf8NoBom = New-Object System.Text.UTF8Encoding $false
                [System.IO.File]::WriteAllText($goModPath, $content, $utf8NoBom)
                
                Write-Host "  FIXED: Updated go.mod" -ForegroundColor Green
                $fixedServices++
                
                # Run go mod tidy
                $currentLocation = Get-Location
                Set-Location $servicePath
                
                Write-Host "  Running go mod tidy..." -ForegroundColor Gray
                go mod tidy 2>$null
                
                Set-Location $currentLocation
            } else {
                Write-Host "  OK: No changes needed" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ERROR: go.mod not found" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== DEPENDENCY FIX SUMMARY ===" -ForegroundColor Blue
Write-Host "Services processed: $($services.Count)" -ForegroundColor Cyan
Write-Host "Services fixed: $fixedServices" -ForegroundColor Green

Write-Host ""
Write-Host "Next step: Run batch compilation test" -ForegroundColor Yellow
Write-Host "Command: .\scripts\simple-batch-test.ps1" -ForegroundColor Cyan 