# PowerShell脚本：修正CINA.CLUB项目中的import路径
# 将所有错误的import路径修正为标准的cina.club/*格式

Write-Host "🔧 开始修正import路径..." -ForegroundColor Green

$successCount = 0
$failureCount = 0

# 定义需要修正的路径映射
$pathMappings = @{
    # 主要的monorepo路径修正
    "github.com/cina-club/monorepo/core" = "cina.club/core"
    "github.com/cina-club/monorepo/pkg" = "cina.club/pkg"
    "github.com/cina-club/monorepo/services/(.+)" = "cina.club/services/`$1"
    
    # cina.club错误路径修正
    "github.com/cina.club/(.+)" = "cina.club/services/`$1"
    
    # 特定服务错误路径修正
    "github.com/cina-club/routines-service" = "cina.club/services/routines-service"
    "github.com/cina-club/(.+)-service" = "cina.club/services/`$1-service"
    
    # 其他变体
    "github.com/CINA-CLUB/(.+)" = "cina.club/`$1"
}

# 修正单个文件中的import路径
function Fix-ImportPaths {
    param(
        [string]$FilePath
    )
    
    if (-not (Test-Path $FilePath)) {
        return $false
    }
    
    $content = Get-Content $FilePath -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    foreach ($oldPattern in $pathMappings.Keys) {
        $newPattern = $pathMappings[$oldPattern]
        
        # 使用正则表达式替换
        $regex = [regex]$oldPattern
        if ($regex.IsMatch($content)) {
            $content = $regex.Replace($content, $newPattern)
            $modified = $true
            Write-Host "🔄 修正路径: $oldPattern -> $newPattern in $FilePath" -ForegroundColor Cyan
        }
    }
    
    if ($modified) {
        try {
            $content | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline
            Write-Host "✅ 已更新: $FilePath" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "❌ 更新失败: $FilePath - $_" -ForegroundColor Red
            return $false
        }
    }
    
    return $false
}

# 获取所有Go文件
Write-Host "📂 扫描Go源文件..." -ForegroundColor Blue
$goFiles = Get-ChildItem -Path "." -Filter "*.go" -Recurse | Where-Object { 
    $_.FullName -notmatch "vendor|\.git|node_modules" 
}

Write-Host "🔍 找到 $($goFiles.Count) 个Go文件" -ForegroundColor Yellow

# 处理每个Go文件
foreach ($file in $goFiles) {
    if (Fix-ImportPaths $file.FullName) {
        $successCount++
    }
}

# 输出结果统计
Write-Host ""
Write-Host "📊 修正结果:" -ForegroundColor Green
Write-Host "✅ 成功修正的文件: $successCount" -ForegroundColor Green
Write-Host "📁 总扫描文件数: $($goFiles.Count)" -ForegroundColor Blue

if ($successCount -gt 0) {
    Write-Host "🎉 import路径修正完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 建议下一步操作:" -ForegroundColor Cyan
    Write-Host "1. 运行: go mod tidy (在各个模块目录)" -ForegroundColor White
    Write-Host "2. 测试编译: go build ./..." -ForegroundColor White
    Write-Host "3. 验证import路径: grep -r 'github.com/cina' . --include='*.go'" -ForegroundColor White
} else {
    Write-Host "ℹ️ 没有发现需要修正的import路径" -ForegroundColor Yellow
} 