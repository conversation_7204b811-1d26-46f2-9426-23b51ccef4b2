/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { AppConfigManager } from './AppConfigManager';
import { common } from '@kit.AbilityKit';

/**
 * 配置管理器
 * 
 * AppConfigManager的简化代理，用于依赖注入系统
 */
export class ConfigManager {
  private appConfigManager: AppConfigManager;

  constructor(context: common.UIAbilityContext) {
    this.appConfigManager = AppConfigManager.getInstance();
    this.appConfigManager.initialize(context);
  }

  /**
   * 获取API基础URL
   */
  getApiBaseUrl(): string {
    return this.appConfigManager.getApiBaseUrl();
  }

  /**
   * 获取WebSocket基础URL
   */
  getWsBaseUrl(): string {
    return this.appConfigManager.getWsBaseUrl();
  }

  /**
   * 获取网络超时时间
   */
  getNetworkTimeout(): number {
    return this.appConfigManager.getNetworkTimeout();
  }

  /**
   * 检查是否启用E2EE
   */
  isE2EEEnabled(): boolean {
    return this.appConfigManager.isE2EEEnabled();
  }

  /**
   * 检查是否启用本地AI
   */
  isLocalAIEnabled(): boolean {
    return this.appConfigManager.isLocalAIEnabled();
  }

  /**
   * 检查是否启用调试模式
   */
  isDebugModeEnabled(): boolean {
    return this.appConfigManager.isDebugModeEnabled();
  }
} 