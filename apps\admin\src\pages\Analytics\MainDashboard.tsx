/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React, { useState, useEffect } from 'react'
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Button, 
  DatePicker, 
  Select, 
  Space, 
  Typography,
  Progress,
  Tag,
  Avatar,
  List
} from 'antd'
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined, 
  UserOutlined,
  EyeOutlined,
  LikeOutlined,
  CommentOutlined,
  ShareAltOutlined,
  DollarOutlined
} from '@ant-design/icons'
import { Line, Column, Pie, Area } from '@ant-design/charts'
import dayjs from 'dayjs'

import { KPI, TimePeriod, UserAnalytics, ContentAnalytics, RevenueAnalytics } from '@/types/analytics'
import { usePermission } from '@/store/auth'
import { Permission } from '@/types/user'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

// Mock KPI data
const mockKPIs: KPI[] = [
  {
    id: '1',
    name: '总用户数',
    description: '平台注册用户总数',
    value: 15420,
    unit: '人',
    change: 12.5,
    changeType: 'INCREASE',
    target: 20000,
    status: 'GOOD',
    trend: [
      { timestamp: '2025-01-20', value: 14800 },
      { timestamp: '2025-01-21', value: 15100 },
      { timestamp: '2025-01-22', value: 15280 },
      { timestamp: '2025-01-23', value: 15420 },
    ],
  },
  {
    id: '2',
    name: '日活跃用户',
    description: '今日活跃用户数',
    value: 3245,
    unit: '人',
    change: -5.2,
    changeType: 'DECREASE',
    target: 3500,
    status: 'WARNING',
    trend: [
      { timestamp: '2025-01-20', value: 3420 },
      { timestamp: '2025-01-21', value: 3380 },
      { timestamp: '2025-01-22', value: 3300 },
      { timestamp: '2025-01-23', value: 3245 },
    ],
  },
  {
    id: '3',
    name: '内容发布量',
    description: '今日新发布内容数',
    value: 892,
    unit: '条',
    change: 18.7,
    changeType: 'INCREASE',
    status: 'GOOD',
    trend: [
      { timestamp: '2025-01-20', value: 750 },
      { timestamp: '2025-01-21', value: 820 },
      { timestamp: '2025-01-22', value: 856 },
      { timestamp: '2025-01-23', value: 892 },
    ],
  },
  {
    id: '4',
    name: '今日收入',
    description: '今日平台收入',
    value: 28450,
    unit: '元',
    change: 8.3,
    changeType: 'INCREASE',
    status: 'GOOD',
    trend: [
      { timestamp: '2025-01-20', value: 26200 },
      { timestamp: '2025-01-21', value: 27100 },
      { timestamp: '2025-01-22', value: 26800 },
      { timestamp: '2025-01-23', value: 28450 },
    ],
  },
]

// Mock chart data
const userGrowthData = [
  { date: '2025-01-01', users: 12000, newUsers: 120 },
  { date: '2025-01-08', users: 13200, newUsers: 150 },
  { date: '2025-01-15', users: 14800, newUsers: 180 },
  { date: '2025-01-22', users: 15420, newUsers: 210 },
]

const contentTypeData = [
  { type: '文章', count: 3420, percentage: 45.2 },
  { type: '图片', count: 2180, percentage: 28.8 },
  { type: '视频', count: 1240, percentage: 16.4 },
  { type: '其他', count: 720, percentage: 9.6 },
]

const revenueData = [
  { month: '2024-09', revenue: 480000 },
  { month: '2024-10', revenue: 520000 },
  { month: '2024-11', revenue: 580000 },
  { month: '2024-12', revenue: 620000 },
  { month: '2025-01', revenue: 680000 },
]

const topContent = [
  {
    id: '1',
    title: '2025年科技趋势预测',
    author: '科技达人',
    views: 15420,
    likes: 892,
    comments: 234,
  },
  {
    id: '2',
    title: '健康生活小贴士',
    author: '健康专家',
    views: 12680,
    likes: 756,
    comments: 189,
  },
  {
    id: '3',
    title: '投资理财入门指南',
    author: '财经分析师',
    views: 9840,
    likes: 623,
    comments: 156,
  },
]

/**
 * 主分析仪表板
 */
const MainDashboard: React.FC = () => {
  const { hasPermission } = usePermission()
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ])
  const [timePeriod, setTimePeriod] = useState<TimePeriod>(TimePeriod.DAY)

  // 权限检查
  const canViewAnalytics = hasPermission(Permission.ANALYTICS_VIEW)

  // 获取变化趋势图标和颜色
  const getTrendIcon = (changeType: 'INCREASE' | 'DECREASE' | 'NEUTRAL') => {
    switch (changeType) {
      case 'INCREASE':
        return <ArrowUpOutlined style={{ color: '#52c41a' }} />
      case 'DECREASE':
        return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />
      default:
        return null
    }
  }

  const getTrendColor = (changeType: 'INCREASE' | 'DECREASE' | 'NEUTRAL') => {
    switch (changeType) {
      case 'INCREASE': return '#52c41a'
      case 'DECREASE': return '#ff4d4f'
      default: return '#666'
    }
  }

  if (!canViewAnalytics) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h3>权限不足</h3>
        <p>您没有权限查看数据分析</p>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={3} style={{ margin: 0 }}>数据分析仪表板</Title>
          <Text type="secondary">实时监控平台核心指标和业务数据</Text>
        </Col>
        <Col>
          <Space>
            <Select value={timePeriod} onChange={setTimePeriod} style={{ width: 120 }}>
              <Option value={TimePeriod.HOUR}>小时</Option>
              <Option value={TimePeriod.DAY}>天</Option>
              <Option value={TimePeriod.WEEK}>周</Option>
              <Option value={TimePeriod.MONTH}>月</Option>
            </Select>
            <RangePicker 
              value={dateRange}
              onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
            />
          </Space>
        </Col>
      </Row>

      {/* KPI 指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {mockKPIs.map(kpi => (
          <Col xs={24} sm={12} lg={6} key={kpi.id}>
            <Card>
              <Statistic
                title={kpi.name}
                value={kpi.value}
                precision={0}
                valueStyle={{ 
                  color: kpi.status === 'GOOD' ? '#52c41a' : 
                         kpi.status === 'WARNING' ? '#faad14' : '#ff4d4f'
                }}
                prefix={
                  kpi.name.includes('用户') ? <UserOutlined /> :
                  kpi.name.includes('收入') ? <DollarOutlined /> :
                  kpi.name.includes('内容') ? <EyeOutlined /> : undefined
                }
                suffix={kpi.unit}
              />
              <div style={{ marginTop: '8px' }}>
                <Space>
                  {getTrendIcon(kpi.changeType)}
                  <Text style={{ color: getTrendColor(kpi.changeType) }}>
                    {Math.abs(kpi.change)}%
                  </Text>
                  <Text type="secondary">vs 昨日</Text>
                </Space>
              </div>
              {kpi.target && (
                <div style={{ marginTop: '8px' }}>
                  <Progress 
                    percent={Math.round((kpi.value / kpi.target) * 100)} 
                    size="small"
                    status={kpi.value >= kpi.target ? 'success' : 'active'}
                  />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    目标: {kpi.target.toLocaleString()}{kpi.unit}
                  </Text>
                </div>
              )}
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {/* 用户增长趋势 */}
        <Col xs={24} lg={12}>
          <Card title="用户增长趋势">
            <Area
              data={userGrowthData}
              xField="date"
              yField="users"
              height={250}
              smooth
              color="#1890ff"
              areaStyle={{
                fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',
              }}
            />
          </Card>
        </Col>

        {/* 收入趋势 */}
        <Col xs={24} lg={12}>
          <Card title="收入趋势">
            <Column
              data={revenueData}
              xField="month"
              yField="revenue"
              height={250}
              color="#52c41a"
              columnStyle={{
                radius: [4, 4, 0, 0],
              }}
              meta={{
                revenue: {
                  formatter: (value: number) => `¥${(value / 1000).toFixed(0)}K`,
                },
              }}
            />
          </Card>
        </Col>

        {/* 内容类型分布 */}
        <Col xs={24} lg={12}>
          <Card title="内容类型分布">
            <Pie
              data={contentTypeData}
              angleField="count"
              colorField="type"
              height={250}
              radius={0.8}
              label={{
                type: 'outer',
                content: '{name} {percentage}',
              }}
              interactions={[{ type: 'element-active' }]}
            />
          </Card>
        </Col>

        {/* 热门内容排行 */}
        <Col xs={24} lg={12}>
          <Card title="热门内容排行" extra={<Button type="link">查看更多</Button>}>
            <List
              dataSource={topContent}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ 
                          backgroundColor: index === 0 ? '#f56a00' : 
                                           index === 1 ? '#7265e6' : '#00a2ae' 
                        }}
                      >
                        {index + 1}
                      </Avatar>
                    }
                    title={item.title}
                    description={`作者: ${item.author}`}
                  />
                  <div style={{ textAlign: 'right' }}>
                    <div>
                      <Space>
                        <EyeOutlined />
                        <Text>{item.views.toLocaleString()}</Text>
                      </Space>
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Space>
                        <LikeOutlined />
                        <Text>{item.likes}</Text>
                        <CommentOutlined />
                        <Text>{item.comments}</Text>
                      </Space>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card title="用户活跃度">
            <div style={{ marginBottom: '16px' }}>
              <Text>日活跃率</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress percent={21.1} strokeColor="#1890ff" />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  3,245 / 15,420 用户
                </Text>
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Text>周活跃率</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress percent={45.8} strokeColor="#52c41a" />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  7,062 / 15,420 用户
                </Text>
              </div>
            </div>
            <div>
              <Text>月活跃率</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress percent={78.3} strokeColor="#722ed1" />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  12,074 / 15,420 用户
                </Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="内容统计">
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <Statistic
                  title="总内容数"
                  value={45683}
                  prefix={<EyeOutlined />}
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="今日新增"
                  value={892}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<ArrowUpOutlined />}
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="待审核"
                  value={156}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="系统概览">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>服务状态</Text>
                <Tag color="green">正常</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>系统负载</Text>
                <Text>45%</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>在线用户</Text>
                <Text>2,847</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>数据同步</Text>
                <Tag color="blue">实时</Tag>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default MainDashboard 