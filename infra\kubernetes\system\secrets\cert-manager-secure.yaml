# CINA.CLUB Platform - Cert-Manager Secure Configuration
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: v1
kind: Namespace
metadata:
  name: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: system
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cert-manager-secure
  namespace: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: controller
  annotations:
    description: "Secure service account for Cert-Manager with minimal required privileges"

---
# Precise RBAC configuration for Cert-Manager
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cert-manager-controller-secure
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: controller
rules:
  # Core resources needed for certificate management
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch", "create", "update", "patch"]
  
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]
  
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get", "list", "watch"]
  
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch"]
  
  # Networking resources for ACME challenges
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  
  # Cert-Manager CRDs
  - apiGroups: ["cert-manager.io"]
    resources: ["certificates", "certificaterequests", "issuers", "clusterissuers"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  
  - apiGroups: ["cert-manager.io"]
    resources: ["certificates/status", "certificaterequests/status", "issuers/status", "clusterissuers/status"]
    verbs: ["get", "update", "patch"]
  
  # ACME resources
  - apiGroups: ["acme.cert-manager.io"]
    resources: ["orders", "challenges"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  
  - apiGroups: ["acme.cert-manager.io"]
    resources: ["orders/status", "challenges/status"]
    verbs: ["get", "update", "patch"]
  
  # API extension resources (limited scope)
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  
  # Admission controllers (limited scope)
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["validatingwebhookconfigurations", "mutatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cert-manager-controller-secure
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cert-manager-controller-secure
subjects:
  - kind: ServiceAccount
    name: cert-manager-secure
    namespace: cert-manager

---
# Network Policy for Cert-Manager
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cert-manager-network-policy
  namespace: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: network-security
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
      app.kubernetes.io/component: controller
  
  policyTypes:
    - Ingress
    - Egress
  
  # Ingress rules - allow webhook traffic
  ingress:
    - from:
        - namespaceSelector: {}  # Allow from any namespace for webhook
      ports:
        - protocol: TCP
          port: 9443  # Webhook port
  
  # Egress rules - allow specific connections
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow connection to Kubernetes API
    - to: []
      ports:
        - protocol: TCP
          port: 443
    
    # Allow ACME challenges (HTTP/HTTPS)
    - to: []
      ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 443

---
# Resource Quota for Cert-Manager namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: cert-manager-quota
  namespace: cert-manager
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: resource-management
spec:
  hard:
    requests.cpu: "1"
    requests.memory: 1Gi
    limits.cpu: "2"
    limits.memory: 2Gi
    pods: "10"
    secrets: "100" 