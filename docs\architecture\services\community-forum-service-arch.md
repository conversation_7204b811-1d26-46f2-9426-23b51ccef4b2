好的，遵照您的指示。我将为您生成一份专门针对 **`community-forum-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`community-forum-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**富文本内容管理、高性能的Feed流查询、基于热度算法的排序、版主权限体系，以及与平台生态的深度事件驱动集成**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `community-forum-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `community-forum-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 事件驱动 + 为高并发读写优化的数据模型

## 1. 概述

`community-forum-service` 是CINA.CLUB平台用户互动和长篇内容沉淀的主要场所。其核心挑战在于：
1.  **高并发读写**: 帖子的浏览、回复、点赞等操作频率极高，对数据库的读写性能要求苛刻。
2.  **复杂的内容管理**: 需要支持富文本、媒体嵌入、草稿、编辑历史、以及置顶、加精等策展工作流。
3.  **动态内容发现**: 如何通过热度算法等机制，将优质内容推荐给用户，而不是简单的按时间排序。
4.  **精细化权限**: 需要实现一个支持版主(Moderator)的、基于版块的权限管理体系。
5.  **深度的生态集成**: 作为一个核心UGC服务，需要与内容审核、搜索、通知、游戏化等多个服务进行紧密、可靠的协同。

本架构设计通过采用**整洁架构**，结合**事件驱动**模式进行服务解耦，并采用**数据冗余(Denormalization)**和**缓存**策略来优化读取性能，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (事件驱动的UGC生命周期)

```mermaid
graph TD
    subgraph "用户与客户端"
        Client
    end
    
    subgraph "CommunityForumService"
        A[API Layer (gRPC/REST)<br/><em>adapter/transport</em>]
        B[Application Service<br/><em>(application/service)</em>]
        C[Domain Logic<br/><em>(domain/aggregate, domain/service)</em>]
        D[Repository<br/><em>(adapter/repository)</em>]
        E[Kafka Producer<br/><em>adapter/event</em>]
        F[Cache<br/><em>adapter/cache</em>]
    end
    
    subgraph "平台下游服务"
        G[content-moderation-service]
        H[search-indexer-service]
        I[notification-dispatch-service]
        J[gamification-service]
        K[activity-feed-service]
    end

    Client -- "1. 发表/回复帖子" --> A
    A -- "调用" --> B
    B -- "使用" --> C
    C -- "持久化" --> D
    
    B -- "2. 发布事件" --> E
    E -- "ThreadSubmittedEvent" --> Kafka[(Kafka)]
    
    Kafka -- "3a. 内容审核" --> G
    Kafka -- "3b. 预索引" --> H
    
    subgraph "审核通过后"
        G -- "ModerationResultEvent" --> Kafka
        Kafka -- "4. 消费审核结果" --> B
        B -- "更新帖子状态为PUBLISHED" --> D
        B -- "5. 发布最终事件" --> E
        E -- "ThreadPublishedEvent" --> Kafka
    end

    Kafka -- "6a. 更新索引" --> H
    Kafka -- "6b. 发送@/回复通知" --> I
    Kafka -- "6c. 奖励积分" --> J
    Kafka -- "6d. 生成互动Feed" --> K

    Client -- "7. 查询帖子列表" --> A
    A -- "查询" --> B
    B -- "优先查缓存" --> F
    F -- "未命中则查DB" --> D
```

### 2.2 最终目录结构 (`services/community-forum-service/`)

```
community-forum-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # ✨ 后台任务Worker入口 (e.g., 热度分计算) ✨
├── internal/
│   ├── adapter/
│   │   ├── cache/
│   │   │   └── redis_cache.go
│   │   ├── client/
│   │   │   └── content_moderation_client.go
│   │   ├── event/
│   │   │   ├── producer.go
│   │   │   └── moderation_consumer.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── forum_service.go # 核心应用服务实现
│   └── domain/
│       ├── aggregate/
│       │   └── thread_aggregate.go # 封装帖子及其回复、投票的业务规则
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── permission_service.go # ✨ 版主权限检查服务 ✨
│           └── hotness_service.go    # ✨ 热度分计算服务 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Community Rules)

*   `domain/model/`: 使用`/core/models`中与内容、互动相关的`struct`。
*   **`domain/service/permission_service.go`**:
    *   **`PermissionService`**: 一个无状态的领域服务，封装了复杂的版主权限检查逻辑。
    *   **`CanPinThread(actor User, thread Thread)`**, **`CanLockThread(...)`**等方法。
    *   **逻辑**: 这些方法接收操作者(actor)和目标对象(thread)，并根据actor是否为系统管理员、是否为该版块的版主、或是否为帖子作者，来决定操作是否被允许。
*   **`domain/service/hotness_service.go`**:
    *   **`HotnessService`**: 一个无状态的领域服务，封装了热度分计算算法。
    *   **`CalculateScore(votes, replies, views, creationTime)`**: 实现`Hotness = (P-1) / (T+2)^G`等热度算法的纯函数。
*   **`domain/aggregate/thread_aggregate.go`**:
    *   **`Thread`聚合根**: 封装了`Thread`实体及其关联的`Post`列表和`Vote`列表。
    *   **方法**: `AddReply()`, `CastVote()`, `Pin(actor)`, `Lock(actor)`。
    *   **工作方式**: 在执行状态变更前，这些方法会先调用`PermissionService`进行权限检查。通过后才修改聚合根内部状态，并记录领域事件（如`ThreadLockedEvent`）。

### 3.2 `application/` - 应用层 (The Use Cases)

*   **`application/service/forum_service.go`**: 实现`ForumService`接口，是所有业务流程的编排者。
    *   **`CreateThread(ctx, author, title, content, ...)`**:
        1.  开启工作单元（事务）。
        2.  创建一个新的`Thread`聚合根（初始状态为`PENDING_MODERATION`）。
        3.  将聚合根交由仓储持久化。
        4.  提交事务。
        5.  **异步**:
            a. 调用`content-moderation-service`客户端，提交内容进行审核。
            b. **发布`ThreadSubmittedEvent`**到Kafka，通知下游服务（如`search-indexer`）。
    *   **`HandleModerationResult(ctx, result)` (由事件消费者调用)**:
        1.  根据审核结果，从仓储加载`Thread`聚合根。
        2.  调用`thread.Publish()`或`thread.Reject()`方法更新其状态。
        3.  持久化变更。
        4.  如果审核通过，**发布`ThreadPublishedEvent`**。
    *   **`ListThreads(ctx, categoryID, sortBy, page)`**:
        1.  **读优化**: 根据`sortBy`参数决定查询策略。
        2.  如果`sortBy == "hot"`，优先从**缓存(`port.Cache`)**中获取热榜帖子的ID列表。
        3.  如果缓存未命中，或按其他方式排序，则调用`repository.ListThreads(...)`从数据库查询。
        4.  获取到帖子列表后，返回给gRPC handler。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库选型**: **PostgreSQL**。其JSONB和全文检索能力非常适合论坛场景。
    *   **数据模型优化**: **必须**采用**数据冗余(Denormalization)**策略来提升读性能。
        *   `forum_threads`表: 冗余存储`reply_count`, `vote_score`, `last_reply_at`, `last_replier_info (JSONB)`等字段。
        *   `forum_categories`表: 冗余存储`thread_count`, `post_count`, `last_thread_info (JSONB)`。
    *   **更新**: 这些冗余字段在创建新帖子或回复时，通过**数据库触发器(Triggers)**或在应用层的事务中以原子方式更新。
*   **`adapter/cache/`**:
    *   `redis_cache.go`:
        *   **热榜缓存**: 使用**Redis Sorted Set**来缓存热度榜。`Key: hot_threads:{categoryID}`, `Score: hotness_score`, `Member: threadID`。由后台Worker定期更新。
        *   **对象缓存**: 对单个`Thread`和`Post`的完整数据进行缓存，采用Cache-Aside模式。
*   **`adapter/event/`**:
    *   `producer.go`: 封装`pkg/messaging`，用于发布`Thread...Event`。
    *   `moderation_consumer.go`: 消费来自`content-moderation-service`的审核结果事件。
*   **`adapter/grpc/`**: 实现gRPC接口，将请求路由到`application.ForumService`。

### 3.4 `cmd/worker/` - 后台任务

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   **热度分计算器 (Hotness Score Calculator)**:
        *   以**Kubernetes CronJob**的形式定期运行（如每15分钟）。
        *   批量从数据库中拉取近期有活动的帖子。
        *   调用`domain.HotnessService.CalculateScore()`为每个帖子计算新的热度分。
        *   将新的热度分批量更新回数据库。
        *   **更新Redis热榜缓存**。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`community-forum-service`：
1.  **领域驱动设计**: 将复杂的权限检查(`PermissionService`)、热度算法(`HotnessService`)和内容生命周期(`Thread`聚合根)封装在领域层，保证了业务规则的清晰和内聚。
2.  **事件驱动解耦**: 通过Kafka将内容生命周期的关键节点（提交、发布）广播出去，与审核、搜索、通知等下游系统完全解耦，实现了高度的可扩展性和韧性。
3.  **读性能深度优化**: 综合运用**数据冗余**（在SQL中）、**缓存**（在Redis中）和**后台预计算**（热度分Worker）等多种策略，来应对论坛系统典型的高并发读压力。
4.  **后台任务分离**: 将资源密集型的热度计算任务移至独立的Worker进程，确保了在线API的低延迟和高可用性。

这种架构确保了`community-forum-service`在功能丰富、交互复杂的同时，依然保持了代码的健壮、性能的高效和系统的可扩展性，能够为CINA.CLUB平台打造一个充满活力的社区核心。