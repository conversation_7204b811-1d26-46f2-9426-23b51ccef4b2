/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"cina.club/services/activity-feed-service/internal/domain"
)

// InteractionHandler handles social interaction events
type InteractionHandler struct {
	feedRepo        domain.FeedRepository
	unreadCountRepo domain.UnreadCountRepository
	aggregator      *domain.Aggregator
	logger          domain.Logger
}

// NewInteractionHandler creates a new interaction handler
func NewInteractionHandler(
	feedRepo domain.FeedRepository,
	unreadCountRepo domain.UnreadCountRepository,
	aggregator *domain.Aggregator,
	logger domain.Logger,
) *InteractionHandler {
	return &InteractionHandler{
		feedRepo:        feedRepo,
		unreadCountRepo: unreadCountRepo,
		aggregator:      aggregator,
		logger:          logger,
	}
}

// Handle processes interaction events (likes, comments, follows, mentions)
func (h *InteractionHandler) Handle(ctx context.Context, event domain.Event) error {
	eventType := event.GetEventType()

	h.logger.Debug(ctx, "Processing interaction event",
		"event_type", eventType,
		"event_id", event.GetEventID(),
		"user_id", event.GetUserID())

	// Parse event payload
	var eventData map[string]interface{}
	if err := json.Unmarshal(event.GetPayload(), &eventData); err != nil {
		h.logger.Error(ctx, "Failed to parse event payload",
			"event_type", eventType,
			"error", err)
		return fmt.Errorf("failed to parse event payload: %w", err)
	}

	// Create activity feed item based on event type
	item, err := h.createFeedItemFromEvent(eventType, eventData)
	if err != nil {
		h.logger.Error(ctx, "Failed to create feed item from event",
			"event_type", eventType,
			"error", err)
		return fmt.Errorf("failed to create feed item: %w", err)
	}

	if item == nil {
		h.logger.Debug(ctx, "No feed item created for event", "event_type", eventType)
		return nil // Not all events result in feed items
	}

	// Set event metadata
	item.EventID = event.GetEventID()
	item.EventType = eventType

	// Process with aggregation
	result, err := h.aggregator.AggregateOrNew(ctx, item)
	if err != nil {
		h.logger.Error(ctx, "Aggregation processing failed",
			"event_type", eventType,
			"item_id", item.ID,
			"error", err)
		return fmt.Errorf("aggregation processing failed: %w", err)
	}

	// Handle aggregation result
	switch result.Action {
	case domain.AggregationActionCreate:
		if err := h.feedRepo.CreateFeedItem(ctx, result.Item); err != nil {
			h.logger.Error(ctx, "Failed to create feed item",
				"item_id", result.Item.ID,
				"error", err)
			return fmt.Errorf("failed to create feed item: %w", err)
		}

		// Increment unread count
		if err := h.unreadCountRepo.IncrementUnreadCount(ctx, result.Item.UserID, result.Item.FeedType); err != nil {
			h.logger.Warn(ctx, "Failed to increment unread count",
				"user_id", result.Item.UserID,
				"feed_type", result.Item.FeedType,
				"error", err)
		}

		h.logger.Info(ctx, "Created new interaction feed item",
			"item_id", result.Item.ID,
			"activity_type", result.Item.ActivityType,
			"user_id", result.Item.UserID)

	case domain.AggregationActionUpdate:
		// Get existing item and update it
		existingItem, err := h.feedRepo.GetFeedItem(ctx, result.ExistingItemID)
		if err != nil {
			h.logger.Error(ctx, "Failed to get existing item for update",
				"existing_item_id", result.ExistingItemID,
				"error", err)
			return fmt.Errorf("failed to get existing item: %w", err)
		}

		// Update aggregated actors
		existingItem.AggregatedActors = append(existingItem.AggregatedActors, result.UpdatedActors...)
		existingItem.AggregationCount = len(existingItem.AggregatedActors)
		existingItem.UpdatedAt = item.CreatedAt

		// Update display text for aggregated content
		h.updateAggregatedDisplayText(existingItem)

		if err := h.feedRepo.UpdateFeedItem(ctx, existingItem); err != nil {
			h.logger.Error(ctx, "Failed to update aggregated feed item",
				"item_id", existingItem.ID,
				"error", err)
			return fmt.Errorf("failed to update feed item: %w", err)
		}

		h.logger.Info(ctx, "Updated aggregated interaction feed item",
			"item_id", existingItem.ID,
			"aggregation_count", existingItem.AggregationCount)
	}

	return nil
}

// createFeedItemFromEvent creates a feed item based on the event type and data
func (h *InteractionHandler) createFeedItemFromEvent(eventType string, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	// Extract common fields
	targetUserID, _ := eventData["target_user_id"].(string)
	actorUserID, _ := eventData["actor_user_id"].(string)
	actorName, _ := eventData["actor_name"].(string)
	actorAvatar, _ := eventData["actor_avatar"].(string)

	if targetUserID == "" || actorUserID == "" {
		return nil, fmt.Errorf("missing required user IDs")
	}

	// Create actor
	actor := domain.Actor{
		ID:     actorUserID,
		Name:   actorName,
		Avatar: actorAvatar,
	}

	// Determine activity type and create appropriate feed item
	switch eventType {
	case "forum.post.liked":
		return h.createPostLikedItem(targetUserID, actor, eventData)
	case "forum.post.commented":
		return h.createPostCommentedItem(targetUserID, actor, eventData)
	case "user.followed":
		return h.createUserFollowedItem(targetUserID, actor, eventData)
	case "user.mentioned":
		return h.createUserMentionedItem(targetUserID, actor, eventData)
	case "service.reviewed":
		return h.createServiceReviewedItem(targetUserID, actor, eventData)
	default:
		h.logger.Warn(context.Background(), "Unknown interaction event type", "event_type", eventType)
		return nil, nil
	}
}

// createPostLikedItem creates a feed item for post likes
func (h *InteractionHandler) createPostLikedItem(userID string, actor domain.Actor, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	postID, _ := eventData["post_id"].(string)
	postTitle, _ := eventData["post_title"].(string)

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeInteractions, domain.ActivityTypePostLiked)
	item.AddActor(actor)
	item.SetTarget(domain.Target{
		ID:   postID,
		Type: "post",
		Name: postTitle,
		URL:  fmt.Sprintf("/posts/%s", postID),
	})
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("%s liked your post", actor.Name),
		Message: fmt.Sprintf("Your post \"%s\" was liked", postTitle),
		IconURL: "/icons/like.png",
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://post/%s", postID)

	return item, nil
}

// createPostCommentedItem creates a feed item for post comments
func (h *InteractionHandler) createPostCommentedItem(userID string, actor domain.Actor, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	postID, _ := eventData["post_id"].(string)
	postTitle, _ := eventData["post_title"].(string)
	commentText, _ := eventData["comment_text"].(string)

	// Truncate comment for display
	if len(commentText) > 100 {
		commentText = commentText[:100] + "..."
	}

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeInteractions, domain.ActivityTypePostCommented)
	item.AddActor(actor)
	item.SetTarget(domain.Target{
		ID:   postID,
		Type: "post",
		Name: postTitle,
		URL:  fmt.Sprintf("/posts/%s", postID),
	})
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("%s commented on your post", actor.Name),
		Message: commentText,
		IconURL: "/icons/comment.png",
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://post/%s", postID)

	return item, nil
}

// createUserFollowedItem creates a feed item for user follows
func (h *InteractionHandler) createUserFollowedItem(userID string, actor domain.Actor, _ map[string]interface{}) (*domain.ActivityFeedItem, error) {
	item := domain.NewActivityFeedItem(userID, domain.FeedTypeInteractions, domain.ActivityTypeFollowed)
	item.AddActor(actor)
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("%s started following you", actor.Name),
		IconURL: "/icons/follow.png",
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://user/%s", actor.ID)

	return item, nil
}

// createUserMentionedItem creates a feed item for user mentions
func (h *InteractionHandler) createUserMentionedItem(userID string, actor domain.Actor, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	contentID, _ := eventData["content_id"].(string)
	contentType, _ := eventData["content_type"].(string)
	contentTitle, _ := eventData["content_title"].(string)
	mentionText, _ := eventData["mention_text"].(string)

	// Truncate mention text for display
	if len(mentionText) > 100 {
		mentionText = mentionText[:100] + "..."
	}

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeInteractions, domain.ActivityTypeUserMentioned)
	item.AddActor(actor)
	item.SetTarget(domain.Target{
		ID:   contentID,
		Type: contentType,
		Name: contentTitle,
		URL:  fmt.Sprintf("/%s/%s", contentType, contentID),
	})
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("%s mentioned you", actor.Name),
		Message: mentionText,
		IconURL: "/icons/mention.png",
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://%s/%s", contentType, contentID)

	return item, nil
}

// createServiceReviewedItem creates a feed item for service reviews
func (h *InteractionHandler) createServiceReviewedItem(userID string, actor domain.Actor, eventData map[string]interface{}) (*domain.ActivityFeedItem, error) {
	serviceID, _ := eventData["service_id"].(string)
	serviceName, _ := eventData["service_name"].(string)
	rating, _ := eventData["rating"].(float64)

	item := domain.NewActivityFeedItem(userID, domain.FeedTypeInteractions, domain.ActivityTypeReplyReceived)
	item.AddActor(actor)
	item.SetTarget(domain.Target{
		ID:   serviceID,
		Type: "service",
		Name: serviceName,
		URL:  fmt.Sprintf("/services/%s", serviceID),
	})
	item.SetDisplay(domain.DisplayData{
		Title:   fmt.Sprintf("%s reviewed your service", actor.Name),
		Message: fmt.Sprintf("Your service \"%s\" received a %.1f star rating", serviceName, rating),
		IconURL: "/icons/review.png",
	})
	item.DeepLinkURL = fmt.Sprintf("cinaclub://service/%s", serviceID)

	return item, nil
}

// updateAggregatedDisplayText updates the display text for aggregated items
func (h *InteractionHandler) updateAggregatedDisplayText(item *domain.ActivityFeedItem) {
	count := item.AggregationCount
	if count <= 1 {
		return
	}

	// Get primary actor name
	primaryActorName := "Someone"
	if len(item.Actors) > 0 {
		primaryActorName = item.Actors[0].Name
	}

	switch item.ActivityType {
	case domain.ActivityTypePostLiked:
		if count == 2 {
			item.Display.Title = fmt.Sprintf("%s and 1 other liked your post", primaryActorName)
		} else {
			item.Display.Title = fmt.Sprintf("%s and %d others liked your post", primaryActorName, count-1)
		}
	case domain.ActivityTypePostCommented:
		if count == 2 {
			item.Display.Title = fmt.Sprintf("%s and 1 other commented on your post", primaryActorName)
		} else {
			item.Display.Title = fmt.Sprintf("%s and %d others commented on your post", primaryActorName, count-1)
		}
	case domain.ActivityTypeFollowed:
		if count == 2 {
			item.Display.Title = fmt.Sprintf("%s and 1 other started following you", primaryActorName)
		} else {
			item.Display.Title = fmt.Sprintf("%s and %d others started following you", primaryActorName, count-1)
		}
	}

	// Generate aggregated actors list for detailed view
	actorNames := make([]string, 0, len(item.AggregatedActors))
	for _, actor := range item.AggregatedActors {
		actorNames = append(actorNames, actor.Name)
	}

	if len(actorNames) > 3 {
		item.Display.Message = fmt.Sprintf("%s and %d others", strings.Join(actorNames[:3], ", "), len(actorNames)-3)
	} else {
		item.Display.Message = strings.Join(actorNames, ", ")
	}
}
