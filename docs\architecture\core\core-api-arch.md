好的，遵照您的指示。我将为您生成一份专门针对 **`core/api`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`core/api` SRS，并严格遵循我们在`L4_Service_Design_Patterns`中定义的架构原则。它将深入到**Protobuf文件的组织、命名规范、标准API设计模式、以及`buf`工具链的配置和使用**等实现细节，为所有开发团队提供一份清晰、可执行的、用于定义平台通信契约的“立法指南”。

---
### CINA.CLUB - `core/api` 内部架构设计

**文档状态**: 设计完成 (Designed) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `core/api-srs.md` (v1.0)
**核心架构**: Protobuf v3 + gRPC + `buf`工具链

## 1. 概述

`core/api` 是CINA.CLUB Monorepo中**所有通信的“圣经”**。它定义了平台所有组件之间交互的**语言和语法**。其架构设计的核心目标是：
1.  **单一事实来源 (SSoT)**: 任何跨服务的API或事件定义，**必须**且**只能**在这里找到。
2.  **强类型与明确性**: 契约必须是强类型的、无歧义的，能在编译时暴露问题。
3.  **语言无关性**: 必须能为平台所有技术栈（Go, TypeScript, Kotlin, Swift, C#, Python）生成一致的、原生的代码。
4.  **可演进性与稳定性**: 必须支持API的向后兼容演进，确保对API的修改不会破坏现有的消费者。
5.  **自动化与治理**: API的定义、校验、代码生成和文档生成必须是高度自动化的。

本架构设计通过采用**Protocol Buffers v3**作为接口定义语言(IDL)，并结合**`buf`工具链**进行全生命周ールの治理，来构建一个世界级的API契约中心。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (API契约的生命周期)

```mermaid
graph TD
    subgraph "设计与开发 (Design & Develop)"
        A[开发者<br/><em>(在/core/api下编写.proto文件)</em>]
    end

    subgraph "治理与校验 (Govern & Validate)"
        style "治理与校验 (Govern & Validate)" fill:#e0f7fa
        B(buf CLI)
        C[Linting Rules<br/><em>(buf.yaml)</em>]
        D[Breaking Change Rules<br/><em>(buf.yaml)</em>]
    end

    subgraph "生成与消费 (Generate & Consume)"
        style "生成与消费 (Generate & Consume)" fill:#e8f5e9
        E(buf generate)
        F[生成配置<br/><em>(buf.gen.yaml)</em>]
        G[Go Code<br/><em>(services/*, core/*)</em>]
        H[TypeScript Code<br/><em>(apps/web)</em>]
        I[Swift/Kotlin Code<br/><em>(apps/apple, apps/android)</em>]
        J[OpenAPI Spec<br/><em>(docs/api)</em>]
    end
    
    subgraph "版本控制 (Git)"
        Git
    end

    A --> Git
    Git -- "CI/CD触发" --> B
    
    B -- "使用" --> C
    B -- "使用" --> D
    
    B -- "校验通过后, 调用" --> E
    E -- "使用" --> F
    
    E -- "生成" --> G & H & I & J
    G & H & I & J --> Git
```

### 2.2 最终目录结构 (`core/api/`)

```
core/api/
├── buf.gen.yaml                # ✨ 代码生成配置文件 ✨
├── buf.yaml                    # ✨ Lint和破坏性变更检测配置文件 ✨
├── buf.lock                    # Buf依赖锁定文件 (e.g., googleapis, protoc-gen-validate)
└── proto/
    └── v1/
        ├── common/             # ✨ 平台级通用类型 ✨
        │   ├── common.proto
        │   ├── pagination.proto
        │   └── money.proto
        ├── errors/
        │   └── errors.proto    # 标准化错误详情
        │
        ├── user_core/          # ✨ 按服务领域划分的目录 ✨
        │   ├── user_core_service.proto
        │   ├── user_model.proto
        │   └── user_events.proto
        │
        ├── billing/
        │   ├── billing_service.proto
        │   ├── billing_model.proto
        │   └── billing_events.proto
        │
        └── ... (所有其他服务领域)
```

**设计决策**:
*   **按领域/服务划分目录**: 将每个微服务相关的`.proto`文件组织在自己的子目录中（如`user_core/`），而不是将所有文件平铺在`v1/`下。这极大地提高了可维护性和所有权清晰度。
*   **分离`service`, `model`, `events`**: 在每个领域目录中，将`service`定义、`message`定义和`event`定义拆分到不同的文件中，遵循单一职责原则。

---

## 3. `buf`工具链配置详解

### 3.1 `buf.yaml` - 规则与治理

```yaml
# core/api/buf.yaml
version: v1
name: buf.build/cinaclub/core
deps:
  - buf.build/googleapis/googleapis
  - buf.build/envoyproxy/protoc-gen-validate
lint:
  use:
    - DEFAULT # 使用Buf的默认Lint规则集
  except:
    - RPC_REQUEST_STANDARD_NAME # (可选) 允许一些例外
  rpc_allow_google_protobuf_empty_requests: true
breaking:
  use:
    - FILE # 保证向后兼容性，是生产环境的最高标准
```
*   **`deps`**: 明确声明对外部Protobuf库（如Google通用类型）的依赖。
*   **`lint.use: [DEFAULT]`**: 强制执行一套业界最佳实践的命名和风格规范。
*   **`breaking.use: [FILE]`**: 强制执行最严格的向后兼容性检查。任何破坏性变更（如删除字段）都会在CI中导致失败。

### 3.2 `buf.gen.yaml` - 代码生成

```yaml
# core/api/buf.gen.yaml
version: v2
plugins:
  # --- Go Backend ---
  - remote: buf.build/protocolbuffers/plugins/go:v1.31.0
    out: ../../ # 输出到Monorepo根目录
    opt:
      - paths=source_relative
  - remote: buf.build/grpc/plugins/go:v1.3.0
    out: ../../
    opt:
      - paths=source_relative
      - require_unimplemented_servers=false
  - remote: buf.build/envoyproxy/plugins/protoc-gen-validate:v0.10.1
    out: ../../
    opt:
      - paths=source_relative
      - lang=go

  # --- TypeScript Frontend (gRPC-Web) ---
  - remote: buf.build/grpc-ecosystem/plugins/ts:v1.2.0
    out: ../apps/web/src/gen/proto # 输出到Web应用指定目录
    opt:
      - long_type_string
  - remote: buf.build/grpc-ecosystem/plugins/grpc-web:v1.4.2
    out: ../apps/web/src/gen/proto
    opt:
      - import_style=typescript
      - mode=grpcwebtext

  # --- OpenAPI Spec for REST Gateway ---
  - remote: buf.build/grpc-ecosystem/plugins/openapiv2:v2.15.0
    out: ../docs/api/rest # 输出到文档目录
    opt:
      - logtostderr=true
```
*   **声明式生成**: 这个文件清晰地声明了我们需要为哪些语言、使用哪些插件、以及输出到哪个目录。
*   **自动化基础**: `scripts/gen-proto.sh`脚本的核心就是一行命令：`buf generate core/api`。

---

## 4. 标准API设计模式实现

### 4.1 资源导向的URL (HTTP注解)
```protobuf
// core/api/proto/v1/user_core/user_core_service.proto
service UserCoreService {
  rpc GetUser(GetUserRequest) returns (user_core.User) {
    option (google.api.http) = {
      get: "/v1/users/{user_id}"
    };
  }
}
message GetUserRequest {
  string user_id = 1 [(validate.rules).string.uuid = true];
}
```

### 4.2 部分更新 (FieldMask)
```protobuf
import "google/protobuf/field_mask.proto";

message UpdateUserProfileRequest {
  string user_id = 1 [(validate.rules).string.uuid = true];
  user_core.UserProfile profile = 2;
  google.protobuf.FieldMask update_mask = 3; // 告诉后端只更新哪些字段
}
```

### 4.3 无状态分页 (Page Token)
```protobuf
// core/api/proto/v1/common/pagination.proto
message ListRequest {
  int32 page_size = 1 [(validate.rules).int32 = {lte: 100}];
  string page_token = 2;
}

message ListResponse {
  // repeated items...
  string next_page_token = 1;
}
```

---

## 5. 总结

本架构设计通过以下关键点来构建一个生产级的`core/api`：
1.  **`buf`作为核心治理工具**: 利用`buf`实现了对Protobuf定义的**Lint、破坏性变更检测、依赖管理和代码生成**的全生命周期自动化治理。
2.  **清晰的目录与文件组织**: 采用“按领域划分目录，按职责拆分文件”的模式，解决了大型项目中`.proto`文件管理的混乱问题。
3.  **标准化的API设计模式**: 强制所有API遵循业界成熟的设计模式（RESTful映射、FieldMask、无状态分页），保证了平台API的一致性和易用性。
4.  **与CI/CD的深度集成**: API契约的任何变更都会在CI流水线中受到自动校验，不合规或破坏性的变更会被自动拦截，保证了主分支的稳定性。
5.  **多语言代码生成的自动化**: 通过一个`buf.gen.yaml`文件，集中管理了为所有目标平台生成代码的逻辑，实现了真正的“一次定义，多端生成”。

这种架构确保了`core/api`能够作为一个**稳定、可靠、易于协作且面向未来**的通信契约中心，为CINA.CLUB整个分布式系统的健康、高效演进提供了最坚实的基础。