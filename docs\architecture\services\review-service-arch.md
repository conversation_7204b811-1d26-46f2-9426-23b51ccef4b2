好的，遵照您的指示。我将为您生成一份专门针对 **`review-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`review-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**多维度评价体系的实现、评价聚合的高性能计算、与AI服务协同进行内容增强、以及公正的申诉与仲裁流程**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `review-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `review-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + 事件驱动 + 为高并发读写优化的聚合模型

## 1. 概述

`review-service` 是CINA.CLUB平台的**信任基础设施**和**信誉计算中心**。它为平台所有可被评价的实体（如服务、任务、用户）提供统一的评价与反馈能力。其核心挑战在于：
1.  **高并发读写**: 评价的提交（写）和展示（读）都是平台的高频操作。
2.  **实时聚合计算**: 对评价目标的总体评分、各维度评分等统计数据，需要近乎实时地更新，并且查询性能要高。
3.  **模型的灵活性**: 需要支持对不同类型的实体，采用不同的评价维度和模板。
4.  **公正性与防作弊**: 必须有机制来校验评价资格，处理不实评价的申诉，并防止恶意刷分。
5.  **与生态的深度集成**: 需要与`service-offering-service`（触发评价）、`content-moderation`（审核）、`ai-assistant`（内容增强）、`gamification`（奖励）等多个服务进行可靠的协同。

本架构设计通过采用**整洁架构**，并利用**事件驱动**来异步处理聚合计算和内容增强，同时通过**数据冗余**和**缓存**来优化读取性能，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (评价提交与聚合更新流程)

```mermaid
graph TD
    subgraph "上游服务 (触发者)"
        style "上游服务 (触发者)" fill:#eee
        SourceService[e.g., service-offering]
    end

    subgraph "ReviewService"
        style ReviewService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[Application Service<br/><em>application/service</em>]
        C[Domain Logic<br/><em>(domain/aggregate, domain/service)</em>]
        D[Repository<br/><em>adapter/repository</em>]
        E[Kafka Producer<br/><em>adapter/event</em>]
        F[Kafka Consumer (for self)<br/><em>adapter/event</em>]
    end

    subgraph "平台下游服务"
        style "平台下游服务" fill:#f3e5f5
        ModerationSvc[content-moderation]
        AISvc[ai-assistant]
        SearchIndexer[search-indexer]
    end

    SourceService -- "1. 触发评价资格" --> ClientApp[(Client App)]
    ClientApp -- "2. POST /reviews" --> A
    
    A -- "调用" --> B
    B -- "3. Validate eligibility & create Review" --> C & D
    
    B -- "4. Publish ReviewSubmittedEvent" --> E
    E --> Kafka[(Kafka)]

    Kafka -- "5a. 审核" --> ModerationSvc
    Kafka -- "5b. AI增强" --> AISvc
    Kafka -- "5c. ✨ 异步聚合 ✨" --> F
    
    F -- "调用" --> B
    B -- "6. Atomically update RatingAggregate in DB" --> D
    
    ModerationSvc & AISvc -- "Result Events" --> Kafka
    Kafka -- "消费结果" --> B
    B -- "更新Review状态或内容" --> D

    D -- "变更后发布事件" --> E
    E -- "ReviewPublishedEvent" --> Kafka
    Kafka -- "索引" --> SearchIndexer
```

### 2.2 最终目录结构 (`services/review-service/`)

```
review-service/
├── cmd/server/
│   └── main.go                 # API服务和事件消费者的统一入口
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   └── ai_assistant_client.go
│   │   ├── event/
│   │   │   ├── producer.go
│   │   │   └── review_event_consumer.go # ✨ 消费自身事件以进行异步聚合 ✨
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── review_service.go # 核心应用服务实现
│   └── domain/
│       ├── aggregate/
│       │   └── review_aggregate.go # 封装评价及其回复、申诉的业务规则
│       ├── model/
│       │   └── alias.go
│       └── service/
│           ├── rating_aggregator_service.go # ✨ 评分聚合计算服务 ✨
│           └── eligibility_service.go # 评价资格校验服务
├── config/
│   └── review_templates.yaml   # ✨ 多维度评价模板配置文件 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `config/review_templates.yaml` - 评价模板的“单一事实来源”

```yaml
templates:
  - target_entity_type: "SERVICE_OFFERING"
    dimensions:
      - id: "communication"
        name: "沟通交流"
        description: "服务过程中的沟通是否清晰、及时？"
      - id: "professionalism"
        name: "专业程度"
      - id: "punctuality"
        name: "准时守信"
    tags:
      positive: ["服务超预期", "态度友好", "性价比高"]
      negative: ["态度恶劣", "迟到严重", "与描述不符"]
  - target_entity_type: "SHARED_KB_ITEM"
    # CKB条目只有“有用/无用”反馈，没有星级，此处可为空或不同结构
```

### 3.2 `domain/` - 领域层 (The Trust Rules)

*   `domain/model/`: 使用`/core/models`中与评价相关的`struct`，如`Review`, `RatingAggregate`。
*   **`domain/service/eligibility_service.go`**:
    *   **`EligibilityService`**: 封装评价资格的校验逻辑。
    *   **`CanPostReview(ctx, reviewerID, target)`**:
        1.  根据`target`的类型，调用对应的上游服务（如`service-offering-service`）的内部接口。
        2.  查询该用户是否完成了与该目标相关的、可触发评价的交易或互动。
        3.  返回校验结果。
*   **`domain/service/rating_aggregator_service.go`**: **这是聚合计算的核心**。
    *   **`RatingAggregator`**: 一个无状态的领域服务。
    *   **`RecalculateForNewReview(currentAgg, newReview)`**:
        *   这是一个纯函数，接收当前的`RatingAggregate`和一条新评价。
        *   **逻辑**: 使用增量平均算法，高效地计算出新的总体平均分和各子维度的平均分，并更新评分分布计数。
        *   `new_avg = (old_avg * old_count + new_rating) / (old_count + 1)`
        *   （高级）如果引入信誉权重，则算法会更复杂：`new_avg = (sum_of_weighted_ratings + new_rating * new_weight) / (sum_of_weights + new_weight)`。
    *   **`RecalculateForDeletedReview(...)`**: 实现反向计算。
*   **`domain/aggregate/review_aggregate.go`**:
    *   **`Review`聚合根**: 封装了`Review`实体及其回复、申诉`Appeal`等。
    *   **方法**: `AddReply()`, `FileAppeal(appellant, reason)`。
    *   `FileAppeal`方法会检查申诉人是否有权申诉（如必须是被评价方），然后创建一个新的`Appeal`实体并附加到聚合根，同时记录`ReviewAppealFiledEvent`。

### 3.3 `application/` - 应用层 (The Use Cases)

*   **`application/service/review_service.go`**: 实现`ReviewService`接口。
    *   **`SubmitReview(ctx, reviewData)`**:
        1.  **调用`domain.EligibilityService.CanPostReview()`**进行资格校验。
        2.  从配置中加载与`targetEntityType`匹配的评价模板，并验证`reviewData`中的维度和标签是否合法。
        3.  开启数据库事务，调用`repository.CreateReview()`创建评价记录（初始状态`PENDING_MODERATION`）。
        4.  提交事务。
        5.  **发布`ReviewSubmittedEvent`到Kafka**。这个事件是触发所有下游流程（审核、AI增强、聚合）的起点。
    *   **`HandleReviewSubmittedEvent(ctx, event)` (由内部消费者调用)**:
        *   **这是一个核心的异步处理流程**。
        *   **步骤1**: 调用`repository.UpdateRatingAggregate()`。这个方法内部会：
            a. **使用`SELECT ... FOR UPDATE`锁定**对应的`rating_aggregates`行。
            b. 加载当前的聚合数据。
            c. 调用`domain.RatingAggregatorService`计算新的聚合值。
            d. 将新值写回数据库。
        *   这是一个**最终一致性**的操作。
    *   **`HandleModerationResult(ctx, result)`**: 根据审核结果，更新评价的状态为`PUBLISHED`或`REJECTED`。如果通过，则发布`ReviewPublishedEvent`。
    *   **`HandleAIEnrichmentResult(ctx, result)`**: 将AI生成的摘要和关键词更新到对应的`Review`记录中。
    *   **`ListReviews(ctx, target)`**: 从仓储分页查询评价列表。

### 3.4 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库**: **PostgreSQL**。
    *   `repo.go`:
        *   **`UpdateRatingAggregate`**: **必须在一个数据库事务中**，并使用`SELECT ... FOR UPDATE`对`rating_aggregates`行加锁，以保证在高并发下聚合计算的原子性和正确性。
*   **`adapter/event/`**:
    *   `producer.go`: 封装`pkg/messaging`，发布`ReviewSubmittedEvent`等。
    *   `review_event_consumer.go`: **这是一个内部消费者**，它消费自己服务发布的`ReviewSubmittedEvent`，以触发**异步的评分聚合**。这种“自己消费自己”的模式是实现写操作与高耗时计算解耦的常用方法。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现gRPC接口，将请求路由到`application.ReviewService`。

### 3.5 `cmd/worker/` (可选，用于后台任务)

*   如果聚合计算逻辑非常复杂，或需要定期对所有评分进行重新校准，可以将其放入一个独立的Worker中，由CronJob定期触发。对于增量更新，使用内部事件消费者通常已足够。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`review-service`：
1.  **异步聚合**: 将对性能影响最大的**评分聚合计算**，通过发布和消费内部事件的方式，从主体的写流程中剥离出来，实现了异步化。这保证了用户提交评价的API可以快速响应。
2.  **配置驱动的评价模板**: 将多维度评价的定义外部化到配置文件中，使得运营团队可以灵活地为不同业务场景配置不同的评价体系，而无需修改代码。
3.  **领域服务封装核心算法**: 将评价资格校验(`EligibilityService`)和评分聚合计算(`RatingAggregatorService`)等纯粹的、复杂的业务规则封装在无状态的领域服务中，易于独立测试和维护。
4.  **原子性的数据更新**: 在仓储层，通过数据库**事务和悲观锁**，确保了对高并发的聚合统计数据的更新是安全和一致的。
5.  **事件驱动的生态协同**: 通过向Kafka发布标准化的领域事件（`ReviewSubmittedEvent`, `ReviewPublishedEvent`），与内容审核、AI增强、搜索索引等下游服务实现了完美的解耦。

这种架构确保了`review-service`能够作为一个**公正、透明、高性能且可扩展**的信任基础设施，为CINA.CLUB平台的健康交易生态提供坚实的数据支持。