好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/messaging`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`pkg/messaging`的职责、接口、与Kafka的集成策略和最佳实践，作为所有后端服务统一消息收发的权威设计蓝图。

---
### CINA.CLUB - 共享后端库 `pkg/messaging` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师/首席工程师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计与集成策略](#3-核心设计与集成策略)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [使用示例与最佳实践](#6-使用示例与最佳实践)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的事件驱动微服务架构中，消息队列（Kafka）是实现服务解耦、异步处理和数据流动的核心。为了避免每个服务都重复实现与Kafka的复杂交互（如连接管理、序列化、错误处理、追踪上下文传播），`pkg/messaging` 包应运而生。其目的在于提供一套**标准化的、高可靠的、可观测的**消息队列客户端封装，为所有后端服务提供简单、统一的事件发布和消费接口。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   提供一个通用的**生产者(Producer)**接口，用于向Kafka发布消息。
    *   提供一个通用的**消费者组(Consumer Group)**封装，用于从Kafka消费消息。
    *   处理消息的**序列化与反序列化**（使用Protocol Buffers）。
    *   自动在消息头中**注入和提取分布式追踪上下文**（OpenTelemetry）。
    *   封装连接管理、错误处理和重试逻辑。
*   **范围之外 (Out-of-Scope)**:
    *   **消息队列(Kafka)集群的部署和运维**: 由`infra/`和DevOps/SRE团队负责。
    *   **Topic的创建和管理**: 通常由DevOps/SRE团队通过自动化工具管理。
    *   **消息内容的定义**: 由`/core/api/proto/v1`中定义的`.proto`文件负责。
    *   **具体的业务处理逻辑**: 这是各个服务中事件消费者的职责。

#### 1.3. 目标用户
*   **CINA.CLUB所有需要进行异步通信的后端Go微服务**的开发者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/messaging` 是位于`pkg/`目录下的一个核心基础设施库。它封装了与Kafka客户端库的直接交互，为上层应用提供了一个更高层次、更易于使用的抽象。

#### 2.2. 设计原则
*   **抽象与解耦**: 开发者应面向`pkg/messaging`定义的`Producer`和`ConsumerHandler`接口编程，而不是直接与`segmentio/kafka-go`等底层库打交道。
*   **可靠性优先 (Reliability First)**: 默认提供“至少一次(At-Least-Once)”的投递和消费语义。封装健壮的错误处理、重试和死信队列(DLQ)逻辑。
*   **可观测性内建 (Observability by Default)**: 任何通过本包收发的消息，都必须自动处理分布式追踪上下文的传播，并记录关键的日志和指标。
*   **类型安全**: 利用Protobuf强制实现消息内容的类型安全。
*   **配置驱动**: 所有Kafka相关的参数（Brokers地址、Topic名称、消费者组ID等）都必须通过`pkg/config`进行配置。

---

### 3. 核心设计与集成策略

#### 3.1. 消息格式约定
*   **消息体 (Value)**: 必须是经过**Protocol Buffers**序列化后的二进制数据。这保证了跨语言的兼容性和高效的序列化性能。
*   **消息头 (Headers)**:
    *   **分布式追踪**: 必须使用W3C Trace Context标准，在消息头中注入/提取`traceparent`和`tracestate`。
    *   **事件元数据**: 必须包含`event-id`, `event-type`, `source-service`等标准元数据头，便于路由和调试。

#### 3.2. 生产者 (Producer) 设计
*   提供一个单例的、线程安全的`Producer`实例。
*   内部使用`kafka-go`的`Writer`，并配置为异步、批量发送以获得高吞吐量。
*   `Publish`方法接收`context.Context`和`proto.Message`。它会：
    1.  从`ctx`中提取OpenTelemetry的Trace Span。
    2.  将Trace Context注入到Kafka消息头中。
    3.  将`proto.Message`序列化为二进制。
    4.  将消息写入`kafka-go`的内部缓冲区。
    5.  返回的错误代表写入缓冲区失败，而不是消息已确认送达Broker。投递状态通过日志和指标进行监控。

#### 3.3. 消费者 (Consumer) 设计
*   提供一个`ConsumerGroup`结构体，用于管理一个消费者组的生命周期。
*   开发者需要提供一个实现了`ConsumerHandler`接口的业务逻辑处理器。
*   `ConsumerGroup`的`Run`方法会启动一个无限循环：
    1.  从Kafka拉取一批消息。
    2.  对于每条消息：
        a. 从消息头中提取Trace Context，并创建一个新的子Span。
        b. 将Trace信息和`event-id`等注入到新的`context.Context`中。
        c. 将消息体反序列化为对应的`proto.Message`。
        d. 调用开发者提供的`Handler.Handle(ctx, message)`方法。
    3.  如果`Handle`方法返回`nil`（无错误），则提交该消息的offset。
    4.  如果`Handle`方法返回错误，则根据错误类型和重试策略进行重试。达到最大重试次数后，将消息发送到**死信队列(DLQ)**，然后提交原消息的offset。
    5.  处理`SIGINT`/`SIGTERM`信号，实现优雅关闭。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 生产者
*   **FR4.1.1 (工厂函数)**: 提供`NewProducer(cfg KafkaConfig)`函数，根据配置创建一个全局生产者实例。
*   **FR4.1.2 (发布接口)**: 提供`Publish(ctx context.Context, topic string, key string, msg proto.Message)`接口。
*   **FR4.1.3 (Protobuf序列化)**: 必须自动将传入的`proto.Message`序列化。
*   **FR4.1.4 (追踪上下文注入)**: 必须自动从`ctx`中提取追踪信息并注入到消息头。

#### 4.2. 消费者
*   **FR4.2.1 (消费者组封装)**: 提供`NewConsumerGroup(cfg KafkaConsumerConfig, handler ConsumerHandler, logger *slog.Logger, ...)`函数来创建一个消费者组实例。
*   **FR4.2.2 (处理器接口)**: 定义一个`ConsumerHandler`接口，包含`Handle(ctx context.Context, msg proto.Message) error`方法，供业务服务实现。
*   **FR4.2.3 (Protobuf反序列化)**: 消费者必须能根据消息头中的`event-type`，将消息体反序列化为正确的Protobuf消息类型。这需要一个`event-type`到`proto.Message`类型的映射注册表。
*   **FR4.2.4 (追踪上下文提取)**: 必须自动从消息头中提取追踪信息，并创建一个新的、带有追踪上下文的`context.Context`传递给处理器。
*   **FR4.2.5 (可靠消费)**:
    *   **自动Offset管理**: 成功处理后自动提交offset。
    *   **错误处理与重试**: 实现带指数退避的有限次重试。
    *   **死信队列(DLQ)**: 重试失败后，将原始消息（包含所有头和错误信息）发布到一个专用的DLQ Topic。

---

### 5. 接口定义 (API Specification)

```go
// pkg/messaging/producer.go

type Producer interface {
    // Publish 发布一条消息到指定的topic。
    // key用于分区，msg是Protobuf消息体。
    Publish(ctx context.Context, topic string, key string, msg proto.Message) error
    Close() error
}

func NewProducer(cfg KafkaProducerConfig, logger *slog.Logger, tracer trace.Tracer) (Producer, error)


// pkg/messaging/consumer.go

// ConsumerHandler 是业务逻辑需要实现的接口。
type ConsumerHandler interface {
    // Handle 处理单条消息。返回nil表示成功，返回error将触发重试/DLQ逻辑。
    Handle(ctx context.Context, msg proto.Message) error
}

// ConsumerGroup 管理一个消费者组的生命周期。
type ConsumerGroup struct { ... }

// NewConsumerGroup 创建一个新的消费者组。
// eventTypeToProtoMap 是一个从事件类型字符串到空的proto.Message实例的映射，用于反序列化。
func NewConsumerGroup(
    cfg KafkaConsumerConfig,
    handler ConsumerHandler,
    eventTypeToProtoMap map[string]func() proto.Message,
    // ... logger, tracer, etc.
) (*ConsumerGroup, error)

// Run 启动消费者组，这是一个阻塞操作。
func (cg *ConsumerGroup) Run(ctx context.Context) error

// Close 优雅地关闭消费者组。
func (cg *ConsumerGroup) Close() error
```

---

### 6. 使用示例与最佳实践

#### 6.1. 发布事件
```go
// services/user-core-service/internal/application/service/user_service.go

// producer 是通过依赖注入传入的 pkg/messaging.Producer
func (s *userService) RegisterUser(ctx context.Context, ...) error {
    // ... 创建用户 ...

    event := &user_v1.UserRegisteredEvent{ UserId: "..." }
    
    // 从ctx中自动传播追踪信息
    err := s.producer.Publish(ctx, "user-events", event.UserId, event)
    if err != nil {
        logger.Error(ctx, err, "failed to publish UserRegisteredEvent")
        // 注意：这里通常不回滚用户创建，因为发布是异步解耦的。需要有补偿机制或后台对账。
    }

    return nil
}
```

#### 6.2. 消费事件
```go
// services/activity-feed-service/internal/adapter/event/consumer.go

// UserEventsHandler 实现了 ConsumerHandler 接口
type UserEventsHandler struct { ... }

func (h *UserEventsHandler) Handle(ctx context.Context, msg proto.Message) error {
    switch event := msg.(type) {
    case *user_v1.UserRegisteredEvent:
        // 使用带有追踪信息的ctx来调用业务逻辑
        return h.activityService.CreateWelcomeActivity(ctx, event.UserId)
    case *user_v1.UserProfileUpdatedEvent:
        // ...
    default:
        logger.Warn(ctx, "unhandled event type", "type", proto.MessageName(msg))
        return nil // 对于不关心的事件，直接确认
    }
}

// 在 main.go 中启动消费者
func main() {
    // ...
    handler := NewUserEventsHandler(...)
    
    // 注册事件类型和对应的空的Protobuf消息类型
    eventMap := map[string]func() proto.Message{
        "user.registered":   func() proto.Message { return &user_v1.UserRegisteredEvent{} },
        "user.profile.updated": func() proto.Message { return &user_v1.UserProfileUpdatedEvent{} },
    }
    
    consumer, _ := messaging.NewConsumerGroup(cfg, handler, eventMap, ...)
    go consumer.Run(context.Background())
    // ...
}
```

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   **生产者**: `Publish`方法必须是低延迟、非阻塞的。P99延迟 < 2ms。
    *   **消费者**: 消息处理的框架开销应极小。
*   **NFR7.2 (可靠性)**:
    *   **不丢消息**: 必须保证“至少一次”的交付语义。
    *   **顺序性**: 在单个分区内，必须保证消息按顺序被处理。
*   **NFR7.3 (可扩展性)**: 生产者和消费者都必须是线程安全的，并能通过增加服务实例（即增加消费者组成员）来水平扩展消费能力。
*   **NFR7.4 (可测试性)**: 提供`MockProducer`和`MockConsumerGroup`，方便业务服务在单元测试中模拟消息收发。

---

### 8. 技术约束与开发规范

*   **TC8.1 (核心依赖)**:
    *   **Kafka客户端**: `segmentio/kafka-go`。因其纯Go实现，无CGO依赖，更易于部署和管理。
    *   **Protobuf**: `google.golang.org/protobuf`。
    *   **Tracing**: OpenTelemetry的官方Kafka集成。
*   **TC8.2 (开发规范)**:
    *   **强制使用**: 所有微服务间的异步通信**必须**通过`pkg/messaging`进行。
    *   **Topic命名**: 遵循统一的命名规范，如`<source_service>.<entity>.<version>.events`。
    *   **事件Schema**: 所有事件的Protobuf定义必须位于`/core/api/proto/v1`中，并经过评审。
    *   **幂等性**: 消费者端的业务逻辑**必须**设计为幂等的，以应对Kafka可能的消息重投。

---
这份SRS为`pkg/messaging`库的设计和实现提供了坚实、可靠的指导。通过这个标准化的消息收发包，CINA.CLUB平台可以构建一个健壮、可观测且高效的事件驱动架构，为所有微服务的解耦和异步协作提供核心支持。