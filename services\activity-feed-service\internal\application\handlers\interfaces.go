/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package handlers

import (
	"context"
	"fmt"

	"cina.club/services/activity-feed-service/internal/domain"
)

// FeedHandler defines the interface for handling specific event types
type FeedHandler interface {
	Handle(ctx context.Context, event domain.Event) error
}

// Factory creates and manages feed handlers
type Factory struct {
	feedRepo        domain.FeedRepository
	unreadCountRepo domain.UnreadCountRepository
	aggregator      *domain.Aggregator
	logger          domain.Logger
	handlers        map[string]FeedHandler
}

// NewFactory creates a new handler factory
func NewFactory(
	feedRepo domain.FeedRepository,
	unreadCountRepo domain.UnreadCountRepository,
	aggregator *domain.Aggregator,
	logger domain.Logger,
) *Factory {
	factory := &Factory{
		feedRepo:        feedRepo,
		unreadCountRepo: unreadCountRepo,
		aggregator:      aggregator,
		logger:          logger,
		handlers:        make(map[string]FeedHandler),
	}

	// Initialize handlers
	factory.initializeHandlers()

	return factory
}

// initializeHandlers creates all handler instances
func (f *Factory) initializeHandlers() {
	f.handlers["interaction"] = NewInteractionHandler(
		f.feedRepo,
		f.unreadCountRepo,
		f.aggregator,
		f.logger,
	)

	f.handlers["notification"] = NewNotificationHandler(
		f.feedRepo,
		f.unreadCountRepo,
		f.aggregator,
		f.logger,
	)

	f.handlers["following"] = NewFollowingHandler(
		f.feedRepo,
		f.unreadCountRepo,
		f.aggregator,
		f.logger,
	)
}

// GetHandler returns a handler by name
func (f *Factory) GetHandler(name string) (FeedHandler, error) {
	handler, exists := f.handlers[name]
	if !exists {
		return nil, fmt.Errorf("handler not found: %s", name)
	}
	return handler, nil
}

// GetAvailableHandlers returns all available handler names
func (f *Factory) GetAvailableHandlers() []string {
	names := make([]string, 0, len(f.handlers))
	for name := range f.handlers {
		names = append(names, name)
	}
	return names
}
