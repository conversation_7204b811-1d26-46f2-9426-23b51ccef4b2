/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package grpc

import (
	"context"

	"cina.club/pkg/logger"
	"cina.club/services/activity-feed-service/internal/application"
)

// Config represents gRPC server configuration
type Config struct {
	Port int
}

// Server represents the gRPC server
type Server struct {
	config       *Config
	logger       logger.Logger
	queryService *application.QueryService
}

// NewServer creates a new gRPC server
func NewServer(config *Config, logger logger.Logger) *Server {
	return &Server{
		config: config,
		logger: logger,
	}
}

// SetQueryService sets the query service
func (s *Server) SetQueryService(queryService *application.QueryService) {
	s.queryService = queryService
}

// Start starts the gRPC server
func (s *Server) Start() error {
	s.logger.Info(context.Background(), "Starting gRPC server", "port", s.config.Port)
	// Mock implementation - would normally start actual gRPC server
	return nil
}

// Stop stops the gRPC server
func (s *Server) Stop(ctx context.Context) error {
	s.logger.Info(ctx, "Stopping gRPC server")
	// Mock implementation
	return nil
}
