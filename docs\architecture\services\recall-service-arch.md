好的，遵照您的指示。我将为您生成一份专门针对 **`recall-service` (召回服务)** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于我们刚生成的`recall-service-srs.md`，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**多路召回策略的并行执行与容错、与多种异构数据源的适配、以及作为一个高性能在线服务的技术实现细节**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `recall-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `recall-service-srs.md` (v1.0)
**核心架构**: 整洁架构 + 策略模式(Strategy Pattern) + 并行执行与聚合

## 1. 概述

`recall-service` 是推荐系统漏斗的**第一层**，扮演着“**信息渔夫**”的角色。它是一个**高性能、无状态的在线计算服务**。其架构设计的核心目标是：
1.  **低延迟**: 必须在几十毫秒内，从海量物料库中拉取数百个候选物料。
2.  **高并发**: 能够处理来自`recommendation-service`的大量并发请求。
3.  **多策略并行**: 能够同时执行多种不同的召回策略（如协同过滤、向量、热门），并对结果进行聚合。
4.  **容错性**: 任何单个召回策略的失败或超时，都不能影响整个召回请求的成功。
5.  **可扩展性**: 添加一种新的召回策略，应该是一个简单、低耦合的过程。

本架构设计通过采用**整洁架构**，并在应用层实现一个**基于策略模式的、并行的召回执行器**，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (并行召回与聚合)

```mermaid
graph TD
    subgraph "上游调用方"
        A[recommendation-service]
    end

    subgraph "RecallService"
        style RecallService fill:#e0f7fa
        B[API Layer (gRPC)<br/><em>adapter/transport</em>]
        C[RecallApplicationService<br/><em>application/service</em>]
        D{RecallStrategyFactory<br/><em>domain/strategy</em>}
        E{Recall Strategies<br/>(I2I, Vector, Hot, ...)}
        F[Downstream Adapters<br/>(Redis, ES, VectorDB, gRPC)<br/><em>adapter/source</em>]
    end

    subgraph "下游数据源"
        style "下游数据源" fill:#f3e5f5
        S1[Redis<br/>(热门榜, I2I矩阵)]
        S2[Elasticsearch<br/>(倒排索引)]
        S3[VectorDB<br/>(向量索引)]
        S4[social-service<br/>(gRPC)]
    end

    A -- "1. RequestCandidates(strategies: [s1, s2, ...])" --> B
    B -- "调用" --> C
    
    C -- "2. For each strategy, get impl from" --> D
    D -- "Returns strategy instances" --> E

    C -- "3. ✨ Execute all strategies IN PARALLEL ✨" --> E
    
    subgraph "并行执行"
        E -- "I2IStrategy calls" --> F
        F -- "RedisAdapter calls" --> S1
        
        E -- "VectorStrategy calls" --> F
        F -- "VectorDBAdapter calls" --> S3

        E -- "SocialStrategy calls" --> F
        F -- "SocialServiceAdapter calls" --> S4
    end

    E -- "4. Return results (or timeout/error) to" --> C
    
    C -- "5. Aggregate, Deduplicate & Merge results" --> C
    C -- "6. Return final candidate list" --> B
```

### 2.2 最终目录结构 (`services/recall-service/`)

```
recall-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── source/             # ✨ 下游数据源的适配器实现 ✨
│   │       ├── interface.go    # 定义SourceAdapter接口
│   │       ├── redis_source_adapter.go
│   │       ├── es_source_adapter.go
│   │       └── social_grpc_adapter.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── source.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── recall_service.go # 核心应用服务实现
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── strategy/           # ✨ 召回策略模式实现 ✨
│           ├── interface.go      # 定义RecallStrategy接口
│           ├── factory.go        # RecallStrategyFactory
│           ├── i2i_strategy.go
│           ├── vector_strategy.go
│           └── hot_strategy.go
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Recall Strategies)

*   `domain/model/`: 定义`RecalledItem`（包含ID, Score, Source）和`RecallRequest`等核心领域对象。
*   **`domain/strategy/`**: **这是本服务的核心逻辑，策略模式的实现**。
    *   `interface.go`: 定义`RecallStrategy`接口。
        ```go
        type RecallContext struct { // 包含所有策略可能需要的信息
            UserID      string
            UserVector  []float32
            SeedItemIDs []string
            // ...
        }
        type RecallStrategy interface {
            // Name 返回策略的唯一标识符, e.g., "i2i_redis"
            Name() string
            // Recall 执行召回逻辑
            Recall(ctx context.Context, context *RecallContext, limit int) ([]*model.RecalledItem, error)
        }
        ```
    *   **具体策略实现 (`i2i_strategy.go`, ...)**:
        *   每个文件实现一个具体的`RecallStrategy`。
        *   **构造函数**: `NewI2IStrategy(sourceAdapter source.I2ISource)`。**策略本身不直接与外部世界交互，而是通过注入的`SourceAdapter`接口**。
        *   **`Recall`方法**:
            1.  从`RecallContext`中获取其需要的参数（如`SeedItemIDs`）。
            2.  调用`sourceAdapter`的方法来获取数据（如`sourceAdapter.GetSimilarItems(ctx, seedIDs)`）。
            3.  将`sourceAdapter`返回的数据，转换为标准的`[]*model.RecalledItem`列表并返回。
    *   `factory.go`: `RecallStrategyFactory`在启动时被初始化，它会注册所有可用的`RecallStrategy`实现 (`map[strategyName]RecallStrategy`)。

### 3.2 `application/` - 应用层 (The Parallel Executor)

*   **`application/port/`**: 定义`RecallService`和各种`Source`接口。
*   **`application/service/recall_service.go`**: **这是并行执行和聚合的核心**。
    *   **`RecallApplicationService`**: 注入`RecallStrategyFactory`。
    *   **`RequestCandidates(ctx, request)`**:
        1.  **构建上下文**: `recallContext := buildRecallContext(request.UserID, ...)`。
        2.  **初始化并发控制器**: `g, gCtx := errgroup.WithContext(ctx)`。
        3.  创建一个带锁的map或channel来收集所有并行的结果：`results := make(chan []*model.RecalledItem, len(request.Strategies))`。
        4.  **并行调度**:
            ```go
            for _, strategyInfo := range request.Strategies {
                strategyName := strategyInfo.Name
                // 闭包捕获变量
                g.Go(func() error {
                    // a. 从工厂获取策略实例
                    strategy := s.factory.GetStrategy(strategyName)
                    if strategy == nil { return nil } // or log warning
                    
                    // b. ✨ 为每个goroutine设置独立的、更短的超时 ✨
                    strategyCtx, cancel := context.WithTimeout(gCtx, 50*time.Millisecond)
                    defer cancel()

                    // c. 执行召回
                    items, err := strategy.Recall(strategyCtx, recallContext, ...)
                    if err != nil {
                        // ✨ 容错：只记录错误，不返回error，不影响其他goroutine ✨
                        logger.Warn(strategyCtx, "recall strategy failed", "strategy", strategyName, "error", err)
                        return nil 
                    }
                    
                    // d. 将结果发送到channel
                    results <- items
                    return nil
                })
            }
            ```
        5.  **等待与聚合**:
            a. `g.Wait()` (忽略返回的error，因为我们在goroutine内部处理了)。
            b. 关闭`results` channel。
            c. **从channel中读取所有结果，并进行合并与去重**。
        6.  返回最终的候选集。

### 3.3 `adapter/` - 适配层 (The Data Source Bridge)

*   **`adapter/source/`**: **这是与所有下游数据源交互的实现**。
    *   `interface.go`: 定义`I2ISource`, `VectorSource`, `HotSource`, `SocialSource`等**细粒度**的接口。
    *   `redis_source_adapter.go`:
        *   实现了`I2ISource`和`HotSource`接口。
        *   封装了对Redis的`HGETALL`, `ZREVRANGE`等命令的调用。
    *   `es_source_adapter.go`: 实现了`KeywordSource`接口，封装了对ES的DSL查询。
    *   `social_grpc_adapter.go`: 实现了`SocialSource`接口，封装了对`social-service`的gRPC客户端调用。
*   **`adapter/grpc/handler.go`**:
    *   实现`recall-service.proto`中定义的gRPC服务。
    *   将请求转发给`application.RecallApplicationService`。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`recall-service`：
1.  **策略模式的深度应用**: 将每一种召回算法都封装成一个独立的、可插拔的`RecallStrategy`。这使得添加新的召回通路（如未来增加一个“地理位置召回”）只需要实现一个新的策略并注册即可，完全符合**开闭原则**。
2.  **严格的依赖倒置**: `domain/strategy`（策略层）依赖于`application/port/source`（数据源接口），而`adapter/source`（数据源实现）也依赖于这个接口。这使得策略逻辑与具体的数据存储技术（Redis, ES）完全解耦，极大地增强了可测试性和可维护性。
3.  **健壮的并行与容错模型**:
    *   使用`errgroup`来高效地并行执行所有召回路。
    *   为每个并行的召回通路设置了独立的、极短的超时。
    *   明确了**单路召回失败不影响整体请求**的容错原则，这是保证整个推荐系统高可用的关键。
4.  **高性能设计**: 整个服务是无状态的，并且其核心流程被设计为并行的内存计算和网络I/O，能够最大限度地利用多核CPU和网络带宽，满足极低的延迟要求。

这种架构确保了`recall-service`能够作为一个**高性能、高可用、高扩展性**的召回引擎，为推荐系统的上层排序阶段稳定地提供丰富、多样的候选物料。