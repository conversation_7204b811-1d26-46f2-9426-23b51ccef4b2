/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

# pkg/logger - 统一日志记录包

## 概述

`pkg/logger` 是 CINA.CLUB 平台的核心日志记录包，基于 Go 1.21+ 的 `log/slog` 标准库构建。它提供了统一、结构化、上下文感知的日志记录解决方案，确保所有微服务产生的日志都具有一致的格式和丰富的上下文信息。

## 设计原则

- **结构化优先**: 所有日志必须是 JSON 格式，便于机器解析和查询
- **上下文感知**: 日志自动包含来自请求上下文的追踪信息（trace_id, user_id）
- **高性能**: 基于 slog 的零分配设计，日志记录不应成为性能瓶颈
- **与错误集成**: 智能处理 `pkg/errors.AppError`，自动记录结构化错误信息
- **开发者友好**: 提供简洁的 API，鼓励使用键值对而非字符串拼接

## 核心特性

### 1. 标准化日志格式

所有日志都包含标准字段：

```json
{
  "time": "2025-01-20T12:00:00.123Z",
  "level": "INFO",
  "msg": "User logged in successfully",
  "service": {
    "name": "user-core-service",
    "version": "v1.2.3"
  },
  "trace": {
    "trace_id": "a1b2c3d4..."
  },
  "user_id": "usr_abc123",
  "custom_field": "value"
}
```

### 2. 上下文传播

通过 `context.Context` 自动传递日志上下文：

```go
// 在 gRPC 中间件中注入上下文
ctxLogger := baseLogger.With("trace_id", traceID, "user_id", userID)
newCtx := logger.ContextWithLogger(ctx, ctxLogger)

// 在业务代码中使用
logger.Info(newCtx, "operation completed", "duration_ms", 150)
```

### 3. 智能错误处理

自动处理 `pkg/errors.AppError`：

```go
appErr := errors.New(errors.InvalidArgument, "email required")
appErr = errors.WithMeta(appErr, "field", "email")

logger.Error(ctx, appErr, "validation failed")
// 输出包含 error_code, error_message, error_metadata 等结构化字段
```

## 安装和配置

### 基本配置

```go
cfg := logger.Config{
    Level:          "info",           // debug, info, warn, error
    Format:         "json",           // json, text
    AddSource:      false,            // 是否包含源文件信息
    ServiceName:    "my-service",     // 服务名称
    ServiceVersion: "v1.0.0",         // 服务版本
}

// 创建 logger
appLogger, err := logger.New(cfg)
if err != nil {
    log.Fatal("Failed to create logger:", err)
}

// 或者使用 MustNew (启动阶段推荐)
appLogger := logger.MustNew(cfg)
```

### 与配置系统集成

```go
// 使用 pkg/config 加载配置
type AppConfig struct {
    Logger logger.Config `mapstructure:"logger"`
    // ... 其他配置
}

var cfg AppConfig
// ... 加载配置 ...

appLogger := logger.MustNew(cfg.Logger)
```

## 基本使用

### 快捷函数

```go
ctx := context.Background()

// 基本日志记录
logger.Debug(ctx, "debug message", "key", "value")
logger.Info(ctx, "info message", "key", "value") 
logger.Warn(ctx, "warning message", "key", "value")
logger.ErrorMsg(ctx, "error message", "key", "value")

// 错误日志 (with error object)
err := errors.New(errors.Internal, "database error")
logger.Error(ctx, err, "operation failed", "operation", "create_user")
```

### 上下文管理

```go
// 注入 logger 到上下文
ctx = logger.ContextWithLogger(ctx, customLogger)

// 添加属性到上下文
ctx = logger.ContextWithAttrs(ctx, "request_id", "req_123", "user_id", "user_456")

// 添加分组
ctx = logger.ContextWithGroup(ctx, "database")

// 从上下文获取 logger
log := logger.FromContext(ctx)
log.Info("message from context logger")
```

### 创建带属性的 Logger

```go
// 创建带属性的新 logger
userLogger := logger.With(ctx, "user_id", "user_123", "action", "login")

// 创建分组 logger
dbLogger := logger.WithGroup(ctx, "database")
```

## 在微服务中的使用

### 服务启动 (main.go)

```go
func main() {
    // 1. 加载配置
    cfg := loadConfig()
    
    // 2. 初始化 logger
    appLogger := logger.MustNew(cfg.Logger)
    
    // 3. 设置为全局默认 (可选)
    logger.SetGlobal(appLogger)
    
    // 4. 在需要的地方传递 logger
    server := NewServer(appLogger)
    server.Start()
}
```

### gRPC 中间件

```go
func LoggingInterceptor(baseLogger *slog.Logger) grpc.UnaryServerInterceptor {
    return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
        // 提取追踪信息
        traceID := extractTraceID(ctx)
        userID := extractUserID(ctx)
        
        // 创建带上下文的 logger
        ctxLogger := baseLogger.With(
            "trace_id", traceID,
            "user_id", userID,
            "method", info.FullMethod,
        )
        
        // 注入到上下文
        newCtx := logger.ContextWithLogger(ctx, ctxLogger)
        
        // 记录请求开始
        logger.Info(newCtx, "request started")
        
        start := time.Now()
        resp, err := handler(newCtx, req)
        duration := time.Since(start)
        
        // 记录请求结果
        if err != nil {
            logger.Error(newCtx, err, "request failed", "duration_ms", duration.Milliseconds())
        } else {
            logger.Info(newCtx, "request completed", "duration_ms", duration.Milliseconds())
        }
        
        return resp, err
    }
}
```

### Repository 层

```go
func (r *userRepo) GetByID(ctx context.Context, id string) (*User, error) {
    logger.Debug(ctx, "fetching user by ID", "user_id", id)
    
    user, err := r.db.QueryRow(ctx, "SELECT * FROM users WHERE id = $1", id)
    if err != nil {
        if errors.Is(err, pgx.ErrNoRows) {
            appErr := errors.Wrap(err, errors.NotFound, "user not found")
            logger.Error(ctx, appErr, "user lookup failed", "user_id", id)
            return nil, appErr
        }
        
        appErr := errors.Wrap(err, errors.Internal, "database query failed")
        logger.Error(ctx, appErr, "database error in GetByID", "user_id", id)
        return nil, appErr
    }
    
    logger.Info(ctx, "user fetched successfully", "user_id", id)
    return user, nil
}
```

### Service 层

```go
func (s *userService) CreateUser(ctx context.Context, req *CreateUserRequest) error {
    // 添加操作上下文
    ctx = logger.ContextWithAttrs(ctx, "operation", "create_user", "email", req.Email)
    
    logger.Info(ctx, "creating new user")
    
    // 参数验证
    if req.Email == "" {
        err := errors.New(errors.InvalidArgument, "email is required")
        err = errors.WithMeta(err, "field", "email")
        logger.Error(ctx, err, "validation failed")
        return err
    }
    
    // 业务逻辑
    if err := s.repo.Create(ctx, user); err != nil {
        if errors.IsCode(err, errors.AlreadyExists) {
            logger.Warn(ctx, "user already exists")
            return err
        }
        
        logger.Error(ctx, err, "failed to create user")
        return errors.Wrap(err, errors.Internal, "user creation failed")
    }
    
    logger.Info(ctx, "user created successfully", "user_id", user.ID)
    return nil
}
```

## 最佳实践

### 1. 日志级别选择

- **Debug**: 详细的调试信息，生产环境通常关闭
- **Info**: 一般信息，重要的业务流程节点
- **Warn**: 警告信息，可能的问题但不影响功能
- **Error**: 错误信息，需要关注的问题

### 2. 结构化日志

**推荐**:
```go
logger.Info(ctx, "user operation completed", 
    "user_id", userID,
    "operation", "update_profile",
    "duration_ms", duration.Milliseconds(),
)
```

**避免**:
```go
logger.Info(ctx, fmt.Sprintf("User %s completed %s operation in %dms", 
    userID, "update_profile", duration.Milliseconds()))
```

### 3. 错误日志

**推荐**:
```go
// 使用 Error 函数处理 error 对象
logger.Error(ctx, err, "operation failed", "operation", "create_user")

// 或者使用 ErrorMsg 记录纯文本错误
logger.ErrorMsg(ctx, "configuration invalid", "config_file", filename)
```

### 4. 上下文传递

**推荐**:
```go
// 在函数签名中使用 context
func (s *service) DoSomething(ctx context.Context, params Params) error {
    logger.Info(ctx, "starting operation")
    // ...
}

// 在中间件中注入上下文
ctx = logger.ContextWithAttrs(ctx, "request_id", requestID)
```

### 5. 敏感信息处理

**避免记录敏感信息**:
```go
// 避免
logger.Info(ctx, "user login", "password", password)

// 推荐
logger.Info(ctx, "user login", "user_id", userID, "success", true)
```

## 性能考虑

### 1. 日志级别过滤

```go
// 生产环境使用 info 级别，避免 debug 日志的性能开销
cfg := logger.Config{
    Level: "info",  // 而非 "debug"
    AddSource: false,  // 生产环境关闭源文件信息
}
```

### 2. 懒加载昂贵操作

```go
// 避免在日志参数中进行昂贵操作
logger.Debug(ctx, "processing data", "data", expensiveDataSerialization())

// 推荐：先检查级别
if slog.Default().Enabled(ctx, slog.LevelDebug) {
    logger.Debug(ctx, "processing data", "data", expensiveDataSerialization())
}
```

### 3. 批量日志

对于高频日志，考虑使用采样或批量处理：

```go
// 采样示例
if rand.Intn(100) < 10 { // 10% 采样
    logger.Debug(ctx, "high frequency event", "event_id", eventID)
}
```

## 测试

### 测试 Logger

```go
func TestSomething(t *testing.T) {
    // 使用测试 logger
    testLogger := logger.NewTestLogger()
    ctx := logger.ContextWithLogger(context.Background(), testLogger)
    
    // 测试你的代码
    err := yourFunction(ctx)
    assert.NoError(t, err)
}
```

### 捕获日志输出

```go
func TestLogOutput(t *testing.T) {
    // 重定向输出进行测试
    var buf bytes.Buffer
    
    cfg := logger.Config{
        Level:          "info",
        Format:         "json",
        ServiceName:    "test",
        ServiceVersion: "v1.0.0",
    }
    
    // 这里需要自定义输出目标，在实际实现中可能需要扩展配置
    // ...
}
```

## API 参考

### 配置

- `Config` - 日志配置结构体
- `Config.Validate()` - 验证配置
- `Config.ToSlogLevel()` - 转换为 slog.Level
- `Config.IsJSONFormat()` - 检查是否为 JSON 格式

### 创建函数

- `New(cfg Config) (*slog.Logger, error)` - 创建新 logger
- `MustNew(cfg Config) *slog.Logger` - 创建 logger，失败则 panic
- `NewTestLogger() *slog.Logger` - 创建测试用 logger

### 上下文函数

- `FromContext(ctx context.Context) *slog.Logger` - 从上下文获取 logger
- `ContextWithLogger(ctx context.Context, logger *slog.Logger) context.Context` - 注入 logger 到上下文
- `ContextWithAttrs(ctx context.Context, args ...interface{}) context.Context` - 添加属性到上下文
- `ContextWithGroup(ctx context.Context, name string) context.Context` - 添加分组到上下文

### 快捷函数

- `Debug(ctx context.Context, msg string, args ...interface{})` - 调试日志
- `Info(ctx context.Context, msg string, args ...interface{})` - 信息日志
- `Warn(ctx context.Context, msg string, args ...interface{})` - 警告日志
- `Error(ctx context.Context, err error, msg string, args ...interface{})` - 错误日志（带错误对象）
- `ErrorMsg(ctx context.Context, msg string, args ...interface{})` - 错误日志（纯文本）

### 工具函数

- `With(ctx context.Context, args ...interface{}) *slog.Logger` - 创建带属性的 logger
- `WithGroup(ctx context.Context, name string) *slog.Logger` - 创建分组 logger
- `SetGlobal(logger *slog.Logger)` - 设置全局 logger
- `GetGlobal() *slog.Logger` - 获取全局 logger

## 故障排除

### 常见问题

1. **日志不输出**
   - 检查日志级别配置
   - 确认 logger 已正确初始化

2. **缺少上下文信息**
   - 确认在中间件中正确注入了上下文
   - 检查 FromContext 的使用

3. **性能问题**
   - 关闭 AddSource 选项
   - 使用合适的日志级别
   - 避免在日志参数中进行昂贵操作

### 调试技巧

```go
// 临时启用详细日志
cfg.Level = "debug"
cfg.AddSource = true

// 检查全局 logger 状态
globalLogger := logger.GetGlobal()
```

## 依赖

- Go 1.21+ (log/slog)
- pkg/errors (错误集成)

## 许可证

Copyright (c) 2025 Cina.Club, All rights reserved. 