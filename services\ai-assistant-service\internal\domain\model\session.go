/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package model

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// DialogState represents the conversation state
type DialogState struct {
	SessionID      string                 `json:"session_id"`
	UserID         string                 `json:"user_id"`
	MessageHistory []Message              `json:"message_history"`
	ExecutionState *ExecutionState        `json:"execution_state,omitempty"`
	Context        map[string]interface{} `json:"context,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	ExpiresAt      time.Time              `json:"expires_at"`
}

// Message represents a conversation message
type Message struct {
	ID        string                 `json:"id"`
	Role      MessageRole            `json:"role"`
	Content   []ContentItem          `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// MessageRole represents message roles
type MessageRole string

const (
	MessageRoleUser      MessageRole = "user"
	MessageRoleAssistant MessageRole = "assistant"
	MessageRoleSystem    MessageRole = "system"
	MessageRoleTool      MessageRole = "tool"
)

// ContentItem represents message content item
type ContentItem struct {
	Type     ContentType            `json:"type"`
	Text     string                 `json:"text,omitempty"`
	ImageURL string                 `json:"image_url,omitempty"`
	AudioURL string                 `json:"audio_url,omitempty"`
	VideoURL string                 `json:"video_url,omitempty"`
	FileURL  string                 `json:"file_url,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// ContentType represents content types
type ContentType string

const (
	ContentTypeText  ContentType = "text"
	ContentTypeImage ContentType = "image"
	ContentTypeAudio ContentType = "audio"
	ContentTypeVideo ContentType = "video"
	ContentTypeFile  ContentType = "file"
)

// ExecutionState represents workflow execution state
type ExecutionState struct {
	PlanID              string                 `json:"plan_id"`
	Plan                *WorkflowPlan          `json:"plan,omitempty"`
	CurrentStep         int                    `json:"current_step"`
	StepResults         map[string]interface{} `json:"step_results"`
	IntermediateResults map[string]interface{} `json:"intermediate_results"`
	Status              ExecutionStatus        `json:"status"`
	Error               string                 `json:"error,omitempty"`
	PausedAt            *time.Time             `json:"paused_at,omitempty"`
	CompletedAt         *time.Time             `json:"completed_at,omitempty"`
}

// ExecutionStatus represents execution status
type ExecutionStatus string

const (
	ExecutionStatusPending   ExecutionStatus = "pending"
	ExecutionStatusRunning   ExecutionStatus = "running"
	ExecutionStatusPaused    ExecutionStatus = "paused"
	ExecutionStatusCompleted ExecutionStatus = "completed"
	ExecutionStatusFailed    ExecutionStatus = "failed"
)

// WorkflowPlan represents a workflow plan
type WorkflowPlan struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Steps       []WorkflowStep         `json:"steps"`
	Variables   map[string]interface{} `json:"variables,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
}

// WorkflowStep represents a workflow step
type WorkflowStep struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	ToolID       string                 `json:"tool_id"`
	Inputs       map[string]interface{} `json:"inputs"`
	Outputs      map[string]interface{} `json:"outputs,omitempty"`
	Dependencies []string               `json:"dependencies,omitempty"`
	Condition    string                 `json:"condition,omitempty"`
	OnError      string                 `json:"on_error,omitempty"`
	Timeout      *time.Duration         `json:"timeout,omitempty"`
	Retries      int                    `json:"retries,omitempty"`
}

// NewDialogState creates a new dialog state
func NewDialogState(userID string) *DialogState {
	now := time.Now()
	return &DialogState{
		SessionID:      uuid.New().String(),
		UserID:         userID,
		MessageHistory: make([]Message, 0),
		Context:        make(map[string]interface{}),
		CreatedAt:      now,
		UpdatedAt:      now,
		ExpiresAt:      now.Add(24 * time.Hour), // Expires in 24 hours
	}
}

// AddMessage adds a message to conversation history
func (ds *DialogState) AddMessage(role MessageRole, content []ContentItem) {
	message := Message{
		ID:        uuid.New().String(),
		Role:      role,
		Content:   content,
		Timestamp: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	ds.MessageHistory = append(ds.MessageHistory, message)
	ds.UpdatedAt = time.Now()
}

// AddTextMessage adds a text message
func (ds *DialogState) AddTextMessage(role MessageRole, text string) {
	content := []ContentItem{
		{
			Type: ContentTypeText,
			Text: text,
		},
	}
	ds.AddMessage(role, content)
}

// GetLastMessages gets the last N messages
func (ds *DialogState) GetLastMessages(n int) []Message {
	if n <= 0 || len(ds.MessageHistory) == 0 {
		return []Message{}
	}

	start := len(ds.MessageHistory) - n
	if start < 0 {
		start = 0
	}

	return ds.MessageHistory[start:]
}

// SetExecutionState sets execution state
func (ds *DialogState) SetExecutionState(state *ExecutionState) {
	ds.ExecutionState = state
	ds.UpdatedAt = time.Now()
}

// UpdateExecutionStep updates execution step
func (ds *DialogState) UpdateExecutionStep(step int, result interface{}) {
	if ds.ExecutionState == nil {
		return
	}

	ds.ExecutionState.CurrentStep = step
	if ds.ExecutionState.StepResults == nil {
		ds.ExecutionState.StepResults = make(map[string]interface{})
	}

	stepID := ""
	if step < len(ds.ExecutionState.Plan.Steps) {
		stepID = ds.ExecutionState.Plan.Steps[step].ID
	}

	if stepID != "" {
		ds.ExecutionState.StepResults[stepID] = result
	}

	ds.UpdatedAt = time.Now()
}

// IsExpired checks if the session is expired
func (ds *DialogState) IsExpired() bool {
	return time.Now().After(ds.ExpiresAt)
}

// ExtendExpiry extends expiry time
func (ds *DialogState) ExtendExpiry(duration time.Duration) {
	ds.ExpiresAt = time.Now().Add(duration)
	ds.UpdatedAt = time.Now()
}

// ToJSON serializes to JSON
func (ds *DialogState) ToJSON() ([]byte, error) {
	return json.Marshal(ds)
}

// FromJSON deserializes from JSON
func (ds *DialogState) FromJSON(data []byte) error {
	return json.Unmarshal(data, ds)
}

// NewWorkflowPlan creates a new workflow plan
func NewWorkflowPlan(name, description string) *WorkflowPlan {
	return &WorkflowPlan{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		Steps:       make([]WorkflowStep, 0),
		Variables:   make(map[string]interface{}),
		CreatedAt:   time.Now(),
	}
}

// AddStep adds a workflow step
func (wp *WorkflowPlan) AddStep(step WorkflowStep) {
	if step.ID == "" {
		step.ID = uuid.New().String()
	}
	wp.Steps = append(wp.Steps, step)
}

// GetStep gets a step by ID
func (wp *WorkflowPlan) GetStep(stepID string) *WorkflowStep {
	for i, step := range wp.Steps {
		if step.ID == stepID {
			return &wp.Steps[i]
		}
	}
	return nil
}

// NewExecutionState creates a new execution state
func NewExecutionState(plan *WorkflowPlan) *ExecutionState {
	return &ExecutionState{
		PlanID:              plan.ID,
		Plan:                plan,
		CurrentStep:         0,
		StepResults:         make(map[string]interface{}),
		IntermediateResults: make(map[string]interface{}),
		Status:              ExecutionStatusPending,
	}
}

// MarkAsRunning marks as running
func (es *ExecutionState) MarkAsRunning() {
	es.Status = ExecutionStatusRunning
}

// MarkAsPaused marks as paused
func (es *ExecutionState) MarkAsPaused() {
	es.Status = ExecutionStatusPaused
	now := time.Now()
	es.PausedAt = &now
}

// MarkAsCompleted marks as completed
func (es *ExecutionState) MarkAsCompleted() {
	es.Status = ExecutionStatusCompleted
	now := time.Now()
	es.CompletedAt = &now
}

// MarkAsFailed marks as failed
func (es *ExecutionState) MarkAsFailed(err error) {
	es.Status = ExecutionStatusFailed
	if err != nil {
		es.Error = err.Error()
	}
}

// IsCompleted checks if execution is completed
func (es *ExecutionState) IsCompleted() bool {
	return es.Status == ExecutionStatusCompleted || es.Status == ExecutionStatusFailed
}

// CanResume checks if execution can be resumed
func (es *ExecutionState) CanResume() bool {
	return es.Status == ExecutionStatusPaused
}
