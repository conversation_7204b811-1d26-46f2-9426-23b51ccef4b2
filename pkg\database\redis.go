/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 11:00:00
Modified: 2025-01-21 11:00:00
*/

package database

import (
	"context"
	"crypto/tls"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel/trace"

	"cina.club/pkg/database/internal/instrument"
)

// RedisConfig defines configuration for Redis client
type RedisConfig struct {
	// Connection
	Addr     string `mapstructure:"addr" validate:"required" default:"localhost:6379"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db" validate:"gte=0" default:"0"`

	// Connection Pool
	PoolSize        int           `mapstructure:"pool_size" validate:"gte=1" default:"10"`
	PoolTimeout     time.Duration `mapstructure:"pool_timeout" default:"4s"`
	MinIdleConns    int           `mapstructure:"min_idle_conns" validate:"gte=0" default:"0"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns" validate:"gte=0" default:"0"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time" default:"30m"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime" default:"0"`

	// Timeouts
	DialTimeout  time.Duration `mapstructure:"dial_timeout" default:"5s"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout" default:"3s"`
	WriteTimeout time.Duration `mapstructure:"write_timeout" default:"3s"`

	// Retry
	MaxRetries      int           `mapstructure:"max_retries" validate:"gte=0" default:"3"`
	MinRetryBackoff time.Duration `mapstructure:"min_retry_backoff" default:"8ms"`
	MaxRetryBackoff time.Duration `mapstructure:"max_retry_backoff" default:"512ms"`

	// TLS
	TLSEnabled bool `mapstructure:"tls_enabled" default:"false"`
}

// NewRedisClient creates a new Redis client with built-in observability.
// This function automatically integrates OpenTelemetry tracing and structured logging
// for all Redis operations performed through the returned client.
//
// Parameters:
//   - ctx: Context for timeout and cancellation
//   - cfg: Redis configuration including address, auth, and connection settings
//   - logger: Structured logger for Redis operation logs
//   - tracer: OpenTelemetry tracer for distributed tracing
//
// Returns a *redis.Client that is ready to use with integrated observability,
// or an error if connection or configuration fails.
func NewRedisClient(ctx context.Context, cfg RedisConfig, logger *slog.Logger, tracer trace.Tracer) (*redis.Client, error) {
	// Build Redis options from configuration
	opts := &redis.Options{
		Addr:     cfg.Addr,
		Password: cfg.Password,
		DB:       cfg.DB,

		// Connection pool settings
		PoolSize:        cfg.PoolSize,
		PoolTimeout:     cfg.PoolTimeout,
		MinIdleConns:    cfg.MinIdleConns,
		MaxIdleConns:    cfg.MaxIdleConns,
		ConnMaxIdleTime: cfg.ConnMaxIdleTime,
		ConnMaxLifetime: cfg.ConnMaxLifetime,

		// Timeout settings
		DialTimeout:  cfg.DialTimeout,
		ReadTimeout:  cfg.ReadTimeout,
		WriteTimeout: cfg.WriteTimeout,

		// Retry settings
		MaxRetries:      cfg.MaxRetries,
		MinRetryBackoff: cfg.MinRetryBackoff,
		MaxRetryBackoff: cfg.MaxRetryBackoff,
	}

	// Configure TLS if enabled
	if cfg.TLSEnabled {
		opts.TLSConfig = &tls.Config{
			ServerName: extractServerName(cfg.Addr),
		}
		logger.Debug("Redis TLS enabled", "server_name", opts.TLSConfig.ServerName)
	}

	// Create Redis client
	client := redis.NewClient(opts)

	// Integrate observability - this is the core value of this package
	if tracer != nil && logger != nil {
		// Create and add our custom hook that implements redis.Hook
		redisHook := instrument.NewRedisHook(tracer, logger)
		client.AddHook(redisHook)
	}

	// Perform health check to ensure the connection is working
	if err := performRedisHealthCheck(ctx, client, logger); err != nil {
		client.Close()
		return nil, fmt.Errorf("redis health check failed: %w", err)
	}

	logger.Info("Redis client created successfully",
		"addr", cfg.Addr,
		"db", cfg.DB,
		"pool_size", cfg.PoolSize,
		"tls_enabled", cfg.TLSEnabled)

	return client, nil
}

// performRedisHealthCheck verifies the Redis connection is healthy
func performRedisHealthCheck(ctx context.Context, client *redis.Client, logger *slog.Logger) error {
	// Create a timeout context for the health check
	healthCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Perform a ping command
	pong, err := client.Ping(healthCtx).Result()
	if err != nil {
		logger.Error("Redis ping failed", "error", err)
		return fmt.Errorf("ping failed: %w", err)
	}

	if pong != "PONG" {
		return fmt.Errorf("unexpected ping response: got %s, expected PONG", pong)
	}

	// Test basic operations
	testKey := "health_check_test"
	testValue := "test_value"

	// Set a test value
	err = client.Set(healthCtx, testKey, testValue, time.Minute).Err()
	if err != nil {
		logger.Error("Redis SET health check failed", "error", err)
		return fmt.Errorf("SET operation failed: %w", err)
	}

	// Get the test value
	result, err := client.Get(healthCtx, testKey).Result()
	if err != nil {
		logger.Error("Redis GET health check failed", "error", err)
		return fmt.Errorf("GET operation failed: %w", err)
	}

	if result != testValue {
		return fmt.Errorf("unexpected GET result: got %s, expected %s", result, testValue)
	}

	// Clean up test key
	client.Del(healthCtx, testKey)

	logger.Debug("Redis health check passed")
	return nil
}

// RedisClient is a convenience wrapper that includes both the client and logger
// for easier dependency injection in services
type RedisClient struct {
	Client *redis.Client
	Logger *slog.Logger
}

// Close gracefully closes the Redis client
func (r *RedisClient) Close() error {
	if r.Client != nil {
		err := r.Client.Close()
		if r.Logger != nil {
			if err != nil {
				r.Logger.Error("Redis client close failed", "error", err)
			} else {
				r.Logger.Info("Redis client closed successfully")
			}
		}
		return err
	}
	return nil
}

// NewRedisClientWithWrapper creates a Redis client wrapped with additional utilities
func NewRedisClientWithWrapper(ctx context.Context, cfg RedisConfig, logger *slog.Logger, tracer trace.Tracer) (*RedisClient, error) {
	client, err := NewRedisClient(ctx, cfg, logger, tracer)
	if err != nil {
		return nil, err
	}

	return &RedisClient{
		Client: client,
		Logger: logger,
	}, nil
}

// Stats returns Redis client statistics
func (r *RedisClient) Stats() *redis.PoolStats {
	if r.Client == nil {
		return nil
	}
	return r.Client.PoolStats()
}

// LogStats logs current Redis client statistics
func (r *RedisClient) LogStats() {
	if r.Client == nil || r.Logger == nil {
		return
	}

	stats := r.Client.PoolStats()
	r.Logger.Debug("Redis client statistics",
		"total_conns", stats.TotalConns,
		"idle_conns", stats.IdleConns,
		"stale_conns", stats.StaleConns,
		"hits", stats.Hits,
		"misses", stats.Misses,
		"timeouts", stats.Timeouts)
}

// Ping performs a health check on the Redis connection
func (r *RedisClient) Ping(ctx context.Context) error {
	if r.Client == nil {
		return fmt.Errorf("redis client is nil")
	}

	pong, err := r.Client.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("ping failed: %w", err)
	}

	if pong != "PONG" {
		return fmt.Errorf("unexpected ping response: %s", pong)
	}

	return nil
}

// WithPipeline provides a helper for executing multiple commands in a pipeline
func (r *RedisClient) WithPipeline(ctx context.Context, fn func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	if r.Client == nil {
		return nil, fmt.Errorf("redis client is nil")
	}

	pipe := r.Client.Pipeline()

	if err := fn(pipe); err != nil {
		return nil, fmt.Errorf("pipeline function failed: %w", err)
	}

	cmds, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("pipeline execution failed: %w", err)
	}

	return cmds, nil
}

// WithTransaction provides a helper for executing commands in a transaction
func (r *RedisClient) WithTransaction(ctx context.Context, keys []string, fn func(*redis.Tx) error) error {
	if r.Client == nil {
		return fmt.Errorf("redis client is nil")
	}

	return r.Client.Watch(ctx, func(tx *redis.Tx) error {
		return fn(tx)
	}, keys...)
}

// extractServerName extracts server name from Redis address for TLS configuration
func extractServerName(addr string) string {
	// For addresses like "hostname:port", return just the hostname
	if colonIndex := strings.LastIndex(addr, ":"); colonIndex != -1 {
		return addr[:colonIndex]
	}
	return addr
}

// RedisClusterConfig defines configuration for Redis Cluster client
type RedisClusterConfig struct {
	Addrs    []string `mapstructure:"addrs" validate:"required,min=1"`
	Password string   `mapstructure:"password"`

	// Connection Pool
	PoolSize        int           `mapstructure:"pool_size" validate:"gte=1" default:"10"`
	PoolTimeout     time.Duration `mapstructure:"pool_timeout" default:"4s"`
	MinIdleConns    int           `mapstructure:"min_idle_conns" validate:"gte=0" default:"0"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns" validate:"gte=0" default:"0"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time" default:"30m"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime" default:"0"`

	// Timeouts
	DialTimeout  time.Duration `mapstructure:"dial_timeout" default:"5s"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout" default:"3s"`
	WriteTimeout time.Duration `mapstructure:"write_timeout" default:"3s"`

	// Cluster specific
	MaxRedirects   int  `mapstructure:"max_redirects" validate:"gte=0" default:"3"`
	ReadOnly       bool `mapstructure:"read_only" default:"false"`
	RouteByLatency bool `mapstructure:"route_by_latency" default:"false"`
	RouteRandomly  bool `mapstructure:"route_randomly" default:"false"`

	// TLS
	TLSEnabled bool `mapstructure:"tls_enabled" default:"false"`
}

// NewRedisClusterClient creates a new Redis Cluster client with built-in observability
func NewRedisClusterClient(ctx context.Context, cfg RedisClusterConfig, logger *slog.Logger, tracer trace.Tracer) (*redis.ClusterClient, error) {
	opts := &redis.ClusterOptions{
		Addrs:    cfg.Addrs,
		Password: cfg.Password,

		// Connection pool settings
		PoolSize:        cfg.PoolSize,
		PoolTimeout:     cfg.PoolTimeout,
		MinIdleConns:    cfg.MinIdleConns,
		MaxIdleConns:    cfg.MaxIdleConns,
		ConnMaxIdleTime: cfg.ConnMaxIdleTime,
		ConnMaxLifetime: cfg.ConnMaxLifetime,

		// Timeout settings
		DialTimeout:  cfg.DialTimeout,
		ReadTimeout:  cfg.ReadTimeout,
		WriteTimeout: cfg.WriteTimeout,

		// Cluster specific settings
		MaxRedirects:   cfg.MaxRedirects,
		ReadOnly:       cfg.ReadOnly,
		RouteByLatency: cfg.RouteByLatency,
		RouteRandomly:  cfg.RouteRandomly,
	}

	// Configure TLS if enabled
	if cfg.TLSEnabled {
		opts.TLSConfig = &tls.Config{}
		logger.Debug("Redis Cluster TLS enabled")
	}

	// Create Redis cluster client
	client := redis.NewClusterClient(opts)

	// Integrate observability
	if tracer != nil && logger != nil {
		redisHook := instrument.NewRedisHook(tracer, logger)
		client.AddHook(redisHook)
	}

	// Perform health check
	healthCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	err := client.ForEachShard(healthCtx, func(ctx context.Context, shard *redis.Client) error {
		return shard.Ping(ctx).Err()
	})
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("redis cluster health check failed: %w", err)
	}

	logger.Info("Redis Cluster client created successfully",
		"addrs", cfg.Addrs,
		"pool_size", cfg.PoolSize,
		"tls_enabled", cfg.TLSEnabled)

	return client, nil
}
