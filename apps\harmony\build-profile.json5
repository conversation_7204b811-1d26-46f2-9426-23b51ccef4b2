/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "build/default.p7b",
          "storePassword": "0000001B",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B",
          "profile": "build/default.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "build/default.p12"
        }
      }
    ],
    "compileSdkVersion": "5.0.0(12)",
    "compatibleSdkVersion": "5.0.0(12)",
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compileSdkVersion": "5.0.0(12)",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS"
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "core",
      "srcPath": "./core",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "feature_auth",
      "srcPath": "./feature/auth",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "feature_chat",
      "srcPath": "./feature/chat",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "feature_pkb",
      "srcPath": "./feature/pkb",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
} 