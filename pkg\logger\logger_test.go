/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package logger

import (
	"bytes"
	"context"
	"encoding/json"
	"log/slog"
	"os"
	"strings"
	"testing"

	"cina.club/pkg/errors"
)

func TestConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: Config{
				Level:          "info",
				Format:         "json",
				AddSource:      false,
				ServiceName:    "test-service",
				ServiceVersion: "v1.0.0",
			},
			wantErr: false,
		},
		{
			name: "missing service name",
			config: Config{
				Level:          "info",
				Format:         "json",
				ServiceVersion: "v1.0.0",
			},
			wantErr: true,
		},
		{
			name: "missing service version",
			config: Config{
				Level:       "info",
				Format:      "json",
				ServiceName: "test-service",
			},
			wantErr: true,
		},
		{
			name: "invalid log level",
			config: Config{
				Level:          "invalid",
				Format:         "json",
				ServiceName:    "test-service",
				ServiceVersion: "v1.0.0",
			},
			wantErr: true,
		},
		{
			name: "invalid format",
			config: Config{
				Level:          "info",
				Format:         "invalid",
				ServiceName:    "test-service",
				ServiceVersion: "v1.0.0",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Config.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestConfig_ToSlogLevel(t *testing.T) {
	tests := []struct {
		level string
		want  slog.Level
	}{
		{"debug", slog.LevelDebug},
		{"info", slog.LevelInfo},
		{"warn", slog.LevelWarn},
		{"error", slog.LevelError},
		{"invalid", slog.LevelInfo}, // 默认值
	}

	for _, tt := range tests {
		t.Run(tt.level, func(t *testing.T) {
			cfg := &Config{Level: tt.level}
			if got := cfg.ToSlogLevel(); got != tt.want {
				t.Errorf("Config.ToSlogLevel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConfig_IsJSONFormat(t *testing.T) {
	tests := []struct {
		format string
		want   bool
	}{
		{"json", true},
		{"JSON", true},
		{"text", false},
		{"TEXT", false},
		{"invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.format, func(t *testing.T) {
			cfg := &Config{Format: tt.format}
			if got := cfg.IsJSONFormat(); got != tt.want {
				t.Errorf("Config.IsJSONFormat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNew(t *testing.T) {
	// 保存原始的stdout
	originalStdout := os.Stdout
	defer func() {
		os.Stdout = originalStdout
	}()

	// 创建管道来捕获输出
	r, w, _ := os.Pipe()
	os.Stdout = w

	cfg := Config{
		Level:          "info",
		Format:         "json",
		AddSource:      false,
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}

	logger, err := New(cfg)
	if err != nil {
		t.Fatalf("New() error = %v", err)
	}

	if logger == nil {
		t.Fatal("New() returned nil logger")
	}

	// 测试日志输出
	logger.Info("test message", "key", "value")

	// 关闭写入端并读取输出
	w.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(r)

	// 验证输出是有效的JSON
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Errorf("Log output is not valid JSON: %v\nOutput: %s", err, buf.String())
	}

	// 验证包含服务信息
	if service, ok := logEntry["service"].(map[string]interface{}); ok {
		if service["name"] != "test-service" {
			t.Errorf("Expected service name 'test-service', got %v", service["name"])
		}
		if service["version"] != "v1.0.0" {
			t.Errorf("Expected service version 'v1.0.0', got %v", service["version"])
		}
	} else {
		t.Error("Log entry missing service information")
	}
}

func TestMustNew(t *testing.T) {
	// 测试正常情况
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}

	defer func() {
		if r := recover(); r != nil {
			t.Errorf("MustNew() panic = %v", r)
		}
	}()

	logger := MustNew(cfg)
	if logger == nil {
		t.Fatal("MustNew() returned nil logger")
	}
}

func TestMustNew_Panic(t *testing.T) {
	// 测试panic情况
	cfg := Config{
		Level:  "info",
		Format: "json",
		// 缺少必需字段
	}

	defer func() {
		if r := recover(); r == nil {
			t.Error("MustNew() should panic with invalid config")
		}
	}()

	MustNew(cfg)
}

func TestNewTestLogger(t *testing.T) {
	logger := NewTestLogger()
	if logger == nil {
		t.Fatal("NewTestLogger() returned nil")
	}

	// 测试logger可以正常工作
	logger.Debug("test debug message")
	logger.Info("test info message")
}

func TestFromContext(t *testing.T) {
	// 初始化一个基础logger
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}
	_, _ = New(cfg)

	t.Run("nil context", func(t *testing.T) {
		logger := FromContext(context.TODO())
		if logger == nil {
			t.Error("FromContext with empty context should return global logger, not nil")
		}
	})

	t.Run("context without logger", func(t *testing.T) {
		ctx := context.Background()
		logger := FromContext(ctx)
		if logger == nil {
			t.Error("FromContext() should return global logger when context has no logger")
		}
	})

	t.Run("context with logger", func(t *testing.T) {
		testLogger := NewTestLogger()
		ctx := ContextWithLogger(context.Background(), testLogger)
		logger := FromContext(ctx)
		if logger != testLogger {
			t.Error("FromContext() should return the logger stored in context")
		}
	})
}

func TestContextWithLogger(t *testing.T) {
	testLogger := NewTestLogger()

	t.Run("nil context", func(t *testing.T) {
		ctx := ContextWithLogger(context.TODO(), testLogger)
		if ctx == nil {
			t.Error("ContextWithLogger with empty context should return valid context")
		}

		retrievedLogger := FromContext(ctx)
		if retrievedLogger != testLogger {
			t.Error("Logger not properly stored in context")
		}
	})

	t.Run("valid context", func(t *testing.T) {
		ctx := ContextWithLogger(context.Background(), testLogger)
		retrievedLogger := FromContext(ctx)
		if retrievedLogger != testLogger {
			t.Error("Logger not properly stored in context")
		}
	})
}

func TestContextWithAttrs(t *testing.T) {
	// 初始化logger
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}
	_, _ = New(cfg)

	ctx := context.Background()
	newCtx := ContextWithAttrs(ctx, "key1", "value1", "key2", "value2")

	logger := FromContext(newCtx)
	if logger == nil {
		t.Fatal("ContextWithAttrs() returned context with nil logger")
	}

	// 实际测试需要捕获日志输出来验证属性是否正确添加
	// 这里简化为确保logger不为nil
}

func TestShortcutFunctions(t *testing.T) {
	// 重定向stdout来捕获日志输出
	originalStdout := os.Stdout
	defer func() {
		os.Stdout = originalStdout
	}()

	r, w, _ := os.Pipe()
	os.Stdout = w

	// 初始化logger
	cfg := Config{
		Level:          "debug",
		Format:         "json",
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}
	_, _ = New(cfg)

	ctx := context.Background()

	// 测试所有快捷函数
	Debug(ctx, "debug message", "key", "value")
	Info(ctx, "info message", "key", "value")
	Warn(ctx, "warn message", "key", "value")
	ErrorMsg(ctx, "error message", "key", "value")

	// 关闭写入端
	w.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(r)

	output := buf.String()

	// 验证所有级别的日志都被输出
	if !strings.Contains(output, "debug message") {
		t.Error("Debug log not found in output")
	}
	if !strings.Contains(output, "info message") {
		t.Error("Info log not found in output")
	}
	if !strings.Contains(output, "warn message") {
		t.Error("Warn log not found in output")
	}
	if !strings.Contains(output, "error message") {
		t.Error("Error log not found in output")
	}
}

func TestErrorFunction(t *testing.T) {
	// 重定向stdout
	originalStdout := os.Stdout
	defer func() {
		os.Stdout = originalStdout
	}()

	r, w, _ := os.Pipe()
	os.Stdout = w

	// 初始化logger
	cfg := Config{
		Level:          "error",
		Format:         "json",
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}
	_, _ = New(cfg)

	ctx := context.Background()

	// 创建一个AppError来测试错误处理
	appErr := errors.New(errors.InvalidArgument, "test error")
	appErr = errors.WithMeta(appErr, "field", "email").(*errors.AppError)

	Error(ctx, appErr, "operation failed", "operation", "create_user")

	// 关闭写入端并读取输出
	w.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(r)

	output := buf.String()

	// 验证包含错误信息
	if !strings.Contains(output, "operation failed") {
		t.Error("Error message not found in output")
	}
	if !strings.Contains(output, "InvalidArgument") {
		t.Error("Error code not found in output")
	}
}

func TestWith(t *testing.T) {
	// 初始化logger
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}
	_, _ = New(cfg)

	ctx := context.Background()
	logger := With(ctx, "key1", "value1", "key2", "value2")

	if logger == nil {
		t.Fatal("With() returned nil logger")
	}
}

func TestWithGroup(t *testing.T) {
	// 初始化logger
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "test-service",
		ServiceVersion: "v1.0.0",
	}
	_, _ = New(cfg)

	ctx := context.Background()
	logger := WithGroup(ctx, "test_group")

	if logger == nil {
		t.Fatal("WithGroup() returned nil logger")
	}
}

func TestSetGlobal_GetGlobal(t *testing.T) {
	testLogger := NewTestLogger()

	SetGlobal(testLogger)

	retrievedLogger := GetGlobal()
	if retrievedLogger != testLogger {
		t.Error("SetGlobal/GetGlobal did not work correctly")
	}
}

// 基准测试
func BenchmarkNew(b *testing.B) {
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "bench-service",
		ServiceVersion: "v1.0.0",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = New(cfg)
	}
}

func BenchmarkFromContext(b *testing.B) {
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "bench-service",
		ServiceVersion: "v1.0.0",
	}
	logger, _ := New(cfg)
	ctx := ContextWithLogger(context.Background(), logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = FromContext(ctx)
	}
}

func BenchmarkInfo(b *testing.B) {
	cfg := Config{
		Level:          "info",
		Format:         "json",
		ServiceName:    "bench-service",
		ServiceVersion: "v1.0.0",
	}
	_, _ = New(cfg)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Info(ctx, "benchmark message", "key", "value")
	}
}
