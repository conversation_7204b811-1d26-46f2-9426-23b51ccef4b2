﻿好的，遵照您的指示，我们来生成一份为 `cina-coin-ledger-service` (灵境币账本服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **双账户记账法 (Double-Entry Bookkeeping)**: 引入更严谨的会计原则，将简单的余额增减，升级为基于“科目(Account)”和“分录(Entry)”的双式记账模型。这极大地提升了系统的可审计性和准确性。
2.  **事务类型与科目表**: 详细定义核心的事务类型和会计科目表，为平台所有经济活动提供标准化的记账依据。
3.  **并发控制与锁机制**: 明确在高并发场景下，如何使用数据库的悲观锁（`SELECT ... FOR UPDATE`）来保证账户余额更新的原子性和正确性。
4.  **对账与审计**: 增加对后台自动对账和手动调账流程的详细描述，这是生产级金融系统的必备功能。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行重构，以支持双式记账。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和安全指标。

这份文档将描绘一个功能强大、逻辑严谨、安全可靠，且符合金融级别要求的虚拟货币账本系统。

---

### CINA.CLUB - cina-coin-ledger-service 需求规格说明书

**版本: 2.0 (生产级定义，基于双式记账法)**  
**发布日期: 2025-06-21**  
**最后修订日期: 2025-06-21**  
**文档负责人:** [金融/平台经济团队负责人/架构师名称]  
**审批人:** [CTO/CFO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心记账原则与流程](#3-核心记账原则与流程)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
`cina-coin-ledger-service` 是CINA.CLUB平台内部经济系统的核心基础。它旨在提供一个**绝对安全、原子精确、高并发、且完全可审计**的“灵境币”账本管理服务。本服务采用**双式记账法 (Double-Entry Bookkeeping)** 原则，确保每一笔价值流动都有据可查，并保持整个账本系统的平衡，为平台的虚拟商品交易、服务付费、内容激励和商业化运营提供坚实可信的金融基础设施。

#### 1.2. 服务范围
本服务 **负责**:
*   **会计科目(Account)管理**: 管理平台预定义的会计科目表。
*   **用户资金账户管理**: 为每个用户创建和管理其资金账户（资产类科目）。
*   **原子记账操作**: 提供内部API，以**事务(Transaction)**的形式执行记账操作。每个事务包含多条**分录(Entry)**，并保证借贷必相等。
*   **余额冻结/解TODo**: 支持在业务流程中（如担保交易）临时冻结和解冻用户部分余额。
*   **交易流水记录**: 为每一笔事务和分录创建详细、不可篡改的记录。
*   **余额与流水查询**: 提供API供用户和内部服务查询账户余额和交易历史。
*   **幂等性保证**: 确保所有记账操作接口具有幂等性。
*   **对账与调账**: 提供后台对账能力和严格审计下的手动调账功能。

本服务 **不负责**:
*   处理法定货币支付 (由 `payment-service` 负责)。
*   复杂的计费规则与订阅逻辑 (由 `billing-service` 负责)。
*   发放奖励的规则引擎 (由 `gamification-service` 负责)。

#### 1.3. 目标用户/调用方
*   **`billing-service`, `service-offering-service`等业务服务**: (主要) 调用本服务的API执行业务相关的记账。
*   **CINA.CLUB客户端应用**: (间接) 通过API查询自身余额和交易流水。
*   **CINA.CLUB财务/运营团队**: 通过管理后台进行对账、调账和审计。

#### 1.4. 定义与缩略语
*   **双式记账法**: 每笔交易至少涉及两个账户，且所有借方金额总和必须等于所有贷方金额总和。
*   **科目 (Account)**: 会计要素的具体类别，如“用户资产-灵境币”、“平台收入-服务费”。
*   **事务 (Transaction)**: 一次完整的业务操作，包含一组（两条或以上）相关的记账分录。
*   **分录 (Entry)**: 一条记账记录，指明某个科目发生了借记(Debit)或贷记(Credit)。
*   **借方 (Debit) / 贷方 (Credit)**: 记账方向。对于资产类账户，借方表示增加，贷方表示减少。对于负债/权益/收入类账户，则相反。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`cina-coin-ledger-service` 是CINA.CLUB平台经济体系的“**中央银行**”和“**总账本**”。它不关心具体的业务场景，只负责忠实、准确地记录每一次由业务服务发起的价值转移。通过强制实施双式记账法，它确保了平台经济的内部一致性和可审计性，是所有涉及虚拟货币功能的信任基石。

#### 2.2. 主要功能概述
*   基于双式记账法的原子记账引擎。
*   预定义的、可扩展的会计科目体系。
*   支持资金冻结/解冻的担保交易模式。
*   严格的幂等性控制和并发处理。
*   完善的对账和审计能力。

### 3. 核心记账原则与流程

#### 3.1. 会计科目表示例 (Account Chart)
| Code | Name                               | Type    | Normal Balance | Description                            |
| :--- | :--------------------------------- | :------ | :------------- | :------------------------------------- |
| 1001 | 用户资产 - 灵境币 (User Assets)    | ASSET   | DEBIT          | 存储所有用户的可用灵境币总额           |
| 1002 | 用户冻结资产 (User Frozen Assets)  | ASSET   | DEBIT          | 存储用户被冻结的灵境币                 |
| 2001 | 平台应付 - 知识分成 (Royalty Payable) | LIABILITY| CREDIT         | 待支付给内容贡献者的分成               |
| 4001 | 平台收入 - 服务费 (Service Fee Revenue) | REVENUE | CREDIT         | 平台从交易中收取的手续费收入           |
| 4002 | 平台收入 - 灵境币销售 (Coin Sale Revenue)| REVENUE | CREDIT         | 用户通过法币购买灵境币产生的收入       |
| 5001 | 平台支出 - 奖励 (Reward Expense)   | EXPENSE | DEBIT          | 平台通过活动奖励给用户的灵境币成本     |

#### 3.2. 核心流程：用户购买服务
**场景**: 用户A (U-A) 向用户B (U-B) 购买一项价值100灵境币的服务，平台抽成10%。

**记账流程**:
1.  **[冻结]**: 用户A下单时，`billing-service`调用本服务`freeze`接口。
    *   **Transaction 1**:
        *   `DEBIT`: `User Frozen Assets: U-A` +100 (用户A的冻结资产增加)
        *   `CREDIT`: `User Assets: U-A` -100 (用户A的可用资产减少)
2.  **[结算]**: 服务完成后，`billing-service`调用本服务`commit`接口。
    *   **Transaction 2**:
        *   `DEBIT`: `User Assets: U-B` +90 (用户B的可用资产增加)
        *   `DEBIT`: `Service Fee Revenue` +10 (平台服务费收入增加 - *此处科目为收入，实际记账为贷方*)
        *   `CREDIT`: `User Frozen Assets: U-A` -100 (用户A的冻结资产减少)

**注意**: 在实际实现中，`User Assets: U-A`这样的科目在数据库中会是具体的`accounts`表的一行，其`owner_id`为用户A的ID。

### 4. 功能需求 (Functional Requirements)

#### 4.1. 账户与科目管理
*   **FR4.1.1 (科目表)**: 系统必须内置一个不可变的、核心的会计科目表。支持未来通过受控的管理接口增加新科目。
*   **FR4.1.2 (用户账户)**: 系统应在用户首次进行相关操作时，为其自动创建对应的资金账户记录，并与`user_id`关联。

#### 4.2. 核心记账API
*   **FR4.2.1 (创建事务)**: 系统必须提供一个统一的、内部的`POST /transactions`接口，用于执行所有记账操作。
*   **FR4.2.2 (事务原子性)**: 该接口接收一个事务请求对象，其中包含一个唯一的幂等键和一组分录(entries)。**整个事务的处理必须在单个数据库事务中完成，所有分录要么全部成功，要么全部失败。**
*   **FR4.2.3 (借贷平衡校验)**: 在提交数据库事务前，系统必须校验该笔事务的所有分录的借方总额是否严格等于贷方总额。不等则拒绝操作。
*   **FR4.2.4 (幂等性)**: 接口必须通过请求头中的`X-Idempotency-Key`实现幂等性。如果一个幂等键已被成功处理，则直接返回上次的结果。

#### 4.3. 余额冻结/解冻工作流
*   **FR4.3.1 (冻结)**: 冻结操作是一个特殊的、标准化的事务，即将资金从用户的“可用资产”科目转移到“冻结资产”科目。
*   **FR4.3.2 (解冻并提交)**: 结算操作是另一个事务，将资金从“冻结资产”科目，转移到收款方（或其他相关方）的“可用资产”科目和平台的“收入”科目。
*   **FR4.3.3 (解冻并返还)**: 取消操作是将资金从“冻结资产”科目返还到“可用资产”科目。
*   **关联ID**: 所有冻结/解冻相关的事务必须能关联到一个唯一的业务操作ID（如`booking_id`），以便追踪。

#### 4.4. 查询与对账
*   **FR4.4.1 (余额查询)**: 提供API供用户查询自己账户的可用余额和冻结余额。
*   **FR4.4.2 (流水查询)**: 提供API供用户或管理员分页查询交易流水。
*   **FR4.4.3 (对账)**: 系统必须有一个后台任务，定期（如每日）进行内部对账：
    *   **校验1**: 检查所有分录的借贷总额是否为0。
    *   **校验2**: 检查每个账户的当前余额是否等于其初始余额加上所有相关分录的变动总和。
    *   任何对账失败都必须触发最高优先级的告警。

#### 4.5. 管理员功能
*   **FR4.5.1 (调账)**: 提供一个严格受控且有完整审计日志的API，允许授权管理员进行手动调账（用于冲正、补偿等）。调账本身也必须是以一笔借贷平衡的事务来完成。
*   **FR4.5.2 (审计查询)**: 提供强大的后台查询接口，供财务和审计人员追踪任何一笔资金的来龙去脉。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC/RESTful API接口
*   **版本**: `/api/v1/ledger`
*   **认证**: 严格的S2S认证。
*   **核心端点**:
    *   `POST /transactions`: **统一记账接口**。
        *   **Request Header**: `X-Idempotency-Key: <unique_key>`
        *   **Request Body**: `CreateTransactionRequest { reference_id, transaction_type, entries: [{account_code, owner_id, direction, amount}] }`
        *   **Response**: `TransactionResult { transaction_id, status }`
    *   `GET /accounts/{userId}`: 查询用户余额。
    *   `GET /transactions?userId=...&referenceId=...`: 查询交易流水。
*   **便捷接口 (封装了`POST /transactions`)**:
    *   `POST /simple/credit`: (内部调用`POST /transactions`)
    *   `POST /simple/debit`: (内部调用`POST /transactions`)

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`accounts` (科目表)**: `code (PK)`, `name`, `type` (ASSET, LIABILITY, ...), `normal_balance`. (此表相对静态)
*   **`ledgers` (用户/实体账户表)**:
    *   `id (PK)`
    *   `owner_id (VARCHAR, e.g., user_id)`
    *   `account_code (FK to accounts)`
    *   `balance (NUMERIC(19, 4))`
    *   `version (BIGINT for optimistic locking)`
    *   UNIQUE on (`owner_id`, `account_code`)
*   **`transactions` (事务表)**: `id (PK)`, `idempotency_key (UNIQUE)`, `reference_id`, `type`, `status`, `timestamp`.
*   **`transaction_entries` (分录表)**:
    *   `id (PK)`
    *   `transaction_id (FK)`
    *   `ledger_id (FK)`
    *   `direction` ('DEBIT' or 'CREDIT')
    *   `amount (NUMERIC(19, 4))`

#### 6.2. 并发控制
*   在更新`ledgers`表中的`balance`时，必须使用数据库的**悲观行级锁 (`SELECT ... FOR UPDATE`)** 来锁定将要被修改的账户行，直到整个数据库事务提交，以防止高并发下的数据竞争和余额不一致。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **核心记账API (`POST /transactions`)**: P99延迟应 `< 80ms`。
*   **吞吐量**: 系统应能处理高并发的记账请求（目标TPS > 5000）。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.99%。账本服务是平台的金融命脉，可用性要求最高。
*   **数据准确性与不丢失**: 必须保证 RPO=0。所有数据必须有实时复制和定期备份，并有经过演练的恢复计划。

#### 7.3. 可扩展性需求
*   API服务可水平扩展。
*   数据库是主要扩展点，`transaction_entries`表会非常庞大，必须从设计初期就考虑基于`timestamp`或`transaction_id`的**分区(Partitioning)**策略。

#### 7.4. 安全性与可审计性
*   **安全**:
    *   防止未授权访问: 严格的S2S认证。
    *   防止数据篡改: 数据库访问控制、不可变交易流水、并发锁。
    *   防止重放攻击: 严格的幂等性实现。
*   **可审计性 (最高优先级)**:
    *   所有记账操作必须留下**不可篡改**的、可通过事务ID关联的事务和分录记录。
    *   任何手动调账都必须有独立的、详细的审计日志，记录操作人、原因、时间和审批流程。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: **PostgreSQL**。其强大的ACID事务、`NUMERIC`类型、以及对行级锁的良好支持，是实现可靠账本系统的基石。
*   **并发模型**: 严格使用数据库事务和`SELECT ... FOR UPDATE`来管理并发。
*   **API设计**: 暴露统一的、基于事务的记账接口，是保证系统内聚性和逻辑严谨性的关键。

---
这份版本2.0的SRS文档为`cina-coin-ledger-service`构建了一个金融级别的、基于双式记账法的虚拟货币账本系统。它通过严谨的设计，确保了平台内部经济系统的准确、安全和可审计，为所有上层商业化功能提供了坚实的基础。