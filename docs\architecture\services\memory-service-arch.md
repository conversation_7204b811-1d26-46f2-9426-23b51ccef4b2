好的，遵照您的指示。我将为您生成一份专门针对 **`memory-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`memory-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**应用层加密(ALE)的实现、模拟人脑记忆的生命周期与权重模型、为RAG优化的加权混合搜索，以及与`ai-assistant-service`和加密服务的深度安全协同**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `memory-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `memory-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + RAG检索模型 + 应用层加密(ALE)

## 1. 概述

`memory-service` 是CINA.CLUB平台实现深度个性化AI体验的“**长期记忆海马体**”。它为AI助手提供一个安全的、可智能检索的长期记忆库。其核心挑战在于：
1.  **绝对的隐私与安全**: 记忆内容是用户最私密的数据之一，必须采用**服务器端零知识**的原则进行存储，即通过应用层加密(ALE)，使服务本身无法解密数据。
2.  **智能与仿生检索**: 简单的关键词或向量搜索不足以模拟人类记忆。需要一个能结合**新近度(Recency)、重要性(Importance)和相关性(Relevance)**的加权检索模型。
3.  **动态的记忆生命周期**: 记忆应该有“权重”和“衰减”，重要的记忆更容易被想起，不重要的、久远的记忆会“遗忘”，以保持记忆库的高效和相关性。
4.  **与AI和加密服务的无缝集成**: 需要与`ai-assistant-service` (决策者)、`embedding-service` (语义化) 和 `key-management-proxy-service` (密钥提供者) 进行高频、安全、可靠的协同。

本架构设计通过采用**整洁架构**，并在领域层实现一个**仿生记忆检索模型**，同时在应用层严格执行**应用层加密**流程，来应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (RAG检索流程)

```mermaid
graph TD
    subgraph "调用方 (AI Assistant)"
        style "调用方 (AI Assistant)" fill:#eee
        AIAssistant[ai-assistant-service]
    end

    subgraph "MemoryService"
        style MemoryService fill:#e0f7fa
        A[API Layer (gRPC)<br/><em>adapter/transport</em>]
        B[MemoryQueryService<br/><em>application/query</em>]
        C[RetrievalModel<br/><em>domain/service</em>]
        D[Repository<br/><em>adapter/repository</em>]
        E[VectorDB Client<br/><em>adapter/client</em>]
        F[KMSProxy Client<br/><em>adapter/client</em>]
    end

    subgraph "下游依赖"
        style "下游依赖" fill:#f3e5f5
        VectorDB[(Vector Database)]
        KMSProxy[key-management-proxy-service]
        PostgreSQL[(PostgreSQL)]
    end
    
    AIAssistant -- "1. SearchMemories(queryVector, weights)" --> A
    A -- "调用" --> B
    B -- "2. Retrieve relevant items" --> C
    
    C -- "2a. Semantic search" --> E
    E --> VectorDB
    VectorDB -- "Candidate IDs" --> E
    E --> C
    
    C -- "2b. Get full metadata for candidates" --> D
    D --> PostgreSQL
    PostgreSQL -- "Metadata (importance, recency)" --> D
    D --> C
    
    C -- "3. Re-rank with weights" --> C
    C -- "4. Top N memories" --> B
    
    B -- "5. Decrypt content" --> F
    F -- "DecryptData RPC" --> KMSProxy
    KMSProxy -- "Decrypted content" --> F
    F --> B
    
    B -- "6. Return results" --> A
    A --> AIAssistant
```

### 2.2 最终目录结构 (`services/memory-service/`)

```
memory-service/
├── cmd/server/
│   └── main.go
├── cmd/worker/
│   └── main.go                 # ✨ 记忆衰减等后台任务的独立入口 ✨
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── kms_proxy_client.go
│   │   │   └── embedding_client.go
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go        # 数据库实体模型
│   │       ├── postgres_repo.go
│   │       └── vectordb_repo.go # VectorDB仓储实现
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   ├── command/
│   │   │   └── memory_command_service.go # 处理创建、更新记忆
│   │   └── query/
│   │       └── memory_query_service.go   # ✨ 处理高级检索 ✨
│   └── domain/
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── retrieval_model.go # ✨ R-I-R加权检索模型服务 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Memory Model)

*   `domain/model/`: 定义`PersonalMemory`核心领域对象，包含`Content` (明文), `ImportanceScore`, `LastAccessedAt`, `RecencyScore`等。
*   **`domain/service/retrieval_model.go`**: **这是仿生检索的核心**。
    *   **`RetrievalModel`**: 一个无状态的领域服务。
    *   **`CalculateScores(memories, relevanceScores)`**:
        *   接收一批从VectorDB召回的记忆及其相关性分数。
        *   **计算新近度分(`RecencyScore`)**: 对每个记忆，根据其`LastAccessedAt`和当前时间，使用一个**指数衰减函数**来计算新近度分。`RecencyScore = exp(-decay_rate * time_elapsed)`。
        *   **规范化**: 将`ImportanceScore`和计算出的`RecencyScore`进行规范化（如min-max scaling到0-1范围）。
    *   **`ReRank(scoredMemories, weights)`**:
        *   接收带有三种分数的记忆列表和用户请求的权重。
        *   **计算最终得分**: `FinalScore = weights.relevance * relevance_score + weights.importance * importance_score + weights.recency * recency_score`。
        *   根据`FinalScore`对列表进行排序。

### 3.2 `application/` - 应用层 (The Secure Operations)

采用CQRS思想，分离读写路径。

*   **`application/command/memory_command_service.go`**: **处理所有写操作，并强制执行加密**。
    *   **`CreateMemory(ctx, memoryData)`**:
        1.  **获取明文内容**: `memoryData.Content`。
        2.  **调用`embedding-service`**: 将明文内容发送去生成嵌入向量。
        3.  **调用`kms-proxy-service`**: 调用`kmsProxyClient.EncryptData(userID, memoryData.Content)`，获取**加密后的内容**。
        4.  **开启数据库事务**:
            a. 调用`vectordb_repo.Upsert(vector)`，将向量存入向量数据库。
            b. 调用`postgres_repo.CreateMemory(...)`，将**加密后的内容**和其他元数据（`importance_score`等）存入PostgreSQL。
        5.  **提交事务**。
*   **`application/query/memory_query_service.go`**: **处理所有读操作，并执行RAG流程**。
    *   **`SearchMemories(ctx, userID, queryVector, weights)`**:
        1.  **步骤1 (召回 - Relevance)**: 调用`vectordb_repo.Search(queryVector)`，从向量数据库中召回一批候选记忆的ID及其相关性分数。
        2.  **步骤2 (获取元数据)**: 调用`postgres_repo.GetMemoryMetadataBatch(ids)`，批量从PostgreSQL中获取这些候选记忆的元数据（特别是`importance_score`和`last_accessed_at`）。
        3.  **步骤3 (评分与重排)**:
            a. 调用`domain.RetrievalModel.CalculateScores()`计算新近度分。
            b. 调用`domain.RetrievalModel.ReRank()`计算最终得分并排序。
        4.  **步骤4 (获取并解密内容)**:
            a. 取Top-N的记忆ID，并从`postgres_repo`获取其**加密内容**。
            b. **并行地**调用`kmsProxyClient.DecryptData(userID, encryptedContent)`为每个记忆解密。
        5.  **步骤5 (更新访问时间)**: **异步地**（如通过发布一个事件）调用`memory_command_service.UpdateAccessTimes(ids)`，来更新被成功检索的记忆的`last_accessed_at`时间戳，以实现“回顾增强”。
        6.  返回解密后的Top-N记忆列表。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   `postgres_repo.go`: 实现与PostgreSQL的交互，存储和查询记忆的元数据和**加密内容**。
    *   `vectordb_repo.go`: 封装对**向量数据库 (Pinecone/Weaviate/Milvus)**的客户端调用。负责向量的插入和k-NN相似性搜索。
*   **`adapter/client/`**:
    *   `kms_proxy_client.go`: **安全核心**。封装对`key-management-proxy-service`的gRPC调用。所有加解密操作都必须通过此客户端。
    *   `embedding_client.go`: 封装对`embedding-service`的调用。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现`memory-service.proto`中定义的gRPC服务。

### 3.4 `cmd/worker/` - 后台任务

*   **`main.go`**: 一个独立的、可部署的Go程序。
*   **职责**:
    *   **记忆衰减任务 (Memory Decay Job)**:
        *   以**Kubernetes CronJob**的形式每日运行。
        *   **工作**: 查询出长时间未被访问（`last_accessed_at`很旧）且重要性较低（`importance_score`低）的记忆。
        *   对这些记忆的`importance_score`应用一个衰减因子，并更新回数据库。
        *   （可选）对于分数低于某个极低阈值的记忆，可以将其归档或删除。

## 4. 总结

本架构设计通过以下关键点来构建一个生产级的`memory-service`：
1.  **应用层加密(ALE)的严格执行**: 将加解密职责完全委托给`kms-proxy-service`，本服务在持久化层只处理密文，确保了服务器端零知识和用户数据的绝对隐私。
2.  **仿生检索模型**: 通过在领域层实现一个结合**新近度、重要性、相关性**的加权检索与重排模型，超越了简单的向量搜索，更能模拟人类记忆的工作方式。
3.  **动态生命周期**: 通过“回顾增强”（更新访问时间）和“记忆衰减”（后台任务），实现了记忆库的动态管理，使其能自动聚焦于对用户最重要的信息。
4.  **CQRS思想的读写分离**: 将高计算复杂度的写操作（加密、嵌入）和读操作（RAG检索）分离，便于针对性优化和理解。
5.  **清晰的外部依赖**: 与`embedding-service`和`kms-proxy-service`的交互界限清晰，职责分明。

这种架构确保了`memory-service`能够为AI助手提供一个**安全、智能、且富有“人性化”记忆特征**的长期上下文支持，是CINA.CLUB平台实现真正深度个性化交互体验的关键所在。