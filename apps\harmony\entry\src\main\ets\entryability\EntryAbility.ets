/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import { AbilityConstant, UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { DependencyInjector } from '../common/di/DependencyInjector';
import { AppConfigManager } from '../common/config/AppConfigManager';
import { CoreGoBridge } from '../common/core/CoreGoBridge';

/**
 * CINA.CLUB HarmonyOS应用主入口
 * 
 * 职责：
 * 1. 应用生命周期管理
 * 2. 全局依赖注入初始化
 * 3. Go核心库初始化
 * 4. 分布式能力初始化
 */
export default class EntryAbility extends UIAbility {
  private static readonly TAG = 'EntryAbility';

  /**
   * 应用创建时的回调
   */
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(0x0000, EntryAbility.TAG, '%{public}s', 'Ability onCreate');
    
    // 初始化应用配置
    this.initializeAppConfig();
    
    // 初始化Go核心库
    this.initializeCoreGoBridge();
    
    // 初始化依赖注入容器
    this.initializeDependencyInjection();
  }

  /**
   * 应用销毁时的回调
   */
  onDestroy(): void {
    hilog.info(0x0000, EntryAbility.TAG, '%{public}s', 'Ability onDestroy');
    
    // 清理Go核心库资源
    CoreGoBridge.getInstance().cleanup();
    
    // 清理依赖注入容器
    DependencyInjector.getInstance().cleanup();
  }

  /**
   * 窗口阶段创建时的回调
   */
  onWindowStageCreate(windowStage: window.WindowStage): void {
    hilog.info(0x0000, EntryAbility.TAG, '%{public}s', 'Ability onWindowStageCreate');

    // 设置主页面路径
    windowStage.loadContent('pages/Index', (err) => {
      if (err.code) {
        hilog.error(0x0000, EntryAbility.TAG, 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, EntryAbility.TAG, 'Succeeded in loading the content.');
    });

    // 配置窗口属性
    this.configureWindow(windowStage);
  }

  /**
   * 窗口阶段销毁时的回调
   */
  onWindowStageDestroy(): void {
    hilog.info(0x0000, EntryAbility.TAG, '%{public}s', 'Ability onWindowStageDestroy');
  }

  /**
   * 应用前台激活时的回调
   */
  onForeground(): void {
    hilog.info(0x0000, EntryAbility.TAG, '%{public}s', 'Ability onForeground');
    
    // 恢复WebSocket连接等
    DependencyInjector.getInstance().getNetworkManager().reconnectIfNeeded();
  }

  /**
   * 应用后台时的回调
   */
  onBackground(): void {
    hilog.info(0x0000, EntryAbility.TAG, '%{public}s', 'Ability onBackground');
    
    // 可选择性断开WebSocket连接以节省资源
    DependencyInjector.getInstance().getNetworkManager().handleBackground();
  }

  /**
   * 处理跨设备流转
   */
  onContinue(wantParam: Record<string, Object>): AbilityConstant.OnContinueResult {
    hilog.info(0x0000, EntryAbility.TAG, '%{public}s', 'Ability onContinue');
    
    // 保存当前状态用于跨设备流转
    wantParam['currentPage'] = 'pages/Index';
    wantParam['userState'] = this.getCurrentUserState();
    
    return AbilityConstant.OnContinueResult.AGREE;
  }

  /**
   * 初始化应用配置
   */
  private initializeAppConfig(): void {
    try {
      AppConfigManager.getInstance().initialize();
      hilog.info(0x0000, EntryAbility.TAG, 'App config initialized successfully');
    } catch (error) {
      hilog.error(0x0000, EntryAbility.TAG, 'Failed to initialize app config: %{public}s', error.message);
    }
  }

  /**
   * 初始化Go核心库
   */
  private initializeCoreGoBridge(): void {
    try {
      CoreGoBridge.getInstance().initialize();
      hilog.info(0x0000, EntryAbility.TAG, 'Core Go Bridge initialized successfully');
    } catch (error) {
      hilog.error(0x0000, EntryAbility.TAG, 'Failed to initialize Core Go Bridge: %{public}s', error.message);
    }
  }

  /**
   * 初始化依赖注入
   */
  private initializeDependencyInjection(): void {
    try {
      DependencyInjector.getInstance().initialize(this.context);
      hilog.info(0x0000, EntryAbility.TAG, 'Dependency injection initialized successfully');
    } catch (error) {
      hilog.error(0x0000, EntryAbility.TAG, 'Failed to initialize dependency injection: %{public}s', error.message);
    }
  }

  /**
   * 配置窗口属性
   */
  private configureWindow(windowStage: window.WindowStage): void {
    windowStage.getMainWindow().then((windowClass) => {
      // 设置沉浸式状态栏
      windowClass.setWindowSystemBarEnable(['status', 'navigation']);
      
      // 设置状态栏样式
      windowClass.setWindowSystemBarProperties({
        statusBarColor: '#00000000',
        navigationBarColor: '#00000000',
        statusBarContentColor: '#FF000000',
        navigationBarContentColor: '#FF000000'
      });
    }).catch((error) => {
      hilog.error(0x0000, EntryAbility.TAG, 'Failed to configure window: %{public}s', JSON.stringify(error));
    });
  }

  /**
   * 获取当前用户状态（用于跨设备流转）
   */
  private getCurrentUserState(): Record<string, Object> {
    const userManager = DependencyInjector.getInstance().getUserManager();
    return {
      isLoggedIn: userManager.isLoggedIn(),
      userId: userManager.getCurrentUserId(),
      // 其他需要流转的状态...
    };
  }
} 