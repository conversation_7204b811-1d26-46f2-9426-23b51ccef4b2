/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

import SwiftUI
import AppCore
import DataLayer
import DesignSystem
import FeatureAuth

@main
struct CinaClubApp: App {
    @StateObject private var appCore = AppCore.shared
    @StateObject private var appCoordinator = AppCoordinator()
    
    init() {
        setupApp()
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appCore)
                .environmentObject(appCoordinator)
                .task {
                    await initializeApp()
                }
        }
    }
    
    private func setupApp() {
        // Configure app appearance
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        appearance.titleTextAttributes = [.foregroundColor: UIColor.label]
        appearance.largeTitleTextAttributes = [.foregroundColor: UIColor.label]
        
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        
        // Configure tab bar appearance
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithOpaqueBackground()
        tabBarAppearance.backgroundColor = UIColor.systemBackground
        
        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
    }
    
    private func initializeApp() async {
        do {
            try await appCore.initialize()
        } catch {
            print("Failed to initialize app: \(error)")
        }
    }
}

struct ContentView: View {
    @EnvironmentObject private var appCore: AppCore
    @EnvironmentObject private var appCoordinator: AppCoordinator
    
    var body: some View {
        Group {
            if appCore.isAuthenticated {
                MainTabView()
            } else {
                AuthView()
            }
        }
        .animation(.easeInOut(duration: 0.3), value: appCore.isAuthenticated)
    }
}

struct MainTabView: View {
    @EnvironmentObject private var appCoordinator: AppCoordinator
    
    var body: some View {
        TabView(selection: $appCoordinator.currentTab) {
            ForEach(AppTab.allCases, id: \.self) { tab in
                tabView(for: tab)
                    .tabItem {
                        Image(systemName: tab.iconName)
                        Text(tab.displayName)
                    }
                    .tag(tab)
            }
        }
        .accentColor(.cinaPrimary)
    }
    
    @ViewBuilder
    private func tabView(for tab: AppTab) -> some View {
        switch tab {
        case .home:
            HomeView()
        case .chat:
            ChatView()
        case .pkb:
            PKBView()
        case .marketplace:
            MarketplaceView()
        case .profile:
            ProfileView()
        }
    }
}

// MARK: - Placeholder Views

struct HomeView: View {
    var body: some View {
        NavigationView {
            EmptyStateView(
                image: "house",
                title: "Welcome Home",
                message: "Your personal dashboard is coming soon!"
            )
            .navigationTitle("Home")
        }
    }
}

struct ChatView: View {
    var body: some View {
        NavigationView {
            EmptyStateView(
                image: "message",
                title: "No Conversations",
                message: "Start chatting with your AI assistant or friends!",
                actionTitle: "Start Chat"
            ) {
                // TODO: Implement chat creation
            }
            .navigationTitle("Chat")
        }
    }
}

struct PKBView: View {
    var body: some View {
        NavigationView {
            EmptyStateView(
                image: "brain",
                title: "Your Knowledge Base",
                message: "Create your first knowledge item to get started!",
                actionTitle: "Add Item"
            ) {
                // TODO: Implement PKB item creation
            }
            .navigationTitle("Knowledge")
        }
    }
}

struct MarketplaceView: View {
    var body: some View {
        NavigationView {
            EmptyStateView(
                image: "storefront",
                title: "Service Marketplace",
                message: "Discover and book amazing services from our community!",
                actionTitle: "Browse Services"
            ) {
                // TODO: Implement marketplace browsing
            }
            .navigationTitle("Marketplace")
        }
    }
}

struct ProfileView: View {
    @EnvironmentObject private var appCore: AppCore
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.lg) {
                    // User profile header
                    VStack(spacing: Spacing.md) {
                        AvatarView(
                            imageURL: appCore.currentUser?.avatarURL,
                            size: 80,
                            fallbackText: String(appCore.currentUser?.displayName.prefix(1) ?? "?")
                        )
                        
                        Text(appCore.currentUser?.displayName ?? "User")
                            .font(.cinaTitle2)
                            .foregroundColor(.cinaLabel)
                        
                        Text(appCore.currentUser?.email ?? "")
                            .font(.cinaCallout)
                            .foregroundColor(.cinaSecondaryLabel)
                        
                        TagView(
                            text: appCore.currentUser?.membershipType.displayName ?? "Basic",
                            color: .cinaPrimary
                        )
                    }
                    .cardStyle()
                    
                    // Profile actions
                    VStack(spacing: Spacing.sm) {
                        ProfileActionRow(
                            icon: "person.circle",
                            title: "Edit Profile",
                            action: { /* TODO */ }
                        )
                        
                        ProfileActionRow(
                            icon: "bell",
                            title: "Notifications",
                            action: { /* TODO */ }
                        )
                        
                        ProfileActionRow(
                            icon: "lock",
                            title: "Privacy & Security",
                            action: { /* TODO */ }
                        )
                        
                        ProfileActionRow(
                            icon: "questionmark.circle",
                            title: "Help & Support",
                            action: { /* TODO */ }
                        )
                    }
                    .cardStyle()
                    
                    // Sign out button
                    SecondaryButton(title: "Sign Out") {
                        Task {
                            await appCore.signOut()
                        }
                    }
                    .padding(.horizontal, Spacing.lg)
                }
                .padding()
            }
            .navigationTitle("Profile")
        }
    }
}

struct ProfileActionRow: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.cinaTitle3)
                    .foregroundColor(.cinaPrimary)
                    .frame(width: 24)
                
                Text(title)
                    .font(.cinaBody)
                    .foregroundColor(.cinaLabel)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.cinaCallout)
                    .foregroundColor(.cinaSecondaryLabel)
            }
            .padding(.vertical, Spacing.sm)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ContentView()
        .environmentObject(AppCore.shared)
        .environmentObject(AppCoordinator())
} 