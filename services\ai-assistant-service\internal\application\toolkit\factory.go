/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package toolkit

import (
	"context"
	"fmt"
	"sync"

	"cina.club/services/ai-assistant-service/internal/application/port"
)

// ToolkitFactory toolkit factory
type ToolkitFactory struct {
	tools      map[string]port.Tool
	categories map[port.ToolCategory][]port.Tool
	mutex      sync.RWMutex
}

// NewToolkitFactory creates a new toolkit factory
func NewToolkitFactory() *ToolkitFactory {
	return &ToolkitFactory{
		tools:      make(map[string]port.Tool),
		categories: make(map[port.ToolCategory][]port.Tool),
	}
}

// RegisterTool registers a tool
func (tf *ToolkitFactory) RegisterTool(tool port.Tool) error {
	tf.mutex.Lock()
	defer tf.mutex.Unlock()

	name := tool.Name()
	if name == "" {
		return fmt.Errorf("tool name cannot be empty")
	}

	// Check if tool with same name already exists
	if _, exists := tf.tools[name]; exists {
		return fmt.Errorf("tool with name '%s' already exists", name)
	}

	// Register tool
	tf.tools[name] = tool

	// Organize tools by category
	category := tool.Category()
	tf.categories[category] = append(tf.categories[category], tool)

	return nil
}

// GetTool gets tool by name
func (tf *ToolkitFactory) GetTool(name string) (port.Tool, error) {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	tool, exists := tf.tools[name]
	if !exists {
		return nil, fmt.Errorf("tool '%s' not found", name)
	}

	return tool, nil
}

// GetAllTools gets all tools
func (tf *ToolkitFactory) GetAllTools() map[string]port.Tool {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	// Return copy to avoid concurrent modification
	result := make(map[string]port.Tool)
	for name, tool := range tf.tools {
		result[name] = tool
	}

	return result
}

// GetToolsByCategory gets tools by category
func (tf *ToolkitFactory) GetToolsByCategory(category port.ToolCategory) []port.Tool {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	tools, exists := tf.categories[category]
	if !exists {
		return []port.Tool{}
	}

	// Return copy
	result := make([]port.Tool, len(tools))
	copy(result, tools)

	return result
}

// GetToolSchemas gets all tool schemas (used for LLM planning)
func (tf *ToolkitFactory) GetToolSchemas() []*port.ToolSchema {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	schemas := make([]*port.ToolSchema, 0, len(tf.tools))

	for _, tool := range tf.tools {
		schema := &port.ToolSchema{
			Name:         tool.Name(),
			Description:  tool.Description(),
			Category:     string(tool.Category()),
			InputSchema:  tool.InputSchema(),
			RequiresAuth: tool.RequiresAuth(),
			IsAsync:      tool.IsAsync(),
		}
		schemas = append(schemas, schema)
	}

	return schemas
}

// ValidateToolCall validates tool call parameters
func (tf *ToolkitFactory) ValidateToolCall(toolName string, inputs map[string]interface{}) error {
	tool, err := tf.GetTool(toolName)
	if err != nil {
		return err
	}

	// Get input schema
	inputSchema := tool.InputSchema()
	if inputSchema == nil {
		return nil // No schema means no validation
	}

	// Validate required fields
	if inputSchema.Required != nil {
		for _, required := range inputSchema.Required {
			if _, exists := inputs[required]; !exists {
				return fmt.Errorf("required field '%s' is missing", required)
			}
		}
	}

	// Validate field types (simplified version)
	if inputSchema.Properties != nil {
		for fieldName, value := range inputs {
			fieldSchema, exists := inputSchema.Properties[fieldName]
			if !exists {
				continue // Allow extra fields
			}

			if err := tf.validateFieldType(fieldName, value, fieldSchema); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateFieldType validates field type
func (tf *ToolkitFactory) validateFieldType(fieldName string, value interface{}, schema *port.JSONSchema) error {
	if schema == nil {
		return nil
	}

	switch schema.Type {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("field '%s' must be a string", fieldName)
		}
	case "integer":
		switch v := value.(type) {
		case int, int32, int64:
			// Check range
			if schema.Minimum != nil || schema.Maximum != nil {
				var num float64
				switch val := v.(type) {
				case int:
					num = float64(val)
				case int32:
					num = float64(val)
				case int64:
					num = float64(val)
				}

				if schema.Minimum != nil && num < *schema.Minimum {
					return fmt.Errorf("field '%s' must be >= %v", fieldName, *schema.Minimum)
				}
				if schema.Maximum != nil && num > *schema.Maximum {
					return fmt.Errorf("field '%s' must be <= %v", fieldName, *schema.Maximum)
				}
			}
		case float64:
			// JSON numbers default to float64
			if v != float64(int64(v)) {
				return fmt.Errorf("field '%s' must be an integer", fieldName)
			}
		default:
			return fmt.Errorf("field '%s' must be an integer", fieldName)
		}
	case "number":
		if _, ok := value.(float64); !ok {
			if _, ok := value.(int); !ok {
				return fmt.Errorf("field '%s' must be a number", fieldName)
			}
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("field '%s' must be a boolean", fieldName)
		}
	case "array":
		if _, ok := value.([]interface{}); !ok {
			return fmt.Errorf("field '%s' must be an array", fieldName)
		}
	case "object":
		if _, ok := value.(map[string]interface{}); !ok {
			return fmt.Errorf("field '%s' must be an object", fieldName)
		}
	}

	return nil
}

// ExecuteTool executes a tool
func (tf *ToolkitFactory) ExecuteTool(ctx context.Context, toolName string, inputs map[string]interface{}) (*port.ToolResult, error) {
	// Validate tool call
	if err := tf.ValidateToolCall(toolName, inputs); err != nil {
		return port.NewToolError(fmt.Sprintf("validation failed: %v", err)), nil
	}

	// Get tool
	tool, err := tf.GetTool(toolName)
	if err != nil {
		return port.NewToolError(fmt.Sprintf("tool not found: %v", err)), nil
	}

	// Execute tool
	result, err := tool.Execute(ctx, inputs)
	if err != nil {
		return port.NewToolError(fmt.Sprintf("execution failed: %v", err)), nil
	}

	return result, nil
}

// GetToolCount gets number of tools
func (tf *ToolkitFactory) GetToolCount() int {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	return len(tf.tools)
}

// GetCategoryCount gets number of categories
func (tf *ToolkitFactory) GetCategoryCount() int {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	return len(tf.categories)
}

// ListToolNames lists all tool names
func (tf *ToolkitFactory) ListToolNames() []string {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	names := make([]string, 0, len(tf.tools))
	for name := range tf.tools {
		names = append(names, name)
	}

	return names
}

// ListCategories lists all categories
func (tf *ToolkitFactory) ListCategories() []port.ToolCategory {
	tf.mutex.RLock()
	defer tf.mutex.RUnlock()

	categories := make([]port.ToolCategory, 0, len(tf.categories))
	for category := range tf.categories {
		categories = append(categories, category)
	}

	return categories
}

// Clear clears all tools (mainly for testing)
func (tf *ToolkitFactory) Clear() {
	tf.mutex.Lock()
	defer tf.mutex.Unlock()

	tf.tools = make(map[string]port.Tool)
	tf.categories = make(map[port.ToolCategory][]port.Tool)
}

// RegisterDefaultTools registers default tool set
func (tf *ToolkitFactory) RegisterDefaultTools() error {
	// Register LLM tool
	if err := tf.RegisterTool(NewLLMTool()); err != nil {
		return fmt.Errorf("failed to register LLM tool: %w", err)
	}

	// Register search tool
	if err := tf.RegisterTool(NewSearchServicesTool()); err != nil {
		return fmt.Errorf("failed to register search services tool: %w", err)
	}

	// Register schedule tool
	if err := tf.RegisterTool(NewCreateScheduleTool()); err != nil {
		return fmt.Errorf("failed to register create schedule tool: %w", err)
	}

	// Register memory tool
	if err := tf.RegisterTool(NewQueryMemoryTool()); err != nil {
		return fmt.Errorf("failed to register query memory tool: %w", err)
	}

	// Register knowledge base tool
	if err := tf.RegisterTool(NewQueryKnowledgeTool()); err != nil {
		return fmt.Errorf("failed to register query knowledge tool: %w", err)
	}

	// Register user info tool
	if err := tf.RegisterTool(NewGetUserInfoTool()); err != nil {
		return fmt.Errorf("failed to register get user info tool: %w", err)
	}

	return nil
}
