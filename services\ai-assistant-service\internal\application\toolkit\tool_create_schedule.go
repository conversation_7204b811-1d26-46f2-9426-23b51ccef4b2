/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package toolkit

import (
	"context"
	"time"

	"cina.club/services/ai-assistant-service/internal/application/port"
)

// CreateScheduleTool create schedule tool
type CreateScheduleTool struct {
	name        string
	description string
}

// NewCreateScheduleTool creates a new schedule tool
func NewCreateScheduleTool() port.Tool {
	return &CreateScheduleTool{
		name:        "create_schedule",
		description: "Create schedule arrangements for users, supporting reminders and recurring rules",
	}
}

// Name returns tool name
func (t *CreateScheduleTool) Name() string {
	return t.name
}

// Description returns tool description
func (t *CreateScheduleTool) Description() string {
	return t.description
}

// Category returns tool category
func (t *CreateScheduleTool) Category() port.ToolCategory {
	return port.ToolCategorySchedule
}

// RequiresAuth returns whether authentication is required
func (t *CreateScheduleTool) RequiresAuth() bool {
	return true // Creating schedule requires user authentication
}

// IsAsync returns whether this is an async tool
func (t *CreateScheduleTool) IsAsync() bool {
	return false
}

// InputSchema returns input parameter schema
func (t *CreateScheduleTool) InputSchema() *port.JSONSchema {
	schema := port.NewObjectSchema(
		"Create schedule parameters",
		map[string]*port.JSONSchema{},
		[]string{"title", "start_time"},
	)

	schema.AddProperty("title", port.NewStringSchema("Schedule title", true))
	schema.AddProperty("description", port.NewStringSchema("Schedule description", false))
	schema.AddProperty("start_time", port.NewStringSchema("Start time (ISO 8601 format)", true))
	schema.AddProperty("end_time", port.NewStringSchema("End time (ISO 8601 format)", false))
	schema.AddProperty("location", port.NewStringSchema("Location", false))
	schema.AddProperty("reminder_minutes", port.NewIntegerSchema("Reminder time (minutes)",
		func() *float64 { v := float64(0); return &v }(),
		func() *float64 { v := float64(10080); return &v }())) // Max 7 days
	schema.AddProperty("is_all_day", port.NewBooleanSchema("Whether it's an all-day event"))

	return schema
}

// OutputSchema returns output result schema
func (t *CreateScheduleTool) OutputSchema() *port.JSONSchema {
	return port.NewObjectSchema(
		"Create schedule result",
		map[string]*port.JSONSchema{
			"schedule_id": port.NewStringSchema("Schedule ID", true),
			"title":       port.NewStringSchema("Schedule title", true),
			"start_time":  port.NewStringSchema("Start time", true),
			"end_time":    port.NewStringSchema("End time", false),
			"location":    port.NewStringSchema("Location", false),
			"created_at":  port.NewStringSchema("Creation time", true),
		},
		[]string{"schedule_id", "title", "start_time", "created_at"},
	)
}

// Execute executes tool logic
func (t *CreateScheduleTool) Execute(ctx context.Context, inputs map[string]interface{}) (*port.ToolResult, error) {
	// Extract parameters
	title, ok := inputs["title"].(string)
	if !ok || title == "" {
		return port.NewToolError("title is required and must be a string"), nil
	}

	startTimeStr, ok := inputs["start_time"].(string)
	if !ok || startTimeStr == "" {
		return port.NewToolError("start_time is required and must be a string"), nil
	}

	// Parse time
	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		return port.NewToolError("invalid start_time format, expected ISO 8601"), nil
	}

	description := ""
	if d, ok := inputs["description"].(string); ok {
		description = d
	}

	var endTime *time.Time
	if et, ok := inputs["end_time"].(string); ok && et != "" {
		if parsed, err := time.Parse(time.RFC3339, et); err == nil {
			endTime = &parsed
		}
	}

	location := ""
	if l, ok := inputs["location"].(string); ok {
		location = l
	}

	reminderMinutes := 15 // Default 15 minutes reminder
	if rm, ok := inputs["reminder_minutes"].(float64); ok {
		reminderMinutes = int(rm)
	}

	isAllDay := false
	if ad, ok := inputs["is_all_day"].(bool); ok {
		isAllDay = ad
	}

	// TODO: Actual schedule creation logic
	// This should call schedule-service gRPC interface
	result := t.createSchedule(ctx, title, description, startTime, endTime, location, reminderMinutes, isAllDay)

	return port.NewToolResult(map[string]interface{}{
		"schedule_id": result.ScheduleID,
		"title":       result.Title,
		"start_time":  result.StartTime,
		"end_time":    result.EndTime,
		"location":    result.Location,
		"created_at":  result.CreatedAt,
	}), nil
}

// ScheduleResult schedule creation result
type ScheduleResult struct {
	ScheduleID string `json:"schedule_id"`
	Title      string `json:"title"`
	StartTime  string `json:"start_time"`
	EndTime    string `json:"end_time,omitempty"`
	Location   string `json:"location,omitempty"`
	CreatedAt  string `json:"created_at"`
}

// createSchedule creates schedule (simplified implementation)
func (t *CreateScheduleTool) createSchedule(_ context.Context, title, _ string, startTime time.Time, endTime *time.Time, location string, _ int, isAllDay bool) *ScheduleResult {
	// This is a simplified implementation, should call schedule-service gRPC interface in production

	scheduleID := "schedule-" + time.Now().Format("20060102150405")

	endTimeStr := ""
	if endTime != nil {
		endTimeStr = endTime.Format(time.RFC3339)
	} else if isAllDay {
		// If it's an all-day event without specified end time, set to 23:59 of the day
		endOfDay := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 0, startTime.Location())
		endTimeStr = endOfDay.Format(time.RFC3339)
	}

	return &ScheduleResult{
		ScheduleID: scheduleID,
		Title:      title,
		StartTime:  startTime.Format(time.RFC3339),
		EndTime:    endTimeStr,
		Location:   location,
		CreatedAt:  time.Now().Format(time.RFC3339),
	}
}
