/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

// System configuration types
export enum ConfigType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON',
  ARRAY = 'ARRAY',
  SECRET = 'SECRET',
}

// Configuration categories
export enum ConfigCategory {
  GENERAL = 'GENERAL',
  SECURITY = 'SECURITY',
  PERFORMANCE = 'PERFORMANCE',
  FEATURES = 'FEATURES',
  INTEGRATIONS = 'INTEGRATIONS',
  NOTIFICATIONS = 'NOTIFICATIONS',
  STORAGE = 'STORAGE',
  CACHE = 'CACHE',
}

// System status
export enum SystemStatus {
  OPERATIONAL = 'OPERATIONAL',
  MAINTENANCE = 'MAINTENANCE',
  DEGRADED = 'DEGRADED',
  OUTAGE = 'OUTAGE',
}

// Audit action types
export enum AuditAction {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  BACKUP = 'BACKUP',
  RESTORE = 'RESTORE',
}

// System configuration
export interface SystemConfig {
  id: string
  key: string
  value: any
  type: ConfigType
  category: ConfigCategory
  description: string
  isRequired: boolean
  isSecret: boolean
  defaultValue?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    options?: string[]
  }
  updatedBy: string
  updatedAt: string
  environment: 'DEVELOPMENT' | 'STAGING' | 'PRODUCTION'
}

// Feature flag
export interface FeatureFlag {
  id: string
  name: string
  key: string
  description: string
  isEnabled: boolean
  rolloutPercentage: number
  userSegments: string[]
  startDate?: string
  endDate?: string
  metadata?: Record<string, any>
  createdBy: string
  createdAt: string
  updatedAt: string
}

// System health
export interface SystemHealth {
  status: SystemStatus
  version: string
  uptime: number
  timestamp: string
  components: Array<{
    name: string
    status: 'UP' | 'DOWN' | 'DEGRADED'
    responseTime?: number
    message?: string
  }>
  metrics: {
    cpu: number
    memory: number
    disk: number
    database: {
      connections: number
      queryTime: number
    }
    cache: {
      hitRate: number
      connections: number
    }
  }
}

// Audit log entry
export interface AuditLog {
  id: string
  userId?: string
  userName?: string
  action: AuditAction
  resource: string
  resourceId?: string
  details: string
  metadata?: Record<string, any>
  ip: string
  userAgent: string
  timestamp: string
  success: boolean
  errorMessage?: string
}

// System maintenance
export interface MaintenanceWindow {
  id: string
  title: string
  description: string
  scheduledStart: string
  scheduledEnd: string
  actualStart?: string
  actualEnd?: string
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  affectedServices: string[]
  maintenanceType: 'PLANNED' | 'EMERGENCY' | 'SECURITY'
  createdBy: string
  createdAt: string
  notifications: {
    users: boolean
    admins: boolean
    external: boolean
  }
}

// System backup
export interface SystemBackup {
  id: string
  name: string
  type: 'FULL' | 'INCREMENTAL' | 'DIFFERENTIAL'
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED'
  size: number
  location: string
  checksum: string
  retentionDays: number
  createdAt: string
  completedAt?: string
  expiresAt: string
  metadata: {
    databases: string[]
    files: string[]
    configurations: boolean
  }
}

// API key management
export interface ApiKey {
  id: string
  name: string
  key: string // masked in responses
  permissions: string[]
  rateLimit: {
    requests: number
    period: 'MINUTE' | 'HOUR' | 'DAY'
  }
  isActive: boolean
  lastUsed?: string
  expiresAt?: string
  createdBy: string
  createdAt: string
  usage: {
    requests: number
    errors: number
    lastRequest?: string
  }
}

// System notification
export interface SystemNotification {
  id: string
  title: string
  message: string
  type: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS'
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  targetAudience: 'ALL_USERS' | 'ADMINS' | 'SPECIFIC_USERS' | 'USER_ROLES'
  targets?: string[]
  channels: Array<'IN_APP' | 'EMAIL' | 'SMS' | 'PUSH'>
  scheduledAt?: string
  expiresAt?: string
  isActive: boolean
  createdBy: string
  createdAt: string
  stats: {
    sent: number
    delivered: number
    read: number
    clicked: number
  }
}

// System log
export interface SystemLog {
  id: string
  level: 'TRACE' | 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL'
  message: string
  source: string
  service?: string
  timestamp: string
  metadata?: Record<string, any>
  traceId?: string
  spanId?: string
  userId?: string
  sessionId?: string
}

// System metrics
export interface SystemMetrics {
  timestamp: string
  server: {
    cpu: {
      usage: number
      cores: number
    }
    memory: {
      total: number
      used: number
      free: number
      percentage: number
    }
    disk: {
      total: number
      used: number
      free: number
      percentage: number
    }
    network: {
      bytesIn: number
      bytesOut: number
      packetsIn: number
      packetsOut: number
    }
  }
  application: {
    activeUsers: number
    sessions: number
    requests: {
      total: number
      success: number
      errors: number
      rate: number
    }
    responseTime: {
      average: number
      p50: number
      p95: number
      p99: number
    }
  }
  database: {
    connections: {
      active: number
      idle: number
      total: number
    }
    queries: {
      total: number
      slow: number
      failed: number
      averageTime: number
    }
    size: number
  }
}

// Environment configuration
export interface Environment {
  name: string
  displayName: string
  description: string
  isProduction: boolean
  variables: Record<string, string>
  secrets: string[]
  services: Array<{
    name: string
    url: string
    status: 'UP' | 'DOWN'
  }>
  deployment: {
    lastDeployment?: string
    version?: string
    deployedBy?: string
  }
}

// System alert rule
export interface AlertRule {
  id: string
  name: string
  description: string
  metric: string
  condition: {
    operator: 'GREATER_THAN' | 'LESS_THAN' | 'EQUALS' | 'NOT_EQUALS'
    threshold: number
    duration: number // minutes
  }
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  notifications: {
    channels: Array<'EMAIL' | 'SMS' | 'SLACK' | 'WEBHOOK'>
    recipients: string[]
    template?: string
  }
  isActive: boolean
  lastTriggered?: string
  createdBy: string
  createdAt: string
} 