好的，遵照您的指示，我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/auth`** 这个具体包的、极致细化的、生产级的软件需求/设计规格说明书（SRS）。

这份文档将详细阐述`pkg/auth`的职责、接口、安全模型和实现细节，作为后端服务认证与授权中间件的权威设计蓝图。

---
### CINA.CLUB - 共享后端库 `pkg/auth` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-25**  
**文档负责人:** [平台总架构师/安全架构师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心安全模型](#3-核心安全模型)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [上下文传播 (Context Propagation)](#6-上下文传播-context-propagation)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的微服务架构中，确保每个服务间（S2S）的调用和对内（Admin）的API访问都是经过严格认证和授权的，是平台安全体系的基石。`pkg/auth` 的目的在于提供一套**标准化的、高性能的、安全的Go语言认证与授权中间件**。它将被所有后端微服务统一使用，以拦截和处理gRPC请求，从而将复杂的认证逻辑从业务代码中完全解耦。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   提供用于验证**最终用户JWT (Access Token)** 的gRPC拦截器。
    *   提供用于验证**服务间JWT (S2S Token)** 的gRPC拦截器。
    *   提供基于角色的访问控制（RBAC）检查的gRPC拦截器。
    *   提供从请求上下文中安全提取和注入认证信息的工具函数。
*   **范围之外 (Out-of-Scope)**:
    *   **JWT的签发、刷新、吊销**: 这是`user-core-service`的职责。
    *   **JWKS端点的托管**: 由`user-core-service`负责。
    *   **角色和权限的定义与管理**: 由`user-core-service`负责。
    *   任何具体的业务逻辑。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/auth` 是位于`pkg/`目录下的一个核心共享库。它被设计为一个**纯粹的功能库**，不包含任何可运行的主程序。所有需要保护其gRPC端点的微服务，都会在其服务启动时，将`pkg/auth`提供的拦截器链入gRPC服务器。

#### 2.2. 设计原则
*   **零信任 (Zero Trust)**: 不信任任何网络来源，所有请求都必须经过验证。
*   **性能优先**: 认证是每个请求的必经之路，其性能开销必须被控制在最低水平。
*   **配置驱动**: 所有行为，如JWKS地址、S2S密钥、RBAC策略等，都通过配置注入，而非硬编码。
*   **故障关闭 (Fail-Close)**: 在验证过程中出现任何错误（如无法获取JWKS、token格式错误），都应立即拒绝请求，而不是放行。
*   **最小权限**: 中间件只应解析和验证token，不应尝试执行任何业务操作。

---

### 3. 核心安全模型

#### 3.1. 用户认证流程 (User JWT)
1.  客户端携带由`user-core-service`签发的**Access Token**访问`api-gateway`。
2.  `api-gateway`验证此JWT的签名和有效期。
3.  `api-gateway`将JWT中的`userId`和其他关键信息注入到请求头，转发给下游微服务。
4.  （可选，用于更高安全性）下游微服务可以使用`pkg/auth`的拦截器再次验证该JWT，以防止网关被绕过。

#### 3.2. 服务间认证流程 (S2S JWT)
1.  服务A（调用方）需要调用服务B（被调用方）。
2.  服务A使用自己的**服务私钥**，签发一个短生命周期的**S2S Token**。该Token的`claims`中包含`sub` (服务A的名称) 和 `aud` (服务B的名称)。
3.  服务A将S2S Token附加到gRPC请求的元数据中。
4.  服务B的`pkg/auth` S2S拦截器接收到请求。
5.  拦截器使用**服务A的公钥**来验证S2S Token的签名。公钥通过一个内部配置或服务发现机制获取。
6.  验证通过后，请求被放行到业务逻辑。

#### 3.3. RBAC授权流程
1.  一个Admin用户通过管理后台，携带其JWT访问一个需要特定权限的gRPC方法。
2.  `pkg/auth`的RBAC拦截器首先验证JWT（如3.1所述）。
3.  验证成功后，从JWT的`claims`中解析出该用户的`roles`。
4.  拦截器查询一个**预加载的、内存中的RBAC策略映射**（`map[role][]permission`），以确定该用户的角色拥有哪些权限。
5.  拦截器检查该gRPC方法所需的权限是否在该用户的权限列表中。
6.  检查通过，则放行请求；否则，返回`PermissionDenied`错误。

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. JWT验证器 (JWT Validator)
*   **FR4.1.1 (JWKS自动刷新)**: 必须实现一个JWKS客户端，能从`user-core-service`的JWKS端点获取公钥集。该客户端必须支持**基于缓存和速率限制的自动刷新**，以避免频繁请求JWKS端点。
*   **FR4.1.2 (非对称签名验证)**: 必须支持RS256, RS512, ES256, ES512等标准的非对称签名算法。
*   **FR4.1.3 (标准Claims验证)**: 必须严格验证JWT的标准`claims`：
    *   `exp` (Expiration Time): 检查是否过期。
    *   `nbf` (Not Before): 检查是否已生效。
    *   `iss` (Issuer): 检查签发者是否为预期的`user-core-service`。
    *   `aud` (Audience): 检查受众是否包含当前服务。

#### 4.2. gRPC拦截器 (Interceptors)
*   **FR4.2.1 (用户JWT拦截器)**:
    *   提供一个`UnaryUserJWTInterceptor`和`StreamUserJWTInterceptor`。
    *   从gRPC元数据中提取`Authorization`头。
    *   使用**FR4.1**的验证器对Token进行验证。
    *   验证成功后，将解析出的用户信息（如`AuthenticatedUser` struct）注入到`context.Context`中。
*   **FR4.2.2 (S2S JWT拦截器)**:
    *   提供一个`UnaryS2SJWTInterceptor`和`StreamS2SJWTInterceptor`。
    *   从gRPC元数据中提取S2S Token。
    *   使用一个基于内存缓存的公钥提供者，获取调用方服务的公钥并验证Token。
    *   验证成功后，将调用方服务信息注入到`context.Context`中。
*   **FR4.2.3 (RBAC拦截器)**:
    *   提供一个`UnaryRBACInterceptor`和`StreamRBACInterceptor`。
    *   它必须依赖于用户JWT拦截器已运行，并从`context.Context`中获取用户信息和角色。
    *   它需要一个**方法到权限的映射**作为输入，以确定当前RPC需要什么权限。
    *   执行**FR3.3**中描述的授权检查逻辑。

#### 4.3. 上下文工具 (Context Utils)
*   **FR4.3.1 (信息注入与提取)**: 提供一组工具函数，用于：
    *   `NewContextWithAuthenticatedUser(ctx, user)`: 将认证后的用户信息注入上下文。
    *   `UserFromContext(ctx)`: 从上下文中安全地提取用户信息。如果不存在，返回错误。
    *   类似的函数用于处理S2S调用方信息。
*   **FR4.3.2 (类型安全)**: 上下文的`key`必须是私有的、不可导出的类型，以防止包外冲突。

---

### 5. 接口定义 (API Specification)

```go
// pkg/auth/auth.go

// AuthenticatedUser 代表一个通过JWT验证的最终用户信息
type AuthenticatedUser struct {
    ID    string
    Roles []string
    // ... 其他需要从token claims中获取的字段
}

// ServiceIdentity 代表一个通过S2S JWT验证的服务身份
type ServiceIdentity struct {
    Name string // e.g., "user-core-service"
}

// Config 结构体，用于初始化所有拦截器
type Config struct {
    UserCoreServiceJWKSUrl string
    RBACPolicy             map[string][]string // map[role][]permission
    S2SPublicKeyProvider   PublicKeyProvider   // 一个能根据服务名提供公钥的接口
}

// PublicKeyProvider 接口
type PublicKeyProvider interface {
    GetPublicKey(ctx context.Context, serviceName string) (crypto.PublicKey, error)
}


// --- Interceptors ---

// NewInterceptorsChain 创建一个包含所有必要认证授权拦截器的链
func NewInterceptorsChain(cfg Config, rpcPermissions map[string]string) grpc.ServerOption

// --- Context Utils ---

// UserFromContext 从上下文中提取认证后的用户信息
func UserFromContext(ctx context.Context) (*AuthenticatedUser, error)

// ServiceFromContext 从上下文中提取调用方服务信息
func ServiceFromContext(ctx context.Context) (*ServiceIdentity, error)

// ...
```

---

### 6. 上下文传播 (Context Propagation)

`pkg/auth`的核心职责之一是确保认证信息的安全、高效传播。

*   **数据结构**: `AuthenticatedUser`和`ServiceIdentity`将被存储在`context.Value`中。
*   **传播链**:
    1.  `api-gateway`验证初始请求，将用户信息注入gRPC请求。
    2.  下游服务的拦截器验证并重新确认用户信息，然后将其传递给业务逻辑。
    3.  如果业务逻辑需要调用更下游的服务，它将使用S2S客户端（该客户端会自动生成并附加S2S Token）进行调用，同时将用户的Trace ID等信息通过gRPC元数据继续传递。

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**:
    *   单个拦截器的平均处理开销 P99 < 1ms。
    *   JWKS的刷新和公钥的获取必须是异步和缓存的，绝不能阻塞请求处理路径。
*   **NFR7.2 (可靠性)**:
    *   JWKS端点或公钥服务不可用时，应拒绝所有需要验证的请求，并记录严重错误。
    *   拦截器本身不能产生panic，必须有完善的错误处理。
*   **NFR7.3 (可测试性)**:
    *   所有拦截器和验证逻辑必须有独立的单元测试。
    *   测试用例需覆盖：有效token、过期token、无效签名、错误claims、缺少token等所有场景。
    *   提供mock实现，方便业务服务在测试中模拟不同的认证用户和角色。

---

### 8. 技术约束与开发规范

*   **TC8.1 (依赖库)**:
    *   **JWT处理**: 推荐使用`golang-jwt/jwt/v5`，这是一个社区广泛接受的标准库。
    *   **JWKS处理**: 推荐使用`MicahParks/keyfunc`等库来简化JWKS的获取和缓存。
*   **TC8.2 (安全性)**:
    *   **算法安全**: 禁用对称JWT算法（如HS256）。强制使用RS256或更高强度的非对称算法。
    *   **时钟漂移**: 在验证`exp`和`nbf`时，必须允许一个小的、可配置的时钟漂移容差（如几秒钟）。
*   **TC8.3 (开发规范)**:
    *   所有对`pkg/auth`的修改都必须经过**安全团队或首席架构师的审查**。
    *   禁止在日志中打印任何token的完整内容或敏感的claims。
    *   必须提供清晰的文档和示例，指导其他开发者如何在他们的服务中正确使用这些拦截器。

---

这份SRS为`pkg/auth`的开发提供了坚实、安全、高性能的设计基础。它是保障CINA.CLUB整个微服务生态系统安全通信的基石，其实现质量至关重要。