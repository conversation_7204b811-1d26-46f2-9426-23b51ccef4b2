好的，遵照您的指示。我将为您生成`L4_Service_Design_Patterns/`目录下的全部三份核心文档的、详细的、生产级的内容。

这组文档是**微服务实现的战术指南**，旨在为所有后端开发人员提供一套统一的、经过验证的编码模式和最佳实践。遵循这些模式可以确保每个微服务的内部代码结构是**清晰的、可测试的、可维护的**，从而提升整个平台的工程质量。

---
### 文件1: `docs/architecture/L4_Service_Design_Patterns/01_clean_architecture_for_services.md`

```markdown
# 微服务设计模式: 整洁架构 (Clean Architecture)

**文档状态**: 已采纳 (Accepted)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27

## 1. 概述

为了确保CINA.CLUB的后端微服务具有高度的可维护性、可测试性和技术无关性，所有服务**必须**遵循**整洁架构 (Clean Architecture)** 或其变体（如六边形架构、洋葱架构）的核心原则。本文档定义了推荐的目录结构和各层职责，作为所有新服务创建和旧服务重构的“建筑规范”。

---

## 2. 核心原则：依赖关系规则

**整洁架构的黄金法则：依赖箭头永远指向内层。**

*   **内层 (领域核心)** 对外层（基础设施）一无所知。
*   外层（如数据库实现、gRPC Handler）依赖于内层（业务逻辑接口）。
*   这意味着，更换数据库、gRPC框架或任何外部技术，都**不应**需要修改核心的业务逻辑代码。

```mermaid
graph TD
    subgraph "外部世界 (Frameworks & Drivers)"
        A[gRPC Handlers<br/>(adapter/grpc)]
        B[Database Repositories<br/>(adapter/repository)]
        C[Event Consumers/Producers<br/>(adapter/event)]
        D[Third-party Clients<br/>(adapter/client)]
    end
    
    subgraph "应用层 (Application Business Rules)"
        E[Use Cases / Services<br/>(application/service)]
    end
    
    subgraph "领域层 (Enterprise Business Rules)"
        F[Domain Models & Value Objects<br/>(domain/model, domain/valueobject)]
        G[Repository & Service Interfaces<br/>(application/port)]
    end
    
    A -- "调用" --> E
    E -- "使用" --> G
    B -- "实现" --> G
    C -- "实现/调用" --> G
    E -- "操作" --> F
    
    style F fill:#ffb3b3,stroke:#333,stroke-width:2px
    style G fill:#ffb3b3,stroke:#333,stroke-width:2px
    style E fill:#b3d9ff,stroke:#333,stroke-width:2px
    style A fill:#b3ffb3,stroke:#333,stroke-width:2px
    style B fill:#b3ffb3,stroke:#333,stroke-width:2px
    style C fill:#b3ffb3,stroke:#333,stroke-width:2px
    style D fill:#b3ffb3,stroke:#333,stroke-width:2px

```
_图：整洁架构依赖关系图_

---

## 3. 推荐的目录结构 (`services/<service-name>/internal/`)

```
internal/
├── adapter/                # 适配层: 与外部世界交互
│   ├── grpc/               # gRPC服务器实现
│   │   ├── server.go       # gRPC服务注册和启动
│   │   └── user_handler.go # 实现了proto中定义的RPC
│   ├── event/              # 事件消费者/生产者
│   │   ├── consumer.go     # Kafka消费者实现
│   │   └── producer.go     # Kafka生产者实现
│   └── repository/         # 数据库仓库实现
│       ├── model.go        # 数据库实体模型 (带GORM/DB标签)
│       └── user_repo.go    # 实现了UserRepository接口
│
├── application/            # 应用层: 编排业务逻辑
│   ├── port/               # 端口 (Interfaces)
│   │   ├── user_repo.go    # 定义了UserRepository接口
│   │   └── user_service.go # 定义了UserService接口
│   └── service/            # 服务 (Use Cases)
│       └── user_service.go # UserService接口的实现
│
└── domain/                 # 领域层: 核心业务规则与模型
    ├── model/              # 领域模型
    │   └── user.go         # 纯粹的业务对象 (来自core/models)
    └── valueobject/        # 值对象
        └── email.go
```

### 3.1 `domain/` - 领域层 (The Core)

*   **职责**: 包含平台最核心、最稳定、与技术实现无关的业务规则和数据结构。
*   **内容**:
    *   `model/`: 直接使用或别名化`/core/models`中定义的`struct`。这是业务的核心实体。
    *   `valueobject/`: 包含具有业务含义但没有唯一标识的类型（如Email, Money）。
*   **规则**: **此层不应依赖任何其他层**。它对数据库、API框架、日志库一无所知。

### 3.2 `application/` - 应用层 (The Brain)

*   **职责**: 编排`domain`层的实体来完成具体的业务用例（Use Cases）。它定义了系统的“能做什么”。
*   **内容**:
    *   **`port/` (端口)**: **这是解耦的关键**。它定义了一系列**接口**，描述了应用层需要外部世界（`adapter`层）为其提供的能力。例如，`UserRepository`接口定义了“我需要一个能按ID获取用户的方法”，但它不关心这个方法是如何实现的（是从PostgreSQL还是从内存中获取）。
    *   **`service/` (服务)**: 对`port/`中定义的接口的具体实现。它包含了业务流程的编排逻辑，调用`UserRepository`接口来获取数据，然后对`Domain Model`执行操作。
*   **规则**: **此层只依赖`domain`层**。

### 3.3 `adapter/` - 适配层 (The Bridge)

*   **职责**: 作为应用层与外部技术和工具之间的“适配器”或“桥梁”。它负责将外部请求转换为对应用层的调用，并将应用层的输出转换为外部世界能理解的格式。
*   **内容**:
    *   **`grpc/`**: 实现了在`/core/api`中定义的gRPC `service`。它的handler会调用`application/service`中的方法来执行业务逻辑。
    *   **`repository/`**: 实现了`application/port`中定义的`Repository`接口。它负责与具体的数据库（如PostgreSQL）进行交互，执行SQL查询，并将数据库实体模型转换为领域模型。
    *   **`event/`**: 实现了事件的消费和发布。消费者会调用`application/service`，而`application/service`可能会调用生产者来发布新的事件。
*   **规则**: **此层依赖`application`层**（因为它需要实现`port`接口和调用`service`）。

