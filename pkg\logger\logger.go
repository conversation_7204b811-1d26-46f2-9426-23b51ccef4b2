/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-16
 * Modified: 2025-01-16
 */

package logger

import (
	"context"
	"log/slog"
	"os"

	"github.com/sirupsen/logrus"
)

// 包级别的全局logger，作为默认fallback
var globalLogger *slog.Logger

// 上下文键类型，用于在context中存储logger
type contextKey string

const (
	loggerContextKey contextKey = "logger"
)

// Logger interface defines the logging interface used by services
type Logger interface {
	Debug(ctx context.Context, msg string, args ...interface{})
	Info(ctx context.Context, msg string, args ...interface{})
	Warn(ctx context.Context, msg string, args ...interface{})
	Error(ctx context.Context, msg string, args ...interface{})
	GetSlogLogger() *slog.Logger
}

// slogWrapper wraps *slog.Logger to implement Logger interface
type slogWrapper struct {
	logger *slog.Logger
}

// NewLogger creates a new Logger instance that wraps slog.Logger
func NewLogger(cfg *Config) (Logger, error) {
	slogLogger, err := New(*cfg)
	if err != nil {
		return nil, err
	}
	return &slogWrapper{logger: slogLogger}, nil
}

// Debug implements Logger interface
func (w *slogWrapper) Debug(ctx context.Context, msg string, args ...interface{}) {
	w.logger.DebugContext(ctx, msg, args...)
}

// Info implements Logger interface
func (w *slogWrapper) Info(ctx context.Context, msg string, args ...interface{}) {
	w.logger.InfoContext(ctx, msg, args...)
}

// Warn implements Logger interface
func (w *slogWrapper) Warn(ctx context.Context, msg string, args ...interface{}) {
	w.logger.WarnContext(ctx, msg, args...)
}

// Error implements Logger interface
func (w *slogWrapper) Error(ctx context.Context, msg string, args ...interface{}) {
	w.logger.ErrorContext(ctx, msg, args...)
}

// GetSlogLogger returns the underlying *slog.Logger
func (w *slogWrapper) GetSlogLogger() *slog.Logger {
	return w.logger
}

// New 创建一个新的配置好的logger实例
// 这是整个服务生命周期中唯一一次调用的初始化函数
func New(cfg Config) (*slog.Logger, error) {
	// 验证配置
	if err := cfg.Validate(); err != nil {
		return nil, err
	}

	// 创建自定义handler
	handler := NewCinaLogHandler(&cfg)

	// 创建logger
	logger := slog.New(handler)

	// 设置为全局默认logger
	globalLogger = logger
	slog.SetDefault(logger)

	return logger, nil
}

// MustNew 创建logger，如果失败则panic
// 适用于应用启动阶段，配置错误应该立即暴露
func MustNew(cfg Config) *slog.Logger {
	logger, err := New(cfg)
	if err != nil {
		panic("failed to create logger: " + err.Error())
	}
	return logger
}

// NewTestLogger 创建一个用于测试的logger
// 将日志输出重定向到os.Stderr，便于测试中查看
func NewTestLogger() *slog.Logger {
	opts := &slog.HandlerOptions{
		Level:     slog.LevelDebug,
		AddSource: true,
	}
	handler := slog.NewTextHandler(os.Stderr, opts)
	return slog.New(handler)
}

// FromContext 从上下文中获取logger
// 如果上下文中没有logger，返回全局默认logger，确保永远不会返回nil
func FromContext(ctx context.Context) *slog.Logger {
	if ctx == nil {
		return getGlobalLogger()
	}

	if logger, ok := ctx.Value(loggerContextKey).(*slog.Logger); ok && logger != nil {
		return logger
	}

	return getGlobalLogger()
}

// ContextWithLogger 将logger注入到新的上下文中
func ContextWithLogger(ctx context.Context, logger *slog.Logger) context.Context {
	if ctx == nil {
		ctx = context.Background()
	}
	return context.WithValue(ctx, loggerContextKey, logger)
}

// ContextWithAttrs 创建一个新的上下文，其中包含附加了指定属性的logger
func ContextWithAttrs(ctx context.Context, args ...interface{}) context.Context {
	logger := FromContext(ctx)
	newLogger := logger.With(args...)
	return ContextWithLogger(ctx, newLogger)
}

// ContextWithGroup 创建一个新的上下文，其中包含指定分组的logger
func ContextWithGroup(ctx context.Context, name string) context.Context {
	logger := FromContext(ctx)
	newLogger := logger.WithGroup(name)
	return ContextWithLogger(ctx, newLogger)
}

// getGlobalLogger 获取全局logger，提供防御性fallback
func getGlobalLogger() *slog.Logger {
	if globalLogger != nil {
		return globalLogger
	}

	// 如果全局logger未初始化，创建一个基本的fallback logger
	return slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))
}

// === 快捷函数 ===
// 这些函数提供了便捷的日志记录接口，自动从上下文中获取logger

// Debug 记录调试级别日志
func Debug(ctx context.Context, msg string, args ...interface{}) {
	FromContext(ctx).DebugContext(ctx, msg, args...)
}

// Info 记录信息级别日志
func Info(ctx context.Context, msg string, args ...interface{}) {
	FromContext(ctx).InfoContext(ctx, msg, args...)
}

// Warn 记录警告级别日志
func Warn(ctx context.Context, msg string, args ...interface{}) {
	FromContext(ctx).WarnContext(ctx, msg, args...)
}

// Error 记录错误级别日志，特殊处理error类型
func Error(ctx context.Context, err error, msg string, args ...interface{}) {
	logger := FromContext(ctx)

	// 将error作为结构化字段添加，这样CinaLogHandler可以进行智能处理
	errorArgs := append([]interface{}{"error", err}, args...)
	logger.ErrorContext(ctx, msg, errorArgs...)
}

// ErrorMsg 记录纯错误消息，不包含error对象
func ErrorMsg(ctx context.Context, msg string, args ...interface{}) {
	FromContext(ctx).ErrorContext(ctx, msg, args...)
}

// With 创建一个带有指定属性的新logger并返回
func With(ctx context.Context, args ...interface{}) *slog.Logger {
	logger := FromContext(ctx)
	return logger.With(args...)
}

// WithGroup 创建一个带有指定分组的新logger并返回
func WithGroup(ctx context.Context, name string) *slog.Logger {
	return FromContext(ctx).WithGroup(name)
}

// SetGlobal 设置全局默认logger
// 通常在应用启动时调用一次
func SetGlobal(logger *slog.Logger) {
	globalLogger = logger
	slog.SetDefault(logger)
}

// GetGlobal 获取全局logger
func GetGlobal() *slog.Logger {
	return getGlobalLogger()
}

// Init initializes the logger with specified level and format
func Init(level, format string) {
	// Set log level
	switch level {
	case "debug":
		logrus.SetLevel(logrus.DebugLevel)
	case "info":
		logrus.SetLevel(logrus.InfoLevel)
	case "warn":
		logrus.SetLevel(logrus.WarnLevel)
	case "error":
		logrus.SetLevel(logrus.ErrorLevel)
	default:
		logrus.SetLevel(logrus.InfoLevel)
	}

	// Set log format
	switch format {
	case "json":
		logrus.SetFormatter(&logrus.JSONFormatter{})
	case "text":
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	default:
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}
}
