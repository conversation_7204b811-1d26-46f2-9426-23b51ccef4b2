// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-18
// Modified: 2025-06-18

package datasync

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
)

// VersionVector represents a logical clock for distributed systems
// It tracks the version/timestamp for each participant in the system
type VersionVector map[string]int64

// NewVersionVector creates a new empty version vector
func NewVersionVector() VersionVector {
	return make(VersionVector)
}

// NewVersionVectorFromMap creates a version vector from a map
func NewVersionVectorFromMap(m map[string]int64) VersionVector {
	vv := make(VersionVector)
	for k, v := range m {
		vv[k] = v
	}
	return vv
}

// <PERSON><PERSON> creates a deep copy of the version vector
func (vv VersionVector) Clone() VersionVector {
	clone := make(VersionVector)
	for k, v := range vv {
		clone[k] = v
	}
	return clone
}

// Increment increments the version for the given participant
func (vv VersionVector) Increment(participant string) {
	vv[participant]++
}

// Update updates the version for the given participant to the specified value
// Only updates if the new value is greater than the current value
func (vv VersionVector) Update(participant string, version int64) {
	if current, exists := vv[participant]; !exists || version > current {
		vv[participant] = version
	}
}

// Get returns the version for the given participant
func (vv VersionVector) Get(participant string) int64 {
	return vv[participant]
}

// Merge merges another version vector into this one
// Takes the maximum version for each participant
func (vv VersionVector) Merge(other VersionVector) {
	for participant, version := range other {
		if current, exists := vv[participant]; !exists || version > current {
			vv[participant] = version
		}
	}
}

// CompareResult represents the result of comparing two version vectors
type CompareResult int

const (
	// Concurrent means the version vectors are concurrent (neither is greater)
	Concurrent CompareResult = iota
	// Greater means this version vector is greater than the other
	Greater
	// Less means this version vector is less than the other
	Less
	// Equal means the version vectors are equal
	Equal
)

// String returns a string representation of the compare result
func (cr CompareResult) String() string {
	switch cr {
	case Concurrent:
		return "CONCURRENT"
	case Greater:
		return "GREATER"
	case Less:
		return "LESS"
	case Equal:
		return "EQUAL"
	default:
		return "UNKNOWN"
	}
}

// Compare compares this version vector with another
func (vv VersionVector) Compare(other VersionVector) CompareResult {
	if vv.Equals(other) {
		return Equal
	}

	allParticipants := make(map[string]bool)
	for p := range vv {
		allParticipants[p] = true
	}
	for p := range other {
		allParticipants[p] = true
	}

	thisGreater := false
	otherGreater := false

	for participant := range allParticipants {
		thisVersion := vv.Get(participant)
		otherVersion := other.Get(participant)

		if thisVersion > otherVersion {
			thisGreater = true
		} else if otherVersion > thisVersion {
			otherGreater = true
		}
	}

	if thisGreater && !otherGreater {
		return Greater
	}
	if otherGreater && !thisGreater {
		return Less
	}
	return Concurrent
}

// Equals checks if two version vectors are equal
func (vv VersionVector) Equals(other VersionVector) bool {
	if len(vv) != len(other) {
		return false
	}

	for participant, version := range vv {
		if other.Get(participant) != version {
			return false
		}
	}

	return true
}

// IsGreaterThan checks if this version vector is greater than another
func (vv VersionVector) IsGreaterThan(other VersionVector) bool {
	return vv.Compare(other) == Greater
}

// IsLessThan checks if this version vector is less than another
func (vv VersionVector) IsLessThan(other VersionVector) bool {
	return vv.Compare(other) == Less
}

// IsConcurrentWith checks if this version vector is concurrent with another
func (vv VersionVector) IsConcurrentWith(other VersionVector) bool {
	return vv.Compare(other) == Concurrent
}

// IsEmpty checks if the version vector has no entries
func (vv VersionVector) IsEmpty() bool {
	return len(vv) == 0
}

// Participants returns a sorted list of all participants
func (vv VersionVector) Participants() []string {
	participants := make([]string, 0, len(vv))
	for p := range vv {
		participants = append(participants, p)
	}
	sort.Strings(participants)
	return participants
}

// String returns a string representation of the version vector
func (vv VersionVector) String() string {
	if len(vv) == 0 {
		return "{}"
	}

	participants := vv.Participants()
	parts := make([]string, len(participants))
	for i, p := range participants {
		parts[i] = fmt.Sprintf("%s:%d", p, vv[p])
	}

	return "{" + strings.Join(parts, ", ") + "}"
}

// MarshalJSON implements json.Marshaler
func (vv VersionVector) MarshalJSON() ([]byte, error) {
	return json.Marshal(map[string]int64(vv))
}

// UnmarshalJSON implements json.Unmarshaler
func (vv *VersionVector) UnmarshalJSON(data []byte) error {
	var m map[string]int64
	if err := json.Unmarshal(data, &m); err != nil {
		return err
	}
	*vv = NewVersionVectorFromMap(m)
	return nil
}

// VersionVectorClock represents a logical clock using version vectors
type VersionVectorClock struct {
	participantID string
	vector        VersionVector
}

// NewVersionVectorClock creates a new version vector clock for the given participant
func NewVersionVectorClock(participantID string) *VersionVectorClock {
	return &VersionVectorClock{
		participantID: participantID,
		vector:        NewVersionVector(),
	}
}

// NewVersionVectorClockFromVector creates a version vector clock from an existing vector
func NewVersionVectorClockFromVector(participantID string, vector VersionVector) *VersionVectorClock {
	return &VersionVectorClock{
		participantID: participantID,
		vector:        vector.Clone(),
	}
}

// Tick increments the local logical time
func (vvc *VersionVectorClock) Tick() {
	vvc.vector.Increment(vvc.participantID)
}

// Update updates the clock with a received message's timestamp
func (vvc *VersionVectorClock) Update(receivedVector VersionVector) {
	// Merge the received vector
	vvc.vector.Merge(receivedVector)
	// Increment our own time
	vvc.Tick()
}

// GetVector returns a copy of the current version vector
func (vvc *VersionVectorClock) GetVector() VersionVector {
	return vvc.vector.Clone()
}

// GetTime returns the current logical time for this participant
func (vvc *VersionVectorClock) GetTime() int64 {
	return vvc.vector.Get(vvc.participantID)
}

// GetParticipantID returns the participant ID
func (vvc *VersionVectorClock) GetParticipantID() string {
	return vvc.participantID
}

// Compare compares this clock's vector with another vector
func (vvc *VersionVectorClock) Compare(other VersionVector) CompareResult {
	return vvc.vector.Compare(other)
}

// String returns a string representation of the clock
func (vvc *VersionVectorClock) String() string {
	return fmt.Sprintf("Clock[%s]: %s", vvc.participantID, vvc.vector.String())
}
