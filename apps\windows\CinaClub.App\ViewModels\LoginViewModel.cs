/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using CinaClub.Core.UseCases.Auth;
using CinaClub.App.Services;

namespace CinaClub.App.ViewModels;

/// <summary>
/// 登录页面的ViewModel
/// </summary>
public partial class LoginViewModel : ObservableObject
{
    private readonly ILogger<LoginViewModel> _logger;
    private readonly LoginUseCase _loginUseCase;
    private readonly INavigationService _navigationService;
    private readonly IDialogService _dialogService;

    #region 属性

    [ObservableProperty]
    private string _username = string.Empty;

    [ObservableProperty]
    private string _password = string.Empty;

    [ObservableProperty]
    private bool _rememberMe = false;

    [ObservableProperty]
    private bool _isLoggingIn = false;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    /// <summary>
    /// 是否可以登录（用户名和密码都不为空且不在登录中）
    /// </summary>
    public bool CanLogin => !string.IsNullOrWhiteSpace(Username) && 
                           !string.IsNullOrWhiteSpace(Password) && 
                           !IsLoggingIn;

    /// <summary>
    /// 是否不在登录中（用于UI显示）
    /// </summary>
    public bool IsNotLoggingIn => !IsLoggingIn;

    #endregion

    #region 命令

    /// <summary>
    /// 登录命令
    /// </summary>
    public ICommand LoginCommand { get; }

    /// <summary>
    /// 忘记密码命令
    /// </summary>
    public ICommand ForgotPasswordCommand { get; }

    /// <summary>
    /// 导航到注册页面命令
    /// </summary>
    public ICommand NavigateToRegisterCommand { get; }

    #endregion

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="loginUseCase">登录用例</param>
    /// <param name="navigationService">导航服务</param>
    /// <param name="dialogService">对话框服务</param>
    public LoginViewModel(
        ILogger<LoginViewModel> logger,
        LoginUseCase loginUseCase,
        INavigationService navigationService,
        IDialogService dialogService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _loginUseCase = loginUseCase ?? throw new ArgumentNullException(nameof(loginUseCase));
        _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
        _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

        // 初始化命令
        LoginCommand = new AsyncRelayCommand(LoginAsync, () => CanLogin);
        ForgotPasswordCommand = new RelayCommand(ForgotPassword);
        NavigateToRegisterCommand = new RelayCommand(NavigateToRegister);

        _logger.LogDebug("LoginViewModel已初始化");
    }

    /// <summary>
    /// 用户名或密码变化时更新登录按钮状态
    /// </summary>
    partial void OnUsernameChanged(string value)
    {
        OnPropertyChanged(nameof(CanLogin));
        OnPropertyChanged(nameof(IsNotLoggingIn));
        (LoginCommand as AsyncRelayCommand)?.NotifyCanExecuteChanged();
        ClearError();
    }

    /// <summary>
    /// 密码变化时更新登录按钮状态
    /// </summary>
    partial void OnPasswordChanged(string value)
    {
        OnPropertyChanged(nameof(CanLogin));
        OnPropertyChanged(nameof(IsNotLoggingIn));
        (LoginCommand as AsyncRelayCommand)?.NotifyCanExecuteChanged();
        ClearError();
    }

    /// <summary>
    /// 登录状态变化时更新UI
    /// </summary>
    partial void OnIsLoggingInChanged(bool value)
    {
        OnPropertyChanged(nameof(CanLogin));
        OnPropertyChanged(nameof(IsNotLoggingIn));
        (LoginCommand as AsyncRelayCommand)?.NotifyCanExecuteChanged();
    }

    /// <summary>
    /// 执行登录操作
    /// </summary>
    private async Task LoginAsync()
    {
        if (IsLoggingIn) return;

        try
        {
            _logger.LogInformation("开始登录，用户名: {Username}", Username);
            IsLoggingIn = true;
            ClearError();

            // 调用登录用例
            var loginRequest = new LoginRequest
            {
                Username = Username.Trim(),
                Password = Password,
                RememberMe = RememberMe
            };

            var result = await _loginUseCase.ExecuteAsync(loginRequest);

            if (result.IsSuccess)
            {
                _logger.LogInformation("登录成功，用户ID: {UserId}", result.User?.UserId);
                
                // 登录成功，导航到主页面
                _navigationService.NavigateTo<Views.ShellPage>();
                
                // 显示欢迎消息
                await _dialogService.ShowInfoAsync(
                    "登录成功", 
                    $"欢迎回来，{result.User?.GetDisplayName()}！"
                );
            }
            else
            {
                _logger.LogWarning("登录失败: {Error}", result.ErrorMessage);
                ErrorMessage = result.ErrorMessage ?? "登录失败，请检查用户名和密码";
                
                // 显示错误对话框
                await _dialogService.ShowErrorAsync("登录失败", ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登录过程中发生异常");
            ErrorMessage = "网络连接异常，请稍后重试";
            
            await _dialogService.ShowErrorAsync("网络异常", ErrorMessage);
        }
        finally
        {
            IsLoggingIn = false;
        }
    }

    /// <summary>
    /// 忘记密码
    /// </summary>
    private void ForgotPassword()
    {
        _logger.LogInformation("用户点击忘记密码");
        // TODO: 实现忘记密码功能
        _dialogService.ShowInfoAsync("提示", "忘记密码功能即将推出，请联系客服重置密码。");
    }

    /// <summary>
    /// 导航到注册页面
    /// </summary>
    private void NavigateToRegister()
    {
        _logger.LogInformation("导航到注册页面");
        // TODO: 实现注册页面导航
        _dialogService.ShowInfoAsync("提示", "注册功能即将推出，请使用现有账户登录。");
    }

    /// <summary>
    /// 清除错误消息
    /// </summary>
    private void ClearError()
    {
        if (!string.IsNullOrEmpty(ErrorMessage))
        {
            ErrorMessage = string.Empty;
        }
    }
}

/// <summary>
/// 登录请求模型
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// 用户名/邮箱/手机号
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 是否记住我
    /// </summary>
    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// 登录结果模型
/// </summary>
public class LoginResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public CinaClub.Core.Models.User? User { get; set; }

    /// <summary>
    /// 访问令牌
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string? RefreshToken { get; set; }
} 