/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import "errors"

// 配置相关错误
var (
	ErrInvalidBrokers = errors.New("invalid brokers configuration")
	ErrInvalidGroupID = errors.New("invalid group ID")
	ErrInvalidTopics  = errors.New("invalid topics configuration")
)

// 生产者相关错误
var (
	ErrProducerClosed    = errors.New("producer is closed")
	ErrSerializationFail = errors.New("message serialization failed")
	ErrPublishTimeout    = errors.New("publish timeout")
)

// 消费者相关错误
var (
	ErrConsumerClosed      = errors.New("consumer is closed")
	ErrDeserializationFail = errors.New("message deserialization failed")
	ErrHandlerPanic        = errors.New("handler panic")
	ErrUnknownEventType    = errors.New("unknown event type")
	ErrMaxRetriesExceeded  = errors.New("max retries exceeded")
)

// 追踪相关错误
var (
	ErrTraceContextExtraction = errors.New("trace context extraction failed")
	ErrTraceContextInjection  = errors.New("trace context injection failed")
)

// 重试相关错误
var (
	ErrRetryableError    = errors.New("retryable error")
	ErrNonRetryableError = errors.New("non-retryable error")
)

// IsRetryableError 检查错误是否可重试
func IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// 根据错误类型判断是否可重试
	switch {
	case errors.Is(err, ErrRetryableError):
		return true
	case errors.Is(err, ErrNonRetryableError):
		return false
	case errors.Is(err, ErrUnknownEventType):
		return false
	case errors.Is(err, ErrDeserializationFail):
		return false
	default:
		// 默认认为未知错误可重试
		return true
	}
}

// WrapRetryableError 包装可重试错误
func WrapRetryableError(err error) error {
	if err == nil {
		return nil
	}
	return &RetryableError{Err: err}
}

// WrapNonRetryableError 包装不可重试错误
func WrapNonRetryableError(err error) error {
	if err == nil {
		return nil
	}
	return &NonRetryableError{Err: err}
}

// RetryableError 可重试错误
type RetryableError struct {
	Err error
}

func (e *RetryableError) Error() string {
	return "retryable: " + e.Err.Error()
}

func (e *RetryableError) Unwrap() error {
	return e.Err
}

func (e *RetryableError) Is(target error) bool {
	return target == ErrRetryableError
}

// NonRetryableError 不可重试错误
type NonRetryableError struct {
	Err error
}

func (e *NonRetryableError) Error() string {
	return "non-retryable: " + e.Err.Error()
}

func (e *NonRetryableError) Unwrap() error {
	return e.Err
}

func (e *NonRetryableError) Is(target error) bool {
	return target == ErrNonRetryableError
}
