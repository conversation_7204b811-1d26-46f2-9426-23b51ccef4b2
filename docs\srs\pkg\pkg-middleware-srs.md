好的，遵照您的指示。我将为您生成一份专门针对CINA.CLUB Monorepo中 **`pkg/middleware`** 这个具体包的、极致细化的、生产级的设计规格说明书（SRS）。

这份文档将详细阐述`pkg/middleware`的职责、每个中间件的功能、组合方式和最佳实践，作为所有后端服务统一请求处理流程的权威设计蓝图。

---
### CINA.CLUB - 共享后端库 `pkg/middleware` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-26**  
**文档负责人:** [平台总架构师/首席工程师]  
**审批人:** [CTO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心设计与组合策略](#3-核心设计与组合策略)
4.  [功能需求 (按中间件拆分)](#4-功能需求-按中间件拆分)
    *   [4.1 Recovery 中间件](#41-recovery-中间件)
    *   [4.2 Tracing 中间件](#42-tracing-中间件)
    *   [4.3 Logging 中间件](#43-logging-中间件)
    *   [4.4 Metrics 中间件](#44-metrics-中间件)
    *   [4.5 Validation 中间件](#45-validation-中间件)
5.  [接口定义 (API Specification)](#5-接口定义-api-specification)
6.  [使用示例与最佳实践](#6-使用示例与最佳实践)
7.  [非功能性需求](#7-非功能性需求)
8.  [技术约束与开发规范](#8-技术约束与开发规范)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的微服务架构中，每个gRPC请求在到达业务逻辑之前和之后，都需要执行一系列通用的横切关注点操作，如Panic恢复、分布式追踪、日志记录、指标收集和参数验证。`pkg/middleware` 包的目的在于提供一套**标准化的、可组合的、高性能的gRPC服务器拦截器(Interceptor)**。通过将这些通用逻辑封装成可复用的中间件，可以确保所有后端服务的请求处理流程具有一致性、健壮性和可观测性，同时保持业务代码的纯粹和整洁。

#### 1.2. 范围与边界
*   **范围之内 (In-Scope)**:
    *   提供一系列gRPC `UnaryServerInterceptor` 和 `StreamServerInterceptor`。
    *   实现Panic恢复、分布式追踪上下文处理、请求/响应日志、Prometheus指标收集、请求体验证等功能。
    *   提供一个便捷的方式来将多个拦截器链接(chain)在一起。
*   **范围之外 (Out-of-Scope)**:
    *   **认证与授权逻辑**: 由`pkg/auth`中间件负责。
    *   **业务逻辑**: 任何与具体业务相关的逻辑。
    *   **gRPC服务器的实现**: 本包只提供拦截器，不负责创建或管理gRPC服务器本身。

#### 1.3. 目标用户
*   **CINA.CLUB所有后端Go微服务**的开发者。

---

### 2. 总体描述

#### 2.1. 在Monorepo中的定位
`pkg/middleware` 是位于`pkg/`目录下的一个核心基础设施库。它被所有`services/`中的微服务在初始化gRPC服务器时使用，用于构建其请求处理管道。

#### 2.2. 设计原则
*   **模块化与可组合性 (Modularity & Composability)**: 每个中间件都应是独立的，只负责一项功能。它们可以像乐高积木一样，以任意顺序组合成一个处理链。
*   **上下文驱动 (Context-Driven)**: 中间件之间主要通过`context.Context`来传递数据（如`trace_id`, `user_id`, 上下文Logger）。
*   **高性能**: 作为每个请求的必经路径，所有中间件的性能开销必须被严格控制。
*   **顺序敏感 (Order-Sensitive)**: 中间件的执行顺序非常重要。本包应提供清晰的文档和推荐的组合顺序。
*   **依赖注入**: 中间件所需的所有依赖（如Logger, Tracer）都应通过其构造函数显式注入，而不是使用全局变量。

---

### 3. 核心设计与组合策略

#### 3.1. 拦截器链 (Interceptor Chaining)
gRPC服务器只接受一个`UnaryInterceptor`和一个`StreamInterceptor`。为了使用多个中间件，`pkg/middleware`必须提供或推荐一个方式来将它们链接起来。推荐使用 `go-grpc-middleware/v2` 库，它提供了强大的`ChainUnaryServer`和`ChainStreamServer`功能。

#### 3.2. 推荐的组合顺序
拦截器的执行顺序遵循“洋葱模型”。请求从外层进入，逐层深入，到达业务handler后，再逐层返回。推荐的顺序如下（从外到内）：

1.  **Recovery**: 最外层，捕获任何下游中间件或业务逻辑产生的panic。
2.  **Tracing**: 第二层，为整个请求创建或提取Trace Span，并将Trace信息注入上下文，供所有内层使用。
3.  **Logging**: 第三层，在请求开始时注入带追踪信息的Logger到上下文，在请求结束时记录完整的请求日志。
4.  **Metrics**: 第四层，记录请求计数和延迟。
5.  **Auth (from `pkg/auth`)**: 第五层，进行认证和授权检查。
6.  **Validation**: 第六层，对请求的Protobuf消息进行结构化验证。
7.  **(业务Handler)**: 最内层，执行业务逻辑。

---

### 4. 功能需求 (按中间件拆分)

#### 4.1. Recovery 中间件
*   **FR4.1.1 (Panic捕获)**: 必须使用`defer`和`recover()`来捕获下游处理流程中发生的任何panic。
*   **FR4.1.2 (错误转换)**: 捕获panic后，必须将其转换为一个标准的`Internal` gRPC错误（使用`pkg/errors`）。绝不能让panic导致服务进程崩溃。
*   **FR4.1.3 (日志记录)**: 捕获panic时，必须使用`pkg/logger`记录一个`ERROR`级别的日志，并包含完整的堆栈跟踪信息。

#### 4.2. Tracing 中间件
*   **FR4.2.1 (上下文提取)**: 必须能从入站gRPC请求的元数据中，提取W3C Trace Context，并使用`otel.propagation`进行解析。
*   **FR4.2.2 (Span创建)**:
    *   如果提取到有效的Trace Context，则创建一个新的子Span。
    *   如果未提取到，则创建一个新的根Span（作为Trace的起点）。
*   **FR4.2.3 (Span属性)**: 新创建的Span必须自动添加标准的gRPC属性，如`rpc.system`, `rpc.service`, `rpc.method`, `net.peer.addr`等。
*   **FR4.2.4 (上下文注入)**: 必须将包含新Span的`context.Context`传递给下游。
*   **FR4.2.5 (Span结束)**: 在请求处理完成后，必须负责结束(end)该Span，并记录最终的状态（`OK`或`Error`）。

#### 4.3. Logging 中间件
*   **FR4.3.1 (上下文Logger创建)**:
    *   依赖`Tracing`中间件，从上下文中获取`trace_id`和`span_id`。
    *   依赖`Auth`中间件，从上下文中获取`user_id`（如果存在）。
    *   将这些追踪信息作为固定字段，创建一个新的上下文Logger实例。
    *   将这个新的Logger注入到`context.Context`中。
*   **FR4.3.2 (请求日志记录)**: 在请求处理完成后，记录一条包含完整信息的`INFO`级别日志。该日志必须包含：`method`, `duration_ms`, `status_code`, `peer_addr`等。
*   **FR4.3.3 (错误日志增强)**: 如果请求返回错误，日志级别应提升为`WARN`或`ERROR`（根据错误码），并记录错误的详细信息。

#### 4.4. Metrics 中间件
*   **FR4.4.1 (Prometheus指标)**: 必须收集并暴露标准的gRPC Prometheus指标。
*   **FR4.4.2 (指标类型)**:
    *   **请求计数器 (`grpc_server_handled_total`)**: 一个`CounterVec`，按`grpc_service`, `grpc_method`, `grpc_code`进行区分。
    *   **请求延迟直方图 (`grpc_server_handling_seconds`)**: 一个`HistogramVec`，按`grpc_service`, `grpc_method`进行区分。
*   **FR4.4.3 (初始化)**: 提供一个函数，用于在Prometheus的默认注册表中注册这些指标。

#### 4.5. Validation 中间件
*   **FR4.5.1 (自动验证)**: 必须能自动检查传入的gRPC请求消息（`req`）。
*   **FR4.5.2 (验证接口)**: 定义一个`Validator`接口，其中包含`Validate() error`方法。所有需要被验证的Protobuf消息生成的Go `struct`，都应实现此接口。
*   **FR4.5.3 (错误转换)**: 如果`Validate()`方法返回错误，中间件必须将其转换为一个`InvalidArgument` gRPC错误（使用`pkg/errors`），并立即中断请求，不再调用下游handler。

---

### 5. 接口定义 (API Specification)

```go
// pkg/middleware/chain.go

// ChainUnaryServer creates a single interceptor from a chain of interceptors.
// 推荐直接使用 go-grpc-middleware/v2 的实现。
// func ChainUnaryServer(interceptors ...grpc.UnaryServerInterceptor) grpc.UnaryServerInterceptor
// func ChainStreamServer(interceptors ...grpc.StreamServerInterceptor) grpc.StreamServerInterceptor


// pkg/middleware/recovery.go
func RecoveryInterceptor(logger *slog.Logger) grpc.UnaryServerInterceptor


// pkg/middleware/tracing.go
func TracingInterceptor(tracer trace.Tracer) grpc.UnaryServerInterceptor


// pkg/middleware/logging.go
func LoggingInterceptor(logger *slog.Logger) grpc.UnaryServerInterceptor


// pkg/middleware/metrics.go
type ServerMetrics struct { ... }
func NewServerMetrics() *ServerMetrics
func (m *ServerMetrics) UnaryServerInterceptor() grpc.UnaryServerInterceptor
func (m *ServerMetrics) Register(reg prometheus.Registerer) // 注册到Prometheus


// pkg/middleware/validation.go
type Validator interface {
	Validate() error
}
func ValidationInterceptor() grpc.UnaryServerInterceptor
```

---

### 6. 使用示例与最佳实践

#### 6.1. 在微服务中组装拦截器链
在每个服务的`cmd/server/main.go`中初始化gRPC服务器时：
```go
import (
    grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors"
    // ...
)

// ...
// 1. 初始化所有依赖
logger := logger.NewLogger(...)
tracer := tracing.InitTracerProvider(...).Tracer(...)
serverMetrics := middleware.NewServerMetrics()
serverMetrics.Register(prometheus.DefaultRegisterer)

authConfig := auth.Config{ ... }
authInterceptors := auth.NewInterceptors(authConfig, ...)

// 2. 按推荐顺序组装拦截器链
server := grpc.NewServer(
    grpc.ChainUnaryInterceptor(
        middleware.RecoveryInterceptor(logger),
        middleware.TracingInterceptor(tracer),
        middleware.LoggingInterceptor(logger),
        serverMetrics.UnaryServerInterceptor(),
        authInterceptors.UnaryAuthInterceptor(), // 假设pkg/auth提供了一个组合好的认证拦截器
        middleware.ValidationInterceptor(),
    ),
    // ... Stream interceptors chain
)

// 3. 注册服务并启动
pb.RegisterMyServiceServer(server, myServiceImpl)
server.Serve(lis)
```

---

### 7. 非功能性需求

*   **NFR7.1 (性能)**: 所有中间件的总性能开销 P99 < 5ms。必须避免在请求路径上进行任何阻塞I/O或重量级计算。
*   **NFR7.2 (可靠性)**: 中间件本身绝不能引入新的错误或panic。必须是线程安全的，并能处理各种畸形的请求元数据。
*   **NFR7.3 (可测试性)**: 每个中间件都必须有独立的单元测试。测试应覆盖拦截和不拦截、成功和失败等多种场景。

---

### 8. 技术约束与开发规范

*   **TC8.1 (核心依赖)**:
    *   **gRPC**: `google.golang.org/grpc`
    *   **Interceptor Chaining**: `github.com/grpc-ecosystem/go-grpc-middleware/v2`
    *   **Tracing**: `go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc`
    *   **Metrics**: `github.com/prometheus/client_golang`
*   **TC8.2 (开发规范)**:
    *   **强制使用**: 所有微服务**必须**使用`pkg/middleware`提供的标准拦截器链。
    *   **顺序规范**: 必须遵循**第3.2节**中推荐的拦截器组合顺序，以保证依赖关系的正确性（如Logging依赖Tracing）。
    *   **上下文不变性**: 中间件在修改`context.Context`时，应创建新的上下文，而不是修改传入的上下文，以符合Go的最佳实践。

---
这份SRS为`pkg/middleware`库的设计和实现提供了坚实、全面的指导。通过为所有后端服务提供一个标准化的、可观测的、健壮的请求处理管道，它极大地提升了整个CINA.CLUB平台的开发效率、稳定性和可维护性。