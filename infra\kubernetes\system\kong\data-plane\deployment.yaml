# CINA.CLUB Platform - Kong Proxy (Data Plane)
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Kong Proxy Deployment (Data Plane)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kong-proxy
  namespace: kong-system
  labels:
    app: kong-proxy
    component: data-plane
    tier: platform-infrastructure
    app.kubernetes.io/name: kong-proxy
    app.kubernetes.io/component: data-plane
    app.kubernetes.io/part-of: kong-gateway
    app.kubernetes.io/managed-by: platform-engineering
  annotations:
    description: "Kong Proxy - high-performance data plane for API Gateway traffic"
spec:
  replicas: 3  # High availability with minimum 3 replicas
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0  # Ensure zero downtime during updates
  
  selector:
    matchLabels:
      app: kong-proxy
      component: data-plane
  
  template:
    metadata:
      labels:
        app: kong-proxy
        component: data-plane
        tier: platform-infrastructure
        version: "3.4"
      annotations:
        # Prometheus scraping configuration
        prometheus.io/scrape: "true"
        prometheus.io/port: "8100"
        prometheus.io/path: "/metrics"
        # Configuration checksum for rolling updates
        checksum/config: "placeholder-for-gitops"
    
    spec:
      # Security context for pod
      securityContext:
        runAsNonRoot: true
        runAsUser: 100  # Kong user
        runAsGroup: 101  # Kong group
        fsGroup: 101
        seccompProfile:
          type: RuntimeDefault
      
      # Anti-affinity for high availability
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app: kong-proxy
                    component: data-plane
                topologyKey: kubernetes.io/hostname
            - weight: 50
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app: kong-proxy
                    component: data-plane
                topologyKey: topology.kubernetes.io/zone
      
      # Node selection preferences
      nodeSelector:
        kubernetes.io/os: linux
      
      # Containers
      containers:
        - name: kong-proxy
          image: kong:3.4.2-alpine
          imagePullPolicy: IfNotPresent
          
          # Security context for container
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
              add:
                - NET_BIND_SERVICE  # Required for binding to port 80/443
          
          # Environment variables for Kong configuration
          env:
            # Core Kong configuration
            - name: KONG_DATABASE
              value: "off"  # DB-less mode for cloud-native deployment
            - name: KONG_ROLE
              value: "data_plane"
            
            # Proxy listeners
            - name: KONG_PROXY_LISTEN
              value: "0.0.0.0:8000 reuseport backlog=16384, 0.0.0.0:8443 http2 ssl reuseport backlog=16384"
            - name: KONG_ADMIN_LISTEN
              value: "127.0.0.1:8001"  # Admin API only on localhost for security
            - name: KONG_STATUS_LISTEN
              value: "0.0.0.0:8100"  # Status endpoint for health checks and metrics
            
            # Performance tuning
            - name: KONG_WORKER_PROCESSES
              value: "auto"  # Automatically detect CPU cores
            - name: KONG_WORKER_CONNECTIONS
              value: "1024"
            - name: KONG_NGINX_WORKER_PROCESSES
              value: "auto"
            - name: KONG_UPSTREAM_KEEPALIVE_POOL_SIZE
              value: "60"
            - name: KONG_UPSTREAM_KEEPALIVE_MAX_REQUESTS
              value: "100"
            - name: KONG_UPSTREAM_KEEPALIVE_IDLE_TIMEOUT
              value: "60"
            
            # Memory and connection settings
            - name: KONG_MEM_CACHE_SIZE
              value: "512m"
            - name: KONG_PROXY_BUFFER_SIZE
              value: "8k"
            - name: KONG_CLIENT_BODY_BUFFER_SIZE
              value: "8k"
            
            # Logging configuration
            - name: KONG_LOG_LEVEL
              value: "notice"
            - name: KONG_ACCESS_LOG
              value: "/dev/stdout"
            - name: KONG_ERROR_LOG
              value: "/dev/stderr"
            - name: KONG_PROXY_ACCESS_LOG
              value: "/dev/stdout"
            - name: KONG_PROXY_ERROR_LOG
              value: "/dev/stderr"
            
            # Security settings
            - name: KONG_REAL_IP_HEADER
              value: "X-Forwarded-For"
            - name: KONG_REAL_IP_RECURSIVE
              value: "on"
            - name: KONG_TRUSTED_IPS
              value: "0.0.0.0/0,::/0"  # Trust all IPs (behind cloud load balancer)
            
            # Plugin configuration
            - name: KONG_PLUGINS
              value: "bundled"  # Enable all bundled plugins
            - name: KONG_PLUGINSERVER_NAMES
              value: ""  # No external plugin servers
            
            # Headers and timeouts
            - name: KONG_CLIENT_MAX_BODY_SIZE
              value: "10m"  # 10MB max request body
            - name: KONG_CLIENT_BODY_TIMEOUT
              value: "60s"
            - name: KONG_CLIENT_HEADER_TIMEOUT
              value: "60s"
            - name: KONG_SEND_TIMEOUT
              value: "60s"
            
            # Lua settings
            - name: KONG_LUA_SHARED_DICT
              value: "prometheus_metrics 5m"
            - name: KONG_LUA_PACKAGE_PATH
              value: "/opt/?.lua;/opt/?/init.lua;;"
            
            # Admin API security (disabled external access)
            - name: KONG_ADMIN_ACCESS_LOG
              value: "off"
            - name: KONG_ADMIN_ERROR_LOG
              value: "off"
          
          # Ports
          ports:
            - name: proxy
              containerPort: 8000
              protocol: TCP
            - name: proxy-ssl
              containerPort: 8443
              protocol: TCP
            - name: admin
              containerPort: 8001
              protocol: TCP
            - name: status
              containerPort: 8100
              protocol: TCP
          
          # Volume mounts
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: kong-prefix
              mountPath: /kong_prefix
            - name: kong-tmp
              mountPath: /kong_tmp
          
          # Resource requirements
          resources:
            requests:
              cpu: "500m"      # Minimum CPU for production traffic
              memory: "1Gi"    # Minimum memory for caching and buffers
            limits:
              cpu: "2"         # Maximum CPU per pod
              memory: "2Gi"    # Maximum memory per pod
          
          # Health checks
          livenessProbe:
            httpGet:
              path: /status
              port: 8100
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /status/ready
              port: 8100
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          
          # Startup probe for initial configuration loading
          startupProbe:
            httpGet:
              path: /status
              port: 8100
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 30
      
      # Volumes
      volumes:
        - name: tmp
          emptyDir:
            sizeLimit: 1Gi
        - name: kong-prefix
          emptyDir:
            sizeLimit: 1Gi
        - name: kong-tmp
          emptyDir:
            sizeLimit: 1Gi
      
      # Termination grace period for graceful shutdown
      terminationGracePeriodSeconds: 30
      
      # DNS configuration optimized for Kubernetes
      dnsPolicy: ClusterFirst
      dnsConfig:
        options:
          - name: ndots
            value: "2"
          - name: edns0

---
# Horizontal Pod Autoscaler for Kong Proxy
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: kong-proxy-hpa
  namespace: kong-system
  labels:
    app: kong-proxy
    component: data-plane
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: kong-proxy
  
  minReplicas: 3   # Minimum replicas for high availability
  maxReplicas: 20  # Maximum replicas for high traffic
  
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes stabilization
      policies:
        - type: Percent
          value: 25  # Scale down by 25% at most
          periodSeconds: 60
        - type: Pods
          value: 2   # Scale down by 2 pods at most
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60   # 1 minute stabilization
      policies:
        - type: Percent
          value: 100  # Scale up by 100% at most
          periodSeconds: 15
        - type: Pods
          value: 4    # Scale up by 4 pods at most
          periodSeconds: 15
  
  metrics:
    # CPU utilization
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    
    # Memory utilization
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80

---
# Pod Disruption Budget for Kong Proxy
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: kong-proxy-pdb
  namespace: kong-system
  labels:
    app: kong-proxy
    component: data-plane
spec:
  selector:
    matchLabels:
      app: kong-proxy
      component: data-plane
  
  minAvailable: 2  # Always keep at least 2 pods running during disruptions 