# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB Redis 开发环境配置

# 基础配置
port 6379
bind 0.0.0.0
protected-mode no

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 启用 AOF
appendonly yes
appendfsync everysec

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 日志配置
loglevel notice
logfile ""

# 客户端配置
timeout 300
tcp-keepalive 300

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 数据库数量
databases 16

# 禁用危险命令（开发环境可以保留）
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command DEBUG ""

# 启用键空间通知
notify-keyspace-events Ex 