# Copyright (c) 2025 Cina.Club, All rights reserved
# Created: 2025-06-27, Modified: 2025-06-27

output "id" {
  description = "The RDS instance ID"
  value       = aws_db_instance.main.id
}

output "arn" {
  description = "The ARN of the RDS instance"
  value       = aws_db_instance.main.arn
}

output "endpoint" {
  description = "The RDS instance endpoint"
  value       = aws_db_instance.main.endpoint
}

output "port" {
  description = "The RDS instance port"
  value       = aws_db_instance.main.port
}

output "database_name" {
  description = "The database name"
  value       = aws_db_instance.main.db_name
}

output "username" {
  description = "The master username for the database"
  value       = aws_db_instance.main.username
  sensitive   = true
}

output "security_group_id" {
  description = "The ID of the security group"
  value       = aws_security_group.rds.id
}

output "subnet_group_name" {
  description = "The db subnet group name"
  value       = aws_db_subnet_group.main.name
}

output "parameter_group_name" {
  description = "The db parameter group name"
  value       = aws_db_parameter_group.main.name
}

output "secret_arn" {
  description = "The ARN of the secret containing the database password"
  value       = aws_secretsmanager_secret.db_password.arn
}

output "secret_name" {
  description = "The name of the secret containing the database password"
  value       = aws_secretsmanager_secret.db_password.name
} 