/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-20 12:00:00
 * Modified: 2025-01-20 12:00:00
 */

package errors

import (
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// ToGRPCStatus 将AppError转换为gRPC Status
func ToGRPCStatus(err error) *status.Status {
	if err == nil {
		return status.New(codes.OK, "")
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		// 将AppError转换为gRPC Status
		grpcCode := appErr.Code.ToGRPCCode()
		st := status.New(grpcCode, appErr.Message)

		// 注意：在这个简化版本中，我们不使用details
		// 在生产版本中，应该使用生成的protobuf代码
		return st
	}

	// 如果不是AppError，将其视为Internal错误
	st := status.New(codes.Internal, err.Error())
	return st
}

// FromGRPCError 将gRPC错误转换为AppError
func FromGRPCError(err error) *AppError {
	if err == nil {
		return nil
	}

	// 尝试从gRPC错误中提取status
	st, ok := status.FromError(err)
	if !ok {
		// 如果不是gRPC错误，包装为Internal错误
		return Wrap(err, Internal, "non-grpc error")
	}

	// 从gRPC状态码映射到平台错误码
	errorCode := FromGRPCCode(st.Code())
	message := st.Message()

	// 注意：在这个简化版本中，我们不解析details
	// 在生产版本中，应该从details中恢复完整的AppError

	// 根据gRPC状态码创建基本的AppError
	return &AppError{
		Code:     errorCode,
		Message:  message,
		Metadata: make(map[string]string),
		stack:    newStack(1),
	}
}

// ToGRPCError 将错误转换为gRPC错误
func ToGRPCError(err error) error {
	if err == nil {
		return nil
	}

	return ToGRPCStatus(err).Err()
}

// IsGRPCError 检查错误是否为gRPC错误
func IsGRPCError(err error) bool {
	if err == nil {
		return false
	}

	_, ok := status.FromError(err)
	return ok
}

// GetGRPCCode 从错误中获取gRPC状态码
func GetGRPCCode(err error) codes.Code {
	if err == nil {
		return codes.OK
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Code.ToGRPCCode()
	}

	if st, ok := status.FromError(err); ok {
		return st.Code()
	}

	return codes.Internal
}
