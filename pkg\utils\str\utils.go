/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package str

import (
	"fmt"
	"strings"
	"unicode/utf8"
)

// Truncate truncates a string to the specified maximum length.
// If the string is longer than maxLength, it adds the ellipsis suffix.
// The ellipsis is included in the total length calculation.
//
// Example:
//
//	str.Truncate("Hello, World!", 10, "...")  // returns "Hello, ..."
//	str.Truncate("Hello", 10, "...")          // returns "Hello"
//	str.Truncate("", 10, "...")               // returns ""
func Truncate(s string, maxLength int, ellipsis string) string {
	if maxLength <= 0 {
		return ""
	}
	
	// Count runes, not bytes, for proper Unicode support
	runeCount := utf8.RuneCountInString(s)
	ellipsisRuneCount := utf8.RuneCountInString(ellipsis)
	
	if runeCount <= maxLength {
		return s
	}
	
	if ellipsisRuneCount >= maxLength {
		// If ellipsis is longer than maxLength, return truncated ellipsis
		return string([]rune(ellipsis)[:maxLength])
	}
	
	// Truncate string and add ellipsis
	targetLength := maxLength - ellipsisRuneCount
	runes := []rune(s)
	return string(runes[:targetLength]) + ellipsis
}

// TruncateBytes truncates a string to the specified maximum byte length.
// This is useful when you need to limit the byte size of a string (e.g., for database fields).
//
// Example:
//
//	str.TruncateBytes("Hello, World!", 10, "...")  // truncates by bytes, not runes
func TruncateBytes(s string, maxBytes int, ellipsis string) string {
	if maxBytes <= 0 {
		return ""
	}
	
	if len(s) <= maxBytes {
		return s
	}
	
	ellipsisBytes := len(ellipsis)
	if ellipsisBytes >= maxBytes {
		// If ellipsis is longer than maxBytes, return truncated ellipsis
		return ellipsis[:maxBytes]
	}
	
	// Truncate string and add ellipsis, ensuring we don't break UTF-8 sequences
	targetBytes := maxBytes - ellipsisBytes
	
	// Find the largest valid UTF-8 prefix that fits in targetBytes
	for targetBytes > 0 {
		if utf8.ValidString(s[:targetBytes]) {
			return s[:targetBytes] + ellipsis
		}
		targetBytes--
	}
	
	return ellipsis
}

// Pad pads a string to the specified length with the given pad string.
// If the string is already longer than the target length, it returns the original string.
//
// Example:
//
//	str.Pad("hello", 10, " ")     // returns "hello     "
//	str.Pad("hello", 10, "0")     // returns "hello00000"
//	str.Pad("hello", 3, " ")      // returns "hello"
func Pad(s string, length int, padStr string) string {
	if length <= len(s) || padStr == "" {
		return s
	}
	
	needed := length - len(s)
	padRepeats := needed/len(padStr) + 1
	padding := strings.Repeat(padStr, padRepeats)[:needed]
	
	return s + padding
}

// PadLeft pads a string to the specified length with the given pad string on the left.
//
// Example:
//
//	str.PadLeft("hello", 10, " ")     // returns "     hello"
//	str.PadLeft("hello", 10, "0")     // returns "00000hello"
func PadLeft(s string, length int, padStr string) string {
	if length <= len(s) || padStr == "" {
		return s
	}
	
	needed := length - len(s)
	padRepeats := needed/len(padStr) + 1
	padding := strings.Repeat(padStr, padRepeats)[:needed]
	
	return padding + s
}

// PadCenter pads a string to the specified length with the given pad string, centering the original string.
//
// Example:
//
//	str.PadCenter("hello", 10, " ")   // returns "  hello   "
//	str.PadCenter("hello", 11, " ")   // returns "   hello   "
func PadCenter(s string, length int, padStr string) string {
	if length <= len(s) || padStr == "" {
		return s
	}
	
	needed := length - len(s)
	leftPad := needed / 2
	rightPad := needed - leftPad
	
	leftPadRepeats := leftPad/len(padStr) + 1
	rightPadRepeats := rightPad/len(padStr) + 1
	
	leftPadding := strings.Repeat(padStr, leftPadRepeats)[:leftPad]
	rightPadding := strings.Repeat(padStr, rightPadRepeats)[:rightPad]
	
	return leftPadding + s + rightPadding
}

// Reverse reverses a string, properly handling Unicode characters.
//
// Example:
//
//	str.Reverse("hello")      // returns "olleh"
//	str.Reverse("你好世界")    // returns "界世好你"
func Reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// IsEmpty checks if a string is empty or contains only whitespace.
//
// Example:
//
//	str.IsEmpty("")           // returns true
//	str.IsEmpty("   ")        // returns true
//	str.IsEmpty("hello")      // returns false
func IsEmpty(s string) bool {
	return strings.TrimSpace(s) == ""
}

// IsBlank is an alias for IsEmpty for compatibility with other libraries.
func IsBlank(s string) bool {
	return IsEmpty(s)
}

// IsNotEmpty checks if a string is not empty and contains non-whitespace characters.
func IsNotEmpty(s string) bool {
	return !IsEmpty(s)
}

// DefaultIfEmpty returns the default value if the string is empty, otherwise returns the original string.
//
// Example:
//
//	str.DefaultIfEmpty("", "default")     // returns "default"
//	str.DefaultIfEmpty("hello", "default") // returns "hello"
//	str.DefaultIfEmpty("   ", "default")  // returns "default"
func DefaultIfEmpty(s, defaultValue string) string {
	if IsEmpty(s) {
		return defaultValue
	}
	return s
}

// RenderTemplate performs simple template substitution using {key} placeholders.
// This is a basic implementation for simple use cases.
//
// Example:
//
//	data := map[string]interface{}{
//	    "name": "John",
//	    "age":  30,
//	}
//	result := str.RenderTemplate("Hello {name}, you are {age} years old!", data)
//	// returns "Hello John, you are 30 years old!"
func RenderTemplate(template string, data map[string]interface{}) string {
	result := template
	for key, value := range data {
		placeholder := "{" + key + "}"
		replacement := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, replacement)
	}
	return result
}

// Contains checks if a string contains a substring (case-sensitive).
//
// Example:
//
//	str.Contains("Hello World", "World")  // returns true
//	str.Contains("Hello World", "world")  // returns false
func Contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

// ContainsIgnoreCase checks if a string contains a substring (case-insensitive).
//
// Example:
//
//	str.ContainsIgnoreCase("Hello World", "WORLD")  // returns true
//	str.ContainsIgnoreCase("Hello World", "world")  // returns true
func ContainsIgnoreCase(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// ContainsAny checks if a string contains any of the specified substrings.
//
// Example:
//
//	str.ContainsAny("Hello World", []string{"foo", "World"})  // returns true
//	str.ContainsAny("Hello World", []string{"foo", "bar"})    // returns false
func ContainsAny(s string, substrs []string) bool {
	for _, substr := range substrs {
		if strings.Contains(s, substr) {
			return true
		}
	}
	return false
}

// ContainsAll checks if a string contains all of the specified substrings.
//
// Example:
//
//	str.ContainsAll("Hello World", []string{"Hello", "World"})  // returns true
//	str.ContainsAll("Hello World", []string{"Hello", "foo"})    // returns false
func ContainsAll(s string, substrs []string) bool {
	for _, substr := range substrs {
		if !strings.Contains(s, substr) {
			return false
		}
	}
	return true
}

// Lines splits a string into lines, handling different line ending formats.
//
// Example:
//
//	str.Lines("line1\nline2\r\nline3")  // returns ["line1", "line2", "line3"]
func Lines(s string) []string {
	// Replace Windows line endings with Unix line endings
	s = strings.ReplaceAll(s, "\r\n", "\n")
	// Replace old Mac line endings with Unix line endings
	s = strings.ReplaceAll(s, "\r", "\n")
	// Split by Unix line endings
	return strings.Split(s, "\n")
}

// Words splits a string into words, handling multiple whitespace characters.
//
// Example:
//
//	str.Words("  hello   world  ")  // returns ["hello", "world"]
func Words(s string) []string {
	return strings.Fields(s)
}

// WordCount returns the number of words in a string.
//
// Example:
//
//	str.WordCount("hello world")      // returns 2
//	str.WordCount("  hello   world  ") // returns 2
func WordCount(s string) int {
	return len(Words(s))
} 