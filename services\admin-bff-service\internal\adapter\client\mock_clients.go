/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package client

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"cina.club/services/admin-bff-service/internal/application/port"
)

// BillingClient billing service client
type BillingClient struct {
	conn   *grpc.ClientConn
	logger *logrus.Entry
}

// NewBillingClient creates a billing service client
func NewBillingClient(conn *grpc.ClientConn, logger *logrus.Entry) *BillingClient {
	return &BillingClient{conn: conn, logger: logger}
}

// GetUserSubscriptions gets user subscription information
func (c *BillingClient) GetUserSubscriptions(ctx context.Context, userID string) (*BillingData, error) {
	c.logger.WithField("user_id", userID).Debug("Getting user subscriptions")

	return &BillingData{
		UserID:           userID,
		IsPremium:        true,
		PremiumStartAt:   timePtr(time.Now().Add(-30 * 24 * time.Hour)),
		PremiumExpireAt:  timePtr(time.Now().Add(30 * 24 * time.Hour)),
		SubscriptionPlan: "premium_monthly",
		CoinBalance:      1500.50,
		TotalSpent:       2500.00,
		TotalEarned:      500.00,
		UnpaidInvoices:   0,
	}, nil
}

// SocialClient social service client
type SocialClient struct {
	conn   *grpc.ClientConn
	logger *logrus.Entry
}

// NewSocialClient creates a social service client
func NewSocialClient(conn *grpc.ClientConn, logger *logrus.Entry) *SocialClient {
	return &SocialClient{conn: conn, logger: logger}
}

// GetUserSocialStats gets user social statistics
func (c *SocialClient) GetUserSocialStats(ctx context.Context, userID string) (*SocialData, error) {
	c.logger.WithField("user_id", userID).Debug("Getting user social stats")

	return &SocialData{
		UserID:         userID,
		FollowersCount: 1250,
		FollowingCount: 876,
		PostsCount:     450,
		LikesCount:     12500,
	}, nil
}

// ContentModerationClient content moderation service client
type ContentModerationClient struct {
	conn   *grpc.ClientConn
	logger *logrus.Entry
}

// NewContentModerationClient creates a content moderation service client
func NewContentModerationClient(conn *grpc.ClientConn, logger *logrus.Entry) *ContentModerationClient {
	return &ContentModerationClient{conn: conn, logger: logger}
}

// GetModerationQueue gets moderation queue
func (c *ContentModerationClient) GetModerationQueue(ctx context.Context, filter port.ModerationFilter) (*ModerationData, error) {
	c.logger.Debug("Getting moderation queue")

	tasks := make([]*ModerationTaskData, 0)
	for i := 0; i < filter.PageSize && i < 5; i++ {
		tasks = append(tasks, &ModerationTaskData{
			ID:          fmt.Sprintf("task_%d", i+1),
			ContentID:   fmt.Sprintf("content_%d", i+1),
			ContentType: "post",
			Title:       fmt.Sprintf("Content Title %d", i+1),
			AuthorID:    fmt.Sprintf("author_%d", i+1),
			AuthorName:  fmt.Sprintf("Author %d", i+1),
			Status:      "pending",
			Priority:    "medium",
			ReportCount: i + 1,
			CreatedAt:   time.Now().Add(-time.Duration(i+1) * time.Hour),
		})
	}

	return &ModerationData{
		Tasks: tasks,
		Total: 25,
	}, nil
}

// ServiceOfferingClient service offering client
type ServiceOfferingClient struct {
	conn   *grpc.ClientConn
	logger *logrus.Entry
}

// NewServiceOfferingClient creates a service offering client
func NewServiceOfferingClient(conn *grpc.ClientConn, logger *logrus.Entry) *ServiceOfferingClient {
	return &ServiceOfferingClient{conn: conn, logger: logger}
}

// GetOrders gets order list
func (c *ServiceOfferingClient) GetOrders(ctx context.Context, filter port.OrderFilter) (*OrdersData, error) {
	c.logger.Debug("Getting orders")

	orders := make([]*OrderSummaryData, 0)
	for i := 0; i < filter.PageSize && i < 10; i++ {
		paidAt := time.Now().Add(-time.Duration(i) * time.Hour)
		orders = append(orders, &OrderSummaryData{
			ID:          fmt.Sprintf("order_%d", i+1),
			UserID:      fmt.Sprintf("user_%d", i+1),
			UserName:    fmt.Sprintf("User %d", i+1),
			ServiceID:   fmt.Sprintf("service_%d", i+1),
			ServiceName: fmt.Sprintf("Service %d", i+1),
			Status:      "paid",
			Amount:      100.0 + float64(i*10),
			Currency:    "USD",
			CreatedAt:   time.Now().Add(-time.Duration(i+2) * time.Hour),
			PaidAt:      &paidAt,
		})
	}

	return &OrdersData{
		Orders: orders,
		Total:  150,
	}, nil
}

// AnalyticsClient analytics service client
type AnalyticsClient struct {
	conn   *grpc.ClientConn
	logger *logrus.Entry
}

// NewAnalyticsClient creates an analytics service client
func NewAnalyticsClient(conn *grpc.ClientConn, logger *logrus.Entry) *AnalyticsClient {
	return &AnalyticsClient{conn: conn, logger: logger}
}

// GetDashboardSummary gets dashboard summary
func (c *AnalyticsClient) GetDashboardSummary(ctx context.Context) (*DashboardData, error) {
	c.logger.Debug("Getting dashboard summary")

	return &DashboardData{
		TotalUsers:        50000,
		ActiveUsers:       12000,
		NewUsersToday:     150,
		PremiumUsers:      8000,
		TotalContent:      150000,
		PendingModeration: 25,
		PublishedToday:    1200,
		TotalRevenue:      250000.00,
		RevenueToday:      2500.00,
		PendingPayments:   5000.00,
		SystemHealth:      "healthy",
		ActiveSessions:    1500,
		ErrorRate:         0.01,
	}, nil
}

// NotificationDispatchClient notification dispatch service client
type NotificationDispatchClient struct {
	conn   *grpc.ClientConn
	logger *logrus.Entry
}

// NewNotificationDispatchClient creates a notification dispatch service client
func NewNotificationDispatchClient(conn *grpc.ClientConn, logger *logrus.Entry) *NotificationDispatchClient {
	return &NotificationDispatchClient{conn: conn, logger: logger}
}

// Data transfer object definitions

// BillingData billing data
type BillingData struct {
	UserID           string     `json:"user_id"`
	IsPremium        bool       `json:"is_premium"`
	PremiumStartAt   *time.Time `json:"premium_start_at"`
	PremiumExpireAt  *time.Time `json:"premium_expire_at"`
	SubscriptionPlan string     `json:"subscription_plan"`
	CoinBalance      float64    `json:"coin_balance"`
	TotalSpent       float64    `json:"total_spent"`
	TotalEarned      float64    `json:"total_earned"`
	UnpaidInvoices   int        `json:"unpaid_invoices"`
}

// SocialData social data
type SocialData struct {
	UserID         string `json:"user_id"`
	FollowersCount int    `json:"followers_count"`
	FollowingCount int    `json:"following_count"`
	PostsCount     int    `json:"posts_count"`
	LikesCount     int    `json:"likes_count"`
}

// ModerationData moderation data
type ModerationData struct {
	Tasks []*ModerationTaskData `json:"tasks"`
	Total int64                 `json:"total"`
}

// ModerationTaskData moderation task data
type ModerationTaskData struct {
	ID          string     `json:"id"`
	ContentID   string     `json:"content_id"`
	ContentType string     `json:"content_type"`
	Title       string     `json:"title"`
	AuthorID    string     `json:"author_id"`
	AuthorName  string     `json:"author_name"`
	Status      string     `json:"status"`
	Priority    string     `json:"priority"`
	ReportCount int        `json:"report_count"`
	CreatedAt   time.Time  `json:"created_at"`
	AssignedTo  string     `json:"assigned_to"`
	ReviewedAt  *time.Time `json:"reviewed_at"`
	ReviewedBy  string     `json:"reviewed_by"`
}

// OrdersData orders data
type OrdersData struct {
	Orders []*OrderSummaryData `json:"orders"`
	Total  int64               `json:"total"`
}

// OrderSummaryData order summary data
type OrderSummaryData struct {
	ID          string     `json:"id"`
	UserID      string     `json:"user_id"`
	UserName    string     `json:"user_name"`
	ServiceID   string     `json:"service_id"`
	ServiceName string     `json:"service_name"`
	Status      string     `json:"status"`
	Amount      float64    `json:"amount"`
	Currency    string     `json:"currency"`
	CreatedAt   time.Time  `json:"created_at"`
	PaidAt      *time.Time `json:"paid_at"`
}

// DashboardData dashboard data
type DashboardData struct {
	TotalUsers        int64   `json:"total_users"`
	ActiveUsers       int64   `json:"active_users"`
	NewUsersToday     int64   `json:"new_users_today"`
	PremiumUsers      int64   `json:"premium_users"`
	TotalContent      int64   `json:"total_content"`
	PendingModeration int64   `json:"pending_moderation"`
	PublishedToday    int64   `json:"published_today"`
	TotalRevenue      float64 `json:"total_revenue"`
	RevenueToday      float64 `json:"revenue_today"`
	PendingPayments   float64 `json:"pending_payments"`
	SystemHealth      string  `json:"system_health"`
	ActiveSessions    int64   `json:"active_sessions"`
	ErrorRate         float64 `json:"error_rate"`
}
