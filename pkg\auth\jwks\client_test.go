/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 12:00:00
Modified: 2025-01-21 12:00:00
*/

package jwks

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/time/rate"
	"gopkg.in/square/go-jose.v2"
)

// generateTestJWKS generates a test JWKS with one RSA key
func generateTestJWKS() (*jose.JSONWebKeySet, *rsa.PrivateKey, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, nil, err
	}

	jwk := jose.JSONWebKey{
		Key:       &privateKey.PublicKey,
		KeyID:     "test-key-1",
		Algorithm: "RS256",
		Use:       "sig",
	}

	jwks := &jose.JSONWebKeySet{
		Keys: []jose.JSONWebKey{jwk},
	}

	return jwks, privateKey, nil
}

// createMockJWKSServer creates a test HTTP server that serves JWKS
func createMockJWKSServer(jwks *jose.JSONWebKeySet, statusCode int, delay time.Duration) *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if delay > 0 {
			time.Sleep(delay)
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(statusCode)

		if statusCode == http.StatusOK && jwks != nil {
			json.NewEncoder(w).Encode(jwks)
		} else {
			json.NewEncoder(w).Encode(map[string]string{"error": "test error"})
		}
	}))
}

func TestNewClient(t *testing.T) {
	jwksURL := "https://example.com/jwks"
	client := NewClient(jwksURL)

	if client == nil {
		t.Fatal("NewClient returned nil")
	}

	if client.jwksURL != jwksURL {
		t.Errorf("Expected JWKS URL %s, got %s", jwksURL, client.jwksURL)
	}

	if client.cache == nil {
		t.Error("Client cache is nil")
	}

	if client.refresher == nil {
		t.Error("Client refresher is nil")
	}

	if client.httpClient == nil {
		t.Error("Client HTTP client is nil")
	}
}

func TestNewClientWithOptions(t *testing.T) {
	jwksURL := "https://example.com/jwks"
	cacheTTL := 2 * time.Hour
	refreshLimit := rate.Every(5 * time.Second)
	timeout := 10 * time.Second

	client := NewClientWithOptions(jwksURL, cacheTTL, refreshLimit, timeout)

	if client == nil {
		t.Fatal("NewClientWithOptions returned nil")
	}

	if client.jwksURL != jwksURL {
		t.Errorf("Expected JWKS URL %s, got %s", jwksURL, client.jwksURL)
	}

	if client.httpClient.Timeout != timeout {
		t.Errorf("Expected timeout %v, got %v", timeout, client.httpClient.Timeout)
	}
}

func TestClient_GetKey_Success(t *testing.T) {
	jwks, privateKey, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	server := createMockJWKSServer(jwks, http.StatusOK, 0)
	defer server.Close()

	client := NewClient(server.URL)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// Get the key
	key, err := client.GetKey(token)
	if err != nil {
		t.Fatalf("GetKey failed: %v", err)
	}

	if key == nil {
		t.Fatal("Expected key, got nil")
	}

	// Verify it's the correct key
	rsaKey, ok := key.(*rsa.PublicKey)
	if !ok {
		t.Fatal("Expected RSA public key")
	}

	if rsaKey.N.Cmp(privateKey.PublicKey.N) != 0 {
		t.Error("Retrieved key doesn't match expected key")
	}
}

func TestClient_GetKey_MissingKid(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	server := createMockJWKSServer(jwks, http.StatusOK, 0)
	defer server.Close()

	client := NewClient(server.URL)

	// Create a test JWT token without kid
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})

	// Get the key should fail
	_, err = client.GetKey(token)
	if err == nil {
		t.Error("Expected error for token without kid")
	}

	if err.Error() != "token missing kid claim" {
		t.Errorf("Expected specific error message, got: %v", err)
	}
}

func TestClient_GetKey_KeyNotFound(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	server := createMockJWKSServer(jwks, http.StatusOK, 0)
	defer server.Close()

	client := NewClient(server.URL)

	// Create a test JWT token with non-existent kid
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "non-existent-key"

	// Get the key should fail
	_, err = client.GetKey(token)
	if err == nil {
		t.Error("Expected error for non-existent key")
	}

	errStr := fmt.Sprintf("%v", err)
	if !strings.Contains(errStr, "key not found in JWKS") {
		t.Errorf("Expected key not found error, got: %v", err)
	}
}

func TestClient_GetKey_ServerError(t *testing.T) {
	server := createMockJWKSServer(nil, http.StatusInternalServerError, 0)
	defer server.Close()

	client := NewClient(server.URL)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// Get the key should fail
	_, err := client.GetKey(token)
	if err == nil {
		t.Error("Expected error for server error")
	}
}

func TestClient_GetKey_NetworkError(t *testing.T) {
	// Use a non-existent URL to simulate network error
	client := NewClient("http://localhost:99999/jwks")

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// Get the key should fail
	_, err := client.GetKey(token)
	if err == nil {
		t.Error("Expected error for network error")
	}
}

func TestClient_Caching(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(jwks)
	}))
	defer server.Close()

	client := NewClient(server.URL)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// First request should hit the server
	_, err = client.GetKey(token)
	if err != nil {
		t.Fatalf("First GetKey failed: %v", err)
	}

	if requestCount != 1 {
		t.Errorf("Expected 1 server request, got %d", requestCount)
	}

	// Second request should use cache
	_, err = client.GetKey(token)
	if err != nil {
		t.Fatalf("Second GetKey failed: %v", err)
	}

	if requestCount != 1 {
		t.Errorf("Expected 1 server request (cached), got %d", requestCount)
	}
}

func TestClient_RateLimit(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(jwks)
	}))
	defer server.Close()

	// Create client with very restrictive rate limit
	client := NewClientWithOptions(server.URL, 1*time.Minute, rate.Every(1*time.Hour), 30*time.Second)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// First request should succeed
	_, err = client.GetKey(token)
	if err != nil {
		t.Fatalf("First GetKey failed: %v", err)
	}

	// Clear cache to force refresh
	client.ClearCache()

	// Second request should be rate limited, but should still work if we have stale cache
	_, err = client.GetKey(token)
	// The behavior depends on implementation - it might succeed with stale cache or fail
	// We just verify it doesn't panic
	if err != nil {
		t.Logf("Second request failed (expected due to rate limiting): %v", err)
	}
}

func TestClient_Preload(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	server := createMockJWKSServer(jwks, http.StatusOK, 0)
	defer server.Close()

	client := NewClient(server.URL)
	ctx := context.Background()

	// Preload should succeed
	err = client.Preload(ctx)
	if err != nil {
		t.Errorf("Preload failed: %v", err)
	}

	// After preload, GetKey should use cached data
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	_, err = client.GetKey(token)
	if err != nil {
		t.Errorf("GetKey after preload failed: %v", err)
	}
}

func TestClient_PreloadError(t *testing.T) {
	server := createMockJWKSServer(nil, http.StatusInternalServerError, 0)
	defer server.Close()

	client := NewClient(server.URL)
	ctx := context.Background()

	// Preload should fail
	err := client.Preload(ctx)
	if err == nil {
		t.Error("Expected preload to fail with server error")
	}
}

func TestClient_ClearCache(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(jwks)
	}))
	defer server.Close()

	// Create client with relaxed rate limiting for this test
	client := NewClientWithOptions(server.URL, 1*time.Hour, rate.Every(1*time.Millisecond), 30*time.Second)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// First request
	_, err = client.GetKey(token)
	if err != nil {
		t.Fatalf("First GetKey failed: %v", err)
	}

	if requestCount != 1 {
		t.Errorf("Expected 1 server request, got %d", requestCount)
	}

	// Clear cache
	client.ClearCache()

	// Next request should hit server again
	_, err = client.GetKey(token)
	if err != nil {
		t.Fatalf("GetKey after cache clear failed: %v", err)
	}

	if requestCount != 2 {
		t.Errorf("Expected 2 server requests after cache clear, got %d", requestCount)
	}
}

func TestClient_KeyFunc(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	server := createMockJWKSServer(jwks, http.StatusOK, 0)
	defer server.Close()

	client := NewClient(server.URL)
	keyFunc := client.KeyFunc()

	if keyFunc == nil {
		t.Fatal("KeyFunc returned nil")
	}

	// Test that KeyFunc works like GetKey
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	key, err := keyFunc(token)
	if err != nil {
		t.Fatalf("KeyFunc failed: %v", err)
	}

	if key == nil {
		t.Fatal("KeyFunc returned nil key")
	}
}

func TestClient_Timeout(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	// Server with long delay
	server := createMockJWKSServer(jwks, http.StatusOK, 2*time.Second)
	defer server.Close()

	// Client with short timeout
	client := NewClientWithOptions(server.URL, 1*time.Hour, rate.Every(1*time.Second), 500*time.Millisecond)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// Request should timeout
	_, err = client.GetKey(token)
	if err == nil {
		t.Error("Expected timeout error")
	}
}

func TestClient_StaleCache(t *testing.T) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		t.Fatalf("Failed to generate test JWKS: %v", err)
	}

	requestCount := 0
	serverDown := false

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		if serverDown {
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(jwks)
	}))
	defer server.Close()

	client := NewClient(server.URL)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// First request should succeed and populate cache
	_, err = client.GetKey(token)
	if err != nil {
		t.Fatalf("First GetKey failed: %v", err)
	}

	// Simulate server going down
	serverDown = true

	// Clear the regular cache to force a refresh attempt
	client.ClearCache()

	// This request should fail to refresh but potentially succeed with stale cache
	// The exact behavior depends on implementation details
	_, err = client.GetKey(token)
	// We don't assert on the error since the behavior with stale cache may vary
	t.Logf("Request with server down resulted in: %v", err)
}

// Benchmark tests
func BenchmarkClient_GetKey(b *testing.B) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		b.Fatalf("Failed to generate test JWKS: %v", err)
	}

	server := createMockJWKSServer(jwks, http.StatusOK, 0)
	defer server.Close()

	client := NewClient(server.URL)

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	// Populate cache first
	_, err = client.GetKey(token)
	if err != nil {
		b.Fatalf("Initial GetKey failed: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := client.GetKey(token)
		if err != nil {
			b.Fatalf("GetKey failed: %v", err)
		}
	}
}

func BenchmarkClient_GetKeyColdCache(b *testing.B) {
	jwks, _, err := generateTestJWKS()
	if err != nil {
		b.Fatalf("Failed to generate test JWKS: %v", err)
	}

	server := createMockJWKSServer(jwks, http.StatusOK, 0)
	defer server.Close()

	// Create a test JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-issuer",
		"sub": "test-subject",
		"exp": time.Now().Add(time.Hour).Unix(),
	})
	token.Header["kid"] = "test-key-1"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		client := NewClient(server.URL)
		_, err := client.GetKey(token)
		if err != nil {
			b.Fatalf("GetKey failed: %v", err)
		}
	}
}
