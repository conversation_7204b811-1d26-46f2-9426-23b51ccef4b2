/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 18:30:00
 * Modified: 2025-01-23 18:30:00
 */

import { describe, it, expect } from 'vitest';
import { UserSchema, formatZodErrors } from './validation';
import { UserRole, UserStatus } from '@/types/user';

describe('Validation Schemas and Utilities', () => {
  describe('UserSchema', () => {
    const validUserData = {
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      roles: [UserRole.ADMIN],
      status: UserStatus.ACTIVE,
    };

    it('should validate a correct user object', () => {
      const result = UserSchema.safeParse(validUserData);
      expect(result.success).toBe(true);
    });

    it('should fail validation for an invalid email', () => {
      const invalidData = { ...validUserData, email: 'not-an-email' };
      const result = UserSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should fail validation if username is too short', () => {
      const invalidData = { ...validUserData, username: 'a' };
      const result = UserSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      // You can also check the error message
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at least 3 character');
      }
    });

    it('should fail if required fields are missing', () => {
        const invalidData = { ...validUserData, email: undefined };
        const result = UserSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
      });
  });

  describe('formatZodErrors', () => {
    it('should format Zod errors into a simple key-value object', () => {
      const invalidData = { email: 'invalid', username: 'a' };
      const result = UserSchema.safeParse(invalidData);

      if (!result.success) {
        const formattedErrors = formatZodErrors(result.error);
        expect(formattedErrors).toHaveProperty('email');
        expect(formattedErrors).toHaveProperty('username');
        expect(formattedErrors.email).toBe('Invalid email format');
        expect(formattedErrors.username).toContain('at least 3 character');
      }
    });

    it('should return an empty object for no errors', () => {
        const validResult = { success: true, data: {} };
        const formattedErrors = formatZodErrors(validResult as any);
        expect(Object.keys(formattedErrors).length).toBe(0);
    });
  });
}); 