# Simple Import Path Fix Script

Write-Host "Starting import path fixes..." -ForegroundColor Green

$fixedFiles = 0
$services = Get-ChildItem -Path "services" -Directory

foreach ($service in $services) {
    $serviceName = $service.Name
    Write-Host "Processing: $serviceName" -ForegroundColor Yellow
    
    $goFiles = Get-ChildItem -Path $service.FullName -Filter "*.go" -Recurse
    
    foreach ($goFile in $goFiles) {
        try {
            $content = Get-Content $goFile.FullName -Raw
            if ([string]::IsNullOrWhiteSpace($content)) { continue }
            
            $original = $content
            
            # Fix common patterns
            $content = $content -replace "$serviceName/pkg/", "cina.club/pkg/"
            $content = $content -replace "short-video-service/pkg/", "cina.club/pkg/"
            $content = $content -replace "social-service/pkg/", "cina.club/pkg/"
            
            if ($content -ne $original) {
                $utf8 = New-Object System.Text.UTF8Encoding $false
                [System.IO.File]::WriteAllText($goFile.FullName, $content, $utf8)
                $fixedFiles++
                Write-Host "  Fixed: $($goFile.Name)" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "  Error: $($goFile.Name)" -ForegroundColor Red
        }
    }
}

Write-Host "Fixed $fixedFiles files" -ForegroundColor Cyan
Write-Host "Run: .\scripts\simple-batch-test.ps1 to test results" -ForegroundColor Yellow 