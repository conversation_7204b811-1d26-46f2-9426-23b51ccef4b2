package crypto

import (
	"crypto/rand"
	"fmt"
	"log"
	"math"
	"sync"

	"cina.club/core/di"
)

// CryptoServiceConfig holds configuration for crypto services
type CryptoServiceConfig struct {
	// Maximum number of key rotations allowed
	MaxKeyRotations int
	// Minimum key length in bytes
	MinKeyLength int
	// Enable additional security features
	EnhancedSecurity bool
}

// CryptoServiceRegistrar manages crypto service registration and initialization
type CryptoServiceRegistrar struct {
	e2eeEngine      *E2EEEngine
	keyVault        *KeyVault
	config          CryptoServiceConfig
	initializeMutex sync.Mutex
	isInitialized   bool
}

// KeyVault manages cryptographic keys with enhanced security
type KeyVault struct {
	keys         map[string][]byte
	keyRotations map[string]int
	mu           sync.RWMutex
	maxRotations int
}

// NewKeyVault creates a new key vault
func NewKeyVault(maxRotations int) *KeyVault {
	return &KeyVault{
		keys:         make(map[string][]byte),
		keyRotations: make(map[string]int),
		maxRotations: maxRotations,
	}
}

// Store<PERSON>ey adds a key to the vault with rotation tracking
func (kv *KeyVault) StoreKey(keyID string, key []byte) error {
	kv.mu.Lock()
	defer kv.mu.Unlock()

	// Check key rotation limit
	if rotations, exists := kv.keyRotations[keyID]; exists {
		if rotations >= kv.maxRotations {
			return fmt.Errorf("maximum key rotations exceeded for key %s", keyID)
		}
		kv.keyRotations[keyID]++
	} else {
		kv.keyRotations[keyID] = 1
	}

	// Securely store key
	kv.keys[keyID] = make([]byte, len(key))
	copy(kv.keys[keyID], key)
	return nil
}

// RetrieveKey gets a key from the vault
func (kv *KeyVault) RetrieveKey(keyID string) ([]byte, bool) {
	kv.mu.RLock()
	defer kv.mu.RUnlock()

	key, exists := kv.keys[keyID]
	if !exists {
		return nil, false
	}

	// Return a copy to prevent direct modification
	safeKey := make([]byte, len(key))
	copy(safeKey, key)
	return safeKey, true
}

// NewCryptoServiceRegistrar creates a new crypto service registrar
func NewCryptoServiceRegistrar(config CryptoServiceConfig) *CryptoServiceRegistrar {
	return &CryptoServiceRegistrar{
		e2eeEngine: NewE2EEEngine(),
		config:     config,
		keyVault:   NewKeyVault(config.MaxKeyRotations),
	}
}

// Register implements the CryptoServiceRegistrar interface
func (r *CryptoServiceRegistrar) Register(container *di.Container) error {
	r.initializeMutex.Lock()
	defer r.initializeMutex.Unlock()

	// Register E2EE Engine
	if err := container.Register(r.e2eeEngine); err != nil {
		return fmt.Errorf("failed to register E2EE engine: %w", err)
	}

	// Register Key Vault
	if err := container.Register(r.keyVault); err != nil {
		return fmt.Errorf("failed to register key vault: %w", err)
	}

	return nil
}

// InitializeCryptoProviders sets up initial cryptographic resources
func (r *CryptoServiceRegistrar) InitializeCryptoProviders() error {
	r.initializeMutex.Lock()
	defer r.initializeMutex.Unlock()

	if r.isInitialized {
		return nil
	}

	// Generate initial master key
	masterKey, err := r.e2eeEngine.GenerateKey()
	if err != nil {
		return fmt.Errorf("failed to generate master key: %w", err)
	}

	// Store master key in vault
	masterKeyID := r.e2eeEngine.deriveKeyIDFromBytes(masterKey)
	if err := r.keyVault.StoreKey(masterKeyID, masterKey); err != nil {
		return fmt.Errorf("failed to store master key: %w", err)
	}

	// Perform additional security checks if enabled
	if r.config.EnhancedSecurity {
		if err := r.performSecurityChecks(); err != nil {
			return fmt.Errorf("security checks failed: %w", err)
		}
	}

	// Securely wipe the master key from memory
	SecureWipe(masterKey)

	r.isInitialized = true
	return nil
}

// performSecurityChecks runs additional security validations
func (r *CryptoServiceRegistrar) performSecurityChecks() error {
	// Check entropy of random number generator
	randomnessTest := make([]byte, 1024)
	if _, err := rand.Read(randomnessTest); err != nil {
		return fmt.Errorf("cryptographically secure random number generation failed: %w", err)
	}

	// Check for potential weak keys or predictable patterns
	if err := r.checkRandomnessQuality(randomnessTest); err != nil {
		return fmt.Errorf("randomness quality check failed: %w", err)
	}

	return nil
}

// checkRandomnessQuality performs statistical tests on random data
func (r *CryptoServiceRegistrar) checkRandomnessQuality(data []byte) error {
	// Simple entropy check
	entropy := calculateEntropy(data)
	if entropy < 7.9 { // Close to ideal entropy of 8 for truly random data
		return fmt.Errorf("insufficient entropy in random data: %f", entropy)
	}

	return nil
}

// calculateEntropy computes Shannon entropy of a byte slice
func calculateEntropy(data []byte) float64 {
	frequencies := make(map[byte]int)
	for _, b := range data {
		frequencies[b]++
	}

	var entropy float64
	for _, count := range frequencies {
		prob := float64(count) / float64(len(data))
		entropy -= prob * (math.Log2(prob))
	}

	return entropy
}

// Example usage and testing function
func ExampleCryptoServiceRegistration() {
	// Create configuration
	config := CryptoServiceConfig{
		MaxKeyRotations:  3,
		MinKeyLength:     32,
		EnhancedSecurity: true,
	}

	// Create service registrar
	registrar := NewCryptoServiceRegistrar(config)

	// Register services
	err := registrar.Register(di.GlobalContainer)
	if err != nil {
		log.Fatalf("Failed to register crypto services: %v", err)
	}

	// Initialize crypto providers
	err = registrar.InitializeCryptoProviders()
	if err != nil {
		log.Fatalf("Failed to initialize crypto providers: %v", err)
	}
}
