/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 17:00:00
 * Modified: 2025-01-23 17:00:00
 */

import React, { useState } from 'react';
import {
  Card,
  Table,
  Space,
  Button,
  Input,
  Select,
  DatePicker,
  Tag,
  Typography,
  Modal,
  Form,
  Tooltip,
  Drawer,
  Descriptions,
  Alert,
  Row,
  Col,
  Statistic,
  List,
  Avatar,
  Divider,
  Badge,
  Steps,
  Progress,
  Popconfirm,
  message,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  EyeOutlined,
  StopOutlined,
  EditOutlined,
  SyncOutlined,
  PlusOutlined,
  BarChartOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Line, Column, Pie } from '@ant-design/charts';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface Subscription {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  plan: {
    id: string;
    name: 'Basic' | 'Professional' | 'Enterprise';
    price: number;
    interval: 'month' | 'year';
  };
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'unpaid';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  trialEnd?: string;
  createdAt: string;
  cancelAtPeriodEnd: boolean;
  metadata: {
    source: string;
    utm_campaign?: string;
  };
}

interface SubscriptionAnalytics {
  totalSubscriptions: number;
  mrr: number;
  arr: number;
  churnRate: number;
  newSubscriptions: number;
  cancellations: number;
  activeTrials: number;
  conversionRate: number;
}

const Subscriptions: React.FC = () => {
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);
  const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
  const [isChangePlanModalVisible, setIsChangePlanModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    plan: '',
    status: '',
    dateRange: null as any,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // Mock data queries
  const { data: subscriptions = [], isLoading } = useQuery({
    queryKey: ['subscriptions', filters, pagination.current, pagination.pageSize],
    queryFn: async (): Promise<{ data: Subscription[]; total: number }> => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockSubscriptions: Subscription[] = [
        {
          id: 'sub_1',
          user: {
            id: 'user_1',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Alice',
          },
          plan: {
            id: 'plan_prof',
            name: 'Professional',
            price: 49.99,
            interval: 'month',
          },
          status: 'active',
          currentPeriodStart: '2025-01-15T00:00:00Z',
          currentPeriodEnd: '2025-02-15T00:00:00Z',
          createdAt: '2024-10-15T00:00:00Z',
          cancelAtPeriodEnd: false,
          metadata: { source: 'web' },
        },
        {
          id: 'sub_2',
          user: {
            id: 'user_2',
            name: 'Bob Smith',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Bob',
          },
          plan: {
            id: 'plan_ent',
            name: 'Enterprise',
            price: 499.99,
            interval: 'year',
          },
          status: 'trialing',
          currentPeriodStart: '2025-01-20T00:00:00Z',
          currentPeriodEnd: '2025-02-03T00:00:00Z',
          trialEnd: '2025-02-03T00:00:00Z',
          createdAt: '2025-01-20T00:00:00Z',
          cancelAtPeriodEnd: false,
          metadata: { source: 'sales', utm_campaign: 'q1_outreach' },
        },
        {
          id: 'sub_3',
          user: {
            id: 'user_3',
            name: 'Charlie Brown',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Charlie',
          },
          plan: {
            id: 'plan_basic',
            name: 'Basic',
            price: 9.99,
            interval: 'month',
          },
          status: 'canceled',
          currentPeriodStart: '2024-12-10T00:00:00Z',
          currentPeriodEnd: '2025-01-10T00:00:00Z',
          createdAt: '2024-06-10T00:00:00Z',
          cancelAtPeriodEnd: true,
          metadata: { source: 'mobile' },
        },
      ];
      return { data: mockSubscriptions, total: 42 };
    },
  });

  const { data: analyticsData } = useQuery({
    queryKey: ['subscription-analytics', filters.dateRange],
    queryFn: async (): Promise<SubscriptionAnalytics> => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        totalSubscriptions: 12456,
        mrr: 156789.45,
        arr: 1881473.4,
        churnRate: 2.3,
        newSubscriptions: 1234,
        cancellations: 456,
        activeTrials: 789,
        conversionRate: 65.7,
      };
    },
  });

  // Mutations
  const cancelSubscriptionMutation = useMutation({
    mutationFn: async (subscriptionId: string) => {
      await new Promise(resolve => setTimeout(resolve, 1500));
      return { success: true, subscriptionId };
    },
    onSuccess: () => {
      message.success('Subscription scheduled for cancellation.');
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
    },
    onError: () => {
      message.error('Failed to cancel subscription.');
    },
  });

  const changePlanMutation = useMutation({
    mutationFn: async ({ subscriptionId, newPlanId }: { subscriptionId: string; newPlanId: string }) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      return { success: true, subscriptionId, newPlanId };
    },
    onSuccess: () => {
      message.success('Subscription plan changed successfully.');
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      setIsChangePlanModalVisible(false);
    },
    onError: () => {
      message.error('Failed to change subscription plan.');
    },
  });

  const handleViewDetails = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setIsDetailDrawerVisible(true);
  };

  const handleChangePlan = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    form.setFieldsValue({ newPlanId: subscription.plan.id });
    setIsChangePlanModalVisible(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'trialing':
        return 'blue';
      case 'past_due':
      case 'unpaid':
        return 'orange';
      case 'canceled':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns: ColumnsType<Subscription> = [
    {
      title: 'User',
      key: 'user',
      width: 250,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.user.avatar} icon={<UserOutlined />} size="small" style={{ marginRight: '8px' }} />
          <div>
            <Text strong>{record.user.name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.user.email}</Text>
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
          {record.cancelAtPeriodEnd && status !== 'canceled' && (
            <Tooltip title={`Cancels on ${new Date(record.currentPeriodEnd).toLocaleDateString()}`}>
              <ClockCircleOutlined style={{ marginLeft: '4px' }} />
            </Tooltip>
          )}
        </Tag>
      ),
    },
    {
      title: 'Plan',
      key: 'plan',
      width: 150,
      render: (_, record) => (
        <div>
          <Text strong>{record.plan.name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ${record.plan.price}/{record.plan.interval}
          </Text>
        </div>
      ),
    },
    {
      title: 'Current Period',
      key: 'period',
      width: 200,
      render: (_, record) => (
        <div>
          {new Date(record.currentPeriodStart).toLocaleDateString()} - {new Date(record.currentPeriodEnd).toLocaleDateString()}
          {record.status === 'trialing' && (
            <Tooltip title={`Trial ends on ${new Date(record.trialEnd!).toLocaleDateString()}`}>
              <Badge status="processing" style={{ marginLeft: '8px' }} />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button type="text" icon={<EyeOutlined />} size="small" onClick={() => handleViewDetails(record)} />
          </Tooltip>
          {record.status === 'active' && !record.cancelAtPeriodEnd && (
            <>
              <Tooltip title="Change Plan">
                <Button type="text" icon={<EditOutlined />} size="small" onClick={() => handleChangePlan(record)} />
              </Tooltip>
              <Popconfirm
                title="Cancel Subscription?"
                description="The subscription will be canceled at the end of the current billing period."
                onConfirm={() => cancelSubscriptionMutation.mutate(record.id)}
                okText="Yes, cancel"
                cancelText="No"
              >
                <Tooltip title="Cancel Subscription">
                  <Button type="text" danger icon={<StopOutlined />} size="small" />
                </Tooltip>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}><BarChartOutlined /> Subscription Management</Title>
        <Paragraph type="secondary">Monitor and manage all user subscriptions and view key metrics.</Paragraph>
      </div>

      {/* Analytics Cards */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card><Statistic title="Total Subscriptions" value={analyticsData?.totalSubscriptions || 0} prefix={<UserOutlined />} /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="Monthly Recurring Revenue (MRR)" value={analyticsData?.mrr || 0} prefix={<DollarOutlined />} precision={2} valueStyle={{ color: '#52c41a' }} /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="Annual Recurring Revenue (ARR)" value={analyticsData?.arr || 0} prefix={<DollarOutlined />} precision={2} valueStyle={{ color: '#52c41a' }} /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="Churn Rate (Monthly)" value={analyticsData?.churnRate || 0} suffix="%" precision={1} valueStyle={{ color: '#f5222d' }} /></Card>
        </Col>
      </Row>
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card><Statistic title="New Subscriptions (L30D)" value={analyticsData?.newSubscriptions || 0} prefix={<ArrowUpOutlined />} valueStyle={{ color: '#52c41a' }} /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="Cancellations (L30D)" value={analyticsData?.cancellations || 0} prefix={<ArrowDownOutlined />} valueStyle={{ color: '#f5222d' }} /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="Active Trials" value={analyticsData?.activeTrials || 0} /></Card>
        </Col>
        <Col span={6}>
          <Card><Statistic title="Trial Conversion Rate" value={analyticsData?.conversionRate || 0} suffix="%" precision={1} /></Card>
        </Col>
      </Row>

      {/* Filters and Table */}
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Input
              placeholder="Search by user name or email"
              prefix={<SearchOutlined />}
              style={{ width: 250 }}
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              allowClear
            />
            <Select
              placeholder="Filter by Plan"
              style={{ width: 150 }}
              value={filters.plan}
              onChange={(value) => setFilters({ ...filters, plan: value })}
              allowClear
            >
              <Option value="plan_basic">Basic</Option>
              <Option value="plan_prof">Professional</Option>
              <Option value="plan_ent">Enterprise</Option>
            </Select>
            <Select
              placeholder="Filter by Status"
              style={{ width: 150 }}
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              allowClear
            >
              <Option value="active">Active</Option>
              <Option value="trialing">Trialing</Option>
              <Option value="past_due">Past Due</Option>
              <Option value="canceled">Canceled</Option>
            </Select>
            <RangePicker value={filters.dateRange} onChange={(dates) => setFilters({ ...filters, dateRange: dates })} />
          </Space>
          <Button icon={<DownloadOutlined />}>Export CSV</Button>
        </div>
        <Table
          columns={columns}
          dataSource={subscriptions.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: subscriptions.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) => setPagination({ ...pagination, current: page, pageSize: pageSize || 10 }),
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Subscription Detail Drawer */}
      <Drawer
        title="Subscription Details"
        width={600}
        open={isDetailDrawerVisible}
        onClose={() => setIsDetailDrawerVisible(false)}
      >
        {selectedSubscription && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Subscription ID"><Text code>{selectedSubscription.id}</Text></Descriptions.Item>
            <Descriptions.Item label="User">{selectedSubscription.user.name} ({selectedSubscription.user.email})</Descriptions.Item>
            <Descriptions.Item label="Plan">{selectedSubscription.plan.name} (${selectedSubscription.plan.price}/{selectedSubscription.plan.interval})</Descriptions.Item>
            <Descriptions.Item label="Status">
              <Tag color={getStatusColor(selectedSubscription.status)}>{selectedSubscription.status.toUpperCase()}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Current Period">{new Date(selectedSubscription.currentPeriodStart).toLocaleString()} to {new Date(selectedSubscription.currentPeriodEnd).toLocaleString()}</Descriptions.Item>
            {selectedSubscription.trialEnd && <Descriptions.Item label="Trial End">{new Date(selectedSubscription.trialEnd).toLocaleString()}</Descriptions.Item>}
            <Descriptions.Item label="Created At">{new Date(selectedSubscription.createdAt).toLocaleString()}</Descriptions.Item>
            <Descriptions.Item label="Cancel at Period End">
              {selectedSubscription.cancelAtPeriodEnd ? <CheckCircleOutlined style={{ color: 'green' }} /> : <ClockCircleOutlined />}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Drawer>

      {/* Change Plan Modal */}
      <Modal
        title="Change Subscription Plan"
        open={isChangePlanModalVisible}
        onCancel={() => setIsChangePlanModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedSubscription && (
          <Form
            form={form}
            layout="vertical"
            onFinish={(values) => {
              changePlanMutation.mutate({
                subscriptionId: selectedSubscription.id,
                newPlanId: values.newPlanId,
              });
            }}
          >
            <Alert
              message="Plan Change Information"
              description="Changing the plan may result in immediate proration charges. The new plan will take effect immediately."
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <Row gutter={16}>
              <Col span={12}>
                <Title level={5}>Current Plan</Title>
                <Card>
                  <Statistic title={selectedSubscription.plan.name} value={`$${selectedSubscription.plan.price}`} suffix={`/ ${selectedSubscription.plan.interval}`} />
                </Card>
              </Col>
              <Col span={12}>
                <Title level={5}>New Plan</Title>
                <Form.Item name="newPlanId" rules={[{ required: true, message: 'Please select a new plan' }]}>
                  <Select placeholder="Select a new plan">
                    <Option value="plan_basic">Basic ($9.99/month)</Option>
                    <Option value="plan_prof">Professional ($49.99/month)</Option>
                    <Option value="plan_ent_month">Enterprise ($599/month)</Option>
                    <Option value="plan_ent_year">Enterprise ($5999/year)</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item style={{ marginTop: '24px' }}>
              <Space>
                <Button type="primary" htmlType="submit" loading={changePlanMutation.isPending}>
                  Confirm Change
                </Button>
                <Button onClick={() => setIsChangePlanModalVisible(false)}>Cancel</Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default Subscriptions; 