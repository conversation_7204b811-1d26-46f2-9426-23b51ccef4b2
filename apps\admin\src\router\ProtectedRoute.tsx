/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Result, Button } from 'antd'

import { useAuthStore, usePermission } from '@/store/auth'
import type { Permission, UserRole } from '@/types/auth'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermissions?: Permission[]
  requiredRoles?: UserRole[]
  requireAll?: boolean // 是否需要满足所有权限/角色（默认满足任一即可）
  fallback?: React.ReactNode // 权限不足时的自定义组件
}

/**
 * 路由守卫组件
 * 负责保护需要认证和权限的路由
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
  fallback,
}) => {
  const location = useLocation()
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const user = useAuthStore((state) => state.user)
  const { hasPermission, hasRole, hasAnyPermission, hasAllPermissions } = usePermission()

  // 未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // 用户信息异常
  if (!user) {
    return (
      <Result
        status="error"
        title="用户信息异常"
        subTitle="无法获取用户信息，请重新登录"
        extra={
          <Button type="primary" onClick={() => window.location.href = '/login'}>
            重新登录
          </Button>
        }
      />
    )
  }

  // 检查角色权限
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requireAll
      ? requiredRoles.every((role) => hasRole(role))
      : requiredRoles.some((role) => hasRole(role))

    if (!hasRequiredRole) {
      return (
        fallback || (
          <Result
            status="403"
            title="403"
            subTitle="抱歉，您没有权限访问此页面"
            extra={
              <Button type="primary" onClick={() => window.history.back()}>
                返回上一页
              </Button>
            }
          />
        )
      )
    }
  }

  // 检查具体权限
  if (requiredPermissions.length > 0) {
    const hasRequiredPermission = requireAll
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions)

    if (!hasRequiredPermission) {
      return (
        fallback || (
          <Result
            status="403"
            title="权限不足"
            subTitle={`您需要以下权限才能访问此页面: ${requiredPermissions.join(', ')}`}
            extra={
              <Button type="primary" onClick={() => window.history.back()}>
                返回上一页
              </Button>
            }
          />
        )
      )
    }
  }

  // 通过所有检查，渲染子组件
  return <>{children}</>
}

/**
 * 权限检查Hook
 * 用于在组件内部进行权限检查
 */
export const useRoutePermission = (
  requiredPermissions?: Permission[],
  requiredRoles?: UserRole[],
  requireAll = false
) => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const user = useAuthStore((state) => state.user)
  const { hasPermission, hasRole, hasAnyPermission, hasAllPermissions } = usePermission()

  const canAccess = React.useMemo(() => {
    if (!isAuthenticated || !user) {
      return false
    }

    // 检查角色
    if (requiredRoles && requiredRoles.length > 0) {
      const hasRequiredRole = requireAll
        ? requiredRoles.every((role) => hasRole(role))
        : requiredRoles.some((role) => hasRole(role))

      if (!hasRequiredRole) {
        return false
      }
    }

    // 检查权限
    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasRequiredPermission = requireAll
        ? hasAllPermissions(requiredPermissions)
        : hasAnyPermission(requiredPermissions)

      if (!hasRequiredPermission) {
        return false
      }
    }

    return true
  }, [
    isAuthenticated,
    user,
    requiredPermissions,
    requiredRoles,
    requireAll,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
  ])

  return {
    canAccess,
    isAuthenticated,
    user,
    hasPermission,
    hasRole,
  }
} 