// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "solmate/auth/Owned.sol";
import "solmate/utils/ReentrancyGuard.sol";
import {UUPSUpgradeable} from "solmate/auth/UUPSUpgradeable.sol";

contract FamilyTree is Owned, ReentrancyGuard, UUPSUpgradeable {
    
    // ======== Structs ========

    // To save gas, only the most core information is stored on-chain
    struct Member {
        bool exists;
        string arweaveMetadataURI; // Pointer to metadata on Arweave
    }

    // ======== State Variables ========

    // Mapping from member address to their node information
    mapping(address => Member) public members;

    // The core of the relationship graph: source => relationshipType => targets[]
    // e.g., relationships[alice]["CHILDREN"][0] = bob
    mapping(address => mapping(bytes32 => address[])) private relationships;

    // ======== Events ========

    event MemberAdded(address indexed member, string metadataURI);
    event RelationshipCreated(address indexed source, address indexed target, bytes32 indexed relationshipType);
    event RelationshipRemoved(address indexed source, address indexed target, bytes32 indexed relationshipType);
    
    // ======== Constructor & Initializer ========

    // Constructor is empty for UUPS proxy pattern
    constructor() {}

    // Initializer function, can only be called once
    function initialize(address initialOwner) public initializer {
        owner = initialOwner;
    }

    // ======== Core Logic ========

    // Add a new member node
    function addMember(address _member, string calldata _metadataURI) external onlyOwner {
        require(!members[_member].exists, "FamilyTree: Member already exists");
        members[_member] = Member({
            exists: true,
            arweaveMetadataURI: _metadataURI
        });
        emit MemberAdded(_member, _metadataURI);
    }
    
    // Create a relationship (core function called by users)
    function createRelationship(address _target, bytes32 _relationshipType) external nonReentrant {
        address _source = msg.sender;
        require(members[_source].exists, "FamilyTree: Source not a member");
        require(members[_target].exists, "FamilyTree: Target not a member");
        
        // Core validation to prevent logical paradoxes
        // In a real implementation, this check would be more complex, possibly requiring
        // on-chain or off-chain helpers for loop detection.
        // For a simple parent-child relationship, one could check that _target is not already
        // an ancestor of _source. However, full cycle detection is extremely expensive on-chain,
        // and this responsibility is often moved to a backend service.
        
        // Add relationship
        _addRelationship(_source, _target, _relationshipType);
        
        // Automatically add the reverse relationship
        bytes32 reverseType = _getReverseRelationshipType(_relationshipType);
        _addRelationship(_target, _source, reverseType);
        
        emit RelationshipCreated(_source, _target, _relationshipType);
    }

    function removeRelationship(address _target, bytes32 _relationshipType) external nonReentrant {
        address _source = msg.sender;
        require(members[_source].exists, "FamilyTree: Source not a member");
        require(members[_target].exists, "FamilyTree: Target not a member");

        _removeRelationship(_source, _target, _relationshipType);

        bytes32 reverseType = _getReverseRelationshipType(_relationshipType);
        _removeRelationship(_target, _source, reverseType);

        emit RelationshipRemoved(_source, _target, _relationshipType);
    }
    
    // ======== View Functions ========

    function getRelationships(address _source, bytes32 _type) external view returns (address[] memory) {
        return relationships[_source][_type];
    }

    // ======== Internal Helpers ========

    function _addRelationship(address _source, address _target, bytes32 _type) internal {
        // Check if relationship already exists to prevent duplicates
        address[] storage targets = relationships[_source][_type];
        for (uint i = 0; i < targets.length; i++) {
            if (targets[i] == _target) {
                return; // Relationship already exists
            }
        }
        targets.push(_target);
    }

    function _removeRelationship(address _source, address _target, bytes32 _type) internal {
        address[] storage targets = relationships[_source][_type];
        for (uint i = 0; i < targets.length; i++) {
            if (targets[i] == _target) {
                // Replace the target with the last element and pop
                targets[i] = targets[targets.length - 1];
                targets.pop();
                break;
            }
        }
    }
    
    function _getReverseRelationshipType(bytes32 _relationshipType) internal pure returns (bytes32) {
        // This is a simplified logic. A real system might need a more robust, on-chain mapping.
        if (_relationshipType == keccak256("CHILD")) return keccak256("PARENT");
        if (_relationshipType == keccak256("PARENT")) return keccak256("CHILD");
        if (_relationshipType == keccak256("SPOUSE")) return keccak256("SPOUSE");
        // A generic fallback for other relationship types
        return keccak256("RELATED_TO");
    }

    // ======== Upgrades (UUPS) ========

    // `_authorizeUpgrade` must be overridden to authorize the new implementation contract.
    // Typically, only the contract owner has the authority to upgrade.
    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}
} 