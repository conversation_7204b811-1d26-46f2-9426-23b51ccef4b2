/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package middleware

import (
	"context"
	"strings"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
	"google.golang.org/grpc/status"
)

// TracingInterceptor 创建一个分布式追踪拦截器
func TracingInterceptor(serviceName string) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 提取传播的上下文
		md, _ := metadata.FromIncomingContext(ctx)
		propagator := otel.GetTextMapPropagator()
		carrier := &metadataCarrier{md: md}
		ctx = propagator.Extract(ctx, carrier)

		// 创建新的 span
		tracer := otel.Tracer("pkg/middleware")
		ctx, span := tracer.Start(ctx, info.FullMethod)
		defer span.End()

		// 添加自定义 span 属性
		if span.IsRecording() {
			span.SetAttributes(
				attribute.String("service.name", serviceName),
				attribute.String("rpc.system", "grpc"),
				attribute.String("rpc.service", extractServiceName(info.FullMethod)),
				attribute.String("rpc.method", extractMethodName(info.FullMethod)),
			)

			// 添加客户端信息
			if p, ok := peer.FromContext(ctx); ok {
				span.SetAttributes(attribute.String("net.peer.addr", p.Addr.String()))
			}

			// 添加请求元数据
			if md, ok := metadata.FromIncomingContext(ctx); ok {
				if userAgent := md.Get("user-agent"); len(userAgent) > 0 {
					span.SetAttributes(attribute.String("http.user_agent", userAgent[0]))
				}
				if requestId := md.Get("x-request-id"); len(requestId) > 0 {
					span.SetAttributes(attribute.String("request.id", requestId[0]))
				}
			}
		}

		// 调用实际的 handler
		resp, err := handler(ctx, req)

		// 设置 span 状态
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
			if s, ok := status.FromError(err); ok {
				span.SetAttributes(attribute.String("grpc.status_code", s.Code().String()))
			}
		} else {
			span.SetStatus(codes.Ok, "")
		}

		return resp, err
	}
}

// TracingStreamInterceptor 创建一个分布式追踪流拦截器
func TracingStreamInterceptor(serviceName string) grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		ctx := ss.Context()

		// 提取传播的上下文
		md, _ := metadata.FromIncomingContext(ctx)
		propagator := otel.GetTextMapPropagator()
		carrier := &metadataCarrier{md: md}
		ctx = propagator.Extract(ctx, carrier)

		// 创建新的 span
		tracer := otel.Tracer("pkg/middleware")
		ctx, span := tracer.Start(ctx, info.FullMethod)
		defer span.End()

		// 添加自定义 span 属性
		if span.IsRecording() {
			span.SetAttributes(
				attribute.String("service.name", serviceName),
				attribute.String("rpc.system", "grpc"),
				attribute.String("rpc.service", extractServiceName(info.FullMethod)),
				attribute.String("rpc.method", extractMethodName(info.FullMethod)),
			)

			if p, ok := peer.FromContext(ctx); ok {
				span.SetAttributes(attribute.String("net.peer.addr", p.Addr.String()))
			}
		}

		// 包装 ServerStream
		wrappedStream := &tracingServerStream{
			ServerStream: ss,
			ctx:          ctx,
		}

		// 调用实际的 handler
		err := handler(srv, wrappedStream)

		// 设置 span 状态
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
			if s, ok := status.FromError(err); ok {
				span.SetAttributes(attribute.String("grpc.status_code", s.Code().String()))
			}
		} else {
			span.SetStatus(codes.Ok, "")
		}

		return err
	}
}

// tracingServerStream 包装 grpc.ServerStream 以提供追踪信息
type tracingServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

func (s *tracingServerStream) Context() context.Context {
	return s.ctx
}

// metadataCarrier 实现 propagation.TextMapCarrier 接口
type metadataCarrier struct {
	md metadata.MD
}

func (c *metadataCarrier) Get(key string) string {
	values := c.md.Get(key)
	if len(values) > 0 {
		return values[0]
	}
	return ""
}

func (c *metadataCarrier) Set(key, value string) {
	c.md.Set(key, value)
}

func (c *metadataCarrier) Keys() []string {
	keys := make([]string, 0, len(c.md))
	for k := range c.md {
		keys = append(keys, k)
	}
	return keys
}

// extractServiceName 从完整方法名中提取服务名
func extractServiceName(fullMethod string) string {
	if len(fullMethod) == 0 {
		return "unknown"
	}

	// gRPC 方法名格式为 "/package.service/method"
	if fullMethod[0] == '/' {
		fullMethod = fullMethod[1:]
	}

	// 找到最后一个 '/' 的位置
	lastSlash := strings.LastIndex(fullMethod, "/")
	if lastSlash == -1 {
		return "unknown"
	}

	return fullMethod[:lastSlash]
}

// extractMethodName 从完整方法名中提取方法名
func extractMethodName(fullMethod string) string {
	if len(fullMethod) == 0 {
		return "unknown"
	}

	// 找到最后一个 '/' 的位置
	lastSlash := strings.LastIndex(fullMethod, "/")
	if lastSlash == -1 {
		return fullMethod
	}

	return fullMethod[lastSlash+1:]
}

// GetTraceID 从上下文中获取 Trace ID
func GetTraceID(ctx context.Context) string {
	span := trace.SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		return span.SpanContext().TraceID().String()
	}
	return ""
}

// GetSpanID 从上下文中获取 Span ID
func GetSpanID(ctx context.Context) string {
	span := trace.SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		return span.SpanContext().SpanID().String()
	}
	return ""
}

// AddSpanAttribute 向当前 span 添加属性
func AddSpanAttribute(ctx context.Context, key string, value interface{}) {
	span := trace.SpanFromContext(ctx)
	if span.IsRecording() {
		switch v := value.(type) {
		case string:
			span.SetAttributes(attribute.String(key, v))
		case int:
			span.SetAttributes(attribute.Int(key, v))
		case int64:
			span.SetAttributes(attribute.Int64(key, v))
		case float64:
			span.SetAttributes(attribute.Float64(key, v))
		case bool:
			span.SetAttributes(attribute.Bool(key, v))
		default:
			span.SetAttributes(attribute.String(key, "unsupported_type"))
		}
	}
}

// StartSpan 在当前上下文中创建一个新的子 span
func StartSpan(ctx context.Context, operationName string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	tracer := otel.Tracer("pkg/middleware")
	return tracer.Start(ctx, operationName, opts...)
}

// RecordError 记录错误到当前 span
func RecordError(ctx context.Context, err error) {
	span := trace.SpanFromContext(ctx)
	if span.IsRecording() && err != nil {
		span.RecordError(err)
	}
}

// TracingOptions 追踪拦截器的配置选项
type TracingOptions struct {
	// ServiceName 服务名称
	ServiceName string
	// TracerProvider 追踪提供者
	TracerProvider trace.TracerProvider
	// Propagators 传播器
	Propagators propagation.TextMapPropagator
	// ExcludeMethods 要排除追踪的方法列表
	ExcludeMethods []string
}

// DefaultTracingOptions 返回默认的追踪选项
func DefaultTracingOptions(serviceName string) *TracingOptions {
	return &TracingOptions{
		ServiceName:    serviceName,
		TracerProvider: otel.GetTracerProvider(),
		Propagators:    otel.GetTextMapPropagator(),
		ExcludeMethods: []string{
			"/grpc.health.v1.Health/Check",
			"/grpc.health.v1.Health/Watch",
		},
	}
}

// TracingInterceptorWithOptions 创建一个带有自定义选项的追踪拦截器
func TracingInterceptorWithOptions(opts *TracingOptions) grpc.UnaryServerInterceptor {
	if opts == nil {
		opts = DefaultTracingOptions("unknown-service")
	}

	// 创建方法过滤器
	excludeMap := make(map[string]bool)
	for _, method := range opts.ExcludeMethods {
		excludeMap[method] = true
	}

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 检查是否需要排除此方法
		if excludeMap[info.FullMethod] {
			return handler(ctx, req)
		}

		// 使用自定义的追踪拦截器
		interceptor := TracingInterceptor(opts.ServiceName)
		return interceptor(ctx, req, info, handler)
	}
}
