# 系统架构图

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

本文档包含CINA.CLUB平台的系统架构图，展示各组件之间的关系和数据流。

## 整体系统架构

### 高层架构视图

```mermaid
graph TB
    %% 客户端层
    subgraph "客户端层"
        WebApp[Web应用<br/>React/TypeScript]
        AndroidApp[Android应用<br/>Kotlin/Go Mobile]
        AppleApp[Apple应用<br/>Swift/Go Mobile]
        WindowsApp[Windows应用<br/>C#/.NET/Go Mobile]
        HarmonyApp[HarmonyOS应用<br/>ArkTS/Go Mobile]
    end

    %% API网关层
    subgraph "API网关层"
        Gateway[API Gateway<br/>Go/Gin]
        Auth[认证服务<br/>JWT/OAuth2]
        RateLimit[限流控制]
        LoadBalancer[负载均衡器]
    end

    %% 业务服务层
    subgraph "微服务层"
        UserService[用户服务]
        ChatService[聊天服务]
        PKBService[个人知识库服务]
        AIService[AI助手服务]
        NotificationService[通知服务]
        PaymentService[支付服务]
        FileService[文件存储服务]
        SearchService[搜索服务]
    end

    %% 共享核心层
    subgraph "共享核心层"
        GoCore[Go核心库<br/>加密/AI/数据同步]
        Crypto[加密模块<br/>AES/RSA/ECDH]
        AIEngine[AI引擎<br/>本地推理/云端API]
        DataSync[数据同步<br/>CRDT/版本控制]
    end

    %% 数据层
    subgraph "数据存储层"
        PostgreSQL[(PostgreSQL<br/>主数据库)]
        Redis[(Redis<br/>缓存/会话)]
        Elasticsearch[(Elasticsearch<br/>搜索引擎)]
        S3[(对象存储<br/>S3/MinIO)]
        DistributedKV[(分布式KV<br/>etcd/Consul)]
    end

    %% 基础设施层
    subgraph "基础设施层"
        Kubernetes[Kubernetes集群]
        Docker[Docker容器]
        Prometheus[Prometheus监控]
        Grafana[Grafana仪表板]
        ELK[ELK日志栈]
    end

    %% 连接关系
    WebApp --> Gateway
    AndroidApp --> Gateway
    AppleApp --> Gateway
    WindowsApp --> Gateway
    HarmonyApp --> Gateway

    Gateway --> Auth
    Gateway --> RateLimit
    Gateway --> LoadBalancer

    LoadBalancer --> UserService
    LoadBalancer --> ChatService
    LoadBalancer --> PKBService
    LoadBalancer --> AIService
    LoadBalancer --> NotificationService
    LoadBalancer --> PaymentService
    LoadBalancer --> FileService
    LoadBalancer --> SearchService

    UserService --> GoCore
    ChatService --> GoCore
    PKBService --> GoCore
    AIService --> GoCore

    GoCore --> Crypto
    GoCore --> AIEngine
    GoCore --> DataSync

    UserService --> PostgreSQL
    ChatService --> Redis
    PKBService --> PostgreSQL
    AIService --> Redis
    SearchService --> Elasticsearch
    FileService --> S3
    NotificationService --> DistributedKV

    Kubernetes --> Docker
    Kubernetes --> Prometheus
    Prometheus --> Grafana
    Docker --> ELK
```

### 移动端架构详图

```mermaid
graph TB
    subgraph "移动端应用架构"
        subgraph "表现层"
            UI[用户界面<br/>ArkTS/Swift/Kotlin]
            WebView[WebView组件<br/>混合开发]
        end

        subgraph "业务逻辑层"
            BLL[业务逻辑层<br/>TypeScript/Swift/Kotlin]
            StateManager[状态管理<br/>Redux/MobX]
            Router[路由管理]
        end

        subgraph "数据访问层"
            HTTP[HTTP客户端<br/>网络请求]
            WebSocket[WebSocket<br/>实时通信]
            LocalDB[本地数据库<br/>SQLite]
            SecureStorage[安全存储<br/>Keychain/Keystore]
        end

        subgraph "Go核心桥接层"
            NAPI[NAPI桥接<br/>C++/Go Mobile]
            CoreLib[Go核心库<br/>libcore.so/.a]
        end

        subgraph "核心功能模块"
            Encryption[端到端加密<br/>AES-256/ECDH]
            AILocal[本地AI推理<br/>ONNX/TensorFlow Lite]
            DataSyncCore[数据同步核心<br/>CRDT算法]
            BiometricAuth[生物识别<br/>指纹/面容]
        end
    end

    %% 连接关系
    UI --> BLL
    WebView --> BLL
    BLL --> StateManager
    BLL --> Router
    BLL --> HTTP
    BLL --> WebSocket
    BLL --> LocalDB
    BLL --> SecureStorage

    HTTP --> NAPI
    LocalDB --> NAPI
    SecureStorage --> NAPI
    NAPI --> CoreLib

    CoreLib --> Encryption
    CoreLib --> AILocal
    CoreLib --> DataSyncCore
    BLL --> BiometricAuth

    %% 外部连接
    HTTP -.-> |HTTPS/WSS| Internet[互联网]
    WebSocket -.-> |WSS| Internet
```

### 数据流架构

```mermaid
sequenceDiagram
    participant Client as 客户端应用
    participant Gateway as API网关
    participant Auth as 认证服务
    participant Service as 业务服务
    participant Core as Go核心库
    participant DB as 数据库
    participant Cache as 缓存

    Note over Client, Cache: 用户登录数据流

    Client->>Gateway: 登录请求(加密)
    Gateway->>Auth: 验证凭据
    Auth->>DB: 查询用户信息
    DB-->>Auth: 返回用户数据
    Auth->>Core: 生成JWT令牌
    Core-->>Auth: 加密令牌
    Auth-->>Gateway: 返回令牌
    Gateway-->>Client: 登录成功响应

    Note over Client, Cache: 数据同步流程

    Client->>Gateway: 同步请求(带令牌)
    Gateway->>Auth: 验证令牌
    Auth-->>Gateway: 令牌有效
    Gateway->>Service: 转发请求
    Service->>Core: 数据加密/解密
    Service->>DB: 查询/更新数据
    Service->>Cache: 更新缓存
    Cache-->>Service: 确认更新
    DB-->>Service: 返回结果
    Service->>Core: 处理业务逻辑
    Core-->>Service: 返回处理结果
    Service-->>Gateway: 返回响应
    Gateway-->>Client: 同步完成
```

## 安全架构

### 端到端加密架构

```mermaid
graph LR
    subgraph "客户端A"
        ClientA_UI[用户界面]
        ClientA_Crypto[加密模块]
        ClientA_Key[私钥存储]
    end

    subgraph "传输层"
        TLS[TLS 1.3加密]
        Server[服务器<br/>只存储密文]
    end

    subgraph "客户端B"
        ClientB_UI[用户界面]
        ClientB_Crypto[加密模块]
        ClientB_Key[私钥存储]
    end

    ClientA_UI --> ClientA_Crypto
    ClientA_Crypto --> ClientA_Key
    ClientA_Crypto -->|加密消息| TLS
    TLS --> Server
    Server --> TLS
    TLS -->|传输密文| ClientB_Crypto
    ClientB_Crypto --> ClientB_Key
    ClientB_Crypto --> ClientB_UI

    Note1[密钥交换: ECDH]
    Note2[对称加密: AES-256-GCM]
    Note3[数字签名: Ed25519]
    Note4[服务器无法解密]
```

### 认证授权架构

```mermaid
graph TB
    subgraph "认证层"
        Login[用户登录]
        MFA[多因素认证]
        Biometric[生物识别]
        JWT[JWT令牌]
    end

    subgraph "授权层"
        RBAC[基于角色的访问控制]
        Permissions[权限管理]
        Resources[资源访问]
    end

    subgraph "安全策略"
        RateLimit[访问频率限制]
        IPWhitelist[IP白名单]
        DeviceFingerprint[设备指纹]
        AuditLog[审计日志]
    end

    Login --> MFA
    MFA --> Biometric
    Biometric --> JWT
    JWT --> RBAC
    RBAC --> Permissions
    Permissions --> Resources

    JWT --> RateLimit
    JWT --> IPWhitelist
    JWT --> DeviceFingerprint
    Resources --> AuditLog
```

## 部署架构

### Kubernetes部署架构

```mermaid
graph TB
    subgraph "Kubernetes集群"
        subgraph "命名空间: cina-prod"
            subgraph "前端层"
                WebPod[Web应用Pod]
                AdminPod[管理后台Pod]
            end

            subgraph "API层"
                GatewayPod[API网关Pod]
                AuthPod[认证服务Pod]
            end

            subgraph "业务服务层"
                UserPod[用户服务Pod]
                ChatPod[聊天服务Pod]
                PKBPod[PKB服务Pod]
                AIPod[AI服务Pod]
            end

            subgraph "数据层"
                PostgreSQLPod[PostgreSQL Pod]
                RedisPod[Redis Pod]
                ElasticPod[Elasticsearch Pod]
            end
        end

        subgraph "命名空间: monitoring"
            PrometheusPod[Prometheus Pod]
            GrafanaPod[Grafana Pod]
            AlertManagerPod[AlertManager Pod]
        end

        subgraph "命名空间: logging"
            ElasticsearchPod[Elasticsearch Pod]
            LogstashPod[Logstash Pod]
            KibanaPod[Kibana Pod]
        end

        subgraph "网络组件"
            Ingress[Ingress Controller]
            LoadBalancer[负载均衡器]
            ServiceMesh[服务网格<br/>Istio]
        end
    end

    subgraph "外部服务"
        CDN[CDN服务]
        DNS[DNS服务]
        SSL[SSL证书]
    end

    %% 连接关系
    CDN --> Ingress
    DNS --> LoadBalancer
    SSL --> Ingress
    Ingress --> GatewayPod
    LoadBalancer --> ServiceMesh
    ServiceMesh --> UserPod
    ServiceMesh --> ChatPod
    ServiceMesh --> PKBPod
    ServiceMesh --> AIPod
```

### 云原生架构

```mermaid
graph TB
    subgraph "云平台层"
        subgraph "计算服务"
            EKS[Amazon EKS<br/>Kubernetes]
            EC2[EC2实例<br/>工作节点]
            Lambda[Lambda函数<br/>无服务器]
        end

        subgraph "存储服务"
            RDS[RDS PostgreSQL<br/>关系数据库]
            ElastiCache[ElastiCache Redis<br/>内存缓存]
            S3[S3对象存储<br/>文件存储]
        end

        subgraph "网络服务"
            VPC[VPC虚拟私有云]
            ALB[应用负载均衡器]
            CloudFront[CloudFront CDN]
        end

        subgraph "监控服务"
            CloudWatch[CloudWatch监控]
            XRay[X-Ray链路追踪]
            Config[Config配置管理]
        end

        subgraph "安全服务"
            IAM[IAM身份管理]
            Secrets[Secrets Manager]
            KMS[Key Management Service]
        end
    end

    %% 连接关系
    EKS --> EC2
    EKS --> VPC
    ALB --> EKS
    CloudFront --> ALB
    EKS --> RDS
    EKS --> ElastiCache
    EKS --> S3
    CloudWatch --> EKS
    XRay --> EKS
    IAM --> EKS
    Secrets --> EKS
    KMS --> Secrets
```

## 性能架构

### 缓存架构

```mermaid
graph TB
    subgraph "多级缓存架构"
        subgraph "客户端缓存"
            BrowserCache[浏览器缓存]
            AppCache[应用缓存]
            LocalStorage[本地存储]
        end

        subgraph "CDN缓存"
            EdgeCache[边缘缓存]
            StaticAssets[静态资源]
        end

        subgraph "应用缓存"
            RedisCluster[Redis集群]
            MemoryCache[内存缓存]
            L2Cache[二级缓存]
        end

        subgraph "数据库缓存"
            QueryCache[查询缓存]
            ConnectionPool[连接池]
            ReadReplica[只读副本]
        end
    end

    %% 缓存策略
    BrowserCache -.->|过期| EdgeCache
    AppCache -.->|失效| RedisCluster
    EdgeCache -.->|回源| RedisCluster
    RedisCluster -.->|穿透| QueryCache
    QueryCache -.->|未命中| ReadReplica

    %% 缓存更新
    RedisCluster -->|更新| L2Cache
    L2Cache -->|失效| MemoryCache
```

### 数据分片架构

```mermaid
graph TB
    subgraph "数据分片策略"
        subgraph "用户数据分片"
            UserShard1[用户分片1<br/>uid % 4 = 0]
            UserShard2[用户分片2<br/>uid % 4 = 1]
            UserShard3[用户分片3<br/>uid % 4 = 2]
            UserShard4[用户分片4<br/>uid % 4 = 3]
        end

        subgraph "内容数据分片"
            ContentShard1[内容分片1<br/>按时间范围]
            ContentShard2[内容分片2<br/>按地理位置]
            ContentShard3[内容分片3<br/>按内容类型]
        end

        subgraph "分片路由"
            ShardRouter[分片路由器]
            HashRing[一致性哈希环]
            MetadataDB[元数据库]
        end
    end

    ShardRouter --> HashRing
    ShardRouter --> MetadataDB
    HashRing --> UserShard1
    HashRing --> UserShard2
    HashRing --> UserShard3
    HashRing --> UserShard4
    
    MetadataDB --> ContentShard1
    MetadataDB --> ContentShard2
    MetadataDB --> ContentShard3
```

---

**注：所有架构图都可以使用Mermaid工具进行渲染和编辑。** 