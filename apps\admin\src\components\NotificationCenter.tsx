/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:30:00
 * Modified: 2025-01-23 20:20:00
 */

import React, { useState, useEffect } from 'react';
import {
  Drawer,
  List,
  Badge,
  Button,
  Space,
  Typography,
  Avatar,
  Tag,
  Dropdown,
  Menu,
  Divider,
  Empty,
  Tabs,
  Select,
  Input,
  Modal,
  Form,
  Checkbox,
  TimePicker,
  Alert,
} from 'antd';
import {
  BellOutlined,
  SettingOutlined,
  DeleteOutlined,
  CheckOutlined,
  MoreOutlined,
  ClearOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  SystemUpdateOutlined,
  SecurityScanOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useWebSocket } from '@/lib/websocket';

const { Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Search } = Input;

// Type Definitions
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'system' | 'security' | 'financial';
  title: string;
  message: string;
  category: 'user' | 'system' | 'security' | 'content' | 'financial' | 'general';
  priority: 'low' | 'medium' | 'high' | 'critical';
  read: boolean;
  archived: boolean;
  createdAt: string;
  expiresAt?: string;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
  userId?: string;
  userName?: string;
}

export interface NotificationAction {
  id: string;
  label: string;
  type: 'primary' | 'default' | 'danger';
  action: string;
  params?: Record<string, any>;
}

export interface NotificationPreferences {
  enabled: boolean;
  categories: {
    user: boolean;
    system: boolean;
    security: boolean;
    content: boolean;
    financial: boolean;
    general: boolean;
  };
  channels: {
    inApp: boolean;
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
}

interface NotificationCenterProps {
  visible: boolean;
  onClose: () => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ visible, onClose }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isPreferencesVisible, setIsPreferencesVisible] = useState(false);
  const [form] = Form.useForm<NotificationPreferences>();
  const queryClient = useQueryClient();
  const wsClient = useWebSocket('notifications');

  // Fetching data
  const { data: notifications = [], isLoading } = useQuery<Notification[]>(['notifications']);
  const { data: preferences } = useQuery<NotificationPreferences>(['notification-preferences']);

  // Mutations
  const markAsReadMutation = useMutation({
    mutationFn: (ids: string[]) => Promise.resolve(ids), // Mock mutation
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['notifications'] }),
  });
  const deleteMutation = useMutation({
    mutationFn: (ids: string[]) => Promise.resolve(ids), // Mock mutation
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['notifications'] }),
  });
  const updatePreferencesMutation = useMutation({
    mutationFn: (prefs: NotificationPreferences) => Promise.resolve(prefs), // Mock mutation
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notification-preferences'] });
      setIsPreferencesVisible(false);
    },
  });

  // WebSocket effect
  useEffect(() => {
    if (wsClient) {
      const unsubscribe = wsClient.on('notification', (notification: Notification) => {
        queryClient.setQueryData<Notification[]>(['notifications'], (old = []) => [notification, ...old]);
        // ... browser notification logic
      });
      return unsubscribe;
    }
  }, [wsClient, queryClient]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'error': return <ExclamationCircleOutlined style={{ color: '#f5222d' }} />;
      case 'security': return <SecurityScanOutlined style={{ color: '#f5222d' }} />;
      case 'system': return <SystemUpdateOutlined style={{ color: '#1890ff' }} />;
      case 'financial': return <DollarOutlined style={{ color: '#52c41a' }} />;
      default: return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'critical': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'blue';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  const handleMarkAllAsRead = () => {
    const unreadIds = notifications.filter(n => !n.read).map(n => n.id);
    if (unreadIds.length > 0) markAsReadMutation.mutate(unreadIds);
  };
  
  const handleClearAll = () => {
    Modal.confirm({
      title: 'Clear All Notifications?',
      content: 'This will delete all notifications and cannot be undone.',
      onOk: () => {
        const allIds = notifications.map(n => n.id);
        if (allIds.length > 0) deleteMutation.mutate(allIds);
      },
    });
  };

  const handleNotificationAction = (notification: Notification, action: NotificationAction) => {
    if (action.action === 'navigate' && action.params?.url) {
      window.location.href = action.params.url;
    } else {
        markAsReadMutation.mutate([notification.id]);
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  const filteredNotifications = notifications.filter((notification: Notification) => {
    if (activeTab === 'unread' && notification.read) return false;
    if (activeTab === 'archived' && !notification.archived) return false;
    return true;
  });

  return (
    <>
      <Drawer
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <BellOutlined />
              <span>Notifications</span>
              {unreadCount > 0 && <Badge count={unreadCount} />}
            </Space>
            <Dropdown overlay={<Menu items={[
                { key: 'mark_all_read', label: 'Mark All as Read', onClick: handleMarkAllAsRead, disabled: unreadCount === 0 },
                { key: 'clear_all', label: 'Clear All', onClick: handleClearAll, disabled: notifications.length === 0 },
                { type: 'divider' },
                { key: 'preferences', label: 'Preferences', onClick: () => setIsPreferencesVisible(true) },
            ]} />} trigger={['click']}>
              <Button type="text" icon={<MoreOutlined />} />
            </Dropdown>
          </div>
        }
        width={400}
        open={visible}
        onClose={onClose}
        bodyStyle={{ padding: 0 }}
      >
        <div style={{ padding: '16px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Search
              placeholder="Search notifications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              allowClear
            />
            
            <Select
              value={filter}
              onChange={setFilter}
              style={{ width: '100%' }}
              placeholder="Filter by category"
            >
              <Option value="all">All Categories</Option>
              <Option value="user">User</Option>
              <Option value="system">System</Option>
              <Option value="security">Security</Option>
              <Option value="content">Content</Option>
              <Option value="financial">Financial</Option>
              <Option value="general">General</Option>
            </Select>
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginTop: 0 }}>
          <TabPane tab={`All (${notifications.length})`} key="all" />
          <TabPane tab={`Unread (${unreadCount})`} key="unread" />
          <TabPane tab="Archived" key="archived" />
        </Tabs>

        <div style={{ height: 'calc(100vh - 200px)', overflow: 'auto' }}>
          {filteredNotifications.length === 0 ? (
            <Empty
              description="No notifications"
              style={{ marginTop: '50px' }}
            />
          ) : (
            <List
              dataSource={filteredNotifications}
              loading={isLoading}
              renderItem={(notification: Notification) => (
                <List.Item
                  style={{
                    padding: '12px 16px',
                    backgroundColor: notification.read ? 'transparent' : '#f6ffed',
                    borderLeft: notification.read ? 'none' : '3px solid #52c41a'
                  }}
                  actions={[
                    <Dropdown
                      overlay={
                        <Menu
                          items={[
                            {
                              key: 'mark_read',
                              label: notification.read ? 'Mark as Unread' : 'Mark as Read',
                              icon: <CheckOutlined />,
                              onClick: () => markAsReadMutation.mutate([notification.id])
                            },
                            {
                              key: 'delete',
                              label: 'Delete',
                              icon: <DeleteOutlined />,
                              onClick: () => deleteMutation.mutate([notification.id])
                            }
                          ]}
                        />
                      }
                      trigger={['click']}
                    >
                      <Button type="text" icon={<MoreOutlined />} size="small" />
                    </Dropdown>
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        icon={getNotificationIcon(notification.type)}
                        style={{ backgroundColor: 'transparent' }}
                      />
                    }
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text strong={!notification.read}>{notification.title}</Text>
                        <Space>
                          <Tag color={getPriorityColor(notification.priority)} size="small">
                            {notification.priority}
                          </Tag>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {new Date(notification.createdAt).toLocaleTimeString()}
                          </Text>
                        </Space>
                      </div>
                    }
                    description={
                      <div>
                        <Text type="secondary">{notification.message}</Text>
                        {notification.actions && notification.actions.length > 0 && (
                          <div style={{ marginTop: '8px' }}>
                            <Space size="small">
                              {notification.actions.map(action => (
                                <Button
                                  key={action.id}
                                  type={action.type}
                                  size="small"
                                  onClick={() => handleNotificationAction(notification, action)}
                                >
                                  {action.label}
                                </Button>
                              ))}
                            </Space>
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </div>
      </Drawer>

      <Modal
        title="Notification Preferences"
        open={isPreferencesVisible}
        onCancel={() => setIsPreferencesVisible(false)}
        footer={null}
        width={600}
      >
        <Form<NotificationPreferences>
          form={form}
          layout="vertical"
          initialValues={preferences}
          onFinish={(values) => updatePreferencesMutation.mutate(values)}
        >
          <Alert
            message="Browser Notifications"
            description={
              'Notification' in window && Notification.permission !== 'granted' ? (
                <div>
                  <Text>Enable browser notifications for real-time alerts.</Text>
                  <Button 
                    type="link" 
                    onClick={() => {
                      if ('Notification' in window) {
                        Notification.requestPermission().then(permission => {
                          if (permission === 'granted') {
                            updatePreferencesMutation.mutate({
                              enabled: true,
                              categories: {
                                user: true,
                                system: true,
                                security: true,
                                content: true,
                                financial: true,
                                general: true
                              },
                              channels: {
                                inApp: true,
                                email: true,
                                push: true,
                                sms: true
                              },
                              quietHours: {
                                enabled: false,
                                start: '22:00',
                                end: '08:00'
                              },
                              frequency: 'immediate'
                            });
                          }
                        });
                      }
                    }}
                    style={{ padding: 0, marginLeft: '8px' }}
                  >
                    Enable Now
                  </Button>
                </div>
              ) : (
                'Browser notifications are enabled.'
              )
            }
            type={Notification.permission === 'granted' ? 'success' : 'info'}
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <Form.Item name="enabled" valuePropName="checked">
            <Checkbox>Enable all notifications</Checkbox>
          </Form.Item>

          <Divider>Categories</Divider>
          
          <Form.Item label="Notification Categories">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Form.Item name={['categories', 'user']} valuePropName="checked" noStyle>
                <Checkbox>User Management</Checkbox>
              </Form.Item>
              <Form.Item name={['categories', 'system']} valuePropName="checked" noStyle>
                <Checkbox>System Events</Checkbox>
              </Form.Item>
              <Form.Item name={['categories', 'security']} valuePropName="checked" noStyle>
                <Checkbox>Security Alerts</Checkbox>
              </Form.Item>
              <Form.Item name={['categories', 'content']} valuePropName="checked" noStyle>
                <Checkbox>Content Moderation</Checkbox>
              </Form.Item>
              <Form.Item name={['categories', 'financial']} valuePropName="checked" noStyle>
                <Checkbox>Financial Transactions</Checkbox>
              </Form.Item>
              <Form.Item name={['categories', 'general']} valuePropName="checked" noStyle>
                <Checkbox>General Updates</Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>

          <Divider>Channels</Divider>
          
          <Form.Item label="Notification Channels">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Form.Item name={['channels', 'inApp']} valuePropName="checked" noStyle>
                <Checkbox>In-App Notifications</Checkbox>
              </Form.Item>
              <Form.Item name={['channels', 'email']} valuePropName="checked" noStyle>
                <Checkbox>Email Notifications</Checkbox>
              </Form.Item>
              <Form.Item name={['channels', 'push']} valuePropName="checked" noStyle>
                <Checkbox>Push Notifications</Checkbox>
              </Form.Item>
              <Form.Item name={['channels', 'sms']} valuePropName="checked" noStyle>
                <Checkbox>SMS Notifications</Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>

          <Divider>Quiet Hours</Divider>
          
          <Form.Item name={['quietHours', 'enabled']} valuePropName="checked">
            <Checkbox>Enable quiet hours</Checkbox>
          </Form.Item>

          <Space>
            <Form.Item name={['quietHours', 'start']} label="Start Time">
              <TimePicker format="HH:mm" />
            </Form.Item>
            <Form.Item name={['quietHours', 'end']} label="End Time">
              <TimePicker format="HH:mm" />
            </Form.Item>
          </Space>

          <Form.Item name="frequency" label="Notification Frequency">
            <Select>
              <Option value="immediate">Immediate</Option>
              <Option value="hourly">Hourly Digest</Option>
              <Option value="daily">Daily Digest</Option>
              <Option value="weekly">Weekly Digest</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={updatePreferencesMutation.isPending}
              >
                Save Preferences
              </Button>
              <Button onClick={() => setIsPreferencesVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default NotificationCenter; 