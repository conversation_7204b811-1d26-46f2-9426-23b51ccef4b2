<!--
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
-->

<Application
    x:Class="CinaClub.App.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
                <!-- Other merged dictionaries here -->
            </ResourceDictionary.MergedDictionaries>
            <!-- Other app resources here -->
            
            <!-- Brand Colors -->
            <Color x:Key="CinaClubPrimary">#2563EB</Color>
            <Color x:Key="CinaClubSecondary">#7C3AED</Color>
            <Color x:Key="CinaClubAccent">#059669</Color>
            
            <SolidColorBrush x:Key="CinaClubPrimaryBrush" Color="{StaticResource CinaClubPrimary}" />
            <SolidColorBrush x:Key="CinaClubSecondaryBrush" Color="{StaticResource CinaClubSecondary}" />
            <SolidColorBrush x:Key="CinaClubAccentBrush" Color="{StaticResource CinaClubAccent}" />
            
            <!-- Custom Styles -->
            <Style x:Key="CinaClubButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource CinaClubPrimaryBrush}" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="CornerRadius" Value="8" />
                <Setter Property="Padding" Value="16,8" />
                <Setter Property="FontWeight" Value="SemiBold" />
            </Style>
            
            <Style x:Key="CinaClubCardStyle" TargetType="Border">
                <Setter Property="Background" Value="{ThemeResource CardBackgroundFillColorDefaultBrush}" />
                <Setter Property="BorderBrush" Value="{ThemeResource CardStrokeColorDefaultBrush}" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="CornerRadius" Value="8" />
                <Setter Property="Padding" Value="16" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application> 