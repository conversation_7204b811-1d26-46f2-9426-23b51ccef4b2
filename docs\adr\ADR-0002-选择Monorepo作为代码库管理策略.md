
### ADR-002: 选择Monorepo作为代码库管理策略

**标题**: 采用Monorepo（单一代码库）管理所有后端微服务
**状态**: 已接受
**日期**: 2025-06-12

#### 背景 (Context)

在决定采用微服务架构（见ADR-001）后，我们需要确定如何组织和管理这30多个微服务的源代码。

#### 决策驱动因素 (Decision Drivers)

*   **代码复用**: 平台中存在大量需要跨服务复用的通用逻辑，如认证中间件、日志记录器、数据库客户端、错误处理等。
*   **依赖管理**: 需要一种简单的方式来管理服务间的内部依赖和第三方库的版本。
*   **原子性变更**: 经常需要进行跨多个服务的原子性修改（例如，当一个gRPC API契约变更时，需要同时更新服务端和所有客户端）。
*   **开发效率**: 简化新服务的创建流程，降低跨团队协作的沟通成本，方便IDE中的代码导航和重构。
*   **统一的工具链**: 希望能在整个代码库上实施统一的构建、测试、代码扫描和部署流程。

#### 备选方案 (Considered Options)

1.  **多代码库 (Polyrepo / Multi-repo)**:
    *   每个微服务拥有其独立的Git代码仓库。
    *   *优点*: 强隔离，每个团队对其仓库有完全的控制权；访问控制更精细；每个仓库的CI/CD流程简单独立。
    *   *缺点*:
        *   **代码复用困难**: 共享逻辑需要打包成独立的库并发布到私有包管理器，版本管理和依赖更新成为一个巨大的痛点（“依赖地狱”）。
        *   **原子性变更不可能**: 跨多个仓库的原子提交无法实现，需要复杂的脚本或流程来协调多个PR的合并。
        *   **发现和协作成本高**: 开发者难以获得整个系统的全局视图，跨团队协作需要频繁地在不同仓库间切换。
        *   **工具链碎片化**: 难以强制实施统一的代码质量标准和CI/CD流程。

2.  **单一代码库 (Monorepo)**:
    *   所有微服务及其相关代码（API定义、文档、基础设施配置）都存放在同一个Git代码仓库中。
    *   *优点*:
        *   **无缝的代码共享**: 通用逻辑放在`pkg/`目录下，所有服务直接导入即可，无需版本管理。
        *   **简化的依赖管理**: 通常整个代码库使用统一的第三方依赖版本。
        *   **原子性提交**: 可以通过一次提交（一个PR）完成对多个服务的原子性修改。
        *   **增强的协作和可见性**: 开发者可以轻松地查看和理解任何服务的代码，促进了知识共享和代码重构。
        *   **统一的工具链**: 易于在顶层实施统一的CI/CD和代码质量检查。
    *   *缺点*:
        *   **仓库体积可能变大**: 对Git性能有一定挑战（可通过稀疏检出等缓解）。
        *   **构建系统要求高**: 需要智能的CI/CD系统，只构建和测试受变更影响的部分，而不是每次都构建所有服务。
        *   **访问控制较粗**: 对代码的访问控制是仓库级别的。

#### 决策结果 (Decision Outcome)

**选择方案2：Monorepo**。

**理由**:
对于CINA.CLUB这样一个拥有大量高度关联微服务的平台，Monorepo在**简化代码共享、保证原子性变更和提升开发效率**方面的优势，远大于其带来的挑战。第三方库版本冲突和共享库更新的痛苦是Polyrepo模式在大型项目中难以克服的顽疾。我们将投资于现代化的构建工具（如`bazel`或智能的CI脚本）和Go Workspaces来优化构建流程，并相信Monorepo提供的整体一致性和协作便利性，是确保平台长期架构健康和开发速度的关键。

