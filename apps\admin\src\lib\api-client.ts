/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 10:00:00
Modified: 2025-01-23 10:00:00
*/

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { message } from 'antd'

import type { ApiResponse, ApiError } from '@/types/api'
import { useAuthStore } from '@/store/auth'

/**
 * API客户端配置
 */
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
const MAX_RETRY_ATTEMPTS = 3
const RETRY_DELAY = 1000 // 1 second

/**
 * 请求重试配置
 */
interface RetryConfig {
  retries: number
  retryDelay: number
  retryCondition: (error: AxiosError) => boolean
}

/**
 * 默认重试条件
 */
const defaultRetryCondition = (error: AxiosError): boolean => {
  return !error.response || (error.response.status >= 500 && error.response.status <= 599)
}

/**
 * 延迟函数
 */
const delay = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 创建axios实例
 */
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

/**
 * 请求拦截器
 */
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * 请求重试逻辑
 */
const executeWithRetry = async (config: any, retryCount = 0): Promise<any> => {
  try {
    return await apiClient(config)
  } catch (error: any) {
    const shouldRetry = defaultRetryCondition(error) && retryCount < MAX_RETRY_ATTEMPTS
    
    if (shouldRetry) {
      await delay(RETRY_DELAY * Math.pow(2, retryCount)) // 指数退避
      return executeWithRetry(config, retryCount + 1)
    }
    
    throw error
  }
}

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

/**
 * 响应拦截器
 */
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 检查业务层面的错误
    if (response.data && !response.data.success) {
      const error: ApiError = {
        success: false,
        message: response.data.message || '请求失败',
        code: response.data.code || 0,
        timestamp: response.data.timestamp || new Date().toISOString(),
      }
      
      // 显示错误消息
      message.error(error.message)
      
      return Promise.reject(error)
    }

    return response
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise(function(resolve, reject) {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers['Authorization'] = 'Bearer ' + token;
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = useAuthStore.getState().refreshToken;
      if (refreshToken) {
        return new Promise(function(resolve, reject) {
          axios.post(`${API_BASE_URL}/auth/refresh`, { refreshToken })
            .then(refreshResponse => {
              const { token, user } = refreshResponse.data.data;
              useAuthStore.getState().login(token, refreshToken, user);
              apiClient.defaults.headers.common['Authorization'] = 'Bearer ' + token;
              originalRequest.headers['Authorization'] = 'Bearer ' + token;
              processQueue(null, token);
              resolve(apiClient(originalRequest));
            })
            .catch((err) => {
              processQueue(err, null);
              useAuthStore.getState().logout();
              window.location.href = '/login';
              reject(err);
            })
            .finally(() => { isRefreshing = false });
        });
      }
    }
    
    const { response, config } = error

    // 处理网络错误 - 自动重试
    if (!response) {
      if (!config._retryCount) {
        config._retryCount = 0
      }
      
      if (config._retryCount < MAX_RETRY_ATTEMPTS) {
        config._retryCount++
        await delay(RETRY_DELAY * config._retryCount)
        return apiClient(config)
      }
      
      message.error('网络连接失败，请检查您的网络设置')
      return Promise.reject(error)
    }

    const { status } = response

    switch (status) {
      case 403:
        message.error('权限不足，无法访问该资源')
        break

      case 404:
        message.error('请求的资源不存在')
        break

      case 422:
        // 表单验证错误
        const validationErrors = response.data?.details
        if (validationErrors) {
          Object.values(validationErrors).forEach((errorMsg: any) => {
            message.error(errorMsg)
          })
        } else {
          message.error(response.data?.message || '请求参数错误')
        }
        break

      case 429:
        message.error('请求过于频繁，请稍后再试')
        break

      case 500:
      case 502:
      case 503:
      case 504:
        // 服务器错误 - 自动重试
        if (!config._retryCount) {
          config._retryCount = 0
        }
        
        if (config._retryCount < MAX_RETRY_ATTEMPTS) {
          config._retryCount++
          await delay(RETRY_DELAY * Math.pow(2, config._retryCount))
          return apiClient(config)
        }
        
        message.error('服务暂时不可用，请稍后再试')
        break

      default:
        message.error(response.data?.message || '请求失败，请稍后再试')
    }

    return Promise.reject(error)
  }
)

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * GET请求
 */
export const get = <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return apiClient.get<ApiResponse<T>>(url, config).then((response) => response.data.data)
}

/**
 * POST请求
 */
export const post = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.post<ApiResponse<T>>(url, data, config).then((response) => response.data.data)
}

/**
 * PUT请求
 */
export const put = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.put<ApiResponse<T>>(url, data, config).then((response) => response.data.data)
}

/**
 * PATCH请求
 */
export const patch = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.patch<ApiResponse<T>>(url, data, config).then((response) => response.data.data)
}

/**
 * DELETE请求
 */
export const del = <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return apiClient.delete<ApiResponse<T>>(url, config).then((response) => response.data.data)
}

/**
 * 文件上传
 */
export const upload = <T = any>(
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<T> => {
  const formData = new FormData()
  formData.append('file', file)

  return apiClient
    .post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
    .then((response) => response.data.data)
}

/**
 * 文件下载
 */
export const download = (url: string, filename?: string): Promise<void> => {
  return apiClient
    .get(url, {
      responseType: 'blob',
    })
    .then((response) => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
} 