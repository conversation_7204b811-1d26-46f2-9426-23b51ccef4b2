/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package toolkit

import (
	"context"
	"fmt"
	"time"

	"cina.club/services/ai-assistant-service/internal/application/port"
)

// GetUserInfoTool get user information tool
type GetUserInfoTool struct {
	name        string
	description string
}

// NewGetUserInfoTool creates a new user info tool
func NewGetUserInfoTool() port.Tool {
	return &GetUserInfoTool{
		name:        "get_user_info",
		description: "Get user information including profile, preferences, order history, etc.",
	}
}

// Name returns tool name
func (t *GetUserInfoTool) Name() string {
	return t.name
}

// Description returns tool description
func (t *GetUserInfoTool) Description() string {
	return t.description
}

// Category returns tool category
func (t *GetUserInfoTool) Category() port.ToolCategory {
	return port.ToolCategoryUser
}

// RequiresAuth returns whether authentication is required
func (t *GetUserInfoTool) RequiresAuth() bool {
	return true // Getting user info requires user authentication
}

// IsAsync returns whether this is an async tool
func (t *GetUserInfoTool) IsAsync() bool {
	return false
}

// InputSchema returns input parameter schema
func (t *GetUserInfoTool) InputSchema() *port.JSONSchema {
	schema := port.NewObjectSchema(
		"Get user information parameters",
		map[string]*port.JSONSchema{},
		[]string{},
	)

	schema.AddProperty("include_profile", port.NewBooleanSchema("Whether to include user profile"))
	schema.AddProperty("include_preferences", port.NewBooleanSchema("Whether to include user preferences"))
	schema.AddProperty("include_order_history", port.NewBooleanSchema("Whether to include order history"))
	schema.AddProperty("include_social_info", port.NewBooleanSchema("Whether to include social information"))
	schema.AddProperty("order_limit", port.NewIntegerSchema("Limit for order history",
		func() *float64 { v := float64(1); return &v }(),
		func() *float64 { v := float64(100); return &v }()))

	return schema
}

// OutputSchema returns output result schema
func (t *GetUserInfoTool) OutputSchema() *port.JSONSchema {
	return port.NewObjectSchema(
		"User information result",
		map[string]*port.JSONSchema{
			"user_id":       port.NewStringSchema("User ID", true),
			"profile":       port.NewObjectSchema("User profile", nil, nil),
			"preferences":   port.NewObjectSchema("User preferences", nil, nil),
			"order_history": port.NewArraySchema("Order history", nil),
			"social_info":   port.NewObjectSchema("Social information", nil, nil),
			"retrieved_at":  port.NewStringSchema("Data retrieval time", true),
		},
		[]string{"user_id", "retrieved_at"},
	)
}

// Execute executes tool logic
func (t *GetUserInfoTool) Execute(ctx context.Context, inputs map[string]interface{}) (*port.ToolResult, error) {
	// Extract parameters
	includeProfile := false
	if p, ok := inputs["include_profile"].(bool); ok {
		includeProfile = p
	}

	includePreferences := false
	if p, ok := inputs["include_preferences"].(bool); ok {
		includePreferences = p
	}

	includeOrderHistory := false
	if p, ok := inputs["include_order_history"].(bool); ok {
		includeOrderHistory = p
	}

	includeSocialInfo := false
	if p, ok := inputs["include_social_info"].(bool); ok {
		includeSocialInfo = p
	}

	orderLimit := 10
	if p, ok := inputs["order_limit"].(float64); ok {
		orderLimit = int(p)
	}

	// TODO: Actual user info retrieval logic
	// This should call user-core-service gRPC interface
	result := t.getUserInfo(ctx, includeProfile, includePreferences, includeOrderHistory, includeSocialInfo, orderLimit)

	return port.NewToolResult(map[string]interface{}{
		"user_id":       result.UserID,
		"profile":       result.Profile,
		"preferences":   result.Preferences,
		"order_history": result.OrderHistory,
		"social_info":   result.SocialInfo,
		"retrieved_at":  result.RetrievedAt,
	}), nil
}

// UserInfoResult user information result
type UserInfoResult struct {
	UserID       string                 `json:"user_id"`
	Profile      map[string]interface{} `json:"profile,omitempty"`
	Preferences  map[string]interface{} `json:"preferences,omitempty"`
	OrderHistory []interface{}          `json:"order_history,omitempty"`
	SocialInfo   map[string]interface{} `json:"social_info,omitempty"`
	RetrievedAt  string                 `json:"retrieved_at"`
}

// getUserInfo gets user information (simplified implementation)
func (t *GetUserInfoTool) getUserInfo(_ context.Context, includeProfile, includePreferences, includeOrderHistory, includeSocialInfo bool, orderLimit int) *UserInfoResult {
	// This is a simplified implementation, should call user-core-service gRPC interface in production

	result := &UserInfoResult{
		UserID:      "user-12345",
		RetrievedAt: time.Now().Format(time.RFC3339),
	}

	if includeProfile {
		result.Profile = map[string]interface{}{
			"name":     "John Doe",
			"email":    "<EMAIL>",
			"phone":    "+1234567890",
			"location": "New York, NY",
			"avatar":   "https://example.com/avatar.jpg",
		}
	}

	if includePreferences {
		result.Preferences = map[string]interface{}{
			"language":           "en",
			"timezone":           "America/New_York",
			"notifications":      true,
			"preferred_services": []string{"photography", "catering", "music"},
		}
	}

	if includeOrderHistory {
		orders := make([]interface{}, 0)
		for i := 0; i < orderLimit && i < 5; i++ { // Max 5 mock orders
			orders = append(orders, map[string]interface{}{
				"order_id":   fmt.Sprintf("order-%d", i+1),
				"service":    "Photography Service",
				"amount":     299.99,
				"status":     "completed",
				"created_at": time.Now().AddDate(0, -i, 0).Format(time.RFC3339),
			})
		}
		result.OrderHistory = orders
	}

	if includeSocialInfo {
		result.SocialInfo = map[string]interface{}{
			"followers":  1250,
			"following":  340,
			"posts":      89,
			"reputation": 4.8,
		}
	}

	return result
}
