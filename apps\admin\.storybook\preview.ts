/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 19:42:00
 * Modified: 2025-01-23 19:42:00
 */
import React from 'react';
import type { Preview } from '@storybook/react';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import 'antd/dist/reset.css';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  decorators: [
    (Story) => (
      <BrowserRouter>
        <ConfigProvider>
          <AntdApp>
            <Story />
          </AntdApp>
        </ConfigProvider>
      </BrowserRouter>
    ),
  ],
};

export default preview; 