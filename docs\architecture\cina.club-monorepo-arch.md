﻿cina.club-monorepo/
├── .config/
│   ├── config.dev.yaml.template
│   ├── config.staging.yaml.template
│   └── config.prod.yaml.template
├── .github/
│   ├── ISSUE_TEMPLATE/
│   │   ├── bug_report.md
│   │   └── feature_request.md
│   └── workflows/
│       ├── ci-backend.yml
│       ├── ci-frontend.yml
│       ├── release.yml
│       ├── deploy.yml
│       └── generate.yml
├── .vscode/
│   ├── extensions.json
│   ├── launch.json
│   └── settings.json
├── apps/
│   ├── android/
│   │   ├── app/
│   │   │   ├── build.gradle.kts
│   │   │   └── src/main/java/com/cinaclub/
│   │   │       ├── CinaClubApplication.java
│   │   │       ├── MainActivity.java
│   │   │       └── nativemodules/
│   │   │           ├── AICoreModule.kt
│   │   │           ├── CoreGoBridgePackage.kt
│   │   │           ├── CryptoModule.kt
│   │   │           └── DataSyncModule.kt
│   │   ├── build-logic/
│   │   ├── core/
│   │   │   ├── common/
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── go-bridge/
│   │   ├── feature_auth/
│   │   ├── feature_chat/
│   │   ├── feature_pkb/
│   │   └── libs/
│   │       └── core-go.aar
│   ├── apple/
│   │   ├── CinaClub.xcodeproj/
│   │   ├── Frameworks/
│   │   │   └── CoreGo.xcframework
│   │   ├── Packages/
│   │   │   ├── AppCore/
│   │   │   ├── DataLayer/
│   │   │   ├── DesignSystem/
│   │   │   ├── Feature/
│   │   │   └── GoBridge/
│   │   └── Targets/
│   │       ├── iOS_iPadOS/
│   │       ├── macOS/
│   │       ├── visionOS/
│   │       └── watchOS/
│   ├── web/
│   │   ├── src/
│   │   │   ├── lib/
│   │   │   │   └── client/
│   │   │   │       └── wasm_loader.ts
│   │   │   └── routes/
│   │   └── static/
│   │       └── core.wasm
│   └── windows/
│       ├── CinaClub.sln
│       ├── CinaClub.App/
│       ├── CinaClub.Core/
│       ├── CinaClub.Infrastructure/
│       │   └── GoBridge/
│       └── libs/
│           └── x64/
│               ├── core_go.dll
│               └── core_go.h
├── core/
│   ├── api/
│   │   ├── buf.gen.yaml
│   │   ├── buf.lock
│   │   ├── buf.yaml
│   │   └── proto/
│   │       └── v1/
│   │           ├── activity_feed.proto
│   │           ├── ai_assistant.proto
│   │           ├── analytics.proto
│   │           ├── billing.proto
│   │           ├── calendar_sync.proto
│   │           ├── chat.proto
│   │           ├── cina_coin_ledger.proto
│   │           ├── cloud_sync.proto
│   │           ├── common.proto
│   │           ├── community_forum.proto
│   │           ├── community_qa.proto
│   │           ├── content_moderation.proto
│   │           ├── digital_twin.proto
│   │           ├── embedding.proto
│   │           ├── errors.proto
│   │           ├── family_tree.proto
│   │           ├── file_storage.proto
│   │           ├── gamification.proto
│   │           ├── key_management_proxy.proto
│   │           ├── location.proto
│   │           ├── memory.proto
│   │           ├── metaverse_engine.proto
│   │           ├── model_management.proto
│   │           ├── notification_dispatch.proto
│   │           ├── payment.proto
│   │           ├── personal_kb.proto
│   │           ├── review.proto
│   │           ├── routines.proto
│   │           ├── schedule.proto
│   │           ├── search.proto
│   │           ├── service_offering.proto
│   │           ├── short_video.proto
│   │           ├── social.proto
│   │           └── user_core.proto
│   ├── aic/
│   │   ├── cgo_bindings.go
│   │   ├── engine.go
│   │   ├── engine_llama_cpp.go
│   │   └── exports_mobile.go
│   ├── crypto/
│   │   ├── e2ee.go
│   │   ├── exports_mobile.go
│   │   └── exports_wasm.go
│   ├── datasync/
│   │   ├── chunker.go
│   │   ├── engine.go
│   │   ├── version_vector.go
│   │   ├── exports_mobile.go
│   │   └── exports_wasm.go
│   ├── models/
│   │   ├── asset.go
│   │   ├── content.go
│   │   ├── service.go
│   │   ├── social.go
│   │   └── user.go
│   └── go.mod
├── docs/
│   ├── architecture/
│   │   ├── L1_Principles_and_Vision/
│   │   │   ├── 01_architectural_principles.md
│   │   │   └── 02_technology_radar.md
│   │   ├── L2_System_Landscape/
│   │   │   ├── 01_system_context_and_domains.md
│   │   │   ├── 02_service_communication_patterns.md
│   │   │   └── 03_platform_data_flow.md
│   │   ├── L3_Core_Capabilities_Deep_Dive/
│   │   │   ├── 01_authentication_and_authorization.md
│   │   │   ├── 02_observability_stack.md
│   │   │   ├── 03_data_management_and_persistence.md
│   │   │   ├── 04_asynchronous_processing_and_events.md
│   │   │   ├── 05_security_and_privacy_model.md
│   │   │   └── 06_ai_and_workflow_engine.md
│   │   └── L4_Service_Design_Patterns/
│   │       ├── 01_clean_architecture_for_services.md
│   │       ├── 02_repository_and_unit_of_work_pattern.md
│   │       └── 03_error_handling_best_practices.md
│   ├── adr/
│   │   └── 0001-example-adr.md
│   ├── diagrams/
│   │   └── system_context_diagram.md
│   └── srs/
│       ├── platform-overall-srs.md
│       ├── packages/
│       │   ├── pkg-auth-srs.md
│       │   ├── pkg-config-srs.md
│       │   ├── pkg-database-srs.md
│       │   ├── pkg-errors-srs.md
│       │   ├── pkg-logger-srs.md
│       │   ├── pkg-messaging-srs.md
│       │   ├── pkg-middleware-srs.md
│       │   ├── pkg-tracing-srs.md
│       │   └── pkg-workflow-srs.md
│       └── services/
│           ├── activity-feed-service-srs.md
│           ├── ai-assistant-service-srs.md
│           ├── analytics-service-srs.md
│           ├── billing-service-srs.md
│           ├── calendar-sync-service-srs.md
│           ├── chat-api-service-srs.md
│           ├── chat-websocket-server-srs.md
│           ├── cina-coin-ledger-service-srs.md
│           ├── cloud-sync-service-srs.md
│           ├── community-forum-service-srs.md
│           ├── community-qa-service-srs.md
│           ├── content-moderation-service-srs.md
│           ├── digital-twin-service-srs.md
│           ├── embedding-service-srs.md
│           ├── family-tree-service-srs.md
│           ├── file-storage-service-srs.md
│           ├── gamification-service-srs.md
│           ├── key-management-proxy-service-srs.md
│           ├── location-service-srs.md
│           ├── memory-service-srs.md
│           ├── metaverse-engine-service-srs.md
│           ├── model-management-service-srs.md
│           ├── notification-dispatch-service-srs.md
│           ├── payment-service-srs.md
│           ├── personal-kb-service-srs.md
│           ├── review-service-srs.md
│           ├── routines-service-srs.md
│           ├── schedule-service-srs.md
│           ├── search-indexer-service-srs.md
│           ├── search-service-srs.md
│           ├── service-offering-service-srs.md
│           ├── short-video-service-srs.md
│           ├── social-service-srs.md
│           └── user-core-service-srs.md
├── infra/
│   ├── docker/
│   │   ├── base/
│   │   │   ├── Dockerfile.go
│   │   │   └── Dockerfile.python
│   │   ├── dev/
│   │   │   └── docker-compose.yml
│   │   └── services/
│   │       ├── activity-feed.Dockerfile
│   │       ├── ai-assistant.Dockerfile
│   │       ├── analytics.Dockerfile
│   │       ├── billing.Dockerfile
│   │       ├── calendar-sync.Dockerfile
│   │       ├── chat-api.Dockerfile
│   │       ├── chat-websocket-server-srs.md
│   │       ├── cina-coin-ledger.Dockerfile
│   │       ├── cloud-sync.Dockerfile
│   │       ├── community-forum.Dockerfile
│   │       ├── community-qa.Dockerfile
│   │       ├── content-moderation.Dockerfile
│   │       ├── digital-twin.Dockerfile
│   │       ├── embedding.Dockerfile
│   │       ├── family-tree.Dockerfile
│   │       ├── file-storage.Dockerfile
│   │       ├── gamification.Dockerfile
│   │       ├── key-management-proxy.Dockerfile
│   │       ├── location.Dockerfile
│   │       ├── memory.Dockerfile
│   │       ├── metaverse-engine.Dockerfile
│   │       ├── model-management.Dockerfile
│   │       ├── notification-dispatch.Dockerfile
│   │       ├── payment.Dockerfile
│   │       ├── personal-kb.Dockerfile
│   │       ├── review.Dockerfile
│   │       ├── routines.Dockerfile
│   │       ├── schedule.Dockerfile
│   │       ├── search-indexer.Dockerfile
│   │       ├── search.Dockerfile
│   │       ├── service-offering.Dockerfile
│   │       ├── short-video.Dockerfile
│   │       ├── social.Dockerfile
│   │       └── user-core.Dockerfile
│   ├── kubernetes/
│   │   ├── base/
│   │   ├── overlays/
│   │   └── system/
│   └── terraform/
│       ├── environments/
│       └── modules/
├── pkg/
│   ├── auth/
│   │   ├── context.go
│   │   ├── interceptor/
│   │   └── jwks/
│   ├── config/
│   │   └── config.go
│   ├── database/
│   │   ├── postgres.go
│   │   └── redis.go
│   ├── errors/
│   │   └── errors.go
│   ├── logger/
│   │   └── logger.go
│   ├── messaging/
│   │   ├── consumer.go
│   │   └── producer.go
│   ├── middleware/
│   │   ├── logging.go
│   │   ├── metrics.go
│   │   ├── recovery.go
│   │   └── tracing.go
│   ├── tracing/
│   │   └── tracing.go
│   ├── workflow/
│   │   ├── engine.go
│   │   └── interface.go
│   └── utils/
│       ├── conv/
│       ├── crypto/
│       ├── rand/
│       ├── slice/
│       ├── str/
│       └── timeutil/
├── scripts/
│   ├── ci/
│   │   ├── lint.sh
│   │   └── test.sh
│   ├── db/
│   │   ├── migrate.sh
│   │   └── seed.sh
│   ├── gen/
│   │   ├── core.sh
│   │   └── proto.sh
│   ├── lib/
│   │   └── helpers.sh
│   └── setup/
│       └── dev-env.sh
├── services/
│   ├── activity-feed-service/
│   ├── ai-assistant-service/
│   ├── analytics-service/
│   ├── api-gateway/
│   ├── billing-service/
│   ├── calendar-sync-service/
│   ├── chat-api-service/
│   ├── chat-websocket-server/
│   ├── cina-coin-ledger-service/
│   ├── cloud-sync-service/
│   ├── community-forum-service/
│   ├── community-qa-service/
│   ├── content-moderation-service/
│   ├── digital-twin-service/
│   ├── embedding-service/
│   ├── family-tree-service/
│   ├── file-storage-service/
│   ├── gamification-service/
│   ├── key-management-proxy-service/
│   ├── location-service/
│   ├── memory-service/
│   ├── metaverse-engine-service/
│   ├── model-management-service/
│   ├── notification-dispatch-service/
│   ├── payment-service/
│   ├── personal-kb-service/
│   ├── review-service/
│   ├── routines-service/
│   ├── schedule-service/
│   ├── search-indexer-service/
│   ├── search-service/
│   ├── service-offering-service/
│   ├── short-video-service/
│   ├── social-service/
│   └── user-core-service/
├── tools/
│   ├── codegen/
│   │   └── main.go
│   ├── config-validator/
│   │   └── main.go
│   └── license-scanner/
│       └── main.go
│
├── .dockerignore
├── .editorconfig
├── .gitignore
├── go.mod
├── go.work
├── package.json
├── pnpm-workspace.yaml
├── README.md
└── turbo.json