/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:15:00
 * Modified: 2025-01-23 16:15:00
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Space,
  Button,
  Table,
  Tag,
  Alert,
  Tabs,
  List,
  Badge,
  Tooltip,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  Divider,
} from 'antd';
import {
  MonitorOutlined,
  ServerOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FireOutlined,
  DatabaseOutlined,
  CloudOutlined,
  SettingOutlined,
  BellOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  LineChartOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Line, Gauge, Column } from '@ant-design/plots';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    usage: number;
  };
  disk: {
    used: number;
    total: number;
    usage: number;
  };
  network: {
    inbound: number;
    outbound: number;
  };
  uptime: number;
  timestamp: string;
}

interface ErrorLog {
  id: string;
  timestamp: string;
  level: 'error' | 'warning' | 'critical';
  service: string;
  message: string;
  count: number;
  lastOccurrence: string;
  resolved: boolean;
}

interface AlertRule {
  id: string;
  name: string;
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals';
  threshold: number;
  duration: number;
  enabled: boolean;
  notifications: string[];
  createdAt: string;
  lastTriggered?: string;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'good' | 'warning' | 'critical';
}

const SystemMonitor: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isAlertModalVisible, setIsAlertModalVisible] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<AlertRule | null>(null);
  const [form] = Form.useForm();
  const [realTimeData, setRealTimeData] = useState<SystemMetrics[]>([]);
  const queryClient = useQueryClient();

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      const newMetric: SystemMetrics = {
        cpu: {
          usage: Math.random() * 100,
          cores: 8,
          loadAverage: [Math.random() * 2, Math.random() * 2, Math.random() * 2]
        },
        memory: {
          used: Math.random() * 16 * 1024 * 1024 * 1024,
          total: 16 * 1024 * 1024 * 1024,
          usage: Math.random() * 100
        },
        disk: {
          used: Math.random() * 500 * 1024 * 1024 * 1024,
          total: 1000 * 1024 * 1024 * 1024,
          usage: Math.random() * 100
        },
        network: {
          inbound: Math.random() * 100,
          outbound: Math.random() * 100
        },
        uptime: 86400 * 30 + Math.random() * 86400,
        timestamp: new Date().toISOString()
      };

      setRealTimeData(prev => [...prev.slice(-23), newMetric]);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Mock data queries
  const { data: currentMetrics, isLoading: metricsLoading } = useQuery({
    queryKey: ['system', 'metrics'],
    queryFn: async (): Promise<SystemMetrics> => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        cpu: {
          usage: 45.2,
          cores: 8,
          loadAverage: [1.2, 1.5, 1.8]
        },
        memory: {
          used: 12 * 1024 * 1024 * 1024,
          total: 16 * 1024 * 1024 * 1024,
          usage: 75.0
        },
        disk: {
          used: 350 * 1024 * 1024 * 1024,
          total: 1000 * 1024 * 1024 * 1024,
          usage: 35.0
        },
        network: {
          inbound: 45.6,
          outbound: 23.4
        },
        uptime: 86400 * 30 + 3600 * 12,
        timestamp: new Date().toISOString()
      };
    },
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  const { data: errorLogs = [], isLoading: errorsLoading } = useQuery({
    queryKey: ['system', 'errors'],
    queryFn: async (): Promise<ErrorLog[]> => {
      await new Promise(resolve => setTimeout(resolve, 600));
      return [
        {
          id: '1',
          timestamp: '2025-01-23T15:30:00Z',
          level: 'error',
          service: 'user-service',
          message: 'Database connection timeout',
          count: 5,
          lastOccurrence: '2025-01-23T15:30:00Z',
          resolved: false
        },
        {
          id: '2',
          timestamp: '2025-01-23T15:25:00Z',
          level: 'warning',
          service: 'api-gateway',
          message: 'High response time detected',
          count: 12,
          lastOccurrence: '2025-01-23T15:28:00Z',
          resolved: false
        },
        {
          id: '3',
          timestamp: '2025-01-23T15:20:00Z',
          level: 'critical',
          service: 'payment-service',
          message: 'Payment processing failed',
          count: 2,
          lastOccurrence: '2025-01-23T15:22:00Z',
          resolved: true
        }
      ];
    }
  });

  const { data: alertRules = [], isLoading: alertsLoading } = useQuery({
    queryKey: ['system', 'alerts'],
    queryFn: async (): Promise<AlertRule[]> => {
      await new Promise(resolve => setTimeout(resolve, 500));
      return [
        {
          id: '1',
          name: 'High CPU Usage',
          metric: 'cpu.usage',
          condition: 'greater_than',
          threshold: 80,
          duration: 300,
          enabled: true,
          notifications: ['<EMAIL>', '<EMAIL>'],
          createdAt: '2025-01-20T10:00:00Z',
          lastTriggered: '2025-01-22T14:30:00Z'
        },
        {
          id: '2',
          name: 'Low Memory Available',
          metric: 'memory.usage',
          condition: 'greater_than',
          threshold: 90,
          duration: 180,
          enabled: true,
          notifications: ['<EMAIL>'],
          createdAt: '2025-01-20T10:00:00Z'
        },
        {
          id: '3',
          name: 'Disk Space Warning',
          metric: 'disk.usage',
          condition: 'greater_than',
          threshold: 85,
          duration: 600,
          enabled: false,
          notifications: ['<EMAIL>'],
          createdAt: '2025-01-21T15:00:00Z'
        }
      ];
    }
  });

  const { data: performanceMetrics = [] } = useQuery({
    queryKey: ['system', 'performance'],
    queryFn: async (): Promise<PerformanceMetric[]> => {
      await new Promise(resolve => setTimeout(resolve, 400));
      return [
        { name: 'Average Response Time', value: 245, unit: 'ms', trend: 'down', status: 'good' },
        { name: 'Requests per Second', value: 1250, unit: 'req/s', trend: 'up', status: 'good' },
        { name: 'Error Rate', value: 0.5, unit: '%', trend: 'stable', status: 'good' },
        { name: 'Database Connections', value: 45, unit: 'conn', trend: 'stable', status: 'good' },
        { name: 'Cache Hit Rate', value: 95.2, unit: '%', trend: 'up', status: 'good' },
        { name: 'Queue Length', value: 12, unit: 'items', trend: 'down', status: 'warning' }
      ];
    }
  });

  // Mutations
  const createAlertMutation = useMutation({
    mutationFn: async (data: any) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { ...data, id: Date.now().toString() };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system', 'alerts'] });
      setIsAlertModalVisible(false);
      form.resetFields();
    }
  });

  const updateAlertMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      await new Promise(resolve => setTimeout(resolve, 800));
      return { id, ...data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system', 'alerts'] });
      setIsAlertModalVisible(false);
      setSelectedAlert(null);
    }
  });

  const toggleAlertMutation = useMutation({
    mutationFn: async ({ id, enabled }: { id: string; enabled: boolean }) => {
      await new Promise(resolve => setTimeout(resolve, 500));
      return { id, enabled };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system', 'alerts'] });
    }
  });

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return '#52c41a';
      case 'warning': return '#faad14';
      case 'critical': return '#f5222d';
      default: return '#1890ff';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '→';
      default: return '→';
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'red';
      case 'error': return 'orange';
      case 'warning': return 'yellow';
      default: return 'blue';
    }
  };

  const errorColumns = [
    {
      title: 'Level',
      dataIndex: 'level',
      key: 'level',
      render: (level: string) => (
        <Tag color={getLevelColor(level)}>
          {level.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Service',
      dataIndex: 'service',
      key: 'service',
      render: (service: string) => <Text code>{service}</Text>
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message'
    },
    {
      title: 'Count',
      dataIndex: 'count',
      key: 'count',
      render: (count: number) => <Badge count={count} />
    },
    {
      title: 'Last Occurrence',
      dataIndex: 'lastOccurrence',
      key: 'lastOccurrence',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: 'Status',
      dataIndex: 'resolved',
      key: 'resolved',
      render: (resolved: boolean) => (
        <Tag color={resolved ? 'green' : 'red'}>
          {resolved ? 'Resolved' : 'Active'}
        </Tag>
      )
    }
  ];

  const alertColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Metric',
      dataIndex: 'metric',
      key: 'metric',
      render: (metric: string) => <Text code>{metric}</Text>
    },
    {
      title: 'Condition',
      key: 'condition',
      render: (_, record: AlertRule) => (
        <Text>
          {record.condition.replace('_', ' ')} {record.threshold}
        </Text>
      )
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => `${duration}s`
    },
    {
      title: 'Status',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean, record: AlertRule) => (
        <Switch
          checked={enabled}
          onChange={(checked) => toggleAlertMutation.mutate({ id: record.id, enabled: checked })}
          loading={toggleAlertMutation.isPending}
        />
      )
    },
    {
      title: 'Last Triggered',
      dataIndex: 'lastTriggered',
      key: 'lastTriggered',
      render: (time?: string) => 
        time ? new Date(time).toLocaleString() : <Text type="secondary">Never</Text>
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: AlertRule) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => {
              setSelectedAlert(record);
              form.setFieldsValue(record);
              setIsAlertModalVisible(true);
            }}
          />
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />}
          />
        </Space>
      )
    }
  ];

  const cpuGaugeConfig = {
    percent: (currentMetrics?.cpu.usage || 0) / 100,
    range: {
      color: ['#30BF78', '#FAAD14', '#F4664A'],
    },
    indicator: {
      pointer: {
        style: {
          stroke: '#D0D0D0',
        },
      },
      pin: {
        style: {
          stroke: '#D0D0D0',
        },
      },
    },
    statistic: {
      content: {
        style: {
          fontSize: '36px',
          lineHeight: '36px',
        },
        formatter: () => `${(currentMetrics?.cpu.usage || 0).toFixed(1)}%`,
      },
    },
  };

  const memoryGaugeConfig = {
    percent: (currentMetrics?.memory.usage || 0) / 100,
    range: {
      color: ['#30BF78', '#FAAD14', '#F4664A'],
    },
    statistic: {
      content: {
        formatter: () => `${(currentMetrics?.memory.usage || 0).toFixed(1)}%`,
      },
    },
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <MonitorOutlined /> System Monitor
        </Title>
        <Paragraph type="secondary">
          Monitor system resources, performance metrics, and configure alerts.
        </Paragraph>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <LineChartOutlined />
              Overview
            </span>
          }
          key="overview"
        >
          {/* System Health Alert */}
          <Alert
            message="System Status: Operational"
            description="All systems are running normally. No critical issues detected."
            type="success"
            showIcon
            style={{ marginBottom: '24px' }}
          />

          {/* Resource Usage */}
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={8}>
              <Card title="CPU Usage" loading={metricsLoading}>
                <div style={{ textAlign: 'center' }}>
                  <Gauge {...cpuGaugeConfig} height={200} />
                  <div style={{ marginTop: '16px' }}>
                    <Text type="secondary">
                      Load Average: {currentMetrics?.cpu.loadAverage.map(l => l.toFixed(2)).join(', ')}
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Memory Usage" loading={metricsLoading}>
                <div style={{ textAlign: 'center' }}>
                  <Gauge {...memoryGaugeConfig} height={200} />
                  <div style={{ marginTop: '16px' }}>
                    <Text type="secondary">
                      {formatBytes(currentMetrics?.memory.used || 0)} / {formatBytes(currentMetrics?.memory.total || 0)}
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Disk Usage" loading={metricsLoading}>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Progress
                    type="circle"
                    percent={currentMetrics?.disk.usage || 0}
                    size={160}
                    format={() => `${(currentMetrics?.disk.usage || 0).toFixed(1)}%`}
                  />
                  <div style={{ marginTop: '16px' }}>
                    <Text type="secondary">
                      {formatBytes(currentMetrics?.disk.used || 0)} / {formatBytes(currentMetrics?.disk.total || 0)}
                    </Text>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>

          {/* System Statistics */}
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Uptime"
                  value={formatUptime(currentMetrics?.uptime || 0)}
                  prefix={<ServerOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Network In"
                  value={currentMetrics?.network.inbound || 0}
                  suffix="MB/s"
                  prefix={<CloudOutlined />}
                  precision={1}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Network Out"
                  value={currentMetrics?.network.outbound || 0}
                  suffix="MB/s"
                  prefix={<CloudOutlined />}
                  precision={1}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="CPU Cores"
                  value={currentMetrics?.cpu.cores || 0}
                  prefix={<ServerOutlined />}
                />
              </Card>
            </Col>
          </Row>

          {/* Performance Metrics */}
          <Card title="Performance Metrics">
            <Row gutter={16}>
              {performanceMetrics.map((metric, index) => (
                <Col span={8} key={index} style={{ marginBottom: '16px' }}>
                  <Card size="small">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Text strong>{metric.name}</Text>
                        <Text>{getTrendIcon(metric.trend)}</Text>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text style={{ fontSize: '24px', color: getStatusColor(metric.status) }}>
                          {metric.value.toLocaleString()}
                        </Text>
                        <Text type="secondary">{metric.unit}</Text>
                      </div>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <ExclamationCircleOutlined />
              Error Logs
            </span>
          }
          key="errors"
        >
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div>
                <Title level={4}>System Error Logs</Title>
                <Paragraph type="secondary">
                  Monitor and track system errors and warnings.
                </Paragraph>
              </div>
              <Button icon={<ReloadOutlined />}>
                Refresh
              </Button>
            </div>

            <Table
              columns={errorColumns}
              dataSource={errorLogs}
              loading={errorsLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <BellOutlined />
              Alerts
            </span>
          }
          key="alerts"
        >
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div>
                <Title level={4}>Alert Rules</Title>
                <Paragraph type="secondary">
                  Configure monitoring alerts and notifications.
                </Paragraph>
              </div>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setSelectedAlert(null);
                  form.resetFields();
                  setIsAlertModalVisible(true);
                }}
              >
                Create Alert
              </Button>
            </div>

            <Table
              columns={alertColumns}
              dataSource={alertRules}
              loading={alertsLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* Alert Rule Modal */}
      <Modal
        title={selectedAlert ? 'Edit Alert Rule' : 'Create Alert Rule'}
        open={isAlertModalVisible}
        onCancel={() => {
          setIsAlertModalVisible(false);
          setSelectedAlert(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => {
            if (selectedAlert) {
              updateAlertMutation.mutate({ id: selectedAlert.id, ...values });
            } else {
              createAlertMutation.mutate(values);
            }
          }}
        >
          <Form.Item
            name="name"
            label="Alert Name"
            rules={[{ required: true, message: 'Please enter alert name' }]}
          >
            <Input placeholder="Enter alert name" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="metric"
                label="Metric"
                rules={[{ required: true, message: 'Please select metric' }]}
              >
                <Select placeholder="Select metric">
                  <Option value="cpu.usage">CPU Usage</Option>
                  <Option value="memory.usage">Memory Usage</Option>
                  <Option value="disk.usage">Disk Usage</Option>
                  <Option value="network.inbound">Network Inbound</Option>
                  <Option value="network.outbound">Network Outbound</Option>
                  <Option value="response.time">Response Time</Option>
                  <Option value="error.rate">Error Rate</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="condition"
                label="Condition"
                rules={[{ required: true, message: 'Please select condition' }]}
              >
                <Select placeholder="Select condition">
                  <Option value="greater_than">Greater Than</Option>
                  <Option value="less_than">Less Than</Option>
                  <Option value="equals">Equals</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="threshold"
                label="Threshold"
                rules={[{ required: true, message: 'Please enter threshold' }]}
              >
                <InputNumber 
                  placeholder="80"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="duration"
                label="Duration (seconds)"
                rules={[{ required: true, message: 'Please enter duration' }]}
              >
                <InputNumber 
                  placeholder="300"
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notifications"
            label="Notification Recipients"
            rules={[{ required: true, message: 'Please add recipients' }]}
          >
            <Select
              mode="tags"
              placeholder="Enter email addresses"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={createAlertMutation.isPending || updateAlertMutation.isPending}
              >
                {selectedAlert ? 'Update' : 'Create'} Alert
              </Button>
              <Button onClick={() => setIsAlertModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SystemMonitor; 