# CINA.CLUB Platform - AI Assistant Service Ingress Configuration
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# AI Assistant Service Ingress - AI-powered Assistant API
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-assistant-ingress
  namespace: ai-assistant
  labels:
    app: ai-assistant-service
    component: ingress
    tier: application
    service: ai-assistant
  annotations:
    # Kong Ingress Controller configuration
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # AI services need special rate limiting + response-based limiting
    konghq.com/plugins: "jwt-validator-user-service, rate-limit-user, response-rate-limit, cors-global, prometheus-metrics, opentelemetry-tracing, request-id, request-size-limit"
    
    # Protocol configuration for gRPC backend
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # AI-optimized timeouts (longer for AI processing)
    konghq.com/read-timeout: "120000"       # 2 minutes for AI processing
    konghq.com/write-timeout: "120000"      # 2 minutes
    konghq.com/connect-timeout: "5000"      # 5 seconds
    
    # Large request body support for AI inputs
    konghq.com/request-buffering: "true"
    konghq.com/response-buffering: "true"
    
    # Upstream configuration
    konghq.com/host-header: "ai-assistant-service.ai-assistant.svc.cluster.local"
    
    # Description and metadata
    description: "AI Assistant Service for AI-powered chat and assistance"
    service-owner: "<EMAIL>"
    api-version: "v1"

spec:
  # TLS configuration
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  
  rules:
    - host: "api.cina.club"
      http:
        paths:
          # AI Chat endpoints (JWT required)
          - path: /api/v1/ai/chat
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # AI Completions
          - path: /api/v1/ai/completions
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # AI Embeddings
          - path: /api/v1/ai/embeddings
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # AI Model Management
          - path: /api/v1/ai/models
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # AI Conversation History
          - path: /api/v1/ai/conversations
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080

---
# Premium AI Endpoints (for premium users with higher limits)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-assistant-premium-ingress
  namespace: ai-assistant
  labels:
    app: ai-assistant-service
    component: ingress
    tier: premium
    security-level: premium
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Premium AI endpoints with higher rate limits
    konghq.com/plugins: "jwt-validator-user-service, rate-limit-premium, response-rate-limit, cors-global, prometheus-metrics, opentelemetry-tracing, request-id"
    
    # Protocol configuration
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Extended timeouts for premium features
    konghq.com/read-timeout: "300000"       # 5 minutes for premium AI
    konghq.com/write-timeout: "300000"      # 5 minutes
    
    # Description
    description: "Premium AI endpoints with extended capabilities and higher limits"

spec:
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  
  rules:
    - host: "api.cina.club"
      http:
        paths:
          # Premium AI endpoints with /premium prefix
          - path: /api/v1/ai/premium/chat
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # Premium AI image generation
          - path: /api/v1/ai/premium/images
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # Premium AI fine-tuning
          - path: /api/v1/ai/premium/fine-tune
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080

---
# Public AI Endpoints (for demonstration, limited access)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-assistant-public-ingress
  namespace: ai-assistant
  labels:
    app: ai-assistant-service
    component: ingress
    tier: public
    security-level: public
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    
    # Public AI endpoints with strict IP-based rate limiting
    konghq.com/plugins: "rate-limit-ip, cors-public-api, prometheus-metrics, request-id, request-size-limit"
    
    # Protocol configuration
    konghq.com/protocol: "grpc"
    konghq.com/path: "/"
    
    # Shorter timeouts for public access
    konghq.com/read-timeout: "30000"        # 30 seconds for public AI
    konghq.com/write-timeout: "30000"       # 30 seconds
    
    # Description
    description: "Public AI demo endpoints with limited capabilities"

spec:
  tls:
    - hosts:
        - "api.cina.club"
      secretName: "cina-club-api-tls"
  
  rules:
    - host: "api.cina.club"
      http:
        paths:
          # Public AI demo
          - path: /api/v1/ai/public/demo
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # AI service health and capabilities info
          - path: /api/v1/ai/health
            pathType: Exact
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080
          
          # AI model information (public)
          - path: /api/v1/ai/public/models
            pathType: Prefix
            backend:
              service:
                name: ai-assistant-service
                port:
                  number: 8080

---
# Kong Service for AI Assistant Service
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: ai-assistant-kong-service
  namespace: ai-assistant
  labels:
    app: ai-assistant-service
    component: kong-service
  annotations:
    description: "Kong service configuration for AI Assistant Service"

spec:
  # Backend service configuration
  host: "ai-assistant-service.ai-assistant.svc.cluster.local"
  port: 8080
  protocol: "grpc"                         # gRPC protocol for backend
  
  # AI-optimized connection settings (longer timeouts)
  connect_timeout: 5000                    # 5 seconds
  read_timeout: 120000                     # 2 minutes for AI processing
  write_timeout: 120000                    # 2 minutes
  
  # Retry configuration (fewer retries for expensive AI operations)
  retries: 1                               # Only 1 retry for AI services
  
  # Load balancing
  path: "/"

---
# Kong Upstream for AI Assistant Service
apiVersion: configuration.konghq.com/v1
kind: KongUpstream
metadata:
  name: ai-assistant-upstream
  namespace: ai-assistant
  labels:
    app: ai-assistant-service
    component: kong-upstream
  annotations:
    description: "Kong upstream for AI Assistant Service with AI-optimized settings"

spec:
  # Load balancing algorithm for AI workloads
  algorithm: "least-connections"           # Better for long-running AI requests
  
  # Health checks for AI services
  healthchecks:
    active:
      http_path: "/health"
      https_verify_certificate: false
      healthy:
        interval: 30                       # Less frequent for AI services
        successes: 2
      unhealthy:
        interval: 20
        tcp_failures: 3                    # More tolerance for AI processing
        http_failures: 3
        timeouts: 3
    
    passive:
      healthy:
        successes: 3
      unhealthy:
        tcp_failures: 3
        http_failures: 3
        timeouts: 3
  
  # Connection slots for AI service
  slots: 500                               # Fewer slots for resource-intensive AI 