/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import (
	"crypto/tls"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl"
	"github.com/segmentio/kafka-go/sasl/plain"
	"github.com/segmentio/kafka-go/sasl/scram"
)

// NewKafkaWriter 创建 Kafka Writer
func NewKafkaWriter(cfg KafkaProducerConfig) (*kafka.Writer, error) {
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid producer config: %w", err)
	}

	// 创建基本的写入器
	writer := &kafka.Writer{
		Addr:         kafka.TCP(cfg.Brokers...),
		BatchSize:    cfg.WriterConfig.BatchSize,
		BatchTimeout: cfg.WriterConfig.BatchTimeout,
		Async:        cfg.WriterConfig.Async,
		WriteTimeout: cfg.WriterConfig.WriteTimeout,
		ReadTimeout:  cfg.WriterConfig.ReadTimeout,
	}

	// 配置压缩算法
	if compression := getCompressionAlgorithm(cfg.WriterConfig.Compression); compression != "" {
		// 注意：新版本的 kafka-go 中压缩算法设置方式可能不同
		// 这里需要根据实际版本调整
	}

	// 配置安全选项
	if cfg.Security.SASL.Enabled || cfg.Security.TLS.Enabled {
		dialer, err := KafkaDialer(cfg.Security)
		if err != nil {
			return nil, fmt.Errorf("failed to create dialer: %w", err)
		}
		writer.Transport = &kafka.Transport{
			Dial: dialer.DialFunc,
		}
	}

	return writer, nil
}

// NewKafkaReader 创建 Kafka Reader
func NewKafkaReader(cfg KafkaConsumerConfig) (*kafka.Reader, error) {
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid consumer config: %w", err)
	}

	readerConfig := kafka.ReaderConfig{
		Brokers:        cfg.Brokers,
		GroupID:        cfg.GroupID,
		Topic:          cfg.Topics[0], // 新版本只支持单个topic
		MinBytes:       cfg.ReaderConfig.MinBytes,
		MaxBytes:       cfg.ReaderConfig.MaxBytes,
		MaxWait:        cfg.ReaderConfig.MaxWait,
		CommitInterval: cfg.ReaderConfig.CommitInterval,
	}

	// 配置起始偏移量
	switch cfg.ReaderConfig.StartOffset {
	case "first":
		readerConfig.StartOffset = kafka.FirstOffset
	case "last":
		readerConfig.StartOffset = kafka.LastOffset
	default:
		readerConfig.StartOffset = kafka.LastOffset
	}

	// 配置安全选项
	if cfg.Security.SASL.Enabled {
		saslMechanism, err := getSASLMechanism(cfg.Security.SASL)
		if err != nil {
			return nil, fmt.Errorf("invalid SASL config: %w", err)
		}

		dialer := &kafka.Dialer{
			Timeout:       30 * time.Second,
			DualStack:     true,
			SASLMechanism: saslMechanism,
			TLS:           cfg.Security.TLS.enhancedTLSConfig(),
		}
		readerConfig.Dialer = dialer
	} else if cfg.Security.TLS.Enabled {
		dialer := &kafka.Dialer{
			Timeout:   30 * time.Second,
			DualStack: true,
			TLS:       cfg.Security.TLS.enhancedTLSConfig(),
		}
		readerConfig.Dialer = dialer
	}

	return kafka.NewReader(readerConfig), nil
}

// getCompressionAlgorithm 获取压缩算法名称
func getCompressionAlgorithm(compression string) string {
	switch compression {
	case "", "none":
		return ""
	case "gzip":
		return "gzip"
	case "snappy":
		return "snappy"
	case "lz4":
		return "lz4"
	case "zstd":
		return "zstd"
	default:
		return ""
	}
}

// getSASLMechanism 获取 SASL 认证机制
func getSASLMechanism(cfg SASLConfig) (sasl.Mechanism, error) {
	switch cfg.Mechanism {
	case "PLAIN":
		return plain.Mechanism{
			Username: cfg.Username,
			Password: cfg.Password,
		}, nil
	case "SCRAM-SHA-256":
		mechanism, err := scram.Mechanism(scram.SHA256, cfg.Username, cfg.Password)
		if err != nil {
			return nil, fmt.Errorf("failed to create SCRAM-SHA-256 mechanism: %w", err)
		}
		return mechanism, nil
	case "SCRAM-SHA-512":
		mechanism, err := scram.Mechanism(scram.SHA512, cfg.Username, cfg.Password)
		if err != nil {
			return nil, fmt.Errorf("failed to create SCRAM-SHA-512 mechanism: %w", err)
		}
		return mechanism, nil
	default:
		return nil, fmt.Errorf("unsupported SASL mechanism: %s", cfg.Mechanism)
	}
}

// enhancedTLSConfig 转换为增强的 *tls.Config
func (c *TLSConfig) enhancedTLSConfig() *tls.Config {
	if !c.Enabled {
		return nil
	}

	tlsConfig := &tls.Config{
		InsecureSkipVerify: c.InsecureSkipVerify,
	}

	// 如果有证书文件，加载客户端证书
	if c.CertFile != "" && c.KeyFile != "" {
		cert, err := tls.LoadX509KeyPair(c.CertFile, c.KeyFile)
		if err == nil {
			tlsConfig.Certificates = []tls.Certificate{cert}
		}
	}

	return tlsConfig
}

// KafkaDialer 创建 Kafka 拨号器
func KafkaDialer(cfg KafkaSecurityConfig) (*kafka.Dialer, error) {
	dialer := &kafka.Dialer{
		Timeout:   30 * time.Second,
		DualStack: true,
	}

	if cfg.SASL.Enabled {
		saslMechanism, err := getSASLMechanism(cfg.SASL)
		if err != nil {
			return nil, fmt.Errorf("invalid SASL config: %w", err)
		}
		dialer.SASLMechanism = saslMechanism
	}

	if cfg.TLS.Enabled {
		dialer.TLS = cfg.TLS.enhancedTLSConfig()
	}

	return dialer, nil
}
