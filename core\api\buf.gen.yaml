version: v1
managed:
  enabled: true
  go_package_prefix:
    default: cina.club/core/api
    except:
      - buf.build/googleapis/googleapis
      - buf.build/grpc-ecosystem/grpc-gateway
plugins:
  # Go gRPC
  - plugin: buf.build/protocolbuffers/go
    out: gen/go
    opt:
      - paths=source_relative
  - plugin: buf.build/grpc/go
    out: gen/go
    opt:
      - paths=source_relative

  # gRPC Gateway
  - plugin: buf.build/grpc-ecosystem/gateway
    out: gen/go
    opt:
      - paths=source_relative
      - generate_unbound_methods=true

  # OpenAPI/Swagger
  - plugin: buf.build/grpc-ecosystem/openapiv2
    out: gen/openapi
    opt:
      - generate_unbound_methods=true
      - json_names_for_fields=false

  # Documentation
  - plugin: buf.build/pseudomuto/doc
    out: gen/docs
    opt:
      - html,index.html

  # TypeScript definitions (for frontend)
  - plugin: buf.build/bufbuild/es
    out: gen/ts
    opt:
      - target=ts 