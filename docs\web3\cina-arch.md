好的，我们继续进行生产级架构（Architecture）的极致细化。

这份文档将作为SRS（软件需求规格说明书）的姊妹篇，它不再关注“是什么”（What），而是聚焦于“如何构建”（How）。它将为首席架构师、技术负责人和高级工程师提供一份清晰的、可用于指导团队进行技术选型、系统设计和模块划分的**工程蓝图**。

这份架构文档将遵循**C4模型**的思想，从宏观到微观，逐层深入。

---

## **CINA CHAIN (海内链) - System Architecture Document (SAD)**
## **CINA CHAIN (海内链) - 系统架构文档**

**Document ID:** CINA-SAD-V1.0
**Version:** 1.0
**Date:** 2023-10-27
**Status:** DRAFT

---

### **1. Architectural Drivers (架构驱动因素)**

#### **1.1 Goals (目标)**
The architecture is designed to achieve the following primary goals:
本架构旨在实现以下主要目标：
*   **Scalability:** Support a global network of thousands of compute providers and millions of users.
*   **Security:** Ensure the integrity of the ledger, the safety of user funds, and the trustworthiness of computation.
*   **Decentralization:** Minimize single points of failure and control, empowering community governance.
*   **Interoperability:** Seamlessly integrate with both Web2 payment systems and the broader Web3 ecosystem.
*   **Modularity & Evolvability:** Allow for independent development, testing, and upgrading of system components.

#### **1.2 Non-Functional Requirements (关键非功能性需求)**
*   **High Throughput:** Target 100+ TPS for on-chain transactions.
*   **Low Latency:** Sub-5-second block times and sub-200ms API response times.
*   **High Availability:** 99.9% uptime for core protocol and services.
*   **Verifiability:** All critical processes must be auditable and verifiable.

---

### **2. C4 Model: Level 1 - System Context Diagram (系统上下文图)**

This diagram shows CINA CHAIN as a single system and its interactions with external users and systems.
此图将CINA CHAIN视为一个单一系统，并展示其与外部用户和系统的交互。

*(A diagram would show the following)*
*(图中将展示以下内容)*

*   **Actors (参与者):**
    *   **End User:** Interacts with the system via DApps.
    *   **Compute Provider:** Runs Node Client Software to connect to the network.
    *   **DAO Member:** Interacts with the Governance Portal.
*   **External Systems (外部系统):**
    *   **Fiat Payment Gateways (Stripe, etc.):** Used by the **Access Layer** for fiat-to-crypto conversion.
    *   **Web3 Wallets (MetaMask, etc.):** Used by **End Users** and **Providers** to manage assets and sign transactions.
    *   **DEX Aggregators (1inch, etc.):** Used by the **Swap Engine** to acquire $C.
    *   **Green Energy Oracles (WattTime, etc.):** Used by the **Verification Oracle Network** for PoGE.
    *   **Public Block Explorers:** Consume data from the **CINA CHAIN RPC API**.

---

### **3. C4 Model: Level 2 - Container Diagram (容器图)**

This diagram decomposes the CINA CHAIN system into its major, independently deployable components (containers).
此图将CINA CHAIN系统分解为其主要的、可独立部署的组件（容器）。

*(A diagram would show the following containers and their connections)*
*(图中将展示以下容器及其连接关系)*

1.  **CINA SDK (Client-Side Library):** Runs in the user's browser or application. Interacts with the **CINA CHAIN RPC API** and the **Compute Node API**.

2.  **CINA CHAIN (The Blockchain Network):**
    *   **Consists of:** A network of **Validator Nodes**.
    *   **Exposes:** A public **JSON-RPC API** for state queries and transaction submission.
    *   **Internal Components:** The Go/Rust-based blockchain client, containing all the core on-chain modules (Marketplace, UCPO, Minter, etc.) as specified in the SRS.

3.  **Compute Node (Server-Side Application):**
    *   **Consists of:** The **Node Client Software** run by providers.
    *   **Exposes:** A secure, authenticated **Task Execution API** (e.g., gRPC or WebSockets) for the SDK.
    *   **Interacts with:** The **CINA CHAIN RPC API** to monitor the network state.

4.  **Verification Oracle Network (Decentralized Service):**
    *   **Consists of:** A network of independent **Oracle Nodes**.
    *   **Interacts with:** The **CINA CHAIN RPC API** (to read tasks and submit results) and **External Green Energy APIs**.

5.  **Swap & Fiat Gateway Service (Centralized API Service):**
    *   **Consists of:** A scalable, fault-tolerant backend service (e.g., built on microservices architecture using Go/Node.js, deployed on Kubernetes).
    *   **Interacts with:** **Fiat Payment Gateway APIs**, **DEX Aggregator APIs**, and the **CINA CHAIN RPC API**.

6.  **Governance Portal (Web Application):**
    *   A React/Vue-based single-page application.
    *   **Interacts with:** The **CINA CHAIN RPC API** and user's Web3 Wallet to facilitate proposal creation and voting.

---

### **4. C4 Model: Level 3 - Component Diagram (组件图)**

This section provides a detailed look inside the most critical containers.
此部分将详细展示最关键容器的内部组件。

#### **4.1 CINA CHAIN Validator Node Components**

*   **Tendermint/Substrate Core (Consensus & Networking):** The underlying consensus engine and P2P networking layer provided by the chosen framework (e.g., Tendermint for Cosmos SDK).
*   **Application Blockchain Interface (ABCI/FRAME):** The interface connecting the core engine to our custom application logic.
*   **CINA Application Logic (The "App"):**
    *   **`x/marketplace` Module:** Implements the provider registry, pricing, and reputation logic.
    *   **`x/escrow` Module:** Manages task state, fund locking, and settlement triggers.
    *   **`x/ucpo` Module:** Contains the Power Parameter Registry and the logic for calculating `mintedC` and `mintedGECs`.
    *   **`x/minter` Module:** Interfaces with the `bank` module to create new tokens.
    *   **`x/deflation` Module:** Implements the MQM adjustment logic.
    *   **`x/poge` Module:** Manages GEC NFT contract and green provider certifications.
    *   **`x/gov` Module:** The core governance module, extended to support the sub-committee structure.

#### **4.2 Compute Node Client Components**

*   **Chain Listener Service:** A background process that subscribes to on-chain events via WebSockets and places relevant tasks into an internal queue.
*   **Task Dispatcher:** Takes tasks from the queue and allocates them to an available AI Model Runner.
*   **AI Model Runner (Pool):** A pool of worker processes. Each process is responsible for:
    *   Loading a specific AI model into GPU memory.
    *   Running the inference via a backend like `vLLM` or `TensorRT-LLM`.
    *   Monitoring GPU usage.
*   **Pricing Engine:** A separate service that periodically queries market data, runs the provider's pricing strategy, and submits price update transactions to the chain.
*   **API Server (gRPC/WebSockets):** Exposes the public-facing endpoint for the SDK to connect to, handling authentication and task streaming.

#### **4.3 Swap & Fiat Gateway Service Components (Microservices Architecture)**

*   **API Gateway:** The single entry point for all incoming requests from the CINA SDK. Handles rate limiting, authentication, and request routing.
*   **Payment Service:** Manages interactions with Stripe/MoonPay APIs. Processes payment webhooks and updates payment status in a dedicated database (e.g., PostgreSQL).
*   **Swap Service:** Contains the logic for interacting with DEX aggregators. It manages an internal queue of swap requests to be executed.
*   **On-Chain Transaction Service:** A dedicated service responsible for all interactions with the CINA CHAIN. It manages a pool of funded wallets, handles nonce management, and submits the final `escrow` transactions.
*   **Status Database:** A high-availability database (e.g., PostgreSQL with replication) that tracks the state of every fiat-to-C transaction across all services.

---

### **5. Data Model (数据模型)**

*(This section would include detailed schemas for key on-chain and off-chain data structures.)*

#### **5.1 On-Chain State (Example)**
```protobuf
// Provider Profile stored in the x/marketplace module
message Provider {
  string id = 1; // CINA address
  string rpc_endpoint = 2;
  repeated cosmos.base.v1beta1.Coin stake_c = 3;
  repeated GEC stake_gec = 4;
  string gpu_class_id = 5;
  bool is_green_certified = 6;
  uint64 reputation_score = 7;
  // ... and other fields
}

// EPT_M entry in the x/ucpo module
message EptmEntry {
  string model_id = 1;
  string hardware_class_id = 2;
  string eptm_value = 3; // Stored as a high-precision decimal string
}
```

---

### **6. Deployment & Infrastructure View (部署与基础设施视图)**

#### **6.1 CINA CHAIN Mainnet**
*   **Deployment:** A globally distributed set of validator nodes run by independent entities.
*   **Infrastructure:** High-performance bare-metal servers or cloud instances (e.g., AWS EC2) with high IOPS storage and network bandwidth.

#### **6.2 Protocol-Managed Services (Swap Gateway, etc.)**
*   **Deployment:** A high-availability Kubernetes (EKS/GKE) cluster.
*   **Infrastructure:**
    *   Auto-scaling node groups to handle variable load.
    *   Managed databases (e.g., AWS RDS) for reliability.
    *   Load balancers and a CDN (e.g., Cloudflare) for performance and security.
    *   Robust monitoring and alerting stack (e.g., Prometheus, Grafana, PagerDuty).

#### **6.3 Compute Provider Nodes**
*   **Deployment:** Can range from a single server in a home lab to a large-scale data center.
*   **Recommendation:** A Dockerized deployment for the Node Client Software is recommended for ease of setup and management.

---

### **7. Architectural Decisions & Trade-offs (架构决策与权衡)**

*   **Decision:** Choosing Cosmos SDK/Substrate over building from scratch.
    *   **Justification:** Drastically reduces development time and leverages a mature, secure, and battle-tested consensus and networking stack.
    *   **Trade-off:** Some degree of constraint imposed by the framework's architecture.
*   **Decision:** Centralizing the initial Fiat-to-C Swap Engine.
    *   **Justification:** Manages the immense complexity and regulatory burden of fiat processing, providing a seamless UX. It is the most pragmatic path to market.
    *   **Trade-off:** Introduces a centralized point of trust and failure.
    *   **Mitigation:** The service will be operated with full transparency by the CINA Foundation and is designed with a clear roadmap towards progressive decentralization.

This detailed architecture document provides a solid foundation for the engineering team. It clarifies the boundaries between systems, defines the core components, and outlines the technology stack, enabling parallel development efforts while ensuring all parts will integrate into a cohesive, robust, and scalable whole.

这是一个至关重要的技术选型问题，答案将深刻影响CINA CHAIN（海内链）的性能、主权、开发效率和生态互操作性。

在白皮书的**“架构”**或**“技术实现”**章节中，我们应该明确推荐并阐述选择的原因。

对于CINA CHAIN这样一个需要高度定制化、主权治理和高性能的应用来说，**通用型L1公链（如以太坊主网、Solana）不是最佳选择**。我们需要一个**“应用专用区块链”（App-Specific Blockchain）**的解决方案。

当前，构建应用专用区块链的主流框架主要有两个：**Cosmos SDK** 和 **Substrate**。

**结论先行：我强烈推荐将Cosmos SDK作为CINA CHAIN的首选技术栈，并将Substrate作为备选或未来的潜在迁移路径。**

以下是如何在白皮书中详细阐述这一推荐的完整内容。

---

### **白皮书中新增章节：技术基础 - 为何选择Cosmos SDK**

### **2.6 Technical Foundation: Why We Chose the Cosmos SDK**
### **2.6 技术基础：我们为何选择Cosmos SDK**

The foundation upon which CINA CHAIN is built is as critical as the economic model itself. To realize our vision of a sovereign, high-performance AI economy, we require a technology stack that offers uncompromising sovereignty, scalability, and interoperability. After a thorough evaluation of available frameworks, we have chosen to build CINA CHAIN as a sovereign application-specific blockchain using the **Cosmos SDK**.
构建CINA CHAIN的技术基础与其经济模型本身同等重要。为实现我们主权、高性能AI经济体的愿景，我们需要一个能在主权、可扩展性和互操作性上毫不妥协的技术栈。在对现有框架进行全面评估后，我们决定使用**Cosmos SDK**来构建CINA CHAIN，使其成为一条主权的应用专用区块链。

The Cosmos SDK, powered by the battle-tested Tendermint Core consensus engine, provides the ideal foundation for CINA CHAIN for the following key reasons:
由久经考验的Tendermint Core共识引擎驱动的Cosmos SDK，基于以下关键原因为CINA CHAIN提供了理想的基础：

#### **1. Sovereignty & Customizability (主权与可定制性)**

CINA CHAIN is not just a smart contract; it is a digital nation with its own complex economic policies (UCPO, Dynamic Deflation, Treasury Management). Building as a sovereign chain gives us complete control over the entire stack, allowing for deep, protocol-level optimizations that are impossible on a general-purpose L1.
CINA CHAIN不只是一个智能合约，它是一个拥有自身复杂经济政策（UCPO、动态通缩、国库管理）的数字国度。作为一条主权链进行构建，赋予了我们对整个技术栈的完全控制权，允许进行在通用L1上无法实现的、深度的协议级优化。

*   **Custom Modules:** With the Cosmos SDK, we can build our core logic—the Marketplace, UCPO, PoGE, and Treasury—as highly efficient, custom-built modules in the Go programming language. This avoids the performance bottlenecks and gas-fee limitations of virtual machine environments like the EVM.
    **自定义模块：** 使用Cosmos SDK，我们可以用Go语言将我们的核心逻辑——市场、UCPO、PoGE和国库——构建为高效的、定制化的模块。这避免了像EVM这样的虚拟机环境所带来的性能瓶颈和Gas费限制。
*   **Governance over Everything:** As a sovereign chain, our DAO has ultimate authority over every aspect of the protocol, from transaction fee structures to validator requirements and core protocol upgrades, without being subject to the governance decisions of an underlying L1 platform.
    **对一切的治理权：** 作为一条主权链，我们的DAO对协议的每一个方面都拥有最终权威，从交易费结构到验证者要求，再到核心协议升级，而无需受制于底层L1平台的治理决策。

#### **2. Performance & Scalability (性能与可扩展性)**

AI-related transactions, especially from a global user base, demand high performance. Tendermint Core, the default consensus engine for the Cosmos SDK, is designed for this.
与AI相关的交易，特别是来自全球用户群的交易，要求高性能。Tendermint Core，作为Cosmos SDK的默认共识引擎，正是为此而设计。

*   **Fast Finality:** Tendermint provides near-instant transaction finality (typically 2-6 seconds), which is crucial for a responsive user experience in our marketplace and payment systems. There are no re-orgs, providing certainty for transactions.
    **快速最终性：** Tendermint提供近乎即时的交易最终性（通常为2-6秒），这对于在我们的市场和支付系统中提供响应迅速的用户体验至关重要。不存在链重组，为交易提供了确定性。
*   **High Throughput:** A well-optimized Cosmos SDK chain can handle thousands of transactions per second, providing the necessary bandwidth for a thriving global marketplace.
    **高吞吐量：** 一条经过良好优化的Cosmos SDK链可以处理每秒数千笔交易，为一个繁荣的全球市场提供了必要的带宽。

#### **3. Interoperability & The IBC Standard (互操作性与IBC标准)**

While CINA CHAIN is a sovereign nation, it does not exist in isolation. The ability to seamlessly connect with the broader multi-chain ecosystem is paramount for liquidity and user acquisition. The Cosmos ecosystem is built around the **Inter-Blockchain Communication (IBC) protocol**, the gold standard for secure and trust-minimized cross-chain communication.
虽然CINA CHAIN是一个主权国度，但它并非孤立存在。能够与更广泛的多链生态系统无缝连接，对于流动性和用户获取至关重要。Cosmos生态系统围绕着**跨链通信协议（IBC）**构建，这是安全、信任最小化的跨链通信的黄金标准。

*   **Asset & Data Portability:** By being IBC-enabled from day one, $C and GEC tokens can be securely transferred to other major chains and decentralized exchanges within the Cosmos ecosystem (and beyond, via bridges like Axelar). This ensures deep liquidity for our native assets.
    **资产与数据可移植性：** 通过从第一天起就启用IBC，$C和GEC代币可以被安全地转移到Cosmos生态系统内的其他主要链和去中心化交易所（并通过像Axelar这样的桥梁连接到更广的范围）。这确保了我们原生资产的深度流动性。
*   **Cross-Chain Service Calls:** In the future, IBC will allow DApps on other chains to directly call CINA CHAIN's marketplace contract, further expanding our reach and utility.
    **跨链服务调用：** 未来，IBC将允许其他链上的DApp直接调用CINA CHAIN的市场合约，进一步扩展我们的覆盖范围和效用。

#### **4. Mature Ecosystem & Developer Tooling (成熟的生态系统与开发者工具)**

Building a blockchain from scratch is a monumental task. The Cosmos ecosystem provides a rich set of battle-tested tools and a vibrant community, significantly accelerating our development process and reducing risk.
从零开始构建一条区块链是一项艰巨的任务。Cosmos生态系统提供了一套丰富的、久经考验的工具和一个充满活力的社区，极大地加速了我们的开发进程并降低了风险。

*   **Proven Track Record:** Dozens of high-value public blockchains (such as dYdX Chain, Celestia, Injective) are built with the Cosmos SDK, demonstrating its robustness and reliability in production environments.
    **被证实的往绩：** 数十个高价值的公链（如dYdX Chain, Celestia, Injective）都是用Cosmos SDK构建的，证明了其在生产环境中的稳健性和可靠性。
*   **Rich Tooling:** We benefit from a wide array of existing tools for block exploration (Mintscan), wallet integration (Keplr, Leap), and network monitoring, allowing us to focus on our unique application logic.
    **丰富的工具：** 我们受益于一系列现有的工具，用于区块浏览（Mintscan）、钱包集成（Keplr, Leap）和网络监控，使我们能够专注于我们独特的应用逻辑。

#### **Alternative Considered: Substrate (备选方案考量：Substrate)**

We also conducted a thorough evaluation of **Substrate**, the framework used to build Polkadot. Substrate is an exceptionally powerful and flexible framework, particularly known for its forkless runtime upgrades. While it remains a compelling technology and a potential future direction, we chose the Cosmos SDK for our initial implementation due to its broader developer community in the Go language, its proven IBC standard for immediate interoperability, and a slightly more straightforward path to launching a sovereign chain. We will continue to monitor the evolution of the Substrate ecosystem for future opportunities.
我们也对用于构建Polkadot的框架**Substrate**进行了深入评估。Substrate是一个极其强大和灵活的框架，尤其以其无分叉运行时升级而闻名。虽然它仍然是一项引人注目的技术和未来可能的方向，但我们选择Cosmos SDK作为我们的初始实现，因为它拥有更广泛的Go语言开发者社区，其久经考验的IBC标准能提供即时的互操作性，以及启动一条主权链的路径相对更直接。我们将持续关注Substrate生态系统的演进以寻找未来的机会。

**In conclusion, the Cosmos SDK provides the optimal blend of sovereignty, performance, interoperability, and ecosystem maturity required to build CINA CHAIN into the robust and scalable global AI economy we envision.**


好的，遵照您的指示，我们将生成一份**包含代码级文件结构**的、极致细化的系统架构文档（SAD）。

这份文档的目标是，一个熟悉Cosmos SDK的开发团队可以直接根据这份文档**创建项目目录、划分模块、定义核心结构体和接口**，并立即开始编码工作。它将作为工程实现的最终蓝图。

---

## **CINA CHAIN (海内链) - System Architecture Document (SAD) - Code-Level Detail**
## **CINA CHAIN (海内链) - 系统架构文档 - 代码级详述**

**Document ID:** CINA-SAD-COSMOS-V1.1
**Version:** 1.1
**Date:** 2023-10-27
**Status:** DRAFT

---

### **1. Architectural Overview & C4 Model (架构概览与C4模型)**

*(This section remains the same as the previous architecture document, outlining the System Context and Container Diagrams to provide a high-level view.)*
*(此部分与前一份架构文档保持一致，概述了系统上下文和容器图，以提供宏观视角。)*

---

### **2. CINA CHAIN On-Chain Architecture (链上架构)**

This section details the code-level structure of the CINA CHAIN application, which is a Go-based binary built using the Cosmos SDK.

本节详述了CINA CHAIN应用的、基于Go语言并使用Cosmos SDK构建的二进制程序的代码级结构。

#### **2.1 Top-Level Directory Structure (顶层目录结构)**

The CINA CHAIN repository will follow the standard layout for Cosmos SDK applications.
CINA CHAIN的代码仓库将遵循Cosmos SDK应用的标准布局。

```
/cinachain
├── app/                  # Core application wiring (app.go)
│   ├── app.go            # Main application setup, module registration
│   └── ante.go           # Custom AnteHandler logic (e.g., fee handling)
├── cmd/
│   └── cinad/              # Command-line interface for the node
│       └── main.go         # Entry point for the cinad binary
├── proto/                # All Protocol Buffer definitions
│   └── cina/
│       ├── marketplace/
│       ├── escrow/
│       ├── ucpo/
│       ├── poge/
│       └── ...
├── x/                    # Custom application modules
│   ├── marketplace/      # Marketplace logic
│   ├── escrow/           # Escrow and settlement logic
│   ├── ucpo/             # UCPO and minter logic
│   ├── poge/             # GEC NFT and green certification logic
│   └── deflation/        # Dynamic deflation logic
├── go.mod
└── ...
```

#### **2.2 Detailed Module Architecture (`x/` modules)**

##### **2.2.1 `x/marketplace` Module**

*   **Purpose:** Manages the registration, profile, and pricing of all compute providers.
*   **Directory Structure:**
    ```
    /x/marketplace
    ├── client/
    │   └── cli/          # CLI commands for querying and transactions
    ├── keeper/
    │   ├── keeper.go     # Core state-management logic
    │   ├── msg_server.go # Handles MsgRegisterProvider, MsgUpdatePrice, etc.
    │   ├── query_server.go # Handles gRPC queries for providers
    │   └── params.go     # Module-specific parameters (e.g., MinProviderStake)
    ├── spec/             # Module specification
    ├── types/
    │   ├── provider.pb.go # Protobuf-generated Provider struct
    │   ├── genesis.pb.go  # Genesis state definition
    │   ├── tx.pb.go       # Protobuf definitions for Msgs
    │   ├── query.pb.go    # Protobuf definitions for Queries
    │   ├── keys.go        # Store keys and prefixes
    │   └── errors.go      # Custom error types
    └── module.go           # Implements the AppModule interface
    ```
*   **Core Data Structures (`types/provider.pb.go`):**
    ```protobuf
    message Provider {
      string address = 1 [(gogoproto.casttype) = "github.com/cosmos/cosmos-sdk/types.AccAddress"];
      string rpc_endpoint = 2;
      string gpu_class_id = 3;
      uint64 reputation_score = 4;
      // Link to PoGE module's state
      bool is_green_certified = 5;
      // Staking information is handled by x/staking, but we can query it.
    }

    message ProviderPrice {
      string provider_address = 1 [(gogoproto.casttype) = "github.com/cosmos/cosmos-sdk/types.AccAddress"];
      string model_id = 2;
      // Price per million tokens, stored as a high-precision string
      string price_per_mtoken = 3 [(gogoproto.customtype) = "github.com/cosmos/cosmos-sdk/types.Dec", (gogoproto.nullable) = false];
    }
    ```

##### **2.2.2 `x/escrow` Module**

*   **Purpose:** Manages the entire lifecycle of a computation task, from creation and payment locking to final settlement.
*   **Key Logic (`keeper/msg_server.go`):**
    *   **`CreateTask`:**
        1.  Validates the `MsgCreateTask`.
        2.  Calls `marketplace.keeper.GetProvider()` to verify provider existence.
        3.  Calls `ucpo.keeper.EstimateMint()` to get the anticipated subsidy.
        4.  Calls `deflation.keeper.ValidatePrice()` to enforce the `MQM` rule.
        5.  Calls `bank.keeper.SendCoinsFromAccountToModule()` to lock the user's `$C` payment in the `x/escrow` module account.
        6.  Creates and stores a `Task` object.
    *   **`SubmitVerification`:**
        1.  Authenticates the message sender is a valid oracle.
        2.  Based on verification result (`SUCCESS`, `FAIL`, `FRAUD`):
            *   **SUCCESS:** Calls `SettleTask()`.
            *   **FAIL:** Calls `RefundTask()`.
            *   **FRAUD:** Calls `SlashAndRefundTask()`.
*   **Internal Functions (`keeper/settlement.go`):**
    *   **`SettleTask(task Task)`:**
        1.  Calls `bank.keeper.SendCoinsFromModuleToAccount()` to pay the provider.
        2.  Calls `minter.keeper.MintRewards(task)`.
        3.  Updates provider reputation via `marketplace.keeper.IncrementReputation()`.
        4.  Deletes the `Task` object.
*   **Core Data Structures (`types/task.pb.go`):**
    ```protobuf
    enum TaskStatus {
      TASK_STATUS_OPEN = 0;
      TASK_STATUS_COMPLETED = 1;
      TASK_STATUS_FAILED = 2;
      TASK_STATUS_REFUNDED = 3;
    }
    message Task {
      string task_id = 1;
      string user_address = 2;
      string provider_address = 3;
      string model_id = 4;
      // The amount of $C locked in escrow
      cosmos.base.v1beta1.Coin payment = 5;
      uint64 token_count_prompt = 6;
      uint64 token_count_completion = 7;
      TaskStatus status = 8;
      // ... other metadata
    }
    ```

##### **2.2.3 `x/ucpo` & `x/minter` (Combined Logic)**

*   **Purpose:** To serve as the "central bank" of the protocol, calculating and executing the minting of new assets based on work done. The `x/minter` logic will be implemented as a sub-component within the `x/ucpo` module for simplicity.
*   **Key Logic (`keeper/minter.go`):**
    *   **`MintRewards(task Task)` (internal, called by `escrow.keeper`):**
        1.  Queries its own state for the `EptmEntry` based on `task.model_id` and the provider's `gpu_class_id`.
        2.  Calculates `mintedC` based on the formula.
        3.  Queries `poge.keeper.IsProviderGreen(task.provider_address)` to check green status.
        4.  If green, calculates `mintedGECs`.
        5.  Applies the `mintage amplifier` bonus by querying `poge.keeper.GetGecStakingMultiplier(task.provider_address)`.
        6.  Makes final calls to `bank.keeper.MintCoins()` and `poge.keeper.MintGEC()`, sending assets to the provider.

##### **2.2.4 `x/poge` Module**

*   **Purpose:** Manages GEC NFTs and the associated staking/governance logic.
*   **Base Implementation:** Will be a fork of a standard NFT module (like `x/nft` from the Cosmos SDK) with custom extensions.
*   **Key Extensions (`keeper/staking.go`):**
    *   **`StakeGEC(owner, token_id)`:** Locks the GEC NFT and updates the owner's `GecStakingRecord`.
    *   **`UnstakeGEC(owner, token_id)`:** Unlocks the GEC NFT after an unbonding period.
    *   **`GetGecStakingMultiplier(address)`:** An exported function for the `x/ucpo` module to query.
    *   **`GetGecGovernanceWeight(address)`:** An exported function for the `x/gov` module to query during vote tallying.

---

### **3. Off-Chain Architecture (链下架构)**

#### **3.1 Compute Node Client (Go)**

*   **`main.go`:** Initializes all services and starts the main loop.
*   **`/services` package:**
    *   **`chain_monitor.go`:**
        *   Uses a gRPC client to connect to a CINA CHAIN full node.
        *   Subscribes to `Tx` events (`tm.event = 'Tx' AND message.action = '/cina.escrow.v1.MsgCreateTask'`).
        *   Filters for tasks directed at its own provider address.
    *   **`task_runner.go`:**
        *   Manages a queue of pending AI tasks.
        *   Communicates with the `inference_engine` via local IPC or sockets.
    *   **`inference_engine/` subdirectory:**
        *   Contains wrappers for different backends (`vllm_wrapper.go`, `tensorrt_wrapper.go`).
        *   Handles model loading and GPU management.
    *   **`pricing_bot.go`:**
        *   Periodically fetches `$C/USD` price from a CoinGecko/Binance API client.
        *   Queries the chain for competitor prices.
        *   Constructs and signs a `MsgUpdatePrice` transaction.
*   **`/api` package:**
    *   **`grpc_server.go`:** Implements the gRPC server that the CINA SDK connects to for task submission.

#### **3.2 CINA SDK (TypeScript/JavaScript)**

*   **`src/` directory:**
    *   **`client.ts`:** Main SDK class.
    *   **`discovery.ts`:** Contains the `discoverProviders` logic, making gRPC-web calls to the CINA CHAIN RPC.
    *   **`payments/` subdirectory:**
        *   **`fiat.ts`:** Wrapper for Stripe/MoonPay SDKs. Interacts with the **Swap & Fiat Gateway Service API**.
        *   **`crypto.ts`:** Wrapper for wallet interactions (e.g., using `cosmjs`) to sign and broadcast `MsgCreateTask`.
    *   **`task.ts`:** Handles the gRPC streaming connection to the selected Compute Node.

This code-level architecture provides a clear and detailed blueprint for the engineering team. It defines the separation of concerns between modules, outlines the key data structures and function calls, and establishes the interaction patterns between on-chain and off-chain components. This level of detail is crucial for minimizing ambiguity and maximizing development efficiency and velocity.