
好的，遵照您的指示。我将为您生成一份专门针对 **`apps/` 目录** 的、极致细化的、生产级**架构设计文档**。

这份文档将详细阐述CINA.CLUB平台下所有前端应用的**内部结构、技术选型、模块化策略和协同方式**。它将覆盖**Android原生、Apple全家桶、Windows原生、Web**以及一个独立的**后台管理前端**，并特别将**HarmonyOS**作为一个与Android既有联系又有区别的平台进行说明。

这份设计旨在成为前端所有团队（Android, Apple, Windows, Web, Admin）的权威“建房图纸”。

---
### CINA.CLUB - 前端应用层 (`apps/`) 架构设计

**文档状态**: 已采纳 (Accepted) & 权威 (Authoritative)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**文档负责人:** [前端总架构师]

## 1. 概述

### 1.1 设计哲学
`apps/` 目录是CINA.CLUB平台**最终面向用户的产品**的所在地。本架构的核心哲学是：**“共享核心，原生呈现 (Shared Core, Native Shell)”**。

*   **共享核心**: 所有平台（包括后台）共享由Go编写的、位于`/core`目录的核心业务逻辑库，以及由TypeScript/JavaScript编写的、位于`/packages`（或SPM/Gradle等原生包管理器）的共享状态管理和API客户端。
*   **原生呈现**: 为每个主流生态系统（Android, Apple, Windows, Web）采用其**官方推荐的、现代化的声明式UI框架**，以提供最极致的性能、最原生的交互体验和对平台新特性的最快支持。

### 1.2 目录总览

```
apps/
├── admin-frontend/         # 1. 后台管理系统 (Web)
├── android/                # 2. Android 原生应用
├── apple/                  # 3. Apple 生态原生应用
├── harmony/                # 4. HarmonyOS 原生应用
├── web/                    # 5. 面向用户的Web应用
└── windows/                # 6. Windows 原生应用
```

---

## 2. 各应用架构深度解析

### 2.1 `apps/admin-frontend/` (后台管理系统)

*   **目标**: 为内部员工提供一个功能强大、数据密集、开发高效的管理后台。
*   **技术选型**:
    *   **框架**: **React 18+** + **TypeScript**
    *   **脚手架**: **Ant Design Pro v5+**
    *   **UI组件库**: **Ant Design v5+**
    *   **状态管理**: **Redux Toolkit (RTK)** (因其强大的可追溯性和中间件能力，更适合复杂B端场景)
    *   **数据请求**: **TanStack Query**
    *   **API客户端**: `openapi-typescript-codegen` 自动生成
*   **内部结构**:
    ```
    admin-frontend/
    ├── config/                 # 路由、菜单、代理等配置
    ├── public/
    ├── src/
    │   ├── api/                # [自动生成] 类型安全的API客户端
    │   ├── components/         # 可复用的业务组件 (e.g., UserSelector)
    │   ├── layouts/            # 页面布局
    │   ├── models/             # (可选) antd pro的dva models
    │   ├── pages/              # 页面级组件，按业务模块组织
    │   ├── services/           # 封装TanStack Query的API调用逻辑
    │   └── store/              # Redux Toolkit (RTK) store
    └── package.json
    ```
*   **核心思想**: **效率优先**。利用Ant Design Pro提供的开箱即用的解决方案，让开发团队能快速响应运营和管理需求。

### 2.2 `apps/android/` (Android原生应用)

*   **目标**: 为Android用户提供符合Material Design规范的、高性能、体验流畅的应用。
*   **技术选型**:
    *   **语言**: **Kotlin** (100%)
    *   **UI框架**: **Jetpack Compose**
    *   **架构模式**: **MVI (Model-View-Intent)** 或 MVVM
    *   **异步处理**: Kotlin Coroutines + Flow
    *   **依赖注入**: Hilt 或 Koin
    *   **核心逻辑集成**: Go Mobile (编译为`.aar`)
*   **内部结构 (多模块架构)**:
    ```
    android/
    ├── app/                    # ✨ 主应用模块 (Application Shell) ✨
    │   ├── libs/
    │   │   └── core-go.aar     # Go核心库
    │   └── src/main/java/com/cinaclub/
    │       ├── MainActivity.kt # 应用主入口
    │       ├── navigation/     # 主导航框架 (BottomNav, etc.)
    │       └── di/             # Hilt依赖注入配置
    ├── build-logic/            # Gradle构建逻辑
    ├── core/                   # ✨ 共享基础设施模块 ✨
    │   ├── common/             # 工具类、扩展函数
    │   ├── data/               # Repository接口、DTOs
    │   ├── design-system/      # ✨ 统一的Compose组件库 (Atoms, Molecules) ✨
    │   └── go-bridge/          # ✨ 调用core-go.aar的Kotlin封装层 ✨
    ├── feature_auth/           # ✨ 认证微应用 ✨
    ├── feature_chat/           # ✨ 聊天微应用 ✨
    ├── feature_pkb/            # ✨ 个人知识库微应用 ✨
    └── ... (其他feature模块)
    ```
*   **核心思想**: **模块化与解耦**。采用Google推荐的多模块架构，`app`模块作为主应用壳，只负责组装。每个`feature_*`模块都是一个高内聚的“微应用”，它们都依赖于`core`模块提供的共享能力。

### 2.3 `apps/harmony/` (HarmonyOS原生应用)

*   **目标**: 为华为设备提供利用HarmonyOS新特性的原生应用，同时最大化与Android的代码复用。
*   **技术选型**:
    *   **语言**: **ArkTS** (首选) 或 **Kotlin**
    *   **UI框架**: **ArkUI** (基于声明式范式)
    *   **核心逻辑集成**:
        *   **方案A (与Android共享)**: 利用OpenHarmony对Android AAR的兼容层，直接复用`apps/android`中的大部分业务逻辑和Go核心库(`core-go.aar`)。这是**最经济高效**的方案。
        *   **方案B (独立编译)**: 如果需要极致的HarmonyOS原生性能，可以为`/core`的Go代码创建一个新的编译目标，通过FFI(Foreign Function Interface)与ArkTS交互。此方案更复杂。
*   **内部结构 (基于方案A)**:
    ```
    harmony/
    ├── entry/                  # 主应用模块
    │   └── src/main/ets/
    │       ├── entryability/
    │       └── pages/            # ArkTS页面
    ├── feature_chat/           # HarmonyOS版本的聊天功能模块
    │   └── src/main/ets/...
    └── oh_modules/               # OpenHarmony的依赖
    ```
*   **核心思想**: **兼容与复用**。在初期，将HarmonyOS视为一个需要进行UI和部分系统API适配的“特殊Android版本”。在`apps/android`的`core`和`feature`模块中，通过接口和依赖注入来隔离平台差异，然后在`apps/harmony`中提供HarmonyOS的特定实现。

### 2.4 `apps/apple/` (Apple生态原生应用)

*   **目标**: 为所有Apple设备提供符合HIG (Human Interface Guidelines)规范的、无缝、统一的体验。
*   **技术选型**:
    *   **语言**: **Swift** (100%)
    *   **UI框架**: **SwiftUI** (跨平台UI)
    *   **架构模式**: **MVVM-C (Model-View-ViewModel-Coordinator)** 或 TCA (The Composable Architecture)
    *   **异步处理**: Swift Concurrency (`async/await`)
    *   **包管理器**: **Swift Package Manager (SPM)**
    *   **核心逻辑集成**: Go Mobile (编译为`.xcframework`)
*   **内部结构 (基于SPM的模块化)**:
    ```
    apple/
    ├── CinaClub.xcodeproj/     # Xcode项目文件
    ├── Frameworks/
    │   └── CoreGo.xcframework  # Go核心库
    ├── Packages/               # ✨ SPM本地包 ✨
    │   ├── AppCore/            # 主应用壳
    │   ├── DesignSystem/       # 统一的SwiftUI组件库
    │   ├── FeatureChat/
    │   ├── FeatureSchedule/
    │   └── GoBridge/           # 调用CoreGo.xcframework的Swift封装层
    └── Targets/                # 最终的应用目标
        ├── iOS_iPadOS/
        ├── macOS/
        ├── visionOS/
        └── watchOS/
    ```
*   **核心思想**: **生态系统统一**。使用SwiftUI和SPM构建一个高度模块化的代码库，一套核心业务逻辑和UI组件可以服务于所有Apple设备，只在各个`Target`中做少量平台适配。

### 2.5 `apps/web/` (面向用户的Web应用)
*   (与之前版本一致，此处为保持完整性)
*   **目标**: 提供一个轻量、快速、SEO友好的Web门户和核心功能体验。
*   **技术选型**:
    *   **框架**: **SvelteKit** + **TypeScript**
    *   **核心逻辑集成**: WebAssembly (`.wasm`)
*   **核心思想**: **性能优先**。利用Svelte的编译时优化和WASM的近原生速度，为Web用户提供最佳性能。

### 2.6 `apps/windows/` (Windows原生应用)
*   **目标**: 为Windows用户提供利用Fluent Design体系的、与系统深度集成的桌面体验。
*   **技术选型**:
    *   **语言**: **C#**
    *   **UI框架**: **WinUI 3** (Windows App SDK)
    *   **架构模式**: **MVVM**
    *   **核心逻辑集成**: C-shared library (`.dll`) + **P/Invoke**
*   **内部结构 (标准.NET解决方案)**:
    ```
    windows/
    ├── CinaClub.sln            # 解决方案文件
    ├── CinaClub.App/           # 主应用项目 (UI层)
    ├── CinaClub.Core/          # 共享的C#业务逻辑 (ViewModels, Services)
    └── CinaClub.Infrastructure/ # 基础设施层
        └── GoBridge/           # ✨ P/Invoke调用core_go.dll的C#封装层 ✨
    ```
*   **核心思想**: **平台原生**。完全拥抱Windows生态，提供最佳的桌面应用体验。

---
### 4. 总结

这份**版本v1.1**的`apps/`目录架构，是CINA.CLUB“**共享核心，原生呈现**”战略的最终体现。

*   **后端与核心 (`/core`, `/pkg`, `/services`)**: 提供稳定、高性能、平台无关的**能力**。
*   **前端 (`/apps`)**: 负责将这些能力，以**最适合各个目标平台**的方式，**翻译和呈现**给最终用户。

这种架构虽然对前端团队提出了多技术栈的要求，但它避免了“一套代码到处跑，但到处体验都平庸”的跨平台陷阱。通过在`/core`中复用最复杂、最关键的逻辑，它在**追求极致用户体验**和**控制开发成本**之间，取得了最佳的、生产级的平衡。