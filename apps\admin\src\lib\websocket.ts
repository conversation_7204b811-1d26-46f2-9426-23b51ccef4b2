/**
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-01-23 16:25:00
 * Modified: 2025-01-23 16:25:00
 */

import { message } from 'antd';

/**
 * WebSocket Client with Auto-Reconnection and Event Management
 * Provides real-time communication with proper error handling and connection management
 */

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  id?: string;
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  debug?: boolean;
}

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';

export type WebSocketEventHandler = (data: any) => void;

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private config: Required<WebSocketConfig>;
  private eventHandlers: Map<string, Set<WebSocketEventHandler>> = new Map();
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private statusListeners: Set<(status: ConnectionStatus) => void> = new Set();
  private messageQueue: WebSocketMessage[] = [];
  private isManualClose = false;

  constructor(config: WebSocketConfig) {
    this.config = {
      protocols: [],
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      debug: false,
      ...config,
    };

    if (this.config.debug) {
      console.log('🔌 WebSocket client initialized with config:', this.config);
    }
  }

  /**
   * Connect to WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.isManualClose = false;
      this.setConnectionStatus('connecting');

      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols);
        this.setupEventListeners(resolve, reject);
      } catch (error) {
        this.handleConnectionError(error as Error);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.isManualClose = true;
    this.clearTimers();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
    
    this.setConnectionStatus('disconnected');
    
    if (this.config.debug) {
      console.log('🔌 WebSocket manually disconnected');
    }
  }

  /**
   * Send message to server
   */
  send(type: string, data: any): boolean {
    const message: WebSocketMessage = {
      type,
      data,
      timestamp: new Date().toISOString(),
      id: this.generateMessageId(),
    };

    if (this.ws?.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        
        if (this.config.debug) {
          console.log('📤 WebSocket message sent:', message);
        }
        
        return true;
      } catch (error) {
        console.error('❌ Failed to send WebSocket message:', error);
        return false;
      }
    } else {
      // Queue message for later sending
      this.messageQueue.push(message);
      
      if (this.config.debug) {
        console.log('📋 WebSocket message queued:', message);
      }
      
      return false;
    }
  }

  /**
   * Subscribe to specific event type
   */
  on(eventType: string, handler: WebSocketEventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    
    this.eventHandlers.get(eventType)!.add(handler);
    
    if (this.config.debug) {
      console.log(`🎧 WebSocket event handler added for: ${eventType}`);
    }

    // Return unsubscribe function
    return () => {
      this.off(eventType, handler);
    };
  }

  /**
   * Unsubscribe from event type
   */
  off(eventType: string, handler?: WebSocketEventHandler): void {
    if (handler) {
      this.eventHandlers.get(eventType)?.delete(handler);
    } else {
      this.eventHandlers.delete(eventType);
    }
    
    if (this.config.debug) {
      console.log(`🎧 WebSocket event handler removed for: ${eventType}`);
    }
  }

  /**
   * Subscribe to connection status changes
   */
  onStatusChange(listener: (status: ConnectionStatus) => void): () => void {
    this.statusListeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.statusListeners.delete(listener);
    };
  }

  /**
   * Get current connection status
   */
  getStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection statistics
   */
  getStats() {
    return {
      status: this.connectionStatus,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      eventHandlers: Array.from(this.eventHandlers.keys()),
      isConnected: this.isConnected(),
    };
  }

  private setupEventListeners(resolve: () => void, reject: (error: Error) => void): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      this.reconnectAttempts = 0;
      this.setConnectionStatus('connected');
      this.startHeartbeat();
      this.processMessageQueue();
      
      if (this.config.debug) {
        console.log('✅ WebSocket connected successfully');
      }
      
      resolve();
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('❌ Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      this.clearTimers();
      
      if (this.config.debug) {
        console.log('🔌 WebSocket closed:', event.code, event.reason);
      }

      if (!this.isManualClose && this.reconnectAttempts < this.config.maxReconnectAttempts) {
        this.setConnectionStatus('reconnecting');
        this.scheduleReconnect();
      } else {
        this.setConnectionStatus('disconnected');
        if (!this.isManualClose) {
          message.error('WebSocket connection lost and could not be restored');
        }
      }
    };

    this.ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      this.setConnectionStatus('error');
      
      if (this.reconnectAttempts === 0) {
        reject(new Error('WebSocket connection failed'));
      }
    };
  }

  private handleMessage(message: WebSocketMessage): void {
    if (this.config.debug) {
      console.log('📥 WebSocket message received:', message);
    }

    // Handle heartbeat/ping messages
    if (message.type === 'ping') {
      this.send('pong', { timestamp: new Date().toISOString() });
      return;
    }

    // Emit to event handlers
    const handlers = this.eventHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.data);
        } catch (error) {
          console.error(`❌ Error in WebSocket event handler for ${message.type}:`, error);
        }
      });
    }

    // Emit to wildcard handlers
    const wildcardHandlers = this.eventHandlers.get('*');
    if (wildcardHandlers) {
      wildcardHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('❌ Error in WebSocket wildcard handler:', error);
        }
      });
    }
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      30000 // Max 30 seconds
    );

    if (this.config.debug) {
      console.log(`🔄 Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    }

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        console.error('❌ WebSocket reconnect failed:', error);
      });
    }, delay);
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.send('ping', { timestamp: new Date().toISOString() });
      }
    }, this.config.heartbeatInterval);
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!;
      this.ws!.send(JSON.stringify(message));
      
      if (this.config.debug) {
        console.log('📤 Queued WebSocket message sent:', message);
      }
    }
  }

  private setConnectionStatus(status: ConnectionStatus): void {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status;
      this.statusListeners.forEach(listener => {
        try {
          listener(status);
        } catch (error) {
          console.error('❌ Error in WebSocket status listener:', error);
        }
      });
    }
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private handleConnectionError(error: Error): void {
    console.error('❌ WebSocket connection error:', error);
    this.setConnectionStatus('error');
  }
}

/**
 * WebSocket Manager - Singleton for managing multiple WebSocket connections
 */
export class WebSocketManager {
  private static instance: WebSocketManager;
  private connections: Map<string, WebSocketClient> = new Map();

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Create or get WebSocket connection
   */
  connect(name: string, config: WebSocketConfig): WebSocketClient {
    if (this.connections.has(name)) {
      return this.connections.get(name)!;
    }

    const client = new WebSocketClient(config);
    this.connections.set(name, client);
    
    // Auto-connect
    client.connect().catch(error => {
      console.error(`❌ Failed to connect WebSocket "${name}":`, error);
    });

    return client;
  }

  /**
   * Get existing connection
   */
  getConnection(name: string): WebSocketClient | undefined {
    return this.connections.get(name);
  }

  /**
   * Disconnect and remove connection
   */
  disconnect(name: string): void {
    const client = this.connections.get(name);
    if (client) {
      client.disconnect();
      this.connections.delete(name);
    }
  }

  /**
   * Disconnect all connections
   */
  disconnectAll(): void {
    this.connections.forEach((client, name) => {
      client.disconnect();
    });
    this.connections.clear();
  }

  /**
   * Get all connection statuses
   */
  getAllStatuses(): Record<string, ConnectionStatus> {
    const statuses: Record<string, ConnectionStatus> = {};
    this.connections.forEach((client, name) => {
      statuses[name] = client.getStatus();
    });
    return statuses;
  }
}

// Default WebSocket configurations for different services
export const WebSocketConfigs = {
  admin: {
    url: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/admin`,
    protocols: ['admin-protocol'],
    debug: process.env.NODE_ENV === 'development',
  },
  
  notifications: {
    url: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/notifications`,
    protocols: ['notification-protocol'],
    heartbeatInterval: 60000,
    debug: process.env.NODE_ENV === 'development',
  },
  
  analytics: {
    url: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/analytics`,
    protocols: ['analytics-protocol'],
    heartbeatInterval: 10000,
    debug: process.env.NODE_ENV === 'development',
  },
  
  monitoring: {
    url: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/monitoring`,
    protocols: ['monitoring-protocol'],
    heartbeatInterval: 5000,
    debug: process.env.NODE_ENV === 'development',
  },
};

// Convenience functions for common use cases
export const createWebSocketHook = (connectionName: string, config: WebSocketConfig) => {
  const manager = WebSocketManager.getInstance();
  return manager.connect(connectionName, config);
};

export const useWebSocket = (connectionName: string) => {
  const manager = WebSocketManager.getInstance();
  return manager.getConnection(connectionName);
};

export default WebSocketClient; 