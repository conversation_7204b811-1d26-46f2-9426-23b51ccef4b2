# Admin Dashboard - User Manual

Welcome to the user manual for the Cina.Club Admin Dashboard. This guide provides a comprehensive overview of how to use the dashboard's features to manage the platform effectively.

## 1. Getting Started

### 1.1. Logging In

-   Navigate to the admin URL provided to you.
-   Enter your administrator email and password.
-   Upon successful login, you will be redirected to the main **Dashboard**.

### 1.2. The Dashboard

The main dashboard provides a high-level overview of platform activity, including:
-   Key Performance Indicators (KPIs) for user growth, revenue, and content.
-   Real-time metrics for active users and services.
-   Quick links to major sections of the admin panel.

## 2. Managing Users

Navigate to the **User Management** section from the sidebar.

### 2.1. Viewing Users

The user list displays all users on the platform. You can:
-   **Search** for users by name, email, or username.
-   **Filter** users by their status (e.g., Active, Suspended) or role.
-   **Sort** the list by clicking on column headers.

### 2.2. Creating a User

1.  Click the **"新建用户" (Create User)** button.
2.  Fill in the user's details, including first name, last name, email, and username.
3.  Assign one or more roles to the user.
4.  Click **"Create User"**. An invitation email will be sent to the user.

### 2.3. Editing a User

1.  Find the user in the list.
2.  Click the "Actions" menu (•••) and select **"Edit User"**.
3.  Modify the user's profile information, status, or roles.
4.  Click **"Save Changes"**.

### 2.4. Deleting a User

1.  Find the user in the list.
2.  Click the "Actions" menu (•••) and select **"Delete User"**.
3.  Confirm the action in the popup modal. This is a soft delete and can be reversed by an administrator if needed.

## 3. Monitoring Services

*(Instructions on how to use the Service Monitoring dashboard would go here...)*

## 4. Content Moderation

*(Instructions on how to use the Content Moderation Queue would go here...)*

## 5. Financial Management

*(Instructions on how to view transactions, manage subscriptions, and analyze revenue would go here...)*

---
*This document is a template. Please expand it with more details and screenshots.* 