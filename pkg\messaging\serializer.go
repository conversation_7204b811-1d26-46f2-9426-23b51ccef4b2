/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package messaging

import (
	"fmt"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
)

const (
	// HeaderEventType 事件类型头
	HeaderEventType = "x-event-type"
	// HeaderEventID 事件ID头
	HeaderEventID = "x-event-id"
	// HeaderSourceService 源服务头
	HeaderSourceService = "x-source-service"
	// HeaderTimestamp 时间戳头
	HeaderTimestamp = "x-timestamp"
)

// Serializer 序列化器接口
type Serializer interface {
	// Marshal 序列化 Protobuf 消息
	Marshal(msg proto.Message) ([]byte, error)
	// Unmarshal 反序列化 Protobuf 消息
	Unmarshal(data []byte, msg proto.Message) error
}

// ProtobufSerializer Protobuf 序列化器实现
type ProtobufSerializer struct{}

// NewProtobufSerializer 创建新的 Protobuf 序列化器
func NewProtobufSerializer() *ProtobufSerializer {
	return &ProtobufSerializer{}
}

// Marshal 序列化 Protobuf 消息
func (s *ProtobufSerializer) Marshal(msg proto.Message) ([]byte, error) {
	if msg == nil {
		return nil, fmt.Errorf("message cannot be nil")
	}

	data, err := proto.Marshal(msg)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrSerializationFail, err)
	}

	return data, nil
}

// Unmarshal 反序列化 Protobuf 消息
func (s *ProtobufSerializer) Unmarshal(data []byte, msg proto.Message) error {
	if data == nil {
		return fmt.Errorf("data cannot be nil")
	}
	if msg == nil {
		return fmt.Errorf("message cannot be nil")
	}

	if err := proto.Unmarshal(data, msg); err != nil {
		return fmt.Errorf("%w: %v", ErrDeserializationFail, err)
	}

	return nil
}

// Deserializer 反序列化器，支持根据事件类型动态创建消息
type Deserializer struct {
	serializer       Serializer
	eventTypeRegistry map[string]func() proto.Message
}

// NewDeserializer 创建新的反序列化器
func NewDeserializer(serializer Serializer) *Deserializer {
	return &Deserializer{
		serializer:       serializer,
		eventTypeRegistry: make(map[string]func() proto.Message),
	}
}

// RegisterEventType 注册事件类型
func (d *Deserializer) RegisterEventType(eventType string, factory func() proto.Message) {
	d.eventTypeRegistry[eventType] = factory
}

// RegisterEventTypes 批量注册事件类型
func (d *Deserializer) RegisterEventTypes(eventTypes map[string]func() proto.Message) {
	for eventType, factory := range eventTypes {
		d.eventTypeRegistry[eventType] = factory
	}
}

// Deserialize 根据事件类型反序列化消息
func (d *Deserializer) Deserialize(headers map[string]string, data []byte) (proto.Message, error) {
	eventType, ok := headers[HeaderEventType]
	if !ok {
		return nil, fmt.Errorf("%w: missing event type header", ErrUnknownEventType)
	}

	factory, ok := d.eventTypeRegistry[eventType]
	if !ok {
		return nil, fmt.Errorf("%w: %s", ErrUnknownEventType, eventType)
	}

	msg := factory()
	if err := d.serializer.Unmarshal(data, msg); err != nil {
		return nil, err
	}

	return msg, nil
}

// GetEventType 获取消息的事件类型
func GetEventType(msg proto.Message) string {
	if msg == nil {
		return ""
	}
	return string(msg.ProtoReflect().Descriptor().FullName())
}

// MessageFactory 消息工厂
type MessageFactory struct {
	registry map[string]func() proto.Message
}

// NewMessageFactory 创建消息工厂
func NewMessageFactory() *MessageFactory {
	return &MessageFactory{
		registry: make(map[string]func() proto.Message),
	}
}

// Register 注册消息类型
func (f *MessageFactory) Register(eventType string, factory func() proto.Message) {
	f.registry[eventType] = factory
}

// RegisterFromProtoRegistry 从 Proto 注册表自动注册
func (f *MessageFactory) RegisterFromProtoRegistry() error {
	protoregistry.GlobalTypes.RangeMessages(func(mt protoreflect.MessageType) bool {
		msgName := string(mt.Descriptor().FullName())
		f.registry[msgName] = func() proto.Message {
			return mt.New().Interface()
		}
		return true
	})
	return nil
}

// Create 创建消息实例
func (f *MessageFactory) Create(eventType string) (proto.Message, error) {
	factory, ok := f.registry[eventType]
	if !ok {
		return nil, fmt.Errorf("%w: %s", ErrUnknownEventType, eventType)
	}
	return factory(), nil
}

// GetRegisteredTypes 获取已注册的类型列表
func (f *MessageFactory) GetRegisteredTypes() []string {
	types := make([]string, 0, len(f.registry))
	for eventType := range f.registry {
		types = append(types, eventType)
	}
	return types
} 