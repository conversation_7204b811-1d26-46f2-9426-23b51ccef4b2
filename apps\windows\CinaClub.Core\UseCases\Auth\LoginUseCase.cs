/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using CinaClub.Core.Interfaces;
using CinaClub.Core.Models;
using CinaClub.App.ViewModels;

namespace CinaClub.Core.UseCases.Auth;

/// <summary>
/// 登录用例
/// 处理用户登录的业务逻辑
/// </summary>
public class LoginUseCase
{
    private readonly ILogger<LoginUseCase> _logger;
    private readonly IUserRepository _userRepository;
    private readonly ICryptoVault _cryptoVault;
    private readonly ISecureStorageService _secureStorage;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="userRepository">用户仓储</param>
    /// <param name="cryptoVault">加密保险库</param>
    /// <param name="secureStorage">安全存储服务</param>
    public LoginUseCase(
        ILogger<LoginUseCase> logger,
        IUserRepository userRepository,
        ICryptoVault cryptoVault,
        ISecureStorageService secureStorage)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _cryptoVault = cryptoVault ?? throw new ArgumentNullException(nameof(cryptoVault));
        _secureStorage = secureStorage ?? throw new ArgumentNullException(nameof(secureStorage));
    }

    /// <summary>
    /// 执行登录操作
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录结果</returns>
    public async Task<LoginResult> ExecuteAsync(LoginRequest request)
    {
        if (request == null)
        {
            throw new ArgumentNullException(nameof(request));
        }

        if (string.IsNullOrWhiteSpace(request.Username))
        {
            return new LoginResult
            {
                IsSuccess = false,
                ErrorMessage = "用户名不能为空"
            };
        }

        if (string.IsNullOrWhiteSpace(request.Password))
        {
            return new LoginResult
            {
                IsSuccess = false,
                ErrorMessage = "密码不能为空"
            };
        }

        try
        {
            _logger.LogInformation("开始登录用户: {Username}", request.Username);

            // 调用用户仓储进行登录验证
            var authResult = await _userRepository.AuthenticateAsync(request.Username, request.Password);

            if (!authResult.IsSuccess)
            {
                _logger.LogWarning("用户认证失败: {Username}", request.Username);
                return new LoginResult
                {
                    IsSuccess = false,
                    ErrorMessage = authResult.ErrorMessage ?? "用户名或密码错误"
                };
            }

            // 登录成功，获取用户信息
            var user = authResult.User;
            if (user == null)
            {
                _logger.LogError("认证成功但用户信息为空");
                return new LoginResult
                {
                    IsSuccess = false,
                    ErrorMessage = "系统错误，请稍后重试"
                };
            }

            // 派生用户主密钥（用于E2EE）
            var masterKey = await _cryptoVault.DeriveKeyFromPasswordAsync(request.Password, user.UserId);

            // 如果用户选择记住我，存储凭据
            if (request.RememberMe)
            {
                await StoreCredentialsAsync(user.UserId, authResult.AccessToken, authResult.RefreshToken, masterKey);
            }

            // 更新最后登录时间
            user.LastLoginAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("用户登录成功: {UserId}", user.UserId);

            return new LoginResult
            {
                IsSuccess = true,
                User = user,
                AccessToken = authResult.AccessToken,
                RefreshToken = authResult.RefreshToken
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登录过程中发生异常: {Username}", request.Username);
            return new LoginResult
            {
                IsSuccess = false,
                ErrorMessage = "网络连接异常，请稍后重试"
            };
        }
    }

    /// <summary>
    /// 存储用户凭据（记住我功能）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="accessToken">访问令牌</param>
    /// <param name="refreshToken">刷新令牌</param>
    /// <param name="masterKey">主密钥</param>
    private async Task StoreCredentialsAsync(string userId, string? accessToken, string? refreshToken, byte[] masterKey)
    {
        try
        {
            if (!string.IsNullOrEmpty(accessToken))
            {
                await _secureStorage.SetAsync($"access_token_{userId}", accessToken);
            }

            if (!string.IsNullOrEmpty(refreshToken))
            {
                await _secureStorage.SetAsync($"refresh_token_{userId}", refreshToken);
            }

            // 存储加密后的主密钥
            await _secureStorage.SetAsync($"master_key_{userId}", Convert.ToBase64String(masterKey));

            // 记录当前登录用户
            await _secureStorage.SetAsync("current_user_id", userId);

            _logger.LogDebug("用户凭据已安全存储: {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "存储用户凭据时发生异常: {UserId}", userId);
            // 不抛出异常，因为这不是关键功能
        }
    }
}

/// <summary>
/// 认证结果
/// </summary>
public class AuthenticationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public User? User { get; set; }

    /// <summary>
    /// 访问令牌
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string? RefreshToken { get; set; }
} 