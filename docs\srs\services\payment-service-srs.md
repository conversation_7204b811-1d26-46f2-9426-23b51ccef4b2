﻿好的，遵照您的指示，我们来生成一份为 `payment-service` (核心支付处理服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **支付路由与多网关**: 详细定义如何支持多个支付网关（Stripe, PayPal, Alipay, WeChat Pay），并引入基于地区、货币和成本的智能支付路由。
2.  **支付订单状态机**: 引入更健壮、更细致的支付订单状态机，覆盖预授权、捕获、多方支付等复杂场景。
3.  **退款与争议处理**: 增加对部分退款、多次退款以及处理支付渠道争议（Chargeback）的流程描述。
4.  **支付方式管理**: 详细定义如何安全地管理用户绑定的支付方式令牌（如Stripe的PaymentMethod ID），并支持默认支付方式设置。
5.  **对账与结算**: 明确后台自动对账和生成结算报告的流程，这是生产级金融系统的必备功能。
6.  **细化API与数据模型**: 提供更具体的gRPC/Protobuf接口定义和事件契约，并对数据模型进行优化。
7.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性、安全和合规性指标。

这份文档将描绘一个功能强大、安全合规、高可用，且能作为整个平台所有法币交易统一入口的支付网关服务。

---

### CINA.CLUB - payment-service 需求规格说明书

**版本: 2.0 (生产级定义，集成多网关路由与高级交易)**  
**发布日期: 2025-06-23**  
**最后修订日期: 2025-06-23**  
**文档负责人:** [支付与金融技术负责人/安全架构师名称]  
**审批人:** [CTO/CFO]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
CINA.CLUB 平台涉及多种需要法定货币交易的场景，如购买虚拟货币、支付服务费等。`payment-service` 的目的在于构建一个**极度安全、可靠、准确且合规的中央支付处理核心**。它通过封装和智能路由对所有外部支付网关（如Stripe, PayPal, 支付宝, 微信支付）的复杂交互，为平台所有需要法币交易的业务提供统一、简单、经济高效的底层支付能力。

#### 1.2. 服务范围
本服务 **负责**:
*   **支付订单管理**: 接收来自平台内部的标准化支付请求，并管理支付订单的完整生命周期。
*   **多支付网关集成与智能路由**:
    *   封装对多个支付网关API的调用。
    *   根据地区、货币、成本等因素，为每笔交易动态选择最优的支付网关。
*   **支付流程处理**:
    *   处理标准的“支付-捕获”流程。
    *   支持“预授权-捕获/取消”流程，用于担保交易。
*   **支付网关回调处理**: 提供安全的Webhook端点，可靠地接收和验证支付网关的异步支付结果通知。
*   **退款与争议处理**:
    *   处理全额、部分和多次退款请求。
    *   记录和处理来自支付网关的争议（Chargeback）事件。
*   **支付方式管理**: 安全地管理用户绑定的支付方式的非敏感引用令牌。
*   **对账与结算**: 提供后台自动对账和生成结算报告的能力。

本服务 **不负责**:
*   **业务订单管理**: 由各自的业务服务（如`billing-service`）负责。
*   **复杂的计费和订阅逻辑**: 由`billing-service`负责。
*   **灵境币的内部账本管理**: 由`cina-coin-ledger-service`负责。
*   **直接存储任何完整的、敏感的支付信息 (如信用卡号)**。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB内部业务微服务 (主要)**: `billing-service`, `cina-coin-ledger-service`等。
*   **外部支付网关**: (通过Webhook) 向本服务发送异步通知。
*   **CINA.CLUB财务/运营团队**: 通过管理后台进行交易查询、退款审批、对账。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`payment-service` 是CINA.CLUB平台的**资金流动网关**，是所有法币交易的**唯一、权威处理者**。它将复杂的支付渠道差异性对上层业务服务屏蔽，提供了一个标准化的支付接口，同时集中实施了最高级别的安全、合规和成本优化策略。其健壮性是平台商业化运作的生命线。

#### 2.2. 主要功能概述
*   支持多支付网关的适配器与智能路由。
*   健壮的、支持预授权的支付订单生命周期管理。
*   可靠、幂等的Webhook回调处理。
*   完善的退款、争议处理和对账流程。
*   符合PCI-DSS合规要求的安全支付方式管理。

### 3. 核心流程图

#### 3.1. 处理一笔需要预授权的支付
```mermaid
sequenceDiagram
    participant BusinessService as "e.g., service-offering"
    participant PaymentService as PS
    participant PaymentGateway as "e.g., Stripe"
    participant MQ

    BusinessService->>PS: 1. POST /initiate (amount: 100, capture_method: "MANUAL")
    PS->>PaymentGateway: 2. Create PaymentIntent (amount: 100, capture_method: "manual")
    PaymentGateway-->>PS: (client_secret, payment_intent_id)
    PS->>DB: 3. Create PaymentOrder (status: AWAITING_AUTHORIZATION)
    PS-->>BusinessService: 4. (client_secret, payment_order_id)
    
    Note over BusinessService: Client uses client_secret to confirm payment on frontend.
    
    PaymentGateway->>PS: 5. **[Webhook]** PaymentIntent Succeeded (Authorized)
    PS->>PS: 6. Validate Webhook, find PaymentOrder
    PS->>DB: 7. Update PaymentOrder status to AUTHORIZED
    PS->>MQ: 8. Publish PaymentAuthorizedEvent
    
    Note over BusinessService: Later, service is completed.
    
    BusinessService->>PS: 9. POST /capture (payment_order_id)
    PS->>PaymentGateway: 10. Capture PaymentIntent
    
    PaymentGateway->>PS: 11. **[Webhook]** PaymentIntent Captured
    PS->>DB: 12. Update PaymentOrder status to SUCCEEDED
    PS->>MQ: 13. Publish PaymentSucceededEvent
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 支付流程处理
*   **FR4.1.1 (支付订单状态机)**: `PaymentOrder`的状态必须能准确反映支付的整个生命周期：
    *   `PENDING`: 已创建，待初始化。
    *   `AWAITING_USER_ACTION`: 等待用户在支付网关页面完成操作。
    *   `AUTHORIZED`: (用于预授权) 资金已授权，但未捕获。
    *   `PROCESSING`: 正在处理中。
    *   `SUCCEEDED`: 支付成功。
    *   `FAILED`: 支付失败。
    *   `CANCELED`: 已取消。
    *   `PARTIALLY_REFUNDED` / `REFUNDED`: 已部分/全额退款。
*   **FR4.1.2 (预授权与捕获)**: 系统必须支持“预授权-捕获”流程。`initiate`接口需支持`capture_method: "AUTOMATIC" | "MANUAL"`参数。对于`MANUAL`，后续必须通过`capture`接口来实际扣款。

#### 4.2. 多支付网关与智能路由
*   **FR4.2.1 (适配器模式)**: 必须为每个集成的支付网关实现统一的`PaymentGatewayProvider`接口。
*   **FR4.2.2 (智能路由)**: `initiate`接口在被调用时，路由引擎应根据**请求的货币、用户所在地区、以及各网关的交易成本**，动态选择最优的支付网关。此路由逻辑必须是可配置的。
*   **FR4.2.3 (故障降级)**: 当对主网关的API调用失败时，应能自动尝试使用备用网关。

#### 4.3. 退款与争议处理
*   **FR4.3.1 (部分退款)**: 退款接口必须支持指定退款金额，允许部分退款。
*   **FR4.3.2 (退款流水)**: 每次退款都必须生成一条独立的`RefundTransaction`记录，并与原始的`PaymentTransaction`关联。
*   **FR4.3.3 (争议处理)**: 必须提供Webhook端点来接收支付网关的争议（Chargeback）通知。收到通知后，应更新`PaymentOrder`状态，并创建一条`Dispute`记录，同时发送告警给运营团队。

#### 4.4. 支付方式管理
*   **FR4.4.1 (安全存储)**: 系统**绝不**存储卡号、CVV等敏感信息。只存储支付网关返回的、安全的、可用于后续扣款的支付方式令牌/ID（如Stripe的`pm_...`或`cus_...`）。
*   **FR4.4.2 (CRUD)**: 提供API供用户（通过客户端）管理其绑定的支付方式（列出、设置默认、删除）。删除操作会调用网关API来分离(detach)该支付方式。

#### 4.5. 对账与结算
*   **FR4.5.1 (自动对账)**: 系统必须有一个后台任务，定期（如每日）通过API下载支付网关的交易对账单。
*   **FR4.5.2 (差异分析)**: 该任务将对账单与本服务的`payment_transactions`表进行比对，自动核对一致的交易，并高亮显示任何状态或金额不符的差异，生成差异报告。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC API接口 (S2S)
*   **Package**: `hina.vip.payment.v1`
*   **认证**: 严格的S2S认证 (mTLS + 服务级JWT)。
*   **核心RPC**:
    ```protobuf
    service PaymentService {
      rpc InitiatePayment(InitiatePaymentRequest) returns (InitiatePaymentResponse);
      rpc CapturePayment(CapturePaymentRequest) returns (PaymentOrder);
      rpc CancelPayment(CancelPaymentRequest) returns (PaymentOrder);
      rpc InitiateRefund(InitiateRefundRequest) returns (Refund);
      rpc GetPaymentStatusByBusinessOrder(GetPaymentStatusByBusinessOrderRequest) returns (PaymentOrder);
    }
    ```

#### 5.2. Webhook API接口 (来自支付网关)
*   `POST /webhooks/{gatewayType}`: 必须通过严格的签名验证。

#### 5.3. 消息队列事件契约 (出站)
*   **Topic**: `hina_payment_events`
*   **Events**: `PaymentSucceededEvent`, `PaymentFailedEvent`, `PaymentAuthorizedEvent`, `RefundCompletedEvent`, `DisputeCreatedEvent`.

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL)
*   **`payment_orders`**: `id`, `user_id`, `business_order_id`, `status`, `amount`, `currency`, `gateway`, `gateway_payment_id`.
*   **`payment_transactions`**: `id`, `payment_order_id`, `type` (`CHARGE`, `REFUND`), `status`, `amount`, `gateway_transaction_id`.
*   **`refunds`**: `id`, `payment_order_id`, `status`, `amount`, `reason`.
*   **`disputes`**: `id`, `payment_order_id`, `status`, `reason`, `amount`.
*   **`payment_methods`**: `id`, `user_id`, `gateway`, `gateway_method_id`, `type` (`CARD`, `ALIPAY`), `details (JSONB)`, `is_default`.

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**: P99 < 150ms (不含外部网关调用时间)。
*   **Webhook处理**: 接收并确认Webhook的P99 < 50ms。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.99%。支付服务是平台的关键路径，可用性要求最高。
*   **数据准确性**: 财务数据的准确性是第一要务。所有金额计算必须精确无误。
*   **容错**: 对支付网关的调用失败必须有健壮的重试、超时和熔断机制。

#### 7.3. 可扩展性需求
*   服务应为无状态，可水平扩展。数据库是扩展瓶颈，需考虑读写分离和分区。

#### 7.4. 安全性与合规性 (最高优先级)
*   **PCI DSS合规**: 严格遵循不直接处理和存储敏感持卡人数据的原则，将合规负担转移到支付网关。
*   **密钥安全**: 支付网关的API密钥、私钥、Webhook密钥必须安全存储在Vault或KMS中。
*   **审计日志**: 所有资金相关的操作都必须有不可篡改的审计日志。
*   **合规性**: 遵守目标市场的支付、金融、反洗钱(AML)相关法规。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: PostgreSQL。
*   **网关集成**: 采用“适配器”模式，清晰隔离不同支付网关的实现。
*   **异步处理**: Webhook处理、对账等后台任务必须异步化。

---
这份版本2.0的SRS文档为`payment-service`构建了一个功能全面、安全合规、架构先进的支付中台。它通过多网关路由、高级交易流程和完善的后台治理功能，能够为CINA.CLUB平台复杂多变的商业化需求提供坚实、可靠的支撑。