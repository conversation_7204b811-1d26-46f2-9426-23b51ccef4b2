/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-23 11:30:00
Modified: 2025-01-23 11:30:00
*/

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'

import { 
  User, 
  UserRole, 
  UserStatus, 
  CreateUserData, 
  UpdateUserData,
  UserActivity,
  UserPermissions,
  UserPreferences
} from '@/types/user'
import { apiClient } from '@/lib/api-client'

// Query keys for user management
export const USER_QUERY_KEYS = {
  // Users list and search
  users: 'users',
  usersList: (params?: any) => ['users', 'list', params],
  usersSearch: (query: string) => ['users', 'search', query],
  
  // Individual user
  user: (id: string) => ['users', id],
  userProfile: (id: string) => ['users', id, 'profile'],
  userActivity: (id: string) => ['users', id, 'activity'],
  userPermissions: (id: string) => ['users', id, 'permissions'],
  userPreferences: (id: string) => ['users', id, 'preferences'],
  userSessions: (id: string) => ['users', id, 'sessions'],
  
  // User analytics
  userStats: 'user-stats',
  userGrowth: 'user-growth',
  userRoleDistribution: 'user-role-distribution',
  
  // Bulk operations
  bulkOperations: 'user-bulk-operations',
  
  // User verification
  verification: 'user-verification',
} as const

// Main user management hooks
export const useUserManagement = () => {
  const queryClient = useQueryClient()

  // Get users list with pagination and filters
  const useUsers = (params?: any) => {
    return useQuery({
      queryKey: USER_QUERY_KEYS.usersList(params),
      queryFn: async () => {
        const response = await apiClient.get('/admin/users', { params })
        return response.data
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
      keepPreviousData: true, // Keep previous data while loading new data
    })
  }

  // Get user by ID
  const useUser = (userId: string) => {
    return useQuery({
      queryKey: USER_QUERY_KEYS.user(userId),
      queryFn: async () => {
        const response = await apiClient.get(`/admin/users/${userId}`)
        return response.data
      },
      enabled: !!userId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  // Search users
  const useSearchUsers = (query: string) => {
    return useQuery({
      queryKey: USER_QUERY_KEYS.usersSearch(query),
      queryFn: async () => {
        const response = await apiClient.get('/admin/users/search', {
          params: { q: query }
        })
        return response.data
      },
      enabled: query.length >= 2, // Only search if query is at least 2 characters
      staleTime: 30 * 1000, // 30 seconds for search results
    })
  }

  // Create new user
  const useCreateUser = () => {
    return useMutation({
      mutationFn: async (userData: CreateUserData) => {
        const response = await apiClient.post('/admin/users', userData)
        return response.data
      },
      onSuccess: (data) => {
        message.success('用户创建成功')
        // Invalidate users list to refresh the data
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
        // Add the new user to cache
        queryClient.setQueryData(USER_QUERY_KEYS.user(data.id), data)
      },
      onError: (error: any) => {
        console.error('Create user failed:', error)
        message.error(error.response?.data?.message || '用户创建失败')
      },
    })
  }

  // Update user
  const useUpdateUser = () => {
    return useMutation({
      mutationFn: async ({ 
        userId, 
        userData 
      }: {
        userId: string
        userData: UpdateUserData
      }) => {
        const response = await apiClient.put(`/admin/users/${userId}`, userData)
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success('用户更新成功')
        // Update user in cache
        queryClient.setQueryData(USER_QUERY_KEYS.user(variables.userId), data)
        // Invalidate users list to refresh
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
      },
      onError: (error: any) => {
        console.error('Update user failed:', error)
        message.error(error.response?.data?.message || '用户更新失败')
      },
    })
  }

  // Delete user
  const useDeleteUser = () => {
    return useMutation({
      mutationFn: async (userId: string) => {
        const response = await apiClient.delete(`/admin/users/${userId}`)
        return response.data
      },
      onSuccess: (_, userId) => {
        message.success('用户删除成功')
        // Remove user from cache
        queryClient.removeQueries({ queryKey: USER_QUERY_KEYS.user(userId) })
        // Invalidate users list
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
      },
      onError: (error: any) => {
        console.error('Delete user failed:', error)
        message.error(error.response?.data?.message || '用户删除失败')
      },
    })
  }

  // Update user status
  const useUpdateUserStatus = () => {
    return useMutation({
      mutationFn: async ({ 
        userId, 
        status, 
        reason 
      }: {
        userId: string
        status: UserStatus
        reason?: string
      }) => {
        const response = await apiClient.put(`/admin/users/${userId}/status`, {
          status,
          reason,
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success('用户状态更新成功')
        // Update user status in cache
        queryClient.setQueryData(
          USER_QUERY_KEYS.user(variables.userId), 
          (oldData: User) => ({ ...oldData, status: variables.status })
        )
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
      },
      onError: (error: any) => {
        console.error('Update user status failed:', error)
        message.error(error.response?.data?.message || '状态更新失败')
      },
    })
  }

  // Update user role
  const useUpdateUserRole = () => {
    return useMutation({
      mutationFn: async ({ 
        userId, 
        role, 
        reason 
      }: {
        userId: string
        role: UserRole
        reason?: string
      }) => {
        const response = await apiClient.put(`/admin/users/${userId}/role`, {
          role,
          reason,
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success('用户角色更新成功')
        // Update user role in cache
        queryClient.setQueryData(
          USER_QUERY_KEYS.user(variables.userId), 
          (oldData: User) => ({ ...oldData, role: variables.role })
        )
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.userRoleDistribution] })
      },
      onError: (error: any) => {
        console.error('Update user role failed:', error)
        message.error(error.response?.data?.message || '角色更新失败')
      },
    })
  }

  // Reset user password
  const useResetUserPassword = () => {
    return useMutation({
      mutationFn: async ({ 
        userId, 
        reason, 
        notifyUser 
      }: {
        userId: string
        reason: string
        notifyUser?: boolean
      }) => {
        const response = await apiClient.post(`/admin/users/${userId}/reset-password`, {
          reason,
          notifyUser,
        })
        return response.data
      },
      onSuccess: () => {
        message.success('密码重置成功，新密码已发送到用户邮箱')
      },
      onError: (error: any) => {
        console.error('Reset password failed:', error)
        message.error(error.response?.data?.message || '密码重置失败')
      },
    })
  }

  return {
    useUsers,
    useUser,
    useSearchUsers,
    useCreateUser,
    useUpdateUser,
    useDeleteUser,
    useUpdateUserStatus,
    useUpdateUserRole,
    useResetUserPassword,
  }
}

// User activity and audit hooks
export const useUserActivity = () => {
  // Get user activity log
  const useUserActivityLog = (userId: string, params?: any) => {
    return useQuery({
      queryKey: USER_QUERY_KEYS.userActivity(userId),
      queryFn: async () => {
        const response = await apiClient.get(`/admin/users/${userId}/activity`, { params })
        return response.data
      },
      enabled: !!userId,
      staleTime: 60 * 1000, // 1 minute
    })
  }

  return {
    useUserActivityLog,
  }
}

// User permissions and roles hooks
export const useUserPermissions = () => {
  // Get user permissions
  const useUserPermissions = (userId: string) => {
    return useQuery({
      queryKey: USER_QUERY_KEYS.userPermissions(userId),
      queryFn: async () => {
        const response = await apiClient.get(`/admin/users/${userId}/permissions`)
        return response.data
      },
      enabled: !!userId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    })
  }

  // Update user permissions
  const useUpdateUserPermissions = () => {
    const queryClient = useQueryClient()
    
    return useMutation({
      mutationFn: async ({ 
        userId, 
        permissions 
      }: {
        userId: string
        permissions: UserPermissions
      }) => {
        const response = await apiClient.put(`/admin/users/${userId}/permissions`, permissions)
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success('用户权限更新成功')
        queryClient.setQueryData(USER_QUERY_KEYS.userPermissions(variables.userId), data)
        queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.user(variables.userId) })
      },
      onError: (error: any) => {
        console.error('Update user permissions failed:', error)
        message.error(error.response?.data?.message || '权限更新失败')
      },
    })
  }

  return {
    useUserPermissions,
    useUpdateUserPermissions,
  }
}

// User preferences hooks
export const useUserPreferences = () => {
  // Get user preferences
  const useUserPreferences = (userId: string) => {
    return useQuery({
      queryKey: USER_QUERY_KEYS.userPreferences(userId),
      queryFn: async () => {
        const response = await apiClient.get(`/admin/users/${userId}/preferences`)
        return response.data
      },
      enabled: !!userId,
      staleTime: 15 * 60 * 1000, // 15 minutes
    })
  }

  // Update user preferences
  const useUpdateUserPreferences = () => {
    const queryClient = useQueryClient()
    
    return useMutation({
      mutationFn: async ({ 
        userId, 
        preferences 
      }: {
        userId: string
        preferences: UserPreferences
      }) => {
        const response = await apiClient.put(`/admin/users/${userId}/preferences`, preferences)
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success('用户偏好设置更新成功')
        queryClient.setQueryData(USER_QUERY_KEYS.userPreferences(variables.userId), data)
      },
      onError: (error: any) => {
        console.error('Update user preferences failed:', error)
        message.error(error.response?.data?.message || '偏好设置更新失败')
      },
    })
  }

  return {
    useUserPreferences,
    useUpdateUserPreferences,
  }
}

// Bulk operations hooks
export const useBulkUserOperations = () => {
  const queryClient = useQueryClient()

  // Bulk delete users
  const useBulkDeleteUsers = () => {
    return useMutation({
      mutationFn: async ({ 
        userIds, 
        reason 
      }: {
        userIds: string[]
        reason?: string
      }) => {
        const response = await apiClient.post('/admin/users/bulk/delete', {
          userIds,
          reason,
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success(`成功删除 ${variables.userIds.length} 个用户`)
        // Remove deleted users from cache
        variables.userIds.forEach(userId => {
          queryClient.removeQueries({ queryKey: USER_QUERY_KEYS.user(userId) })
        })
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
      },
      onError: (error: any) => {
        console.error('Bulk delete users failed:', error)
        message.error(error.response?.data?.message || '批量删除失败')
      },
    })
  }

  // Bulk update user status
  const useBulkUpdateUserStatus = () => {
    return useMutation({
      mutationFn: async ({ 
        userIds, 
        status, 
        reason 
      }: {
        userIds: string[]
        status: UserStatus
        reason?: string
      }) => {
        const response = await apiClient.post('/admin/users/bulk/status', {
          userIds,
          status,
          reason,
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success(`成功更新 ${variables.userIds.length} 个用户状态`)
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
      },
      onError: (error: any) => {
        console.error('Bulk update user status failed:', error)
        message.error(error.response?.data?.message || '批量状态更新失败')
      },
    })
  }

  // Bulk update user role
  const useBulkUpdateUserRole = () => {
    return useMutation({
      mutationFn: async ({ 
        userIds, 
        role, 
        reason 
      }: {
        userIds: string[]
        role: UserRole
        reason?: string
      }) => {
        const response = await apiClient.post('/admin/users/bulk/role', {
          userIds,
          role,
          reason,
        })
        return response.data
      },
      onSuccess: (data, variables) => {
        message.success(`成功更新 ${variables.userIds.length} 个用户角色`)
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.users] })
        queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEYS.userRoleDistribution] })
      },
      onError: (error: any) => {
        console.error('Bulk update user role failed:', error)
        message.error(error.response?.data?.message || '批量角色更新失败')
      },
    })
  }

  return {
    useBulkDeleteUsers,
    useBulkUpdateUserStatus,
    useBulkUpdateUserRole,
  }
}

// User verification hooks
export const useUserVerification = () => {
  const queryClient = useQueryClient()

  // Send email verification
  const useSendEmailVerification = () => {
    return useMutation({
      mutationFn: async (userId: string) => {
        const response = await apiClient.post(`/admin/users/${userId}/verify-email`)
        return response.data
      },
      onSuccess: () => {
        message.success('邮箱验证邮件发送成功')
      },
      onError: (error: any) => {
        console.error('Send email verification failed:', error)
        message.error(error.response?.data?.message || '邮箱验证发送失败')
      },
    })
  }

  // Send phone verification
  const useSendPhoneVerification = () => {
    return useMutation({
      mutationFn: async (userId: string) => {
        const response = await apiClient.post(`/admin/users/${userId}/verify-phone`)
        return response.data
      },
      onSuccess: () => {
        message.success('手机验证码发送成功')
      },
      onError: (error: any) => {
        console.error('Send phone verification failed:', error)
        message.error(error.response?.data?.message || '手机验证发送失败')
      },
    })
  }

  // Reset user password
  const useResetUserPassword = () => {
    return useMutation({
      mutationFn: async ({ 
        userId, 
        reason, 
        notifyUser 
      }: {
        userId: string
        reason: string
        notifyUser?: boolean
      }) => {
        const response = await apiClient.post(`/admin/users/${userId}/reset-password`, {
          reason,
          notifyUser,
        })
        return response.data
      },
      onSuccess: () => {
        message.success('密码重置成功，新密码已发送到用户邮箱')
      },
      onError: (error: any) => {
        console.error('Reset password failed:', error)
        message.error(error.response?.data?.message || '密码重置失败')
      },
    })
  }

  return {
    useSendEmailVerification,
    useSendPhoneVerification,
    useResetUserPassword,
  }
}

// User statistics hooks
export const useUserStatistics = () => {
  // Get user statistics
  const useUserStats = () => {
    return useQuery({
      queryKey: [USER_QUERY_KEYS.userStats],
      queryFn: async () => {
        const response = await apiClient.get('/admin/users/stats')
        return response.data
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchInterval: 10 * 60 * 1000, // Refresh every 10 minutes
    })
  }

  // Get user growth data
  const useUserGrowth = (dateRange: string = '30days') => {
    return useQuery({
      queryKey: [USER_QUERY_KEYS.userGrowth, dateRange],
      queryFn: async () => {
        const response = await apiClient.get('/admin/users/growth', {
          params: { dateRange }
        })
        return response.data
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    })
  }

  // Get user role distribution
  const useUserRoleDistribution = () => {
    return useQuery({
      queryKey: [USER_QUERY_KEYS.userRoleDistribution],
      queryFn: async () => {
        const response = await apiClient.get('/admin/users/role-distribution')
        return response.data
      },
      staleTime: 15 * 60 * 1000, // 15 minutes
    })
  }

  return {
    useUserStats,
    useUserGrowth,
    useUserRoleDistribution,
  }
}

// Export utility hook that combines all user management hooks
export const useAllUserHooks = () => {
  const userManagement = useUserManagement()
  const userActivity = useUserActivity()
  const userPermissions = useUserPermissions()
  const userPreferences = useUserPreferences()
  const bulkOperations = useBulkUserOperations()
  const verification = useUserVerification()
  const statistics = useUserStatistics()

  return {
    ...userManagement,
    ...userActivity,
    ...userPermissions,
    ...userPreferences,
    ...bulkOperations,
    ...verification,
    ...statistics,
  }
}

// Export individual hook categories
export { 
  useUserManagement, 
  useUserActivity, 
  useUserPermissions, 
  useUserPreferences,
  useBulkUserOperations,
  useUserVerification,
  useUserStatistics 
} 