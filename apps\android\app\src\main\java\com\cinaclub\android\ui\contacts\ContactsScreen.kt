/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 16:30:00
 * Modified: 2025-06-20 16:30:00
 */

package com.cinaclub.android.ui.contacts

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Enterprise contacts screen following WeChat Work design.
 * Shows organizational structure and employee directory.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactsScreen(
    onNavigateToSearch: () -> Unit,
    onDepartmentClick: (Department) -> Unit,
    onContactClick: (Contact) -> Unit,
    modifier: Modifier = Modifier
) {
    val departments = remember {
        listOf(
            Department(
                id = "1",
                name = "海内电气的供应商",
                icon = Icons.Default.Business,
                color = Color(0xFF4A90E2)
            ),
            Department(
                id = "2", 
                name = "总经办",
                icon = Icons.Default.Folder,
                color = Color(0xFF4A90E2),
                subtitle = "企业通讯录"
            ),
            Department(
                id = "3",
                name = "管理部", 
                icon = Icons.Default.Folder,
                color = Color(0xFF4A90E2)
            ),
            Department(
                id = "4",
                name = "财务部",
                icon = Icons.Default.Folder, 
                color = Color(0xFF4A90E2)
            ),
            Department(
                id = "5",
                name = "制造部",
                icon = Icons.Default.Folder,
                color = Color(0xFF4A90E2)
            ),
            Department(
                id = "6", 
                name = "企划部",
                icon = Icons.Default.Folder,
                color = Color(0xFF4A90E2)
            ),
            Department(
                id = "7",
                name = "业务部", 
                icon = Icons.Default.Folder,
                color = Color(0xFF4A90E2)
            ),
            Department(
                id = "8",
                name = "研发部",
                icon = Icons.Default.Folder,
                color = Color(0xFF4A90E2)
            )
        )
    }
    
    val aiAssistants = remember {
        listOf(
            Contact(
                id = "ai_1",
                name = "智能机器人",
                subtitle = "共1人"
            )
        )
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "海内电气",
                    fontWeight = FontWeight.Medium
                )
            },
            actions = {
                IconButton(onClick = onNavigateToSearch) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "Search"
                    )
                }
                
                IconButton(onClick = { /* Open more options */ }) {
                    Icon(
                        imageVector = Icons.Default.Menu,
                        contentDescription = "More"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFF4A90E2)
            )
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Company header
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Company icon
                        Surface(
                            modifier = Modifier.size(40.dp),
                            color = Color(0xFF4A90E2),
                            shape = CircleShape
                        ) {
                            Icon(
                                imageVector = Icons.Default.Business,
                                contentDescription = "Company",
                                tint = Color.White,
                                modifier = Modifier.padding(8.dp)
                            )
                        }
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = "海内电气的供应商",
                                color = Color(0xFF333333),
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                        
                        Icon(
                            imageVector = Icons.Default.ChevronRight,
                            contentDescription = null,
                            tint = Color(0xFFBBBBBB)
                        )
                    }
                }
            }
            
            // Departments section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Column {
                        departments.forEachIndexed { index, department ->
                            DepartmentItem(
                                department = department,
                                onClick = { onDepartmentClick(department) }
                            )
                            
                            if (index < departments.size - 1) {
                                Divider(
                                    modifier = Modifier.padding(start = 56.dp),
                                    color = Color(0xFFE0E0E0)
                                )
                            }
                        }
                        
                        // Add member button
                        Divider(color = Color(0xFFE0E0E0))
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { /* Add member action */ }
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Surface(
                                modifier = Modifier.size(24.dp),
                                color = Color(0xFF4A90E2),
                                shape = CircleShape
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = "Add Member",
                                    tint = Color.White,
                                    modifier = Modifier.padding(4.dp)
                                )
                            }
                            
                            Spacer(modifier = Modifier.width(16.dp))
                            
                            Text(
                                text = "添加成员",
                                color = Color(0xFF4A90E2),
                                fontSize = 16.sp
                            )
                        }
                    }
                }
            }
            
            // AI Assistants section
            item {
                Text(
                    text = "智能机器人",
                    color = Color(0xFF888888),
                    fontSize = 14.sp,
                    modifier = Modifier.padding(start = 16.dp, bottom = 8.dp)
                )
                
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    )
                ) {
                    Column {
                        aiAssistants.forEach { contact ->
                            ContactItem(
                                contact = contact,
                                onClick = { onContactClick(contact) }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun DepartmentItem(
    department: Department,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Surface(
            modifier = Modifier.size(24.dp),
            color = department.color,
            shape = CircleShape
        ) {
            Icon(
                imageVector = department.icon,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.padding(4.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = department.name,
                color = Color(0xFF333333),
                fontSize = 16.sp
            )
            
            if (department.subtitle != null) {
                Text(
                    text = department.subtitle,
                    color = Color(0xFF888888),
                    fontSize = 12.sp
                )
            }
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = null,
            tint = Color(0xFFBBBBBB)
        )
    }
}

@Composable
private fun ContactItem(
    contact: Contact,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Surface(
            modifier = Modifier.size(40.dp),
            color = Color(0xFF4A90E2),
            shape = CircleShape
        ) {
            Icon(
                imageVector = Icons.Default.SmartToy,
                contentDescription = "AI Assistant",
                tint = Color.White,
                modifier = Modifier.padding(8.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = contact.name,
                color = Color(0xFF333333),
                fontSize = 16.sp
            )
            
            if (contact.subtitle != null) {
                Text(
                    text = contact.subtitle,
                    color = Color(0xFF888888),
                    fontSize = 12.sp
                )
            }
        }
    }
}

// Data classes for contacts and departments
data class Department(
    val id: String,
    val name: String,
    val icon: ImageVector,
    val color: Color,
    val subtitle: String? = null
)

data class Contact(
    val id: String,
    val name: String,
    val subtitle: String? = null
) 