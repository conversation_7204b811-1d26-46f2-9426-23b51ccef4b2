/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

#include <napi/native_api.h>
#include <hilog/log.h>
#include "include/core_go.h"
#include "utils/type_converter.h"
#include "utils/async_work.h"

constexpr unsigned int LOG_DOMAIN = 0x3900;
constexpr char LOG_TAG[] = "CryptoNAPI";

namespace {

/**
 * 加密异步工作数据
 */
struct EncryptAsyncData {
    napi_async_work work;
    napi_deferred deferred;
    GoSlice key;
    GoSlice plaintext;
    GoSlice result;
    bool success;
    std::string errorMessage;
};

/**
 * 解密异步工作数据
 */
struct DecryptAsyncData {
    napi_async_work work;
    napi_deferred deferred;
    GoSlice key;
    GoSlice ciphertext;
    GoSlice result;
    bool success;
    std::string errorMessage;
};

/**
 * 异步执行加密操作
 */
void ExecuteEncrypt(napi_env env, void* data) {
    auto* asyncData = static_cast<EncryptAsyncData*>(data);
    
    try {
        asyncData->result = GoEncryptSymmetric(asyncData->key, asyncData->plaintext);
        asyncData->success = true;
        OH_LOG_INFO(LOG_DOMAIN, LOG_TAG, "Encryption completed successfully");
    } catch (const std::exception& e) {
        asyncData->success = false;
        asyncData->errorMessage = e.what();
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Encryption failed: %{public}s", e.what());
    }
}

/**
 * 加密操作完成回调
 */
void CompleteEncrypt(napi_env env, napi_status status, void* data) {
    auto* asyncData = static_cast<EncryptAsyncData*>(data);
    
    if (asyncData->success) {
        napi_value result;
        TypeConverter::GoSliceToArrayBuffer(env, asyncData->result, &result);
        napi_resolve_deferred(env, asyncData->deferred, result);
        
        // 释放Go分配的内存
        GoFreeSlice(asyncData->result);
    } else {
        napi_value error;
        napi_create_string_utf8(env, asyncData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &error);
        napi_reject_deferred(env, asyncData->deferred, error);
    }
    
    // 清理工作数据
    napi_delete_async_work(env, asyncData->work);
    delete asyncData;
}

/**
 * 异步执行解密操作
 */
void ExecuteDecrypt(napi_env env, void* data) {
    auto* asyncData = static_cast<DecryptAsyncData*>(data);
    
    try {
        asyncData->result = GoDecryptSymmetric(asyncData->key, asyncData->ciphertext);
        asyncData->success = true;
        OH_LOG_INFO(LOG_DOMAIN, LOG_TAG, "Decryption completed successfully");
    } catch (const std::exception& e) {
        asyncData->success = false;
        asyncData->errorMessage = e.what();
        OH_LOG_ERROR(LOG_DOMAIN, LOG_TAG, "Decryption failed: %{public}s", e.what());
    }
}

/**
 * 解密操作完成回调
 */
void CompleteDecrypt(napi_env env, napi_status status, void* data) {
    auto* asyncData = static_cast<DecryptAsyncData*>(data);
    
    if (asyncData->success) {
        napi_value result;
        TypeConverter::GoSliceToArrayBuffer(env, asyncData->result, &result);
        napi_resolve_deferred(env, asyncData->deferred, result);
        
        // 释放Go分配的内存
        GoFreeSlice(asyncData->result);
    } else {
        napi_value error;
        napi_create_string_utf8(env, asyncData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &error);
        napi_reject_deferred(env, asyncData->deferred, error);
    }
    
    // 清理工作数据
    napi_delete_async_work(env, asyncData->work);
    delete asyncData;
}

} // anonymous namespace

/**
 * 对称加密函数
 * @param env NAPI环境
 * @param info 回调信息
 * @return Promise<ArrayBuffer> 加密后的数据
 */
napi_value EncryptSymmetric(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
    
    if (argc != 2) {
        napi_throw_error(env, nullptr, "Expected 2 arguments: key and plaintext");
        return nullptr;
    }
    
    // 创建Promise
    napi_value promise;
    napi_deferred deferred;
    napi_create_promise(env, &deferred, &promise);
    
    // 准备异步工作数据
    auto* asyncData = new EncryptAsyncData();
    asyncData->deferred = deferred;
    asyncData->success = false;
    
    // 转换参数
    if (!TypeConverter::ArrayBufferToGoSlice(env, args[0], &asyncData->key) ||
        !TypeConverter::ArrayBufferToGoSlice(env, args[1], &asyncData->plaintext)) {
        napi_value error;
        napi_create_string_utf8(env, "Invalid arguments: expected ArrayBuffer", NAPI_AUTO_LENGTH, &error);
        napi_reject_deferred(env, deferred, error);
        delete asyncData;
        return promise;
    }
    
    // 创建异步工作
    napi_value workName;
    napi_create_string_utf8(env, "EncryptSymmetric", NAPI_AUTO_LENGTH, &workName);
    
    napi_create_async_work(env, nullptr, workName, ExecuteEncrypt, CompleteEncrypt, asyncData, &asyncData->work);
    napi_queue_async_work(env, asyncData->work);
    
    return promise;
}

/**
 * 对称解密函数
 * @param env NAPI环境
 * @param info 回调信息
 * @return Promise<ArrayBuffer> 解密后的数据
 */
napi_value DecryptSymmetric(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
    
    if (argc != 2) {
        napi_throw_error(env, nullptr, "Expected 2 arguments: key and ciphertext");
        return nullptr;
    }
    
    // 创建Promise
    napi_value promise;
    napi_deferred deferred;
    napi_create_promise(env, &deferred, &promise);
    
    // 准备异步工作数据
    auto* asyncData = new DecryptAsyncData();
    asyncData->deferred = deferred;
    asyncData->success = false;
    
    // 转换参数
    if (!TypeConverter::ArrayBufferToGoSlice(env, args[0], &asyncData->key) ||
        !TypeConverter::ArrayBufferToGoSlice(env, args[1], &asyncData->ciphertext)) {
        napi_value error;
        napi_create_string_utf8(env, "Invalid arguments: expected ArrayBuffer", NAPI_AUTO_LENGTH, &error);
        napi_reject_deferred(env, deferred, error);
        delete asyncData;
        return promise;
    }
    
    // 创建异步工作
    napi_value workName;
    napi_create_string_utf8(env, "DecryptSymmetric", NAPI_AUTO_LENGTH, &workName);
    
    napi_create_async_work(env, nullptr, workName, ExecuteDecrypt, CompleteDecrypt, asyncData, &asyncData->work);
    napi_queue_async_work(env, asyncData->work);
    
    return promise;
}

/**
 * 派生密钥函数（同步）
 * @param env NAPI环境
 * @param info 回调信息
 * @return ArrayBuffer 派生的密钥
 */
napi_value DeriveKey(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
    
    if (argc != 2) {
        napi_throw_error(env, nullptr, "Expected 2 arguments: password and salt");
        return nullptr;
    }
    
    // 转换参数
    GoString password;
    GoSlice salt;
    
    if (!TypeConverter::StringToGoString(env, args[0], &password) ||
        !TypeConverter::ArrayBufferToGoSlice(env, args[1], &salt)) {
        napi_throw_error(env, nullptr, "Invalid arguments");
        return nullptr;
    }
    
    try {
        // 调用Go函数
        GoSlice result = GoDeriveKey(password, salt);
        
        // 转换结果
        napi_value napiResult;
        TypeConverter::GoSliceToArrayBuffer(env, result, &napiResult);
        
        // 释放Go分配的内存
        GoFreeSlice(result);
        
        return napiResult;
    } catch (const std::exception& e) {
        napi_throw_error(env, nullptr, e.what());
        return nullptr;
    }
}

/**
 * 注册加密相关的NAPI函数
 */
napi_value RegisterCryptoFunctions(napi_env env, napi_value exports) {
    napi_property_descriptor desc[] = {
        {"encryptSymmetric", nullptr, EncryptSymmetric, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"decryptSymmetric", nullptr, DecryptSymmetric, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"deriveKey", nullptr, DeriveKey, nullptr, nullptr, nullptr, napi_default, nullptr}
    };
    
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    
    OH_LOG_INFO(LOG_DOMAIN, LOG_TAG, "Crypto functions registered successfully");
    return exports;
} 