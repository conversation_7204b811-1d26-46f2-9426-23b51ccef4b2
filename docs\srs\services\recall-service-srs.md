好的，遵照您的指示。我将为您生成一份专门针对 **`recall-service` (召回服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义这个新增的、作为推荐系统“信息源”的服务的功能、接口、数据依赖、性能和可靠性需求，作为推荐系统开发的核心依据。

---
### CINA.CLUB - `recall-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [推荐算法/平台工程负责人]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
在CINA.CLUB的个性化推荐系统中，首要步骤是从海量的物料库（视频、服务、文章等）中，快速、高效地筛选出一个与用户可能相关的候选集。这个过程被称为**召回(Recall)**。`recall-service` 的目的在于构建一个**高性能、多策略、可扩展的统一召回中枢**。它封装了对多种底层数据源（如倒排索引、向量数据库、实时缓存）的查询逻辑，为上游的`recommendation-service`提供丰富的、多样化的候选物料集。

#### 1.2. 服务范围
本服务 **负责**:
*   **统一召回接口**: 提供一个统一的gRPC API，接收召回请求，包含用户信息、场景信息和要执行的**召回策略列表**。
*   **多策略召回实现**:
    *   **内容匹配**: 基于倒排索引（Elasticsearch）的关键词召回。
    *   **协同过滤**: 基于离线计算好的“物料-物料”(I2I)或“用户-用户”(U2U)相似度矩阵进行召回。
    *   **向量召回**: 基于用户画像向量或种子物料向量，在向量数据库中进行k-NN（K-Nearest Neighbors）相似性搜索。
    *   **热门召回**: 从实时缓存（Redis）中获取全局或分类下的热门物料。
    *   **社交召回**: 从`social-service`获取用户关注关系，召回关注对象的最新动态。
*   **并行执行与结果聚合**: 能够**并行地**执行多个召回策略，并将各路召回的结果进行**去重和合并**。
*   **缓存与性能优化**: 对可复用的召回结果（如热门榜单、I2I相似列表）进行缓存。

本服务 **不负责**:
*   **排序(Ranking)**: 本服务只负责“找出来”，不负责“哪个更好”。排序由`ranking-service`负责。
*   **特征工程与模型训练**: 离线的相似度计算和向量生成由数据平台负责。
*   **用户画像的生成与存储**: 由`analytics-service`和特征存储服务负责。
*   **提供面向最终用户的API**。

#### 1.3. 目标用户/调用方
*   **`recommendation-service` (唯一)**: 是本服务的唯一调用方。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`recall-service` 是推荐系统漏斗的**第一层和基础**。它扮演着“**信息渔夫**”的角色，使用多张不同的“渔网”（召回策略），从浩瀚的“信息海洋”（全量物料库）中，为每个用户捕捞出最可能的一批“鱼”（候选集）。它的**召回率(Recall Rate)**和**性能**直接决定了整个推荐系统的天花板和最终效果。

#### 2.2. 主要功能概述
*   统一的、支持多策略并行执行的召回API。
*   封装了对多种异构数据源（ES, VectorDB, Redis, gRPC）的查询逻辑。
*   高性能的结果聚合与去重。
*   对高频、非个性化结果的缓存。

---

### 3. 核心流程图

#### 3.1. 处理一次多路召回请求

```mermaid
sequenceDiagram
    participant RecoService as recommendation-service
    participant RecallService
    participant FeatureStore
    participant SocialService as social-service
    participant Elasticsearch
    participant VectorDB
    participant Redis

    RecoService->>RecallService: 1. RequestCandidates(userId, strategies: ["social", "vector", "hot"])
    
    RecallService->>FeatureStore: 2. 获取用户画像向量
    
    par [并行执行召回策略]
        RecallService->>SocialService: 3a. **[社交召回]** GetFollowingFeed(userId)
        SocialService-->>RecallService: (IDs: [v1, v2])
    and
        RecallService->>VectorDB: 3b. **[向量召回]** KNN_Search(userVector)
        VectorDB-->>RecallService: (IDs: [v3, v4])
    and
        RecallService->>Redis: 3c. **[热门召回]** ZREVRANGE('hot_videos', 0, 100)
        Redis-->>RecallService: (IDs: [v5, v6])
    end
    
    RecallService->>RecallService: 4. **聚合与去重**: <br/>- 合并所有ID: [v1, v2, v3, v4, v5, v6]<br/>- 去除重复ID
    
    RecallService-->>RecoService: 5. 返回最终的候选ID列表
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 召回API
*   **FR4.1.1 (统一接口)**: 必须提供一个`RequestCandidates` gRPC接口。
*   **FR4.1.2 (动态策略选择)**: 请求中**必须**包含一个`strategies`数组，明确指定本次请求需要执行哪些召回策略。这使得上游的`recommendation-service`可以根据不同场景，灵活地组合召回通路。
*   **FR4.1.3 (并行执行)**: 接收到请求后，**必须**使用goroutine和`errgroup`，并行地执行所有指定的召回策略。
*   **FR4.1.4 (结果聚合)**: 所有并行的召回策略执行完毕后，**必须**将其返回的物料ID列表进行合并和去重，然后返回一个最终的候选集。
*   **FR4.1.5 (超时控制)**: 整个召回过程必须有一个严格的超时控制（如P99 < 50ms）。任何一个策略的超时不应阻塞其他策略的完成。

#### 4.2. 召回策略实现
系统必须内置多种可插拔的召回策略。
*   **FR4.2.1 (热门召回)**:
    *   **实现**: 从Redis的Sorted Set中，按分数（热度分）倒序获取Top N的物料ID。
    *   **数据源**: Redis。
*   **FR4.2.2 (协同过滤I2I召回)**:
    *   **实现**: 根据请求中提供的“种子”物料ID列表，从一个KV存储（如Redis Hash或独立的KV数据库）中，查找这些物料最相似的Top K个其他物料。
    *   **数据源**: 离线计算好的`item_similarity`表，通常存储在Redis或类似的高性能KV存储中。`Key: itemID`, `Value: a list of {similarItemID, score}`。
*   **FR4.2.3 (向量召回)**:
    *   **实现**:
        a. 如果请求中提供了用户ID，则从特征存储获取该用户的画像向量。
        b. 如果请求中提供了种子物料ID，则获取该物料的内容向量。
        c. 使用该向量，向向量数据库发起k-NN搜索。
    *   **数据源**: 向量数据库 (Pinecone, Milvus, Weaviate)。
*   **FR4.2.4 (社交召回)**:
    *   **实现**: 调用`social-service`的`GetFollowingList`接口，获取用户关注的人，然后调用其他服务的接口（如`short-video-service.ListVideosByUsers`）获取这些人最近发布的内容。
    *   **数据源**: `social-service`等业务服务的gRPC API。
*   **FR4.2.5 (关键词召回)**:
    *   **实现**: 将用户查询（如果提供）或用户兴趣标签，转换为对Elasticsearch的`match`或`bool`查询。
    *   **数据源**: Elasticsearch / OpenSearch。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 内部gRPC API接口 (供`recommendation-service`调用)
*   **Package**: `hina.v1.recall`
*   **核心RPC**:
    ```protobuf
    service RecallService {
      rpc RequestCandidates(RequestCandidatesRequest) returns (RequestCandidatesResponse);
    }

    message RecallStrategy {
      string name = 1; // "hot", "i2i", "vector_semantic", etc.
      map<string, string> params = 2; // 策略所需参数, e.g., {"seed_item_id": "xxx"}
    }
    
    message RequestCandidatesRequest {
      string user_id = 1;
      string scene = 2;
      repeated RecallStrategy strategies = 3;
      int32 size = 4; // 期望返回的总候选集大小
    }

    message RecalledItem {
      string id = 1;
      double score = 2;       // 原始召回分数
      string source = 3;      // 来源策略名, e.g., "i2i"
    }

    message RequestCandidatesResponse {
      repeated RecalledItem items = 1;
    }
    ```

---

### 6. 数据需求 (Data Requirements)

*   **无核心持久化数据库**: 本服务是**无状态**的计算代理服务。
*   **依赖的数据源**:
    *   **Redis**: 存储热门榜单、I2I相似度矩阵、U2U相似度矩阵。
    *   **Elasticsearch/OpenSearch**: 用于关键词召回。
    *   **Vector Database**: 用于向量召回。
    *   **其他微服务的gRPC API**: 用于社交召回等。
*   **缓存 (Redis)**: 对非个性化的召回结果（如热门榜单）进行短时间的内存或Redis缓存，以应对流量洪峰。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与延迟 - 最高优先级)**:
    *   **API端到端延迟**: P95**必须 `< 50ms**。P99 < 80ms。这是整个推荐系统性能的**第一道关卡**。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.95%。
    *   **容错**:
        *   对所有下游数据源（Redis, ES, VectorDB, gRPC）的调用**必须**有极短的超时设置和熔断器。
        *   **单路故障不影响整体**: 任何一个召回策略的失败或超时，**绝不能**导致整个`RequestCandidates`请求失败。应返回其他通路成功召回的结果。
*   **NFR7.3 (可扩展性)**: 服务应为无状态，易于水平扩展。

---

### 8. 技术约束与选型建议
*   **语言**: Go。其强大的并发模型非常适合并行执行多路召回，并能满足低延迟要求。
*   **并发模型**: **必须**使用`context.WithTimeout`来控制每个召回策略的执行时间，并使用`errgroup`来管理并行执行和错误聚合。
*   **可插拔策略**:
    *   **核心架构**: 采用**策略模式(Strategy Pattern)**。每个召回策略都实现一个统一的`RecallStrategy`接口。
    *   **`application/service`**: 服务层根据请求中的`strategies`列表，从一个策略工厂中获取对应的实例，并并行执行它们。这使得添加新的召回通路非常简单。

---
这份SRS为`recall-service`的设计和实现提供了坚实、全面的指导。通过将其设计为一个**高性能、高可用、多策略并行的统一召回层**，它为上游的`recommendation-service`提供了丰富而多样的“弹药”，是整个个性化推荐系统取得良好效果的基础和前提。