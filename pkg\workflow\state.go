/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package workflow

import (
	"bytes"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"text/template"
	"time"
)

// ExecutionState represents the current state of workflow execution.
// It contains all the data produced by executed nodes and can be used for resuming workflows.
type ExecutionState struct {
	// WorkflowID is the ID of the workflow being executed
	WorkflowID string `json:"workflowId,omitempty"`

	// ExecutionID is a unique identifier for this execution instance
	ExecutionID string `json:"executionId,omitempty"`

	// Status represents the overall execution status
	Status ExecutionStatus `json:"status"`

	// StartTime is when the execution started (RFC3339 format)
	StartTime string `json:"startTime,omitempty"`

	// EndTime is when the execution completed (RFC3339 format)
	EndTime string `json:"endTime,omitempty"`

	// CurrentNode is the ID of the node currently being executed
	CurrentNode string `json:"currentNode,omitempty"`

	// NodeResults contains the execution results for each completed node
	NodeResults map[string]*NodeResult `json:"nodeResults"`

	// Variables are global variables available throughout the workflow
	Variables map[string]interface{} `json:"variables,omitempty"`

	// Context provides additional execution context
	Context *ExecutionContext `json:"context,omitempty"`

	// Error contains error information if the workflow failed
	Error string `json:"error,omitempty"`

	// Metadata can store additional execution-specific information
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// ExecutionStatus represents the overall status of workflow execution.
type ExecutionStatus string

const (
	// ExecutionStatusPending indicates the workflow is waiting to start
	ExecutionStatusPending ExecutionStatus = "pending"

	// ExecutionStatusRunning indicates the workflow is currently executing
	ExecutionStatusRunning ExecutionStatus = "running"

	// ExecutionStatusCompleted indicates the workflow completed successfully
	ExecutionStatusCompleted ExecutionStatus = "completed"

	// ExecutionStatusFailed indicates the workflow execution failed
	ExecutionStatusFailed ExecutionStatus = "failed"

	// ExecutionStatusPaused indicates the workflow is paused
	ExecutionStatusPaused ExecutionStatus = "paused"

	// ExecutionStatusCancelled indicates the workflow was cancelled
	ExecutionStatusCancelled ExecutionStatus = "cancelled"
)

// NewExecutionState creates a new execution state with default values.
func NewExecutionState() *ExecutionState {
	return &ExecutionState{
		Status:      ExecutionStatusPending,
		StartTime:   time.Now().Format(time.RFC3339),
		NodeResults: make(map[string]*NodeResult),
		Variables:   make(map[string]interface{}),
		Metadata:    make(map[string]interface{}),
	}
}

// NewExecutionStateWithID creates a new execution state with specified IDs.
func NewExecutionStateWithID(workflowID, executionID string) *ExecutionState {
	state := NewExecutionState()
	state.WorkflowID = workflowID
	state.ExecutionID = executionID
	return state
}

// SetNodeResult stores the execution result for a node.
func (s *ExecutionState) SetNodeResult(nodeID string, result *NodeResult) {
	if s.NodeResults == nil {
		s.NodeResults = make(map[string]*NodeResult)
	}
	s.NodeResults[nodeID] = result
}

// GetNodeResult retrieves the execution result for a node.
func (s *ExecutionState) GetNodeResult(nodeID string) *NodeResult {
	if s.NodeResults == nil {
		return nil
	}
	return s.NodeResults[nodeID]
}

// GetNodeOutputs retrieves the outputs for a specific node.
func (s *ExecutionState) GetNodeOutputs(nodeID string) map[string]interface{} {
	result := s.GetNodeResult(nodeID)
	if result == nil {
		return nil
	}
	return result.Outputs
}

// GetNodeOutput retrieves a specific output value from a node.
func (s *ExecutionState) GetNodeOutput(nodeID, outputKey string) interface{} {
	outputs := s.GetNodeOutputs(nodeID)
	if outputs == nil {
		return nil
	}
	return outputs[outputKey]
}

// IsNodeCompleted checks if a node has completed execution.
func (s *ExecutionState) IsNodeCompleted(nodeID string) bool {
	result := s.GetNodeResult(nodeID)
	return result != nil && result.Status == NodeStatusCompleted
}

// IsNodeFailed checks if a node has failed execution.
func (s *ExecutionState) IsNodeFailed(nodeID string) bool {
	result := s.GetNodeResult(nodeID)
	return result != nil && result.Status == NodeStatusFailed
}

// GetCompletedNodes returns the IDs of all completed nodes.
func (s *ExecutionState) GetCompletedNodes() []string {
	var completed []string
	for nodeID, result := range s.NodeResults {
		if result.Status == NodeStatusCompleted {
			completed = append(completed, nodeID)
		}
	}
	return completed
}

// GetFailedNodes returns the IDs of all failed nodes.
func (s *ExecutionState) GetFailedNodes() []string {
	var failed []string
	for nodeID, result := range s.NodeResults {
		if result.Status == NodeStatusFailed {
			failed = append(failed, nodeID)
		}
	}
	return failed
}

// SetVariable sets a global variable.
func (s *ExecutionState) SetVariable(key string, value interface{}) {
	if s.Variables == nil {
		s.Variables = make(map[string]interface{})
	}
	s.Variables[key] = value
}

// GetVariable retrieves a global variable.
func (s *ExecutionState) GetVariable(key string) interface{} {
	if s.Variables == nil {
		return nil
	}
	return s.Variables[key]
}

// Clone creates a deep copy of the execution state.
func (s *ExecutionState) Clone() (*ExecutionState, error) {
	data, err := json.Marshal(s)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal execution state: %w", err)
	}

	var cloned ExecutionState
	if err := json.Unmarshal(data, &cloned); err != nil {
		return nil, fmt.Errorf("failed to unmarshal execution state: %w", err)
	}

	return &cloned, nil
}

// ToJSON serializes the execution state to JSON.
func (s *ExecutionState) ToJSON() ([]byte, error) {
	return json.MarshalIndent(s, "", "  ")
}

// FromJSON deserializes an execution state from JSON.
func ExecutionStateFromJSON(data []byte) (*ExecutionState, error) {
	var state ExecutionState
	if err := json.Unmarshal(data, &state); err != nil {
		return nil, fmt.Errorf("failed to parse execution state JSON: %w", err)
	}
	return &state, nil
}

// ExpressionInterpolator handles the interpolation of template expressions in node inputs.
// It uses Go's text/template package to resolve expressions like "{{ .nodes.node1.outputs.result }}".
type ExpressionInterpolator struct {
	// EnableDebug enables debug logging for expression evaluation
	EnableDebug bool

	// CustomFunctions provides additional template functions
	CustomFunctions template.FuncMap
}

// NewExpressionInterpolator creates a new expression interpolator.
func NewExpressionInterpolator() *ExpressionInterpolator {
	return &ExpressionInterpolator{
		CustomFunctions: make(template.FuncMap),
	}
}

// InterpolateInputs resolves all template expressions in the input map.
func (ei *ExpressionInterpolator) InterpolateInputs(inputs map[string]interface{}, state *ExecutionState) (map[string]interface{}, error) {
	if inputs == nil {
		return nil, nil
	}

	result := make(map[string]interface{})

	for key, value := range inputs {
		interpolated, err := ei.InterpolateValue(value, state)
		if err != nil {
			return nil, fmt.Errorf("failed to interpolate input '%s': %w", key, err)
		}
		result[key] = interpolated
	}

	return result, nil
}

// InterpolateValue resolves template expressions in a single value.
func (ei *ExpressionInterpolator) InterpolateValue(value interface{}, state *ExecutionState) (interface{}, error) {
	switch v := value.(type) {
	case string:
		return ei.interpolateString(v, state)
	case map[string]interface{}:
		return ei.interpolateMap(v, state)
	case []interface{}:
		return ei.interpolateSlice(v, state)
	default:
		// Non-string values are returned as-is
		return value, nil
	}
}

// interpolateString processes a string value and resolves any template expressions.
func (ei *ExpressionInterpolator) interpolateString(value string, state *ExecutionState) (interface{}, error) {
	// Check if the string contains template expressions
	if !strings.Contains(value, "{{") || !strings.Contains(value, "}}") {
		return value, nil
	}

	// Create template data from execution state
	templateData := ei.createTemplateData(state)

	// Create and parse template
	tmpl, err := template.New("interpolate").Funcs(ei.getTemplateFunctions()).Parse(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse template '%s': %w", value, err)
	}

	// Execute template
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, templateData); err != nil {
		return nil, fmt.Errorf("failed to execute template '%s': %w", value, err)
	}

	result := buf.String()

	// If the string is a single template expression (no other text),
	// try to parse the result to preserve the original type
	trimmedValue := strings.TrimSpace(value)
	if strings.HasPrefix(trimmedValue, "{{") && strings.HasSuffix(trimmedValue, "}}") &&
		strings.Count(trimmedValue, "{{") == 1 && strings.Count(trimmedValue, "}}") == 1 {
		return ei.parseResult(result)
	}

	return result, nil
}

// interpolateMap processes a map and resolves template expressions in its values.
func (ei *ExpressionInterpolator) interpolateMap(value map[string]interface{}, state *ExecutionState) (interface{}, error) {
	result := make(map[string]interface{})

	for k, v := range value {
		interpolated, err := ei.InterpolateValue(v, state)
		if err != nil {
			return nil, err
		}
		result[k] = interpolated
	}

	return result, nil
}

// interpolateSlice processes a slice and resolves template expressions in its elements.
func (ei *ExpressionInterpolator) interpolateSlice(value []interface{}, state *ExecutionState) (interface{}, error) {
	result := make([]interface{}, len(value))

	for i, v := range value {
		interpolated, err := ei.InterpolateValue(v, state)
		if err != nil {
			return nil, err
		}
		result[i] = interpolated
	}

	return result, nil
}

// createTemplateData creates the data structure available in templates.
func (ei *ExpressionInterpolator) createTemplateData(state *ExecutionState) map[string]interface{} {
	data := map[string]interface{}{
		"nodes":     ei.createNodesData(state),
		"variables": state.Variables,
		"execution": map[string]interface{}{
			"id":        state.ExecutionID,
			"workflow":  state.WorkflowID,
			"startTime": state.StartTime,
			"status":    state.Status,
		},
	}

	if state.Context != nil {
		data["context"] = state.Context
	}

	return data
}

// createNodesData creates the nodes data structure for templates.
func (ei *ExpressionInterpolator) createNodesData(state *ExecutionState) map[string]interface{} {
	nodes := make(map[string]interface{})

	for nodeID, result := range state.NodeResults {
		nodes[nodeID] = map[string]interface{}{
			"outputs":  result.Outputs,
			"status":   result.Status,
			"error":    result.Error,
			"duration": result.Duration,
			"attempt":  result.Attempt,
		}
	}

	return nodes
}

// getTemplateFunctions returns the functions available in templates.
func (ei *ExpressionInterpolator) getTemplateFunctions() template.FuncMap {
	funcs := template.FuncMap{
		"default": func(defaultValue interface{}, value interface{}) interface{} {
			if value == nil || (reflect.ValueOf(value).Kind() == reflect.String && value.(string) == "") {
				return defaultValue
			}
			return value
		},
		"lower": strings.ToLower,
		"upper": strings.ToUpper,
		"trim":  strings.TrimSpace,
		"join": func(sep string, items []interface{}) string {
			strItems := make([]string, len(items))
			for i, item := range items {
				strItems[i] = fmt.Sprintf("%v", item)
			}
			return strings.Join(strItems, sep)
		},
		"split": func(sep, s string) []string {
			return strings.Split(s, sep)
		},
		"contains":  strings.Contains,
		"hasPrefix": strings.HasPrefix,
		"hasSuffix": strings.HasSuffix,
	}

	// Add custom functions
	for name, fn := range ei.CustomFunctions {
		funcs[name] = fn
	}

	return funcs
}

// parseResult attempts to parse a template result as JSON if it looks like a non-string value.
func (ei *ExpressionInterpolator) parseResult(result string) (interface{}, error) {
	trimmed := strings.TrimSpace(result)

	// Try to parse as JSON for all types (including numbers)
	var parsed interface{}
	if err := json.Unmarshal([]byte(trimmed), &parsed); err == nil {
		// If JSON parsing resulted in a float64 that represents a whole number,
		// convert it to int to match the original type more closely
		if f, ok := parsed.(float64); ok {
			if f == float64(int(f)) {
				return int(f), nil
			}
		}
		return parsed, nil
	}

	// Return as string if JSON parsing fails
	return result, nil
}
