/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 12:00:00
Modified: 2025-06-27 12:00:00
*/

package handlers

import (
	"context"

	"cina.club/services/activity-feed-service/internal/domain"
)

// FollowingHandler handles events from users that are being followed
type Following<PERSON><PERSON>ler struct {
	feedRepo        domain.FeedRepository
	unreadCountRepo domain.UnreadCountRepository
	aggregator      *domain.Aggregator
	logger          domain.Logger
}

// NewFollowingHandler creates a new following handler
func NewFollowingHandler(
	feedRepo domain.FeedRepository,
	unreadCountRepo domain.UnreadCountRepository,
	aggregator *domain.Aggregator,
	logger domain.Logger,
) *FollowingHandler {
	return &FollowingHandler{
		feedRepo:        feedRepo,
		unreadCountRepo: unreadCountRepo,
		aggregator:      aggregator,
		logger:          logger,
	}
}

// Handle processes events from followed users (content published, status updates, etc.)
func (h *FollowingHandler) Handle(ctx context.Context, event domain.Event) error {
	eventType := event.GetEventType()

	h.logger.Debug(ctx, "Processing following event",
		"event_type", eventType,
		"event_id", event.GetEventID(),
		"user_id", event.GetUserID())

	return nil
}
