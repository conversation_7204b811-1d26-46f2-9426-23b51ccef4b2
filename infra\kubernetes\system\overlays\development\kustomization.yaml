# CINA.CLUB Platform - Development Environment Overlay
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: cina-club-system-development
  annotations:
    description: "Development environment configuration for CINA.CLUB platform"

# Base configuration
resources:
  - ../../base

# Environment-specific labels
commonLabels:
  environment: development
  criticality: low
  tier: development

# Environment-specific annotations
commonAnnotations:
  deployment.environment: development
  monitoring.enabled: "true"
  backup.enabled: "false"
  debug.enabled: "true"

# Development-specific patches
patchesStrategicMerge:
  - patches/development-resources.yaml
  - patches/development-debug.yaml

# Development replicas (minimal for resource saving)
replicas:
  - name: kong-proxy
    count: 1
  - name: kong-ingress-controller
    count: 1
  - name: prometheus
    count: 1

# Development images (latest or development tags)
images:
  - name: kong/kubernetes-ingress-controller
    newTag: "2.12.0"
  - name: kong
    newTag: "3.4.2-alpine"
  - name: prom/prometheus
    newTag: "v2.47.2"
  - name: grafana/grafana
    newTag: "10.2.0"

# Development secrets
secretGenerator:
  - name: development-secrets
    literals:
      - environment=development
      - elasticsearch.password=dev-password
      - grafana.admin.password=admin123
      - alert.webhook.url=https://hooks.slack.com/services/DEV/WEBHOOK/URL
    type: Opaque

# Development config maps
configMapGenerator:
  - name: development-config
    literals:
      - environment=development
      - log.level=debug
      - metrics.retention=7d
      - domain=dev-api.cina.club
      - tls.enabled=false
      - debug.enabled=true

# Namespace prefix for development
namePrefix: dev- 