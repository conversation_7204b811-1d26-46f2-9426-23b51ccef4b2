/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

/**
 * 个人资料Tab内容组件
 */
@Component
export struct ProfileTabContent {
  @State private userName: string = '用户';
  @State private userLevel: string = '免费用户';

  build() {
    Column() {
      // 用户信息卡片
      Column() {
        // 头像
        Row() {
          Text(this.userName.charAt(0))
            .fontSize(32)
            .fontColor('#FFFFFF')
        }
        .width(80)
        .height(80)
        .backgroundColor('#007AFF')
        .borderRadius(40)
        .justifyContent(FlexAlign.Center)
        .margin({ bottom: 16 })
        
        Text(this.userName)
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 8 })
        
        Text(this.userLevel)
          .fontSize(14)
          .fontColor('#666666')
          .backgroundColor('#F0F0F0')
          .padding({ left: 12, right: 12, top: 4, bottom: 4 })
          .borderRadius(12)
      }
      .width('100%')
      .padding(20)
      .backgroundColor('#F8F9FA')
      .margin({ bottom: 20 })
      
      // 功能菜单
      Column() {
        this.MenuItem('⚙️', '设置', '个人偏好和应用设置')
        this.MenuItem('🔒', '隐私', '加密设置和数据安全')
        this.MenuItem('💾', '存储', '本地数据和同步状态')
        this.MenuItem('📊', '统计', '使用情况和活动统计')
        this.MenuItem('❓', '帮助', '使用指南和常见问题')
        this.MenuItem('📞', '反馈', '意见建议和问题反馈')
      }
      .width('100%')
      
      Blank()
      
      // 底部信息
      Column() {
        Text('CINA.CLUB v1.0.0')
          .fontSize(12)
          .fontColor('#999999')
          .margin({ bottom: 4 })
        
        Text('Copyright © 2025 Cina.Club')
          .fontSize(12)
          .fontColor('#999999')
      }
      .width('100%')
      .padding(16)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
  }

  @Builder
  MenuItem(icon: string, title: string, description: string) {
    Row() {
      Text(icon)
        .fontSize(24)
        .margin({ right: 16 })
      
      Column() {
        Text(title)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .margin({ bottom: 4 })
        
        Text(description)
          .fontSize(14)
          .fontColor('#666666')
      }
      .alignItems(HorizontalAlign.Start)
      
      Blank()
      
      Text('>')
        .fontSize(16)
        .fontColor('#999999')
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 16, bottom: 16 })
    .onClick(() => {
      // 处理点击事件
    })
  }
} 