# CINA.CLUB Platform - CORS Plugin (Platform Standard)
# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 12:00:00
# Modified: 2025-01-27 12:00:00

# Global CORS Plugin - Platform Standard
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cors-global
  namespace: kong-system
  labels:
    app: kong-plugin
    component: cors
    tier: platform-standard
    category: security
    scope: global
  annotations:
    description: "Global CORS policy for CINA.CLUB platform web applications"
    usage: "Applied globally to all routes, can be overridden per service"
    owner: "<EMAIL>"
    konghq.com/global: "true"               # Apply globally to all services

plugin: cors
config:
  # Allowed origins for CORS requests
  origins:
    # Production domains
    - "https://cina.club"
    - "https://www.cina.club"
    - "https://app.cina.club"
    - "https://admin.cina.club"
    - "https://api.cina.club"
    
    # Development and staging domains
    - "https://dev.cina.club"
    - "https://staging.cina.club"
    - "https://test.cina.club"
    
    # Mobile app origins (for hybrid apps)
    - "capacitor://localhost"
    - "ionic://localhost"
    - "file://"
    
    # Development origins (localhost)
    - "http://localhost:3000"               # Next.js dev server
    - "http://localhost:3001"               # Admin panel dev
    - "http://localhost:4200"               # Angular dev
    - "http://localhost:5173"               # Vite dev server
    - "http://localhost:8080"               # Vue dev server
    - "http://localhost:8100"               # Ionic dev server
    - "https://localhost:3000"              # HTTPS localhost
    - "https://localhost:3001"
    - "https://localhost:4200"
    - "https://localhost:5173"
    - "https://localhost:8080"
    - "https://localhost:8100"
  
  # Allowed HTTP methods
  methods:
    - "GET"
    - "POST"
    - "PUT"
    - "PATCH"
    - "DELETE"
    - "OPTIONS"
    - "HEAD"
  
  # Allowed headers (both standard and custom)
  headers:
    # Standard headers
    - "Accept"
    - "Accept-Language"
    - "Content-Language"
    - "Content-Type"
    - "Authorization"
    - "User-Agent"
    - "Referer"
    - "Origin"
    
    # Custom headers for CINA.CLUB platform
    - "X-Requested-With"
    - "X-API-Key"
    - "X-Auth-Token"
    - "X-Client-Version"
    - "X-Platform"
    - "X-Device-ID"
    - "X-Session-ID"
    - "X-Request-ID"
    - "X-Correlation-ID"
    - "X-Trace-ID"
    
    # File upload headers
    - "X-File-Name"
    - "X-File-Size"
    - "X-File-Type"
    
    # Progressive Web App headers
    - "X-PWA-Mode"
    - "X-Service-Worker"
  
  # Exposed headers (headers that the frontend can access)
  exposed_headers:
    - "X-RateLimit-Limit"
    - "X-RateLimit-Remaining"
    - "X-RateLimit-Reset"
    - "X-Request-ID"
    - "X-Response-Time"
    - "X-API-Version"
    - "X-Server-Time"
    - "Content-Length"
    - "Content-Range"
    - "ETag"
    - "Last-Modified"
  
  # Credentials support
  credentials: true                         # Allow cookies and auth headers
  
  # Preflight request caching
  max_age: 86400                           # Cache preflight for 24 hours
  
  # Handle preflight requests
  preflight_continue: false                # Don't continue to upstream for preflight

---
# CORS Plugin - Restrictive (for admin/sensitive endpoints)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cors-restrictive
  namespace: kong-system
  labels:
    app: kong-plugin
    component: cors
    tier: admin
    category: security
    security-level: high
  annotations:
    description: "Restrictive CORS policy for admin and sensitive endpoints"
    use-case: "Apply to admin routes and sensitive API endpoints"

plugin: cors
config:
  # Only allow production admin origins
  origins:
    - "https://admin.cina.club"             # Production admin panel
    - "https://admin.staging.cina.club"     # Staging admin panel
    - "http://localhost:3001"               # Local admin development
    - "https://localhost:3001"              # HTTPS local admin
  
  # Limited methods for admin
  methods:
    - "GET"
    - "POST"
    - "PUT"
    - "PATCH"
    - "DELETE"
    - "OPTIONS"
  
  # Restricted headers for admin
  headers:
    - "Accept"
    - "Content-Type"
    - "Authorization"
    - "X-API-Key"
    - "X-Admin-Token"
    - "X-Request-ID"
    - "X-CSRF-Token"
  
  # Minimal exposed headers
  exposed_headers:
    - "X-Request-ID"
    - "X-API-Version"
    - "X-RateLimit-Limit"
    - "X-RateLimit-Remaining"
  
  # Require credentials for admin
  credentials: true
  
  # Shorter preflight cache for admin
  max_age: 3600                            # Cache for 1 hour only
  preflight_continue: false

---
# CORS Plugin - Public API (for public/third-party access)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cors-public-api
  namespace: kong-system
  labels:
    app: kong-plugin
    component: cors
    tier: public
    category: integration
  annotations:
    description: "Open CORS policy for public API endpoints"
    use-case: "Apply to public API routes that need third-party access"

plugin: cors
config:
  # Allow any origin for public APIs
  origins:
    - "*"                                  # Allow any origin for public APIs
  
  # Limited methods for public APIs
  methods:
    - "GET"
    - "POST"
    - "OPTIONS"
    - "HEAD"
  
  # Basic headers for public APIs
  headers:
    - "Accept"
    - "Content-Type"
    - "Authorization"
    - "X-API-Key"
    - "X-Requested-With"
    - "User-Agent"
    - "Origin"
  
  # Public API exposed headers
  exposed_headers:
    - "X-RateLimit-Limit"
    - "X-RateLimit-Remaining"
    - "X-RateLimit-Reset"
    - "X-API-Version"
    - "Content-Length"
  
  # No credentials for public APIs (security)
  credentials: false                       # Don't allow credentials for public APIs
  
  # Standard preflight cache
  max_age: 7200                            # Cache for 2 hours
  preflight_continue: false

---
# CORS Plugin - Mobile Apps (for mobile app origins)
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cors-mobile
  namespace: kong-system
  labels:
    app: kong-plugin
    component: cors
    tier: mobile
    category: integration
  annotations:
    description: "CORS policy optimized for mobile applications"
    use-case: "Apply to routes accessed by mobile apps"

plugin: cors
config:
  # Mobile-specific origins
  origins:
    # Hybrid mobile app origins
    - "capacitor://localhost"
    - "ionic://localhost"
    - "file://"
    - "https://localhost"
    
    # React Native and Expo
    - "exp://localhost:19000"
    - "exp://*************:19000"          # Common local network
    - "exp://**********:19000"             # Common local network
    
    # Progressive Web App
    - "https://app.cina.club"
    - "https://pwa.cina.club"
    
    # Development
    - "http://localhost:8100"               # Ionic dev
    - "https://localhost:8100"
    - "http://localhost:19006"              # Expo web
    - "https://localhost:19006"
  
  # Mobile-optimized methods
  methods:
    - "GET"
    - "POST"
    - "PUT"
    - "PATCH"
    - "DELETE"
    - "OPTIONS"
    - "HEAD"
  
  # Mobile-specific headers
  headers:
    - "Accept"
    - "Content-Type"
    - "Authorization"
    - "X-API-Key"
    - "X-Device-ID"
    - "X-Platform"
    - "X-App-Version"
    - "X-Client-Version"
    - "X-Session-ID"
    - "X-Push-Token"
    - "User-Agent"
  
  # Headers useful for mobile apps
  exposed_headers:
    - "X-RateLimit-Limit"
    - "X-RateLimit-Remaining"
    - "X-API-Version"
    - "X-Server-Time"
    - "X-App-Update-Available"
    - "X-Maintenance-Mode"
    - "Content-Length"
  
  # Allow credentials for mobile apps
  credentials: true
  
  # Longer cache for mobile (they change less frequently)
  max_age: 172800                          # Cache for 48 hours
  preflight_continue: false 