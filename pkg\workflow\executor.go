/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-20 12:00:00
Modified: 2025-01-20 12:00:00
*/

package workflow

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// Executor is the main workflow execution engine.
// It orchestrates the execution of workflow nodes based on their dependencies.
type Executor struct {
	// registry manages node type registrations
	registry NodeRegistry

	// interpolator handles template expression evaluation
	interpolator *ExpressionInterpolator

	// conditionEvaluator handles edge condition evaluation
	conditionEvaluator ConditionEvaluator

	// listeners are notified of execution events
	listeners []ExecutionListener

	// options contains configuration for the executor
	options ExecutorOptions

	// mutex protects concurrent access to the executor
	mutex sync.RWMutex
}

// ExecutorOptions contains configuration options for the executor.
type ExecutorOptions struct {
	// MaxConcurrency limits the number of nodes that can execute concurrently
	MaxConcurrency int

	// DefaultTimeout is the default timeout for node execution (in seconds)
	DefaultTimeout int

	// EnableRetry determines if retry logic is enabled by default
	EnableRetry bool

	// EnableDebug enables debug logging and tracing
	EnableDebug bool

	// StopOnFirstError determines if execution should stop on the first node error
	StopOnFirstError bool
}

// DefaultExecutorOptions returns the default executor options.
func DefaultExecutorOptions() ExecutorOptions {
	return ExecutorOptions{
		MaxConcurrency:   1,   // Sequential execution by default
		DefaultTimeout:   300, // 5 minutes
		EnableRetry:      true,
		EnableDebug:      false,
		StopOnFirstError: true,
	}
}

// NewExecutor creates a new workflow executor with default options.
func NewExecutor() *Executor {
	return NewExecutorWithOptions(DefaultExecutorOptions())
}

// NewExecutorWithOptions creates a new workflow executor with custom options.
func NewExecutorWithOptions(options ExecutorOptions) *Executor {
	return &Executor{
		registry:           NewSimpleNodeRegistry(),
		interpolator:       NewExpressionInterpolator(),
		conditionEvaluator: NewDefaultConditionEvaluator(),
		listeners:          make([]ExecutionListener, 0),
		options:            options,
	}
}

// RegisterNode registers a node executor for a specific node type.
func (e *Executor) RegisterNode(nodeType string, executor NodeExecutor) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.registry.Register(nodeType, executor)
}

// UnregisterNode removes a node type registration.
func (e *Executor) UnregisterNode(nodeType string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.registry.Unregister(nodeType)
}

// RegisterListener adds an execution listener.
func (e *Executor) RegisterListener(listener ExecutionListener) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.listeners = append(e.listeners, listener)
}

// Execute runs a workflow with the given initial state.
func (e *Executor) Execute(ctx context.Context, workflow *Workflow, initialState *ExecutionState) (*ExecutionState, error) {
	if workflow == nil {
		return nil, fmt.Errorf("workflow cannot be nil")
	}

	if initialState == nil {
		initialState = NewExecutionState()
	}

	// Create execution context
	execCtx := &ExecutionContext{
		WorkflowID:  workflow.ID,
		ExecutionID: initialState.ExecutionID,
		StartTime:   time.Now().Format(time.RFC3339),
		Variables:   make(map[string]interface{}),
		Services:    make(map[string]interface{}),
		Metadata:    make(map[string]interface{}),
	}
	initialState.Context = execCtx

	// Validate workflow
	if err := e.Validate(workflow); err != nil {
		return nil, fmt.Errorf("workflow validation failed: %w", err)
	}

	// Build execution graph
	graph, err := NewGraph(workflow)
	if err != nil {
		return nil, fmt.Errorf("failed to build workflow graph: %w", err)
	}

	// Update execution state
	initialState.Status = ExecutionStatusRunning
	initialState.WorkflowID = workflow.ID

	// Notify listeners
	e.notifyWorkflowStart(ctx, workflow, initialState)

	// Execute workflow
	finalState, err := e.executeWorkflow(ctx, workflow, graph, initialState)
	if err != nil {
		finalState.Status = ExecutionStatusFailed
		finalState.Error = err.Error()
		finalState.EndTime = time.Now().Format(time.RFC3339)
		e.notifyWorkflowError(ctx, workflow, finalState, err)
		return finalState, err
	}

	// Mark as completed
	finalState.Status = ExecutionStatusCompleted
	finalState.EndTime = time.Now().Format(time.RFC3339)

	// Notify listeners
	e.notifyWorkflowComplete(ctx, workflow, finalState)

	return finalState, nil
}

// executeWorkflow performs the actual workflow execution.
func (e *Executor) executeWorkflow(ctx context.Context, workflow *Workflow, graph *Graph, state *ExecutionState) (*ExecutionState, error) {
	// Get execution order
	executionOrder, err := graph.TopologicalSort()
	if err != nil {
		return state, fmt.Errorf("failed to determine execution order: %w", err)
	}

	// Execute nodes in order
	for _, nodeID := range executionOrder {
		select {
		case <-ctx.Done():
			return state, ctx.Err()
		default:
		}

		// Skip if node already completed
		if state.IsNodeCompleted(nodeID) {
			continue
		}

		// Check if node is ready to execute
		if !graph.IsReadyToExecute(nodeID, state) {
			// This shouldn't happen if topological sort is correct
			return state, fmt.Errorf("node %s is not ready to execute", nodeID)
		}

		// Execute the node
		if err := e.executeNode(ctx, graph, nodeID, state); err != nil {
			if e.options.StopOnFirstError {
				return state, fmt.Errorf("node %s execution failed: %w", nodeID, err)
			}
			// Continue execution even if node failed
			continue
		}
	}

	return state, nil
}

// executeNode executes a single node.
func (e *Executor) executeNode(ctx context.Context, graph *Graph, nodeID string, state *ExecutionState) error {
	node := graph.GetNode(nodeID)
	if node == nil {
		return fmt.Errorf("node %s not found", nodeID)
	}

	// Get node executor
	executor := e.registry.Get(node.Type)
	if executor == nil {
		return fmt.Errorf("no executor registered for node type: %s", node.Type)
	}

	// Interpolate node inputs
	inputs, err := e.interpolator.InterpolateInputs(node.Inputs, state)
	if err != nil {
		return fmt.Errorf("failed to interpolate inputs: %w", err)
	}

	// Set current node
	state.CurrentNode = nodeID

	// Notify listeners
	e.notifyNodeStart(ctx, node, inputs)

	// Execute with retry logic
	result, err := e.executeNodeWithRetry(ctx, node, executor, inputs)
	if err != nil {
		e.notifyNodeError(ctx, node, err, result.Attempt)
		return err
	}

	// Store result
	state.SetNodeResult(nodeID, result)

	// Notify listeners
	e.notifyNodeComplete(ctx, node, result)

	return nil
}

// executeNodeWithRetry executes a node with retry logic.
func (e *Executor) executeNodeWithRetry(ctx context.Context, node *Node, executor NodeExecutor, inputs map[string]interface{}) (*NodeResult, error) {
	maxAttempts := 1
	backoffSeconds := 1
	backoffMultiplier := 2.0

	// Configure retry from node settings
	if node.Retry != nil && e.options.EnableRetry {
		maxAttempts = node.Retry.MaxAttempts
		backoffSeconds = node.Retry.BackoffSeconds
		if node.Retry.BackoffMultiplier > 0 {
			backoffMultiplier = node.Retry.BackoffMultiplier
		}
	}

	var lastErr error
	currentBackoff := backoffSeconds

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		// Create node context with timeout
		nodeCtx := ctx
		if node.Timeout > 0 {
			var cancel context.CancelFunc
			nodeCtx, cancel = context.WithTimeout(ctx, time.Duration(node.Timeout)*time.Second)
			defer cancel()
		} else if e.options.DefaultTimeout > 0 {
			var cancel context.CancelFunc
			nodeCtx, cancel = context.WithTimeout(ctx, time.Duration(e.options.DefaultTimeout)*time.Second)
			defer cancel()
		}

		// Execute node
		startTime := time.Now()
		outputs, err := executor.Execute(nodeCtx, inputs)
		duration := time.Since(startTime)

		// Create result
		result := &NodeResult{
			NodeID:    node.ID,
			Outputs:   outputs,
			StartTime: startTime.Format(time.RFC3339),
			EndTime:   time.Now().Format(time.RFC3339),
			Duration:  duration.Milliseconds(),
			Attempt:   attempt,
		}

		if err == nil {
			result.Status = NodeStatusCompleted
			return result, nil
		}

		// Handle error
		lastErr = err
		result.Status = NodeStatusFailed
		result.Error = err.Error()

		// Check if we should retry
		if attempt < maxAttempts && e.shouldRetry(err, node.Retry) {
			// Wait before retry
			if attempt > 1 {
				select {
				case <-ctx.Done():
					return result, ctx.Err()
				case <-time.After(time.Duration(currentBackoff) * time.Second):
				}
			}
			currentBackoff = int(float64(currentBackoff) * backoffMultiplier)
			continue
		}

		return result, lastErr
	}

	return nil, lastErr
}

// shouldRetry determines if an error should trigger a retry.
func (e *Executor) shouldRetry(err error, retryConfig *RetryConfig) bool {
	if retryConfig == nil {
		return true
	}

	// If specific retryable errors are configured, check if this error matches
	if len(retryConfig.RetryableErrors) > 0 {
		errStr := err.Error()
		for _, retryableErr := range retryConfig.RetryableErrors {
			if errStr == retryableErr {
				return true
			}
		}
		return false
	}

	// Default: retry all errors
	return true
}

// Validate validates a workflow for execution.
func (e *Executor) Validate(workflow *Workflow) error {
	// Basic workflow validation
	if err := workflow.Validate(); err != nil {
		return err
	}

	// Build graph to check for cycles
	graph, err := NewGraph(workflow)
	if err != nil {
		return err
	}

	// Validate node types are registered
	if err := graph.ValidateNodeTypes(e.registry); err != nil {
		return err
	}

	return nil
}

// GetExecutionOrder returns the order in which nodes would be executed.
func (e *Executor) GetExecutionOrder(workflow *Workflow) ([]string, error) {
	graph, err := NewGraph(workflow)
	if err != nil {
		return nil, err
	}

	return graph.TopologicalSort()
}

// Notification methods for listeners

func (e *Executor) notifyWorkflowStart(ctx context.Context, workflow *Workflow, state *ExecutionState) {
	e.mutex.RLock()
	listeners := make([]ExecutionListener, len(e.listeners))
	copy(listeners, e.listeners)
	e.mutex.RUnlock()

	for _, listener := range listeners {
		listener.OnWorkflowStart(ctx, workflow, state)
	}
}

func (e *Executor) notifyWorkflowComplete(ctx context.Context, workflow *Workflow, state *ExecutionState) {
	e.mutex.RLock()
	listeners := make([]ExecutionListener, len(e.listeners))
	copy(listeners, e.listeners)
	e.mutex.RUnlock()

	for _, listener := range listeners {
		listener.OnWorkflowComplete(ctx, workflow, state)
	}
}

func (e *Executor) notifyWorkflowError(ctx context.Context, workflow *Workflow, state *ExecutionState, err error) {
	e.mutex.RLock()
	listeners := make([]ExecutionListener, len(e.listeners))
	copy(listeners, e.listeners)
	e.mutex.RUnlock()

	for _, listener := range listeners {
		listener.OnWorkflowError(ctx, workflow, state, err)
	}
}

func (e *Executor) notifyNodeStart(ctx context.Context, node *Node, inputs map[string]interface{}) {
	e.mutex.RLock()
	listeners := make([]ExecutionListener, len(e.listeners))
	copy(listeners, e.listeners)
	e.mutex.RUnlock()

	for _, listener := range listeners {
		listener.OnNodeStart(ctx, node, inputs)
	}
}

func (e *Executor) notifyNodeComplete(ctx context.Context, node *Node, result *NodeResult) {
	e.mutex.RLock()
	listeners := make([]ExecutionListener, len(e.listeners))
	copy(listeners, e.listeners)
	e.mutex.RUnlock()

	for _, listener := range listeners {
		listener.OnNodeComplete(ctx, node, result)
	}
}

func (e *Executor) notifyNodeError(ctx context.Context, node *Node, err error, attempt int) {
	e.mutex.RLock()
	listeners := make([]ExecutionListener, len(e.listeners))
	copy(listeners, e.listeners)
	e.mutex.RUnlock()

	for _, listener := range listeners {
		listener.OnNodeError(ctx, node, err, attempt)
	}
}

// SimpleNodeRegistry is a basic implementation of NodeRegistry.
type SimpleNodeRegistry struct {
	executors map[string]NodeExecutor
	mutex     sync.RWMutex
}

// NewSimpleNodeRegistry creates a new simple node registry.
func NewSimpleNodeRegistry() *SimpleNodeRegistry {
	return &SimpleNodeRegistry{
		executors: make(map[string]NodeExecutor),
	}
}

// Register associates a node type with its executor implementation.
func (r *SimpleNodeRegistry) Register(nodeType string, executor NodeExecutor) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.executors[nodeType] = executor
}

// Get retrieves the executor for a given node type.
func (r *SimpleNodeRegistry) Get(nodeType string) NodeExecutor {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.executors[nodeType]
}

// List returns all registered node types.
func (r *SimpleNodeRegistry) List() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	types := make([]string, 0, len(r.executors))
	for nodeType := range r.executors {
		types = append(types, nodeType)
	}
	return types
}

// Unregister removes a node type registration.
func (r *SimpleNodeRegistry) Unregister(nodeType string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.executors, nodeType)
}

// DefaultConditionEvaluator is a basic implementation of ConditionEvaluator.
type DefaultConditionEvaluator struct{}

// NewDefaultConditionEvaluator creates a new default condition evaluator.
func NewDefaultConditionEvaluator() *DefaultConditionEvaluator {
	return &DefaultConditionEvaluator{}
}

// Evaluate determines whether an edge condition is satisfied.
func (e *DefaultConditionEvaluator) Evaluate(condition *EdgeCondition, nodeOutputs map[string]interface{}) (bool, error) {
	if condition == nil {
		return true, nil
	}

	switch condition.Type {
	case "equals":
		if condition.Field == "" {
			return false, fmt.Errorf("field is required for equals condition")
		}
		actual := nodeOutputs[condition.Field]
		return actual == condition.Value, nil

	case "not_equals":
		if condition.Field == "" {
			return false, fmt.Errorf("field is required for not_equals condition")
		}
		actual := nodeOutputs[condition.Field]
		return actual != condition.Value, nil

	case "exists":
		if condition.Field == "" {
			return false, fmt.Errorf("field is required for exists condition")
		}
		_, exists := nodeOutputs[condition.Field]
		return exists, nil

	case "not_exists":
		if condition.Field == "" {
			return false, fmt.Errorf("field is required for not_exists condition")
		}
		_, exists := nodeOutputs[condition.Field]
		return !exists, nil

	case "expression":
		// TODO: Implement expression evaluation
		return false, fmt.Errorf("expression conditions not yet implemented")

	default:
		return false, fmt.Errorf("unknown condition type: %s", condition.Type)
	}
}
