# CINA.CLUB HarmonyOS Application

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

## 概述

CINA.CLUB HarmonyOS应用是一个现代化的原生应用，基于华为官方推荐的技术栈构建，为华为生态用户提供完整的CINA.CLUB服务体验。

### 核心特性

- 🚀 **现代架构**: 采用ArkTS + ArkUI的声明式开发模式
- 🔒 **端到端加密**: 通过Go核心库实现的安全加密功能
- 🤖 **本地AI**: 集成端侧AI模型和推理能力
- 🌐 **分布式同步**: 利用HarmonyOS分布式能力实现跨设备协同
- 📱 **多设备适配**: 支持手机、平板、智慧屏等多种设备形态
- ⚡ **高性能**: Go Mobile + NAPI桥接实现的高性能核心逻辑

## 技术架构

### 技术栈

| 层级 | 技术选型 | 说明 |
|------|----------|------|
| **UI层** | ArkTS + ArkUI | 华为官方声明式UI框架 |
| **业务逻辑层** | ArkTS + MVVM | 清晰的分层架构 |
| **核心逻辑层** | Go Mobile + NAPI | 复用平台核心能力 |
| **数据层** | SQLite + Preferences | 本地数据存储 |
| **网络层** | @ohos.net.http + WebSocket | 网络通信 |

### 模块架构

```
harmony/
├── entry/                    # 主应用模块 (Entry HAP)
│   ├── src/main/ets/
│   │   ├── entryability/     # 应用入口
│   │   ├── pages/            # 页面组件
│   │   └── common/           # 通用组件和服务
├── core/                     # 核心库模块 (Core HAR)
│   ├── src/main/ets/         # ArkTS核心代码
│   └── src/main/cpp/         # Go Bridge NAPI层
├── feature/                  # 功能模块
│   ├── auth/                 # 认证模块
│   ├── chat/                 # 聊天模块
│   └── pkb/                  # 个人知识库模块
└── AppScope/                 # 应用级资源
```

## 核心组件

### 1. Go Bridge NAPI层

位于 `core/src/main/cpp/`，实现Go与ArkTS的桥接：

- **加密功能**: 对称加密/解密、密钥派生
- **AI功能**: 本地模型推理、流式预测
- **数据同步**: 分块同步、版本管理

### 2. 依赖注入系统

`DependencyInjector` 管理所有服务实例：

```typescript
// 获取用户管理器
const userManager = DependencyInjector.getInstance().getUserManager();

// 获取网络管理器
const networkManager = DependencyInjector.getInstance().getNetworkManager();
```

### 3. 应用配置管理

`AppConfigManager` 统一管理应用配置：

```typescript
const config = AppConfigManager.getInstance();
const apiUrl = config.getApiBaseUrl();
const isE2EEEnabled = config.isE2EEEnabled();
```

## 构建和运行

### 环境要求

- **HarmonyOS SDK**: 5.0.0(12) 或更高版本
- **DevEco Studio**: 最新版本
- **Node.js**: 18.x 或更高版本
- **Go**: 1.22+ (用于编译核心库)

### 构建步骤

1. **安装依赖**:
   ```bash
   ohpm install
   ```

2. **编译Go核心库**:
   ```bash
   # 在项目根目录运行
   cd ../../core
   gomobile bind -buildmode=c-archive -target=android/arm64,android/arm .
   cp libcore.* ../apps/harmony/core/src/main/cpp/libs/
   ```

3. **构建应用**:
   ```bash
   # 调试版本
   ohpm run build
   
   # 发布版本
   ohpm run build:release
   ```

## 功能模块

### 认证模块 (feature/auth)

- 用户登录/注册
- 生物识别认证
- 令牌管理

### 聊天模块 (feature/chat)

- 实时消息
- AI助手对话
- 群聊功能

### 个人知识库模块 (feature/pkb)

- 端到端加密笔记
- 智能搜索
- 跨设备同步

## 版本信息

- **当前版本**: 1.0.0
- **最低支持版本**: HarmonyOS 5.0.0(12)
- **目标版本**: HarmonyOS 5.0.0(12)

## 许可证

本项目采用专有许可证，版权归CINA.CLUB所有。未经授权，禁止复制、修改或分发。

---

**构建智能生活，无限可能 | CINA.CLUB** 