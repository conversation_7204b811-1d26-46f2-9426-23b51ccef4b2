#!/usr/bin/env bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-06-18
# Modified: 2025-06-18

# CINA.CLUB 开发环境设置脚本
# 一键配置完整的多平台开发环境

set -euo pipefail

# 导入共享函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# shellcheck source=../lib/helpers.sh
source "$SCRIPT_DIR/../lib/helpers.sh"

# 脚本配置
readonly SCRIPT_NAME="dev-env-setup"

# 必需的工具及其安装提示
declare -A REQUIRED_TOOLS=(
    ["go"]="Install Go from https://golang.org/dl/"
    ["git"]="Install Git from https://git-scm.com/"
    ["docker"]="Install Docker from https://docker.com/"
    ["node"]="Install Node.js from https://nodejs.org/"
)

# 平台特定工具
declare -A PLATFORM_TOOLS=(
    ["android"]="Android Studio and NDK"
    ["ios"]="Xcode and Command Line Tools (macOS only)"
    ["windows"]="Visual Studio with .NET SDK"
    ["web"]="Already covered by Node.js"
)

# 可选工具
declare -A OPTIONAL_TOOLS=(
    ["buf"]="protobuf toolchain"
    ["migrate"]="database migration tool"
    ["golangci-lint"]="Go linter"
    ["air"]="Go hot reload tool"
    ["pnpm"]="fast package manager"
    ["yq"]="YAML processor"
)

# 显示使用说明
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Set up the complete CINA.CLUB development environment.

Options:
    --skip-tools           Skip tool installation checks
    --skip-git-hooks       Skip Git hooks installation
    --skip-generation      Skip code generation
    --skip-docker          Skip Docker setup
    --platforms PLATFORMS  Comma-separated list of platforms to set up
                          Available: android,ios,windows,web,all
                          Default: all
    --minimal              Minimal setup (skip optional tools and generation)
    -v, --verbose          Enable verbose output
    -h, --help             Show this help message

Examples:
    $0                                    # Full setup for all platforms
    $0 --platforms android,web           # Setup only for Android and Web
    $0 --minimal                          # Minimal setup
    $0 --skip-docker                      # Skip Docker setup

Environment Variables:
    DEBUG=1                Enable debug output
    SKIP_INTERACTIVE      Skip interactive prompts
EOF
}

# 解析命令行参数
parse_args() {
    local skip_tools=false
    local skip_git_hooks=false
    local skip_generation=false
    local skip_docker=false
    local platforms="all"
    local minimal=false
    local verbose=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tools)
                skip_tools=true
                shift
                ;;
            --skip-git-hooks)
                skip_git_hooks=true
                shift
                ;;
            --skip-generation)
                skip_generation=true
                shift
                ;;
            --skip-docker)
                skip_docker=true
                shift
                ;;
            --platforms)
                platforms="$2"
                shift 2
                ;;
            --minimal)
                minimal=true
                skip_generation=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 设置全局变量
    SKIP_TOOLS="$skip_tools"
    SKIP_GIT_HOOKS="$skip_git_hooks"
    SKIP_GENERATION="$skip_generation"
    SKIP_DOCKER="$skip_docker"
    PLATFORMS="$platforms"
    MINIMAL="$minimal"
    
    if [[ "$verbose" == "true" ]]; then
        export DEBUG=1
    fi
}

# 检查必需的工具
check_required_tools() {
    if [[ "$SKIP_TOOLS" == "true" ]]; then
        info "Skipping tool checks as requested"
        return 0
    fi
    
    step "Checking required tools"
    
    local missing_tools=()
    
    for tool in "${!REQUIRED_TOOLS[@]}"; do
        if check_command "$tool"; then
            # 显示版本信息
            local version
            case "$tool" in
                "go")
                    version=$(go version | awk '{print $3}')
                    ;;
                "git")
                    version=$(git --version | awk '{print $3}')
                    ;;
                "docker")
                    version=$(docker --version | awk '{print $3}' | tr -d ',')
                    ;;
                "node")
                    version=$(node --version)
                    ;;
                *)
                    version="installed"
                    ;;
            esac
            info "✓ $tool ($version)"
        else
            missing_tools+=("$tool")
            error "✗ $tool - ${REQUIRED_TOOLS[$tool]}"
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        error "Missing required tools: ${missing_tools[*]}"
        error "Please install the missing tools and run this script again"
        exit 1
    fi
    
    success "All required tools are available"
}

# 检查可选工具
check_optional_tools() {
    if [[ "$MINIMAL" == "true" ]]; then
        return 0
    fi
    
    step "Checking optional tools"
    
    local missing_optional=()
    
    for tool in "${!OPTIONAL_TOOLS[@]}"; do
        if check_command "$tool"; then
            info "✓ $tool"
        else
            missing_optional+=("$tool")
            warn "○ $tool - ${OPTIONAL_TOOLS[$tool]}"
        fi
    done
    
    if [[ ${#missing_optional[@]} -gt 0 ]]; then
        info "Optional tools not found: ${missing_optional[*]}"
        
        if [[ "${SKIP_INTERACTIVE:-false}" != "true" ]]; then
            echo
            read -p "Would you like to install missing optional tools? (y/N) " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                install_optional_tools "${missing_optional[@]}"
            fi
        fi
    else
        success "All optional tools are available"
    fi
}

# 安装可选工具
install_optional_tools() {
    local tools=("$@")
    
    step "Installing optional tools"
    
    for tool in "${tools[@]}"; do
        info "Installing $tool..."
        
        case "$tool" in
            "buf")
                if command -v go &> /dev/null; then
                    run_cmd "go install github.com/bufbuild/buf/cmd/buf@latest"
                else
                    warn "Go not available, skipping buf installation"
                fi
                ;;
            "migrate")
                if command -v go &> /dev/null; then
                    run_cmd "go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest"
                else
                    warn "Go not available, skipping migrate installation"
                fi
                ;;
            "golangci-lint")
                if command -v go &> /dev/null; then
                    run_cmd "go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"
                else
                    warn "Go not available, skipping golangci-lint installation"
                fi
                ;;
            "air")
                if command -v go &> /dev/null; then
                    run_cmd "go install github.com/cosmtrek/air@latest"
                else
                    warn "Go not available, skipping air installation"
                fi
                ;;
            "pnpm")
                if command -v npm &> /dev/null; then
                    run_cmd "npm install -g pnpm"
                else
                    warn "npm not available, skipping pnpm installation"
                fi
                ;;
            "yq")
                if command -v go &> /dev/null; then
                    run_cmd "go install github.com/mikefarah/yq/v4@latest"
                else
                    warn "Go not available, skipping yq installation"
                fi
                ;;
            *)
                warn "Don't know how to install $tool automatically"
                ;;
        esac
    done
}

# 检查平台特定工具
check_platform_tools() {
    local platforms
    IFS=',' read -ra platforms <<< "$PLATFORMS"
    
    if [[ " ${platforms[*]} " =~ " all " ]]; then
        platforms=("android" "ios" "windows" "web")
    fi
    
    step "Checking platform-specific tools"
    
    local current_os
    current_os=$(detect_os)
    
    for platform in "${platforms[@]}"; do
        platform=$(echo "$platform" | xargs)  # 去除空格
        
        case "$platform" in
            "android")
                info "Android development requirements:"
                if [[ "$current_os" == "macos" ]] || [[ "$current_os" == "linux" ]]; then
                    info "  - Android Studio with SDK"
                    info "  - Android NDK (for Go Mobile)"
                    
                    if [[ -n "${ANDROID_HOME:-}" ]] || [[ -n "${ANDROID_SDK_ROOT:-}" ]]; then
                        info "  ✓ Android SDK path configured"
                    else
                        warn "  ○ Android SDK path not configured"
                        warn "    Set ANDROID_HOME or ANDROID_SDK_ROOT environment variable"
                    fi
                    
                    if [[ -n "${NDK_ROOT:-}" ]] || [[ -d "${ANDROID_HOME:-}/ndk" ]] || [[ -d "${ANDROID_SDK_ROOT:-}/ndk" ]]; then
                        info "  ✓ Android NDK appears to be available"
                    else
                        warn "  ○ Android NDK not found"
                        warn "    Install NDK through Android Studio SDK Manager"
                    fi
                else
                    warn "  Android development is supported on macOS and Linux"
                fi
                ;;
            "ios")
                info "iOS development requirements:"
                if [[ "$current_os" == "macos" ]]; then
                    if command -v xcodebuild &> /dev/null; then
                        local xcode_version
                        xcode_version=$(xcodebuild -version | head -1)
                        info "  ✓ $xcode_version"
                    else
                        warn "  ○ Xcode not found"
                        warn "    Install Xcode from App Store"
                    fi
                    
                    if xcode-select -p &> /dev/null; then
                        info "  ✓ Xcode Command Line Tools"
                    else
                        warn "  ○ Xcode Command Line Tools not found"
                        warn "    Run: xcode-select --install"
                    fi
                else
                    warn "  iOS development requires macOS"
                fi
                ;;
            "windows")
                info "Windows development requirements:"
                info "  - Visual Studio with .NET SDK"
                info "  - WinUI 3 project templates"
                
                if command -v dotnet &> /dev/null; then
                    local dotnet_version
                    dotnet_version=$(dotnet --version)
                    info "  ✓ .NET SDK ($dotnet_version)"
                else
                    warn "  ○ .NET SDK not found"
                    warn "    Install from https://dotnet.microsoft.com/"
                fi
                ;;
            "web")
                info "Web development requirements:"
                
                if command -v node &> /dev/null; then
                    local node_version
                    node_version=$(node --version)
                    info "  ✓ Node.js ($node_version)"
                else
                    warn "  ○ Node.js not found"
                fi
                
                if command -v pnpm &> /dev/null; then
                    local pnpm_version
                    pnpm_version=$(pnpm --version)
                    info "  ✓ pnpm ($pnpm_version)"
                elif command -v npm &> /dev/null; then
                    info "  ✓ npm (consider installing pnpm for better performance)"
                else
                    warn "  ○ No package manager found"
                fi
                ;;
        esac
    done
    
    success "Platform tool check completed"
}

# 设置 Git hooks
setup_git_hooks() {
    if [[ "$SKIP_GIT_HOOKS" == "true" ]]; then
        info "Skipping Git hooks setup as requested"
        return 0
    fi
    
    step "Setting up Git hooks"
    
    local project_root
    project_root="$(get_project_root)"
    local hooks_dir="$project_root/.git/hooks"
    
    if [[ ! -d "$hooks_dir" ]]; then
        error "Git hooks directory not found: $hooks_dir"
        error "Make sure you're in a Git repository"
        return 1
    fi
    
    # Pre-commit hook
    local pre_commit_hook="$hooks_dir/pre-commit"
    info "Installing pre-commit hook..."
    
    cat > "$pre_commit_hook" << 'EOF'
#!/bin/bash
# CINA.CLUB pre-commit hook
# Copyright (c) 2025 Cina.Club

set -e

echo "Running pre-commit checks..."

# Run linters
if [[ -f "scripts/ci/lint.sh" ]]; then
    echo "→ Running linters..."
    ./scripts/ci/lint.sh --fast
fi

# Check for large files
echo "→ Checking for large files..."
git diff --cached --name-only | while read -r file; do
    if [[ -f "$file" ]]; then
        size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        if [[ $size -gt 10485760 ]]; then  # 10MB
            echo "Error: File $file is larger than 10MB ($size bytes)"
            echo "Consider using Git LFS for large files"
            exit 1
        fi
    fi
done

echo "Pre-commit checks passed ✓"
EOF
    
    chmod +x "$pre_commit_hook"
    success "Pre-commit hook installed"
    
    # Pre-push hook
    local pre_push_hook="$hooks_dir/pre-push"
    info "Installing pre-push hook..."
    
    cat > "$pre_push_hook" << 'EOF'
#!/bin/bash
# CINA.CLUB pre-push hook
# Copyright (c) 2025 Cina.Club

set -e

echo "Running pre-push checks..."

# Run tests
if [[ -f "scripts/ci/test.sh" ]]; then
    echo "→ Running tests..."
    ./scripts/ci/test.sh --fast
fi

echo "Pre-push checks passed ✓"
EOF
    
    chmod +x "$pre_push_hook"
    success "Pre-push hook installed"
}

# 运行代码生成
run_code_generation() {
    if [[ "$SKIP_GENERATION" == "true" ]]; then
        info "Skipping code generation as requested"
        return 0
    fi
    
    step "Running code generation"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 生成 protobuf 代码
    if [[ -f "$project_root/scripts/gen/proto.sh" ]]; then
        info "Generating Protobuf code..."
        if ! run_in_dir "$project_root" "./scripts/gen/proto.sh"; then
            warn "Protobuf generation failed, but continuing..."
        fi
    fi
    
    # 编译核心库
    if [[ -f "$project_root/scripts/gen/core.sh" ]]; then
        info "Compiling core libraries..."
        
        local platforms
        IFS=',' read -ra platforms <<< "$PLATFORMS"
        
        if [[ " ${platforms[*]} " =~ " all " ]]; then
            if ! run_in_dir "$project_root" "./scripts/gen/core.sh --target all"; then
                warn "Core library compilation failed, but continuing..."
            fi
        else
            local platform_targets=""
            for platform in "${platforms[@]}"; do
                case "$platform" in
                    "android"|"ios"|"wasm"|"windows")
                        if [[ -z "$platform_targets" ]]; then
                            platform_targets="$platform"
                        else
                            platform_targets="$platform_targets,$platform"
                        fi
                        ;;
                esac
            done
            
            if [[ -n "$platform_targets" ]]; then
                if ! run_in_dir "$project_root" "./scripts/gen/core.sh --target $platform_targets"; then
                    warn "Core library compilation failed, but continuing..."
                fi
            fi
        fi
    fi
    
    success "Code generation completed"
}

# 设置 Docker 环境
setup_docker_environment() {
    if [[ "$SKIP_DOCKER" == "true" ]]; then
        info "Skipping Docker setup as requested"
        return 0
    fi
    
    step "Setting up Docker environment"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 检查 Docker Compose 文件
    local compose_files=(
        "$project_root/docker-compose.yml"
        "$project_root/infra/docker/dev/docker-compose.yml"
    )
    
    local compose_file=""
    for file in "${compose_files[@]}"; do
        if [[ -f "$file" ]]; then
            compose_file="$file"
            break
        fi
    done
    
    if [[ -z "$compose_file" ]]; then
        warn "No Docker Compose file found"
        return 0
    fi
    
    info "Found Docker Compose file: $compose_file"
    
    if [[ "${SKIP_INTERACTIVE:-false}" != "true" ]]; then
        echo
        read -p "Start Docker development services now? (y/N) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            info "Starting Docker services..."
            run_in_dir "$(dirname "$compose_file")" "docker-compose up -d"
            success "Docker services started"
        fi
    fi
}

# 创建开发配置
create_dev_config() {
    step "Creating development configuration"
    
    local project_root
    project_root="$(get_project_root)"
    
    # 创建 .env 文件（如果不存在）
    local env_file="$project_root/.env"
    if [[ ! -f "$env_file" ]]; then
        info "Creating .env file..."
        
        cat > "$env_file" << EOF
# CINA.CLUB Development Environment
# Copyright (c) 2025 Cina.Club

# Development mode
NODE_ENV=development
GO_ENV=development

# Debug settings
DEBUG=false
LOG_LEVEL=info

# Service URLs (development)
DATABASE_URL=postgres://postgres:postgres@localhost:5432/cinaclub_dev?sslmode=disable
REDIS_URL=redis://localhost:6379
KAFKA_BROKERS=localhost:9092

# API Keys (development - replace with real values)
# OPENAI_API_KEY=your_openai_key_here
# STRIPE_SECRET_KEY=your_stripe_secret_key_here

# Security (development)
JWT_SECRET=dev_jwt_secret_change_in_production
ENCRYPTION_KEY=dev_encryption_key_change_in_production

# Platform configurations
ANDROID_HOME=/usr/local/android-sdk
# XCODE_PATH=/Applications/Xcode.app
EOF
        
        success "Created .env file"
    else
        info ".env file already exists"
    fi
    
    # 创建 VS Code 配置
    local vscode_dir="$project_root/.vscode"
    ensure_dir "$vscode_dir"
    
    if [[ ! -f "$vscode_dir/settings.json" ]]; then
        info "Creating VS Code settings..."
        
        cat > "$vscode_dir/settings.json" << EOF
{
    "go.toolsManagement.checkForUpdates": "local",
    "go.useLanguageServer": true,
    "go.lintOnSave": "package",
    "go.formatTool": "goimports",
    "typescript.preferences.importModuleSpecifier": "relative",
    "eslint.workingDirectories": ["apps/web"],
    "files.exclude": {
        "**/node_modules": true,
        "**/dist": true,
        "**/build": true,
        "**/*.pb.go": true,
        "**/gen/**": true
    },
    "search.exclude": {
        "**/node_modules": true,
        "**/dist": true,
        "**/build": true,
        "**/*.pb.go": true
    }
}
EOF
        
        success "Created VS Code settings"
    fi
}

# 显示完成总结
show_completion_summary() {
    step "Setup Summary"
    
    success "Development environment setup completed!"
    
    info "Next steps:"
    info "  1. Review the generated .env file and update with your API keys"
    info "  2. Start the development services:"
    info "     • Run: docker-compose up -d"
    info "  3. Test the setup:"
    info "     • Run: make test (or your preferred test command)"
    info "  4. Start developing:"
    info "     • Backend: ./scripts/run-service.sh <service-name>"
    info "     • Frontend: cd apps/<platform> && start development server"
    
    if [[ "$MINIMAL" == "true" ]]; then
        info ""
        info "Minimal setup completed. You may want to:"
        info "  • Run the full setup later: $0"
        info "  • Install optional tools manually"
        info "  • Generate code: ./scripts/gen/proto.sh && ./scripts/gen/core.sh"
    fi
    
    local current_os
    current_os=$(detect_os)
    
    case "$current_os" in
        "macos")
            info ""
            info "macOS-specific notes:"
            info "  • iOS development is available"
            info "  • Consider installing Homebrew for package management"
            ;;
        "linux")
            info ""
            info "Linux-specific notes:"
            info "  • Android development is available"
            info "  • iOS development requires macOS"
            ;;
        "windows")
            info ""
            info "Windows-specific notes:"
            info "  • Windows app development is available"
            info "  • Consider using WSL2 for better development experience"
            ;;
    esac
}

# 主函数
main() {
    # 初始化脚本
    init_script "$SCRIPT_NAME"
    
    # 解析命令行参数
    parse_args "$@"
    
    # 显示平台信息
    info "Setting up development environment for: $PLATFORMS"
    info "Current OS: $(detect_os)"
    info "Current architecture: $(detect_arch)"
    
    # 检查必需工具
    check_required_tools
    
    # 检查可选工具
    check_optional_tools
    
    # 检查平台特定工具
    check_platform_tools
    
    # 设置 Git hooks
    setup_git_hooks
    
    # 运行代码生成
    run_code_generation
    
    # 设置 Docker 环境
    setup_docker_environment
    
    # 创建开发配置
    create_dev_config
    
    # 显示完成总结
    show_completion_summary
    
    # 完成
    finish_script "$SCRIPT_NAME"
}

# 只有在直接执行时才运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 