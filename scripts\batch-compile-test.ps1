# CINA.CLUB Monorepo 批量编译测试脚本
# 自动测试所有服务模块的编译状态并生成详细报告

param(
    [string]$OutputFile = "BATCH_COMPILE_RESULTS.md",
    [switch]$AutoFix = $false,
    [switch]$Verbose = $false
)

Write-Host "🔧 CINA.CLUB Monorepo 批量编译测试开始..." -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Blue

$successCount = 0
$failureCount = 0
$fixedCount = 0
$results = @()
$startTime = Get-Date

# 获取所有服务目录
$serviceDirectories = Get-ChildItem -Path "services" -Directory | Where-Object { 
    Test-Path (Join-Path $_.FullName "go.mod") 
}

Write-Host "📂 发现 $($serviceDirectories.Count) 个服务模块" -ForegroundColor Cyan

# 测试函数
function Test-ServiceCompilation {
    param(
        [string]$ServicePath,
        [string]$ServiceName
    )
    
    Write-Host "📦 测试服务: $ServiceName" -ForegroundColor Yellow
    
    $originalLocation = Get-Location
    $testResult = @{
        ServiceName = $ServiceName
        Path = $ServicePath
        Success = $false
        Errors = @()
        Warnings = @()
        Fixed = $false
        Duration = 0
    }
    
    try {
        Set-Location $ServicePath
        $testStart = Get-Date
        
        # 运行编译测试
        $output = go build ./... 2>&1
        $compileSuccess = $LASTEXITCODE -eq 0
        $testEnd = Get-Date
        $testResult.Duration = ($testEnd - $testStart).TotalSeconds
        
        if ($compileSuccess) {
            $testResult.Success = $true
            Write-Host "  ✅ 编译成功" -ForegroundColor Green
        } else {
            $testResult.Success = $false
            $testResult.Errors = $output | Where-Object { $_ -match "error|Error|ERROR" }
            $testResult.Warnings = $output | Where-Object { $_ -match "warning|Warning|WARN" }
            
            Write-Host "  ❌ 编译失败" -ForegroundColor Red
            if ($Verbose) {
                Write-Host "  错误详情:" -ForegroundColor Yellow
                $testResult.Errors | ForEach-Object { Write-Host "    $_" -ForegroundColor Red }
            }
            
            # 尝试自动修复
            if ($AutoFix) {
                $fixed = Repair-ServiceIssues -ServicePath $ServicePath -ServiceName $ServiceName -Errors $testResult.Errors
                if ($fixed) {
                    Write-Host "  🔧 尝试自动修复..." -ForegroundColor Cyan
                    $output = go build ./... 2>&1
                    if ($LASTEXITCODE -eq 0) {
                        $testResult.Success = $true
                        $testResult.Fixed = $true
                        Write-Host "  ✅ 修复成功!" -ForegroundColor Green
                    }
                }
            }
        }
    }
    catch {
        $testResult.Errors += "PowerShell错误: $($_.Exception.Message)"
        Write-Host "  ❌ 测试过程出错: $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        Set-Location $originalLocation
    }
    
    return $testResult
}

# 自动修复函数
function Repair-ServiceIssues {
    param(
        [string]$ServicePath,
        [string]$ServiceName,
        [array]$Errors
    )
    
    $fixed = $false
    
    # 检查是否是依赖问题
    if ($Errors -join " " -match "go.sum|missing go.sum|malformed go.sum") {
        Write-Host "    🔧 检测到依赖问题，运行 go mod tidy..." -ForegroundColor Cyan
        try {
            Remove-Item "go.sum" -ErrorAction SilentlyContinue
            go mod tidy
            if ($LASTEXITCODE -eq 0) {
                $fixed = $true
            }
        }
        catch {
            Write-Host "    ❌ go mod tidy 失败" -ForegroundColor Red
        }
    }
    
    # 检查空文件问题
    if ($Errors -join " " -match "expected 'package', found 'EOF'") {
        Write-Host "    🔧 检测到空文件，清理中..." -ForegroundColor Cyan
        try {
            Get-ChildItem -Path . -Filter "*.go" -Recurse | ForEach-Object {
                $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
                if ([string]::IsNullOrWhiteSpace($content) -or $content -match "^\s*$") {
                    Write-Host "    🗑️ 删除空文件: $($_.FullName)" -ForegroundColor Yellow
                    Remove-Item $_.FullName -Force
                    $fixed = $true
                }
            }
        }
        catch {
            Write-Host "    ❌ 清理空文件失败" -ForegroundColor Red
        }
    }
    
    return $fixed
}

# 生成标准化go.mod
function New-StandardGoMod {
    param(
        [string]$ServicePath,
        [string]$ServiceName
    )
    
    $goModContent = @"
module cina.club/services/$ServiceName

go 1.22

replace (
    cina.club/core => ../../core
    cina.club/pkg => ../../pkg
    cina.club/services/$ServiceName => ./
)
"@
    
    $goModPath = Join-Path $ServicePath "go.mod"
    $goModContent | Out-File -FilePath $goModPath -Encoding UTF8
}

# 执行批量测试
Write-Host ""
Write-Host "🚀 开始批量编译测试..." -ForegroundColor Green

foreach ($serviceDir in $serviceDirectories) {
    $serviceName = $serviceDir.Name
    $servicePath = $serviceDir.FullName
    
    $result = Test-ServiceCompilation -ServicePath $servicePath -ServiceName $serviceName
    $results += $result
    
    if ($result.Success) {
        $successCount++
    } else {
        $failureCount++
    }
    
    if ($result.Fixed) {
        $fixedCount++
    }
    
    Start-Sleep -Milliseconds 500  # 避免过快执行
}

$endTime = Get-Date
$totalDuration = ($endTime - $startTime).TotalMinutes

# 生成详细报告
Write-Host ""
Write-Host "📊 生成测试报告..." -ForegroundColor Cyan

$reportContent = @"
# CINA.CLUB Monorepo 批量编译测试报告

**测试时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')  
**测试持续时间**: $([math]::Round($totalDuration, 2)) 分钟  
**测试服务数**: $($serviceDirectories.Count)

## 📊 测试统计

- **✅ 编译成功**: $successCount 个服务
- **❌ 编译失败**: $failureCount 个服务  
- **🔧 自动修复**: $fixedCount 个服务
- **📈 成功率**: $([math]::Round(($successCount / $serviceDirectories.Count) * 100, 1))%

## 📋 详细测试结果

### ✅ 编译成功的服务

"@

# 添加成功的服务
$successfulServices = $results | Where-Object { $_.Success }
foreach ($service in $successfulServices) {
    $statusIcon = if ($service.Fixed) { "🔧✅" } else { "✅" }
    $reportContent += "`n- **$($service.ServiceName)** $statusIcon - 编译时间: $([math]::Round($service.Duration, 2))秒"
}

$reportContent += "`n`n### ❌ 编译失败的服务`n"

# 添加失败的服务
$failedServices = $results | Where-Object { -not $_.Success }
foreach ($service in $failedServices) {
    $reportContent += "`n#### $($service.ServiceName) ❌`n"
    $reportContent += "**路径**: ``$($service.Path)```n"
    $reportContent += "**主要错误**:`n"
    
    $topErrors = $service.Errors | Select-Object -First 3
    foreach ($error in $topErrors) {
        $reportContent += "- $error`n"
    }
    
    if ($service.Errors.Count -gt 3) {
        $reportContent += "- ... 还有 $($service.Errors.Count - 3) 个错误`n"
    }
    $reportContent += ""
}

# 添加修复建议
$reportContent += @"

## 🔧 修复建议

### 优先修复列表

"@

$criticalServices = $failedServices | Sort-Object { $_.Errors.Count } | Select-Object -First 5
foreach ($service in $criticalServices) {
    $reportContent += "1. **$($service.ServiceName)** - $($service.Errors.Count) 个错误`n"
}

$reportContent += @"

### 通用修复方案

1. **依赖问题修复**:
   ```bash
   cd services/{service-name}
   rm go.sum
   go mod tidy
   ```

2. **空文件清理**:
   ```bash
   find . -name "*.go" -size 0 -delete
   ```

3. **标准化go.mod**:
   ```go
   module cina.club/services/{service-name}
   
   go 1.22
   
   replace (
       cina.club/core => ../../core
       cina.club/pkg => ../../pkg
       cina.club/services/{service-name} => ./
   )
   ```

## 📈 项目健康度分析

- **核心稳定性**: ✅ 良好 (core/pkg模块稳定)
- **服务可用性**: $([math]::Round(($successCount / $serviceDirectories.Count) * 100, 1))% 
- **整体健康度**: $(if (($successCount / $serviceDirectories.Count) -gt 0.8) { "🟢 健康" } elseif (($successCount / $serviceDirectories.Count) -gt 0.6) { "🟡 中等" } else { "🔴 需要改进" })

## 🚀 下一步行动计划

1. **立即行动**: 修复编译失败的 $failureCount 个服务
2. **质量提升**: 实施标准化go.mod配置
3. **自动化**: 集成CI/CD编译检查
4. **监控**: 定期运行批量编译测试

---
**报告生成**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')  
**工具**: CINA.CLUB 批量编译测试脚本
"@

# 保存报告
$reportContent | Out-File -FilePath $OutputFile -Encoding UTF8

# 输出总结
Write-Host ""
Write-Host "🎉 批量编译测试完成!" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Blue
Write-Host "📊 测试统计:" -ForegroundColor Cyan
Write-Host "  ✅ 成功: $successCount/$($serviceDirectories.Count) 服务" -ForegroundColor Green
Write-Host "  ❌ 失败: $failureCount/$($serviceDirectories.Count) 服务" -ForegroundColor Red
Write-Host "  🔧 修复: $fixedCount 服务" -ForegroundColor Yellow
Write-Host "  📈 成功率: $([math]::Round(($successCount / $serviceDirectories.Count) * 100, 1))%" -ForegroundColor Cyan
Write-Host "  ⏱️ 总耗时: $([math]::Round($totalDuration, 2)) 分钟" -ForegroundColor Blue
Write-Host ""
Write-Host "📄 详细报告已保存到: $OutputFile" -ForegroundColor Yellow

if ($failureCount -gt 0) {
    Write-Host ""
    Write-Host "🔧 建议下一步:" -ForegroundColor Cyan
    Write-Host "1. 查看详细报告了解失败原因" -ForegroundColor White
    Write-Host "2. 运行 .\scripts\batch-compile-test.ps1 -AutoFix 尝试自动修复" -ForegroundColor White
    Write-Host "3. 手动修复剩余的复杂问题" -ForegroundColor White
} 