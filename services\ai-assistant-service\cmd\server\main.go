/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-16 08:30:00
Modified: 2025-01-16 08:30:00
*/

package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"cina.club/services/ai-assistant-service/internal/application/service"
	"cina.club/services/ai-assistant-service/internal/application/toolkit"
	"cina.club/services/ai-assistant-service/internal/domain/model"
	"cina.club/services/ai-assistant-service/internal/domain/session"
	"cina.club/services/ai-assistant-service/internal/infrastructure/http/handler"
)

func main() {
	// Create toolkit factory and register tools
	toolkitFactory := toolkit.NewToolkitFactory()
	err := toolkitFactory.RegisterDefaultTools()
	if err != nil {
		log.Fatalf("Failed to register default tools: %v", err)
	}

	// Create session store (using memory store as example)
	sessionStore := NewMemorySessionStore()
	sessionManager := session.NewSessionManager(sessionStore)

	// Create AI assistant service
	assistantService := service.NewAIAssistantService(sessionManager, toolkitFactory)

	// Create HTTP handler
	assistantHandler := handler.NewAssistantHandler(assistantService)

	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	// Create router
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Register routes
	assistantHandler.RegisterRoutes(router)

	// Create HTTP server
	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// Start server
	go func() {
		log.Printf("AI Assistant Service starting on port 8080...")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

// MemorySessionStore in-memory session store implementation (for demonstration)
type MemorySessionStore struct {
	sessions map[string]*SessionData
}

// SessionData session data structure
type SessionData struct {
	State     []byte
	ExpiresAt time.Time
}

// NewMemorySessionStore creates a new memory session store
func NewMemorySessionStore() session.SessionStore {
	return &MemorySessionStore{
		sessions: make(map[string]*SessionData),
	}
}

// Get retrieves session state
func (s *MemorySessionStore) Get(ctx context.Context, sessionID string) (*model.DialogState, error) {
	data, exists := s.sessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session not found")
	}

	if time.Now().After(data.ExpiresAt) {
		delete(s.sessions, sessionID)
		return nil, fmt.Errorf("session expired")
	}

	// Here should deserialize session state
	// For simplification, we return a new state
	return model.NewDialogState("user-123"), nil
}

// Set saves session state
func (s *MemorySessionStore) Set(ctx context.Context, sessionID string, state *model.DialogState, ttl time.Duration) error {
	data, err := state.ToJSON()
	if err != nil {
		return err
	}

	s.sessions[sessionID] = &SessionData{
		State:     data,
		ExpiresAt: time.Now().Add(ttl),
	}

	return nil
}

// Delete removes session state
func (s *MemorySessionStore) Delete(ctx context.Context, sessionID string) error {
	delete(s.sessions, sessionID)
	return nil
}

// Exists checks if session exists
func (s *MemorySessionStore) Exists(ctx context.Context, sessionID string) (bool, error) {
	data, exists := s.sessions[sessionID]
	if !exists {
		return false, nil
	}

	if time.Now().After(data.ExpiresAt) {
		delete(s.sessions, sessionID)
		return false, nil
	}

	return true, nil
}

// ExtendTTL extends session TTL
func (s *MemorySessionStore) ExtendTTL(ctx context.Context, sessionID string, ttl time.Duration) error {
	data, exists := s.sessions[sessionID]
	if !exists {
		return fmt.Errorf("session not found")
	}

	data.ExpiresAt = time.Now().Add(ttl)
	return nil
}
