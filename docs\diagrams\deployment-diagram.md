# 部署架构图

**版权所有 (c) 2025 Cina.Club**  
**所有权利保留**

本文档详细展示CINA.CLUB平台在不同环境下的部署架构，包括开发、测试、预生产和生产环境。

## 环境概览

### 多环境部署策略

```mermaid
graph TB
    subgraph "开发环境 (Development)"
        DevK8s[K8s集群<br/>单节点]
        DevDB[(开发数据库<br/>PostgreSQL)]
        DevRedis[(Redis缓存)]
    end

    subgraph "测试环境 (Testing)"
        TestK8s[K8s集群<br/>3节点]
        TestDB[(测试数据库<br/>PostgreSQL)]
        TestRedis[(Redis集群)]
        TestMQ[消息队列<br/>Kafka]
    end

    subgraph "预生产环境 (Staging)"
        StagingK8s[K8s集群<br/>5节点]
        StagingDB[(预生产数据库<br/>PostgreSQL主从)]
        StagingRedis[(Redis集群)]
        StagingMQ[Kafka集群]
        StagingES[(Elasticsearch)]
    end

    subgraph "生产环境 (Production)"
        ProdK8s[K8s集群<br/>多可用区]
        ProdDB[(生产数据库<br/>PostgreSQL集群)]
        ProdRedis[(Redis集群<br/>哨兵模式)]
        ProdMQ[Kafka集群]
        ProdES[(Elasticsearch集群)]
        ProdS3[(对象存储<br/>S3/MinIO)]
    end

    %% 数据流向
    DevK8s -.->|代码部署| TestK8s
    TestK8s -.->|测试通过| StagingK8s
    StagingK8s -.->|验收通过| ProdK8s

    %% 数据同步
    DevDB -.->|数据同步| TestDB
    TestDB -.->|脱敏数据| StagingDB
```

## 生产环境架构

### 云原生部署架构

```mermaid
graph TB
    subgraph "Internet"
        Users[用户设备]
        CDN[全球CDN<br/>CloudFront/Fastly]
    end

    subgraph "AWS云平台"
        subgraph "可用区A (us-east-1a)"
            subgraph "公共子网A"
                ALB_A[应用负载均衡器A]
                NAT_A[NAT网关A]
            end
            
            subgraph "私有子网A"
                EKS_A[EKS工作节点组A]
                RDS_A[(RDS PostgreSQL<br/>主库)]
                ElastiCache_A[(ElastiCache Redis<br/>主节点)]
            end
        end

        subgraph "可用区B (us-east-1b)"
            subgraph "公共子网B"
                ALB_B[应用负载均衡器B]
                NAT_B[NAT网关B]
            end
            
            subgraph "私有子网B"
                EKS_B[EKS工作节点组B]
                RDS_B[(RDS PostgreSQL<br/>只读副本)]
                ElastiCache_B[(ElastiCache Redis<br/>副本节点)]
            end
        end

        subgraph "可用区C (us-east-1c)"
            subgraph "私有子网C"
                EKS_C[EKS工作节点组C]
                RDS_C[(RDS PostgreSQL<br/>只读副本)]
            end
        end

        subgraph "共享服务"
            S3[S3对象存储<br/>多区域复制]
            Route53[Route53 DNS]
            IAM[IAM身份管理]
            Secrets[Secrets Manager]
            CloudWatch[CloudWatch监控]
            ELK[托管ES服务]
        end
    end

    %% 连接关系
    Users --> CDN
    CDN --> Route53
    Route53 --> ALB_A
    Route53 --> ALB_B
    
    ALB_A --> EKS_A
    ALB_B --> EKS_B
    
    EKS_A --> RDS_A
    EKS_A --> ElastiCache_A
    EKS_B --> RDS_B
    EKS_B --> ElastiCache_B
    EKS_C --> RDS_C
    
    EKS_A --> S3
    EKS_B --> S3
    EKS_C --> S3
    
    RDS_A -.->|异步复制| RDS_B
    RDS_A -.->|异步复制| RDS_C
    ElastiCache_A -.->|主从复制| ElastiCache_B
```

### Kubernetes集群架构

```mermaid
graph TB
    subgraph "EKS控制平面"
        APIServer[API Server]
        Scheduler[调度器]
        Controller[控制器管理器]
        etcd[(etcd集群)]
    end

    subgraph "工作节点组"
        subgraph "应用节点组"
            AppNode1[应用节点1<br/>c5.xlarge]
            AppNode2[应用节点2<br/>c5.xlarge]
            AppNode3[应用节点3<br/>c5.xlarge]
        end

        subgraph "系统节点组"
            SysNode1[系统节点1<br/>t3.medium]
            SysNode2[系统节点2<br/>t3.medium]
        end

        subgraph "GPU节点组"
            GPUNode1[AI节点1<br/>p3.2xlarge]
            GPUNode2[AI节点2<br/>p3.2xlarge]
        end
    end

    subgraph "网络组件"
        CNI[AWS VPC CNI]
        LoadBalancer[AWS Load Balancer Controller]
        Ingress[NGINX Ingress Controller]
        ServiceMesh[Istio服务网格]
    end

    subgraph "存储组件"
        EBS[EBS CSI Driver]
        EFS[EFS CSI Driver]
        S3CSI[S3 CSI Driver]
    end

    %% 连接关系
    APIServer --> AppNode1
    APIServer --> AppNode2
    APIServer --> AppNode3
    APIServer --> SysNode1
    APIServer --> SysNode2
    APIServer --> GPUNode1
    APIServer --> GPUNode2

    Scheduler --> APIServer
    Controller --> APIServer
    etcd --> APIServer

    CNI --> AppNode1
    CNI --> AppNode2
    LoadBalancer --> Ingress
    ServiceMesh --> AppNode1
    ServiceMesh --> AppNode2

    EBS --> AppNode1
    EFS --> AppNode2
    S3CSI --> AppNode3
```

## 应用服务部署

### 微服务部署拓扑

```mermaid
graph TB
    subgraph "命名空间: cina-gateway"
        subgraph "API网关层"
            Gateway1[gateway-deployment<br/>副本数: 3]
            Gateway2[auth-service<br/>副本数: 2]
        end
    end

    subgraph "命名空间: cina-services"
        subgraph "核心服务"
            UserSvc[user-service<br/>副本数: 3]
            ChatSvc[chat-service<br/>副本数: 5]
            PKBSvc[pkb-service<br/>副本数: 3]
            AISvc[ai-service<br/>副本数: 2]
        end

        subgraph "支撑服务"
            NotifySvc[notification-service<br/>副本数: 2]
            PaySvc[payment-service<br/>副本数: 2]
            FileSvc[file-service<br/>副本数: 3]
            SearchSvc[search-service<br/>副本数: 2]
        end
    end

    subgraph "命名空间: cina-data"
        subgraph "数据服务"
            PostgreSQL[postgresql-ha<br/>主从部署]
            Redis[redis-cluster<br/>3主3从]
            Kafka[kafka-cluster<br/>3个broker]
            ES[elasticsearch<br/>3节点集群]
        end
    end

    subgraph "命名空间: cina-system"
        subgraph "系统服务"
            Prometheus[prometheus-server]
            Grafana[grafana-dashboard]
            AlertManager[alertmanager]
            Jaeger[jaeger-tracing]
            Fluentd[fluentd-logging]
        end
    end

    %% 服务依赖关系
    Gateway1 --> UserSvc
    Gateway1 --> ChatSvc
    Gateway1 --> PKBSvc
    Gateway1 --> AISvc

    UserSvc --> PostgreSQL
    ChatSvc --> Redis
    PKBSvc --> PostgreSQL
    AISvc --> Redis

    SearchSvc --> ES
    FileSvc --> S3[S3存储]
    NotifySvc --> Kafka
    PaySvc --> PostgreSQL

    Prometheus --> UserSvc
    Prometheus --> ChatSvc
    Fluentd --> ES
```

### 服务配置管理

```mermaid
graph LR
    subgraph "配置源"
        ConfigRepo[配置仓库<br/>Git Repository]
        SecretManager[AWS Secrets Manager]
        ParamStore[AWS Parameter Store]
        Vault[HashiCorp Vault]
    end

    subgraph "配置管理"
        ConfigMap[ConfigMap]
        Secret[Secret]
        EnvVar[环境变量]
        CSI[Secrets Store CSI]
    end

    subgraph "应用服务"
        Pod1[Pod 1]
        Pod2[Pod 2]
        Pod3[Pod 3]
    end

    ConfigRepo --> ConfigMap
    SecretManager --> CSI
    ParamStore --> CSI
    Vault --> Secret

    ConfigMap --> Pod1
    Secret --> Pod2
    CSI --> Pod3
    EnvVar --> Pod1
    EnvVar --> Pod2
    EnvVar --> Pod3
```

## 数据层部署

### 数据库集群架构

```mermaid
graph TB
    subgraph "PostgreSQL集群"
        subgraph "主库 (可用区A)"
            PG_Master[PostgreSQL主库<br/>db.r5.2xlarge<br/>读写操作]
        end

        subgraph "只读副本 (可用区B)"
            PG_Replica1[PostgreSQL副本1<br/>db.r5.xlarge<br/>只读查询]
        end

        subgraph "只读副本 (可用区C)"
            PG_Replica2[PostgreSQL副本2<br/>db.r5.xlarge<br/>只读查询]
        end

        subgraph "备份存储"
            S3Backup[S3自动备份<br/>7天保留]
            PointInTime[时间点恢复<br/>35天窗口]
        end
    end

    subgraph "Redis集群"
        subgraph "分片1"
            Redis_Master1[Redis主节点1<br/>cache.r5.large]
            Redis_Replica1[Redis副本1<br/>cache.r5.large]
        end

        subgraph "分片2"
            Redis_Master2[Redis主节点2<br/>cache.r5.large]
            Redis_Replica2[Redis副本2<br/>cache.r5.large]
        end

        subgraph "分片3"
            Redis_Master3[Redis主节点3<br/>cache.r5.large]
            Redis_Replica3[Redis副本3<br/>cache.r5.large]
        end
    end

    %% 数据复制关系
    PG_Master -.->|异步复制| PG_Replica1
    PG_Master -.->|异步复制| PG_Replica2
    PG_Master -.->|自动备份| S3Backup
    PG_Master -.->|WAL日志| PointInTime

    Redis_Master1 -.->|主从复制| Redis_Replica1
    Redis_Master2 -.->|主从复制| Redis_Replica2
    Redis_Master3 -.->|主从复制| Redis_Replica3
```

### 消息队列部署

```mermaid
graph TB
    subgraph "Kafka集群"
        subgraph "Broker节点"
            Kafka1[Kafka Broker 1<br/>kafka.m5.large<br/>可用区A]
            Kafka2[Kafka Broker 2<br/>kafka.m5.large<br/>可用区B]
            Kafka3[Kafka Broker 3<br/>kafka.m5.large<br/>可用区C]
        end

        subgraph "Zookeeper集群"
            ZK1[Zookeeper 1<br/>可用区A]
            ZK2[Zookeeper 2<br/>可用区B]
            ZK3[Zookeeper 3<br/>可用区C]
        end

        subgraph "Topic分区"
            ChatTopic[聊天消息Topic<br/>分区数: 12<br/>副本数: 3]
            NotifyTopic[通知Topic<br/>分区数: 6<br/>副本数: 3]
            LogTopic[日志Topic<br/>分区数: 24<br/>副本数: 2]
        end
    end

    %% 连接关系
    Kafka1 --> ZK1
    Kafka2 --> ZK2
    Kafka3 --> ZK3

    Kafka1 --> ChatTopic
    Kafka2 --> ChatTopic
    Kafka3 --> ChatTopic

    Kafka1 --> NotifyTopic
    Kafka2 --> NotifyTopic

    Kafka1 --> LogTopic
    Kafka3 --> LogTopic
```

## 监控告警部署

### 监控系统架构

```mermaid
graph TB
    subgraph "数据采集层"
        NodeExporter[Node Exporter<br/>系统指标]
        PodExporter[Pod Exporter<br/>容器指标]
        AppMetrics[应用指标<br/>业务指标]
        Fluentd[Fluentd<br/>日志采集]
    end

    subgraph "数据存储层"
        Prometheus[Prometheus<br/>时序数据库]
        ES[Elasticsearch<br/>日志存储]
        S3Logs[S3<br/>长期日志存储]
    end

    subgraph "可视化层"
        Grafana[Grafana<br/>监控面板]
        Kibana[Kibana<br/>日志分析]
        AlertManager[AlertManager<br/>告警管理]
    end

    subgraph "告警通道"
        Slack[Slack通知]
        Email[邮件告警]
        PagerDuty[PagerDuty<br/>值班系统]
        Webhook[Webhook<br/>自定义告警]
    end

    %% 数据流向
    NodeExporter --> Prometheus
    PodExporter --> Prometheus
    AppMetrics --> Prometheus
    Fluentd --> ES

    Prometheus --> Grafana
    ES --> Kibana
    Prometheus --> AlertManager

    AlertManager --> Slack
    AlertManager --> Email
    AlertManager --> PagerDuty
    AlertManager --> Webhook

    ES -.->|归档| S3Logs
```

### 日志聚合架构

```mermaid
graph TB
    subgraph "日志源"
        AppPods[应用Pod日志]
        SystemLogs[系统日志]
        AuditLogs[审计日志]
        AccessLogs[访问日志]
    end

    subgraph "日志收集"
        FluentdDS[Fluentd DaemonSet]
        FluentBit[Fluent Bit<br/>轻量级采集器]
        Filebeat[Filebeat<br/>ELK采集器]
    end

    subgraph "日志处理"
        Logstash[Logstash<br/>日志处理管道]
        Kafka[Kafka<br/>消息缓冲]
    end

    subgraph "日志存储"
        ESCluster[Elasticsearch集群<br/>热数据存储]
        S3Archive[S3<br/>冷数据归档]
        Glacier[Glacier<br/>长期归档]
    end

    subgraph "日志分析"
        Kibana[Kibana<br/>搜索和可视化]
        Grafana[Grafana<br/>监控面板]
        AlertManager[告警系统]
    end

    %% 数据流向
    AppPods --> FluentdDS
    SystemLogs --> FluentBit
    AuditLogs --> Filebeat
    AccessLogs --> FluentdDS

    FluentdDS --> Kafka
    FluentBit --> Logstash
    Filebeat --> Kafka

    Kafka --> Logstash
    Logstash --> ESCluster

    ESCluster --> Kibana
    ESCluster --> Grafana
    ESCluster --> AlertManager

    ESCluster -.->|生命周期管理| S3Archive
    S3Archive -.->|长期归档| Glacier
```

## 安全部署架构

### 网络安全层

```mermaid
graph TB
    subgraph "边界安全"
        WAF[Web应用防火墙<br/>AWS WAF]
        DDoS[DDoS防护<br/>Shield Advanced]
        CDN[CDN安全<br/>CloudFront]
    end

    subgraph "网络层安全"
        VPC[VPC网络隔离]
        SecurityGroups[安全组<br/>端口控制]
        NACL[网络ACL<br/>子网级控制]
        PrivateSubnet[私有子网<br/>内部服务]
    end

    subgraph "应用层安全"
        ServiceMesh[Istio服务网格<br/>mTLS加密]
        NetworkPolicy[Network Policy<br/>Pod级网络控制]
        IngressTLS[Ingress TLS<br/>SSL终止]
    end

    subgraph "数据层安全"
        DBEncryption[数据库加密<br/>静态加密]
        RedisAuth[Redis认证<br/>密码保护]
        S3Encryption[S3加密<br/>KMS密钥]
    end

    %% 安全层级
    WAF --> VPC
    DDoS --> VPC
    CDN --> VPC

    VPC --> SecurityGroups
    SecurityGroups --> NACL
    NACL --> PrivateSubnet

    PrivateSubnet --> ServiceMesh
    ServiceMesh --> NetworkPolicy
    NetworkPolicy --> IngressTLS

    IngressTLS --> DBEncryption
    IngressTLS --> RedisAuth
    IngressTLS --> S3Encryption
```

## 灾难恢复架构

### 多区域备份策略

```mermaid
graph TB
    subgraph "主区域 (us-east-1)"
        PrimaryCluster[主Kubernetes集群]
        PrimaryDB[(主数据库)]
        PrimaryS3[(主S3存储)]
    end

    subgraph "备份区域 (us-west-2)"
        BackupCluster[备份Kubernetes集群]
        BackupDB[(备份数据库)]
        BackupS3[(备份S3存储)]
    end

    subgraph "监控中心"
        HealthCheck[健康检查]
        FailoverLogic[故障转移逻辑]
        DNS[Route53 DNS]
    end

    %% 数据同步
    PrimaryDB -.->|跨区域复制| BackupDB
    PrimaryS3 -.->|跨区域复制| BackupS3
    PrimaryCluster -.->|配置同步| BackupCluster

    %% 故障转移
    HealthCheck --> PrimaryCluster
    HealthCheck --> BackupCluster
    FailoverLogic --> DNS
    DNS --> PrimaryCluster
    DNS -.->|故障转移| BackupCluster
```

---

**所有部署架构图都经过生产环境验证，支持高可用和自动扩缩容。** 