# CINA.CLUB Platform - Cleanup Duplicate Dockerfiles
# Copyright (c) 2025 Cina.Club
# PowerShell script to remove duplicate Dockerfiles from infra/docker/services

Write-Host "🧹 CINA.CLUB - Cleaning Duplicate Dockerfiles" -ForegroundColor Green
Write-Host "Copyright (c) 2025 Cina.Club" -ForegroundColor Yellow
Write-Host ""

$infraDockerPath = "infra/docker/services"
$duplicateFiles = Get-ChildItem $infraDockerPath -Filter "*.Dockerfile"

Write-Host "📊 Found $($duplicateFiles.Count) duplicate Dockerfile(s) in $infraDockerPath" -ForegroundColor Cyan
Write-Host ""

foreach ($file in $duplicateFiles) {
    Write-Host "❌ Deleting: $($file.Name)" -ForegroundColor Red
    Remove-Item $file.FullName -Force
}

Write-Host ""
Write-Host "✅ Cleanup completed!" -ForegroundColor Green
Write-Host "🎯 Dockerfiles now only exist in their respective service directories" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Recommended build pattern:" -ForegroundColor Cyan
Write-Host "docker build -t <service-name> services/<service-name>/" -ForegroundColor Gray 