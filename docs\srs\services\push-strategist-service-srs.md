好的，遵照您的指示。我将为您生成一份专门针对 **`push-strategist-service` (推送策略服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义这个新增的、作为主动内容触达“决策中心”的服务的功能、接口、数据依赖、性能和可靠性需求，作为推荐和运营系统开发的核心依据。

---
### CINA.CLUB - `push-strategist-service` 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [用户增长/推荐算法负责人]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台内主动地、智能地向用户传递高价值信息，以提升用户留存、活跃度和商业转化，需要一个专门的系统来决策**“什么内容、在什么时候、以何种方式、推送给哪些最相关的用户”**。`push-strategist-service` 的目的在于构建这样一个**智能推送决策中枢**。它通过消费平台内的各种实时事件，结合用户画像和业务规则，智能地筛选出目标受众，并生成最终的推送指令，交由`notification-dispatch-service`执行。

#### 1.2. 服务范围
本服务 **负责**:
*   **多源触发器监听**:
    *   **事件驱动**: 消费来自平台事件总线的各类高价值事件（如`VideoBecameHotEvent`, `UserFollowedYouEvent`, `MajorNewsPublishedEvent`）。
    *   **时间驱动**: 支持由Cron Job触发的、周期性的推送任务（如“每日精选”）。
*   **推送价值评估**: 对每个潜在的推送内容，根据业务规则评估其推送价值和适宜的推送范围。
*   **智能受众筛选 (Audience Selection)**:
    *   **核心职责**: 根据推送内容和用户画像，从全量用户中筛选出一批**最可能感兴趣**的目标用户。
    *   支持多种筛选策略，如“兴趣标签匹配”、“协同过滤找相似人群”、“关注关系”等。
*   **防打扰与合规性过滤**:
    *   **必须**与`user-core-service`协同，检查并遵守用户的通知偏好设置。
    *   **必须**执行平台级的频率控制（如“一个用户每天最多收到N条营销推送”）和静默时段策略。
*   **推送指令生成与下发**:
    *   对于通过所有检查的目标用户，生成标准化的`DispatchNotificationCommand`。
    *   将该指令发布到消息队列，供`notification-dispatch-service`消费和执行。

本服务 **不负责**:
*   **实际的推送发送**: 完全由`notification-dispatch-service`负责。
*   **用户画像和物料特征的生成**: 由数据平台和特征存储服务负责。
*   **召回和排序算法的实现**: 但会**调用**`recall-service`等推荐服务来实现受众筛选。
*   **提供面向最终用户的API**。

#### 1.3. 目标用户/调用方
*   **平台事件总线 (Kafka)**: (主要输入) 是本服务的主要触发源。
*   **后台调度系统 (Cron Job)**: 触发周期性的推送任务。
*   **`notification-dispatch-service`**: (主要输出目标，通过Kafka)。
*   **`recall-service`, `user-core-service`等**: (被本服务调用) 获取数据以支持决策。
*   **CINA.CLUB运营团队**: (管理用户) 通过后台配置推送策略和任务。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`push-strategist-service` 是平台**主动用户触达的“决策大脑”**。它位于信息产生（如爆款视频）和信息触达（如手机推送）之间，扮演着“**智能筛选器**”和“**策略执行官**”的角色。它将“广播给所有人”的粗放式运营，升级为“只推送给最相关的人”的精细化、个性化运营，其决策质量直接影响到推送的打开率、用户满意度和平台的商业目标。

#### 2.2. 主要功能概述
*   事件驱动和时间驱动的混合式触发模型。
*   基于推荐算法和业务规则的智能受众圈好的，遵照您的指示。我将为您生成一份专门针对 **`push-strategist-service` (推送策略服务)** 的、极致细化的、生产级**软件需求规格说明书（SRS）**。

这份文档将详细定义这个新增选。
*   严格的、以用户为中心的防打扰和合规性过滤。
*   将复杂的决策逻辑，封装为对下游通知服务的简单指令。

---

### 3. 核心流程图

#### 3.1. 处理一个“爆款视频诞生”事件并决策推送

```mermaid
sequenceDiagram
    的、作为主动内容触达“决策中心”的服务的功能、接口、数据依赖、性能和可靠性需求，作为推荐和运营系统开发的核心依据。

---
### CINA.CLUB - `push-strategist-service` participant Kafka
    participant PushStrategist as push-strategist-service
    participant RecallService as recall-service
    participant UserCore as user-core-service
    participant Redis (for Rate Limit)
    participant NotificationDispatch as notification-dispatch-service

    Kafka-->>PushStrategist: 1. Consume VideoBecameHotEvent (videoId: 'v123')
    
    PushStrategist->>PushStrategist: 2. **[价值评估]**<br/>- 需求规格说明书

**版本: 1.0 (生产级定义)**  
**发布日期: 2025-06-27**  
**文档负责人:** [用户增长/推荐算法负责人]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言检查视频是否已过时<br/>- 检查是否已推送过
    
    PushStrategist->>RecallService: 3. **[受众圈选]**<br/>"Find users interested in video v123" (e.g., via I2U CF)
    RecallService-->>PushStrategist: (返回一批候选用户ID)
    
    PushStrategist->>UserCore: 4. **[偏好过滤]**<br/>批量检查候选用户的通知开关
    UserCore-->>PushStrategist: (返回允许推送的用户ID)
    
    PushStrategist->>Redis: 5. **[频率过滤]**<br/>批量检查这些用户的今日已推送次数
    Redis-->>PushStrategist: (返回未达上限的用户ID)
    
    note over PushStrategist: 最终确定目标用户列表 TargetUserIDs
    
    PushStrategist->>Kafka: 6. **

#### 1.1. 项目背景与目的
为了在CINA.CLUB平台主动地、智能地将高价值内容触达给最相关的用户，以提升用户留存、活跃度和平台事件的参与度，需要一个专门的系统来决策**“什么内容，在什么时候，推送给哪些用户”**。`push-strategist-service` 的目的在于构建这样一个**智能推送决策中枢**。它通过消费平台内的实时事件、结合用户画像和业务规则，筛选出有价值的推送机会，并为这些机会匹配最合适的目标受众，最终调用`notification-dispatch-service`来执行推送。

#### 1.2. 服务范围
本服务 **负责**:
*   **推送机会发现**:
    *   **事件驱动**: 消费平台事件总线中的各类事件（如`VideoPublished[生成并发布指令]**<br/>For each user in TargetUserIDs,<br/>Publish DispatchNotificationCommand
    
    Kafka-->>NotificationDispatch: 7. 消费指令并执行推送
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 触发器管理
*   **FR4.1.1 (事件触发器)**:
    *   系统**必须**能消费多种预定义的平台领域事件。
    *   必须有一个可配置的**事件到策略的映射**，定义了哪种事件应触发哪个推送策略。
*   **FR4.1.2 (定时触发器)**:
    *   必须支持由外部Cron Job触发的、周期性的推送任务。
    *   管理员应能通过后台配置这些任务（如“每日上午10点执行‘每日精选’推送策略”）。

#### 4.2. 推送策略与受众圈选
*   **FR4.2.1 (策略定义)**: 系统必须支持定义多种**推送策略(Push Strategy)**。每个策略是一个包含多个Event`, `LiveStartedEvent`, `MajorNewsEvent`），识别出潜在的、有推送价值的内容或事件。
    *   **时间驱动**: 支持通过定时任务（Cron）触发周期性的推送活动（如“每日精选”）。
*   **目标受众选择 (Audience Selection)**:
    *   对于每个推送机会，调用`recall-service`或直接查询用户画像库，筛选出最可能对此内容感兴趣的目标用户群。
    *   支持基于用户标签、地理位置、历史行为等多种维度的圈人逻辑。
*   **推送策略与防打扰**:
    *   应用一系列业务规则，对目标用户群进行二次过滤。
    *   **必须**与`notification-dispatch-service`协同，遵守用户的全局通知偏好、频率控制和静默时段。
*   **推送任务生成与下发**:
    *   步骤的工作流。
*   **FR4.2.2 (受众圈选 - Audience Selection)**: **这是策略的核心**。必须支持多种圈选用户的能力：
    *   **基于规则**: 如“所有VIP等级大于3的用户”、“过去7天内未登录的用户”。
    *   **基于关系**: 如“某个视频作者的所有粉丝”。通过调用`social-service`。
    *   **基于兴趣 (算法驱动)**: **必须**能调用`recall-service`，执行如“与物料X相似的用户(I2U)”、“与用户Y相似的用户(U2U)”等复杂的算法召回。
*   **FR4.2.3 (A/B测试支持)**: 在圈选出受众后，能将受众随机分为实验组和对照组（不发送），或将不同组路由到不同的推送文案模板。

#### 4.3. 防为最终筛选出的用户和内容，生成一个标准化的`DispatchNotificationCommand`。
    *   将该指令发布到消息队列，由`notification-dispatch-service`消费并执行。
*   **效果追踪与反馈**: 记录每次推送决策的日志，以便进行后续的A/B测试分析和效果归因。

本服务 **不负责**:
*   **实际的推送执行**: 如与APNS, FCM的交互，由`notification-dispatch-service`负责。
*   **用户画像的计算**: 由`analytics-service`和数据平台负责。
*   **召回算法的实现**: 由`recall-service`负责。
*   **提供面向最终用户的API**。

#### 1.3. 目标用户/调用方
*   **平台事件总线 (Kafka)**: (主要输入) 本服务是平台各类事件的消费者。
*   **后台调度系统 (Cron)**: (主要输入) 定时触发周期性推送任务。
*   **`notification打扰与合规性过滤层
在生成最终推送列表前，**必须**经过一个强制的过滤层。
*   **FR4.3.1 (用户偏好)**: **必须**调用`user-core-service`，批量过滤掉那些在设置中关闭了此类通知的用户。
*   **FR4.3.2 (频率控制)**: **必须**实现一个基于Redis的、可配置的全局/分类频率控制器。例如：
    *   `Key: push_freq:user_id:{YYYY-MM-DD}`, `Type: Counter`，记录用户当天收到的总推送数。
    *   `Key: push_freq:user_id:marketing:{YYYY-MM-DD}`, `Type: Counter`，记录用户当天收到的营销类推送数。
*   **FR4.3.3 (静默时段)**: **必须**调用`user-core-service`获取用户的时区，并检查当前时间是否处于用户的静默时段内。

#### 4.4. 推送指令生成
*   **FR4.4.1 (标准化指令)**: 对于最终确定要推送的-dispatch-service` (通过Kafka)**: (主要输出) 本服务是其推送指令的生产者。
*   **`recall-service`, `user-core-service`等**: (被本服务调用) 用于获取用户和物料信息以支持决策。
*   **CINA.CLUB运营团队**: (管理用户) 通过管理后台配置推送策略和活动。

---

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`push-strategist-service` 是平台**主动用户触达的“决策大脑”**每个用户，**必须**生成一个标准的`DispatchNotificationCommand` Protobuf消息。
*   **FR4.4.2 (个性化内容)**: 指令中**必须**包含用于个性化模板渲染的`template_context`。例如，`{"video_title": "...", "author_name": "..."}`。
*   **FR4.4.3 (批量发布)**: **必须**将生成的多个指令，批量地发布到Kafka的`hina_notification_requests` Topic中。

---

### 5. 接口需求 (Interface Requirements)

#### 5.1. 消息队列事件契约 (主要接口)
*   **入站Topic**: `platform-events` (消费多种事件), `push-tasks` (接收定时任务指令)。
*   **出站Topic**: `hina_notification_requests`。。它位于内容产生和通知执行之间，扮演着“**机会捕手**”、“**人群圈选专家**”和“**智能调度员**”的角色。它将“广播给所有人”的粗放式推送，升级为“只把对的内容在对的时间给对的人”的**精细化、个性化运营**，是提升用户体验、避免用户流失的关键服务。

#### 2.2. 主要功能概述
*   事件驱动和时间驱动的混合式推送机会发现。
*   基于用户画像和召回服务的高级目标受众选择。
*   内置防打扰和业务规则的、可配置的推送策略引擎。
*   与通知分发服务的解耦协同。
*   完整的决策日志，支持效果分析。

---

### 3. 核心流程图

#### 3.1. 处理一个“爆款视频诞生”的事件驱动推送流程

```mermaid
sequenceDiagram
    participant Kafka
    participant PushStrategist as push-strategist-service
    participant UserProfileStore as "User Profile Store"
    participant RecallService as recall-service
    participant NotificationSvc as notification-dispatch-service
    
    Kafka-->>PushStrategist: 1. Consume HotVideoCreatedEvent (videoId: 'v123')
    
    PushStrategist->>PushStrategist: 2. **[价值评估]** 判断视频是否适合推送 (e.g., 非小众)
    
    alt "值得推送"
        PushStrategist->>RecallService: 3. **[受众选择]** RequestCandidates(seed_item: 'v123', strategy: 'i2i_for_push')
        Note right

#### 5.2. 管理后台API接口
*   **版本**: `/api/v1/push-strategist`
*   **核心端点**:
    *   `GET /strategies`: 查看所有推送策略。
    *   `POST /strategies`: 创建/更新一个推送策略（定义其触发器、受众圈选逻辑、防打扰规则等）。
    *   `POST /tasks/trigger`: 手动触发一个指定的推送策略（用于测试或运营活动）。

---

### 6. 数据需求 (Data Requirements)

* of RecallService: 找出与v123相似的用户
        RecallService-->>PushStrategist: 4. (返回~10000个高兴趣用户ID)
        
        PushStrategist->>PushStrategist: 5. **[策略过滤]**
        Note right of PushStrategist: - 过滤掉已看过该视频的用户<br/>- 过滤掉今日推送已达上限的用户<br/>- 过滤掉处于静默时段的用户
        
        PushStrategist->>PushStrategist: 6. 得到最终目标用户列表 (e.g., 5000人)
        
        PushStrategist->>Kafka: 7. **[下发任务]** 为这5000人批量发布DispatchNotificationCommand
    end

    Kafka-->>NotificationSvc: 8. 消费指令，执行最终的手机推送
```

---

### 4. 功能需求 (Functional Requirements)

#### 4.1. 推送触发器
*   **FR4.1.1 (事件触发器)**: 系统**必须**能消费   **核心持久化数据库 (PostgreSQL)**:
    *   **`push_strategies`**: `id`, `name`, `trigger_type`, `audience_selection_config (JSONB)`, `anti_disturbance_rules (JSONB)`.
    *   **`push_task_logs`**: 记录每次推送任务的执行情况，如`task_id`, `strategy_id`, `start_time`, `status`, `target_audience_size`, `final_sent_count`.
*   **缓存与状态存储 (Redis)**:
    *   **频率控制计数器**: 如**FR4.3.2**所述。

---

### 7. 非功能性需求

*   **NFR7.1 (性能与吞吐量)**:
    *   **事件处理延迟**: 从消费到一个事件到发布最终的推送指令，P99延迟应 `< 5秒`（主要耗时在受众圈选的API调用上）。
    *   **吞吐量**: 能够处理平台的事件洪峰，并能为一次大型推送任务（如面向百万用户）在数分钟内完成所有决策和指令下发。
*   **NFR7.2 (可靠性与可用性)**:
    *   **可用性**: > 99.9%。
    *   **容错**: 对所有下游服务（`recall-service`, `user-core-service`）的调用失败必须有健壮的重试和降级逻辑。
*   **NFR7.3 (可扩展性)**: 服务作为Kafka消费者，可以通过增加实例来水平扩展。

---

### 8. 技术约束与选型建议
*   **语言**: Go。其高并发特性非常适合处理事件流和并行地调用多个外部服务。
*   **核心架构**: 事件驱动的消费者/处理器模式。
*   **策略实现**: 可以将每个“受众圈选”逻辑封装成一个实现了`AudienceProvider`接口的策略，通过工厂模式进行调用。

---
这份SRS为`push-strategist-service`的设计和实现提供了坚实、全面的指导。通过构建这样一个智能、精细、且以用户为中心的推送决策引擎，CINA.CLUB平台能够将主动触达能力从一种可能的“骚扰”转变为一种受欢迎的“个性化服务”，从而在提升业务指标和用户体验之间找到最佳平衡。