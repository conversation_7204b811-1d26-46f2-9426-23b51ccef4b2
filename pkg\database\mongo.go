/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 11:00:00
Modified: 2025-01-21 11:00:00
*/

package database

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
	"go.opentelemetry.io/otel/trace"

	"cina.club/pkg/database/internal/instrument"
)

// MongoConfig defines configuration for MongoDB client
type MongoConfig struct {
	// Connection
	URI      string `mapstructure:"uri" validate:"required"`
	Database string `mapstructure:"database" validate:"required"`

	// Connection Pool
	MaxPoolSize     uint64        `mapstructure:"max_pool_size" validate:"gte=1" default:"100"`
	MinPoolSize     uint64        `mapstructure:"min_pool_size" validate:"gte=0" default:"0"`
	MaxConnIdleTime time.Duration `mapstructure:"max_conn_idle_time" default:"30m"`

	// Timeouts
	ConnectTimeout         time.Duration `mapstructure:"connect_timeout" default:"10s"`
	ServerSelectionTimeout time.Duration `mapstructure:"server_selection_timeout" default:"30s"`
	SocketTimeout          time.Duration `mapstructure:"socket_timeout" default:"30s"`

	// Read Preference
	ReadPreference string `mapstructure:"read_preference" validate:"oneof=primary primaryPreferred secondary secondaryPreferred nearest" default:"primary"`

	// Write Concern
	WriteConcern string        `mapstructure:"write_concern" default:"majority"`
	WTimeout     time.Duration `mapstructure:"w_timeout" default:"10s"`

	// TLS
	TLSEnabled bool `mapstructure:"tls_enabled" default:"false"`
}

// NewMongoClient creates a new MongoDB client with built-in observability.
// This function automatically integrates OpenTelemetry tracing and structured logging
// for all MongoDB operations performed through the returned client.
//
// Parameters:
//   - ctx: Context for timeout and cancellation
//   - cfg: MongoDB configuration including URI, database name, and connection settings
//   - logger: Structured logger for MongoDB operation logs
//   - tracer: OpenTelemetry tracer for distributed tracing
//
// Returns a *mongo.Client that is ready to use with integrated observability,
// or an error if connection or configuration fails.
func NewMongoClient(ctx context.Context, cfg MongoConfig, logger *slog.Logger, tracer trace.Tracer) (*mongo.Client, error) {
	// Create client options from configuration
	clientOpts := options.Client().ApplyURI(cfg.URI)

	// Configure connection pool
	clientOpts.SetMaxPoolSize(cfg.MaxPoolSize)
	clientOpts.SetMinPoolSize(cfg.MinPoolSize)
	clientOpts.SetMaxConnIdleTime(cfg.MaxConnIdleTime)

	// Configure timeouts
	clientOpts.SetConnectTimeout(cfg.ConnectTimeout)
	clientOpts.SetServerSelectionTimeout(cfg.ServerSelectionTimeout)
	clientOpts.SetSocketTimeout(cfg.SocketTimeout)

	// Configure read preference
	readPref, err := parseReadPreference(cfg.ReadPreference)
	if err != nil {
		return nil, fmt.Errorf("invalid read preference: %w", err)
	}
	clientOpts.SetReadPreference(readPref)

	// Configure write concern
	writeConcern, err := parseWriteConcern(cfg.WriteConcern, cfg.WTimeout)
	if err != nil {
		return nil, fmt.Errorf("invalid write concern: %w", err)
	}
	clientOpts.SetWriteConcern(writeConcern)

	// Integrate observability - this is the core value of this package
	if tracer != nil && logger != nil {
		// Create and set our custom command monitor
		commandMonitor := instrument.NewMongoCommandMonitor(tracer, logger)
		clientOpts.SetMonitor(commandMonitor)
	}

	// Create MongoDB client
	client, err := mongo.Connect(ctx, clientOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// Perform health check to ensure the connection is working
	if err := performMongoHealthCheck(ctx, client, cfg.Database, logger); err != nil {
		client.Disconnect(ctx)
		return nil, fmt.Errorf("mongo health check failed: %w", err)
	}

	logger.Info("MongoDB client created successfully",
		"database", cfg.Database,
		"max_pool_size", cfg.MaxPoolSize,
		"read_preference", cfg.ReadPreference,
		"write_concern", cfg.WriteConcern,
		"tls_enabled", cfg.TLSEnabled)

	return client, nil
}

// performMongoHealthCheck verifies the MongoDB connection is healthy
func performMongoHealthCheck(ctx context.Context, client *mongo.Client, dbName string, logger *slog.Logger) error {
	// Create a timeout context for the health check
	healthCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Ping the database
	if err := client.Ping(healthCtx, readpref.Primary()); err != nil {
		logger.Error("MongoDB ping failed", "error", err)
		return fmt.Errorf("ping failed: %w", err)
	}

	// Test basic database operations
	database := client.Database(dbName)

	// Test collection creation/access
	testCollection := database.Collection("health_check_test")

	// Insert a test document
	testDoc := map[string]interface{}{
		"test":      true,
		"timestamp": time.Now(),
	}

	insertResult, err := testCollection.InsertOne(healthCtx, testDoc)
	if err != nil {
		logger.Error("MongoDB insert health check failed", "error", err)
		return fmt.Errorf("insert operation failed: %w", err)
	}

	// Find the test document
	var result map[string]interface{}
	err = testCollection.FindOne(healthCtx, map[string]interface{}{"_id": insertResult.InsertedID}).Decode(&result)
	if err != nil {
		logger.Error("MongoDB find health check failed", "error", err)
		return fmt.Errorf("find operation failed: %w", err)
	}

	// Clean up test document
	testCollection.DeleteOne(healthCtx, map[string]interface{}{"_id": insertResult.InsertedID})

	logger.Debug("MongoDB health check passed")
	return nil
}

// MongoClient is a convenience wrapper that includes both the client and logger
// for easier dependency injection in services
type MongoClient struct {
	Client   *mongo.Client
	Database *mongo.Database
	Logger   *slog.Logger
}

// Close gracefully closes the MongoDB client
func (m *MongoClient) Close(ctx context.Context) error {
	if m.Client != nil {
		err := m.Client.Disconnect(ctx)
		if m.Logger != nil {
			if err != nil {
				m.Logger.Error("MongoDB client disconnect failed", "error", err)
			} else {
				m.Logger.Info("MongoDB client disconnected successfully")
			}
		}
		return err
	}
	return nil
}

// NewMongoClientWithWrapper creates a MongoDB client wrapped with additional utilities
func NewMongoClientWithWrapper(ctx context.Context, cfg MongoConfig, logger *slog.Logger, tracer trace.Tracer) (*MongoClient, error) {
	client, err := NewMongoClient(ctx, cfg, logger, tracer)
	if err != nil {
		return nil, err
	}

	database := client.Database(cfg.Database)

	return &MongoClient{
		Client:   client,
		Database: database,
		Logger:   logger,
	}, nil
}

// Ping performs a health check on the MongoDB connection
func (m *MongoClient) Ping(ctx context.Context) error {
	if m.Client == nil {
		return fmt.Errorf("mongo client is nil")
	}

	return m.Client.Ping(ctx, readpref.Primary())
}

// WithTransaction provides a helper for executing operations within a transaction
func (m *MongoClient) WithTransaction(ctx context.Context, fn func(mongo.SessionContext) (interface{}, error)) (interface{}, error) {
	if m.Client == nil {
		return nil, fmt.Errorf("mongo client is nil")
	}

	session, err := m.Client.StartSession()
	if err != nil {
		return nil, fmt.Errorf("failed to start session: %w", err)
	}
	defer session.EndSession(ctx)

	return session.WithTransaction(ctx, fn)
}

// Collection returns a collection from the default database
func (m *MongoClient) Collection(name string) *mongo.Collection {
	if m.Database == nil {
		return nil
	}
	return m.Database.Collection(name)
}

// parseReadPreference converts string read preference to MongoDB read preference
func parseReadPreference(readPref string) (*readpref.ReadPref, error) {
	switch readPref {
	case "primary":
		return readpref.Primary(), nil
	case "primaryPreferred":
		return readpref.PrimaryPreferred(), nil
	case "secondary":
		return readpref.Secondary(), nil
	case "secondaryPreferred":
		return readpref.SecondaryPreferred(), nil
	case "nearest":
		return readpref.Nearest(), nil
	default:
		return nil, fmt.Errorf("unknown read preference: %s", readPref)
	}
}

// parseWriteConcern converts string write concern to MongoDB write concern
func parseWriteConcern(wc string, timeout time.Duration) (*writeconcern.WriteConcern, error) {
	switch wc {
	case "majority":
		return writeconcern.New(writeconcern.WMajority(), writeconcern.WTimeout(timeout)), nil
	case "1":
		return writeconcern.New(writeconcern.W(1), writeconcern.WTimeout(timeout)), nil
	case "0":
		return writeconcern.New(writeconcern.W(0)), nil
	default:
		// Try to parse as number
		var w int
		if _, err := fmt.Sscanf(wc, "%d", &w); err == nil {
			return writeconcern.New(writeconcern.W(w), writeconcern.WTimeout(timeout)), nil
		}
		return nil, fmt.Errorf("unknown write concern: %s", wc)
	}
}
