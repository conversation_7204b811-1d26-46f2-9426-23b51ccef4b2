﻿
这份文档将基于我们之前的讨论，并深度融合类似 **n8n** 或 **Zapier** 的**工作流编排（Orchestration）**理念，同时支持多模态输入和多AI后端，将其定位为CINA.CLUB平台真正的“智能中枢”。

---

### CINA.CLUB - ai-assistant-service 需求规格说明书

**版本: 2.0 (生产级定义，集成工作流编排引擎)**  
**发布日期: 2025-06-18**  
**最后修订日期: 2025-06-18**  
**文档负责人:** [AI平台/CINA.CLUB]  
**审批人:** [CTO/CINA.CLUB]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图: Agentic Workflow](#3-核心流程图-agentic-workflow)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性需求-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
`ai-assistant-service` 是CINA.CLUB平台的核心智能引擎。其目的在于提供一个高性能、高并发、可扩展的后端服务，负责处理所有与AI助手的多模态交互。它不仅仅是一个聊天机器人，更是一个强大的**智能代理（Agent）和工作流编排器**，能够理解用户复杂的、跨领域的自然语言指令，动态规划并执行一系列动作（调用大语言模型、内部微服务API、或第三方工具），最终为用户提供智能、个性化且具有行动能力的解决方案。

#### 1.2. 服务范围
本服务 **负责**:
*   **多模态输入处理**: 接收来自客户端的标准化AI请求，包含文本、以及图片/音频/视频的引用。
*   **对话管理**: 维持多轮对话的短期上下文和状态。
*   **NLU与意图规划 (NLU & Intent Planning)**: 深度理解用户意图，并将其分解为一系列可执行的步骤（工作流计划）。
*   **工作流编排与执行 (Workflow Orchestration & Execution)**:
    *   像 **n8n** 一样，动态地、按顺序或并行地执行计划中的节点。
    *   每个节点代表一个“工具调用”。
*   **工具箱管理 (Toolkit Management)**:
    *   **LLM工具**: 封装和管理对多个外部大语言模型（OpenAI, Google Gemini, Anthropic Claude）和本地部署模型（通过`model-management-service`分发）的调用。
    *   **MCP工具 (Microservice Connection Points)**: 封装对CINA.CLUB所有内部微服务API的安全调用。
    *   **第三方工具**: （未来）封装对外部API（如天气、股票、新闻）的调用。
*   **响应生成**: 整合所有工具调用的结果，生成最终的、富文本或结构化的用户回复。
*   **个性化集成**: 调用`user-core-service`, `memory-service`, `personal-kb-service`以实现深度个性化。
*   **AI伦理与安全护栏**: 实施严格的输入/输出过滤和安全控制。

本服务 **不负责**:
*   训练或微调AI模型 (由上游ML平台负责)。
*   存储用户长期记忆(PM)或个人知识库(PKB) (由`memory-service`和`personal-kb-service`负责)。
*   直接的WebSocket连接管理 (由`chat-websocket-server`负责，本服务提供请求-响应式或流式的API)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 通过API Gateway调用本服务的`/chat`等核心端点。
*   **`routines-service`**: 调用本服务以执行AI相关的动作。
*   **其他内部微服务 (可选)**: 如`digital-twin-service`需要AI进行Persona分析。
*   **CINA.CLUB管理后台**: 用于监控AI交互、调试、查看分析报告。

#### 1.4. 定义与缩略语
*   **Agent**: 智能代理，具备规划、使用工具、并根据结果反思的能力。
*   **Workflow/Plan**: 由Agent生成的一系列有序的、待执行的“工具调用”节点。
*   **Tool**: AI可以调用的一个外部能力，如调用LLM、查询数据库、调用一个API。
*   **MCP**: Microservice Connection Point，特指调用CINA.CLUB内部微服务的工具。
*   **n8n**: 一个流行的、基于节点的可视化工作流自动化工具，本服务在概念上借鉴其编排思想。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`ai-assistant-service` 是平台的“中央处理单元(CPU)”和“总调度室”。它位于用户自然语言输入与平台所有结构化功能之间，不拥有核心业务数据，而是作为这些数据的智能**调度员、编排者和加工者**。它通过编排所有其他微服务的能力，为用户提供统一、智能的交互入口。

#### 2.2. 主要功能概述
*   多模态、多轮对话管理。
*   基于LLM的动态工作流规划与编排。
*   可扩展的、包含内部MCP和外部LLM的统一工具箱。
*   与平台内部知识、记忆、业务服务的深度集成。
*   支持流式响应，提供极致的用户体验。

### 3. 核心流程图: Agentic Workflow
```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant AIAssistantService as AIAS
    participant LLM_Planner as "LLM (for Planning)"
    participant ToolExecutor as "Tool Executor"
    participant OtherServices as "Other Microservices (MCPs)"

    Client->>API Gateway: POST /chat (text: "帮我找下周在北京的3个摄影服务，并创建一个日程提醒")
    API Gateway->>AIAS: (forward request)
    
    AIAS->>LLM_Planner: 1. **[Planning]** Generate a step-by-step plan based on user query and available tools.
    LLM_Planner-->>AIAS: **Plan:** <br/> 1. Call `search-service` with query="摄影服务", location="北京". <br/> 2. Call `schedule-service` to find available slots for next week. <br/> 3. Format results. <br/> 4. Ask user for confirmation. <br/> 5. If confirmed, call `schedule-service` to create reminder.

    Note over AIAS: **[Orchestration]** Start executing the plan...

    AIAS->>ToolExecutor: 2. Execute Step 1: Search Services
    ToolExecutor->>OtherServices: Call `search-service`.GET("/?q=摄影服务&loc=北京")
    OtherServices-->>ToolExecutor: (Search Results)
    ToolExecutor-->>AIAS: (Formatted Search Results)

    AIAS->>ToolExecutor: 3. Execute Step 2: Check Availability
    ToolExecutor->>OtherServices: Call `schedule-service`.GET("/availability?...")
    OtherServices-->>ToolExecutor: (Availability Info)
    ToolExecutor-->>AIAS: (Formatted Availability)

    AIAS->>LLM_Planner: 4. **[Synthesis]** Synthesize results and generate response for user.
    LLM_Planner-->>AIAS: **Response:** "我为您找到了以下3个在北京的摄影服务，它们下周都有空闲时间...您希望我为您创建提醒吗？"

    AIAS-->>Client: **(Streaming Response)** "我为您找到了..."
    
    Note over Client: User confirms.
    
    Client->>API Gateway: POST /chat (text: "好的，就提醒第一个")
    API Gateway->>AIAS: (forward request, with session context)
    
    Note over AIAS: **[Continue Execution]** Resume plan...

    AIAS->>ToolExecutor: 5. Execute Step 5: Create Reminder
    ToolExecutor->>OtherServices: Call `schedule-service`.POST("/me", {title:"摄影服务提醒", ...})
    OtherServices-->>ToolExecutor: (Success)
    ToolExecutor-->>AIAS: (Success)

    AIAS->>LLM_Planner: 6. **[Final Response]**
    LLM_Planner-->>AIAS: "好的，提醒已为您创建成功。"
    AIAS-->>Client: **(Streaming Response)** "好的，提醒..."
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. AI交互与上下文管理
*   **FR4.1.1 (多模态请求)**: 系统必须提供API，接收包含`text`、`sessionId`、以及可选的`media_attachments` (包含`media_type`和`url/key`)的请求。
*   **FR4.1.2 (对话状态管理)**: 系统必须使用`sessionId`在Redis中追踪和管理多轮对话的状态，包括对话历史、当前执行计划、已执行步骤的结果和中间变量。此状态必须有TTL。
*   **FR4.1.3 (上下文窗口)**: 必须实现智能的上下文窗口管理策略（如滑动窗口、摘要），以在保持对话连贯性的同时避免超出LLM的token限制。

#### 4.2. 工作流规划与编排引擎 (n8n-like Core)
*   **FR4.2.1 (动态规划)**: 对于每个新查询，系统必须调用一个LLM（作为“规划师”）来分析用户意图，并根据已注册的工具集，生成一个结构化的、分步骤的执行计划（Workflow Plan）。
*   **FR4.2.2 (计划表示)**: 计划应为JSON格式，包含一个节点列表。每个节点定义了要调用的`tool_id`、输入参数（可以引用前序节点的输出或用户原始输入）、以及错误处理策略。
*   **FR4.2.3 (编排执行)**: 系统必须有一个工作流编排器，能按顺序（或未来支持并行）执行计划中的节点。编排器负责：
    *   传递上下文和参数给每个节点。
    *   接收节点的输出。
    *   根据计划的逻辑流转到下一个节点。
    *   处理暂停（如等待用户输入）和恢复。
*   **FR4.2.4 (迭代与反思 - ReAct模式)**: 系统必须支持多步工具调用和反思的循环。如果一个工具执行失败或结果不满足需求，Agent可以决定调用另一个工具或向用户提问，动态地调整后续计划。

#### 4.3. 统一工具箱 (Unified Toolkit)
*   **FR4.3.1 (工具注册与发现)**: 系统内部必须有一个可扩展的工具注册表。每个工具都必须有清晰的元数据定义（如`tool_id`, `description`, `input_schema`, `output_schema`），以便“规划师”LLM能够理解和选择。
*   **FR4.3.2 (LLM工具)**:
    *   提供对不同LLM提供商（OpenAI, Google, Anthropic等）的抽象调用。
    *   支持模型选择、流式响应、以及token用量统计。
*   **FR4.3.3 (MCP工具 - 内部微服务)**:
    *   为每个需要被AI调用的内部微服务API封装一个MCP工具。
    *   MCP工具负责处理S2S认证、API调用、错误处理和结果转换。
    *   示例: `SearchServicesTool`, `CreateScheduleTool`, `QueryUserMemoryTool`.
*   **FR4.3.4 (本地模型工具 - 可选)**: 对于部署在端侧或私有云的模型，通过`model-management-service`获取模型信息并进行调用。

#### 4.4. 个性化与安全
*   **FR4.4.1 (个性化集成)**: 在规划和执行的每一步，系统都必须有能力调用`user-core-service`, `memory-service`, `personal-kb-service`来获取用户偏好、长期记忆和私有知识，并将这些信息作为决策的输入。
*   **FR4.4.2 (AI安全护栏)**:
    *   **输入过滤**: 拦截明显的恶意指令（Prompt Injection）。
    *   **工具调用沙箱**: 对LLM生成的工具调用参数进行严格的校验，防止其执行非预期的或破坏性的操作。
    *   **输出过滤**: 在将LLM生成的任何内容返回给用户之前，必须通过一个安全过滤层，检查并处理有害、有偏见的内容。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端RESTful API接口
*   **版本**: `/api/v1/ai`
*   **认证**: 用户JWT。
*   **核心端点**:
    *   `POST /chat/completions`: 主交互端点。
        *   **Request Body**: `AIChatRequest { session_id?: string, messages: [{role, content: [{type, text?, image_url?}] }] }` (类似OpenAI的格式)
        *   **Response**:
            *   非流式: `200 OK` - `AIChatResponse`。
            *   流式: `Content-Type: text/event-stream`，持续推送JSON数据块。

#### 5.2. 与其他微服务的接口 (出站)
本服务是平台中调用其他微服务最多的服务，通过gRPC+S2S认证调用几乎所有其他服务的内部API。

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (内存中 & Redis)
*   **`DialogState`**: `session_id`, `user_id`, `message_history`, `current_plan_json`, `execution_step`, `intermediate_results_map`.
*   **`ToolDefinition`**: `tool_id`, `description`, `input_json_schema`, `output_json_schema`.

#### 6.2. 数据持久化与存储
*   **短期对话状态 (Redis)**:
    *   `Key`: `ai_session:{sessionId}`
    *   `Value`: `DialogState`的JSON/MessagePack序列化字符串。
    *   必须设置合理的TTL（如24小时）。
*   **无核心业务数据库**: 本服务不拥有核心业务数据。所有需要持久化的信息都通过调用其他服务来完成。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**:
    *   对于简单请求，P99延迟 < 200ms（不含LLM时间）。
    *   流式响应的“首字节时间 (Time to First Byte)” P99 < 1秒。
*   **吞吐量**: 能够通过水平扩展处理高并发的聊天请求。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.9%。
*   **容错**: 对所有下游服务（LLM API, 内部MCP）的调用必须有健壮的超时、重试和熔断机制。当某个工具不可用时，Agent应能优雅地降级并告知用户。

#### 7.3. 可扩展性需求
*   服务必须是无状态的（状态在Redis中），易于水平扩展。
*   工作流执行引擎应能处理大量并发的、长时间运行（等待用户输入）的会话。

#### 7.4. 安全性需求 (最高优先级)
*   严格执行**FR4.4.2**中定义的AI安全护栏。
*   保护所有外部API Key和S2S认证凭证。
*   防止因工作流设计不当导致的无限循环或资源滥用。

#### 7.5. 可维护性与可观测性
*   **可维护性**:
    *   Agentic/工作流架构将对话流程分解为独立的、可测试的节点和工具。
    *   工具的抽象与封装使得添加新功能变得简单。
    *   Prompt模板应与代码逻辑分离，便于迭代和优化。
*   **可观测性**:
    *   **日志**: 记录每个请求的完整生命周期，包括识别出的意图、生成的计划、每个工具的输入输出、LLM的Prompt和响应（脱敏后）。
    *   **指标**: API性能；各意图分布；各工具调用频率、成功率、延迟；各LLM提供商的Token消耗量、延迟、错误率。
    *   **追踪**: 分布式追踪必须覆盖从API接收请求，到Agent的每一步决策，再到所有工具调用的完整链路。

### 8. 技术约束与选型建议
*   **语言**: Go。其强大的并发能力（Goroutines, Channels）非常适合并行执行工具调用和处理流式I/O。
*   **工作流引擎**: 自研一个轻量级的、基于状态机的图执行引擎是核心技术难点。可以借鉴LangChain/LangGraph, n8n, Temporal等框架的思想，但应保持轻量。
*   **与LLM的集成**: 持续关注各提供商API的最新特性，特别是工具使用/函数调用方面的改进。
*   **Prompt工程**: 建立一套系统化的Prompt管理和版本控制机制。

---
这份深度细化的SRS文档，将 `ai-assistant-service` 定位为一个高度智能、具备自主规划和编排能力的平台大脑。它的成功实现将是CINA.CLUB提供革命性用户体验的关键。