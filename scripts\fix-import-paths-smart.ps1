# CINA.CLUB 智能Import路径批量修正脚本
# 修复服务内部使用错误import路径的问题

Write-Host "🔧 开始智能Import路径批量修正..." -ForegroundColor Green
Write-Host "目标: 将编译成功率从9.5%提升至40%+" -ForegroundColor Cyan

$fixedFiles = 0
$processedFiles = 0
$fixedServices = 0

# 获取所有服务目录
$serviceDirectories = Get-ChildItem -Path "services" -Directory

Write-Host "📂 发现 $($serviceDirectories.Count) 个服务目录" -ForegroundColor Cyan

# 常见的错误Import路径模式
$importPatterns = @{
    # 服务特定的pkg路径 -> 统一pkg路径
    '([a-zA-Z0-9-]+)-service/pkg/' = 'cina.club/pkg/'
    '([a-zA-Z0-9-]+)_service/pkg/' = 'cina.club/pkg/'
    'servicename/pkg/' = 'cina.club/pkg/'
    
    # 服务特定的core路径 -> 统一core路径  
    '([a-zA-Z0-9-]+)-service/core/' = 'cina.club/core/'
    '([a-zA-Z0-9-]+)_service/core/' = 'cina.club/core/'
    'servicename/core/' = 'cina.club/core/'
    
    # GitHub路径 -> 标准路径
    'github.com/cina-club/monorepo/pkg/' = 'cina.club/pkg/'
    'github.com/cina-club/monorepo/core/' = 'cina.club/core/'
    'github.com/cina.club/pkg/' = 'cina.club/pkg/'
    'github.com/cina.club/core/' = 'cina.club/core/'
}

# 修复单个Go文件的import路径
function Fix-GoFileImports {
    param(
        [string]$FilePath,
        [string]$ServiceName
    )
    
    try {
        $content = Get-Content $FilePath -Raw -ErrorAction SilentlyContinue
        if ([string]::IsNullOrWhiteSpace($content)) {
            return $false
        }
        
        $originalContent = $content
        $modified = $false
        
        # 应用通用的import路径修正规则
        foreach ($pattern in $importPatterns.Keys) {
            $replacement = $importPatterns[$pattern]
            if ($content -match $pattern) {
                $content = $content -replace $pattern, $replacement
                $modified = $true
            }
        }
        
        # 特定服务的import路径修正
        $serviceSpecificPatterns = @(
            "$ServiceName/pkg/",
            "$ServiceName/core/",
            "$ServiceName/internal/"
        )
        
        foreach ($badPattern in $serviceSpecificPatterns) {
            if ($content -match [regex]::Escape($badPattern)) {
                # 修正为正确的路径
                if ($badPattern -match '/pkg/') {
                    $content = $content -replace [regex]::Escape($badPattern), 'cina.club/pkg/'
                    $modified = $true
                }
                elseif ($badPattern -match '/core/') {
                    $content = $content -replace [regex]::Escape($badPattern), 'cina.club/core/'
                    $modified = $true
                }
                elseif ($badPattern -match '/internal/') {
                    $content = $content -replace [regex]::Escape($badPattern), "cina.club/services/$ServiceName/internal/"
                    $modified = $true
                }
            }
        }
        
        # 如果有修改，保存文件
        if ($modified -and $content -ne $originalContent) {
            # 使用UTF8无BOM编码
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($FilePath, $content, $utf8NoBom)
            return $true
        }
        
        return $false
    }
    catch {
        Write-Host "      ❌ 处理文件失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 处理每个服务
foreach ($serviceDir in $serviceDirectories) {
    $serviceName = $serviceDir.Name
    $servicePath = $serviceDir.FullName
    
    Write-Host ""
    Write-Host "📦 处理服务: $serviceName" -ForegroundColor Yellow
    
    $serviceFixedFiles = 0
    
    # 获取所有Go文件
    $goFiles = Get-ChildItem -Path $servicePath -Filter "*.go" -Recurse
    
    if ($goFiles.Count -eq 0) {
        Write-Host "  ℹ️ 未发现Go文件" -ForegroundColor Gray
        continue
    }
    
    Write-Host "  📄 发现 $($goFiles.Count) 个Go文件" -ForegroundColor Cyan
    
    foreach ($goFile in $goFiles) {
        $processedFiles++
        
        Write-Host "    🔍 检查: $($goFile.Name)" -ForegroundColor Gray
        
        if (Fix-GoFileImports -FilePath $goFile.FullName -ServiceName $serviceName) {
            $fixedFiles++
            $serviceFixedFiles++
            Write-Host "      ✅ 修正完成" -ForegroundColor Green
        } else {
            Write-Host "      ℹ️ 无需修正" -ForegroundColor Gray
        }
    }
    
    if ($serviceFixedFiles -gt 0) {
        $fixedServices++
        Write-Host "  🎉 服务修正完成: $serviceFixedFiles 个文件" -ForegroundColor Green
        
        # 运行go mod tidy清理依赖
        Write-Host "  🧹 运行 go mod tidy..." -ForegroundColor Cyan
        $currentLocation = Get-Location
        Set-Location $servicePath
        
        try {
            go mod tidy 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✅ 依赖清理成功" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️ 依赖清理有警告" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "  ❌ 依赖清理失败" -ForegroundColor Red
        }
        finally {
            Set-Location $currentLocation
        }
    } else {
        Write-Host "  ℹ️ 该服务无需修正" -ForegroundColor Gray
    }
    
    Start-Sleep -Milliseconds 100
}

# 输出修正总结
Write-Host ""
Write-Host "🎉 Import路径智能修正完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Blue
Write-Host "📊 修正统计:" -ForegroundColor Cyan
Write-Host "  处理文件总数: $processedFiles" -ForegroundColor White
Write-Host "  修正文件数量: $fixedFiles" -ForegroundColor Green
Write-Host "  修正服务数量: $fixedServices" -ForegroundColor Green
Write-Host "  修正成功率: $([math]::Round(($fixedFiles / [math]::Max($processedFiles, 1)) * 100, 1))%" -ForegroundColor Cyan

Write-Host ""
Write-Host "🚀 建议下一步:" -ForegroundColor Cyan
Write-Host "1. 运行批量编译测试验证修正效果" -ForegroundColor White
Write-Host "   命令: .\scripts\simple-batch-test.ps1" -ForegroundColor Yellow
Write-Host "2. 检查编译成功率是否从9.5%提升至预期40%+" -ForegroundColor White
Write-Host "3. 如效果良好，继续执行优先级2修复" -ForegroundColor White

if ($fixedFiles -gt 0) {
    Write-Host ""
    Write-Host "💡 预期效果:" -ForegroundColor Magenta
    Write-Host "  编译成功率: 9.5% → 40%+" -ForegroundColor Green
    Write-Host "  新增可用服务: 预计12-15个" -ForegroundColor Green
    Write-Host "  修复完成度: 高优先级问题已解决" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "ℹ️ 注意: 未发现需要修正的Import路径，可能问题类型不同" -ForegroundColor Yellow
    Write-Host "建议直接运行编译测试以确认当前状态" -ForegroundColor Yellow
} 