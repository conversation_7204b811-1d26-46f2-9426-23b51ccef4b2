/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
*/

using System.Threading.Tasks;

namespace CinaClub.Core.Interfaces;

/// <summary>
/// 加密保险库接口
/// 提供端到端加密(E2EE)的核心功能
/// </summary>
public interface ICryptoVault
{
    /// <summary>
    /// 从用户密码派生主加密密钥
    /// </summary>
    /// <param name="password">用户密码</param>
    /// <param name="salt">盐值（可选，用于增强安全性）</param>
    /// <returns>派生的密钥</returns>
    Task<byte[]> DeriveKeyFromPasswordAsync(string password, byte[]? salt = null);

    /// <summary>
    /// 加密数据
    /// </summary>
    /// <param name="key">加密密钥</param>
    /// <param name="plaintext">待加密的明文数据</param>
    /// <returns>加密后的密文数据</returns>
    Task<byte[]> EncryptAsync(byte[] key, byte[] plaintext);

    /// <summary>
    /// 解密数据
    /// </summary>
    /// <param name="key">解密密钥</param>
    /// <param name="ciphertext">待解密的密文数据</param>
    /// <returns>解密后的明文数据</returns>
    Task<byte[]> DecryptAsync(byte[] key, byte[] ciphertext);

    /// <summary>
    /// 生成加密强度的随机密钥
    /// </summary>
    /// <param name="keySize">密钥长度（字节）</param>
    /// <returns>生成的随机密钥</returns>
    Task<byte[]> GenerateRandomKeyAsync(int keySize = 32);

    /// <summary>
    /// 获取Go核心库版本信息
    /// </summary>
    /// <returns>版本字符串</returns>
    string GetVersion();
} 