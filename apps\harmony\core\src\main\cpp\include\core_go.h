/*
 * Copyright (c) 2025 Cina.Club
 * All rights reserved.
 * Created: 2025-06-20 HH:MM:SS
 * Modified: 2025-06-20 HH:MM:SS
 */

#ifndef CORE_GO_H
#define CORE_GO_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Go Slice类型定义（与Go Mobile生成的兼容）
typedef struct {
    void* data;
    int64_t len;
    int64_t cap;
} GoSlice;

typedef struct {
    const char* p;
    int64_t n;
} GoString;

// 加密相关函数（由Go核心库导出）
extern GoSlice GoEncryptSymmetric(GoSlice key, GoSlice plaintext);
extern GoSlice GoDecryptSymmetric(GoSlice key, GoSlice ciphertext);
extern GoSlice GoDeriveKey(GoString password, GoSlice salt);
extern int GoValidateKey(GoSlice key);

// AI相关函数
extern int GoInitializeAIEngine(GoString modelPath);
extern GoSlice GoPredictText(GoString prompt, int maxTokens);
extern int GoStartStreamPredict(GoString prompt, int callbackId);
extern void GoCleanupAIEngine();

// 数据同步相关函数
extern GoSlice GoCreateSyncChunk(GoSlice data, GoString chunkId);
extern GoSlice GoMergeSyncChunks(GoSlice chunks);
extern GoString GoGetSyncVersion(GoString userId);
extern int GoUpdateSyncVersion(GoString userId, GoString version);

// 内存管理
extern void GoFreeSlice(GoSlice slice);
extern void GoFreeString(GoString str);

// 初始化和清理
extern int GoInitializeCore();
extern void GoCleanupCore();

// 错误处理
extern GoString GoGetLastError();

#ifdef __cplusplus
}
#endif

#endif // CORE_GO_H 