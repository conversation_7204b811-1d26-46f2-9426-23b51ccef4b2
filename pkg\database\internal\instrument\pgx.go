/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-21 11:00:00
Modified: 2025-01-21 11:00:00
*/

// Package instrument provides observability integration for database clients.
// It implements tracing, logging, and metrics collection for various database drivers.
package instrument

import (
	"context"
	"log/slog"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/tracelog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// PgxTracer implements pgx.QueryTracer interface to provide OpenTelemetry tracing
// for PostgreSQL operations
type PgxTracer struct {
	tracer trace.Tracer
	logger *slog.Logger
}

// NewPgxTracer creates a new PostgreSQL tracer with observability integration
func NewPgxTracer(tracer trace.Tracer, logger *slog.Logger) *PgxTracer {
	return &PgxTracer{
		tracer: tracer,
		logger: logger,
	}
}

// TraceQueryStart is called at the beginning of Query, QueryRow, and Exec calls.
// It starts a new trace span for the database operation.
func (t *PgxTracer) TraceQueryStart(ctx context.Context, conn *pgx.Conn, data pgx.TraceQueryStartData) context.Context {
	if t.tracer == nil {
		return ctx
	}

	// Start a new span for the database query
	spanCtx, span := t.tracer.Start(ctx, "db.postgresql.query",
		trace.WithSpanKind(trace.SpanKindClient),
		trace.WithAttributes(
			attribute.String("db.system", "postgresql"),
			attribute.String("db.statement", data.SQL),
			attribute.String("db.operation", extractOperation(data.SQL)),
		),
	)

	// Add connection information if available
	if conn != nil && conn.Config() != nil {
		config := conn.Config()
		span.SetAttributes(
			attribute.String("db.connection_string", sanitizeDSN(config.ConnString())),
			attribute.String("db.user", config.User),
			attribute.String("db.name", config.Database),
			attribute.String("net.peer.name", config.Host),
			attribute.Int("net.peer.port", int(config.Port)),
		)
	}

	// Add query arguments if present (be careful with sensitive data)
	if len(data.Args) > 0 {
		span.SetAttributes(
			attribute.Int("db.args.count", len(data.Args)),
		)
		// Note: We don't log actual argument values for security reasons
	}

	// Log the query start
	if t.logger != nil {
		t.logger.Debug("PostgreSQL query started",
			"sql", data.SQL,
			"args_count", len(data.Args),
		)
	}

	return spanCtx
}

// TraceQueryEnd is called at the end of Query, QueryRow, and Exec calls.
// It finishes the trace span and logs the operation result.
func (t *PgxTracer) TraceQueryEnd(ctx context.Context, conn *pgx.Conn, data pgx.TraceQueryEndData) {
	span := trace.SpanFromContext(ctx)
	if !span.IsRecording() {
		return
	}

	// Query completed - span will be ended

	// Set span attributes
	span.SetAttributes(
		attribute.String("db.operation_end", "query_completed"),
	)

	// Handle errors
	if data.Err != nil {
		span.RecordError(data.Err)
		span.SetStatus(codes.Error, data.Err.Error())
		span.SetAttributes(
			attribute.String("db.error", data.Err.Error()),
		)

		// Log error
		if t.logger != nil {
			t.logger.Error("PostgreSQL query failed",
				"error", data.Err,
			)
		}
	} else {
		span.SetStatus(codes.Ok, "")

		// Log successful completion
		if t.logger != nil {
			t.logger.Debug("PostgreSQL query completed")
		}
	}

	// Add command tag information if available
	if data.CommandTag.String() != "" {
		span.SetAttributes(
			attribute.String("db.command_tag", data.CommandTag.String()),
			attribute.Int64("db.rows_affected", data.CommandTag.RowsAffected()),
		)
	}

	// Finish the span
	span.End()
}

// PgxLogger implements pgx.Logger interface to provide structured logging
// for PostgreSQL connection events
type PgxLogger struct {
	logger *slog.Logger
}

// NewPgxLogger creates a new PostgreSQL logger adapter
func NewPgxLogger(logger *slog.Logger) *PgxLogger {
	return &PgxLogger{
		logger: logger,
	}
}

// Log logs a message at the specified level
func (l *PgxLogger) Log(ctx context.Context, level tracelog.LogLevel, msg string, data map[string]interface{}) {
	if l.logger == nil {
		return
	}

	// Convert pgx log level to slog level
	slogLevel := convertPgxLogLevel(level)

	// Create structured log fields
	attrs := make([]slog.Attr, 0, len(data)+1)
	attrs = append(attrs, slog.String("component", "pgx"))

	for key, value := range data {
		attrs = append(attrs, slog.Any(key, value))
	}

	l.logger.LogAttrs(ctx, slogLevel, msg, attrs...)
}

// convertPgxLogLevel converts pgx log levels to slog log levels
func convertPgxLogLevel(level tracelog.LogLevel) slog.Level {
	switch level {
	case tracelog.LogLevelTrace:
		return slog.LevelDebug - 1 // Below debug
	case tracelog.LogLevelDebug:
		return slog.LevelDebug
	case tracelog.LogLevelInfo:
		return slog.LevelInfo
	case tracelog.LogLevelWarn:
		return slog.LevelWarn
	case tracelog.LogLevelError:
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// extractOperation extracts the SQL operation type from a query
func extractOperation(sql string) string {
	if len(sql) == 0 {
		return "unknown"
	}

	// Simple operation extraction - look for first word
	sql = strings.TrimSpace(sql)
	spaceIndex := strings.Index(sql, " ")
	if spaceIndex == -1 {
		return normalizeOperation(sql)
	}

	operation := sql[:spaceIndex]
	return normalizeOperation(operation)
}

// normalizeOperation normalizes SQL operation names
func normalizeOperation(op string) string {
	// Convert to lowercase and handle common variations
	op = strings.ToLower(op)
	switch op {
	case "select":
		return "select"
	case "insert":
		return "insert"
	case "update":
		return "update"
	case "delete":
		return "delete"
	case "create":
		return "create"
	case "drop":
		return "drop"
	case "alter":
		return "alter"
	case "begin":
		return "begin"
	case "commit":
		return "commit"
	case "rollback":
		return "rollback"
	default:
		return "other"
	}
}

// sanitizeDSN removes sensitive information from connection strings
func sanitizeDSN(dsn string) string {
	// This is a simple implementation - in production you might want
	// more sophisticated DSN parsing and sanitization
	if dsn == "" {
		return ""
	}

	// For security, we'll just return a placeholder indicating the DSN was sanitized
	return "[SANITIZED]"
}
