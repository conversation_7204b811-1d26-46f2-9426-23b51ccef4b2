﻿好的，遵照您的指示，我们来生成一份为 `chat-api-service` (聊天API服务) 编写的、生产级的、极其详尽的软件需求规格说明书（SRS）。

这份 **版本 2.0** 的文档将在之前的版本基础上，进行以下关键增强：

1.  **消息类型与内容结构化**: 详细定义多种消息类型（文本、图片、语音、系统通知、卡片消息），并使用JSONB来支持灵活的内容结构。
2.  **高级群聊功能**: 增加对`@mentions`、消息引用回复、群公告、以及禁言等高级群聊管理功能的支持。
3.  **消息搜索与索引**: 明确与 `search-indexer-service` 的集成，实现强大的聊天记录搜索。
4.  **数据安全与合规**: 强化对用户隐私数据、内容审核和数据生命周期管理的描述。
5.  **细化API与数据模型**: 提供更具体的请求/响应体示例，并对数据模型进行优化，以支持新功能。
6.  **强化非功能性需求**: 补充更具体、可量化的性能、可靠性和扩展性指标，特别是针对海量消息存储的挑战。

这份文档将描绘一个功能丰富、性能卓越、安全可靠，且能支撑大规模用户实时通信的后端核心。

---

### CINA.CLUB - chat-api-service 需求规格说明书

**版本: 2.0 (生产级定义，支持高级群聊与消息搜索)**  
**发布日期: 2025-06-20**  
**最后修订日期: 2025-06-20**  
**文档负责人:** [实时通信团队负责人/架构师名称]  
**审批人:** [CTO/产品总监]

#### 目录
1.  [引言](#1-引言)
2.  [总体描述](#2-总体描述)
3.  [核心流程图](#3-核心流程图)
4.  [功能需求 (Functional Requirements)](#4-功能需求-functional-requirements)
5.  [接口需求 (Interface Requirements)](#5-接口需求-interface-requirements)
6.  [数据需求 (Data Requirements)](#6-数据需求-data-requirements)
7.  [非功能性需求 (Non-Functional Requirements)](#7-非功能性-non-functional-requirements)
8.  [技术约束与选型建议](#8-技术约束-与-选型-建议)

---

### 1. 引言

#### 1.1. 项目背景与目的
为了支持CINA.CLUB平台内用户之间以及用户与服务提供者之间的高效沟通，需要一个功能强大、高度可靠的聊天系统。`chat-api-service` 是该聊天系统的核心数据与逻辑层，其目的在于提供持久化的聊天数据管理和丰富的非实时API接口，与负责实时消息传递的 `chat-websocket-server` 协同工作，共同构建完整的通信解决方案。

#### 1.2. 服务范围
本服务 **负责**:
*   **聊天室(Room)的统一管理**: 创建和管理所有聊天室，包括**1对1聊天**、**群聊**，以及与特定业务对象关联的聊天室。
*   **高级群聊管理**: 支持群成员管理（添加、移除）、群角色分配（群主、管理员、成员）、群信息更新、群公告、以及**成员禁言**。
*   **结构化消息管理**:
    *   持久化存储多种类型的消息（文本、图片、语音、系统通知、**卡片消息**）。
    *   支持消息的**引用回复**和`@mentions`。
*   **消息历史与搜索**:
    *   提供API供客户端分页获取历史消息。
    *   将消息内容发布至消息队列，供`search-indexer-service`消费，以实现强大的全文检索。
*   **会话列表与未读状态**: 提供API供用户获取其会话列表，并精确管理每个会话的未读消息数。

本服务 **不负责**:
*   处理实时WebSocket连接和消息广播 (由 `chat-websocket-server` 负责)。
*   管理用户在线状态 (Presence) (由 `chat-websocket-server` 和Redis负责)。
*   发送离线推送通知 (由 `notification-dispatch-service` 负责)。
*   存储图片、语音等二进制文件 (由 `file-storage-service` 负责)。

#### 1.3. 目标用户/调用方
*   **CINA.CLUB客户端应用 (主要)**: 调用API获取会话列表、历史消息、创建群聊、管理群成员等。
*   **`chat-websocket-server`**: (通过MQ和API) 将待持久化的消息发送到MQ；调用本服务的内部API进行权限校验。
*   **`search-indexer-service`**: (消费本服务事件) 消费`ChatMessagePublishedEvent`来索引聊天记录。
*   **其他内部微服务**: 调用本服务API来创建与业务关联的聊天室。

#### 1.4. 定义与缩略语
*   **Room**: 聊天室，一个抽象的对话空间。
*   **Session/Conversation**: 同义于Room，指一个对话。
*   **Message Type**: 消息类型, 如 `TEXT`, `IMAGE`, `VOICE`, `SYSTEM`, `CARD`.
*   **@mention**: 在消息中提及某个或所有用户。
*   **Card Message**: 一种结构化的消息，用于显示格式化的内容，如商品链接、任务邀请。

### 2. 总体描述

#### 2.1. 服务在CINA.CLUB生态中的定位
`chat-api-service` 是CINA.CLUB聊天系统的“**历史档案馆**”和“**管理中心**”。它与负责“现在”的 `chat-websocket-server` 形成动静分离，确保所有交流都有据可查、可搜索，并为用户提供管理和回顾其所有对话（包括私聊和群聊）的能力。它是聊天功能数据持久性、管理性和可检索性的核心。

#### 2.2. 主要功能概述
*   事件驱动的消息持久化。
*   高效、可分页的消息和会话列表查询。
*   功能丰富的群聊管理，支持角色、权限和高级操作。
*   支持多种结构化消息类型。
*   与搜索引擎集成，提供聊天记录全文检索。

### 3. 核心流程图

#### 3.1. 发送一条带@和引用的消息
```mermaid
sequenceDiagram
    participant Client
    participant WebSocketServer as WS
    participant MQ
    participant ChatAPIService as CAS
    participant SearchIndexer

    Client->>WS: 1. SendMessage (content: "@Bob, about your last message...", replyToMsgId: "msg_abc")
    WS->>WS: 2. Validate sender is in room
    WS->>MQ: 3. Publish RawChatMessageEvent to MQ
    WS-->>Client: 4. ACK message sent

    MQ-->>CAS: 5. Consume RawChatMessageEvent
    CAS->>CAS: 6. Parse content, identify mentions (@Bob -> userId_bob)
    CAS->>DB: 7. **Start Transaction**
    CAS->>DB: 8. Insert new ChatMessage record (with `mentions` and `reply_to_message_id` fields)
    CAS->>DB: 9. Update ChatRoom's `last_message` and `updated_at`
    CAS->>DB: 10. **Commit Transaction**

    CAS->>MQ: 11. Publish ChatMessagePublishedEvent (for search indexing)
    
    MQ-->>SearchIndexer: 12. Consume event
    SearchIndexer->>Elasticsearch: 13. Index new message content
```

### 4. 功能需求 (Functional Requirements)

#### 4.1. 聊天室 (Room) 与会话管理
*   **FR4.1.1 (创建)**: 支持创建`ONE_TO_ONE`和`GROUP`类型的聊天室。`ONE_TO_ONE`在两个用户间必须唯一。
*   **FR4.1.2 (会话列表)**: 提供API获取用户的所有会话列表，按`updated_at`（最后一条消息时间）倒序排列。每项需包含房间信息、最后一条消息预览、以及该用户的**未读消息数**。
*   **FR4.1.3 (未读数管理)**:
    *   系统精确追踪用户在每个房间的`last_read_timestamp`。
    *   未读数通过计算该时间戳之后的消息数量得出。此计算需高效（可由Redis计数器辅助）。
    *   提供API供用户上报已读，以更新`last_read_timestamp`。

#### 4.2. 高级群聊管理
*   **FR4.2.1 (角色与权限)**:
    *   角色: `OWNER`, `ADMIN`, `MEMBER`.
    *   权限: `OWNER`拥有所有权限。`ADMIN`拥有除解散、转让外的所有管理权限。`MEMBER`只能发言和查看信息。
*   **FR4.2.2 (成员管理)**: 支持`ADMIN`及以上角色添加/移除成员。
*   **FR4.2.3 (禁言)**: `ADMIN`及以上角色可以对普通成员进行临时或永久禁言。被禁言成员的状态需存储在`room_members`表中。
*   **FR4.2.4 (群公告)**: `ADMIN`及以上角色可以发布和置顶群公告。公告本质上是一种特殊的`SYSTEM`消息。

#### 4.3. 结构化消息管理
*   **FR4.3.1 (多类型支持)**: 消息的`content`字段为JSONB，其结构由`type`字段决定。
    *   `TEXT`: `{ "text": "Hello" }`
    *   `IMAGE`: `{ "url": "...", "width": 1024, "height": 768, "size_kb": 128 }`
    *   `CARD`: `{ "title": "任务邀请", "description": "帮我代购...", "cover_url": "...", "deep_link_url": "cinaclub://task/..." }`
*   **FR4.3.2 (引用回复)**: 消息可以包含一个`reply_to_message_id`字段，指向被回复的消息。客户端据此渲染引用UI。
*   **FR4.3.3 (@Mentions)**: 消息可以包含一个`mentions`数组，存储被`@`的用户的`userId`列表。`@all`为特殊标识。被`@`的用户会收到强提醒。
*   **FR4.3.4 (消息撤回)**: 发送方可以在可配置的时间窗口内（如2分钟）撤回消息。撤回后，消息`type`变为`REVOKED`，`content`被清空。

#### 4.4. 消息搜索
*   **FR4.4.1 (索引)**: 当任何消息被创建或编辑时，本服务必须发布一个`ChatMessagePublishedEvent`。
*   **FR4.4.2 (事件内容)**: 事件`payload`必须包含所有需要被索引的数据，如`room_id`, `sender_id`, `text_content`等。
*   **FR4.4.3 (搜索API)**: 本服务**不直接提供**搜索API。搜索请求由客户端发送给`search-service`，`search-service`查询Elasticsearch并返回结果。

#### 4.5. 数据安全与合规
*   **FR4.5.1 (内容审核)**: 所有用户生成的消息内容，在持久化后都必须异步提交给`content-moderation-service`进行审核。
*   **FR4.5.2 (审核结果处理)**: 根据审核结果，更新消息的`moderation_status`。被拒绝的消息对发送方外其他用户不可见。
*   **FR4.5.3 (数据生命周期)**: 必须有策略（如后台任务）对非常陈旧的聊天记录进行归档或删除，以控制存储成本和履行隐私承诺。

### 5. 接口需求 (Interface Requirements)

#### 5.1. 客户端/内部 RESTful API接口
*   **版本**: `/api/v1/chat`
*   **认证**: 用户JWT。
*   **核心端点**:
    *   **Rooms & Sessions**:
        *   `POST /rooms`: 创建新聊天室。
        *   `GET /sessions`: 获取我的会话列表。
        *   `GET /rooms/{roomId}`: 获取聊天室详情（含成员列表）。
        *   `DELETE /rooms/{roomId}`: 退出或解散群聊。
    *   **Group Management**:
        *   `POST /rooms/{roomId}/members`: 添加成员 (Admin+)。
        *   `DELETE /rooms/{roomId}/members/{memberUserId}`: 移除成员 (Admin+)。
        *   `PATCH /rooms/{roomId}/members/{memberUserId}`: 修改成员角色或禁言 (Admin+)。
    *   **Messages**:
        *   `GET /rooms/{roomId}/messages?before_id=...&limit=...`: 获取历史消息。
        *   `POST /messages/{messageId}/recall`: 撤回消息。

#### 5.2. 消息队列事件契约
*   **入站 (消费)**: `RawChatMessageEvent` (from `chat-websocket-server`), `ContentModerationResultEvent`.
*   **出站 (发布)**: `ChatMessagePublishedEvent` (for `search-indexer-service`), `UserMentionedInMessageEvent` (for `notification-dispatch-service`).

### 6. 数据需求 (Data Requirements)

#### 6.1. 核心数据模型 (PostgreSQL or CockroachDB)
*   **`chat_rooms`**: `id`, `type`, `name`, `avatar_url`, `last_message (JSONB)`, `creator_user_id`, `created_at`, `updated_at`.
*   **`room_members`**: `(room_id, user_id)` (PK), `role`, `is_muted` (禁言), `last_read_timestamp`.
*   **`chat_messages`**:
    *   `id (PK, UUID)`
    *   `room_id (FK, INDEX)`
    *   `sender_id (FK, INDEX)`
    *   `type (VARCHAR)`
    *   `content (JSONB)`: 存储结构化消息内容。
    *   `mentions (TEXT[])`: 存储被@的`userId`数组。
    *   `reply_to_message_id (FK, nullable)`
    *   `timestamp (TIMESTAMPTZ, INDEX)`
    *   `moderation_status (VARCHAR)`
    *   `is_deleted (BOOLEAN)`

#### 6.2. 数据存储与扩展
*   **数据库**: **CockroachDB** 或 **TiDB** 是海量消息场景下的理想选择，因为它们兼容PostgreSQL协议且原生支持水平扩展。如果选用PostgreSQL，必须从早期就规划好基于`room_id`或`timestamp`的分区/分片策略。
*   **全文检索**: Elasticsearch / OpenSearch。

### 7. 非功能性需求 (Non-Functional Requirements)

#### 7.1. 性能需求
*   **API延迟**:
    *   `GET /sessions` (会话列表) P99 < 150ms。
    *   `GET /rooms/{roomId}/messages` (历史消息) P99 < 100ms。
*   **消息持久化吞吐量**: 能够处理每秒数万条消息的峰值写入量。
*   **数据可见性**: 消息在持久化后，应在1秒内对历史消息查询API可见。

#### 7.2. 可靠性与可用性需求
*   **可用性**: > 99.95%。
*   **消息不丢失**: 消息从MQ消费到DB持久化的过程必须保证“至少一次”语义。

#### 7.3. 可扩展性需求
*   API服务和消息消费者均可水平扩展。
*   数据库必须支持水平扩展，以应对数据量的线性增长。

#### 7.4. 安全性需求
*   **授权**: 严格校验用户是否有权在房间内发言、拉人、禁言等。
*   **数据隐私**: 聊天内容是高度敏感的隐私数据，数据库访问和管理员后台访问必须有严格的权限控制和审计。

### 8. 技术约束与选型建议
*   **语言**: Go。
*   **数据库**: 优先考虑分布式SQL数据库（CockroachDB, TiDB）。
*   **解耦**: 与`chat-websocket-server`通过MQ解耦是保证实时性能的关键。与`search-service`通过`search-indexer-service`解耦是保证架构清晰的关键。
*   **用户信息缓存**: 必须实现对用户信息（昵称、头像）的缓存或冗余+事件更新策略，避免高频调用`user-core-service`。

---
这份版本2.0的SRS文档为`chat-api-service`构建了一个功能丰富、架构健壮、可支持海量用户的聊天后端。它通过统一的模型支持了私聊和高级群聊，并通过与搜索引擎的集成，解决了聊天记录的可发现性问题，为CINA.CLUB平台提供了世界一流的通信基础。