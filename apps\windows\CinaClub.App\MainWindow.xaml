<!--
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 00:00:00
Modified: 2025-01-27 00:00:00
-->

<Window x:Class="CinaClub.App.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="CINA.CLUB"
        MinWidth="1200"
        MinHeight="800">

    <Grid>
        <Frame x:Name="ContentFrame"
               NavigationFailed="ContentFrame_NavigationFailed">
            <Frame.ContentTransitions>
                <TransitionCollection>
                    <NavigationThemeTransition>
                        <NavigationThemeTransition.DefaultNavigationTransitionInfo>
                            <EntranceNavigationTransitionInfo />
                        </NavigationThemeTransition.DefaultNavigationTransitionInfo>
                    </NavigationThemeTransition>
                </TransitionCollection>
            </Frame.ContentTransitions>
        </Frame>

        <!-- 全局加载指示器 -->
        <Grid x:Name="LoadingOverlay"
              Background="{ThemeResource SystemControlAcrylicWindowBrush}"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Spacing="16">
                <ProgressRing IsActive="True" 
                             Width="48" 
                             Height="48"
                             Foreground="{StaticResource CinaClubPrimaryBrush}" />
                <TextBlock x:Name="LoadingText"
                          Text="正在加载..."
                          HorizontalAlignment="Center"
                          Style="{ThemeResource BodyTextBlockStyle}" />
            </StackPanel>
        </Grid>

        <!-- 通知栏 -->
        <InfoBar x:Name="NotificationBar"
                 VerticalAlignment="Top"
                 IsOpen="False"
                 Margin="12,12,12,0"
                 CornerRadius="8" />
    </Grid>
</Window> 