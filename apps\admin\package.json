{"name": "@cina.club/admin-frontend", "version": "1.0.0", "private": true, "description": "CINA.CLUB统一后台管理系统", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "gen:api": "openapi -i admin-bff-openapi.json -o src/api/generated -c axios --name ApiClient", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:coverage": "vitest run --coverage", "docker:build": "docker build -t cina-club-admin .", "docker:run": "docker run -p 3001:80 cina-club-admin", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@ant-design/pro-components": "^2.6.43", "@ant-design/pro-layout": "^7.17.16", "@ant-design/pro-table": "^3.12.8", "@ant-design/pro-form": "^2.25.1", "@ant-design/charts": "^2.0.3", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@tanstack/react-query": "^5.13.4", "@tanstack/react-query-devtools": "^5.13.5", "zustand": "^4.5.2", "immer": "^10.0.3", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "ahooks": "^3.7.8", "classnames": "^2.3.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.23.8", "react-window": "^1.8.10", "react-resizable": "^3.0.5"}, "devDependencies": {"@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "openapi-typescript-codegen": "^0.29.0", "typescript": "^5.2.2", "vite": "^5.3.1", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@types/react-window": "^1.8.8", "@types/react-resizable": "^3.0.7", "webpack-bundle-analyzer": "^4.10.2", "rollup-plugin-visualizer": "^5.12.0", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-pwa": "^0.20.0", "msw": "^2.3.1", "@playwright/test": "^1.44.1", "playwright-lighthouse": "^4.0.0", "axe-playwright": "^2.0.1", "@sentry/react": "^8.7.0", "@sentry/vite-plugin": "^2.16.1", "posthog-js": "^1.142.1", "storybook": "^8.1.11", "@storybook/react": "^8.1.11", "@storybook/react-vite": "^8.1.11", "@storybook/addon-essentials": "^8.1.11", "@storybook/addon-interactions": "^8.1.11", "@storybook/addon-links": "^8.1.11", "@storybook/blocks": "^8.1.11", "@storybook/testing-library": "^0.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.0"}