// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-16
// Modified: 2025-01-16

package middleware

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	Enabled bool
	RPS     int
}

// CORS middleware for handling cross-origin requests
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-Device-ID")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestID middleware adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// RateLimit middleware for rate limiting (simplified implementation)
func RateLimit(config RateLimitConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !config.Enabled {
			c.Next()
			return
		}

		// In a real implementation, this would use Redis or in-memory store
		// For now, just add a small delay to simulate rate limiting
		time.Sleep(time.Millisecond * 10)
		c.Next()
	}
}

// JWTAuth middleware for JWT authentication (simplified)
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// In a real implementation, validate JWT token here
		// For now, just extract a mock user ID
		userID := "user-123" // This would come from JWT parsing
		c.Set("user_id", userID)
		c.Next()
	}
}

// DeviceID middleware for extracting device ID
func DeviceID() gin.HandlerFunc {
	return func(c *gin.Context) {
		deviceID := c.GetHeader("X-Device-ID")
		if deviceID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "X-Device-ID header required"})
			c.Abort()
			return
		}

		c.Set("device_id", deviceID)
		c.Next()
	}
}

// AdminAuth middleware for admin authentication (simplified)
func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// In a real implementation, validate admin JWT token here
		// For now, just allow all requests with any token
		c.Set("user_id", "admin")
		c.Next()
	}
}
