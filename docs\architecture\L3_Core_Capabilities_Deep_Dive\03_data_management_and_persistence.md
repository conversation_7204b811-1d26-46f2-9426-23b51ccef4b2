
### 文件3: `docs/architecture/L3_Core_Capabilities_Deep_Dive/03_data_management_and_persistence.md`

(内容大纲)
*   **概述**: 定义平台的数据存储哲学。
*   **多态持久化 (Polyglot Persistence) 策略**:
    *   **何时使用PostgreSQL**: 强事务性、结构化数据（`user-core`, `billing`）。
    *   **何时使用MongoDB**: 灵活Schema、文档型数据（`digital-twin`）。
    *   **何时使用Redis**: 缓存、会话、排行榜、分布式锁。
    *   **何时使用Elasticsearch**: 全文搜索、日志。
    *   **何时使用VectorDB**: 语义搜索。
    *   **何时使用Kafka**: 事件流、持久化日志。
    *   **何时使用对象存储**: 二进制文件。
*   **数据一致性模型**:
    *   **本地事务**: 单个服务内必须保证ACID。
    *   **分布式事务**:
        *   **Saga模式 (编排式)**: 详细描述`service-offering-service`中的订单流程如何作为Saga协调者，通过执行/补偿操作来保证最终一致性。提供序列图。
        *   **原则**: 优先使用Saga。只有在极少数强一致性要求下，才考虑两阶段提交（2PC），但平台目前不推荐。
*   **数据库迁移**:
    *   **工具**: 强制使用`golang-migrate`。
    *   **流程**: 迁移脚本与代码一同在PR中提交 -> CI中测试 -> CD流水线中自动执行。
    *   **规范**: 迁移只能新增或修改，禁止破坏性操作（如删除列）。数据清理通过独立的脚本完成。
*   **相关包**: 链接到`pkg/database`的SRS。
