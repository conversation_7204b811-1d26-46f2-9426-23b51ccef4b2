// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-06-24 10:19:52
// Modified: 2025-06-24 10:19:52

package models

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// User represents the core user entity in the CINA.CLUB platform
type User struct {
	ID          uuid.UUID              `json:"id"`
	Email       string                 `json:"email"`
	Username    string                 `json:"username"`
	DisplayName string                 `json:"display_name"`
	Avatar      string                 `json:"avatar"`
	Bio         string                 `json:"bio"`
	Status      UserStatus             `json:"status"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// UserStatus represents the status of a user account
type UserStatus string

const (
	UserStatusActive    UserStatus = "ACTIVE"
	UserStatusInactive  UserStatus = "INACTIVE"
	UserStatusSuspended UserStatus = "SUSPENDED"
	UserStatusDeleted   UserStatus = "DELETED"
)

// IsActive returns true if the user is in active status
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// FullName returns the full display name or username as fallback
func (u *User) FullName() string {
	if u.DisplayName != "" {
		return u.DisplayName
	}
	return u.Username
}

// UserProfile represents extended user profile information
type UserProfile struct {
	UserID      uuid.UUID              `json:"user_id"`
	FirstName   string                 `json:"first_name"`
	LastName    string                 `json:"last_name"`
	DateOfBirth *time.Time             `json:"date_of_birth,omitempty"`
	Gender      string                 `json:"gender"`
	Location    string                 `json:"location"`
	Timezone    string                 `json:"timezone"`
	Language    string                 `json:"language"`
	Phone       string                 `json:"phone"`
	Website     string                 `json:"website"`
	Social      map[string]string      `json:"social"`
	Preferences map[string]interface{} `json:"preferences"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// GetFullName returns the combined first and last name
func (p *UserProfile) GetFullName() string {
	if p.FirstName != "" && p.LastName != "" {
		return p.FirstName + " " + p.LastName
	}
	if p.FirstName != "" {
		return p.FirstName
	}
	if p.LastName != "" {
		return p.LastName
	}
	return ""
}

// UserSettings represents user application settings
type UserSettings struct {
	UserID              uuid.UUID              `json:"user_id"`
	NotificationEnabled bool                   `json:"notification_enabled"`
	EmailNotifications  bool                   `json:"email_notifications"`
	PushNotifications   bool                   `json:"push_notifications"`
	PrivacyLevel        PrivacyLevel           `json:"privacy_level"`
	Theme               string                 `json:"theme"`
	Language            string                 `json:"language"`
	Timezone            string                 `json:"timezone"`
	Custom              map[string]interface{} `json:"custom"`
	UpdatedAt           time.Time              `json:"updated_at"`
}

// PrivacyLevel represents user privacy settings
type PrivacyLevel string

const (
	PrivacyLevelPublic  PrivacyLevel = "PUBLIC"
	PrivacyLevelFriends PrivacyLevel = "FRIENDS"
	PrivacyLevelPrivate PrivacyLevel = "PRIVATE"
)

// UserMembership represents user subscription and membership information
type UserMembership struct {
	UserID    uuid.UUID              `json:"user_id"`
	Level     MembershipLevel        `json:"level"`
	Type      MembershipType         `json:"type"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
	Features  []string               `json:"features"`
	Metadata  map[string]interface{} `json:"metadata"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// MembershipLevel represents the user's membership level
type MembershipLevel string

const (
	MembershipLevelBasic      MembershipLevel = "BASIC"
	MembershipLevelPremium    MembershipLevel = "PREMIUM"
	MembershipLevelPro        MembershipLevel = "PRO"
	MembershipLevelEnterprise MembershipLevel = "ENTERPRISE"
)

// MembershipType represents the type of membership
type MembershipType string

const (
	MembershipTypeFree     MembershipType = "FREE"
	MembershipTypePaid     MembershipType = "PAID"
	MembershipTypeTrial    MembershipType = "TRIAL"
	MembershipTypeLifetime MembershipType = "LIFETIME"
)

// IsActive returns true if the membership is currently active
func (m *UserMembership) IsActive() bool {
	if m.ExpiresAt == nil {
		return true // No expiration means lifetime or perpetual
	}
	return time.Now().Before(*m.ExpiresAt)
}

// HasFeature checks if the membership includes a specific feature
func (m *UserMembership) HasFeature(feature string) bool {
	for _, f := range m.Features {
		if f == feature {
			return true
		}
	}
	return false
}

// UserStatistics represents user engagement and activity statistics
type UserStatistics struct {
	UserID           uuid.UUID `json:"user_id"`
	TotalLogins      int64     `json:"total_logins"`
	LastLoginAt      time.Time `json:"last_login_at"`
	TotalServices    int64     `json:"total_services"`
	TotalSpent       int64     `json:"total_spent"`  // in cents
	TotalEarned      int64     `json:"total_earned"` // in cents
	AverageRating    float64   `json:"average_rating"`
	TotalReviews     int64     `json:"total_reviews"`
	TotalConnections int64     `json:"total_connections"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// GetAverageRatingDisplay returns the average rating formatted for display
func (s *UserStatistics) GetAverageRatingDisplay() string {
	if s.AverageRating == 0 {
		return "No ratings yet"
	}
	return fmt.Sprintf("%.1f", s.AverageRating)
}
