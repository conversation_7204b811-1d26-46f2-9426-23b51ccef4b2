/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package http

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// MockBFFService is a mock implementation of the BFFService interface
type MockBFFService struct {
	mock.Mock
}

func (m *MockBFFService) CreateSession(ctx context.Context, employee *model.Employee, ipAddress, userAgent string) (*model.AdminSession, error) {
	args := m.Called(ctx, employee, ipAddress, userAgent)
	return args.Get(0).(*model.AdminSession), args.Error(1)
}

func (m *MockBFFService) ValidateSession(ctx context.Context, sessionID string) (*model.AdminSession, error) {
	args := m.Called(ctx, sessionID)
	return args.Get(0).(*model.AdminSession), args.Error(1)
}

func (m *MockBFFService) RefreshSession(ctx context.Context, sessionID string) (*model.AdminSession, error) {
	args := m.Called(ctx, sessionID)
	return args.Get(0).(*model.AdminSession), args.Error(1)
}

func (m *MockBFFService) DestroySession(ctx context.Context, sessionID string) error {
	args := m.Called(ctx, sessionID)
	return args.Error(0)
}

func (m *MockBFFService) DestroyAllUserSessions(ctx context.Context, employeeID string) error {
	args := m.Called(ctx, employeeID)
	return args.Error(0)
}

func (m *MockBFFService) GetUsers(ctx context.Context, filter port.UserFilter) (*port.UsersResponse, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(*port.UsersResponse), args.Error(1)
}

func (m *MockBFFService) GetUserFullProfile(ctx context.Context, userID string) (*port.UserFullProfileDTO, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*port.UserFullProfileDTO), args.Error(1)
}

func (m *MockBFFService) SuspendUser(ctx context.Context, actorInfo *port.ActorInfo, userID string, reason string) error {
	args := m.Called(ctx, actorInfo, userID, reason)
	return args.Error(0)
}

func (m *MockBFFService) RestoreUser(ctx context.Context, actorInfo *port.ActorInfo, userID string, reason string) error {
	args := m.Called(ctx, actorInfo, userID, reason)
	return args.Error(0)
}

func (m *MockBFFService) UpdateUserStatus(ctx context.Context, actorInfo *port.ActorInfo, userID string, status string) error {
	args := m.Called(ctx, actorInfo, userID, status)
	return args.Error(0)
}

func (m *MockBFFService) GetModerationQueue(ctx context.Context, filter port.ModerationFilter) (*port.ModerationQueueResponse, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(*port.ModerationQueueResponse), args.Error(1)
}

func (m *MockBFFService) ApproveContent(ctx context.Context, actorInfo *port.ActorInfo, contentID string, reason string) error {
	args := m.Called(ctx, actorInfo, contentID, reason)
	return args.Error(0)
}

func (m *MockBFFService) RejectContent(ctx context.Context, actorInfo *port.ActorInfo, contentID string, reason string) error {
	args := m.Called(ctx, actorInfo, contentID, reason)
	return args.Error(0)
}

func (m *MockBFFService) GetOrders(ctx context.Context, filter port.OrderFilter) (*port.OrdersResponse, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(*port.OrdersResponse), args.Error(1)
}

func (m *MockBFFService) GetOrderDetails(ctx context.Context, orderID string) (*port.OrderDetailsDTO, error) {
	args := m.Called(ctx, orderID)
	return args.Get(0).(*port.OrderDetailsDTO), args.Error(1)
}

func (m *MockBFFService) CancelOrder(ctx context.Context, actorInfo *port.ActorInfo, orderID string, reason string) error {
	args := m.Called(ctx, actorInfo, orderID, reason)
	return args.Error(0)
}

func (m *MockBFFService) RefundOrder(ctx context.Context, actorInfo *port.ActorInfo, orderID string, amount float64, reason string) error {
	args := m.Called(ctx, actorInfo, orderID, amount, reason)
	return args.Error(0)
}

func (m *MockBFFService) GetDashboardSummary(ctx context.Context) (*port.DashboardSummaryDTO, error) {
	args := m.Called(ctx)
	return args.Get(0).(*port.DashboardSummaryDTO), args.Error(1)
}

func (m *MockBFFService) GetUserAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.UserAnalyticsDTO, error) {
	args := m.Called(ctx, timeRange)
	return args.Get(0).(*port.UserAnalyticsDTO), args.Error(1)
}

func (m *MockBFFService) GetContentAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.ContentAnalyticsDTO, error) {
	args := m.Called(ctx, timeRange)
	return args.Get(0).(*port.ContentAnalyticsDTO), args.Error(1)
}

func (m *MockBFFService) GetRevenueAnalytics(ctx context.Context, timeRange port.TimeRange) (*port.RevenueAnalyticsDTO, error) {
	args := m.Called(ctx, timeRange)
	return args.Get(0).(*port.RevenueAnalyticsDTO), args.Error(1)
}

func (m *MockBFFService) GetSystemHealth(ctx context.Context) (*port.SystemHealthDTO, error) {
	args := m.Called(ctx)
	return args.Get(0).(*port.SystemHealthDTO), args.Error(1)
}

func (m *MockBFFService) GetAuditLogs(ctx context.Context, filter model.AuditLogFilter) (*port.AuditLogsResponse, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(*port.AuditLogsResponse), args.Error(1)
}

// Test helper functions

func createTestHandler() (*Handler, *MockBFFService) {
	mockService := &MockBFFService{}
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	handler := NewHandler(mockService, logger)
	return handler, mockService
}

func createTestRequest(method, url string, body interface{}) *http.Request {
	var reqBody []byte
	if body != nil {
		reqBody, _ = json.Marshal(body)
	}

	req := httptest.NewRequest(method, url, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Add mock session to context
	session := &model.AdminSession{
		ID:         "test-session-id",
		EmployeeID: "test-employee-id",
		Email:      "<EMAIL>",
		Name:       "Test Admin",
		Roles:      []string{"admin"},
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		LastUsedAt: time.Now(),
		IPAddress:  "127.0.0.1",
		UserAgent:  "test-agent",
	}

	actorInfo := &port.ActorInfo{
		EmployeeID: session.EmployeeID,
		Email:      session.Email,
		Roles:      session.Roles,
		IPAddress:  "127.0.0.1",
		UserAgent:  "test-agent",
	}

	ctx := context.WithValue(req.Context(), "session", session)
	ctx = context.WithValue(ctx, "actor_info", actorInfo)

	return req.WithContext(ctx)
}

// Test cases

func TestHandler_GetCurrentUser(t *testing.T) {
	handler, _ := createTestHandler()

	req := createTestRequest("GET", "/auth/me", nil)
	rr := httptest.NewRecorder()

	handler.GetCurrentUser(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)

	var response map[string]interface{}
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "test-employee-id", response["id"])
	assert.Equal(t, "<EMAIL>", response["email"])
}

func TestHandler_Logout(t *testing.T) {
	handler, mockService := createTestHandler()

	mockService.On("DestroySession", mock.Anything, "test-session-id").Return(nil)

	req := createTestRequest("POST", "/auth/logout", nil)
	rr := httptest.NewRecorder()

	handler.Logout(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	mockService.AssertExpectations(t)
}

func TestHandler_GetUsers(t *testing.T) {
	handler, mockService := createTestHandler()

	expectedResponse := &port.UsersResponse{
		Users: []*port.UserSummaryDTO{
			{
				ID:          "user1",
				Email:       "<EMAIL>",
				Username:    "user1",
				DisplayName: "User One",
				Status:      "active",
				Level:       1,
				CreatedAt:   time.Now(),
			},
		},
		Total:      1,
		Page:       1,
		PageSize:   20,
		TotalPages: 1,
	}

	mockService.On("GetUsers", mock.Anything, mock.AnythingOfType("port.UserFilter")).Return(expectedResponse, nil)

	req := createTestRequest("GET", "/users?page=1&page_size=20", nil)
	rr := httptest.NewRecorder()

	handler.GetUsers(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)

	var response port.UsersResponse
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), response.Total)
	assert.Len(t, response.Users, 1)

	mockService.AssertExpectations(t)
}

func TestHandler_GetUserProfile(t *testing.T) {
	handler, mockService := createTestHandler()

	expectedProfile := &port.UserFullProfileDTO{
		ID:          "user1",
		Email:       "<EMAIL>",
		Username:    "user1",
		DisplayName: "User One",
		Status:      "active",
		Level:       1,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	mockService.On("GetUserFullProfile", mock.Anything, "user1").Return(expectedProfile, nil)

	req := createTestRequest("GET", "/users/user1/profile", nil)
	rr := httptest.NewRecorder()

	// Set up URL parameter
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("userID", "user1")
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	handler.GetUserProfile(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)

	var response port.UserFullProfileDTO
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "user1", response.ID)

	mockService.AssertExpectations(t)
}

func TestHandler_SuspendUser(t *testing.T) {
	handler, mockService := createTestHandler()

	requestBody := map[string]string{
		"reason": "Violation of terms",
	}

	mockService.On("SuspendUser", mock.Anything, mock.AnythingOfType("*port.ActorInfo"), "user1", "Violation of terms").Return(nil)

	req := createTestRequest("POST", "/users/user1/suspend", requestBody)
	rr := httptest.NewRecorder()

	// Set up URL parameter
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("userID", "user1")
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	handler.SuspendUser(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "User suspended successfully", response["message"])

	mockService.AssertExpectations(t)
}

func TestHandler_SuspendUser_InvalidRequest(t *testing.T) {
	handler, _ := createTestHandler()

	req := createTestRequest("POST", "/users/user1/suspend", "invalid json")
	rr := httptest.NewRecorder()

	// Set up URL parameter
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("userID", "user1")
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	handler.SuspendUser(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestHandler_SuspendUser_MissingUserID(t *testing.T) {
	handler, _ := createTestHandler()

	requestBody := map[string]string{
		"reason": "Violation of terms",
	}

	req := createTestRequest("POST", "/users//suspend", requestBody)
	rr := httptest.NewRecorder()

	handler.SuspendUser(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestHandler_GetDashboardSummary(t *testing.T) {
	handler, mockService := createTestHandler()

	expectedSummary := &port.DashboardSummaryDTO{
		TotalUsers:        1000,
		ActiveUsers:       800,
		NewUsersToday:     10,
		PremiumUsers:      200,
		TotalContent:      5000,
		PendingModeration: 25,
		PublishedToday:    50,
		TotalRevenue:      10000.50,
		RevenueToday:      250.75,
		SystemHealth:      "healthy",
		ActiveSessions:    150,
		ErrorRate:         0.01,
	}

	mockService.On("GetDashboardSummary", mock.Anything).Return(expectedSummary, nil)

	req := createTestRequest("GET", "/analytics/dashboard", nil)
	rr := httptest.NewRecorder()

	handler.GetDashboardSummary(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)

	var response port.DashboardSummaryDTO
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, int64(1000), response.TotalUsers)
	assert.Equal(t, "healthy", response.SystemHealth)

	mockService.AssertExpectations(t)
}

func TestHandler_ApproveContent(t *testing.T) {
	handler, mockService := createTestHandler()

	requestBody := map[string]string{
		"reason": "Content meets guidelines",
	}

	mockService.On("ApproveContent", mock.Anything, mock.AnythingOfType("*port.ActorInfo"), "task1", "Content meets guidelines").Return(nil)

	req := createTestRequest("POST", "/content/tasks/task1/approve", requestBody)
	rr := httptest.NewRecorder()

	// Set up URL parameter
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("taskID", "task1")
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

	handler.ApproveContent(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Content approved successfully", response["message"])

	mockService.AssertExpectations(t)
}

func TestHandler_parseUserFilter(t *testing.T) {
	handler, _ := createTestHandler()

	req := httptest.NewRequest("GET", "/users?page=2&page_size=50&email=<EMAIL>&status=active&search=john", nil)

	filter := handler.parseUserFilter(req)

	assert.Equal(t, 2, filter.Page)
	assert.Equal(t, 50, filter.PageSize)
	assert.Equal(t, "<EMAIL>", filter.Email)
	assert.Equal(t, "active", filter.Status)
	assert.Equal(t, "john", filter.SearchTerm)
	assert.Equal(t, "created_at", filter.SortBy)
	assert.Equal(t, "desc", filter.SortOrder)
}

func TestHandler_parseUserFilter_Defaults(t *testing.T) {
	handler, _ := createTestHandler()

	req := httptest.NewRequest("GET", "/users", nil)

	filter := handler.parseUserFilter(req)

	assert.Equal(t, 1, filter.Page)
	assert.Equal(t, 20, filter.PageSize)
	assert.Equal(t, "created_at", filter.SortBy)
	assert.Equal(t, "desc", filter.SortOrder)
}

func TestHandler_parseTimeRange(t *testing.T) {
	handler, _ := createTestHandler()

	// Use fixed times to avoid timing issues
	startTime := time.Date(2025, 6, 16, 12, 0, 0, 0, time.UTC)
	endTime := time.Date(2025, 6, 23, 12, 0, 0, 0, time.UTC)

	start := startTime.Format(time.RFC3339)
	end := endTime.Format(time.RFC3339)

	req := httptest.NewRequest("GET", "/analytics/users?start="+start+"&end="+end, nil)

	timeRange := handler.parseTimeRange(req)

	assert.WithinDuration(t, startTime, timeRange.Start, time.Second)
	assert.WithinDuration(t, endTime, timeRange.End, time.Second)
}

// Benchmark tests

func BenchmarkHandler_GetCurrentUser(b *testing.B) {
	handler, _ := createTestHandler()
	req := createTestRequest("GET", "/auth/me", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rr := httptest.NewRecorder()
		handler.GetCurrentUser(rr, req)
	}
}

func BenchmarkHandler_parseUserFilter(b *testing.B) {
	handler, _ := createTestHandler()
	req := httptest.NewRequest("GET", "/users?page=2&page_size=50&email=<EMAIL>&status=active&search=john", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		handler.parseUserFilter(req)
	}
}
