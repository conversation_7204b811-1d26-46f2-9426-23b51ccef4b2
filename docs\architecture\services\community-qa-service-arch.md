好的，遵照您的指示。我将为您生成一份专门针对 **`community-qa-service`** 的、极致细化的、生产级**内部架构设计文档**。

这份文档将基于您提供的`community-qa-service-srs.md` (v2.0)，并严格遵循我们在`L4_Service_Design_Patterns`中定义的**整洁架构**和相关模式。它将特别关注**结构化问答流程、悬赏与奖励的Saga事务、声望系统、以及作为AI助手核心知识源的集成方式**，为开发团队提供一份清晰、可执行的“施工图”。

---
### CINA.CLUB - `community-qa-service` 内部架构设计

**文档状态**: 设计完成 (Designed)  
**版本**: 1.0  
**最后修订日期**: 2025-06-27
**参考SRS**: `community-qa-service-srs.md` (v2.0)
**核心架构**: 整洁架构 + Saga编排模式 + 策略模式(用于奖励)

## 1. 概述

`community-qa-service` 是CINA.CLUB平台的“**智慧银行**”和“**知识精炼厂**”。其核心挑战在于：
1.  **结构化流程管理**: 与论坛的发散性讨论不同，Q&A的核心是“提出问题 -> 多个回答 -> 采纳最佳答案”的收敛性流程。
2.  **经济激励与一致性**: 悬赏功能引入了与`cina-coin-ledger-service`的金融交互，必须通过分布式事务（Saga）来保证资金操作的最终一致性。
3.  **内容质量与声望**: 需要一个有效的机制（投票、采纳）来筛选高质量内容，并将其转化为对专家的声望(Reputation)激励。
4.  **AI知识源**: 必须为`ai-assistant-service`提供高效、可靠的接口，使其能将已解决的问答作为权威知识来源。
5.  **与平台生态的深度集成**: 需要与内容审核、搜索、通知、游戏化等多个服务协同。

本架构设计通过采用**整洁架构**，并结合**编排式Saga**来处理悬赏流程，以及**策略模式**来定义不同的奖励计算，以应对上述挑战。

---

## 2. 架构图与目录结构

### 2.1 核心架构图 (悬赏与采纳流程)

```mermaid
sequenceDiagram
    participant UserA as "提问者"
    participant UserB as "回答者"
    participant QAService
    participant LedgerService as "cina-coin-ledger"
    participant GamificationService as "gamification-service"
    participant NotificationService
    
    UserA->>QAService: 1. POST /questions (bounty: 100)
    
    note over QAService: **Start BountyQuestion Saga**
    
    QAService->>LedgerService: 2. [Execute] Freeze 100 coins
    LedgerService-->>QAService: (Success)
    
    QAService->>DB: 3. Create Question (status: OPEN)
    QAService-->>UserA: (questionId)

    UserB->>QAService: 4. POST /answers
    QAService->>NotificationService: 5. Notify UserA of new answer
    
    UserA->>QAService: 6. POST /answers/{id}/accept
    
    note over QAService: **Start AcceptAnswer Saga**
    
    QAService->>DB: 7. Update Answer.is_accepted=true, Question.status=RESOLVED
    
    QAService->>LedgerService: 8a. [Async] Commit frozen balance to UserB
    QAService->>GamificationService: 8b. [Async] Award reputation points to UserB
    QAService->>NotificationService: 8c. [Async] Notify UserB of acceptance & reward
```

### 2.2 最终目录结构 (`services/community-qa-service/`)

```
community-qa-service/
├── cmd/server/
│   └── main.go
├── internal/
│   ├── adapter/
│   │   ├── client/
│   │   │   ├── ledger_client.go
│   │   │   └── gamification_client.go
│   │   ├── event/
│   │   │   └── ... (消费者, 如有)
│   │   ├── grpc/
│   │   │   └── handler.go
│   │   └── repository/
│   │       ├── model.go
│   │       └── repo.go
│   ├── application/
│   │   ├── port/
│   │   │   ├── repository.go
│   │   │   └── service.go
│   │   └── service/
│   │       └── qa_service.go   # ✨ 核心应用服务, Saga协调者 ✨
│   └── domain/
│       ├── aggregate/
│       │   └── question_aggregate.go # 封装Q&A的核心业务规则
│       ├── model/
│       │   └── alias.go
│       └── service/
│           └── reputation_service.go # ✨ 声望计算领域服务 ✨
├── go.mod
└── Dockerfile
```

---

## 3. 各层职责深度解析

### 3.1 `domain/` - 领域层 (The Q&A Rules)

*   `domain/model/`: 使用`/core/models`中与Q&A相关的`struct`，如`Question`, `Answer`, `Comment`。
*   **`domain/service/reputation_service.go`**:
    *   **`ReputationService`**: 一个无状态的领域服务，封装了声望的计算逻辑。
    *   **`CalculateForAnswerAccepted(answer)`**, **`CalculateForVote(...)`**等方法。
    *   **逻辑**: 这些方法根据不同的事件类型和预定义的规则（如“采纳+15分”、“被点赞+10分”、“被点踩-2分”），返回一个需要增减的声望值。规则可以从配置中读取。
*   **`domain/aggregate/question_aggregate.go`**:
    *   **`Question`聚合根**: 封装了`Question`实体及其所有的`Answer`和`Comment`。
    *   **方法**:
        *   **`AddAnswer(answer)`**: 检查问题是否已关闭，然后添加一个回答。
        *   **`AcceptAnswer(actorID, answerID)`**:
            1.  **规则校验**: **必须**检查`actorID`是否为提问者`author_id`。
            2.  **规则校验**: **必须**检查问题状态是否为`OPEN`或`ANSWERED`。
            3.  **规则校验**: **必须**检查该问题是否已有被采纳的答案。
            4.  如果校验通过，则将目标`Answer`标记为`is_accepted=true`，并将`Question`的状态更新为`RESOLVED`。
            5.  **记录领域事件**: 生成`AnswerAcceptedEvent`，其中包含提问者、回答者、问题、答案和悬赏金额等所有必要信息。

### 3.2 `application/` - 应用层 (The Saga Orchestrator)

*   `application/port/`: 定义`Repository`和`QAService`接口。
*   **`application/service/qa_service.go`**: **这是所有业务流程的编排者，也是Saga事务的协调者**。
    *   **`AskQuestionWithBounty(ctx, questionData, bountyAmount)`**:
        *   **这是一个Saga事务**。
        *   **步骤1 (执行)**: 调用`ledger_client.FreezeBalance(...)`冻结用户悬赏金。
        *   **如果步骤1失败**: Saga结束，返回错误给用户。
        *   **如果步骤1成功**:
            *   **步骤2 (执行)**: 在本地数据库事务中，创建一个`Question`记录，状态为`OPEN`，并记录下`ledger_freeze_op_id`。
            *   **如果步骤2失败**:
                *   **步骤3 (补偿)**: 调用`ledger_client.UnfreezeBalance(...)`解冻并返还用户的资金。
            *   **如果步骤2成功**: Saga成功结束。
    *   **`AcceptAnswer(ctx, actorID, answerID)`**:
        *   **这是一个Saga事务**（尽管它的补偿逻辑更简单）。
        *   **步骤1 (执行)**:
            a. 开启本地数据库事务。
            b. 从仓储加载`Question`聚合根。
            c. 调用`question.AcceptAnswer(actorID, answerID)`。
            d. 从`ReputationService`获取应得的声望值。
            e. 更新回答者的声望分数。
            f. 持久化所有变更。
            g. 提交本地数据库事务。
        *   **如果步骤1成功**:
            *   **步骤2 (异步执行，带重试)**:
                a. 如果问题有悬赏，调用`ledger_client.CommitFrozenBalance(...)`将悬赏金转给回答者。
                b. 调用`gamification_client.AwardPointsForAcceptedAnswer(...)`。
                c. 调用`notification_client.SendAnswerAcceptedNotification(...)`。
        *   **注意**: 步骤2中的操作是最终一致的。如果调用失败，会有后台任务进行重试。最坏情况下，如果资金转移失败，需要人工介入，但本地的“已采纳”状态不会回滚。

### 3.3 `adapter/` - 适配层 (The Infrastructure Bridge)

*   **`adapter/repository/`**:
    *   **数据库选型**: **PostgreSQL**。其强事务性是保证Q&A流程严谨性的基础。
    *   实现`port.Repository`接口，负责与数据库交互。
*   **`adapter/client/`**:
    *   封装对`cina-coin-ledger-service`和`gamification-service`的gRPC客户端调用。
    *   必须集成`pkg/tracing`和`pkg/errors`。
*   **`adapter/grpc/`**:
    *   `handler.go`: 实现`community-qa-service.proto`中定义的gRPC服务。
    *   **`InternalSearchForAI` RPC实现**:
        *   这是一个为`ai-assistant-service`优化的内部接口。
        *   它接收一个查询字符串。
        *   **步骤1**: 调用`embedding-service`将查询转换为向量。
        *   **步骤2**: 在本服务的数据库中（如果使用`pgvector`）或专门的向量数据库中，进行**语义搜索**，找出最相关的、**状态为`RESOLVED`的**问题。
        *   **步骤3**: 获取这些问题及其被采纳的最佳答案。
        *   **步骤4**: 返回一个结构化的、包含问题-答案对的列表。

## 4. AI助手集成架构

`community-qa-service`是AI助手最重要的**权威UGC知识源**。集成方式如下：

1.  **数据供给 (Q&A -> AI)**:
    *   当AI助手需要回答一个事实性问题时，它的工作流规划器会优先选择调用`community-qa-service`的`InternalSearchForAI`工具。
    *   本服务通过**语义搜索**返回最相关的、已经由社区验证过的“问题-最佳答案”对。
    *   AI助手将这些高质量的Q&A对作为其生成回答的**核心上下文(Context)**，并可以在其最终回复中明确地**引用(Cite)**来源链接。
2.  **能力增强 (AI -> Q&A)**:
    *   **辅助提问**: 当用户用自然语言提出一个好问题时，AI助手可以调用`qa_service.AskQuestion`，帮助用户将其格式化、打上合适的标签，并代为发布。
    *   **辅助回答**: （未来）当一个问题长时间未被回答时，AI可以尝试根据其通用知识或CKB中的信息，生成一个初步的答案草稿，并标记为“由AI生成”，供社区用户参考和完善。

## 5. 总结

本架构设计通过以下关键点来构建一个生产级的`community-qa-service`：
1.  **领域驱动设计**: 使用`Question`聚合根封装了“采纳答案”这一核心业务流程的复杂规则，保证了逻辑的内聚和正确性。
2.  **Saga模式**: 通过编排式Saga，安全、可靠地处理了与外部金融服务（账本）的分布式事务，保证了悬赏功能的最终一致性。
3.  **AI知识源定位**: 设计了专门的、高性能的内部搜索接口，将Q&A社区沉淀的高质量内容，高效地提供给AI助手作为其回答的权威依据。
4.  **激励闭环**: 将采纳、投票等社区行为，通过`ReputationService`和与`gamification-service`的集成，转化为对用户的有效激励，形成了促进高质量内容产生的正向循环。

这种架构确保了`community-qa-service`不仅能构建一个功能丰富的知识问答社区，更能成为整个CINA.CLUB平台知识体系和AI能力的核心支柱。