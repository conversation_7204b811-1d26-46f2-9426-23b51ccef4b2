/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 14:00:00
*/

package client

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"cina.club/services/admin-bff-service/internal/application/port"
	"cina.club/services/admin-bff-service/internal/domain/model"
)

// UserCoreClient UserCore service client
type UserCoreClient struct {
	// client    pb.UserCoreServiceClient // In actual implementation, use generated gRPC client
	conn   *grpc.ClientConn
	logger *logrus.Entry
}

// NewUserCoreClient creates a new UserCore client
func NewUserCoreClient(conn *grpc.ClientConn, logger *logrus.Entry) *UserCoreClient {
	return &UserCoreClient{
		// client: pb.NewUserCoreServiceClient(conn),
		conn:   conn,
		logger: logger,
	}
}

// GetUserForAdmin gets user management information
func (c *UserCoreClient) GetUserForAdmin(ctx context.Context, userID string) (*UserCoreData, error) {
	start := time.Now()
	c.logger.WithFields(logrus.Fields{
		"method":  "GetUserForAdmin",
		"user_id": userID,
	}).Debug("Calling UserCore service")

	// In actual implementation, this will call gRPC interface
	// req := &pb.GetUserForAdminRequest{UserId: userID}
	// resp, err := c.client.GetUserForAdmin(ctx, req)

	// Mock data
	userData := &UserCoreData{
		ID:              userID,
		Email:           "<EMAIL>",
		Username:        "user123",
		DisplayName:     "User Name",
		Avatar:          "https://example.com/avatar.jpg",
		Bio:             "User bio",
		Status:          "active",
		Level:           5,
		CreatedAt:       time.Now().Add(-30 * 24 * time.Hour),
		UpdatedAt:       time.Now(),
		LastLoginAt:     timePtr(time.Now().Add(-2 * time.Hour)),
		IsVerified:      true,
		VerifiedAt:      timePtr(time.Now().Add(-20 * 24 * time.Hour)),
		KYCStatus:       "completed",
		KYCCompletedAt:  timePtr(time.Now().Add(-15 * 24 * time.Hour)),
		LoginCount:      156,
		LastActiveAt:    timePtr(time.Now().Add(-1 * time.Hour)),
		DeviceCount:     3,
		WarningCount:    0,
		SuspensionCount: 0,
		ReportedCount:   0,
	}

	duration := time.Since(start)
	c.logger.WithFields(logrus.Fields{
		"method":      "GetUserForAdmin",
		"user_id":     userID,
		"duration_ms": duration.Milliseconds(),
	}).Debug("UserCore service call completed")

	return userData, nil
}

// SuspendUser suspends a user
func (c *UserCoreClient) SuspendUser(ctx context.Context, userID, reason string) error {
	start := time.Now()
	c.logger.WithFields(logrus.Fields{
		"method":  "SuspendUser",
		"user_id": userID,
		"reason":  reason,
	}).Info("Suspending user")

	// In actual implementation, call gRPC interface
	// req := &pb.SuspendUserRequest{
	//     UserId: userID,
	//     Reason: reason,
	// }
	// _, err := c.client.SuspendUser(ctx, req)

	duration := time.Since(start)
	c.logger.WithFields(logrus.Fields{
		"method":      "SuspendUser",
		"user_id":     userID,
		"duration_ms": duration.Milliseconds(),
	}).Info("User suspended successfully")

	return nil
}

// RestoreUser restores a user
func (c *UserCoreClient) RestoreUser(ctx context.Context, userID, reason string) error {
	start := time.Now()
	c.logger.WithFields(logrus.Fields{
		"method":  "RestoreUser",
		"user_id": userID,
		"reason":  reason,
	}).Info("Restoring user")

	// In actual implementation, call gRPC interface
	duration := time.Since(start)
	c.logger.WithFields(logrus.Fields{
		"method":      "RestoreUser",
		"user_id":     userID,
		"duration_ms": duration.Milliseconds(),
	}).Info("User restored successfully")

	return nil
}

// GetUsers gets user list
func (c *UserCoreClient) GetUsers(ctx context.Context, filter port.UserFilter) (*UsersData, error) {
	start := time.Now()
	c.logger.WithFields(logrus.Fields{
		"method": "GetUsers",
		"filter": fmt.Sprintf("%+v", filter),
	}).Debug("Getting users list")

	// Mock pagination data
	users := make([]*UserSummaryData, 0)
	for i := 0; i < filter.PageSize && i < 10; i++ {
		users = append(users, &UserSummaryData{
			ID:             fmt.Sprintf("user_%d", i+1),
			Email:          fmt.Sprintf("<EMAIL>", i+1),
			Username:       fmt.Sprintf("user%d", i+1),
			DisplayName:    fmt.Sprintf("User %d", i+1),
			Status:         "active",
			Level:          i + 1,
			CreatedAt:      time.Now().Add(-time.Duration(i+1) * 24 * time.Hour),
			LastLoginAt:    timePtr(time.Now().Add(-time.Duration(i+1) * time.Hour)),
			FollowersCount: (i + 1) * 10,
			IsVerified:     i%2 == 0,
			IsPremium:      i%3 == 0,
		})
	}

	result := &UsersData{
		Users: users,
		Total: 100, // Mock total count
	}

	duration := time.Since(start)
	c.logger.WithFields(logrus.Fields{
		"method":      "GetUsers",
		"count":       len(users),
		"total":       result.Total,
		"duration_ms": duration.Milliseconds(),
	}).Debug("Users list retrieved")

	return result, nil
}

// ValidateEmployee validates employee identity
func (c *UserCoreClient) ValidateEmployee(ctx context.Context, employeeID string) (*model.Employee, error) {
	start := time.Now()
	c.logger.WithFields(logrus.Fields{
		"method":      "ValidateEmployee",
		"employee_id": employeeID,
	}).Debug("Validating employee")

	// Mock employee data
	employee := &model.Employee{
		ID:       employeeID,
		Email:    "<EMAIL>",
		Name:     "Admin User",
		Roles:    []string{"admin", "user_manager"},
		IsActive: true,
	}

	duration := time.Since(start)
	c.logger.WithFields(logrus.Fields{
		"method":      "ValidateEmployee",
		"employee_id": employeeID,
		"duration_ms": duration.Milliseconds(),
	}).Debug("Employee validated")

	return employee, nil
}

// Data transfer objects

// UserCoreData user core data
type UserCoreData struct {
	ID              string     `json:"id"`
	Email           string     `json:"email"`
	Username        string     `json:"username"`
	DisplayName     string     `json:"display_name"`
	Avatar          string     `json:"avatar"`
	Bio             string     `json:"bio"`
	Status          string     `json:"status"`
	Level           int        `json:"level"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	LastLoginAt     *time.Time `json:"last_login_at"`
	IsVerified      bool       `json:"is_verified"`
	VerifiedAt      *time.Time `json:"verified_at"`
	KYCStatus       string     `json:"kyc_status"`
	KYCCompletedAt  *time.Time `json:"kyc_completed_at"`
	LoginCount      int        `json:"login_count"`
	LastActiveAt    *time.Time `json:"last_active_at"`
	DeviceCount     int        `json:"device_count"`
	WarningCount    int        `json:"warning_count"`
	SuspensionCount int        `json:"suspension_count"`
	LastSuspendedAt *time.Time `json:"last_suspended_at"`
	ReportedCount   int        `json:"reported_count"`
}

// UserSummaryData user summary data
type UserSummaryData struct {
	ID             string     `json:"id"`
	Email          string     `json:"email"`
	Username       string     `json:"username"`
	DisplayName    string     `json:"display_name"`
	Status         string     `json:"status"`
	Level          int        `json:"level"`
	CreatedAt      time.Time  `json:"created_at"`
	LastLoginAt    *time.Time `json:"last_login_at"`
	FollowersCount int        `json:"followers_count"`
	IsVerified     bool       `json:"is_verified"`
	IsPremium      bool       `json:"is_premium"`
}

// UsersData user list data
type UsersData struct {
	Users []*UserSummaryData `json:"users"`
	Total int64              `json:"total"`
}

// timePtr converts time.Time to *time.Time helper function
func timePtr(t time.Time) *time.Time {
	return &t
}
