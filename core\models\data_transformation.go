// Copyright (c) 2025 Cina.Club
// All rights reserved.
// Created: 2025-01-23 16:30:00
// Modified: 2025-01-23 16:30:00

package models

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"
)

// DataTransformer provides comprehensive data transformation capabilities
type DataTransformer struct {
	mappings       map[string]*TransformationMapping
	validators     map[string]ValidationFunc
	converters     map[string]ConverterFunc
	preprocessors  []PreprocessorFunc
	postprocessors []PostprocessorFunc
}

// TransformationMapping defines how to transform data from source to target schema
type TransformationMapping struct {
	SourceSchema     string                 `json:"source_schema"`
	TargetSchema     string                 `json:"target_schema"`
	FieldMappings    []FieldMapping         `json:"field_mappings"`
	Rules            []TransformationRule   `json:"rules"`
	DefaultValues    map[string]interface{} `json:"default_values"`
	RequiredFields   []string               `json:"required_fields"`
	OptionalFields   []string               `json:"optional_fields"`
	ValidationRules  []ValidationRule       `json:"validation_rules"`
	ConditionalLogic []ConditionalTransform `json:"conditional_logic"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// FieldMapping defines how individual fields are mapped
type FieldMapping struct {
	SourceField      string                 `json:"source_field"`
	TargetField      string                 `json:"target_field"`
	Transform        string                 `json:"transform,omitempty"`
	DefaultValue     interface{}            `json:"default_value,omitempty"`
	Required         bool                   `json:"required"`
	Validation       string                 `json:"validation,omitempty"`
	ConditionalValue *ConditionalMapping    `json:"conditional_value,omitempty"`
	NestedMapping    *TransformationMapping `json:"nested_mapping,omitempty"`
	ArrayTransform   *ArrayTransformation   `json:"array_transform,omitempty"`
}

// TransformationRule defines a transformation rule
type TransformationRule struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Condition   string                 `json:"condition"`
	Action      string                 `json:"action"`
	Parameters  map[string]interface{} `json:"parameters"`
	Priority    int                    `json:"priority"`
}

// ConditionalTransform defines conditional transformation logic
type ConditionalTransform struct {
	Condition   string                 `json:"condition"`
	TrueAction  TransformationAction   `json:"true_action"`
	FalseAction *TransformationAction  `json:"false_action,omitempty"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// ConditionalMapping defines conditional field mapping
type ConditionalMapping struct {
	Conditions []ConditionValuePair `json:"conditions"`
	Default    interface{}          `json:"default"`
}

// ConditionValuePair represents a condition and its corresponding value
type ConditionValuePair struct {
	Condition string      `json:"condition"`
	Value     interface{} `json:"value"`
}

// ArrayTransformation defines how to transform arrays
type ArrayTransformation struct {
	ElementMapping  *TransformationMapping `json:"element_mapping"`
	FilterCondition string                 `json:"filter_condition,omitempty"`
	SortBy          string                 `json:"sort_by,omitempty"`
	Limit           *int                   `json:"limit,omitempty"`
	Aggregation     *AggregationConfig     `json:"aggregation,omitempty"`
}

// AggregationConfig defines aggregation operations
type AggregationConfig struct {
	Type    string `json:"type"` // "count", "sum", "avg", "min", "max", "group"
	Field   string `json:"field,omitempty"`
	GroupBy string `json:"group_by,omitempty"`
}

// TransformationAction defines an action to take during transformation
type TransformationAction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target"`
	Value      interface{}            `json:"value"`
	Parameters map[string]interface{} `json:"parameters"`
}

// Function types for transformation operations
type ValidationFunc func(interface{}) error
type ConverterFunc func(interface{}) (interface{}, error)
type PreprocessorFunc func(map[string]interface{}) error
type PostprocessorFunc func(map[string]interface{}) error

// TransformationResult represents the result of a transformation
type TransformationResult struct {
	Success         bool                   `json:"success"`
	Data            interface{}            `json:"data"`
	Errors          []TransformationError  `json:"errors"`
	Warnings        []TransformationError  `json:"warnings"`
	Metadata        map[string]interface{} `json:"metadata"`
	ProcessingTime  time.Duration          `json:"processing_time"`
	FieldsProcessed int                    `json:"fields_processed"`
	FieldsSkipped   int                    `json:"fields_skipped"`
}

// TransformationError represents an error during transformation
type TransformationError struct {
	Field      string                 `json:"field"`
	Message    string                 `json:"message"`
	Code       string                 `json:"code"`
	Severity   string                 `json:"severity"`
	Context    map[string]interface{} `json:"context"`
	Suggestion string                 `json:"suggestion,omitempty"`
}

// NewDataTransformer creates a new data transformer
func NewDataTransformer() *DataTransformer {
	dt := &DataTransformer{
		mappings:   make(map[string]*TransformationMapping),
		validators: make(map[string]ValidationFunc),
		converters: make(map[string]ConverterFunc),
	}

	// Register default converters
	dt.registerDefaultConverters()
	dt.registerDefaultValidators()

	return dt
}

// RegisterMapping registers a transformation mapping
func (dt *DataTransformer) RegisterMapping(name string, mapping *TransformationMapping) {
	dt.mappings[name] = mapping
}

// RegisterValidator registers a validation function
func (dt *DataTransformer) RegisterValidator(name string, validator ValidationFunc) {
	dt.validators[name] = validator
}

// RegisterConverter registers a converter function
func (dt *DataTransformer) RegisterConverter(name string, converter ConverterFunc) {
	dt.converters[name] = converter
}

// AddPreprocessor adds a preprocessor function
func (dt *DataTransformer) AddPreprocessor(preprocessor PreprocessorFunc) {
	dt.preprocessors = append(dt.preprocessors, preprocessor)
}

// AddPostprocessor adds a postprocessor function
func (dt *DataTransformer) AddPostprocessor(postprocessor PostprocessorFunc) {
	dt.postprocessors = append(dt.postprocessors, postprocessor)
}

// Transform transforms data using the specified mapping
func (dt *DataTransformer) Transform(mappingName string, sourceData interface{}) (*TransformationResult, error) {
	startTime := time.Now()

	mapping, exists := dt.mappings[mappingName]
	if !exists {
		return nil, fmt.Errorf("mapping %s not found", mappingName)
	}

	result := &TransformationResult{
		Success:  true,
		Metadata: make(map[string]interface{}),
	}

	// Convert source data to map for processing
	sourceMap, err := dt.toMap(sourceData)
	if err != nil {
		return nil, fmt.Errorf("failed to convert source data: %w", err)
	}

	// Apply preprocessors
	for _, preprocessor := range dt.preprocessors {
		if err := preprocessor(sourceMap); err != nil {
			result.Errors = append(result.Errors, TransformationError{
				Message:  fmt.Sprintf("Preprocessor failed: %v", err),
				Code:     "PREPROCESSOR_ERROR",
				Severity: "error",
			})
			result.Success = false
		}
	}

	// Perform transformation
	targetMap := make(map[string]interface{})

	// Apply field mappings
	for _, fieldMapping := range mapping.FieldMappings {
		if err := dt.applyFieldMapping(sourceMap, targetMap, fieldMapping); err != nil {
			result.Errors = append(result.Errors, TransformationError{
				Field:    fieldMapping.TargetField,
				Message:  err.Error(),
				Code:     "FIELD_MAPPING_ERROR",
				Severity: "error",
			})
			result.FieldsSkipped++
			if fieldMapping.Required {
				result.Success = false
			}
		} else {
			result.FieldsProcessed++
		}
	}

	// Apply default values
	for field, value := range mapping.DefaultValues {
		if _, exists := targetMap[field]; !exists {
			targetMap[field] = value
		}
	}

	// Apply transformation rules
	for _, rule := range mapping.Rules {
		if err := dt.applyTransformationRule(sourceMap, targetMap, rule); err != nil {
			result.Warnings = append(result.Warnings, TransformationError{
				Message:  fmt.Sprintf("Rule %s failed: %v", rule.Name, err),
				Code:     "RULE_ERROR",
				Severity: "warning",
			})
		}
	}

	// Apply conditional logic
	for _, conditional := range mapping.ConditionalLogic {
		if err := dt.applyConditionalTransform(sourceMap, targetMap, conditional); err != nil {
			result.Warnings = append(result.Warnings, TransformationError{
				Message:  fmt.Sprintf("Conditional logic failed: %v", err),
				Code:     "CONDITIONAL_ERROR",
				Severity: "warning",
			})
		}
	}

	// Validate required fields
	for _, field := range mapping.RequiredFields {
		if _, exists := targetMap[field]; !exists {
			result.Errors = append(result.Errors, TransformationError{
				Field:    field,
				Message:  "Required field is missing",
				Code:     "REQUIRED_FIELD_MISSING",
				Severity: "error",
			})
			result.Success = false
		}
	}

	// Apply validation rules
	for _, validationRule := range mapping.ValidationRules {
		if err := dt.applyValidationRule(targetMap, validationRule); err != nil {
			errorSeverity := validationRule.Severity
			if errorSeverity == "" {
				errorSeverity = "error"
			}

			transformError := TransformationError{
				Field:    validationRule.Field,
				Message:  err.Error(),
				Code:     "VALIDATION_ERROR",
				Severity: string(errorSeverity),
			}

			if errorSeverity == "error" {
				result.Errors = append(result.Errors, transformError)
				result.Success = false
			} else {
				result.Warnings = append(result.Warnings, transformError)
			}
		}
	}

	// Apply postprocessors
	for _, postprocessor := range dt.postprocessors {
		if err := postprocessor(targetMap); err != nil {
			result.Warnings = append(result.Warnings, TransformationError{
				Message:  fmt.Sprintf("Postprocessor failed: %v", err),
				Code:     "POSTPROCESSOR_ERROR",
				Severity: "warning",
			})
		}
	}

	result.Data = targetMap
	result.ProcessingTime = time.Since(startTime)
	result.Metadata["mapping_name"] = mappingName
	result.Metadata["source_schema"] = mapping.SourceSchema
	result.Metadata["target_schema"] = mapping.TargetSchema

	return result, nil
}

// applyFieldMapping applies a single field mapping
func (dt *DataTransformer) applyFieldMapping(sourceMap, targetMap map[string]interface{}, mapping FieldMapping) error {
	// Get source value
	sourceValue, exists := dt.getNestedValue(sourceMap, mapping.SourceField)
	if !exists {
		if mapping.Required {
			return fmt.Errorf("required source field %s not found", mapping.SourceField)
		}
		if mapping.DefaultValue != nil {
			sourceValue = mapping.DefaultValue
		} else {
			return nil // Skip optional missing fields
		}
	}

	// Handle conditional mapping
	if mapping.ConditionalValue != nil {
		conditionalValue, err := dt.evaluateConditionalMapping(sourceMap, mapping.ConditionalValue)
		if err != nil {
			return fmt.Errorf("conditional mapping failed: %w", err)
		}
		sourceValue = conditionalValue
	}

	// Apply transformation
	var transformedValue interface{} = sourceValue
	if mapping.Transform != "" {
		converter, exists := dt.converters[mapping.Transform]
		if !exists {
			return fmt.Errorf("converter %s not found", mapping.Transform)
		}

		var err error
		transformedValue, err = converter(sourceValue)
		if err != nil {
			return fmt.Errorf("transformation failed: %w", err)
		}
	}

	// Handle array transformation
	if mapping.ArrayTransform != nil {
		arrayValue, err := dt.transformArray(sourceValue, mapping.ArrayTransform)
		if err != nil {
			return fmt.Errorf("array transformation failed: %w", err)
		}
		transformedValue = arrayValue
	}

	// Apply validation
	if mapping.Validation != "" {
		validator, exists := dt.validators[mapping.Validation]
		if exists {
			if err := validator(transformedValue); err != nil {
				return fmt.Errorf("validation failed: %w", err)
			}
		}
	}

	// Set target value
	return dt.setNestedValue(targetMap, mapping.TargetField, transformedValue)
}

// applyTransformationRule applies a transformation rule
func (dt *DataTransformer) applyTransformationRule(sourceMap, targetMap map[string]interface{}, rule TransformationRule) error {
	// Evaluate condition
	conditionMet, err := dt.evaluateCondition(sourceMap, rule.Condition)
	if err != nil {
		return fmt.Errorf("condition evaluation failed: %w", err)
	}

	if !conditionMet {
		return nil
	}

	// Apply action based on rule type
	switch rule.Action {
	case "set_value":
		field := rule.Parameters["field"].(string)
		value := rule.Parameters["value"]
		return dt.setNestedValue(targetMap, field, value)

	case "copy_field":
		sourceField := rule.Parameters["source"].(string)
		targetField := rule.Parameters["target"].(string)
		value, exists := dt.getNestedValue(sourceMap, sourceField)
		if exists {
			return dt.setNestedValue(targetMap, targetField, value)
		}

	case "transform_field":
		field := rule.Parameters["field"].(string)
		transform := rule.Parameters["transform"].(string)

		value, exists := dt.getNestedValue(targetMap, field)
		if !exists {
			return nil
		}

		converter, exists := dt.converters[transform]
		if !exists {
			return fmt.Errorf("converter %s not found", transform)
		}

		transformedValue, err := converter(value)
		if err != nil {
			return err
		}

		return dt.setNestedValue(targetMap, field, transformedValue)
	}

	return nil
}

// applyConditionalTransform applies conditional transformation logic
func (dt *DataTransformer) applyConditionalTransform(sourceMap, targetMap map[string]interface{}, conditional ConditionalTransform) error {
	conditionMet, err := dt.evaluateCondition(sourceMap, conditional.Condition)
	if err != nil {
		return err
	}

	var action TransformationAction
	if conditionMet {
		action = conditional.TrueAction
	} else if conditional.FalseAction != nil {
		action = *conditional.FalseAction
	} else {
		return nil
	}

	return dt.applyTransformationAction(sourceMap, targetMap, action)
}

// applyTransformationAction applies a transformation action
func (dt *DataTransformer) applyTransformationAction(sourceMap, targetMap map[string]interface{}, action TransformationAction) error {
	switch action.Type {
	case "set":
		return dt.setNestedValue(targetMap, action.Target, action.Value)
	case "copy":
		value, exists := dt.getNestedValue(sourceMap, action.Value.(string))
		if exists {
			return dt.setNestedValue(targetMap, action.Target, value)
		}
	case "transform":
		transform := action.Parameters["transform"].(string)
		sourceField := action.Parameters["source"].(string)

		value, exists := dt.getNestedValue(sourceMap, sourceField)
		if !exists {
			return nil
		}

		converter, exists := dt.converters[transform]
		if !exists {
			return fmt.Errorf("converter %s not found", transform)
		}

		transformedValue, err := converter(value)
		if err != nil {
			return err
		}

		return dt.setNestedValue(targetMap, action.Target, transformedValue)
	}

	return nil
}

// applyValidationRule applies a validation rule
func (dt *DataTransformer) applyValidationRule(targetMap map[string]interface{}, rule ValidationRule) error {
	value, exists := dt.getNestedValue(targetMap, rule.Field)
	if !exists {
		return nil // Skip validation for missing optional fields
	}

	validator, exists := dt.validators[rule.Validator]
	if !exists {
		return fmt.Errorf("validator %s not found", rule.Validator)
	}

	return validator(value)
}

// Helper methods for nested field access
func (dt *DataTransformer) getNestedValue(data map[string]interface{}, field string) (interface{}, bool) {
	parts := strings.Split(field, ".")
	current := data

	for i, part := range parts {
		value, exists := current[part]
		if !exists {
			return nil, false
		}

		if i == len(parts)-1 {
			return value, true
		}

		if nestedMap, ok := value.(map[string]interface{}); ok {
			current = nestedMap
		} else {
			return nil, false
		}
	}

	return nil, false
}

func (dt *DataTransformer) setNestedValue(data map[string]interface{}, field string, value interface{}) error {
	parts := strings.Split(field, ".")
	current := data

	for i, part := range parts {
		if i == len(parts)-1 {
			current[part] = value
			return nil
		}

		if _, exists := current[part]; !exists {
			current[part] = make(map[string]interface{})
		}

		if nestedMap, ok := current[part].(map[string]interface{}); ok {
			current = nestedMap
		} else {
			return fmt.Errorf("cannot set nested value: %s is not a map", part)
		}
	}

	return nil
}

// toMap converts various data types to map[string]interface{}
func (dt *DataTransformer) toMap(data interface{}) (map[string]interface{}, error) {
	switch v := data.(type) {
	case map[string]interface{}:
		return v, nil
	case []byte:
		var result map[string]interface{}
		if err := json.Unmarshal(v, &result); err != nil {
			return nil, err
		}
		return result, nil
	case string:
		var result map[string]interface{}
		if err := json.Unmarshal([]byte(v), &result); err != nil {
			return nil, err
		}
		return result, nil
	default:
		// Use reflection to convert struct to map
		return dt.structToMap(data), nil
	}
}

// structToMap converts a struct to map using reflection
func (dt *DataTransformer) structToMap(data interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	value := reflect.ValueOf(data)
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	if value.Kind() != reflect.Struct {
		return result
	}

	typ := value.Type()
	for i := 0; i < value.NumField(); i++ {
		field := typ.Field(i)
		fieldValue := value.Field(i)

		if !fieldValue.CanInterface() {
			continue
		}

		jsonTag := field.Tag.Get("json")
		if jsonTag == "-" {
			continue
		}

		fieldName := field.Name
		if jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			if parts[0] != "" {
				fieldName = parts[0]
			}
		}

		result[fieldName] = fieldValue.Interface()
	}

	return result
}

// Register default converters and validators
func (dt *DataTransformer) registerDefaultConverters() {
	// String converters
	dt.converters["to_string"] = func(value interface{}) (interface{}, error) {
		return fmt.Sprintf("%v", value), nil
	}

	dt.converters["to_upper"] = func(value interface{}) (interface{}, error) {
		if str, ok := value.(string); ok {
			return strings.ToUpper(str), nil
		}
		return value, nil
	}

	dt.converters["to_lower"] = func(value interface{}) (interface{}, error) {
		if str, ok := value.(string); ok {
			return strings.ToLower(str), nil
		}
		return value, nil
	}

	dt.converters["trim"] = func(value interface{}) (interface{}, error) {
		if str, ok := value.(string); ok {
			return strings.TrimSpace(str), nil
		}
		return value, nil
	}

	// Number converters
	dt.converters["to_int"] = func(value interface{}) (interface{}, error) {
		switch v := value.(type) {
		case int:
			return v, nil
		case float64:
			return int(v), nil
		case string:
			return strconv.Atoi(v)
		default:
			return nil, fmt.Errorf("cannot convert %T to int", value)
		}
	}

	dt.converters["to_float"] = func(value interface{}) (interface{}, error) {
		switch v := value.(type) {
		case float64:
			return v, nil
		case int:
			return float64(v), nil
		case string:
			return strconv.ParseFloat(v, 64)
		default:
			return nil, fmt.Errorf("cannot convert %T to float", value)
		}
	}

	// Date converters
	dt.converters["to_timestamp"] = func(value interface{}) (interface{}, error) {
		switch v := value.(type) {
		case time.Time:
			return v.Unix(), nil
		case string:
			t, err := time.Parse(time.RFC3339, v)
			if err != nil {
				return nil, err
			}
			return t.Unix(), nil
		default:
			return nil, fmt.Errorf("cannot convert %T to timestamp", value)
		}
	}

	dt.converters["to_iso_date"] = func(value interface{}) (interface{}, error) {
		switch v := value.(type) {
		case time.Time:
			return v.Format(time.RFC3339), nil
		case int64:
			return time.Unix(v, 0).Format(time.RFC3339), nil
		case string:
			// Try to parse and reformat
			t, err := time.Parse(time.RFC3339, v)
			if err != nil {
				return nil, err
			}
			return t.Format(time.RFC3339), nil
		default:
			return nil, fmt.Errorf("cannot convert %T to ISO date", value)
		}
	}
}

func (dt *DataTransformer) registerDefaultValidators() {
	dt.validators["required"] = func(value interface{}) error {
		if value == nil {
			return fmt.Errorf("value is required")
		}

		// Check for zero values
		v := reflect.ValueOf(value)
		if v.IsZero() {
			return fmt.Errorf("value is required")
		}

		return nil
	}

	dt.validators["email"] = func(value interface{}) error {
		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("value must be a string")
		}

		// Simple email validation
		if !strings.Contains(str, "@") || !strings.Contains(str, ".") {
			return fmt.Errorf("invalid email format")
		}

		return nil
	}

	dt.validators["min_length"] = func(value interface{}) error {
		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("value must be a string")
		}

		if len(str) < 3 { // Default minimum length
			return fmt.Errorf("value must be at least 3 characters")
		}

		return nil
	}

	dt.validators["positive_number"] = func(value interface{}) error {
		switch v := value.(type) {
		case int:
			if v <= 0 {
				return fmt.Errorf("value must be positive")
			}
		case float64:
			if v <= 0 {
				return fmt.Errorf("value must be positive")
			}
		default:
			return fmt.Errorf("value must be a number")
		}

		return nil
	}
}

// Additional helper methods
func (dt *DataTransformer) evaluateCondition(data map[string]interface{}, condition string) (bool, error) {
	// Simple condition evaluation - in practice, you'd use a more sophisticated expression evaluator
	if condition == "true" {
		return true, nil
	}
	if condition == "false" {
		return false, nil
	}

	// Check for field existence
	if strings.HasPrefix(condition, "exists:") {
		field := strings.TrimPrefix(condition, "exists:")
		_, exists := dt.getNestedValue(data, field)
		return exists, nil
	}

	// Check for field equality
	if strings.Contains(condition, "==") {
		parts := strings.Split(condition, "==")
		if len(parts) == 2 {
			field := strings.TrimSpace(parts[0])
			expectedValue := strings.TrimSpace(parts[1])

			value, exists := dt.getNestedValue(data, field)
			if !exists {
				return false, nil
			}

			return fmt.Sprintf("%v", value) == expectedValue, nil
		}
	}

	return false, fmt.Errorf("unsupported condition: %s", condition)
}

func (dt *DataTransformer) evaluateConditionalMapping(data map[string]interface{}, mapping *ConditionalMapping) (interface{}, error) {
	for _, condition := range mapping.Conditions {
		conditionMet, err := dt.evaluateCondition(data, condition.Condition)
		if err != nil {
			continue
		}

		if conditionMet {
			return condition.Value, nil
		}
	}

	return mapping.Default, nil
}

func (dt *DataTransformer) transformArray(value interface{}, transform *ArrayTransformation) (interface{}, error) {
	// Convert to slice
	v := reflect.ValueOf(value)
	if v.Kind() != reflect.Slice && v.Kind() != reflect.Array {
		return nil, fmt.Errorf("value is not an array")
	}

	var result []interface{}

	for i := 0; i < v.Len(); i++ {
		element := v.Index(i).Interface()

		// Apply element mapping if specified
		if transform.ElementMapping != nil {
			// This would require recursive transformation
			// For now, just pass through
			result = append(result, element)
		} else {
			result = append(result, element)
		}
	}

	// Apply limit if specified
	if transform.Limit != nil && len(result) > *transform.Limit {
		result = result[:*transform.Limit]
	}

	return result, nil
}
